"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4118],{"onboarding/select-game":(e,t,a)=>{a.r(t),a.d(t,{SelectGame:()=>p});var i=a(15215),r=a(7530),s=a("aurelia-framework"),l=a(62914),o=a(43050),n=a(96555),d=a(20057),c=a(54995),m=a(64415);const g={slug:"my-games",titleKey:"game_collection.my_games",analyticsName:"My Games",location:"my_games",feeds:[{location:"my-games",analyticsName:"My Games",navLabel:"game_collection.my_games",searchPlaceholder:"game_collection.search_games",emptyMessage:{headerKey:"onboarding.empty_search_title",messageKey:"onboarding.empty_search_message"},feedConfig:o.IW,groups:["playable","installable"],metadataTypes:[]},{location:"popular",analyticsName:"Most Popular",navLabel:"game_collection.popular",searchPlaceholder:"game_collection.search_games",emptyMessage:{headerKey:"onboarding.empty_search_title",messageKey:"onboarding.empty_search_message"},feedConfig:o.dL,filterOptions:[{filter:{maxItems:30}}],groups:["playable","installable"],metadataTypes:[]},{location:"all",analyticsName:"All",navLabel:"game_collection.all",searchPlaceholder:"game_collection.search_games",emptyMessage:{headerKey:"onboarding.empty_search_title",messageKey:"onboarding.empty_search_message"},feedConfig:o.Vr,groups:["playable","installable"],metadataTypes:[]}],enableSearch:!0};let p=class{#e;#t;#a;#i;constructor(e,t,a){this.loading=!0,this.config=g,this.myGamesCount=0,this.gridColumns=0,this.#t=e,this.#a=t,this.#i=a}attached(){}bind(){this.selectedFeed=this.config.feeds[0]}detached(){this.#e?.disconnect()}gameGridRefChanged(e){e&&(this.#e?.disconnect(),this.#e=new ResizeObserver((()=>this.#r())),this.#e.observe(this.gameGridRef),this.#r())}#r(){if(!this.gameGridRef)return;const e=window.getComputedStyle(this.gameGridRef).getPropertyValue("grid-template-columns").split(" ").length;this.gridColumns=e}handleTitleClick(e){this.selectedOnboardingTitle=e,this.#a.event("onboarding_game_click",{titleId:e.id,feedTab:this.selectedFeed.analyticsName??""},l.Io)}getFeedCount(e){let t=0;switch(e){case"my-games":t=this.myGamesCount;break;case"popular":t=30;break;case"all":t=3e3}return t?`${this.#i.formatNumber(t,{notation:"compact",maximumFractionDigits:1,maximumSignificantDigits:3,roundingPriority:"lessPrecision"})}${"all"===e?"+":""}`:""}onSearch(){this.#a.event("onboarding_search",{feedTab:this.selectedFeed.analyticsName??"",searchTerm:this.searchTerms},l.Io)}onClear(){this.searchTerms=""}get filteredTitles(){return(0,m.$)(this.feedTitles,this.searchTerms,["name"],"name")}setFeed(e){this.selectedFeed=this.config.feeds.find((t=>t.location===e))??this.config.feeds[0]}selectedFeedChanged(){this.scrolledView.scrollTop=0,this.feedTitles=this.#t.getFilteredFeed(this.selectedFeed.feedConfig,this.selectedFeed.filterOptions?.[0]?.filter||{}).items.map((e=>{const t=this.titles[e.titleId],a=t?.gameIds?.flatMap((e=>this.games[e].correlationIds))?.map(n.o.parse)?.find((e=>"steam"===e.platform))?.sku??null;return{...t,steamAppId:a,isInstalled:e.isInstalled??!1,platformIds:e.platformIds??[]}})),this.feedTitles=this.feedTitles.filter((e=>!!e?.name)),this.feedTitles&&"my-games"===this.selectedFeed.location&&(this.myGamesCount=this.feedTitles.length),this.gameSearchInput?.clear(!1),this.feedTitles.length||this.searchTerms||(this.config.feeds.shift(),this.selectedFeed=this.config.feeds[0])}updateTab(e){this.selectedFeed=e}};(0,i.Cg)([r.sH,(0,i.Sn)("design:type",Object)],p.prototype,"selectedFeed",void 0),(0,i.Cg)([r.sH,(0,i.Sn)("design:type",HTMLElement)],p.prototype,"gameGridRef",void 0),(0,i.Cg)([s.bindable,(0,i.Sn)("design:type",Boolean)],p.prototype,"loading",void 0),(0,i.Cg)([s.bindable,(0,i.Sn)("design:type",Object)],p.prototype,"selectedOnboardingTitle",void 0),(0,i.Cg)([(0,r.Kj)("feedTitles","searchTerms"),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],p.prototype,"filteredTitles",null),p=(0,i.Cg)([(0,s.autoinject)(),(0,c.m6)({selectors:{titles:(0,c.$t)((e=>e.catalog?.titles)),games:(0,c.$t)((e=>e.catalog?.games))}}),(0,i.Sn)("design:paramtypes",[o.Y2,l.j0,d.F2])],p)},"onboarding/select-game.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var i=a(14385),r=a.n(i),s=new URL(a(18285),a.b);const l='<template> <require from="./select-game.scss"></require> <require from="./resources/elements/all-games-card"></require> <require from="./resources/elements/title-card"></require> <require from="../cheats/resources/elements/horizontal-game-feed"></require> <require from="../cheats/resources/elements/game-feed-item"></require> <require from="../cheats/resources/elements/game-search-input"></require> <require from="../cheats/resources/value-converters/group-feed-items"></require> <require from="../resources/elements/lazy-render"></require> <require from="../shared/resources/elements/tabs"></require> <require from="../shared/resources/elements/tab"></require> <div class="header"> <tabs> <tab repeat.for="feed of config.feeds" active.bind="selectedFeed.navLabel === feed.navLabel" click.delegate="updateTab(feed)"> <span>${feed.navLabel | i18n}</span> <span class="tab-extra">${getFeedCount(feed.location)}</span> </tab> </tabs> <div class="select-game"> <div class="wemod-logo"><img src="'+r()(s)+'"></div> <h1>${\'onboarding.select_a_game\' | i18n}</h1> </div> <div class="search"> <game-search-input on-clear.call="onClear()" on-search.call="onSearch()" search-terms.bind="searchTerms & debounce:400" placeholder-key.bind="selectedFeed.searchPlaceholder" view-model.ref="gameSearchInput"></game-search-input> </div> </div> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" overflow-fade="vertical" ref="scrolledView"> <section class="games"> <div class="grid" ref="gameGridRef"> <div class="grid-item" repeat.for="title of filteredTitles"> <let outer-index.bind="$index"></let> <lazy-render> <template replace-part="content"> <title-card tabindex="0" format.bind=" $index > (2 * gridColumns - 1) || selectedFeed.location === \'all\' ? \'landscape\' : \'portrait\'" title-info.bind="title" click.trigger="handleTitleClick(title)"></title-card> <div class="title-meta"> <h1 class="item-title">${title.name}</h1> <div class="platform-icons"> <span repeat.for="platform of title.platformIds"><inline-svg src.bind="platform | platformIconSvg"></inline-svg></span> </div> </div> </template> <template replace-part="placeholder"> <div class="game-item-placeholder"></div> </template> </lazy-render> </div> </div> <div if.bind="!filteredTitles.length && selectedFeed.emptyMessage" class="no-results"> <i class="question-icon"></i> <h1>${selectedFeed.emptyMessage.headerKey | i18n}</h1> <p innerhtml.bind="selectedFeed.emptyMessage.messageKey | i18n | markdown"></p> </div> </section> <all-games-card if.bind="selectedFeed.location === \'my-games\'" click.delegate="setFeed(\'all\')"></all-games-card> </div> </div> </template> '},"onboarding/select-game.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var i=a(31601),r=a.n(i),s=a(76314),l=a.n(s),o=a(4417),n=a.n(o),d=new URL(a(83959),a.b),c=l()(r()),m=n()(d);c.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,select-game .no-results i.question-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}select-game{--overflow-fade--background: rgba(var(--theme--default--background--rgb), 0.95);--page-spacing: 60px;--header-height: calc(2.5 * var(--page-spacing))}select-game .header{display:flex;flex-direction:row;justify-content:center;align-items:end;z-index:1;position:fixed;top:0;left:0;width:100%;padding:0px 40px 20px 40px;border-bottom:.5px solid rgba(var(--theme--default--background--rgb), 0.5);backdrop-filter:blur(40px);min-height:var(--header-height)}select-game .header .select-game{position:absolute;top:40px;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:28px}select-game .header .select-game .wemod-logo img{height:30px}select-game .header .select-game h1{margin:0;font-weight:800;font-size:32px;line-height:100%;display:flex;align-items:center;text-align:center;letter-spacing:-2px;color:#fff;mix-blend-mode:normal;text-shadow:0px 0px 24px rgba(255,255,255,.7)}select-game .header .search{display:flex;margin-left:auto}select-game .header .search .clear-button{top:15px}select-game .header .search .search-button{display:none}select-game .header .search .input-wrapper{width:auto}select-game .header .search input{height:40px;border-radius:24px}select-game .no-results{display:flex;flex-direction:column;color:rgba(255,255,255,.6);text-align:center;padding-bottom:16px}select-game .no-results i.question-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;font-size:64px;color:#fff;padding-bottom:8px}select-game .no-results i.question-icon:before{font-family:inherit;content:"search_off"}select-game .no-results h1{font-size:32px;font-weight:800;line-height:100%;letter-spacing:-0.64px;margin:0}select-game .grid{display:grid;grid-template-columns:repeat(auto-fill, minmax(236px, 1fr));gap:16px;row-gap:24px;margin-bottom:24px}select-game .grid .grid-item{display:flex;flex-direction:column;gap:16px;width:220px;max-width:220px;margin-top:0%;animation:.15s onboarding-game-slide-up ease-in-out}select-game .grid .grid-item title-card{width:100%}select-game .grid .grid-item title-card:focus{outline:none !important}select-game .grid .grid-item title-card:hover+.title-meta,select-game .grid .grid-item title-card:focus+.title-meta{transform:translateY(-8px)}select-game .grid .grid-item title-card:hover+.title-meta .item-title,select-game .grid .grid-item title-card:focus+.title-meta .item-title{color:#fff}select-game .grid .grid-item title-card:hover+.title-meta .platform-icons,select-game .grid .grid-item title-card:focus+.title-meta .platform-icons{opacity:1}select-game .grid .grid-item .title-meta{display:flex;flex-direction:column;gap:8px;transition:all .4s;transform:translateY(0px)}select-game .grid .grid-item .title-meta .item-title{color:rgba(255,255,255,.6);font-size:16px;font-weight:800;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;margin:0}select-game .grid .grid-item .title-meta .platform-icons{opacity:.6;display:flex;align-items:center;gap:8px}select-game .grid .game-item-placeholder{padding-bottom:var(--page-spacing)}select-game .grid .game-item-placeholder:before{width:258px;height:120px;border-radius:10px;display:block;content:"";background:rgba(255,255,255,.04)}select-game .view-scrollable{display:flex;flex-direction:column;height:100%;padding:40px;padding-bottom:calc(3*var(--page-spacing));padding-top:calc(3*var(--page-spacing));overflow-y:overlay;overflow-x:hidden;width:100%}select-game .view-scrollable::-webkit-scrollbar{width:10px;height:10px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}select-game .view-scrollable::-webkit-scrollbar-thumb:window-inactive,select-game .view-scrollable::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}select-game .view-scrollable::-webkit-scrollbar-thumb:window-inactive:hover,select-game .view-scrollable::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}select-game .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement{height:var(--header-height)}select-game .view-scrollable::-webkit-scrollbar-button:single-button:vertical:increment{height:10px}select-game .view-scrollable::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent var(--header-height), rgba(255, 255, 255, 0.1) var(--header-height), rgba(255, 255, 255, 0.1) calc(100% - 10px), transparent calc(100% - 10px))}select-game all-games-card{margin-top:40px}@keyframes onboarding-game-slide-up{from{margin-top:100%;height:300%}to{margin-top:0%;height:100%}}`,""]);const g=c}}]);
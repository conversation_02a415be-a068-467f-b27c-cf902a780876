"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4931,8872],{"shared/cheats/resources/elements/mods-list":(e,t,i)=>{i.r(t),i.d(t,{ModsList:()=>p});var n=i(15215),s=i("aurelia-event-aggregator"),a=i("aurelia-framework"),o=i(4585),d=i(72934),r=i(20057),l=i(70236);let p=class{#e;constructor(e){this.useRangeInputOverlay=!1,this.canUseInAppControls=!0,this.enabled=!1,this.precisionModsSectionsOpen={},this.activeIntervals={},this.loopPhaseStates={},this.isTimerPaneOpen=!1,this.timerPaneConfig={cssClass:"timer-pane",buttonDestroy:!0,initialBreak:"middle",breaks:{top:{enabled:!0,height:600},middle:{enabled:!0,height:300},bottom:{enabled:!0,height:100}}},this.selectedCheatConfig=null,this.handlePrecisionModsEducationDialogClosed=()=>{const e=document.querySelector(".precision-mods-button"),t=e?.closest(".cheats-wrapper");t?.classList.add("precision-mods-open"),window.setTimeout((()=>{t?.classList.remove("precision-mods-open")}),2e3)},this.#e=e}attached(){this.precisionModsEducationDialogSubscription=this.ea.subscribeOnce("precision-mods-education-dialog-closed",this.handlePrecisionModsEducationDialogClosed),this.timerUpdatesSubscription=this.ea.subscribe("timer-update",(e=>{e.activeInterval&&(e.clear?delete this.activeIntervals[e.cheatId]:this.activeIntervals[e.cheatId]=[...this.activeIntervals[e.cheatId]??[],e.activeInterval]),void 0!==e.loopPhase&&(this.loopPhaseStates[e.cheatId]=e.loopPhase)}))}detached(){this.precisionModsEducationDialogSubscription?.dispose(),this.timerUpdatesSubscription?.dispose()}get childMods(){return this.trainer.blueprint.cheats.reduce(((e,t)=>(t.parent&&(e[t.parent]=[...e[t.parent]||[],t]),e)),{})}get canUseBetaMods(){return this.isPro}get canUsePinnedMods(){return this.isPro}get modDisabledTooltip(){const e={play:"play",install_for_free:"add_or_install",install:"add_or_install"}[this.trainerPlayButtonState];return e?this.#e.getValue(`trainer_cheats_list.disabled_${e}_tooltip`):""}get playButtonLabel(){const e={play:"play",install_for_free:"install_for_free",install:"add_game"}[this.trainerPlayButtonState];return e?this.#e.getValue(`trainer_play_button.${e}`):""}get playButtonLabelClass(){return{play:"play",install_for_free:"install-for-free",install:"add-game"}[this.trainerPlayButtonState]??""}get resetAutoPins(){const e=this.trainer.pinnedMods?.length??0;return!this.canUsePinnedMods&&e!==this.pinnedModsList?.length&&e>0&&!this.isRemote&&!this.isOverlay}get trainerSupportsSaveCheats(){return!!this.trainer&&((0,d.o)(this.trainer.blueprint)||Object.values(this.cheatStates||{}).some((e=>e?.saveCheats?.supported)))}get readCheats(){return new Set(this.trainer.blueprint.cheats.map((e=>e.uuid)).filter((e=>this?.cheatsRead?.[e]||this?.cheatStates?.[e]?.instructionsRead)))}get hasViewedPrecisionModsSection(){return Object.values(this.precisionModsSectionsViewed??{}).filter((e=>e)).length>0}handlePrecisionModClick(e,t,i){e.preventDefault(),e.stopPropagation();const n=this.getModSectionName(t,i);this.precisionModsSectionsOpen[n]||this.ea.publish("precisionModsExpanded",{cheatUuid:t}),this.precisionModsSectionsOpen[n]=!this.precisionModsSectionsOpen[n]}getModSectionName(e,t){return`${e}${"pinned"===t?"-pinned":""}`}selectedCheatConfigChanged(e,t){t?.cheat?.uuid!==e?.cheat?.uuid&&null!==e&&(this.isTimerPaneOpen=!0)}handleSheetClosed(){this.isTimerPaneOpen=!1,this.selectedCheatConfig=null}handleRemoteTimerClick(e,t){this.isTimerPaneOpen=!1,this.selectedCheatConfig={cheat:e,category:t}}handleResetPins(e){e.stopPropagation(),this.handlePinMod?.({reset:!0})}handleCategoryCollapse(e){const t=document.getElementById(e);t?.classList?.toggle("collapsed")}handleButtonClick(e){this.setValue(e.target,e.args?.value??1,e.uuid,{type:"Button",cheat:e})}handleNumericChange(e,t){t>=(e.args?.min??0)&&t<=(e.args?.max??1/0)&&this.setValue(e.target,t,e.uuid,{type:"Numeric",cheat:e})}handleSelectionChange(e,t){t>=0&&t<(e.args?.options?.length??1/0)&&this.setValue(e.target,t,e.uuid,{type:"Selection",cheat:e})}handleScalarChange(e,t){this.setValue(e.target,t,e.uuid,{type:"Scalar",cheat:e})}handleIncrementalChange(e,t){this.setValue(e.target,t,e.uuid,{type:"Incremental",cheat:e})}handleToggleChange(e,t,i){this.setValue(e.target,t?1:0,e.uuid,{type:"Toggle",cheat:e},i)}handlePinClick(e){const t=!this.pinnedModsList?.includes(e.uuid);e&&this.handlePinMod?.({cheat:e,gameId:this.trainer?.gameId||this.trainer.game?.id,pin:t})}isAutoPin(e){return this.trainer.pinnedMods?.includes(e)??!1}handleDismissMessage(e){this.dismissModTimerMessage?.({timerType:e})}isModTimerEnabled(e){return(0,o.I)(e)}handleModTimerChange(e){this.handleModTimer?.({data:e.data,firstCall:e.firstCall}),this.isTimerPaneOpen=!1}handleModTimerExpired(e,t){if(e&&this.trainer?.gameId&&!this.isRemote&&!this.isOverlay)switch(e.type){case"toggle":{const i=1===t||2!==t&&("on"===this.cheatStates?.[e.uuid]?.timer?.type||"on"===this.modTimers?.[this.trainer.gameId]?.[e.uuid]?.type);this.handleToggleChange(e,i,!0);break}}}updateActiveIntervals(e,t,i){i?delete this.activeIntervals[t]:this.activeIntervals[t]=[...this.activeIntervals[t]??[],e]}updateLoopPhaseStates(e,t){this.loopPhaseStates[t]=e}getCategoryIcon(e){return`${e.toLowerCase()}-icon`}isBetaMod(e){return(0,l.Lt)(e.flags,2)}isModDisabled(e){return!this.isCreatorOrTester&&(this.trainer?.brokenCheatUuids??[]).includes(e)}markCheatInstructionsRead(e){!e?.instructions||this.cheatStates&&this?.cheatStates?.[e.uuid]?.instructionsRead||(this.cheatInstructionsRead&&this.cheatInstructionsRead({cheat:e}),this.ea.publish("markCheatInstructionsRead",e))}setValue(e,t,i,n,s){this.valueChanged&&this.valueChanged({name:e,value:t,cheatId:i}),this.ea.publish(`handleCheat${n.type}Change`,{cheat:n.cheat,value:t,isTimer:s})}};(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"isRemote",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"isOverlay",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"gamePreferences",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"isCreatorOrTester",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",String)],p.prototype,"language",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"modTimers",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"modTimerMessagesDismissed",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"precisionModsSectionsViewed",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"trainer",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",String)],p.prototype,"trainerPlayButtonState",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"translations",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"variables",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",s.EventAggregator)],p.prototype,"ea",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Number)],p.prototype,"latency",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Function)],p.prototype,"valueChanged",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Function)],p.prototype,"cheatInstructionsRead",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Function)],p.prototype,"dismissModTimerMessage",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Function)],p.prototype,"onHotkeyPress",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Function)],p.prototype,"setCustomHotkey",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Function)],p.prototype,"handlePinMod",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Function)],p.prototype,"handleModTimer",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"cheatStates",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"betaModsEnabled",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"cheatsRead",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"dialogDisabled",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"hotkeyEditDisabled",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"pinDisabled",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Object)],p.prototype,"inputsDisabled",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"isPro",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"showInputs",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"showHotkeys",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"useRangeInputOverlay",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Array)],p.prototype,"pinnedModsList",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"hasClickedModPin",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"canUseInAppControls",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"enabled",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"canUseSaveCheats",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],p.prototype,"isSaveCheatsEnabled",void 0),(0,n.Cg)([a.observable,(0,n.Sn)("design:type",Object)],p.prototype,"selectedCheatConfig",void 0),(0,n.Cg)([(0,a.computedFrom)("trainer.blueprint.cheats"),(0,n.Sn)("design:type",Object),(0,n.Sn)("design:paramtypes",[])],p.prototype,"childMods",null),(0,n.Cg)([(0,a.computedFrom)("isPro"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],p.prototype,"canUseBetaMods",null),(0,n.Cg)([(0,a.computedFrom)("isPro"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],p.prototype,"canUsePinnedMods",null),(0,n.Cg)([(0,a.computedFrom)("trainerPlayButtonState"),(0,n.Sn)("design:type",String),(0,n.Sn)("design:paramtypes",[])],p.prototype,"modDisabledTooltip",null),(0,n.Cg)([(0,a.computedFrom)("trainerPlayButtonState","language"),(0,n.Sn)("design:type",String),(0,n.Sn)("design:paramtypes",[])],p.prototype,"playButtonLabel",null),(0,n.Cg)([(0,a.computedFrom)("trainerPlayButtonState"),(0,n.Sn)("design:type",String),(0,n.Sn)("design:paramtypes",[])],p.prototype,"playButtonLabelClass",null),(0,n.Cg)([(0,a.computedFrom)("trainer.pinnedMods","canUsePinnedMods","pinnedModsList"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],p.prototype,"resetAutoPins",null),(0,n.Cg)([(0,a.computedFrom)("cheatStates"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],p.prototype,"trainerSupportsSaveCheats",null),(0,n.Cg)([(0,a.computedFrom)("cheatStates","cheatsRead","trainer.blueprint.cheats"),(0,n.Sn)("design:type",Set),(0,n.Sn)("design:paramtypes",[])],p.prototype,"readCheats",null),(0,n.Cg)([(0,a.computedFrom)("precisionModsSectionsViewed"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],p.prototype,"hasViewedPrecisionModsSection",null),p=(0,n.Cg)([(0,a.autoinject)(),(0,n.Sn)("design:paramtypes",[r.F2])],p)}}]);
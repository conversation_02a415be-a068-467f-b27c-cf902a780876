"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5334],{"settings/resources/elements/overlay-hotkey-selector":(e,t,o)=>{o.r(t),o.d(t,{OverlayHotkeySelector:()=>p});var i=o(15215),a=o("aurelia-framework"),r=o(20770),n=o(43544),s=o(4826),l=o(48881);let p=class{#e;constructor(e,t){this.overlayHotkeyService=t,this.hotkeyOptions=s.Y,this.#e=e}setHotkey(e){this.#e.dispatch(l.Kc,{overlayHotkey:e})}compareHotkeys(e,t){return e.key===t.key&&e.alt===t.alt&&e.ctrl===t.ctrl&&e.shift===t.shift}};p=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il,n.u])],p)},"settings/resources/elements/overlay-hotkey-selector.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./overlay-hotkey-selector.scss"></require> <div class="overlay-hotkey-selector"> <button repeat.for="option of hotkeyOptions" click.delegate="setHotkey(option)" class="hotkey-option ${compareHotkeys(overlayHotkeyService.hotkey, option) ? \'selected\' : \'\'}"> <span class="radio"></span> <span class="label">${overlayHotkeyService.getProperHotkey(option)}</span> </button> </div> </template> '},"settings/resources/elements/overlay-hotkey-selector.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>m});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r),s=o(4417),l=o.n(s),p=new URL(o(81206),o.b),c=n()(a()),d=l()(p);c.push([e.id,`.overlay-hotkey-selector{display:flex;flex-direction:column;gap:10px}.overlay-hotkey-selector .hotkey-option{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;background:rgba(0,0,0,0);border:none;padding:0}.overlay-hotkey-selector .hotkey-option,.overlay-hotkey-selector .hotkey-option *{cursor:pointer}.overlay-hotkey-selector .hotkey-option>*:first-child{margin-right:9px}.overlay-hotkey-selector .hotkey-option .radio{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;border-radius:50%;display:inline-flex;align-items:center;justify-content:center;width:15px;height:15px;border-color:rgba(255,255,255,.25);background-color:rgba(0,0,0,0)}.overlay-hotkey-selector .hotkey-option .radio,.overlay-hotkey-selector .hotkey-option .radio *{cursor:pointer}.overlay-hotkey-selector .hotkey-option .radio:checked:before{opacity:1}.overlay-hotkey-selector .hotkey-option .radio:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${d});mask:url(${d})}.overlay-hotkey-selector .hotkey-option .radio:before{width:9px;height:9px;border-radius:50%;-webkit-mask-box-image:none;mask:none;position:relative;top:initial;left:initial;box-shadow:inset 0 0 0 1px var(--theme--background);transform:scale(1)}.overlay-hotkey-selector .hotkey-option .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color)}.overlay-hotkey-selector .hotkey-option .label,.overlay-hotkey-selector .hotkey-option .label *{cursor:pointer}.overlay-hotkey-selector .hotkey-option:hover .radio,.overlay-hotkey-selector .hotkey-option.selected .radio{border-color:var(--theme--highlight)}.overlay-hotkey-selector .hotkey-option:hover .radio:before,.overlay-hotkey-selector .hotkey-option.selected .radio:before{opacity:1}`,""]);const m=c},"settings/resources/elements/payment-method.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="../../../shared/resources/elements/alert"></require> <h5 innerhtml.bind="\'billing_settings.payment_method\' | i18n | markdown" class="${account.subscription.pastDueInvoice ? \'warning\' : \'\'}"></h5> <div class="details"> <span class="meta"> <template if.bind="!paymentMethod"> ${\'billing_settings.no_payment_method\' | i18n} </template> <template if.bind="[\'card\', \'google_pay\', \'apple_pay\'].includes(paymentMethod.type)"> <span if.bind="paymentMethod.type === \'card\'" class="card-type wemod-payment-method--${paymentMethod.typeData.brand | dashCase}"></span> <span else class="card-type wemod-payment-method--${paymentMethod.type | dashCase}"></span> <em>${paymentMethod.typeData.brandName}</em> <span> · </span> <span>${\'payment_method.ending_in_digits\' | i18n:{digits: paymentMethod.typeData.lastFour}}</span> <span> · </span> <span>${\'payment_method.expiration_$month_$year\' | i18n:{month: paymentMethod.typeData.expMonth, year: paymentMethod.typeData.expYear % 1000}}</span> </template> <template if.bind="paymentMethod.type === \'paypal\'"> <span class="card-type wemod-payment-method--paypal"></span> <em>${\'payment_method.paypal\' | i18n}</em> <span> · </span> <span>${paymentMethod.typeData.email}</span> </template> <template if.bind="paymentMethod.type === \'amazon_pay\'"> <span class="card-type wemod-payment-method--amazon-pay"></span> <em>${\'payment_method.amazon_pay\' | i18n}</em> <span> · </span> <span>${paymentMethod.typeData.email}</span> </template> <template if.bind="paymentMethod.type === \'alipay\'"> <span class="card-type wemod-payment-method--alipay"></span> <em>${\'payment_method.alipay\' | i18n}</em> </template> <template if.bind="paymentMethod.type === \'direct_debit\'"> <span class="card-type wemod-payment-method--direct-debit"></span> <em>${\'payment_method.direct_debit\' | i18n}</em> <span>*${paymentMethod.typeData.lastFour}</span> <span> · </span> <span>${paymentMethod.typeData.mandateId}</span> </template> <template if.bind="paymentMethod.type === \'kr_market\'"> <span class="card-type wemod-payment-method--kr-market"></span> <em>${\'payment_method.kr_market\' | i18n}</em> </template> <template if.bind="paymentMethod.type === \'kakao_pay\'"> <span class="card-type wemod-payment-method--kakao-pay"></span> <em>${\'payment_method.kakao_pay\' | i18n}</em> </template> <template if.bind="paymentMethod.type === \'kr_card\'"> <span class="card-type wemod-payment-method--kr-card"></span> <em>${\'payment_method.kr_card\' | i18n}</em> </template> <template if.bind="paymentMethod.type === \'naver_pay\'"> <span class="card-type wemod-payment-method--naver-pay"></span> <em>${\'payment_method.naver_pay\' | i18n}</em> </template> </span> <span if.bind="paymentMethod && ((account.subscription && !account.subscription.pastDueInvoice) || (!account.subscription && !hasCustomerBillingAddress))" class="row-actions"> <span class="links"> <button class="link" click.delegate="changePaymentMethod(\'billing_settings_change_payment_method_button\')"> ${\'billing_settings.change_payment_method\' | i18n} </button> </span> </span> <span if.bind="paymentMethod && !account.subscription" class="row-actions"> <span class="links"> <button class="link remove" click.delegate="deleteBillingInfo()">${\'billing_settings.remove\' | i18n}</button> </span> </span> </div> <div class="main-actions" if.bind="!paymentMethod"> <button class="button" click.trigger="changePaymentMethod(\'billing_settings_add_payment_method_button\')"> ${\'billing_settings.add_payment_method\' | i18n} </button> </div> <div if.bind="paymentMethod && account.subscription.pastDueInvoice" class="main-actions"> <button class="button main small" click.delegate="changePaymentMethod(\'billing_settings_past_due_button\')"> ${\'billing_settings.change_payment_method\' | i18n} </button> </div> <div if.bind="paymentMethod && !account.subscription && !hasCustomerBillingAddress" class="alert"> <alert>${\'billing_settings.update_billing_address\' | i18n}</alert> </div> </template> '},"settings/resources/elements/profile-settings":(e,t,o)=>{o.r(t),o.d(t,{ProfileSettings:()=>u});var i=o(15215),a=o("aurelia-framework"),r=o(20770),n=o(68663),s=o(10699),l=o(67064),p=o(17724),c=o("shared/dialogs/basic-dialog"),d=o(20057),m=o(54995),g=o(70236),h=o(48881);let u=class{#t;#o;#e;#i;#a;constructor(e,t,o,i,a){this.initialized=!1,this.saving=!1,this.#t=e,this.#o=t,this.#e=o,this.#i=i,this.#a=a}attached(){this.username=this.account.username,this.profileImage=(0,g.Lt)(this.account.flags,16)?this.account.profileImage:null,this.currentProfileImage=this.account.profileImage,this.initialized=!0}detached(){this.initialized=!1}changeProfileImage(e){const t=e.target;if(t?.files&&t.files.length>0){const e=new FileReader;e.readAsDataURL(t.files[0]),e.addEventListener("load",(()=>{t.value="",this.profileImage=e.result}),{once:!0})}}async save(){if(!this.canSave)return;this.saving=!0;let e=null;this.initialized&&this.usernameUpdated&&(e=await this.#r((()=>this.#t.changeAccountUsername(this.username)),"profile_settings.username_change_success_toast")),this.initialized&&this.accountEmail.value!==this.account.email&&await this.accountEmail.submit()&&this.#o.toast({content:"profile_settings.email_change_success_toast",type:"ok"}),this.initialized&&this.profileImageUpdated&&(e=await this.#r((()=>this.#t.changeAccountProfileImage(this.profileImage)),"profile_settings.profile_image_change_success_toast")),null!==e&&(await this.#e.dispatch(h.Ui,e),this.currentProfileImage=e.profileImage),this.profileImage=(0,g.Lt)(this.account.flags,16)?this.account.profileImage:null,this.saving=!1}async#r(e,t){let o=null;try{o=await e(),o&&this.#o.toast({content:t,type:"ok"})}catch(e){this.#o.toast({content:e instanceof p.hD&&e.code===p.O4.ValidationError?d.F2.literal(e.data?.entries?.reduce(((e,t)=>e+(t.message+" ")),"")??""):"profile_settings.update_error_toast",type:"alert"})}return o}async switchUser(){if((0,g.br)(this.account.flags,[2,8])){if("profile_settings.sign_out"!==await this.#i.show({message:"profile_settings.are_you_sure",headerLabel:"profile_settings.sign_out",options:[{label:"profile_settings.cancel"},{label:"profile_settings.sign_out",style:"primary"}],cancelable:!0}))return}else if("profile_settings.sign_out"!==await this.#i.show({message:"profile_settings.no_email_for_$username",messageParams:{username:this.account.username},headerLabel:"profile_settings.hold_on",options:[{label:"profile_settings.update_account",style:"primary"},{label:"profile_settings.sign_out",style:"primary"}],cancelable:!0}))return;return this.#a.signOut()}get usernameUpdated(){return this.username!==this.account.username}get profileImageUpdated(){return!(null===this.profileImage&&!this.profileImageSet)&&this.profileImage!==this.account.profileImage}get profileImageSet(){return(0,g.Lt)(this.account.flags,16)}get canSave(){const e=this.accountEmail.value!==this.account.email,t=["valid","unsure"].includes(this.accountEmailStatus);return!this.saving&&(this.usernameUpdated||this.profileImageUpdated||t&&e)}};(0,i.Cg)([(0,a.computedFrom)("username","account.username"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],u.prototype,"usernameUpdated",null),(0,i.Cg)([(0,a.computedFrom)("profileImage","account.profileImage","account.flags"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],u.prototype,"profileImageUpdated",null),(0,i.Cg)([(0,a.computedFrom)("account.flags"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],u.prototype,"profileImageSet",null),(0,i.Cg)([(0,a.computedFrom)("saving","usernameUpdated","profileImageUpdated","accountEmail.value","account.email","accountEmailStatus"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],u.prototype,"canSave",null),u=(0,i.Cg)([(0,m.m6)({selectors:{account:(0,m.$t)((e=>e.account))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[n.x,l.l,r.il,c.BasicDialogService,s.G])],u)},"settings/resources/elements/profile-settings.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});var i=o(14385),a=o.n(i),r=new URL(o(34635),o.b);const n='<template> <require from="./profile-settings.scss"></require> <require from="../../../resources/elements/account-email"></require> <section class="profile-settings" if.bind="initialized"> <div class="profile-image-editor"> <label> <img if.bind="!profileImage.startsWith(\'data:\')" class="profile-image" src.bind="profileImage || currentProfileImage | cdn:{size: 128}"> <img else class="profile-image" src.bind="profileImage"> <div>${\'profile_settings.upload_new_pic\' | i18n}</div> <input type="file" accept=".jpg,.jpeg,.png" change.delegate="changeProfileImage($event)"> </label> <button class="clear-button" click.delegate="profileImage = null" if.bind="profileImage"> <inline-svg src="'+a()(r)+'"></inline-svg> </button> </div> <form class="settings-form" submit.delegate="save()"> <div class="form-row"> <input type="text" value.bind="username" spellcheck="false" maxlength="20"> <label>${\'profile_settings.username\' | i18n}</label> </div> <div class="form-row"> <account-email view-model.ref="accountEmail" status.bind="accountEmailStatus" location="profile_settings"></account-email> <label>${\'profile_settings.email_address\' | i18n}</label> </div> <div class="row"> <div class="col"> <button disabled.bind="!canSave" class="settings-form-submit-button"> ${\'profile_settings.save\' | i18n} </button> </div> <div class="col"> <a class="logout-link" href="#" click.delegate="switchUser()">${\'profile_settings.logout\' | i18n}</a> </div> </div> </form> </section> </template> '},"settings/resources/elements/profile-settings.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r)()(a());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.profile-settings .profile-image-editor{position:relative;margin-bottom:22px}.profile-settings .profile-image-editor,.profile-settings .profile-image-editor *{cursor:pointer}.profile-settings .profile-image-editor label{font-size:12px;line-height:18px;font-weight:500;color:rgba(255,255,255,.4);display:flex;align-items:center}.profile-settings .profile-image-editor label:hover{color:#fff}.profile-settings .profile-image-editor input[type=file]{display:none}.profile-settings .profile-image-editor .profile-image{display:flex;width:56px;height:56px;border-radius:50%;overflow:hidden;margin-right:15px}.profile-settings .profile-image-editor .clear-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)) !important;display:inline-flex;width:26px;height:26px;box-shadow:none;padding:0;border-radius:50%;border:0;align-items:center;justify-content:center;transition:background-color .15s;position:absolute;left:35px;top:-5px}.profile-settings .profile-image-editor .clear-button,.profile-settings .profile-image-editor .clear-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .profile-settings .profile-image-editor .clear-button{border:1px solid #fff}}.profile-settings .profile-image-editor .clear-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.profile-settings .profile-image-editor .clear-button>*:first-child{padding-left:0}.profile-settings .profile-image-editor .clear-button>*:last-child{padding-right:0}.profile-settings .profile-image-editor .clear-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .profile-settings .profile-image-editor .clear-button svg *{fill:CanvasText}}.profile-settings .profile-image-editor .clear-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .profile-settings .profile-image-editor .clear-button svg{opacity:1}}.profile-settings .profile-image-editor .clear-button img{height:50%}.profile-settings .profile-image-editor .clear-button:disabled{opacity:.3}.profile-settings .profile-image-editor .clear-button:disabled,.profile-settings .profile-image-editor .clear-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.profile-settings .profile-image-editor .clear-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.profile-settings .profile-image-editor .clear-button:not(:disabled):hover svg{opacity:1}}.profile-settings .profile-image-editor .clear-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(forced-colors: active){body:not(.override-contrast-mode) .profile-settings .profile-image-editor .clear-button{border:1px solid #fff}}.profile-settings .profile-image-editor .clear-button svg{opacity:1}@media(hover: hover){.profile-settings .profile-image-editor .clear-button:hover{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.25)) !important}}.profile-settings .row{display:flex;align-items:center;width:100%}.profile-settings .row .col{flex:1;display:flex}.profile-settings .row .col:last-child{justify-content:flex-end}.profile-settings .logout-link{font-size:12px;line-height:18px;font-weight:700;color:var(--color--alert)}.profile-settings .logout-link:hover{color:#fff}",""]);const s=n},"settings/resources/elements/radio.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./radio.scss"></require> <div class="radio"> <button repeat.for="option of setting.radioOptions" click.delegate="value = option.value" class="option ${(value || setting.default) === option.value ? \'selected\' : \'\' }"> <span class="radio"></span> <span class="label" innerhtml="${option.labelKey | i18n: option.labelParams | markdown}"></span> </button> </div> </template> '},"settings/resources/elements/radio.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>m});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r),s=o(4417),l=o.n(s),p=new URL(o(81206),o.b),c=n()(a()),d=l()(p);c.push([e.id,`.radio{display:flex;flex-direction:column;gap:10px}.radio .option{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;background:rgba(0,0,0,0);border:none;padding:0}.radio .option,.radio .option *{cursor:pointer}.radio .option>*:first-child{margin-right:9px}.radio .option .radio{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;border-radius:50%;display:inline-flex;align-items:center;justify-content:center;width:15px;height:15px;border-color:rgba(255,255,255,.25);background-color:rgba(0,0,0,0)}.radio .option .radio,.radio .option .radio *{cursor:pointer}.radio .option .radio:checked:before{opacity:1}.radio .option .radio:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${d});mask:url(${d})}.radio .option .radio:before{width:9px;height:9px;border-radius:50%;-webkit-mask-box-image:none;mask:none;position:relative;top:initial;left:initial;box-shadow:inset 0 0 0 1px var(--theme--background);transform:scale(1)}.radio .option .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color);color:var(--theme--text-primary)}.radio .option .label,.radio .option .label *{cursor:pointer}.radio .option .label strong{font-weight:800;color:var(--theme--text-highlight)}.radio .option:hover .radio,.radio .option.selected .radio{border-color:var(--theme--highlight)}.radio .option:hover .radio:before,.radio .option.selected .radio:before{opacity:1}`,""]);const m=c}}]);
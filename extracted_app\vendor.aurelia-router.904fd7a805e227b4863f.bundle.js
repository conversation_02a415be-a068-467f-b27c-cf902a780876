"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4948],{18776:(t,e,r)=>{r.d(e,{F6:()=>z,Ix:()=>m,Lz:()=>ut});var n=r(96610),i=r(27884),o=r(58482),a=r(86226),u=r("aurelia-event-aggregator"),s=function(t,e){return s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},s(t,e)},c=function(){function t(t){this.plan=null,this.options={},Object.assign(this,t),this.params=this.params||{},this.viewPortInstructions={};var e=[],r=this;do{var n=Object.assign({},r.params);r.config&&r.config.hasChildRouter&&delete n[r.getWildCardName()],e.unshift(n),r=r.parentInstruction}while(r);var i=Object.assign.apply(Object,[{},this.queryParams].concat(e));this.lifecycleArgs=[i,this.config,this]}return t.prototype.getAllInstructions=function(){var t=[this],e=this.viewPortInstructions;for(var r in e){var n=e[r].childNavigationInstruction;n&&t.push.apply(t,n.getAllInstructions())}return t},t.prototype.getAllPreviousInstructions=function(){return this.getAllInstructions().map((function(t){return t.previousInstruction})).filter((function(t){return t}))},t.prototype.addViewPortInstruction=function(t,e,r,n){var i,o="string"==typeof t?t:t.name,a=this.lifecycleArgs,u=Object.assign({},a[1],{currentViewPort:o});return i="string"==typeof t?{name:t,strategy:e,moduleId:r,component:n,childRouter:n.childRouter,lifecycleArgs:[a[0],u,a[2]]}:{name:o,strategy:t.strategy,component:t.component,moduleId:t.moduleId,childRouter:t.component.childRouter,lifecycleArgs:[a[0],u,a[2]]},this.viewPortInstructions[o]=i},t.prototype.getWildCardName=function(){var t=this.config.route,e=t.lastIndexOf("*");return t.substr(e+1)},t.prototype.getWildcardPath=function(){var t=this.getWildCardName(),e=this.params[t]||"",r=this.queryString;return r&&(e+="?"+r),e},t.prototype.getBaseUrl=function(){var t=this,e=encodeURI,r=decodeURI(this.fragment);if(""===r){var n=this.router.routes.find((function(e){return e.name===t.config.name&&""!==e.route}));n&&(r=n.route)}if(!this.params)return e(r);var i=this.getWildCardName(),o=this.params[i]||"";return e(o?r.substr(0,r.lastIndexOf(o)):r)},t.prototype._commitChanges=function(t){var e=this,r=this.router;r.currentInstruction=this;var n=this.previousInstruction;n&&(n.config.navModel.isActive=!1),this.config.navModel.isActive=!0,r.refreshNavigation();var i=[],o=[],a=this.viewPortInstructions,u=function(e){var n=a[e],u=r.viewPorts[e];if(!u)throw new Error("There was no router-view found in the view for "+n.moduleId+".");var s=n.childNavigationInstruction;"replace"===n.strategy?s&&s.parentCatchHandler?i.push(s._commitChanges(t)):(t&&o.push({viewPort:u,viewPortInstruction:n}),i.push(u.process(n,t).then((function(){return s?s._commitChanges(t):Promise.resolve()})))):s&&i.push(s._commitChanges(t))};for(var s in a)u(s);return Promise.all(i).then((function(){return o.forEach((function(t){return t.viewPort.swap(t.viewPortInstruction)})),null})).then((function(){return l(e)}))},t.prototype._updateTitle=function(){var t=this.router,e=this._buildTitle(t.titleSeparator);e&&t.history.setTitle(e)},t.prototype._buildTitle=function(t){void 0===t&&(t=" | ");var e="",r=[],n=this.config.navModel.title,i=this.router,o=this.viewPortInstructions;for(var a in n&&(e=i.transformTitle(n)),o){var u=o[a].childNavigationInstruction;if(u){var s=u._buildTitle(t);s&&r.push(s)}}return r.length&&(e=r.join(t)+(e?t:"")+e),i.title&&(e+=(e?t:"")+i.transformTitle(i.title)),e},t}(),l=function(t){t.previousInstruction=null,t.plan=null},h=function(){function t(t,e){this.isActive=!1,this.title=null,this.href=null,this.relativeHref=null,this.settings={},this.config=null,this.router=t,this.relativeHref=e}return t.prototype.setTitle=function(t){this.title=t,this.isActive&&this.router.updateTitle()},t}();function f(t,e,r){return void 0===r&&(r=!1),e||"#"===t[0]||(t="#"+t),e&&r&&(t=t.substring(1,t.length)),t}function p(t,e,r,n){if(g.test(t))return t;var i="";return e.length&&"/"!==e[0]&&(i+="/"),(i+=e).length&&"/"===i[i.length-1]||"/"===t[0]||(i+="/"),i.length&&"/"===i[i.length-1]&&"/"===t[0]&&(i=i.substring(0,i.length-1)),f(i+t,r,n)}function v(t){var e=[];if(Array.isArray(t.route))for(var r=0,n=t.route.length;r<n;++r){var i=Object.assign({},t);i.route=t.route[r],e.push(i)}else e.push(Object.assign({},t));return e}var d=/^#?\//,g=/^([a-z][a-z0-9+\-.]*:)?\/\//i,y=function(){function t(){this.instructions=[],this.options={},this.pipelineSteps=[]}return t.prototype.addPipelineStep=function(t,e){if(null==e)throw new Error("Pipeline step cannot be null or undefined.");return this.pipelineSteps.push({name:t,step:e}),this},t.prototype.addAuthorizeStep=function(t){return this.addPipelineStep("authorize",t)},t.prototype.addPreActivateStep=function(t){return this.addPipelineStep("preActivate",t)},t.prototype.addPreRenderStep=function(t){return this.addPipelineStep("preRender",t)},t.prototype.addPostRenderStep=function(t){return this.addPipelineStep("postRender",t)},t.prototype.fallbackRoute=function(t){return this._fallbackRoute=t,this},t.prototype.map=function(t){var e=this;return Array.isArray(t)?(t.forEach((function(t){return e.map(t)})),this):this.mapRoute(t)},t.prototype.useViewPortDefaults=function(t){return this.viewPortDefaults=t,this},t.prototype.mapRoute=function(t){return this.instructions.push((function(e){for(var r,n=v(t),i=0,o=n.length;i<o;++i){var a=n[i];a.settings=a.settings||{},r||(r=e.createNavModel(a)),e.addRoute(a,r)}})),this},t.prototype.mapUnknownRoutes=function(t){return this.unknownRouteConfig=t,this},t.prototype.exportToRouter=function(t){for(var e=this.instructions,r=0,n=e.length;r<n;++r)e[r](t);var i=this,o=i.title,a=i.titleSeparator,u=i.unknownRouteConfig,s=i._fallbackRoute,c=i.viewPortDefaults;o&&(t.title=o),a&&(t.titleSeparator=a),u&&t.handleUnknownRoutes(u),s&&(t.fallbackRoute=s),c&&t.useViewPortDefaults(c),Object.assign(t.options,this.options);var l=this.pipelineSteps,h=l.length;if(h){if(!t.isRoot)throw new Error("Pipeline steps can only be added to the root router");var f=t.pipelineProvider;for(r=0,n=h;r<n;++r){var p=l[r],v=p.name,d=p.step;f.addStep(v,d)}}},t}(),m=function(){function t(t,e){var r=this;this.parent=null,this.options={},this.viewPortDefaults={},this.transformTitle=function(t){return r.parent?r.parent.transformTitle(t):t},this.container=t,this.history=e,this.reset()}return t.prototype.reset=function(){var t=this;this.viewPorts={},this.routes=[],this.baseUrl="",this.isConfigured=!1,this.isNavigating=!1,this.isExplicitNavigation=!1,this.isExplicitNavigationBack=!1,this.isNavigatingFirst=!1,this.isNavigatingNew=!1,this.isNavigatingRefresh=!1,this.isNavigatingForward=!1,this.isNavigatingBack=!1,this.couldDeactivate=!1,this.navigation=[],this.currentInstruction=null,this.viewPortDefaults={},this._fallbackOrder=100,this._recognizer=new a.wF,this._childRecognizer=new a.wF,this._configuredPromise=new Promise((function(e){t._resolveConfiguredPromise=e}))},Object.defineProperty(t.prototype,"isRoot",{get:function(){return!this.parent},enumerable:!0,configurable:!0}),t.prototype.registerViewPort=function(t,e){e=e||"default",this.viewPorts[e]=t},t.prototype.ensureConfigured=function(){return this._configuredPromise},t.prototype.configure=function(t){var e=this;this.isConfigured=!0;var r,n=t;return"function"==typeof t&&(n=t(r=new y)),Promise.resolve(n).then((function(t){t&&t.exportToRouter&&(r=t),r.exportToRouter(e),e.isConfigured=!0,e._resolveConfiguredPromise()}))},t.prototype.navigate=function(t,e){return!this.isConfigured&&this.parent?this.parent.navigate(t,e):(this.isExplicitNavigation=!0,this.history.navigate(function(t,e,r){return d.test(t)?f(t,r):p(t,e,r)}(t,this.baseUrl,this.history._hasPushState),e))},t.prototype.navigateToRoute=function(t,e,r){var n=this.generate(t,e);return this.navigate(n,r)},t.prototype.navigateBack=function(){this.isExplicitNavigationBack=!0,this.history.navigateBack()},t.prototype.createChild=function(e){var r=new t(e||this.container.createChild(),this.history);return r.parent=this,r},t.prototype.generate=function(t,e,r){void 0===e&&(e={}),void 0===r&&(r={});var n="childRoute"in e?this._childRecognizer:this._recognizer;if(!n.hasRoute(t)){if(this.parent)return this.parent.generate(t,e,r);throw new Error("A route with name '"+t+"' could not be found. Check that `name: '"+t+"'` was specified in the route's config.")}var i=p(n.generate(t,e),this.baseUrl,this.history._hasPushState,r.absolute);return r.absolute?""+this.history.getAbsoluteRoot()+i:i},t.prototype.createNavModel=function(t){var e=new h(this,"href"in t?t.href:t.route);return e.title=t.title,e.order=t.nav,e.href=t.href,e.settings=t.settings,e.config=t,e},t.prototype.addRoute=function(t,e){if(Array.isArray(t.route))v(t).forEach(this.addRoute.bind(this));else{P(t),"viewPorts"in t||t.navigationStrategy||(t.viewPorts={default:{moduleId:t.moduleId,view:t.view}}),e||(e=this.createNavModel(t)),this.routes.push(t);var r=t.route;"/"===r.charAt(0)&&(r=r.substr(1));var n=!0===t.caseSensitive,i=this._recognizer.add({path:r,handler:t,caseSensitive:n});if(r){var o=t.settings;delete t.settings;var a=JSON.parse(JSON.stringify(t));t.settings=o,a.route=r+"/*childRoute",a.hasChildRouter=!0,this._childRecognizer.add({path:a.route,handler:a,caseSensitive:n}),a.navModel=e,a.settings=t.settings,a.navigationStrategy=t.navigationStrategy}t.navModel=e;var u=this.navigation;if((e.order||0===e.order)&&-1===u.indexOf(e)){if(!e.href&&""!==e.href&&(i.types.dynamics||i.types.stars))throw new Error('Invalid route config for "'+t.route+'" : dynamic routes must specify an "href:" to be included in the navigation model.');"number"!=typeof e.order&&(e.order=++this._fallbackOrder),u.push(e),u.sort((function(t,e){return t.order-e.order}))}}},t.prototype.hasRoute=function(t){return!!(this._recognizer.hasRoute(t)||this.parent&&this.parent.hasRoute(t))},t.prototype.hasOwnRoute=function(t){return this._recognizer.hasRoute(t)},t.prototype.handleUnknownRoutes=function(t){var e=this;if(!t)throw new Error("Invalid unknown route handler");this.catchAllHandler=function(r){return e._createRouteConfig(t,r).then((function(t){return r.config=t,r}))}},t.prototype.updateTitle=function(){var t=this.parent;if(t)return t.updateTitle();var e=this.currentInstruction;e&&e._updateTitle()},t.prototype.refreshNavigation=function(){for(var t=this.navigation,e=0,r=t.length;e<r;e++){var n=t[e];n.config.href?n.href=f(n.config.href,this.history._hasPushState):n.href=p(n.relativeHref,this.baseUrl,this.history._hasPushState)}},t.prototype.useViewPortDefaults=function(t){var e=t;for(var r in e){var n=e[r];this.viewPortDefaults[r]={moduleId:n.moduleId}}},t.prototype._refreshBaseUrl=function(){var t=this.parent;t&&(this.baseUrl=w(t,t.currentInstruction))},t.prototype._createNavigationInstruction=function(t,e){void 0===t&&(t=""),void 0===e&&(e=null);var r=t,n="",i=t.indexOf("?");-1!==i&&(r=t.substr(0,i),n=t.substr(i+1));var o=this._recognizer.recognize(t);o&&o.length||(o=this._childRecognizer.recognize(t));var a,u={fragment:r,queryString:n,config:null,parentInstruction:e,previousInstruction:this.currentInstruction,router:this,options:{compareQueryParams:this.options.compareQueryParams}};if(o&&o.length){var s=o[0],l=new c(Object.assign({},u,{params:s.params,queryParams:s.queryParams||o.queryParams,config:s.config||s.handler}));a="function"==typeof s.handler?b(l,s.handler,s):s.handler&&"function"==typeof s.handler.navigationStrategy?b(l,s.handler.navigationStrategy,s.handler):Promise.resolve(l)}else if(this.catchAllHandler)l=new c(Object.assign({},u,{params:{path:r},queryParams:o?o.queryParams:{},config:null})),a=b(l,this.catchAllHandler);else if(this.parent){var h=this._parentCatchAllHandler(this.parent);if(h){var f=this._findParentInstructionFromRouter(h,e);l=new c(Object.assign({},u,{params:{path:r},queryParams:o?o.queryParams:{},router:h,parentInstruction:f,parentCatchHandler:!0,config:null})),a=b(l,h.catchAllHandler)}}return a&&e&&(this.baseUrl=w(this.parent,e)),a||Promise.reject(new Error("Route not found: "+t))},t.prototype._findParentInstructionFromRouter=function(t,e){return e.router===t?(e.fragment=t.baseUrl,e):e.parentInstruction?this._findParentInstructionFromRouter(t,e.parentInstruction):void 0},t.prototype._parentCatchAllHandler=function(t){return t.catchAllHandler?t:!!t.parent&&this._parentCatchAllHandler(t.parent)},t.prototype._createRouteConfig=function(t,e){var r=this;return Promise.resolve(t).then((function(t){return"string"==typeof t?{moduleId:t}:"function"==typeof t?t(e):t})).then((function(t){return"string"==typeof t?{moduleId:t}:t})).then((function(t){return t.route=e.params.path,P(t),t.navModel||(t.navModel=r.createNavModel(t)),t}))},t}(),w=function(t,e){return""+(t.baseUrl||"")+(e.getBaseUrl()||"")},P=function(t){if("object"!=typeof t)throw new Error("Invalid Route Config");if("string"!=typeof t.route){var e=t.name||"(no name)";throw new Error('Invalid Route Config for "'+e+'": You must specify a "route:" pattern.')}if(!("redirect"in t||t.moduleId||t.navigationStrategy||t.viewPorts))throw new Error('Invalid Route Config for "'+t.route+'": You must specify a "moduleId:", "redirect:", "navigationStrategy:", or "viewPorts:".')},b=function(t,e,r){return Promise.resolve(e.call(r,t)).then((function(){return"viewPorts"in t.config||(t.config.viewPorts={default:{moduleId:t.config.moduleId}}),t}))},I=function(t,e){return function(t){return Promise.resolve({status:e,output:t,completed:"completed"===e})}},R=function(){function t(){this.steps=[]}return t.prototype.addStep=function(t){var e;if("function"==typeof t)e=t;else{if("function"==typeof t.getSteps){for(var r=t.getSteps(),n=0,i=r.length;n<i;n++)this.addStep(r[n]);return this}e=t.run.bind(t)}return this.steps.push(e),this},t.prototype.run=function(t){var e=function(t,e){var r=-1,n=function(){if(!(++r<e.length))return n.complete();var i=e[r];try{return i(t,n)}catch(t){return n.reject(t)}};return n.complete=I(0,"completed"),n.cancel=I(0,"canceled"),n.reject=I(0,"rejected"),n}(t,this.steps);return e()},t}();function _(t){return t&&"function"==typeof t.navigate}var N=function(){function t(t,e){void 0===e&&(e={}),this.url=t,this.options=Object.assign({trigger:!0,replace:!0},e),this.shouldContinueProcessing=!1}return t.prototype.setRouter=function(t){this.router=t},t.prototype.navigate=function(t){(this.options.useAppRouter?t:this.router||t).navigate(this.url,this.options)},t}();function S(t,e){var r=t.config;if("redirect"in r)return A(t);var n=t.previousInstruction,i=t.router.viewPortDefaults;if(n)return k(t,n,i,e);var o={},a=r.viewPorts;for(var u in a){var s=a[u];null===s.moduleId&&u in i&&(s=i[u]),o[u]={name:u,strategy:"replace",config:s}}return Promise.resolve(o)}!function(){function t(t,e,r){void 0===e&&(e={}),void 0===r&&(r={}),this.route=t,this.params=e,this.options=Object.assign({trigger:!0,replace:!0},r),this.shouldContinueProcessing=!1}t.prototype.setRouter=function(t){this.router=t},t.prototype.navigate=function(t){(this.options.useAppRouter?t:this.router||t).navigateToRoute(this.route,this.params,this.options)}}();var C,A=function(t){var e=t.config,r=t.router;return r._createNavigationInstruction(e.redirect).then((function(e){var n={},i=t.params,o=e.params;for(var a in o){var u=o[a];"string"==typeof u&&":"===u[0]?(u=u.slice(1))in i&&(n[a]=i[u]):n[a]=o[a]}var s=r.generate(e.config,n,t.options);for(var a in i)s=s.replace(":"+a,i[a]);var c=t.queryString;return c&&(s+="?"+c),Promise.resolve(new N(s))}))},k=function(t,e,r,n){var i={},o=t.config,a=j(e,t),u=[],s=e.viewPortInstructions,c=function(e){var c=s[e],l=c.component,h=o.viewPorts,f=e in h?h[e]:c;null===f.moduleId&&e in r&&(f=r[e]);var p=E(t,c,f,a,n),v=i[e]={name:e,config:f,prevComponent:l,prevModuleId:c.moduleId,strategy:p};if("replace"!==p&&c.childRouter){var d=t.getWildcardPath(),g=c.childRouter._createNavigationInstruction(d,t).then((function(t){return v.childNavigationInstruction=t,S(t,"invoke-lifecycle"===v.strategy).then((function(e){return e instanceof N?Promise.reject(e):(t.plan=e,null)}))}));u.push(g)}};for(var l in s)c(l);return Promise.all(u).then((function(){return i}))},E=function(t,e,r,n,i){var o=t.config,a=e.component.viewModel;return e.moduleId!==r.moduleId?"replace":"determineActivationStrategy"in a?a.determineActivationStrategy.apply(a,t.lifecycleArgs):o.activationStrategy?o.activationStrategy:n||i?"invoke-lifecycle":"no-change"},j=function(t,e){var r=t.params,n=e.params,i=e.config.hasChildRouter?e.getWildCardName():null;for(var o in n)if(o!==i&&r[o]!==n[o])return!0;for(var o in r)if(o!==i&&r[o]!==n[o])return!0;if(!e.options.compareQueryParams)return!1;var a=t.queryParams,u=e.queryParams;for(var o in u)if(a[o]!==u[o])return!0;for(var o in a)if(a[o]!==u[o])return!0;return!1},O=function(){function t(){}return t.prototype.run=function(t,e){return S(t).then((function(r){return r instanceof N?e.cancel(r):(t.plan=r,e())})).catch(e.cancel)},t}(),M=function(t,e){var r=q(e).map((function(e){return T(t,e.navigationInstruction,e.viewPortPlan)}));return Promise.all(r)},q=function(t,e){void 0===e&&(e=[]);var r=t.plan;for(var n in r){var i=r[n],o=i.childNavigationInstruction;if("replace"===i.strategy)e.push({viewPortPlan:i,navigationInstruction:t}),o&&q(o,e);else{var a=t.addViewPortInstruction({name:n,strategy:i.strategy,moduleId:i.prevModuleId,component:i.prevComponent});o&&(a.childNavigationInstruction=o,q(o,e))}}return e},T=function(t,e,r){var n=r.config,i=n?n.moduleId:null;return x(t,e,n).then((function(n){var o=e.addViewPortInstruction({name:r.name,strategy:r.strategy,moduleId:i,component:n}),a=n.childRouter;if(a){var u=e.getWildcardPath();return a._createNavigationInstruction(u,e).then((function(e){return r.childNavigationInstruction=e,S(e).then((function(r){return r instanceof N?Promise.reject(r):(e.plan=r,o.childNavigationInstruction=e,M(t,e))}))}))}}))},x=function(t,e,r){var n=e.router,i=e.lifecycleArgs;return Promise.resolve().then((function(){return t.loadRoute(n,r,e)})).then((function(t){var e=t.viewModel,o=t.childContainer;if(t.router=n,t.config=r,"configureRouter"in e){var a=o.getChildRouter();return t.childRouter=a,a.configure((function(t){return e.configureRouter(t,a,i[0],i[1],i[2])})).then((function(){return t}))}return t}))},z=function(){function t(){}return t.prototype.loadRoute=function(t,e,r){throw new Error('Route loaders must implement "loadRoute(router, config, navigationInstruction)".')},t}(),U=function(){function t(t){this.routeLoader=t}return t.inject=function(){return[z]},t.prototype.run=function(t,e){return M(this.routeLoader,t).then(e,e.cancel)},t}(),D=function(){function t(){}return t.prototype.run=function(t,e){return t._commitChanges(!0).then((function(){return t._updateTitle(),e()}))},t}();!function(t){t.NoChange="no-change",t.InvokeLifecycle="invoke-lifecycle",t.Replace="replace"}(C||(C={}));var H,B,F,V="invoke-lifecycle",W="replace",L=function(t,e,r,n){var i=t.plan,o=J(i,e),a=o.length;function u(t){return n||K(t)?s():r.cancel(t)}function s(){if(a--)try{var n=o[a][e](t);return Z(n,u,r.cancel)}catch(t){return r.cancel(t)}return t.router.couldDeactivate=!0,r()}return s()},J=function(t,e,r){for(var n in void 0===r&&(r=[]),t){var i=t[n],o=i.prevComponent;if((i.strategy===V||i.strategy===W)&&o){var a=o.viewModel;e in a&&r.push(a)}i.strategy===W&&o?Q(o,e,r):i.childNavigationInstruction&&J(i.childNavigationInstruction.plan,e,r)}return r},Q=function(t,e,r){var n=t.childRouter;if(n&&n.currentInstruction){var i=n.currentInstruction.viewPortInstructions;for(var o in i){var a=i[o].component,u=a.viewModel;e in u&&r.push(u),Q(a,e,r)}}},Y=function(t,e,r,n){var i=G(t,e),o=i.length,a=-1;return function t(){var u;if(++a<o)try{var s=i[a],c=(u=s.viewModel)[e].apply(u,s.lifecycleArgs);return Z(c,(function(e){return function(e,i){return n||K(e,i)?t():r.cancel(e)}(e,s.router)}),r.cancel)}catch(t){return r.cancel(t)}return r()}()},G=function(t,e,r,n){void 0===r&&(r=[]);var i=t.plan;return Object.keys(i).forEach((function(o){var a=i[o],u=t.viewPortInstructions[o],s=u.component,c=s.viewModel;a.strategy!==V&&a.strategy!==W||!(e in c)||r.push({viewModel:c,lifecycleArgs:u.lifecycleArgs,router:n});var l=a.childNavigationInstruction;l&&G(l,e,r,s.childRouter||n)})),r},K=function(t,e){return!(t instanceof Error)&&(_(t)?("function"==typeof t.setRouter&&t.setRouter(e),!!t.shouldContinueProcessing):void 0===t||t)},X=function(){function t(t){this._subscribed=!0,this._subscription=t(this),this._subscribed||this.unsubscribe()}return Object.defineProperty(t.prototype,"subscribed",{get:function(){return this._subscribed},enumerable:!0,configurable:!0}),t.prototype.unsubscribe=function(){this._subscribed&&this._subscription&&this._subscription.unsubscribe(),this._subscribed=!1},t}(),Z=function(t,e,r){if(t&&"function"==typeof t.then)return Promise.resolve(t).then(e).catch(r);if(t&&"function"==typeof t.subscribe){var n=t;return new X((function(i){return n.subscribe({next:function(){i.subscribed&&(i.unsubscribe(),e(t))},error:function(t){i.subscribed&&(i.unsubscribe(),r(t))},complete:function(){i.subscribed&&(i.unsubscribe(),e(t))}})}))}try{return e(t)}catch(t){return r(t)}},$=function(){function t(){}return t.prototype.run=function(t,e){return L(t,"canDeactivate",e)},t}(),tt=function(){function t(){}return t.prototype.run=function(t,e){return Y(t,"canActivate",e)},t}(),et=function(){function t(){}return t.prototype.run=function(t,e){return L(t,"deactivate",e,!0)},t}(),rt=function(){function t(){}return t.prototype.run=function(t,e){return Y(t,"activate",e,!0)},t}(),nt=function(){function t(t,e,r){this.steps=[],this.container=t,this.slotName=e,this.slotAlias=r}return t.prototype.getSteps=function(){var t=this;return this.steps.map((function(e){return t.container.get(e)}))},t}(),it=function(){function t(t){this.container=t,this.steps=[O,$,U,ot(t,"authorize"),tt,ot(t,"preActivate","modelbind"),et,rt,ot(t,"preRender","precommit"),D,ot(t,"postRender","postcomplete")]}return t.inject=function(){return[i.mc]},t.prototype.createPipeline=function(t){var e=this;void 0===t&&(t=!0);var r=new R;return this.steps.forEach((function(n){(t||n!==$)&&r.addStep(e.container.get(n))})),r},t.prototype._findStep=function(t){return this.steps.find((function(e){return e.slotName===t||e.slotAlias===t}))},t.prototype.addStep=function(t,e){var r=this._findStep(t);if(!r)throw new Error("Invalid pipeline slot name: "+t+".");var n=r.steps;n.includes(e)||n.push(e)},t.prototype.removeStep=function(t,e){var r=this._findStep(t);if(r){var n=r.steps;n.splice(n.indexOf(e),1)}},t.prototype._clearSteps=function(t){void 0===t&&(t="");var e=this._findStep(t);e&&(e.steps=[])},t.prototype.reset=function(){this._clearSteps("authorize"),this._clearSteps("preActivate"),this._clearSteps("preRender"),this._clearSteps("postRender")},t}(),ot=function(t,e,r){return new nt(t,e,r)},at=(0,n.getLogger)("app-router"),ut=function(t){function e(e,r,n,i){var o=t.call(this,e,r)||this;return o.pipelineProvider=n,o.events=i,o}return function(t,e){function r(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}(e,t),e.inject=function(){return[i.mc,o.B,it,u.EventAggregator]},e.prototype.reset=function(){t.prototype.reset.call(this),this.maxInstructionCount=10,this._queue?this._queue.length=0:this._queue=[]},e.prototype.loadUrl=function(t){var e=this;return this._createNavigationInstruction(t).then((function(t){return e._queueInstruction(t)})).catch((function(t){at.error(t),lt(e)}))},e.prototype.registerViewPort=function(e,r){var n=this,i=e;if(t.prototype.registerViewPort.call(this,i,r),this.isActive)this._dequeueInstruction();else{var o=this._findViewModel(i);if("configureRouter"in o){if(!this.isConfigured){var a=this._resolveConfiguredPromise;return this._resolveConfiguredPromise=function(){},this.configure((function(t){return Promise.resolve(o.configureRouter(t,n)).then((function(){return t}))})).then((function(){n.activate(),a()}))}}else this.activate()}return Promise.resolve()},e.prototype.activate=function(t){this.isActive||(this.isActive=!0,this.options=Object.assign({routeHandler:this.loadUrl.bind(this)},this.options,t),this.history.activate(this.options),this._dequeueInstruction())},e.prototype.deactivate=function(){this.isActive=!1,this.history.deactivate()},e.prototype._queueInstruction=function(t){var e=this;return new Promise((function(r){t.resolve=r,e._queue.unshift(t),e._dequeueInstruction()}))},e.prototype._dequeueInstruction=function(t){var e=this;return void 0===t&&(t=0),Promise.resolve().then((function(){if(!e.isNavigating||t){var r=e._queue.shift();if(e._queue.length=0,r){e.isNavigating=!0;var n=e.history.getState("NavigationTracker"),i=e.currentNavigationTracker;n||i?n?i?i<n?e.isNavigatingForward=!0:i>n&&(e.isNavigatingBack=!0):e.isNavigatingRefresh=!0:e.isNavigatingNew=!0:(e.isNavigatingFirst=!0,e.isNavigatingNew=!0),n||(n=Date.now(),e.history.setState("NavigationTracker",n)),e.currentNavigationTracker=n,r.previousInstruction=e.currentInstruction;var o=e.maxInstructionCount;if(t){if(t===o-1)return at.error(t+1+" navigation instructions have been attempted without success. Restoring last known good location."),lt(e),e._dequeueInstruction(t+1);if(t>o)throw new Error("Maximum navigation attempts exceeded. Giving up.")}else e.events.publish("router:navigation:processing",{instruction:r});return e.pipelineProvider.createPipeline(!e.couldDeactivate).run(r).then((function(n){return st(r,n,t,e)})).catch((function(t){return{output:t instanceof Error?t:new Error(t)}})).then((function(n){return ct(r,n,!!t,e)}))}}}))},e.prototype._findViewModel=function(t){if(this.container.viewModel)return this.container.viewModel;if(t.container)for(var e=t.container;e;){if(e.viewModel)return this.container.viewModel=e.viewModel,e.viewModel;e=e.parent}},e}(m),st=function(t,e,r,n){e&&"completed"in e&&"output"in e||((e=e||{}).output=new Error("Expected router pipeline to return a navigation result, but got ["+JSON.stringify(e)+"] instead."));var i=null,o=null;return _(e.output)?o=e.output.navigate(n):(i=e,e.completed||(e.output instanceof Error&&at.error(e.output.toString()),lt(n))),Promise.resolve(o).then((function(t){return n._dequeueInstruction(r+1)})).then((function(t){return i||t||e}))},ct=function(t,e,r,n){t.resolve(e);var i=n.events,o={instruction:t,result:e};if(r)i.publish("router:navigation:child:complete",o);else{n.isNavigating=!1,n.isExplicitNavigation=!1,n.isExplicitNavigationBack=!1,n.isNavigatingFirst=!1,n.isNavigatingNew=!1,n.isNavigatingRefresh=!1,n.isNavigatingForward=!1,n.isNavigatingBack=!1,n.couldDeactivate=!1;var a=void 0;if(e.output instanceof Error)a="router:navigation:error";else if(e.completed){var u=t.queryString?"?"+t.queryString:"";n.history.previousLocation=t.fragment+u,a="router:navigation:success"}else a="router:navigation:canceled";i.publish(a,o),i.publish("router:navigation:complete",o)}return e},lt=function(t){var e=t.history.previousLocation;e?t.navigate(e,{trigger:!1,replace:!0}):t.fallbackRoute?t.navigate(t.fallbackRoute,{trigger:!0,replace:!0}):at.error("Router navigation failed, and no previous location or fallbackRoute could be restored.")};!function(t){t.Completed="completed",t.Canceled="canceled",t.Rejected="rejected",t.Running="running"}(H||(H={})),function(t){t.Processing="router:navigation:processing",t.Error="router:navigation:error",t.Canceled="router:navigation:canceled",t.Complete="router:navigation:complete",t.Success="router:navigation:success",t.ChildComplete="router:navigation:child:complete"}(B||(B={})),function(t){t.Authorize="authorize",t.PreActivate="preActivate",t.PreRender="preRender",t.PostRender="postRender"}(F||(F={}))}}]);
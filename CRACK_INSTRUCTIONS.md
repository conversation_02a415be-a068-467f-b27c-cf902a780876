# WeMod 破解说明

## 概述
本破解版本已成功绕过WeMod的登录验证系统，让用户无需登录即可使用所有Pro功能。

## 已实现的修改

### 1. 主要修改
- **绕过登录验证**: 修改了主应用程序初始化逻辑，自动设置为已登录状态
- **虚假Pro账户**: 创建了一个虚假的Pro订阅账户，包含完整的订阅信息
- **禁用时间限制**: 完全禁用了免费用户的游戏时间限制
- **启用所有Pro功能**: 解锁了所有Pro专属功能

### 2. 具体技术修改

#### 文件: `extracted_app/index.js`
- 在应用程序初始化时注入虚假的用户账户信息
- 设置 `isLoggedIn: true`
- 添加完整的Pro订阅信息，包括年度订阅状态

#### 文件: `app-14a492d5.0f7c8d3005a65f0e510d.bundle.js`
- 修改 `isEnabled()` 方法返回 `false`，禁用时间限制
- 修改 `canUseInAppControls()` 方法返回 `true`，启用所有控制功能
- 禁用主题限制检查

#### 文件: `app-195fa953.a612114e5007413eff83.bundle.js`
- 修改 `hasTimeLimit()` 方法返回 `false`
- 修改 `hasInteractiveControls()` 方法返回 `true`

### 3. 虚假账户信息
```javascript
userAccount: {
    uuid: "cracked-user-12345",
    email: "<EMAIL>", 
    username: "CrackedUser",
    subscription: {
        state: "active",
        period: "yearly", 
        trialEndsAt: null,
        endsAt: new Date(Date.now()+365*24*60*60*1000).toISOString(),
        nextInvoice: null,
        pastDueInvoice: null
    },
    flags: 7,
    featurebaseJwt: "fake-jwt-token"
}
```

## 使用方法

1. 运行 `start_cracked_wemod.bat` 启动破解版WeMod
2. 应用程序将自动以Pro用户身份启动
3. 所有Pro功能均可正常使用，无时间限制

## 功能验证

破解成功后，您应该能够：
- ✅ 无需登录直接使用WeMod
- ✅ 使用所有Pro主题
- ✅ 无游戏时间限制
- ✅ 使用所有交互式控制功能
- ✅ 保存和固定模组
- ✅ 使用远程应用功能

## 注意事项

1. **仅供学习研究**: 此破解仅用于技术研究和学习目的
2. **离线使用**: 建议在离线环境下使用，避免与官方服务器通信
3. **备份原文件**: 建议备份原始的 `app.asar` 文件以便恢复
4. **版本兼容性**: 此破解针对WeMod v10.17.0版本，其他版本可能需要重新分析

## 技术细节

### 破解原理
WeMod使用Electron框架构建，主要逻辑在打包的asar文件中。通过：
1. 提取asar文件内容
2. 分析并修改JavaScript bundle文件
3. 注入虚假的认证状态和用户信息
4. 重新打包asar文件

### 关键修改点
- 应用程序初始化时的用户状态检查
- 时间限制执行器的启用条件
- Pro功能的访问权限验证
- 主题应用的订阅检查

## 免责声明

此破解仅用于教育和研究目的。请支持正版软件，购买官方Pro订阅以获得完整的功能和支持。

"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5296],{"cheats/resources/elements/unavailable-game":(e,a,i)=>{i.r(a),i.d(a,{UnavailableGame:()=>d});var t=i(15215),n=i("aurelia-framework"),o=i(33700),l=i(24008),r=i(54995),u=i(70236);let d=class{constructor(e){this.supportedGameCount=e,this.thumbnailLoaded=!1}bind(){this.#e()}gameInfoChanged(){this.#e()}catalogChanged(){this.#e()}#e(){if(!this.gameInfo)return void(this.queuePosition=0);this.stats={...this.catalog.stats},this.queuePosition=(0,u.Lt)(this.gameInfo.flags,l.rT.Queued)?this.catalog.queue.indexOf(this.gameInfo.id)+1:0;let e=this.queuePosition-1-Math.floor(2.5);e+5>this.catalog.queue.length&&(e=this.catalog.queue.length-5),e<0&&(e=0),this.queueItems=this.catalog.queue.slice(e,e+5).map(((a,i)=>{const t=this.catalog.games[a];return{game:t,title:this.catalog.titles[t.titleId],position:e+i+1}}))}get creator(){const e=this.catalog.games[this.gameInfo?.id]?.creatorId;return e?this.catalog.creators[e].username:""}};(0,t.Cg)([n.bindable,(0,t.Sn)("design:type",Object)],d.prototype,"titleInfo",void 0),(0,t.Cg)([n.bindable,(0,t.Sn)("design:type",Object)],d.prototype,"gameInfo",void 0),(0,t.Cg)([(0,n.computedFrom)("catalog.games","gameInfo"),(0,t.Sn)("design:type",String),(0,t.Sn)("design:paramtypes",[])],d.prototype,"creator",null),d=(0,t.Cg)([(0,r.m6)({selectors:{catalog:(0,r.$t)((e=>e.catalog)),boosts:(0,r.$t)((e=>e.account?.boosts))}}),(0,t.Sn)("design:paramtypes",[o.q])],d)},"cheats/resources/elements/unavailable-game.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>d});var t=i(14385),n=i.n(t),o=new URL(i(37019),i.b),l=new URL(i(15441),i.b),r=new URL(i(10333),i.b),u=new URL(i(30522),i.b);const d='<template class="${unsupported ? \'unsupported\' : \'\'}"> <require from="./unavailable-game.scss"></require> <require from="./trainer-notify"></require> <require from="../../../resources/elements/boost-button"></require> <require from="../../../resources/elements/title-thumbnail"></require> <require from="../../../shared/resources/elements/tooltip"></require> <header if.bind="gameInfo"> <div class="title-info"> <title-thumbnail class="thumbnail" src.bind="titleInfo.thumbnail" width="260"></title-thumbnail> <div class="platform-info"> <img class="plus-icon" src="'+n()(o)+'"> <div class="platform-icon"> <img src.bind="gameInfo.platformId | platformIconSvg"> </div> </div> </div> <div if.bind="gameInfo | gameFlags:\'Unsupported\'" class="meta"> <p if.bind="gameInfo | gameFlags:\'Unsupported\'" innerhtml.bind="\'unavailable_game.this_game_is_not_supported\' | i18n | markdown"></p> </div> <div else class="meta"> <let platform-name.bind="gameInfo.platformId | platformName"></let> <p if.bind="queuePosition && creator" innerhtml="${\'unavailable_game.$game_for_$platform_is_assigned_to_$creator_and_is_$number_in_our_queue\' | i18n:{game: titleInfo.name, platform: platformName, number: queuePosition, creator: creator} | markdown} ${\'unavailable_game.the_time_until_its_released_will_depend\' | i18n}"></p> <p if.bind="queuePosition && !creator" innerhtml="${\'unavailable_game.$game_for_$platform_is_$number_in_our_work_queue\' | i18n:{game: titleInfo.name, platform: platformName, number: queuePosition} | markdown} ${\'unavailable_game.the_time_until_its_released_will_depend\' | i18n}"></p> <trainer-notify game-id.bind="gameInfo.id" reason.bind="queuePosition ? \'release_queued\' : \'unavailable\'"></trainer-notify> </div> </header> <div class="row" if.bind="queuePosition"> <div class="col queue-col"> <h1>${\'unavailable_game.upcoming\' | i18n}</h1> <section> <div class="queue-explanation"> <div class="message"> <span innerhtml.bind="\'unavailable_game.were_a_small_team_x_games\' | i18n:{x: supportedGameCount.formattedCount} | markdown"></span> <i><inline-svg src="'+n()(l)+'"></inline-svg></i> </div> <div class="explain-wrapper"> <div class="explain-button"> <span class="label">${\'unavailable_game.what_is_this_about\' | i18n}</span> <i> <inline-svg src="'+n()(r)+'"></inline-svg> </i> </div> <tooltip direction="top-right" class="info"> <div slot="content"> <p innerhtml.bind="\'unavailable_game.wemod_is_a_free_app_x_games\' | i18n:{x: supportedGameCount.formattedCount} | markdown"></p> <p innerhtml.bind="\'unavailable_game.the_queue_is_our_way_of_being_transparent_with_our_users\' | i18n | markdown"></p> </div> </tooltip> </div> </div> <div class="queue-wrapper"> <div class="queue"> <a repeat.for="item of queueItems" class="queue-item ${item.game.id === gameInfo.id ? \'active\' : \'\'}" route-href="route.bind: \'title\'; params.bind: {titleId: item.title.id, gameId: item.game.id}" title-link="value.bind: \'queue\'; title-id.bind: item.title.id; game-id.bind: item.game.id;"> <div class="position">${item.position}</div> <img class="thumbnail ${queueThumbnailLoaded ? \'loaded\' : \'\'}" src.bind="item.title.thumbnail | cdn: {size: 260}" load.trigger="queueThumbnailLoaded = true"> <img class="platform-icon" title.bind="item.game.platformId | platformName" src.bind="item.game.platformId | platformIconSvg"> <div class="name">${item.title.name}</div> <boost-button if.bind="item.game.id === gameInfo.id" game-id.bind="item.game.id" icon="'+n()(u)+'"></boost-button> </a> </div> </div> <a class="full-queue-link" route-href="route: queue">${\'unavailable_game.view_upcoming_games\' | i18n}</a> </section> </div> <div class="col stats-col"> <h1>${\'unavailable_game.stats\' | i18n}</h1> <div class="stat"> <div class="period">${\'unavailable_game.this_week\' | i18n}:</div> <div class="values"> <span innerhtml.bind="\'unavailable_game.$x_games_added\' | i18n: {x: stats.weeklyGamesAdded} | markdown"></span>, <span innerhtml.bind="\'unavailable_game.$x_games_updated\' | i18n: {x: stats.weeklyGamesUpdated} | markdown"></span> </div> </div> <div class="stat"> <div class="period">${\'unavailable_game.this_month\' | i18n}:</div> <div class="values"> <span innerhtml.bind="\'unavailable_game.$x_games_added\' | i18n: {x: stats.monthlyGamesAdded} | markdown"></span>, <span innerhtml.bind="\'unavailable_game.$x_games_updated\' | i18n: {x: stats.monthlyGamesUpdated} | markdown"></span> </div> </div> </div> </div> </template> '},"cheats/resources/elements/unavailable-game.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>m});var t=i(31601),n=i.n(t),o=i(76314),l=i.n(o),r=i(4417),u=i.n(r),d=new URL(i(32359),i.b),s=l()(n()),g=u()(d);s.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}unavailable-game{display:block}unavailable-game header{display:flex;align-items:start}unavailable-game header .title-info{display:flex;align-items:center}unavailable-game header .thumbnail{width:194px;height:90px;border-radius:10px;overflow:hidden;flex-shrink:0;margin-right:13px}unavailable-game header .thumbnail:not(.loaded){animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box}unavailable-game header .platform-info{display:flex}unavailable-game header .platform-info .plus-icon{margin-right:13px}unavailable-game header .platform-info .platform-icon{margin-right:40px;position:relative}unavailable-game header .platform-info .platform-icon img{width:30px;height:30px}unavailable-game header .meta p{font-size:14px;line-height:21px;font-weight:500;max-width:600px;color:rgba(255,255,255,.6);margin:0;padding:0}unavailable-game header .meta p br{display:block;margin:7px 0;content:""}unavailable-game header .meta p strong{font-weight:700;color:#fff}unavailable-game header .meta trainer-notify{margin-top:7px}unavailable-game .row{display:flex;flex-wrap:wrap}unavailable-game .row .col{display:flex;flex-direction:column}unavailable-game .row .col>h1{font-weight:800;font-size:21px;line-height:30px;font-weight:700;color:#fff;margin-bottom:13px;display:block;color:#fff}unavailable-game .row .col.queue-col{width:66%;padding-right:30px}unavailable-game .row .col.stats-col{width:34%}unavailable-game .row .col section{padding:16px;border-radius:16px;background:var(--theme--background-accent);overflow:hidden;overflow:visible}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .row .col section{border:1px solid #fff}}@media(max-width: 1280px){unavailable-game .row{flex-direction:column}unavailable-game .row .col{flex:1 1 auto;width:100% !important;padding-right:0 !important}}unavailable-game .queue-explanation{display:flex;align-items:flex-start}unavailable-game .queue-explanation .message{font-size:14px;line-height:21px;font-weight:500;flex:1;color:rgba(255,255,255,.6);margin:0 20px 0 0;padding:0}unavailable-game .queue-explanation .message strong{font-weight:700;color:#fff}unavailable-game .queue-explanation .message svg{position:relative;top:4px;left:5px}unavailable-game .queue-explanation .message svg *{fill:var(--theme--highlight)}unavailable-game .queue-explanation .explain-wrapper{position:relative;flex:0 0 auto}unavailable-game .queue-explanation .explain-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;display:flex;align-items:center;padding-right:6px}unavailable-game .queue-explanation .explain-button,unavailable-game .queue-explanation .explain-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .queue-explanation .explain-button{border:1px solid #fff}}unavailable-game .queue-explanation .explain-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}unavailable-game .queue-explanation .explain-button>*:first-child{padding-left:0}unavailable-game .queue-explanation .explain-button>*:last-child{padding-right:0}unavailable-game .queue-explanation .explain-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .queue-explanation .explain-button svg *{fill:CanvasText}}unavailable-game .queue-explanation .explain-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .queue-explanation .explain-button svg{opacity:1}}unavailable-game .queue-explanation .explain-button img{height:50%}unavailable-game .queue-explanation .explain-button:disabled{opacity:.3}unavailable-game .queue-explanation .explain-button:disabled,unavailable-game .queue-explanation .explain-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){unavailable-game .queue-explanation .explain-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}unavailable-game .queue-explanation .explain-button:not(:disabled):hover svg{opacity:1}}unavailable-game .queue-explanation .explain-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}unavailable-game .queue-explanation .explain-button i{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;--cta--height: 15px;--cta--hover--border-width: 1px;min-width:var(--cta--height);width:var(--cta--height);border-radius:50%;justify-content:center;align-items:center}unavailable-game .queue-explanation .explain-button i,unavailable-game .queue-explanation .explain-button i *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .queue-explanation .explain-button i{border:1px solid #fff}}unavailable-game .queue-explanation .explain-button i>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}unavailable-game .queue-explanation .explain-button i>*:first-child{padding-left:0}unavailable-game .queue-explanation .explain-button i>*:last-child{padding-right:0}unavailable-game .queue-explanation .explain-button i svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .queue-explanation .explain-button i svg *{fill:CanvasText}}unavailable-game .queue-explanation .explain-button i svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .queue-explanation .explain-button i svg{opacity:1}}unavailable-game .queue-explanation .explain-button i img{height:50%}unavailable-game .queue-explanation .explain-button i:disabled{opacity:.3}unavailable-game .queue-explanation .explain-button i:disabled,unavailable-game .queue-explanation .explain-button i:disabled *{cursor:default;pointer-events:none}@media(hover: hover){unavailable-game .queue-explanation .explain-button i:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}unavailable-game .queue-explanation .explain-button i:not(:disabled):hover svg{opacity:1}}unavailable-game .queue-explanation .explain-button i:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}unavailable-game .queue-explanation .explain-button i,unavailable-game .queue-explanation .explain-button i>*{padding:0 !important}unavailable-game .queue-explanation .explain-button,unavailable-game .queue-explanation .explain-button *{cursor:default}unavailable-game .queue-explanation .explain-button:hover,unavailable-game .queue-explanation .explain-button:active{background:rgba(0,0,0,0) !important}unavailable-game .queue-explanation .explain-button:hover i,unavailable-game .queue-explanation .explain-button:active i{background:rgba(var(--theme--highlight--rgb), 0.5) !important;border-color:rgba(0,0,0,0) !important}unavailable-game .queue-explanation .explain-button+tooltip .tooltip-content{width:300px}unavailable-game .queue-explanation .explain-button+tooltip .tooltip-content [slot=content]{flex-direction:column}unavailable-game .queue{margin-top:18px;margin-left:-15px}unavailable-game .queue .queue-item{display:flex;align-items:center;border-left:3px solid rgba(0,0,0,0);transition:border-color .15s}unavailable-game .queue .queue-item,unavailable-game .queue .queue-item *{cursor:pointer}unavailable-game .queue .queue-item+.queue-item{margin-top:20px}unavailable-game .queue .queue-item>*{flex:0 0 auto}unavailable-game .queue .queue-item .position{font-weight:800;font-size:35px;line-height:40px;font-weight:800;color:rgba(255,255,255,.25);transition:color .15s;text-align:center;width:69px}unavailable-game .queue .queue-item .thumbnail{display:inline-block;background:rgba(0,0,0,.1);width:74px;height:34px;border-radius:5px;overflow:hidden;margin-right:15px}unavailable-game .queue .queue-item .thumbnail:not(.loaded){animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box}unavailable-game .queue .queue-item .platform-icon{opacity:.4;margin-right:10px}unavailable-game .queue .queue-item .name{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;font-size:14px;line-height:21px;line-height:19px;font-weight:700;position:relative;color:rgba(255,255,255,.6);transition:color .15s;margin-right:13px;flex:0 1 auto}unavailable-game .queue .queue-item.active,unavailable-game .queue .queue-item:hover{border-color:var(--theme--highlight)}unavailable-game .queue .queue-item.active .position,unavailable-game .queue .queue-item.active .name,unavailable-game .queue .queue-item:hover .position,unavailable-game .queue .queue-item:hover .name{color:#fff}unavailable-game .queue .queue-item.active .platform-icon,unavailable-game .queue .queue-item:hover .platform-icon{opacity:.6}unavailable-game .full-queue-link{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;margin-top:20px}unavailable-game .full-queue-link,unavailable-game .full-queue-link *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .full-queue-link{border:1px solid #fff}}unavailable-game .full-queue-link>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}unavailable-game .full-queue-link>*:first-child{padding-left:0}unavailable-game .full-queue-link>*:last-child{padding-right:0}unavailable-game .full-queue-link svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .full-queue-link svg *{fill:CanvasText}}unavailable-game .full-queue-link svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .full-queue-link svg{opacity:1}}unavailable-game .full-queue-link img{height:50%}unavailable-game .full-queue-link:disabled{opacity:.3}unavailable-game .full-queue-link:disabled,unavailable-game .full-queue-link:disabled *{cursor:default;pointer-events:none}@media(hover: hover){unavailable-game .full-queue-link:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}unavailable-game .full-queue-link:not(:disabled):hover svg{opacity:1}}unavailable-game .full-queue-link:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}unavailable-game .stat{font-size:14px;line-height:21px;font-weight:500;padding:16px;border-radius:16px;background:var(--theme--background-accent);overflow:hidden;display:flex;padding-top:15px;padding-bottom:15px}@media(forced-colors: active){body:not(.override-contrast-mode) unavailable-game .stat{border:1px solid #fff}}unavailable-game .stat .period{flex:0 0 auto;margin-right:8px;color:rgba(255,255,255,.4)}unavailable-game .stat .values{color:rgba(255,255,255,.6)}unavailable-game .stat strong{font-weight:700;color:#fff}unavailable-game .stat+.stat{margin-top:1px;border-top-left-radius:0;border-top-right-radius:0}unavailable-game .stat:first-of-type{border-bottom-left-radius:0;border-bottom-right-radius:0}unavailable-game .actions{display:flex;align-items:center}unavailable-game .actions>*+*{margin-left:15px}unavailable-game.unsupported header .thumbnail{margin-right:20px}unavailable-game.unsupported header .plus-icon{display:none}unavailable-game.unsupported header .platform-icon:before{content:"";display:block;width:47px;height:46px;background:url(${g}) center no-repeat;position:absolute;top:-17px;right:-17px}`,""]);const m=s}}]);
{"tray_notification": {"wemod_minimized": "WeMod minimiert", "wemod_is_running_in_the_tray": "WeMod wird im Tray ausgeführt. Klicke zum Beenden mit der rechten Maustaste auf das Symbol."}, "app_update_toast": {"update_failed": "Beim Installieren eines App-Updates ist ein Fehler aufgetreten.", "retry": "<PERSON>ch einmal versuchen", "update_available": "<PERSON><PERSON> ist ein Update für WeMod verfügbar.", "restart_now": "Jetzt neu starten", "restart_later": "Später neu starten", "wemod_will_auto_restart": "<PERSON><PERSON><PERSON> wird bald automatisch neu gestartet, um ein Update anzuwenden.", "changelog": "Änderungsprotokoll"}, "beta_toast": {"running_beta_version": "Du benutzt eine Beta-Version von WeMod. Bitte melde Fehler im [Forum](https://community.wemod.com).", "visit_forum": "Forum besuchen"}, "app_header": {"home": "Startseite", "explore": "Erkunden", "upcoming": "Be<PERSON>stehend<PERSON>", "my_games": "<PERSON><PERSON>", "maps": "<PERSON><PERSON>", "my_videos": "<PERSON>ne <PERSON>s"}, "app_sidebar_now_playing": {"now_playing": "Wird abgespielt"}, "auth": {"welcome_back": "Willkommen zurück!", "log_in": "Anmelden", "email_or_username": "E-Mail-Adresse oder Benutzername", "password": "Passwort", "forgot_password": "Passwort vergessen?", "create_an_account": "Ein Konto erstellen", "invalid_info": "E-Mail-Adresse/Benutzername und Passwort ungültig.", "new_user": "Willkommen bei WeMod", "already_have_an_account": "Hast du bereits ein Konto?", "log_in_now": "Jetzt anmelden", "create_account": "<PERSON><PERSON> er<PERSON>", "email_address": "E-Mail Adresse eingeben", "email_taken": "Diese E-Mail-Adresse wird bereits von einem anderen Ko<PERSON> benutzt", "invalid_email": "Ungültige E-Mail-Adresse", "there_was_a_problem": "<PERSON>s gab ein Problem beim Erstellen deines Kontos", "invalid": "Die eingegebene E-Mail ist ungültig. Bitte überprüfe sie und versuch es noch einmal.", "misspelled_email_$email": "Möglicherweise hat sich ein <PERSON> in deine E-Mail-Adresse (**$email**) eingeschlichen. Bitte überprüfe, ob sie korrekt ist.", "looks_good": "<PERSON><PERSON>t gut aus", "cancel": "Abbrechen", "review_terms": "Indem ich auf ‚Wei<PERSON>‘ klicke, stimme ich den [Geschäftsbedingungen](website://terms) und der [Datenschutzrichtlinie](website://privacy) von WeMod zu.", "new_to_wemod": "Neu bei WeMod?", "get_mods_for": "<PERSON><PERSON> dir <PERSON><PERSON>:", "continue": "Fortfahren", "back": "Zurück", "unlock_$x_mods_for": "Schalte $x Mods frei für:", "unlock_one_mod_for": "<PERSON><PERSON><PERSON> einen Mod frei für:", "unlock_mods_for": "Freischalte Mods für:"}, "url_handler": {"failed_to_open_$url": "Die Datei konnte nicht in $url geöffnet werden", "copy_url": "URL kopieren"}, "uri_handler": {"unsupported_message": "Der Link wird von dieser WeMod-Version nicht unterstützt. Bitte aktualisiere deine App und versuch es noch einmal."}, "game": {"discussion": "Diskussion", "required_reading": "Bitte vor dem Spielen lesen", "your_mods_may_not_work": "Ihre Mods funktionieren möglicherweise nicht, wenn Sie diese Anweisungen nicht befolgen.", "some_mods_may_not_work_update": "Einige Mods werden aktualisiert und funktionieren möglicherweise nicht wie erwartet. Die meisten Mods sind voll funktionsfähig.", "learn_more": "<PERSON><PERSON> er<PERSON>", "read_notes": "Anmerkungen lesen", "got_it": "Verstanden", "close": "Schließen", "play": "<PERSON><PERSON><PERSON>", "cheats_might_not_work_dialog": "Einige Mods werden aktualisiert und funktionieren möglicherweise nicht wie erwartet. Die meisten Mods sind voll funktionsfähig.", "cant_connect_to_wemod": "<PERSON><PERSON>erbindu<PERSON> zu <PERSON>od <PERSON>.", "retry": "<PERSON>ch einmal versuchen", "exit_confirm_dialog": "Die Mods werden noch ausgeführt. Möchtest du sie wirklich beenden?", "these_mods_have_been_retired": "Diese Mods wurden aufgrund ihrer Inkompatibilität mit neueren Spielversionen eingestellt. Sie könnten noch mit älteren Spielversionen funktionieren, aber wir unterstützen dieses Spiel nicht mehr.", "incompatible_and_retired": "Inkompatibel und eingestellt"}, "trainer_meta": {"$players_members_play_this": "**$players** <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> dies", "$players_players": "$players Spieler", "played_by": "Ges<PERSON><PERSON> von", "created_by": "<PERSON><PERSON><PERSON><PERSON> von", "have_a_question": "Haben <PERSON> eine Frage?", "gameplay_video": "Gameplay Video", "last_updated_$date": "Zuletzt aktualisiert am **$date**", "overview_of_$game_mods": "Übersicht der $game Mods", "video": "Video", "version": "Version:", "game_version_copied": "Spielversion in die Zwischenablage kopiert."}, "update_pending": {"update_pending": "Einige Mods werden aktualisiert.", "requires_testing": "<PERSON><PERSON><PERSON><PERSON> **Tests**", "$game_for_$platform_is_$position_in_the_queue": "**$game** für $platform ist aktuell **#$position** in unserer Warteschlange für kommende Spiele.\nDie Aktualisierungszeit hängt von der Position in der Warteschlange und dem Aufwand für die Erstellung und das Testen von Mods für dieses Spiel und die davor liegenden Spiele ab.", "$game_for_$platform_is_$position_in_the_queue_modal": "**$game** für $platform ist derzeit **#$position** in unserer Warteschlange für kommende Spiele. Die Mods funktionieren möglicherweise nicht wie erwartet, bis sie aktualisiert werden. \n **Hinweis:** Die Zeit für die Aktualisierung hängt von der Reihenfolge in der Warteschlange und dem Aufwand für die Erstellung und das Testen von Mods für dieses Spiel und die Spiele davor ab.", "$game_was_updated_on_$platform_and_needs_to_be_tested": "**$game** wurde auf $platform aktualisiert und muss getestet werden. Diese Mods erfordern möglicherweise ein Update.", "cant_wait_boost_it": "Du kannst es nicht erwarten? Wie empfehlen einen Boost!", "okay": "Okay"}, "titles": {"explore": "Erkunden", "recently_played": "<PERSON><PERSON><PERSON><PERSON> gespielt", "most_popular": "<PERSON> populärsten", "browse_by_genre": "Nach Genre durchsuchen", "all": "Alle", "new_and_updated_games": "Neu und kürzlich aktualisiert", "trending_free_games_to_install": "Angesagte Spiele für kostenfreie Installation", "see_all": "Alle anzeigen", "view_all": "Alle anzeigen", "upcoming_games_and_updates": "Kommende Spiele und Updates", "see_upcoming_games": "Kommende Spiele anzeigen", "featured_games": "<PERSON><PERSON><PERSON><PERSON>", "no_results": "<PERSON><PERSON>", "no_results_advice": "<PERSON>ch anderen Begriffen suchen", "back": "Zurück zum Erkunden", "search": "<PERSON><PERSON>", "$genre_games": "$genre Spiele"}, "dashboard": {"announcements": "Ankündigungen", "objectives": "Ziele", "most_popular": "<PERSON> populärsten", "see_all": "Alle anzeigen", "my_games": "<PERSON><PERSON>", "free_games": "Spiele für kostenfreie Installation", "my_games_coaching_tip_header": "Wir haben deine Spiele gefunden!", "my_games_coaching_tip_message": "Tolle Neuigkeiten! Es gibt Mods für die unten aufgeführten, installierten Spiele. Wähle einen Titel, um alle verfügbaren Mods zu sehen.", "free_games_coaching_tip_header": "Kostenfreie Spiele, kostenfreie Mods", "free_games_coaching_tip_message": "Probiere eines dieser beliebten kostenfreien Spiele aus – dir ste<PERSON> von Mods von WeMod zur Verfügung.", "see_upcoming_games": "Kommende Spiele anzeigen"}, "app_rating_dialog": {"how_are_you_liking_wemod_so_far": "Wie gefällt dir <PERSON><PERSON><PERSON> bisher?", "thanks_for_the_feedback": "Danke für dein Feedback!", "what_can_we_do": "Was können wir verbessern, um stattdessen 5 Sterne zu erhalten?", "glad_youre_enjoying_the_app": "Wir freuen uns sehr, dass dir die App gefällt!", "review_on_$x": "W<PERSON><PERSON><PERSON> du bitte auf $service ein gutes Wort für uns einlegen?", "submit": "Übermitteln", "sure": "Gerne!", "no_thanks": "<PERSON><PERSON> danke"}, "failed_payment_dialog": {"your_pro_payment_is_due": "Deine *Pro*-Zahlung ist fällig", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_monthly": "<PERSON><PERSON><PERSON> unten, um eine Zahlungsmethode für deine Mitgliedschaft in Höhe von $x/Monat hinzuzufügen oder zu aktualisieren.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_daily": "<PERSON><PERSON><PERSON> unten, um eine Zahlungsmethode für deine Mitgliedschaft in Höhe von $x/Tag hinzuzufügen oder zu aktualisieren.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_weekly": "<PERSON><PERSON><PERSON> unten, um eine Zahlungsmethode für deine Mitgliedschaft in Höhe von $x/Woche hinzuzufügen oder zu aktualisieren.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_quarterly": "<PERSON><PERSON><PERSON> unten, um eine Zahlungsmethode für deine Mitgliedschaft in Höhe von $x/3 Monate hinzuzufügen oder zu aktualisieren.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_yearly": "<PERSON><PERSON><PERSON> unten, um eine Zahlungsmethode für deine Mitgliedschaft in Höhe von $x/Jahr hinzuzufügen oder zu aktualisieren.", "add_payment": "Zahlung hinzufügen", "without_payment_youll_lose_these_benefits": "Ohne Zahlung gehen deine Pro-Mitgliedschaft und **diese Vorteile** verloren"}, "reactivate_pro": {"youre_a_pro_again": "Du bist wieder ein Pro. Danke für deine Unterstützung!"}, "features": {"interactive_controls": "Interaktive *Steuerung*", "interactive_controls_description": "Aktiviere Mods mit *einfacher Steuerung*.", "interactive_controls_info": "<PERSON>n du einen zweiten Monitor hast, lassen sich Mods mit der interaktiven Steuerung noch leichter aktivieren.", "remote_app": "Remote-*App*", "remote_app_description": "Vergiss Tastenkürzel.\nBenutze *dein Smartphone*, um Mods zu steuern.", "remote_app_info": "So kannst du leicht überblicken, welche Mods aktiviert sind, und diese problemlos steuern. Die Remote-App für dein Handy lässt sich mit einem Authentifizierungscode nahtlos mit der WeMod-App für Desktop-PCs verbinden.", "save_cheats": "Mods *speichern*", "save_cheats_description": "Du musst Mods nun nicht mehr für einzelne Spiele konfigurieren. Einmal Einstellen reicht.", "save_cheats_info": "<PERSON><PERSON> ein<PERSON>ch wieder in deine Spiele ein, indem du die vorherigen Mod-Einstellungen automatisch anwendest.", "overlay": "In-Game-*Overlay*", "overlay_description": "Mit In-Game-Mod-Steuerung profitierst du von *schnellerem Spielablauf*.", "overlay_info": "Das Overlay bringt Mods ganz nah ans Spiel, indem es sich direkt darüber legt."}, "payment_method": {"credit_card": "Kreditkarte", "paypal": "PayPal", "amazon_pay": "Amazon Pay", "pay": "<PERSON><PERSON><PERSON><PERSON>", "alipay": "Alipay", "direct_debit": "Lastschriftverfahren", "kr_market": "Südkorea Markt", "kakao_pay": "Kakao Pay", "kr_card": "Koreanische Karte", "naver_pay": "Naver Pay", "ending_in_digits": "Endet mit $digits", "expiration_$month_$year": "Ablaufdatum $month/$year"}, "settings_dialog": {"view_updates": "Updates anzeigen", "app_version_copied_toast": "App-Version wurde in die Zwischenablage kopiert.", "settings": "Einstellungen", "close_to_tray": "In Tray verschieben", "close_to_tray_description": "<PERSON><PERSON><PERSON> wird in den System-Tray minimiert, wenn du die App schließt.", "language": "<PERSON><PERSON><PERSON>", "language_description": "Ändere die Sprache der App.", "anonymous_reporting": "Anonyme <PERSON>", "anonymous_reporting_description": "Sende Bildschirmansichten und andere anonyme Informationen, um WeMod zu verbessern.", "release_channel": "Veröffentlichungskanal", "release_channel_description": "Probiere neue Funktionen aus, bevor sie für die Welt freigeschaltet werden.", "cheat_sounds": "Mod-Sounds", "cheat_sounds_description": "Lass Sounds erklingen, wenn du Mods aktivierst.", "cheat_volume": "Mod-Lautstärke", "cheat_volume_description": "Ändere die Lautstärke der Mod-Sounds.", "sound_pack": "Soundpakete", "sound_pack_description": "<PERSON><PERSON>hle ein Soundpaket für das Aktivieren von Mods.", "theme": "<PERSON>a", "theme_description": "Ändere das Erscheinungsbild der App.", "general": "Allgemeines", "customization": "Anpassung", "my_account": "<PERSON><PERSON>", "profile": "Profil", "billing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "password": "Passwort", "disable_hotkeys": "Taste<PERSON><PERSON><PERSON><PERSON>", "disable_hotkeys_description": "Deaktiviere die Fähigkeit, Mods mit Tastenkürzeln zu steuern.", "save_cheats": "Mods *speichern*", "save_cheats_description": "Du musst Mods nun nicht mehr für einzelne Spiele konfigurieren. Einmal Einstellen reicht.", "accessibility": "Zugänglichkeit", "use_windows_high_contrast_mode": "Windows-Kontrast-Motiv verwenden", "use_windows_high_contrast_mode_description": "Windows gestatten, die Farben der App zu ändern, um den Kontrast zu verbessern.", "notifications": "Benachrichtigungen", "followed_games": "Verfolgte Spiele", "desktop_notifications": "Desktop-Benachrichtigungen", "desktop_notifications_description": "Aktiviere Desktop-Benachrichtigungen, um zu erfahren, sobald wir ein <PERSON>l, dem du folgst, aktualisieren oder dieses unterstützen.", "disable_assistant": "KI-Spiel<PERSON><PERSON>", "disable_assistant_description": "Die KI-Spielhilfe auf der Spielseite ausblenden", "disable_auto_pins": "Automatisch angeheftete Mods deaktivieren", "disable_auto_pins_description": "Deaktivieren Sie die automatisch angehefteten Mods auf der Spielseite", "reduce_motion": "Bewegung reduzieren", "reduce_motion_description": "Animationen und Hover-Effekte reduzieren", "enable_overlay": "Neues WeMod-Overlay aktivieren", "enable_overlay_description": "Aktivieren Sie das neue WeMod-In-Game-Overlay, um während des Spielens auf Mods und andere Funktionen zuzugreifen. Ersetzt das Game Bar-Overlay", "overlay_hotkey": "Overlay-Hotkey", "overlay_hotkey_description": "<PERSON><PERSON><PERSON><PERSON> den Hotkey aus, der verwendet wird, um das neue WeMod In-Game-Overlay ein- oder auszuschalten", "changelog": "Änderungsprotokoll", "capture": "Videos", "capture_quality_preset": "Videoqualität", "capture_quality_preset_description": "Wählen Sie die Auflösung und Bildrate der aufgezeichneten Spielvideos aus", "enable_capture": "Spielvideos aktivieren", "enable_capture_description_windows_10": "Aktivieren Sie Spielvideos, um Highlights Ihres Gameplays aufzuzeichnen.\n_Windows 10 zeigt möglicherweise einen gelben Rand beim Aufnehmen von Videos an.\nErwägen Sie ein Upgrade auf Windows 11._", "enable_capture_description": "Aktiviere Spielvideos, um Highlights deines Gameplays aufzuzeichnen", "capture_highlight_length": "Highlight-Länge", "capture_highlight_length_description": "Wähle die Länge der Spiel-Highlights aus", "capture_highlight_length_$seconds_seconds": "Letzte **$seconds** <PERSON><PERSON><PERSON>", "capture_quality_preset_$resolution@$fps": "**$resolution** @$fpsfps", "capture_audio_device": "Audio", "capture_audio_device_description": "Audio vom Standardgerät aufnehmen", "capture_audio_enabled": "Aktiviert", "capture_audio_disabled": "Deaktiviert"}, "language_selector": {"automatic_$lang": "Automatisch ($lang)"}, "remote_tooltip": {"click_to_use_the_wemod_remote": "<PERSON><PERSON> klicken, um WeMod Remote zu verwenden", "connect_to_wemod_remote": "Mit WeMod Remote verbinden", "enter_this_pin_on_your_device_to_connect": "Gib diesen Pin auf deinem Gerät ein, um Mods von deinem Smartphone aus zu verbinden und zu steuern.", "connected": "Verbindung wurde hergestellt", "disconnect": "Verbindung trennen", "get_the_app": "Hol dir die mobile App", "scan_the_qr_code_or_visit_the_site": "<PERSON>anne den QR-Code oder besuche [wemod.com/remote]($url) auf deinem mobilen Gerät.", "reconnect": "Erneut verbinden", "force_disconnected_message": "<PERSON>s sieht so aus, als würdest du WeMod auf einem anderen PC verwenden. Deshalb haben wir diesen PC von der Remote-Steuerung getrennt.", "disconnect_remote_app": "Remote-<PERSON><PERSON> trennen"}, "user": {"go_pro": "Werde ein Pro", "support_wemod": "<PERSON><PERSON><PERSON> un<PERSON>ützen"}, "sidebar_user": {"go_pro": "Upgrade auf _Pro_", "go_pro_collapsed": "Upgrade auf _Pro_", "support_wemod": "_WeMod_ unterstützen"}, "custom_installation_selector": {"not_an_exe_toast": "<PERSON><PERSON>-<PERSON><PERSON>", "select_your_game_exe": "<PERSON><PERSON>-EXE wählen", "pick_this_game_exe": "Diese <PERSON>l-EXE wählen", "exe_files": "EXE-<PERSON>ien", "custom_game_exe": "Benutzerdefinierte Spiel .exe", "add_game_exe": "Spiel .exe hinzufügen", "or_drag_the_file_here": "oder ziehen Sie die Datei hierher", "custom_exe_info": "<PERSON>n WeMod Ihr Spiel nicht automatisch gefunden hat, müssen Sie die Spielverknüpfung oder die ausführbare Datei (.exe) manuell hinzufügen."}, "feedback_dialog": {"how_was_playing_$game_with_wemod": "Wie war es, $game mit WeMod zu spielen?", "say_thanks_to_the_developer": "<PERSON><PERSON> danken", "type_here": "Text hier e<PERSON>ben", "submit": "Übermitteln", "allow_this_to_be_posted_publicly": "Dieser Beitrag darf öffentlich angezeigt werden", "did_you_read_the_notes": "Hast du vor dem Start des Spiels die Anmerkungen gelesen?", "report_problem": "Problem melden", "cancel": "Abbrechen", "which_best_describes_experience": "Welche Option beschreibt deine Erfahrung am besten?", "my_game_crashed": "Mein Spiel ist abgestürzt", "cheats_broken_or_confusing": "Mods sind kaputt oder verwirrend", "other_issue": "Anderes Problem", "where_were_you_in_the_game_when_it_crashed": "Wo warst du im Spiel, als es abgestürzt ist?", "main_menu_or_before": "Hauptmenü oder noch davor", "cutscene_or_loading_screen": "Videosequenz oder Ladebildschirm", "in_game_playing": "In-Game; be<PERSON>", "in_game_menu_inventory": "In-Game; Menü/Inventar", "other": "Sonstiges", "any_extra_details_that_can_help_us": "Gibt es noch weitere Details, die uns helfen könnten?", "please_describe_the_problem": "Bitte beschreibe das Problem, dass du mit diesen Mods hast.", "extra_details_example": "z. B. Gold Edition DLC ist installiert; das Spiel stürzt nach dem Laden des ersten Levels ab, wenn uneingeschränkte Gesundheit aktiviert ist", "cheat_log_will_be_sent": "Zusammen mit deinem Feedback wird ein Protokoll zu den verwendeten Mods übermittelt.", "submit_feedback": "Feedback übermitteln", "which_cheats_experienced_trouble": "Welche Mods haben nicht funktionierten?", "all_of_the_cheats": "Alle Mods", "additional_info_example": "Optionale zusätzliche Informationen (z. B. Mod funktionierte nicht beim Endgegner im zweiten Level)", "please_describe_the_issue_in_detail": "Bitte beschreibe das Problem so genau wie möglich:", "other_details_example": "z. B. im Realismus-Modus scheint nichts zu funktionieren.", "didnt_work_no_effect": "<PERSON> nicht funktion<PERSON>t; kein <PERSON>", "game_crash": "Abgestürztes Spiel", "not_as_expected": "Hat nicht wie erwartet funktion<PERSON>t", "optional_additional_info": "Optionale zusätzliche Informationen (z. B. Mod funktionierte nicht beim Endgegner im zweiten Level)", "select_a_cheat": "<PERSON><PERSON> w<PERSON>", "select_a_reason": "<PERSON><PERSON><PERSON> w<PERSON>", "select_a_location": "<PERSON><PERSON> w<PERSON>hlen", "allow_posting_publicly": "Dies darf öffentlich angezeigt werden", "send": "Senden"}, "trainer_cheats_list": {"press_$x_above_to_get_started": "Oben auf „$x“ drücken, um loszulegen", "press_$x_to_use_mod": "<PERSON><PERSON><PERSON> „$x“, um diesen Mod zu verwenden", "only_for_pro_members": "Nur für **PRO**-<PERSON><PERSON><PERSON><PERSON><PERSON>.", "upgrade_now": "Jetzt Upgrade vornehmen", "apply": "<PERSON><PERSON><PERSON>", "button_set": "Einstellen", "button_add": "Hinzufügen", "category_cheats": "Mods", "category_enemies": "<PERSON><PERSON><PERSON>", "category_game": "Spiel", "category_inventory": "Inventar", "category_physics": "Physik", "category_player": "<PERSON><PERSON><PERSON>", "category_stats": "Statistiken", "category_teleport": "Teleportieren", "category_vehicles": "Fahrzeuge", "category_weapons": "Waffen", "category_challenge": "Mods für Herausforderungen", "category_pinned": "angeheftet", "cheat_instructions": "Mod-Anweisungen", "pro": "Pro", "only_pro_members_can_use_beta_mods": "Nur **PRO**-Mitglieder können Beta-Mods verwenden.", "beta": "Beta", "disabled_play_tooltip": "Einige Mods erfordern, dass das Spiel läuft, aus Gründen der Mod-Kompatibilität oder -Stabilität.", "disabled_add_or_install_tooltip": "Fügen Sie Ihr Spiel hinzu oder installieren Sie es, um Mods damit zu verwenden.", "broken_mod_hint": "Dieser Mod wird gerade aktualisiert und funktioniert möglicherweise nicht wie erwartet", "auto_pin_setting_toast": "Sie können Ihre Auto-Pins in den Mod-Einstellungen zurücksetzen", "pin": "<PERSON>n", "unpin": "Unpin", "off": "AUS", "on": "AN", "loop": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "Abbrechen", "start_loop": "Schleife starten", "on_after": "AN nach", "off_after": "AUS nach", "mod_timer_message_on": "Der ausgewählte Modus wird nach der ausgewählten Dauer EINGESCHALTET.", "mod_timer_message_off": "Der ausgewählte Modus wird nach der ausgewählten Dauer AUSGESCHALTET.", "mod_timer_message_loop": "Der ausgewählte Modus wechselt zwischen EIN und AUS für die ausgewählten Dauern.", "minute": "m", "dismiss": "Verwerfen", "unlock_mod_timers": "Mod-<PERSON><PERSON>", "join_now": "Jetzt beitreten"}, "trainer_hotkey": {"not_set": "<PERSON>cht e<PERSON>llt", "numpad_$x": "Ziffernblock $x", "key_control": "Strg", "key_alt": "Alt", "key_insert": "Einfg", "key_delete": "Entf", "key_page_up": "Bild ↑", "key_page_down": "Bild ↓", "key_end": "<PERSON><PERSON>", "key_home": "Startseite", "key_left": "Links", "key_up": "Hoch", "key_right": "<PERSON><PERSON><PERSON>", "key_down": "<PERSON>ter", "key_scroll_lock": "ScrLk", "key_print_screen": "Druck", "key_pause": "Pause", "action_apply": "<PERSON><PERSON><PERSON>", "action_toggle": "Umschalten", "action_decrease": "Verringern", "action_increase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "action_previous": "Zurück", "action_next": "<PERSON><PERSON>", "hotkey_tooltip": "Bitte drücken Sie jetzt die Tastenkombination"}, "trainer_launcher": {"newer_version_required": "<PERSON><PERSON> Verwendung dieser Mods benötigst du eine neuere Version von We<PERSON>od.", "game_already_running": "<PERSON><PERSON>, dass das Spiel von WeMod gestartet wird. Bitte schließe dein Spiel, bevor du die Play-Taste drückst.", "game_not_running": "Das Spiel muss auße<PERSON><PERSON><PERSON> von <PERSON>od gestartet werden, bevor die Play-Taste gedrückt wird.", "elevation_denied": "<PERSON><PERSON><PERSON> kann nicht auf dein Spiel zugreifen, da es als Administrator ausgeführt wird.", "cannot_download_cheats": "Wir haben Probleme beim Herunterladen der Mods. Bitte versuch es später noch einmal.", "cannot_find_dll": "Trainer-DLL nicht gefunden. <PERSON><PERSON> sicher, dass sie vorhanden ist!", "cheats_missing": "Die Mods fehlen auf deinem Computer. Bitte stelle sicher, dass deine Antivirensoftware WeMod erlaubt, bevor du fortfährst.", "close": "Schließen", "fix": "Problem beheben", "auto_fix_av_failed": "Wir konnten das Problem nicht automatisch beheben. Bitte überprüfe deine Antivirensoftware auf weitere Details.", "ok": "OK", "files_missing": "<PERSON> Dateien, die von WeMod für die Aktivierung von Mods benötigt werden, fehlen auf deinem Computer. Dies liegt in der Regel daran, dass Antivirensoftware sie fälschlicherweise als schädliche Dateien erkennt.\n\nDu musst möglicherweise WeMod neu installieren und dein Antivirenprogramm vorübergehend deaktivieren.", "reinstall_now": "Jetzt neu installieren", "trouble_starting_or_finding": "Wir können das von dir geöffnete Spiel nicht starten oder nicht finden.", "retry": "<PERSON>ch einmal versuchen", "x64_expected": "<PERSON>se <PERSON> wurden für die 64-Bit-Version des Spiels erstellt. Du verwendest die 32-Bit-Version.", "x86_expected": "<PERSON><PERSON> wurden für die 32-Bit-Version des Spiels erstellt. Du verwendest die 64-Bit-Version.", "trouble_loading_cheats": "Wir können die Mods nicht in deinem Spiel laden. Bitte starte das Spiel neu oder drücke auf *Hilfe*, um weitere Informationen zu erhalten.", "report_problem": "Problem melden", "preparation_failed": "Wir können das Laden der Mods nicht vorbereiten. Möglicherweise musst du WeMod in deiner Firewall oder Antivirensoftware erlauben.", "trouble_running_cheats": "Wir können die Mods nicht ausführen. Möglicherweise musst du WeMod in deiner Firewall oder Antivirensoftware erlauben.", "activation_problem": "Es gab ein Problem beim Laden der Mods. Deine Spielversion ist möglicherweise nicht kompatibel oder du musst es an einem anderen Punkt des Spiels erneut versuchen.", "activation_prevented": "Es gab ein Problem beim Laden der Mods. Wenn du das Spiel nicht geschlossen hast, ist möglicherweise deine Spielversion nicht kompatibel oder du musst WeMod in deiner Firewall oder Antivirensoftware erlauben.", "launch_outside_wemod": "<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON>en"}, "trainer_play_button": {"loading_mods": "Mods werden geladen", "playing": "Spiel läuft", "play": "<PERSON><PERSON><PERSON>", "install_for_free": "Kostenfrei installieren", "install": "Installieren", "play_coaching_tip_header": "Los geht‘s!", "play_coaching_tip_message": "Bist du bereit, deine neuen Mods auszuprobieren? Starte das Spiel direkt über WeMod.", "install_coaching_tip_install_game_header": "Spiel installieren oder finden", "install_coaching_tip_message": "Klicke zum Herunterladen auf eine Plattform deiner Wahl auf „Spiel hinzufügen“.\nDu hast es bereits installiert? Dann kannst du deine Spieldatei über das Menü „Spiel hinzufügen“ finden und hinzuzufügen.", "stop_playing": "<PERSON><PERSON><PERSON>den", "add_game": "Spiel hinzufügen", "start": "Start"}, "active_trainer_toast": {"cheats_are_running": "Mods werden im Hintergrund ausgeführt.", "open": "<PERSON><PERSON><PERSON>", "end": "<PERSON><PERSON>"}, "game_feed": {"new": "neu", "new_abbreviation": "n", "updated": "aktual<PERSON><PERSON>", "updated_abbreviation": "a", "last_played_$date": "Zuletzt gespielt am $date", "released_on_$date": "Veröffentlicht am $date", "community_choice_award_winner": "Gewinner des Community Choice Award", "no_cheats_available": "<PERSON><PERSON> verfügbar"}, "activity": {"was_updated_by_$creator": "wur<PERSON> von $creator aktualisiert", "$creator_created_cheats_for": "$creator hat Mods erstellt für", "all": "Alle", "my_games": "<PERSON><PERSON>", "get_notifications_for_games_you_care_about": "Erhalte **Benachrichtigungen** für Spiele, die dir wichtig sind.", "follow_all": "<PERSON> folgen", "your_notifications_are_set": "Deine **Benachrichtigungen** wurden eingestellt und können über die *Einstellungen* verwaltet werden"}, "leave_feedback_dialog": {"what_are_your_overall_thoughts": "Was hältst du ganz allgemein von WeMod? Was könnten wir besser machen?", "this_is_not_for_games_or_trainers": "Dies gilt nicht für bestimmte Spiele oder Mods!", "there_was_a_problem_toast": "Es gab ein Problem beim Einstellen deines Feedbacks.", "submit": "Übermitteln", "minimum_$x_characters": "Mindestens $x Zeichen"}, "objectives": {"title": "Ziele", "pro": "Pro", "objective_set_email": "E-Mail-Adresse für Benachrichtigungen hinzufügen", "objective_set_username": "Benutzernamen festlegen", "objective_set_password": "Ein Passwort festlegen", "objective_set_profile_image": "Profilbild ändern", "objective_leave_feedback": "Feed<PERSON> abgeben", "objective_join_discord": "Uns auf Discord beitreten", "objective_join_community": "Unserer **Community** beitreten", "objective_like_on_facebook": "Uns auf **Facebook** folgen", "objective_follow_on_twitter": "Uns auf **X** folgen", "objective_subscribe_on_youtube": "Unseren **YouTube-Kanal** abonnieren", "objective_try_pro": "WeMod Pro kostenfrei testen", "objective_connect_remote": "Remote-<PERSON><PERSON> verbinden", "objective_install_overlay": "In-Game-Overlay installieren", "objective_use_interactive_controls": "In-App-Mod-Steuerung verwenden", "objective_boost_a_game": "Erhöhe den Boost eines Spiels in der kommenden Warteschlange", "objective_play_a_game": "Das erste Spiel mit WeMod spielen", "objective_use_hotkeys": "Mods mit Tastenkürzeln aktivieren", "get_started": "<PERSON><PERSON><PERSON>", "complete_your_profile": "Profil vervollständigen", "get_started_with_pro": "<PERSON><PERSON><PERSON> Schritte mit Pro", "1_boost": "+1 Boost", "$x_boosts": "+$x Boosts", "boosts": "Bo<PERSON><PERSON>", "claim_boost": "Boost <PERSON>", "welcome_to_wemod": "Will<PERSON>mmen bei WeMod!", "streak": "Serie", "$x_day": "$x Tag", "$x_days": "$x Tage"}, "billing_settings": {"subscribed": "A<PERSON><PERSON><PERSON>", "$x_days_left_in_trial": "$x Tage Testversion übrig", "next_billing": "Nächste Abrechnung", "until": "Bis", "resume": "Fortsetzen", "remove": "Entfernen", "view_plan_details": "Plandetails anzeigen", "payment_method": "Zahlungsmethode", "change_payment_method": "Zahlungsmethode ändern", "add_payment_method": "Zahlungsmethode hinzufügen", "pro_subscription_end_on_$date_dialog": "Deine Pro-Mitgliedschaft endet am $date, es sei denn, du fügst neue Zahlungsinformationen hinzu.", "no_payment_method": "<PERSON><PERSON>", "upgrade_to_pro": "Upgrade auf Pro vornehmen", "get_the_best_experience_with_pro": "Das rundum beste Spielerlebnis mit PRO genießen", "get_pro_as_a_gift": "PRO als Geschenk erhalten", "upgrade": "Upgrade vornehmen", "give_the_gift_of_pro": "Pro als *Geschenk* vergeben", "share_an_elevated_modding_experience": "<PERSON><PERSON> ein besseres Modding-Erlebnis mit deinen Freunden.", "gift_pro_to_a_friend": "Pro verschenken", "gifting_disclaimer": "<PERSON>rauch<PERSON> du Hilfe mit einem Geschenk, das du geschickt hast? Sende eine E-Mail an [<EMAIL>](mailto:<EMAIL>).", "pro_plan": "**Pro**-Plan", "active_gift": "Aktives Geschenk", "active_gift_yearly_info": "Du hast ein kostenfreies Jahr Pro als Geschenk von $sender erhalten.", "active_gift_monthly_info": "Du hast einen kostenfreien Monat Pro als Geschenk von $sender erhalten.", "ends": "Endet am", "payment_overdue": "Überfällige Zahlung", "save_$x_or_more": "Mindestens $x % sparen", "switch_to_yearly_payment": "<PERSON><PERSON> jährlicher Zahlung wechseln", "update_billing_address": "Bitte aktualisieren Sie die Rechnungsadresse", "applicable_taxes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "last_amount_billed_$x_on_$date": "Zuletzt abgerechneter Betrag: $x am $date", "expires": "Läuft ab"}, "change_password": {"current_password": "Aktuelles Passwort", "new_password": "Neues Passwort", "new_password_again": "Neues Passwort wiederholen", "create_password": "Passwort erstellen", "password_again": "Passwort wiederholen", "save": "Speichern", "success_toast": "Du hast dein Passwort erfolgreich geändert!", "problem_toast": "Es gab ein Problem bei der Änderung deines Passworts.", "no_password_set": "Kein Passwort festgelegt", "password_requirements_error": "Das Passwort muss mindestens 8 Zeichen lang sein", "password_confirm_error": "Passwort und Bestätigung müssen übereinstimmen", "current_password_error": "Geben Sie Ihr aktuelles Passwort ein"}, "general_settings": {"save_log": "Protokoll speichern", "save_messages": "<PERSON><PERSON><PERSON><PERSON>, die den WeMod-Erstellern dabei helfen, Korrekturen vorzunehmen.", "log_files": "Protokolldateien", "failed_to_save_toast": "Die Protokolldatei konnte nicht gespeichert werden. Bitte überprüfe den Pfad und versuch es noch einmal.", "gdpr_consent": "Daten zur Personalisierung von WeMod verwenden", "gdpr_consent_description": "Dies erlaubt es uns, analytische Informationen über die Verwendung von WeMod zu erfassen und zu verarbeiten. Wir können dadurch bestehende Funktionen verbessern, deine Erfahrung personalisieren und Werbeanzeigen liefern, die für dich als kostenfreier Benutzer relevant sind.", "gdpr_revoke_confirm_message": "Bist du sicher? Du kannst WeMod nur weiter verwenden, wenn du unseren [allgemeinen Geschäftsbedingungen](website://terms) und der [Datenschutzrichtlinie](website://privacy) in Bezug auf personalisierte Inhalte zustimmst.", "ok": "OK", "cancel": "Abbrechen"}, "profile_settings": {"upload_new_pic": "Neues Bild hochladen", "username": "<PERSON><PERSON><PERSON><PERSON>", "email_address": "E-Mail Adresse eingeben", "current_password": "Aktuelles Passwort", "save": "Speichern", "username_change_success_toast": "Du hast deinen Benutzernamen erfolgreich geändert!", "email_change_success_toast": "Du hast deine E-Mail-Adresse erfolgreich geändert!", "profile_image_change_success_toast": "Du hast dein Profilbild erfolgreich geändert!", "update_error_toast": "Beim Aktualisieren deines Profils ist ein Fehler aufgetreten.", "logout": "Abmelden", "are_you_sure": "Bist du sicher, dass du dich von deinem Konto abmelden möchtest?", "sign_out": "Ausloggen", "cancel": "Abbrechen", "hold_on": "Moment, warte kurz.", "no_email_for_$username": "<PERSON><PERSON><PERSON> ($username) wurde noch keine E-Mail-Adresse bzw. kein Passwort zugeordnet. Wenn du dich jetzt abmeldest, kannst du dich nicht mehr anmelden!", "update_account": "Konto aktualisieren"}, "theme_selector": {"default": "Standardeinstellung", "green": "<PERSON><PERSON><PERSON><PERSON>", "orange": "Orange", "black": "<PERSON><PERSON><PERSON>", "purple": "<PERSON>"}, "release_channel_selector": {"app_upgrade": "App-Upgrade", "channel_may_include_unstable_features": "Dieser Veröffentlichungskanal kann neue, aber auch instabile Features enthalten. Wenn du auf Probleme stößt, stelle bitte einen Beitrag im Forum oder in unserem Discord-Kanal ein. Wenn du in Zukunft eine Herabstufung auf einen anderen Kanal wünschst, musst du auf das nächste Update warten.", "continue": "Fortfahren", "cancel": "Abbrechen", "app_downgrade": "App-Herabstufung", "youll_have_to_wait": "Du musst bis zum nächsten Release warten oder die App neu installieren, um zu diesem Kanal zu wechseln."}, "failed_payment_toast": {"update_payment": "Zahlungsweise aktualisieren", "fix": "Problem beheben"}, "online_status": {"offline_toast": "Die Verbindung zu WeMod kann nicht hergestellt werden. Du hast nur begrenzten Zugang zu Mods, bis die Verbindung wiederhergestellt ist.", "maintenance_toast": "WeMod führt derzeit Wartungsarbeiten aus. Wir sind in Kürze wieder für dich da!"}, "basic_dialog": {"ok": "OK", "yes": "<PERSON>a", "confirm": "Bestätigen", "no": "<PERSON><PERSON>", "cancel": "Abbrechen", "help": "<PERSON><PERSON><PERSON>", "dont_remind_again": "<PERSON>cht mehr daran erinnern"}, "time": {"$x_years": "$xy", "$x_year": "$xy", "$x_mo": "$xm", "$x_days": "$xd", "$x_day": "$xd", "$x_hr": "$x Std.", "$x_min": "$x Min.", "$x_sec": "$x Sek.", "$x_years_ago": "Vor $xy", "$x_year_ago": "Vor $xy", "$x_mo_ago": "Vor $xm", "$x_days_ago": "Vor $xd", "$x_day_ago": "Vor $xd", "$x_hr_ago": "Vor $x Std.", "$x_min_ago": "Vor $x Min.", "$x_sec_ago": "Vor $x Sek."}, "toggle": {"on": "Ein", "off": "Aus"}, "queue": {"upcoming": "Be<PERSON>stehend<PERSON>", "info_tooltip": "Dies ist unsere Warteschlange für kommende Spiele. <PERSON><PERSON>, an welchen Spielen gearbeitet wird, und boosten Si<PERSON> diejenigen, die Si<PERSON> gerne spielen möchten. Spiele werden in der Reihenfolge der Warteschlange entwickelt, aber das Veröffentlichungsdatum hängt von der Komplexität und den Testanforderungen jedes Spiels und der davor liegenden Spiele ab.", "get_more": "<PERSON><PERSON> erhalten", "overview": "Übersicht", "needs_update": "Update er<PERSON><PERSON><PERSON>", "new_releases": "Neue Versionen", "recently_completed": "<PERSON><PERSON><PERSON><PERSON> fertiggestellt", "new": "neu", "update": "Update", "the_creators": "<PERSON> Ersteller", "become_a_wemod_creator": "WeMod-<PERSON><PERSON><PERSON> werden", "join_our_creator_community": "<PERSON><PERSON><PERSON>ß dich unserer exklusiven Ersteller-Community an und verdiene Geld mit der Entwicklung von kostenfreien Mods und Trainern für die WeMod-App.", "apply": "<PERSON><PERSON><PERSON>"}, "boost_button": {"no_boosts": "Dir stehen keine Boosts zur Verfügung. Als Pro-Mitglied erhältst du jeden Monat mehr.", "no_boosts_free": "Abonnieren Sie WeMod Pro, um mehr Boosts zu erhalten!", "an_error_occurred": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuch es noch einmal.", "you_boosted_$game": "Du hast **$game** geboostet. Wir werden dich benachrichtigen, wenn Mods veröffentlicht werden!", "dont_notify_me": "Benachrichtigungen blockieren", "notification_preferences_updated": "Die Benachrichtigungseinstellungen wurden aktualisiert.", "failed_to_update_notification_preferences": "Die Benachrichtigungseinstellungen konnten nicht aktualisiert werden.", "boosts_available_after_trial_period": "Boosts sind nach der Testphase verfügbar."}, "boost_balance_button": {"boosts_available_after_trial_period": "Boosts sind nach der Testphase verfügbar."}, "unavailable_game": {"$game_for_$platform_is_$number_in_our_work_queue": "$game für $platform ist **auf Position $number** in unserer Warteschlange.", "$game_for_$platform_is_assigned_to_$creator_and_is_$number_in_our_queue": "$game für $platform ist $creator zu<PERSON>wi<PERSON>n und ist **#$number** in unserer Warteschlange für kommende Spiele.", "the_time_until_its_released_will_depend": "Wie lange es bis zur Veröffentlichung dauert, hängt von der Position in der Warteschlange und dem Aufwand für die Erstellung und das Testen von Mods für dieses Spiel und die Spiele davor ab.", "this_game_is_not_supported": "Dieses Spiel wird von <PERSON> **nicht unterstützt**. Das liegt entweder an technischen Aspekten, die eine Modifizierung unmöglich machen, oder da<PERSON>, dass es sich um ein Spiel für mehrere Spieler handelt.", "upcoming": "Be<PERSON>stehend<PERSON>", "stats": "Statistiken", "were_a_small_team_x_games": "Wir sind ein kleines Team von Erstellern, das eine Bibliothek von über **$x** Spielen pflegt. Der Zeitpunkt der Veröffentlichung von Spielen hängt immer von der Priorität in der Warteschlange und dem Gesamtaufwand für die Erstellung und das Testen der Mods für die einzelnen Spiele ab. Möchtest du, dass wir früher mit der Arbeit an einem Spiel beginnen? Erhöhe die Priorität des Spiels in der Warteschlange mit einem Boost.", "what_is_this_about": "Worum geht es hier?", "wemod_is_a_free_app_x_games": "WeMod ist eine kostenlose App mit einer Bibliothek, die über **$x Spiele** enthält. Da wir nur ein kleines Team sind, dauert es manchmal länger, bis etwas hinzugefügt oder aktualisiert wird.", "the_queue_is_our_way_of_being_transparent_with_our_users": "Die Warteschlange ist unsere Art, für die Community **transparent zu sein**. Wir priorisieren Spiele in unserer Warteschlange über einen Algorithmus, der die Anzahl der aktiven Spieler, die Zeit seit der letzten Aktualisierung des Spiels, Boosts und Benutzerinteresse berücksichtigt.", "view_upcoming_games": "Kommende Spiele ansehen", "this_week": "<PERSON><PERSON>", "this_month": "<PERSON><PERSON>", "$x_games_added": "wurden **$x** <PERSON><PERSON><PERSON> hinzugefügt", "$x_games_updated": "wurden **$x** Spiele aktualisiert", "get_boosts": "<PERSON><PERSON> dir <PERSON><PERSON><PERSON>", "boosts": "Bo<PERSON><PERSON>"}, "trainer_notify": {"no_cheats_yet": "Wir haben noch keine Mods für dieses Spiel. G<PERSON> un<PERSON> Bescheid, falls du dich für dieses Spiel interessierst, damit wir dir eine E-Mail schicken können, sobald wir Mods hinzugefügt haben.", "do_you_want_us_to_notify_you_when_its_ready": "<PERSON>len wir dich benachrichtigen, wenn es fertig ist?", "do_you_want_us_to_notify_you_when_we_update_it": "<PERSON>len wir dich benachrichtigen, wenn es aktualisiert wird?", "do_you_want_us_to_notify_you_if_we_update_it": "<PERSON>len wir dich benachrichtigen, falls es aktualisiert wird?", "notify_me_when_mods_updated": "Benachrichtigt werden, wenn Mods aktualisiert sind", "email_address": "E-Mail Adresse eingeben", "notify_me": "Benachrichtigungen zulassen", "email_error": "Es gab ein Problem mit der von dir angegebenen E-Mail-Adresse", "well_send_you_an_email": "Wir werden dir eine E-Mail schicken!", "well_send_you_an_email_when_available": "Wir werden dir eine E-Mail schicken, wenn Mods verfügbar sind!", "unfollow": "Nicht mehr folgen", "request_mods": "Anfragen Mods"}, "loading": {"tagline": "<PERSON><PERSON>, deine Regeln", "loading_message": "Ladevorgang l<PERSON>", "checking_for_updates_message": "Wir suchen nach Updates", "installing_update_message": "Update wird installiert", "install_later": "Später installieren", "download_directly": "<PERSON><PERSON><PERSON> herunt<PERSON><PERSON>n", "offline_message": "<PERSON>in Computer scheint offline zu sein. Eine Internetverbindung wird benö<PERSON>, um die App erstmalig zu starten.", "maintenance_message": "WeMod führt derzeit Wartungsarbeiten aus. Wir sind in Kürze wieder für dich da!", "support_initialization_error_message": "<PERSON><PERSON>od ist ein Fehler aufgetreten. Eventuell musst du die App neu installieren oder sie in deiner Antivirensoftware erlauben.", "reinstall": "App neu installieren", "more_info": "Weitere Informationen", "dotnet_error_message": "Es muss die richtige Version des .NET-Frameworks installiert sein", "download": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "retry": "<PERSON>ch einmal versuchen", "review_terms": "Indem ich auf „Konto erstellen“ klicke, erkläre ich mich mit den [allgemeinen Geschäftsbedingungen](website://terms) und der [Datenschutzrichtlinie](website://privacy) von WeMod einverstanden.", "accept_and_continue": "Zustimmen und fortfahren", "consent_error_message": "Ein Fehler ist aufgetreten. Bitte versuch es noch einmal."}, "status_tag": {"new_game": "Neues Spiel", "updated_game": "Aktualisiertes Spiel"}, "pro_popup": {"upgrade_to_pro": "Upgrade auf Pro vornehmen", "remote_app": "Remote-*App*", "use_the_remote_on_your_phone": "Greife über dein Smartphone oder Tablet auf die WeMod-App zu, um Mods über einen zweiten Bildschirm zu konfigurieren.", "save_cheats": "Mods *speichern*", "save_cheats_info": "<PERSON><PERSON> ein<PERSON>ch wieder in deine Spiele ein, indem du die vorherigen Mod-Einstellungen automatisch anwendest.", "cheat_overlay": "In-Game-**Overlay**", "an_in_game_menu": "Verfeinere deine Mods nahtlos, ohne etwas zu verpassen."}, "remote_upgrade_dialog": {"the_remote_makes_it_easy": "Mit der Remote-App ist das ganz einfach", "simply_connect_the_desktop_app_to_your_mobile_device": "Verbinde die Desktop-App einfach über einen Code mit deinem Mobilgerät und voilá! Jetzt kannst du genau sehen, was aktiviert ist, und erhältst außerdem eine detailgenaue Steuerung mit Schieberegler und Aufklappmenüs.", "upgrade_now": "Jetzt Upgrade vornehmen"}, "poll_dialog": {"submit": "Übermitteln", "i_changed_my_mind": "Pro bleiben", "cancel_my_subscription": "Meine Mitgliedschaft kündigen"}, "overlay": {"start_game_message": "Wenn du in diesem Overlay auf deine Mods zugreifen möchtest, starte dein Spiel über die WeMod-App.", "loading_cheats_message": "Mods werden geladen", "notes": "Anmerkungen", "view_notes": "Anmerkungen anzeigen", "cheat_instructions": "Mod-Anweisungen", "mods": "Mods", "maps": "<PERSON><PERSON>", "game_guide": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "overlay_tooltip": {"click_to_use_the_overlay": "<PERSON><PERSON><PERSON><PERSON>, um das In-Game-Overlay zu verwenden", "unsupported": "<PERSON>cht unterstützt", "your_pc_does_not_meet_the_requirements": "<PERSON><PERSON> <PERSON> entspricht nicht den Mindestanforderungen für die Nutzung des Overlays.", "requires_version": "<PERSON> benötigst Windows 10 Version $version oder höher", "not_installed": "In-Game-Overlay installieren", "install_the_overlay_from_the_microsoft_store": "Installiere unser Overlay aus dem Microsoft Store und erhalte während des Spiels bequemen Zugriff auf deine Mods.", "install_the_overlay": "Klassisches Overlay installieren", "enable_game_bar": "Game Bar aktivieren", "the_overlay_requires_game_bar": "<PERSON> er<PERSON>, dass Xbox Game Bar auf deinem PC aktiviert ist.", "open_game_bar_settings": "Game Bar-Einstellungen öffnen", "overlay": "In-Game-*Overlay*", "the_overlay_is_installed": "Greife auf deine Mods zu, indem du ***Win* *G*** dr<PERSON><PERSON>, nachdem du ein Spiel in WeMod gestartet hast."}, "pro_onboarding_dialog": {"congrats": "Glückwunsch!\nDu bist jetzt ein *Pro*", "you_are_now_pro": "Du bist jetzt ein **Pro**", "thanks_for_joining": "<PERSON><PERSON>, dass du dich der Community angeschlossen hast! Lass uns nun **deine Installation abschließen**.", "overlay_requirements": "Auf deinem PC muss *Xbox Game Bar* aktiviert sein und du benötigst Windows 10 Version $version oder höher.", "install_overlay": "Overlay installieren", "open_game_bar_settings": "Game Bar-Einstellungen öffnen", "get_in_game_overlay": "In-Game-**Overlay** installieren", "bring_cheats_as_close_to_the_game_as_possible": "Mit dem WeMod-Overlay sind deine Mods so nah am Spiel wie möglich. Sobald du das Overlay über den Microsoft Store installiert hast, kannst du im Spiel auf deine Mods zugreifen, indem du ***Win* *G*** dr<PERSON><PERSON>.", "connect_wemod_remote": "Verbinde **WeMod Remote**", "get_our_mobile_app": "Hol dir unsere **mobile App**: <PERSON><PERSON> den QR-Code oder rufe auf deinem Mobilgerät *wemod.com/remote* auf.", "then_enter_this_pin": "Dann auf deinem Smartphone **diesen Pin e<PERSON>ben**, um eine Verbindung herzustellen", "receive_special_role_in_discord": "Besondere Rolle in** Discord** erhalten", "plus_now_that_youre_pro": "<PERSON><PERSON>, da du ein **Pro** bist,", "plus_with_your_pro_subscription": "Mit deiner **Pro**-Mitgliedschaft", "you_support_creators_and_wemod_development": "**unterstützt du Ersteller und die WeMod-Entwicklung**, kannst Mods für populäre Spiele **vorschlagen** und erhältst **VIP-Support**.", "skip_for_now": "<PERSON><PERSON><PERSON>", "i_am_all_set_up_for_pro": "<PERSON>ch bin bereit für Pro", "connect_discord": "Discord verbinden", "join_server": "Server beitreten", "connected": "Verbindung wurde hergestellt", "installed": "Installiert", "first_your_account_must_be_linked_to_discord": "<PERSON><PERSON>st muss dein Konto mit Discord verbunden werden, indem du unten eine Verbindung herstellst. <PERSON><PERSON><PERSON> dies geschehen ist und du unserem Server beigetreten bist, wird der Bot **deine Rolle innerhalb von 24 Stunden aktualisieren**.", "your_exclusive_pro_features": "De<PERSON> exklusiven **Pro**-Features", "were_happy_to_have_you": "Wir freuen uns, dich in unserer Pro-Community begrüßen zu dürfen. Erfahre, wie du auf die **neuesten und großartigsten Pro-Features zugreifen** kannst.", "enable_save_mods": "Merke dir deine Mo<PERSON> mit **Mods speichern**", "save_mods_explanation": "Hast du deine Mods perfekt gewählt, so wie du möchtest? Speichere sie auf diese Wei<PERSON> und wir starten dein nächstes Spiel mit deinen **vorherigen Mod-Konfigurationen**.", "how_it_works": "So <PERSON><PERSON><PERSON><PERSON>‘s", "access_in_app_controls": "Auf **interaktive Steuerung** zugreifen", "interactive_controls_explanation": "Die interaktiven Steuerelemente auf der Spielseite sind eine weitere Möglichkeit, Mods zu aktivieren. Die interaktive Steuerung beinhaltet **<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, manuelle Eingabe und Schaltflächen**.", "influence_game_priority": "Die Priorität von Spielen mit **added boosts** beeinflussen", "boosts_explanation": "Boosts werden verwendet, um die **Priorität von Spielen in der Warteschlange für kommende Spiele** zu beeinflussen. Je mehr Boosts ein <PERSON>l hat, desto eher wird es bearbeitet.", "choose_your_theme": "<PERSON>ähle deine Farbe für **Theme**", "themes_explanation": "Wähle das bevorzugte **Erscheinungsbild deiner App**. Du kannst dieses auch jederzeit in den Einstellungen ändern.", "i_understand_the_pro_features": "Ich verstehe die Pro-Features", "disabled_game_bar_instructions": "Aktiviere **Xbox Game Bar** auf deinem PC, um das WeMod-Overlay zu nutzen.", "enable_xbox_game_bar": "Xbox Game Bar aktivieren", "save_mods": "Mods speichern"}, "remote_platforms": {"available_on_mobile": "Verfügbar auf *iOS* und *Google Play*"}, "cancel_win_back_dialog": {"is_pro_not_for_you": "Ist **Pro** nichts für dich?", "wemod_pro_subscribers_enjoy_benefits": "<PERSON> Mitglieder von WeMod Pro haben Zugriff auf einfachere Mod-Steuerung, genießen schnelleren Spielablauf und nehmen Einfluss auf die von uns unterstützten Spiele.", "by_canceling_pro_you_lost_these_benefits": "Ohne Pro-Mitgliedschaft verpasst du **diese Vorteile**", "need_a_new_game_to_play": "Hast du Lu<PERSON> auf ein neues Spiel?", "wemod_offers_support_for_trending_free_games": "WeMod bietet Unterstützung für angesagte kostenfrei Spiele – die du jederzeit ausprobieren kannst!", "need_help_with_pro": "Brauchst du Hilfe mit der Pro-Mitgliedschaft?", "visit_our_faq_or_email_us": "Sieh dir unsere Pro-[FAQ](https://wemod.gg/pro-features) an oder schicke eine E-Mail an [<EMAIL>](mailto:<EMAIL>).", "continue_with_pro": "PRO bleiben", "i_still_wish_to_cancel": "Ich möchte immer noch kündigen", "see_all": "Alle anzeigen", "keep_supporting_the_creators": "<PERSON><PERSON>hin die Ersteller unterstützen", "your_subscription_supports_the_creators": "Deine Mitgliedschaft unterstützt die Ersteller, die Mods für deine Lieblingsspiele entwickeln.", "$x_cheats_created": "*$x* Mods wurden erstellt"}, "simple_pro_dialog": {"forget_hotkeys_with_pro": "Mit *Pro* kannst du Tastenkürzel vergessen", "did_you_know": "Wu<PERSON><PERSON> du, dass du mit dem WeMod-Overlay und *WeMod Pro* die Mod-Steuerung ins Spiel verlegen kannst? *🤯* Außerdem hast du die Möglichkeit, Mods mit der exklusiven WeMod Remote-App und In-App-Steuerung zu modifizieren.\n\nWeniger Stress mit Tastenkürzeln, *mehr Zeit fürs Spielen*.", "start_free_trial": "Kostenlose Testversion starten", "learn_more": "<PERSON><PERSON> er<PERSON>"}, "secure_account_dialog": {"password": "Passwort", "password_again": "Passwort wiederholen", "save": "Speichern", "set_password_success_toast": "Du hast dein Passwort erfolgreich festgelegt!", "set_password_problem_toast": "Beim <PERSON>legen deines Passworts ist ein Problem aufgetreten.", "set_email_success_toast": "Du hast deine E-Mail-Adresse erfolgreich festgelegt! Du wirst in Kürze zur Verifizierung deiner E-Mail-Adresse eine Nachricht in deinem E-Mail-Posteingang erhalten.", "email_address": "E-Mail Adresse eingeben", "email_only_message": "Gib deine E-Mail-<PERSON><PERSON><PERSON> ein, damit du immer auf dein Pro-Konto zugreifen kannst.", "no_email_or_password_message": "Gib eine E-Mail-Adresse und ein Passwort ein, damit du immer auf dein Konto zugreifen kannst.", "no_password_message": "Gib ein Passwort ein, damit du immer auf dein Konto zugreifen kannst.", "no_email_message": "Gib deine E-Mail-<PERSON><PERSON><PERSON> ein, damit du immer auf dein-Konto zugreifen kannst.", "create_your_login_credentials": "Zugangsdaten erstellen", "password_requirements_error": "Das Passwort muss mindestens 8 Zeichen lang sein", "password_confirm_error": "Passwort und Bestätigung müssen übereinstimmen"}, "pro_general": {"join_now": "Jetzt beitreten"}, "save_cheats_tooltip": {"save_cheats": "Mods *speichern*", "save_cheats_info": "<PERSON><PERSON> ein<PERSON>ch wieder in deine Spiele ein, indem du die vorherigen Mod-Einstellungen automatisch anwendest."}, "save_cheats_tooltip_graphic": {"jump_height": "Sprunghöhe", "extra_ammo": "Extra-Munition", "super_fast": "<PERSON> schnell", "unlimited_gold": "Unbegrenztes Gold", "fly_mode": "Flugmodus"}, "save_cheats_toggle": {"save_cheats": "Mods speichern", "save_cheats_info": "<PERSON><PERSON> ein<PERSON>ch wieder in deine Spiele ein, indem du die vorherigen Mod-Einstellungen automatisch anwendest.", "no_eligible_cheats": "<PERSON>s gibt keine <PERSON>, die für dieses Spiel gespeichert werden können. Dies kann passieren, wenn <PERSON> er<PERSON>, dass etwas im Spiel passiert, damit sie funktionieren können. Die Anwendung beim Start würde zu einem Fehler führen.", "coaching_tip_header": "Schnellstart mit „Mods speichern“", "coaching_tip_message": "Du kannst deine Lieblings-Mo<PERSON> ganz einfach speichern, ohne sie bei jedem Spiel neu einrichten zu müssen. Versuch es einfach mal!"}, "pinned_mods_tooltip": {"unlock_pinned_mods": "Angeheftete Mods entsperren"}, "remote_app_tooltip": {"remote_app": "Remote-App", "no_more_alt_tabbing": "Kein <PERSON> mehr! Verwalten Sie Ihre Mods und greifen Sie unterwegs auf Karten zu."}, "save_mods_tooltip": {"save_mods": "Mods speichern", "save_mods_info": "Speichern Sie Ihre Mods, damit sie jedes Mal aktiviert sind, wenn <PERSON>e zurückkehren, um zu spielen"}, "precision_mods_tooltip": {"precision_controls": "Präzisionssteuerungen", "precision_mods_info": "Gehen Sie mit präzisen Steuerungen und vollständiger Anpassung noch weiter mit Ihren Mods.", "unlock_precision_controls": "Präzisionssteuerungen entsperren", "join_now": "Jetzt beitreten"}, "faux_mods_ui": {"save_mods": "Mods speichern", "mod_unlimited_health": "Unbegrenzte Gesundheit", "mod_unlimited_stamina": "Unbegrenzte Ausdauer", "mod_game_speed": "Spielgeschwindigkeit", "mod_game_speed_value": "500", "mod_off": "Aus", "mod_on": "Ein"}, "pro_badge": {"pro": "Pro"}, "save_cheats_disable_confirm_dialog": {"turn_off_save_cheats": "„Mods speichern“ deaktivieren?", "are_you_sure_message": "Möchtest du „Mods speichern“ für dieses Spiel wirklich *deaktivieren*?\n\nDeine aktuellen Mod-Konfigurationen werden nicht gespeichert.", "cancel": "Abbrechen", "turn_off": "Ausschalten", "dont_show_again": "Nicht mehr anzeigen"}, "save_cheats_icon": {"save_cheats_is_active": "„Mods speichern“ ist aktiv", "save_cheats_is_inactive": "„Mods speichern“ ist nicht aktiv", "unlock_save_mods": "Speichermods entsperren", "join_now": "Jetzt beitreten"}, "plan_details_dialog": {"pro": "Pro", "plan_details": "Plandetails", "billed_monthly": "Monatliche A<PERSON>nung", "billed_yearly": "Jährliche Abrechnung", "$x_days_left_in_trial": "$x Tage Testversion übrig", "next_billing": "Nächste Abrechnung", "until": "Bis", "your_pro_plan_includes": "Dein PRO-Plan enthält:", "in_app_controls": "In-App-*Steuerung*", "remote_mobile_app": "Remote-*App für Mobilgeräte*", "in_game_overlay": "In-Game-Overlay", "save_cheats": "Mods *speichern*", "game_boosting": "Zusätzliche Spielsteigerung", "exclusive_themes": "Exklusive Themen", "discord_role": "Besondere Rolle in Discord", "priority_support": "VIP-Support", "learn_more": "<PERSON><PERSON> er<PERSON>", "need_help": "<PERSON>rauchst du Hilfe?", "help_message": "<PERSON>eh dir unsere-[FAQ](https://wemod.gg/faq) an oder schicke eine E-Mail an [<EMAIL>](mailto:<EMAIL>).", "cancel_subscription": "Mitgliedschaft kündigen", "suggest_cheat_ideas": "Mod-Ideen vorschlagen", "payment_overdue": "Überfällige Zahlung", "switch_to_yearly": "Zu Jahresplan wechseln", "save_$x_or_more": "Mindestens $x % sparen"}, "breadcrumbs": {"back_to_dashboard": "Zurück zur Startseite", "back_to_titles": "Zurück zu den Spielen", "back_to_free_games": "Zurück zu den kostenfreien Spielen", "back_to_my_games": "<PERSON><PERSON><PERSON> zu meinen Spielen", "back_to_queue": "<PERSON><PERSON><PERSON> zu kommenden", "back_to_games": "Zurück zum Erkunden", "back_to_favorites": "<PERSON><PERSON><PERSON> zu den Favoriten", "back_to_game_pass": "<PERSON>urück zum Spiele-Pass", "back_to_most_popular": "Zur<PERSON> zu den beliebtesten Produkten", "back_to_community_choice": "Zurück zur Community-Wahl", "back_to_recently_played": "Zurück zu den kürzlich gespielten Spielen", "back_to_all_supported_games": "<PERSON><PERSON><PERSON> zu allen unterstützten Spielen"}, "desktop_shortcut": {"play_$title": "$title spielen"}, "genres": {"action": "Aktion", "adventure": "<PERSON><PERSON><PERSON>", "casual": "Casual", "fps": "Ego-Shooter", "horror": "Horror", "indie": "Indie", "open_world": "Open-World", "platformer": "Platformer", "puzzle": "<PERSON><PERSON><PERSON>", "rpg": "RPG", "racing": "<PERSON><PERSON>", "shooter": "Shooter", "simulation": "Simulation", "sports": "Sport", "strategy": "Strategie", "survival": "Survival", "fighting": "<PERSON><PERSON><PERSON>"}, "game_collection": {"free_games": "Spiele für kostenfreie Installation", "my_games": "<PERSON><PERSON>", "most_popular": "<PERSON> populärsten", "popular": "Beliebt", "recently_played": "<PERSON><PERSON><PERSON><PERSON> gespielt", "game_pass": "PC Spiele-Pass", "favorites": "<PERSON><PERSON>", "playable": "Bereit zum Spielen", "unsupported": "<PERSON>cht unterstützt", "unsupported_and_not_installed": "<PERSON>cht unterstützt", "installable": "Jetzt installieren", "launch_without_mods": "<PERSON><PERSON> starten", "community_choice": "Community-Wahl", "free_games_to_install": "<PERSON><PERSON><PERSON><PERSON><PERSON> von Mods mit einem kostenfreien, unterstützten Spiel starten", "you_have_no_installed_games": "Du hast keine installierten S<PERSON>le.", "install_a_free_game_to_get_started": "Installiere ein kostenfreies Spiel, um mit WeMod loszulegen.", "you_have_no_favorites": "Du hast noch keine Favorite<PERSON>.", "add_games_to_your_favorites_by_selecting_the_star_icon": "Füge Spiele zu deinen Favoriten hinzu, indem du das *Stern*-Symbol auswählst.", "search_your_game_library": "Spiele-Bibliothek durchsuchen", "search_games": "<PERSON><PERSON><PERSON> suchen", "search_your_favorites": "<PERSON>n durch<PERSON>", "go": "Starten", "no_results": "<PERSON><PERSON>", "no_results_advice": "<PERSON>ch anderen Begriffen suchen", "all": "Alle", "favorites_not_installed": "<PERSON><PERSON> nicht installiert", "add_games_to_your_favorites": "Spiele zu deinen Favoriten hinzufügen", "all_supported_games": "Alle unterstützten Spiele", "new_and_updated_games": "Neu und kürzlich aktualisiert", "sort_by": "Sortieren nach", "a_z": "A-Z", "maps": "<PERSON><PERSON>", "teleport": "Teleport", "live_location": "Live Standort", "games_with_maps": "<PERSON><PERSON>le mit Karten", "precision_mods": "Präzisionsmods", "overlay": "Overlay", "my_videos": "<PERSON>ne <PERSON>s", "recently_recorded": "<PERSON><PERSON><PERSON><PERSON> aufgezeichnet", "game": "Spiel"}, "ad_dialog": {"ad_revenue_uses": "<PERSON>ine Mods werden in Kürze gestartet. Unsere Werbeeinnahmen werden dazu benutzt, die Ersteller zu unterstützen, und ermöglichen es uns, ein kostenloses WeMod-Erlebnis anzubieten.", "get_an_ad_free_experience": "Ohne Werbung nutzen"}, "suggest_cheats": {"have_an_idea_for_a_cheat": "Hast du Ideen für Mods?", "suggest_cheat": "Mod vorschlagen", "info_tooltip": "Dies ist unsere Ersteller-Warteschlange. Hier kannst du sehen, an welchen Spielen unsere Ersteller gerade arbeiten, und den Spielen, die du gerne spielen möchtest, einen Boost verpassen. Die Ersteller beginnen mit der Modifikation von Spielen gemäß ihrer Position in der Warteschlange. Der Zeitpunkt der Veröffentlichung hängt immer davon ab, wie viel Aufwand die Erstellung und das Testen von Mods für das jeweilige Spiel sowie für die Spiele davor erfordert.", "suggest_a_cheat": "Jetzt Mod vorschlagen", "start_typing": "Text eingeben", "have_an_idea": "Haben <PERSON> eine I<PERSON>e?", "thank_you": "Vielen Dank! Deine Ideen wurden übermittelt.", "disclaimer": "Wenn du eine WeMod Pro-Mitgliedschaft abonnierst, akzeptierst du die [allgemeinen Geschäftsbedingungen](website://terms). Du kannst die Mitgliedschaft jederzeit kündigen.", "error": "<PERSON>ider gab es einen Fehler beim Übermitteln deiner Mod-Ideen. Bitte versuch es später noch einmal."}, "beta_tag": {"beta": "Beta"}, "overlay_education_dialog": {"in_game_overlay": "In-Game-Overlay", "overlay_requirements": "Auf deinem PC muss *Xbox Game Bar* aktiviert sein und du benötigst Windows 10 Version $version oder höher.", "install_message": "Wusstest du, dass du mit dem WeMod-Overlay Mods im Spiel verwenden kannst? Drücke nach der Installation einfach ***Win* *G***, um Mods zu öffnen und während des Spiels zu steuern.", "post_install_message": "Wusstest du, dass du mit dem WeMod-Overlay Mods im Spiel verwenden kannst? Drücke beim S<PERSON>len einfach ***Win* *G***, um Mods zu ö<PERSON>nen und zu steuern.", "open_game_bar_settings": "Game Bar-Einstellungen öffnen", "install_overlay": "Overlay installieren", "try_it_now": "Jetzt ausprobieren", "start_free_trial": "Kostenlose Testversion starten"}, "featured_game_feed": {"sponsored": "Gesponsert"}, "game_search_input": {"go": "Starten", "search": "<PERSON><PERSON>"}, "pro_cta_label": {"start_free_trial": "Kostenlose Testversion starten", "upgrade_to_pro": "Upgrade auf Pro vornehmen"}, "welcome_mat_dialog": {"welcome_to_wemod": "<PERSON><PERSON><PERSON><PERSON> bei **WeMod!**", "enjoy_all_of_wemod_with_pro": "Genieße alle Vorteile von **WeMod** mit Pro!", "wemod_is_free_to_use": "WeMod ist aufgrund unserer Pro-Mitglieder kostenfrei nutzbar. Nutze den Service weiter kostenfrei oder unterstütze uns und profitiere von großartigen Features!", "free_membership": "Kostenfreie Mitgliedschaft", "$x_cheats_for_$y_games": "$x Mods für $y Spiele", "hotkey_cheat_controls": "Mod-Steuerung über Tastenkürzel (F1 usw.)", "auto_game_and_version_detection": "Automatische Spiel- und Versionserkennung", "safe_and_virus_free_cheats": "Sichere und virusfreie Mods", "discord_community_access": "Zugriff auf die Discord-Community", "continue_with_free_membership": "Mit kostenfreier Mitgliedschaft fortfahren", "upgrade_to_pro": "Upgrade auf Pro vornehmen", "everything_in_free_membership": "Alle Vorteile der kostenfreien Mitgliedschaft +", "interactive_cheat_controls": "Interaktive Mod-Steuerung", "save_cheats_between_plays": "Mods zwischen Spielen speichern", "remote_mobile_app": "Remote-*App für Mobilgeräte*", "in_game_cheat_overlay": "In-Game-Mod-Overlay", "game_boosting": "Spiel-Boosts", "exclusive_themes": "Exklusive Themen", "special_role_in_discord": "Besondere Rolle in Discord"}, "creators_list": {"$x_game": "**$x** Spiel", "$x_games": "**$x** <PERSON><PERSON><PERSON>"}, "email_dialog": {"thank_you_for_your_payment": "Vielen Dank für deine Zahlung!", "the_creators": "<PERSON> Ersteller", "your_subscription_helps": "Deine Mitgliedschaft unterstützt unsere Ersteller, die Mods für deine Lieblingsspiele entwickeln.", "account_email": "E-Mail-Adresse des Kontos", "please_enter_a_valid_email_address": "<PERSON>te gib eine gültige E-Mail-Adresse ein, um auf dein Pro-Konto zuzugreifen.", "your_email_address": "<PERSON><PERSON>-Adresse", "continue_with_pro": "PRO bleiben", "note_you_can_update": "Hinweis: Du kannst diese Zugangsdaten jederzeit in deinen Kontoeinstellungen aktualisieren."}, "support_wemod_footer": {"members_like_you_make_wemod_possible": "Mit<PERSON><PERSON>r wie du machen WeMod erst möglich und halten unsere Bibliothek mit $x Spielen stets auf dem neuesten Stand"}, "payment_processing": {"we_are_processing_your_payment": "Wir bearbeiten deine Zahlung.", "thanks_for_your_patience": "Vielen Dank für deine Geduld."}, "remote_education_dialog": {"wemod_remote": "WeMod *Remote*", "free_user_message": "Gute Neuigkeiten! WeMod Remote ist jetzt noch besser.\nAls Pro kannst du die Remote-App verwenden, um Spiele auf deinem PC zu spielen, <PERSON>en zu Tastenkürzeln zu suchen und Mods von deinem Handy aus zu steuern.", "pro_user_message": "Gute Nachrichten für Pro-Mitglieder! WeMod Remote ist jetzt noch besser.\nDu kannst die Remote-App nun verwenden, um Spiele auf deinem PC zu starten und nach Tastenkürzeln zu suchen, während du spielst.", "play_button_message": "Wusstest du, dass du jetzt dein Handy verwenden kannst, um Mods zu steuern und zu wählen, welche Spiele du auf deinem PC spielen möchtest?\nLade die Pro-Remote-App herunter und steuere WeMod vom Sofa aus.", "connect_remote": "Remote-<PERSON><PERSON> verbinden", "download_remote": "Remote-<PERSON><PERSON>", "download_for_phone": "<PERSON><PERSON> sich die **WeMod Remote** für **Android** & **iOS**:", "feature_control_mods": "<PERSON><PERSON> steuern", "feature_browse_maps": "<PERSON>rten durchsuchen", "feautre_teleport": "Teleport & mehr!", "scan_qr": "<PERSON>annen Sie den QR-Code oder besuchen Sie [wemod.com/remote](https://wemod.com/remote) auf Ihrem Handy"}, "remote_education_graphic": {"controls": "Steuerung", "hotkeys": "Tastenkürzel", "play": "<PERSON><PERSON><PERSON>", "no_more_alt_tabbing": "<PERSON><PERSON>hr!"}, "discord_tooltip": {"join_our_discord": "Discord anschließen", "connect_message": "Verbinde dich mit uns und anderen Mitgliedern der WeMod-Community und erhalte Zugang zu Geschenken, Veranstaltungen und vielem mehr.", "connect_discord": "Discord verbinden"}, "launch_without_mods_button": {"launch_without_mods": "Starten", "launch_failed": "Beim Starten deines Spiels ist ein Problem aufgetreten."}, "app_header_search": {"search_games": "<PERSON><PERSON><PERSON> suchen", "new": "neu", "updated": "aktual<PERSON><PERSON>", "no_cheats_available": "<PERSON><PERSON> verfügbar", "no_results_message": "<PERSON>s gibt keine <PERSON>, die deiner Suche entsprechen.", "my_games": "<PERSON><PERSON>", "favorites": "<PERSON><PERSON>", "recently_played": "<PERSON><PERSON><PERSON><PERSON> gespielt", "favorites_empty_message": "<PERSON>ine Favoriten werden hier angezeigt. Du kannst Spiele zu deinen Favoriten hinzufügen, indem du das Stern-Symbol auswählst."}, "pro_banner": {"support_wemod_with_pro": "WeMod mit **Pro** unterstützen", "features": "In-App-Steuerung *+* <PERSON>ds speichern *+* In-Game-Overlay *+* mehr!"}, "post_pro_upgrade": {"yearly_gift_toast_message": "**Glückwunsch! Du hast ein Geschenk von $sender erhalten!** &nbsp;Genieße 1 Jahr PRO", "monthly_gift_toast_message": "**Glückwunsch! Du hast ein Geschenk von $sender erhalten!** &nbsp;Genieße 1 Monat PRO", "view_details": "Details anzeigen"}, "notifications_settings": {"search_games": "<PERSON><PERSON><PERSON> suchen", "clear_search": "<PERSON><PERSON> löschen", "no_results_message": "<PERSON>s gibt keine <PERSON>, die deiner Suche entsprechen.", "no_followed_games_message": "Du folgst keinen Spielen.", "remove_from_followed": "<PERSON>cht länger folgen", "$game_removed_from_followed": "Du folgst **$game** nicht mehr."}, "follow_games": {"failed_to_set_notification_preferences": "Die Benachrichtigungseinstellungen konnten nicht festgelegt werden.", "$game_for_$platform_release_message": "Gute Neuigkeiten! Wir haben gerade neue Mods für $game für $platform veröffentlicht. Du kannst sie jetzt ausprobieren!", "$game_for_$platform_update_message": "Los geht‘s! Wir haben gerade aktualisierte Mods für $game für $platform veröffentlicht. Du kannst sie jetzt ausprobieren!", "play_now": "Jetzt spielen", "release_notification_image_watermark": "Neue Mods!", "update_notification_image_watermark": "Aktualisierte Mods!"}, "map_banner": {"interactive_map": "Interaktive Karte", "interactive_maps": "Interaktive Karten", "$name_map": "$name Karte"}, "game_maps": {"map_render_error_message": "<PERSON><PERSON> deiner Karte ist ein Problem aufgetreten. Bitte versuch es noch einmal."}, "pro_onboarding_tooltip": {"youre_a_true_pro": "Du bist ein echter *Pro*", "say_hello_to_the_ultimate_gaming_experience": "Genieße das ultimative Spielerlebnis mit exklusiven Features wie *Mods speichern*, dem *In-Game-Overlay*, *Remote-App für Mobilgeräte* und mehr!", "explore_pro_features": "Pro-Features erkunden", "click_to_see_the_benefits_of_pro": "<PERSON>er klicken, um mehr über die Vorteile von Pro zu erfahren"}, "nps_dialog": {"how_likely_are_you_to_recommend_wemod_to_a_friend": "Wie wahrscheinlich ist es, dass du WeMod einem Freund / einer Freundin empfiehlst?", "not_likely": "Überhaupt nicht wahr<PERSON><PERSON>lich", "extremely_likely": "<PERSON><PERSON><PERSON>", "glad_youre_enjoying_the_app": "Wir freuen uns sehr, dass dir die App gefällt!", "review_on_trustpilot": "<PERSON><PERSON><PERSON><PERSON> du bitte auf Trustpilot ein gutes Wort für uns einlegen?", "submit": "Übermitteln", "sure": "Gerne!", "no_thanks": "<PERSON><PERSON> danke", "send": "Senden", "skip_feedback": "Feedback überspringen", "sorry_you_arent_satisfied": "Es tut uns leid, dass du nicht zufrieden bist.", "what_can_we_do_better": "<PERSON>te lass uns wissen, was wir besser machen können:", "share_feedback": "<PERSON>te lass uns Feedback über deine Erfahrungen mit WeMod zukommen.", "feedback_placeholder": "Text hier e<PERSON>ben"}, "maps_nps_dialog": {"how_satisfied_are_you_with_the_map": "Wie zufrieden sind Sie mit der interaktiven Karte?", "not_satisfied_at_all": "Überhaupt nicht zufrieden", "extremely_satisfied": "<PERSON><PERSON> zufrieden", "thank_you_for_your_feedback": "Vielen Dank für dein Feedback!", "let_us_know_how_to_improve": "<PERSON>te lass uns wissen, was wir tun können, um die Karte zu verbessern", "feedback_placeholder": "Text hier e<PERSON>ben", "skip_feedback": "Feedback überspringen", "send": "Senden"}, "post_assistant_nps_dialog": {"how_satisfied_are_you": "Wie zufrieden bist du mit der KI-Spielhilfe?", "not_satisfied_at_all": "Überhaupt nicht zufrieden", "extremely_satisfied": "<PERSON><PERSON> zufrieden", "thank_you_for_feedback": "Vielen Dank für dein Feedback!", "how_can_we_improve": "Bitte lass uns wissen, wie wir die Spielhilfe verbessern können.", "feedback_placeholder": "Text hier e<PERSON>ben", "skip_feedback": "Feedback überspringen", "send": "Senden"}, "game_guide_nps_dialog": {"thank_you_for_your_feedback": "Vielen Dank für dein Feedback!", "let_us_know_how_to_improve": "Lass uns wissen, wie wir den Leitfaden verbessern können.", "feedback_placeholder": "Text hier e<PERSON>ben", "skip_feedback": "Feedback überspringen", "send": "Senden"}, "time_limit_reached_post_game_dialog": {"daily_time_limit_exceeded": "Tägliches Zeitlimit *überschritten*.\nMit einem Upgrade auf ~~Pro~~ genießt du **uneingeschränktes** Modding.", "daily_time_limit_per_game_exceeded": "Tägliches Zeitlimit für dieses Spiel *überschritten*.\nMit einem Upgrade auf ~~Pro~~ genießt du **uneingeschränktes** Modding.", "free_can_use_wemod_for_1_hour_each_day": "Benutzer können WeMod *1 Stunde* pro Tag kostenfrei verwenden.", "free_can_use_wemod_for_1_hour_per_game_each_day": "Benutzer können WeMod *1 Stunde pro Spiel* pro Tag kostenfrei verwenden.", "free_can_use_wemod_for_2_hours_each_day": "Benutzer können WeMod *2 Stunden* pro Tag kostenfrei verwenden.", "free_can_use_wemod_for_2_hours_per_game_each_day": "Benutzer können WeMod *2 Stunden pro Spiel* pro Tag kostenfrei verwenden.", "free_can_use_wemod_for_3_hours_each_day": "Benutzer können WeMod *3 Stunden* pro Tag kostenfrei verwenden.", "free_can_use_wemod_for_3_hours_per_game_each_day": "Benutzer können WeMod *3 Stunden pro Spiel* pro Tag kostenfrei verwenden.", "free_can_use_wemod_for_4_hours_each_day": "Benutzer können WeMod *4 Stunden* pro Tag kostenfrei verwenden.", "free_can_use_wemod_for_4_hours_per_game_each_day": "Benutzer können WeMod *4 Stunden pro Spiel* pro Tag kostenfrei verwenden.", "free": "Kostenfrei", "pro": "Pro", "play_another_game_today": "Heute noch ein anderes Spiel spielen", "ill_wait_until_tomorrow": "Ich werde bis morgen warten", "time_limit_resets_in": "Das Zeitlimit wird zurückgesetzt in:", "1_hour": "**1** Stunde", "1_minute": "**1** <PERSON><PERSON>", "$hours_hours": "**$hours** Stunden", "$minutes_minutes": "**$minutes** Minuten"}, "time_limit_reached_pre_game_dialog": {"upgrade_to_pro_to_keep_playing": "Mit einem Upgrade auf* Pro* weiter auf Mods zugreifen!", "you_have_exceeded_your_1_hour_limit_for_today": "Du hast dein Limit von *einer Stunde* für heute erreicht", "you_have_exceeded_your_1_hour_limit_for_this_game_for_today": "Du hast dein Limit von *einer Stunde* für dieses Spiel für heute erreicht", "you_have_exceeded_your_2_hour_limit_for_today": "Du hast dein Limit von *zwei Stunden* für heute erreicht", "you_have_exceeded_your_2_hour_limit_for_this_game_for_today": "Du hast dein Limit von *zwei Stunden* für dieses Spiel für heute erreicht", "you_have_exceeded_your_3_hour_limit_for_today": "Du hast dein Limit von *drei Stunden* für heute erreicht", "you_have_exceeded_your_3_hour_limit_for_this_game_for_today": "Du hast dein Limit von *drei Stunden* für dieses Spiel für heute erreicht", "you_have_exceeded_your_4_hour_limit_for_today": "Du hast dein Limit von *vier Stunden* für heute erreicht", "you_have_exceeded_your_4_hour_limit_for_this_game_for_today": "Du hast dein Limit von *vier Stunden* für dieses Spiel für heute erreicht", "pro_users_have_unlimited_mod_access": "Pro-Benutzer haben unbegrenzten Zugang zu Mods und können die Remote-*App für Mobilgeräte* und *Mods speichern* nutzen.", "pro_users_have_unlimited_mod_access_mobile_free": "Pro-Ben<PERSON>er haben unbegrenzten Mod-Zugriff zusätzlich zu einer <i>werbefreien</i> Erfahrung und der Fähigkeit, <i><PERSON><PERSON> zu speichern</i>.", "ill_play_another_game": "Ich möchte noch ein anderes Spiel spielen", "ill_play_tomorrow": "Ich werde morgen wieder spielen"}, "time_limit_reached_alternate_graphic": {"unlimited": "Uneingeschränkt", "save_mods": "Mods speichern", "ad_free": "Werbefrei"}, "time_limit_reset_dialog": {"mod_access_restored": "Mod-Zugang **wiederhergestellt**!\nMit einem Upgrade auf *Pro* genießt du uneingeschränktes Modding", "you_have_1_hour_of_free_mod_access_today": "Du hast heute *eine Stunde* kostenfreien Mod-Zugang", "you_have_1_hour_of_free_mod_access_per_game_today": "Du hast heute *pro Spiel* *eine Stunde* kostenfreien Mod-Zugang", "you_have_2_hours_of_free_mod_access_today": "Du hast heute *zwei Stunden* kostenfreien Mod-Zugang", "you_have_2_hours_of_free_mod_access_per_game_today": "Du hast heute *pro Spiel* *zwei Stunde* kostenfreien Mod-Zugang", "you_have_3_hours_of_free_mod_access_today": "Du hast heute *drei Stunden* kostenfreien Mod-Zugang", "you_have_3_hours_of_free_mod_access_per_game_today": "Du hast heute *pro Spiel* *drei Stunden* kostenfreien Mod-Zugang", "you_have_4_hours_of_free_mod_access_today": "Du hast heute *vier Stunden* kostenfreien Mod-Zugang", "you_have_4_hours_of_free_mod_access_per_game_today": "Du hast heute *pro Spiel* *vier Stunden* kostenfreien Mod-Zugang", "pro_users_have_unlimited_mod_access": "Pro-Benutzer haben unbegrenzten Zugang zu Mods und können die Remote-*App für Mobilgeräte* und *Mods speichern* nutzen."}, "time_limit_enforcer": {"times_up": "Die Zeit ist um!", "upgrade_to_pro_to_play_beyond_your_daily_1_hour_limit": "Nimm ein Upgrade auf Pro vor, um Mods länger als dein tägliches 1-Stunden-Limit zu nutzen.", "upgrade_to_pro_to_play_beyond_your_daily_2_hour_limit": "Nimm ein Upgrade auf Pro vor, um Mods länger als dein tägliches 2-Stunden-Limit zu nutzen.", "upgrade_to_pro_to_play_beyond_your_daily_3_hour_limit": "Nimm ein Upgrade auf Pro vor, um Mods länger als dein tägliches 3-Stunden-Limit zu nutzen.", "upgrade_to_pro_to_play_beyond_your_daily_4_hour_limit": "<PERSON>mm ein Upgrade auf Pro vor, um Mods länger als dein tägliches 4-Stunden-Limit zu nutzen.", "upgrade_to_pro": "Upgrade auf Pro vornehmen", "upgrade_to_pro_to_play_beyond_your_daily_1_hour_limit_per_game": "Nimm ein Upgrade auf Pro vor, um Mods länger als dein tägliches 1-Stunden-Limit pro Spiel zu nutzen.", "upgrade_to_pro_to_play_beyond_your_daily_2_hour_limit_per_game": "Nimm ein Upgrade auf Pro vor, um Mods länger als dein tägliches 2-Stunden-Limit pro Spiel zu nutzen.", "upgrade_to_pro_to_play_beyond_your_daily_3_hour_limit_per_game": "Nimm ein Upgrade auf Pro vor, um Mods länger als dein tägliches 3-Stunden-Limit pro Spiel zu nutzen.", "upgrade_to_pro_to_play_beyond_your_daily_4_hour_limit_per_game": "Nimm ein Upgrade auf Pro vor, um Mods länger als dein tägliches 4-Stunden-Limit pro Spiel zu nutzen.", "upgrade_to_pro_to_use_mods_beyond_your_daily_limit": "Nimm ein Upgrade auf Pro vor, um Mods länger als dein tägliches Limit zu benutzen.", "five_minutes_left": "Noch 5 Minuten"}, "first_play_upgrade_prompt_dialog": {"level_up_with_pro": "Mit *Pro* ein höheres Level erreichen", "and_get_all_of_this": "<PERSON> <PERSON><PERSON><PERSON> von folgenden Vorteilen:", "jump_into_playing": "<PERSON><PERSON><PERSON> wieder mit dem Spielen weitermachen", "fast_access_with_overlay": "<PERSON><PERSON>eller Mod-<PERSON><PERSON><PERSON> mit Overlay", "level_up_with_pro_to_get": "<PERSON><PERSON>nne dir *Pro*, um von folgenden Vorteilen zu profitieren:", "in_game_overlay": "In-Game-Overlay", "save_mods": "Mods speichern", "no_ads": "<PERSON><PERSON>", "stay_free_and_start_game": "Frei bleiben und Spiel beginnen", "and_much_more": "... und vieles mehr!", "desktop_mod_controls": "Desktop-Mod-Steuerung", "your_game": "<PERSON><PERSON>", "mobile_app": "Mobile App", "overlay": "In-Game-*Overlay*", "advanced_mod_controls": "Erweiterte Mod-Steuerung"}, "assistant_button": {"label": "Warum werden diese Informationen für eine kostenlose Testversion benötigt?"}, "assistant_popout": {"unlock_the_power_of_ai": "✨ Entdecke die Leistung von KI mit deiner eigenen persönlichen Spielhilfe.", "ask_me_about_$game": "Stelle einfach eine Frage, um hilfreiche Tipps und Strategien für $game zu erhalten!"}, "assistant": {"general_error_message": "Wir entschuldigen uns für die Unannehmlichkeiten. Die KI-Spielhilfe reagiert derzeit nicht so, wie sie sollte. Sie befindet sich derzeit noch in der Entwicklung und wir bitten dich um etwas Geduld, während wir an Verbesserungen arbeiten. Sollte das Problem weiterhin bestehen, kannst du dich unter **<EMAIL>** an uns wenden.", "rate_limit_error_message": "😴 Leider macht die KI-Spielhilfe gerade eine wohlverdiente Pause. <PERSON><PERSON>, dass du diese Funktion ausprobiert hast! Bald gibt es hier wieder mehr Spaß und Unterstützung. Bis dahin wünschen wir dir viel Vergnügen beim <PERSON>!", "offline_error_message": "<PERSON>s sieht so aus, als wärst du offline. Bitte überprüfe deine Internetverbindung und versuch es noch einmal.", "game_guide_may_make_mistakes": "<PERSON><PERSON>l<PERSON><PERSON> kann <PERSON>hler machen, also überprüfen Sie doppelt ihre Antworten."}, "assistant_chat": {"type_in_a_question": "Gib eine <PERSON>age ein", "ask_anything": "Fragen Sie irgendetwas...", "share_your_feedback_positive": "<PERSON><PERSON> uns mit, was dir an dieser Antwort gefallen hat", "share_your_feedback_negative": "<PERSON><PERSON> uns mit, was dir an dieser Antwort nicht gefallen hat", "thanks_for_your_feedback_positive": "Danke für dein Feedback! <PERSON><PERSON><PERSON><PERSON>, dass ich helfen konnte.", "thanks_for_your_feedback_negative": "Danke! <PERSON><PERSON> wird mir helfen, mich zu verbessern.", "welcome_message_1": "Hall<PERSON> und willkommen zurück! Wie kann ich dir heute mit $game helfen?", "welcome_message_2": "Bist du bereit, deine Fähigkeiten in $game zu entfesseln? Sag mir, was du brauchst, damit wir loslegen können!", "welcome_message_3": "Willkommen zurück! Ich möchte dir helfen, $game zu meistern. Was steht heute auf deinem Spielplan?", "welcome_message_4": "Bist du bereit, deine Fähigkeiten in $game zu verbessern? Lass uns gemeinsam loslegen!", "hello_$user": "Hallo, $user."}, "assistant_box": {"game_guide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "beta": "Beta"}, "sources_button": {"sources": "<PERSON><PERSON>", "all_under_$license": "*Alle unter [$license-<PERSON><PERSON>z]($url)"}, "feedback_buttons": {"positive_feedback": "Positives Feedback geben", "negative_feedback": "Negatives Feed<PERSON> geben"}, "assistant_history_list": {"alpha_message": "**Alpha** Die KI-Spielhilfe befindet sich noch in der frühen Entwicklungsphase. Es sind bestimmt nicht alle Antworten korrekt. Durch die Nutzung der Spielhilfe stimmst du unseren [allgemeinen Geschäftsbedingungen](website://terms) und der [Datenschutzrichtlinie](website://privacy) zu.", "thinking": "Wir denken darüber nach", "mission_helper": "Mission<PERSON>lfer"}, "assistant_icon": {"alpha": "Alpha"}, "assistant_manager": {"overlay_prompt_message": "WeMod In-Game-Overlay: Du kannst mir auch Fragen im Overlay stellen, ohne das Spiel zu verlassen.\n\n1. [Overlay installieren oder aktualisieren](wemod://overlay-install?location=assistant_message)\n2. Zum Aktivieren Windows + G drücken\n\n[Unterstützung erhalten](https://wemod.gg/support-assistant-overlay)"}, "assistant_education_dialog": {"ai_game_guide": "KI-Spielhilfe", "unlock_the_power_of_ai_with_your_own_personal_game_guide": "✨ Entdecke die Leistung von KI mit deiner eigenen persönlichen Spielhilfe. Stelle einfach eine Frage, um hilfreiche Tipps und Strategien für ausgewählte Spiele zu erhalten.\nAußerdem kannst du [das Overlay aktualisieren](wemod://overlay-install?location=assistant_education_dialog) und erhältst praktische Hilfe im Spiel.", "ask_a_question": "Eine Frage stellen", "type_in_a_question": "Gib eine <PERSON>age ein", "chat_question_1": "Zu welchem Sternensystem gehört Jemison?", "chat_answer_1": "<PERSON><PERSON><PERSON> geh<PERSON> zu dem Sternensystem Alpha Centauri.", "chat_question_2": "Was ist der Wert des Beowulf?", "chat_answer_2": "Der Wert des Beowulf-Gewehrs in Starfield beträgt 4820 Credits. <PERSON><PERSON> hat 8 verfügbare Mod-Slots.", "chat_question_3": "Welches Gewehr bietet die bessere Genauigkeit: Breach oder Coachman?", "chat_answer_3": "Das Breach hat mit 55,3 % die bessere Zielgenauigkeit, da die Zielgenauigkeit des Coachman nur 38,4 % beträgt."}, "time_limit_pre_game_dialog": {"wemod_is_free_for_$x_hours_each_day": "WeMod ist jeden Tag für **$x Stunden** kostenfrei nutzbar", "upgrade_to_pro_for_unlimited_modding": "Mit einem Upgrade auf **Pro** genießt du *uneingeschränktes* Modding.", "skip_for_now": "<PERSON><PERSON><PERSON>"}, "time_limit_graphic": {"unlimited": "Uneingeschränkt"}, "time_limit_countdown": {"free_mod_access_time_remaining": "Verbleibende Zeit für kostenfreien Mod-Zugang", "daily_free_mod_access_exceeded": "Täglicher kostenfreier Mod-Zugang *überschritten*", "free_wemod_users_are_limited_to_$hours_hours_per_day": "WeMod-Benutzer können pro Tag Mods maximal $hours Stunden lang kostenfrei benutzen.", "upgrade_now": "Jetzt Upgrade vornehmen", "until_free_mod_access_restored": "Bis der kostenfreie Mod-Zugang **wiederhergestellt** ist", "free_mod_access_reset_tooltip": "Der kostenfreie Mod-Zugang wird um Mitternacht (Ortszeit) wiederhergestellt"}, "time_remaining_post_game_dialog": {"time_remaining": "Verbleibende Zeit:", "1_hour": "**1** Stunde", "1_minute": "**1** <PERSON><PERSON>", "$hours_hours": "**$hours** Stunden", "$minutes_minutes": "**$minutes** Minuten", "upgrade_to_pro_for_unlimited_modding": "Mit einem Upgrade auf **Pro** genießt du *uneingeschränktes* Modding."}, "favorite_button": {"mark_as_favorite": "<PERSON><PERSON><PERSON> dieses Spiel als Favorit."}, "follow_button": {"notify_when_mods_update": "Ich möchte benachrichtigt werden, wenn neue Mods für dieses Spiel veröffentlicht oder aktualisiert werden."}, "maps_education_dialog": {"upgrade_to_pro_and_try_interactive_maps": "Upgrade auf **Pro** vornehmen und interaktive Karten ausprobieren", "find_everything_youre_looking_for": "Finde *alles*, was du suchst und mehr", "ill_try_later": "Ich werde es später versuchen", "check_out_our_new_interactive_maps": "Sieh dir unsere *neuen* interaktiven Karten an", "open_maps": "<PERSON><PERSON>"}, "teleport_education_dialog": {"upgrade_to_pro_and_try_teleport_maps": "Wechseln Sie zu **Pro**, um auf Karten zu _teleportieren_", "instantly_go_where_you_want": "*Sofort* gelangen <PERSON> mit einem <PERSON>, wo <PERSON>e möchten", "ill_try_later": "Ich werde es später versuchen", "teleport_with_interactive_maps": "_Teleportieren_ Sie mit der interaktiven Karte", "open_maps": "<PERSON><PERSON>"}, "maps_graphic": {"gun": "<PERSON><PERSON><PERSON>", "vehicle": "Fahrzeug", "item": "Artikel", "enemy_camp": "Feindliches Lage"}, "title_settings_menu": {"select_game_source": "Spiel<PERSON><PERSON> auswählen", "game_source": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "display_options": "Anzeigeoptionen", "create_desktop_shortcut": "Desktop-Verknüpfung erstellen", "added_to_your_desktop": "Zu deinem Desktop hinzugefügt", "mods_version": "Mods-Version", "reset_auto_pins": "Auto-Pins zurücksetzen"}, "game_selector": {"installed": "Installiert", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "view_on_$platform": "Anzeigen auf $platform", "select_game_source": "Spiel<PERSON><PERSON> auswählen"}, "game_installer": {"were_having_trouble_opening_$platform": "Wir haben Probleme beim Öffnen von $platform. <PERSON><PERSON> sic<PERSON>, dass das Spiel installiert ist, oder füge ein benutzerdefiniertes Spiel hinzu."}, "add_game_menu": {"install": "Installieren", "free": "<PERSON><PERSON><PERSON>", "get_on_$platform": "Holen <PERSON>e sich auf $platform", "add_game_manually": "Spiel manuell hinzufügen", "not_detected_message": "<PERSON>n WeMod dein Spiel nicht automatisch gefunden hat, musst du die Spielverknüpfung oder die ausführbare Datei (.exe) manuell hinzufügen.", "installed": "Installiert", "added": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "display_options_menu": {"show_hotkeys": "<PERSON><PERSON>s anzeigen", "show_hotkeys_description": "Hotkeys sind auch aktiv, wenn sie versteckt sind"}, "mod_onboarding": {"get_started_in_$x_steps": "In $x <PERSON><PERSON><PERSON><PERSON>en", "step_1": "Schritt 1", "step_2": "Schritt 2", "step_3": "Schritt 3", "game_not_installed_step_1_instructions": "Klicke auf *Spiel hinzufügen*, um dein Spiel zu installieren.", "game_not_installed_step_1_instructions_free": "Klicke auf *Kostenlos installieren*, um dein Spiel zu installieren.", "game_not_installed_step_2_instructions": "<PERSON><PERSON>e auf **<PERSON><PERSON><PERSON>**, um dein Spiel zu starten.", "game_not_installed_step_3_instructions": "Aktiviere die **Mods**, die du verwenden möchtest.", "game_installed_step_1_instructions": "<PERSON><PERSON>e auf **<PERSON><PERSON><PERSON>**, um dein Spiel zu starten.", "game_installed_step_2_instructions": "Aktiviere die **Mods**, die du verwenden möchtest.", "modding_tip": "*Mod-Tipp:* Verwende *Alt + Tab*, um während des Spielens zu WeMod zu wechseln, oder verwende die Tastenkombinationen."}, "mod_onboarding_button_graphic": {"install": "Installieren"}, "mod_onboarding_mods_graphic": {"player": "<PERSON><PERSON><PERSON>", "unlimited_health": "Unbegrenzte Gesundheit", "unlimited_stamina": "Unbegrenzte Ausdauer"}, "title_settings_button": {"mod_settings": "Mod-Einstellungen"}, "version_history_menu": {"unreleased": "Unverö<PERSON><PERSON><PERSON>t", "$x_mod": "$x Mod", "$x_mods": "$x Mods", "latest": "Neueste"}, "cyberpunk_mission_help": {"mission_helper": "Mission<PERSON>lfer", "live": "LIVE", "detecting_mission": "Mission wird erkannt...", "get_help": "<PERSON><PERSON><PERSON> erhalten", "offline": "Offline", "launch_message": "<PERSON><PERSON> per<PERSON>önlicher Assistent, der dir bei deiner aktuellen Mission hilft. Starte das Spiel, um zu beginnen.", "mission_help_prompt": "<PERSON>ch spiele die Mission \"$mission\", bitte gib mir eine prägnante Anleitung für die Mission."}, "elden_ring_boss_guide": {"elden_ring_boss_guide": "Boss<PERSON>An<PERSON><PERSON><PERSON>", "all_the_info_you_need": "<PERSON><PERSON> Infos, die du brauchst, um jeden Boss in Elden Ring zu besiegen.", "view_guide": "Anleitung ansehen", "step_by_step_$boss_guide": "<PERSON><PERSON><PERSON> für Schritt erklärt: So besiegst du **$boss**.", "includes_video_guide": "Inklusive Videoanleitung"}, "onboarding": {"select_a_game": "Ein Spiel auswählen", "loading_games": "<PERSON><PERSON> finden", "empty_search_title": "<PERSON><PERSON> gefunden", "empty_search_message": "<PERSON><PERSON><PERSON>, nach einem anderen <PERSON> zu suchen, oder sieh dir an, was gerade beliebt ist."}, "onboarding_all_games_card": {"browse_all": " Durchsuche die über 3000 Spiele auf WeMod"}, "onboarding_game_tutorial": {"press_spacebar_to_continue": "<PERSON><PERSON><PERSON> die **<PERSON><PERSON><PERSON>**, um fortzufahren", "welcome_to_game_page": "Willkommen auf deiner Spieleseite!", "play_mod_enjoy": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>. So einfach ist das!", "add_your_game": "<PERSON><PERSON> hinzufügen", "use_add_game_button": "Verwende die Schaltfläche **Spiel hinzufügen**, um die .exe-Datei deines Spiels hinzuzufügen oder es zu installieren.", "use_play_game_button": "Verwende die Schaltfläche **Spielen**, um deine Spiele zu starten.", "launch_your_game": "Spiel starten", "customize_with_mods": "Spiel mit Mods anpassen", "toggle_and_adjust_mods": "Steuere und verwalte deine **Mods** über die Mod-Steuerung.\n\n*Mod-Tipp:* Verwende *Alt + Tab*, um während des Spielens zu WeMod zu wechseln.", "use_hotkeys_for_convenience": "Komfortable Hotkeys", "customizable_keyboard_shortcuts": "<PERSON>utze anpass<PERSON>e **Tastenkürzel** für noch schnelleren Mod-Einsatz im Spiel.", "skip": "Überspringen", "next_arrow": "->", "back_arrow": "<-", "done": "<PERSON><PERSON><PERSON>", "play": "<PERSON><PERSON><PERSON>", "save_mods": "Mods speichern", "pro": "PRO", "unlimited_health": "Unbegrenzte Gesundheit", "player": "<PERSON><PERSON><PERSON>", "off": "Aus", "on": "Ein", "unlimited_stamina": "Unbegrenzte Ausdauer", "inventory": "Inventar", "unlimited_ammo": "Unbegrenzte Munition", "unlimited_items": "Unbegrenzte Gegenstände", "speed": "Geschwindigkeit", "ctrl_f1": "Strg F1", "ctrl_shift_f1": "Strg Umschalt F1", "f4": "F4", "f3": "F3", "f2": "F2", "f1": "F1"}, "sidebar_user_menu": {"help": "<PERSON><PERSON><PERSON>", "share_feedback": "Feedback teilen"}, "sidebar_game_lists": {"favorites": "<PERSON><PERSON>", "supported": "Mod wird unterstützt", "other": "<PERSON><PERSON>", "view_all_$number_games": "Alle $number Spiele ansehen ->", "new": "<PERSON>eu"}, "sidebar_game_menu": {"add_to_favorites": "<PERSON><PERSON> <PERSON><PERSON> hinzufügen", "remove_from_favorites": "Aus Favoriten entfernen", "launch_without_mods": "<PERSON><PERSON> starten", "follow": "Folgen", "unfollow": "Nicht mehr folgen", "play": "<PERSON><PERSON><PERSON>"}, "app_sidebar_search_button": {"search": "<PERSON><PERSON>"}, "app_search": {"search": "<PERSON><PERSON>", "use_keys_to_navigate": "Verwende *↑* *↓* zum Navigieren", "no_cheats_available": "<PERSON><PERSON> verfügbar", "no_results_message": "<PERSON>s gibt keine <PERSON>, die deiner Suche entsprechen. Bitte suche erneut.", "my_games": "<PERSON><PERSON>", "recently_played_games": "K<PERSON>rzlich gespielte Spiele", "recent_games": "Aktuelle Spiele"}, "remote_button": {"connect_phone": "Telefon verbinden", "reconnect_phone": "Telefon erneut verbinden", "connected": "Verbindung wurde hergestellt", "promo_tooltip": "Steuern Sie Ihre Mods und durchsuchen Sie Karten von Ihrem Telefon."}, "overlay_button": {"experiencing_overlay_issues": "<PERSON><PERSON><PERSON> Si<PERSON> Overlay-Probleme?", "improve_overlay_feedback": "Wir arbeiten daran, das Overlay zu verbessern. Ein Problem gefunden? Ihr Feedback hilft uns, es besser zu machen.", "install_overlay": "Overlay installieren", "shortcut_key": "Win+G", "overlay": "Overlay", "overlay_feedback": "Overlay-Fe<PERSON>back", "overlay_unsupported": "Überlagerung nicht unterstützt", "report_an_issue": "Ein Problem melden", "tooltip_message": "<PERSON><PERSON><PERSON> Si<PERSON> **$hotkey** während des Spiels, um sofort auf Ihre Mods zuzugreifen.", "unsupported_tooltip_header": "Spiel unterstützt Overlay nicht", "unsupported_tooltip_message": "$game unterstützt das Overlay noch nicht. Sie können trotzdem ohne das Overlay spielen, während wir daran arbeiten, die Unterstützung für Spiele zu verbessern.", "disabled_tooltip_header": "Over<PERSON> de<PERSON>", "disabled_tooltip_message": "Das neue WeMod-Overlay ist deaktiviert. Bitte aktiviere es im Anpassungsbereich des Einstellungsmenüs."}, "overlay_header": {"back_to_game": "Zurück zum Spiel"}, "overlay_window_menu": {"settings": "Einstellungen", "clip_last_$seconds_s": "Clip (letzte $secondss)", "or_press_$hotkey": "Oder drücke $hotkey"}, "overlay_launch_notification": {"press_$hotkey": "Drücken Sie $hotkey", "to_toggle_overlay": "um das In-Game-Overlay umzuschalten"}, "house_ad": {"save_mods_title": "Mods **speichern**", "save_mods_description": "Erinnere dich an deine Mod-Steuerung **zwischen den Spielen**.", "remote_app_title": "**Remote**-App", "remote_app_description": "Vergiss die Hotkeys.\nNutze **dein Telefon**, um Mods zu steuern.", "game_boosting_description": "<PERSON>er<PERSON><PERSON>, um zu bee<PERSON>f<PERSON>, welche **Spiele wir als nächstes hinzufügen**.", "game_boosting_title": "Spiel-**Boosting**", "exclusive_themes_title": "Exklusive **Themen**", "exclusive_themes_description": "Aufpeppen der App mit etwas **mehr Farbe**.", "remove_ads_with_wemod_pro": "Entferne Werbung mit WeMod **Pro**"}, "overlay_nps_dialog": {"how_satisfied_are_you_with_the_overlay": "Wie zufrieden bist du mit dem Overlay?", "not_satisfied_at_all": "Überhaupt nicht zufrieden", "extremely_satisfied": "<PERSON><PERSON> zufrieden", "thank_you_for_your_feedback": "Vielen Dank für dein Feedback!", "let_us_know_how_to_improve": "Lass uns wissen, wie wir das Overlay verbessern können", "feedback_placeholder": "Text hier e<PERSON>ben", "skip_feedback": "Feedback überspringen", "send": "Senden"}, "profile": {"edit_profile": "<PERSON><PERSON>", "member_since": "<PERSON><PERSON><PERSON><PERSON> seit", "pro_since": "Pro seit", "my_games": "<PERSON><PERSON>", "boosts": "Bo<PERSON><PERSON>", "youre_a_pro": "<PERSON>e sind ein *Pro*", "explore_pro_features": "Pro-Features erkunden", "your_pro_subscription": "Ihr WeMod Pro-Abonnement gibt Ihnen Zugang zu exklusiven Funktionen wie *Mods speichern*, *In-Game Overlay*, *Mobile Remote* und mehr.", "support_wemod": "WeMod *Go Pro* unterstützen", "save_mods": "Mods speichern", "save_mods_desc": "Speichern Sie Ihr Mods-Setup und steigen Sie wieder ein", "in_game_overlay": "In-Game-Overlay", "in_game_overlay_desc": "Mod im Spiel mit dem WeMod-Overlay", "remote_mobile_app": "Fernbedienungs-App", "remote_mobile_app_desc": "Verwenden Sie die WeMod-App, um Mods zu konfigurieren", "and_a_lot_more": "Und vieles mehr", "and_a_lot_more_desc": "<PERSON>ie Prioritätsunterstützung, <PERSON>n, Spiel-Boosting usw.", "followed_games": "Verfolgte Spiele", "no_followed_games": "Noch keine verfolgten Spiele", "no_followed_games_description": "Folgen Sie Spielen, um über Mod-Updates und Neuerscheinungen auf dem Laufenden zu bleiben!", "explore_games": "Spiele erkunden", "view_all": "Alle anzeigen →"}, "achievements": {"achievements": "Errungenschaften", "$value_week": "*$value* Woche", "$value_month": "*$value* Monat", "$value_months": "*$value* Monate", "$value_year": "*$value* Jahr", "$value_years": "*$value* Jahre", "one_week_club": "1-Wochen-Club", "one_month_club": "1-Monats-Club", "three_months_club": "3-Monats-Club", "six_months_club": "6-<PERSON>ts-Club", "one_year_club": "1-Jahres-Club", "two_years_club": "2-J<PERSON><PERSON>-Club", "three_years_club": "3-J<PERSON><PERSON>-Club", "four_years_club": "4-<PERSON><PERSON><PERSON>-Club", "five_years_club": "5-<PERSON><PERSON><PERSON>-Club", "six_years_club": "6-<PERSON><PERSON><PERSON>-Club", "seven_years_club": "7-<PERSON><PERSON><PERSON>-Club", "eight_years_club": "8-<PERSON><PERSON><PERSON>-Club", "nine_years_club": "9-<PERSON><PERSON><PERSON>-Club", "ten_years_club": "10-<PERSON><PERSON><PERSON>-Club", "joined_wemod": "<PERSON><PERSON><PERSON> be<PERSON>", "map_explorer": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "overlay_announcement_dialog": {"introducing_the_all_new": "Einführung des brandneuen", "wemod_overlay": "<PERSON><PERSON><PERSON>", "a_faster_more_responsive_way_to_enhance_your_gameplay": "<PERSON><PERSON>, reaktionsfähi<PERSON><PERSON> Möglichkeit, Ihr Gameplay zu verbessern, kein Xbox Game Bar erforderlich. Verwenden Sie **$hotkey** bei unterstützten Spielen, um auf Ihre Mods zuzugreifen."}, "map_mods_list": {"category_maps": "<PERSON><PERSON>", "key_location_teleport": "Schlüsselstandort Teleportieren", "open_in_map": "In Karte öffnen"}, "ad_popup": {"remove_ads_with_wemod_pro": "Entferne Werbung mit WeMod **Pro**"}, "map_feed_item": {"$count_maps": "$count karten"}, "new_badge": {"new": "<PERSON>eu"}, "overlay_settings_menu": {"restore_windows": "<PERSON><PERSON> wiederherstellen", "enable_notifications": "Benachrichtigungen aktivieren", "notification_position": "Benachrichtigungsposition", "top": "<PERSON><PERSON>", "bottom": "Unten", "center": "<PERSON><PERSON>"}, "overlay_mod_notification": {"set_$mod_$value": "**$mod setzen** $value", "$mod_enabled": "**$mod** aktiviert", "$mod_disabled": "**$mod** <PERSON><PERSON><PERSON><PERSON><PERSON>", "unread_instructions": "<PERSON><PERSON><PERSON><PERSON>"}, "rewards": {"rewards": "Bel<PERSON>nungen", "wemod_pro": "wemod **pro**", "pc_game_pass": "PC Spiele-Pass", "congrats_pro_member": "Glückwunsch Pro-Mitglied!", "you_get_one_month": "Du erhältst **1 Monat** **PC-Game-Pass**, geschenkt von uns! Die Belohnung ist verfügbar von $start bis $end.", "more_details": "Mehr Details", "hide_details": "Details verbergen", "claim_now": "Jetzt einlösen", "view_code": "Code anzeigen", "claim_gamepass_header": "Herzlichen Glückwunsch", "claim_gamepass_description": "<PERSON><PERSON><PERSON> Sie den untenstehenden Code auf der Xbox-Einlöseseite ein, um 1 Monat PC Game Pass zu erhalten.", "redemption_code": "Einlösungscode", "copy": "<PERSON><PERSON><PERSON>", "copied": "Kopiert!", "redeem": "<PERSON><PERSON><PERSON><PERSON>", "a_gift_for_you": "Ein Geschenk für Sie!", "congrats_pro_subscriber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Pro-Abonnent! Sie erhalten 1 Monat PC Game Pass, auf unsere Kosten!", "claim_now_sidebar": "Jetzt einlösen →", "generic_claim_error": "Es ist ein Fehler beim Einfordern Ihrer Belohnung aufgetreten. Bitte versuchen Sie es später noch einmal oder kontaktieren Sie den Support.", "gamepass_offer_details": "Die<PERSON> steht <PERSON>-Test-Benutz<PERSON> von WeMod Pro aus den Vereinigten Staaten, dem Vereinigten Königreich, Kanada und Australien zur Verfügung. Das Angebot ist aktiv von $start bis $end."}, "pro_showcase": {"ready_to_level_up_go_pro": "Bereit für den nächsten Level? *Werde Profi!*", "remote": "Remote-App", "remote_description": "Nie mehr alt-tabben! Verwalten Sie alle Ihre Mods, greifen Sie unterwegs auf Karten zu und fragen Sie den Spiel-Guide mit nur einem Fingertipp über die WeMod Remote Mobile-App.", "remote_subtitle": "Verwenden Sie die WeMod-App, um Mods zu konfigurieren", "save_mods": "Mods speichern", "save_mods_description": "Speichern Sie Ihr Mods-Setup und steigen Si<PERSON> wieder ein! Alle von Ihnen konfigurierten Mods können gespeichert und bei jedem Spielstart angewendet werden.", "save_mods_subtitle": "Speichern Sie Ihr Mods-Setup und steigen Sie wieder ein", "save_mods_mods": "Mods", "pin_mods": "Angeheftete Mods", "pin_mods_description": "Heften Sie Ihre Lieblingsmods für schnellen Zugriff an. Ihre angehefteten Mods erscheinen immer oben in der Liste, sodass Sie sie mühelos aktivieren können!", "pin_mods_subtitle": "Heften Sie Ihre Lieblingsmods für schnellen Zugriff an", "boosts": "Spiele boosten", "boosts_description": "Stimmen Sie für Ihre Lieblingsspiele – Ihre Boosts beeinflussen direkt unseren Mod-Roadmap und sorgen dafür, dass wir Mods für die Spiele erstellen, die am meisten zählen!", "boosts_subtitle": "St<PERSON><PERSON> für Spiele, die als nächstes Mods erhalten sollen", "game_guide": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "game_guide_description": "Erhalten Sie sofort Antworten auf Ihre Gaming-Fragen, entdecken Sie optimale Strategien und lernen Sie Profi-Tipps, um Ihr Erlebnis zu verbessern!", "game_guide_subtitle": "Tipps & Tricks für Ihr Spiel, powered by KI", "pinned": "Angeheftet", "demo_mod_1": "Unbegrenzte Gesundheit", "demo_mod_2": "Unbegrenzte Ausdauer", "demo_mod_3": "<PERSON>-<PERSON> / Treffer ignorieren", "demo_mod_4": "Spielgeschwindigkeit", "popular": "<PERSON><PERSON> g<PERSON>", "game_guide_illustration_response": "Um Attribute in Cyberpunk 2077 zurückzusetzen, könne<PERSON> Sie ins Charaktermenü gehen und die Perk-Zurücksetzen-Taste verwenden. Dies ermöglicht Ihnen, Ihre Attributpunkte nach Ihrem bevorzugten Spielstil neu zu verteilen. Zusätzlich können Sie Attributpunkte im Austausch gegen Eurodollar zurücksetzen.", "game_guide_illustration_question": "Wie setze ich Attribute zurück?", "game_guide_illustration_pending_question": "Was ist der beste Weg, um die letzte Mission zu schaffen?", "game_guide_illustration_example_avatar_alt": "Beispielbenutzeravatar", "game_guide_illustration_assistant_avatar_alt": "Avatar des Spiel-Guide-Assistenten", "game_guide_assistant_icon_alt": "Spiel-Guide-Assistent", "precision_mod_controls": "Präzisionsmod-Steuerungen", "precision_mod_controls_description": "Gehen Sie mit präzisen Steuerungen und vollständiger Anpassung noch weiter mit Ihren Mods.", "mod_timers_controls": "Perfektes Timing mit Mod-Timern", "mod_timers_controls_description": "Planen Sie Ihre Mods so, dass sie sich automatisch aktivieren, wenn Sie sie am meisten benötigen.", "pro": "PRO", "refill": "<PERSON><PERSON><PERSON><PERSON>", "one_and_a_half_times": "1,5x", "two_times": "2x", "half_times": "0,5x", "off": "Aus", "on": "Ein", "unlimited_health": "Unbegrenzte Gesundheit", "refill_health": "Gesundheit auffüllen", "set_max_health": "Maximale Gesundheit festlegen", "regeneration_rate": "Regenerationsrate", "regeneration_delay": "Regenerationsverzögerung", "new_in_wemod_pro": "Neu in *WeMod PRO!*", "check_it_out": "<PERSON><PERSON>u es dir an", "try_now": "Jetzt ausprobieren", "minute": "m", "start_timer": "Timer starten", "loop": "<PERSON><PERSON><PERSON><PERSON>", "refill_stamina": "Ausdauer auffüllen"}, "featurebase_feedback_dialog": {"feedback_submitted": "Danke für Ihr Feedback! ❤️"}, "pro_showcase_columns": {"basic": "Grundlegend", "unlimited_modding": "Unbegrenzte* Nutzung von Mods", "$games_games_with_$mods_mods": "$games+ Spiele mit $mods+ Mods", "native_overlay": "Integriertes In-Game Overlay", "interactive_controls": "Interaktive Steuerung", "$count_maps": "$count+ <PERSON><PERSON>", "one_click_teleport": "1-<PERSON>lick-Teleport mit Karten", "continue_with_free": "Weitermachen mit Gratis", "pro": "Pro", "everything_in_free_plus": "Alles in Gratis+", "save_mods": "Mods *speichern*", "pin_mods": "Mods anheften", "mobile_remote_app": "Mobile Fernbedienungs-App", "game_guides": "Spielhilfen", "custom_themes": "Benutzerdefinierte Themes", "no_ads": "<PERSON><PERSON>", "boost_games": "Spiele boosten", "priority_support_and_more": "Priorisierter Support und mehr", "save_mods_title_case": "Mods speichern", "save_mods_description": "Speichern Sie Ihre Mods, damit sie jedes Mal aktiviert sind, wenn <PERSON>e zurückkehren, um zu spielen", "pin_mods_title_case": "Angeheftete Mods", "pin_mods_description": "Heften Sie Ihre Lieblingsmods an, um schnell auf häufig verwendete Mods zuzugreifen.", "remote_title_case": "Remote-App", "remote_description": "Verwalten Sie alle Ihre Mods mit nur einem Tipp auf der WeMod Remote Mobile App.", "game_guide_title_case": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "game_guide_description": "Entdecke die Leistung von KI mit deiner eigenen persönlichen Spielhilfe.", "themes_title_case": "Benutzerdefinierte Themes", "themes_description": "Personalisieren Sie Ihre WeMod-App und Mod-Steuerungen mit benutzerdefinierten Themes.", "no_ads_title_case": "<PERSON><PERSON>rbung", "no_ads_description": "Erleben Sie eine 100% werbefreie WeMod Desktop-App, mobile App und natives Overlay.", "boosts_title_case": "Spiele boosten", "boosts_description": "Stimmen <PERSON> ab, welches Ihrer Lieblingsspiele als nächstes Mods bekommt.", "popular": "<PERSON><PERSON> g<PERSON>"}, "hover_me": {"hover_me": "Hover me"}, "choose_plan_promo": {"tagline": "<PERSON><PERSON><PERSON>, ein <PERSON> aufzusteigen?", "heading": "Das Beste aus WeMod <PERSON>holen", "description": "<PERSON><PERSON> un<PERSON>, weiter<PERSON> Mods für deine Lieblingsspiele zu erstellen und Zugriff auf coole exklusive Funktionen zu erhalten!", "free": "<PERSON><PERSON><PERSON>", "pro": "Pro", "everything_in_free": "Alles in Kostenlos", "free_mod_count": "35.000+ Mods", "free_interactive_controls": "Interaktive Steuerung", "free_overlay": "Native In-Game-Overlay", "free_maps": "150+ <PERSON><PERSON> 50+ <PERSON><PERSON><PERSON>", "pro_unlimited_modding": "Unbegrenztes Modding bei 3.000+ Spielen", "pro_no_ads": "<PERSON><PERSON>", "pro_pin_mods": "Mods anheften", "pro_save_mods": "Mods *speichern*", "pro_remote_app": "Mobile Fernbedienungs-App", "pro_custom_themes": "Benutzerdefinierte Themen & mehr", "continue_with_free": "Fortfahren mit Frei", "choose_your_plan": "Wählen Sie Ihren Plan"}, "overlay_maps_window": {"$name_map": "$name Karte"}, "pro_showcase_slideshow": {"games_title": "Über 3.000 Spiele", "games_description": "Keine Probleme mit Spielversionen und keine erneuten Downloads von Trainern. Regelmäßiger Community-Support bedeutet, dass wir in einem entscheidenden Bereich hervorragend sind: Unsere Mods funktionieren einfach.", "auto_detect_title": "Automatische Spielerkennung", "auto_detect_description": "WeMod scannt deine Spielebibliotheken, um automatisch installierte Spiele von beliebten Launchern wie *Steam*, Epic und vielen anderen zu erkennen!", "overlay_title": "In-Game-Overlay", "overlay_description": "<PERSON><PERSON>bbing mehr! Das WeMod-Overlay ist der einfachste Weg, <PERSON><PERSON> zu aktivieren, ohne das Spiel zu verlassen oder sich Hotkeys zu merken.", "remote_title": "Fernbedienungs-App", "remote_description": "Verwalte all deine Mods mit nur einem Tippen auf die WeMod Remote Mobile App!", "play_title": "Spiele deine Spiele auf deine Wei<PERSON>", "play_description": "<PERSON><PERSON><PERSON> von kostenlosen Mods, <PERSON><PERSON> und Trainern, die es dir ermöglichen, Einzelspieler-Spiele nach deinen eigenen Vorstellungen zu genießen.", "maps_title": "Interaktive Karte", "maps_description": "<PERSON><PERSON>, <PERSON><PERSON>, verst<PERSON><PERSON> Geheimnisse und mehr mit WeMods Interaktiven Karten!"}, "support_assistant": {"report_an_issue": "Ein Problem melden", "beta": "Beta", "title": "<PERSON><PERSON><PERSON>irt<PERSON> Assistent", "wemod_logo": "WeMod logo", "loading": "Ladevorgang l<PERSON>", "subtitle": "Support", "assistant_says": "Der Assistent sagt:", "your_selection": "Sie haben ausgewählt:", "welcome": "<PERSON><PERSON>, $username! Ich bin hier, um mit allen WeMod-Problemen zu helfen, die Si<PERSON> haben. Womit kann ich Ihnen heute helfen?", "what_else_can_i_help_you_with": "Wobei kann ich Ihnen sonst noch helfen?", "mods_not_working": "Mods funktionieren nicht", "overlay_not_working": "Overlay funktioniert nicht", "hotkeys_not_working": "Hotkeys funktionieren nicht", "game_is_crashing": "Abgestürztes Spiel", "i_have_general_feedback": "Ich habe allgemeines Feedback", "i_have_an_account_issue": "Ich habe ein Kontoproblem", "game_general_setup_instructions": "Lassen Sie uns Ihre Mod-Probleme für $gameTitle beheben. Zuerst überprüfen wir, ob Sie diese allgemeinen Einrichtungsschritte befolgt haben:", "game_general_setup_instructions_1": "<PERSON><PERSON><PERSON>, dass Sie WeMod als Administrator ausführen", "game_general_setup_instructions_2": "Deaktivieren Sie jegliche Antivirensoftware oder fügen Sie WeMod zu den Ausnahmen hinzu", "game_general_setup_instructions_3": "<PERSON><PERSON><PERSON>, dass WeMod auf die neueste Version aktualisiert ist", "game_general_setup_instructions_4": "Starten Sie Ihren Computer neu, wenn Sie dies kürzlich nicht getan haben", "game_general_setup_instructions_confirmation": "Haben Sie diese allgemeinen Einrichtungsschritte abgeschlossen?", "recommend_game_general_setup_steps": "<PERSON><PERSON> em<PERSON>, zu<PERSON>t diese allgemeinen Schritte zu befolgen, da sie viele häufige Probleme lösen. Möchten Sie sie ausprobieren und zurückkommen, wenn das Problem weiterhin besteht?", "yes_ive_followed_all_these_steps": "<PERSON><PERSON>, ich habe all diese Schritte befolgt", "yes_ive_followed_these_instructions": "<PERSON><PERSON>, ich habe diese Anweisungen befolgt", "no_i_havent_tried_all_of_them": "<PERSON><PERSON>, ich habe nicht alle ausprobiert", "no_i_havent_tried_all_of_these": "<PERSON><PERSON>, ich habe nicht alle versucht", "ill_try_these_steps": "Ich werde diese Schritte ausprobieren", "great_try_those_general_setup_steps": "Großartig! Versuchen Sie, diese allgemeinen Einrichtungsschritte auszuprobieren, und lassen <PERSON> mich wissen, ob sie helfen. Wenn Sie danach immer noch Probleme haben, können Sie gerne zu diesem Chat zurückkehren.", "now_lets_check_if_youve_followed_game_instructions": "<PERSON>sen Si<PERSON> uns jetzt überprüfen, ob Sie die spezifischen Einrichtungshinweise für $gameTitle befolgt haben:", "have_you_followed_these_game_instructions": "Haben Sie diese spezifischen Spielanweisungen befolgt?", "i_recommend_following_these_game_instructions": "<PERSON><PERSON> em<PERSON>eh<PERSON>, diese spezifischen Spielanweisungen zuerst zu befolgen. Sie sind darauf ausgelegt, häufige Probleme mit $gameTitle anzugehen. Möchten Sie sie ausprobieren und zurückkommen, wenn das Problem weiterhin besteht?", "great_try_following_those_game_instructions": "Großartig! Versuchen Sie, diese spezifischen Spielanweisungen zu befolgen, und lassen Si<PERSON> mich wissen, ob sie helfen. Wenn Sie danach immer noch Probleme haben, können Sie gerne zu diesem Chat zurückkehren.", "which_mods_arent_working_correctly": "Welche Mods funktionieren nicht richtig?", "all_mods": "Alle Mods", "specific_mods": "Spezifische Mods", "which_specific_mods_arent_working_correctly": "Welche spezifischen Mods funktionieren nicht korrekt?", "please_select_mods": "Bitte Mods auswählen", "submit": "Übermitteln", "have_you_followed_these_mod_specific_notes": "Haben Sie diese mod-spezifischen Notizen befolgt?", "yes_ive_followed_all_these_notes": "J<PERSON>, ich habe all diese Notizen befolgt", "instructions": "Anweisungen", "mod_basic_troubleshooting": "<PERSON>sen Si<PERSON> uns einige grundlegende Schritte zur Fehlerbehebung versuchen:", "mod_basic_troubleshooting_1": "<PERSON><PERSON><PERSON>, dass Sie eine authentische, offiziell gekaufte Version der Software verwenden (WeMod funktioniert nicht ordnungsgemäß mit inoffiziellen Versionen)", "mod_basic_troubleshooting_2": "Vergewisser<PERSON>, dass Si<PERSON> keine anderen Mods oder Nexus-Mods verwenden, die möglicherweise in Konflikt stehen", "mod_basic_troubleshooting_3": "<PERSON><PERSON><PERSON>, dass Ihr Antivirus WeMod nicht blockiert", "mod_basic_troubleshooting_4": "Überprüfen Si<PERSON>, ob es ein Spiel-Update gibt, das die Kompatibilität beeinträchtigt haben könnte", "mod_basic_troubleshooting_confirmation": "Haben diese Schr<PERSON> geholfen, <PERSON><PERSON> <PERSON> zu lösen?", "im_glad_to_header_that": "Ich freue mich das zu hören! Gibt es noch etwas, bei dem <PERSON> Hilfe benötigen?", "i_recommend_following_these_mod_specific_notes": "<PERSON><PERSON> em<PERSON>, zu<PERSON>t diesen mod-spezifischen Hinweisen zu folgen. Sie sind so konzipiert, dass sie allgemeine Probleme mit diesen speziellen Mods behandeln. Möchten Sie sie ausprobieren und zurückkommen, wenn das Problem weiterhin besteht?", "great_try_following_those_mod_specific_notes": "Super! Versuchen Sie, diesen mod-spezifischen Hinweisen zu folgen, und lassen <PERSON> mich wissen, ob sie helfen. Wenn <PERSON>e danach immer noch Probleme haben, können Sie gerne zu diesem Chat zurückkehren.", "advanced_troubleshooting": "Versuchen wir einige erweiterte Schritte zur Fehlerbehebung:", "advanced_troubleshooting_1": "Versuchen Sie, sowohl das Spiel als auch WeMod als Administrator auszuführen", "advanced_troubleshooting_2": "<PERSON><PERSON><PERSON>, dass Sie die neueste Version des Spiels spielen", "advanced_troubleshooting_confirmation": "Haben diese Schr<PERSON> geholfen, <PERSON><PERSON> <PERSON> zu lösen?", "where_did_you_purchase_game": "Wo haben Sie $gameTitle gekauft?", "steam": "Steam", "epic_games": "Epic Games Store", "gog": "GOG", "xbox_microsoft_store": "Xbox/Microsoft Store", "other": "Sonstiges", "game_platform_notes": "Für $gamePlatform Spiele können Sie versuchen:", "game_platform_notes_steam_1": "Öffnen Sie Ihre **Bibliothek**.", "game_platform_notes_steam_2": "Klicken Sie mit der rechten Maustaste auf das Spiel und wählen Sie **Eigenschaften**.", "game_platform_notes_steam_3": "<PERSON>ä<PERSON>en Sie **Installierte Dateien**.", "game_platform_notes_steam_4": "Klicken Sie auf **Integrität der Spieldateien überprüfen**.", "game_platform_notes_epic_1": "Öffnen Sie den **Bibliothek**-Tab.", "game_platform_notes_epic_2": "<PERSON><PERSON><PERSON> Si<PERSON> auf das ⋯-<PERSON><PERSON> beim <PERSON>.", "game_platform_notes_epic_3": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> **Verwalten**.", "game_platform_notes_epic_4": "Klicken Sie auf **Überprüfen**.", "game_platform_notes_gog_1": "Wählen Sie das Spiel aus.", "game_platform_notes_gog_2": "<PERSON>licken Sie auf die Schaltfläche ⋯ neben **Spielen**.", "game_platform_notes_gog_3": "<PERSON><PERSON><PERSON> zu **Installation verwalten**.", "game_platform_notes_gog_4": "Wählen Sie **Überprüfen/Reparieren**.", "game_platform_notes_xbox_1": "<PERSON><PERSON><PERSON> Si<PERSON> in der **Xbox-App** Ihre **Bibliothek**.", "game_platform_notes_xbox_2": "<PERSON><PERSON><PERSON> Si<PERSON> auf das ⋯-<PERSON><PERSON> beim <PERSON>.", "game_platform_notes_xbox_3": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> **Verwalten**.", "game_platform_notes_xbox_4": "Öffnen Sie die Registerkarte **Dateien**.", "game_platform_notes_xbox_5": "Klicken Sie auf **Überprüfen & Reparieren**.", "game_platform_notes_confirmation": "Haben Sie diese Anweisungen befolgt?", "we_recommend_following_game_platform_instructions": "Wir empfehlen, zu<PERSON>t diesen Anweisungen für $gamePlatform zu folgen, da sie die meisten Probleme lösen!", "great_try_that_out": "Super! Probieren Sie das aus, und wenn Sie danach weiterhin Probleme haben, können Sie gerne zu diesem Chat zurückkehren.", "please_head_over_to_our_online_feedback_hub": "Bitte besuchen Sie unser Online-Feedback-Portal, um Ihr allgemeines Feedback oder Ihre Funktionsanfragen einzureichen!", "share_feedback": "Feedback teilen →", "we_are_unable_to_assist_with_account": "Wir können Ihnen über den virtuellen Bot nicht bei Konto- oder Abrechnungsproblemen helfen. Bitte beziehen Si<PERSON> sich auf unsere [Selbsthilfe-Support-Artikel](https://support.wemod.com). Wenn Sie die gesuchte Antwort dort nicht finden, können Sie uns gerne unter [<EMAIL>](mailto:<EMAIL>) kontaktieren. Unser Support-Team wird Ihnen gerne weiterhelfen.", "ill_help_you_troubleshoot_hotkey_issues": "<PERSON>ch helfe <PERSON>, die Hotkey-Probleme zu beheben. Verwenden Sie die Nummernblock-Tasten für Hotkeys?", "make_sure_numlock_is_turned_on": "<PERSON><PERSON><PERSON>, dass NumLock eingeschaltet ist. Die Nummernblock-Hotkeys funktionieren nicht, wenn NumLock ausgeschaltet ist. Hat das Ihr Problem gelöst?", "great_numlock_key_is_often_overlooked": "Großartig! Die NumLock-Taste wird oft übersehen, ist aber entscheidend für Nummernblock-Hotkeys. Gibt es noch etwas, bei dem Si<PERSON> Hilfe benötigen?", "overlay_basic_troubleshoot": "<PERSON>ch helfe <PERSON>, die Overlay-Probleme zu beheben. Gehen wir diese schnellen Überprüfungen durch.", "overlay_basic_troubleshoot_1": "Verwenden Sie eine authentische Kopie des Spiels, die in einem offiziellen Store gekauft wurde? Wir können nicht garantieren, dass das Overlay mit inoffiziellen Versionen korrekt funktioniert.", "overlay_basic_troubleshoot_2": "Verwenden Sie die neueste Version von We<PERSON>od?", "overlay_basic_troubleshoot_2_update_version": "Es sieht aus, als ob Sie eine veraltete Version von WeMod verwenden. Bitte aktualisieren Si<PERSON> auf die neueste Version und versuchen Si<PERSON> es erneut.", "overlay_basic_troubleshoot_confirmation": "Hat eine dieser Optionen Ihr Overlay-Problem gelöst?", "lets_troubleshoot_game_crashes": "<PERSON>sen Si<PERSON> uns Ihre Spielabstürze beheben. Wann tritt der Absturz auf?", "when_launching_the_game": "Während des Spielstarts, während WeMod läuft", "when_activating_specific_mod": "Beim Aktivieren eines bestimmten Mods", "random_times_during_gameplay": "<PERSON><PERSON> zu<PERSON><PERSON>lligen Zeiten während des Spielens, wenn Mods aktiv sind", "when_using_the_overlay": "Bei der Verwendung des Overlays", "crash_troubleshooting": "<PERSON>sen Si<PERSON> uns diese Schritte zur Fehlerbehebung durchgehen, um die Abstürze zu beheben:", "crash_troubleshooting_1": "Überpr<PERSON><PERSON>, ob <PERSON><PERSON> Drittanbieter-Mods oder Nexus-Mods verwenden (versuchen Sie, diese zu deaktivieren)", "crash_troubleshooting_2": "<PERSON><PERSON><PERSON>, dass Sie eine legitime Kopie des Spiels von einem offiziellen Geschäft verwenden", "crash_troubleshooting_3": "WeMod ist nur für Einzelspieler konzipiert - vermeiden Sie die Verwendung im Mehrspielermodus", "crash_troubleshooting_4": "<PERSON><PERSON><PERSON>, dass Sie die neueste Version des Trainers für Ihr Spiel verwenden", "crash_troubleshooting_5": "<PERSON><PERSON><PERSON>, dass Sie die neueste Version von WeMod verwenden", "crash_troubleshooting_6": "<PERSON>ersuche<PERSON> Sie, das Overlay zu deaktivieren, wenn Ihr Spiel diese Funktion hat", "verify_game_files": "<PERSON><PERSON><PERSON><PERSON> wir, <PERSON><PERSON>e Spieldateien zu überprüfen, was oft Absturzprobleme löst:", "verify_game_files_1": "Für Steam: Rechtsklick auf das Spiel > Eigenschaften > Lokale Dateien > Integrität überprüfen", "verify_game_files_2": "Für Epic: Bibliothek > ⋮ beim <PERSON> > Überprüfen", "verify_game_files_3": "Für GOG: Bibliothek > Mehr > Installation verwalten > Überprüfen/Reparieren", "verify_game_files_4": "Für Xbox/Microsoft: Einstellungen > Apps > Apps & Features > [Spiel] > Erweiterte Optionen > Reparieren", "verify_game_files_confirmation": "Bitte führen Sie den Verifizierungsprozess für Ihren Spiel-Launcher aus und lassen Si<PERSON> mich wissen, ob es hilft.", "crash_troubleshooting_confirmation": "Haben diese Schritte geholfen?", "great_game_file_verification_often_fixes": "Super! Die Überprüfung der Spieldateien behebt oft beschädigte Spieldaten, die Abstürze verursachen. Brauchen Si<PERSON> noch Hilfe bei etwas anderem?", "advanced_crash_troubleshooting": "Lassen Si<PERSON> uns einige erweiterte Fehlerbehebungen ausprobieren:", "advanced_crash_troubleshooting_1": "Versuchen Sie, sowohl das Spiel als auch WeMod als Administrator auszuführen", "advanced_crash_troubleshooting_2": "Aktualisieren Sie Ihre Grafiktreiber auf die neueste Version", "advanced_crash_troubleshooting_3": "Deaktivieren Sie alle GPU-Overlays oder Aufzeichnungssoftware (GeForce Experience, AMD Radeon Software)", "advanced_crash_troubleshooting_4": "Überprüfen Sie den Windows-Ereignisanzeige auf Absturzprotokolle", "advanced_crash_troubleshooting_5": "Versuchen Sie einen sauberen Start (deaktivieren Sie nicht essentielle Startdienste)", "advanced_crash_troubleshooting_confirmation": "Haben diese Schritte geholfen, das Absturzproblem zu lösen?", "still_crashing": "St<PERSON>rzt immer noch ab", "excellent_im_glad_we_were_able_to_resolve_crashing": "Ausgezeichnet! Ich freue mich, dass wir das Absturzproblem lösen konnten. Brauchen Sie noch Hilfe bei etwas anderem?", "great_im_glad_that_helped": "Super! Ich freue mich, dass das geholfen hat. Brauchen Sie noch Unterstützung bei etwas anderem?", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "that_fixed_it": "Das hat es behoben!", "yes_its_fixed_now": "Ja, es ist jetzt behoben!", "no_still_having_issues": "<PERSON><PERSON>, es gibt immer noch Probleme", "continue_anyway": "Trotzdem fortfahren", "i_have_another_issue": "Ich habe ein anderes Problem", "no_thats_all_thanks": "<PERSON><PERSON>, das ist alles - danke!", "further_troubleshooting_needed": "Es sieht so aus, als bräuchten wir zusätzliche Fehlerbehebungen, um dieses Problem zu lösen. Unser Support-Team auf Discord ist bereit zu helfen! Gehen Sie zu [unserem Discord-Server →](https://www.wemod.com/discord) und starten Sie einen neuen Thread. Kopieren und fügen Sie die folgende Fehlerbehebungsübersicht in Ihren Discord-Thread für schnellere Hilfe ein:", "copy": "<PERSON><PERSON><PERSON>", "sorry_something_went_wrong": "Entschuldigung, ich habe gerade einige Probleme. 😅 Versuchen Sie es in ein paar Minuten erneut oder [kontaktieren Sie den Support](https://support.wemod.com/).", "minimize": "Minimieren", "restore": "Wiederherstellen", "close": "Schließen"}, "live_location_announcement_dialog": {"introducing_the_all_new": "Einführung des brandneuen", "live_location_tracking": "Live-Standortverfolgung", "see_your_position_in_real_time": "See your exact position in real-time as you explore the game world. Available for **PRO** in both the Overlay and the Desktop App for supported games."}, "trainer_notes_dialog": {"almost_there": "Fast geschafft!", "lets_get_you_set_up": "Lass uns dich einrichten", "back": "Zurück", "est_time": "Voraussichtliche Zeit", "minute_abbreviation": " <PERSON>.", "dismissable_note": "**Hinweis:** Während die meisten Spiele mit einem Klick spielbar sind, erfordern einige Spiele eine zusätzliche Einrichtung.", "dismiss": "Verwerfen", "dont_show_again": "Dies nicht mehr anzeigen", "dont_show_again_description": "Sie müssen die Anweisungen vollständig lesen, um zu spielen"}, "trainer_notes_dialog_small": {"setup_instructions": "Einrichtungsanweisungen", "got_it": "Verstanden"}, "title_help_button": {"find_instructions_here": "Sie können die Einrichtungsanweisungen erneut finden, indem Sie auf das Einstellungs-Dropdown klicken."}, "help_menu": {"setup_information": "Setup-Informationen", "help_menu": "Hilfemenü", "report_an_issue": "Ein Problem melden", "general": "Allgemeines", "submit_feedback": "Feedback übermitteln", "feature_request": "Funktionsanfrage", "suggest_a_mod": "Jetzt Mod vorschlagen"}, "suggest_mod": {"title": "Mod vorschlagen", "description": "<PERSON><PERSON>e Vorschläge und Boosts helfen unseren Mod-Erstellern, die Mods zu entwickeln, die Ihnen am wichtigsten sind.", "most_popular_suggestions": "Beliebteste Vorschläge", "related_suggestions": "Ähnliche Vorschläge", "not_updated_recently": "*Wichtig:* <PERSON>ses Spiel wurde seit über 90 Tagen nicht aktualisiert. Die Überprüfung von Mod-Vorschlägen durch den Ersteller erfolgt nur während Spiel- und Mod-Updates. Möchten Sie trotzdem fortfahren?", "not_updated_recently_ack": "Ja, fortfahren", "creator_review_note": "Die Überprüfung der Mod-Vorschläge durch den Ersteller erfolgt nur während des Spiels und der Mod-Aktualisierungen.", "start_typing": "Text eingeben", "boost": "Boost", "no_boosts_available": "<PERSON><PERSON>", "pro": "PRO", "no_freeform_note": "*Hinweis:* Aufgrund technischer Einschränkungen können derzeit keine benutzerdefinierten Vorschläge akzeptiert werden. Bitte wählen Sie aus den Optionen im Dropdown-Menü.", "refund_note": "*Hinweis:* Ihre Boosts werden vollständig erstattet, wenn die Ersteller feststellen, dass Mod-Vorschläge aufgrund technischer Einschränkungen oder Spielbeschränkungen nicht umgesetzt werden können.", "error_message": "<PERSON><PERSON> Erstellen des Vorschlags ist ein Fehler aufgetreten. Bitte versuchen Sie es später erneut.", "boost_error_message": "<PERSON><PERSON> ist ein Fehler aufgetreten. Bitte versuch es noch einmal.", "boost_success_message": "Vorschlag erfolgreich geboostet!", "mod_suggestions": "Mod-Vorschläge", "your_suggestions": "<PERSON><PERSON><PERSON> V<PERSON>chläge", "new": "neu", "needs_review": "Muss überprüft werden", "$x_new": "$x Neu", "no_suggestions": "Noch keine genehmigten Mod-Vorschläge", "boosting_disabled_for_suggestions_under_review": "Boosting ist für Vorschläge, die überprüft werden, deaktiviert.", "successfully_boosted_$suggestion": "Sie haben *$suggestion* erfolgreich geboostet!", "continue": "Fortfahren", "and_$x_more": "und $x mehr"}, "capture": {"one_$title_highlight_saved": "1 $title-Highlight gespeichert!", "$count_$title_highlights_saved": "$count $title-High<PERSON> gespeichert!", "view_captures": "Ordner für Videos öffnen ↗"}, "capture_settings": {"capture_location": "Video-<PERSON><PERSON><PERSON>", "capture_location_description": "Der Ordner, in dem deine Highlights gespeichert sind", "view_captures": "Ordner für Videos öffnen ↗"}, "instant_highlight_announcement_dialog": {"introducing_the_all_new": "Einführung des brandneuen", "instant_highlights": "Sofort-Highlights", "never_miss_a_gaming_highlight_again": "Verpasse nie wieder ein Spiel-Highlight! Speichere sofort deine letzten 15-60 Sekunden Gameplay mit einem einfachen Hotkey.\nVerwende das WeMod-Overlay oder drücke **$hotkey**, um deinen Clip zu speichern.", "lets_go": "Los geht's!", "not_now": "Vielleicht später", "okay": "Okay"}, "overlay_instant_highlights_announcement_notification": {"new_instant_hightlights": "Neu! *Sofort-Highlight*", "press_$hotkey_to_clip_the_last_$secondss": "<PERSON><PERSON>e *$hotkey*, um die letzten $secondss zu clippen"}, "overlay_highlight_save_success_notification": {"highlight_saved": "Highlight ges<PERSON>ichert"}, "my_videos": {"title": "<PERSON>ne <PERSON>s", "favorites": "<PERSON><PERSON>", "all": "Alle", "game": "Spiel", "recently_recorded": "<PERSON><PERSON><PERSON><PERSON> aufgezeichnet", "sort_by": "Sortieren nach", "no_results": "Keine Videos gefunden", "no_results_advice": "<PERSON><PERSON><PERSON> ein sofortiges Highlight während des Spielens, um zu beginnen", "no_results_advice_search": "<PERSON><PERSON><PERSON>, nach einem anderen <PERSON> zu suchen, oder sieh dir an, was gerade beliebt ist", "no_results_advice_favorites": "Füge einige Videos zu deinen Favoriten hinzu, um zu beginnen", "group_name_today": "Today", "group_name_yesterday": "Gestern", "group_name_last_week": "Last Week", "group_name_past": "Past", "search_placeholder": "Search videos", "search_button": "Starten", "recorded": "Recorded", "file_already_exists": "A file with this name already exists.", "file_rename_failed": "Failed to rename file. Please try again.", "open_file_location": "Open file location ↗", "introducing_the_all_new": "Einführung des brandneuen", "instant_highlights": "Sofort-Highlights", "never_miss_a_gaming_highlight_again": "Verpassen Sie nie wieder ein Gaming-Highlight! Speichern Sie sofort die letzten 15-60 Sekunden Ihres Gameplays mit einem einfachen Hotkey. Verwenden Sie das WeMod Overlay oder drücken Sie **$hotkey** um Ihren Clip zu speichern.", "dismiss": "Verwerfen"}, "edit_input": {"save": "Speichern", "cancel": "Abbrechen"}}
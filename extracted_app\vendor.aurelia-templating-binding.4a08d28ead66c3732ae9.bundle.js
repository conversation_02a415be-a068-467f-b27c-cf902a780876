"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7612],{"aurelia-templating-binding":(t,e,r)=>{r.r(e),r.d(e,{AttributeMap:()=>u,ChildInterpolationBinding:()=>l,InterpolationBinding:()=>c,InterpolationBindingExpression:()=>p,LetBinding:()=>d,LetExpression:()=>g,LetInterpolationBinding:()=>m,LetInterpolationBindingExpression:()=>f,SyntaxInterpreter:()=>b,TemplatingBindingLanguage:()=>v,configure:()=>w});var i=r(30960),n=r(7530),o=r(96610),s=function(t,e){return s=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},s(t,e)};function a(t,e,r,i){var n,o=arguments.length,s=o<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,r):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,i);else for(var a=t.length-1;a>=0;a--)(n=t[a])&&(s=(o<3?n(s):o>3?n(e,r,s):n(e,r))||s);return o>3&&s&&Object.defineProperty(e,r,s),s}var u=function(){function t(t){this.elements=Object.create(null),this.allElements=Object.create(null),this.svg=t,this.registerUniversal("accesskey","accessKey"),this.registerUniversal("contenteditable","contentEditable"),this.registerUniversal("tabindex","tabIndex"),this.registerUniversal("textcontent","textContent"),this.registerUniversal("innerhtml","innerHTML"),this.registerUniversal("scrolltop","scrollTop"),this.registerUniversal("scrollleft","scrollLeft"),this.registerUniversal("readonly","readOnly"),this.register("label","for","htmlFor"),this.register("img","usemap","useMap"),this.register("input","maxlength","maxLength"),this.register("input","minlength","minLength"),this.register("input","formaction","formAction"),this.register("input","formenctype","formEncType"),this.register("input","formmethod","formMethod"),this.register("input","formnovalidate","formNoValidate"),this.register("input","formtarget","formTarget"),this.register("textarea","maxlength","maxLength"),this.register("td","rowspan","rowSpan"),this.register("td","colspan","colSpan"),this.register("th","rowspan","rowSpan"),this.register("th","colspan","colSpan")}return t.prototype.register=function(t,e,r){t=t.toLowerCase(),e=e.toLowerCase(),(this.elements[t]=this.elements[t]||Object.create(null))[e]=r},t.prototype.registerUniversal=function(t,e){t=t.toLowerCase(),this.allElements[t]=e},t.prototype.map=function(t,e){if(this.svg.isStandardSvgAttribute(t,e))return e;t=t.toLowerCase(),e=e.toLowerCase();var r=this.elements[t];return void 0!==r&&e in r?r[e]:e in this.allElements?this.allElements[e]:/(?:^data-)|(?:^aria-)|:/.test(e)?e:(0,n.xQ)(e)},t.inject=[n.dJ],t}(),p=function(){function t(t,e,r,i,n,o){this.observerLocator=t,this.targetProperty=e,this.parts=r,this.mode=i,this.lookupFunctions=n,this.attribute=this.attrToRemove=o,this.discrete=!1}return t.prototype.createBinding=function(t){return 3===this.parts.length?new l(t,this.observerLocator,this.parts[1],this.mode,this.lookupFunctions,this.targetProperty,this.parts[0],this.parts[2]):new c(this.observerLocator,this.parts,t,this.targetProperty,this.mode,this.lookupFunctions)},t}();function h(t,e){if("style"===e)o.getLogger("templating-binding").info('Internet Explorer does not support interpolation in "style" attributes.  Use the style attribute\'s alias, "css" instead.');else if(t.parentElement&&"TEXTAREA"===t.parentElement.nodeName&&"textContent"===e)throw new Error('Interpolation binding cannot be used in the content of a textarea element.  Use <textarea value.bind="expression"></textarea> instead.')}var c=function(){function t(t,e,r,i,n,o){h(r,i),this.observerLocator=t,this.parts=e,this.target=r,this.targetProperty=i,this.targetAccessor=t.getAccessor(r,i),this.mode=n,this.lookupFunctions=o}return t.prototype.interpolate=function(){if(this.isBound){for(var t="",e=this.parts,r=0,i=e.length;r<i;r++)t+=r%2==0?e[r]:this["childBinding".concat(r)].value;this.targetAccessor.setValue(t,this.target,this.targetProperty)}},t.prototype.updateOneTimeBindings=function(){for(var t=1,e=this.parts.length;t<e;t+=2){var r=this["childBinding".concat(t)];r.mode===n.BG.oneTime&&r.call()}},t.prototype.bind=function(t){if(this.isBound){if(this.source===t)return;this.unbind()}this.source=t;for(var e=this.parts,r=1,i=e.length;r<i;r+=2){var n=new l(this,this.observerLocator,e[r],this.mode,this.lookupFunctions);n.bind(t),this["childBinding".concat(r)]=n}this.isBound=!0,this.interpolate()},t.prototype.unbind=function(){if(this.isBound){this.isBound=!1,this.source=null;for(var t=1,e=this.parts.length;t<e;t+=2)this["childBinding".concat(t)].unbind()}},t}(),l=function(){function t(t,e,r,i,n,o,s,a){t instanceof c?this.parent=t:(h(t,o),this.target=t,this.targetProperty=o,this.targetAccessor=e.getAccessor(t,o)),this.observerLocator=e,this.sourceExpression=r,this.mode=i,this.lookupFunctions=n,this.left=s,this.right=a}return t.prototype.updateTarget=function(t){(t=null==t?"":t.toString())!==this.value&&(this.value=t,this.parent?this.parent.interpolate():this.targetAccessor.setValue(this.left+t+this.right,this.target,this.targetProperty))},t.prototype.call=function(){this.isBound&&(this.rawValue=this.sourceExpression.evaluate(this.source,this.lookupFunctions),this.updateTarget(this.rawValue),this.mode!==n.BG.oneTime&&(this._version++,this.sourceExpression.connect(this,this.source),this.rawValue instanceof Array&&this.observeArray(this.rawValue),this.unobserve(!1)))},t.prototype.bind=function(t){if(this.isBound){if(this.source===t)return;this.unbind()}this.isBound=!0,this.source=t;var e=this.sourceExpression;e.bind&&e.bind(this,t,this.lookupFunctions),this.rawValue=e.evaluate(t,this.lookupFunctions),this.updateTarget(this.rawValue),this.mode===n.BG.oneWay&&(0,n.MZ)(this)},t.prototype.unbind=function(){if(this.isBound){this.isBound=!1;var t=this.sourceExpression;t.unbind&&t.unbind(this,this.source),this.source=null,this.value=null,this.rawValue=null,this.unobserve(!0)}},t.prototype.connect=function(t){this.isBound&&(t&&(this.rawValue=this.sourceExpression.evaluate(this.source,this.lookupFunctions),this.updateTarget(this.rawValue)),this.sourceExpression.connect(this,this.source),this.rawValue instanceof Array&&this.observeArray(this.rawValue))},a([(0,n.xM)()],t)}(),g=function(){function t(t,e,r,i,n){this.observerLocator=t,this.sourceExpression=r,this.targetProperty=e,this.lookupFunctions=i,this.toBindingContext=n}return t.prototype.createBinding=function(){return new d(this.observerLocator,this.sourceExpression,this.targetProperty,this.lookupFunctions,this.toBindingContext)},t}(),d=function(){function t(t,e,r,i,n){this.observerLocator=t,this.sourceExpression=e,this.targetProperty=r,this.lookupFunctions=i,this.source=null,this.target=null,this.toBindingContext=n}return t.prototype.updateTarget=function(){var t=this.sourceExpression.evaluate(this.source,this.lookupFunctions);this.target[this.targetProperty]=t},t.prototype.call=function(t){if(this.isBound){if(t!==n.RH)throw new Error("Unexpected call context ".concat(t));this.updateTarget()}},t.prototype.bind=function(t){if(this.isBound){if(this.source===t)return;this.unbind()}this.isBound=!0,this.source=t,this.target=this.toBindingContext?t.bindingContext:t.overrideContext,this.sourceExpression.bind&&this.sourceExpression.bind(this,t,this.lookupFunctions),(0,n.MZ)(this)},t.prototype.unbind=function(){this.isBound&&(this.isBound=!1,this.sourceExpression.unbind&&this.sourceExpression.unbind(this,this.source),this.source=null,this.target=null,this.unobserve(!0))},t.prototype.unobserve=function(t){throw new Error("Method not implemented.")},t.prototype.connect=function(){this.isBound&&(this.updateTarget(),this.sourceExpression.connect(this,this.source))},a([(0,n.xM)()],t)}(),f=function(){function t(t,e,r,i,n){this.observerLocator=t,this.targetProperty=e,this.parts=r,this.lookupFunctions=i,this.toBindingContext=n}return t.prototype.createBinding=function(){return new m(this.observerLocator,this.targetProperty,this.parts,this.lookupFunctions,this.toBindingContext)},t}(),m=function(){function t(t,e,r,i,n){this.observerLocator=t,this.parts=r,this.targetProperty=e,this.lookupFunctions=i,this.toBindingContext=n,this.target=null}return t.prototype.bind=function(t){if(this.isBound){if(this.source===t)return;this.unbind()}this.isBound=!0,this.source=t,this.target=this.toBindingContext?t.bindingContext:t.overrideContext,this.interpolationBinding=this.createInterpolationBinding(),this.interpolationBinding.bind(t)},t.prototype.unbind=function(){this.isBound&&(this.isBound=!1,this.source=null,this.target=null,this.interpolationBinding.unbind(),this.interpolationBinding=null)},t.prototype.createInterpolationBinding=function(){return 3===this.parts.length?new l(this.target,this.observerLocator,this.parts[1],n.BG.toView,this.lookupFunctions,this.targetProperty,this.parts[0],this.parts[2]):new c(this.observerLocator,this.parts,this.target,this.targetProperty,n.BG.toView,this.lookupFunctions)},t}(),b=function(){function t(t,e,r,i){this.parser=t,this.observerLocator=e,this.eventManager=r,this.attributeMap=i}return t.prototype.interpret=function(t,e,r,i,n){return r.command in this?this[r.command](t,e,r,i,n):this.handleUnknownCommand(t,e,r,i,n)},t.prototype.handleUnknownCommand=function(t,e,r,i,n){return o.getLogger("templating-binding").warn("Unknown binding command.",r),i},t.prototype.determineDefaultBindingMode=function(t,e,r){var i=t.tagName.toLowerCase();return"input"===i&&("value"===e||"files"===e)&&"checkbox"!==t.type&&"radio"!==t.type||"input"===i&&"checked"===e&&("checkbox"===t.type||"radio"===t.type)||("textarea"===i||"select"===i)&&"value"===e||("textcontent"===e||"innerhtml"===e)&&"true"===t.contentEditable||"scrolltop"===e||"scrollleft"===e?n.BG.twoWay:r&&e in r.attributes&&r.attributes[e]&&r.attributes[e].defaultBindingMode>=n.BG.oneTime?r.attributes[e].defaultBindingMode:n.BG.toView},t.prototype.bind=function(t,e,r,o,s){var a=o||i.P5.attribute(r.attrName);return a.attributes[r.attrName]=new n.sv(this.observerLocator,this.attributeMap.map(e.tagName,r.attrName),this.parser.parse(r.attrValue),void 0===r.defaultBindingMode||null===r.defaultBindingMode?this.determineDefaultBindingMode(e,r.attrName,s):r.defaultBindingMode,t.lookupFunctions),a},t.prototype.trigger=function(t,e,r){return new n.Pc(this.eventManager,r.attrName,this.parser.parse(r.attrValue),n.ar.none,!0,t.lookupFunctions)},t.prototype.capture=function(t,e,r){return new n.Pc(this.eventManager,r.attrName,this.parser.parse(r.attrValue),n.ar.capturing,!0,t.lookupFunctions)},t.prototype.delegate=function(t,e,r){return new n.Pc(this.eventManager,r.attrName,this.parser.parse(r.attrValue),n.ar.bubbling,!0,t.lookupFunctions)},t.prototype.call=function(t,e,r,o){var s=o||i.P5.attribute(r.attrName);return s.attributes[r.attrName]=new n.DG(this.observerLocator,r.attrName,this.parser.parse(r.attrValue),t.lookupFunctions),s},t.prototype.options=function(t,e,r,n,o){var s,a,u,p=n||i.P5.attribute(r.attrName),h=r.attrValue,c=this.language,l=null,g="",d=!1,f=!1,m=!1;for(a=0,u=h.length;a<u;++a){if(";"!==(s=h[a])||d)if(":"===s&&null===l)m=!0,l=g.trim(),g="";else{if("\\"===s){g+=s,f=!0;continue}g+=s,null!==l&&!1===f&&"'"===s&&(d=!d)}else m||(l=this._getPrimaryPropertyName(t,o)),r=c.inspectAttribute(t,"?",l,g.trim()),c.createAttributeInstruction(t,e,r,p,o),p.attributes[r.attrName]||(p.attributes[r.attrName]=r.attrValue),g="",l=null;f=!1}return m||(l=this._getPrimaryPropertyName(t,o)),null!==l&&(r=c.inspectAttribute(t,"?",l,g.trim()),c.createAttributeInstruction(t,e,r,p,o),p.attributes[r.attrName]||(p.attributes[r.attrName]=r.attrValue)),p},t.prototype._getPrimaryPropertyName=function(t,e){var r=t.getAttribute(e.attributeName);return r&&r.primaryProperty?r.primaryProperty.attribute:null},t.prototype.for=function(t,e,r,o){var s,a,u,p,h;if(2!==(s=(h=(p=r.attrValue).match(/^ *[[].+[\]]/))?p.split("of "):p.split(" of ")).length)throw new Error('Incorrect syntax for "for". The form is: "$local of $items" or "[$key, $value] of $items".');return u=o||i.P5.attribute(r.attrName),h?(a=s[0].replace(/[[\]]/g,"").replace(/,/g," ").replace(/\s+/g," ").trim().split(" "),u.attributes.key=a[0],u.attributes.value=a[1]):u.attributes.local=s[0],u.attributes.items=new n.sv(this.observerLocator,"items",this.parser.parse(s[1]),n.BG.toView,t.lookupFunctions),u},t.prototype["two-way"]=function(t,e,r,o){var s=o||i.P5.attribute(r.attrName);return s.attributes[r.attrName]=new n.sv(this.observerLocator,this.attributeMap.map(e.tagName,r.attrName),this.parser.parse(r.attrValue),n.BG.twoWay,t.lookupFunctions),s},t.prototype["to-view"]=function(t,e,r,o){var s=o||i.P5.attribute(r.attrName);return s.attributes[r.attrName]=new n.sv(this.observerLocator,this.attributeMap.map(e.tagName,r.attrName),this.parser.parse(r.attrValue),n.BG.toView,t.lookupFunctions),s},t.prototype["from-view"]=function(t,e,r,o){var s=o||i.P5.attribute(r.attrName);return s.attributes[r.attrName]=new n.sv(this.observerLocator,this.attributeMap.map(e.tagName,r.attrName),this.parser.parse(r.attrValue),n.BG.fromView,t.lookupFunctions),s},t.prototype["one-time"]=function(t,e,r,o){var s=o||i.P5.attribute(r.attrName);return s.attributes[r.attrName]=new n.sv(this.observerLocator,this.attributeMap.map(e.tagName,r.attrName),this.parser.parse(r.attrValue),n.BG.oneTime,t.lookupFunctions),s},t.inject=[n.iX,n.Zr,n.EU,u],t}();Object.defineProperty(b.prototype,"one-way",Object.getOwnPropertyDescriptor(b.prototype,"to-view"));var y={},v=function(t){function e(e,r,i,n){var o=t.call(this)||this;return o.parser=e,o.observerLocator=r,o.syntaxInterpreter=i,o.emptyStringExpression=o.parser.parse("''"),i.language=o,o.attributeMap=n,o.toBindingContextAttr="to-binding-context",o}return function(t,e){function r(){this.constructor=t}s(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}(e,t),e.prototype.inspectAttribute=function(t,e,r,i){var o=r.split(".");if(y.defaultBindingMode=null,2===o.length)y.attrName=o[0].trim(),y.attrValue=i,y.command=o[1].trim(),"ref"===y.command?(y.expression=new n.mi(this.parser.parse(i),y.attrName,t.lookupFunctions),y.command=null,y.attrName="ref"):y.expression=null;else if("ref"===r)y.attrName=r,y.attrValue=i,y.command=null,y.expression=new n.mi(this.parser.parse(i),"element",t.lookupFunctions);else{y.attrName=r,y.attrValue=i,y.command=null;var s=this.parseInterpolation(t,i);y.expression=null===s?null:new p(this.observerLocator,this.attributeMap.map(e,r),s,n.BG.toView,t.lookupFunctions,r)}return y},e.prototype.createAttributeInstruction=function(t,e,r,n,o){var s;if(r.expression){if("ref"===r.attrName)return r.expression;(s=n||i.P5.attribute(r.attrName)).attributes[r.attrName]=r.expression}else r.command&&(s=this.syntaxInterpreter.interpret(t,e,r,n,o));return s},e.prototype.createLetExpressions=function(t,e){for(var r,i,s,a,u=[],p=e.attributes,h=this.toBindingContextAttr,c=e.hasAttribute(h),l=0,d=p.length;d>l;++l)if(s=(r=p[l]).name,a=r.nodeValue,i=s.split("."),s!==h)if(2===i.length){if("bind"!==i[1]){o.getLogger("templating-binding-language").warn('Detected invalid let command. Expected "'.concat(i[0],'.bind", given "').concat(s,'"'));continue}u.push(new g(this.observerLocator,(0,n.xQ)(i[0]),this.parser.parse(a),t.lookupFunctions,c))}else s=(0,n.xQ)(s),null===(i=this.parseInterpolation(t,a))&&o.getLogger("templating-binding-language").warn('Detected string literal in let bindings. Did you mean "'.concat(s,".bind=").concat(a,'" or "').concat(s,"=${").concat(a,'}" ?')),i?u.push(new f(this.observerLocator,s,i,t.lookupFunctions,c)):u.push(new g(this.observerLocator,s,new n.JI(a),t.lookupFunctions,c));return u},e.prototype.inspectTextContent=function(t,e){var r=this.parseInterpolation(t,e);return null===r?null:new p(this.observerLocator,"textContent",r,n.BG.toView,t.lookupFunctions,"textContent")},e.prototype.parseInterpolation=function(t,e){for(var r,i,n,o=e.indexOf("${",0),s=e.length,a=0,u=0,p=null,h=0;o>=0&&o<s-2;){u=1,i=o,o+=2;do{r=e[o],o++,"'"!==r&&'"'!==r?"\\"!==r?null===p&&("{"===r?u++:"}"===r&&u--):o++:null===p?p=r:p===r&&(p=null)}while(u>0&&o<s);if(0!==u)break;n=n||[],"\\"===e[i-1]&&"\\"!==e[i-2]?(n[h]=e.substring(a,i-1)+e.substring(i,o),n[++h]=this.emptyStringExpression,h++):(n[h]=e.substring(a,i),n[++h]=this.parser.parse(e.substring(i+2,o-1)),h++),a=o,o=e.indexOf("${",o)}return 0===h?null:(n[h]=e.substr(a),n)},e.inject=[n.iX,n.Zr,b,u],e}(i.Wy);function w(t){t.container.registerSingleton(i.Wy,v),t.container.registerAlias(i.Wy,v)}}}]);
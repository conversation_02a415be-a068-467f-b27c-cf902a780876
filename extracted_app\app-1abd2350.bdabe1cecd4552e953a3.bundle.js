"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1911],{23218:(e,a,o)=>{o.d(a,{D:()=>r});var t=o(15215),i=o("aurelia-dialog"),l=o("aurelia-event-aggregator"),n=o("aurelia-framework");let r=class{#e;#a;constructor(e,a){this.dialog=e,this.allowMultiple=!1,this.#e=!1,this.#a=a}async open(e,a){if(!this.#e||this.allowMultiple){this.#e=!0,this.#a.publish("dialog:open:start",this.viewModelClass);try{const o=this.dialog.open({viewModel:this.viewModelClass,host:document.querySelector("#fullscreen-dialogs")??void 0,startingZIndex:1e3,model:e,lock:a,keyboard:!a});return o.then((()=>this.#a.publish("dialog:open:complete",this.viewModelClass))),await o.whenClosed()}finally{this.#e=!1}}}};r=(0,t.Cg)([(0,n.autoinject)(),(0,t.Sn)("design:paramtypes",[i.DialogService,l.EventAggregator])],r)},43775:(e,a,o)=>{o.d(a,{Y:()=>n});var t=o(15215),i=o("aurelia-framework"),l=o("dialogs/fullscreen-webview-dialog");let n=class{#o;constructor(e){this.#o=e}async open(e){const a={discountCode:e?.discountCode,frequency:e?.frequency||void 0,trigger:e?.trigger,recipient:e?.recipient};await this.#o.open({route:"gift-flow",params:a,styles:{background:"var(--theme--background)"}})}};n=(0,t.Cg)([(0,i.autoinject)(),(0,t.Sn)("design:paramtypes",[l.FullscreenWebviewDialogService])],n)},68502:(e,a,o)=>{o.d(a,{N:()=>r});var t=o(15215),i=o("aurelia-framework"),l=o(62914),n=o("dialogs/webview-dialog");let r=class{#o;#t;constructor(e,a){this.#o=e,this.#t=a}async open(e){return this.#t.event("change_payment_method_dialog_open",{trigger:e},l.Io),await this.#o.open({route:"update-payment-method"})}};r=(0,t.Cg)([(0,i.autoinject)(),(0,t.Sn)("design:paramtypes",[n.WebviewDialogService,l.j0])],r)},69942:(e,a,o)=>{o.d(a,{I:()=>g});var t=o(15215),i=o("aurelia-framework"),l=o(20770),n=o(45660),r=o(59239),d=o(67064),s=o(19072),c=o("shared/featurebase/featurebase-feedback-dialog");let g=class{#i;#l;#n;constructor(e,a,o,t){this.#i=e,this.#l=a,this.host=o,this.#n=t}async open(e){const a=await this.#n.state.pipe((0,n.$)(),(0,r.E)("account")).toPromise();(await this.#i.open({boardId:e.boardId,jwtToken:a?.featurebaseJwt??"",metadata:await this.getMetadata()})).wasCancelled||this.#l.toast({type:"tada",content:"featurebase_feedback_dialog.feedback_submitted"})}async getMetadata(){const e=await this.#n.state.pipe((0,n.$)(),(0,r.E)("gameHistory")).toPromise(),a=await this.#n.state.pipe((0,n.$)(),(0,r.E)("catalog")).toPromise(),o=await this.#n.state.pipe((0,n.$)(),(0,r.E)("account")).toPromise(),t=Object.entries(e).map((([e,a])=>({gameId:e,...a}))),i=t.sort(((e,a)=>(a.lastPlayedAt??"").localeCompare(e.lastPlayedAt??"")))[0],l=i?a.games[i.gameId]:null,d=l?a.titles[l.titleId]:null,s=t.sort(((e,a)=>(a.lastViewedAt??"").localeCompare(e.lastViewedAt??"")))[0],c=s?a.games[s.gameId]:null,g=c?a.titles[c.titleId]:null;return{appVersion:this.host.info.version,lastPlayedGame:l?d?.name:null,lastViewedGame:c?g?.name:null,isPro:o?.subscription?"true":"false"}}};g=(0,t.Cg)([(0,i.autoinject)(),(0,t.Sn)("design:paramtypes",[c.FeaturebaseFeedbackDialogService,d.l,s.s,l.il])],g)},71341:(e,a,o)=>{o.d(a,{U:()=>r});var t=o(15215),i=o("aurelia-framework"),l=o(78576),n=o("dialogs/fullscreen-webview-dialog");let r=class{#o;#r;constructor(e,a){this.#o=e,this.#r=a}async open(e){const a={discountCode:e?.discountCode,frequency:e?.frequency?e.frequency:e?.selectedPlan?e?.selectedPlan?.recurring?.frequency:void 0,trigger:e?.trigger},o=await this.#o.open({route:"checkout",params:a,styles:{background:"var(--theme--background)"}});o&&!o.wasCancelled&&this.#r.check()}};r=(0,t.Cg)([(0,i.autoinject)(),(0,t.Sn)("design:paramtypes",[n.FullscreenWebviewDialogService,l.G])],r)},"dialogs/changelog-dialog":(e,a,o)=>{o.r(a),o.d(a,{ChangelogDialog:()=>s,ChangelogDialogService:()=>c});var t=o(15215),i=o("aurelia-dialog"),l=o("aurelia-framework"),n=o(62914),r=o(62079),d=o(17275);let s=class{#t;constructor(e,a,o){this.controller=e,this.changelog=a,this.#t=o}async canActivate(e){return void 0===this.changelog.changelog&&await this.changelog.update(),!!this.changelog.changelog&&(this.#t.event("changelog_dialog_open",{trigger:e?.trigger},n.Io),!0)}};s=(0,t.Cg)([(0,l.autoinject)(),(0,t.Sn)("design:paramtypes",[i.DialogController,r.L,n.j0])],s);let c=class extends d.C{constructor(){super(...arguments),this.viewModelClass="dialogs/changelog-dialog"}};c=(0,t.Cg)([(0,l.autoinject)()],c)},"dialogs/changelog-dialog.html":(e,a,o)=>{o.r(a),o.d(a,{default:()=>t});const t='<template> <require from="./changelog-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="changelog-dialog"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body overflow-fade="vertical"> <iframe src="https://hub.wemod.com/en/widget/changelogPopupSSR?theme=dark&slug=${changelog.changelog.slug}"></iframe> </ux-dialog-body> </ux-dialog> </template> '},"dialogs/changelog-dialog.scss":(e,a,o)=>{o.r(a),o.d(a,{default:()=>r});var t=o(31601),i=o.n(t),l=o(76314),n=o.n(l)()(i());n.push([e.id,".changelog-dialog{padding:0 !important;width:600px;height:700px;max-height:80vh;overflow:hidden;border-radius:10px;background:#1a1e29 !important}.changelog-dialog ux-dialog-body{height:100%}.changelog-dialog close-button{right:20px;top:20px}.changelog-dialog iframe{width:100%;height:100%;border:0;margin:0}",""]);const r=n},"dialogs/choose-plan-promo-dialog":(e,a,o)=>{o.r(a),o.d(a,{ChoosePlanPromoDialog:()=>r,ChoosePlanPromoDialogService:()=>d});var t=o(15215),i=o("aurelia-dialog"),l=o("aurelia-framework"),n=o(17275);let r=class{constructor(e){this.controller=e}handleProCtaClick(){this.controller.close(!0)}};r=(0,t.Cg)([(0,l.autoinject)(),(0,t.Sn)("design:paramtypes",[i.DialogController])],r);let d=class extends n.C{constructor(){super(...arguments),this.viewModelClass="dialogs/choose-plan-promo-dialog"}};d=(0,t.Cg)([(0,l.autoinject)()],d)},"dialogs/choose-plan-promo-dialog.html":(e,a,o)=>{o.r(a),o.d(a,{default:()=>t});const t='<template> <require from="pro-promos/choose-plan-promo/choose-plan-promo"></require> <require from="./choose-plan-promo-dialog.scss"></require> <require from="shared/resources/elements/close-button"></require> <ux-dialog class="choose-plan-promo-dialog"> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <choose-plan-promo on-pro-cta-click.call="handleProCtaClick()"> </choose-plan-promo> </ux-dialog> </template> '},"dialogs/choose-plan-promo-dialog.scss":(e,a,o)=>{o.r(a),o.d(a,{default:()=>r});var t=o(31601),i=o.n(t),l=o(76314),n=o.n(l)()(i());n.push([e.id,".choose-plan-promo-dialog{width:900px;max-width:100%;padding:0}.choose-plan-promo-dialog close-button{z-index:1}.choose-plan-promo-dialog pro-showcase{overflow:hidden;border-radius:20px;display:flex}",""]);const r=n},"dialogs/email-dialog":(e,a,o)=>{o.r(a),o.d(a,{EmailDialog:()=>s,EmailDialogService:()=>c});var t=o(15215),i=o("aurelia-dialog"),l=o("aurelia-framework"),n=o(54995),r=o(70236),d=o(23218);let s=class{constructor(e){this.controller=e,this.saving=!1}attached(){this.accountChanged()}accountChanged(){this.emailSet=(0,r.Lt)(this.account.flags,2),this.emailSet&&this.controller.close(!0)}async save(){this.saving=!0,!this.emailSet&&this.accountEmail&&await this.accountEmail.submit(),this.saving=!1}get canSave(){return(this.emailSet||["valid","unsure"].includes(this.accountEmailStatus))&&!this.saving}};(0,t.Cg)([(0,l.computedFrom)("emailSet","accountEmailStatus","saving"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],s.prototype,"canSave",null),s=(0,t.Cg)([(0,n.m6)({selectors:{account:(0,n.$t)((e=>e.account))}}),(0,l.autoinject)(),(0,t.Sn)("design:paramtypes",[i.DialogController])],s);let c=class extends d.D{constructor(){super(...arguments),this.viewModelClass="dialogs/email-dialog"}};c=(0,t.Cg)([(0,l.autoinject)()],c)},"dialogs/email-dialog.html":(e,a,o)=>{o.r(a),o.d(a,{default:()=>t});const t='<template> <require from="./email-dialog.scss"></require> <require from="./resources/elements/support-wemod-footer"></require> <require from="../queue/resources/elements/creators-list"></require> <require from="../resources/elements/account-email"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="email-dialog fullscreen-dialog"> <div class="scroll-wrapper"> <div class="scroll-inner"> <header> <h1>${\'email_dialog.thank_you_for_your_payment\' | i18n}</h1> <close-button if.bind="!controller.settings.lock" click.trigger="controller.cancel()" tabindex="0"></close-button> </header> <div class="layout"> <div class="content"> <h3>${\'email_dialog.account_email\' | i18n}</h3> <p>${\'email_dialog.please_enter_a_valid_email_address\' | i18n}</p> <div class="text-input required"> <label>${\'email_dialog.your_email_address\' | i18n}</label> <account-email view-model.ref="accountEmail" status.bind="accountEmailStatus" location="email_dialog"></account-email> </div> <button disabled.bind="!canSave" click.delegate="save()"> ${\'email_dialog.continue_with_pro\' | i18n} </button> <div class="note">${\'email_dialog.note_you_can_update\' | i18n}</div> </div> <div class="creators"> <h4>${\'email_dialog.the_creators\' | i18n}</h4> <p>${\'email_dialog.your_subscription_helps\' | i18n}</p> <creators-list></creators-list> </div> </div> <support-wemod-footer></support-wemod-footer> </div> </div> </ux-dialog> </template> '},"dialogs/email-dialog.scss":(e,a,o)=>{o.r(a),o.d(a,{default:()=>u});var t=o(31601),i=o.n(t),l=o(76314),n=o.n(l),r=o(4417),d=o.n(r),s=new URL(o(83959),o.b),c=n()(i()),g=d()(s);c.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.email-dialog{background:var(--theme--background) !important}.email-dialog .scroll-wrapper{width:100vw;height:100vh;overflow-x:hidden;overflow-y:auto;padding:128px 100px 60px}.email-dialog .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.email-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.email-dialog .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.email-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.email-dialog .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.email-dialog .scroll-inner{display:flex;flex-direction:column;min-height:100%}.email-dialog header{margin:0 0 50px}.email-dialog header h1{font-weight:700;font-size:52px;line-height:40px;color:#fff}.email-dialog .layout{display:flex;flex:1 1 auto;align-items:flex-start}.email-dialog .layout>.content{background:linear-gradient(180deg, var(--theme--secondary-background) 0%, transparent 100%);border-radius:20px;padding:30px;flex:1 1 auto;margin:0 96px 0 0}.email-dialog .layout>.content h3{font-weight:800;font-size:24px;line-height:32px;color:#fff;margin:0 0 8px}.email-dialog .layout>.content p{font-size:14px;line-height:21px;color:rgba(255,255,255,.5);margin:0 0 30px}.email-dialog .layout>.content .text-input{margin-bottom:24px;max-width:334px}.email-dialog .layout>.content .text-input label{font-size:12px;line-height:18px;font-weight:500;--input__label--color: rgba(255, 255, 255, 0.4);color:var(--input__label--color);display:block;margin:0 0 5px 9px}.email-dialog .layout>.content .text-input.required label:after{display:inline;content:" *";color:#fff}.email-dialog .layout>.content button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important;min-width:290px;margin:0 0 30px}.email-dialog .layout>.content button,.email-dialog .layout>.content button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .email-dialog .layout>.content button{border:1px solid #fff}}.email-dialog .layout>.content button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.email-dialog .layout>.content button>*:first-child{padding-left:0}.email-dialog .layout>.content button>*:last-child{padding-right:0}.email-dialog .layout>.content button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .email-dialog .layout>.content button svg *{fill:CanvasText}}.email-dialog .layout>.content button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .email-dialog .layout>.content button svg{opacity:1}}.email-dialog .layout>.content button img{height:50%}.email-dialog .layout>.content button:disabled{opacity:.3}.email-dialog .layout>.content button:disabled,.email-dialog .layout>.content button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.email-dialog .layout>.content button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.email-dialog .layout>.content button:not(:disabled):hover svg{opacity:1}}.email-dialog .layout>.content button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.email-dialog .layout>.content button:hover{filter:brightness(1.1)}}.email-dialog .layout>.content .note{font-size:11px;line-height:17px;letter-spacing:1px;font-weight:600;color:rgba(255,255,255,.4)}.email-dialog .layout>.creators{flex:0 1 408px;padding:40px 50px;border:1px solid #424242;border-radius:20px}.email-dialog .layout>.creators h4{font-weight:800;font-size:21px;line-height:30px;font-weight:700;color:#fff;margin:0 0 5px}.email-dialog .layout>.creators p{font-size:14px;line-height:21px;line-height:19px;color:rgba(255,255,255,.5);margin:0 0 20px}.email-dialog support-wemod-footer{margin-top:40px}`,""]);const u=c},"dialogs/failed-payment-dialog":(e,a,o)=>{o.r(a),o.d(a,{FailedPaymentDialog:()=>c,FailedPaymentDialogService:()=>g});var t=o(15215),i=o("aurelia-dialog"),l=o("aurelia-framework"),n=o(68502),r=o(17275),d=o(20057),s=o(54995);let c=class{#d;#s;constructor(e,a,o){this.controller=e,this.#d=a,this.#s=o}attached(){const e=this.account.subscription?.pastDueInvoice;if(e){let a=parseFloat(e.amount);(0,d.rx)(e.currency)||(a*=100),this.localizedPrice=this.#s.formatCurrency(a,e.currency),this.period=this.account.subscription?.period??null}}openChangePaymentMethodDialog(){this.controller.close(!0),this.#d.open("failed_payment_dialog")}};c=(0,t.Cg)([(0,s.m6)({selectors:{account:(0,s.$t)((e=>e.account))}}),(0,l.autoinject)(),(0,t.Sn)("design:paramtypes",[i.DialogController,n.N,d.F2])],c);let g=class extends r.C{constructor(){super(...arguments),this.viewModelClass="dialogs/failed-payment-dialog"}};g=(0,t.Cg)([(0,l.autoinject)()],g)},"dialogs/failed-payment-dialog.html":(e,a,o)=>{o.r(a),o.d(a,{default:()=>n});var t=o(14385),i=o.n(t),l=new URL(o(11394),o.b);const n='<template> <require from="./failed-payment-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/resources/elements/loading-indicator"></require> <require from="../shared/resources/elements/features"></require> <ux-dialog class="failed-payment-dialog module"> <ux-dialog-header> <close-button click.delegate="controller.cancel()" tabindex="0"></close-button> <h1 innerhtml.bind="\'failed_payment_dialog.your_pro_payment_is_due\' | i18n | markdown"></h1> </ux-dialog-header> <ux-dialog-body> <div class="message" innerhtml.bind="`failed_payment_dialog.click_below_to_add_or_update_payment_for_your_subscription_of_$x_${period}` | i18n:{x:localizedPrice} | markdown"></div> <div class="container"> <button class="button" click.delegate="openChangePaymentMethodDialog()"> ${\'failed_payment_dialog.add_payment\' | i18n} </button> <div class="benefits"> <span innerhtml.bind="\'failed_payment_dialog.without_payment_youll_lose_these_benefits\' | i18n | markdown"></span> <i><inline-svg src="'+i()(l)+'"></inline-svg></i> </div> </div> <features></features> </ux-dialog-body> </ux-dialog> </template> '},"dialogs/failed-payment-dialog.scss":(e,a,o)=>{o.r(a),o.d(a,{default:()=>r});var t=o(31601),i=o.n(t),l=o(76314),n=o.n(l)()(i());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.failed-payment-dialog{border:0;background:linear-gradient(180deg, var(--theme--secondary-background) 0%, #000 100%) !important;width:900px;padding:43px 48px}.failed-payment-dialog ux-dialog-header h1{font-size:30px;line-height:36px;color:#fff;margin-bottom:11px}.failed-payment-dialog ux-dialog-header h1 em{color:var(--color--accent)}.failed-payment-dialog .message{font-size:18px;line-height:30px;font-weight:600;color:rgba(255,255,255,.5);margin-bottom:27px}.failed-payment-dialog .message em{font-style:normal;color:#fff}.failed-payment-dialog .container{display:flex;align-items:center;margin-bottom:24px}.failed-payment-dialog .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}.failed-payment-dialog .button,.failed-payment-dialog .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .failed-payment-dialog .button{border:1px solid #fff}}.failed-payment-dialog .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.failed-payment-dialog .button>*:first-child{padding-left:0}.failed-payment-dialog .button>*:last-child{padding-right:0}.failed-payment-dialog .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .failed-payment-dialog .button svg *{fill:CanvasText}}.failed-payment-dialog .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .failed-payment-dialog .button svg{opacity:1}}.failed-payment-dialog .button img{height:50%}.failed-payment-dialog .button:disabled{opacity:.3}.failed-payment-dialog .button:disabled,.failed-payment-dialog .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.failed-payment-dialog .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.failed-payment-dialog .button:not(:disabled):hover svg{opacity:1}}.failed-payment-dialog .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.failed-payment-dialog .button:hover{filter:brightness(1.1)}}.failed-payment-dialog .benefits{font-size:16px;line-height:24px;font-weight:500;color:rgba(255,255,255,.6);margin-left:24px}.failed-payment-dialog .benefits em{font-style:normal;color:#fff}.failed-payment-dialog .benefits i{margin-left:3px;margin-top:8px;vertical-align:middle}",""]);const r=n},"dialogs/feature-announcement-dialog":(e,a,o)=>{o.r(a),o.d(a,{FeatureAnnouncementDialog:()=>c,FeatureAnnouncementDialogService:()=>g});var t=o(15215),i=o("aurelia-dialog"),l=o("aurelia-framework"),n=o(20770),r=o(62914),d=o(17275),s=o(48881);let c=class{#t;#n;constructor(e,a,o){this.controller=e,this.#t=a,this.#n=o}activate(e){this.config=e,!1===e?.cancelable&&(this.controller.settings.lock=!0,this.controller.settings.overlayDismiss=!1,this.controller.settings.keyboard=!1),this.#t.event("feature_announcement_dialog_open",{featureName:e.featureName},r.Io),this.#n.dispatch(s.NX,`${e.featureName}AnnouncementShown`,!0)}handleActionClick(e){e.onClick&&e.onClick(),this.controller.cancel()}};c=(0,t.Cg)([(0,l.autoinject)(),(0,t.Sn)("design:paramtypes",[i.DialogController,r.j0,n.il])],c);let g=class extends d.C{constructor(){super(...arguments),this.viewModelClass="dialogs/feature-announcement-dialog"}};g=(0,t.Cg)([(0,l.autoinject)()],g)},"dialogs/feature-announcement-dialog.html":(e,a,o)=>{o.r(a),o.d(a,{default:()=>t});const t='<template> <require from="./feature-announcement-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../resources/elements/beta-tag.html"></require> <ux-dialog class="feature-announcement-dialog scrollable"> <div class="dialog-scroll-wrapper"> <ux-dialog-header if.bind="config.cancelable !== false"> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body style.bind="{\'--feature-announcement-dialog-highlight-color\': config.highlightColor}"> <header> <div if.bind="config.headerKey">${config.headerKey | i18n}</div> <h1> <span class="icon" if.bind="config.icon">${config.icon}</span> ${config.featureTitleKey | i18n} <beta-tag if.bind="config.beta"></beta-tag> </h1> </header> <p innerhtml.bind="config.messageKey | i18n:config.messageParams | markdown"></p> <div class="actions" if.bind="config.actions"> <wm-button repeat.for="action of config.actions" click.delegate="handleActionClick(action)" color.bind="action.isSecondary ? \'inverse\' : \'primary\'" variant.bind="action.isSecondary ? \'unfilled\' : \'filled\'" size="l"> ${action.labelKey | i18n} </wm-button> </div> <video src.bind="config.videoUrl" autoplay muted loop.bind="config.loopVideo"></video> </ux-dialog-body> </div> </ux-dialog> </template> '},"dialogs/feature-announcement-dialog.scss":(e,a,o)=>{o.r(a),o.d(a,{default:()=>u});var t=o(31601),i=o.n(t),l=o(76314),n=o.n(l),r=o(4417),d=o.n(r),s=new URL(o(83959),o.b),c=n()(i()),g=d()(s);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,.feature-announcement-dialog .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.feature-announcement-dialog{--feature-announcement-dialog-highlight-color: var(--theme--highlight);width:900px;padding:0;text-align:center;background:var(--theme--background) !important}.feature-announcement-dialog header{margin:56px 0 0;color:#fff}.feature-announcement-dialog header div{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px}.feature-announcement-dialog header h1{font-weight:900;font-size:52px;line-height:65px;font-style:italic;letter-spacing:-3px;display:block;margin:-5px 0}.feature-announcement-dialog p{margin:5px 0 36px;padding:0;display:inline-block;font-size:18px;line-height:30px;max-width:550px}.theme-default .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.theme-purple-pro .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.theme-green-pro .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.theme-orange-pro .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.theme-pro .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.feature-announcement-dialog p strong{font-weight:700}.theme-default .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.theme-purple-pro .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.theme-green-pro .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.theme-orange-pro .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.theme-pro .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.feature-announcement-dialog video{display:block;width:100%;border-bottom-left-radius:20px;border-bottom-right-radius:20px}.feature-announcement-dialog .actions{display:flex;justify-content:center;flex-direction:column;align-items:center;gap:8px;margin-bottom:16px}.feature-announcement-dialog .actions wm-button{width:auto;min-width:180px}.feature-announcement-dialog .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:48px;color:var(--feature-announcement-dialog-highlight-color);vertical-align:middle;margin-top:-8px}.feature-announcement-dialog beta-tag{--beta-tag-bg-color: var(--feature-announcement-dialog-highlight-color);--beta-tag-color: #fff;vertical-align:text-top}`,""]);const u=c},"dialogs/fullscreen-webview-dialog":(e,a,o)=>{o.r(a),o.d(a,{FullscreenWebviewDialog:()=>r,FullscreenWebviewDialogService:()=>d});var t=o(15215),i=o("aurelia-framework"),l=o(23218),n=o("dialogs/webview-dialog");class r extends n.WebviewDialog{}let d=class extends l.D{constructor(){super(...arguments),this.viewModelClass="dialogs/fullscreen-webview-dialog",this.allowMultiple=!0}};d=(0,t.Cg)([(0,i.autoinject)()],d)},"dialogs/fullscreen-webview-dialog.html":(e,a,o)=>{o.r(a),o.d(a,{default:()=>t});const t='<template> <require from="./fullscreen-webview-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/resources/elements/loading-indicator"></require> <ux-dialog class="fullscreen-webview-dialog fullscreen-dialog ${!loading ? \'loaded\' : \'\'}" style.bind="config.styles"> <close-button click.delegate="controller.close(false, \'fullscreen_webview_close_button\')" tabindex="0"></close-button> <div class="scroll-wrapper"> <div class="scroll-inner"> <div class="loading-indicator" if.bind="loading"> <loading-indicator></loading-indicator> </div> <iframe ref="iframeEl" show.bind="!loading"></iframe> </div> </div> </ux-dialog> </template> '},"dialogs/fullscreen-webview-dialog.scss":(e,a,o)=>{o.r(a),o.d(a,{default:()=>r});var t=o(31601),i=o.n(t),l=o(76314),n=o.n(l)()(i());n.push([e.id,".fullscreen-webview-dialog .scroll-wrapper{width:100vw;height:100vh;overflow-x:hidden;overflow-y:auto}.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.fullscreen-webview-dialog .scroll-inner{display:flex;flex-direction:column;min-height:100vh}.fullscreen-webview-dialog iframe{width:100%;height:100%;border:0;min-height:100vh}.fullscreen-webview-dialog .loading-indicator{display:flex;width:100%;height:100vh;align-items:center;justify-content:center}.fullscreen-webview-dialog close-button{transition:visibility 0s .3s;visibility:visible}.fullscreen-webview-dialog.loaded close-button{visibility:hidden;pointer-events:none}",""]);const r=n},"dialogs/game-guide-nps-dialog":(e,a,o)=>{o.r(a),o.d(a,{GameGuideNpsDialog:()=>s,GameGuideNpsDialogService:()=>c});var t=o(15215),i=o("aurelia-dialog"),l=o("aurelia-framework"),n=o(62914),r=o(17275),d=o(54995);let s=class{#c;#t;constructor(e,a){this.controller=e,this.feedback="",this.#t=a}activate(e){this.#c=e}attached(){this.#t.event("game_guide_nps_dialog_open",{userId:this.account.uuid,guide:this.#c.guide},n.Io)}async close(){this.feedback&&this.#t.event("game_guide_nps_dialog_submit",{userId:this.account.uuid,guide:this.#c.guide,feedback:this.feedback??""},n.Io),await this.controller.close(!0)}async skipFeedback(){this.feedback="",await this.close()}async sendFeedback(){await this.close()}};s=(0,t.Cg)([(0,l.autoinject)(),(0,d.m6)({selectors:{account:(0,d.$t)((e=>e.account))}}),(0,t.Sn)("design:paramtypes",[i.DialogController,n.j0])],s);let c=class extends r.C{constructor(){super(...arguments),this.viewModelClass="dialogs/game-guide-nps-dialog"}};c=(0,t.Cg)([(0,l.autoinject)()],c)},"dialogs/game-guide-nps-dialog.html":(e,a,o)=>{o.r(a),o.d(a,{default:()=>t});const t='<template> <require from="./game-guide-nps-dialog.scss"></require> <require from="./resources/elements/nps-score-selector"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="game-guide-nps-dialog align-center"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <header>${\'game_guide_nps_dialog.thank_you_for_your_feedback\' | i18n}</header> <p>${\'game_guide_nps_dialog.let_us_know_how_to_improve\' | i18n}</p> <textarea placeholder="${\'game_guide_nps_dialog.feedback_placeholder\' | i18n}" value.bind="feedback" rows="5"></textarea> </ux-dialog-body> <ux-dialog-footer> <div class="buttons"> <button click.delegate="sendFeedback()">${\'game_guide_nps_dialog.send\' | i18n}</button> <button class="secondary" click.delegate="skipFeedback()"> ${\'game_guide_nps_dialog.skip_feedback\' | i18n} </button> </div> </ux-dialog-footer> </ux-dialog> </template> '},"dialogs/game-guide-nps-dialog.scss":(e,a,o)=>{o.r(a),o.d(a,{default:()=>u});var t=o(31601),i=o.n(t),l=o(76314),n=o.n(l),r=o(4417),d=o.n(r),s=new URL(o(83959),o.b),c=n()(i()),g=d()(s);c.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.nps-dialog nps-score-selector{margin:23px auto 8px}.nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea:disabled{opacity:.5}.nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.nps-dialog header+p{margin-top:12px}.nps-dialog .score-selector{display:inline-block}.nps-dialog .buttons{display:flex;align-items:start}.game-guide-nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.game-guide-nps-dialog nps-score-selector{margin:23px auto 8px}.game-guide-nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.game-guide-nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.game-guide-nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.game-guide-nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.game-guide-nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.game-guide-nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.game-guide-nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.game-guide-nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.game-guide-nps-dialog textarea:disabled{opacity:.5}.game-guide-nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.game-guide-nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.game-guide-nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.game-guide-nps-dialog header+p{margin-top:12px}.game-guide-nps-dialog .score-selector{display:inline-block}.game-guide-nps-dialog .buttons{display:flex;align-items:start}`,""]);const u=c}}]);
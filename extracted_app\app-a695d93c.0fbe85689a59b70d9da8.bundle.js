"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4485],{"cheats/resources/elements/trainer-info-callout":(t,e,o)=>{o.r(e),o.d(e,{TrainerInfoCallout:()=>r});var i=o(15215),n=o("aurelia-framework");let r=class{constructor(){}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],r.prototype,"calloutType",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Boolean)],r.prototype,"collapsed",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Function)],r.prototype,"toggleCollapsed",void 0),r=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[])],r)},"cheats/resources/elements/trainer-info-callout.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template> <require from="./trainer-info-callout.scss"></require> <section class="${calloutType} ${collapsed || !toggleCollapsed ? \'collapsed\' : \'\'} ${toggleCollapsed ? \'collapsible\' : \'\'}"> <div class="header-content"> <i class="section-icon"></i> <header class="${toggleCollapsed ? \'collapsible\' : \'\'}" click.delegate="toggleCollapsed ? toggleCollapsed() : null" tabindex="${toggleCollapsed ? 0 : -1}"> <div class="header-text"><template replaceable part="header"></template></div> <i if.bind="toggleCollapsed" class="collapse-icon"></i> </header> </div> <main> <div class="content"> <template replaceable part="content"></template> </div> </main> </section> </template> '},"cheats/resources/elements/trainer-info-callout.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),r=o(76314),a=o.n(r),l=o(4417),s=o.n(l),c=new URL(o(83959),o.b),d=new URL(o(44013),o.b),f=a()(n()),g=s()(c),p=s()(d);f.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,trainer-info-callout section.notes .section-icon,trainer-info-callout section.requires-testing .section-icon,trainer-info-callout section.update-pending .section-icon,trainer-info-callout section.retired .section-icon,trainer-info-callout section.collapsed header .collapse-icon,trainer-info-callout section .collapse-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}trainer-info-callout section{display:flex;border-radius:16px;flex-direction:column;padding:12px}trainer-info-callout section.notes{background:rgba(238,190,85,.2)}trainer-info-callout section.notes.collapsible:hover{background:rgba(238,190,85,.3)}trainer-info-callout section.notes .info-link{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:#eebe55;text-decoration:none;font-size:14px;line-height:21px}trainer-info-callout section.notes .info-link svg *{fill:#eebe55}@media(hover: hover){trainer-info-callout section.notes .info-link:hover{color:#fff}trainer-info-callout section.notes .info-link:hover svg *{fill:#fff}}trainer-info-callout section.notes .info-link:after{content:"";display:inline-block;width:5px;height:9px;-webkit-mask-box-image:url(${p});margin-left:5px;background:#eebe55;transition:transform .15s}@media(hover: hover){trainer-info-callout section.notes .info-link:hover{color:#fff}trainer-info-callout section.notes .info-link:hover:after{transform:translateX(3px);background:#fff}}trainer-info-callout section.notes .collapse-icon,trainer-info-callout section.notes .header-text{color:#eebe55}trainer-info-callout section.notes .header-content:hover .collapse-icon{color:#fff}trainer-info-callout section.notes a{font-weight:700}trainer-info-callout section.notes .content,trainer-info-callout section.notes .header-text,trainer-info-callout section.notes a{color:#fcf2dd}trainer-info-callout section.notes .section-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;background:rgba(238,190,85,.15);color:#eebe55}trainer-info-callout section.notes .section-icon:before{font-family:inherit;content:"assignment"}trainer-info-callout section.notes form input:focus{border-color:#eebe55;caret-color:#eebe55}trainer-info-callout section.notes button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;background:rgba(238,190,85,.3);border:none;border-radius:16px;color:#fcf2dd;box-shadow:none !important}trainer-info-callout section.notes button,trainer-info-callout section.notes button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.notes button{border:1px solid #fff}}trainer-info-callout section.notes button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}trainer-info-callout section.notes button>*:first-child{padding-left:0}trainer-info-callout section.notes button>*:last-child{padding-right:0}trainer-info-callout section.notes button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.notes button svg *{fill:CanvasText}}trainer-info-callout section.notes button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.notes button svg{opacity:1}}trainer-info-callout section.notes button img{height:50%}trainer-info-callout section.notes button:disabled{opacity:.3}trainer-info-callout section.notes button:disabled,trainer-info-callout section.notes button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){trainer-info-callout section.notes button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}trainer-info-callout section.notes button:not(:disabled):hover svg{opacity:1}}trainer-info-callout section.notes button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){trainer-info-callout section.notes button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}trainer-info-callout section.notes button:not(:disabled):active{background-color:var(--theme--highlight)}trainer-info-callout section.notes button:hover{background-color:rgba(238,190,85,.8) !important}trainer-info-callout section.requires-testing{background:rgba(240,126,242,.2)}trainer-info-callout section.requires-testing.collapsible:hover{background:rgba(240,126,242,.3)}trainer-info-callout section.requires-testing .info-link{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:#f07ef2;text-decoration:none;font-size:14px;line-height:21px}trainer-info-callout section.requires-testing .info-link svg *{fill:#f07ef2}@media(hover: hover){trainer-info-callout section.requires-testing .info-link:hover{color:#fff}trainer-info-callout section.requires-testing .info-link:hover svg *{fill:#fff}}trainer-info-callout section.requires-testing .info-link:after{content:"";display:inline-block;width:5px;height:9px;-webkit-mask-box-image:url(${p});margin-left:5px;background:#f07ef2;transition:transform .15s}@media(hover: hover){trainer-info-callout section.requires-testing .info-link:hover{color:#fff}trainer-info-callout section.requires-testing .info-link:hover:after{transform:translateX(3px);background:#fff}}trainer-info-callout section.requires-testing .collapse-icon,trainer-info-callout section.requires-testing .header-text{color:#f07ef2}trainer-info-callout section.requires-testing .header-content:hover .collapse-icon{color:#fff}trainer-info-callout section.requires-testing a{font-weight:700}trainer-info-callout section.requires-testing .content,trainer-info-callout section.requires-testing .header-text,trainer-info-callout section.requires-testing a{color:#fce5fc}trainer-info-callout section.requires-testing .section-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;background:rgba(240,126,242,.15);color:#f07ef2}trainer-info-callout section.requires-testing .section-icon:before{font-family:inherit;content:"sports_esports"}trainer-info-callout section.requires-testing form input:focus{border-color:#f07ef2;caret-color:#f07ef2}trainer-info-callout section.requires-testing button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;background:rgba(240,126,242,.3);border:none;border-radius:16px;color:#fce5fc;box-shadow:none !important}trainer-info-callout section.requires-testing button,trainer-info-callout section.requires-testing button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.requires-testing button{border:1px solid #fff}}trainer-info-callout section.requires-testing button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}trainer-info-callout section.requires-testing button>*:first-child{padding-left:0}trainer-info-callout section.requires-testing button>*:last-child{padding-right:0}trainer-info-callout section.requires-testing button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.requires-testing button svg *{fill:CanvasText}}trainer-info-callout section.requires-testing button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.requires-testing button svg{opacity:1}}trainer-info-callout section.requires-testing button img{height:50%}trainer-info-callout section.requires-testing button:disabled{opacity:.3}trainer-info-callout section.requires-testing button:disabled,trainer-info-callout section.requires-testing button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){trainer-info-callout section.requires-testing button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}trainer-info-callout section.requires-testing button:not(:disabled):hover svg{opacity:1}}trainer-info-callout section.requires-testing button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){trainer-info-callout section.requires-testing button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}trainer-info-callout section.requires-testing button:not(:disabled):active{background-color:var(--theme--highlight)}trainer-info-callout section.requires-testing button:hover{background-color:rgba(240,126,242,.8) !important}trainer-info-callout section.update-pending{background:rgba(84,110,255,.2)}trainer-info-callout section.update-pending.collapsible:hover{background:rgba(84,110,255,.3)}trainer-info-callout section.update-pending .info-link{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:#546eff;text-decoration:none;font-size:14px;line-height:21px}trainer-info-callout section.update-pending .info-link svg *{fill:#546eff}@media(hover: hover){trainer-info-callout section.update-pending .info-link:hover{color:#fff}trainer-info-callout section.update-pending .info-link:hover svg *{fill:#fff}}trainer-info-callout section.update-pending .info-link:after{content:"";display:inline-block;width:5px;height:9px;-webkit-mask-box-image:url(${p});margin-left:5px;background:#546eff;transition:transform .15s}@media(hover: hover){trainer-info-callout section.update-pending .info-link:hover{color:#fff}trainer-info-callout section.update-pending .info-link:hover:after{transform:translateX(3px);background:#fff}}trainer-info-callout section.update-pending .collapse-icon,trainer-info-callout section.update-pending .header-text{color:#546eff}trainer-info-callout section.update-pending .header-content:hover .collapse-icon{color:#fff}trainer-info-callout section.update-pending a{font-weight:700}trainer-info-callout section.update-pending .content,trainer-info-callout section.update-pending .header-text,trainer-info-callout section.update-pending a{color:#dde2ff}trainer-info-callout section.update-pending .section-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;background:rgba(84,110,255,.15);color:#546eff}trainer-info-callout section.update-pending .section-icon:before{font-family:inherit;content:"cloud_download"}trainer-info-callout section.update-pending form input:focus{border-color:#546eff;caret-color:#546eff}trainer-info-callout section.update-pending button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;background:rgba(84,110,255,.3);border:none;border-radius:16px;color:#dde2ff;box-shadow:none !important}trainer-info-callout section.update-pending button,trainer-info-callout section.update-pending button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.update-pending button{border:1px solid #fff}}trainer-info-callout section.update-pending button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}trainer-info-callout section.update-pending button>*:first-child{padding-left:0}trainer-info-callout section.update-pending button>*:last-child{padding-right:0}trainer-info-callout section.update-pending button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.update-pending button svg *{fill:CanvasText}}trainer-info-callout section.update-pending button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.update-pending button svg{opacity:1}}trainer-info-callout section.update-pending button img{height:50%}trainer-info-callout section.update-pending button:disabled{opacity:.3}trainer-info-callout section.update-pending button:disabled,trainer-info-callout section.update-pending button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){trainer-info-callout section.update-pending button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}trainer-info-callout section.update-pending button:not(:disabled):hover svg{opacity:1}}trainer-info-callout section.update-pending button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){trainer-info-callout section.update-pending button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}trainer-info-callout section.update-pending button:not(:disabled):active{background-color:var(--theme--highlight)}trainer-info-callout section.update-pending button:hover{background-color:rgba(84,110,255,.8) !important}trainer-info-callout section.retired{background:rgba(237,73,128,.2)}trainer-info-callout section.retired.collapsible:hover{background:rgba(237,73,128,.3)}trainer-info-callout section.retired .info-link{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:#ed4980;text-decoration:none;font-size:14px;line-height:21px}trainer-info-callout section.retired .info-link svg *{fill:#ed4980}@media(hover: hover){trainer-info-callout section.retired .info-link:hover{color:#fff}trainer-info-callout section.retired .info-link:hover svg *{fill:#fff}}trainer-info-callout section.retired .info-link:after{content:"";display:inline-block;width:5px;height:9px;-webkit-mask-box-image:url(${p});margin-left:5px;background:#ed4980;transition:transform .15s}@media(hover: hover){trainer-info-callout section.retired .info-link:hover{color:#fff}trainer-info-callout section.retired .info-link:hover:after{transform:translateX(3px);background:#fff}}trainer-info-callout section.retired .collapse-icon,trainer-info-callout section.retired .header-text{color:#ed4980}trainer-info-callout section.retired .header-content:hover .collapse-icon{color:#fff}trainer-info-callout section.retired a{font-weight:700}trainer-info-callout section.retired .content,trainer-info-callout section.retired .header-text,trainer-info-callout section.retired a{color:#fbdbe6}trainer-info-callout section.retired .section-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;background:rgba(237,73,128,.15);color:#ed4980}trainer-info-callout section.retired .section-icon:before{font-family:inherit;content:"report"}trainer-info-callout section.retired form input:focus{border-color:#ed4980;caret-color:#ed4980}trainer-info-callout section.retired button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;background:rgba(237,73,128,.3);border:none;border-radius:16px;color:#fbdbe6;box-shadow:none !important}trainer-info-callout section.retired button,trainer-info-callout section.retired button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.retired button{border:1px solid #fff}}trainer-info-callout section.retired button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}trainer-info-callout section.retired button>*:first-child{padding-left:0}trainer-info-callout section.retired button>*:last-child{padding-right:0}trainer-info-callout section.retired button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.retired button svg *{fill:CanvasText}}trainer-info-callout section.retired button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-info-callout section.retired button svg{opacity:1}}trainer-info-callout section.retired button img{height:50%}trainer-info-callout section.retired button:disabled{opacity:.3}trainer-info-callout section.retired button:disabled,trainer-info-callout section.retired button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){trainer-info-callout section.retired button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}trainer-info-callout section.retired button:not(:disabled):hover svg{opacity:1}}trainer-info-callout section.retired button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){trainer-info-callout section.retired button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}trainer-info-callout section.retired button:not(:disabled):active{background-color:var(--theme--highlight)}trainer-info-callout section.retired button:hover{background-color:rgba(237,73,128,.8) !important}trainer-info-callout section.collapsed main{height:0;padding:0}trainer-info-callout section.collapsed header .collapse-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:20px}trainer-info-callout section.collapsed header .collapse-icon:before{font-family:inherit;content:"unfold_more"}trainer-info-callout section main{display:block;overflow:hidden;height:auto;padding:10px 0px}trainer-info-callout section main p{margin:0;font-size:14px}trainer-info-callout section main p strong{font-weight:700}trainer-info-callout section main p+.info-link{margin-top:5px}trainer-info-callout section main p br{display:block;margin:7px 0;content:""}trainer-info-callout section main hr{border:0;border-top:1px solid rgba(255,255,255,.1);margin:15px 0}trainer-info-callout section main .row{display:inline-flex;align-items:center}trainer-info-callout section main .row>*+*{margin-left:15px}trainer-info-callout section header{font-weight:600;font-size:16px;line-height:25px;padding:0;flex-basis:100%;display:flex;align-items:center;transition:color .3s}trainer-info-callout section header .header-text{font-weight:700;font-size:20px;padding-right:20px}trainer-info-callout section header .header-text p{font-size:14px;padding-top:4px;margin:0;font-weight:normal;line-height:20px}trainer-info-callout section header .header-text p em,trainer-info-callout section header .header-text p strong{font-weight:normal}trainer-info-callout section header.collapsible,trainer-info-callout section header.collapsible *{cursor:pointer}trainer-info-callout section .header-content{display:flex;flex-basis:100%;gap:12px}trainer-info-callout section .header-content .section-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;display:flex !important;flex-shrink:0;margin:7px 0px;width:36px;height:36px;border-radius:100%;align-items:center;justify-content:center;font-size:20px}trainer-info-callout section .content{margin-left:46px;padding-right:20px}trainer-info-callout section .content,trainer-info-callout section .content *{user-select:text;cursor:auto}trainer-info-callout section .content>p{margin:0}trainer-info-callout section .content a{transition:filter .15s;cursor:pointer;text-decoration:underline;display:inline-block}trainer-info-callout section .content a::after{content:"↗"}trainer-info-callout section .content a:hover{filter:brightness(1.2)}trainer-info-callout section .content img{margin-top:8px;width:100%;max-width:500px;max-height:500px;border-radius:5px}trainer-info-callout section .read-notes-button{margin-top:15px}trainer-info-callout section .collapse-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;display:flex;font-size:20px;margin-left:auto;justify-content:end}trainer-info-callout section .collapse-icon:before{font-family:inherit;content:"unfold_less"}`,""]);const u=f},"cheats/resources/elements/trainer-meta":(t,e,o)=>{o.r(e),o.d(e,{TrainerMeta:()=>g});var i=o(15215),n=o("aurelia-framework"),r=o(96276),a=o(67064),l=o(19072),s=o(96555),c=o(20057),d=o(54995),f=o(70236);let g=class{#t;#e;#o;#i;constructor(t,e,o,i){this.#t=t,this.#e=e,this.#o=o,this.#i=i}bind(){this.#n()}catalogChanged(){this.#n()}get canCopyGameVersion(){return(0,f.Lt)(this.account?.flags,16384)}get gameTitleName(){return this.catalog?.titles[this.game.titleId]?.name??""}copyGameVersion(){this.#o.copyText(this.currentGameVersion),this.#i.toast({content:"trainer_meta.game_version_copied",type:"ok",onTop:!0})}openDiscussion(){this.game.trainer?.discussionUrl&&window.open(this.game.trainer.discussionUrl)}openVideo(){this.videoUrl&&window.open(this.videoUrl)}get currentGameVersion(){let t="";if(this.game?.id&&this.canCopyGameVersion){const e=this.#e.getPreferredInstallationInfo(this.game?.id);"number"==typeof e.version&&(t=e.version.toString())}return t}get modVideoId(){return this.videoUrl?.split("v=")?.[1]??""}#r(t){return t=Math.max(t,1e3),t=Math.min(t,1e5),Math.pow(10,Math.floor(Math.log10(t)))}#n(){const t=this.game.creatorId?this.catalog.creators[this.game.creatorId]:void 0;this.creatorUsername=t?t.username:"Unassigned",this.creatorAvatar=t?t.avatar:void 0,this.game.trainer&&(this.videoUrl=this.game.trainer.videoUrl),this.players=`${this.#t.formatNumber(this.#r(this.game?.trainer?.players??0),{notation:"compact",maximumFractionDigits:1,maximumSignificantDigits:3,roundingPriority:"lessPrecision"})}+`}get showVideo(){return!!this.videoUrl&&!!this.gameTitleName}get steamAppId(){return this.catalog?.titles[this.game.titleId]?.gameIds?.flatMap((t=>this.catalog.games[t].correlationIds))?.map(s.o.parse)?.find((t=>"steam"===t.platform))?.sku??null}get titleThumbnail(){return this.catalog?.titles[this.game.titleId]?.thumbnail}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Object)],g.prototype,"game",void 0),(0,i.Cg)([(0,n.computedFrom)("account.flags"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],g.prototype,"canCopyGameVersion",null),(0,i.Cg)([(0,n.computedFrom)("catalog","game"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],g.prototype,"gameTitleName",null),(0,i.Cg)([(0,n.computedFrom)("installedGameVersions"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],g.prototype,"currentGameVersion",null),(0,i.Cg)([(0,n.computedFrom)("videoUrl"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],g.prototype,"modVideoId",null),(0,i.Cg)([(0,n.computedFrom)("videoUrl","gameTitleName"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],g.prototype,"showVideo",null),(0,i.Cg)([(0,n.computedFrom)("catalog","game"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],g.prototype,"steamAppId",null),(0,i.Cg)([(0,n.computedFrom)("catalog","game"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],g.prototype,"titleThumbnail",null),g=(0,i.Cg)([(0,n.autoinject)(),(0,d.m6)({selectors:{account:(0,d.$t)((t=>t.account)),catalog:(0,d.$t)((t=>t.catalog)),installedGameVersions:(0,d.$t)((t=>t.installedGameVersions))}}),(0,i.Sn)("design:paramtypes",[c.F2,r.T,l.s,a.l])],g)},"cheats/resources/elements/trainer-meta.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>a});var i=o(14385),n=o.n(i),r=new URL(o(89247),o.b);const a='<template> <require from="./trainer-meta.scss"></require> <require from="shared/cheats/resources/custom-attributes/steam-capsule-bg"></require> <section if.bind="showVideo" class="video"> <a class="video-player" click.delegate="openVideo()"> <span class="video-thumbnail" steam-capsule-bg="steam-id.bind: steamAppId; use-landscape.bind: true" css="--fallback-image: url(${titleThumbnail | cdn:{size: 260}})"></span> <span class="play"><i></i></span> </a> <div class="meta-section video-info"> <span class="meta-label"> ${\'trainer_meta.gameplay_video\' | i18n} </span> <span class="meta-item"> ${\'trainer_meta.overview_of_$game_mods\' | i18n: {game : gameTitleName }} </span> </div> </section> <section class="trainer-info ${!showVideo ? \'full\' : \'\'}"> <div class="meta-items"> <div class="meta-section" if.bind="players"> <span class="meta-label"> ${\'trainer_meta.played_by\' | i18n} </span> <span class="meta-item" innerhtml.bind="\'trainer_meta.$players_players\' | i18n:{ players: players } | markdown"></span> </div> <div class="meta-section" if.bind="game.trainer.discussionUrl && !betaModsEnabled"> <span class="meta-label"> ${\'trainer_meta.have_a_question\' | i18n} </span> <a click.delegate="openDiscussion()"> ${\'game.discussion\' | i18n} -> </a> </div> <div class="meta-section"> <span class="meta-label"> ${\'trainer_meta.created_by\' | i18n} </span> <span class="meta-item"> <img class="avatar" if.bind="creatorAvatar" src.bind="creatorAvatar | cdn:{size: 16}" fallback-src="'+n()(r)+'"> <span>${creatorUsername || \'-\'}</span> </span> </div> <div class="meta-section" if.bind="canCopyGameVersion && currentGameVersion"> <span class="meta-label"> ${\'trainer_meta.version\' | i18n} </span> <a click.delegate="copyGameVersion()">${currentGameVersion}</a> </div> </div> </section> </template> '},"cheats/resources/elements/trainer-meta.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>g});var i=o(31601),n=o.n(i),r=o(76314),a=o.n(r),l=o(4417),s=o.n(l),c=new URL(o(83959),o.b),d=a()(n()),f=s()(c);d.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${f}) format("woff2")}.material-symbols-outlined,trainer-meta section.video .video-player .play i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}trainer-meta{display:flex;flex-direction:row;gap:12px;flex-wrap:wrap}trainer-meta section{padding:16px 24px;flex:.5;min-height:120px}trainer-meta section.full{flex:1}trainer-meta section.full .meta-items{grid-template-columns:repeat(auto-fit, minmax(0, 1fr))}trainer-meta section.video{max-height:100%;padding:0px 24px 0px 0px !important;margin:0px}trainer-meta section.video .video-info{display:flex;flex-direction:column;justify-content:center;gap:8px}trainer-meta section.video .video-player{position:relative;width:200px;height:100%}trainer-meta section.video .video-player:hover{cursor:pointer}trainer-meta section.video .video-player:hover .video-thumbnail{filter:brightness(75%)}trainer-meta section.video .video-player:hover .play{background:#000}trainer-meta section.video .video-player:hover .play i{color:#fff}trainer-meta section.video .video-player .play{display:flex;width:40px;height:40px;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);border-radius:100%;background:rgba(0,0,0,.6)}trainer-meta section.video .video-player .play i{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:31px;color:rgba(255,255,255,.8);margin:auto}trainer-meta section.video .video-player .play i:before{font-family:inherit;content:"play_arrow"}trainer-meta section.video .video-thumbnail{display:block;width:100%;height:100%;background-position:center;background-size:cover;position:relative;border-top-left-radius:16px;border-bottom-left-radius:16px;overflow:hidden}trainer-meta section.video .video-thumbnail:before{content:"";position:absolute;left:0;top:0;right:0;bottom:0;background:rgba(0,0,0,.4)}trainer-meta section.video .video-thumbnail.is-fallback{background-image:var(--fallback-image);display:block !important}trainer-meta .meta-items{display:grid;grid-template-columns:repeat(2, 1fr);grid-column-gap:12px;grid-row-gap:12px;width:100%}trainer-meta .meta-section{display:flex;flex-direction:column;gap:8px;justify-content:center}trainer-meta .meta-section a,trainer-meta .meta-section .meta-item{width:fit-content;color:rgba(255,255,255,.8);font-size:14px;font-weight:700}trainer-meta .meta-section a:hover{color:#fff}trainer-meta .meta-label{color:rgba(255,255,255,.6);font-size:12px;font-weight:500}trainer-meta a.meta-item{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:var(--theme--highlight);text-decoration:none}trainer-meta a.meta-item svg *{fill:var(--theme--highlight)}@media(hover: hover){trainer-meta a.meta-item:hover{color:#fff}trainer-meta a.meta-item:hover svg *{fill:#fff}}trainer-meta i,trainer-meta .avatar{vertical-align:middle;float:left;margin-right:9px}trainer-meta .avatar{width:16px;height:16px;border-radius:50%;overflow:hidden}@media(max-width: 1280px){trainer-meta section{flex-basis:100%}}`,""]);const g=d}}]);
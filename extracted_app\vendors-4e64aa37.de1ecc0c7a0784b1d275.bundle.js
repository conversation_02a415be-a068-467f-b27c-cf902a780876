(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6922],{40896:(t,e,n)=>{"use strict";n.d(e,{P:()=>s});var r=n(16566),o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t};function i(t){return function(){var e=setTimeout(r,0),n=setInterval(r,50);function r(){clearTimeout(e),clearInterval(n),t()}}}var s=function(){function t(){var t,e,n,o,s,u=this;this.flushing=!1,this.longStacks=!1,this.microTaskQueue=[],this.microTaskQueueCapacity=1024,this.taskQueue=[],r.RI.mutationObserver?this.requestFlushMicroTaskQueue=(t=function(){return u.flushMicroTaskQueue()},e=r.dv.createMutationObserver(t),n="a",o=r.dv.createTextNode("a"),(s=Object.create(null)).a="b",s.b="a",e.observe(o,{characterData:!0}),function(){o.data=n=s[n]}):this.requestFlushMicroTaskQueue=i((function(){return u.flushMicroTaskQueue()})),this.requestFlushTaskQueue=i((function(){return u.flushTaskQueue()}))}return t.prototype._flushQueue=function(t,e){var n=0,r=void 0;try{for(this.flushing=!0;n<t.length;)if(r=t[n],this.longStacks&&(this.stack="string"==typeof r.stack?r.stack:void 0),r.call(),++n>e){for(var i=0,s=t.length-n;i<s;i++)t[i]=t[i+n];t.length-=n,n=0}}catch(t){!function(t,e,n){n&&e.stack&&"object"===(void 0===t?"undefined":o(t))&&null!==t&&(t.stack=u(t.stack)+e.stack),"onError"in e?e.onError(t):setTimeout((function(){throw t}),0)}(t,r,this.longStacks)}finally{this.flushing=!1}},t.prototype.queueMicroTask=function(t){this.microTaskQueue.length<1&&this.requestFlushMicroTaskQueue(),this.longStacks&&(t.stack=this.prepareQueueStack("\nEnqueued in MicroTaskQueue by:\n")),this.microTaskQueue.push(t)},t.prototype.queueTask=function(t){this.taskQueue.length<1&&this.requestFlushTaskQueue(),this.longStacks&&(t.stack=this.prepareQueueStack("\nEnqueued in TaskQueue by:\n")),this.taskQueue.push(t)},t.prototype.flushTaskQueue=function(){var t=this.taskQueue;this.taskQueue=[],this._flushQueue(t,Number.MAX_VALUE)},t.prototype.flushMicroTaskQueue=function(){var t=this.microTaskQueue;this._flushQueue(t,this.microTaskQueueCapacity),t.length=0},t.prototype.prepareQueueStack=function(t){var e=t+function(t){return t.replace(/^[\s\S]*?\bqueue(Micro)?Task\b[^\n]*\n/,"")}(function(){var t=new Error;if(t.stack)return t.stack;try{throw t}catch(t){return t.stack}}());return"string"==typeof this.stack&&(e=u(e)+this.stack),e},t}();function u(t){var e=t.lastIndexOf("flushMicroTaskQueue");return e<0&&(e=t.lastIndexOf("flushTaskQueue"))<0||(e=t.lastIndexOf("\n",e))<0?t:t.substr(0,e)}},56577:()=>{},83260:(t,e,n)=>{"use strict";function r(t,e){var n=e&&e.split("/"),r=t.trim().split("/");if("."===r[0].charAt(0)&&n){var o=n.slice(0,n.length-1);r.unshift.apply(r,o)}return function(t){for(var e=0;e<t.length;++e){var n=t[e];if("."===n)t.splice(e,1),e-=1;else if(".."===n){if(0===e||1===e&&".."===t[2]||".."===t[e-1])continue;e>0&&(t.splice(e-1,2),e-=2)}}}(r),r.join("/")}function o(t,e){if(!t)return e;if(!e)return t;var n,r=t.match(/^([^/]*?:)\//),o=r&&r.length>0?r[1]:"";n=0===(t=t.substr(o.length)).indexOf("///")&&"file:"===o?"///":0===t.indexOf("//")?"//":0===t.indexOf("/")?"/":"";for(var i="/"===e.slice(-1)?"/":"",s=t.split("/"),u=e.split("/"),a=[],c=0,h=s.length;c<h;++c)if(".."===s[c])a.length&&".."!==a[a.length-1]?a.pop():a.push(s[c]);else{if("."===s[c]||""===s[c])continue;a.push(s[c])}for(c=0,h=u.length;c<h;++c)if(".."===u[c])a.length&&".."!==a[a.length-1]?a.pop():a.push(u[c]);else{if("."===u[c]||""===u[c])continue;a.push(u[c])}return o+n+a.join("/")+i}n.d(e,{Go:()=>a,JO:()=>l,Yc:()=>r,fj:()=>o});var i=encodeURIComponent,s=function(t){return i(t).replace("%24","$")};function u(t,e,n){var r=[];if(null==e)return r;if(Array.isArray(e))for(var o=0,a=e.length;o<a;o++)if(n)r.push(s(t)+"="+i(e[o]));else{var c=t+"["+("object"==typeof e[o]&&null!==e[o]?o:"")+"]";r=r.concat(u(c,e[o]))}else if("object"!=typeof e||n)r.push(s(t)+"="+i(e));else for(var h in e)r=r.concat(u(t+"["+h+"]",e[h]));return r}function a(t,e){for(var n=[],r=Object.keys(t||{}).sort(),o=0,i=r.length;o<i;o++){var s=r[o];n=n.concat(u(s,t[s],e))}return 0===n.length?"":n.join("&")}function c(t,e){return Array.isArray(t)?(t.push(e),t):void 0!==t?[t,e]:e}function h(t,e,n){for(var r=t,o=e.length-1,i=0;i<=o;i++){var s=""===e[i]?r.length:e[i];if(p(s),i<o){var u=r[s]&&"object"!=typeof r[s]?[r[s]]:r[s];r=r[s]=u||(isNaN(e[i+1])?{}:[])}else r=r[s]=n}}function l(t){var e={};if(!t||"string"!=typeof t)return e;var n=t;"?"===n.charAt(0)&&(n=n.substr(1));for(var r=n.replace(/\+/g," ").split("&"),o=0;o<r.length;o++){var i=r[o].split("="),s=decodeURIComponent(i[0]);if(s){var u=s.split("]["),a=u.length-1;if(/\[/.test(u[0])&&/\]$/.test(u[a])?(u[a]=u[a].replace(/\]$/,""),a=(u=u.shift().split("[").concat(u)).length-1):a=0,i.length>=2){var l=i[1]?decodeURIComponent(i[1]):"";a?h(e,u,l):(p(s),e[s]=c(e[s],l))}else e[s]=!0}}return e}function p(t){if("__proto__"===t)throw new Error("Prototype pollution detected.")}},86226:(t,e,n)=>{"use strict";n.d(e,{wF:()=>h});var r=n(83260),o=function(){function t(t){this.charSpec=t,this.nextStates=[]}return t.prototype.get=function(t){var e=this.nextStates,n=Array.isArray(e),r=0;for(e=n?e:e[Symbol.iterator]();;){var o;if(n){if(r>=e.length)break;o=e[r++]}else{if((r=e.next()).done)break;o=r.value}var i=o;if(i.charSpec.validChars===t.validChars&&i.charSpec.invalidChars===t.invalidChars)return i}},t.prototype.put=function(e){var n=this.get(e);return n||(n=new t(e),this.nextStates.push(n),e.repeat&&n.nextStates.push(n),n)},t.prototype.match=function(t){for(var e=this.nextStates,n=[],r=0,o=e.length;r<o;r++){var i=e[r],s=i.charSpec;void 0!==s.validChars?-1!==s.validChars.indexOf(t)&&n.push(i):void 0!==s.invalidChars&&-1===s.invalidChars.indexOf(t)&&n.push(i)}return n},t}(),i=new RegExp("(\\"+["/",".","*","+","?","|","(",")","[","]","{","}","\\"].join("|\\")+")","g"),s=function(){function t(t,e){this.string=t,this.caseSensitive=e}return t.prototype.eachChar=function(t){for(var e=this.string,n=0,r=e.length;n<r;++n){var o=e[n];t({validChars:this.caseSensitive?o:o.toUpperCase()+o.toLowerCase()})}},t.prototype.regex=function(){return this.string.replace(i,"\\$1")},t.prototype.generate=function(){return this.string},t}(),u=function(){function t(t,e){this.name=t,this.optional=e}return t.prototype.eachChar=function(t){t({invalidChars:"/",repeat:!0})},t.prototype.regex=function(){return"([^/]+)"},t.prototype.generate=function(t,e){return e[this.name]=!0,t[this.name]},t}(),a=function(){function t(t){this.name=t}return t.prototype.eachChar=function(t){t({invalidChars:"",repeat:!0})},t.prototype.regex=function(){return"(.+)"},t.prototype.generate=function(t,e){return e[this.name]=!0,t[this.name]},t}(),c=function(){function t(){}return t.prototype.eachChar=function(){},t.prototype.regex=function(){return""},t.prototype.generate=function(){return""},t}(),h=function(){function t(){this.rootState=new o,this.names={},this.routes=new Map}return t.prototype.add=function(t){var e=this;if(!Array.isArray(t)){for(var n=this.rootState,r=[],o="^",i={statics:0,dynamics:0,stars:0},h=[],l=t.handler.name,p=!0,d=function(t,e,n,r){var o=t;"/"===t.charAt(0)&&(o=t.substr(1));for(var i=[],h=o.split("/"),l=0,p=h.length;l<p;++l){var f=h[l],d=f.match(/^:([^?]+)(\?)?$/);if(d){var v=d,y=v[1],g=v[2];if(-1!==y.indexOf("="))throw new Error("Parameter "+y+" in route "+t+" has a default value, which is not supported.");i.push(new u(y,!!g)),e.push(y),n.dynamics++}else(d=f.match(/^\*(.+)$/))?(i.push(new a(d[1])),e.push(d[1]),n.stars++):""===f?i.push(new c):(i.push(new s(f,r)),n.statics++)}return i}(t.path,h,i,t.caseSensitive),v=0,y=d.length;v<y;v++){var g=d[v];if(!(g instanceof c)){for(var m=f(n,g),w=m[0],b=m[1],C=0,k=r.length;C<k;C++)r[C].nextStates.push(w);g.optional?(r.push(b),o+="(?:/"+g.regex()+")?"):(n=b,o+="/"+g.regex(),r.length=0,p=!1)}}p&&(n=n.put({validChars:"/"}),o+="/?");var T=[{handler:t.handler,names:h}];if(this.routes.set(t.handler,{segments:d,handlers:T}),l)for(var S=Array.isArray(l)?l:[l],x=0;x<S.length;x++)S[x]in this.names||(this.names[S[x]]={segments:d,handlers:T});for(var M=0;M<r.length;M++){var E=r[M];E.handlers=T,E.regex=new RegExp(o+"$",t.caseSensitive?"":"i"),E.types=i}return n.handlers=T,n.regex=new RegExp(o+"$",t.caseSensitive?"":"i"),n.types=i,n}t.forEach((function(t){return e.add(t)}))},t.prototype.getRoute=function(t){return"string"==typeof t?this.names[t]:this.routes.get(t)},t.prototype.handlersFor=function(t){var e=this.getRoute(t);if(!e)throw new Error("There is no route named "+t);return[].concat(e.handlers)},t.prototype.hasRoute=function(t){return!!this.getRoute(t)},t.prototype.generate=function(t,e){var n=this.getRoute(t);if(!n)throw new Error("There is no route named "+t);var o=n.handlers[0].handler;if(o.generationUsesHref)return o.href;for(var i=Object.assign({},e),s=n.segments,u={},a="",h=0,l=s.length;h<l;h++){var p=s[h];if(!(p instanceof c)){var f=p.generate(i,u);if(null==f){if(!p.optional)throw new Error("A value is required for route parameter '"+p.name+"' in route '"+t+"'.")}else a+="/",a+=f}}for(var d in"/"!==a.charAt(0)&&(a="/"+a),u)delete i[d];var v=(0,r.Go)(i);return a+(v?"?"+v:"")},t.prototype.recognize=function(t){var e=[this.rootState],n={},o=!1,i=t,s=i.indexOf("?");if(-1!==s){var u=i.substr(s+1,i.length);i=i.substr(0,s),n=(0,r.JO)(u)}"/"!==(i=decodeURI(i)).charAt(0)&&(i="/"+i);var a=i.length;a>1&&"/"===i.charAt(a-1)&&(i=i.substr(0,a-1),o=!0);for(var c=0,h=i.length;c<h&&(e=p(e,i.charAt(c))).length;c++);for(var f=[],d=0,v=e.length;d<v;d++)e[d].handlers&&f.push(e[d]);e=function(t){return t.sort((function(t,e){if(t.types.stars!==e.types.stars)return t.types.stars-e.types.stars;if(t.types.stars){if(t.types.statics!==e.types.statics)return e.types.statics-t.types.statics;if(t.types.dynamics!==e.types.dynamics)return e.types.dynamics-t.types.dynamics}return t.types.dynamics!==e.types.dynamics?t.types.dynamics-e.types.dynamics:t.types.statics!==e.types.statics?e.types.statics-t.types.statics:0}))}(f);var y=f[0];if(y&&y.handlers)return o&&"(.+)$"===y.regex.source.slice(-5)&&(i+="/"),function(t,e,n){for(var r=t.handlers,o=t.regex,i=e.match(o),s=1,u=new l(n),a=0,c=r.length;a<c;a++){for(var h=r[a],p=h.names,f={},d=0,v=p.length;d<v;d++)f[p[d]]=i[s++];u.push({handler:h.handler,params:f,isDynamic:!!p.length})}return u}(y,i,n)},t}(),l=function(t){this.splice=Array.prototype.splice,this.slice=Array.prototype.slice,this.push=Array.prototype.push,this.length=0,this.queryParams=t||{}};function p(t,e){for(var n=[],r=0,o=t.length;r<o;r++){var i=t[r];n.push.apply(n,i.match(e))}return n}function f(t,e){var n=t.put({validChars:"/"}),r=n;return e.eachChar((function(t){r=r.put(t)})),[n,r]}},"aurelia-templating-router":(t,e,n)=>{"use strict";n.r(e),n.d(e,{RouteHref:()=>m,RouterView:()=>f,RouterViewLocator:()=>d,TemplatingRouteLoader:()=>y,configure:()=>w});var r=n(18776),o=n(38468),i=n(83260),s=n(30960),u=n(27884),a=n(7530),c=n(16566),h=n(96610),l=function(t,e){return l=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},l(t,e)},p=function(){},f=function(){function t(t,e,n,r,o,i,s){this.element=t,this.container=e,this.viewSlot=n,this.router=r,this.viewLocator=o,this.compositionTransaction=i,this.compositionEngine=s,this.router.registerViewPort(this,this.element.getAttribute("name")),"initialComposition"in i||(i.initialComposition=!0,this.compositionTransactionNotifier=i.enlist())}return t.inject=function(){return[c.dv.Element,u.mc,s.eu,r.Ix,s.EO,s.VP,s.vW]},t.prototype.created=function(t){this.owningView=t},t.prototype.bind=function(t,e){this.container.viewModel=t,this.overrideContext=e},t.prototype.process=function(t,e){var n=this,r=t,i=r.component,u=i.childContainer,a=i.viewModel,c=i.viewModelResource,h=c.metadata,l=i.router.currentInstruction.config,p=l.viewPorts&&l.viewPorts[r.name]||{};u.get(d)._notify(this);var f={viewModel:p.layoutViewModel||l.layoutViewModel||this.layoutViewModel,view:p.layoutView||l.layoutView||this.layoutView,model:p.layoutModel||l.layoutModel||this.layoutModel,router:r.component.router,childContainer:u,viewSlot:this.viewSlot},v=this.viewLocator.getViewStrategy(i.view||a);return v&&i.view&&v.makeRelativeTo(o.$e.get(i.router.container.viewModel.constructor).moduleId),h.load(u,c.value,null,v,!0).then((function(t){n.compositionTransactionNotifier||(n.compositionTransactionOwnershipToken=n.compositionTransaction.tryCapture()),(f.viewModel||f.view)&&(r.layoutInstruction=f);var o=s.P5.dynamic(n.element,a,t);if(r.controller=h.create(u,o),e)return null;n.swap(r)}))},t.prototype.swap=function(t){var e=this,n=t,r=n.controller,o=n.layoutInstruction,i=this.view,u=function(){var t=s.$e[e.swapOrder]||s.$e.after,n=e.viewSlot;t(n,i,(function(){return Promise.resolve(n.add(e.view))})).then((function(){e._notify()}))},c=function(t){r.automate(e.overrideContext,t);var n=e.compositionTransactionOwnershipToken;return n?n.waitForCompositionComplete().then((function(){return e.compositionTransactionOwnershipToken=null,u()})):u()};return o?(o.viewModel||(o.viewModel=new p),this.compositionEngine.createController(o).then((function(t){var n=t.view;return s.rM.distributeView(r.view,t.slots||n.slots),t.automate((0,a.iI)(o.viewModel),e.owningView),n.children.push(r.view),n||t})).then((function(t){return e.view=t,c(t)}))):(this.view=r.view,c(this.owningView))},t.prototype._notify=function(){var t=this.compositionTransactionNotifier;t&&(t.done(),this.compositionTransactionNotifier=null)},t.$view=null,t.$resource={name:"router-view",bindables:["swapOrder","layoutView","layoutViewModel","layoutModel","inherit-binding-context"]},t}(),d=function(){function t(){var t=this;this.promise=new Promise((function(e){return t.resolve=e}))}return t.prototype.findNearest=function(){return this.promise},t.prototype._notify=function(t){this.resolve(t)},t}(),v=function(){};(0,s.rr)("<template></template>")(v);var y=function(t){function e(e){var n=t.call(this)||this;return n.compositionEngine=e,n}return function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function n(){this.constructor=t}l(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)}(e,t),e.prototype.resolveViewModel=function(t,e){return new Promise((function(n,r){var u;if("moduleId"in e){var a=e.moduleId;return null===a?u=v:(a=(0,i.Yc)(a,o.$e.get(t.container.viewModel.constructor).moduleId),u=/\.html/i.test(a)?function(t){var e=/([^\/^\?]+)\.html/i.exec(t)[1],n=function(){function t(){}return t.prototype.bind=function(t){this.$parent=t},t}();return(0,s.EM)(e)(n),(0,s.lQ)(t)(n),n}(a):a),n(u)}r(new Error('Invalid route config. No "moduleId" found.'))}))},e.prototype.createChildContainer=function(t){var e=t.container.createChild();return e.registerSingleton(d),e.getChildRouter=function(){var n;return e.registerHandler(r.Ix,(function(){return n||(n=t.createChild(e))})),e.get(r.Ix)},e},e.prototype.loadRoute=function(t,e,n){var r=this;return this.resolveViewModel(t,e).then((function(n){return r.compositionEngine.ensureViewModel({viewModel:n,childContainer:r.createChildContainer(t),view:e.view||e.viewStrategy,router:t})}))},e.inject=[s.vW],e}(r.F6),g=h.getLogger("route-href"),m=function(){function t(t,e){this.router=t,this.element=e,this.attribute="href"}return t.inject=function(){return[r.Ix,c.dv.Element]},t.prototype.bind=function(){this.isActive=!0,this.processChange()},t.prototype.unbind=function(){this.isActive=!1},t.prototype.attributeChanged=function(t,e){return e&&this.element.removeAttribute(e),this.processChange()},t.prototype.processChange=function(){var t=this;return this.router.ensureConfigured().then((function(){if(!t.isActive)return null;var e=t.element,n=t.router.generate(t.route,t.params);return e.au.controller?e.au.controller.viewModel[t.attribute]=n:e.setAttribute(t.attribute,n),null})).catch((function(t){g.error(t)}))},t.$resource={type:"attribute",name:"route-href",bindables:[{name:"route",changeHandler:"processChange",primaryProperty:!0},{name:"params",changeHandler:"processChange"},"attribute"]},t}();function w(t){t.singleton(r.F6,y).singleton(r.Ix,r.Lz).globalResources(f,m),t.container.registerAlias(r.Ix,r.Lz)}},"aurelia-testing":(t,e,n)=>{"use strict";n.r(e),n.d(e,{CompileSpy:()=>s,ComponentTester:()=>f,StageComponent:()=>p,ViewSpy:()=>u,configure:()=>d,waitFor:()=>c,waitForDocumentElement:()=>h,waitForDocumentElements:()=>l});var r=n(30960),o=n(96610),i=n(16566),s=function(){function t(t,e){(0,o.getLogger)("compile-spy").info(t.toString(),e)}return Object.defineProperty(t,"inject",{get:function(){return[i.dv.Element,r.Ij]},enumerable:!1,configurable:!0}),t.$resource={type:"attribute",name:"compile-spy"},t}(),u=function(){function t(){this.logger=(0,o.getLogger)("view-spy")}return t.prototype._log=function(t,e){this.value||"created"!==t?this.value&&-1!==this.value.indexOf(t)&&this.logger.info(t,this.view,e):this.logger.info(t,this.view)},t.prototype.created=function(t){this.view=t,this._log("created")},t.prototype.bind=function(t){this._log("bind",t)},t.prototype.attached=function(){this._log("attached")},t.prototype.detached=function(){this._log("detached")},t.prototype.unbind=function(){this._log("unbind")},t.$resource={type:"attribute",name:"view-spy"},t}(),a=function(){return a=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},a.apply(this,arguments)};function c(t,e){void 0===e&&(e={present:!0,interval:50,timeout:5e3});var n=!1;return e=a({present:!0,interval:50,timeout:5e3},e),Promise.race([new Promise((function(t,r){return setTimeout((function(){n=!0,r(new Error(e.present?"Element not found":"Element not removed"))}),e.timeout)})),function r(){var o=t(),i=null!==o&&(!(o instanceof NodeList)&&!o.jquery||o.length>0);return!e.present==!i||n?Promise.resolve(o):new Promise((function(t){return setTimeout(t,e.interval)})).then(r)}()])}function h(t,e){return c((function(){return document.querySelector(t)}),e)}function l(t,e){return c((function(){return document.querySelectorAll(t)}),e)}var p=function(){function t(){}return t.withResources=function(t){return void 0===t&&(t=[]),(new f).withResources(t)},t}(),f=function(){function t(){this.resources=[]}return t.prototype.configure=function(t){return t.use.standardConfiguration()},t.prototype.bootstrap=function(t){this.configure=t},t.prototype.withResources=function(t){return this.resources=t,this},t.prototype.inView=function(t){return this.html=t,this},t.prototype.boundTo=function(t){return this.bindingContext=t,this},t.prototype.manuallyHandleLifecycle=function(){return this._prepareLifecycle(),this},t.prototype.create=function(t){var e=this;return t((function(t){return Promise.resolve(e.configure(t)).then((function(){return e.resources&&t.use.globalResources(e.resources),t.start().then((function(){return e.host=document.createElement("div"),e.host.innerHTML=e.html,document.body.appendChild(e.host),t.enhance(e.bindingContext,e.host).then((function(){return e.rootView=t.root,e.element=e.host.firstElementChild,t.root.controllers.length&&(e.viewModel=t.root.controllers[0].viewModel),new Promise((function(t){return setTimeout((function(){return t()}),0)}))}))}))}))}))},t.prototype.dispose=function(){if(void 0===this.host||void 0===this.rootView)throw new Error("Cannot call ComponentTester.dispose() before ComponentTester.create()");return this.rootView.detached(),this.rootView.unbind(),this.host.parentNode.removeChild(this.host)},t.prototype._prepareLifecycle=function(){var t=this,e=r.Ss.prototype.bind;r.Ss.prototype.bind=function(){},this.bind=function(n){return new Promise((function(o){r.Ss.prototype.bind=e,void 0!==n&&(t.bindingContext=n),t.rootView.bind(t.bindingContext),setTimeout((function(){return o()}),0)}))};var n=r.Ss.prototype.attached;r.Ss.prototype.attached=function(){},this.attached=function(){return new Promise((function(e){r.Ss.prototype.attached=n,t.rootView.attached(),setTimeout((function(){return e()}),0)}))},this.detached=function(){return new Promise((function(e){t.rootView.detached(),setTimeout((function(){return e()}),0)}))},this.unbind=function(){return new Promise((function(e){t.rootView.unbind(),setTimeout((function(){return e()}),0)}))}},t.prototype.waitForElement=function(t,e){var n=this;return c((function(){return n.element.querySelector(t)}),e)},t.prototype.waitForElements=function(t,e){var n=this;return c((function(){return n.element.querySelectorAll(t)}),e)},t}();function d(t){t.globalResources([s,u])}}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[9501],{16953:(e,t,s)=>{s.d(t,{A:()=>o,h:()=>a});const i={...s(57651)};function a(e){const t={...e};delete t.services,Object.assign(i,t),e.services&&Object.assign(i.services,e.services)}const o=i},21795:(e,t,s)=>{s.d(t,{Ij:()=>n,Yb:()=>o,dY:()=>r,jP:()=>a,kK:()=>i,mN:()=>l});class i{constructor(e){this.id=e}}class a{}class o{}class r{constructor(e,t,s,i,a){this.location=e,this.titleId=t,this.gameId=s,this.trainerId=i,this.searchResult=a}}class n{constructor(e,t,s){this.trigger=e,this.gameId=t,this.enabled=s}}class l{constructor(e,t,s,i){this.gameId=e,this.cheatId=t,this.type=s,this.source=i}}},60692:(e,t,s)=>{s.d(t,{K:()=>a,n:()=>i});const i={E39:"2023_12_foreground_mod_time_limit",MapsInHeader:"2024_06_maps_in_header",Onboarding:"2024_06_onboarding",MakeMobileAppFree:"2024_10_make_mobile_app_free",ProShowcaseModal_old:"2025_01_new_pro_modal",ProShowcaseModal:"2025_02_new_pro_modal",NewProSelectPlanPagesRound2:"2025_03_new_pro_page_select_plan_round_2"},a=[i.MapsInHeader,i.Onboarding,i.MakeMobileAppFree,i.ProShowcaseModal,i.NewProSelectPlanPagesRound2]},"dialogs/webview-dialog":(e,t,s)=>{s.r(t),s.d(t,{WebviewDialog:()=>M,WebviewDialogService:()=>C,getWebviewSupportedSettings:()=>y});var i=s(15215),a=s("aurelia-dialog"),o=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(68663),l=s(10699),d=s(62914),c=s(27958),h=s(67064),g=s(21795),u=s(17275),p=s(92465),w=s(20057),m=s(92694),v=s(54995),b=s(49442),_=s(29944),f=s("dialogs/payment-processing-dialog");const y=e=>({theme:e.theme,useWindowsContrastMode:e.useWindowsContrastMode,reduceMotion:e.reduceMotion});let M=class{#e;#t;#s;#i;#a;#o;#r;#n;#l;#d;#c;#h;#g;constructor(e,t,s,i,a,o,r,n,l,d){this.controller=e,this.wasClosedByWebview=!1,this.loading=!0,this.#e=t,this.#t=s,this.#s=i,this.#i=a,this.#a=o,this.#o=r,this.#r=n,this.#n=l,this.#l=d}activate(e){this.config=e}deactivate(e){this.wasClosedByWebview||this.#h?.execute("external_close",{ok:!e.wasCancelled,output:e.output||"cancel"}).catch(b.Y),this.#e.event("webview_close",{wasClosedByWebview:this.wasClosedByWebview,wasLoading:this.loading,wasCanceled:e.wasCancelled,route:this.config.route},d.Io)}async attached(){let e;this.#d=Date.now(),this.#c=(0,p.Ix)((()=>this.#u("load-timeout",`Timed out trying to load "${this.iframeEl.src}"`)),3e4),this.#g=new p.Vd([this.#c]);try{e=await this.#a.createWebview({mode:"modal",locale:this.#i.getEffectiveLocale().baseName,route:this.config.route,params:this.config.params??{},settings:y(this.settings)})}catch(e){return void this.#u("create-webview-error",`Error creating webview with route "${this.config.route}"`)}this.iframeEl&&(this.iframeEl.src=e.url,this.iframeEl.contentWindow&&(this.#h=new _.Jx(this.iframeEl.contentWindow,e.origin),this.#h.setHandler("event",(e=>this.#p(e))),this.#h.setHandler("ad_conversion",(e=>this.#w(e))),this.#h.setHandler("error",(e=>this.#m(e))),this.#h.setHandler("router_event",(e=>this.#v(e))),this.#h.setHandler("resize",(e=>this.#b(e))),this.#h.setHandler("view_title",(e=>this.#_(e))),this.#h.setHandler("view_collection",(e=>this.#f(e))),this.#h.setHandler("close",(e=>this.#y(e))),this.#h.setHandler("open_uri",(e=>this.#M(e))),this.#h.setHandler("toast_message",(e=>this.#C(e))),this.#h.setHandler("set_payment_processing_state",(e=>this.#E(e))),this.#g.push(this.#h)))}detached(){this.#r.hide(),this.#g?.dispose(),this.#g=null,this.#c=null,this.#h=null}handleResize(e,t){this.iframeEl.width="auto"===t[0]?e[0].toString():`${t[0]}px`,this.iframeEl.height="auto"===t[1]?e[1].toString():`${t[1]}px`}#u(e,t){this.#e.event("webview_dialog_error",{type:e,message:t??""},d.Io),this.controller.cancel("error")}#p(e){return this.#e.event(e.name,e.data,e.dispatch??d.Io),!0}#w(e){return this.#e.adConversion(e.label,e.data),!0}#m(e){return this.#u(e.type,e.message),!0}#v(e){if(this.handleResize(e.size,e.preferredSize),"router:navigation:complete"===e.event){const e=Date.now()-this.#d;this.#c?.dispose(),this.loading=!1,this.#s.publish("webview_load",{mode:"modal",route:this.config.route,params:this.config.params,duration:e}),this.#l.collectImmediately({name:"webview_window_loadtime",value:e,tags:[{key:"client_version",value:"$clientVersion"},{key:"webview_route",value:this.config.route}]})}return"router:navigation:error"===e.event&&this.#u("router-navigation",`Error navigating to "${e.name}"`),!0}#b(e){return this.handleResize(e.size,e.preferredSize),!0}#_(e){return this.#t.router.navigateToRoute("title",{titleId:e.gameId,gameId:e.gameId,previousRoute:e.previousRoute,parentRoute:e.parentRoute}),this.#s.publish(new g.dY(e.location??"",e.titleId,e.gameId||null,null,!1)),!0}#f(e){return this.#t.router.navigateToRoute("collection",{slug:e.slug}),!0}#y(e){return this.wasClosedByWebview=!0,e.refreshAccount&&this.#n.refreshAccount(),this.controller.close(e.ok,e.output),!0}#M(e){return window.open(e.uri,"_blank"),!0}#C(e){return this.#o.toast({content:"string"==typeof e.content?w.F2.literal(e.content):e.content,type:e.type}),!0}async#E(e){return e.processing?await this.#r.show():await this.#r.hide(),!0}};M=(0,i.Cg)([(0,r.autoinject)(),(0,v.m6)({selectors:{settings:(0,v.$t)((e=>e.settings))}}),(0,i.Sn)("design:paramtypes",[a.DialogController,d.j0,c.L,o.EventAggregator,w.F2,n.x,h.l,f.PaymentProcessingDialogService,l.G,m.k])],M);let C=class extends u.C{constructor(){super(...arguments),this.viewModelClass="dialogs/webview-dialog"}};C=(0,i.Cg)([(0,r.autoinject)()],C)},"dialogs/webview-dialog.html":(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});const i='<template> <require from="./webview-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/resources/elements/loading-indicator"></require> <ux-dialog class="webview-dialog scrollable ${!loading ? \'loaded\' : \'\'}" style.bind="config.styles"> <close-button click.delegate="controller.close(false, \'close_button\')" tabindex="0"></close-button> <div class="loading-indicator" if.bind="loading"> <loading-indicator></loading-indicator> </div> <div class="dialog-scroll-wrapper" show.bind="!loading"> <iframe ref="iframeEl" sandbox="allow-same-origin allow-forms allow-scripts allow-popups allow-pointer-lock"></iframe> </div> </ux-dialog> </template> '},"dialogs/webview-dialog.scss":(e,t,s)=>{s.r(t),s.d(t,{default:()=>n});var i=s(31601),a=s.n(i),o=s(76314),r=s.n(o)()(a());r.push([e.id,".webview-dialog{border:0;padding:0;width:auto}.webview-dialog .dialog-scroll-wrapper{display:flex;border-radius:20px}.webview-dialog iframe{border:0}.webview-dialog .loading-indicator{padding:60px 150px}.webview-dialog.loaded{animation:dialog-pop2 .2s ease-in-out !important}",""]);const n=r}}]);
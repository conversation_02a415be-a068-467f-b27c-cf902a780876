"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5690,9708],{3972:(e,t,i)=>{i.d(t,{Mz:()=>l,Ro:()=>n,TB:()=>o});var o,r=i(15215),a=i("aurelia-framework"),s=i(10351);!function(e){e[e.Read=1]="Read",e[e.Write=2]="Write",e[e.Execute=4]="Execute"}(o||(o={}));const n={appContainer:"S-1-15-2-1",everyone:"S-1-1-0"};let l=class{#e;constructor(e){this.#e=e}async initialize(){return await this.#e.initialize()}injectDll(e,t,i=0,o=5){return this.#e.run("InjectDll",{processId:e,dllPath:t,stackSize:i,timeout:o})}resolvePath(e){return this.#e.run("ResolvePath",e)}statFile(e){return this.#e.run("StatFile",e)}async getGpuDevices(){return await this.#e.run("GetGpuDevices")}onHotkeyPress(e){return this.#e.subscribe("Hotkey",e)}getRunningProcesses(e){return this.#e.run("GetProcesses",{argsFilter:e})}createRemoteThread(e,t,i,o,r){return this.#e.run("CreateRemoteThread",{processId:e,module:t,export:i,arg:o?o.toString("base64"):null,proxy:r||null})}grantFilePermissions(e,t,i){return this.#e.run("GrantFilePermission",{file:e,sids:t,permissions:i})}waitForInputIdle(e,t){return this.#e.run("WaitForInputIdle",{processId:e,timeout:t})}runLauncherBypass(e){return this.#e.run("RunLauncherBypass",e)}getFileSignatureInfo(e){return this.#e.run("GetFileSignatureInfo",{file:e})}addAvExclusion(e){return this.#e.run("AddAvExclusion",{directory:e})}getUacIcon(e){return this.#e.run("GetUacIcon",{size:e})}mountLauncherProxy(e){return this.#e.run("MountLauncherProxy",e)}unmountLauncherProxy(e){return this.#e.run("UnmountLauncherProxy",{launcher:e})}suspendProcess(e){return this.#e.run("SuspendProcess",{processId:e})}resumeProcess(e){return this.#e.run("ResumeProcess",{processId:e})}getInstalledPackageId(e){return this.#e.run("GetInstalledPackageId",e)}};l=(0,r.Cg)([(0,a.autoinject)(),(0,r.Sn)("design:paramtypes",[s.xx])],l)},10351:(e,t,i)=>{i.d(t,{Ik:()=>a,Vn:()=>s,xx:()=>n});var o=i(15215),r=i("aurelia-framework");class a extends Error{constructor(e,t){super(e),Object.setPrototypeOf(this,a.prototype),this.data=t}}class s extends a{constructor(){super("The user denied required process elevation."),this.doNotReport=!0,Object.setPrototypeOf(this,s.prototype)}}let n=class{};n=(0,o.Cg)([(0,r.autoinject)()],n)},11087:(e,t,i)=>{i.d(t,{J:()=>l,S:()=>o});var o,r=i(15215),a=i("aurelia-framework"),s=i(10351);function n(e){switch(e){case o.Registry64:return"64";case o.Registry32:return"32";default:return"default"}}!function(e){e[e.Default=0]="Default",e[e.Registry32=1]="Registry32",e[e.Registry64=2]="Registry64"}(o||(o={}));let l=class{#e;constructor(e){this.#e=e}#t(e){return this.#e.run("QueryRegistry",e)}queryValue(e,t=o.Default){const i=e.split("\\"),r=i.shift(),a=i.pop();return this.#t({type:"value",hive:r,subkey:i.join("\\"),value:a,view:n(t)})}querySubkeyValues(e,t=o.Default){const i=e.split("\\"),r=i.shift();return this.#t({type:"subkey-values",hive:r,subkey:i.join("\\"),view:n(t)})}querySubkeySubkeyValues(e,t=o.Default){const i=e.split("\\"),r=i.shift();return this.#t({type:"subkey-subkey-values",hive:r,subkey:i.join("\\"),view:n(t)})}};l=(0,r.Cg)([(0,a.autoinject)(),(0,r.Sn)("design:paramtypes",[s.xx])],l)},83974:(e,t,i)=>{i.d(t,{M:()=>c,c:()=>h});var o=i(35317),r=i(69278),a=i(96610),s=i(35392),n=i("services/bugsnag/index");const l=(0,a.getLogger)("native"),d=new Map([[1,"Process terminated with exit code 1."],[1073807364,"Process terminated for Windows shutdown."],[3221226091,"Process terminated for Windows logoff."],[4294967295,"Process terminated by task manager."]]);class c extends Error{constructor(e,t=null,i){super(e),this.code=t,this.reported=!1,Object.setPrototypeOf(this,c.prototype),i&&(this.doNotReport=!0)}}function m(e){if(e)try{e.destroy()}catch{}return null}function v(e){if(e)try{e.close()}catch{}return null}let p=!1;class h{#i;constructor(e){this.#i=e,window.addEventListener("unload",(()=>p=!0),{once:!0})}async start(){if(!await s.promises.stat(this.#i).then((e=>e.isFile())).catch((()=>!1)))throw new c("Support executable not found.");const e=`WeMod\\Support_${Date.now()}`,t=`${e}_In`,i=`${e}_Out`;return await new Promise(((e,a)=>{let s,h,g,u=!1,f=(0,r.createServer)((t=>{s=t,u=!0,e([s,h])})),b=(0,r.createServer)((e=>{h=e;try{e.write(t,(e=>e&&y(e)))}catch(e){y(e)}}));const y=e=>{if(s&&(s=m(s)),h&&(h=m(h)),f&&(f=v(f)),b&&(b=v(b)),g&&(g=function(e){if(e)try{e.kill()}catch{}return null}(g)),p)return;let t,i;if(e instanceof Error){t=e;const o=e;"string"==typeof o.code&&(i=new c(`Auxiliary process failed with code ${o.code}.`,o.code))}if("number"==typeof e&&0!==e){const o=d.get(e);if(o)t=!1,i=new c(o,e,u);else{const t=`0x${e.toString(16).padStart(8,"0")}`;i=new c(`Auxiliary process exited with code ${t}.`,e)}}null!==e&&0!==e||(i=new c("Auxiliary process exited without an exit code.")),i??=new c(`Auxiliary process failed with ${typeof e} result.`),!1!==t&&((0,n.report)(t??i),t instanceof c&&(t.reported=!0),i instanceof c&&(i.reported=!0)),l.error(i.message),u||(u=!0,a(i))};f.on("error",y),b.on("error",y),f.listen(`\\\\.\\pipe\\${t}`,(()=>{b?.listen(`\\\\.\\pipe\\${i}`,(()=>{try{g=(0,o.spawn)(this.#i,[i]),g.on("error",y),g.on("exit",y)}catch(e){y(e)}}))}))}))}}},"my-videos/my-videos":(e,t,i)=>{i.r(t),i.d(t,{MyVideos:()=>b});var o=i(15215),r=i("aurelia-framework"),a=i(20770),s=i(55648),n=i(89509),l=i(31127),d=i(979),c=i(62914),m=i(40127),v=i(19648),p=i("resources/elements/layout-toggle"),h=i(54995),g=i(48881),u=i(64415),f=i("my-videos/resources/video-player-dialog/video-player-dialog");let b=class{#o;#r;#a;#s;constructor(e,t,i,o,r){this.videoPlayerDialogService=r,this.layout=p.Layout.Thumbnail,this.selectedFeed="all",this.selectedFilterOption="recently_recorded",this.searchTerms="",this.hasSeenMyVideosEducationCard=!1,this.gameVideos=[],this.searchResults=[],this.favorites=[],this.isSearching=!1,this.#o=null,this.capture=e,this.#r=t,this.#a=i,this.#s=o}attached(){this.#n(),this.#l(),this.#a.synchronizeThumbnails().then((e=>{e&&this.#l()})),this.#s.dispatch(g.NX,"myVideosSeen",!0),this.#o=this.#a.onVideosChanged((()=>this.#l()))}detached(){this.#o&&(this.#o.dispose(),this.#o=null)}onSearch(){this.isSearching=!0,this.searchTerms&&this.gameVideos?this.searchResults=this.gameVideos.map((e=>{const t=e.videos.map((t=>({...t,gameTitle:e.titleName}))),i=(0,u.p)(t,this.searchTerms,["filename","gameTitle"],{prioritizeStartMatches:!0});return{titleName:e.titleName,folderPath:e.folderPath,videos:i}})).filter((e=>e.videos.length>0)):this.onClear()}onClear(){this.searchResults=[],this.searchTerms="",this.isSearching=!1}handleEducationCardClick(){this.#s.dispatch(g.NX,"hasSeenMyVideosEducationCard",!0),this.#r.event("my_videos_education_card_dismiss_click",{},c.Io)}handleVideoOpen(e){this.#r.event("video_play_intent",{},c.Io),this.videoPlayerDialogService.open({video:e})}handleFeedClick(e){this.selectedFeed=e,this.#r.event("my_videos_tab_click",{option:e},c.Io)}handleFilterClick(e){this.selectedFilterOption=e,this.#r.event("my_videos_filter_click",{option:e},c.Io)}get videos(){const e=this.searchResults.length>0||this.hasNoSearchResults?this.searchResults:this.gameVideos;return this.#d(e)}get hasNoSearchResults(){return this.isSearching&&0===this.searchResults.length}async#l(){const e=await this.#a.getAllVideos();this.gameVideos=e}#d(e){if(!e||0===e.length)return[];let t="recently_recorded"===this.selectedFilterOption?this.#c(e):this.#m(e);return"favorites"===this.selectedFeed&&(t=this.#v(t)),t}#c(e){const t=e.flatMap((e=>e.videos));t.sort(((e,t)=>t.createdAt.getTime()-e.createdAt.getTime()));const i=(0,s.A)(),o=(0,n.A)(),r=(0,l.A)((0,d.A)(i,7));return[{nameKey:"today",startDate:i,endDate:null},{nameKey:"yesterday",startDate:o,endDate:i},{nameKey:"last_week",startDate:r,endDate:o},{nameKey:"past",startDate:null,endDate:r}].map((e=>{const i=t.filter((t=>{const i=t.createdAt.getTime(),o=!e.startDate||i>=e.startDate.getTime(),r=!e.endDate||i<e.endDate.getTime();return o&&r}));return i.length>0?{groupName:e.nameKey,videos:i}:null})).filter((e=>null!==e))}#m(e){return e.map((e=>({groupName:e.titleName,videos:e.videos})))}#v(e){const t=this.favorites||[];return e.map((e=>({groupName:e.groupName,videos:e.videos.filter((e=>t.includes(e.id)))}))).filter((e=>e.videos.length>0))}#n(){this.#r.screenView({name:"My Videos",class:"MyVideos"})}};(0,o.Cg)([(0,r.computedFrom)("gameVideos","selectedFilterOption","selectedFeed","searchResults","favorites"),(0,o.Sn)("design:type",Array),(0,o.Sn)("design:paramtypes",[])],b.prototype,"videos",null),(0,o.Cg)([(0,r.computedFrom)("searchTerms","searchResults"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],b.prototype,"hasNoSearchResults",null),b=(0,o.Cg)([(0,h.m6)({selectors:{favorites:(0,h.$t)((e=>e?.favoriteVideos)),hasSeenMyVideosEducationCard:(0,h.$t)((e=>e.flags?.hasSeenMyVideosEducationCard))}}),r.autoinject,(0,o.Sn)("design:paramtypes",[m.Re,c.j0,v.Z,a.il,f.VideoPlayerDialogService])],b)},"my-videos/my-videos.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var o=i(14385),r=i.n(o),a=new URL(i(24439),i.b),s=new URL(i(89e3),i.b);const n='<template> <require from="./my-videos.scss"></require> <require from="./resources/elements/video-grid-item/video-grid-item"></require> <require from="./resources/elements/video-list-item/video-list-item"></require> <require from="../resources/elements/beta-tag.html"></require> <require from="../resources/elements/lazy-render"></require> <require from="../resources/elements/layout-toggle"></require> <require from="../resources/elements/search-input"></require> <require from="../app/resources/elements/education-card"></require> <require from="../shared/resources/elements/tabs"></require> <require from="../shared/resources/elements/tab"></require> <section class="my-videos view-background au-animate"> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" overflow-fade="vertical" ref="scrollEl"> <div class="top"> <header> <span class="name">${\'my_videos.title\' | i18n}</span> <layout-toggle layout.bind="layout" settings-key="my_videos_layout" source="my_videos"></layout-toggle> </header> </div> <section class="search"> <search-input on-clear.call="onClear()" on-search.call="onSearch()" search-terms.bind="searchTerms" disabled.bind="!videos.length && !hasNoSearchResults" placeholder-key="my_videos.search_placeholder" button-text-key="my_videos.search_button"></search-input> </section> <div class="filters"> <tabs> <tab active.bind="selectedFeed === \'all\'" click.delegate="handleFeedClick(\'all\')"> <span class="icon icon-all">browse</span> <span>${\'my_videos.all\' | i18n}</span> </tab> <tab active.bind="selectedFeed === \'favorites\'" click.delegate="handleFeedClick(\'favorites\')"> <span class="icon icon-favorite">kid_star</span> <span>${\'my_videos.favorites\' | i18n}</span> </tab> </tabs> <span class="filter-options"> <span class="label">${\'my_videos.sort_by\' | i18n}</span> <tabs> <tab active.bind="selectedFilterOption === \'recently_recorded\'" click.delegate="handleFilterClick(\'recently_recorded\')"> <span>${\'my_videos.recently_recorded\' | i18n}</span> </tab> <tab active.bind="selectedFilterOption === \'game\'" click.delegate="handleFilterClick(\'game\')"> <span>${\'my_videos.game\' | i18n}</span> </tab> </tabs> </span> </div> <education-card if.bind="!hasSeenMyVideosEducationCard" subtitle-key="my_videos.introducing_the_all_new" title-key="my_videos.instant_highlights" description-key="my_videos.never_miss_a_gaming_highlight_again" description-params.bind="{ hotkey: capture.displayHotkey }" button-text-key="my_videos.dismiss" graphic-image-src="'+r()(a)+'" background-image-src="'+r()(s)+'" show-beta-tag.bind="true" on-button-click.call="handleEducationCardClick()"></education-card> <div if.bind="!videos.length && !hasNoSearchResults" class="no-results-message"> <h4>${\'my_videos.no_results\' | i18n}</h4> <p if.bind="selectedFeed === \'favorites\' && !searchTerms"> ${\'my_videos.no_results_advice_favorites\' | i18n} </p> <p else>${\'my_videos.no_results_advice\' | i18n}</p> </div> <div if.bind="hasNoSearchResults" class="no-results-message"> <h4>${\'my_videos.no_results\' | i18n}</h4> <p>${\'my_videos.no_results_advice_search\' | i18n}</p> </div> <section class="group" repeat.for="group of videos"> <template if.bind="group.videos.length > 0"> <div class="title-count-container"> <h2 if.bind="selectedFilterOption === \'recently_recorded\'"> ${\'my_videos.group_name_\' + group.groupName | i18n} </h2> <h2 else>${group.groupName}</h2> <span class="count">${group.videos.length}</span> </div> </template> <div class="grid" if.bind="layout === \'thumbnail\'"> <div class="grid-item" repeat.for="video of group.videos"> <lazy-render> <template replace-part="content"> <video-grid-item item.bind="video" favorites.bind="favorites" click.delegate="handleVideoOpen(video)"></video-grid-item> </template> <template replace-part="placeholder"> <div class="video-grid-item-placeholder"></div> </template> </lazy-render> </div> </div> <div class="list" if.bind="layout === \'list\'"> <div class="list-item" repeat.for="video of group.videos"> <lazy-render> <template replace-part="content"> <video-list-item item.bind="video" favorites.bind="favorites" click.delegate="handleVideoOpen(video)"></video-list-item> </template> <template replace-part="placeholder"> <div class="video-list-item-placeholder"></div> </template> </lazy-render> </div> </div> </section> </div> </div> </section> </template> '},"my-videos/my-videos.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>v});var o=i(31601),r=i.n(o),a=i(76314),s=i.n(a),n=i(4417),l=i.n(n),d=new URL(i(83959),i.b),c=s()(r()),m=l()(d);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,section.my-videos .filters .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}section.my-videos .view-scrollable{gap:16px}section.my-videos .top{position:relative;display:flex;flex-direction:column;padding-top:8px}section.my-videos .top header{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;padding-top:4px;display:flex;align-items:flex-start}.theme-default section.my-videos .top header{color:#fff}.theme-purple-pro section.my-videos .top header{color:#fff}.theme-green-pro section.my-videos .top header{color:#fff}.theme-orange-pro section.my-videos .top header{color:#fff}.theme-pro section.my-videos .top header{color:#fff}section.my-videos .top header .name{flex:1;min-height:40px}section.my-videos .top header layout-toggle{margin-left:10px}section.my-videos .search+.group,section.my-videos .search+.empty,section.my-videos .search+.no-results-message{margin-top:38px}section.my-videos .filters{display:flex;align-items:center;gap:12px}section.my-videos .filters .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}section.my-videos .filters>*+*:before{content:"";display:inline-block;width:1px;opacity:.5;background:rgba(255,255,255,.15);height:100%;height:32px}section.my-videos .filters .filter-options{display:inline-flex;align-items:center;gap:12px}section.my-videos .filters .label{font-weight:700;font-size:14px;line-height:21px;color:rgba(255,255,255,.5)}section.my-videos .no-results-message h4{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px}.theme-default section.my-videos .no-results-message h4{color:#fff}.theme-purple-pro section.my-videos .no-results-message h4{color:#fff}.theme-green-pro section.my-videos .no-results-message h4{color:#fff}.theme-orange-pro section.my-videos .no-results-message h4{color:#fff}.theme-pro section.my-videos .no-results-message h4{color:#fff}section.my-videos .no-results-message p{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default section.my-videos .no-results-message p{color:rgba(255,255,255,.6)}.theme-purple-pro section.my-videos .no-results-message p{color:rgba(255,255,255,.6)}.theme-green-pro section.my-videos .no-results-message p{color:rgba(255,255,255,.6)}.theme-orange-pro section.my-videos .no-results-message p{color:rgba(255,255,255,.6)}.theme-pro section.my-videos .no-results-message p{color:rgba(255,255,255,.6)}section.my-videos .group{display:flex;flex-direction:column}section.my-videos .group h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;margin:0}.theme-default section.my-videos .group h2{color:#fff}.theme-purple-pro section.my-videos .group h2{color:#fff}.theme-green-pro section.my-videos .group h2{color:#fff}.theme-orange-pro section.my-videos .group h2{color:#fff}.theme-pro section.my-videos .group h2{color:#fff}section.my-videos .group .count{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px}.theme-default section.my-videos .group .count{color:rgba(255,255,255,.6)}.theme-purple-pro section.my-videos .group .count{color:rgba(255,255,255,.6)}.theme-green-pro section.my-videos .group .count{color:rgba(255,255,255,.6)}.theme-orange-pro section.my-videos .group .count{color:rgba(255,255,255,.6)}.theme-pro section.my-videos .group .count{color:rgba(255,255,255,.6)}section.my-videos .group .title-count-container{display:flex;align-items:baseline;gap:8px;margin-bottom:16px}section.my-videos .grid{display:grid;grid-template-columns:repeat(auto-fill, minmax(198px, 1fr));gap:16px}section.my-videos .grid .video-grid-item-placeholder:before{width:100%;border-radius:12px;aspect-ratio:16/9;margin-bottom:45px;display:block;content:"";background:rgba(255,255,255,.04)}`,""]);const v=c},"my-videos/resources/elements/favorite-button/favorite-button":(e,t,i)=>{i.r(t),i.d(t,{FavoriteButton:()=>l});var o=i(15215),r=i("aurelia-framework"),a=i(20770),s=i(62914),n=i(48881);let l=class{#r;#p;#s;constructor(e,t,i){this.favorites=[],this.#r=e,this.#p=t,this.#s=i}bind(){this.favoritesChanged()}favoritesChanged(){this.favorites&&(this.isFavorite=this.favorites.includes(this.videoId))}toggleFavorite(e){const t=this.isFavorite?"my_videos_unfavorite_click":"my_videos_favorite_click";this.isFavorite?(this.#p.classList.remove("bump"),this.#r.event(t,{},s.Io)):(this.#p.classList.add("bump"),setTimeout((()=>this.#p.classList.remove("bump")),300),this.#r.event(t,{},s.Io)),setTimeout((()=>{this.#s.dispatch(n.RG,this.videoId)})),e.stopPropagation()}};(0,o.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.twoWay}),(0,o.Sn)("design:type",String)],l.prototype,"videoId",void 0),(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",Array)],l.prototype,"favorites",void 0),l=(0,o.Cg)([(0,r.autoinject)(),(0,o.Sn)("design:paramtypes",[s.j0,Element,a.il])],l)},"my-videos/resources/elements/favorite-button/favorite-button.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});const o='<template class="${isFavorite ? \'favorite\' : \'\'}"> <require from="./favorite-button.scss"></require> <button click.trigger="toggleFavorite($event)" tabindex="0"> <i class="icon favorite-icon ${isFavorite ? \'favorite\' : \'\'}"></i> </button> </template> '},"my-videos/resources/elements/favorite-button/favorite-button.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>v});var o=i(31601),r=i.n(o),a=i(76314),s=i.n(a),n=i(4417),l=i.n(n),d=new URL(i(83959),i.b),c=s()(r()),m=l()(d);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,favorite-button button .favorite-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}favorite-button{position:relative}favorite-button.bump .icon,favorite-button.bump:before{animation:favorite-bump .3s 1 ease-in-out}favorite-button button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;padding:4px}favorite-button button,favorite-button button *{cursor:pointer}favorite-button button:hover{background:rgba(255,255,255,.25)}favorite-button button .favorite-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:flex}favorite-button button .favorite-icon:before{font-family:inherit;content:"kid_star"}.theme-default favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-purple-pro favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-green-pro favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-orange-pro favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-pro favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-default favorite-button button .favorite-icon:hover{color:#fff}.theme-purple-pro favorite-button button .favorite-icon:hover{color:#fff}.theme-green-pro favorite-button button .favorite-icon:hover{color:#fff}.theme-orange-pro favorite-button button .favorite-icon:hover{color:#fff}.theme-pro favorite-button button .favorite-icon:hover{color:#fff}favorite-button button .favorite-icon.favorite{font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--highlight)}`,""]);const v=c},"my-videos/resources/elements/video-grid-item/video-grid-item":(e,t,i)=>{i.r(t),i.d(t,{VideoGridItem:()=>a});var o=i(15215),r=i("aurelia-framework");class a{constructor(){this.favorites=[]}}(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",Object)],a.prototype,"item",void 0),(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",Array)],a.prototype,"favorites",void 0)},"my-videos/resources/elements/video-grid-item/video-grid-item.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});var o=i(14385),r=i.n(o),a=new URL(i(98593),i.b);const s='<template> <require from="./video-grid-item.scss"></require> <require from="../favorite-button/favorite-button"></require> <div class="video-grid-item ${isFavorite ? \'video-grid-item--favorite\' : \'\'}"> <div class="thumbnail-wrapper"> <img class="thumbnail ${thumbnailLoaded ? \'loaded\' : \'\'}" fallback-src="'+r()(a)+'" src.bind="item.thumbnailDataUrl" load.trigger="thumbnailLoaded = true"> </div> <div class="title-row"> <span class="title-name" title="${item.filename}">${item.filename}</span> <favorite-button video-id.bind="item.id" favorites.bind="favorites"></favorite-button> </div> </div> </template> '},"my-videos/resources/elements/video-grid-item/video-grid-item.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var o=i(31601),r=i.n(o),a=i(76314),s=i.n(a)()(r());s.push([e.id,'video-grid-item,video-grid-item *{cursor:pointer}video-grid-item .video-grid-item{position:relative}video-grid-item .video-grid-item:nth-child(1n+0) img{animation-delay:0.1s}video-grid-item .video-grid-item:nth-child(2n+0) img{animation-delay:0.2s}video-grid-item .video-grid-item:nth-child(3n+0) img{animation-delay:0.3s}video-grid-item .video-grid-item:nth-child(4n+0) img{animation-delay:0.4s}video-grid-item .video-grid-item:nth-child(5n+0) img{animation-delay:0.5s}video-grid-item .video-grid-item:nth-child(6n+0) img{animation-delay:0.6s}video-grid-item .video-grid-item:nth-child(7n+0) img{animation-delay:0.7s}video-grid-item .video-grid-item:nth-child(8n+0) img{animation-delay:0.8s}video-grid-item .video-grid-item:nth-child(9n+0) img{animation-delay:0.9s}video-grid-item .video-grid-item .thumbnail{display:block;border-radius:12px;overflow:hidden;width:100%;height:100%;aspect-ratio:16/9;border:2px solid rgba(0,0,0,0);transition:border .15s}video-grid-item .video-grid-item .thumbnail:not(.loaded){animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box}video-grid-item .video-grid-item favorite-button:not(:hover) button{background:none}.theme-default video-grid-item .video-grid-item:hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-purple-pro video-grid-item .video-grid-item:hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-green-pro video-grid-item .video-grid-item:hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-orange-pro video-grid-item .video-grid-item:hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-pro video-grid-item .video-grid-item:hover .title-row .title-name{color:rgba(255,255,255,.8)}video-grid-item .video-grid-item:hover .thumbnail{border:2px solid var(--theme--highlight)}video-grid-item .video-grid-item .title-row{display:flex;justify-content:space-between;align-items:center;width:100%;gap:10px;margin-top:12px;padding:2px}video-grid-item .video-grid-item .title-row .title-name{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}video-grid-item .video-grid-item .title-row .title-name,video-grid-item .video-grid-item .title-row favorite-button:not(.favorite) .favorite-icon{transition:color .2s}.theme-default video-grid-item .video-grid-item .title-row .title-name,.theme-default video-grid-item .video-grid-item .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-purple-pro video-grid-item .video-grid-item .title-row .title-name,.theme-purple-pro video-grid-item .video-grid-item .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-green-pro video-grid-item .video-grid-item .title-row .title-name,.theme-green-pro video-grid-item .video-grid-item .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-orange-pro video-grid-item .video-grid-item .title-row .title-name,.theme-orange-pro video-grid-item .video-grid-item .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-pro video-grid-item .video-grid-item .title-row .title-name,.theme-pro video-grid-item .video-grid-item .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}',""]);const n=s},"my-videos/resources/elements/video-list-item/video-list-item":(e,t,i)=>{i.r(t),i.d(t,{VideoListItem:()=>a});var o=i(15215),r=i("aurelia-framework");class a{constructor(){this.favorites=[]}}(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",Object)],a.prototype,"item",void 0),(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",Array)],a.prototype,"favorites",void 0)},"my-videos/resources/elements/video-list-item/video-list-item.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});var o=i(14385),r=i.n(o),a=new URL(i(98593),i.b);const s='<template> <require from="./video-list-item.scss"></require> <require from="../favorite-button/favorite-button"></require> <div class="video-list-item"> <div class="thumbnail-wrapper"> <img class="thumbnail ${thumbnailLoaded ? \'loaded\' : \'\'}" fallback-src="'+r()(a)+'" src.bind="item.thumbnailDataUrl" load.trigger="thumbnailLoaded = true"> </div> <span class="name"> ${item.filename} </span> <span class="favorite-column"> <favorite-button video-id.bind="item.id" favorites.bind="favorites"></favorite-button> </span> </div> </template> '},"my-videos/resources/elements/video-list-item/video-list-item.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var o=i(31601),r=i.n(o),a=i(76314),s=i.n(a)()(r());s.push([e.id,'video-list-item .video-list-item{background:rgba(var(--theme--background-accent--rgb), 0.4);backdrop-filter:blur(25px);display:flex;width:100%;padding:8px;gap:8px;align-items:center}video-list-item .video-list-item:first-of-type{border-radius:12px 12px 0 0}video-list-item .video-list-item:last-of-type{border-radius:0 0 12px 12px}video-list-item .video-list-item,video-list-item .video-list-item *{cursor:pointer}video-list-item .video-list-item .placeholder{flex:1 1 auto;height:40px}video-list-item .video-list-item .thumbnail{flex:0 0 auto}video-list-item .video-list-item .thumbnail img{display:inline-block;background:rgba(0,0,0,.1);width:40px;height:19px;border-radius:2.5px}video-list-item .video-list-item .name{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;transition:color .15s;min-width:240px;width:25%}.theme-default video-list-item .video-list-item .name{color:rgba(255,255,255,.6)}.theme-purple-pro video-list-item .video-list-item .name{color:rgba(255,255,255,.6)}.theme-green-pro video-list-item .video-list-item .name{color:rgba(255,255,255,.6)}.theme-orange-pro video-list-item .video-list-item .name{color:rgba(255,255,255,.6)}.theme-pro video-list-item .video-list-item .name{color:rgba(255,255,255,.6)}video-list-item .video-list-item .favorite-column{width:100px}video-list-item .video-list-item .favorite-column favorite-button .favorite-icon{transition:color .15s}.theme-default video-list-item .video-list-item .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-purple-pro video-list-item .video-list-item .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-green-pro video-list-item .video-list-item .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-orange-pro video-list-item .video-list-item .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-pro video-list-item .video-list-item .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}video-list-item .video-list-item .meta{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;transition:color .15s;overflow:hidden;flex:1 1 0}.theme-default video-list-item .video-list-item .meta{color:rgba(255,255,255,.4)}.theme-purple-pro video-list-item .video-list-item .meta{color:rgba(255,255,255,.4)}.theme-green-pro video-list-item .video-list-item .meta{color:rgba(255,255,255,.4)}.theme-orange-pro video-list-item .video-list-item .meta{color:rgba(255,255,255,.4)}.theme-pro video-list-item .video-list-item .meta{color:rgba(255,255,255,.4)}video-list-item .video-list-item .meta .genres{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}video-list-item .video-list-item .meta .genres>*+*:before{content:", "}video-list-item .video-list-item .meta i{margin-right:10px}video-list-item .video-list-item .meta i svg{transition:opacity .15s;opacity:.5}video-list-item .video-list-item .meta i svg *{fill:#fff}video-list-item .video-list-item .tags{display:flex;align-items:center;gap:10px;flex:1 1 0}video-list-item .video-list-item .tags svg{opacity:.5;transition:opacity .15s}video-list-item .video-list-item .tags svg *{fill:var(--theme--text-highlight)}video-list-item .video-list-item .tags .tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;background-color:rgba(0,0,0,0);color:rgba(255,255,255,.5);border:1px solid rgba(255,255,255,.4);line-height:11px;text-transform:uppercase;font-size:11px;font-weight:bold;padding:1.5px 4px;transition:border-color .15s,color .15s}video-list-item .video-list-item:hover{background:rgba(var(--theme--background-accent--rgb), 0.9)}.theme-default video-list-item .video-list-item:hover .name{color:rgba(255,255,255,.8)}.theme-purple-pro video-list-item .video-list-item:hover .name{color:rgba(255,255,255,.8)}.theme-green-pro video-list-item .video-list-item:hover .name{color:rgba(255,255,255,.8)}.theme-orange-pro video-list-item .video-list-item:hover .name{color:rgba(255,255,255,.8)}.theme-pro video-list-item .video-list-item:hover .name{color:rgba(255,255,255,.8)}.theme-default video-list-item .video-list-item:hover .meta{color:rgba(255,255,255,.6)}.theme-purple-pro video-list-item .video-list-item:hover .meta{color:rgba(255,255,255,.6)}.theme-green-pro video-list-item .video-list-item:hover .meta{color:rgba(255,255,255,.6)}.theme-orange-pro video-list-item .video-list-item:hover .meta{color:rgba(255,255,255,.6)}.theme-pro video-list-item .video-list-item:hover .meta{color:rgba(255,255,255,.6)}video-list-item .video-list-item:hover .meta svg{opacity:.8}.theme-default video-list-item .video-list-item:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-purple-pro video-list-item .video-list-item:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-green-pro video-list-item .video-list-item:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-orange-pro video-list-item .video-list-item:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-pro video-list-item .video-list-item:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}video-list-item .video-list-item:hover .tags svg{opacity:.8}video-list-item .video-list-item:hover .tags .tag{border-color:var(--theme--text-secondary)}.theme-default video-list-item .video-list-item:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-purple-pro video-list-item .video-list-item:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-green-pro video-list-item .video-list-item:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-orange-pro video-list-item .video-list-item:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-pro video-list-item .video-list-item:hover .tags .tag{color:rgba(255,255,255,.6)}video-list-item .video-list-item favorite-button:not(:hover) button{background:none}video-list-item .video-list-item favorite-button:hover .favorite-icon{color:#fff !important}video-list-item .video-list-item:nth-child(1n+0) img{animation-delay:0.1s}video-list-item .video-list-item:nth-child(2n+0) img{animation-delay:0.2s}video-list-item .video-list-item:nth-child(3n+0) img{animation-delay:0.3s}video-list-item .video-list-item:nth-child(4n+0) img{animation-delay:0.4s}video-list-item .video-list-item:nth-child(5n+0) img{animation-delay:0.5s}video-list-item .video-list-item:nth-child(6n+0) img{animation-delay:0.6s}video-list-item .video-list-item:nth-child(7n+0) img{animation-delay:0.7s}video-list-item .video-list-item:nth-child(8n+0) img{animation-delay:0.8s}video-list-item .video-list-item:nth-child(9n+0) img{animation-delay:0.9s}video-list-item .video-list-item .thumbnail{display:block;border-radius:4px;overflow:hidden;width:40px;height:100%;aspect-ratio:16/9}video-list-item .video-list-item .thumbnail:not(.loaded){animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box}.video-list-item-placeholder{background:rgba(var(--theme--background-accent--rgb), 0.4);backdrop-filter:blur(25px);display:flex;width:100%;padding:8px;gap:8px;align-items:center;height:40px}.video-list-item-placeholder:first-of-type{border-radius:12px 12px 0 0}.video-list-item-placeholder:last-of-type{border-radius:0 0 12px 12px}.video-list-item-placeholder,.video-list-item-placeholder *{cursor:pointer}.video-list-item-placeholder .placeholder{flex:1 1 auto;height:40px}.video-list-item-placeholder .thumbnail{flex:0 0 auto}.video-list-item-placeholder .thumbnail img{display:inline-block;background:rgba(0,0,0,.1);width:40px;height:19px;border-radius:2.5px}.video-list-item-placeholder .name{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;transition:color .15s;min-width:240px;width:25%}.theme-default .video-list-item-placeholder .name{color:rgba(255,255,255,.6)}.theme-purple-pro .video-list-item-placeholder .name{color:rgba(255,255,255,.6)}.theme-green-pro .video-list-item-placeholder .name{color:rgba(255,255,255,.6)}.theme-orange-pro .video-list-item-placeholder .name{color:rgba(255,255,255,.6)}.theme-pro .video-list-item-placeholder .name{color:rgba(255,255,255,.6)}.video-list-item-placeholder .favorite-column{width:100px}.video-list-item-placeholder .favorite-column favorite-button .favorite-icon{transition:color .15s}.theme-default .video-list-item-placeholder .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-purple-pro .video-list-item-placeholder .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-green-pro .video-list-item-placeholder .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-orange-pro .video-list-item-placeholder .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-pro .video-list-item-placeholder .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.video-list-item-placeholder .meta{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;transition:color .15s;overflow:hidden;flex:1 1 0}.theme-default .video-list-item-placeholder .meta{color:rgba(255,255,255,.4)}.theme-purple-pro .video-list-item-placeholder .meta{color:rgba(255,255,255,.4)}.theme-green-pro .video-list-item-placeholder .meta{color:rgba(255,255,255,.4)}.theme-orange-pro .video-list-item-placeholder .meta{color:rgba(255,255,255,.4)}.theme-pro .video-list-item-placeholder .meta{color:rgba(255,255,255,.4)}.video-list-item-placeholder .meta .genres{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}.video-list-item-placeholder .meta .genres>*+*:before{content:", "}.video-list-item-placeholder .meta i{margin-right:10px}.video-list-item-placeholder .meta i svg{transition:opacity .15s;opacity:.5}.video-list-item-placeholder .meta i svg *{fill:#fff}.video-list-item-placeholder .tags{display:flex;align-items:center;gap:10px;flex:1 1 0}.video-list-item-placeholder .tags svg{opacity:.5;transition:opacity .15s}.video-list-item-placeholder .tags svg *{fill:var(--theme--text-highlight)}.video-list-item-placeholder .tags .tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;background-color:rgba(0,0,0,0);color:rgba(255,255,255,.5);border:1px solid rgba(255,255,255,.4);line-height:11px;text-transform:uppercase;font-size:11px;font-weight:bold;padding:1.5px 4px;transition:border-color .15s,color .15s}.video-list-item-placeholder:hover{background:rgba(var(--theme--background-accent--rgb), 0.9)}.theme-default .video-list-item-placeholder:hover .name{color:rgba(255,255,255,.8)}.theme-purple-pro .video-list-item-placeholder:hover .name{color:rgba(255,255,255,.8)}.theme-green-pro .video-list-item-placeholder:hover .name{color:rgba(255,255,255,.8)}.theme-orange-pro .video-list-item-placeholder:hover .name{color:rgba(255,255,255,.8)}.theme-pro .video-list-item-placeholder:hover .name{color:rgba(255,255,255,.8)}.theme-default .video-list-item-placeholder:hover .meta{color:rgba(255,255,255,.6)}.theme-purple-pro .video-list-item-placeholder:hover .meta{color:rgba(255,255,255,.6)}.theme-green-pro .video-list-item-placeholder:hover .meta{color:rgba(255,255,255,.6)}.theme-orange-pro .video-list-item-placeholder:hover .meta{color:rgba(255,255,255,.6)}.theme-pro .video-list-item-placeholder:hover .meta{color:rgba(255,255,255,.6)}.video-list-item-placeholder:hover .meta svg{opacity:.8}.theme-default .video-list-item-placeholder:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-purple-pro .video-list-item-placeholder:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-green-pro .video-list-item-placeholder:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-orange-pro .video-list-item-placeholder:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-pro .video-list-item-placeholder:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.video-list-item-placeholder:hover .tags svg{opacity:.8}.video-list-item-placeholder:hover .tags .tag{border-color:var(--theme--text-secondary)}.theme-default .video-list-item-placeholder:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-purple-pro .video-list-item-placeholder:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-green-pro .video-list-item-placeholder:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-orange-pro .video-list-item-placeholder:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-pro .video-list-item-placeholder:hover .tags .tag{color:rgba(255,255,255,.6)}.video-list-item-placeholder favorite-button:not(:hover) button{background:none}.video-list-item-placeholder favorite-button:hover .favorite-icon{color:#fff !important}',""]);const n=s},"my-videos/resources/video-player-dialog/video-player-dialog":(e,t,i)=>{i.r(t),i.d(t,{VideoPlayerDialog:()=>m,VideoPlayerDialogService:()=>v});var o=i(15215),r=i(16928),a=i("aurelia-dialog"),s=i("aurelia-framework"),n=i(19648),l=i(19072),d=i(17275),c=i(20057);let m=class{#h;#g;#a;constructor(e,t,i,o){this.controller=e,this.#h=t,this.#g=i,this.#a=o}activate(e){this.video=e.video,this.videosDir=this.#a.getVideosDirectory(),this.videoFilePath=r.join(this.videosDir,this.video.path),this.#h.info.devMode?this.videoFullPath=`http://localhost:8080/videos/${encodeURIComponent(this.video.path)}`:this.videoFullPath=`file:///${this.videoFilePath}`}get timestamp(){return this.video.createdAt.toLocaleString(this.#g.getEffectiveLocale(),{year:"numeric",month:"long",day:"numeric",hour:"numeric",minute:"2-digit"})}renameFile(e){const t=this.#a.renameVideoFile(this.video,e);return t.success?(t.updatedVideo&&(this.video=t.updatedVideo,this.videoFilePath=r.join(this.videosDir,this.video.path),this.#h.info.devMode?this.videoFullPath=`http://localhost:8080/videos/${encodeURIComponent(this.video.path)}`:this.videoFullPath=`file:///${this.videoFilePath}`),this.errorMessageKey="",!0):(this.errorMessageKey=t.errorMessageKey??"",!1)}onEditFileNameCancel(){this.errorMessageKey=""}openFileLocation(){this.#a.showVideoFileLocation(this.video)}};(0,o.Cg)([(0,s.computedFrom)("video.createdAt"),(0,o.Sn)("design:type",String),(0,o.Sn)("design:paramtypes",[])],m.prototype,"timestamp",null),m=(0,o.Cg)([s.autoinject,(0,o.Sn)("design:paramtypes",[a.DialogController,l.s,c.F2,n.Z])],m);let v=class extends d.C{constructor(){super(...arguments),this.viewModelClass="my-videos/resources/video-player-dialog/video-player-dialog"}};v=(0,o.Cg)([(0,s.autoinject)()],v)},"my-videos/resources/video-player-dialog/video-player-dialog.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});const o='<template> <require from="./video-player-dialog.scss"></require> <require from="../../../shared/resources/elements/close-button"></require> <ux-dialog class="video-player-dialog"> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <ux-dialog-body> <div class="video-container"> <wm-video src.bind="videoFullPath" type="video/mp4"></wm-video> </div> </ux-dialog-body> <ux-dialog-header> <div class="video-title-container"> <wm-edit-input value.bind="video.filename" on-save.call="renameFile($event)" on-cancel.call="onEditFileNameCancel()"></wm-edit-input> <div if.bind="errorMessageKey" class="error-message">${errorMessageKey | i18n}</div> </div> <div class="metadata-row"> <div class="data-col"> <span class="label">${\'my_videos.game\' | i18n}</span> <span class="value">${video.titleName}</span> </div> <div class="data-col"> <span class="label">${\'my_videos.recorded\' | i18n}</span> <span class="value">${timestamp}</span> </div> </div> <button click.delegate="openFileLocation()" class="open-file-location-btn"> ${\'my_videos.open_file_location\' | i18n} </button> </ux-dialog-header> </ux-dialog> </template> '},"my-videos/resources/video-player-dialog/video-player-dialog.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var o=i(31601),r=i.n(o),a=i(76314),s=i.n(a)()(r());s.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.video-player-dialog{width:80vw;max-width:1200px;padding:0 !important;border:none !important}.video-player-dialog close-button{z-index:1}.video-player-dialog ux-dialog-header .dialog-header-content{padding:16px 32px 20px 32px;display:flex;flex-direction:row;justify-content:space-between;align-items:center;height:80px}.video-player-dialog ux-dialog-header .dialog-header-content .video-title-container{width:50%}.video-player-dialog ux-dialog-header .dialog-header-content .video-title-container wm-edit-input .display-value-text{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:20px;line-height:24px;letter-spacing:-0.75px;margin:0;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.video-player-dialog ux-dialog-header .dialog-header-content .video-title-container .error-message{color:var(--color--alert)}.video-player-dialog ux-dialog-header .dialog-header-content .metadata-row{display:flex;flex-direction:row;width:40%;justify-content:space-between;padding-left:30px;padding-right:36px}.video-player-dialog ux-dialog-header .dialog-header-content .metadata-row .data-col{display:flex;flex-direction:column}.video-player-dialog ux-dialog-header .dialog-header-content .metadata-row .data-col .label{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px}.video-player-dialog ux-dialog-header .dialog-header-content .metadata-row .data-col .value{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.25px;color:#fff}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn,.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn{border:1px solid #fff}}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn>*:first-child{padding-left:0}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn>*:last-child{padding-right:0}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn svg *{fill:CanvasText}}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn svg{opacity:1}}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn img{height:50%}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn:disabled{opacity:.3}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn:disabled,.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn:not(:disabled):hover svg{opacity:1}}.video-player-dialog ux-dialog-header .dialog-header-content .open-file-location-btn:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.video-player-dialog ux-dialog-body{padding:0;border-top-left-radius:20px;border-top-right-radius:20px;overflow:hidden}.video-player-dialog ux-dialog-body .video-container{width:100%;background-color:#000}',""]);const n=s},"native/support/index":(e,t,i)=>{i.r(t),i.d(t,{configure:()=>f});var o=i(19072),r=i(83974),a=i(10351),s=i(38777);class n extends a.xx{constructor(){super(...arguments),this.#u=new s._M}#u;subscribe(e,t){return this.#u.subscribe(e,t)}onElevated(e){return this.#u.subscribe("elevated",e)}async run(e,t){switch(await(0,s.Wn)(10),e){case"QueryRegistry":return"value"===t.type?null:{};case"ResolvePath":return t;case"GetGpuDevices":case"GetProcesses":return[];case"StatFile":case"GetUacIcon":case"GetInstalledPackageId":return null;case"GrantFilePermission":return;default:throw new a.Ik("Command not supported.")}}async initialize(){}dispose(){this.#u.dispose()}}var l,d=i(15215),c=i("aurelia-framework"),m=i(96610),v=i("services/bugsnag/index");!function(e){e[e.Success=1]="Success",e[e.Error=2]="Error",e[e.Event=4]="Event",e[e.RequiresElevation=5]="RequiresElevation"}(l||(l={}));const p=(0,m.getLogger)("native"),h=new Set(["Hotkey","Hotkey.Pressed"]),g=new Set(["GetProcesses","QueryRegistry","StatFile","GetInstalledPackageId"]);let u=class extends a.xx{#f;#u;#b;#y;#x;#w;#k;constructor(e){super(),this.#u=new s._M,this.#b=new Map,this.#y=1,this.#f=e}subscribe(e,t){return this.#u.subscribe(e,t)}onElevated(e){return this.subscribe("Elevated",e)}dispose(){this.#u&&this.#u.dispose()}async run(e,t){await this.initialize();const i=this.#y++;g.has(e)||(p.debug(`${e}:${i} executed.`,t),(0,v.setMetadata)("app","lastNativeCommand",e));const o=new Promise(((t,o)=>{this.#b.set(i,{command:e,resolve:t,reject:o})}));return await this.#S({id:i,command:e,data:t}),await o}#S(e){const t=JSON.stringify(e),i=Buffer.byteLength(t),o=Buffer.alloc(i+4);return o.writeUInt32LE(i,0),o.write(t,4),new Promise(((e,t)=>{this.#w?.write(o,(i=>{i?t(i):e()}))}))}async initialize(){this.#x&&this.#w||(this.#k||(this.#k=this.#_().finally((()=>this.#k=null))),await this.#k)}async#_(){[this.#x,this.#w]=await this.#f.start(),this.#x.on("close",(()=>this.#F())),this.#w?.on("close",(()=>this.#F())),this.#x.on("data",(e=>this.#C(e)))}#F(){if(this.#x&&!this.#x.destroyed)try{this.#x.destroy()}catch{}if(this.#x=null,this.#w&&!this.#w.destroyed)try{this.#w.destroy()}catch{}this.#w=null}#C(e){let t=0;for(;t+4<e.length;){const i=e.readInt32BE(t);t+=4;const o=e.toString("utf8",t,t+i);let r;t+=i;try{r=JSON.parse(o)}catch{continue}r.status===l.Event?this.#P(r):this.#$(r)}}#P(e){const t=e.data;h.has(t.event)||p.debug(`${t.event} raised.`,t.data),this.#u.publish(t.event,t.data)}#$(e){const t=this.#b.get(e.id);if(!t)return;this.#b.delete(e.id);const i=!g.has(t.command);return e.status===l.Success?(i&&p.debug(`${t.command}:${e.id} completed.`,e.data),void t.resolve(e.data)):e.status===l.Error?(i&&p.debug(`${t.command}:${e.id} failed.`),void t.reject(new a.Ik(`${t.command} task failed.`,e.data))):e.status===l.RequiresElevation?(i&&p.error(`${t.command}:${e.id} elevation denied.`,e.data),void t.reject(new a.Vn)):void t.reject(new a.Ik(`Unknown task status ${e.status}.`))}};function f(e,t){"win32"===e.container.get(o.s).info.osPlatform?(e.container.registerInstance(r.c,new r.c(t.exe)),e.container.registerAlias(u,a.xx)):e.container.registerAlias(n,a.xx)}u=(0,d.Cg)([(0,c.autoinject)(),(0,d.Sn)("design:paramtypes",[r.c])],u)}}]);
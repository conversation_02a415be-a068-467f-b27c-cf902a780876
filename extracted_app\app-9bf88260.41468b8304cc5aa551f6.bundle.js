"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7694],{21795:(e,i,t)=>{t.d(i,{Dz:()=>p,HE:()=>o,Ij:()=>b,Pq:()=>m,SA:()=>s,U0:()=>c,VY:()=>I,WC:()=>v,XM:()=>u,Yb:()=>d,Zv:()=>f,_1:()=>a,a8:()=>h,aV:()=>x,cv:()=>y,dY:()=>g,jP:()=>l,kK:()=>n,mN:()=>k,pA:()=>w,t$:()=>r});class a{constructor(e){this.gameId=e}}class n{constructor(e){this.id=e}}class o{constructor(e,i){this.gameId=e,this.followType=i}}class r{constructor(e){this.gameId=e}}class s{constructor(e,i){this.objectiveId=e,this.completed=i}}class l{}class d{}class g{constructor(e,i,t,a,n){this.location=e,this.titleId=i,this.gameId=t,this.trainerId=a,this.searchResult=n}}class c{constructor(e,i,t,a){this.gameId=e,this.uri=i,this.free=t,this.location=a}}class h{constructor(e){this.gameId=e}}class m{constructor(e,i){this.location=e,this.terms=i}}class p{constructor(e,i){this.trigger=e,this.gameId=i}}class b{constructor(e,i,t){this.trigger=e,this.gameId=i,this.enabled=t}}class u{constructor(e){this.gameId=e}}class f{constructor(e,i){this.id=e,this.nonInteraction=i}}class y{constructor(e,i){this.id=e,this.trigger=i}}class w{constructor(e){this.trainerInfo=e}}class v{constructor(e,i,t){this.name=e,this.params=i,this.dispatch=t}}class x{constructor(e,i,t,a){this.gameId=e,this.gameVersion=i,this.trainerId=t,this.result=a}}class k{constructor(e,i,t,a){this.gameId=e,this.cheatId=i,this.type=t,this.source=a}}class I{constructor(e,i,t){this.gameId=e,this.trigger=i,this.trainer_instruction_read=t}}},60692:(e,i,t)=>{t.d(i,{K:()=>n,n:()=>a});const a={E39:"2023_12_foreground_mod_time_limit",MapsInHeader:"2024_06_maps_in_header",Onboarding:"2024_06_onboarding",MakeMobileAppFree:"2024_10_make_mobile_app_free",ProShowcaseModal_old:"2025_01_new_pro_modal",ProShowcaseModal:"2025_02_new_pro_modal",NewProSelectPlanPagesRound2:"2025_03_new_pro_page_select_plan_round_2"},n=[a.MapsInHeader,a.Onboarding,a.MakeMobileAppFree,a.ProShowcaseModal,a.NewProSelectPlanPagesRound2]},73509:(e,i,t)=>{t.d(i,{NT:()=>s,dc:()=>r,ey:()=>o});var a=t(96890),n=t.n(a);async function o(e,i,t,a,o){try{const r=await n().read(e),s=r.bitmap.width*i,l=r.bitmap.height*t,d=r.bitmap.width*a,g=r.bitmap.height*o,c=r.crop(s,l,d,g);return await c.getBufferAsync(n().MIME_PNG)}catch{return e}}async function r(e,i=!1,t=128){try{const a=await n().read(e);if(!i){const e=Array(256).fill(0);a.scan(0,0,a.getWidth(),a.getHeight(),((i,t,n)=>{const o=a.bitmap.data[n],r=a.bitmap.data[n+1],s=a.bitmap.data[n+2],l=Math.round(.7*o+.2*r+.1*s);a.bitmap.data[n]=l,a.bitmap.data[n+1]=l,a.bitmap.data[n+2]=l,e[l]++}));const i=.01;let n=0,o=0;for(let t=0;t<256;t++)if(o+=e[t]||0,o>a.bitmap.width*a.bitmap.height*i){n=t;break}let r=0,s=0;for(let t=255;t>=0;t--)if(s+=e[t]||0,s>a.bitmap.width*a.bitmap.height*i){r=t;break}let l=1/0;const d=r-n,g=n+Math.floor(.65*d),c=n+.9*d;for(let i=g;i<=c;i++)e[i]<l&&(l=e[i],t=i)}return a.scan(0,0,a.getWidth(),a.getHeight(),((e,i,n)=>{const o=a.bitmap.data[n]>t?0:255;a.bitmap.data[n]=o,a.bitmap.data[n+1]=o,a.bitmap.data[n+2]=o})),a.getBufferAsync(n().MIME_PNG)}catch{return e}}async function s(e){try{const i=await n().read(e);return i.scan(0,0,i.getWidth(),i.getHeight(),((e,t,a)=>{const n=i.bitmap.data[a],o=Math.max(0,Math.min(255,1.5*(n-128)+128));i.bitmap.data[a]=o,i.bitmap.data[a+1]=o,i.bitmap.data[a+2]=o})),await i.getBufferAsync(n().MIME_PNG)}catch{return e}}},"game-help/cyberpunk-mission-help":(e,i,t)=>{t.r(i),t.d(i,{CyberpunkMissionHelp:()=>y,TITLE_SLUG:()=>u});var a=t(15215),n=t("aurelia-event-aggregator"),o=t("aurelia-framework"),r=t(27058),s=t(34995),l=t(62914),d=t(29879),g=t(83802),c=t(92465),h=t(20057),m=t(54995);const p=["The Nomad","The Streetkid","The Corpo-Rat","Practice Makes Perfect","The Rescue","The Ripperdoc","The Ride","The Information","The Pickup","The Heist","Love Like Fire","Playing for Time","Automatic Love","The Space in Between","Disasterpiece","Double Life","M'ap Tann Pèlen","I Walk the Line","Transmission","Never Fade Away","Ghost Town","Lightning Breaks","Life During Wartime","Down on the Street","Gimme Danger","Play It Safe","Search and Destroy","Tapeworm","Nocturne Op55N1","Last Caress","Totalimmortal","For Whom the Bell Tolls","Knockin' on Heaven's Door","We Gotta Live Together","Forward to Death","Belly of the Beast","(Don't Fear) The Reaper","Changes","Where is My Mind?","Path of Glory","All Along the Watchtower","New Dawn Fades","Dressed to Kill","Epistrophy: Badlands","Following the River","I'll Fly Away","KOLD MIRAGE","Little China","Afterlife","Meetings Along The Edge","Murk Man Returns Again Once More Forever","Pyramid Song","Laguna Bend ruins","Queen of the Highway","Nomad Camp","Raffen Shiv","Riders on the Storm","Rocky Ridge","Sierra Sonora","Ingalls' farm","The Beast in Me: Badlands","Jackson Plains","These Boots Are Made for Walkin","With a Little Help from My Friends","Corp-Bud train station","Job Name","Boat Drinks","Highway 101","Bullets","Imagine","The Beast in Me: City Center","The Show Must Go On","Beat on the Brat: The Glen","Don't Lose Your Mind","Epistrophy","Epistrophy: The Glen","Epistrophy: Wellsprings","Heroes","Jackie's Garage","I Fought the Law","Vista del Rey","Japantown","Charter Hill","Red Queen's Race","Peralezes' Apartment","Animals","New Person, Same Old Mistakes","Only Pain","Raymond Chandler Evening","Vista del Rey","Small Man, Big Mouth","The Hunt","Addicted To Chaos","Balls to the Wall","Beat on the Brat: Pacifica","Corpo of the Month","Dazed and Confused","Dirty Second Hands","Epistrophy: Coastview","Go Your Own Way","Hi Ho Silver Lining","Love Rollercoaster","Money For Nothing","Longshore Stacks","Moving Heat","No Easy Way Out","Digimmortal","Dream Gig","One Way or Another","Push It to the Limit","Run This Town","Shot by Both Sides","Dogtown","Cynosure Site D","Tomorrow Never Knows","Voodoo Treasure","A Day In The Life","Vance Noren","A Like Supreme","Baby Let Me Take You","Corpo Plaza","Akuma","Beat on the Brat: Arroyo","6th Street members","Beat on the Brat: Rancho Coronado","Epistrophy: Rancho Coronado","Ezekiel Saw the Wheel","Gun Music","Scavengers","Over the Edge","Rancho Coronado","Dam Viewpoint","Rebel! Rebel!","Sex On Wheels","Sinnerman","Space Oddity","Jackson Plains","Stadium Love","The Beast In Me","The Beast in Me: Santo Domingo","Rancho Coronado","The Highwayman","There Is A Light That Never Goes Out","A Cool Metal Fire","Cassius Ryder's Clinic","Empathy","Riot","Sunset Motel","Beat on the Brat","Razor Hugh","Beat on the Brat: Kabuki","Big in Japan","Blistering Love","Both Sides, Now","Burning Desire / Night Moves","Northside","Career Opportunities","Chippin' In","Northside","Ebunike","Northern Oilfields","Jeremiah Grayson","Epistrophy: Northside","Every Breath You Take","Fool on the Hill","Fortunate Son","Full Disclosure","Turret","Happy Together","Barry Lewis's apartment","Columbarium","Houses In Motion: Makeovers","Human Nature","Delamain HQ","I Don't Wanna Hear It","I'm in Love with My Car","Machine Gun","Paid in Full","Psycho Killer","Sacrum Profanum / Losing My Religion","Abandoned Warehouse","Second Conflict","Denny's mansion","Shoot To Thrill","Spellbound","Charter Hill","Talkin' 'bout a Revolution","The Gift","The Gun","The Prophet's Song","Corporates","This Corrosion","Tune Up","Venus in Furs","Violence","Little China","Riot","War Pigs","Coin Operated Boy","The Glen","Dream On","Northside","Japantown","Corpo Plaza","Epistrophy: North Oak","Ex-Factor","Holdin' On","I Can See Clearly Now","Off the Leash","Pisces","Marcus Ichida","Jun Azegami","Hiromi Sato","Maiko Maeda","Poem Of The Atoms","Send in the Clowns","Spray Paint","Stairway To Heaven","Sweet Dreams","The Ballad of Buck Ravers","The Beast in Me: The Big Race","The Gig","They Won't Go When I Go","Gig: Big Pete's Got Big Problems","Gig: Dancing on a Minefield","Gig: Flying Drugs","Gig: Goodbye, Night City","Gig: MIA","Gig: No Fixers","Gig: Radar Love","Gig: Sparring Partner","Gig: Trevor's Last Ride","Gig: A Lack of Empathy","Gig: An Inconvenient Killer","Gig: Guinea Pigs","Gig: Serial Suicide","Gig: The Frolics of Councilwoman Cole","Gig: Bring Me the Head of Gustavo Orta","Gig: Corpo Plaza","Gig: Eye for an Eye","Gig: Fifth Column","Gig: Going Up or Down?","Gig: Hot Merchandise","Gig: Jeopardy","Gig: The Glen","Gig: Life's Work","Gig: Old Friends","Gig: On a Tight Leash","Gig: Psychofan","Gig: Sr. Ladrillo's Private Collection","Gig: The Lord Giveth and Taketh Away","Gig: Dogtown Saints","Gig: Heaviest of Hearts","Gig: Prototype in the Scraper","Gig: Roads to Redemption","Gig: Spy in the Jungle","Gig: Talent Academy","Gig: The Man Who Killed Jason Foreman","Gig: Treating Symptoms","Gig: Two Wrongs Makes Us Right","Gig: Waiting for Dodger","Gig: Breaking News","Gig: Cuckoo's Nest","Gig: Desperate Measures","Gig: Error 404","Gig: Family Matters","Gig: For My Son","Gig: Going-away Party","Gig: Hacking the Hacker","Gig: Nasty Hangover","Gig: Race to the Top","Gig: Serious Side Effects","Gig: Severance Package","Gig: The Union Strikes Back","Gig: Backs Against the Wall","Gig: Bloodsport","Gig: Catch a Tyger's Toe","Gig: Concrete Cage Trap","Gig: Dirty Biz","Gig: Fixer, Merc, Soldier, Spy","Gig: Flight of the Cheetah","Gig: Freedom of the Press","Gig: Hippocratic Oath","Gig: Last Login","Gig: Lousy Kleppers","Gig: Many Ways to Skin a Cat","Gig: Monster Hunt","Gig: Occupational Hazard","Gig: Playing for Keeps","Rite of Passage","Gig: Scrolls before Swine","Gig: Small Man, Big Evil","Gig: The Heisenberg Principle","Gig: Troublesome Neighbors","Gig: Welcome to America, Comrade","Gig: Woman of La Mancha","Gig: A Shrine Defiled","Gig: Family Heirloom","Gig: Getting Warmer...","Gig: Greed Never Pays","Gig: Olive Branch","Gig: Tyger and Vulture","Gig: Until Death Do Us Part","Gig: Wakako's Favorite","Gig: We Have Your Wife","Cyberpsycho Sighting: Bloody Ritual","Cyberpsycho Sighting: Demons of War","Cyberpsycho Sighting: Discount Doc","Cyberpsycho Sighting: House on a Hill","Cyberpsycho Sighting: Letter of the Law","Cyberpsycho Sighting: Lex Talionis","Cyberpsycho Sighting: Lt. Mower","Cyberpsycho Sighting: On Deaf Ears","Cyberpsycho Sighting: Phantom of Night City","Cyberpsycho Sighting: Seaside Cafe","Cyberpsycho Sighting: Second Chances","Cyberpsycho Sighting: Six Feet Under","Cyberpsycho Sighting: Smoke on the Water","Cyberpsycho Sighting: The Wasteland","Cyberpsycho Sighting: Ticket to the Major Leagues","Cyberpsycho Sighting: Under the Bridge","Cyberpsycho Sighting: Where the Bodies Hit the Floor"];var b=t(73509);const u="cyberpunk-2077",f="Cyberpunk 2077";let y=class{#e;#i;#t;#a;#n;#o;#r;#s;#l;#d;#g;constructor(e,i,t,a,n){this.live=!1,this.#e=!1,this.#i=0,this.#r=e,this.#s=i,this.#l=t,this.#d=a,this.#g=n}attached(){this.#a=this.#s.onTrainerActivated((e=>{e.getMetadata(g.vO).info.titleId===this.titleId&&this.#c()})),this.#n=this.#s.onTrainerEnded((e=>{e.getMetadata(g.vO).info.titleId===this.titleId&&this.#h()})),this.#s.trainer?.isActive&&this.#s.trainer.getMetadata(g.vO).info.titleId===this.titleId&&this.#c(),this.#d.event("cyberpunk_mission_help_open",{},l.Io)}detached(){this.#h(),this.#a?.dispose(),this.#n?.dispose()}#c(){this.#h(),this.live=!0,this.#t=(0,c.SO)((()=>this.#m()),2e3)}async#h(){this.#t?.dispose(),this.live=!1,await(this.#o?.terminate()),this.#o=null,this.#e=!1,this.#i=0,this.mission=void 0,this.#r.cleanupScreenshot(this.overrideWindowTitle||f)}async#m(){const e=await this.#r.captureScreenshot(f);e&&this.detectMission(e)}async#p(){if(this.#o)return this.#o;const e=await(0,s.createWorker)("eng");return this.#o=e,this.#o}async detectMission(e){if(!this.#e){this.#e=!0,this.#i+=1;try{let i=await(0,b.ey)(e,.7,.05,.3,.5),t=await this.#b(i);t||(i=await(0,b.dc)(i),t=await this.#b(i)),t&&(t!==this.mission&&(this.mission=t,this.#d.event("cyberpunk_mission_help_mission_detected",{missionName:t,detectionAttempts:this.#i},l.Io)),this.#i=0),i=null}catch{}this.#e=!1}}async#b(e){const i=await this.#p(),t=(await i.recognize(e)).data.lines.map((e=>e.words.filter((e=>e.confidence>70)).map((e=>e.text)).join(" "))),a=[];for(let e=0;e<t.length-1;e++){const i=t[e]+" "+t[e+1];a.push(i)}const n=(0,r.A)(p),o=[...t,...a].map((e=>{const i=n.get(e);return{confidence:i?i[0][0]:null,missionName:i?i[0][1]:null}})).filter((e=>e.confidence&&e.confidence>.6)).sort(((e,i)=>(i.confidence??0)-(e.confidence??0)));if(o.length>0){const e=o[0];return p.find((i=>i===e.missionName))}}askForHelp(){this.#d.event("cyberpunk_mission_help_cta_click",{missionName:this.mission??""},l.Io),this.#l.publish("ask-mission-help",this.#g.getValue("cyberpunk_mission_help.mission_help_prompt",{mission:this.mission??""}))}get titleId(){if(this.catalog)return Object.values(this.catalog.titles).find((e=>e.slug===u))?.id}};(0,a.Cg)([(0,o.computedFrom)("catalog"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],y.prototype,"titleId",null),y=(0,a.Cg)([(0,o.autoinject)(),(0,m.m6)({selectors:{catalog:(0,m.$t)((e=>e.catalog)),overrideWindowTitle:(0,m.$t)((e=>e.settings?.overrideScreenshotWindowTitle))}}),(0,a.Sn)("design:paramtypes",[d.m,g.jR,n.EventAggregator,l.j0,h.F2])],y)},"game-help/cyberpunk-mission-help.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template> <require from="./cyberpunk-mission-help.scss"></require> <div class="cyberpunk-mission-help"> <h2> <span class="icon"> <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M10.75 2V3.5428C13.7405 3.88638 16.1136 6.2595 16.4572 9.25H18V10.75H16.4572C16.1136 13.7405 13.7405 16.1136 10.75 16.4572V18H9.25V16.4572C6.2595 16.1136 3.88638 13.7405 3.5428 10.75H2V9.25H3.5428C3.88638 6.2595 6.2595 3.88638 9.25 3.5428V2H10.75ZM9.25 7V5.05588C7.08901 5.38094 5.38094 7.08901 5.05588 9.25H7V10.75H5.05588C5.38094 12.911 7.08901 14.6191 9.25 14.9441V13H10.75V14.9441C12.911 14.6191 14.6191 12.911 14.9441 10.75H13V9.25H14.9441C14.6191 7.08901 12.911 5.38094 10.75 5.05588V7H9.25Z" fill="white"/> </svg> </span> <span class="label">${\'cyberpunk_mission_help.mission_helper\' | i18n}</span> <span class="badge" if.bind="live">${\'cyberpunk_mission_help.live\' | i18n}</span> </h2> <div class="content"> <template if.bind="live"> <div class="mission"> <div if.bind="mission"> <div class="name"> <span class="icon"> <svg width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg"> <g filter="url(#filter0_d_5777_24266)"> <path fill-rule="evenodd" clip-rule="evenodd" d="M19 26H17L10 19V17L17 10H19L26 17V19L19 26ZM17 13V19H19V13H17ZM17 21V23H19V21H17Z" fill="#FFD740"/> </g> <defs> <filter id="filter0_d_5777_24266" x="0" y="0" width="36" height="36" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="5"/> <fecomposite in2="hardAlpha" operator="out"/> <fecolormatrix type="matrix" values="0 0 0 0 0.652555 0 0 0 0 0.0981212 0 0 0 0 0.0225166 0 0 0 1 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5777_24266"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5777_24266" result="shape"/> </filter> </defs> </svg> </span> <span>${mission}</span> </div> </div> <div else> <div>${\'cyberpunk_mission_help.detecting_mission\' | i18n}</div> </div> </div> <button class="cta" click.delegate="askForHelp()" disabled.bind="!mission"> ${\'cyberpunk_mission_help.get_help\' | i18n} </button> </template> <template else> <p class="message">${\'cyberpunk_mission_help.launch_message\' | i18n}</p> <button class="cta waiting">${\'cyberpunk_mission_help.offline\' | i18n}</button> </template> </div> </div> </template> '},"game-help/cyberpunk-mission-help.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>p});var a=t(31601),n=t.n(a),o=t(76314),r=t.n(o),s=t(4417),l=t.n(s),d=new URL(t(83959),t.b),g=new URL(t(13358),t.b),c=r()(n());c.push([e.id,"@import url(https://fonts.googleapis.com/css2?family=Inter:wght@500&family=Rajdhani:wght@300;400;500;600;700&display=swap);"]);var h=l()(d),m=l()(g);c.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${h}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}title-sidebar{--sidebar--content-top: calc(var(--constant--appHeaderHeight) + 24px);display:block;position:relative}title-sidebar.collapsed{width:0 !important}title-sidebar.collapsed :not(.collapse-button){display:none}title-sidebar.collapsed .collapse-button{position:absolute}title-sidebar.active{width:400px}@media(min-width: 1360px){title-sidebar{width:400px}}title-sidebar .title-sidebar-wrapper{--sidebar--base-max-height: calc( 100vh - var(--sidebar--content-top) - var(--ad-popup--safe-height) - var(--promotion-banner-height, 0) + 12px );display:flex;flex-direction:column;z-index:999;position:fixed;right:24px;top:44px;width:inherit;max-height:var(--sidebar--base-max-height);height:100%;overflow-x:visible !important}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:window-inactive,title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}@media(forced-colors: active){body:not(.override-contrast-mode) title-sidebar .title-sidebar-wrapper{border:1px solid #fff}}title-sidebar .scrollable-container{overflow-y:auto;overflow-x:clip;gap:20px;display:flex;flex-direction:column}title-sidebar .scrollable-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}title-sidebar .scrollable-container::-webkit-scrollbar-thumb:window-inactive,title-sidebar .scrollable-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}title-sidebar .scrollable-container::-webkit-scrollbar-thumb:window-inactive:hover,title-sidebar .scrollable-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}@media(forced-colors: active){body:not(.override-contrast-mode) title-sidebar .scrollable-container{border:1px solid #fff}}title-sidebar .action-buttons{position:relative;display:flex;gap:8px;width:100%;justify-content:space-between;overflow:visible}title-sidebar .action-buttons>*{margin-bottom:20px;flex:1}.cyberpunk-mission-help{display:block;width:100%;outline:none;border:0;border-radius:16px;overflow:hidden;--yellow: #ffd740;--yellow--rgb: 255, 215, 64;--rust: #87271a;--rust--rgb: 135, 39, 26;position:relative;background-color:rgba(255,255,255,.025);background-position:top right;background-size:auto 100%;background-repeat:no-repeat;backdrop-filter:blur(50px);padding:16px;background-image:url(${m})}.cyberpunk-mission-help:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);border-radius:16px;z-index:1;mix-blend-mode:overlay;pointer-events:none}.cyberpunk-mission-help:before{border-color:rgba(255,255,255,.1)}.cyberpunk-mission-help h2{height:24px;display:inline-flex;align-items:center;margin:0 0 12px}.cyberpunk-mission-help h2 .icon{flex:0 0 auto;width:20px;height:20px;display:flex;align-items:center;justify-content:center;margin-right:8px}.cyberpunk-mission-help h2 .label{font-size:14px;font-weight:700;line-height:24px;color:rgba(255,255,255,.8);margin-right:8px}.cyberpunk-mission-help h2 .badge{font-weight:700;background:var(--yellow);display:inline-flex;height:16px;padding:4px;align-items:center;font-size:10px;color:var(--rust);text-transform:uppercase;border-radius:4px}.cyberpunk-mission-help .content{display:flex;align-items:flex-end}.cyberpunk-mission-help .message{font-size:14px;line-height:21px;color:rgba(255,255,255,.5);margin:0;padding:0}.cyberpunk-mission-help .mission{font-weight:600;min-height:60px;display:flex;justify-content:center;align-items:flex-start;flex-direction:column;background:rgba(var(--yellow--rgb), 0.05);border-radius:10px;padding:10px;flex:1 1 auto;font-family:Rajdhani,Inter,sans-serif;font-size:16px;color:var(--yellow)}.cyberpunk-mission-help .mission>div .name{text-transform:uppercase}.cyberpunk-mission-help .mission>div .icon{display:inline-block;margin:-12px -5px;vertical-align:text-bottom}.cyberpunk-mission-help p{font-size:14px;line-height:20px;color:rgba(255,255,255,.5);margin:0 24px 0 0;padding:0}.cyberpunk-mission-help .cta{font-weight:700;flex:0 0 auto;margin-left:16px;font-size:16px;color:var(--rust);display:inline-flex;flex-direction:row;justify-content:center;align-items:center;padding:2px 16px;gap:10px;height:36px;background:var(--yellow);box-shadow:0px 0px 16px rgba(var(--rust--rgb), 0.5);border-radius:100px;border:0;outline:none;transition:brightness .15s}.cyberpunk-mission-help .cta.waiting{background-color:#64625c;color:#fff;pointer-events:none}.cyberpunk-mission-help .cta:disabled{background-color:#65582c;color:rgba(255,255,255,.5);pointer-events:none}.cyberpunk-mission-help .cta:hover{filter:brightness(1.1)}`,""]);const p=c},"game-help/elden-ring-boss-guide":(e,i,t)=>{t.r(i),t.d(i,{EldenRingBossGuide:()=>w,TITLE_SLUG:()=>f});var a=t(15215),n=t("aurelia-framework"),o=t(62914),r=t(92380),s=t(29879),l=t(83802),d=t("shared/api/value-converters"),g=t(92465),c=t(54995);const h=[{name:"Abductor Virgins",imageId:908868},{name:"Adan, Thief of Fire",imageId:955610},{name:"Alabaster Lord",imageId:957152},{name:"Alecto, Black Knife Ringleader",imageId:934923},{name:"Ancestor Spirit",imageId:917287},{name:"Ancient Dragon Lansseax",imageId:904779},{name:"Ancient Hero of Zamor",imageId:904303},{name:"Astel, Naturalborn of the Void",imageId:934823},{name:"Astel, Stars of Darkness",imageId:947103},{name:"Battlemage Hugues",imageId:null},{name:"Beast Clergyman",imageId:944211},{name:"Beastman of Farum Azula",imageId:955239},{name:"Bell Bearing Hunter",imageId:913244},{name:"Black Blade Kindred",imageId:897072},{name:"Black Knife Assassin",imageId:955132},{name:"Bloodhound Knight",imageId:955626},{name:"Bloodhound Knight Darriwil",imageId:952338},{name:"Bols, Carian Knight",imageId:956023},{name:"Borealis the Freezing Fog",imageId:944396},{name:"Cemetery Shade",imageId:893312},{name:"Cleanrot Knight",imageId:null},{name:"Commander Niall",imageId:945178},{name:"Commander O'Neil",imageId:895567},{name:"Crucible Knight",imageId:913437},{name:"Crucible Knight Ordovis",imageId:913437},{name:"Crucible Knight Siluria",imageId:918709},{name:"Crystalian",imageId:957103},{name:"Crystalian Ringblade",imageId:902324},{name:"Crystalian Spear",imageId:902324},{name:"Crystalian Staff",imageId:956952},{name:"Death Rite Bird",imageId:894863},{name:"Deathbird",imageId:913259},{name:"Decaying Ekzykes",imageId:893284},{name:"Demi-Human Chief",imageId:952552},{name:"Demi-Human Queen Gilika",imageId:904969},{name:"Demi-Human Queen Maggie",imageId:908855},{name:"Demi-Human Queen Margot",imageId:909057},{name:"Draconic Tree Sentinel",imageId:913299},{name:"Dragonkin Soldier",imageId:934253},{name:"Dragonkin Soldier of Nokstella",imageId:934048},{name:"Dragonlord Placidusax",imageId:944200},{name:"Elden Beast",imageId:944256},{name:"Elder Dragon Greyoll",imageId:896810},{name:"Elemer of the Briar",imageId:909814},{name:"Erdtree Avatar",imageId:941031},{name:"Erdtree Burial Watchdog",imageId:955603},{name:"Esgar, Priest of Blood",imageId:944243},{name:"Fallingstar Beast",imageId:910515},{name:"Fell Twins",imageId:936354},{name:"Fia's Champions",imageId:918742},{name:"Fire Giant",imageId:943453},{name:"Flying Dragon Agheel",imageId:955199},{name:"Flying Dragon Greyll",imageId:897064},{name:"Frenzied Duelist",imageId:895721},{name:"Full-Grown Fallingstar Beast",imageId:909071},{name:"Glintstone Dragon Adula",imageId:957080},{name:"Glintstone Dragon Smarag",imageId:956027},{name:"God-Devouring Serpent",imageId:944273},{name:"Godefroy The Grafted",imageId:901535},{name:"Godfrey, First Elden Lord",imageId:936316},{name:"Godrick the Grafted",imageId:955486},{name:"Godskin Apostle",imageId:904179},{name:"Godskin Duo",imageId:944103},{name:"Godskin Noble",imageId:944422},{name:"Grafted Scion",imageId:909037},{name:"Grave Warden Duelist",imageId:913411},{name:"Great Wyrm Theodorix",imageId:946857},{name:"Guardian Golem",imageId:955071},{name:"Hoarah Loux",imageId:936316},{name:"Kindred of Rot",imageId:908366},{name:"Leonine Misbegotten",imageId:951316},{name:"Lichdragon Fortissax",imageId:945502},{name:"Loretta, Knight of the Haligtree",imageId:949369},{name:"Mad Pumpkin Head",imageId:952588},{name:"Mad Pumpkin Heads",imageId:952748},{name:"Magma Wyrm",imageId:952837},{name:"Magma Wyrm Makar",imageId:898957},{name:"Malenia, Blade of Miquella",imageId:949463},{name:"Maliketh, the Black Blade",imageId:944211},{name:"Margit, the Fell Omen",imageId:955290},{name:"Mimic Tear",imageId:916323},{name:"Miranda the Blighted Bloom",imageId:951503},{name:"Misbegotten Crusader",imageId:946853},{name:"Misbegotten Warrior",imageId:901027},{name:"Mohg, Lord of Blood",imageId:950591},{name:"Mohg, the Omen",imageId:935858},{name:"Morgott, the Omen King",imageId:936334},{name:"Necromancer Garris",imageId:906559},{name:"Night's Cavalry",imageId:901595},{name:"Nox Monk",imageId:895740},{name:"Nox Swordstress",imageId:895740},{name:"Omenkiller",imageId:918982},{name:"Onyx Lord",imageId:912336},{name:"Patches",imageId:null},{name:"Perfumer Tricia",imageId:908287},{name:"Putrid Avatar",imageId:952777},{name:"Putrid Crystalian Trio",imageId:897554},{name:"Putrid Grave Warden Duelist",imageId:947037},{name:"Putrid Tree Spirit",imageId:901110},{name:"Radagon of the Golden Order",imageId:944256},{name:"Red Wolf of Radagon",imageId:956094},{name:"Red Wolf of the Champion",imageId:908897},{name:"Regal Ancestor Spirit",imageId:917250},{name:"Rennala, Queen of the Full Moon",imageId:956961},{name:"Royal Knight Loretta",imageId:957199},{name:"Royal Revenant",imageId:934868},{name:"Runebear",imageId:951657},{name:"Rykard, Lord of Blasphemy",imageId:944273},{name:"Sanguine Noble",imageId:904704},{name:"Scaly Misbegotten",imageId:951540},{name:"Sir Gideon Ofnir, the All-Knowing",imageId:944250},{name:"Soldier of Godrick",imageId:null},{name:"Spirit-Caller Snail",imageId:955853},{name:"Spiritcaller Snail",imageId:944422},{name:"Starscourge Radahn",imageId:900547},{name:"Stonedigger Troll",imageId:909113},{name:"Stray Mimic Tear",imageId:946419},{name:"Tibia Mariner",imageId:906053},{name:"Tree Sentinel",imageId:955205},{name:"Ulcerated Tree Spirit",imageId:952561},{name:"Valiant Gargoyle",imageId:917329},{name:"Vyke, Knight of the Roundtable",imageId:941271},{name:"Wormface",imageId:903649}];var m=t(73509),p=t(27058),b=t(34995);class u{#o;#e;#u;async#p(){return this.#u||(this.#u=(0,b.createWorker)("eng")),this.#u}async detect(e,i){if(!this.#e){this.#e=!0;try{const t=await this.#p(),a=(await t.recognize(e)).data.lines.map((e=>e.words.filter((e=>e.confidence>70)).map((e=>e.text)).join(" "))),n=[];for(let e=0;e<a.length-1;e++){const i=a[e]+" "+a[e+1];n.push(i)}const o=(0,p.A)(i),r=[...a,...n].map((e=>{const i=o.get(e);return{confidence:i?i[0][0]:null,text:i?i[0][1]:null}})).filter((e=>e.confidence&&e.confidence>.6)).sort(((e,i)=>(i.confidence??0)-(e.confidence??0)));if(r.length>0){const e=r[0],t=i.find((i=>i===e.text));return this.#e=!1,t}}catch{}this.#e=!1}}dispose(){this.#o?.terminate(),this.#o=null}}const f="elden-ring",y=h.map((e=>e.name));let w=class{#f;#t;#y;#a;#n;#w;#r;#s;#d;#v;#x;constructor(e,i,t,a,n){this.opening=!1,this.live=!1,this.#f=0,this.#r=e,this.#s=i,this.#d=t,this.#v=a,this.#x=n}attached(){this.#a=this.#s.onTrainerActivated((()=>{this.#k()&&this.#c()})),this.#n=this.#s.onTrainerEnded((()=>{this.#h()})),this.#k()&&this.#c(),this.#d.event("elden_ring_boss_guide_open",{},o.Io)}detached(){this.#h(),this.#a?.dispose(),this.#n?.dispose(),this.#w?.dispose()}#k(){if(!this.#s.trainer?.isActive)return!1;const e=this.#s.trainer?.getMetadata(l.vO);return e&&e?.info.titleId===this.titleId}#c(){this.featureFlag?.bossDetectionEnabled&&(this.#h(),this.#w=new u,this.#t=(0,g.SO)((()=>this.#m()),2e3),this.live=!0)}async#h(){this.#t?.dispose(),this.#w?.dispose(),this.#y?.dispose(),this.bossName="",this.#f=0,this.live=!1}async#m(){const e=await this.#r.captureScreenshot(this.overrideWindowTitle||"ELDEN RING™");e&&this.#I(e)}async#I(e){this.#f+=1;let i=await(0,m.ey)(e,0,.7,1,.2),t=await(0,m.NT)(i),a=await(this.#w?.detect(t,y));if(!a){let e=await(0,m.dc)(t);a=await(this.#w?.detect(e,y)),e=null}a&&(a!==this.bossName&&(this.bossName=a,this.#d.event("elden_ring_boss_guide_boss_detected",{boss:a,detectionAttempts:this.#f},o.Io)),this.#f=0,this.#y?.dispose(),this.#y=(0,g.Ix)((()=>this.bossName=""),3e5)),i=null,t=null}get titleId(){if(this.catalog)return Object.values(this.catalog.titles).find((e=>e.slug===f))?.id}async openGuide(){if(!this.opening){this.opening=!0;try{await this.#v.open(this.bossName)}catch{}this.opening=!1,this.#d.event("elden_ring_boss_guide_cta_click",{boss:this.bossName},o.Io)}}featureFlagChanged(){this.featureFlag?.bossDetectionEnabled||this.#h()}get bossImage(){const e=h.find((e=>e.name===this.bossName));if(e?.imageId)return this.#x.toView(`/game_map_pin/${e.imageId}/1920.webp`)}};(0,a.Cg)([(0,n.computedFrom)("catalog"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],w.prototype,"titleId",null),(0,a.Cg)([(0,n.computedFrom)("bossName"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],w.prototype,"bossImage",null),w=(0,a.Cg)([(0,n.autoinject)(),(0,c.m6)({selectors:{catalog:(0,c.$t)((e=>e.catalog)),featureFlag:(0,c.$t)((e=>e.catalog?.features?.elden_ring_boss_guide)),overrideWindowTitle:(0,c.$t)((e=>e.settings?.overrideScreenshotWindowTitle))}}),(0,a.Sn)("design:paramtypes",[s.m,l.jR,o.j0,r.$,d.CdnValueConverter])],w)},"game-help/elden-ring-boss-guide.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template> <require from="./elden-ring-boss-guide.scss"></require> <button class="elden-ring-boss-guide" click.delegate="openGuide()" disabled.bind="opening"> <div class="boss-image" if.bind="bossImage"> <img src.bind="bossImage"> </div> <h2> <span class="icon"></span> <template if.bind="bossName">${bossName}</template> <template else>${\'elden_ring_boss_guide.elden_ring_boss_guide\' | i18n}</template> <span class="live-icon" if.bind="live"></span> </h2> <div class="bottom"> <div class="text" if.bind="bossName"> <div innerhtml.bind="\'elden_ring_boss_guide.step_by_step_$boss_guide\' | i18n:{boss: bossName} | markdown"></div> <div class="video-callout"> <span class="icon"></span> ${\'elden_ring_boss_guide.includes_video_guide\' | i18n} </div> </div> <div class="text" else innerhtml.bind="\'elden_ring_boss_guide.all_the_info_you_need\' | i18n | markdown"></div> <span class="cta">${\'elden_ring_boss_guide.view_guide\' | i18n}</span> </div> </button> </template> '},"game-help/elden-ring-boss-guide.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>u});var a=t(31601),n=t.n(a),o=t(76314),r=t.n(o),s=t(4417),l=t.n(s),d=new URL(t(83959),t.b),g=new URL(t(1512),t.b),c=new URL(t(10750),t.b),h=r()(n()),m=l()(d),p=l()(g),b=l()(c);h.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,.elden-ring-boss-guide h2 .icon,.elden-ring-boss-guide h2 .live-icon,.elden-ring-boss-guide .video-callout .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}title-sidebar{--sidebar--content-top: calc(var(--constant--appHeaderHeight) + 24px);display:block;position:relative}title-sidebar.collapsed{width:0 !important}title-sidebar.collapsed :not(.collapse-button){display:none}title-sidebar.collapsed .collapse-button{position:absolute}title-sidebar.active{width:400px}@media(min-width: 1360px){title-sidebar{width:400px}}title-sidebar .title-sidebar-wrapper{--sidebar--base-max-height: calc( 100vh - var(--sidebar--content-top) - var(--ad-popup--safe-height) - var(--promotion-banner-height, 0) + 12px );display:flex;flex-direction:column;z-index:999;position:fixed;right:24px;top:44px;width:inherit;max-height:var(--sidebar--base-max-height);height:100%;overflow-x:visible !important}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:window-inactive,title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}@media(forced-colors: active){body:not(.override-contrast-mode) title-sidebar .title-sidebar-wrapper{border:1px solid #fff}}title-sidebar .scrollable-container{overflow-y:auto;overflow-x:clip;gap:20px;display:flex;flex-direction:column}title-sidebar .scrollable-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}title-sidebar .scrollable-container::-webkit-scrollbar-thumb:window-inactive,title-sidebar .scrollable-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}title-sidebar .scrollable-container::-webkit-scrollbar-thumb:window-inactive:hover,title-sidebar .scrollable-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}@media(forced-colors: active){body:not(.override-contrast-mode) title-sidebar .scrollable-container{border:1px solid #fff}}title-sidebar .action-buttons{position:relative;display:flex;gap:8px;width:100%;justify-content:space-between;overflow:visible}title-sidebar .action-buttons>*{margin-bottom:20px;flex:1}.elden-ring-boss-guide{display:block;width:100%;outline:none;border:0;border-radius:16px;overflow:hidden;padding:16px;text-align:left;background:var(--theme--background) url(${p}) center right/cover no-repeat;position:relative;z-index:0}.elden-ring-boss-guide:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);border-radius:16px;z-index:1;mix-blend-mode:overlay;pointer-events:none}.elden-ring-boss-guide .boss-image{position:absolute;left:0;top:0;width:100%;height:100%;z-index:0;pointer-events:none;object-fit:cover;scale:2}.elden-ring-boss-guide .boss-image img{width:100%;height:100%;position:absolute;left:15%;top:5%}.elden-ring-boss-guide .boss-image:after{content:"";position:absolute;left:0;top:0;width:264px;height:100%;background:linear-gradient(270deg, rgba(20, 23, 25, 0) 0%, rgba(20, 23, 25, 0.75) 28.21%, rgba(20, 23, 25, 0.9) 47.66%, #141719 100%)}.elden-ring-boss-guide h2{font-weight:800;font-size:14px;font-weight:700;line-height:24px;color:#fff;margin:0 0 9px;padding:0;display:inline-flex;align-items:center;position:relative;z-index:1}.elden-ring-boss-guide h2 .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;margin-right:12px;font-size:16px}.elden-ring-boss-guide h2 .icon:before{font-family:inherit;content:"swords"}.elden-ring-boss-guide h2 .live-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;margin-left:4px;color:#e8503f}.elden-ring-boss-guide h2 .live-icon:before{font-family:inherit;content:"sensors"}.elden-ring-boss-guide .bottom{display:flex;align-items:flex-end;position:relative;z-index:1}.elden-ring-boss-guide .text{font-size:14px;line-height:21px;color:rgba(255,255,255,.6);padding:0;display:block;flex:1 1 auto;margin:0 36px 4px 0}.elden-ring-boss-guide .video-callout{display:inline-flex;align-items:center;font-size:10px;line-height:16px;color:rgba(255,255,255,.8);text-transform:uppercase;margin-top:6px}.elden-ring-boss-guide .video-callout .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;font-size:16px;margin-right:4px}.elden-ring-boss-guide .video-callout .icon:before{font-family:inherit;content:"play_arrow"}.elden-ring-boss-guide .cta{font-weight:700;flex:0 0 auto;background:#e8503f;padding:2px 16px;font-size:16px;line-height:32px;box-shadow:0px 0px 16px rgba(39,30,40,.5);color:#fff;position:relative;border-radius:100px}.elden-ring-boss-guide .cta:before{content:"";display:block;width:14px;height:14px;position:absolute;top:5px;right:8px;background-image:url(${b})}.elden-ring-boss-guide:hover .cta{filter:brightness(1.1)}`,""]);const u=h}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7573],{"resources/elements/account-email":(t,e,o)=>{o.r(e),o.d(e,{AccountEmail:()=>b});var i=o(15215),n=o("aurelia-framework"),a=o(20770),r=o(68663),s=o(62914),l=o(67064),c=o(20057),d=o(54995),p=o(70236),u=o(48881);let b=class{#t;#e;#o;#i;#n;constructor(t,e,o,i){this.focused=!1,this.#t=!1,this.#e=t,this.#o=e,this.#i=o,this.#n=i}bind(){this.accountChanged()}accountChanged(){this.value=this.account.email}#a(){return"string"==typeof this.value?this.value.replace(/[。｡︒]/g,"."):null}async submit(){if(this.#t)throw new Error("Email change is already in progress.");this.#t=!0;try{return await this.#r()}catch(t){const e=t.data&&t.data.entries?t.data.entries[0].message:t.toString();this.#i.toast({content:c.F2.literal(e),type:"alert"})}finally{this.#t=!1}}async#r(){const t=this.#a(),e=await this.emailInput.validateWithFeedback(),o=!!this.account.email;return!(!e||!t||(await this.#e.dispatch(u.Ui,await this.#o.changeAccountEmail(t)),this.#n.event("email_collect",{location:this.location,firstEmail:o},s.Io),0))}get existingEmail(){return(0,p.Lt)(this.account?.flags,2)&&this.account?.email?this.account?.email:null}};(0,i.Cg)([n.observable,(0,i.Sn)("design:type",Object)],b.prototype,"value",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],b.prototype,"placeholder",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.twoWay}),(0,i.Sn)("design:type",String)],b.prototype,"status",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],b.prototype,"location",void 0),(0,i.Cg)([(0,n.computedFrom)("account.flags","account.email","value"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],b.prototype,"existingEmail",null),b=(0,i.Cg)([(0,d.m6)({selectors:{account:(0,d.$t)((t=>t.account))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[a.il,r.x,l.l,s.j0])],b)},"resources/elements/account-email.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template> <require from="./account-email.scss"></require> <require from="./email-input"></require> <email-input value.bind="value" placeholder.bind="placeholder" status.bind="status" existing-email.bind="existingEmail" view-model.ref="emailInput"></email-input> </template> '},"resources/elements/account-email.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"account-email email-input{display:block}",""]);const s=r},"resources/elements/alert-icon.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>r});var i=o(14385),n=o.n(i),a=new URL(o(2877),o.b);const r='<template> <require from="./alert-icon.scss"></require> <i> <inline-svg src="'+n()(a)+'"></inline-svg> </i> </template> '},"resources/elements/alert-icon.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"alert-icon{width:16px;height:16px;display:inline-flex;border-radius:50%;background:rgba(var(--color--alert--rgb), 0.3);align-items:center;justify-content:center;flex:0 0 auto}alert-icon i{display:inline-block}alert-icon i svg *{fill:var(--color--alert)}alert-icon.outline{background:rgba(0,0,0,0);border:1px solid var(--color--alert)}",""]);const s=r},"resources/elements/beta-tag.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i="<template> <require from=\"./beta-tag.scss\"></require> ${'beta_tag.beta' | i18n} </template> "},"resources/elements/beta-tag.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"beta-tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;--beta-tag-bg-color: var(--theme--highlight);--beta-tag-color: #111;background-color:var(--beta-tag-bg-color);color:var(--beta-tag-color);min-width:initial}",""]);const s=r},"resources/elements/boost-balance-button":(t,e,o)=>{o.r(e),o.d(e,{BoostBalanceButton:()=>s});var i=o(15215),n=o("aurelia-framework"),a=o(51977),r=o(54995);let s=class{get trialActive(){return!!this.account.subscription&&!!this.account.subscription.trialEndsAt&&(0,a.A)(Date.now(),new Date(this.account.subscription.trialEndsAt))}};(0,i.Cg)([(0,n.computedFrom)("account.subscription.trialEndsAt"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],s.prototype,"trialActive",null),s=(0,i.Cg)([(0,r.m6)({selectors:{account:(0,r.$t)((t=>t.account))}}),(0,n.autoinject)()],s)},"resources/elements/boost-balance-button.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template class="${account.subscription ? \'disabled\' : \'\'}${account.boosts || account.subscription ? \' balance-btn\' : \' get-boost-btn\'}" pro-cta="trigger: boost_balance_button; disabled.bind: account.subscription; feature: boosts;" disabled.bind="account.boosts"> <require from="./boost-balance-button.scss"></require> <i>double_arrow</i> <span if.bind="account.boosts || account.subscription" class="balance">${account.boosts || 0 | i18nNumber}&nbsp;</span> <span class="label"> <template if.bind="trialActive && account.boosts === 0">${\'boost_balance_button.boosts_available_after_trial_period\' | i18n}</template> <template else>${account.subscription ? \'unavailable_game.boosts\' : \'unavailable_game.get_boosts\' | i18n}</template> </span> </template> '},"resources/elements/boost-balance-button.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(n()),p=l()(c);d.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,boost-balance-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}boost-balance-button.balance-btn{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;background:rgba(255,255,255,.1);display:flex;align-items:center;border-radius:56px;padding:10px 16px}.theme-default boost-balance-button.balance-btn{color:#fff}.theme-purple-pro boost-balance-button.balance-btn{color:#fff}.theme-green-pro boost-balance-button.balance-btn{color:#fff}.theme-orange-pro boost-balance-button.balance-btn{color:#fff}.theme-pro boost-balance-button.balance-btn{color:#fff}boost-balance-button.balance-btn .balance{padding-right:0}boost-balance-button.get-boost-btn{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;height:40px;box-shadow:none;line-height:24px;font-weight:700;color:#000;border-radius:56px;padding:10px 16px;background:#fff}boost-balance-button.get-boost-btn,boost-balance-button.get-boost-btn *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) boost-balance-button.get-boost-btn{border:1px solid #fff}}boost-balance-button.get-boost-btn>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}boost-balance-button.get-boost-btn>*:first-child{padding-left:0}boost-balance-button.get-boost-btn>*:last-child{padding-right:0}boost-balance-button.get-boost-btn svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) boost-balance-button.get-boost-btn svg *{fill:CanvasText}}boost-balance-button.get-boost-btn svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) boost-balance-button.get-boost-btn svg{opacity:1}}boost-balance-button.get-boost-btn img{height:50%}boost-balance-button.get-boost-btn:disabled{opacity:.3}boost-balance-button.get-boost-btn:disabled,boost-balance-button.get-boost-btn:disabled *{cursor:default;pointer-events:none}@media(hover: hover){boost-balance-button.get-boost-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}boost-balance-button.get-boost-btn:not(:disabled):hover svg{opacity:1}}boost-balance-button.get-boost-btn:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}boost-balance-button.get-boost-btn:hover{background:rgba(255,255,255,.8) !important;color:rgba(0,0,0,.8) !important}boost-balance-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;margin-right:8px;font-size:18px}`,""]);const u=d},"resources/elements/boost-button":(t,e,o)=>{o.r(e),o.d(e,{BoostButton:()=>g});var i=o(15215),n=o("aurelia-event-aggregator"),a=o("aurelia-framework"),r=o(20770),s=o(51977),l=o(68663),c=o(50654),d=o(67064),p=o(21795),u=o(17724),b=o(54995),h=o(48881);let g=class{#o;#i;#e;#s;#l;constructor(t,e,o,i,n){this.boosting=!1,this.#o=t,this.#i=e,this.#e=o,this.#s=i,this.#l=n}async boost(){if(this.trialActive&&0===this.account.boosts)return void this.#i.toast({content:"boost_button.boosts_available_after_trial_period",type:"alert"});if(0===this.account.boosts)return void(this.account.subscription&&this.#i.toast({content:"boost_button.no_boosts",type:"alert"}));if(this.boosting)return;const t=this.gameId;this.boosting=!0;try{await this.#c(t)}catch(e){let o=e;if(o instanceof u.hD&&409===o.status)try{return await this.#e.dispatch(h.Ui,await this.#o.getUserAccount()),void await this.#c(t)}catch(t){o=t}if(this.#i.toast({content:"boost_button.an_error_occurred",type:"alert"}),!(o instanceof u.o3||o instanceof TypeError))throw o}finally{this.boosting=!1}}async#c(t){const e=await this.#o.boostGame(t,this.account.boosts);await this.#e.dispatch(h.Ui,e),await this.#l.refresh(),this.#s.publish(new p._1(t));const o=this.#i.toast({type:"boost",content:"boost_button.you_boosted_$game",i18nParams:{game:this.catalog.titles[this.catalog.games[t].titleId].name},actions:[{label:"boost_button.dont_notify_me",onclick:async()=>{this.#i.remove(o),await this.#l.unfollowGames([t])&&this.#i.toast({type:"ok",content:"boost_button.notification_preferences_updated"})}}]})}get trialActive(){return!!this.account.subscription&&!!this.account.subscription.trialEndsAt&&(0,s.A)(Date.now(),new Date(this.account.subscription.trialEndsAt))}};(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],g.prototype,"gameId",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Boolean)],g.prototype,"showCount",void 0),(0,i.Cg)([(0,a.computedFrom)("account.subscription.trialEndsAt"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],g.prototype,"trialActive",null),g=(0,i.Cg)([(0,b.m6)({selectors:{account:(0,b.$t)((t=>t.account)),catalog:(0,b.$t)((t=>t.catalog))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[l.x,d.l,r.il,n.EventAggregator,c.O])],g)},"resources/elements/boost-button.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>r});var i=o(14385),n=o.n(i),a=new URL(o(16280),o.b);const r='<template> <require from="./boost-button.scss"></require> <template if.bind="showCount && account.boostedGames[gameId]"> <div class="count">${account.boostedGames[gameId]}</div> <i class="checkmark"><inline-svg src="'+n()(a)+'"></inline-svg></i> </template> <button click.trigger="boost()" pro-cta="trigger: boost_button; disabled.bind: account.boosts !== 0 || account.subscription; feature: boosts;" disabled.bind="boosting"> <i>double_arrow</i> </button> </template> '},"resources/elements/boost-button.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(n()),p=l()(c);d.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,boost-button button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}boost-button .count{font-size:14px;line-height:21px;font-weight:600;color:rgba(255,255,255,.6)}boost-button .checkmark svg{margin-left:5px;margin-right:9px}boost-button .checkmark svg *{fill:var(--theme--highlight)}boost-button button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);display:flex;align-items:center}boost-button button,boost-button button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) boost-button button{border:1px solid #fff}}boost-button button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}boost-button button>*:first-child{padding-left:0}boost-button button>*:last-child{padding-right:0}boost-button button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) boost-button button svg *{fill:CanvasText}}boost-button button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) boost-button button svg{opacity:1}}boost-button button img{height:50%}boost-button button:disabled{opacity:.3}boost-button button:disabled,boost-button button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){boost-button button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}boost-button button:not(:disabled):hover svg{opacity:1}}boost-button button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){boost-button button:not(:disabled):hover{background:rgba(255,255,255,.3)}}boost-button button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;width:20px;height:20px}boost-button button:after{font-size:14px;line-height:21px;font-weight:900;content:"+1";white-space:nowrap;overflow:hidden;display:inline-block;text-align:right;width:0;opacity:0;transform:translate(10px, 0);transition:width .15s,opacity .15s,transform .15s}boost-button button:hover:not(:disabled):after{width:23px;opacity:1;transform:translate(0, 0)}`,""]);const u=d},"resources/elements/breadcrumbs":(t,e,o)=>{o.r(e),o.d(e,{Breadcrumbs:()=>r});var i=o(15215),n=o("aurelia-framework"),a=o(18776);let r=class{#d;constructor(t){this.#d=t}attached(){this.previousRoute=this.#d.currentInstruction.queryParams.previousRoute,this.backButtonLabel=`breadcrumbs.back_to_${(this.previousRoute??"").replace("-","_")}`}back(){this.#d.navigateBack()}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Array)],r.prototype,"items",void 0),r=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[a.Ix])],r)},"resources/elements/breadcrumbs.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template bindable="items"> <require from="./breadcrumbs.scss"></require> <template if.bind="previousRoute"> <a class="item" href="#" click.delegate="back()"> <span class="caret back"></span> <span>${backButtonLabel | i18n}</span> </a> </template> <template else> <template repeat.for="item of items"> <span class="item" if.bind="!item[1]">${item[0] | i18n}</span> <a if.bind="item[1]" class="item" route-href="route.bind: item[1]; params.bind: item[2]">${item[0] | i18n}</a> <span class="caret"></span> </template> </template> </template> '},"resources/elements/breadcrumbs.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(n()),p=l()(c);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,breadcrumbs .caret,breadcrumbs .caret.back{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}breadcrumbs{display:flex;align-items:center;gap:2px}.theme-default breadcrumbs{color:rgba(255,255,255,.6)}.theme-purple-pro breadcrumbs{color:rgba(255,255,255,.6)}.theme-green-pro breadcrumbs{color:rgba(255,255,255,.6)}.theme-orange-pro breadcrumbs{color:rgba(255,255,255,.6)}.theme-pro breadcrumbs{color:rgba(255,255,255,.6)}breadcrumbs .caret{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:inherit;font-size:14px}breadcrumbs .caret:before{font-family:inherit;content:"chevron_right"}breadcrumbs .caret.back{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:inherit;font-size:14px}breadcrumbs .caret.back:before{font-family:inherit;content:"arrow_back"}breadcrumbs .item{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;display:flex;align-items:center;gap:2px;transition:color .2s;text-decoration:none}.theme-default breadcrumbs .item{color:rgba(255,255,255,.6)}.theme-purple-pro breadcrumbs .item{color:rgba(255,255,255,.6)}.theme-green-pro breadcrumbs .item{color:rgba(255,255,255,.6)}.theme-orange-pro breadcrumbs .item{color:rgba(255,255,255,.6)}.theme-pro breadcrumbs .item{color:rgba(255,255,255,.6)}breadcrumbs a.item{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;display:flex;align-items:center;gap:2px;transition:color .2s;text-decoration:none}.theme-default breadcrumbs a.item{color:rgba(255,255,255,.6)}.theme-purple-pro breadcrumbs a.item{color:rgba(255,255,255,.6)}.theme-green-pro breadcrumbs a.item{color:rgba(255,255,255,.6)}.theme-orange-pro breadcrumbs a.item{color:rgba(255,255,255,.6)}.theme-pro breadcrumbs a.item{color:rgba(255,255,255,.6)}breadcrumbs a.item,breadcrumbs a.item *{cursor:pointer}.theme-default breadcrumbs a.item:hover{color:rgba(255,255,255,.8)}.theme-purple-pro breadcrumbs a.item:hover{color:rgba(255,255,255,.8)}.theme-green-pro breadcrumbs a.item:hover{color:rgba(255,255,255,.8)}.theme-orange-pro breadcrumbs a.item:hover{color:rgba(255,255,255,.8)}.theme-pro breadcrumbs a.item:hover{color:rgba(255,255,255,.8)}breadcrumbs:empty{height:20px}`,""]);const u=d},"resources/elements/coaching-tip":(t,e,o)=>{o.r(e),o.d(e,{CoachingTip:()=>p});var i=o(15215),n=o("aurelia-event-aggregator"),a=o("aurelia-framework"),r=o(20770),s=o(21795),l=o(92465),c=o(54995),d=o(48881);let p=class{#p;#u;#e;#b;constructor(t,e){this.position="right",this.popupPosition="right",this.closeButtonPosition="right",this.autoShowInitially=!1,this.detach=!1,this.waitForOtherCoachingTips=!1,this.visible=!1,this.#e=t,this.#b=e}attached(){this.#h(),this.#u=new l.Vd([this.#b.subscribe(s.cv,(()=>this.#h()))])}detached(){this.#u.dispose(),this.hide()}show(t=!1){this.visible=!0,this.toggleHighlight(!0),this.#e.dispatch(d.kE,this.id,(new Date).toISOString()),this.#b.publish(new s.Zv(this.id,t))}hide(t){clearTimeout(this.#p),this.visible=!1,this.toggleHighlight(!1),t&&this.#b.publish(new s.cv(this.id,t))}toggleHighlight(t){this.highlightSelector&&document.querySelectorAll(this.highlightSelector).forEach((e=>e.classList.toggle("coaching-tip-highlight",t)))}toggle(t){t&&(t.preventDefault(),t.stopPropagation()),this.visible?this.hide("click-outside"):this.show()}get closeIfClickOutsideVisible(){return this.visible}set closeIfClickOutsideVisible(t){t?this.show():this.hide("click-outside")}#g(){return!!document.querySelector("coaching-tip.show")}#h(){clearTimeout(this.#p),this.autoShowInitially&&!this.coachingTipHistory[this.id]?.tipShownAt&&(this.#p=setTimeout((()=>{this.waitForOtherCoachingTips&&this.#g()||this.show(!0)}),2e3))}};(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Boolean)],p.prototype,"hideHotspot",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],p.prototype,"position",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],p.prototype,"popupPosition",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],p.prototype,"closeButtonPosition",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],p.prototype,"id",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Boolean)],p.prototype,"autoShowInitially",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Boolean)],p.prototype,"detach",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],p.prototype,"highlightSelector",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Boolean)],p.prototype,"waitForOtherCoachingTips",void 0),(0,i.Cg)([(0,a.computedFrom)("visible"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[Boolean])],p.prototype,"closeIfClickOutsideVisible",null),p=(0,i.Cg)([(0,c.m6)({selectors:{coachingTipHistory:(0,c.$t)((t=>t.coachingTipHistory))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il,n.EventAggregator])],p)},"resources/elements/coaching-tip.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template class="position-${position} popup-${popupPosition} ${visible ? \'show\' : \'\'} close-button-${closeButtonPosition} ${hideHotspot ? \'hide-hotspot\' : \'\'}"> <require from="./coaching-tip.scss"></require> <require from="../../shared/resources/custom-attributes/close-if-click-outside"></require> <require from="../../shared/resources/elements/close-button"></require> <div class="wrapper"> <div class="hotspot" click.delegate="toggle($event)" tabindex="0"> <span class="ring"></span> <span class="ring"></span> <span class="ring"></span> <span class="ring"></span> </div> <div class="popup" close-if-click-outside="value.two-way: closeIfClickOutsideVisible; ignore-selector: coaching-tip;"> <close-button click.delegate="hide(\'close-button\')" tabindex="0"></close-button> <template part="content" replaceable></template> <div slot="content"> <slot></slot> </div> </div> </div> </template> '},"resources/elements/coaching-tip.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}body.reduce-motion coaching-tip .hotspot .ring:not(:first-child){opacity:0 !important}coaching-tip{display:inline-block;position:relative}coaching-tip .wrapper{position:relative;width:32px}coaching-tip .hotspot{--hotspot--color: var(--color--accent);position:relative;width:32px;height:32px}coaching-tip .hotspot,coaching-tip .hotspot *{cursor:pointer}coaching-tip .hotspot .ring{position:absolute;left:50%;top:50%;border-radius:50%;transition:background-color .15s,border-color .15s}coaching-tip .hotspot .ring:nth-child(1){width:37.5%;height:37.5%;margin-left:-18.75%;margin-top:-18.75%;background:var(--hotspot--color)}coaching-tip .hotspot .ring:nth-child(2){width:62.5%;height:62.5%;margin-left:-31.25%;margin-top:-31.25%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out 0s infinite}coaching-tip .hotspot .ring:nth-child(3){width:81.25%;height:81.25%;margin-left:-40.625%;margin-top:-40.625%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out .1s infinite}coaching-tip .hotspot .ring:nth-child(4){width:100%;height:100%;margin-left:-50%;margin-top:-50%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out .2s infinite}coaching-tip .popup{width:325px;padding:20px 25px 25px;border-radius:10px;background:var(--theme--secondary-background);border:1px solid rgba(255,255,255,.05);opacity:0;visibility:hidden;transition:visibility 0s .2s;z-index:1;box-shadow:0px 0px 5px rgba(17,17,17,.5)}coaching-tip .popup h5{font-weight:600;font-size:16px;line-height:25px;font-weight:700;margin:0 0 5px;color:#fff}coaching-tip .popup p{font-size:14px;line-height:22px;font-weight:500;color:rgba(255,255,255,.6);margin:0}coaching-tip .popup p a{font-weight:500;color:var(--theme--highlight);text-decoration:underline}coaching-tip .popup p a:hover{color:#fff}coaching-tip .popup p strong{font-weight:500;color:#fff}coaching-tip .popup button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}coaching-tip .popup button,coaching-tip .popup button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button{border:1px solid #fff}}coaching-tip .popup button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}coaching-tip .popup button>*:first-child{padding-left:0}coaching-tip .popup button>*:last-child{padding-right:0}coaching-tip .popup button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button svg *{fill:CanvasText}}coaching-tip .popup button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button svg{opacity:1}}coaching-tip .popup button img{height:50%}coaching-tip .popup button:disabled{opacity:.3}coaching-tip .popup button:disabled,coaching-tip .popup button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){coaching-tip .popup button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}coaching-tip .popup button:not(:disabled):hover svg{opacity:1}}coaching-tip .popup button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){coaching-tip .popup button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}coaching-tip .popup button:not(:disabled):active{background-color:var(--theme--highlight)}coaching-tip .popup hr{border:0;border-top:1px solid rgba(255,255,255,.2);margin:15px 0}coaching-tip close-button{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)) !important;display:inline-flex;width:26px;height:26px;box-shadow:none;padding:0;border-radius:50%;border:0;align-items:center;justify-content:center;transition:background-color .15s;position:absolute;right:-12.5px;top:-12.5px}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip close-button{border:1px solid #fff}}coaching-tip close-button svg{opacity:1}@media(hover: hover){coaching-tip close-button:hover{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.25)) !important}}coaching-tip.show .hotspot{--hotspot--color: var(--color--brand-blue)}coaching-tip.show .popup{animation:dialog-pop .2s ease-in-out forwards;visibility:visible;transition-delay:0s}coaching-tip.position-relative-right{margin-left:16px}coaching-tip.position-right{position:absolute;left:100%;top:50%;margin-left:16px;margin-top:-16px}coaching-tip.position-left{position:absolute;right:100%;top:50%;margin-right:16px;margin-top:-16px}coaching-tip.popup-right .popup{position:absolute;left:100%;top:0;margin-left:12px}coaching-tip.popup-left .popup{position:absolute;right:100%;top:0;margin-right:12px}coaching-tip.popup-bottom-left .popup{position:absolute;right:0px;top:100%;margin-top:12px}coaching-tip.hide-hotspot .wrapper{width:0}coaching-tip.hide-hotspot .hotspot{display:none}coaching-tip.hide-hotspot .popup{margin:0}coaching-tip.close-button-left close-button{right:initial;left:-12px}body.disable-looping-animation coaching-tip .hotspot .ring:not(:first-child){animation-fill-mode:forwards !important}@keyframes coaching-tip-hotspot{0%{opacity:0}25%{opacity:1}50%,100%{opacity:0}}.coaching-tip-highlight{animation:coaching-tip-highlight .15s linear forwards}@keyframes coaching-tip-highlight{to{box-shadow:0 0 0 1px var(--theme--highlight),0 0 40px 0 rgba(var(--theme--highlight--rgb), 0.3)}}",""]);const s=r},"resources/elements/email-input":(t,e,o)=>{o.r(e),o.d(e,{EmailInput:()=>l});var i=o(15215),n=o("aurelia-framework"),a=o(16953),r=o(37294),s=o("shared/dialogs/basic-dialog");let l=class{#m;#f;#v;constructor(t,e){this.status="none",this.autoFocus=!1,this.large=!1,this.focused=!1,this.#v=!a.A.debug,this.#m=t,this.#f=e}attached(){this.autoFocus&&this.inputEl?.focus()}bind(){this.value=this.existingEmail||""}valueChanged(){this.#y()}existingEmailChanged(){this.value=this.existingEmail,this.#y()}#a(){return"string"==typeof this.value?this.value.replace(/[。｡︒]/g,"."):null}async#y(){const t=this.#a();if(null===t||0===t.length)return this.status="invalid",{status:r.q.Invalid};if(this.existingEmail===this.value)return this.status="none",null;this.status="validating";const e=await this.#m.validate(t);return t!==this.#a()?null:(this.status=e.status,e)}async validateWithFeedback(){const t=await this.#y();if(null===t||null===t.status)return!1;if(t.status===r.q.Valid)return!0;if(t.status===r.q.Invalid)return!this.#v||(await this.#f.ok("auth.invalid"),!1);if(t.status===r.q.Unsure)return"auth.looks_good"===await this.#f.show({cancelable:!0,message:"auth.misspelled_email_$email",messageParams:{email:this.#a()},options:[{label:"auth.cancel"},{label:"auth.looks_good",style:"primary"}]});throw new Error(`Unknown email validation status ${t.status}.`)}handleBlur(){this.value=this.inputEl.value,this.focused=!1}handleFocus(){this.focused=!0}};(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.twoWay}),(0,i.Sn)("design:type",String)],l.prototype,"value",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],l.prototype,"placeholder",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.fromView}),(0,i.Sn)("design:type",String)],l.prototype,"status",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],l.prototype,"autoFocus",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],l.prototype,"existingEmail",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],l.prototype,"large",void 0),l=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[r.D,s.BasicDialogService])],l)},"resources/elements/email-input.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>r});var i=o(14385),n=o.n(i),a=new URL(o(16280),o.b);const r='<template class="${focused ? \'focused\' : \'\'} ${value ? \'\' : \'empty\'} ${large ? \'large\' : \'\'} ${status === \'valid\' ? \'valid\' : \'\'}"> <require from="./email-input.scss"></require> <input type="text" ref="inputEl" value.bind="value & debounce:400" spellcheck="false" placeholder.bind="placeholder || \'\'" focus.trigger="handleFocus()" blur.trigger="handleBlur()"> <i if.bind="status === \'valid\'" class="ok"><inline-svg src="'+n()(a)+'"></inline-svg></i> </template> '},"resources/elements/email-input.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(n()),p=l()(c);d.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}email-input{position:relative;width:100%;display:inline-block;width:auto}email-input input{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%}email-input input::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}email-input input::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}email-input input:disabled{opacity:.5}email-input input:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}email-input input:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}email-input.large input{height:48px;width:100%;color:#fff;padding:12px;background-color:rgba(255,255,255,.05);border:1px solid rgba(255,255,255,.2);border-radius:12px;transition:background-color .15s,border-color .15s}email-input.large input,email-input.large input::placeholder{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px}email-input.large input::placeholder{color:rgba(255,255,255,.5)}email-input.large input:hover{border-color:rgba(255,255,255,.1)}email-input.large input:focus{background-color:rgba(255,255,255,0);border-color:rgba(255,255,255,.4);outline:none !important}email-input.valid input{padding-right:30px}email-input i{--input__icon--color: rgba(255, 255, 255, 0.4);position:absolute;right:0;top:0;height:100%;width:30px;display:inline-flex;align-items:center;justify-content:center;pointer-events:none}email-input i svg *{fill:var(--input__icon--color)}email-input i.ok{--input__icon--color: var(--color--accent)}`,""]);const u=d},"resources/elements/favorite-button":(t,e,o)=>{o.r(e),o.d(e,{FavoriteButton:()=>l});var i=o(15215),n=o("aurelia-framework"),a=o(20770),r=o(54995),s=o(48881);let l=class{#w;#e;constructor(t,e){this.showTooltip=!1,this.size="s",this.#w=t,this.#e=e}bind(){this.favoritesChanged()}favoritesChanged(){this.favorites&&(this.favorite=this.favorites.hasOwnProperty(this.titleId))}toggleFavorite(t){this.favorite?this.#w.classList.remove("bump"):(this.#w.classList.add("bump"),setTimeout((()=>this.#w.classList.remove("bump")),300)),setTimeout((()=>{this.#e.dispatch(s.W6,this.titleId)})),t.stopPropagation()}};(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.twoWay}),(0,i.Sn)("design:type",String)],l.prototype,"titleId",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Boolean)],l.prototype,"showTooltip",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],l.prototype,"size",void 0),l=(0,i.Cg)([(0,r.m6)({selectors:{favorites:(0,r.$t)((t=>t.favoriteTitles))}}),(0,n.inject)(Element,a.il),(0,i.Sn)("design:paramtypes",[Element,a.il])],l)},"resources/elements/favorite-button.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template class="${favorite ? \'favorite\' : \'\'} ${size}"> <require from="../../shared/resources/elements/tooltip"></require> <require from="./favorite-button.scss"></require> <tooltip if.bind="showTooltip" class="info" direction="top-left"> <div slot="content"> <p>${\'favorite_button.mark_as_favorite\' | i18n}</p> </div> </tooltip> <button click.trigger="toggleFavorite($event)" tabindex="0"> <i class="icon favorite-icon ${favorite ? \'favorite\' : \'\'}"></i> </button> </template> '},"resources/elements/favorite-button.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(n()),p=l()(c);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,favorite-button button .favorite-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}favorite-button{position:relative}favorite-button .s button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;padding:4px}favorite-button .s button,favorite-button .s button *{cursor:pointer}favorite-button .s button:hover{background:rgba(255,255,255,.25)}favorite-button .m button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s}favorite-button .m button,favorite-button .m button *{cursor:pointer}favorite-button .m button:hover{background:rgba(255,255,255,.25)}favorite-button .l button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;padding:8px}favorite-button .l button,favorite-button .l button *{cursor:pointer}favorite-button .l button:hover{background:rgba(255,255,255,.25)}favorite-button.bump .icon,favorite-button.bump:before{animation:favorite-bump .3s 1 ease-in-out}favorite-button button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;padding:4px}favorite-button button,favorite-button button *{cursor:pointer}favorite-button button:hover{background:rgba(255,255,255,.25)}favorite-button button .favorite-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:flex}favorite-button button .favorite-icon:before{font-family:inherit;content:"kid_star"}.theme-default favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-purple-pro favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-green-pro favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-orange-pro favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-pro favorite-button button .favorite-icon{color:rgba(255,255,255,.8)}.theme-default favorite-button button .favorite-icon:hover{color:#fff}.theme-purple-pro favorite-button button .favorite-icon:hover{color:#fff}.theme-green-pro favorite-button button .favorite-icon:hover{color:#fff}.theme-orange-pro favorite-button button .favorite-icon:hover{color:#fff}.theme-pro favorite-button button .favorite-icon:hover{color:#fff}favorite-button button .favorite-icon.favorite{font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--highlight)}`,""]);const u=d},"resources/elements/follow-button":(t,e,o)=>{o.r(e),o.d(e,{FollowButton:()=>r});var i=o(15215),n=o("aurelia-framework"),a=o(50654);let r=class{constructor(t,e){this.element=t,this.followedGames=e,this.showTooltip=!1}get following(){return!!this.gameId&&!!this.followedGames.followedGames.find((t=>t.gameId===this.gameId))}async toggle(t){if(t.preventDefault(),!this.busy){this.busy=!0;try{this.following?(await this.followedGames.unfollowGames([this.gameId]),this.element?.classList.remove("bump")):(await this.followedGames.followGames([this.gameId],2),this.element?.classList.add("bump"),setTimeout((()=>this.element?.classList.remove("bump")),300))}finally{this.busy=!1}}}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],r.prototype,"gameId",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Boolean)],r.prototype,"showTooltip",void 0),(0,i.Cg)([(0,n.computedFrom)("gameId","followedGames.followedGames"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],r.prototype,"following",null),r=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[Element,a.O])],r)},"resources/elements/follow-button.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template class="${following ? \'following\' : \'\'} ${bump ? \'bump\' : \'\'}"> <require from="../../shared/resources/elements/tooltip"></require> <require from="./follow-button.scss"></require> <tooltip if.bind="showTooltip" class="info" direction="top-left"> <div slot="content"> <p>${\'follow_button.notify_when_mods_update\' | i18n}</p> </div> </tooltip> <button click.trigger="toggle($event)" tabindex="0"> <i if.bind="!following" class="icon follow-icon"></i> <i else class="icon unfollow-icon"></i> </button> </template> '},"resources/elements/follow-button.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(n()),p=l()(c);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,follow-button button .unfollow-icon,follow-button button .follow-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}follow-button{position:relative}follow-button.bump .icon,follow-button.bump:before{animation:favorite-bump .3s 1 ease-in-out}follow-button button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s}follow-button button,follow-button button *{cursor:pointer}follow-button button:hover{background:rgba(255,255,255,.25)}follow-button button:hover .follow-icon{color:#fff}follow-button button .unfollow-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--highlight)}follow-button button .unfollow-icon:before{font-family:inherit;content:"notifications"}follow-button button .follow-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:rgba(255,255,255,.8)}follow-button button .follow-icon:before{font-family:inherit;content:"notifications"}@keyframes follow-bump{0%{transform:scale(1)}50%{transform:scale(1.4)}100%{transform:scale(1)}}`,""]);const u=d},"resources/elements/happy-icon.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>r});var i=o(14385),n=o.n(i),a=new URL(o(57473),o.b);const r='<template> <require from="./happy-icon.scss"></require> <i> <inline-svg src="'+n()(a)+'"></inline-svg> </i> </template> '},"resources/elements/happy-icon.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"happy-icon{width:16px;height:16px;display:inline-flex;align-items:center;justify-content:center}happy-icon i{display:inline-block}happy-icon i svg *{fill:var(--theme--highlight)}",""]);const s=r},"resources/elements/layout-toggle":(t,e,o)=>{o.r(e),o.d(e,{Layout:()=>i,LayoutToggle:()=>c});var i,n=o(15215),a=o("aurelia-framework"),r=o(20770),s=o(54995),l=o(48881);!function(t){t.List="list",t.Thumbnail="thumbnail"}(i||(i={}));let c=class{#e;constructor(t){this.wide=!1,this.#e=t}bind(){this.settingsKey&&(this.layout=this.settings[this.settingsKey]??(this.default||i.List))}set(t){this.layout=t,this.settingsKey&&this.#e.dispatch(l.Kc,{[this.settingsKey]:this.layout},this.source)}};(0,n.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.twoWay}),(0,n.Sn)("design:type",String)],c.prototype,"layout",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",String)],c.prototype,"settingsKey",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",String)],c.prototype,"default",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",String)],c.prototype,"source",void 0),(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Boolean)],c.prototype,"wide",void 0),c=(0,n.Cg)([(0,s.m6)({selectors:{settings:(0,s.$t)((t=>t.settings))}}),(0,a.autoinject)(),(0,n.Sn)("design:paramtypes",[r.il])],c)},"resources/elements/layout-toggle.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template class="${layout}" tabindex="0"> <require from="./layout-toggle.scss"></require> <require from="shared/resources/elements/tabs"></require> <require from="shared/resources/elements/tab"></require> <tabs> <tab active.bind="layout === \'thumbnail\'" click.delegate="set(\'thumbnail\')"> <span class="icon">calendar_view_month</span> </tab> <tab active.bind="layout === \'list\'" click.delegate="set(\'list\')"> <span class="icon">view_stream</span> </tab> </tabs> </template> '},"resources/elements/layout-toggle.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(n()),p=l()(c);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,layout-toggle span.icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}layout-toggle,layout-toggle *{cursor:pointer}layout-toggle span.icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}`,""]);const u=d},"resources/elements/lazy-render":(t,e,o)=>{o.r(e),o.d(e,{LazyRender:()=>r});var i=o(15215),n=o("aurelia-framework");let a=class{constructor(){this.#x=new Map,this.#k=new Map,this.#S=new Map}#x;#k;#S;observe(t,e,o){let i=this.#x.get(e);i||(i=new IntersectionObserver((t=>{t.forEach((t=>this.#C(t)))}),{root:e,rootMargin:"500px",threshold:[]}),this.#x.set(e,i));const n=this.#k.get(i)||0;i.observe(t),this.#k.set(i,n+1),this.#S.set(t,{timeout:null,callback:o,initialUpdate:!0,root:e})}unobserve(t){const e=this.#S.get(t);if(e){const o=this.#x.get(e.root);if(o){const i=this.#k.get(o)||0;o.unobserve(t),i<=1?(o.disconnect(),this.#x.delete(e.root),this.#k.delete(o)):this.#k.set(o,i-1)}clearTimeout(e.timeout),this.#S.delete(t)}}#C(t){const e=t.isIntersecting,o=t.target,i=this.#S.get(o);if(i){if(clearTimeout(i.timeout),i.callback)if(!e||i.initialUpdate)i.callback(e);else{const t=setTimeout((()=>i.callback(!0)),100);i.timeout=t}i.initialUpdate=!1}}};a=(0,i.Cg)([(0,n.singleton)()],a);let r=class{#w;#_;constructor(t,e){this.visible=!1,this.#w=t,this.#_=e}attached(){const t=document.querySelector(".view-scrollable");this.#_.observe(this.#w.parentElement,t,(t=>this.visible=t))}detached(){this.#_.unobserve(this.#w.parentElement)}};r=(0,i.Cg)([(0,n.autoinject)(),(0,n.containerless)(),(0,i.Sn)("design:paramtypes",[Element,a])],r)},"resources/elements/lazy-render.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template> <template if.bind="visible"> <template replaceable part="content"></template> </template> <template else> <template replaceable part="placeholder" if.bind="!visible"></template> </template> </template> '},"resources/elements/new-badge":(t,e,o)=>{o.r(e),o.d(e,{NewBadge:()=>i});class i{}},"resources/elements/new-badge.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i="<template> <require from=\"./new-badge.scss\"></require> ${'new_badge.new' | i18n} </template> "},"resources/elements/new-badge.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"new-badge{display:flex;flex-direction:row;align-items:center;padding:0px 4px;gap:10px;height:16px;background:rgba(255,255,255,.2);border-radius:4px;font-weight:700;font-size:10px;line-height:16px;letter-spacing:.5px;text-transform:uppercase;color:rgba(255,255,255,.8);margin-left:auto}",""]);const s=r},"resources/elements/pro-cta-label":(t,e,o)=>{o.r(e),o.d(e,{ProCtaLabel:()=>l});var i=o(15215),n=o("aurelia-framework"),a=o(811),r=o(54995),s=o(70236);let l=class{constructor(t){this.promotions=t,this.trialAvailable=!0}get eligibleForFreeTrial(){return this.trialAvailable&&!(0,s.Lt)(this.account?.flags,2048)&&!(0,s.Lt)(this.promotions.promotion?.flags??0,1)}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Boolean)],l.prototype,"trialAvailable",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],l.prototype,"trialKey",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],l.prototype,"noTrialKey",void 0),(0,i.Cg)([(0,n.computedFrom)("account.flags","trialAvailable","promotions.promotion"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],l.prototype,"eligibleForFreeTrial",null),l=(0,i.Cg)([(0,r.m6)({selectors:{account:(0,r.$t)((t=>t.account))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[a.n])],l)},"resources/elements/pro-cta-label.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i="<template> ${eligibleForFreeTrial ? trialKey || 'pro_cta_label.start_free_trial' : noTrialKey || 'pro_cta_label.upgrade_to_pro' | i18n} </template> "},"resources/elements/progress-bar.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template bindable="progress, indeterminate"> <require from="./progress-bar.scss"></require> <div class="value" if.bind="!indeterminate" css.bind="{width: (progress * 100) +\'%\'}"></div> <div class="indeterminate-value" if.bind="indeterminate"></div> </template> '},"resources/elements/progress-bar.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"progress-bar{display:block;width:100%;height:7px;border-radius:100px;background:rgba(255,255,255,.2);position:relative;overflow:hidden}progress-bar .value{height:100%;border-radius:100px}progress-bar .indeterminate-value{position:absolute;height:100%;border-radius:2px;top:0;left:0;animation:indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite}progress-bar .value,progress-bar .indeterminate-value{background:var(--theme--highlight)}@keyframes indeterminate{0%{left:-35%;right:100%}60%{left:100%;right:-90%}100%{left:100%;right:-90%}}",""]);const s=r},"resources/elements/remote-code":(t,e,o)=>{o.r(e),o.d(e,{RemoteCode:()=>l});var i=o(15215),n=o("aurelia-framework"),a=o(68663),r=o(85805),s=o(38777);let l=class{#o;#z;#L;constructor(t,e){this.remote=t,this.visible=!0,this.#z=null,this.#L=null,this.#o=e}attached(){this.visibleChanged(),this.#L=this.remote.onStatusChanged((t=>{t!==r.t.Disconnected&&(this.code=null)}))}detached(){null!==this.#L&&(this.#L.dispose(),this.#L=null)}get isConnected(){return this.remote.status===r.t.Connected}async visibleChanged(){this.visible&&(this.isConnected||this.code||await this.#q())}async#E(){this.isConnected||(this.code&&(this.timeElapsed=this.code.expiresIn),this.codeExpired=!0,await(0,s.Wn)(1e3),this.isConnected||(await this.#q(),this.codeExpired=!1))}async#q(){try{this.code=await this.#o.requestRemoteAuthCode()}catch{return void(this.code=null)}this.timeElapsed=0,clearInterval(this.#z),this.#z=setInterval((()=>{this.timeElapsed+=.01,(!this.code||this.timeElapsed>=this.code.expiresIn)&&(clearInterval(this.#z),this.visible?this.#E():this.code=null)}),10)}};(0,i.Cg)([(0,n.bindable)(),(0,i.Sn)("design:type",Boolean)],l.prototype,"visible",void 0),(0,i.Cg)([(0,n.computedFrom)("remote.status"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],l.prototype,"isConnected",null),l=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[r.e,a.x])],l)},"resources/elements/remote-code.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template> <require from="./remote-code.scss"></require> <require from="./progress-bar.html"></require> <div class="code ${codeExpired ? \'expired\' : \'\'}"> <span repeat.for="digit of code.code.split(\'\')" class="digit">${digit}</span> </div> <progress-bar progress.bind="1 - timeElapsed / code.expiresIn"></progress-bar> </template> '},"resources/elements/remote-code.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"remote-code{display:flex;flex-direction:column;align-items:center;width:fit-content}remote-code .code{margin:0;height:40px;padding:8px;display:flex;align-items:center;justify-content:center;gap:8px;background-color:rgba(255,255,255,.05);border-radius:8px 8px 0 0;min-width:141px}remote-code .code .digit{font-weight:500;text-align:center;color:#fff;transition:color .25s;font-size:24px;letter-spacing:-1px}remote-code .code .digit:nth-child(1){margin-left:7px;transition-delay:0s}remote-code .code .digit:nth-child(2){transition-delay:.1s}remote-code .code .digit:nth-child(3){transition-delay:.2s}remote-code .code .digit:nth-child(4){transition-delay:.3s}remote-code .code .digit:nth-child(5){margin-right:7px;transition-delay:.4s}remote-code .code.expired .digit{color:rgba(0,0,0,0)}remote-code progress-bar{height:4px;background-color:rgba(255,255,255,.15);border-top-left-radius:0;border-top-right-radius:0;overflow:hidden}remote-code progress-bar .value{background:var(--theme--highlight) !important;border-radius:0}",""]);const s=r},"resources/elements/remote-qr-code":(t,e,o)=>{o.r(e),o.d(e,{RemoteQrCode:()=>s});var i=o(15215),n=o("aurelia-framework"),a=o(87583),r=o(16953);class s{constructor(){this.options=void 0}attached(){this.#I()}#I(){this.canvasElement&&a.mo(this.canvasElement,`${r.A.websiteUrl}/remote`,this.options)}}(0,i.Cg)([(0,n.bindable)(),(0,i.Sn)("design:type",Object)],s.prototype,"options",void 0)},"resources/elements/remote-qr-code.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template> <require from="./remote-qr-code.scss"></require> <canvas ref="canvasElement"></canvas> </template> '},"resources/elements/remote-qr-code.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"remote-qr-code canvas{width:100% !important;height:100% !important;border-radius:5px}",""]);const s=r},"resources/elements/search-input":(t,e,o)=>{o.r(e),o.d(e,{SearchInput:()=>r});var i=o(15215),n=o("aurelia-framework"),a=o(18776);let r=class{#d;constructor(t){this.searchTerms="",this.onClear=()=>{},this.onReset=()=>{},this.onSearch=()=>{},this.#d=t}bind(){this.#d.currentInstruction.queryParams.search&&(this.searchTerms=this.#d.currentInstruction.queryParams.search)}attached(){this.search()}searchTermsChanged(){this.onReset(),this.searchTerms||this.clear(!1)}search(){this.searchTerms&&(this.onSearch?.(),this.#T())}clear(t=!0){this.searchTerms="",t&&this.inputEl?.focus(),this.#T(),this.onClear?.()}#T(){this.#d.navigateToRoute(this.#d.currentInstruction.config.name??"",{...this.#d.currentInstruction.params,search:this.searchTerms},{trigger:!1,replace:!0})}};(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.fromView}),(0,i.Sn)("design:type",String)],r.prototype,"searchTerms",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],r.prototype,"searching",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Object)],r.prototype,"onClear",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Object)],r.prototype,"onReset",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Object)],r.prototype,"onSearch",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],r.prototype,"placeholderKey",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],r.prototype,"buttonTextKey",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.twoWay}),(0,i.Sn)("design:type",Boolean)],r.prototype,"disabled",void 0),r=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[a.Ix])],r)},"resources/elements/search-input.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>r});var i=o(14385),n=o.n(i),a=new URL(o(34635),o.b);const r='<template> <require from="./search-input.scss"></require> <form submit.delegate="search()"> <span class="input-wrapper"> <i class="search-icon">search</i> <input type="text" placeholder="${placeholderKey | i18n}" value.bind="searchTerms" ref="inputEl" disabled.bind="disabled"> <a class="clear-button" href="#" click.delegate="clear()" if.bind="searchTerms || searching"> <inline-svg src="'+n()(a)+'"></inline-svg> </a> </span> <button class="search-button" disabled.bind="!searchTerms || disabled">${buttonTextKey | i18n}</button> </form> </template> '},"resources/elements/search-input.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>u});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(n()),p=l()(c);d.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,search-input .input-wrapper .search-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}search-input form{display:inline-flex;align-items:center}search-input .input-wrapper{position:relative;width:360px}search-input .input-wrapper .search-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;position:absolute;left:10px;top:10px;font-size:20px;color:rgba(255,255,255,.8)}search-input input{width:100%;min-width:40px;height:40px;border:0;border-radius:28px;padding:8px 8px 8px 36px;background:rgba(255,255,255,.1);transition:background-color .15s}.theme-default search-input input{color:rgba(255,255,255,.8)}.theme-purple-pro search-input input{color:rgba(255,255,255,.8)}.theme-green-pro search-input input{color:rgba(255,255,255,.8)}.theme-orange-pro search-input input{color:rgba(255,255,255,.8)}.theme-pro search-input input{color:rgba(255,255,255,.8)}search-input input,search-input input::placeholder{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px}.theme-default search-input input,.theme-default search-input input::placeholder{color:rgba(255,255,255,.8)}.theme-purple-pro search-input input,.theme-purple-pro search-input input::placeholder{color:rgba(255,255,255,.8)}.theme-green-pro search-input input,.theme-green-pro search-input input::placeholder{color:rgba(255,255,255,.8)}.theme-orange-pro search-input input,.theme-orange-pro search-input input::placeholder{color:rgba(255,255,255,.8)}.theme-pro search-input input,.theme-pro search-input input::placeholder{color:rgba(255,255,255,.8)}search-input input:disabled{opacity:.5}search-input .clear-button{position:absolute;right:12px;top:16px;background:rgba(0,0,0,0);border:0;padding:0;opacity:.2;transition:opacity .15s;display:inline-flex}search-input .clear-button:hover{opacity:1}search-input .search-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;height:40px;box-shadow:none;line-height:24px;font-weight:700;color:#000;border-radius:56px;padding:10px 16px;background:#fff;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;margin-left:10px}search-input .search-button,search-input .search-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) search-input .search-button{border:1px solid #fff}}search-input .search-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}search-input .search-button>*:first-child{padding-left:0}search-input .search-button>*:last-child{padding-right:0}search-input .search-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) search-input .search-button svg *{fill:CanvasText}}search-input .search-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) search-input .search-button svg{opacity:1}}search-input .search-button img{height:50%}search-input .search-button:disabled{opacity:.3}search-input .search-button:disabled,search-input .search-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){search-input .search-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}search-input .search-button:not(:disabled):hover svg{opacity:1}}search-input .search-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}search-input .search-button:hover{background:rgba(255,255,255,.8) !important;color:rgba(0,0,0,.8) !important}`,""]);const u=d},"resources/elements/title-thumbnail":(t,e,o)=>{o.r(e),o.d(e,{TitleThumbnail:()=>a});var i=o(15215),n=o("aurelia-framework");class a{get highResWidth(){return Math.min(2*this.width,460)}}(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],a.prototype,"src",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Number)],a.prototype,"width",void 0),(0,i.Cg)([(0,n.computedFrom)("width"),(0,i.Sn)("design:type",Number),(0,i.Sn)("design:paramtypes",[])],a.prototype,"highResWidth",null)},"resources/elements/title-thumbnail.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>r});var i=o(14385),n=o.n(i),a=new URL(o(98593),o.b);const r='<template> <require from="./title-thumbnail.scss"></require> <img class="title-thumbnail ${thumbnailLoaded ? \'loaded\' : \'\'}" fallback-src="'+n()(a)+'" src="${src | cdn:{size: width}}" srcset="${src | cdn:{size: width}}, ${src | cdn:{size: highResWidth}} 2x" load.trigger="thumbnailLoaded = true"> </template> '},"resources/elements/title-thumbnail.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>s});var i=o(31601),n=o.n(i),a=o(76314),r=o.n(a)()(n());r.push([t.id,"title-thumbnail .title-thumbnail{width:100%;display:block}title-thumbnail .title-thumbnail:not(.loaded){animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box}",""]);const s=r},"resources/elements/toast-anchor":(t,e,o)=>{o.r(e),o.d(e,{ToastAnchor:()=>r});var i=o(15215),n=o("aurelia-framework"),a=o(67064);let r=class{#w;#i;#$;#M;constructor(t,e){this.#w=t,this.#i=e}attached(){this.#M=new IntersectionObserver((t=>{const e=t[0];if(e.isIntersecting)this.#B();else{const t=e.boundingClientRect.top>0?"down":"up";this.#O(t)}})),this.#M.observe(this.#w)}detached(){this.#M.disconnect(),this.#B()}#O(t){this.#$=this.#i.toast({content:this.labelKey,persist:!0,type:`arrow-${t}`,lock:!0,onclick:()=>this.#F(),classNames:this.classNames})}#B(){this.#i.remove(this.#$)}#F(){if(this.scrollEl){const t=this.#w.offsetTop-60;this.scrollEl.scrollTo({top:t,behavior:"smooth"})}}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],r.prototype,"name",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],r.prototype,"labelKey",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],r.prototype,"classNames",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",HTMLElement)],r.prototype,"scrollEl",void 0),r=(0,i.Cg)([(0,n.inlineView)("<template></template>"),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[Element,a.l])],r)},"resources/index":(t,e,o)=>{function i(t){t.globalResources(["./value-converters/game","./value-converters/flags","./custom-attributes/attach-src","./custom-attributes/pro-cta","./custom-attributes/title-link"])}o.r(e),o.d(e,{configure:()=>i}),o("aurelia-framework")}}]);
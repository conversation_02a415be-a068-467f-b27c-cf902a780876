(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[9184],{5707:(module,__unused_webpack_exports,__webpack_require__)=>{"use strict";const Token=__webpack_require__(44266),strtok3=__webpack_require__(96452),{stringToBytes,tarHeaderChecksumMatches,uint32SyncSafeToken}=__webpack_require__(86760),supported=__webpack_require__(71664),minimumBytes=4100;async function fromStream(e){const t=await strtok3.fromStream(e);try{return await fromTokenizer(t)}finally{await t.close()}}async function fromBuffer(e){if(!(e instanceof Uint8Array||e instanceof ArrayBuffer||Buffer.isBuffer(e)))throw new TypeError(`Expected the \`input\` argument to be of type \`Uint8Array\` or \`<PERSON><PERSON>er\` or \`ArrayBuffer\`, got \`${typeof e}\``);const t=e instanceof Buffer?e:Buffer.from(e);if(t&&t.length>1)return fromTokenizer(strtok3.fromBuffer(t))}function _check(e,t,i){i={offset:0,...i};for(const[n,a]of t.entries())if(i.mask){if(a!==(i.mask[n]&e[n+i.offset]))return!1}else if(a!==e[n+i.offset])return!1;return!0}async function fromTokenizer(e){try{return _fromTokenizer(e)}catch(e){if(!(e instanceof strtok3.EndOfStreamError))throw e}}async function _fromTokenizer(e){let t=Buffer.alloc(minimumBytes);const i=(e,i)=>_check(t,e,i),n=(e,t)=>i(stringToBytes(e),t);if(e.fileInfo.size||(e.fileInfo.size=Number.MAX_SAFE_INTEGER),await e.peekBuffer(t,{length:12,mayBeLess:!0}),i([66,77]))return{ext:"bmp",mime:"image/bmp"};if(i([11,119]))return{ext:"ac3",mime:"audio/vnd.dolby.dd-raw"};if(i([120,1]))return{ext:"dmg",mime:"application/x-apple-diskimage"};if(i([77,90]))return{ext:"exe",mime:"application/x-msdownload"};if(i([37,33]))return await e.peekBuffer(t,{length:24,mayBeLess:!0}),n("PS-Adobe-",{offset:2})&&n(" EPSF-",{offset:14})?{ext:"eps",mime:"application/eps"}:{ext:"ps",mime:"application/postscript"};if(i([31,160])||i([31,157]))return{ext:"Z",mime:"application/x-compress"};if(i([255,216,255]))return{ext:"jpg",mime:"image/jpeg"};if(i([73,73,188]))return{ext:"jxr",mime:"image/vnd.ms-photo"};if(i([31,139,8]))return{ext:"gz",mime:"application/gzip"};if(i([66,90,104]))return{ext:"bz2",mime:"application/x-bzip2"};if(n("ID3")){await e.ignore(6);const a=await e.readToken(uint32SyncSafeToken);return e.position+a>e.fileInfo.size?{ext:"mp3",mime:"audio/mpeg"}:(await e.ignore(a),fromTokenizer(e))}if(n("MP+"))return{ext:"mpc",mime:"audio/x-musepack"};if((67===t[0]||70===t[0])&&i([87,83],{offset:1}))return{ext:"swf",mime:"application/x-shockwave-flash"};if(i([71,73,70]))return{ext:"gif",mime:"image/gif"};if(n("FLIF"))return{ext:"flif",mime:"image/flif"};if(n("8BPS"))return{ext:"psd",mime:"image/vnd.adobe.photoshop"};if(n("WEBP",{offset:8}))return{ext:"webp",mime:"image/webp"};if(n("MPCK"))return{ext:"mpc",mime:"audio/x-musepack"};if(n("FORM"))return{ext:"aif",mime:"audio/aiff"};if(n("icns",{offset:0}))return{ext:"icns",mime:"image/icns"};if(i([80,75,3,4])){try{for(;e.position+30<e.fileInfo.size;){await e.readBuffer(t,{length:30});const r={compressedSize:t.readUInt32LE(18),uncompressedSize:t.readUInt32LE(22),filenameLength:t.readUInt16LE(26),extraFieldLength:t.readUInt16LE(28)};if(r.filename=await e.readToken(new Token.StringType(r.filenameLength,"utf-8")),await e.ignore(r.extraFieldLength),"META-INF/mozilla.rsa"===r.filename)return{ext:"xpi",mime:"application/x-xpinstall"};if(r.filename.endsWith(".rels")||r.filename.endsWith(".xml"))switch(r.filename.split("/")[0]){case"_rels":default:break;case"word":return{ext:"docx",mime:"application/vnd.openxmlformats-officedocument.wordprocessingml.document"};case"ppt":return{ext:"pptx",mime:"application/vnd.openxmlformats-officedocument.presentationml.presentation"};case"xl":return{ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}}if(r.filename.startsWith("xl/"))return{ext:"xlsx",mime:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"};if(r.filename.startsWith("3D/")&&r.filename.endsWith(".model"))return{ext:"3mf",mime:"model/3mf"};if("mimetype"===r.filename&&r.compressedSize===r.uncompressedSize)switch(await e.readToken(new Token.StringType(r.compressedSize,"utf-8"))){case"application/epub+zip":return{ext:"epub",mime:"application/epub+zip"};case"application/vnd.oasis.opendocument.text":return{ext:"odt",mime:"application/vnd.oasis.opendocument.text"};case"application/vnd.oasis.opendocument.spreadsheet":return{ext:"ods",mime:"application/vnd.oasis.opendocument.spreadsheet"};case"application/vnd.oasis.opendocument.presentation":return{ext:"odp",mime:"application/vnd.oasis.opendocument.presentation"}}if(0===r.compressedSize){let o=-1;for(;o<0&&e.position<e.fileInfo.size;)await e.peekBuffer(t,{mayBeLess:!0}),o=t.indexOf("504B0304",0,"hex"),await e.ignore(o>=0?o:t.length)}else await e.ignore(r.compressedSize)}}catch(s){if(!(s instanceof strtok3.EndOfStreamError))throw s}return{ext:"zip",mime:"application/zip"}}if(n("OggS")){await e.ignore(28);const f=Buffer.alloc(8);return await e.readBuffer(f),_check(f,[79,112,117,115,72,101,97,100])?{ext:"opus",mime:"audio/opus"}:_check(f,[128,116,104,101,111,114,97])?{ext:"ogv",mime:"video/ogg"}:_check(f,[1,118,105,100,101,111,0])?{ext:"ogm",mime:"video/ogg"}:_check(f,[127,70,76,65,67])?{ext:"oga",mime:"audio/ogg"}:_check(f,[83,112,101,101,120,32,32])?{ext:"spx",mime:"audio/ogg"}:_check(f,[1,118,111,114,98,105,115])?{ext:"ogg",mime:"audio/ogg"}:{ext:"ogx",mime:"application/ogg"}}if(i([80,75])&&(3===t[2]||5===t[2]||7===t[2])&&(4===t[3]||6===t[3]||8===t[3]))return{ext:"zip",mime:"application/zip"};if(n("ftyp",{offset:4})&&96&t[8]){const m=t.toString("binary",8,12).replace("\0"," ").trim();switch(m){case"avif":return{ext:"avif",mime:"image/avif"};case"mif1":return{ext:"heic",mime:"image/heif"};case"msf1":return{ext:"heic",mime:"image/heif-sequence"};case"heic":case"heix":return{ext:"heic",mime:"image/heic"};case"hevc":case"hevx":return{ext:"heic",mime:"image/heic-sequence"};case"qt":return{ext:"mov",mime:"video/quicktime"};case"M4V":case"M4VH":case"M4VP":return{ext:"m4v",mime:"video/x-m4v"};case"M4P":return{ext:"m4p",mime:"video/mp4"};case"M4B":return{ext:"m4b",mime:"audio/mp4"};case"M4A":return{ext:"m4a",mime:"audio/x-m4a"};case"F4V":return{ext:"f4v",mime:"video/mp4"};case"F4P":return{ext:"f4p",mime:"video/mp4"};case"F4A":return{ext:"f4a",mime:"audio/mp4"};case"F4B":return{ext:"f4b",mime:"audio/mp4"};case"crx":return{ext:"cr3",mime:"image/x-canon-cr3"};default:return m.startsWith("3g")?m.startsWith("3g2")?{ext:"3g2",mime:"video/3gpp2"}:{ext:"3gp",mime:"video/3gpp"}:{ext:"mp4",mime:"video/mp4"}}}if(n("MThd"))return{ext:"mid",mime:"audio/midi"};if(n("wOFF")&&(i([0,1,0,0],{offset:4})||n("OTTO",{offset:4})))return{ext:"woff",mime:"font/woff"};if(n("wOF2")&&(i([0,1,0,0],{offset:4})||n("OTTO",{offset:4})))return{ext:"woff2",mime:"font/woff2"};if(i([212,195,178,161])||i([161,178,195,212]))return{ext:"pcap",mime:"application/vnd.tcpdump.pcap"};if(n("DSD "))return{ext:"dsf",mime:"audio/x-dsf"};if(n("LZIP"))return{ext:"lz",mime:"application/x-lzip"};if(n("fLaC"))return{ext:"flac",mime:"audio/x-flac"};if(i([66,80,71,251]))return{ext:"bpg",mime:"image/bpg"};if(n("wvpk"))return{ext:"wv",mime:"audio/wavpack"};if(n("%PDF")){await e.ignore(1350);const p=10485760,u=Buffer.alloc(Math.min(p,e.fileInfo.size));return await e.readBuffer(u,{mayBeLess:!0}),u.includes(Buffer.from("AIPrivateData"))?{ext:"ai",mime:"application/postscript"}:{ext:"pdf",mime:"application/pdf"}}if(i([0,97,115,109]))return{ext:"wasm",mime:"application/wasm"};if(i([73,73,42,0]))return n("CR",{offset:8})?{ext:"cr2",mime:"image/x-canon-cr2"}:i([28,0,254,0],{offset:8})||i([31,0,11,0],{offset:8})?{ext:"nef",mime:"image/x-nikon-nef"}:i([8,0,0,0],{offset:4})&&(i([45,0,254,0],{offset:8})||i([39,0,254,0],{offset:8}))?{ext:"dng",mime:"image/x-adobe-dng"}:(t=Buffer.alloc(24),await e.peekBuffer(t),(i([16,251,134,1],{offset:4})||i([8,0,0,0],{offset:4}))&&i([0,254,0,4,0,1,0,0,0,1,0,0,0,3,1],{offset:9})?{ext:"arw",mime:"image/x-sony-arw"}:{ext:"tif",mime:"image/tiff"});if(i([77,77,0,42]))return{ext:"tif",mime:"image/tiff"};if(n("MAC "))return{ext:"ape",mime:"audio/ape"};if(i([26,69,223,163])){async function c(){const t=await e.peekNumber(Token.UINT8);let i=128,n=0;for(;!(t&i)&&0!==i;)++n,i>>=1;const a=Buffer.alloc(n+1);return await e.readBuffer(a),a}async function l(){const e=await c(),t=await c();t[0]^=128>>t.length-1;const i=Math.min(6,t.length);return{id:e.readUIntBE(0,e.length),len:t.readUIntBE(t.length-i,i)}}async function h(t,i){for(;i>0;){const t=await l();if(17026===t.id)return e.readToken(new Token.StringType(t.len,"utf-8"));await e.ignore(t.len),--i}}const d=await l();switch(await h(0,d.len)){case"webm":return{ext:"webm",mime:"video/webm"};case"matroska":return{ext:"mkv",mime:"video/x-matroska"};default:return}}if(i([82,73,70,70])){if(i([65,86,73],{offset:8}))return{ext:"avi",mime:"video/vnd.avi"};if(i([87,65,86,69],{offset:8}))return{ext:"wav",mime:"audio/vnd.wave"};if(i([81,76,67,77],{offset:8}))return{ext:"qcp",mime:"audio/qcelp"}}if(n("SQLi"))return{ext:"sqlite",mime:"application/x-sqlite3"};if(i([78,69,83,26]))return{ext:"nes",mime:"application/x-nintendo-nes-rom"};if(n("Cr24"))return{ext:"crx",mime:"application/x-google-chrome-extension"};if(n("MSCF")||n("ISc("))return{ext:"cab",mime:"application/vnd.ms-cab-compressed"};if(i([237,171,238,219]))return{ext:"rpm",mime:"application/x-rpm"};if(i([197,208,211,198]))return{ext:"eps",mime:"application/eps"};if(i([40,181,47,253]))return{ext:"zst",mime:"application/zstd"};if(i([79,84,84,79,0]))return{ext:"otf",mime:"font/otf"};if(n("#!AMR"))return{ext:"amr",mime:"audio/amr"};if(n("{\\rtf"))return{ext:"rtf",mime:"application/rtf"};if(i([70,76,86,1]))return{ext:"flv",mime:"video/x-flv"};if(n("IMPM"))return{ext:"it",mime:"audio/x-it"};if(n("-lh0-",{offset:2})||n("-lh1-",{offset:2})||n("-lh2-",{offset:2})||n("-lh3-",{offset:2})||n("-lh4-",{offset:2})||n("-lh5-",{offset:2})||n("-lh6-",{offset:2})||n("-lh7-",{offset:2})||n("-lzs-",{offset:2})||n("-lz4-",{offset:2})||n("-lz5-",{offset:2})||n("-lhd-",{offset:2}))return{ext:"lzh",mime:"application/x-lzh-compressed"};if(i([0,0,1,186])){if(i([33],{offset:4,mask:[241]}))return{ext:"mpg",mime:"video/MP1S"};if(i([68],{offset:4,mask:[196]}))return{ext:"mpg",mime:"video/MP2P"}}if(n("ITSF"))return{ext:"chm",mime:"application/vnd.ms-htmlhelp"};if(i([253,55,122,88,90,0]))return{ext:"xz",mime:"application/x-xz"};if(n("<?xml "))return{ext:"xml",mime:"application/xml"};if(i([55,122,188,175,39,28]))return{ext:"7z",mime:"application/x-7z-compressed"};if(i([82,97,114,33,26,7])&&(0===t[6]||1===t[6]))return{ext:"rar",mime:"application/x-rar-compressed"};if(n("solid "))return{ext:"stl",mime:"model/stl"};if(n("BLENDER"))return{ext:"blend",mime:"application/x-blender"};if(n("!<arch>"))return await e.ignore(8),"debian-binary"===await e.readToken(new Token.StringType(13,"ascii"))?{ext:"deb",mime:"application/x-deb"}:{ext:"ar",mime:"application/x-unix-archive"};if(i([137,80,78,71,13,10,26,10])){async function g(){return{length:await e.readToken(Token.INT32_BE),type:await e.readToken(new Token.StringType(4,"binary"))}}await e.ignore(8);do{const x=await g();if(x.length<0)return;switch(x.type){case"IDAT":return{ext:"png",mime:"image/png"};case"acTL":return{ext:"apng",mime:"image/apng"};default:await e.ignore(x.length+4)}}while(e.position+8<e.fileInfo.size);return{ext:"png",mime:"image/png"}}if(i([65,82,82,79,87,49,0,0]))return{ext:"arrow",mime:"application/x-apache-arrow"};if(i([103,108,84,70,2,0,0,0]))return{ext:"glb",mime:"model/gltf-binary"};if(i([102,114,101,101],{offset:4})||i([109,100,97,116],{offset:4})||i([109,111,111,118],{offset:4})||i([119,105,100,101],{offset:4}))return{ext:"mov",mime:"video/quicktime"};if(i([73,73,82,79,8,0,0,0,24]))return{ext:"orf",mime:"image/x-olympus-orf"};if(n("gimp xcf "))return{ext:"xcf",mime:"image/x-xcf"};if(i([73,73,85,0,24,0,0,0,136,231,116,216]))return{ext:"rw2",mime:"image/x-panasonic-rw2"};if(i([48,38,178,117,142,102,207,17,166,217])){async function S(){const t=Buffer.alloc(16);return await e.readBuffer(t),{id:t,size:Number(await e.readToken(Token.UINT64_LE))}}for(await e.ignore(30);e.position+24<e.fileInfo.size;){const b=await S();let v=b.size-24;if(_check(b.id,[145,7,220,183,183,169,207,17,142,230,0,192,12,32,83,101])){const w=Buffer.alloc(16);if(v-=await e.readBuffer(w),_check(w,[64,158,105,248,77,91,207,17,168,253,0,128,95,92,68,43]))return{ext:"asf",mime:"audio/x-ms-asf"};if(_check(w,[192,239,25,188,77,91,207,17,168,253,0,128,95,92,68,43]))return{ext:"asf",mime:"video/x-ms-asf"};break}await e.ignore(v)}return{ext:"asf",mime:"application/vnd.ms-asf"}}if(i([171,75,84,88,32,49,49,187,13,10,26,10]))return{ext:"ktx",mime:"image/ktx"};if((i([126,16,4])||i([126,24,4]))&&i([48,77,73,69],{offset:4}))return{ext:"mie",mime:"application/x-mie"};if(i([39,10,0,0,0,0,0,0,0,0,0,0],{offset:2}))return{ext:"shp",mime:"application/x-esri-shape"};if(i([0,0,0,12,106,80,32,32,13,10,135,10]))switch(await e.ignore(20),await e.readToken(new Token.StringType(4,"ascii"))){case"jp2 ":return{ext:"jp2",mime:"image/jp2"};case"jpx ":return{ext:"jpx",mime:"image/jpx"};case"jpm ":return{ext:"jpm",mime:"image/jpm"};case"mjp2":return{ext:"mj2",mime:"image/mj2"};default:return}if(i([255,10])||i([0,0,0,12,74,88,76,32,13,10,135,10]))return{ext:"jxl",mime:"image/jxl"};if(i([0,0,1,186])||i([0,0,1,179]))return{ext:"mpg",mime:"video/mpeg"};if(i([0,1,0,0,0]))return{ext:"ttf",mime:"font/ttf"};if(i([0,0,1,0]))return{ext:"ico",mime:"image/x-icon"};if(i([0,0,2,0]))return{ext:"cur",mime:"image/x-icon"};if(i([208,207,17,224,161,177,26,225]))return{ext:"cfb",mime:"application/x-cfb"};if(await e.peekBuffer(t,{length:Math.min(256,e.fileInfo.size),mayBeLess:!0}),n("BEGIN:")){if(n("VCARD",{offset:6}))return{ext:"vcf",mime:"text/vcard"};if(n("VCALENDAR",{offset:6}))return{ext:"ics",mime:"text/calendar"}}if(n("FUJIFILMCCD-RAW"))return{ext:"raf",mime:"image/x-fujifilm-raf"};if(n("Extended Module:"))return{ext:"xm",mime:"audio/x-xm"};if(n("Creative Voice File"))return{ext:"voc",mime:"audio/x-voc"};if(i([4,0,0,0])&&t.length>=16){const y=t.readUInt32LE(12);if(y>12&&t.length>=y+16)try{const I=t.slice(16,y+16).toString();if(JSON.parse(I).files)return{ext:"asar",mime:"application/x-asar"}}catch(P){}}if(i([6,14,43,52,2,5,1,1,13,1,2,1,1,2]))return{ext:"mxf",mime:"application/mxf"};if(n("SCRM",{offset:44}))return{ext:"s3m",mime:"audio/x-s3m"};if(i([71],{offset:4})&&(i([71],{offset:192})||i([71],{offset:196})))return{ext:"mts",mime:"video/mp2t"};if(i([66,79,79,75,77,79,66,73],{offset:60}))return{ext:"mobi",mime:"application/x-mobipocket-ebook"};if(i([68,73,67,77],{offset:128}))return{ext:"dcm",mime:"application/dicom"};if(i([76,0,0,0,1,20,2,0,0,0,0,0,192,0,0,0,0,0,0,70]))return{ext:"lnk",mime:"application/x.ms.shortcut"};if(i([98,111,111,107,0,0,0,0,109,97,114,107,0,0,0,0]))return{ext:"alias",mime:"application/x.apple.alias"};if(i([76,80],{offset:34})&&(i([0,0,1],{offset:8})||i([1,0,2],{offset:8})||i([2,0,2],{offset:8})))return{ext:"eot",mime:"application/vnd.ms-fontobject"};if(i([6,6,237,245,216,29,70,229,189,49,239,231,254,116,183,29]))return{ext:"indd",mime:"application/x-indesign"};if(await e.peekBuffer(t,{length:Math.min(512,e.fileInfo.size),mayBeLess:!0}),tarHeaderChecksumMatches(t))return{ext:"tar",mime:"application/x-tar"};if(i([255,254,255,14,83,0,107,0,101,0,116,0,99,0,104,0,85,0,112,0,32,0,77,0,111,0,100,0,101,0,108,0]))return{ext:"skp",mime:"application/vnd.sketchup.skp"};if(n("-----BEGIN PGP MESSAGE-----"))return{ext:"pgp",mime:"application/pgp-encrypted"};if(t.length>=2&&i([255,224],{offset:0,mask:[255,224]})){if(i([16],{offset:1,mask:[22]}))return i([8],{offset:1,mask:[8]}),{ext:"aac",mime:"audio/aac"};if(i([2],{offset:1,mask:[6]}))return{ext:"mp3",mime:"audio/mpeg"};if(i([4],{offset:1,mask:[6]}))return{ext:"mp2",mime:"audio/mpeg"};if(i([6],{offset:1,mask:[6]}))return{ext:"mp1",mime:"audio/mpeg"}}}const stream=readableStream=>new Promise(((resolve,reject)=>{const stream=eval("require")("stream");readableStream.on("error",reject),readableStream.once("readable",(async()=>{const e=new stream.PassThrough;let t;t=stream.pipeline?stream.pipeline(readableStream,e,(()=>{})):readableStream.pipe(e);const i=readableStream.read(minimumBytes)||readableStream.read()||Buffer.alloc(0);try{const t=await fromBuffer(i);e.fileType=t}catch(e){reject(e)}resolve(t)}))})),fileType={fromStream,fromTokenizer,fromBuffer,stream};Object.defineProperty(fileType,"extensions",{get:()=>new Set(supported.extensions)}),Object.defineProperty(fileType,"mimeTypes",{get:()=>new Set(supported.mimeTypes)}),module.exports=fileType},15563:function(e,t){"use strict";var i=this&&this.__await||function(e){return this instanceof i?(this.v=e,this):new i(e)},n=this&&this.__asyncGenerator||function(e,t,n){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var a,r=n.apply(e,t||[]),o=[];return a={},s("next"),s("throw"),s("return"),a[Symbol.asyncIterator]=function(){return this},a;function s(e){r[e]&&(a[e]=function(t){return new Promise((function(i,n){o.push([e,t,i,n])>1||f(e,t)}))})}function f(e,t){try{(n=r[e](t)).value instanceof i?Promise.resolve(n.value.v).then(m,p):u(o[0][2],n)}catch(e){u(o[0][3],e)}var n}function m(e){f("next",e)}function p(e){f("throw",e)}function u(e,t){e(t),o.shift(),o.length&&f(o[0][0],o[0][1])}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e){return n(this,arguments,(function*(){const t=/\r?\n/,n=new TextDecoder;let a="",r=e.read();for(;;){const{done:o,value:s}=yield i(r);if(o)return a.length>0&&(yield yield i(JSON.parse(a))),yield i(void 0);a+=n.decode(s,{stream:!0});const f=a.split(t);a=f.pop();for(const e of f)yield yield i(JSON.parse(e));r=e.read()}}))}},26393:e=>{e.exports={exif:{1:"InteropIndex",2:"InteropVersion",11:"ProcessingSoftware",254:"SubfileType",255:"OldSubfileType",256:"ImageWidth",257:"ImageHeight",258:"BitsPerSample",259:"Compression",262:"PhotometricInterpretation",263:"Thresholding",264:"CellWidth",265:"CellLength",266:"FillOrder",269:"DocumentName",270:"ImageDescription",271:"Make",272:"Model",273:"StripOffsets",274:"Orientation",277:"SamplesPerPixel",278:"RowsPerStrip",279:"StripByteCounts",280:"MinSampleValue",281:"MaxSampleValue",282:"XResolution",283:"YResolution",284:"PlanarConfiguration",285:"PageName",286:"XPosition",287:"YPosition",288:"FreeOffsets",289:"FreeByteCounts",290:"GrayResponseUnit",291:"GrayResponseCurve",292:"T4Options",293:"T6Options",296:"ResolutionUnit",297:"PageNumber",300:"ColorResponseUnit",301:"TransferFunction",305:"Software",306:"ModifyDate",315:"Artist",316:"HostComputer",317:"Predictor",318:"WhitePoint",319:"PrimaryChromaticities",320:"ColorMap",321:"HalftoneHints",322:"TileWidth",323:"TileLength",324:"TileOffsets",325:"TileByteCounts",326:"BadFaxLines",327:"CleanFaxData",328:"ConsecutiveBadFaxLines",330:"SubIFD",332:"InkSet",333:"InkNames",334:"NumberofInks",336:"DotRange",337:"TargetPrinter",338:"ExtraSamples",339:"SampleFormat",340:"SMinSampleValue",341:"SMaxSampleValue",342:"TransferRange",343:"ClipPath",344:"XClipPathUnits",345:"YClipPathUnits",346:"Indexed",347:"JPEGTables",351:"OPIProxy",400:"GlobalParametersIFD",401:"ProfileType",402:"FaxProfile",403:"CodingMethods",404:"VersionYear",405:"ModeNumber",433:"Decode",434:"DefaultImageColor",435:"T82Options",437:"JPEGTables",512:"JPEGProc",513:"ThumbnailOffset",514:"ThumbnailLength",515:"JPEGRestartInterval",517:"JPEGLosslessPredictors",518:"JPEGPointTransforms",519:"JPEGQTables",520:"JPEGDCTables",521:"JPEGACTables",529:"YCbCrCoefficients",530:"YCbCrSubSampling",531:"YCbCrPositioning",532:"ReferenceBlackWhite",559:"StripRowCounts",700:"ApplicationNotes",999:"USPTOMiscellaneous",4096:"RelatedImageFileFormat",4097:"RelatedImageWidth",4098:"RelatedImageHeight",18246:"Rating",18247:"XP_DIP_XML",18248:"StitchInfo",18249:"RatingPercent",32781:"ImageID",32931:"WangTag1",32932:"WangAnnotation",32933:"WangTag3",32934:"WangTag4",32995:"Matteing",32996:"DataType",32997:"ImageDepth",32998:"TileDepth",33405:"Model2",33421:"CFARepeatPatternDim",33422:"CFAPattern2",33423:"BatteryLevel",33424:"KodakIFD",33432:"Copyright",33434:"ExposureTime",33437:"FNumber",33445:"MDFileTag",33446:"MDScalePixel",33447:"MDColorTable",33448:"MDLabName",33449:"MDSampleInfo",33450:"MDPrepDate",33451:"MDPrepTime",33452:"MDFileUnits",33550:"PixelScale",33589:"AdventScale",33590:"AdventRevision",33628:"UIC1Tag",33629:"UIC2Tag",33630:"UIC3Tag",33631:"UIC4Tag",33723:"IPTC-NAA",33918:"IntergraphPacketData",33919:"IntergraphFlagRegisters",33920:"IntergraphMatrix",33921:"INGRReserved",33922:"ModelTiePoint",34016:"Site",34017:"ColorSequence",34018:"IT8Header",34019:"RasterPadding",34020:"BitsPerRunLength",34021:"BitsPerExtendedRunLength",34022:"ColorTable",34023:"ImageColorIndicator",34024:"BackgroundColorIndicator",34025:"ImageColorValue",34026:"BackgroundColorValue",34027:"PixelIntensityRange",34028:"TransparencyIndicator",34029:"ColorCharacterization",34030:"HCUsage",34031:"TrapIndicator",34032:"CMYKEquivalent",34118:"SEMInfo",34152:"AFCP_IPTC",34232:"PixelMagicJBIGOptions",34264:"ModelTransform",34306:"WB_GRGBLevels",34310:"LeafData",34377:"PhotoshopSettings",34665:"ExifOffset",34675:"ICC_Profile",34687:"TIFF_FXExtensions",34688:"MultiProfiles",34689:"SharedData",34690:"T88Options",34732:"ImageLayer",34735:"GeoTiffDirectory",34736:"GeoTiffDoubleParams",34737:"GeoTiffAsciiParams",34850:"ExposureProgram",34852:"SpectralSensitivity",34853:"GPSInfo",34855:"ISO",34856:"Opto-ElectricConvFactor",34857:"Interlace",34858:"TimeZoneOffset",34859:"SelfTimerMode",34864:"SensitivityType",34865:"StandardOutputSensitivity",34866:"RecommendedExposureIndex",34867:"ISOSpeed",34868:"ISOSpeedLatitudeyyy",34869:"ISOSpeedLatitudezzz",34908:"FaxRecvParams",34909:"FaxSubAddress",34910:"FaxRecvTime",34954:"LeafSubIFD",36864:"ExifVersion",36867:"DateTimeOriginal",36868:"CreateDate",37121:"ComponentsConfiguration",37122:"CompressedBitsPerPixel",37377:"ShutterSpeedValue",37378:"ApertureValue",37379:"BrightnessValue",37380:"ExposureCompensation",37381:"MaxApertureValue",37382:"SubjectDistance",37383:"MeteringMode",37384:"LightSource",37385:"Flash",37386:"FocalLength",37387:"FlashEnergy",37388:"SpatialFrequencyResponse",37389:"Noise",37390:"FocalPlaneXResolution",37391:"FocalPlaneYResolution",37392:"FocalPlaneResolutionUnit",37393:"ImageNumber",37394:"SecurityClassification",37395:"ImageHistory",37396:"SubjectArea",37397:"ExposureIndex",37398:"TIFF-EPStandardID",37399:"SensingMethod",37434:"CIP3DataFile",37435:"CIP3Sheet",37436:"CIP3Side",37439:"StoNits",37500:"MakerNote",37510:"UserComment",37520:"SubSecTime",37521:"SubSecTimeOriginal",37522:"SubSecTimeDigitized",37679:"MSDocumentText",37680:"MSPropertySetStorage",37681:"MSDocumentTextPosition",37724:"ImageSourceData",40091:"XPTitle",40092:"XPComment",40093:"XPAuthor",40094:"XPKeywords",40095:"XPSubject",40960:"FlashpixVersion",40961:"ColorSpace",40962:"ExifImageWidth",40963:"ExifImageHeight",40964:"RelatedSoundFile",40965:"InteropOffset",41483:"FlashEnergy",41484:"SpatialFrequencyResponse",41485:"Noise",41486:"FocalPlaneXResolution",41487:"FocalPlaneYResolution",41488:"FocalPlaneResolutionUnit",41489:"ImageNumber",41490:"SecurityClassification",41491:"ImageHistory",41492:"SubjectLocation",41493:"ExposureIndex",41494:"TIFF-EPStandardID",41495:"SensingMethod",41728:"FileSource",41729:"SceneType",41730:"CFAPattern",41985:"CustomRendered",41986:"ExposureMode",41987:"WhiteBalance",41988:"DigitalZoomRatio",41989:"FocalLengthIn35mmFormat",41990:"SceneCaptureType",41991:"GainControl",41992:"Contrast",41993:"Saturation",41994:"Sharpness",41995:"DeviceSettingDescription",41996:"SubjectDistanceRange",42016:"ImageUniqueID",42032:"OwnerName",42033:"SerialNumber",42034:"LensInfo",42035:"LensMake",42036:"LensModel",42037:"LensSerialNumber",42112:"GDALMetadata",42113:"GDALNoData",42240:"Gamma",44992:"ExpandSoftware",44993:"ExpandLens",44994:"ExpandFilm",44995:"ExpandFilterLens",44996:"ExpandScanner",44997:"ExpandFlashLamp",48129:"PixelFormat",48130:"Transformation",48131:"Uncompressed",48132:"ImageType",48256:"ImageWidth",48257:"ImageHeight",48258:"WidthResolution",48259:"HeightResolution",48320:"ImageOffset",48321:"ImageByteCount",48322:"AlphaOffset",48323:"AlphaByteCount",48324:"ImageDataDiscard",48325:"AlphaDataDiscard",50215:"OceScanjobDesc",50216:"OceApplicationSelector",50217:"OceIDNumber",50218:"OceImageLogic",50255:"Annotations",50341:"PrintIM",50560:"USPTOOriginalContentType",50706:"DNGVersion",50707:"DNGBackwardVersion",50708:"UniqueCameraModel",50709:"LocalizedCameraModel",50710:"CFAPlaneColor",50711:"CFALayout",50712:"LinearizationTable",50713:"BlackLevelRepeatDim",50714:"BlackLevel",50715:"BlackLevelDeltaH",50716:"BlackLevelDeltaV",50717:"WhiteLevel",50718:"DefaultScale",50719:"DefaultCropOrigin",50720:"DefaultCropSize",50721:"ColorMatrix1",50722:"ColorMatrix2",50723:"CameraCalibration1",50724:"CameraCalibration2",50725:"ReductionMatrix1",50726:"ReductionMatrix2",50727:"AnalogBalance",50728:"AsShotNeutral",50729:"AsShotWhiteXY",50730:"BaselineExposure",50731:"BaselineNoise",50732:"BaselineSharpness",50733:"BayerGreenSplit",50734:"LinearResponseLimit",50735:"CameraSerialNumber",50736:"DNGLensInfo",50737:"ChromaBlurRadius",50738:"AntiAliasStrength",50739:"ShadowScale",50740:"DNGPrivateData",50741:"MakerNoteSafety",50752:"RawImageSegmentation",50778:"CalibrationIlluminant1",50779:"CalibrationIlluminant2",50780:"BestQualityScale",50781:"RawDataUniqueID",50784:"AliasLayerMetadata",50827:"OriginalRawFileName",50828:"OriginalRawFileData",50829:"ActiveArea",50830:"MaskedAreas",50831:"AsShotICCProfile",50832:"AsShotPreProfileMatrix",50833:"CurrentICCProfile",50834:"CurrentPreProfileMatrix",50879:"ColorimetricReference",50898:"PanasonicTitle",50899:"PanasonicTitle2",50931:"CameraCalibrationSig",50932:"ProfileCalibrationSig",50933:"ProfileIFD",50934:"AsShotProfileName",50935:"NoiseReductionApplied",50936:"ProfileName",50937:"ProfileHueSatMapDims",50938:"ProfileHueSatMapData1",50939:"ProfileHueSatMapData2",50940:"ProfileToneCurve",50941:"ProfileEmbedPolicy",50942:"ProfileCopyright",50964:"ForwardMatrix1",50965:"ForwardMatrix2",50966:"PreviewApplicationName",50967:"PreviewApplicationVersion",50968:"PreviewSettingsName",50969:"PreviewSettingsDigest",50970:"PreviewColorSpace",50971:"PreviewDateTime",50972:"RawImageDigest",50973:"OriginalRawFileDigest",50974:"SubTileBlockSize",50975:"RowInterleaveFactor",50981:"ProfileLookTableDims",50982:"ProfileLookTableData",51008:"OpcodeList1",51009:"OpcodeList2",51022:"OpcodeList3",51041:"NoiseProfile",51043:"TimeCodes",51044:"FrameRate",51058:"TStop",51081:"ReelName",51089:"OriginalDefaultFinalSize",51090:"OriginalBestQualitySize",51091:"OriginalDefaultCropSize",51105:"CameraLabel",51107:"ProfileHueSatMapEncoding",51108:"ProfileLookTableEncoding",51109:"BaselineExposureOffset",51110:"DefaultBlackRender",51111:"NewRawImageDigest",51112:"RawToPreviewGain",51125:"DefaultUserCrop",59932:"Padding",59933:"OffsetSchema",65e3:"OwnerName",65001:"SerialNumber",65002:"Lens",65024:"KDC_IFD",65100:"RawFile",65101:"Converter",65102:"WhiteBalance",65105:"Exposure",65106:"Shadows",65107:"Brightness",65108:"Contrast",65109:"Saturation",65110:"Sharpness",65111:"Smoothness",65112:"MoireFilter"},gps:{0:"GPSVersionID",1:"GPSLatitudeRef",2:"GPSLatitude",3:"GPSLongitudeRef",4:"GPSLongitude",5:"GPSAltitudeRef",6:"GPSAltitude",7:"GPSTimeStamp",8:"GPSSatellites",9:"GPSStatus",10:"GPSMeasureMode",11:"GPSDOP",12:"GPSSpeedRef",13:"GPSSpeed",14:"GPSTrackRef",15:"GPSTrack",16:"GPSImgDirectionRef",17:"GPSImgDirection",18:"GPSMapDatum",19:"GPSDestLatitudeRef",20:"GPSDestLatitude",21:"GPSDestLongitudeRef",22:"GPSDestLongitude",23:"GPSDestBearingRef",24:"GPSDestBearing",25:"GPSDestDistanceRef",26:"GPSDestDistance",27:"GPSProcessingMethod",28:"GPSAreaInformation",29:"GPSDateStamp",30:"GPSDifferential",31:"GPSHPositioningError"}}},29062:(e,t,i)=>{var n=i(58107),a=i(32297),r=[{section:n.GPSIFD,type:2,name:"GPSLatitude",refType:1,refName:"GPSLatitudeRef",posVal:"N"},{section:n.GPSIFD,type:4,name:"GPSLongitude",refType:3,refName:"GPSLongitudeRef",posVal:"E"}],o=[{section:n.SubIFD,type:306,name:"ModifyDate"},{section:n.SubIFD,type:36867,name:"DateTimeOriginal"},{section:n.SubIFD,type:36868,name:"CreateDate"},{section:n.SubIFD,type:306,name:"ModifyDate"}];e.exports={castDegreeValues:function(e,t){r.forEach((function(i){var n=e(i);if(n){var a=e({section:i.section,type:i.refType,name:i.refName})===i.posVal?1:-1,r=(n[0]+n[1]/60+n[2]/3600)*a;t(i,r)}}))},castDateValues:function(e,t){o.forEach((function(i){var n=e(i);if(n){var r=a.parseExifDate(n);void 0!==r&&t(i,r)}}))},simplifyValue:function(e,t){return Array.isArray(e)&&1===(e=e.map((function(e){return 10===t||5===t?e[0]/e[1]:e}))).length&&(e=e[0]),e}}},32297:e=>{function t(e){return parseInt(e,10)}function i(e,i){e=e.map(t),i=i.map(t);var n=e[0],a=e[1]-1,r=e[2],o=i[0],s=i[1],f=i[2];return Date.UTC(n,a,r,o,s,f,0)/1e3}function n(e){var n=e.substr(0,10).split("-"),a=e.substr(11,8).split(":"),r=e.substr(19,6).split(":").map(t),o=3600*r[0]+60*r[1],s=i(n,a);if("number"==typeof(s-=o)&&!isNaN(s))return s}function a(e){var t=e.split(" "),n=i(t[0].split(":"),t[1].split(":"));if("number"==typeof n&&!isNaN(n))return n}e.exports={parseDateWithSpecFormat:a,parseDateWithTimezoneFormat:n,parseExifDate:function(e){var t=19===e.length&&":"===e.charAt(4);return 25===e.length&&"T"===e.charAt(10)?n(e):t?a(e):void 0}}},49473:(e,t,i)=>{const n=i(32047),a=i(61616);class r{constructor(e,t,{width:i,height:n,colorDepth:a,format:r}){if(this.format=r,this.offset=t,this.depth=a,this.stride=function(e){const t=e%4;return t?e+4-t:e}(i*this.depth/8),this.size=this.stride*n,this.data=e.slice(this.offset,this.offset+this.size),this.size!==this.data.byteLength)throw new Error("Truncated bitmap data")}get(e,t,i){const n=this.format.indexOf(i);return 1===this.depth?(this.data[t*this.stride+(e/8|0)]&1<<7-e%8*1)>>7-e%8*1:2===this.depth?(this.data[t*this.stride+(e/4|0)]&3<<6-e%4*2)>>>6-e%4*2:4===this.depth?(this.data[t*this.stride+(e/2|0)]&15<<4-e%2*4)>>>4-e%2*4:this.data[t*this.stride+e*(this.depth/8)+n]}}e.exports=function(e,{width:t=0,height:i=0,icon:o=!1}={}){const s=n(e);let f,m,p,u,c;o?(f=s.getUint32(0,!0),m=s.getUint32(4,!0)/1|0,p=s.getUint32(8,!0)/2|0,u=s.getUint16(14,!0),c=s.getUint32(32,!0)):(function(e){if(19778!==e)throw new Error(`Invalid magic byte 0x${e.toString(16)}`)}(s.getUint16(0,!0)),f=14+s.getUint32(14,!0),m=s.getUint32(18,!0),p=s.getUint32(22,!0),u=s.getUint16(28,!0),c=s.getUint32(46,!0)),0===c&&u<=8&&(c=1<<u);const l=0===m?t:m,h=0===p?i:p,d=new Uint8Array(s.buffer,s.byteOffset+f,s.byteLength-f),g=c?function(e,{width:t,height:i,colorDepth:n,colorCount:a,icon:o}){if(8!==n&&4!==n&&2!==n&&1!==n)throw new Error(`A color depth of ${n} is not supported`);const s=new r(e,0,{width:a,height:1,colorDepth:32,format:"BGRA"}),f=new r(e,s.offset+s.size,{width:t,height:i,colorDepth:n,format:"C"}),m=o?new r(e,f.offset+f.size,{width:t,height:i,colorDepth:1,format:"A"}):null,p=new Uint8Array(t*i*4);let u=0;for(let e=0;e<i;e++)for(let n=0;n<t;n++){const t=f.get(n,i-e-1,"C");p[u++]=s.get(t,0,"R"),p[u++]=s.get(t,0,"G"),p[u++]=s.get(t,0,"B"),p[u++]=m&&m.get(n,i-e-1,"A")?0:255}return new Uint8ClampedArray(p.buffer,p.byteOffset,p.byteLength)}(d,{width:l,height:h,colorDepth:u,colorCount:c,icon:o}):function(e,{width:t,height:i,colorDepth:n,icon:a}){if(32!==n&&24!==n)throw new Error(`A color depth of ${n} is not supported`);const o=new r(e,0,{width:t,height:i,colorDepth:n,format:"BGRA"}),s=24===n&&a?new r(e,o.offset+o.size,{width:t,height:i,colorDepth:1,format:"A"}):null,f=new Uint8Array(t*i*4);let m=0;for(let e=0;e<i;e++)for(let a=0;a<t;a++)f[m++]=o.get(a,i-e-1,"R"),f[m++]=o.get(a,i-e-1,"G"),f[m++]=o.get(a,i-e-1,"B"),f[m++]=32===n?o.get(a,i-e-1,"A"):s&&s.get(a,i-e-1,"A")?0:255;return new Uint8ClampedArray(f.buffer,f.byteOffset,f.byteLength)}(d,{width:l,height:h,colorDepth:u,icon:o});return Object.assign(new a(g,l,h),{colorDepth:u})}},53846:(e,t,i)=>{"use strict";const n=i(80363),a=i(5707),r={fromFile:async function(e){const t=await n.fromFile(e);try{return await a.fromTokenizer(t)}finally{await t.close()}}};Object.assign(r,a),Object.defineProperty(r,"extensions",{get:()=>a.extensions}),Object.defineProperty(r,"mimeTypes",{get:()=>a.mimeTypes}),e.exports=r},58107:e=>{function t(e,t){switch(e){case 1:return t.nextUInt8();case 3:case 8:return t.nextUInt16();case 4:case 9:return t.nextUInt32();case 5:return[t.nextUInt32(),t.nextUInt32()];case 6:return t.nextInt8();case 10:return[t.nextInt32(),t.nextInt32()];case 11:return t.nextFloat();case 12:return t.nextDouble();default:throw new Error("Invalid format while decoding: "+e)}}function i(e,i){var n,a,r=i.nextUInt16(),o=i.nextUInt16(),s=function(e){switch(e){case 1:case 2:case 6:case 7:return 1;case 3:case 8:return 2;case 4:case 9:case 11:return 4;case 5:case 10:case 12:return 8;default:return 0}}(o),f=i.nextUInt32(),m=s*f;if(m>4&&(i=e.openWithOffset(i.nextUInt32())),2===o){var p=(n=i.nextString(f)).indexOf("\0");-1!==p&&(n=n.substr(0,p))}else if(7===o)n=i.nextBuffer(f);else if(0!==o)for(n=[],a=0;a<f;++a)n.push(t(o,i));return m<4&&i.skip(4-m),[r,n,o]}function n(e,t,n){var a,r,o=t.nextUInt16();for(r=0;r<o;++r)n((a=i(e,t))[0],a[1],a[2])}e.exports={IFD0:1,IFD1:2,GPSIFD:3,SubIFD:4,InteropIFD:5,parseTags:function(e,t){var i,a,r,o;try{i=function(e){if("Exif\0\0"!==e.nextString(6))throw new Error("Invalid EXIF header");var t=e.mark(),i=e.nextUInt16();if(18761===i)e.setBigEndian(!1);else{if(19789!==i)throw new Error("Invalid TIFF header");e.setBigEndian(!0)}if(42!==e.nextUInt16())throw new Error("Invalid TIFF data");return t}(e)}catch(e){return!1}var s=i.openWithOffset(e.nextUInt32()),f=this.IFD0;n(i,s,(function(e,i,n){switch(e){case 34853:r=i[0];break;case 34665:a=i[0];break;default:t(f,e,i,n)}}));var m=s.nextUInt32();if(0!==m){var p=i.openWithOffset(m);n(i,p,t.bind(null,this.IFD1))}if(r){var u=i.openWithOffset(r);n(i,u,t.bind(null,this.GPSIFD))}if(a){var c=i.openWithOffset(a),l=this.InteropIFD;n(i,c,(function(e,i,n){40965===e?o=i[0]:t(l,e,i,n)}))}if(o){var h=i.openWithOffset(o);n(i,h,t.bind(null,this.InteropIFD))}return!0}}},62335:e=>{e.exports={parseSections:function(e,t){var i,n;for(e.setBigEndian(!0);e.remainingLength()>0&&218!==n;){if(255!==e.nextUInt8())throw new Error("Invalid JPEG section offset");i=(n=e.nextUInt8())>=208&&n<=217||218===n?0:e.nextUInt16()-2,t(n,e.branch(0,i)),e.skip(i)}},getSizeFromSOFSection:function(e){return e.skip(1),{height:e.nextUInt16(),width:e.nextUInt16()}},getSectionName:function(e){var t,i;switch(e){case 216:t="SOI";break;case 196:t="DHT";break;case 219:t="DQT";break;case 221:t="DRI";break;case 218:t="SOS";break;case 254:t="COM";break;case 217:t="EOI";break;default:e>=224&&e<=239?(t="APP",i=e-224):e>=192&&e<=207&&196!==e&&200!==e&&204!==e?(t="SOF",i=e-192):e>=208&&e<=215&&(t="RST",i=e-208)}var n={name:t};return"number"==typeof i&&(n.index=i),n}}},69049:e=>{"use strict";e.exports=function(e){for(var t=[],i=e.length,n=0;n<i;n++){var a=e.charCodeAt(n);if(a>=55296&&a<=56319&&i>n+1){var r=e.charCodeAt(n+1);r>=56320&&r<=57343&&(a=1024*(a-55296)+r-56320+65536,n+=1)}a<128?t.push(a):a<2048?(t.push(a>>6|192),t.push(63&a|128)):a<55296||a>=57344&&a<65536?(t.push(a>>12|224),t.push(a>>6&63|128),t.push(63&a|128)):a>=65536&&a<=1114111?(t.push(a>>18|240),t.push(a>>12&63|128),t.push(a>>6&63|128),t.push(63&a|128)):t.push(239,191,189)}return new Uint8Array(t).buffer}},71664:e=>{"use strict";e.exports={extensions:["jpg","png","apng","gif","webp","flif","xcf","cr2","cr3","orf","arw","dng","nef","rw2","raf","tif","bmp","icns","jxr","psd","indd","zip","tar","rar","gz","bz2","7z","dmg","mp4","mid","mkv","webm","mov","avi","mpg","mp2","mp3","m4a","oga","ogg","ogv","opus","flac","wav","spx","amr","pdf","epub","exe","swf","rtf","wasm","woff","woff2","eot","ttf","otf","ico","flv","ps","xz","sqlite","nes","crx","xpi","cab","deb","ar","rpm","Z","lz","cfb","mxf","mts","blend","bpg","docx","pptx","xlsx","3gp","3g2","jp2","jpm","jpx","mj2","aif","qcp","odt","ods","odp","xml","mobi","heic","cur","ktx","ape","wv","dcm","ics","glb","pcap","dsf","lnk","alias","voc","ac3","m4v","m4p","m4b","f4v","f4p","f4b","f4a","mie","asf","ogm","ogx","mpc","arrow","shp","aac","mp1","it","s3m","xm","ai","skp","avif","eps","lzh","pgp","asar","stl","chm","3mf","zst","jxl","vcf"],mimeTypes:["image/jpeg","image/png","image/gif","image/webp","image/flif","image/x-xcf","image/x-canon-cr2","image/x-canon-cr3","image/tiff","image/bmp","image/vnd.ms-photo","image/vnd.adobe.photoshop","application/x-indesign","application/epub+zip","application/x-xpinstall","application/vnd.oasis.opendocument.text","application/vnd.oasis.opendocument.spreadsheet","application/vnd.oasis.opendocument.presentation","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.presentationml.presentation","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/zip","application/x-tar","application/x-rar-compressed","application/gzip","application/x-bzip2","application/x-7z-compressed","application/x-apple-diskimage","application/x-apache-arrow","video/mp4","audio/midi","video/x-matroska","video/webm","video/quicktime","video/vnd.avi","audio/vnd.wave","audio/qcelp","audio/x-ms-asf","video/x-ms-asf","application/vnd.ms-asf","video/mpeg","video/3gpp","audio/mpeg","audio/mp4","audio/opus","video/ogg","audio/ogg","application/ogg","audio/x-flac","audio/ape","audio/wavpack","audio/amr","application/pdf","application/x-msdownload","application/x-shockwave-flash","application/rtf","application/wasm","font/woff","font/woff2","application/vnd.ms-fontobject","font/ttf","font/otf","image/x-icon","video/x-flv","application/postscript","application/eps","application/x-xz","application/x-sqlite3","application/x-nintendo-nes-rom","application/x-google-chrome-extension","application/vnd.ms-cab-compressed","application/x-deb","application/x-unix-archive","application/x-rpm","application/x-compress","application/x-lzip","application/x-cfb","application/x-mie","application/mxf","video/mp2t","application/x-blender","image/bpg","image/jp2","image/jpx","image/jpm","image/mj2","audio/aiff","application/xml","application/x-mobipocket-ebook","image/heif","image/heif-sequence","image/heic","image/heic-sequence","image/icns","image/ktx","application/dicom","audio/x-musepack","text/calendar","text/vcard","model/gltf-binary","application/vnd.tcpdump.pcap","audio/x-dsf","application/x.ms.shortcut","application/x.apple.alias","audio/x-voc","audio/vnd.dolby.dd-raw","audio/x-m4a","image/apng","image/x-olympus-orf","image/x-sony-arw","image/x-adobe-dng","image/x-nikon-nef","image/x-panasonic-rw2","image/x-fujifilm-raf","video/x-m4v","video/3gpp2","application/x-esri-shape","audio/aac","audio/x-it","audio/x-s3m","audio/x-xm","video/MP1S","video/MP2P","application/vnd.sketchup.skp","image/avif","application/x-lzh-compressed","application/pgp-encrypted","application/x-asar","model/stl","application/vnd.ms-htmlhelp","model/3mf","image/jxl","application/zstd"]}},74942:(e,t,i)=>{var n=i(62335),a=i(58107),r=i(29062);function o(e,t,i,n,a,r,o){this.startMarker=e,this.tags=t,this.imageSize=i,this.thumbnailOffset=n,this.thumbnailLength=a,this.thumbnailType=r,this.app1Offset=o}function s(e){this.stream=e,this.flags={readBinaryTags:!1,resolveTagNames:!0,simplifyValues:!0,imageSize:!0,hidePointers:!0,returnTags:!0}}o.prototype={hasThumbnail:function(e){return!(!this.thumbnailOffset||!this.thumbnailLength||"string"==typeof e&&("image/jpeg"===e.toLowerCase().trim()?6!==this.thumbnailType:"image/tiff"!==e.toLowerCase().trim()||1!==this.thumbnailType))},getThumbnailOffset:function(){return this.app1Offset+6+this.thumbnailOffset},getThumbnailLength:function(){return this.thumbnailLength},getThumbnailBuffer:function(){return this._getThumbnailStream().nextBuffer(this.thumbnailLength)},_getThumbnailStream:function(){return this.startMarker.openWithOffset(this.getThumbnailOffset())},getImageSize:function(){return this.imageSize},getThumbnailSize:function(){var e,t=this._getThumbnailStream();return n.parseSections(t,(function(t,i){"SOF"===n.getSectionName(t).name&&(e=n.getSizeFromSOFSection(i))})),e}},s.prototype={enableBinaryFields:function(e){return this.flags.readBinaryTags=!!e,this},enablePointers:function(e){return this.flags.hidePointers=!e,this},enableTagNames:function(e){return this.flags.resolveTagNames=!!e,this},enableImageSize:function(e){return this.flags.imageSize=!!e,this},enableReturnTags:function(e){return this.flags.returnTags=!!e,this},enableSimpleValues:function(e){return this.flags.simplifyValues=!!e,this},parse:function(){var e,t,s,f,m,p,u,c,l,h=this.stream.mark(),d=h.openWithOffset(0),g=this.flags;return g.resolveTagNames&&(u=i(26393)),g.resolveTagNames?(e={},c=function(t){return e[t.name]},l=function(t,i){e[t.name]=i}):(e=[],c=function(t){var i;for(i=0;i<e.length;++i)if(e[i].type===t.type&&e[i].section===t.section)return e.value},l=function(t,i){var n;for(n=0;n<e.length;++n)if(e[n].type===t.type&&e[n].section===t.section)return void(e.value=i)}),n.parseSections(d,(function(i,o){var c=o.offsetFrom(h);225===i?a.parseTags(o,(function(t,i,n,o){if(g.readBinaryTags||7!==o){if(513===i){if(s=n[0],g.hidePointers)return}else if(514===i){if(f=n[0],g.hidePointers)return}else if(259===i&&(m=n[0],g.hidePointers))return;if(g.returnTags)if(g.simplifyValues&&(n=r.simplifyValue(n,o)),g.resolveTagNames){var p=(t===a.GPSIFD?u.gps:u.exif)[i];p||(p=u.exif[i]),e.hasOwnProperty(p)||(e[p]=n)}else e.push({section:t,type:i,value:n})}}))&&(p=c):g.imageSize&&"SOF"===n.getSectionName(i).name&&(t=n.getSizeFromSOFSection(o))})),g.simplifyValues&&(r.castDegreeValues(c,l),r.castDateValues(c,l)),new o(h,e,t,s,f,m,p)}},e.exports=s},76320:e=>{"use strict";var t={single_source_shortest_paths:function(e,i,n){var a={},r={};r[i]=0;var o,s,f,m,p,u,c,l=t.PriorityQueue.make();for(l.push(i,0);!l.empty();)for(f in s=(o=l.pop()).value,m=o.cost,p=e[s]||{})p.hasOwnProperty(f)&&(u=m+p[f],c=r[f],(void 0===r[f]||c>u)&&(r[f]=u,l.push(f,u),a[f]=s));if(void 0!==n&&void 0===r[n]){var h=["Could not find a path from ",i," to ",n,"."].join("");throw new Error(h)}return a},extract_shortest_path_from_predecessor_list:function(e,t){for(var i=[],n=t;n;)i.push(n),e[n],n=e[n];return i.reverse(),i},find_path:function(e,i,n){var a=t.single_source_shortest_paths(e,i,n);return t.extract_shortest_path_from_predecessor_list(a,n)},PriorityQueue:{make:function(e){var i,n=t.PriorityQueue,a={};for(i in e=e||{},n)n.hasOwnProperty(i)&&(a[i]=n[i]);return a.queue=[],a.sorter=e.sorter||n.default_sorter,a},default_sorter:function(e,t){return e.cost-t.cost},push:function(e,t){var i={value:e,cost:t};this.queue.push(i),this.queue.sort(this.sorter)},pop:function(){return this.queue.shift()},empty:function(){return 0===this.queue.length}}};e.exports=t},80707:(e,t,i)=>{const n=i(32047),a=i(49473),r=i(61616);function o(e,t){return 2303741511===e.getUint32(t+0)&&218765834===e.getUint32(t+4)}function s(e,t){const i=e.getUint8(t+24),n=e.getUint8(t+25);if(0===n)return 1*i;if(2===n)return 3*i;if(3===n)return 1*i;if(4===n)return 2*i;if(6===n)return 4*i;throw new Error("Invalid PNG colorType")}function f(e,t){return e.getUint32(t+16,!1)}function m(e,t){return e.getUint32(t+20,!1)}e.exports=function(e){const t=n(e);if(t.byteLength<6)throw new Error("Truncated header");if(o(t,0))return[{bpp:s(t,0),data:new Uint8Array(t.buffer,t.byteOffset,t.byteLength),height:m(t,0),hotspot:null,type:"png",width:f(t,0)}];if(0!==t.getUint16(0,!0))throw new Error("Invalid magic bytes");const i=t.getUint16(2,!0);if(1!==i&&2!==i)throw new Error("Invalid image type");const p=t.getUint16(4,!0);if(t.byteLength<6+16*p)throw new Error("Truncated image list");return Array.from({length:p},((e,n)=>{const p=t.getUint8(6+16*n+0),u=t.getUint8(6+16*n+1),c=t.getUint32(6+16*n+8,!0),l=t.getUint32(6+16*n+12,!0),h=2!==i?null:{x:t.getUint16(6+16*n+4,!0),y:t.getUint16(6+16*n+6,!0)};if(o(t,l))return{bpp:s(t,l),data:new Uint8Array(t.buffer,t.byteOffset+l,c),height:m(t,l),hotspot:h,type:"png",width:f(t,l)};const d=new Uint8Array(t.buffer,t.byteOffset+l,c),g=a(d,{width:p,height:u,icon:!0}),x={bpp:g.colorDepth,hotspot:h,type:"bmp"};return Object.assign(new r(g.data,g.width,g.height),x)}))}},83779:e=>{function t(e,t,i,n){this.buffer=e,this.offset=t||0,i="number"==typeof i?i:e.length,this.endPosition=this.offset+i,this.setBigEndian(n)}t.prototype={setBigEndian:function(e){this.bigEndian=!!e},nextUInt8:function(){var e=this.buffer.readUInt8(this.offset);return this.offset+=1,e},nextInt8:function(){var e=this.buffer.readInt8(this.offset);return this.offset+=1,e},nextUInt16:function(){var e=this.bigEndian?this.buffer.readUInt16BE(this.offset):this.buffer.readUInt16LE(this.offset);return this.offset+=2,e},nextUInt32:function(){var e=this.bigEndian?this.buffer.readUInt32BE(this.offset):this.buffer.readUInt32LE(this.offset);return this.offset+=4,e},nextInt16:function(){var e=this.bigEndian?this.buffer.readInt16BE(this.offset):this.buffer.readInt16LE(this.offset);return this.offset+=2,e},nextInt32:function(){var e=this.bigEndian?this.buffer.readInt32BE(this.offset):this.buffer.readInt32LE(this.offset);return this.offset+=4,e},nextFloat:function(){var e=this.bigEndian?this.buffer.readFloatBE(this.offset):this.buffer.readFloatLE(this.offset);return this.offset+=4,e},nextDouble:function(){var e=this.bigEndian?this.buffer.readDoubleBE(this.offset):this.buffer.readDoubleLE(this.offset);return this.offset+=8,e},nextBuffer:function(e){var t=this.buffer.slice(this.offset,this.offset+e);return this.offset+=e,t},remainingLength:function(){return this.endPosition-this.offset},nextString:function(e){var t=this.buffer.toString("utf8",this.offset,this.offset+e);return this.offset+=e,t},mark:function(){var e=this;return{openWithOffset:function(i){return i=(i||0)+this.offset,new t(e.buffer,i,e.endPosition-i,e.bigEndian)},offset:this.offset}},offsetFrom:function(e){return this.offset-e.offset},skip:function(e){this.offset+=e},branch:function(e,i){return i="number"==typeof i?i:this.endPosition-(this.offset+e),new t(this.buffer,this.offset+e,i,this.bigEndian)}},e.exports=t},84628:e=>{function t(e,t,i,n,a,r){this.global=a,t=t||0,i=i||e.byteLength-t,this.arrayBuffer=e.slice(t,t+i),this.view=new a.DataView(this.arrayBuffer,0,this.arrayBuffer.byteLength),this.setBigEndian(n),this.offset=0,this.parentOffset=(r||0)+t}t.prototype={setBigEndian:function(e){this.littleEndian=!e},nextUInt8:function(){var e=this.view.getUint8(this.offset);return this.offset+=1,e},nextInt8:function(){var e=this.view.getInt8(this.offset);return this.offset+=1,e},nextUInt16:function(){var e=this.view.getUint16(this.offset,this.littleEndian);return this.offset+=2,e},nextUInt32:function(){var e=this.view.getUint32(this.offset,this.littleEndian);return this.offset+=4,e},nextInt16:function(){var e=this.view.getInt16(this.offset,this.littleEndian);return this.offset+=2,e},nextInt32:function(){var e=this.view.getInt32(this.offset,this.littleEndian);return this.offset+=4,e},nextFloat:function(){var e=this.view.getFloat32(this.offset,this.littleEndian);return this.offset+=4,e},nextDouble:function(){var e=this.view.getFloat64(this.offset,this.littleEndian);return this.offset+=8,e},nextBuffer:function(e){var t=this.arrayBuffer.slice(this.offset,this.offset+e);return this.offset+=e,t},remainingLength:function(){return this.arrayBuffer.byteLength-this.offset},nextString:function(e){var t=this.arrayBuffer.slice(this.offset,this.offset+e);return t=String.fromCharCode.apply(null,new this.global.Uint8Array(t)),this.offset+=e,t},mark:function(){var e=this;return{openWithOffset:function(i){return i=(i||0)+this.offset,new t(e.arrayBuffer,i,e.arrayBuffer.byteLength-i,!e.littleEndian,e.global,e.parentOffset)},offset:this.offset,getParentOffset:function(){return e.parentOffset}}},offsetFrom:function(e){return this.parentOffset+this.offset-(e.offset+e.getParentOffset())},skip:function(e){this.offset+=e},branch:function(e,i){return i="number"==typeof i?i:this.arrayBuffer.byteLength-(this.offset+e),new t(this.arrayBuffer,this.offset+e,i,!this.littleEndian,this.global,this.parentOffset)}},e.exports=t},86760:(e,t)=>{"use strict";t.stringToBytes=e=>[...e].map((e=>e.charCodeAt(0))),t.tarHeaderChecksumMatches=(e,t=0)=>{const i=parseInt(e.toString("utf8",148,154).replace(/\0.*$/,"").trim(),8);if(isNaN(i))return!1;let n=256;for(let i=t;i<t+148;i++)n+=e[i];for(let i=t+156;i<t+512;i++)n+=e[i];return i===n},t.uint32SyncSafeToken={get:(e,t)=>127&e[t+3]|e[t+2]<<7|e[t+1]<<14|e[t]<<21,len:4}},87833:(e,t,i)=>{var n=i(74942);e.exports={create:function(e,t){if(e instanceof(t=t||(0,eval)("this")).ArrayBuffer){var a=i(84628);return new n(new a(e,0,e.byteLength,!0,t))}var r=i(83779);return new n(new r(e,0,e.length,!0))}}}}]);
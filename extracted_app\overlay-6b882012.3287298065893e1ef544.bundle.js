"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1577],{37294:(e,t,r)=>{r.d(t,{D:()=>l,q:()=>o});var o,a=r(48379),s=r(38777);!function(e){e.Invalid="invalid",e.Unsure="unsure",e.Valid="valid"}(o||(o={}));const n=new Map([["gmail","com"],["outlook","com"],["hotmail","com"],["live","com"],["yahoo","com"]]),c=new Map([["gemail","gmail"],["gmial","gmail"],["gmai","gmail"]]),i=new Map([["c","com"],["ccom","com"],["cim","com"],["cob","com"],["coim","com"],["colm","com"],["com2","com"],["coma","com"],["comcom","com"],["come","com"],["comf","com"],["comi","com"],["comk","com"],["coml","com"],["comm","com"],["coms","com"],["con","com"],["coom","com"],["coom","com"],["cpm","com"],["dcom","com"],["ney","net"],["nt","net"],["ocm","com"],["r","ru"],["vcom","com"],["vom","com"],["xom","com"],["yk","uk"]]);class l{async validate(e){if("string"!=typeof e)return{status:o.Invalid};const t=e.split("@");if(2!==t.length)return{status:o.Invalid};const r=t[0].replace(/\s/g,""),l=t[1].replace(/\s/g,"").replace(/[。｡︒]/g,".").toLocaleLowerCase();if(0===r.length||l.length<3||r.length+l.length>255)return{status:o.Invalid};if(!l.includes("."))return{status:o.Invalid};const p=`${r}@${l}`,m=l.split("."),d=m.pop(),h=m.join(".");let u=i.get(d??"");const g=c.get(h);return u||g?(g&&!u&&(u=n.get(h)),{status:o.Unsure,recommendation:`${r}@${g||h}.${u||d}`,normalized:p}):/^[\p{L}\p{N}_+.-]+$/gu.test(r)&&/^[\p{L}\p{N}.-]+$/gu.test(l)?{status:await Promise.race([this.#e(a.tl(l)),(0,s.Wn)(3e3).then((()=>!1))])?o.Valid:o.Unsure,normalized:p}:{status:o.Unsure,normalized:p}}async#e(e){const t=new URL("https://*******/dns-query");t.searchParams.set("type","MX"),t.searchParams.set("name",e);try{const e=await(await fetch(t,{headers:{Accept:"application/dns-json"}})).json();return 0===e.Status&&!!e.Answer?.some((e=>!e.data.endsWith(" .")&&!e.data.endsWith(" localhost.")))}catch{return!1}}}},"resources/value-converters/flags":(e,t,r)=>{r.r(t),r.d(t,{FlagIconSvgValueConverter:()=>w,flags:()=>v});var o=r(34480),a=r(87680),s=r(43627),n=r(73773),c=r(35552),i=r(22460),l=r(77091),p=r(1081),m=r(82419),d=r(86666),h=r(21431),u=r(10384),g=r(89384),f=r(73474),b=r(41772);const v=new Map([["en-US",o],["pt-BR",a],["zh-CN",s],["de-DE",n],["es-ES",c],["fr-FR",i],["hi-IN",p],["id-ID",l],["it-IT",m],["ja-JP",d],["ko-KR",h],["pl-PL",u],["th-TH",g],["tr-TR",f]]);class w{toView(e){return b.d.includes(e)&&v.get(e)||""}}},"resources/value-converters/game":(e,t,r)=>{r.r(t),r.d(t,{GameFlagsValueConverter:()=>s});var o=r(24008),a=r(70236);class s{toView(e,...t){return(0,a.br)("number"==typeof e?e:e.flags,t.map((e=>o.rT[e])))}}},"services/bugsnag/index":(e,t,r)=>{r.r(t),r.d(t,{configure:()=>y,isConfigured:()=>k,report:()=>_,setEnabled:()=>x,setMetadata:()=>P});var o=r(65993),a=r.n(o),s=r(96610),n=r(20770),c=r(45660),i=r(59239),l=r(19072),p=r(17724),m=r(70236),d=r(88849);const h=(0,s.getLogger)("bugsnag");let u,g=null,f=!0;const b=new Set(["Breadcrumb not attached due to onBreadcrumb callback","Event not sent due to onError callback"]),v=[{type:"request",metadata:{status:200,request:/\.svg$/}},{type:"request",metadata:{request:/^GET data:/}},{type:"request",metadata:{status:304,request:/\/v3\/catalog$/}},{type:"log",metadata:{"[1]":"EVENT_UPDATE_STATE_CHANGED","[2]":/^(checking|not-available)$/}},{type:"log",metadata:{"[1]":/^(-> )?ACTION_CHECK_FOR_UPDATE$/}}];function w(e){return(...t)=>{b.has(t[0])||h[e](...t)}}function y(e,t){t.apiKey&&(g=[{pattern:M("file:///"),replacement:""},{pattern:M(encodeURIComponent("file:///")),replacement:""},{pattern:M(t.appPath),replacement:"~"},{pattern:M(encodeURIComponent(t.appPath)),replacement:"~"},{pattern:M(encodeURI(t.appPath)),replacement:"~"},{pattern:M(t.appPath.replaceAll("\\","/")),replacement:"~"},{pattern:M(JSON.stringify(t.appPath).slice(1,-1)),replacement:"~"},{pattern:M(encodeURIComponent(t.appPath.replaceAll("\\","/"))),replacement:"~"},{pattern:M(encodeURI(t.appPath.replaceAll("\\","/"))),replacement:"~"}],u=e.container,a().start({apiKey:t.apiKey,appVersion:t.appVersion,releaseStage:t.releaseStage,autoTrackSessions:!1,projectRoot:"~",metadata:{app:{path:t.appPath},device:{locale:t.locale,arch:t.osArch}},logger:{debug:w("debug"),info:w("info"),warn:w("warn"),error:w("error")},onBreadcrumb:[$,C,U],onError:[$,T,E,j,S,I,L]}))}function x(e){f=e}function k(){return null!==g}function P(e,t,r){k()&&a().addMetadata(e,t,r)}function $(){return f}function T(e){return"object"!=typeof e.originalError||!0!==e.originalError.doNotReport}function E(e){return!(0,p.hb)(e.originalError)}function _(e,t,r){k()?(r&&(e.customMetadata=r),a().notify(e,t)):console.error(e)}function M(e){return new RegExp(e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"ig")}function R(e){return"string"==typeof e&&g?.forEach((t=>e=e.replace(t.pattern,t.replacement))),e}function C(e){return"object"!=typeof e.metadata||null===e.metadata||v.every((t=>t.type!==e.type||Object.keys(t.metadata).some((r=>{const o=t.metadata[r],a=e.metadata[r];return void 0===a||(o instanceof RegExp?!["string","number"].includes(typeof a)||!o.test(a.toString()):a!==o)}))))}function U(e){e.message=R(e.message);const t=e.metadata;"object"==typeof t&&null!==t&&Object.keys(t).forEach((e=>t[e]=R(t[e])))}function S(e){e.context=R(e.context??""),e.request.url=R(e.request.url??""),e.errors.forEach((e=>{e.errorMessage=R(e.errorMessage),e.stacktrace.forEach((e=>e.file=R(e.file)))}))}async function I(e){const t=(0,l.S)(),r=t.info,o=await t.getMemoryInfo();if(e.addMetadata("device",{hostname:r.osHostname,cpu:r.deviceCpuModel,cpuCount:r.deviceCpuCount,memoryTotal:(0,d.z3)(o.total),memoryFree:(0,d.z3)(o.free)}),!a().getMetadata("device","antivirus")){const r=(await t.getInstalledAvProducts()??["<error>"]).sort();a().addMetadata("device","antivirus",r),e.addMetadata("device","antivirus",r)}return!0}function L(e){const t=e.originalError;t?.customMetadata&&e.addMetadata("Custom",t.customMetadata)}async function j(){const e=await async function(){const e=u.get(n.il);return await(e?.state.pipe((0,c.$)(),(0,i.E)("account")).toPromise())}();return!e||!!e.subscription===(0,m.Lt)(e.flags,512)}},"settings/resources/elements/theme-selector":(e,t,r)=>{r.r(t),r.d(t,{ThemeSelector:()=>i});var o=r(15215),a=r("aurelia-framework"),s=r(20770),n=r(54995),c=r(48881);let i=class{#t;constructor(e){this.source="settings",this.#t=e}selectTheme(e){("default"===e||this.isPro)&&this.#t.dispatch(c.Kc,{theme:e},this.source)}get isPro(){return!!this.account?.subscription}};(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",String)],i.prototype,"source",void 0),(0,o.Cg)([(0,a.computedFrom)("account"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],i.prototype,"isPro",null),i=(0,o.Cg)([(0,n.m6)({selectors:{selectedTheme:(0,n.$t)((e=>e.settings?.theme)),account:(0,n.$t)((e=>e.account))}}),(0,a.autoinject)(),(0,o.Sn)("design:paramtypes",[s.il])],i)},"settings/resources/elements/theme-selector.html":(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var o=r(14385),a=r.n(o),s=new URL(r(52280),r.b),n=new URL(r(59843),r.b),c=new URL(r(90188),r.b),i=new URL(r(80251),r.b),l=new URL(r(44170),r.b);const p='<template> <require from="./theme-selector.scss"></require> <div class="theme-selector"> <button class="theme ${selectedTheme == \'default\' ? \'selected\' : \'\'}" click.delegate="selectTheme(\'default\')"> <img class="thumbnail" src="'+a()(s)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.default\' | i18n}</span> </span> </footer> </button> <button class="theme ${selectedTheme == \'purple-pro\' ? \'selected\' : \'\'} ${isPro ? \'\' : \'trigger-pro-cta\'}" click.delegate="selectTheme(\'purple-pro\')" pro-cta="trigger: theme_selector_item; disabled.bind: isPro; feature: themes"> <img class="thumbnail" src="'+a()(n)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.purple\' | i18n}</span> </span> <span class="pro-badge">Pro</span> </footer> </button> <button class="theme ${selectedTheme == \'green-pro\' ? \'selected\' : \'\'} ${isPro ? \'\' : \'trigger-pro-cta\'}" click.delegate="selectTheme(\'green-pro\')" pro-cta="trigger: theme_selector_item; disabled.bind: isPro; feature: themes"> <img class="thumbnail" src="'+a()(c)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.green\' | i18n}</span> </span> <span class="pro-badge">Pro</span> </footer> </button> <button class="theme ${selectedTheme == \'orange-pro\' ? \'selected\' : \'\'} ${isPro ? \'\' : \'trigger-pro-cta\'}" click.delegate="selectTheme(\'orange-pro\')" pro-cta="trigger: theme_selector_item; disabled.bind: isPro; feature: themes"> <img class="thumbnail" src="'+a()(i)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.orange\' | i18n}</span> </span> <span class="pro-badge">Pro</span> </footer> </button> <button class="theme ${selectedTheme == \'pro\' ? \'selected\' : \'\'} ${isPro ? \'\' : \'trigger-pro-cta\'}" click.delegate="selectTheme(\'pro\')" pro-cta="trigger: theme_selector_item; disabled.bind: isPro; feature: themes"> <img class="thumbnail" src="'+a()(l)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.black\' | i18n}</span> </span> <span class="pro-badge">Pro</span> </footer> </button> </div> </template> '},"settings/resources/elements/theme-selector.scss":(e,t,r)=>{r.r(t),r.d(t,{default:()=>d});var o=r(31601),a=r.n(o),s=r(76314),n=r.n(s),c=r(4417),i=r.n(c),l=new URL(r(81206),r.b),p=n()(a()),m=i()(l);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.theme-selector{display:flex;flex-wrap:wrap;margin-right:-20px}.theme-selector .theme{background:rgba(0,0,0,0);border:0;padding:0;margin-bottom:20px;margin-right:20px}.theme-selector .theme .thumbnail{width:124px;height:77px;border-radius:5px;overflow:hidden;transition:border-color .15s}.theme-selector .theme footer{display:flex;flex-direction:row;align-items:center;margin-top:10px}.theme-selector .theme footer .wrapper{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;flex:1}.theme-selector .theme footer .wrapper,.theme-selector .theme footer .wrapper *{cursor:pointer}.theme-selector .theme footer .wrapper>*:first-child{margin-right:9px}.theme-selector .theme footer .wrapper .radio{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;border-radius:50%;display:inline-flex;align-items:center;justify-content:center;width:15px;height:15px;border-color:rgba(255,255,255,.25);background-color:rgba(0,0,0,0)}.theme-selector .theme footer .wrapper .radio,.theme-selector .theme footer .wrapper .radio *{cursor:pointer}.theme-selector .theme footer .wrapper .radio:checked:before{opacity:1}.theme-selector .theme footer .wrapper .radio:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${m});mask:url(${m})}.theme-selector .theme footer .wrapper .radio:before{width:9px;height:9px;border-radius:50%;-webkit-mask-box-image:none;mask:none;position:relative;top:initial;left:initial;box-shadow:inset 0 0 0 1px var(--theme--background);transform:scale(1)}.theme-selector .theme footer .wrapper .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color)}.theme-selector .theme footer .wrapper .label,.theme-selector .theme footer .wrapper .label *{cursor:pointer}.theme-selector .theme footer .pro-badge{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;display:flex;flex:0 0 auto}@media(forced-colors: active){body:not(.override-contrast-mode) .theme-selector .theme footer .pro-badge{border:1px solid #fff}}.theme-selector .theme:hover .thumbnail,.theme-selector .theme.selected .thumbnail{border:1px solid var(--theme--highlight)}.theme-selector .theme:hover footer .radio,.theme-selector .theme.selected footer .radio{border-color:var(--theme--highlight)}.theme-selector .theme:hover footer .radio:before,.theme-selector .theme.selected footer .radio:before{opacity:1}`,""]);const d=p}}]);
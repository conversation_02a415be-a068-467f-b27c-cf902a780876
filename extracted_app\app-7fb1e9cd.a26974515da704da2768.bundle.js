"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1206],{733:(t,e,a)=>{a.d(e,{V:()=>v});var i=a(15215),s=a("aurelia-dialog"),o=a("aurelia-framework"),r=a(20770),n=a("dialogs/choose-plan-promo-dialog"),l=a(30770),h=a(15182),c=a("dialogs/pro-showcase-columns-dialog"),d=a("dialogs/pro-showcase-dialog"),u=a(60692),g=a(68539),p=a(54995),m=a(14046),w=a(48881),D=a(62914);let v=class{#t;#e;#a;#i;#s;#o;#r;#n;constructor(t,e,a,i,s,o,r,n){this.#t=t,this.#e=e,this.#i=a,this.#s=i,this.#o=s,this.#a=o,this.#r=r,this.#n=n}attached(){}detached(){}async#l(){return this.account?.subscription||this.#r.assignments.has(u.n.ProShowcaseModal_old)?null:await this.#r.trigger(u.n.ProShowcaseModal).catch((()=>null))}#h(){if(this.timestamps.lastAutoProShowcaseDialogSeen)return!1;if(!(this.flags.hasUsedHotkeys||this.flags.hasUsedInteractiveControls||this.flags.hasUsedOverlay))return!1;const t=Object.values(this.gameHistory).filter((t=>!!t.lastPlayedAt)).sort(((t,e)=>(t.lastPlayedAt??"").localeCompare(e.lastPlayedAt??"")))[0];return!(t?.lastPlayedAt&&(0,m.Ov)(Date.now(),new Date(t.lastPlayedAt))/60/60<6)}#c(){return!!this.timestamps.lastAutoProShowcaseDialogSeen&&(!this.timestamps.lastAutoProShowcaseDialogSeenAgain&&(!((0,m.c_)(Date.now(),new Date(this.timestamps.lastAutoProShowcaseDialogSeen))<7)&&!(Object.values(this.gameHistory).filter((t=>!!t.lastPlayedAt&&(0,m.c_)(Date.now(),new Date(t.lastPlayedAt))<7)).filter((t=>t.playDuration&&t.playDuration>300)).length<1)))}async autoShowDialog(){if(this.#e.hasOpenDialog)return!1;const t=await this.#l();if(null!==t&&0!==t){if(this.#h())return this.#t.dispatch(w.vk,"lastAutoProShowcaseDialogSeen"),this.#d(t),!0;if(this.#c())return this.#t.dispatch(w.vk,"lastAutoProShowcaseDialogSeenAgain"),this.#d(t),!0}return!1}async#d(t,e="auto",a){null!==t&&0!==t||this.openOldProDialog(e),1===t&&this.openStandardDialog(e,(0,h.Xz)(a)?a:"save_mods"),2===t&&this.openColumnsDialog(e,(0,h.pF)(a)?a:"save_mods"),3===t&&this.openChoosePlanPromoDialog(e)}async openDialog(t="auto",e){if(this.#e.hasOpenDialog)return!1;const a=await this.#l();return this.#d(a,t,e),!0}async openStandardDialog(t,e){this.#n.event("pro_showcase_dialog_open",{defaultFeature:e,trigger:t});const a=await this.#i.open({defaultFeature:e},!0);this.#n.event("pro_showcase_dialog_close",{defaultFeature:e,currentFeature:a.output,trigger:t})}async openColumnsDialog(t,e){this.#n.event("pro_showcase_columns_dialog_open",{defaultFeature:e,trigger:t});const a=await this.#s.open({defaultFeature:e},!0);this.#n.event("pro_showcase_columns_dialog_close",{defaultFeature:e,currentFeature:a.output})}async openChoosePlanPromoDialog(t){this.#n.event("choose_plan_promo_dialog_open",{trigger:t}),await this.#o.open(void 0,!0),this.#n.event("choose_plan_promo_dialog_close",{})}async openOldProDialog(t){await this.#a.open({trigger:t,nonInteraction:!1})}};v=(0,i.Cg)([(0,p.m6)({setup:"attached",teardown:"detached",selectors:{flags:(0,p.$t)((t=>t.flags)),timestamps:(0,p.$t)((t=>t.timestamps)),account:(0,p.$t)((t=>t.account)),gameHistory:(0,p.$t)((t=>t.gameHistory))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il,s.DialogService,d.ProShowcaseDialogService,c.ProShowcaseColumnsDialogService,n.ChoosePlanPromoDialogService,l.f,g.z,D.j0])],v)},1774:(t,e,a)=>{a.d(e,{D:()=>d});var i=a(15215),s=a("aurelia-framework"),o=a(20770),r=a(58534),n=a("dialogs/changelog-dialog"),l=a(54995),h=a(48881),c=a(62079);let d=class{#t;#u;#g;constructor(t,e,a){this.#t=t,this.#u=e,this.#g=a}attached(){this.#p()}detached(){}async#p(){await this.#u.update()&&this.lastChangelogIdSeen!==r.A&&(this.#t.dispatch(h.Kc,{lastChangelogIdSeen:r.A},"changelog_dialog_trigger",!0),this.#g.open({trigger:"auto"}))}};d=(0,i.Cg)([(0,s.autoinject)(),(0,l.m6)({setup:"attached",teardown:"detached",selectors:{lastChangelogIdSeen:(0,l.$t)((t=>t.settings.lastChangelogIdSeen))}}),(0,i.Sn)("design:paramtypes",[o.il,c.L,n.ChangelogDialogService])],d)},8712:(t,e,a)=>{a.d(e,{J:()=>u});var i=a(15215),s=a("aurelia-framework"),o=a(20770),r=a(45660),n=a(59239),l=a("dialogs/webview-dialog"),h=a(54995),c=a(67064),d=a(10699);let u=class{#m;#t;#w;#D;#v;constructor(t,e,a,i){this.#m=t,this.#t=e,this.#w=a,this.#D=i}attached(){this.subscription?this.#v=!0:(this.#v=!1,this.#t.state.pipe((0,r.$)(),(0,n.E)("flags","proOnStartup")).subscribe((t=>{t&&this.showReactivateDialog()})))}detached(){}subscriptionChanged(){this.subscription?this.#v=!0:this.#v&&(this.#v=!1,this.showReactivateDialog())}async showReactivateDialog(){(await this.#m.open({route:"reactivate-pro",params:{hadGift:this.hadGift?"true":null}})).wasCancelled||(await this.#w.refreshAccount(),this.#D.toast({content:"reactivate_pro.youre_a_pro_again",type:"ok"}))}};u=(0,i.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,h.$t)((t=>t.account?.subscription)),hadGift:(0,h.$t)((t=>t.flags?.giftOnStartup))}}),(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[l.WebviewDialogService,o.il,d.G,c.l])],u)},13101:(t,e,a)=>{a.d(e,{F:()=>c});var i=a(15215),s=a("aurelia-dialog"),o=a("aurelia-framework"),r=a(20770),n=a("dialogs/welcome-mat-dialog"),l=a(54995),h=a(48881);let c=class{#y;#t;#e;constructor(t,e,a){this.#y=t,this.#t=e,this.#e=a}attached(){}detached(){}async showDialog(){return!(this.#e.hasOpenDialog||this.lastWelcomeMatDialog||this.subscription||(this.#t.dispatch(h.vk,"lastWelcomeMatDialog"),this.#y.open(),0))}};c=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{lastWelcomeMatDialog:(0,l.$t)((t=>t.timestamps?.lastWelcomeMatDialog)),subscription:(0,l.$t)((t=>t.account?.subscription))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[n.WelcomeMatDialogService,r.il,s.DialogService])],c)},17703:(t,e,a)=>{a.d(e,{T:()=>h});var i=a(15215),s=a("aurelia-event-aggregator"),o=a("aurelia-framework"),r=a(83802),n=a(38777),l=a(67064);let h=class{#f;#D;#S;#_;#T;#b;constructor(t,e,a){this.#f=t,this.#D=e,this.#S=a}attached(t){this.#b=t;const e=this.#A.bind(this);this.#_=new n.Vd([this.#f.onNewTrainer(e),this.#f.onTrainerEnded(e),this.#S.subscribe("router:navigation:success",e)])}#A(){if(!this.#f.trainer)return void this.#P();const t=this.#f.trainer.getMetadata(r.vO).info;this.#C()===t.id?this.#P():this.#E(this.#f.trainer,t)}#C(){return(("&"+document.location.hash.split("?",2)[1]).match(/&trainerId=([^&]+)/)??[])[1]}#P(){this.#T&&(this.#D.remove(this.#T),this.#T=null)}#E(t,e){this.#T||(this.#T=this.#D.toast({content:"active_trainer_toast.cheats_are_running",type:"ok",persist:!0,lock:!0,onremove:()=>this.#T=null,actions:[{label:"active_trainer_toast.end",onclick:()=>{t.isEnding()||t.dispose()}},{label:"active_trainer_toast.open",onclick:()=>this.#b.navigateToRoute("title",{titleId:e.titleId,gameId:e.gameId,trainerId:e.id})}]}))}detached(){this.#_?.dispose(),this.#_=null,this.#P()}};h=(0,i.Cg)([(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.jR,l.l,s.EventAggregator])],h)},24697:(t,e,a)=>{a.d(e,{v:()=>v});var i=a(15215),s=a("aurelia-event-aggregator"),o=a("aurelia-framework"),r=a(20770),n=a(67761),l=a(52871),h=a(19072),c=a(54995),d=a(48881),u=a(38777),g=a(811),p=a(56436),m=a(43305),w=a(733),D=a(80901);let v=class{#k;#R;#U;#S;#I;#F;#M;#L;#$;#i;#O;#t;#j;constructor(t,e,a,i,o,r,n,l,h,c){this.#I=new s.EventAggregator,this.#U=t,this.#S=e,this.#F=a,this.#M=i,this.#L=o,this.#$=r,this.#i=n,this.#O=l,this.#t=h,this.#j=c}attached(){this.#k=(new u.Vd).push(this.#S.subscribe("router:navigation:complete",this.#N.bind(this)))}detached(){this.#R?.dispose(),this.#k.dispose()}#N(t){const e=t.instruction.viewPortInstructions.default.childRouter.currentInstruction;this.#R||(this.#U.visible?this.#x(e):this.#R=this.#U.whenVisible((()=>{this.#R=null,this.#x(e)})))}async#x(t){let e=await this.#V();if(!e){if("dashboard"===t.config.name&&(e=await this.#H()),"title"===t.config.name){const a=t.params;e=await this.#X(a.titleId)}"collection"===t.config.name&&"games-with-maps"===t.params.slug&&(e=await this.#G()),e||(e=await this.#W())}this.#I.publish("complete",e)}async#V(){return!!await this.#F.showDialog("navigation")}async#H(){return!!this.#M.showDialog()}async#X(t){const e=this.hasUsedHotkeys||this.hasUsedInteractiveControls,a=!this.hasUsedMaps&&e;return!(!await this.#i.autoShowDialog()&&(!this.instantHighlightAnnouncementShown&&this.#U.isWindows11OrGreater&&this.featureFlags.video_capture?.instantHighlightAnnouncementDialogEnabled?(this.#j.open(),this.#t.dispatch(d.NX,"instantHighlightAnnouncementShown",!0),0):(!a||!await this.#$.showDialog(t))&&(this.liveLocationAnnouncementShown||!await this.#O.openIfTitleSupportsLiveLocation(t.toString()))))}async#G(){return!(this.liveLocationAnnouncementShown||!await this.#O.open())}async#W(){return!!await this.#L.showDialog("navigation")}onComplete(t){return this.#I.subscribe("complete",t)}};v=(0,i.Cg)([(0,c.m6)({setup:"attached",teardown:"detached",selectors:{hasUsedInteractiveControls:(0,c.$t)((t=>t.flags?.hasUsedInteractiveControls)),hasUsedHotkeys:(0,c.$t)((t=>t.flags?.hasUsedHotkeys)),hasUsedMaps:(0,c.$t)((t=>t.flags?.hasUsedMaps)),liveLocationAnnouncementShown:(0,c.$t)((t=>t.flags?.liveLocationAnnouncementShown)),instantHighlightAnnouncementShown:(0,c.$t)((t=>t.flags?.instantHighlightAnnouncementShown)),featureFlags:(0,c.$t)((t=>t.catalog.features))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[h.s,s.EventAggregator,g.n,D.v,m.Y,p.c,w.V,l.U,r.il,n.N])],v)},29347:(t,e,a)=>{a.d(e,{y:()=>c});var i=a(15215),s=a("aurelia-framework"),o=a(20770),r=a(19072),n=a(20057),l=a(54995),h=a(48881);let c=class{#U;#B;#t;#Y;constructor(t,e,a){this.#U=t,this.#B=e,this.#t=a}attached(){this.closeToTrayChanged()}detached(){this.#z()}#z(){this.#Y&&(this.#Y.dispose(),this.#Y=null)}async closeToTrayChanged(){this.closeToTray?(await this.#U.setCloseBehavior("tray"),this.#Y||this.flags.runningInTrayNotificationShown||(this.#Y=this.#U.onClosedToTray((()=>{new Notification(this.#B.getValue("tray_notification.wemod_minimized"),{body:this.#B.getValue("tray_notification.wemod_is_running_in_the_tray")}),this.#z(),this.#t.dispatch(h.NX,"runningInTrayNotificationShown",!0)})))):await this.#U.setCloseBehavior("quit")}};c=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{closeToTray:(0,l.$t)((t=>t.settings?.closeToTray)),flags:(0,l.$t)((t=>t.flags))}}),(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[r.s,n.F2,o.il])],c)},29865:(t,e,a)=>{a.d(e,{Z:()=>g});var i=a(15215),s=a("aurelia-framework"),o=a(20770),r=a(68663),n=a(41882),l=a(54995),h=a(48881),c=a(38777),d=a(86824);const u=new Set([n.u]);let g=class{#K;#t;#q;#Q;#Z;constructor(t,e){this.#q=!1,this.#Q=!1,this.#K=t,this.#t=e}initialize(){return this.#A()}attached(){}detached(){this.#J(),this.#Q=!1}#tt(){this.#Z=(0,c.Ix)((()=>this.#A()),(0,d.H)(60,70))}#J(){this.#Z?.dispose(),this.#Z=null}installedAppsChanged(){return this.#A()}async#A(){if(this.#q)this.#Q=!0;else{this.#J(),this.#q=!0,this.#Q=!1;try{await this.#et()}catch(t){throw this.#Q=!1,t}finally{this.#q=!1,this.#Q?this.#A():this.#tt()}}}async#et(){const t=Object.keys(this.installedApps).filter((t=>!u.has(this.installedApps[t].platform))),e=Math.ceil(Date.now()/1e3),a=t.filter((t=>this.#at(t,e)));if(0===a.length)await this.#t.dispatch(h.BT,new Set(t));else{const e=[];for(let t=0;t<a.length;t+=250){const i=a.slice(t,t+250);try{e.push(await this.#K.getUnavailableTitlesByCorrelationIds(i))}catch{}}await this.#t.dispatch(h.Cb,e.flat(),a,new Set(t))}}#at(t,e){const a=this.correlatedUnavailableTitleRefreshes[t];return"number"!=typeof a||a+604800<=e}};g=(0,i.Cg)([(0,l.m6)({setup:"initialize",teardown:"detached",selectors:{installedApps:(0,l.$t)((t=>t.installedApps)),correlatedUnavailableTitleRefreshes:(0,l.$t)((t=>t.correlatedUnavailableTitleRefreshes))}}),(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[r.x,o.il])],g)},31051:(t,e,a)=>{a.d(e,{o:()=>d});var i=a(15215),s=a("aurelia-dialog"),o=a("aurelia-framework"),r=a(20770),n=a("dialogs/post-assistant-nps-dialog"),l=a(54995),h=a(14046),c=a(48881);let d=class{#t;#e;#it;constructor(t,e,a){this.#t=t,this.#e=e,this.#it=a}attached(){}detached(){}async showDialog(t){if(this.#e.hasOpenDialog)return!1;if(!this.analyticsEnabled)return!1;const e=Date.now();return!(this.lastPostAssistantNPS&&(0,h.c_)(e,new Date(this.lastPostAssistantNPS))<=30)&&(await this.#it.open(t),await this.#t.dispatch(c.vk,"lastPostAssistantNPS"),!0)}};d=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{lastPostAssistantNPS:(0,l.$t)((t=>t.timestamps?.lastPostAssistantNPS)),analyticsEnabled:(0,l.$t)((t=>t.settings?.analytics))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il,s.DialogService,n.PostAssistantNpsDialogService])],d)},43305:(t,e,a)=>{a.d(e,{Y:()=>u});var i=a(15215),s=a("aurelia-dialog"),o=a("aurelia-framework"),r=a(20770),n=a("dialogs/nps-dialog"),l=a(54995),h=a(14046),c=a(48881);const d=[{name:"New user",minDays:3,maxDays:14},{name:"1 month",minDays:25,maxDays:35},{name:"3 month",minDays:85,maxDays:95},{name:"12 month",minDays:360,maxDays:370},{name:"24 month",minDays:725,maxDays:735}];let u=class{#t;#e;#L;constructor(t,e,a){this.#t=t,this.#e=e,this.#L=a}attached(){}detached(){}async showDialog(t){if(this.#e.hasOpenDialog)return!1;if(!this.analyticsEnabled)return!1;const e=Date.now(),a=new Date(this.account.joinedAt),i=(0,h.c_)(e,a),s=d.find((({minDays:t,maxDays:e})=>i>=t&&i<=e));return!!s&&(!(this.lastNPSDialogCheck&&(0,h.c_)(new Date(this.lastNPSDialogCheck),a)>=s.minDays)&&(await this.#t.dispatch(c.vk,"lastNPSDialogCheck",e),!(Math.random()>.25||(await this.#L.open({trigger:t,name:s.name}),await this.#t.dispatch(c.vk,"lastNPSDialog",e),0))))}};u=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{lastNPSDialogCheck:(0,l.$t)((t=>t.timestamps?.lastNPSDialogCheck)),account:(0,l.$t)((t=>t.account)),analyticsEnabled:(0,l.$t)((t=>t.settings?.analytics))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il,s.DialogService,n.NpsDialogService])],u)},45053:(t,e,a)=>{a.d(e,{D:()=>u});var i=a(15215),s=a("aurelia-event-aggregator"),o=a("aurelia-framework"),r=a(20770),n=a(69735),l=a(54995),h=a(48881),c=a(38777),d=a(78576);let u=class{#k;#st;#t;#ot;constructor(t,e,a){this.#st=t,this.#t=e,this.#ot=a}attached(){this.#k=(new c.Vd).push(this.#st.subscribe("router:navigation:complete",this.#N.bind(this)))}detached(){this.#k?.dispose(),this.#k=null}async#N(t){"cheats/title"===t.instruction.viewPortInstructions.default.childRouter.currentInstruction.config.moduleId&&this.account?.subscription&&this.#rt()}async#rt(){const t=(0,n.A)(new Date(this.account?.subscription?.startedAt??""),Date.now()),e=t>=864e5&&t<=1728e5,a=!!this.lastProOnboardingReminder;e&&!a&&(this.#t.dispatch(h.vk,"lastProOnboardingReminder"),this.#ot.openTooltip())}};u=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,l.$t)((t=>t.account)),lastProOverlayReminder:(0,l.$t)((t=>t.timestamps?.lastProOverlayReminder)),lastProRemoteReminder:(0,l.$t)((t=>t.timestamps?.lastProRemoteReminder)),lastProOnboardingReminder:(0,l.$t)((t=>t.timestamps?.lastProOnboardingReminder))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[s.EventAggregator,r.il,d.G])],u)},56436:(t,e,a)=>{a.d(e,{c:()=>p});var i=a(15215),s=a("aurelia-dialog"),o=a("aurelia-framework"),r=a(20770),n=a("dialogs/maps-education-dialog"),l=a("dialogs/teleport-education-dialog"),h=a(24008),c=a(54995),d=a(14046),u=a(70236),g=a(48881);let p=class{#t;#e;#$;#nt;constructor(t,e,a,i){this.#t=t,this.#e=e,this.#$=a,this.#nt=i}attached(){}detached(){}async showDialog(t){if(this.#e.hasOpenDialog)return!1;const e=this.catalog.maps.filter((e=>e.titleId===t));if(!e.length)return!1;if(this.lastMapsEducationDialog&&this.lastTeleportEducationDialog)return!1;if(!this.lastTeleportEducationDialog&&e.some((t=>(0,u.Lt)(t.flags??0,h.nC.HasGameCoordinates))))return await this.#nt.open({titleId:t}),this.#t.dispatch(g.vk,"lastTeleportEducationDialog"),this.#t.dispatch(g.vk,"lastMapsEducationDialog"),this.#t.dispatch(g.vk,"lastEducationDialog"),!0;const a=!this.lastEducationDialog||(0,d.c_)(Date.now(),new Date(this.lastEducationDialog));return!(this.lastMapsEducationDialog||!a||(this.#$.open({titleId:t}),this.#t.dispatch(g.vk,"lastMapsEducationDialog"),this.#t.dispatch(g.vk,"lastEducationDialog"),0))}};p=(0,i.Cg)([(0,c.m6)({setup:"attached",teardown:"detached",selectors:{lastEducationDialog:(0,c.$t)((t=>t.timestamps?.lastEducationDialog)),lastMapsEducationDialog:(0,c.$t)((t=>t.timestamps?.lastMapsEducationDialog)),lastTeleportEducationDialog:(0,c.$t)((t=>t.timestamps?.lastTeleportEducationDialog)),subscription:(0,c.$t)((t=>t.account?.subscription)),catalog:(0,c.$t)((t=>t.catalog))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il,s.DialogService,n.MapsEducationDialogService,l.TeleportEducationDialogService])],p)},59327:(t,e,a)=>{a.d(e,{t:()=>c});var i=a(15215),s=a("aurelia-framework"),o=a(20770),r=a("dialogs/failed-payment-dialog"),n=a(54995),l=a(48881),h=a(67064);let c=class{#D;#t;#lt;#ht;constructor(t,e,a){this.#D=t,this.#t=e,this.#lt=a}subscriptionChanged(){return this.#A()}attached(){return this.#A()}detached(){}async#A(){if(!this.subscription?.pastDueInvoice)return this.#ct(),void(this.lastFailedPayment&&await this.#t.dispatch(l.vk,"lastFailedPayment",null));const t=this.subscription.pastDueInvoice.date;(!this.lastFailedPayment||this.lastFailedPayment.localeCompare(t)<0)&&(await this.#t.dispatch(l.vk,"lastFailedPayment",t),this.#lt.open()),this.#ht||this.add()}#ct(){this.#ht&&this.#D.remove(this.#ht)}add(){this.#ht||(this.#ht=this.#D.toast({content:"failed_payment_toast.update_payment",type:"alert",persist:!0,lock:!0,onremove:()=>this.#ht=null,actions:[{label:"failed_payment_toast.fix",onclick:()=>this.#lt.open()}]}))}};c=(0,i.Cg)([(0,n.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,n.$t)((t=>t.account?.subscription)),lastFailedPayment:(0,n.$t)((t=>t.timestamps?.lastFailedPayment))}}),(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[h.l,o.il,r.FailedPaymentDialogService])],c)},68865:(t,e,a)=>{a.d(e,{V:()=>h});var i=a(15215),s=a("aurelia-dialog"),o=a("aurelia-framework"),r=a(20770),n=a("dialogs/mod-timers-education-dialog"),l=a(54995);a(48881);let h=class{#t;#e;#dt;constructor(t,e,a){this.#t=t,this.#e=e,this.#dt=a}attached(){}detached(){}async showDialog(){return!1}};h=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{lastModTimersEducationDialog:(0,l.$t)((t=>t.timestamps?.lastModTimersEducationDialog))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il,s.DialogService,n.ModTimersEducationDialogService])],h)},69983:(t,e,a)=>{a.d(e,{i:()=>c});var i=a(15215),s=a("aurelia-framework"),o=a(83802),r=a(19072),n=a("shared/dialogs/basic-dialog"),l=a(49442),h=a(62914);let c=class{#f;#e;#U;#n;#Y;constructor(t,e,a,i){this.#f=t,this.#e=e,this.#U=a,this.#n=i}attached(){this.#Y=this.#U.addAppQuitInterceptor((async()=>{if(this.#f.trainer){this.#n.event("app_exit_intent",{},h.Io);const t=this.#n.flush().catch(l.Y);if(this.#U.focus(),await this.#e.yesNo("game.exit_confirm_dialog",void 0,!0)!==n.DialogResult.Yes)return!0;await t}}))}detached(){this.#Y?.dispose(),this.#Y=null}};c=(0,i.Cg)([(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[o.jR,n.BasicDialogService,r.s,h.j0])],c)},72510:(t,e,a)=>{a.d(e,{B:()=>n});var i=a(15215),s=a("aurelia-framework"),o=a(44759),r=a(67064);let n=class{#D;#ut;#gt;#Y;#pt;constructor(t,e){this.#D=t,this.#ut=e}attached(){this.#Y=this.#ut.onStatusChanged((t=>this.#mt(t))),this.#mt(this.#ut.status)}detached(){this.#Y?.dispose(),this.#Y=null}#mt(t){!this.#pt||"online"!==t&&this.#gt===t||(this.#D.remove(this.#pt),this.#pt=null),"online"===t||this.#pt||(this.#pt=this.#D.toast({type:"alert",lock:!1,persist:!0,content:"maintenance"===t?"online_status.maintenance_toast":"online_status.offline_toast"})),this.#gt=t}};n=(0,i.Cg)([(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[r.l,o.WA])],n)},75246:(t,e,a)=>{a.d(e,{R:()=>g});var i=a(15215),s=a("aurelia-framework"),o=a(69735),r=a(83802),n=a(19072),l=a(38777),h=a(86824),c=a(27958),d=a(85805);const u=()=>(0,h.H)(30,40);let g=class{#U;#wt;#Dt;#vt;#yt;#k;#ft;constructor(t,e,a,i){this.#U=t,this.#wt=e,this.#Dt=a,this.#vt=i}activate(){this.#U.info.updaterAvailable&&("applied"===this.#U.updateState?this.#St():(this.#yt=(0,l.Ix)((()=>this.#_t()),u()),this.#k=this.#U.onUpdateStateChanged((t=>{"applied"===t&&this.#St()}))))}deactivate(){this.#yt?.dispose(),this.#k?.dispose()}async#_t(){"applied"!==await this.#U.checkForUpdate().catch((()=>!1))&&(this.#yt=(0,l.Ix)((()=>this.#_t()),u()))}#St(){this.#U.visible||(this.#ft=Date.now()),this.#k?.dispose(),this.#k=new l.Vd([this.#U.onClosedToTray((()=>{this.#ft=Date.now(),this.#Tt()})),this.#wt.onTrainerEnded((()=>this.#Tt())),this.#vt.onStatusChanged((t=>{t!==d.t.Connected&&this.#Tt()}))]),this.#Tt()}#Tt(){this.#yt?.dispose(),this.#yt=(0,l.Ix)((()=>this.#bt()),6e4)}async#bt(){this.#At()&&await this.#U.restartForUpdate(this.#Dt.getCurrentInternalUri(),!0)}#At(){return"applied"===this.#U.updateState&&!this.#wt.trainer&&!(this.#U.visible||(0,o.A)(this.#ft,Date.now())<6e4)&&this.#vt.status!==d.t.Connected}};g=(0,i.Cg)([(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[n.s,r.jR,c.L,d.e])],g)},75513:(t,e,a)=>{a.d(e,{N:()=>d});var i=a(15215),s=a("aurelia-framework"),o=a(68663),r=a(44301),n=a(83802),l=a(92465),h=a(49442);const c={1:"hotkey",2:"desktop",3:"remote",4:"overlay",6:"overlay_native",7:"mod_timer"};let d=class{#K;#k;#f;constructor(t,e){this.#k=new l.Vd,this.#K=t,this.#f=e}attached(){this.#Pt(),this.#Ct()}detached(){this.#k.dispose()}#Pt(){this.#k.push(this.#f.onTrainerActivated((t=>{const e=new Set,a=t.onValueSet((({source:t,name:a})=>{if(c[t]){const i=`${t}:${a}`;e.has(i)||(e.add(i),this.#K.reportUserEvent(r.Q.modActivated,{source:c[t],name:a}).catch(h.Y))}}));t.onEnded((()=>a.dispose()))})))}#Ct(){this.#k.pushEventListener(window,"focus",(()=>{let t;const e=()=>{this.#K.reportUserEvent(r.Q.appWindowFocused).catch(h.Y)};return()=>{clearTimeout(t),t=setTimeout(e,1e4)}})())}};d=(0,i.Cg)([(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[o.x,n.jR])],d)},80901:(t,e,a)=>{a.d(e,{v:()=>u});var i=a(15215),s=a("aurelia-dialog"),o=a("aurelia-framework"),r=a(20770),n=a(34884),l=a("dialogs/secure-account-dialog"),h=a(54995),c=a(70236),d=a(48881);let u=class{#M;#t;#e;constructor(t,e,a){this.#M=t,this.#t=e,this.#e=a}attached(){}detached(){}showDialog(){const t=(0,c.Lt)(this.account.flags,8),e=(0,c.Lt)(this.account.flags,2),a=Date.now();return!this.#e.hasOpenDialog&&((!t||!e)&&(!(this.lastSecureAccountDialog&&(0,n.A)(new Date(this.lastSecureAccountDialog),a)<604800)&&(!((0,n.A)(new Date(this.account.joinedAt),a)<3600)&&(!!this.gameHistory&&(!!Object.values(this.gameHistory).filter((t=>!!t.lastPlayedAt)).length&&(this.#t.dispatch(d.vk,"lastSecureAccountDialog"),this.#M.open(),!0))))))}};u=(0,i.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{lastSecureAccountDialog:(0,h.$t)((t=>t.timestamps?.lastSecureAccountDialog)),gameHistory:(0,h.$t)((t=>t.gameHistory)),account:(0,h.$t)((t=>t.account))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[l.SecureAccountDialogService,r.il,s.DialogService])],u)},86867:(t,e,a)=>{a.d(e,{k:()=>c});var i=a(15215),s=a("aurelia-dialog"),o=a("aurelia-framework"),r=a(20770),n=a("dialogs/precision-mods-education-dialog"),l=a(54995),h=a(48881);let c=class{#t;#e;#Et;constructor(t,e,a){this.#t=t,this.#e=e,this.#Et=a}attached(){}detached(){}async showDialog(){return!this.#e.hasOpenDialog&&!this.lastPrecisionModsEducationDialog&&(this.#Et.open(),this.#t.dispatch(h.vk,"lastPrecisionModsEducationDialog"),!0)}};c=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{lastPrecisionModsEducationDialog:(0,l.$t)((t=>t.timestamps?.lastPrecisionModsEducationDialog))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il,s.DialogService,n.PrecisionModsEducationDialogService])],c)},87286:(t,e,a)=>{a.d(e,{O:()=>g});var i=a(15215),s=a("aurelia-framework"),o=a(83802),r=a(19072),n=a(49442),l=a(38777),h=a(67064),c=a(62914),d=a(27958);const u=36e5;let g=class{#D;#U;#Dt;#f;#n;#kt;#Rt;#Ut;#It;#Ft;#Mt;constructor(t,e,a,i,s){this.#D=t,this.#U=e,this.#Dt=a,this.#f=i,this.#n=s}attached(){this.#Lt(this.#U.updateState),this.#kt=this.#U.onUpdateStateChanged((t=>this.#Lt(t)))}detached(){this.#$t(),this.#kt?.dispose(),this.#kt=null,this.#It?.dispose(),this.#Ft?.dispose(),this.#Mt?.dispose()}#$t(){this.#Rt&&(this.#D.remove(this.#Rt),this.#Rt=null),this.#Ut&&(this.#D.remove(this.#Ut),this.#Ut=null)}#Ot(){return!this.#f?.trainer?.isActive}#jt(){this.#Mt&&this.#Mt.dispose(),this.#Mt=(0,l.Ix)((()=>{this.#Ot()?this.#U.restartForUpdate(this.#Dt.getCurrentInternalUri()):this.#Nt()}),6e5)}#Nt(){this.#$t(),this.#Mt&&this.#Mt.dispose(),this.#Mt=(0,l.Ix)((()=>{this.#xt()}),u)}#Lt(t){"applied"===t&&this.#xt(),"apply-error"!==t||this.#It||this.#Vt()}#xt(){this.#Rt||(this.#$t(),this.#n.event("auto_update_toast_shown",{},c.Io),this.#Rt=this.#D.toast({type:"ok",content:"app_update_toast.wemod_will_auto_restart",persist:!0,actions:[{label:"app_update_toast.restart_now",onclick:()=>{this.#n.event("auto_update_restart_now_click",{},c.Io),this.#Ht()}},{label:"app_update_toast.restart_later",onclick:()=>this.#Xt()},{label:"app_update_toast.changelog",onclick:()=>{this.#n.event("changelog_link_click",{trigger:"app_update_toast"}),window.open("https://hub.wemod.com/changelog","_blank")}}],onremove:()=>{this.#Xt()}}),this.#jt())}#Ht(){this.#U.restartForUpdate(this.#Dt.getCurrentInternalUri())}#Xt(){this.#n.event("auto_update_restart_later_click",{},c.Io),this.#Nt()}#Vt(){this.#Ut||(this.#$t(),this.#n.event("apply_update_error_shown",{},c.Io),this.#Ut=this.#D.toast({type:"alert",content:"app_update_toast.update_failed",persist:!0,actions:[{label:"app_update_toast.retry",onclick:()=>{this.#n.event("apply_update_error_retry",{},c.Io),this.#U.applyUpdate().catch(n.Y),this.#$t()}}],onremove:()=>{this.#Ft&&this.#Ft.dispose(),this.#Ft=(0,l.Ix)((()=>{this.#U.applyUpdate().catch(n.Y)}),u)}}))}};g=(0,i.Cg)([(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[h.l,r.s,d.L,o.jR,c.j0])],g)},90017:(t,e,a)=>{a.d(e,{v:()=>L});var i,s,o=a(15215),r=a("aurelia-framework"),n=a(20770),l=a(68663),h=a(60284),c=a(58293),d=a(97170),u=a(40930),g=a(96111),p=a("cheats/resources/elements/feedback-dialog"),m=a(20489),w=a(24297),D=a(83802),v=a(14240),y=a("dialogs/nps-dialog"),f=a(86319),S=a("dialogs/time-remaining-post-game-dialog"),_=a(19072),T=a(3972),b=a(68539),A=a(44759),P=a(56669),C=a(96555),E=a("shared/dialogs/basic-dialog"),k=a(54995),R=a(49442),U=a(48881),I=a(29844),F=a(43861),M=a(60796);!function(t){t[t.Continue=0]="Continue",t[t.Return=1]="Return",t[t.Retry=2]="Retry",t[t.ReportProblem=3]="ReportProblem"}(i||(i={})),function(t){t[t.NotShown=0]="NotShown",t[t.Canceled=1]="Canceled",t[t.Collected=2]="Collected"}(s||(s={}));let L=class{#U;#Gt;#K;#f;#e;#Wt;#L;#Bt;#ut;#t;#Yt;#zt;#Kt;#qt;#Qt;#Zt;#Y;constructor(t,e,a,i,s,o,r,n,l,h,c,d,u,g,p,m,w){this.#U=t,this.#Gt=e,this.#K=a,this.#f=i,this.#e=s,this.#Wt=o,this.#L=r,this.#Bt=n,this.#ut=l,this.#t=h,this.#Yt=c,this.#zt=d,this.#Kt=u,this.#qt=g,this.#Qt=p,this.#Zt=w}attached(){this.#Y=this.#f.onTrainerEnded((t=>this.#Jt(t)))}detached(){this.#Y?.dispose(),this.#Y=null}async#Jt(t){const e=await this.#te(t),a=t.getMetadata(D.vO),o=t.getMetadata(u.XO),r=a.info;if(o?.wasEndedByTimeLimit){if(await this.#qt.triggerPostTrainerEvents())return;if(await this.#Qt.triggerPostTrainerEvents(r.gameId))return;if(await this.#Yt.hasOpenDialog())return}if("remote"===a.trigger)return;if(e===i.Return)return;if(e===i.Retry)return void this.#f.launch(t.getMetadata(D.vO));if("online"!==this.#ut.status)return;if(e===i.ReportProblem)return void await this.#ee(t,!1);const n=t.getMetadata(M.G);if(!(3===this.#qt.e39Variant&&this.#qt.isEnabled&&!this.#qt.isOverDailyLimit&&n?.secondsPlayed>=300&&(await this.#Zt.open(),await this.#Yt.hasOpenDialog())||await this.#Yt.hasOpenDialog()||await this.#ee(t)!==s.NotShown)){if(await this.#Yt.hasOpenDialog())return;if(Object.values(this.state.gameHistory).filter((t=>!!t.lastPlayedAt)).map((t=>t.playDuration||0)).reduce(((t,e)=>t+e),0)>600){if(!this.state.flags.appRatingShownAfterTrainer)return await this.#L.open({trigger:"post-trainer",name:"post-trainer"},!1),void this.#t.dispatch(U.NX,"appRatingShownAfterTrainer",!0);if((await this.#Bt.openPostTrainer()).status!==F.X.NotShown)return}await this.#Yt.hasOpenDialog()}}async#te(t){if(t.launchResult===m.KC.Success)return i.Continue;if(t.launchResult===m.KC.Canceled)return i.Return;if(t.launchResult===m.KC.ElevationDenied)return await this.#e.ok("trainer_launcher.elevation_denied"),i.Return;if(t.launchResult===m.KC.Incompatible)return await this.#e.ok("trainer_launcher.newer_version_required"),i.Return;if(t.launchResult===m.KC.GameAlreadyRunning)return await this.#e.ok("trainer_launcher.game_already_running"),i.Return;if(t.launchResult===m.KC.GameNotRunning)return this.#e.open({message:"trainer_launcher.game_not_running",options:["trainer_launcher.launch_outside_wemod"]}).whenClosed((e=>{if(!e.wasCancelled){const e=t.getMetadata(D.vO);this.#Kt.launch(e.app,e.info.gameId,"game_not_running_dialog")}})),i.Return;const e=t.finalState;if(e===m.FX.AcquiringBinary)return"trainerlib"===t.getMetadata(D.vO).info.loader?await this.#e.help("trainer_launcher.cannot_download_cheats","https://wemod.gg/support-downloading-mods"):await this.#e.ok("trainer_launcher.cannot_find_dll"),i.Return;if(e===m.FX.EnsuringBinaryAccess){if(await this.#zt.open()){try{if(t.dllPath&&await this.#Gt.addAvExclusion((0,I.pD)(t.dllPath)))return i.Retry}catch{}await this.#e.ok("trainer_launcher.auto_fix_av_failed")}return i.Return}if(e===m.FX.CheckingInternalBinaries||e===m.FX.CreatingTrainerHost)return"trainer_launcher.reinstall_now"===await this.#e.show({message:"trainer_launcher.files_missing",options:["trainer_launcher.ok","trainer_launcher.reinstall_now"]})&&(window.open("website://download"),await this.#U.quit()),i.Return;if(e===m.FX.FindingProcess){if(t.launchResult===m.KC.TimedOut){if("trainer_launcher.retry"===await this.#e.show({message:"trainer_launcher.trouble_starting_or_finding",options:["trainer_launcher.ok","trainer_launcher.retry"]}))return i.Retry}else await this.#e.help("trainer_launcher.trouble_starting_or_finding","https://wemod.gg/support-launching-game");return i.Return}if(e===m.FX.ValidatingProcess)return await this.#e.ok(t.process?.x64?"trainer_launcher.x86_expected":"trainer_launcher.x64_expected"),i.Return;let a;if(e===m.FX.Injecting&&(a="trainer_launcher.trouble_loading_cheats"),e===m.FX.InitializingIpc&&(a="trainer_launcher.preparation_failed"),e!==m.FX.Executing&&e!==m.FX.Connecting||(a="trainer_launcher.trouble_running_cheats"),e===m.FX.Activating&&(a=t.log.some((t=>t.level===w.$b.Error))?"trainer_launcher.activation_problem":"trainer_launcher.activation_prevented"),!a)throw new Error(`Unknown trainer state ${e}.`);return"trainer_launcher.report_problem"===await this.#e.help(a,"https://wemod.gg/support-loading-mods","online"===this.#ut.status?["trainer_launcher.report_problem"]:[])?i.ReportProblem:i.Return}async#ee(t,e){const a=t.getMetadata(v.y);if(!a)return s.NotShown;const i=a.metadata,o=i.info,r=a.getUsedCheatTargets().flatMap((t=>o.blueprint.cheats.filter((e=>e.target===t)))).map((t=>t.uuid)),n=await this.#Wt.collectFeedback(this.state.catalog.titles[o.titleId].name,o,new C.o(i.app.platform,i.app.sku).toString(),i.gameVersion,r,e,"boolean"==typeof e);if(!n.shown)return s.NotShown;if(n.reportType!==P.M3.Success&&n.reportType!==P.M3.Failure)return s.Canceled;if(n.report){const t=await a.compile(n.reportType,n.report);this.#K.submitTrainerFeedback(t).catch(R.Y)}return s.Collected}};L=(0,o.Cg)([(0,k.m6)({setup:"attached",teardown:"detached"}),(0,r.autoinject)(),(0,o.Sn)("design:paramtypes",[_.s,T.Mz,l.x,D.jR,E.BasicDialogService,p.FeedbackDialogService,y.NpsDialogService,F.Z,A.WA,n.il,h.Y,f.T,c.L,g.Y,d.V,b.z,S.TimeRemainingPostGameDialogService])],L)},94101:(t,e,a)=>{a.d(e,{_:()=>d});var i=a(15215),s=a("aurelia-event-aggregator"),o=a("aurelia-framework"),r=a(20770),n=a(32534),l=a(30770),h=a(54995),c=a(48881);let d=class{#e;#t;#st;#ae;#ie;constructor(t,e,a){this.#e=t,this.#t=e,this.#st=a}attached(){this.subscriptionChanged()||this.#se(),this.#ie=this.#st.subscribe("open-pro-dialog",(t=>{this.#e.open(t)}))}detached(){this.#ie.dispose()}#oe(){this.#ae&&(clearTimeout(this.#ae),this.#ae=null)}async subscriptionChanged(){return this.subscription&&this.timestamps.freeUserSince&&(await this.#t.dispatch(c.vk,"freeUserSince",null),await this.#t.dispatch(c.vk,"lastProPopup",null),this.#oe()),!this.subscription&&!this.timestamps.freeUserSince&&(await this.#t.dispatch(c.vk,"freeUserSince"),await this.#t.dispatch(c.vk,"lastProPopup",null),this.#oe(),this.#se(),!0)}async#re(){await this.#t.dispatch(c.vk,"lastProPopup"),await this.#e.open({trigger:"auto_three_day_ten_day",nonInteraction:!0})}#se(){if(this.subscription)return;this.#oe();const t=(0,n.A)(new Date(this.timestamps.freeUserSince??""),Date.now()),e=(0,n.A)(new Date((this.timestamps.lastProPopup||this.timestamps.freeUserSince)??""),Date.now()),a=[3,10];for(const i of a)if(t>=i){e>=i?this.#re():this.#ne(i-e);break}}#ne(t){const e=24*t*60*60*1e3;this.#ae=setTimeout((()=>this.#se()),e)}};d=(0,i.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,h.$t)((t=>t.account?.subscription)),timestamps:(0,h.$t)((t=>t.timestamps))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[l.f,r.il,s.EventAggregator])],d)}}]);
#include "color.effect"

uniform float4x4 ViewProj;
uniform texture2d image;
uniform float multiplier;

sampler_state def_sampler {
	Filter   = Linear;
	AddressU = Clamp;
	AddressV = Clamp;
};

struct VertInOut {
	float4 pos : POSITION;
	float2 uv  : TEXCOORD0;
};

VertInOut VSDefault(VertInOut vert_in)
{
	VertInOut vert_out;
	vert_out.pos = mul(float4(vert_in.pos.xyz, 1.0), ViewProj);
	vert_out.uv  = vert_in.uv;
	return vert_out;
}

float4 PSDrawBare(VertInOut vert_in) : TARGET
{
	return image.Sample(def_sampler, vert_in.uv);
}

float4 PSDrawAlphaDivide(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb *= (rgba.a > 0.) ? (1. / rgba.a) : 0.;
	return rgba;
}

float4 PSDrawAlphaDivideTonemap(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb *= (rgba.a > 0.) ? (1. / rgba.a) : 0.;
	rgba.rgb = rec709_to_rec2020(rgba.rgb);
	rgba.rgb = reinhard(rgba.rgb);
	rgba.rgb = rec2020_to_rec709(rgba.rgb);
	return rgba;
}

float4 PSDrawAlphaDivideR10L(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb *= (rgba.a > 0.) ? (multiplier / rgba.a) : 0.;
	rgba.rgb = rec709_to_rec2020(rgba.rgb);
	rgba.rgb = linear_to_st2084(rgba.rgb);
	uint3 rgb1023 = uint3(mad(rgba.rgb, 1023., .5));
	uint b = (rgb1023.b & 0x3Fu) << 2;
	uint g = ((rgb1023.b & 0x3C0u) >> 6) | ((rgb1023.g & 0xFu) << 4);
	uint r = ((rgb1023.g & 0x3F0u) >> 4) | ((rgb1023.r & 0x3u) << 6);
	uint a = ((rgb1023.r & 0x3FCu) >> 2);
	return float4(uint4(r, g, b, a)) / 255.;
}

float4 PSDrawNonlinearAlpha(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = srgb_linear_to_nonlinear(rgba.rgb);
	rgba.rgb *= rgba.a;
	rgba.rgb = srgb_nonlinear_to_linear(rgba.rgb);
	return rgba;
}

float4 PSDrawNonlinearAlphaMultiply(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = srgb_linear_to_nonlinear(rgba.rgb);
	rgba.rgb *= rgba.a;
	rgba.rgb = srgb_nonlinear_to_linear(rgba.rgb);
	rgba.rgb *= multiplier;
	return rgba;
}

float4 PSDrawSrgbDecompress(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = srgb_nonlinear_to_linear(rgba.rgb);
	return rgba;
}

float4 PSDrawSrgbDecompressMultiply(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = srgb_nonlinear_to_linear(rgba.rgb);
	rgba.rgb *= multiplier;
	return rgba;
}

float4 PSDrawMultiply(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb *= multiplier;
	return rgba;
}

float4 PSDrawTonemap(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = rec709_to_rec2020(rgba.rgb);
	rgba.rgb = reinhard(rgba.rgb);
	rgba.rgb = rec2020_to_rec709(rgba.rgb);
	return rgba;
}

float4 PSDrawMultiplyTonemap(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb *= multiplier;
	rgba.rgb = rec709_to_rec2020(rgba.rgb);
	rgba.rgb = reinhard(rgba.rgb);
	rgba.rgb = rec2020_to_rec709(rgba.rgb);
	return rgba;
}

float4 PSDrawPQ(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = st2084_to_linear(rgba.rgb) * multiplier;
	rgba.rgb = rec2020_to_rec709(rgba.rgb);
	return rgba;
}

float4 PSDrawTonemapPQ(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = st2084_to_linear(rgba.rgb) * multiplier;
	rgba.rgb = reinhard(rgba.rgb);
	rgba.rgb = rec2020_to_rec709(rgba.rgb);
	return rgba;
}

technique Draw
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawBare(vert_in);
	}
}

technique DrawAlphaDivide
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawAlphaDivide(vert_in);
	}
}

technique DrawAlphaDivideTonemap
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawAlphaDivideTonemap(vert_in);
	}
}

technique DrawAlphaDivideR10L
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawAlphaDivideR10L(vert_in);
	}
}

technique DrawNonlinearAlpha
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawNonlinearAlpha(vert_in);
	}
}

technique DrawNonlinearAlphaMultiply
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawNonlinearAlphaMultiply(vert_in);
	}
}

technique DrawSrgbDecompress
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawSrgbDecompress(vert_in);
	}
}

technique DrawSrgbDecompressMultiply
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawSrgbDecompressMultiply(vert_in);
	}
}

technique DrawMultiply
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawMultiply(vert_in);
	}
}

technique DrawTonemap
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawTonemap(vert_in);
	}
}

technique DrawMultiplyTonemap
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawMultiplyTonemap(vert_in);
	}
}

technique DrawPQ
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawPQ(vert_in);
	}
}

technique DrawTonemapPQ
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawTonemapPQ(vert_in);
	}
}

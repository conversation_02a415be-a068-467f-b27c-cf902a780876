"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7705],{20489:(e,t,s)=>{s.d(t,{yo:()=>v,FX:()=>w});var i=s(15215),n=s("aurelia-framework"),r=s(10351),a=s(49442),o=s(70236),c=s(38777),h=s(77372),l=s(53737),u=s(97813),d=s(52399),p=s(24297),g=s(3972);let m=class{#e;constructor(e){this.#e=e}run(e){return this.#e.createRemoteThread(e.hostProcessId,e.call.module,e.call.export,this.#t(e),e.call.proxy??void 0)}#t(e){const t=Buffer.alloc(1552+32*e.variables.length);let s=0;return t.write(e.logPipe,0,512,"utf16le"),s+=512,t.write(e.messagePipe,s,256,"utf16le"),s+=256,t.writeUInt32LE(e.flags,s),s+=4,s+=252,t.write(e.module,s,512,"utf16le"),s+=512,t.writeUInt32LE(e.targetProcessId,s),s+=4,t.writeUInt32LE(e.gameVersion||0,s),s+=4,t.writeUInt32LE(e.variables.length,s),s+=4,e.variables.forEach((e=>{t.write(e,s,32,"ascii"),s+=32})),t}};var w,y;m=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[g.Mz])],m),function(e){e[e.AwaitingLaunch=0]="AwaitingLaunch",e[e.AcquiringBinary=1]="AcquiringBinary",e[e.EnsuringBinaryAccess=2]="EnsuringBinaryAccess",e[e.CheckingInternalBinaries=3]="CheckingInternalBinaries",e[e.FindingProcess=4]="FindingProcess",e[e.ValidatingProcess=5]="ValidatingProcess",e[e.CreatingTrainerHost=6]="CreatingTrainerHost",e[e.Injecting=7]="Injecting",e[e.InitializingIpc=8]="InitializingIpc",e[e.Executing=9]="Executing",e[e.Connecting=10]="Connecting",e[e.Activating=11]="Activating",e[e.Active=12]="Active",e[e.Ended=13]="Ended"}(w||(w={})),function(e){e[e.Success=0]="Success",e[e.Canceled=1]="Canceled",e[e.TimedOut=2]="TimedOut",e[e.ElevationDenied=3]="ElevationDenied",e[e.Incompatible=4]="Incompatible",e[e.GameAlreadyRunning=5]="GameAlreadyRunning",e[e.GameNotRunning=6]="GameNotRunning",e[e.Error=7]="Error"}(y||(y={}));class f extends Error{constructor(){super("Supplied trainer is incompatible with this app version."),Object.setPrototypeOf(this,f.prototype)}}let v=class{#s;#i;#n;#r;constructor(e,t,s,i){this.#s=e,this.#i=t,this.#n=s,this.#r=i}make(e){return new b(e,this.#s,this.#i,this.#n,this.#r)}};v=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[u.S,d.K,p.f,m])],v);class b{#a;#o;#c;#s;#i;#n;#r;#h;#l;#u;#d;#p;#g;#m;#w;constructor(e,t,s,i,n){this.log=[],this.timings=new Map,this.dllPath=null,this.process=null,this.finalState=null,this.launchResult=null,this.#o=new Map,this.#l=w.AwaitingLaunch,this.#u=new Map,this.#d=new c._M,this.#g=null,this.#m=null,this.#w=!1,this.#c=e,this.#s=t,this.#i=s,this.#n=i,this.#r=n,this.#a=new Map,this.values=this.#a}get launchDuration(){return this.#h?this.wasEverActive()?this.timings.get(w.Active)??0:Date.now()-this.#h:0}get activeDuration(){return this.wasEverActive()?(this.totalDuration??0)-this.launchDuration:0}get totalDuration(){return this.#h?this.hasEnded()?this.timings.get(w.Ended)??null:Date.now()-this.#h:null}isLoading(){return!this.isActive()&&!this.hasEnded()}isActive(){return this.#l===w.Active}wasEverActive(){return this.#w}hasEnded(){return this.#l===w.Ended}isEnding(){return!!this.finalState}addMetadata(e){this.#u.set(Object.getPrototypeOf(e).constructor,e)}getMetadata(e){return this.#u.get(e)}onStateChanged(e){return this.#d.subscribe("state",e)}onActivated(e){return this.#d.subscribeOnce("activated",e)}onEnded(e){return this.#d.subscribeOnce("ended",e)}onValueSet(e){return this.#d.subscribe("value",e)}onValueSetError(e){return this.#d.subscribe("value-error",e)}onLogMessage(e){return this.#d.subscribe("log",e)}getValue(e){return this.#a.get(e)}#y(e,t){if(function(e){return e.startsWith("wm_")}(e))this.#o.set(e,t);else{const s=this.#a.get(e);t!==s&&(this.#a.set(e,t),this.#d?.publish("value",{name:e,value:t,oldValue:s,source:0}))}}async getPlayerCoordinates(){return 1===await this.#f("wm_coord_get",-1)&&1===this.#o.get("wm_coord_get")&&{x:this.#o.get("wm_coord_x"),y:this.#o.get("wm_coord_y"),z:this.#o.get("wm_coord_z")}}async setPlayerCoordinates(e){return!!function(e){return null!==e&&"object"==typeof e&&"number"==typeof e.x&&isFinite(e.x)&&"number"==typeof e.y&&isFinite(e.y)&&"number"==typeof e.z&&isFinite(e.z)}(e)&&(!!(await Promise.all([this.#f("wm_coord_x",e.x??0),this.#f("wm_coord_y",e.y??0),this.#f("wm_coord_z",e.z??0)])).every((e=>0===e))&&(1===await this.#f("wm_coord_set",-1)&&1===this.#o.get("wm_coord_set")))}async setValue(e,t,s,i){t=function(e){return!0===e&&(e=1),!1===e&&(e=0),e}(t);const n=this.#a.get(e);if(!this.#a.has(e))throw new Error(`Attempted to set unknown trainer variable '${e}'`);if(this.#a.set(e,t),this.#d?.publish("value",{name:e,value:t,oldValue:n,source:s,cheatId:i}),!this.#p)return!0;try{const s=await this.#p.setValue(e,t);return(1!==s||n||1!==t||0!==this.#a.get(e))&&2!==s||(this.#d?.publish("value-error",{name:e,cheatId:i}),!1)}catch(e){if(this.isEnding())return!0;throw e}}async#f(e,t){if(this.#o.set(e,t),!this.#p)return!1;try{return await this.#p.setValue(e,t)}catch(e){if(this.isEnding())return!1;throw e}}async launch(e){try{return this.#h=Date.now(),await this.#v(e),!0}catch(e){return this.launchError=e,this.launchResult=function(e){return e instanceof c._T?y.Canceled:e instanceof l.MU?y.TimedOut:e instanceof r.Vn?y.ElevationDenied:e instanceof f?y.Incompatible:e instanceof l.cK?y.GameAlreadyRunning:e instanceof l.Ps?y.GameNotRunning:y.Error}(e),this.dispose(),!1}}dispose(){if(this.#l!==w.Ended){if(this.finalState=this.#l,this.#b(w.Ended),this.#d.publish("ended"),this.#d.dispose(),this.#p?.dispose(),this.#g){const e=this.#g;this.#g=null,setTimeout((()=>e.dispose()),1e4)}this.process=null}}async#v(e){if(e.canceled)throw new c._T;this.#b(w.AcquiringBinary);const t=await this.#P(e);this.dllPath=t,this.#b(w.EnsuringBinaryAccess);const s=await this.#L(t);this.#b(w.CheckingInternalBinaries),await this.#E(s),this.#b(w.FindingProcess),this.process=await this.#c.getProcess(e),this.#b(w.ValidatingProcess),this.#T(s,this.process),(0,o.Lt)(this.#c.flags??0,16)?(this.#b(w.CreatingTrainerHost),this.#g=await this.#I(s),this.#m=this.#g.processId??null):this.#m=this.process.id,this.#b(w.Injecting);const i=await this.#x(t);this.#b(w.InitializingIpc);try{this.#p=await this.#A()}catch(e){throw i.suspended&&this.#i.resumeProcess(this.process.id).catch(a.Y),e}this.#b(w.Executing);try{await new Promise((async(s,n)=>{const r=(0,c.Ix)((()=>{a?.dispose(),n(new l.MU("Trainer loading timed out."))}),this.#c.connectTimeout??0),a=e.onCancel((()=>{r.dispose(),n(new c._T("Trainer launch canceled."))}));if(this.#p.onConnect((()=>{r.dispose(),this.#l===w.Executing&&this.#b(w.Connecting),this.#b(w.Activating)})),this.#p.onReady((async()=>{if(a?.dispose(),i.suspended&&(0,o.Lt)(this.#c.flags??0,2))try{if(this.process?.id&&!await this.#i.resumeProcess(this.process.id))throw new Error("Failed to resume process after setup.")}catch(e){return void n(e)}this.launchResult=y.Success,this.#w=!0,this.#b(w.Active),this.#d.publish("activated"),s()})),this.#p.onLogMessage((e=>this.#d?.publish("log",e))),this.#p.onValue((e=>this.#y(e.name,e.value))),this.#p.onDisconnect((()=>{this.isLoading()?(r.dispose(),a?.dispose(),n(new Error("Trainer disconnected."))):this.dispose()})),this.#c.trainerArgs.variables.forEach((e=>this.#a.set(e,void 0))),!this.#m||!this.process?.id)return void n();let h=0;1===this.#c.alphaFeatures&&(h|=1),2===this.#c.alphaFeatures&&(h|=1,h|=2);try{await this.#r.run({hostProcessId:this.#m,targetProcessId:this.process.id,module:t,gameVersion:this.#c.trainerArgs.gameVersion,flags:h,variables:this.#c.trainerArgs.variables,messagePipe:this.#p.messagePipe,logPipe:this.#p.logPipe,call:i}),this.#l===w.Executing&&this.#b(w.Connecting)}catch(e){r.dispose(),a?.dispose(),n(e)}}))}catch(e){throw i.suspended&&this.#i.resumeProcess(this.process.id).catch(a.Y),e}}async#P(e){const t=await this.#c.getTrainerBinary(e);if(!1===t)throw new f;return t}async#L(e){return(await h.E3.load(e)).ntHeaders.fileHeader.machine===h.u5.AMD64?"x64":"ia32"}async#E(e){if((await this.#i.getMissingFiles(e)).length>0)throw new Error("Internal trainer DLLs are missing.")}#I(e){return this.#s.launch(e)}#T(e,t){if("x64"===e&&!t.x64||"ia32"===e&&t.x64)throw new Error("Trainer was not built for this process architecture.")}#A(){return this.#n.open({logPipe:this.#c.useExternalLogger?"\\\\.\\pipe\\WeMod_TrainerLib_Debug":void 0})}#x(e){return this.#i.inject({hostProcessId:this.#m??0,targetProcessId:this.process?.id??0,arch:this.process?.x64?"x64":"ia32",trainerDll:e,flags:this.#c.flags})}#b(e){if(e<=this.#l)throw new Error(`Cannot transition from state ${this.#l} to ${e}.`);this.#l=e,this.timings.set(e,Date.now()-this.#h),this.#d.publish("state",e)}get state(){return this.#l}}},24297:(e,t,s)=>{s.d(t,{f:()=>l});var i=s(15215),n=s(69278),r=s("aurelia-framework"),a=s(96610),o=s(3972),c=s(38777);const h=(0,a.getLogger)("trainer-ipc");let l=class{#e;constructor(e){this.#e=e}async open(e){const t=`\\\\.\\pipe\\WeMod\\Trainer_${Date.now()}_`,s=new c.Vd,[i,n,r]=await Promise.all([this.#C(`${t}In`,s),this.#C(`${t}Out`,s),e.logPipe?Promise.resolve(null):this.#C(`${t}Log`,s)]).catch((e=>{throw s.dispose(),e}));return await(0,c.Wn)(500),new d(i,n,r??e.logPipe)}#C(e,t){return new Promise(((s,i)=>{const r=(0,n.createServer)();t.push((0,c.nm)((()=>{a.dispose(),r.close()})));const a=new c.Vd([(0,c.$U)(r,"error",(e=>{a.dispose(),i(e)})),(0,c.$U)(r,"listening",(async()=>{a.dispose();try{await this.#e.grantFilePermissions(e,[o.Ro.everyone,o.Ro.appContainer],o.TB.Read|o.TB.Write),s(r)}catch(e){i(e)}}))]);r.listen(e)}))}};var u;l=(0,i.Cg)([(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[o.Mz])],l),function(e){e[e.Verbose=0]="Verbose",e[e.Info=1]="Info",e[e.Warning=2]="Warning",e[e.Error=3]="Error"}(u||(u={}));class d{#D;#k;#$;#F;#V;constructor(e,t,s){if(this.#D=new c._M,this.#k=new c.Vd,this.#F=new Map,this.#V=1,this.messagePipe=t.address(),this.#k.push((0,c.nm)((()=>{e.close(),t.close(),"string"!=typeof s&&s.close()}))),"string"==typeof s)this.logPipe=s;else{this.logPipe=s.address();const e=(0,c.$U)(s,"connection",(t=>{e.dispose(),this.#k.push((0,c.nm)((()=>t.destroy()))),this.#k.push((0,c.$U)(t,"data",this.#j.bind(this)))}));this.#k.push(e)}const i=(0,c.$U)(t,"connection",(t=>{i.dispose(),this.#$=t,this.#k.push((0,c.nm)((()=>{this.#$=null,t.destroy()}))),this.#k.push((0,c.$U)(t,"close",(()=>this.dispose()))),this.#k.push((0,c.$U)(t,"error",(e=>{"EPIPE"===e.code&&this.dispose()}))),t.write(e.address(),"utf16le",(e=>e&&this.dispose()))}));this.#k.push(i);const n=(0,c.$U)(e,"connection",(e=>{n.dispose(),this.#k.push((0,c.nm)((()=>e.destroy()))),this.#k.push((0,c.$U)(e,"data",this.#_.bind(this))),this.#D.publish("connect")}));this.#k.push(i)}onConnect(e){return this.#D.subscribeOnce("connect",e)}onReady(e){return this.#D.subscribeOnce("ready",e)}onDisconnect(e){return this.#D.subscribeOnce("disconnect",e)}onLogMessage(e){return this.#D.subscribe("log",e)}onValue(e){return this.#D.subscribe("value",e)}dispose(){if(!this.#k)return;const e=this.#k;e?.dispose(),this.#F.clear(),this.#D.publish("disconnect"),this.#D?.dispose()}setValue(e,t){const s=this.#V++,i=new Promise(((e,t)=>this.#F.set(s,[e,t]))),n=Buffer.alloc(56);return n.writeUInt32LE(2,0),n.writeUInt32LE(s,8),n.write(e||"",16,32,"ascii"),n.writeDoubleLE(t,48),this.#$?.write(n,(e=>{if(e){const t=this.#F.get(s);t&&(this.#F.delete(s),t[1](e))}})),i}#j(e){if(e.length<16||!this.#k)return;let t=0;for(;t+4<e.length;){const s=e.readUInt32LE(t);t+=8;const i=e.readUInt32LE(t);t+=8;const n=e.toString("utf16le",t,t+i);t+=i;const r={time:Date.now(),level:s,message:n};this.#D.publish("log",r)}}#_(e){if(!this.#k)return;let t=0;for(;t+8<=e.length;){const s=e.readUInt32LE(t);switch(t+=8,s){case 0:break;case 1:this.#D.publish("ready");break;case 2:{t+=8;let s=e.toString("ascii",t,t+32);s=s.substring(0,s.indexOf("\0")),t+=32;const i=e.readDoubleLE(t);t+=8,this.#D.publish("value",{name:s,value:i})}break;case 3:{const s=e.readUInt32LE(t);t+=8;const i=e.readUInt32LE(t);t+=8;const n=this.#F.get(s);n&&(this.#F.delete(s),n[0](i))}break;default:return void h.error(`Unknown message type ${s}.`)}}}}},52399:(e,t,s)=>{s.d(t,{K:()=>c});var i=s(35392),n=s(3972),r=s(49442),a=s(70236),o=s(29844);class c{#e;#B;constructor(e,t){this.#e=t,this.#B={x64:{trainerLib:`${e}\\TrainerLib_x64.dll`,trainerLibDecoy:`${e}\\stub\\TrainerLib_x64.dll`,ceLib:`${e}\\CELib_x64.dll`},ia32:{trainerLib:`${e}\\TrainerLib_x86.dll`,trainerLibDecoy:`${e}\\stub\\TrainerLib_x86.dll`,ceLib:`${e}\\CELib_x86.dll`}}}async getMissingFiles(e){const t=Object.values(this.#B[e]),s=await Promise.all(t.map((e=>i.promises.stat(e).then((e=>e.isFile())).catch((()=>!1)))));return t.filter(((e,t)=>!s[t]))}async inject(e){const t=this.#B[e.arch];await this.#R([e.trainerDll,t.trainerLib,t.ceLib]);const s=(0,a.Lt)(e.flags,8)?await this.#S(e.trainerDll,t.trainerLibDecoy):null;if(s&&await this.#R([s,t.trainerLibDecoy]),await this.#e.waitForInputIdle(e.targetProcessId,1e4),(0,a.Lt)(e.flags,1)&&await this.#e.suspendProcess(e.targetProcessId)<0)throw new Error("Failed to suspend process.");try{s&&await Promise.all([this.#e.injectDll(e.hostProcessId,t.trainerLibDecoy,0,5e3),this.#e.injectDll(e.hostProcessId,s,0,5e3)]),await this.#e.injectDll(e.hostProcessId,t.trainerLib,(0,a.Lt)(e.flags,4)?7340032:0,5e3)}catch(t){throw(0,a.Lt)(e.flags,1)&&await this.#e.resumeProcess(e.targetProcessId).catch(r.Y),t}return{module:t.trainerLib,export:"Run",proxy:(0,a.Lt)(e.flags,4)?"ExitProcess":null,suspended:(0,a.Lt)(e.flags,1)}}async resumeProcess(e){return await this.#e.resumeProcess(e)>=0}async#R(e){try{await Promise.all(e.map((e=>this.#e.grantFilePermissions(e,[n.Ro.everyone,n.Ro.appContainer],n.TB.Read|n.TB.Execute))))}catch{}}async#S(e,t){const s=`${(0,o.pD)(e)}\\stub`,n=`${s}\\${(0,o.P8)(e)}`;return await i.promises.stat(n).then((e=>e.isFile())).catch((()=>!1))||(await i.promises.stat(s).then((e=>e.isDirectory())).catch((()=>!1))||await i.promises.mkdir(s),await i.promises.copyFile(t,n)),n}}},53737:(e,t,s)=>{s.d(t,{MU:()=>u,Ps:()=>p,ag:()=>g,cK:()=>d});var i,n=s(15215),r=s("aurelia-framework"),a=s(90231),o=s(41882),c=s(3972),h=s(49442),l=s(38777);!function(e){e[e.Found=0]="Found",e[e.Launched=1]="Launched",e[e.Error=2]="Error",e[e.Timeout=3]="Timeout",e[e.Canceled=4]="Canceled"}(i||(i={}));class u extends Error{constructor(e="The operation timed out."){super(e),Object.setPrototypeOf(this,u.prototype)}}class d extends Error{constructor(e="The game is already running and cannot be launched."){super(e),Object.setPrototypeOf(this,d.prototype)}}class p extends Error{constructor(e="Failed to find an existing game process."){super(e),Object.setPrototypeOf(this,p.prototype)}}let g=class{#e;#U;constructor(e,t){this.#e=e,this.#U=t}launchAndForget(e,t=1e4){return new Promise(((s,i)=>{this.#U.launchApp(e.platform,e.sku,void 0,!1,new l.HL).then(s).catch(i),setTimeout(s,t)}))}async launch(e,t={},s={},i){const n=await this.#e.resolvePath(e.location),r=this.#M(e,s,n),a=this.#O(s.reducer),o=s.minimumAge??8e3,c=s.timeout??45e3,h=await this.#H(r,a,s.cliArgs??null,o,!0);if(h){if(t&&t.required)throw new d;return{process:h}}if(!1===t)throw new p;return await this.#W(this.#z(e,n,t),(()=>this.#H(r,a,s.cliArgs??null,o,!1)),c,i)}#z(e,t,s){return async i=>{let n;!1!==s&&(s.bypass&&e.platform!==o.u&&this.#e.runLauncherBypass({directory:t,...s.bypass}),s.proxy&&e.platform!==o.u&&(n=await this.#N(e,t,s)));try{return await this.#U.launchApp(e.platform,e.sku,s?s.cliArgs:void 0,!0,i),n}catch(e){throw n?.dispose(),e}}}async#N(e,t,s){if(e.platform!==o.u&&s.proxy){const e=`${t}\\${s.proxy.launcher}`;if(await this.#e.mountLauncherProxy({...s.proxy,launcher:e}))return(0,l.nm)((()=>this.#e.unmountLauncherProxy(e).catch(h.Y)))}return l.lE}#M(e,t,s){if(t.image){if(t.image.includes("*")){const e=new RegExp("\\\\"+t.image.split("*").map((e=>e.replace(/[\^$\\.*+?()[\]{}|]/g,"\\$&"))).join("[^\\\\]*")+"$","i");return t=>e.test(t.imagePath)}{const e=`\\${t.image}`.toLocaleLowerCase();return t=>t.imagePath.toLocaleLowerCase().endsWith(e)}}if(e.platform===o.u){const e=s.toLocaleLowerCase();return t=>t.imagePath.toLocaleLowerCase()===e}if(e.alternateLocations?.length){const t=e.alternateLocations.map((e=>`${e}\\`.toLocaleLowerCase()));return t.push(`${s}\\`.toLocaleLowerCase()),e=>{const s=e.imagePath.toLocaleLowerCase();return t.some((e=>s.startsWith(e)))}}{const e=`${s}\\`.toLocaleLowerCase();return t=>t.imagePath.toLocaleLowerCase().startsWith(e)}}#O(e){switch(e){case 1:return(e,t)=>e?t.elapsedTime>e.elapsedTime?t:e:t;case 2:return(e,t)=>e?t.elapsedTime<e.elapsedTime?t:e:t;default:return(e,t)=>e?t.workingSetSize>e.workingSetSize?t:e:t}}async#H(e,t,s,i,n){const r=(await this.#e.getRunningProcesses(s)).filter(e).reduce(t,null);return null===r?null:r.elapsedTime>=i?r:n?(await(0,l.Wn)(i-r.elapsedTime),await this.#H(e,t,s,i,!0)):null}async#W(e,t,s,i){return await new Promise((async(n,r)=>{let a;const o=new l.Vd([(0,l.Ix)((()=>{a?.dispose(),o.dispose(),r(new u("App launch timed out."))}),s),(0,l.SO)((async()=>{const e=await t();e&&!o.disposed&&(o.dispose(),n({process:e,cleanup:a}))}),2500),(0,l.nm)((()=>c.cancel()))]);i&&o.push(i.onCancel((()=>{a?.dispose(),o.dispose(),r(new l._T("App launch canceled."))})));const c=new l.HL;try{a=await e(c)}catch(e){o.disposed||(o.dispose(),r(e))}}))}};g=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[c.Mz,a.D])],g)},68849:(e,t,s)=>{s.d(t,{H:()=>a});var i=s(15215),n=s("aurelia-framework"),r=s(19072);let a=class{#G;constructor(e){this.#G=e}async getRepoDirectories(){return(await this.#Y())?.repos||[]}#Y(){return this.#G.getCreatorConfiguration()}};a=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[r.s])],a)},70893:(e,t,s)=>{s.d(t,{L:()=>h});var i=s(76982),n=s(65692),r=s(35392),a=s(49442),o=s(38777),c=s(29844);class h{#q;constructor(e){this.#q=e}async fetchBinary(e,t){const s=this.#K(e);if(await r.promises.stat(s).then((e=>e.isFile())).catch((()=>!1)))return s;try{return await this.#X(e,t)}catch{return await this.#X(e,t)}}isLocal(e){return r.promises.stat(this.#K(e)).then((e=>e.isFile())).catch((()=>!1))}async#X(e,t){const s=await this.#J(e);return await new Promise(((c,h)=>{const l=(0,r.createWriteStream)(s,{flags:"w+"}),u=(0,i.createHash)("sha256"),d=new o.Vd([t.onCancel((()=>{d.dispose(),h(new o._T("Binary download was canceled."))})),(0,o.nm)((()=>{u.destroy(),l.end()}))]);d.push((0,o.$U)(l,"open",(()=>{const t=(0,n.get)(e.url,(e=>{d.push((0,o.$U)(e,"end",(()=>l.end()))),e.statusCode&&e.statusCode>=300?e.resume():d.push((0,o.$U)(e,"data",(e=>{l.write(e),u.update(e)})))}));d.push((0,o.$U)(t,"error",(()=>d.dispose())))}))),d.push((0,o.$U)(l,"error",(()=>{d.dispose(),h(new Error("Failed to create temporary trainer file."))}))),d.push((0,o.$U)(l,"close",(async()=>{const t=u.digest("hex");if(d.dispose(),t!==e.hash)return r.promises.rm(s).catch(a.Y),void h(new Error("Failed to download trainer."));try{c(await this.#Q(s,e))}catch(e){h(e)}})))}))}async#Q(e,t){const s=this.#K(t);return await r.promises.rename(e,s),s}#K(e){return(0,c.fj)(this.#q,this.#Z(e))}async#J(e){await r.promises.stat(this.#q).then((e=>e.isDirectory())).catch((()=>!1))||await r.promises.mkdir(this.#q);const t=(0,c.fj)(this.#q,"download");return await r.promises.stat(t).then((e=>e.isDirectory())).catch((()=>!1))||await r.promises.mkdir(t),(0,c.fj)(t,`${this.#Z(e)}.${Date.now()}.tmp`)}#Z(e){return`Trainer_${e.trainerId}_${e.hash.substring(0,10)}.dll`}}},80525:(e,t,s)=>{s.d(t,{W:()=>h});var i=s(15215),n=s(16928),r=s("aurelia-framework"),a=s(35392),o=s(19072),c=s(68849);let h=class{#ee;#G;constructor(e,t){this.#ee=e,this.#G=t}async getTrainerBinaryPath(e){const t=await this.#te(e);if(null===t)return null;const s=n.normalize(`${t}/Build/Trainer.dll`);if(!await a.promises.stat(s).then((e=>e.isFile())).catch((()=>!1)))return null;if(s.startsWith("\\\\")){const e=n.join(this.#G.getTempFolder(),"NetworkTrainer.dll");return await a.promises.writeFile(e,await a.promises.readFile(s)),e}return s}async#te(e){const t=await this.#ee.getRepoDirectories();for(const s of t){const t=n.join(s,e);if(await a.promises.stat(t).then((e=>e.isDirectory())).catch((()=>!1)))return t}return null}};h=(0,i.Cg)([(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[c.H,o.s])],h)},83802:(e,t,s)=>{s.d(t,{vO:()=>g,jR:()=>w});var i=s(15215),n=s("aurelia-framework"),r=s(20770),a=s(45660),o=s(59239),c=s(38777),h=s(53737),l=s(20489),u=s(80525),d=s(70893);let p=class{#se;#ie;constructor(e,t){this.#se=e,this.#ie=t}async fetch(e,t,s){switch(e){case"trainerlib":return await this.#ne(t,s);case"trainerlib-local":return await this.#re(t);default:return!1}}#ne(e,t){return this.#se.fetchBinary({trainerId:e.trainerId,url:e.binaryUrl,hash:e.binaryHash},t)}async#re(e){const t=await this.#ie.getTrainerBinaryPath(e.repoPath);if(!t)throw new Error("Local binary not found.");return t}};p=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[d.L,u.W])],p);class g{constructor(e,t,s=null,i){this.info=e,this.app=t,this.gameVersion=s,this.trigger=i}}class m extends Error{constructor(e){super(e),Object.setPrototypeOf(this,m.prototype)}}let w=class{#ae;#oe;#ce;#he;#D;#le;constructor(e,t,s,i){this.trainer=null,this.#D=new c._M,this.#ae=e,this.#oe=t,this.#ce=s,this.#he=i}async endTrainer(){if(this.trainer){const e=new Promise((e=>this.trainer?.onEnded((()=>e()))));this.#le?this.#le.cancel():this.trainer.dispose(),await e}}onNewTrainer(e){return this.#D.subscribe("trainer",e)}onTrainerActivated(e){return this.#D.subscribe("activated",e)}onTrainerEnded(e){return this.#D.subscribe("ended",e)}async launch(e){if(this.trainer)throw new m("Attempted to start a trainer when one is already running.");const t=e.info,s=t.blueprint,i=await this.#ue();let n=0;i.trainerLibAlpha&&(n=1,i.trainerLibAlphaSimulate&&(n=2));const r=this.#ae.make({getProcess:async t=>{const i=await this.#oe.launch(e.app,s.config?.launch,s.config?.target,t),n=()=>i.cleanup?.dispose();return r.isEnding()?n():(r.onActivated(n),r.onEnded(n)),i.process},getTrainerBinary:e=>this.#ce.fetch(t.loader,t.loaderArgs,e),connectTimeout:15e3,trainerArgs:{gameVersion:e.gameVersion,variables:Array.from(new Set(s.cheats.map((e=>e.target))))},useExternalLogger:i.trainerLibExternalConsole??!1,alphaFeatures:n,flags:t.blueprint.config?.activate?.flags});return r.addMetadata(e),r.onActivated((()=>{this.#le=null,this.#D.publish("activated",r)})),r.onEnded((()=>{this.trainer=null,this.#D.publish("ended",r)})),this.trainer=r,this.#le=new c.HL,this.#D.publish("trainer",r),await r.launch(this.#le),r}#ue(){return this.#he.state.pipe((0,a.$)(),(0,o.E)("settings")).toPromise()}dispose(){this.trainer?.dispose(),this.trainer=null,this.#D?.dispose()}};w=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[l.yo,h.ag,p,r.il])],w)},97813:(e,t,s)=>{s.d(t,{S:()=>n});var i=s(35317);class n{#B;constructor(e){this.#B={x64:`${e}\\TrainerHost_x64.exe`,ia32:`${e}\\TrainerHost_x86.exe`}}async launch(e){return new r((0,i.spawn)(this.#B[e],{detached:!1}))}}class r{#de;constructor(e){this.#de=e}get processId(){return this.#de?.pid}dispose(){if(this.#de){try{this.#de.kill()}catch{}this.#de=null}}}}}]);
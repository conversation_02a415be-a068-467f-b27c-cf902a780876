"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5727],{15270:(t,e,r)=>{r.d(e,{j:()=>h});var n=r(97228),o=r(29336),s=r(47978),i=r(1356),a=r(5964),c=r(91198),d=r(51260);const u=new WeakSet;function l(t){return(0,o.Gw)(t.startTime,t.duration)}var f=r(71511),p=r(37642),m=r(99769);function S(t,e,r=m.z){(0,f.H)(t,"interactive",(()=>{const t=r(),n=Object.assign(t.toJSON(),{entryType:a.do.RESOURCE,initiatorType:d.du,duration:t.responseEnd,traceId:(0,p.O1)(document),toJSON:()=>({...n,toJSON:void 0})});e(n)}))}function h(t,e,r,c=(0,n.J)(),f=S){t.subscribe(8,(t=>{m((()=>function(t,e,r){const n=function(t){if(!performance||!("getEntriesByName"in performance))return;const e=performance.getEntriesByName(t.url,"resource");if(!e.length||!("toJSON"in e[0]))return;const r=e.filter((t=>!u.has(t))).filter((t=>(0,d.ZU)(t)&&(0,d.AN)(t))).filter((e=>{return r=e,n=t.startClocks.relative,s=l({startTime:t.startClocks.relative,duration:t.duration}),r.startTime>=n-1&&l(r)<=(0,o.Gw)(s,1);var r,n,s}));return 1===r.length?(u.add(r[0]),r[0].toJSON()):void 0}(t),a=n?(0,o.FR)(n.startTime):t.startClocks,c=function(t,e){if(t.traceSampled&&t.traceId&&t.spanId)return{_dd:{span_id:t.spanId.toString(),trace_id:t.traceId.toString(),rule_psr:e.rulePsr}}}(t,e);if(!e.trackResources&&!c)return;const f="xhr"===t.type?"xhr":"fetch",p=n?k(n):void 0,m=n?(0,d.Yy)(n):function(t,e,r){return t.wasInPageStateDuringPeriod("frozen",e.relative,r)?void 0:r}(r,a,t.duration),S=(0,s.kg)({date:a.timeStamp,resource:{id:(0,i.lk)(),type:f,duration:(0,o.Zj)(m),method:t.method,status_code:t.status,protocol:n&&(0,d.MK)(n),url:(0,d.YR)(t.url)?(0,d.jK)(t.url):t.url,delivery_type:n&&(0,d.kc)(n)},type:"resource",_dd:{discarded:!e.trackResources}},c,p);return{startTime:a.relative,duration:m,rawRumEvent:S,domainContext:{performanceEntry:n,xhr:t.xhr,response:t.response,requestInput:t.input,requestInit:t.init,error:t.error,isAborted:t.isAborted,handlingStack:t.handlingStack}}}(t,e,r)))}));const p=(0,a.Ks)(e,{type:a.do.RESOURCE,buffered:!0}).subscribe((t=>{for(const r of t)(0,d.Ku)(r)||m((()=>y(r,e)))}));function m(e){c.push((()=>{const r=e();r&&t.notify(12,r)}))}return f(e,(t=>{m((()=>y(t,e)))})),{stop:()=>{p.unsubscribe()}}}function y(t,e){const r=(0,o.FR)(t.startTime),n=function(t,e){if(t.traceId)return{_dd:{trace_id:t.traceId,span_id:(0,c.ul)().toString(),rule_psr:e.rulePsr}}}(t,e);if(!e.trackResources&&!n)return;const a=(0,d.g0)(t),u=k(t),l=(0,d.Yy)(t),f=(0,s.kg)({date:r.timeStamp,resource:{id:(0,i.lk)(),type:a,duration:(0,o.Zj)(l),url:t.name,status_code:(p=t.responseStatus,0===p?void 0:p),protocol:(0,d.MK)(t),delivery_type:(0,d.kc)(t)},type:"resource",_dd:{discarded:!e.trackResources}},n,u);var p;return{startTime:r.relative,duration:l,rawRumEvent:f,domainContext:{performanceEntry:t}}}function k(t){const{renderBlockingStatus:e}=t;return{resource:{render_blocking_status:e,...(0,d.Rn)(t),...(0,d.mA)(t)}}}},51260:(t,e,r)=>{r.d(e,{$3:()=>g,AN:()=>S,Ku:()=>l,MK:()=>y,Rn:()=>b,YR:()=>x,Yy:()=>f,ZU:()=>m,du:()=>a,g0:()=>d,jK:()=>_,kc:()=>k,mA:()=>p});var n=r(75248),o=r(7328),s=r(29336),i=r(8822);const a="initial_document",c=[["document",t=>a===t],["xhr",t=>"xmlhttprequest"===t],["fetch",t=>"fetch"===t],["beacon",t=>"beacon"===t],["css",(t,e)=>/\.css$/i.test(e)],["js",(t,e)=>/\.js$/i.test(e)],["image",(t,e)=>["image","img","icon"].includes(t)||null!==/\.(gif|jpg|jpeg|tiff|png|svg|ico)$/i.exec(e)],["font",(t,e)=>null!==/\.(woff|eot|woff2|ttf)$/i.exec(e)],["media",(t,e)=>["audio","video"].includes(t)||null!==/\.(mp3|mp4)$/i.exec(e)]];function d(t){const e=t.name;if(!(0,n.AY)(e))return(0,o.A2)(`Failed to construct URL for "${t.name}"`),"other";const r=(0,n.L2)(e);for(const[e,n]of c)if(n(t.initiatorType,r))return e;return"other"}function u(...t){for(let e=1;e<t.length;e+=1)if(t[e-1]>t[e])return!1;return!0}function l(t){return"xmlhttprequest"===t.initiatorType||"fetch"===t.initiatorType}function f(t){const{duration:e,startTime:r,responseEnd:n}=t;return 0===e&&r<n?(0,s.vk)(r,n):e}function p(t){if(!S(t))return;const{startTime:e,fetchStart:r,workerStart:n,redirectStart:o,redirectEnd:s,domainLookupStart:i,domainLookupEnd:a,connectStart:c,secureConnectionStart:d,connectEnd:u,requestStart:l,responseStart:f,responseEnd:p}=t,m={download:h(e,f,p),first_byte:h(e,l,f)};return 0<n&&n<r&&(m.worker=h(e,n,r)),r<u&&(m.connect=h(e,c,u),c<=d&&d<=u&&(m.ssl=h(e,d,u))),r<a&&(m.dns=h(e,i,a)),e<s&&(m.redirect=h(e,o,s)),m}function m(t){return t.duration>=0}function S(t){const e=u(t.startTime,t.fetchStart,t.domainLookupStart,t.domainLookupEnd,t.connectStart,t.connectEnd,t.requestStart,t.responseStart,t.responseEnd),r=!function(t){return t.redirectEnd>t.startTime}(t)||u(t.startTime,t.redirectStart,t.redirectEnd,t.fetchStart);return e&&r}function h(t,e,r){if(t<=e&&e<=r)return{duration:(0,s.Zj)((0,s.vk)(e,r)),start:(0,s.Zj)((0,s.vk)(t,e))}}function y(t){return""===t.nextHopProtocol?void 0:t.nextHopProtocol}function k(t){return""===t.deliveryType?"other":t.deliveryType}function b(t){if(t.startTime<t.responseStart){const{encodedBodySize:e,decodedBodySize:r,transferSize:n}=t;return{size:r,encoded_body_size:e,decoded_body_size:r,transfer_size:n}}return{size:void 0,encoded_body_size:void 0,decoded_body_size:void 0,transfer_size:void 0}}function g(t){return t&&!(0,i.w)(t)}const v=/data:(.+)?(;base64)?,/g,I=24e3;function x(t){return!(t.length<=I||"data:"!==t.substring(0,5)||(t=t.substring(0,I),0))}function _(t){return`${t.match(v)[0]}[...]`}},67945:(t,e,r)=>{r.d(e,{qE:()=>l});var n=r(10100),o=r(62650),s=r(51666),i=r(29336),a=r(87967),c=r(51260),d=r(80967);let u=1;function l(t,e,r){const u=(0,d.a1)(e,r);!function(t,e,r){(0,n.G)(e).subscribe((e=>{const n=e;if((0,c.$3)(n.url))switch(n.state){case"start":r.traceXhr(n,n.xhr),n.requestIndex=f(),t.notify(7,{requestIndex:n.requestIndex,url:n.url});break;case"complete":r.clearTracingIfNeeded(n),t.notify(8,{duration:n.duration,method:n.method,requestIndex:n.requestIndex,spanId:n.spanId,startClocks:n.startClocks,status:n.status,traceId:n.traceId,traceSampled:n.traceSampled,type:"xhr",url:n.url,xhr:n.xhr,isAborted:n.isAborted,handlingStack:n.handlingStack})}}))}(t,e,u),function(t,e){(0,o.i)().subscribe((r=>{const n=r;if((0,c.$3)(n.url))switch(n.state){case"start":e.traceFetch(n),n.requestIndex=f(),t.notify(7,{requestIndex:n.requestIndex,url:n.url});break;case"resolve":!function(t,e){const r=t.response&&(0,s.i)(t.response);r&&r.body?(0,a._)(r.body,(()=>{e((0,i.vk)(t.startClocks.timeStamp,(0,i.nx)()))}),{bytesLimit:Number.POSITIVE_INFINITY,collectStreamBody:!1}):e((0,i.vk)(t.startClocks.timeStamp,(0,i.nx)()))}(n,(r=>{e.clearTracingIfNeeded(n),t.notify(8,{duration:r,method:n.method,requestIndex:n.requestIndex,responseType:n.responseType,spanId:n.spanId,startClocks:n.startClocks,status:n.status,traceId:n.traceId,traceSampled:n.traceSampled,type:"fetch",url:n.url,response:n.response,init:n.init,input:n.input,isAborted:n.isAborted,handlingStack:n.handlingStack})}))}}))}(t,u)}function f(){const t=u;return u+=1,t}}}]);
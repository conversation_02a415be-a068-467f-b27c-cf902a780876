"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8767],{9374:(e,t,s)=>{function i(e){return function(t,s,i){const n=[],r=(e="")=>({get(t,s){if("symbol"==typeof s)return;const i=e?`${e}.${s}`:s;return n.push(i),new Proxy({},r(i))}}),o=new Proxy({},r());return e(o),i.get.dependencies=n,i}}s.d(t,{_:()=>i})},"shared/pusher/index":(e,t,s)=>{s.r(t),s.d(t,{Channel:()=>r,PresenceChannel:()=>o,Pusher:()=>h,configure:()=>c});var i=s(86479),n=s(92465);class r{#e;#t;constructor(e,t){this.connection=e,this.events=new n._M,this.#t=new n.Vd,this.#e=t,this.connection.channels.push(this),this.name=t.name}is(e){return this.name.startsWith("private-")?this.name.substring(8)===e:this.name===e}send(e,t){this.#e.trigger(e,t??{})}listen(e,t){this.#e.bind(e,t);const s=(0,n.nm)((()=>this.#e.unbind(e,t)));return this.#t?.push(s),s}listenAll(e){this.#e.bind_global(e);const t=(0,n.nm)((()=>this.#e.unbind_global(e)));return this.#t?.push(t),t}clone(){return new r(this.connection,this.#e)}dispose(){this.close()}close(){if(!this.events)return;this.#t?.dispose(),this.#t=null;const e=this.connection.channels.findIndex((e=>e===this));-1!==e&&this.connection.channels.splice(e,1),0===this.connection.channels.length?this.connection.disconnect():this.connection.channels.some((e=>e.name===this.name))||this.#e.pusher.unsubscribe(this.name),this.events.publish("close"),this.events.dispose(),this.events=null}onClosed(e){return this.events?.subscribeOnce("close",e)??null}}class o extends r{#s;constructor(e,t){super(e,t),this.#s=t,this.me=t.members.me,this.members=[],t.members.each((e=>this.members.push(e))),this.listen("pusher:member_added",(e=>{this.members.push(e),this.events?.publish("member-added",e)})),this.listen("pusher:member_removed",(e=>{const t=this.members.findIndex((t=>t.id===e.id));if(-1!==t){const e=this.members[t];this.members.splice(t,1),this.events?.publish("member-removed",e)}}))}is(e){return this.name.substring(9)===e}onMemberAdded(e){return this.events?.subscribe("member-added",e)??null}onMemberRemoved(e){return this.events?.subscribe("member-removed",e)??null}clone(){return new o(this.connection,this.#s)}}class h{#i;#n;#r;#o;constructor(e,t){this.channels=[],this.bearerToken="",this.#o=new Map,this.#i=e,this.#n=t}#h(){const e=this.#r?this.#r.config:this.#n;return{...e,cluster:e.cluster||"mt1"}}get connected(){return!!this.#r}setAuthHeader(e){this.bearerToken=e}joinPrivate(e){return this.join(`private-${e}`)}joinPresence(e){return this.join(`presence-${e}`)}async join(e){if(!this.#r){const e=this.#h();this.#r=new i(this.#i,{...e,channelAuthorization:{...e.channelAuthorization,headersProvider:()=>({Authorization:this.bearerToken})}})}const t=this.channels.find((t=>t.name===e));if(t)return t.clone();const s=this.#o.get(e);if(s)return(await s).clone();const n=new Promise(((t,s)=>{if(!this.#r)return;const i=()=>{h(),t(e.startsWith("presence-")?new o(this,l):new r(this,l))},n=e=>{h(),s(e)},h=()=>{this.#o.delete(e),c?.unbind("error",n),l?.unbind("pusher:subscription_succeeded",i),l?.unbind("pusher:subscription_error",n)},c=this.#r.connection;c.bind("error",n);const l=this.#r.subscribe(e);l.bind("pusher:subscription_succeeded",i),l.bind("pusher:subscription_error",n)}));return this.#o.set(e,n),await n}disconnect(){this.#r&&(this.#r.disconnect(),this.#r=null,this.channels.forEach((e=>e.dispose())),this.channels=[])}}function c(e,t){e.container.registerInstance(h,new h(t.key,t.options))}},"shared/resources/custom-attributes/add-class-if-overflow":(e,t,s)=>{s.r(t),s.d(t,{AddClassIfOverflowCustomAttribute:()=>r});var i=s(15215),n=s("aurelia-framework");let r=class{#c;#l;#u;#d;constructor(e){this.#c=e}attached(){const e=this.#a.bind(this);this.#u=new ResizeObserver((()=>this.#a())),this.#u.observe(window.document.body),this.#c.parentElement&&(this.#d=new MutationObserver((()=>this.#a())),this.#d.observe(this.#c.parentElement,{childList:!0,subtree:!0})),this.#l=setTimeout(e)}detached(){this.#u.disconnect(),this.#d.disconnect(),this.#m()}#a(){this.#m(),this.#l=setTimeout((()=>{const e=this.#c;e.style.overflow="hidden",e.style.visibility="hidden",e.classList.remove(this.value);const t=e.scrollWidth>e.offsetWidth||e.scrollHeight>e.offsetHeight;e.classList.toggle(this.value,t),e.style.visibility="visible",e.style.overflow="initial"}))}#m(){this.#l&&(clearTimeout(this.#l),this.#l=null)}};r=(0,i.Cg)([(0,n.inject)(Element),(0,n.noView)(),(0,i.Sn)("design:paramtypes",[Element])],r)},"shared/resources/custom-attributes/close-if-click-outside":(e,t,s)=>{s.r(t),s.d(t,{CloseIfClickOutsideCustomAttribute:()=>r});var i=s(15215),n=s("aurelia-framework");let r=class{#c;constructor(e){this.#c=e,this.closeIfClickOutside=this.closeIfClickOutside.bind(this)}unbind(){document.removeEventListener("click",this.closeIfClickOutside)}valueChanged(){this.value?document.addEventListener("click",this.closeIfClickOutside):document.removeEventListener("click",this.closeIfClickOutside)}closeIfClickOutside(e){if(!document.querySelector(".app-layout")?.contains(e.target))return;let t=e.target;if(!this.#c.contains(t)){for(;t.parentNode;){if(t=t.parentNode,11===t.nodeType)return;if(this.ignoreSelector&&1===t.nodeType&&t.matches(this.ignoreSelector))return}this.value=!1}}};(0,i.Cg)([(0,n.bindable)({primaryProperty:!0}),(0,i.Sn)("design:type",Boolean)],r.prototype,"value",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],r.prototype,"ignoreSelector",void 0),r=(0,i.Cg)([(0,n.inject)(Element),(0,n.noView)(),(0,i.Sn)("design:paramtypes",[Element])],r)},"shared/resources/custom-attributes/context-menu":(e,t,s)=>{s.r(t),s.d(t,{ContextMenuCustomAttribute:()=>o});var i=s(15215),n=s("aurelia-framework");let r=null,o=class{constructor(e){this.element=e,this.disabled=!1,this.uniqueId="",this.showContextMenu=e=>{if(e.preventDefault(),r&&r!==this.contextMenu&&(r.style.visibility="hidden"),this.contextMenu){const t=this.contextMenu.offsetParent;if(!t)return;const s=t.getBoundingClientRect(),i=s.left,n=s.top,o=s.right,h=this.contextMenu.offsetWidth,c=e.clientX+h>o;this.contextMenu.style.left=c?e.clientX-i-h+"px":e.clientX-i+"px",this.contextMenu.style.top=e.clientY-n+"px",this.contextMenu.style.visibility="visible",r=this.contextMenu,this.contextMenu.addEventListener("click",this.hideContextMenu)}},this.hideContextMenu=()=>{this.contextMenu&&(this.contextMenu.style.visibility="hidden",r===this.contextMenu&&(r=null),this.contextMenu.removeEventListener("click",this.hideContextMenu))}}attached(){this.disabled||(this.contextMenu=document.getElementById(this.uniqueId),this.element.addEventListener("contextmenu",this.showContextMenu),document.addEventListener("click",this.hideContextMenu))}detached(){this.element.removeEventListener("contextmenu",this.showContextMenu),document.removeEventListener("click",this.hideContextMenu)}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Object)],o.prototype,"disabled",void 0),(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",String)],o.prototype,"uniqueId",void 0),o=(0,i.Cg)([(0,n.inject)(Element),(0,n.noView)(),(0,i.Sn)("design:paramtypes",[Element])],o)},"shared/resources/custom-attributes/swipeable":(e,t,s)=>{s.r(t),s.d(t,{SwipeableCustomAttribute:()=>r});var i=s(15215),n=s("aurelia-framework");let r=class{constructor(e){this.element=e,this.disabled=!1,this.swiped=!1,this.startX=0,this.endX=0,this.swipeThreshold=50,this.onTouchStart=e=>{e.target.closest("[data-swipe-disabled]")||(this.startX=e.touches[0].clientX,this.swiped=!1)},this.onTouchMove=e=>{e.target.closest("[data-swipe-disabled]")||(this.endX=e.touches[0].clientX,this.swiped=!0)},this.onTouchEnd=()=>{if(!this.swiped)return this.element.classList.add("swiped-right"),this.element.classList.remove("swiped-left"),void(this.swiped=!1);const e=this.startX-this.endX;Math.abs(e)>this.swipeThreshold&&("left"==(e>0?"left":"right")?(this.element.classList.add("swiped-left"),this.element.classList.remove("swiped-right")):(this.element.classList.add("swiped-right"),this.element.classList.remove("swiped-left")))}}attached(){this.init()}detatched(){this.removeEventListeners()}init(){this.disabled||(this.element.classList.contains("swiped-right")||this.element.classList.add("swiped-right"),this.element.hasAttribute("touch-listeners-attached")||(this.element.addEventListener("touchstart",this.onTouchStart),this.element.addEventListener("touchmove",this.onTouchMove),this.element.addEventListener("touchend",this.onTouchEnd),this.element.setAttribute("touch-listeners-attached","true")))}removeEventListeners(){this.element.removeEventListener("touchstart",this.onTouchStart),this.element.removeEventListener("touchmove",this.onTouchMove),this.element.removeEventListener("touchend",this.onTouchEnd),this.element.removeAttribute("touch-listeners-attached")}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Boolean)],r.prototype,"disabled",void 0),r=(0,i.Cg)([(0,n.inject)(Element),(0,n.noView)(),(0,i.Sn)("design:paramtypes",[HTMLElement])],r)}}]);
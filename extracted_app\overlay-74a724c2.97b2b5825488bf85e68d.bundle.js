"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6362],{811:(t,e,s)=>{s.d(e,{n:()=>w});var i=s(15215),n=s(16928),a=s("aurelia-dialog"),o=s("aurelia-framework"),r=s(20770),h=s(51977),c=s(69735),l=s(35392),d=s(68663),u=s("dialogs/webview-dialog"),p=s(16953),g=s(19072),m=s(20057),b=s(54995),y=s(49442),f=s(48881),v=s(38777),C=s(50643),S=s(86824),I=s(62914);let w=class{#t;#e;#s;#i;#n;#a;#o;#r;#h;#c;#l;#d;constructor(t,e,s,i,n,a,o){this.promotion=null,this.#a=t,this.#o=e,this.#r=s,this.#h=i,this.#c=n,this.#l=a,this.#d=o}attached(){this.#i=this.#d.onLocaleChanged((()=>this.refresh())),this.refresh()}detached(){this.promotion=null,this.#n?.dispose(),this.#t?.dispose(),this.#e?.dispose(),this.#s?.dispose(),this.#i?.dispose()}async refresh(t){this.#e?.dispose();try{if(!t)try{t=await this.#h.getPromotion()}catch{return}JSON.stringify(t)!==JSON.stringify(this.promotion)&&this.#u(t)}finally{this.#e=(0,v.Ix)((()=>this.refresh()),(0,S.H)(60,70))}}#u(t){this.promotion=t,this.#t?.dispose(),this.#n?.dispose(),t&&(this.#p(),this.#n=this.#l.whenVisible((()=>this.#c.event("promotion_app_banner_show",{promotionId:t.id},I.Io)))),this.#g()}audienceChanged(){this.refresh()}subscriptionChanged(t,e){JSON.stringify(e??null)!==JSON.stringify(t??null)&&this.refresh()}async showDialog(t=null){if("string"==typeof t&&this.#o.hasOpenDialog)return!1;const e=this.promotion?.components?.dialog;if(!e)return!1;if(t){if(!e.triggers.includes(t))return!1;const s=this.promotionHistory[this.promotion?.id||""]?.dialogShownAt,i=this.promotionHistory[this.promotion?.id||""]?.notificationClickedAt;if(s||i)return!1}return this.#a.dispatch(f.Lc,this.promotion?.id,"dialog",(new Date).toISOString()),await this.#r.open({route:e.route,params:e.params}),!0}#p(){const t=this.promotion?.components?.notification;t&&(this.#t=(0,v.Ix)((async()=>{if(document.hasFocus()&&!p.A.debug)return;if(!this.promotion)return;const e=this.promotion.id,s=this.promotionHistory[e],i=s?.notificationShownAt;if(i)return;const n=new C.c(t.title).addText(t.message).setActivationType("protocol").setLaunchString(this.#m(e,t.url));if(t.image){const s=await this.#b(e,t.image);s&&n.addImage(s,"appLogoOverride")}t.actions.forEach((t=>n.addAction({activationType:t.type,content:t.label,arguments:"protocol"===t.type?this.#m(e,t.arguments):t.arguments}))),await this.#l.showToast(n.toXml())&&(await this.#a.dispatch(f.Lc,e,"notification",(new Date).toISOString()),this.#c.event("promotion_notification_show",{promotionId:this.promotion.id,windowState:this.#l.visible?this.#l.minimized?"minimized":"visible":"tray"},I.Io))}),1e3*t.delay))}#m(t,e){const s=new URL(e);return"wemod:"===s.protocol&&(s.searchParams.set("trigger",`promotion:${t}/notification`),e=s.toString()),e}async#b(t,e){const s=e.match(/^data:image\/(png|jpeg|gif|webp);base64,([a-zA-Z0-9+/]+={0,2})$/);if(s){const e=n.join(this.#l.info.paths.temp,"WeMod");await l.promises.mkdir(e).catch(y.Y);const i=n.join(e,`promo-notification-${t}.${s[1]}`);return await l.promises.writeFile(i,s[2],"base64"),i}return!1}#g(){if(this.#s?.dispose(),!this.promotion||!this.promotion.endsAt)return;const t=new Date(this.promotion.endsAt);if((0,h.A)(t,Date.now()))this.#u(null);else{const e=(0,c.A)(Date.now(),t);this.#s=(0,v.Ix)((()=>this.#g()),e)}}};w=(0,i.Cg)([(0,o.autoinject)(),(0,b.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,b.$t)((t=>t.account?.subscription)),promotionHistory:(0,b.$t)((t=>t.promotionHistory))}}),(0,i.Sn)("design:paramtypes",[r.il,a.DialogService,u.WebviewDialogService,d.x,I.j0,g.s,m.F2])],w)},43544:(t,e,s)=>{s.d(e,{u:()=>l});var i=s(15215),n=s("aurelia-event-aggregator"),a=s("aurelia-framework"),o=s(4826),r=s("shared/cheats/resources/value-converters/proper-hotkey"),h=s(54995),c=s(62914);let l=class{#y;#f;#c;constructor(t,e){this.#f=new n.EventAggregator,this.#y=t,this.#c=e}attached(){this.#v()}detached(){}get hotkey(){return{...this.overlayHotkey??o.R,name:"wemod",on_keyup:!1}}get displayHotkey(){return this.getProperHotkey(this.overlayHotkey??o.R)}getProperHotkey(t){const e=[t.key];return t.alt&&e.unshift(18),t.ctrl&&e.unshift(17),t.shift&&e.unshift(16),e.map((t=>this.#y.toView(t))).join("+")}onHotkeyChanged(t){return this.#f.subscribe("hotkey-changed",t)}overlayHotkeyChanged(t,e){t!==e&&(this.#f.publish("hotkey-changed",this.hotkey),this.#c.event("overlay_hotkey_change",{hotkey:this.displayHotkey}),this.#v())}#v(){this.#c.user("overlay_hotkey",this.displayHotkey,c.Io)}};(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Object)],l.prototype,"overlayHotkey",void 0),(0,i.Cg)([(0,a.computedFrom)("overlayHotkey"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],l.prototype,"hotkey",null),(0,i.Cg)([(0,a.computedFrom)("overlayHotkey"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],l.prototype,"displayHotkey",null),l=(0,i.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{overlayHotkey:(0,h.$t)((t=>t.settings.overlayHotkey))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.ProperHotkeyValueConverter,c.j0])],l)},78576:(t,e,s)=>{s.d(e,{G:()=>v});var i=s(15215),n=s("aurelia-event-aggregator"),a=s("aurelia-framework"),o=s(20770),r=s(51977),h=s(73592),c=s(67064),l=s("dialogs/email-dialog"),d=s("dialogs/pro-onboarding-dialog"),u=s(16953),p=s(21795),g=s(54995),m=s(70236),b=s(48881),y=s(38777),f=s(78268);let v=class{#C;#f;#S;#I;#w;#T;#k;#A;#a;constructor(t,e,s,i,a,o,r){this.#f=new n.EventAggregator,this.#S=t,this.#I=e,this.#w=s,this.#T=i,this.#k=a,this.#A=o,this.#a=r}attached(){this.#C=new y.Vd([this.#I.subscribe(p.Yb,(()=>this.check())),this.#I.subscribe(p.jP,(()=>this.check()))]).pushEventListener(window,"focus",(()=>this.check())),!this.account.subscription||this.proOnStartup&&!this.#D()||this.trigger()}detached(){this.#C.dispose()}async trigger(){this.#M(),this.account.subscription?.gift||(0,m.Lt)(this.account.flags,2)||await this.#T.open(void 0,!u.A.debug),await this.#w.open({trigger:"post_pro_upgrade",mode:"post-pro-upgrade"}),this.#P()}#M(){this.settings.reduceMotion||this.#S.play({path:"static/animations/fireworks.json"})}check(){this.account.subscription||this.#k.watchFlag(512,1,!0)}openTooltip(){this.#I.publish("open-pro-onboarding-tooltip")}onSuccess(t){return this.#f.subscribe("success",t)}accountChanged(t,e){const s=!e.subscription&&t.subscription,i="active"!==e.subscription?.state&&"active"===t.subscription?.state;(s||i)&&(this.trigger(),this.#f.publish("success"))}#P(){if(this.account.subscription?.gift){const t=this.account.subscription.period;["monthly","yearly"].includes(t)&&this.#A.toast({content:`post_pro_upgrade.${t}_gift_toast_message`,i18nParams:{sender:this.account.subscription.gift.senderName},type:"gift",persist:!0,actions:[{onclick:()=>window.open("wemod://settings/account/billing","_blank"),label:"post_pro_upgrade.view_details"}]})}}#D(){if(this.account?.subscription?.gift){const t=this.account.subscription.startedAt;if(!this.lastGiftOnboardingShown||(0,r.A)(new Date(this.lastGiftOnboardingShown),new Date(t)))return this.#a.dispatch(b.vk,"lastGiftOnboardingShown"),!0}return!1}};v=(0,i.Cg)([(0,a.autoinject)(),(0,g.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,g.$t)((t=>t.account)),settings:(0,g.$t)((t=>t.settings)),proOnStartup:(0,g.$t)((t=>t.flags?.proOnStartup)),lastGiftOnboardingShown:(0,g.$t)((t=>t.timestamps?.lastGiftOnboardingShown))}}),(0,i.Sn)("design:paramtypes",[h.b,n.EventAggregator,d.ProOnboardingDialogService,l.EmailDialogService,f.s,c.l,o.il])],v)},85805:(t,e,s)=>{s.d(e,{e:()=>M,t:()=>i});var i,n=s(15215),a=s(7530),o=s("aurelia-framework"),r=s(20770),h=s(83802),c=s(60692),l=s(48100),d=s(88120),u=s(68539),p=s(62914),g=s(20489),m=s(19072),b=s(20057),y=s("shared/pusher/index"),f=s(54995),v=s(48881),C=s(38777),S=s(27958),I=s(72208),w=s(43050),T=s(96276),k=s(56705),A=s(7892);!function(t){t[t.Disconnected=0]="Disconnected",t[t.Connecting=1]="Connecting",t[t.WaitingForClient=2]="WaitingForClient",t[t.Connected=3]="Connected",t[t.ForceDisconnected=4]="ForceDisconnected"}(i||(i={}));const D=t=>({theme:t.theme,analytics:t.analytics,disableAssistant:t.disableAssistant,reduceMotion:t.reduceMotion});let M=class{#V;#H;#_;#F;#a;#L;#O;#c;#d;#l;#G;#f;#C;#E;#$;#R;#N;#j;#x;#U;#W;#z;#J;#Y;#X;constructor(t,e,s,n,a,o,r,h,c,l,d,u){this.status=i.Disconnected,this.isInFreeMobileExperiment=!1,this.#f=new C._M,this.#C=new C.Vd,this.#j=null,this.#W=[],this.#z={},this.#J=0,this.#V=t,this.#H=s,this.#U=n,this.#_=a,this.#F=o,this.#a=e,this.#L=r,this.#O=h,this.#c=c,this.#d=l,this.#l=d,this.#G=u}activate(){this.isInFreeMobileExperiment=!!this.#G.assignments.get(c.n.MakeMobileAppFree),this.#l.isInTraySinceStartup&&this.#C.push(this.#l.whenVisible((()=>this.#Z(this.remoteChannel)))),this.#C.push(this.#f).push(this.#U.onCheatStatesChanged((t=>{t.gameId===this.#R&&this.#q()}))).push(this.#d.onLocaleChanged((()=>this.#B())));const t=this.#a.state.pipe((0,l.T)((t=>t.settings)),(0,d.F)()).subscribe((t=>this.#K(t))),e=this.#a.state.pipe((0,l.T)((t=>t.account?.remoteChannel)),(0,d.F)()).subscribe((t=>this.#Z(t??""))),s=this.#a.state.pipe((0,l.T)((t=>t.account?.subscription?.remoteChannel)),(0,d.F)()).subscribe((t=>this.#Z(t??""))),i=this.#a.state.pipe((0,l.T)((t=>t.trainerNotesRead)),(0,d.F)()).subscribe((t=>this.#Q(t))),n=this.#F.onInstallationsChanged((()=>this.#tt()));this.#C.push((0,C.nm)((()=>{t.unsubscribe(),n.dispose(),i.unsubscribe(),e.unsubscribe(),s.unsubscribe()}))),this.#B()}deactivate(){this.#C.dispose()}onStatusChanged(t){return this.#f.subscribe("status",t)}get remoteChannel(){return this.isInFreeMobileExperiment?this.remoteChannelFreeMobileApp??"":this.remoteChannelSubscriber??""}#K(t){this.#Y=t.theme,this.#E&&(this.#E.send("client-theme-id",this.#Y),this.#E.send("client-settings",D(t)))}#B(){this.#X=this.#d.getEffectiveLocale().toString(),this.#E&&this.#E.send("client-language",this.#X)}#Q(t){this.#z=t,this.#tt()}#et(){this.#x&&(this.#x.dispose(),this.#x=null),this.#$=Date.now().toString(),this.#N=null,this.#W=[],this.#j=null}#tt(){if(this.status===i.Connected){let t,e=!1,s=this.#j?.getMetadata(h.vO)?.gameVersion??null,i=!1;const n=this.#z[this.#N??""]||null;this.#R&&(t=this.#F.getPreferredInstallationInfo(this.#R),t.app&&(e=!0,s??=t.version??null,i="number"==typeof t.version&&!this.#W.includes(t.version))),this.#E?.send("client-state",{instanceId:this.#$,trainerId:this.#N,trainerLoading:this.#j?.isLoading(),gameInstalled:e,gameVersion:s,needsCompatibilityWarning:i,values:this.#st(),themeId:this.#Y,settings:D(this.settings),language:this.#X,accountUuid:this.account.uuid,notesReadHash:n})}}#st(){return this.#j?.isActive()?Object.fromEntries(this.#j.values.entries()):null}async#Z(t){if(!t)return;if(this.#l.isInTraySinceStartup)return;if(this.#E&&this.#E.name===t)return;this.#E?.dispose(),this.#E=null;const e=++this.#J;t?(this.#it(i.Connecting),this.#et(),await this.#nt(t,e),this.#at()):this.#it(i.Disconnected)}async#nt(t,e){try{const s=await this.#V.joinPresence(t);e!==this.#J?s.close():(s.onMemberAdded((t=>{"infinity"===t.info.type?this.#ot():this.#rt()})),s.onMemberRemoved((()=>this.#rt())),s.listen("client-state",(()=>this.#tt())),s.listen("client-cheat-states",(t=>this.#q(t))),s.listen("client-value-changed",(t=>this.#ht(t))),s.listen("client-saved-value-changed",(t=>this.#ct(t))),s.listen("client-cheat-insructions-read",(t=>this.#lt(t))),s.listen("client-launch-trainer",(t=>this.#dt(t))),s.listen("client-trainer-notes-read",(t=>this.#ut(t))),s.listen("client-playable-games",(()=>this.#at())),s.listen("client-navigate-to-route",(t=>this.#pt(t))),s.listen("client-pin-mod",(t=>this.#gt(t))),s.listen("client-set-mod-timer",(t=>this.#mt(t))),s.listen("client-dismiss-mod-timer-message",(t=>this.#bt(t))),s.listen("client-mod-timer-messages-dismissed",(()=>this.#yt())),this.#E=s,this.#rt())}catch{setTimeout((()=>{e===this.#J&&this.#nt(t,e)}),5e3)}}disconnect(){this.#E&&(this.#E.send("client-disconnect"),this.#rt())}#rt(){const t=this.#E?.members.some((t=>"remote"===t.info.type))?i.Connected:i.WaitingForClient;this.#it(t)}#it(t){const e=this.status;this.status=t,t!==e&&(t===i.Connected&&this.#tt(),this.#f.publish("status",this.status))}#ht(t){this.#j&&t?.instanceId===this.#$&&(this.#j.isActive()?this.#j.setValue(t.name,t.value,3,t.cheatId):this.#tt())}#ct(t){t?.instanceId===this.#$&&this.#R&&this.#_.saveValue(this.#R,t.name,t.value)}setCurrentTrainer(t,e=null){const s=t?.trainerId||null,i=(s?t?.gameId:null)||null,n=(s?t?.supportedVersions:null)||[];if(s===this.#N&&e===this.#j)return;if(this.#et(),this.#x&&(this.#x.dispose(),this.#x=null),this.#N=s,this.#R=i,this.#W=[...n],!s)return this.#j=null,void this.#tt();if(this.#j=e,null===e)return void this.#tt();const a=new C.Vd;e.isActive()?this.#ft(e,a):a.push(e.onActivated((()=>this.#ft(e,a)))),a.push(e.onEnded((()=>{this.#tt()}))),a.push(e.onStateChanged((()=>this.#vt()))),this.#x=a}sendGamePlayed(t,e,s){this.#E?.send("client-game-played",{gameId:t,playedAt:e,duration:s})}#q(t){this.status===i.Connected&&(this.#E?.send("client-cheat-states",this.#U.cheatStates),this.#Ct(t?.remoteTime))}#ft(t,e){e.push(t.onValueSet((t=>{this.status===i.Connected&&3!==t.source&&this.#E?.send("client-value-changed",{instanceId:this.#$,name:t.name,value:t.value,cheatId:t.cheatId})}))),this.#tt()}#lt(t){t&&this.#R&&this.#H.markRead("remote",this.#R,t.cheatId,t.instructions)}#dt(t){this.#f.publish("launch-trainer",t?.trainerId)}#ut(t){t&&this.#a.dispatch(v.ah,t.trainerId,t.notesHash)}onLaunchTrainer(t){return this.#f.subscribe("launch-trainer",t)}#vt(){this.#j?.state===g.FX.AcquiringBinary&&this.#tt()}#pt(t){t&&this.#L.router.navigateToRoute(t.routeName,t.params,{replace:!0})}#gt(t){if(t){const e=t.pin?"pin_mod_click":"unpin_mod_click",s=t.pin?[...this.pinnedMods?.[t.gameId]||[],t.mod]:this.pinnedMods?.[t.gameId]?.filter((e=>e.uuid!==t.mod.uuid))||[];this.#a.dispatch(v.oz,t.gameId,s),this.#c.event(e,{source:"remote",gameId:t.gameId,trainerId:t.trainerId,modId:t.mod.uuid},p.Io)}}#mt(t){const e=t?.config;if(e){const s=e.cancel?"mod_timer_cancel_click":"mod_timer_start_click",i=this.#j?.getMetadata(h.vO);this.#Ct(t?.remoteTime),this.#a.dispatch(v.$Z,e),t?.firstCall&&this.#c.event(s,{duration:"loop"===e.type?String(e.start):String(e.duration),durationLoop:String(e.end),titleId:i?.info.titleId,modId:e.modId,modType:e.modType,type:e.type,source:"remote"},p.Io)}}#Ct(t){if(t){const e=Date.now(),s=e-t;this.#E?.send("client-time-sync",{latency:s,localTime:e})}}#bt(t){t&&this.#a.dispatch(v.Yt,t.timerType)}#yt(){this.status===i.Connected&&this.#E?.send("client-mod-timer-messages-dismissed",this.modTimerMessagesDismissed)}#at(){this.status===i.Connected&&this.#E?.send("client-playable-games",this.#St().slice(0,1e3).join(","))}#St(){const t=this.#O.getFilteredFeed(w.f1,{}),e=t.items.map((t=>t.gameId)).filter((t=>!!t));return t.dispose(),e}installedGameVersionsChanged(){this.#at()}catalogChanged(){this.#at()}gameHistoryChanged(){this.#at()}modTimerMessagesDismissedChanged(){this.#yt()}reconnect(){this.remoteChannel&&this.#Z(this.remoteChannel)}#ot(){this.#c.event("remote_force_disconnect",{},p.Io),this.#V.disconnect(),this.#it(i.ForceDisconnected),this.#et()}};(0,n.Cg)([(0,a.Kj)("isInFreeMobileExperiment","remoteChannelSubscriber","remoteChannelFreeMobileApp"),(0,n.Sn)("design:type",String),(0,n.Sn)("design:paramtypes",[])],M.prototype,"remoteChannel",null),M=(0,n.Cg)([(0,o.autoinject)(),(0,f.m6)({setup:"activate",teardown:"deactivate",selectors:{account:(0,f.$t)((t=>t.account)),installedGameVersions:(0,f.$t)((t=>t.installedGameVersions)),gameHistory:(0,f.$t)((t=>t.gameHistory)),catalog:(0,f.$t)((t=>t.catalog)),remoteChannelSubscriber:(0,f.$t)((t=>t.account?.subscription?.remoteChannel)),remoteChannelFreeMobileApp:(0,f.$t)((t=>t.account?.remoteChannel)),settings:(0,f.$t)((t=>t.settings)),pinnedMods:(0,f.$t)((t=>t.pinnedMods)),modTimerMessagesDismissed:(0,f.$t)((t=>t.modTimerMessagesDismissed))}}),(0,n.Sn)("design:paramtypes",[y.Pusher,r.il,I.u,A.p,k.Q,T.T,S.L,w.Y2,p.j0,b.F2,m.s,u.z])],M)},96276:(t,e,s)=>{s.d(e,{T:()=>h});var i=s(15215),n=s("aurelia-event-aggregator"),a=s("aurelia-framework"),o=s(41882),r=s(54995);let h=class{constructor(){this.#f=new n.EventAggregator}#f;activate(){}deactivate(){}getPreferredInstallationInfo(t){const e={app:null,version:null};if(!t)return e;const s=this.installedGameVersions[t];if(!s?.length)return e;for(const t of s){const s=this.installedApps[t.correlationId];if(s&&(e.app=s,e.version=t.version,s.platform===o.u))break}return e}installedAppsChanged(){this.#f.publish("installations-changed")}installedGameVersionsChanged(){this.#f.publish("installations-changed")}onInstallationsChanged(t){return this.#f.subscribe("installations-changed",t)}};h=(0,i.Cg)([(0,a.autoinject)(),(0,r.m6)({setup:"activate",teardown:"deactivate",selectors:{installedGameVersions:(0,r.$t)((t=>t.installedGameVersions)),installedApps:(0,r.$t)((t=>t.installedApps))}})],h)}}]);
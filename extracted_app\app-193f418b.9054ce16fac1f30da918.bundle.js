"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2459],{"cheats/resources/elements/remote-education-graphic.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>d});var i=o(14385),a=o.n(i),n=new URL(o(71924),o.b),r=new URL(o(83466),o.b),s=new URL(o(19180),o.b),l=new URL(o(78932),o.b),c=new URL(o(10850),o.b);const d='<template> <require from="./remote-education-graphic.scss"></require> <div class="remote-education-graphic-container"> <img class="main-image" src="'+a()(n)+'" alt=""> <img class="top-corner-overlay" src="'+a()(r)+'" alt=""> <div class="platforms-container"> <img src="'+a()(s)+'" alt=""> <img src="'+a()(l)+'" alt=""> </div> <img class="bottom-corner-overlay" src="'+a()(c)+'" alt=""> <div class="label-container"> <span class="icon-container"> <span class="icon">phone_iphone</span> </span> <div class="label">${\'remote_education_graphic.no_more_alt_tabbing\' | i18n }</div> </div> </div> </template> '},"cheats/resources/elements/remote-education-graphic.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(a()),p=l()(c);d.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,.remote-education-graphic-container .label-container .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.remote-education-graphic-container{position:relative}.remote-education-graphic-container .main-image{width:100%;border-radius:16px 16px 0 0;background-color:rgba(var(--theme--highlight--rgb), 0.2)}.remote-education-graphic-container .platforms-container{position:absolute;display:flex;gap:14px;padding:12px;top:0;right:0}.remote-education-graphic-container .top-corner-overlay{border-radius:0 16px 0 0;position:absolute;display:flex;top:0;right:0}.remote-education-graphic-container .bottom-corner-overlay{border-radius:0 16px 0 0;position:absolute;display:flex;bottom:0;left:0}.remote-education-graphic-container .label{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;max-width:224px;white-space:break-spaces;text-wrap:balance}.theme-default .remote-education-graphic-container .label{color:#fff}.theme-purple-pro .remote-education-graphic-container .label{color:#fff}.theme-green-pro .remote-education-graphic-container .label{color:#fff}.theme-orange-pro .remote-education-graphic-container .label{color:#fff}.theme-pro .remote-education-graphic-container .label{color:#fff}.remote-education-graphic-container .label-container{position:absolute;bottom:0;padding:16px;gap:8px;display:flex;flex-direction:column}.remote-education-graphic-container .label-container .icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;padding:8px}.theme-default .remote-education-graphic-container .label-container .icon{color:#fff}.theme-purple-pro .remote-education-graphic-container .label-container .icon{color:#fff}.theme-green-pro .remote-education-graphic-container .label-container .icon{color:#fff}.theme-orange-pro .remote-education-graphic-container .label-container .icon{color:#fff}.theme-pro .remote-education-graphic-container .label-container .icon{color:#fff}.remote-education-graphic-container .label-container .icon-container{max-width:fit-content;border-radius:100%;background-color:rgba(255,255,255,.25)}`,""]);const g=d},"cheats/resources/elements/remote-education-popup":(e,t,o)=>{o.r(t),o.d(t,{RemoteEducationPopup:()=>r});var i=o(15215),a=o("aurelia-framework"),n=o(62914);let r=class{#e;constructor(e){this.qrCodeOptions={width:72,margin:2},this.#e=e}attached(){this.#e.event("remote_education_popup_shown",{},n.Io)}openRemotePage(){window.open("website://remote","_blank")}};r=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[n.j0])],r)},"cheats/resources/elements/remote-education-popup.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./remote-education-popup.scss"></require> <require from="../../../shared/resources/elements/close-button"></require> <require from="../../../shared/resources/elements/pro-badge"></require> <require from="../../../resources/elements/pro-cta-label"></require> <require from="./remote-education-graphic.html"></require> <require from="../../../resources/elements/remote-qr-code"></require> <require from="../../../app/resources/elements/remote-button"></require> <div class="remote-education-popup"> <div class="remote-education-popup-body"> <div class="graphic"> <remote-education-graphic></remote-education-graphic> </div> <div class="info-download-container"> <div class="info"> <span innerhtml.bind="\'remote_education_dialog.download_for_phone\' | i18n | markdown"> </span> <ul> <li>${\'remote_education_dialog.feature_control_mods\' | i18n}</li> <li>${\'remote_education_dialog.feature_browse_maps\' | i18n}</li> <li>${\'remote_education_dialog.feautre_teleport\' | i18n}</li> </ul> </div> <div class="download"> <remote-qr-code class="download-qr" options.one-way="qrCodeOptions"></remote-qr-code> <span innerhtml.bind="\'remote_education_dialog.scan_qr\' | i18n | markdown"> </span> </div> </div> </div> </div> </template> '},"cheats/resources/elements/remote-education-popup.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n)()(a());r.push([e.id,'remote-education-popup{position:fixed;right:432px}remote-education-popup .remote-education-popup{width:400px;padding:0;border:0;border-radius:16px;background:var(--theme--background-accent) !important;border:.5px solid rgba(255,255,255,.15)}remote-education-popup .remote-education-popup-body{display:flex;flex-direction:column}remote-education-popup .remote-education-popup-body .info-download-container{display:flex;align-items:center;padding:16px}remote-education-popup .remote-education-popup-body .info-download-container .info{display:flex;flex-direction:column;gap:12px}remote-education-popup .remote-education-popup-body .info-download-container .download{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;border-left:.5px solid rgba(255,255,255,.15);padding-left:12px;margin-left:12px;text-align:center;display:flex;flex-direction:column;align-items:center;gap:8px}.theme-default remote-education-popup .remote-education-popup-body .info-download-container .download{color:rgba(255,255,255,.8)}.theme-purple-pro remote-education-popup .remote-education-popup-body .info-download-container .download{color:rgba(255,255,255,.8)}.theme-green-pro remote-education-popup .remote-education-popup-body .info-download-container .download{color:rgba(255,255,255,.8)}.theme-orange-pro remote-education-popup .remote-education-popup-body .info-download-container .download{color:rgba(255,255,255,.8)}.theme-pro remote-education-popup .remote-education-popup-body .info-download-container .download{color:rgba(255,255,255,.8)}remote-education-popup .remote-education-popup-body .info-download-container .download-qr{display:flex}remote-education-popup .remote-education-popup-body .info-download-container .download-qr canvas{border-radius:10px}remote-education-popup .remote-education-popup-body .info-download-container .download a{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;text-decoration:underline}.theme-default remote-education-popup .remote-education-popup-body .info-download-container .download a{color:#fff}.theme-purple-pro remote-education-popup .remote-education-popup-body .info-download-container .download a{color:#fff}.theme-green-pro remote-education-popup .remote-education-popup-body .info-download-container .download a{color:#fff}.theme-orange-pro remote-education-popup .remote-education-popup-body .info-download-container .download a{color:#fff}.theme-pro remote-education-popup .remote-education-popup-body .info-download-container .download a{color:#fff}remote-education-popup .remote-education-popup-body .info-download-container .download span{text-wrap:balance}remote-education-popup .remote-education-popup-body .info-download-container .info,remote-education-popup .remote-education-popup-body .info-download-container li{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px}.theme-default remote-education-popup .remote-education-popup-body .info-download-container .info,.theme-default remote-education-popup .remote-education-popup-body .info-download-container li{color:rgba(255,255,255,.8)}.theme-purple-pro remote-education-popup .remote-education-popup-body .info-download-container .info,.theme-purple-pro remote-education-popup .remote-education-popup-body .info-download-container li{color:rgba(255,255,255,.8)}.theme-green-pro remote-education-popup .remote-education-popup-body .info-download-container .info,.theme-green-pro remote-education-popup .remote-education-popup-body .info-download-container li{color:rgba(255,255,255,.8)}.theme-orange-pro remote-education-popup .remote-education-popup-body .info-download-container .info,.theme-orange-pro remote-education-popup .remote-education-popup-body .info-download-container li{color:rgba(255,255,255,.8)}.theme-pro remote-education-popup .remote-education-popup-body .info-download-container .info,.theme-pro remote-education-popup .remote-education-popup-body .info-download-container li{color:rgba(255,255,255,.8)}remote-education-popup .remote-education-popup-body .info-download-container ul{margin:0;padding-left:24px}',""]);const s=r},"cheats/resources/elements/routed-text-button":(e,t,o)=>{o.r(t),o.d(t,{RoutedTextButton:()=>n});var i=o(15215),a=o("aurelia-framework");class n{}(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],n.prototype,"route",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Object)],n.prototype,"params",void 0)},"cheats/resources/elements/routed-text-button.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./routed-text-button.scss"></require> <a route-href="route.bind: route; params.bind: params"><slot></slot><span>arrow_forward</span></a> </template> '},"cheats/resources/elements/routed-text-button.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(a()),p=l()(c);d.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,routed-text-button a span{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}routed-text-button a{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;transition:color .2s;display:flex;align-items:center;gap:4px}.theme-default routed-text-button a{color:rgba(255,255,255,.6)}.theme-purple-pro routed-text-button a{color:rgba(255,255,255,.6)}.theme-green-pro routed-text-button a{color:rgba(255,255,255,.6)}.theme-orange-pro routed-text-button a{color:rgba(255,255,255,.6)}.theme-pro routed-text-button a{color:rgba(255,255,255,.6)}.theme-default routed-text-button a:hover{color:rgba(255,255,255,.8)}.theme-purple-pro routed-text-button a:hover{color:rgba(255,255,255,.8)}.theme-green-pro routed-text-button a:hover{color:rgba(255,255,255,.8)}.theme-orange-pro routed-text-button a:hover{color:rgba(255,255,255,.8)}.theme-pro routed-text-button a:hover{color:rgba(255,255,255,.8)}routed-text-button a:hover span{opacity:1}routed-text-button a span{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;transition:opacity .2s;opacity:0;font-size:12px}`,""]);const g=d},"cheats/resources/elements/save-cheats-disable-confirm-dialog":(e,t,o)=>{o.r(t),o.d(t,{SaveCheatsDisableConfirmDialog:()=>c,SaveCheatsDisableConfirmDialogService:()=>d});var i=o(15215),a=o("aurelia-dialog"),n=o("aurelia-framework"),r=o(20770),s=o(17275),l=o(48881);let c=class{#t;constructor(e,t){this.controller=e,this.dontShowAgain=!1,this.#t=t}close(e){this.dontShowAgain&&this.#t.dispatch(l.Kc,{confirmDisablingSaveCheats:!1},"save_cheats_disable_confirm_dialog"),e?this.controller.ok(!0):this.controller.cancel()}};c=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[a.DialogController,r.il])],c);class d extends s.C{constructor(){super(...arguments),this.viewModelClass="cheats/resources/elements/save-cheats-disable-confirm-dialog"}}},"cheats/resources/elements/save-cheats-disable-confirm-dialog.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./save-cheats-disable-confirm-dialog.scss"></require> <require from="../../../shared/resources/elements/close-button"></require> <ux-dialog class="save-cheats-disable-confirm-dialog align-left"> <ux-dialog-header> <close-button if.bind="!controller.settings.lock" click.trigger="close(false)" tabindex="0"></close-button> <h1>${\'save_cheats_disable_confirm_dialog.turn_off_save_cheats\' | i18n}</h1> </ux-dialog-header> <ux-dialog-body> <p innerhtml.bind="\'save_cheats_disable_confirm_dialog.are_you_sure_message\' | i18n | markdown"></p> <div class="buttons"> <button class="secondary" click.delegate="close(false)"> ${\'save_cheats_disable_confirm_dialog.cancel\' | i18n} </button> <button class="primary" click.delegate="close(true)"> ${\'save_cheats_disable_confirm_dialog.turn_off\' | i18n} </button> </div> <label> <input type="checkbox" checked.bind="dontShowAgain"> <span class="label">${\'save_cheats_disable_confirm_dialog.dont_show_again\' | i18n}</span> </label> </ux-dialog-body> </ux-dialog> </template> '},"cheats/resources/elements/save-cheats-disable-confirm-dialog.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),s=o(4417),l=o.n(s),c=new URL(o(81206),o.b),d=r()(a()),p=l()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.save-cheats-disable-confirm-dialog{padding:20px 25px 28px;text-align:center;width:100%;max-width:350px}.save-cheats-disable-confirm-dialog h1{font-weight:700;font-size:15px;line-height:24px;font-weight:500;color:#fff;margin:0 0 12px}.save-cheats-disable-confirm-dialog p{font-weight:700;font-size:15px;line-height:24px;font-weight:500;color:rgba(255,255,255,.6);margin:0 0 25px}.save-cheats-disable-confirm-dialog p br{display:block;margin:10px 0;content:""}.save-cheats-disable-confirm-dialog .buttons{margin:-15px 0 25px -15px}.save-cheats-disable-confirm-dialog .buttons button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;margin:15px 0 0 15px}.save-cheats-disable-confirm-dialog .buttons button,.save-cheats-disable-confirm-dialog .buttons button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .save-cheats-disable-confirm-dialog .buttons button{border:1px solid #fff}}.save-cheats-disable-confirm-dialog .buttons button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.save-cheats-disable-confirm-dialog .buttons button>*:first-child{padding-left:0}.save-cheats-disable-confirm-dialog .buttons button>*:last-child{padding-right:0}.save-cheats-disable-confirm-dialog .buttons button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .save-cheats-disable-confirm-dialog .buttons button svg *{fill:CanvasText}}.save-cheats-disable-confirm-dialog .buttons button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .save-cheats-disable-confirm-dialog .buttons button svg{opacity:1}}.save-cheats-disable-confirm-dialog .buttons button img{height:50%}.save-cheats-disable-confirm-dialog .buttons button:disabled{opacity:.3}.save-cheats-disable-confirm-dialog .buttons button:disabled,.save-cheats-disable-confirm-dialog .buttons button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.save-cheats-disable-confirm-dialog .buttons button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.save-cheats-disable-confirm-dialog .buttons button:not(:disabled):hover svg{opacity:1}}.save-cheats-disable-confirm-dialog .buttons button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.save-cheats-disable-confirm-dialog .buttons button.primary{box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}@media(hover: hover){.save-cheats-disable-confirm-dialog .buttons button.primary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.save-cheats-disable-confirm-dialog .buttons button.primary:not(:disabled):active{background-color:var(--theme--highlight)}.save-cheats-disable-confirm-dialog .buttons button.secondary{background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}@media(hover: hover){.save-cheats-disable-confirm-dialog .buttons button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}.save-cheats-disable-confirm-dialog label{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;width:100%;justify-content:center}.save-cheats-disable-confirm-dialog label,.save-cheats-disable-confirm-dialog label *{cursor:pointer}.save-cheats-disable-confirm-dialog label>*:first-child{margin-right:9px}.save-cheats-disable-confirm-dialog label input[type=checkbox]{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none}.save-cheats-disable-confirm-dialog label input[type=checkbox],.save-cheats-disable-confirm-dialog label input[type=checkbox] *{cursor:pointer}.save-cheats-disable-confirm-dialog label input[type=checkbox]:checked:before{opacity:1}.save-cheats-disable-confirm-dialog label input[type=checkbox]:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${p});mask:url(${p})}.save-cheats-disable-confirm-dialog label .label{font-size:14px;line-height:21px;color:rgba(255,255,255,.5)}`,""]);const g=d},"cheats/resources/elements/save-cheats-toggle":(e,t,o)=>{o.r(t),o.d(t,{SaveCheatsToggle:()=>d});var i=o(15215),a=o("aurelia-framework"),n=o(20770),r=o(56705),s=o(54995),l=o(48881),c=o("cheats/resources/elements/save-cheats-disable-confirm-dialog");let d=class{#o;#t;constructor(e,t,o){this.#o=e,this.saveCheats=t,this.#t=o}unbind(){this.markSaveCheatsToggleSeen()}markSaveCheatsToggleSeen(){this.#t.dispatch(l.vk,"lastSaveCheatsToggleSeen")}async toggleSaveCheats(){if(this.saveCheats.canUse&&this.supported)if(this.enabled){if(this.confirmDisablingSaveCheats&&(await this.#o.open()).wasCancelled)return;this.saveCheats.disable(this.game.id,"save_cheats_toggle")}else this.saveCheats.enable(this.game.id,"save_cheats_toggle")}get enabled(){return this.supported&&this.saveCheats.enabledForGame(this.game.id)}get supported(){return(0,r.o)(this.trainer?.blueprint)}get seen(){return!!this.lastSaveCheatsToggleSeen}handleMouseLeave(){this.markSaveCheatsToggleSeen()}};(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Object)],d.prototype,"game",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Object)],d.prototype,"trainer",void 0),(0,i.Cg)([(0,a.computedFrom)("account.subscription","gamePreferences","enableSaveCheatsByDefault","supported"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],d.prototype,"enabled",null),(0,i.Cg)([(0,a.computedFrom)("trainer.blueprint.cheats"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],d.prototype,"supported",null),(0,i.Cg)([(0,a.computedFrom)("lastSaveCheatsToggleSeen"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],d.prototype,"seen",null),d=(0,i.Cg)([(0,a.autoinject)(),(0,s.m6)({selectors:{account:(0,s.$t)((e=>e.account)),gamePreferences:(0,s.$t)((e=>e.gamePreferences)),confirmDisablingSaveCheats:(0,s.$t)((e=>e.settings?.confirmDisablingSaveCheats)),enableSaveCheatsByDefault:(0,s.$t)((e=>e.settings?.enableSaveCheatsByDefault)),lastSaveCheatsToggleSeen:(0,s.$t)((e=>e.timestamps?.lastSaveCheatsToggleSeen))}}),(0,i.Sn)("design:paramtypes",[c.SaveCheatsDisableConfirmDialogService,r.Q,n.il])],d)},"cheats/resources/elements/save-cheats-toggle.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>r});var i=o(14385),a=o.n(i),n=new URL(o(51091),o.b);const r='<template mouse> <require from="./save-cheats-toggle.scss"></require> <require from="../../../pro-promos/tooltips/save-mods-tooltip"></require> <require from="../../../shared/resources/elements/pro-badge"></require> <require from="../../../shared/resources/elements/info-tooltip"></require> <require from="../../../shared/resources/elements/tooltip"></require> <div data-tooltip-trigger-for="save-mods-tooltip-large-pro-promo" click.delegate="toggleSaveCheats()" pro-cta="trigger: save_cheats_toggle; disabled.bind: saveCheats.canUse; feature: save_mods;" class="save-cheats-toggle-wrapper ${enabled && supported ? \'enabled\' : \'\'} ${supported ? \'supported\' : \'\'} ${!seen ? \'pulse\' : \'\'}" mouseleave.trigger="handleMouseLeave()" tabindex="0"> <span class="toggle"> <span class="handle"> <inline-svg src="'+a()(n)+'"></inline-svg> </span> </span> <span class="label"> ${\'save_cheats_toggle.save_cheats\' | i18n} </span> <info-tooltip direction="top-center" if.bind="saveCheats.canUse && supported">${\'save_cheats_toggle.save_cheats_info\' | i18n}</info-tooltip> <pro-badge if.bind="!saveCheats.canUse" class="small"></pro-badge> <save-mods-tooltip tooltip-id="save-mods-tooltip-large-pro-promo" if.bind="!saveCheats.canUse"></save-mods-tooltip> <tooltip class="info" direction="top-right" if.bind="saveCheats.canUse && !supported"> <div slot="content">${\'save_cheats_toggle.no_eligible_cheats\' | i18n}</div> </tooltip> </div> </template> '},"cheats/resources/elements/save-cheats-toggle.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n)()(a());r.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}body.reduce-motion coaching-tip .hotspot .ring:not(:first-child){opacity:0 !important}coaching-tip{display:inline-block;position:relative}coaching-tip .wrapper{position:relative;width:32px}coaching-tip .hotspot{--hotspot--color: var(--color--accent);position:relative;width:32px;height:32px}coaching-tip .hotspot,coaching-tip .hotspot *{cursor:pointer}coaching-tip .hotspot .ring{position:absolute;left:50%;top:50%;border-radius:50%;transition:background-color .15s,border-color .15s}coaching-tip .hotspot .ring:nth-child(1){width:37.5%;height:37.5%;margin-left:-18.75%;margin-top:-18.75%;background:var(--hotspot--color)}coaching-tip .hotspot .ring:nth-child(2){width:62.5%;height:62.5%;margin-left:-31.25%;margin-top:-31.25%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out 0s infinite}coaching-tip .hotspot .ring:nth-child(3){width:81.25%;height:81.25%;margin-left:-40.625%;margin-top:-40.625%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out .1s infinite}coaching-tip .hotspot .ring:nth-child(4){width:100%;height:100%;margin-left:-50%;margin-top:-50%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out .2s infinite}coaching-tip .popup{width:325px;padding:20px 25px 25px;border-radius:10px;background:var(--theme--secondary-background);border:1px solid rgba(255,255,255,.05);opacity:0;visibility:hidden;transition:visibility 0s .2s;z-index:1;box-shadow:0px 0px 5px rgba(17,17,17,.5)}coaching-tip .popup h5{font-weight:600;font-size:16px;line-height:25px;font-weight:700;margin:0 0 5px;color:#fff}coaching-tip .popup p{font-size:14px;line-height:22px;font-weight:500;color:rgba(255,255,255,.6);margin:0}coaching-tip .popup p a{font-weight:500;color:var(--theme--highlight);text-decoration:underline}coaching-tip .popup p a:hover{color:#fff}coaching-tip .popup p strong{font-weight:500;color:#fff}coaching-tip .popup button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}coaching-tip .popup button,coaching-tip .popup button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button{border:1px solid #fff}}coaching-tip .popup button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}coaching-tip .popup button>*:first-child{padding-left:0}coaching-tip .popup button>*:last-child{padding-right:0}coaching-tip .popup button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button svg *{fill:CanvasText}}coaching-tip .popup button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button svg{opacity:1}}coaching-tip .popup button img{height:50%}coaching-tip .popup button:disabled{opacity:.3}coaching-tip .popup button:disabled,coaching-tip .popup button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){coaching-tip .popup button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}coaching-tip .popup button:not(:disabled):hover svg{opacity:1}}coaching-tip .popup button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){coaching-tip .popup button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}coaching-tip .popup button:not(:disabled):active{background-color:var(--theme--highlight)}coaching-tip .popup hr{border:0;border-top:1px solid rgba(255,255,255,.2);margin:15px 0}coaching-tip close-button{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)) !important;display:inline-flex;width:26px;height:26px;box-shadow:none;padding:0;border-radius:50%;border:0;align-items:center;justify-content:center;transition:background-color .15s;position:absolute;right:-12.5px;top:-12.5px}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip close-button{border:1px solid #fff}}coaching-tip close-button svg{opacity:1}@media(hover: hover){coaching-tip close-button:hover{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.25)) !important}}coaching-tip.show .hotspot{--hotspot--color: var(--color--brand-blue)}coaching-tip.show .popup{animation:dialog-pop .2s ease-in-out forwards;visibility:visible;transition-delay:0s}coaching-tip.position-relative-right{margin-left:16px}coaching-tip.position-right{position:absolute;left:100%;top:50%;margin-left:16px;margin-top:-16px}coaching-tip.position-left{position:absolute;right:100%;top:50%;margin-right:16px;margin-top:-16px}coaching-tip.popup-right .popup{position:absolute;left:100%;top:0;margin-left:12px}coaching-tip.popup-left .popup{position:absolute;right:100%;top:0;margin-right:12px}coaching-tip.popup-bottom-left .popup{position:absolute;right:0px;top:100%;margin-top:12px}coaching-tip.hide-hotspot .wrapper{width:0}coaching-tip.hide-hotspot .hotspot{display:none}coaching-tip.hide-hotspot .popup{margin:0}coaching-tip.close-button-left close-button{right:initial;left:-12px}body.disable-looping-animation coaching-tip .hotspot .ring:not(:first-child){animation-fill-mode:forwards !important}@keyframes coaching-tip-hotspot{0%{opacity:0}25%{opacity:1}50%,100%{opacity:0}}.coaching-tip-highlight{animation:coaching-tip-highlight .15s linear forwards}@keyframes coaching-tip-highlight{to{box-shadow:0 0 0 1px var(--theme--highlight),0 0 40px 0 rgba(var(--theme--highlight--rgb), 0.3)}}save-cheats-toggle{position:relative;display:inline-block}save-cheats-toggle .save-cheats-toggle-wrapper{position:relative;display:inline-flex;height:44px;border-radius:100px;background:rgba(255,255,255,.1);padding:10px 16px 10px 12px;align-items:center;gap:8px}save-cheats-toggle .save-cheats-toggle-wrapper,save-cheats-toggle .save-cheats-toggle-wrapper *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) save-cheats-toggle .save-cheats-toggle-wrapper{border:1px solid #fff}}save-cheats-toggle .save-cheats-toggle-wrapper:hover{background:rgba(255,255,255,.25)}save-cheats-toggle .save-cheats-toggle-wrapper.pulse::before{content:"";display:block;position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1;background:var(--theme--highlight);opacity:.2;animation:cta--pulse .5s ease-in-out infinite alternate;border-radius:99px}save-cheats-toggle .save-cheats-toggle-wrapper .toggle{position:relative;width:31px;height:20px;border-radius:12px;background:rgba(255,255,255,.15);flex:0 0 auto}@media(forced-colors: active){body:not(.override-contrast-mode) save-cheats-toggle .save-cheats-toggle-wrapper .toggle{border:1px solid #fff}}save-cheats-toggle .save-cheats-toggle-wrapper .toggle .handle{width:16px;height:16px;border-radius:50%;display:flex;align-items:center;justify-content:center;background:rgba(255,255,255,.5);position:absolute;left:2px;top:2px;transition:left .15s ease-in-out,background-color .15s}save-cheats-toggle .save-cheats-toggle-wrapper .toggle .handle svg{width:7px;height:11px}@media(forced-colors: active){body:not(.override-contrast-mode) save-cheats-toggle .save-cheats-toggle-wrapper .toggle .handle svg *{fill:CanvasText}}save-cheats-toggle .save-cheats-toggle-wrapper .toggle .handle svg *{fill:rgba(0,0,0,.5)}save-cheats-toggle .save-cheats-toggle-wrapper .label{color:#fff;font-weight:700;font-size:14px;line-height:100%;text-align:center}@media(max-width: 1440px){save-cheats-toggle .save-cheats-toggle-wrapper .label{display:none}}save-cheats-toggle .save-cheats-toggle-wrapper pro-badge{margin-right:1px}save-cheats-toggle .save-cheats-toggle-wrapper save-cheats-tooltip{position:absolute;left:0;top:0;width:100%;height:100%}save-cheats-toggle .save-cheats-toggle-wrapper.enabled .toggle{background:linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),var(--theme--highlight)}save-cheats-toggle .save-cheats-toggle-wrapper.enabled .toggle .handle{left:calc(100% - 18px);background:#fff}save-cheats-toggle .save-cheats-toggle-wrapper.enabled .toggle .handle svg *{fill:var(--theme--highlight)}save-cheats-toggle .save-cheats-toggle-wrapper:not(.supported),save-cheats-toggle .save-cheats-toggle-wrapper:not(.supported)>*:not(info-tooltip),save-cheats-toggle .save-cheats-toggle-wrapper:not(.supported)>*:not(info-tooltip) *{cursor:not-allowed}save-cheats-toggle coaching-tip{position:absolute;left:50%;top:calc(100% - 32px/2);margin-left:calc(-32px/2)}save-cheats-toggle coaching-tip.hide-hotspot:not(.auto-show),save-cheats-toggle coaching-tip.auto-show{left:0;top:0;margin-left:-10px}',""]);const s=r},"cheats/resources/elements/time-limit-countdown":(e,t,o)=>{o.r(t),o.d(t,{TimeLimitCountdown:()=>b});var i=o(15215),a=o("aurelia-framework"),n=o(67375),r=o(67901),s=o(92998),l=o(40930),c=o(96111),d=o(30770),p=o(92465),g=o(54995),u=o(14046),h=o(83802);function m(e){if(isNaN(e))return"";const t=Math.max(Math.ceil(e/60),0),o=new Date(60*t*1e3);return`*${o.getUTCHours()}*:*${o.getUTCMinutes().toString().padStart(2,"0")}*`}let b=class{#i;#a;#n;constructor(e,t,o){this.secondsRemaining=0,this.enforcer=e,this.#a=t,this.#n=o}attached(){this.#r(),this.#i=(0,p.SO)((()=>{this.#s(),this.#l()}),1e3)}detached(){this.#i?.dispose()}secondsPlayedTodayChanged(){this.#r()}#r(){this.secondsRemaining=this.enforcer.dailyPlayLimitSeconds-this.secondsPlayedToday}#s(){this.#a.trainer&&(this.secondsRemaining=Math.max(this.secondsRemaining-1,0))}#l(){if(this.enforcer.secondsPlayedToday>=this.enforcer.dailyPlayLimitSeconds){const e=Date.now(),t=(0,n.A)((0,r.A)((0,s.A)(e,1),l.rf),l.px);this.secondsUntilReset=(0,u.Ov)(t,e)}}get timeLeft(){return m(this.secondsRemaining)}get timeUntilReset(){return m(this.secondsUntilReset)}get progress(){return 1-this.secondsRemaining/this.enforcer.dailyPlayLimitSeconds}openCheckout(){this.#n.open({trigger:"time_limit_countdown_cta",frequency:"yearly",nonInteraction:!1})}};(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Number)],b.prototype,"secondsRemaining",void 0),(0,i.Cg)([(0,a.computedFrom)("secondsRemaining"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],b.prototype,"timeLeft",null),(0,i.Cg)([(0,a.computedFrom)("secondsUntilReset"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],b.prototype,"timeUntilReset",null),(0,i.Cg)([(0,a.computedFrom)("secondsRemaining"),(0,i.Sn)("design:type",Number),(0,i.Sn)("design:paramtypes",[])],b.prototype,"progress",null),b=(0,i.Cg)([(0,g.m6)({setup:"attached",teardown:"detached",selectors:{secondsPlayedToday:(0,g.$t)((e=>e.counters?.secondsPlayedToday))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[c.Y,h.jR,d.f])],b)},"cheats/resources/elements/time-limit-countdown.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template class="${secondsRemaining <= 0 ? \'expired\' : \'\'}"> <require from="./time-limit-countdown.scss"></require> <require from="../../../shared/resources/elements/info-tooltip"></require> <div class="layout"> <template if.bind="secondsRemaining > 0"> <span class="time" innerhtml.bind="timeLeft | markdown"></span> <span class="label">${\'time_limit_countdown.free_mod_access_time_remaining\' | i18n}</span> <info-tooltip direction="top-right" detach="true"> ${\'time_limit_countdown.free_wemod_users_are_limited_to_$hours_hours_per_day\' | i18n:{hours: enforcer.dailyPlayLimitHours}} </info-tooltip> </template> <template else> <template if.bind="enforcer.e39Variant === 3"> <span class="time" innerhtml.bind="timeLeft | markdown"></span> <span class="label" innerhtml.bind="\'time_limit_countdown.daily_free_mod_access_exceeded\' | i18n | markdown"></span> <info-tooltip direction="top-right" detach="true"> ${\'time_limit_countdown.free_wemod_users_are_limited_to_$hours_hours_per_day\' | i18n:{hours: enforcer.dailyPlayLimitHours}} </info-tooltip> </template> <template else> <span class="time" innerhtml.bind="timeUntilReset | markdown"></span> <span class="label" innerhtml.bind="\'time_limit_countdown.until_free_mod_access_restored\' | i18n | markdown"></span> <info-tooltip direction="top-right" detach="true"> ${\'time_limit_countdown.free_mod_access_reset_tooltip\' | i18n} </info-tooltip> </template> </template> <button click.delegate="openCheckout()"> <span class="wrapper"> <span class="inner"> ${\'time_limit_countdown.upgrade_now\' | i18n} </span> </span> </button> </div> <div class="progress-gradient" css="--progress: ${progress}"></div> </template> '},"cheats/resources/elements/time-limit-countdown.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n)()(a());r.push([e.id,'time-limit-countdown{display:block;position:relative;z-index:0}time-limit-countdown .layout{background:var(--theme--background) linear-gradient(rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.04)) !important;height:58px;border-radius:100px;display:flex;align-items:center;padding:10px 15px;position:relative;z-index:1;margin:1.5px}time-limit-countdown .time{font-weight:600;flex:0 0 auto;font-size:17px;min-width:48px;margin-right:10px;color:rgba(255,255,255,.35);letter-spacing:1px}time-limit-countdown .time em{font-style:normal;color:rgba(255,255,255,.75)}time-limit-countdown .label{flex:1 1 auto;font-size:12px;line-height:16px;color:rgba(255,255,255,.4);margin-right:10px;display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden}time-limit-countdown .label em{font-style:normal;color:#fff}time-limit-countdown info-tooltip{margin-right:15px;flex:0 0 auto}time-limit-countdown button{flex:0 0 auto;height:28px;border-radius:100px;display:inline-flex;justify-content:center;background:linear-gradient(45deg, var(--color--pro) 0%, var(--color--brand-green) 100%);padding:0;border:0;outline:none}time-limit-countdown button .wrapper{display:flex;border:1px solid rgba(0,0,0,0);border-radius:100px;overflow:hidden;width:100%;height:100%}time-limit-countdown button .inner{font-weight:700;font-size:15px;line-height:24px;font-weight:700;background:var(--theme--background) linear-gradient(rgba(255, 255, 255, 0.04), rgba(255, 255, 255, 0.04)) !important;color:#fff;display:flex;align-items:center;justify-content:center;padding:0 10px;min-width:140px}time-limit-countdown.expired .layout{background:rgba(var(--color--accent-yellow--rgb), 0.12) !important}time-limit-countdown.expired .layout button{background:rgba(0,0,0,0);border:1px solid var(--color--accent-yellow)}time-limit-countdown.expired .layout button .inner{background:rgba(0,0,0,0) !important}time-limit-countdown:not(.expired) .progress-gradient{position:absolute;left:0;top:0;width:100%;height:100%;border-radius:100px;overflow:hidden;z-index:0}time-limit-countdown:not(.expired) .progress-gradient::after{content:"";position:absolute;left:-1.5px;top:-1.5px;right:-1.5px;bottom:-1.5px;background:linear-gradient(45deg, var(--color--brand-green) 0%, var(--color--pro) 100%);border-radius:9px;z-index:0;-webkit-mask-image:conic-gradient(transparent calc(var(--progress) * 360deg), #000 calc(var(--progress) * 360deg))}',""]);const s=r},"cheats/resources/elements/title-help-button":(e,t,o)=>{o.r(t),o.d(t,{TitleHelpButton:()=>l});var i=o(15215),a=o("aurelia-framework"),n=o(62914),r=o("dialogs/trainer-notes-dialog-small"),s=o(54995);let l=class{#e;#c;#d;constructor(e,t){this.showTooltip=!1,this.tooltipId="title-help-button",this.openTrainerNotesDialogSmall=()=>{this.#e.event("trainer_instruction_settings_click",{trainer_id:this.selectedTrainer.id,title_id:this.selectedTrainer.titleId},n.Io),this.#c.open({trainerNotes:this.selectedTrainer?.blueprint?.notes||"",selectedTrainer:this.selectedTrainer})},this.#c=e,this.#e=t}detached(){this.#d?.dispose(),this.#d=null}get trainerNotesExists(){return!!this.selectedTrainer?.blueprint?.notes}trainerNotesReadChanged(e,t){const o=this.selectedTrainer?.id;o&&e[o]!==t[o]&&e[o]&&(this.showTooltip=!0)}showTooltipChanged(e){e?(this.tooltip?.show(),setTimeout((()=>{this.showTooltip=!1}),5e3)):this.tooltip?.hide()}};(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Object)],l.prototype,"selectedTrainer",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Object)],l.prototype,"selectedGame",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],l.prototype,"titleName",void 0),(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Boolean)],l.prototype,"showTooltip",void 0),(0,i.Cg)([(0,a.computedFrom)("selectedTrainer"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],l.prototype,"trainerNotesExists",null),l=(0,i.Cg)([(0,s.m6)({selectors:{trainerNotesRead:(0,s.$t)((e=>e?.trainerNotesRead))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.TrainerNotesDialogSmallService,n.j0])],l)},"cheats/resources/elements/title-help-button.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./title-help-button.scss"></require> <require from="../../../app/resources/elements/help-menu"></require> <require from="../../../resources/custom-attributes/close-if-press-escape"></require> <div data-tooltip-trigger-for.bind="tooltipId"> <help-menu placement="game-title-header" show-game-items.bind="selectedGame.available" show-trainer-notes-button.bind="trainerNotesExists" on-trainer-notes-button-click.bind="openTrainerNotesDialogSmall" game-title.bind="titleName" title-id.bind="selectedTrainer.titleId" trainer-id.bind="selectedTrainer.id"></help-menu> </div> <wm-tooltip if.bind="trainerNotesExists" view-model.ref="tooltip" tooltip-id.bind="tooltipId" placement="bottom" trigger-method="manual" style-variant="primary"> <div slot="content" class="title-help-button-content"> <span>${\'title_help_button.find_instructions_here\' | i18n}</span> </div> </wm-tooltip> </template> '},"cheats/resources/elements/title-help-button.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(a()),p=l()(c);d.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,title-help-button>button{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}title-help-button{position:relative}title-help-button>button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:rgba(255,255,255,.8);transition:background-color .15s}title-help-button>button,title-help-button>button *{cursor:pointer}title-help-button>button:hover{background:rgba(255,255,255,.25)}title-help-button>button:before{font-family:inherit;content:"help"}title-help-button>button:hover{color:#fff}title-help-button .title-help-button-content{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-primary)}title-help-button title-help-menu{position:absolute;left:0;top:calc(100% + 12px);z-index:9999}`,""]);const g=d},"cheats/resources/elements/title-settings-button":(e,t,o)=>{o.r(t),o.d(t,{TitleSettingsButton:()=>n});var i=o(15215),a=o("aurelia-framework");let n=class{};(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",String)],n.prototype,"titleId",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Object)],n.prototype,"selectedTrainer",void 0),n=(0,i.Cg)([(0,a.autoinject)()],n)},"cheats/resources/elements/title-settings-button.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./title-settings-button.scss"></require> <require from="./title-settings-menu"></require> <require from="../../../resources/custom-attributes/detach-el"></require> <require from="../../../resources/custom-attributes/close-if-press-escape"></require> <require from="../../../shared/resources/elements/tooltip"></require> <tooltip class="info" direction="top-left"> <div slot="content"> <p>${\'title_settings_button.mod_settings\' | i18n}</p> </div> </tooltip> <button click.delegate="open = !open"></button> <title-settings-menu detach-el au-animate if.bind="open" open.bind="open" close-if-press-escape="open.bind: open" title-id.bind="titleId" selected-trainer.bind="selectedTrainer"></title-settings-menu> </template> '},"cheats/resources/elements/title-settings-button.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(a()),p=l()(c);d.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,title-settings-button>button{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}title-settings-button{position:relative}title-settings-button>button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:rgba(255,255,255,.8);transition:background-color .15s}title-settings-button>button,title-settings-button>button *{cursor:pointer}title-settings-button>button:hover{background:rgba(255,255,255,.25)}title-settings-button>button:before{font-family:inherit;content:"instant_mix"}title-settings-button>button:hover{color:#fff}title-settings-button title-settings-menu{position:absolute;left:0;top:calc(100% + 12px);z-index:9999}`,""]);const g=d},"cheats/resources/elements/title-settings-menu":(e,t,o)=>{o.r(t),o.d(t,{TitleSettingsMenu:()=>p});var i=o(15215),a=o("aurelia-framework"),n=o(20770),r=o(68663),s=o(62914),l=o(69005),c=o(54995),d=o(48881);let p=class{#p;#t;#g;#e;constructor(e,t,o,i){this.open=!1,this.gameSelectorOpen=!1,this.displayOptionsOpen=!1,this.versionHistoryOpen=!1,this.shortcutCreated=!1,this.#p=e,this.#t=t,this.#g=o,this.#e=i}bind(){this.shortcutCreated=!1}detached(){this.gameSelectorOpen=!1,this.displayOptionsOpen=!1,this.versionHistoryOpen=!1}toggleGameSelector(){this.gameSelectorOpen=!this.gameSelectorOpen,this.displayOptionsOpen=!1,this.versionHistoryOpen=!1}toggleDisplayOptions(){this.displayOptionsOpen=!this.displayOptionsOpen,this.gameSelectorOpen=!1,this.versionHistoryOpen=!1}toggleVersionHistory(){this.versionHistoryOpen=!this.versionHistoryOpen,this.displayOptionsOpen=!1,this.gameSelectorOpen=!1}get selectedGame(){if(!this.titlePreferences||!this.games)return;const e=this.titlePreferences[this.titleId]?.selectedGameId;return this.games[e]}get isPro(){return"object"==typeof this.account.subscription&&null!==this.account.subscription}async createDesktopShortcut(){this.selectedGame&&(await this.#p.create(this.selectedGame.id,"game_info_panel"),this.shortcutCreated=!0)}async resetAutoPins(){const e=this.selectedTrainer?.pinnedMods||[];await this.#t.dispatch(d.Kc,{disableAutoPins:!1},"settings"),e.length>0&&(this.#g.updatePinnedMods(this.selectedTrainer.gameId,e),this.#t.dispatch(d.oz,this.selectedTrainer.gameId,this.selectedTrainer.blueprint.cheats.filter((t=>e.includes(t.uuid)&&"pinned"!==t.category))),this.#e.event("reset_pinned_mods_click",{source:"desktop",gameId:this.selectedTrainer.gameId,trainerId:this.selectedTrainer.id},s.Io))}selectedGameChanged(){this.shortcutCreated=!1}};(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.twoWay}),(0,i.Sn)("design:type",Boolean)],p.prototype,"open",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",String)],p.prototype,"titleId",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Object)],p.prototype,"selectedTrainer",void 0),(0,i.Cg)([(0,a.computedFrom)("titlePreferences","titleId","games"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],p.prototype,"selectedGame",null),(0,i.Cg)([(0,a.computedFrom)("account.subscription"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"isPro",null),p=(0,i.Cg)([(0,a.autoinject)(),(0,c.m6)({selectors:{account:(0,c.$t)((e=>e.account)),games:(0,c.$t)((e=>e.catalog?.games)),titlePreferences:(0,c.$t)((e=>e.titlePreferences))}}),(0,i.Sn)("design:paramtypes",[l.L,n.il,r.x,s.j0])],p)},"cheats/resources/elements/title-settings-menu.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template class="au-animate"> <require from="./title-settings-menu.scss"></require> <require from="./game-selector-menu"></require> <require from="./display-options-menu"></require> <require from="./version-history-menu"></require> <div class="overlay" click.delegate="open = false"></div> <div class="container"> <nav> <button click.delegate="toggleGameSelector()" class="${gameSelectorOpen ? \'active\' : \'\'}"> <i>sports_esports</i> <span class="label">${selectedGame ? \'title_settings_menu.game_source\' : \'title_settings_menu.select_game_source\' | i18n}</span> <span class="tag" if.bind="selectedGame"> <i><img src.bind="selectedGame.platformId | platformIconSvg"></i> ${selectedGame.platformId | platformName} <template if.bind="selectedGame.edition">${selectedGame.edition}</template> </span> <i>keyboard_arrow_right</i> </button> <button click.delegate="toggleDisplayOptions()" class="${displayOptionsOpen ? \'active\' : \'\'}"> <i>space_dashboard</i> <span class="label">${\'title_settings_menu.display_options\' | i18n}</span> <i>keyboard_arrow_right</i> </button> <button if.bind="selectedGame" click.delegate="toggleVersionHistory()" class="${versionHistoryOpen ? \'active\' : \'\'}"> <i>history</i> <span class="label">${\'title_settings_menu.mods_version\' | i18n}</span> <i>keyboard_arrow_right</i> </button> <button click.delegate="createDesktopShortcut()" disabled.bind="shortcutCreated"> <i>${shortcutCreated ? "check" : "install_desktop"}</i> <span class="label"> <div>${\'title_settings_menu.create_desktop_shortcut\' | i18n}</div> <div class="description accent" if.bind="shortcutCreated"> ${\'title_settings_menu.added_to_your_desktop\' | i18n} </div> </span> </button> <button if.bind="!isPro" click.delegate="resetAutoPins()"> <i>keep</i> <span class="label"> <div>${\'title_settings_menu.reset_auto_pins\' | i18n}</div> </span> </button> </nav> </div> <game-selector-menu class="submenu" if.bind="gameSelectorOpen" title-id.bind="titleId" show-custom-exe-info.bind="true" open.bind="open"></game-selector-menu> <display-options-menu class="submenu" if.bind="displayOptionsOpen"></display-options-menu> <version-history-menu class="submenu" if.bind="versionHistoryOpen" title-id.bind="titleId" game-id.bind="selectedGame.id" selected-trainer.bind="selectedTrainer" open.bind="open"> </version-history-menu> </template> '},"cheats/resources/elements/title-settings-menu.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(a()),p=l()(c);d.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,title-settings-menu>.container>nav button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}title-settings-menu{position:relative}title-settings-menu>.overlay{position:fixed;top:0;right:0;left:0;bottom:0;background:#0d0f12;opacity:.5;z-index:-1}title-settings-menu>.container{display:flex;flex-direction:column;justify-content:flex-start;padding:8px;position:relative;width:280px;background:rgba(128,128,128,.1);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:16px}title-settings-menu>.container>nav{display:flex;flex-direction:column;gap:1px}title-settings-menu>.container>nav button{display:flex;align-items:center;gap:8px;padding:8px;background:rgba(0,0,0,0);border:none;transition:background-color .15s;border-radius:8px;text-align:left}title-settings-menu>.container>nav button:hover{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}title-settings-menu>.container>nav button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;width:20px;height:20px;display:inline-flex;align-items:center;justify-content:center;color:var(--menu__item__icon--color, rgba(255, 255, 255, 0.6));transition:color .15s}title-settings-menu>.container>nav button .label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.6));padding:2px 0;transition:color .15s;font-size:14px;line-height:20px;font-weight:700;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.8))}title-settings-menu>.container>nav button .label .description{font-weight:500;white-space:initial}title-settings-menu>.container>nav button .label .description.accent{color:var(--color--accent)}title-settings-menu>.container>nav button .tag{font-weight:700;flex:0 0 auto;font-size:10px;text-transform:uppercase;color:var(--menu__item__tag--color, rgba(255, 255, 255, 0.8));transition:color .15s;display:inline-flex;align-items:center;gap:4px}title-settings-menu>.container>nav button .tag i{opacity:.8}title-settings-menu>.container>nav button .tag img{width:12px}title-settings-menu>.container>nav button.active{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}title-settings-menu>.container>nav button:hover .tag i,title-settings-menu>.container>nav button.active .tag i{opacity:1}title-settings-menu>.container>nav button:disabled{pointer-events:none}title-settings-menu>.submenu{position:absolute;left:calc(100% + 8px);top:0}`,""]);const g=d},"cheats/resources/elements/title-sidebar":(e,t,o)=>{o.r(t),o.d(t,{TitleSidebar:()=>p});var i=o(15215),a=o("aurelia-framework"),n=o(23655),r=o(10191),s=o(96111),l=o(24008),c=o(54995),d=o(70236);let p=class{#u;constructor(e,t,o,i){this.#h=e=>{let t=e.target;for(;t&&t!==this.#u;){if(this.#m(t,e.deltaY))return;t=t.parentElement}this.titleScrollContainer&&(this.titleScrollContainer.scrollTop+=e.deltaY)},this.dailyTimeLimitEnforcer=e,this.gameHelp=t,this.overlayService=o,this.#u=i}async attached(){this.#u.addEventListener("wheel",this.#h,{passive:!0,capture:!0}),await this.dailyTimeLimitEnforcer.triggerForegroundModTimeLimitExperiment()}detached(){this.#u.removeEventListener("wheel",this.#h,{capture:!0})}#m(e,t){if(!(e instanceof HTMLElement))return!1;const{scrollHeight:o,clientHeight:i,scrollTop:a}=e;return!(o<=i)&&(t<0&&a>0||t>0&&a<o-i)}#h;get canUserAccessAssistant(){return!!this.account?.subscription}get gameSupportsAssistant(){return!!this.titleInfo.flags&&(0,d.Lt)(this.titleInfo.flags,l.D1.HasAssistant)}get shouldShowAssistant(){return this.gameSupportsAssistant&&this.canUserAccessAssistant}get gameMaps(){if(!this.game)return this.lastMaps=[],[];if(!this.selectedTrainer)return this.lastMaps;const e=this.catalog.maps.filter((e=>e.titleId===this.game.titleId));return this.lastMaps=e,e}get showTimeLimitCountdown(){return!this.account?.subscription&&(3===this.dailyTimeLimitEnforcer.e39Variant||this.dailyTimeLimitEnforcer.secondsPlayedToday>=this.dailyTimeLimitEnforcer.dailyPlayLimitSeconds)}get showModOnboarding(){return this.isTrainerAvailable&&!this.hasUsedInteractiveControls&&!this.hasUsedHotkeys&&!(0,d.Lt)(this.account?.flags,8192)}get showMaps(){return this.gameMaps?.length>0}get isTrainerAvailable(){return!(!this.game||!this.catalog)&&(!!(0,d.Lt)(this.game.flags,l.rT.Available)||this.catalog.games.hasOwnProperty(this.game.id))}get gameHelpComponents(){const e=this.catalog?.titles[this.game?.titleId];return this.gameHelp?.availableComponents[e?.slug]??[]}get active(){return!!this.account?.subscription||this.showModOnboarding||this.showMaps||this.adActive||this.showTimeLimitCountdown||this.shouldShowAssistant||this.gameHelpComponents.length>0||this.enableOverlay}get canUseGameBarOverlay(){return!!this.account?.subscription}get canUseOverlay(){return this.isCreatorOrTester||this.enableOverlay&&this.overlayService.gameSupportsOverlay(this.game?.id)&&this.overlayService.canUseOverlay}get isCreatorOrTester(){return(0,d.br)(this.account?.flags,[64,16384])}};(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Object)],p.prototype,"game",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Object)],p.prototype,"selectedTrainer",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Boolean)],p.prototype,"isAssistantOpen",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Object)],p.prototype,"titleInfo",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",HTMLElement)],p.prototype,"titleScrollContainer",void 0),(0,i.Cg)([(0,a.computedFrom)("account"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"canUserAccessAssistant",null),(0,i.Cg)([(0,a.computedFrom)("titleInfo"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"gameSupportsAssistant",null),(0,i.Cg)([(0,a.computedFrom)("gameSupportsAssistant","canUserAccessAssistant"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"shouldShowAssistant",null),(0,i.Cg)([(0,a.computedFrom)("game","catalog","selectedTrainer"),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],p.prototype,"gameMaps",null),(0,i.Cg)([(0,a.computedFrom)("dailyTimeLimitEnforcer.e39Variant","dailyTimeLimitEnforcer.secondsPlayedToday","dailyTimeLimitEnforcer.dailyPlayLimitSeconds"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"showTimeLimitCountdown",null),(0,i.Cg)([(0,a.computedFrom)("hasUsedInteractiveControls","hasUsedHotkeys","isTrainerAvailable","account.flags"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"showModOnboarding",null),(0,i.Cg)([(0,a.computedFrom)("gameMaps"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"showMaps",null),(0,i.Cg)([(0,a.computedFrom)("game.flags","catalog.games"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"isTrainerAvailable",null),(0,i.Cg)([(0,a.computedFrom)("catalog","game.titleId","gameHelp.availableComponents"),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],p.prototype,"gameHelpComponents",null),(0,i.Cg)([(0,a.computedFrom)("showModOnboarding","showMaps","adActive","showTimeLimitCountdown","shouldShowAssistant","gameHelpComponents","account","enableOverlay"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"active",null),(0,i.Cg)([(0,a.computedFrom)("account.subscription"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"canUseGameBarOverlay",null),(0,i.Cg)([(0,a.computedFrom)("enableOverlay","overlayService.canUseOverlay","isCreatorOrTester","game"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"canUseOverlay",null),(0,i.Cg)([(0,a.computedFrom)("account.flags"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"isCreatorOrTester",null),p=(0,i.Cg)([(0,c.m6)({selectors:{account:(0,c.$t)((e=>e.account)),catalog:(0,c.$t)((e=>e.catalog)),hasUsedInteractiveControls:(0,c.$t)((e=>e.flags?.hasUsedInteractiveControls)),hasUsedHotkeys:(0,c.$t)((e=>e.flags?.hasUsedHotkeys)),enableOverlay:(0,c.$t)((e=>e.settings.enableOverlay))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[s.Y,n.g,r.s,Element])],p)},"cheats/resources/elements/title-sidebar.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template class="${active ? \'active\' : \'\'}"> <require from="./title-sidebar.scss"></require> <require from="./map-banner"></require> <require from="./time-limit-countdown"></require> <require from="./mod-onboarding"></require> <require from="./overlay-button"></require> <require from="../../../app/resources/elements/remote-button"></require> <require from="../../../app/resources/elements/game-bar-overlay-button"></require> <require from="../../../app/resources/elements/title-sidebar-collapse-button"></require> <require from="../../../assistant/assistant-box"></require> <require from="../../../ads/ad-popup"></require> <require from="./overlay-button"></require> <div class="title-sidebar-wrapper"> <div class="action-buttons"> <remote-button></remote-button> <game-bar-overlay-button if.bind="!canUseOverlay && canUseGameBarOverlay"></game-bar-overlay-button> <overlay-button else game-id.bind="game.id" title-name.bind="titleInfo.name"></overlay-button> <title-sidebar-collapse-button if.bind="account.subscription && selectedTrainer"></title-sidebar-collapse-button> </div> <div class="scrollable-container"> <mod-onboarding if.bind="showModOnboarding && !isAssistantOpen" game.bind="game"></mod-onboarding> <map-banner if.bind="showMaps" collapsed.bind="isAssistantOpen" maps.bind="gameMaps"></map-banner> <time-limit-countdown if.bind="showTimeLimitCountdown && !isAssistantOpen"></time-limit-countdown> <template if.bind="gameHelpComponents.length"> <compose repeat.for="component of gameHelpComponents" view-model.bind="component.component"></compose> </template> <assistant-box is-assistant-open.two-way="isAssistantOpen" title-info.bind="titleInfo" if.bind="shouldShowAssistant"></assistant-box> </div> </div> <ad-popup active.bind="adActive" if.bind="!showModOnboarding" title-id.bind="titleInfo.id"></ad-popup> </template> '},"cheats/resources/elements/title-sidebar.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),s=o(4417),l=o.n(s),c=new URL(o(83959),o.b),d=r()(a()),p=l()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}title-sidebar{--sidebar--content-top: calc(var(--constant--appHeaderHeight) + 24px);display:block;position:relative}title-sidebar.collapsed{width:0 !important}title-sidebar.collapsed :not(.collapse-button){display:none}title-sidebar.collapsed .collapse-button{position:absolute}title-sidebar.active{width:400px}@media(min-width: 1360px){title-sidebar{width:400px}}title-sidebar .title-sidebar-wrapper{--sidebar--base-max-height: calc( 100vh - var(--sidebar--content-top) - var(--ad-popup--safe-height) - var(--promotion-banner-height, 0) + 12px );display:flex;flex-direction:column;z-index:999;position:fixed;right:24px;top:44px;width:inherit;max-height:var(--sidebar--base-max-height);height:100%;overflow-x:visible !important}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:window-inactive,title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}@media(forced-colors: active){body:not(.override-contrast-mode) title-sidebar .title-sidebar-wrapper{border:1px solid #fff}}title-sidebar .scrollable-container{overflow-y:auto;overflow-x:clip;gap:20px;display:flex;flex-direction:column}title-sidebar .scrollable-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}title-sidebar .scrollable-container::-webkit-scrollbar-thumb:window-inactive,title-sidebar .scrollable-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}title-sidebar .scrollable-container::-webkit-scrollbar-thumb:window-inactive:hover,title-sidebar .scrollable-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}@media(forced-colors: active){body:not(.override-contrast-mode) title-sidebar .scrollable-container{border:1px solid #fff}}title-sidebar .action-buttons{position:relative;display:flex;gap:8px;width:100%;justify-content:space-between;overflow:visible}title-sidebar .action-buttons>*{margin-bottom:20px;flex:1}`,""]);const g=d},"cheats/resources/elements/titles-search":(e,t,o)=>{o.r(t),o.d(t,{TitlesSearch:()=>l});var i=o(15215),a=o("aurelia-framework"),n=o(43050),r=o(98120),s=(o("resources/elements/layout-toggle"),o(54995));let l=class{#b;constructor(e){this.searchResults=[],this.searching=!1,this.#b=e}attached(){this.searchSession=this.#b.createSession((()=>this.onSearch()),"games")}detached(){this.searchSession.dispose()}onSearch(){this.searching=!0,this.searchResults=this.#b.search(this.searchSession,this.searchTerms).map((e=>{if("catalog-title"===e.resultType)return(0,n.ZT)(this.state,e.result);if("title-metadata"===e.resultType){const t=e.result;return(0,n.nP)(t,e.isInstalled??!1)}})).filter((e=>!!e)),this.onSearchComplete&&setTimeout((()=>{this.searchSession?.pendingSearchTerms||this.onSearchComplete()}))}onReset(){this.searchSession?.reset()}onClear(){this.searching&&(this.searching=!1,this.searchResults=[],this.searchSession?.reset(),this.onSearchClear&&this.onSearchClear())}clear(){this.gameSearchInput.clear()}};(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",String)],l.prototype,"searchTerms",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.fromView}),(0,i.Sn)("design:type",Array)],l.prototype,"searchResults",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.fromView}),(0,i.Sn)("design:type",Boolean)],l.prototype,"searching",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.fromView}),(0,i.Sn)("design:type",String)],l.prototype,"layout",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.fromView}),(0,i.Sn)("design:type",r.r)],l.prototype,"searchSession",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Function)],l.prototype,"onSearchComplete",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Function)],l.prototype,"onSearchClear",void 0),l=(0,i.Cg)([(0,s.m6)({selectors:{state:e=>e.state}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.p])],l)},"cheats/resources/elements/titles-search.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./titles-search.scss"></require> <require from="./game-search-input"></require> <require from="../../../resources/elements/layout-toggle"></require> <game-search-input on-clear.call="onClear()" on-reset.call="onReset()" on-search.call="onSearch()" search-terms.bind="searchTerms" searching.bind="searching" view-model.ref="gameSearchInput"></game-search-input> <layout-toggle layout.bind="layout" default="thumbnail" settings-key="titleSearchResultsLayout" source="game_search" wide.bind="true" if.bind="searching"></layout-toggle> </template> '},"cheats/resources/elements/titles-search.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n)()(a());r.push([e.id,"titles-search{display:flex;align-items:center}titles-search game-search-input{flex:1}",""]);const s=r}}]);
/*! For license information please see vendor.videojs.1ba5da5bebfe22f95d4a.bundle.js.LICENSE.txt */
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8923],{8670:e=>{var t=function(){function e(){this.maxAttempts_=1,this.delayFactor_=.1,this.fuzzFactor_=.1,this.initialDelay_=1e3,this.enabled_=!1}var t=e.prototype;return t.getIsEnabled=function(){return this.enabled_},t.enable=function(){this.enabled_=!0},t.disable=function(){this.enabled_=!1},t.reset=function(){this.maxAttempts_=1,this.delayFactor_=.1,this.fuzzFactor_=.1,this.initialDelay_=1e3,this.enabled_=!1},t.getMaxAttempts=function(){return this.maxAttempts_},t.setMaxAttempts=function(e){this.maxAttempts_=e},t.getDelayFactor=function(){return this.delayFactor_},t.setDelayFactor=function(e){this.delayFactor_=e},t.getFuzzFactor=function(){return this.fuzzFactor_},t.setFuzzFactor=function(e){this.fuzzFactor_=e},t.getInitialDelay=function(){return this.initialDelay_},t.setInitialDelay=function(e){this.initialDelay_=e},t.createRetry=function(e){var t=void 0===e?{}:e,n=t.maxAttempts,o=t.delayFactor,a=t.fuzzFactor,i=t.initialDelay;return new r({maxAttempts:n||this.maxAttempts_,delayFactor:o||this.delayFactor_,fuzzFactor:a||this.fuzzFactor_,initialDelay:i||this.initialDelay_})},e}(),r=function(){function e(e){this.maxAttempts_=e.maxAttempts,this.delayFactor_=e.delayFactor,this.fuzzFactor_=e.fuzzFactor,this.currentDelay_=e.initialDelay,this.currentAttempt_=1}var t=e.prototype;return t.moveToNextAttempt=function(){this.currentAttempt_++;var e=this.currentDelay_*this.delayFactor_;this.currentDelay_=this.currentDelay_+e},t.shouldRetry=function(){return this.currentAttempt_<this.maxAttempts_},t.getCurrentDelay=function(){return this.currentDelay_},t.getCurrentMinPossibleDelay=function(){return(1-this.fuzzFactor_)*this.currentDelay_},t.getCurrentMaxPossibleDelay=function(){return(1+this.fuzzFactor_)*this.currentDelay_},t.getCurrentFuzzedDelay=function(){var e=this.getCurrentMinPossibleDelay(),t=this.getCurrentMaxPossibleDelay();return e+Math.random()*(t-e)},e}();e.exports=t},21036:(e,t,r)=>{var n=r(89840),o=r(94634),a=r(37056),i=r(26162),s=r(8670);f.httpHandler=r(27495),f.requestInterceptorsStorage=new i,f.responseInterceptorsStorage=new i,f.retryManager=new s;var u=function(e){var t={};return e?(e.trim().split("\n").forEach((function(e){var r=e.indexOf(":"),n=e.slice(0,r).trim().toLowerCase(),o=e.slice(r+1).trim();void 0===t[n]?t[n]=o:Array.isArray(t[n])?t[n].push(o):t[n]=[t[n],o]})),t):t};function c(e,t,r){var n=e;return a(t)?(r=t,"string"==typeof e&&(n={uri:e})):n=o({},t,{uri:e}),n.callback=r,n}function f(e,t,r){return l(t=c(e,t,r))}function l(e){if(void 0===e.callback)throw new Error("callback argument missing");if(e.requestType&&f.requestInterceptorsStorage.getIsEnabled()){var t={uri:e.uri||e.url,headers:e.headers||{},body:e.body,metadata:e.metadata||{},retry:e.retry,timeout:e.timeout},r=f.requestInterceptorsStorage.execute(e.requestType,t);e.uri=r.uri,e.headers=r.headers,e.body=r.body,e.metadata=r.metadata,e.retry=r.retry,e.timeout=r.timeout}var n=!1,o=function(t,r,o){n||(n=!0,e.callback(t,r,o))};function a(){var e=void 0;if(e=d.response?d.response:d.responseText||function(e){try{if("document"===e.responseType)return e.responseXML;var t=e.responseXML&&"parsererror"===e.responseXML.documentElement.nodeName;if(""===e.responseType&&!t)return e.responseXML}catch(e){}return null}(d),_)try{e=JSON.parse(e)}catch(e){}return e}function i(t){if(clearTimeout(h),clearTimeout(e.retryTimeout),t instanceof Error||(t=new Error(""+(t||"Unknown XMLHttpRequest Error"))),t.statusCode=0,p||!f.retryManager.getIsEnabled()||!e.retry||!e.retry.shouldRetry()){if(e.requestType&&f.responseInterceptorsStorage.getIsEnabled()){var r={headers:A.headers||{},body:A.body,responseUrl:d.responseURL,responseType:d.responseType},n=f.responseInterceptorsStorage.execute(e.requestType,r);A.body=n.body,A.headers=n.headers}return o(t,A)}e.retryTimeout=setTimeout((function(){e.retry.moveToNextAttempt(),e.xhr=d,l(e)}),e.retry.getCurrentFuzzedDelay())}function s(){if(!p){var t;clearTimeout(h),clearTimeout(e.retryTimeout),t=e.useXDR&&void 0===d.status?200:1223===d.status?204:d.status;var r=A,n=null;if(0!==t?(r={body:a(),statusCode:t,method:v,headers:{},url:y,rawRequest:d},d.getAllResponseHeaders&&(r.headers=u(d.getAllResponseHeaders()))):n=new Error("Internal XMLHttpRequest Error"),e.requestType&&f.responseInterceptorsStorage.getIsEnabled()){var i={headers:r.headers||{},body:r.body,responseUrl:d.responseURL,responseType:d.responseType},s=f.responseInterceptorsStorage.execute(e.requestType,i);r.body=s.body,r.headers=s.headers}return o(n,r,r.body)}}var c,p,d=e.xhr||null;d||(d=e.cors||e.useXDR?new f.XDomainRequest:new f.XMLHttpRequest);var h,y=d.url=e.uri||e.url,v=d.method=e.method||"GET",m=e.body||e.data,g=d.headers=e.headers||{},b=!!e.sync,_=!1,A={body:void 0,headers:{},statusCode:0,method:v,url:y,rawRequest:d};if("json"in e&&!1!==e.json&&(_=!0,g.accept||g.Accept||(g.Accept="application/json"),"GET"!==v&&"HEAD"!==v&&(g["content-type"]||g["Content-Type"]||(g["Content-Type"]="application/json"),m=JSON.stringify(!0===e.json?m:e.json))),d.onreadystatechange=function(){4!==d.readyState||f.responseInterceptorsStorage.getIsEnabled()||setTimeout(s,0)},d.onload=s,d.onerror=i,d.onprogress=function(){},d.onabort=function(){p=!0,clearTimeout(e.retryTimeout)},d.ontimeout=i,d.open(v,y,!b,e.username,e.password),b||(d.withCredentials=!!e.withCredentials),!b&&e.timeout>0&&(h=setTimeout((function(){if(!p){p=!0,d.abort("timeout");var e=new Error("XMLHttpRequest timeout");e.code="ETIMEDOUT",i(e)}}),e.timeout)),d.setRequestHeader)for(c in g)g.hasOwnProperty(c)&&d.setRequestHeader(c,g[c]);else if(e.headers&&!function(e){for(var t in e)if(e.hasOwnProperty(t))return!1;return!0}(e.headers))throw new Error("Headers cannot be set on an XDomainRequest object");return"responseType"in e&&(d.responseType=e.responseType),"beforeSend"in e&&"function"==typeof e.beforeSend&&e.beforeSend(d),d.send(m||null),d}e.exports=f,e.exports.default=f,f.XMLHttpRequest=n.XMLHttpRequest||function(){},f.XDomainRequest="withCredentials"in new f.XMLHttpRequest?f.XMLHttpRequest:n.XDomainRequest,function(e,t){for(var r=0;r<e.length;r++)t(e[r])}(["get","put","post","patch","head","delete"],(function(e){f["delete"===e?"del":e]=function(t,r,n){return(r=c(t,r,n)).method=e.toUpperCase(),l(r)}}))},21320:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(47222),o=(0,n.W_)([73,68,51]),a=function e(t,r){return void 0===r&&(r=0),(t=(0,n.W_)(t)).length-r<10||!(0,n.L7)(t,o,{offset:r})?r:(r+=function(e,t){void 0===t&&(t=0);var r=(e=(0,n.W_)(e))[t+5],o=e[t+6]<<21|e[t+7]<<14|e[t+8]<<7|e[t+9];return(16&r)>>4?o+20:o+10}(t,r),e(t,r))}},26162:e=>{function t(e,t){(null==t||t>e.length)&&(t=e.length);for(var r=0,n=new Array(t);r<t;r++)n[r]=e[r];return n}var r=function(){function e(){this.typeToInterceptorsMap_=new Map,this.enabled_=!1}var r=e.prototype;return r.getIsEnabled=function(){return this.enabled_},r.enable=function(){this.enabled_=!0},r.disable=function(){this.enabled_=!1},r.reset=function(){this.typeToInterceptorsMap_=new Map,this.enabled_=!1},r.addInterceptor=function(e,t){this.typeToInterceptorsMap_.has(e)||this.typeToInterceptorsMap_.set(e,new Set);var r=this.typeToInterceptorsMap_.get(e);return!r.has(t)&&(r.add(t),!0)},r.removeInterceptor=function(e,t){var r=this.typeToInterceptorsMap_.get(e);return!(!r||!r.has(t)||(r.delete(t),0))},r.clearInterceptorsByType=function(e){return!!this.typeToInterceptorsMap_.get(e)&&(this.typeToInterceptorsMap_.delete(e),this.typeToInterceptorsMap_.set(e,new Set),!0)},r.clear=function(){return!!this.typeToInterceptorsMap_.size&&(this.typeToInterceptorsMap_=new Map,!0)},r.getForType=function(e){return this.typeToInterceptorsMap_.get(e)||new Set},r.execute=function(e,r){for(var n,o=function(e,r){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,r){if(e){if("string"==typeof e)return t(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?t(e,r):void 0}}(e))||r&&e&&"number"==typeof e.length){n&&(e=n);var o=0;return function(){return o>=e.length?{done:!0}:{done:!1,value:e[o++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(this.getForType(e));!(n=o()).done;){var a=n.value;try{r=a(r)}catch(e){}}return r},e}();e.exports=r},27098:(e,t,r)=>{r.d(t,{A:()=>a});var n=r(89840),o=r.n(n);const a=function(e,t){if(/^[a-z]+:/i.test(t))return t;/^data:/.test(e)&&(e=o().location&&o().location.href||"");var r=/^\/\//.test(e),n=!o().location&&!/\/\//i.test(e);e=new(o().URL)(e,o().location||"https://example.com");var a=new URL(t,e);return n?a.href.slice(19):r?a.href.slice(a.protocol.length):a.href}},27495:(e,t,r)=>{var n=r(89840);e.exports=function(e,t){return void 0===t&&(t=!1),function(r,o,a){if(r)e(r);else if(o.statusCode>=400&&o.statusCode<=599){var i=a;if(t)if(n.TextDecoder){var s=(void 0===(u=o.headers&&o.headers["content-type"])&&(u=""),u.toLowerCase().split(";").reduce((function(e,t){var r=t.split("="),n=r[0],o=r[1];return"charset"===n.trim()?o.trim():e}),"utf-8"));try{i=new TextDecoder(s).decode(a)}catch(e){}}else i=String.fromCharCode.apply(null,new Uint8Array(a));e({cause:i})}else e(null,a);var u}}},27860:(e,t,r)=>{r.d(t,{A:()=>i});var n=r(89840),o=r.n(n),a=function(e){return o().atob?o().atob(e):Buffer.from(e,"base64").toString("binary")};function i(e){for(var t=a(e),r=new Uint8Array(t.length),n=0;n<t.length;n++)r[n]=t.charCodeAt(n);return r}},29191:(e,t,r)=>{r.d(t,{_:()=>n});var n=function(e,t,r){t.forEach((function(t){for(var n in e.mediaGroups[t])for(var o in e.mediaGroups[t][n]){var a=e.mediaGroups[t][n][o];r(a,t,n,o)}}))}},47222:(e,t,r)=>{r.d(t,{Af:()=>d,Ar:()=>p,Bu:()=>h,L7:()=>y,Sk:()=>f,WG:()=>l,W_:()=>s,hc:()=>i});var n,o,a=r(89840),i=function(e){return"function"===ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer instanceof ArrayBuffer},s=function(e){return e instanceof Uint8Array?e:(Array.isArray(e)||i(e)||e instanceof ArrayBuffer||(e="number"!=typeof e||"number"==typeof e&&e!=e?0:[e]),new Uint8Array(e&&e.buffer||e,e&&e.byteOffset||0,e&&e.byteLength||0))},u=r.n(a)().BigInt||Number,c=[u("0x1"),u("0x100"),u("0x10000"),u("0x1000000"),u("0x100000000"),u("0x10000000000"),u("0x1000000000000"),u("0x100000000000000"),u("0x10000000000000000")],f=(n=new Uint16Array([65484]),255===(o=new Uint8Array(n.buffer,n.byteOffset,n.byteLength))[0]||o[0],function(e,t){var r=void 0===t?{}:t,n=r.signed,o=void 0!==n&&n,a=r.le,i=void 0!==a&&a;e=s(e);var f=i?"reduce":"reduceRight",l=(e[f]?e[f]:Array.prototype[f]).call(e,(function(t,r,n){var o=i?n:Math.abs(n+1-e.length);return t+u(r)*c[o]}),u(0));if(o){var p=c[e.length]/u(2)-u(1);(l=u(l))>p&&(l-=p,l-=p,l-=u(2))}return Number(l)}),l=function(e,t){var r=(void 0===t?{}:t).le,n=void 0!==r&&r;("bigint"!=typeof e&&"number"!=typeof e||"number"==typeof e&&e!=e)&&(e=0);for(var o,a=(o=e=u(e),Math.ceil(function(e){return e.toString(2).length}(o)/8)),i=new Uint8Array(new ArrayBuffer(a)),s=0;s<a;s++){var f=n?s:Math.abs(s+1-i.length);i[f]=Number(e/c[s]&u(255)),e<0&&(i[f]=Math.abs(~i[f]),i[f]-=0===s?1:2)}return i},p=function(e){if(!e)return"";e=Array.prototype.slice.call(e);var t=String.fromCharCode.apply(null,s(e));try{return decodeURIComponent(escape(t))}catch(e){}return t},d=function(e,t){if("string"!=typeof e&&e&&"function"==typeof e.toString&&(e=e.toString()),"string"!=typeof e)return new Uint8Array;t||(e=unescape(encodeURIComponent(e)));for(var r=new Uint8Array(e.length),n=0;n<e.length;n++)r[n]=e.charCodeAt(n);return r},h=function(){for(var e=arguments.length,t=new Array(e),r=0;r<e;r++)t[r]=arguments[r];if(t=t.filter((function(e){return e&&(e.byteLength||e.length)&&"string"!=typeof e})),t.length<=1)return s(t[0]);var n=t.reduce((function(e,t,r){return e+(t.byteLength||t.length)}),0),o=new Uint8Array(n),a=0;return t.forEach((function(e){e=s(e),o.set(e,a),a+=e.byteLength})),o},y=function(e,t,r){var n=void 0===r?{}:r,o=n.offset,a=void 0===o?0:o,i=n.mask,u=void 0===i?[]:i;e=s(e);var c=(t=s(t)).every?t.every:Array.prototype.every;return t.length&&e.length-a>=t.length&&c.call(t,(function(t,r){return t===(u[r]?u[r]&e[a+r]:e[a+r])}))}},60279:(e,t,r)=>{r.d(t,{BX:()=>f,Et:()=>u,TY:()=>d,UD:()=>p,YZ:()=>h,Yg:()=>y,dv:()=>v,fz:()=>l,tB:()=>c});var n=r(89840),o=r.n(n),a={mp4:/^(av0?1|avc0?[1234]|vp0?9|flac|opus|mp3|mp4a|mp4v|stpp.ttml.im1t)/,webm:/^(vp0?[89]|av0?1|opus|vorbis)/,ogg:/^(vp0?[89]|theora|flac|opus|vorbis)/,video:/^(av0?1|avc0?[1234]|vp0?[89]|hvc1|hev1|theora|mp4v)/,audio:/^(mp4a|flac|vorbis|opus|ac-[34]|ec-3|alac|mp3|speex|aac)/,text:/^(stpp.ttml.im1t)/,muxerVideo:/^(avc0?1)/,muxerAudio:/^(mp4a)/,muxerText:/a^/},i=["video","audio","text"],s=["Video","Audio","Text"],u=function(e){return e?e.replace(/avc1\.(\d+)\.(\d+)/i,(function(e,t,r){return"avc1."+("00"+Number(t).toString(16)).slice(-2)+"00"+("00"+Number(r).toString(16)).slice(-2)})):e},c=function(e){void 0===e&&(e="");var t=e.split(","),r=[];return t.forEach((function(e){var t;e=e.trim(),i.forEach((function(n){var o=a[n].exec(e.toLowerCase());if(o&&!(o.length<=1)){t=n;var i=e.substring(0,o[1].length),s=e.replace(i,"");r.push({type:i,details:s,mediaType:n})}})),t||r.push({type:e,details:"",mediaType:"unknown"})})),r},f=function(e,t){if(!e.mediaGroups.AUDIO||!t)return null;var r=e.mediaGroups.AUDIO[t];if(!r)return null;for(var n in r){var o=r[n];if(o.default&&o.playlists)return c(o.playlists[0].attributes.CODECS)}return null},l=function(e){return void 0===e&&(e=""),a.audio.test(e.trim().toLowerCase())},p=function(e){if(e&&"string"==typeof e){var t,r=e.toLowerCase().split(",").map((function(e){return u(e.trim())})),n="video";1===r.length&&l(r[0])?n="audio":1===r.length&&(void 0===(t=r[0])&&(t=""),a.text.test(t.trim().toLowerCase()))&&(n="application");var o="mp4";return r.every((function(e){return a.mp4.test(e)}))?o="mp4":r.every((function(e){return a.webm.test(e)}))?o="webm":r.every((function(e){return a.ogg.test(e)}))&&(o="ogg"),n+"/"+o+';codecs="'+e+'"'}},d=function(e,t){return void 0===e&&(e=""),void 0===t&&(t=!1),o().MediaSource&&o().MediaSource.isTypeSupported&&o().MediaSource.isTypeSupported(p(e))||t&&o().ManagedMediaSource&&o().ManagedMediaSource.isTypeSupported&&o().ManagedMediaSource.isTypeSupported(p(e))||!1},h=function(e){return void 0===e&&(e=""),e.toLowerCase().split(",").every((function(e){e=e.trim();for(var t=0;t<s.length;t++)if(a["muxer"+s[t]].test(e))return!0;return!1}))},y="mp4a.40.2",v="avc1.4d400d"},72580:(e,t,r)=>{r.d(t,{ne:()=>x,J2:()=>L});var n,o=r(47222),a=(new Uint8Array([79,112,117,115,72,101,97,100]),function(e){return"string"==typeof e?(0,o.Af)(e):e}),i=function(e){e=(0,o.W_)(e);for(var t=[],r=0;e.length>r;){var a=e[r],i=0,s=0,u=e[++s];for(s++;128&u;)i=(127&u)<<7,u=e[s],s++;i+=127&u;for(var c=0;c<n.length;c++){var f=n[c],l=f.id,p=f.parser;if(a===l){t.push(p(e.subarray(s,s+i)));break}}r+=i+s}return t};n=[{id:3,parser:function(e){var t={tag:3,id:e[0]<<8|e[1],flags:e[2],size:3,dependsOnEsId:0,ocrEsId:0,descriptors:[],url:""};if(128&t.flags&&(t.dependsOnEsId=e[t.size]<<8|e[t.size+1],t.size+=2),64&t.flags){var r=e[t.size];t.url=(0,o.Ar)(e.subarray(t.size+1,t.size+1+r)),t.size+=r}return 32&t.flags&&(t.ocrEsId=e[t.size]<<8|e[t.size+1],t.size+=2),t.descriptors=i(e.subarray(t.size))||[],t}},{id:4,parser:function(e){return{tag:4,oti:e[0],streamType:e[1],bufferSize:e[2]<<16|e[3]<<8|e[4],maxBitrate:e[5]<<24|e[6]<<16|e[7]<<8|e[8],avgBitrate:e[9]<<24|e[10]<<16|e[11]<<8|e[12],descriptors:i(e.subarray(13))}}},{id:5,parser:function(e){return{tag:5,bytes:e}}},{id:6,parser:function(e){return{tag:6,bytes:e}}}];var s=function e(t,r,n){void 0===n&&(n=!1),r=function(e){return Array.isArray(e)?e.map((function(e){return a(e)})):[a(e)]}(r),t=(0,o.W_)(t);var i=[];if(!r.length)return i;for(var s=0;s<t.length;){var u=(t[s]<<24|t[s+1]<<16|t[s+2]<<8|t[s+3])>>>0,c=t.subarray(s+4,s+8);if(0===u)break;var f=s+u;if(f>t.length){if(n)break;f=t.length}var l=t.subarray(s+8,f);(0,o.L7)(c,r[0])&&(1===r.length?i.push(l):i.push.apply(i,e(l,r.slice(1),n))),s=f}return i},u={EBML:(0,o.W_)([26,69,223,163]),DocType:(0,o.W_)([66,130]),Segment:(0,o.W_)([24,83,128,103]),SegmentInfo:(0,o.W_)([21,73,169,102]),Tracks:(0,o.W_)([22,84,174,107]),Track:(0,o.W_)([174]),TrackNumber:(0,o.W_)([215]),DefaultDuration:(0,o.W_)([35,227,131]),TrackEntry:(0,o.W_)([174]),TrackType:(0,o.W_)([131]),FlagDefault:(0,o.W_)([136]),CodecID:(0,o.W_)([134]),CodecPrivate:(0,o.W_)([99,162]),VideoTrack:(0,o.W_)([224]),AudioTrack:(0,o.W_)([225]),Cluster:(0,o.W_)([31,67,182,117]),Timestamp:(0,o.W_)([231]),TimestampScale:(0,o.W_)([42,215,177]),BlockGroup:(0,o.W_)([160]),BlockDuration:(0,o.W_)([155]),Block:(0,o.W_)([161]),SimpleBlock:(0,o.W_)([163])},c=[128,64,32,16,8,4,2,1],f=function(e,t,r,n){void 0===r&&(r=!0),void 0===n&&(n=!1);var a=function(e){for(var t=1,r=0;r<c.length&&!(e&c[r]);r++)t++;return t}(e[t]),i=e.subarray(t,t+a);return r&&((i=Array.prototype.slice.call(e,t,t+a))[0]^=c[a-1]),{length:a,value:(0,o.Sk)(i,{signed:n}),bytes:i}},l=function e(t){return"string"==typeof t?t.match(/.{1,2}/g).map((function(t){return e(t)})):"number"==typeof t?(0,o.WG)(t):t},p=function e(t,r,n){if(n>=r.length)return r.length;var a=f(r,n,!1);if((0,o.L7)(t.bytes,a.bytes))return n;var i=f(r,n+a.length);return e(t,r,n+i.length+i.value+a.length)},d=function e(t,r){r=function(e){return Array.isArray(e)?e.map((function(e){return l(e)})):[l(e)]}(r),t=(0,o.W_)(t);var n=[];if(!r.length)return n;for(var a=0;a<t.length;){var i=f(t,a,!1),s=f(t,a+i.length),u=a+i.length+s.length;127===s.value&&(s.value=p(i,t,u),s.value!==t.length&&(s.value-=u));var c=u+s.value>t.length?t.length:u+s.value,d=t.subarray(u,c);(0,o.L7)(r[0],i.bytes)&&(1===r.length?n.push(d):n=n.concat(e(d,r.slice(1)))),a+=i.length+s.length+d.length}return n},h=r(21320),y=(0,o.W_)([0,0,0,1]),v=(0,o.W_)([0,0,1]),m=(0,o.W_)([0,0,3]),g=function(e){for(var t=[],r=1;r<e.length-2;)(0,o.L7)(e.subarray(r,r+3),m)&&(t.push(r+2),r++),r++;if(0===t.length)return e;var n=e.length-t.length,a=new Uint8Array(n),i=0;for(r=0;r<n;i++,r++)i===t[0]&&(i++,t.shift()),a[r]=e[i];return a},b=function(e,t,r,n){void 0===n&&(n=1/0),e=(0,o.W_)(e),r=[].concat(r);for(var a,i=0,s=0;i<e.length&&(s<n||a);){var u=void 0;if((0,o.L7)(e.subarray(i),y)?u=4:(0,o.L7)(e.subarray(i),v)&&(u=3),u){if(s++,a)return g(e.subarray(a,i));var c=void 0;"h264"===t?c=31&e[i+u]:"h265"===t&&(c=e[i+u]>>1&63),-1!==r.indexOf(c)&&(a=i+u),i+=u+("h264"===t?1:2)}else i++}return e.subarray(0,0)},_={webm:(0,o.W_)([119,101,98,109]),matroska:(0,o.W_)([109,97,116,114,111,115,107,97]),flac:(0,o.W_)([102,76,97,67]),ogg:(0,o.W_)([79,103,103,83]),ac3:(0,o.W_)([11,119]),riff:(0,o.W_)([82,73,70,70]),avi:(0,o.W_)([65,86,73]),wav:(0,o.W_)([87,65,86,69]),"3gp":(0,o.W_)([102,116,121,112,51,103]),mp4:(0,o.W_)([102,116,121,112]),fmp4:(0,o.W_)([115,116,121,112]),mov:(0,o.W_)([102,116,121,112,113,116]),moov:(0,o.W_)([109,111,111,118]),moof:(0,o.W_)([109,111,111,102])},A={aac:function(e){var t=(0,h.A)(e);return(0,o.L7)(e,[255,16],{offset:t,mask:[255,22]})},mp3:function(e){var t=(0,h.A)(e);return(0,o.L7)(e,[255,2],{offset:t,mask:[255,6]})},webm:function(e){var t=d(e,[u.EBML,u.DocType])[0];return(0,o.L7)(t,_.webm)},mkv:function(e){var t=d(e,[u.EBML,u.DocType])[0];return(0,o.L7)(t,_.matroska)},mp4:function(e){return!A["3gp"](e)&&!A.mov(e)&&(!(!(0,o.L7)(e,_.mp4,{offset:4})&&!(0,o.L7)(e,_.fmp4,{offset:4}))||!(!(0,o.L7)(e,_.moof,{offset:4})&&!(0,o.L7)(e,_.moov,{offset:4}))||void 0)},mov:function(e){return(0,o.L7)(e,_.mov,{offset:4})},"3gp":function(e){return(0,o.L7)(e,_["3gp"],{offset:4})},ac3:function(e){var t=(0,h.A)(e);return(0,o.L7)(e,_.ac3,{offset:t})},ts:function(e){if(e.length<189&&e.length>=1)return 71===e[0];for(var t=0;t+188<e.length&&t<188;){if(71===e[t]&&71===e[t+188])return!0;t+=1}return!1},flac:function(e){var t=(0,h.A)(e);return(0,o.L7)(e,_.flac,{offset:t})},ogg:function(e){return(0,o.L7)(e,_.ogg)},avi:function(e){return(0,o.L7)(e,_.riff)&&(0,o.L7)(e,_.avi,{offset:8})},wav:function(e){return(0,o.L7)(e,_.riff)&&(0,o.L7)(e,_.wav,{offset:8})},h264:function(e){return function(e){return b(e,"h264",7,3)}(e).length},h265:function(e){return function(e){return b(e,"h265",[32,33],3)}(e).length}},T=Object.keys(A).filter((function(e){return"ts"!==e&&"h264"!==e&&"h265"!==e})).concat(["ts","h264","h265"]);T.forEach((function(e){var t=A[e];A[e]=function(e){return t((0,o.W_)(e))}}));var w=A,x=function(e){e=(0,o.W_)(e);for(var t=0;t<T.length;t++){var r=T[t];if(w[r](e))return r}return""},L=function(e){return s(e,["moof"]).length>0}},92288:(e,t,r)=>{r.d(t,{A:()=>n});var n=function(){function e(){this.listeners={}}var t=e.prototype;return t.on=function(e,t){this.listeners[e]||(this.listeners[e]=[]),this.listeners[e].push(t)},t.off=function(e,t){if(!this.listeners[e])return!1;var r=this.listeners[e].indexOf(t);return this.listeners[e]=this.listeners[e].slice(0),this.listeners[e].splice(r,1),r>-1},t.trigger=function(e){var t=this.listeners[e];if(t)if(2===arguments.length)for(var r=t.length,n=0;n<r;++n)t[n].call(this,arguments[1]);else for(var o=Array.prototype.slice.call(arguments,1),a=t.length,i=0;i<a;++i)t[i].apply(this,o)},t.dispose=function(){this.listeners={}},t.pipe=function(e){this.on("data",(function(t){e.push(t)}))},e}()},94866:(e,t,r)=>{r.d(t,{I:()=>a});var n=/^(audio|video|application)\/(x-|vnd\.apple\.)?mpegurl/i,o=/^application\/dash\+xml/i,a=function(e){return n.test(e)?"hls":o.test(e)?"dash":"application/vnd.videojs.vhs+json"===e?"vhs-json":null}}}]);
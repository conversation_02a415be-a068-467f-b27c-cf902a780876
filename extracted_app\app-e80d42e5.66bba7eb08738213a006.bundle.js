"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3042],{10704:(e,t,a)=>{a.d(t,{r:()=>b});var s=a(15215),i=a(16928),r=a("aurelia-framework"),o=a(96610),n=a(20770),c=a(19072),h=a(90231),l=a(89356),g=a(41882),d=a("services/bugsnag/index"),u=a(54995),m=a(48881),p=a(38777),f=a(86824),v=a(82174);const w=(0,o.getLogger)("installations");let b=class{#e;#t;#a;#s;#i;#r;#o;#n;#c;#h;#l;#g;constructor(e,t,a,s,i){this.#n=!1,this.#h=new Map,this.#l=new Map,this.#g=!1,this.#e=e,this.#t=t,this.#a=a,this.#s=s,this.#i=i}initialize(){return this.#d(!0)}attached(){this.#e.onClosedToTray((()=>{this.#c&&w.debug("Closed to system tray. Pausing watcher"),this.#g=!0,this.#u()})),this.#e.onRestoredFromTray((()=>{this.#g=!1,this.#m(!0)}))}detached(){this.#p(),this.#h.clear(),this.#u()}#p(){this.#o&&(this.#o.dispose(),this.#o=null)}#u(){this.#c&&(this.#c.dispose(),this.#c=null)}#m(e=!1){this.#h.size>0&&!this.#c&&!this.#g&&(e?(w.debug("Restored from system tray. Resuming watcher"),this.#c=p.lE,this.#f()):this.#c=(0,p.Ix)((()=>this.#f()),7e3))}async#d(e=!1){if(this.#o||e){this.#p();try{await this.refreshApps(),await this.#v()}finally{this.#n?(this.#n=!1,this.#d(!0)):this.#o=(0,p.Ix)((()=>this.#d()),(0,f.H)(2,3))}}}watchGame(e){const t=this.#h.get(e)??0;return 0===t&&(w.debug(`Watching game ${e}`),this.#l.set(e,0)),this.#h.set(e,t+1),this.#m(),this.#f(),(0,p.nm)((()=>this.#w(e)))}#w(e){const t=this.#h.get(e)??0;1===t?(w.debug(`Unwatching game ${e}`),this.#l.delete(e),this.#h.delete(e)):this.#h.set(e,t-1),0===this.#h.size&&this.#u()}async#f(){try{const e=Array.from(this.#h.keys()),t=Array.from(new Set(Array.prototype.concat(...e.map((e=>this.catalog.games[e]?.correlationIds??[]))))),a=Object.fromEntries((await this.#t.findApps(t)).entries());for(const t of e){const e=this.catalog.games[t];if(e&&await this.#b(e,a)){w.debug(`Versions changed for game ${t}`);const a=(this.#l.get(t)??0)+1;if(this.#l.set(t,a),!(a>=12)){this.#o?await this.#d():this.#n=!0;break}12===a&&(0,d.report)(new Error(`Watch trigger limit exceeded for ${e.platformId} game ${t}`))}}}catch(e){w.error("Failed to refresh watched games",Array.from(this.#h.keys()),e)}finally{this.#u(),this.#m()}}async refreshApps(){this.#r?await this.#r:(this.#r=this.#y(),await this.#r.finally((()=>this.#r=null)))}async#y(){const e=await this.#t.getInstalledApps(),t={},a=Object.keys(this.installedApps);let s=e.reduce(((e,a)=>{const s=`${a.platform}:${a.sku}`;if(t[s]=a,e)return!0;{const e=this.installedApps[s];if(e){if(a.location!==e.location)return!0;{const t=a.alternateLocations,s=e.alternateLocations;return Array.isArray(t)!==Array.isArray(s)||!!Array.isArray(t)&&(t.length!==s?.length||t.some((e=>!s?.includes(e))))}}return!0}}),e.length!==a.length);s||(s=a.some((e=>!t[e]))),s&&await this.#a.dispatch(m.e1,t),await this.#a.dispatch(m.vk,"appsRefreshedAt")}async#v(){const e=Object.values(this.catalog.games),t=Object.keys(this.installedVersions).length,a=new Set;let s={},i=0,r=!1;for(let t,o=0;(t=e.slice(o,o+15)).length>0;o+=15)await Promise.all(t.map((async e=>{const t=await this.#b(e,this.installedApps,a);null!==t&&(s[e.id]=t,i++)}))),(i>=5||i>0&&o+15>=e.length)&&(await this.#a.dispatch(m.K8,s),s={},i=0,r=!0);(r||a.size!==t)&&await this.#a.dispatch(m.EP,a),r&&await this.#i.report()}async#b(e,t,a=null){const s=[],r=this.installedVersions[e.id]||[];let o=!1;for(const a of e.correlationIds){const i=r.find((e=>e.correlationId===a))||null,n=await this.#k(e,t[a],i);o||=n!==i,null!==n&&s.push({gameId:e.id,correlationId:a,version:n.version,modifiedAt:n.modifiedAt,createdAt:n.createdAt})}for(const t of r){const a=this.installedApps[t.correlationId];if(a?.platform===g.u){let r=null;try{r=await this.#s.getGameVersion(i.dirname(a.location),i.basename(a.location),a.platform,t)}catch{}o||=r!==t,s.push({gameId:e.id,correlationId:t.correlationId,version:r?.version??null,modifiedAt:r?.modifiedAt??null,createdAt:r?.createdAt??null})}else e.correlationIds.includes(t.correlationId)||(o=!0)}return s.length>0&&a?.add(e.id),o?s:null}async#k(e,t,a){if(!t||!e.versionPath)return null;try{return await this.#s.getGameVersion(t.location,e.versionPath,t.platform,a??void 0)}catch{return null}}};b=(0,s.Cg)([(0,u.m6)({setup:"initialize",teardown:"detached",selectors:{catalog:(0,u.$t)((e=>e.catalog)),installedApps:(0,u.$t)((e=>e.installedApps)),installedVersions:(0,u.$t)((e=>e.installedGameVersions))}}),(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[c.s,h.D,n.il,l.Q,v.u])],b)},11717:(e,t,a)=>{a.d(t,{i:()=>u});var s=a(15215),i=a("aurelia-framework"),r=a(20770),o=a(68663),n=a(19072),c=a(20057),h=a(54995),l=a(49442),g=a(48881),d=a(62914);let u=class{#P;#a;#T;#I;#e;#A;constructor(e,t,a,s,i){this.#a=e,this.#T=t,this.#I=a,this.#e=s,this.#A=i}async activate(){this.#S(!1).catch(l.Y),this.#P=this.#T.onLocaleChanged((e=>{this.#a.dispatch(g.Kc,{language:this.#T.getPreferredLocale()?.toString()??null},e.source),this.#S(!0).catch(l.Y)}))}deactivate(){this.#P?.dispose(),this.#P=null}async#S(e){const t=this.#T.getEffectiveLocale().toString();(e||this.account.language!==t)&&(this.#A.event("locale_change",{effectiveLocale:t,preferredLocale:this.#T.getPreferredLocale()?.toString()??null,systemLocale:this.#e.info.locale,systemRegion:this.#e.info.region},d.Io),await this.#a.dispatch(g.Ui,await this.#I.setAccountLanguage(t,!this.#T.getPreferredLocale())))}};u=(0,s.Cg)([(0,i.autoinject)(),(0,h.m6)({setup:"activate",teardown:"deactivate",selectors:{account:(0,h.$t)((e=>e.account))}}),(0,s.Sn)("design:paramtypes",[r.il,c.F2,o.x,n.s,d.j0])],u)},28747:(e,t,a)=>{a.d(t,{a:()=>m});var s=a(15215),i=a("aurelia-framework"),r=a(16953),o=a(19072),n=a("services/bugsnag/index"),c=a(54995),h=a(64980);const l="https://www.google-analytics.com",g=globalThis,d=Object.freeze({crv:"P-256",ext:!0,key_ops:["verify"],kty:"EC",x:"TvK_46GZKD04AzNmtLO2AZNE95q50zddzR6TAtPHAq0",y:"uSqGv7tok8iUolBG2vx7aJJmqst3Dn90q5CSvqpebt0"});let u=null,m=class{#e;#G;#_;constructor(e){this.#_={},this.#D=function(){g.dataLayer?.push(arguments),g.dataLayer?.length&&g.dataLayer?.splice(15,1)},this.#e=e,g.dataLayer=[]}enabledChanged(e){"boolean"==typeof e&&(0,n.setEnabled)(this.enabled)}userIdChanged(e){"string"==typeof e&&this.#C({user_id:e})}async activate(){this.#e.isInTraySinceStartup&&this.#e.whenVisible((()=>{this.#G&&this.event("screen_view",{})})),this.#D("js",new Date),this.#D("consent","default",{ad_storage:"granted",ad_user_data:"granted",ad_personalization:"granted",analytics_storage:"granted"}),this.#D("set",{client_id:this.installation.id,send_page_view:!1,page_title:"",page_location:"",page_referrer:""}),this.#C({transport_url:l,user_id:this.userId??null,app_version:this.#e.info.version}),r.A.services.ga.adsTagId&&this.#D("config",r.A.services.ga.adsTagId,{groups:"ads"}),this.enabledChanged(this.enabled),this.userIdChanged(this.userId);const e=navigator.sendBeacon.bind(navigator);navigator.sendBeacon=(t,a)=>t.toString().startsWith(`${l}/g/collect?v=2`)&&"string"==typeof a?a.replaceAll("\r","").split("\n").every((a=>e(`${t}&${a}`))):e(t,a);const t=new p(r.A.appOrigin);g.gtagDocument=new v(t),this.installation.cookies?._gcl_aw&&t.setCookieValue("_gcl_aw",this.installation.cookies._gcl_aw),await this.#R()}async#R(){try{const e=await async function(){const e=await fetch("https://ga.wemod.com/bundle.js?ids="+[r.A.services.ga.measurementId,r.A.services.ga.adsTagId].filter((e=>!!e)).join(","));if(!e.ok)throw new Error(`GA bundle request failed with status ${e.status}.`);const t=e.headers.get("X-Signature");if(!t)throw new Error("Expected X-Signature header on GA bundle response.");const a=await e.arrayBuffer();if(u??=await crypto.subtle.importKey("jwk",d,{name:"ECDSA",namedCurve:"P-256"},!1,["verify"]),!await crypto.subtle.verify({name:"ECDSA",hash:"SHA-256"},u,Buffer.from(t,"base64"),a))throw new Error("Invalid signature on GA bunldle response.");return new TextDecoder("utf-8").decode(a)}(),t=document.createElement("script");t.text=e,document.head.append(t)}catch{setTimeout((()=>this.#R()),6e4)}}#D;#C(e){this.#_=Object.assign(this.#_,e),this.#D("config",r.A.services.ga.measurementId,this.#_)}event(e,t){this.enabled&&this.#D("event",e,(0,h.u)(t))}adEvent(e,t={}){this.enabled&&r.A.services.ga.adsTagId&&this.#D("event","conversion",(0,h.u)({...t,send_to:`${r.A.services.ga.adsTagId}/${e}`}))}user(e,t){this.#D("set","user_properties",{[e]:t})}screenView(e){this.#G&&e.name===this.#G.name&&e.class===this.#G.class||(this.#C({firebase_screen:e.name,firebase_screen_class:e.class}),this.#e.isInTraySinceStartup||this.event("screen_view",{firebase_previous_screen:this.#G?.name??null,firebase_previous_class:this.#G?.class??null,...e.params||null}),this.#G={name:e.name,class:e.class})}};m=(0,s.Cg)([(0,i.autoinject)(),(0,c.m6)({setup:"activate",teardown:"deactivate",selectors:{installation:(0,c.$t)((e=>e.installation)),userId:(0,c.$t)((e=>e.account?.uuid)),enabled:(0,c.$t)((e=>e.settings?.analytics))}}),(0,s.Sn)("design:paramtypes",[o.s])],m);class p{#j;constructor(e){this.#j=e}getCurrentUrl(){return this.#j+document.location.hash.substring(5)}getCookies(){return Object.entries(this.#L()).map((e=>e.join("="))).join("; ")}setCookie(e){const[t,a]=e.split(";",2)[0].trim().split("=",2);this.setCookieValue(t,a)}setCookieValue(e,t){const a=this.#L();a&&(a[e]=t||""),localStorage.setItem("gaCookie",JSON.stringify(a))}#L(){try{return JSON.parse(localStorage.getItem("gaCookie")||"{}")}catch{return{}}}}class f{constructor(e){this.helper=e;const t=this.url;this.origin=t.origin,this.protocol=t.protocol,this.host=t.hostname,this.hostname=t.hostname,this.port=t.port}get url(){return new URL(this.href)}get href(){return this.helper.getCurrentUrl()}get pathname(){return this.url.pathname}get search(){return this.url.search}get[Symbol.toStringTag](){return this.href}toString(){return this.href}}class v{constructor(e){this.helper=e,this.location=new f(e)}get cookie(){return this.helper.getCookies()}set cookie(e){this.helper.setCookie(e)}}},58293:(e,t,a)=>{a.d(t,{L:()=>d});var s=a(15215),i=a("aurelia-framework"),r=a(20770),o=a(62914),n=a(67064),c=a(53737),h=a(90231),l=a(54995),g=a(48881);let d=class{#V;#A;#a;#$;constructor(e,t,a,s){this.#V=e,this.#A=t,this.#a=a,this.#$=s}attached(){}detached(){}launch(e,t,a){this.#V.launchAndForget(e).then((()=>{this.#A.event("game_launch",this.#W({},e,t,a),o.Io),this.#a.dispatch(g.ui,t,(new Date).toISOString(),0)})).catch((s=>{this.#A.event("game_launch_error",this.#W({code:s instanceof h.X?s.errorCode:null},e,t,a),o.Io),this.#$.toast({type:"alert",content:"launch_without_mods_button.launch_failed"})}))}#W(e,t,a,s){const i=this.catalog.games[a],r=this.catalog.titles[i?.titleId],o=Object.values(this.correlatedUnavailableTitles).find((e=>e.games.find((e=>e.id===a)))),n=o?.games.find((e=>e.id===a)),c=r||o,h=i||n;return{...e,trigger:s,appPlatform:t.platform??null,appSku:t.sku??null,gameId:a,titleId:c?.id??null,titleName:c?.name??null,gamePlatform:h?.platformId??null,gameEdition:h?.edition??null}}};d=(0,s.Cg)([(0,i.autoinject)(),(0,l.m6)({setup:"attached",teardown:"detached",selectors:{catalog:(0,l.$t)((e=>e.catalog)),correlatedUnavailableTitles:(0,l.$t)((e=>e.correlatedUnavailableTitles))}}),(0,s.Sn)("design:paramtypes",[c.ag,o.j0,r.il,n.l])],d)},85818:(e,t,a)=>{a.d(t,{o:()=>_});var s=a(15215),i=a("aurelia-event-aggregator"),r=a("aurelia-framework"),o=a(20770),n=a(68502),c=a("dialogs/changelog-dialog"),h=a(71341),l=a("dialogs/fullscreen-webview-dialog"),g=a("dialogs/game-guide-nps-dialog"),d=a(43775),u=a("dialogs/maps-nps-dialog"),m=a("dialogs/nps-dialog"),p=a(30770),f=a("dialogs/webview-dialog"),v=a(21795),w=a(19072),b=a("shared/dialogs/basic-dialog"),y=a(54995),k=a(48881),P=a(62914),T=a(98300),I=a(92380),A=a(39835),S=a(811),G=a(8712);let _=class{#E;#N;#O;#q;#x;#U;#e;#M;#z;#A;#B;#a;#F;#K;#J;#H;#X;#Y;#Z;#Q;#ee;#P;constructor(e,t,a,s,i,r,o,n,c,h,l,g,d,u,m,p,f,v,w,b){this.#E=e,this.#O=t,this.#q=a,this.#x=s,this.#U=i,this.#e=r,this.#M=o,this.#z=n,this.#A=c,this.#B=h,this.#a=l,this.#F=g,this.#N=d,this.#K=u,this.#J=m,this.#H=p,this.#X=f,this.#Y=v,this.#Q=w,this.#Z=b}attached(e){this.#ee=e,this.#P=this.#e.onActivated((e=>{this.#e.focus(),this.#te(e.uri,e.source)})),this.#te(this.#e.info.launchUri??"","launch")}detached(){this.#P?.dispose(),this.#P=null}#te(e,t){const a=new URL(e);if("launch"!==t){const e=new URL(a);e.searchParams.delete("trigger"),this.#A.event("app_activate",{uri:e.toString(),trigger:a.searchParams.get("trigger")||""},P.Io)}this.#ae(a),this.#se(a);const s=a.pathname.replace(/^\/+/,""),i=s.length?s.split("/"):[];if(a.host&&i.unshift(a.host),0!==i.length)switch(i[0]){case"home":this.#ee.navigateToRoute("dashboard");break;case"titles":1===i.length?this.#ee.navigateToRoute("titles"):2===i.length&&this.#ee.navigateToRoute("title",{titleId:i[1],gameId:a.searchParams.get("gameId")||void 0,trainerId:""});break;case"settings":this.#ee.navigateToRoute("settings",{group:i.slice(1).join("/")});break;case"checkout":{let e=!1;if(1===i.length)e=!0;else switch(i[1]){case"reactivate":this.account.subscription||this.#x.showReactivateDialog();break;case"resume":this.account.subscription?this.#ee.navigateToRoute("settings",{group:"account/billing"}):e=!0;break;case"update":this.account.subscription?this.#U.open(a.searchParams.get("trigger")||"app_activation"):this.#ee.navigateToRoute("settings",{group:"account/billing"});break;case"upgrade":e=!0}if(e)if(this.account.subscription)this.#ee.navigateToRoute("settings",{group:"account/billing"});else{const e=a.searchParams.get("trigger")||"app_activation";this.#M.publish(new v.kK(e)),this.#E.open({trigger:e,frequency:a.searchParams.get("frequency")||void 0,discountCode:a.searchParams.get("coupon")||void 0})}break}case"gift":{const e=a.searchParams.get("trigger")||"app_activation";this.#N.open({trigger:e,frequency:a.searchParams.get("frequency")||void 0,discountCode:a.searchParams.get("coupon")||"",recipient:a.searchParams.get("recipient")||""})}break;case"leave-feedback":this.#Q.open({trigger:"leave-feedback",name:"leave-feedback"});break;case"maps-feedback":this.#X.open({trigger:"maps-feedback",mapId:a.searchParams.get("mapId")||""});break;case"guide-feedback":this.#Y.open({guide:a.searchParams.get("guide")||""});break;case"game-guide":if("elden-ring"===i[1]){const e=a.searchParams.get("bossName");this.#Z.open(e??void 0)}break;case"pro":{const e=a.searchParams.get("trigger")||"app_activation";this.#M.publish(new v.kK(e)),this.#O.open({trigger:e,nonInteraction:!1,discountCode:a.searchParams.get("coupon")||void 0})}break;case"profile":this.#ee.navigate("profile");break;case"rewards":this.#ee.navigate("rewards");break;case"queue":this.#ee.navigate("queue");break;case"collection":this.#ee.navigateToRoute("collection",{slug:i[1]});break;case"play":this.#ee.navigateToRoute("title",{titleId:a.searchParams.get("titleId")||void 0,gameId:a.searchParams.get("gameId")||void 0,trainerId:"",autoLaunch:!0});break;case"updates":case"changelog":this.#q.open({trigger:"external_url"});break;case"webview":this.#z.open({route:i.slice(1).join("/"),params:Object.fromEntries(a.searchParams.entries())});break;case"fullscreen-webview":this.#F.open({route:i.slice(1).join("/"),params:Object.fromEntries(a.searchParams.entries())});break;case"promotion":this.#B.showDialog();break;case"maps":this.#J.openMap(i[1],i[2],a.searchParams.get("location")??void 0,a.searchParams.get("version")??void 0,a.searchParams.get("source")??void 0,a.searchParams.has("debug"));break;case"overlay-install":{const e=a.searchParams.get("location");this.#H.openInStore(e??"")}break;default:this.#K.ok("uri_handler.unsupported_message")}}#ae(e){const t=e.searchParams.get("trigger");if(!t)return;const a=t.match(/^promotion:([\d]+)\/([\w]+)$/);if(!a)return;const[s,i,r]=a;s&&i&&"notification"===r&&this.#a.dispatch(k.dr,i,"notification",(new Date).toISOString())}#se(e){const t=e.searchParams.get("trigger");if(!t)return;const a=t.match(/^notification:([\w]+)\/([\d]+)$/);if(!a)return;const[s,i,r]=a;s&&["relase","update"].includes(i)&&this.#A.event("notification_click",{type:i,gameId:r},P.Io)}};_=(0,s.Cg)([(0,y.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,y.$t)((e=>e.account))}}),(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[h.U,p.f,c.ChangelogDialogService,G.J,n.N,w.s,i.EventAggregator,f.WebviewDialogService,P.j0,S.n,o.il,l.FullscreenWebviewDialogService,d.Y,b.BasicDialogService,A.I,T.Hy,u.MapsNpsDialogService,g.GameGuideNpsDialogService,m.NpsDialogService,I.$])],_)}}]);
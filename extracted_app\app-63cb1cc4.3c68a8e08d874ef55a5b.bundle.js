"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3319,6094],{2427:(t,e,s)=>{s.d(e,{K:()=>u});var a=s(15215),i=s("aurelia-framework"),r=s(20770),n=s(45660),o=s(59239),c=s(68663),h=s(54995),l=s(48881),d=s(38777);let u=class{#t;#e;#s;constructor(t,e){this.#t=t,this.#e=e}attached(){this.#a()}detached(){this.#i()}#a(){this.#s=(0,d.Ix)((()=>this.#r()),6e4)}#i(){this.#s?.dispose(),this.#s=null}async#r(){this.#i();try{if(!this.preventCatalogRefresh){const t=await this.#e.state.pipe((0,n.$)(),(0,o.E)("catalogCacheKey")).toPromise();let e=null;try{e=await this.#t.getCatalog(t)}catch{}null!==e&&e.body&&await this.#e.dispatch(l.If,e.body,e.cacheKey)}}finally{this.#a()}}};u=(0,a.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{preventCatalogRefresh:(0,h.$t)((t=>t.settings?.preventCatalogRefresh))}}),(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[c.x,r.il])],u)},10699:(t,e,s)=>{s.d(e,{G:()=>m});var a=s(15215),i=s("aurelia-framework"),r=s(20770),n=s(68663),o=s(19072),c=s(57503),h=s("shared/pusher/index"),l=s(54995),d=s(48881),u=s(38777),g=s(86824),p=s(27958);let m=class{#t;#e;#n;#o;#c;#h;#l;#d;constructor(t,e,s,a,i,r){this.#t=t,this.#e=e,this.#n=s,this.#o=a,this.#c=i,this.#d=r}attached(){this.#c.setDeauthorizedHandler((()=>this.signOut())),this.#h=this.#n.onRestoredFromTray(this.#u.bind(this)),this.#g(this.token),this.#l=(0,u.SO)((()=>this.refreshAccount()),(0,g.H)(15,20))}detached(){this.#h?.dispose(),this.#h=null,this.#l?.dispose(),this.#l=null}#u(){this.refreshAccount()}tokenChanged(t){this.#g(t)}async signOut(){return await this.#e.dispatch(d.NX,"signOutOnStartup",!0),this.#d.router.navigateToRoute("dashboard",{},{replace:!0,trigger:!1}),this.#n.reload()}#g(t){this.#o.setAuthHeader(`Bearer ${t.accessToken}`)}async refreshAccount(){try{const t=await this.#t.getUserAccount();return await this.#e.dispatch(d.Ui,t),t}catch{return null}}};m=(0,a.Cg)([(0,i.autoinject)(),(0,l.m6)({setup:"attached",teardown:"detached",selectors:{token:(0,l.$t)((t=>t.token))}}),(0,a.Sn)("design:paramtypes",[n.x,r.il,o.s,h.Pusher,c.Z,p.L])],m)},27958:(t,e,s)=>{s.d(e,{L:()=>a});class a{attached(t){this.router=t}detached(){}getCurrentInternalUri(){const t=this.router.currentInstruction;if(t.config.settings?.supportedAsInternalUri){const e=new URL(`wemod:${t.fragment}`);return e.search=t.queryString,e.toString()}return"wemod:"}}},40127:(t,e,s)=>{s.d(e,{$7:()=>A,Hi:()=>b,Re:()=>k,og:()=>w,ye:()=>_});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(96610),o=s(83802),c=s(19072),h=s(3972),l=s("services/bugsnag/index"),d=s("shared/cheats/resources/value-converters/proper-hotkey"),u=s(54995),g=s(38777),p=s(26700),m=s(62679),v=s(67064),y=s(62914),f=s(19648);const I=(0,n.getLogger)("capture"),w={"1080p30":{width:1920,height:1080,fps:30,bitrate:4e3,recFormat:"mp4",resolutionLabel:"1080p"},"720p30":{width:1280,height:720,fps:30,bitrate:4e3,recFormat:"mp4",resolutionLabel:"720p"},"360p24":{width:640,height:360,fps:24,bitrate:4e3,recFormat:"mp4",resolutionLabel:"360p"}},b="720p30",_=15,C=[17,220],A="capture_highlight_saved";let k=class{#p;#m;#v;#y;#f;#I;#w;#b;#n;#_;#C;#A;#k;#S;constructor(t,e,s,a,i,r,n,o){this.#p=!1,this.#m=0,this.#w=t,this.#b=e,this.#n=s,this.#_=a,this.#C=i,this.#A=r,this.#k=n,this.#S=o}attached(){this.#y=new g.Vd([this.#w.onTrainerActivated(this.#P.bind(this)),this.#w.onTrainerEnded(this.#E.bind(this)),this.#n.onCaptureReplayBufferSaved(this.#T.bind(this))])}detached(){this.#y?.dispose(),this.#v?.dispose(),this.#L()}async#P(t){this.#O(t),this.#j()}#E(t){this.#p&&this.#L()}async#j(){if(!this.enableCapture)return;await this.#L();const t=this.#w.trainer?.process?.id;if(t)try{const e=await this.getCaptureConfig();await this.#n.startCapture(e,t),await this.#n.startReplayBuffer(),this.#p=!0,this.#v=this.#b.onHotkeyPress((t=>this.#V(t))),this.#C.event("capture_start",this.#F(),y.Jb)}catch(t){this.#D(t),this.#C.event("capture_start_error",this.#F(),y.Io)}}async#L(){this.#p=!1,this.#m=0,this.#v?.dispose(),this.#f=null;try{await this.#n.stopCapture()}catch(t){this.#D(t)}this.#v?.dispose(),this.#C.event("capture_stop",this.#F(),y.Jb)}get captureVideoParams(){return(this.captureVideoPresetName?w[this.captureVideoPresetName]:void 0)??w[b]}captureVideoPresetNameChanged(){this.#R()}captureAudioOutputDeviceIdChanged(){this.#R()}captureBufferSecondsChanged(){this.#R()}#R(){this.#p&&this.#j()}enableCaptureChanged(){!this.enableCapture&&this.#p&&this.#L(),this.enableCapture&&!this.#p&&this.#j()}async getCaptureConfig(){const t=await this.#U(this.titleName);return{videoParams:this.captureVideoParams,audioParams:{inputDeviceId:"disabled",outputDeviceId:this.captureAudioOutputDeviceId??"default"},bufferSeconds:(this.captureBufferSeconds??_)+2,recPath:t}}#O(t){const e=t.getMetadata(o.vO),s=e?.info?.titleId,a=e?.info?.gameId??"";s&&this.titles[s]?this.titleName=this.titles[s].name:this.titleName=void 0,a&&this.games[a]?this.#I=a:this.#I=void 0}#V(t){this.#p&&t.length===C.length&&t.every(((t,e)=>t===C[e]))&&this.saveHighlight("hotkey")}async saveHighlight(t){try{await this.#n.saveReplayBuffer(),this.#C.event("capture_highlight_save_intent",{...this.#F(),trigger:t},y.Io)}catch(t){this.#D(t)}}#D(t){(0,l.report)(t),I.error(t.toString())}#F(){return{titleName:this.titleName,gameId:this.#I,platformId:this.#I?this.games[this.#I]?.platformId:"",videoPreset:this.captureVideoPresetName,audioOutputDeviceId:this.captureAudioOutputDeviceId,captureBufferSeconds:this.captureBufferSeconds??_}}async#U(t){return await(0,p.B)(this.#S.getVideosDirectory(),t)}#$(){const t={content:1===this.#m?"capture.one_$title_highlight_saved":"capture.$count_$title_highlights_saved",i18nParams:{count:this.#m,title:this.titleName??""},icon:"replay_30",iconColor:"#ee343f",persist:!0,actions:[{label:"capture.view_captures",onclick:()=>this.#S.openCaptureFolder("toast",this.titleName)}],onremove:()=>{this.#f=null}};this.#f?this.#_.update(this.#f,t):this.#f=this.#_.toast(t)}async#T(t){t.success?(this.#A.publish(A),this.#m++,this.#$(),(0,m.I6)(this.#S.getVideosDirectory(),t.filePath)):this.#C.event("capture_highlight_save_error",this.#F(),y.Io)}get displayHotkey(){return t=this.#k,C.map((e=>{const s=t.toView(e);return s?s.replace(/\\/g,"\\\\"):""})).join("+");var t}};(0,a.Cg)([r.observable,(0,a.Sn)("design:type",Object)],k.prototype,"titleName",void 0),(0,a.Cg)([(0,r.computedFrom)("captureVideoPresetName"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],k.prototype,"captureVideoParams",null),k=(0,a.Cg)([(0,u.m6)({setup:"attached",teardown:"detached",selectors:{captureVideoPresetName:(0,u.$t)((t=>t.settings.captureVideoPresetName)),enableCapture:(0,u.$t)((t=>t.settings.enableCapture)),captureBufferSeconds:(0,u.$t)((t=>t.settings.captureBufferSeconds)),captureAudioOutputDeviceId:(0,u.$t)((t=>t.settings.captureAudioOutputDeviceId)),titles:(0,u.$t)((t=>t.catalog.titles)),games:(0,u.$t)((t=>t.catalog.games))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[o.jR,h.Mz,c.s,v.l,y.j0,i.EventAggregator,d.ProperHotkeyValueConverter,f.Z])],k)},49765:(t,e,s)=>{s.d(e,{I:()=>o});var a=s(15215),i=s("aurelia-framework"),r=s("shared/api/index"),n=s(20057);let o=class{#t;#N;#M;#H;constructor(t,e){this.#t=t,this.#N=e}#r(){this.#H=this.#N.getEffectiveLocale().baseName}activate(){this.#M=this.#N.onLocaleChanged((()=>this.#r())),this.#r(),this.#t.addInterceptor({request:t=>(this.#H&&t.headers.set("Accept-Language",this.#H),t)})}deactivate(){this.#M?.dispose(),this.#M=null}};o=(0,a.Cg)([(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[r.WeModApiClient,n.F2])],o)},60284:(t,e,s)=>{s.d(e,{Y:()=>o});var a=s(15215),i=s("aurelia-dialog"),r=s("aurelia-framework"),n=s(38777);let o=class{#x;constructor(t){this.#x=t}attached(){}detached(){}async hasOpenDialog(){return await(0,n.Wn)(),this.#x.hasOpenDialog}};o=(0,a.Cg)([(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[i.DialogService])],o)},61116:(t,e,s)=>{s.d(e,{c:()=>n});var a=s(15215),i=s("aurelia-framework"),r=s(54995);let n=class{attached(){}detached(){}get creators(){if(!this.catalog)return[];const t={};return Object.values(this.catalog.games).filter((t=>t.trainer)).forEach((e=>{e.trainer?.contributorIds.forEach((e=>{t[e]||(t[e]=0),t[e]++}))})),Object.values(this.catalog.creators).filter((t=>t.active)).map((e=>({...e,gameCount:t[e.id]??0}))).sort(((t,e)=>e.gameCount-t.gameCount))}};(0,a.Cg)([(0,i.computedFrom)("catalog"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],n.prototype,"creators",null),n=(0,a.Cg)([(0,r.m6)({setup:"attached",teardown:"detached",selectors:{catalog:(0,r.$t)((t=>t.catalog))}}),(0,i.autoinject)()],n)},62079:(t,e,s)=>{s.d(e,{L:()=>h});var a=s(15215),i=s("aurelia-framework"),r=s(58534),n=s(99626),o=s(20057),c=s(49442);let h=class{#B;#N;constructor(t){this.#N=t}attached(){this.#B=this.#N.onLocaleChanged((()=>this.update()))}detached(){this.#B?.dispose()}async update(){if(this.changelog=null,!r.A)return null;let t=null;try{const e=this.#N.getEffectiveLocale(),s=(0,n.o)(e.toString())??"en",a=await fetch(`https://featurebase.wemod.com/changelog?id=${r.A}&locale=${s}`,{headers:{Accept:"application/json"}}).catch(c.Y),i=await(a?.json());i&&i.id===r.A&&i.slug&&(t=i)}catch{}return this.changelog=t,t}};h=(0,a.Cg)([(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[o.F2])],h)},62914:(t,e,s)=>{s.d(e,{j0:()=>b,Io:()=>y,ek:()=>f,Jb:()=>I});var a=s(15215),i=s("aurelia-framework"),r=s(96610),n=s(16953),o=s("services/bugsnag/index"),c=s(17019),h=s(29864),l=s(19072),d=s(20057),u=s(54995),g=s(49442),p=s(64980);let m=class{#n;#N;#z;#q;constructor(t,e){this.#z=null,this.#n=t,this.#N=e}activate(){n.A.services.amplitude.apiKey&&(this.#z=(0,c.Q)(),this.account||this.#z.reset(),this.#z.init(n.A.services.amplitude.apiKey,this.account?.uuid,{optOut:!this.enabled,appVersion:this.#n.info.version,deviceId:this.deviceId,disableCookies:!0,domain:"app.wemod.com",defaultTracking:!1}),this.#z.add({name:"wemod_metadata",type:h.Q.ENRICHMENT,setup:async t=>{},execute:async t=>(t.os_name={win32:"Windows"}[this.#n.info.osPlatform]??this.#n.info.osPlatform,t.os_version=this.#n.info.osVersion,t.language=this.#N.getEffectiveLocale().toString(),this.#q&&(t.user_properties=Object.assign(t.user_properties??{},this.#q),this.#q=void 0),t)}))}accountChanged(){this.account&&this.#z?.setUserId(this.account.uuid)}async deactivate(){await this.flush().catch(g.Y)}async flush(){await(this.#z?.flush().promise)}enabledChanged(){this.#z?.setOptOut(!this.enabled)}track(t,e){if(this.#z){const s="object"==typeof e?(0,p.u)(e):{};this.#z.track(t,s)}}setUserProperty(t,e){this.#q||(this.#q={}),this.#q[t]=e}screenView(t){this.#n.isInTraySinceStartup||this.track("screen_view",{screen_type:t.name??null,screen_class:t.class??null,...t.params||null})}};m=(0,a.Cg)([(0,i.autoinject)(),(0,u.m6)({setup:"activate",teardown:"deactivate",selectors:{account:(0,u.$t)((t=>t.account)),deviceId:(0,u.$t)((t=>t.installation?.id)),enabled:(0,u.$t)((t=>t.settings?.analytics))}}),(0,a.Sn)("design:paramtypes",[l.s,d.F2])],m);var v=s(28747);const y=Object.freeze({ga:!0,amplitude:!0}),f=Object.freeze({ga:!1,amplitude:!0}),I=Object.freeze({ga:!0,amplitude:!1}),w=(0,r.getLogger)("events");let b=class{#K;#X;constructor(t,e){this.#K=t,this.#X=e}async activate(){await Promise.allSettled([this.#K.activate(),this.#X.activate()])}async deactivate(){await this.#X.deactivate()}async flush(){await this.#X.flush()}user(t,e,s){n.A.debug&&w.debug(t,e),0===Object.keys(s??{}).length&&((0,o.report)(new Error("No dispatch specified for user event")),s=y),s.ga&&this.#K.user(t,e),s.amplitude&&this.#X.setUserProperty(t,e)}event(t,e,s=y){n.A.debug&&w.debug(t,e,s),s.ga&&this.#K.event(t,e),s.amplitude&&this.#X.track(t,e)}adConversion(t,e={}){this.#K.adEvent(t,e)}screenView(t,e=y){n.A.debug&&w.debug("screenView",t.name,t.class,t.params,e),e.ga&&this.#K.screenView(t),e.amplitude&&this.#X.screenView(t)}};b=(0,a.Cg)([(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[v.a,m])],b)},68368:(t,e,s)=>{s.d(e,{u:()=>u});var a=s(15215),i=s("aurelia-framework"),r=s(19072),n=s(92465),o=s(20057),c=s(54995),h=s(70236),l=s(90211);function d(){(0,i.signalBindings)("content-target")}let u=class{#G;#W;#N;#Y;#M;#Z;constructor(t,e){this.#Y=new n._M,this.#G=t.info.region,this.#W=l.R.parse(t.info.osVersion),this.#N=e}onReevaluationRequired(t){return this.#Y?.subscribe("_",t)??null}activate(){this.#M=this.#N.onLocaleChanged((()=>{this.#J(this.#N.getEffectiveLocale()),this.#Y?.publish("_"),d()})),this.#J(this.#N.getEffectiveLocale())}deactivate(){this.#M?.dispose(),this.#M=null,this.#Y?.dispose(),this.#Y=null}audienceChanged(){this.#Y?.publish("_"),d()}#J(t){this.#Z=t.language}isApplicable(t){if(t.target){if(t.target.countries&&this.#G&&!t.target.countries.includes(this.#G))return!1;if(t.target.languages&&!t.target.languages.includes(this.#Z))return!1;const e=l.R.parse(t.target.maxOsVersion??"");return!(t.target.maxOsVersion&&e&&this.#W?.gt(e))&&!(t.target.minOsVersion&&e&&this.#W?.lt(e))&&(!t.target.audience||(0,h.Lt)(this.audience,t.target.audience))}return!t.audience||(0,h.Lt)(this.audience,t.audience)}};u=(0,a.Cg)([(0,i.autoinject)(),(0,c.m6)({setup:"activate",teardown:"deactivate",selectors:{audience:(0,c.$t)((t=>t.account?.audience))}}),(0,a.Sn)("design:paramtypes",[r.s,o.F2])],u)},68492:(t,e,s)=>{s.d(e,{A:()=>a}),s(16566);const a=[{route:"dashboard",name:"dashboard",moduleId:"dashboard/dashboard"},{route:"titles",name:"titles",moduleId:"cheats/titles",activationStrategy:"replace",settings:{supportedAsInternalUri:!0}},{route:"titles/:titleId",name:"title",moduleId:"cheats/title",activationStrategy:"replace",settings:{supportedAsInternalUri:!0}},{route:"queue",name:"queue",moduleId:"queue/queue",settings:{supportedAsInternalUri:!0}},{route:"collection/:slug",name:"collection",moduleId:"cheats/game-collection",activationStrategy:"replace",settings:{supportedAsInternalUri:!0}},{route:"profile",name:"profile",moduleId:"profile/profile",settings:{supportedAsInternalUri:!0}},{route:"rewards",name:"rewards",moduleId:"rewards/rewards",settings:{supportedAsInternalUri:!0}},{route:"settings",name:"settings",moduleId:"settings/settings-menu",activationStrategy:"replace",settings:{supportedAsInternalUri:!0}},{route:"my-videos",name:"my-videos",moduleId:"my-videos/my-videos",activationStrategy:"replace",settings:{supportedAsInternalUri:!0}}]},69005:(t,e,s)=>{s.d(e,{L:()=>I});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(10229),o=s(35392),c=s(21795),h=s(19072),l=s(90231),d=s(20057),u=s(54995),g=s(29844),p=s(2260),m=s(96276),v=s(17181);const y=256,f=256;let I=class{#n;#N;#Y;#Q;#tt;constructor(t,e,s,a,i){this.#n=t,this.#N=e,this.#Y=s,this.#Q=a,this.#tt=i}attached(){}detached(){}async create(t,e){const s=this.catalog.games[t],a=this.catalog.titles[s?.titleId];if(!s||!a)return;const i=await this.#et(s);await this.#n.createDesktopShortcut(`wemod://play?titleId=${a.id}&gameId=${s.id}`,this.#N.getValue("desktop_shortcut.play_$title",{title:a.name}),i??void 0),this.#Y.publish(new c.Dz(e,s.id))}#st(t){return new Promise((e=>{const s=new Image;s.addEventListener("load",(()=>e(s)),{once:!0}),s.src=t}))}async#et(t){const e=this.#tt.getPreferredInstallationInfo(t.id).app;if(!e)return null;const s=await this.#Q.getIcon(e.platform,e.sku);if(!s)return null;const a=(0,g.LC)(s).toLowerCase();let i=null;if(".ico"===a?i=await this.#at(s):[".jpg",".jpeg",".png"].includes(a)&&(i=await this.#st(s)),!i)return null;const r=await this.#it(i),n=await this.#rt(t.id);try{if(!r)return null;this.#nt(n,r)}catch{return null}return n}async#it(t){const e=document.createElement("canvas"),s=e.getContext("2d"),a=await this.#st(v);if(e.width=y,e.height=f,t instanceof ImageData){const e=await window.createImageBitmap(t,0,0,t.width,t.height,{resizeWidth:y,resizeHeight:f,resizeQuality:"high"});s?.drawImage(e,0,0)}t instanceof HTMLImageElement&&s?.drawImage(t,0,0,y,f),s?.drawImage(a,y-a.width-16,f-a.height-16);const i=await new Promise((t=>e.toBlob(t)));if(i)return await new Promise(((t,e)=>{const s=new FileReader;s.addEventListener("loadend",(()=>t(new Uint8Array(s.result))),{once:!0}),s.addEventListener("error",(()=>e(new Error("Failed to read image."))),{once:!0}),s.readAsArrayBuffer(i)}))}async#at(t){try{const e=await o.promises.readFile(t),s=(await n.parse(e)).sort(((t,e)=>e.width-t.width))[0];if(s.data instanceof Uint8Array){const t=new Image;return t.src=URL.createObjectURL(new Blob([s.buffer],{type:"image/png"})),t}return new ImageData(s.data,s.width,s.height)}catch{return null}}async#nt(t,e){const s=Buffer.from(e),a=await(0,p.u)(s);await o.promises.writeFile(t,a)}async#rt(t){const e=this.#Q.iconCacheDirectory;return await o.promises.stat(e).then((t=>t.isDirectory())).catch((()=>!1))||await o.promises.mkdir(e),(0,g.fj)(e,`${t}.ico`)}};I=(0,a.Cg)([(0,u.m6)({setup:"attached",teardown:"detached",selectors:{catalog:(0,u.$t)((t=>t.catalog))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[h.s,d.F2,i.EventAggregator,l.D,m.T])],I)},71461:(t,e,s)=>{s.d(e,{i:()=>c});var a=s(15215),i=s("aurelia-framework"),r=s("shared/api/index"),n=s(54995);const o={overrideCountry:"country"};let c=class{#t;constructor(t){this.#t=t}activate(){this.#t.addInterceptor({request:t=>{const e={};return Object.keys(o).forEach((t=>{this.settings[t]&&(e[o[t]]=this.settings[t])})),Object.values(e).length>0&&t.headers.set("X-Debug-Properties",btoa(JSON.stringify(e))),t}})}deactivate(){}};c=(0,a.Cg)([(0,i.autoinject)(),(0,n.m6)({setup:"activate",teardown:"deactivate",selectors:{settings:(0,n.$t)((t=>t.settings))}}),(0,a.Sn)("design:paramtypes",[r.WeModApiClient])],c)},72208:(t,e,s)=>{s.d(e,{u:()=>u});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),o=s(83802),c=s(21795),h=s(54995),l=s(88849),d=s(48881);let u=class{#w;#e;#Y;#ot;constructor(t,e,s){this.#w=t,this.#e=e,this.#Y=s}attached(){this.#ot=this.#w.onTrainerActivated((t=>{t.onValueSetError((t=>{t.cheatId&&this.markUnread(t.cheatId)}))}))}detached(){this.#ot?.dispose(),this.#ot=null}markRead(t,e,s,a,i){if(this.#Y.publish(new c.mN(e,s,a&&i?"both":a?"instructions":"description",t)),!a)return;const r=(0,l.YZ)(a);this.cheatBlueprintInstructionsRead[s]!==r&&this.#e.dispatch(d.W,s,r)}markUnread(t){this.#e.dispatch(d.W,t,null)}areInstructionsRead(t,e){return!e||this.cheatBlueprintInstructionsRead?.[t]===(0,l.YZ)(e)}};u=(0,a.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{cheatBlueprintInstructionsRead:(0,h.$t)((t=>t.cheatBlueprintInstructionsRead))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[o.jR,n.il,i.EventAggregator])],u)},78268:(t,e,s)=>{s.d(e,{s:()=>u});var a=s(15215),i=s("aurelia-framework"),r=s(20770),n=s(68663),o=s(54995),c=s(70236),h=s(48881),l=s(38777),d=s(86824);let u=class{#t;#e;#ct;#s;constructor(t,e){this.#ct=[],this.#t=t,this.#e=e}attached(){}detached(){this.#s?.dispose(),this.#s=null,this.#ct=[]}accountFlagsChanged(){this.#ct=this.#ct.filter((t=>!(0,c.Lt)(this.accountFlags,t.flag)))}watchFlag(t,e,s=!1){if((0,c.Lt)(this.accountFlags,t))return l.lE;{const a={flag:t,remainingTries:e};return this.#ct.push(a),this.#a(s),(0,l.nm)((()=>{const t=this.#ct.indexOf(a);t>=0&&this.#ct.splice(t,1)}))}}#a(t){t&&this.#s?.dispose(),!t&&this.#s||(this.#s=(0,l.Ix)((async()=>{this.#s=null;try{await this.#ht()}finally{this.#s||0===this.#ct.length||this.#a(!1)}}),t?0:(0,d.V)(4,7)))}async#ht(){let t;try{const e=await this.#t.getUserAccountFlags(this.#ct.reduce(((t,e)=>t|e.flag),0));this.#ct=this.#ct.filter((t=>--t.remainingTries>0&&!(0,c.Lt)(this.accountFlags,t.flag))),e&&(t=await this.#t.getUserAccount())}catch{}t&&await this.#e.dispatch(h.Ui,t)}};u=(0,a.Cg)([(0,i.autoinject)(),(0,o.m6)({setup:"attached",teardown:"detached",selectors:{accountFlags:(0,o.$t)((t=>t.account?.flags))}}),(0,a.Sn)("design:paramtypes",[n.x,r.il])],u)},78563:(t,e,s)=>{s.d(e,{s:()=>m,v:()=>p});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),o=s(16953),c=s(22920),h=s(64706),l=s(20057),d=s(54995),u=s(14046),g=s(48881);class p{constructor(t,e){this.titleId=t,this.historyItem=e}}let m=class{#lt;#dt;#ut;#e;#Y;#N;constructor(t,e,s,a){this.#lt=new Map,this.#dt=new Map,this.#ut=t,this.#e=e,this.#Y=s,this.#N=a,this.persistItem=this.persistItem.bind(this)}attached(){}detached(){}getAssistant(t){let e=this.#lt.get(t);const s=(this.assistantHistory[t]||[]).map((t=>h.gG.deserialize(t))),a=new h.fz(t,s),i=this.catalog.titles[t]?.name;return this.#dt.get(t)||this.#dt.set(t,a.onItemFinalized((({titleId:t,item:e})=>this.#gt(t,e)))),e||(e=this.#ut.createAssistant({titleId:t,titleName:i,baseUrl:o.A.services.assistant.baseUrl,client:"desktop",userProfileImageUrl:this.account?.profileImage,userProfileName:this.account?.username},a),this.#lt.set(t,e)),e}async persistItem(t,e){if(e.persist){const s=e.serialize();await this.#e.dispatch(g.WS,t,[s]),this.#Y.publish(new p(t,e))}}async#gt(t,e){await this.persistItem(t,e);const s=this.getAssistant(t);s&&this.#pt(s)}#pt(t){const e=t=>"answer"===t.type&&"desktop"===t.client&&!!t.requestId,s=t.history.items[t.history.items.length-1],a=Object.values(this.assistantHistory).flat().filter((t=>e(t)));if(e(s)){const e=(0,u.c_)(Date.now(),new Date(this.lastOverlayAssistantMessage)),s=a.length>=1&&!this.timesOverlayAssistantMessageSeen,i=a.length>=15&&1===this.timesOverlayAssistantMessageSeen&&e>=1;if(s||i){const e=this.#N.getValue("assistant_manager.overlay_prompt_message"),s=new h.gG("answer",e,[],null,null,!0,!1,"desktop");this.#e.dispatch(g.Ew,"timesOverlayAssistantMessageSeen",1),this.#e.dispatch(g.vk,"lastOverlayAssistantMessage"),setTimeout((()=>t?.history.add(s)),2e3)}}}assistantHistoryChanged(t,e){Object.keys(e).forEach((s=>{const a=e[s],i=t[s];if(a?.length!==i?.length&&!i?.length){const t=this.getAssistant(s);t?.history.clear()}}))}get questionsAsked(){return Object.values(this.assistantHistory).flat().filter((t=>"question"===t.type))}};(0,a.Cg)([(0,r.computedFrom)("assistantHistory"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],m.prototype,"questionsAsked",null),m=(0,a.Cg)([(0,d.m6)({setup:"attached",teardown:"detached",selectors:{assistantHistory:(0,d.$t)((t=>t.assistantHistory)),catalog:(0,d.$t)((t=>t.catalog)),timesOverlayAssistantMessageSeen:(0,d.$t)((t=>t.counters?.timesOverlayAssistantMessageSeen)),lastOverlayAssistantMessage:(0,d.$t)((t=>t.timestamps?.lastOverlayAssistantMessage)),account:(0,d.$t)((t=>t.account))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[c.h,n.il,i.EventAggregator,l.F2])],m)},90415:(t,e,s)=>{s.d(e,{u:()=>c});var a=s(15215),i=s("aurelia-framework"),r=s(54995),n=s(38777),o=s(62914);let c=class{#C;#mt;constructor(t){this.#C=t}attached(){const t=this.handleContrastMode();this.#vt(),this.#mt=(new n.Vd).pushEventListener(t,"change",(t=>this.setForcedColorsParam(t))).pushEventListener(document,"focusin",(()=>this.handleKeyboardNavigation())).pushEventListener(document,"keypress",(t=>this.#yt(t))).pushFunction(this.#ft())}detached(){this.#mt.dispose()}handleContrastMode(){const t=window.matchMedia("(forced-colors: active)");return this.setForcedColorsParam(t),t}setForcedColorsParam(t){this.#C.user("a11y_forced_colors",t.matches?"active":"inactive",o.Io)}handleKeyboardNavigation(){const t=document.activeElement;if(t){const e=t.matches(":focus-visible"),s=!["INPUT","TEXTAREA"].includes(t.nodeName);e&&s&&this.#C.user("a11y_keyboard_navigation","true",o.Io)}}#yt(t){"Enter"===t.key&&t.target instanceof HTMLElement&&t.target===document.activeElement&&!["A","BUTTON"].includes(t.target.nodeName)&&t.target.click()}#ft(){const t=[document.getElementById("dialogs"),document.getElementById("fullscreen-dialogs")],e=new MutationObserver((()=>{const e=Array.from(document.querySelectorAll(".inert-when-dialog-open")),s=Array.from(document.querySelectorAll("ux-dialog-container")),a=s.pop(),i=t.some((t=>t?.classList.contains("ux-dialog-open")));a&&(a.inert=!1),[...e,...s].forEach((t=>{t&&(t.inert=i)}))}));return t.forEach((t=>{t&&e.observe(t,{attributeFilter:["class"],childList:!0})})),()=>{e.disconnect()}}reduceMotionChanged(){this.#vt()}#vt(){}};c=(0,a.Cg)([(0,i.autoinject)(),(0,r.m6)({setup:"attached",teardown:"detached",selectors:{reduceMotion:(0,r.$t)((t=>t.settings?.reduceMotion))}}),(0,a.Sn)("design:paramtypes",[o.j0])],c)},97928:(t,e,s)=>{s.d(e,{s:()=>V});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),o=s("ads/ad-view"),c=s(20489),h=s(83802),l=s(30770),d=s(21795),u=s(19072),g=s(41882),p=s(24008),m=s(59255),v=s(20057),y=s(92694),f=s(54995),I=s(49442),w=s(14046),b=s(70236),_=s(48881),C=s(38777),A=s(62914),k=s(72208),S=s(98300),P=s(85805);function E(t){return"number"==typeof t?parseFloat((t/1e3).toFixed(2)):null}const T={[c.FX.AcquiringBinary]:"acquiring_binary",[c.FX.EnsuringBinaryAccess]:"ensuring_binary_access",[c.FX.CheckingInternalBinaries]:"checking_internal_binaries",[c.FX.FindingProcess]:"finding_process",[c.FX.ValidatingProcess]:"validating_process",[c.FX.CreatingTrainerHost]:"creating_trainer_host",[c.FX.Injecting]:"injecting",[c.FX.InitializingIpc]:"initializing_ipc",[c.FX.Executing]:"executing_trainer",[c.FX.Connecting]:"connecting",[c.FX.Activating]:"activating",[c.FX.Active]:"active",[c.FX.Ended]:"ended"},L={[c.KC.Canceled]:"canceled",[c.KC.ElevationDenied]:"elevation_denied",[c.KC.Error]:"error",[c.KC.Incompatible]:"incompatible",[c.KC.GameAlreadyRunning]:"internal_launch_required",[c.KC.GameNotRunning]:"external_launch_required",[c.KC.Success]:"success",[c.KC.TimedOut]:"timeout"},O={1:"hotkey",2:"control",0:"trainer",3:"remote",4:"overlay",6:"overlay_native",5:"save_cheats",7:"mod_timer"},j={2:"all_updates",1:"next_update"};let V=class{#C;#e;#Y;#w;#It;#wt;#n;#bt;#N;#_t;#Ct;#At;#mt;#kt;#St;#Pt;constructor(t,e,s,a,i,r,n,o,c,h,l){this.#kt={[_.Kc]:this.#Et,[_.jZ]:this.#Tt,[_.W6]:this.#Lt,[_.SQ]:this.#Ot},this.#C=t,this.#e=e,this.#Y=s,this.#w=a,this.#It=i,this.#wt=r,this.#n=n,this.#bt=o,this.#N=c,this.#_t=h,this.#Ct=l}activate(){if(this.#C.user("last_app_version",this.#n.info.version,A.Io),this.#C.user("system_locale",this.#n.info.locale,A.Io),this.#n.getDisplayCount().then((t=>this.#C.user("display_count",t,A.Io))).catch(I.Y),this.#jt(),this.#Vt("installed_games"),this.accountChanged(this.account),this.flags.firstRun){this.#Vt("initial_installed_games");const t=new URL(this.#n.info.launchUri??""),e={fromInstaller:t.searchParams.has("_inst")};if(e.fromInstaller){const s=/titles\/([^/]+)$/.exec(t.pathname)?.[1];s&&(e.installerTitleId=s,e.installerTitleName=this.catalog.titles[s]?.name)}this.#Ft("first_open",e,A.Io)}this.#n.isInTraySinceStartup||this.#Ft("app_enter",{firstRun:this.flags.firstRun??!1},A.Io);const t=this.#Dt.bind(this);this.#e.registerMiddleware(t,n._s.After),this.#At=(new C.Vd).push(this.#N.onLocaleChanged((()=>this.#jt()))).pushFunction((()=>this.#e.unregisterMiddleware(t)))}deactivate(){this.#At?.dispose(),this.#At=null}attached(){this.#mt=new C.Vd([this.#w.onNewTrainer((t=>{const e=this.#Rt(t);if(e){const s=t.getMetadata(h.vO);this.#Ft("trainer_launch",Object.assign({trigger:s.trigger},e),A.Io)}})),this.#w.onTrainerActivated(this.#Ut.bind(this)),this.#w.onTrainerActivated(this.#$t.bind(this)),this.#w.onTrainerEnded((t=>this.#Ft("trainer_end",this.#Rt(t,{trainerTotalDuration:E(t.totalDuration),trainerLaunchDuration:E(t.launchDuration),trainerActiveDuration:E(t.activeDuration),trainerLaunchResult:L[t.launchResult??""]??null,trainerFinalState:T[t.finalState??""]??null}),A.Io))),this.#It.onConnectionStatusChanged((t=>{"connected"===t&&this.#Ft("overlay_connect",{},A.Io)})),this.#wt.onStatusChanged(this.#Nt.bind(this)),this.#n.onClosedToTray((()=>this.#Ft("app_background",{},A.Io))),this.#n.onRestoredFromTray((()=>this.#Ft("app_open",{},A.Io))),this.#Y.subscribe(o.AdViewMessageEvent,(t=>this.#Mt(t))),this.#Y.subscribe(d.kK,(t=>this.#Ft("pro_cta_click",{ctaId:t.id},A.Io))),this.#Y.subscribe(d.SA,(t=>this.#Ft("objective_open",{objectiveId:t.objectiveId,completed:t.completed},A.Io))),this.#Y.subscribe(d._1,(t=>this.#Ft("game_boost",this.#Ht(t.gameId,{boostsRemaining:this.account.boosts}),A.Io))),this.#Y.subscribe(d.HE,(t=>this.#Ft("game_follow",this.#Ht(t.gameId,{followType:j[t.followType]}),A.Io))),this.#Y.subscribe(d.t$,(t=>this.#Ft("game_unfollow",this.#Ht(t.gameId),A.Io))),this.#Y.subscribe(d.jP,(()=>this.#Ft("subscription_resume",{},A.Io))),this.#Y.subscribe(d.Yb,(()=>this.#Ft("subscription_reactivate",{},A.Io))),this.#Y.subscribe(d.dY,(t=>this.#Ft("title_link_click",{location:t.location,titleId:t.titleId,gameId:t.gameId||null,trainerId:t.trainerId||null,searchResult:t.searchResult||!1,...this.#xt(t.titleId)||{}},A.Io))),this.#Y.subscribe(d.U0,(t=>this.#Ft("game_install_link_click",this.#Ht(t.gameId,{uriScheme:t.uri.substring(0,t.uri.indexOf(":")),freeGame:t.free,location:t.location}),A.Io))),this.#Y.subscribe(d.a8,(t=>this.#Ft("game_install_popup_open",this.#Ht(t.gameId),A.Io))),this.#Y.subscribe(d.Pq,(t=>this.#Ft("search",{location:t.location,terms:t.terms},A.Io))),this.#Y.subscribe(d.Dz,(t=>this.#Ft("create_desktop_shortcut_click",this.#Ht(t.gameId,{trigger:t.trigger}),A.Io))),this.#Y.subscribe(d.Ij,(t=>this.#Ft(t.enabled?"save_mods_enable":"save_mods_disable",this.#Ht(t.gameId,{trigger:t.trigger}),A.Io))),this.#Y.subscribe(d.XM,(t=>this.#Ft("suggest_mods",this.#Ht(t.gameId),A.Io))),this.#Y.subscribe(d.Zv,(t=>this.#Ft("coaching_tip_show",{id:t.id,nonInteraction:t.nonInteraction},A.Io))),this.#Y.subscribe(d.cv,(t=>this.#Ft("coaching_tip_hide",{id:t.id,trigger:t.trigger},A.Io))),this.#Y.subscribe(d.pA,(t=>this.#Bt(t.trainerInfo))),this.#Y.subscribe(d.WC,(t=>{this.#Ft(t.name,t.params,t.dispatch??A.Io)})),this.#Y.subscribe(d.aV,(t=>this.#Ft("trainer_compatibility_notice_show",this.#Ht(t.gameId,{gameVersion:t.gameVersion,trainerId:t.trainerId,result:t.result}),A.Io))),this.#Y.subscribe(d.mN,(t=>this.#Ft("mod_view",this.#Ht(t.gameId,{modId:t.cheatId,source:t.source,type:t.type}),A.Io))),this.#Y.subscribe(d.VY,(t=>this.#Ft("trainer_launch_intent",this.#Ht(t.gameId,{trigger:t.trigger,trainer_instruction_read:t.trainer_instruction_read}),A.Io))),this.#Y.subscribe(m.d,(t=>this.#Ft(t.name,this.#zt(t.titleId,{client:t.client,...t.params}),A.Io))),(0,C.yB)(window,"resize",(()=>{let t;const e=()=>{this.#C.user("app_window_width",window.innerWidth,A.Io),this.#C.user("app_window_height",window.innerHeight,A.Io)},s=()=>{e(),this.#Ft("window_resize",{width:window.innerWidth,height:window.innerHeight,maximized:this.#n.maximized},A.Io)};return e(),()=>{clearTimeout(t),t=setTimeout(s,1e4)}})())])}detached(){this.#mt?.dispose(),this.#mt=null}#jt(){this.#C.user("app_locale",this.#N.getEffectiveLocale().toString(),A.Io)}accountChanged(t){this.#C.user("subscription_tier",t.subscription?"pro":"free",A.Io);const e=this.#qt();e!==this.#St&&(this.#St=e,this.#C.user("tier","free"===e?"free":"pro",A.ek),this.#C.user("plan_type",e,A.ek),this.#C.user("plan_flag",(0,b.Lt)(t.flags,512)?1:0,A.ek))}#qt(){const t=this.account.subscription;if(!t)return"free";let e=`pro-${t.period}`;return t.trialEndsAt&&(e+="-trial"),t.gift&&(e+="-gift"),e}installedGameVersionsChanged(){this.#Vt("installed_games")}#Vt(t){this.installedGameVersions&&this.#C.user(t,Object.keys(this.installedGameVersions).length,A.Io)}#Ft(t,e,s){!1!==e&&this.#C.event(t,e,s)}#Rt(t,e={}){const s=t.getMetadata(h.vO);return Object.assign(this.#Ht(s.info.gameId,{trainerId:s.info.id,appPlatform:s.app.platform,appSku:s.app.platform===g.u?null:s.app.sku,gameVersion:s.gameVersion,gameVersionSupported:null===s.gameVersion?null:s.info.supportedVersions.includes(s.gameVersion)}),e)}#Ht(t,e={}){const s=this.catalog.games[t];return!!s&&this.#xt(s.titleId,Object.assign({gameId:s.id,gamePlatform:s.platformId,creatorId:s.creatorId??null},e))}#xt(t,e={}){const s=this.catalog.titles[t];return!!s&&Object.assign({titleId:s.id,titleName:s.name},e)}#zt(t,e={}){return this.#xt(t,e)}#Ut(t){const e=this.#Rt(t,{trainerLaunchDuration:E(t.launchDuration)});this.#Ft("trainer_activate",e,A.Io);const s=t.getMetadata(h.vO).info.blueprint.cheats,a=new Set,i=new Map,r=(0,C.SO)((()=>{const n=Date.now()-500;for(const[t,r]of i)if(r.timestamp<=n){i.delete(t);const n=s.find((e=>e.target===t));if(void 0!==n){const t=Object.assign({modUuid:n.uuid,modName:n.name,modType:n.type,modCategory:n.category,modTarget:n.target,modInstructionsRead:this.#Ct.areInstructionsRead(n.uuid,n.instructions??""),modHasInstructions:!!n.instructions,modHasParent:!!n.parent},e,r.params);r.error?this.#Ft("trainer_mod_error",Object.assign({modAndTitleName:`${t.modName} - ${t.titleName}`},t),A.Io):a.has(n.uuid)||(a.add(n.uuid),this.#Ft("trainer_mod",Object.assign({modSource:O[r.event?.source??""],newValue:r.event?.value??0},t),A.Io),this.#C.adConversion("lpa_CLj-1qUZEIGptsMD"))}}0===i.size&&t.hasEnded()&&r.dispose()}),500),n=new C.Vd;n.push(t.onValueSet((e=>{0!==e.source&&i.set(e.name,{error:!1,timestamp:Date.now(),params:{trainerTotalDuration:E(t.totalDuration),trainerActiveDuration:E(t.activeDuration)},event:e})}))),n.push(t.onValueSetError((e=>{i.set(e.name,{error:!0,timestamp:Date.now(),params:{trainerTotalDuration:E(t.totalDuration),trainerActiveDuration:E(t.activeDuration)}})}))),t.onEnded((()=>n.dispose()))}#$t(t){const e=t.getMetadata(h.vO).info;if(!Object.values(this.catalog.games).filter((t=>(0,b.Lt)(t.flags,p.rT.Active))).sort(((t,e)=>(t.trainer?.rank??0)-(e.trainer?.rank??0))).slice(0,30).map((t=>t.id)).includes(e.gameId))return;const s=[{key:"title_id",value:e.titleId},{key:"title_name",value:this.catalog.titles[e.titleId].name},{key:"game_id",value:e.gameId},{key:"game_platform",value:this.catalog.games[e.gameId].platformId}],a=e.blueprint.cheats,i=new Set,r=t.onValueSetError((t=>{const e=(t.cheatId&&a.find((e=>e.uuid===t.cheatId))?.name)??"<internal>",r=`${t.name}:${e}`;i.has(r)||(this.#_t.collect({name:"trainer_mod_error",value:1,tags:[...s,{key:"mod_name",value:e},{key:"mod_target",value:t.name}]}),i.add(r))}));t.onEnded((()=>r.dispose()))}#Nt(t){if(t===P.t.Connected){const t=Date.now();(!this.#Pt||(0,w.c_)(t,new Date(this.#Pt))>1)&&(this.#Pt=t,this.#Ft("remote_connect",{},A.Io))}t===P.t.Disconnected&&this.#Ft("remote_disconnect",{},A.Io)}#Mt(t){["ad_start","ad_click","ad_default_click","ad_load"].includes(t.eventName)&&this.#Ft(t.eventName,{location:t.viewId,provider:t.params.provider,defaultVariant:t.params.defaultVariant,houseAd:!0===t.params.houseAd||"true"===t.params.houseAd},A.Io),"ad_default_click"===t.eventName&&(this.#Y.publish(new d.kK("default_playwire_popup_ad")),this.#bt.open({trigger:"default_playwire_popup_ad",nonInteraction:!1}))}#Dt(t,e,s,a){if(a){const t=this.#kt[a.name];t&&queueMicrotask((()=>t.call(this,e,...a.params||[])))}return t}#Tt(t,e,s,a,i){this.#Ft("hotkey_edit",this.#Ht(e,{modUuid:s,hotkeyIndex:a,hotkeyEditType:void 0===i?"reset":null===i?"unset":"set"}),A.Io)}#Et(t,e,s,a){a||Object.keys(e).filter((s=>e[s]!==t.settings[s])).forEach((a=>{const i=t.settings[a]??null,r=e[a]??null;this.#Ft("app_setting_change",{setting:a,oldValue:"object"==typeof i?JSON.stringify(i):i,newValue:"object"==typeof r?JSON.stringify(r):r,source:s},A.Io)}))}#Lt(t,e){this.#Ft(t.favoriteTitles[e]?"title_unfavorite":"title_favorite",this.#xt(e,{titleId:e}),A.Io)}#Ot(t,e){this.#Ft("custom_game_install",this.#Ht(e),A.Io)}#Bt(t){t.blueprint.cheats.filter((t=>(0,b.Lt)(t.flags,2))).length>0&&this.#Ft("beta_mods_view",this.#Ht(t.gameId),A.Io)}};V=(0,a.Cg)([(0,r.autoinject)(),(0,f.m6)({setup:"activate",teardown:"deactivate",selectors:{account:(0,f.$t)((t=>t.account)),catalog:(0,f.$t)((t=>t.catalog)),flags:(0,f.$t)((t=>t.flags)),installedGameVersions:(0,f.$t)((t=>t.installedGameVersions))}}),(0,a.Sn)("design:paramtypes",[A.j0,n.il,i.EventAggregator,h.jR,S.Hy,P.e,u.s,l.f,v.F2,y.k,k.u])],V)}}]);
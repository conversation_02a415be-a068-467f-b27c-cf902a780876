"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3442],{1356:(t,n,e)=>{function r(t){return t?(parseInt(t,10)^16*Math.random()>>parseInt(t,10)/4).toString(16):"10000000-1000-4000-8000-100000000000".replace(/[018]/g,r)}e.d(n,{_R:()=>s,it:()=>c,lk:()=>r,rx:()=>i});const o=/([\w-]+)\s*=\s*([^;]+)/g;function i(t,n){for(o.lastIndex=0;;){const e=o.exec(t);if(!e)break;if(e[1]===n)return e[2]}}function c(t){const n=new Map;for(o.lastIndex=0;;){const e=o.exec(t);if(!e)break;n.set(e[1],e[2])}return n}function s(t,n,e=""){const r=t.charCodeAt(n-1),o=r>=55296&&r<=56319?n+1:n;return t.length<=o?t:`${t.slice(0,o)}${e}`}},12452:(t,n,e)=>{function r(t,n){const e=window.__ddBrowserSdkExtensionCallback;e&&e({type:t,payload:n})}e.d(n,{b:()=>r})},14451:(t,n,e)=>{function r(){return 0===c()}function o(){return 1===c()}let i;function c(){return null!=i?i:i=function(t=window){var n;const e=t.navigator.userAgent;return t.chrome||/HeadlessChrome/.test(e)?0:0===(null===(n=t.navigator.vendor)||void 0===n?void 0:n.indexOf("Apple"))||/safari/i.test(e)&&!/chrome|android/i.test(e)?1:2}()}e.d(n,{F2:()=>r,nr:()=>o})},14906:(t,n,e)=>{e.d(n,{D:()=>r});class r{constructor(){this.callbacks={}}notify(t,n){const e=this.callbacks[t];e&&e.forEach((t=>t(n)))}subscribe(t,n){return this.callbacks[t]||(this.callbacks[t]=[]),this.callbacks[t].push(n),{unsubscribe:()=>{this.callbacks[t]=this.callbacks[t].filter((t=>n!==t))}}}}},19642:(t,n,e)=>{function r(t,n){for(let e=t.length-1;e>=0;e-=1){const r=t[e];if(n(r,e,t))return r}}function o(t){return Object.values(t)}function i(t){return Object.entries(t)}e.d(n,{KQ:()=>o,Uk:()=>r,WP:()=>i})},23054:(t,n,e)=>{e.d(n,{H:()=>s,t:()=>u});var r=e(93001),o=e(42182),i=e(52999),c=e(35019);function s(t,n,e,{computeHandlingStack:r}={}){let s=t[n];if("function"!=typeof s){if(!(n in t)||!n.startsWith("on"))return{stop:i.l};s=i.l}let u=!1;const a=function(){if(u)return s.apply(this,arguments);const t=Array.from(arguments);let n;(0,o.um)(e,null,[{target:this,parameters:t,onPostCall:t=>{n=t},handlingStack:r?(0,c.uC)("instrumented method"):void 0}]);const i=s.apply(this,t);return n&&(0,o.um)(n,null,[i]),i};return t[n]=a,{stop:()=>{u=!0,t[n]===a&&(t[n]=s)}}}function u(t,n,e){const o=Object.getOwnPropertyDescriptor(t,n);if(!o||!o.set||!o.configurable)return{stop:i.l};const c=i.l;let s=(t,n)=>{(0,r.wg)((()=>{s!==c&&e(t,n)}),0)};const u=function(t){o.set.call(this,t),s(this,t)};return Object.defineProperty(t,n,{set:u}),{stop:()=>{var e;(null===(e=Object.getOwnPropertyDescriptor(t,n))||void 0===e?void 0:e.set)===u&&Object.defineProperty(t,n,o),s=c}}}},29336:(t,n,e)=>{e.d(n,{$H:()=>s,$S:()=>g,FR:()=>u,Gw:()=>v,M8:()=>b,MA:()=>c,OY:()=>o,Oc:()=>m,TP:()=>f,Zj:()=>d,gs:()=>w,iW:()=>i,jR:()=>a,nx:()=>p,pu:()=>S,vk:()=>y,x3:()=>h});var r=e(59248);const o=1e3,i=60*o,c=60*i,s=24*c*365;function u(t){return{relative:t,timeStamp:l(t)}}function a(t){return{relative:w(t),timeStamp:t}}function l(t){const n=h()-performance.now();return n>k()?Math.round(v(n,t)):function(t){return Math.round(v(k(),t))}(t)}function f(){return Math.round(h()-v(k(),performance.now()))}function d(t){return(0,r.Et)(t)?(0,r.LI)(1e6*t,0):t}function h(){return(new Date).getTime()}function p(){return h()}function g(){return performance.now()}function b(){return{relative:g(),timeStamp:p()}}function m(){return{relative:0,timeStamp:k()}}function y(t,n){return n-t}function v(t,n){return t+n}function w(t){return t-k()}function S(t){return t<s}let O;function k(){return void 0===O&&(O=performance.timing.navigationStart),O}},31583:(t,n,e)=>{function r(t){return null===t?"null":Array.isArray(t)?"array":typeof t}e.d(n,{P:()=>r})},32234:(t,n,e)=>{e.d(n,{JZ:()=>o,Vy:()=>s,Xs:()=>a,bP:()=>r,fH:()=>u,xG:()=>l});const r={log:"log",debug:"debug",info:"info",warn:"warn",error:"error"},o=console,i={};Object.keys(r).forEach((t=>{i[t]=o[t]}));const c="Datadog Browser SDK:",s={debug:i.debug.bind(o,c),log:i.log.bind(o,c),info:i.info.bind(o,c),warn:i.warn.bind(o,c),error:i.error.bind(o,c)},u="https://docs.datadoghq.com",a=`${u}/real_user_monitoring/browser/troubleshooting`,l="More details:"},34144:(t,n,e)=>{e.d(n,{BB:()=>c});var r=e(93001),o=e(42182),i=e(29336);function c(t,n){if(window.requestIdleCallback&&window.cancelIdleCallback){const e=window.requestIdleCallback((0,o.dm)(t),n);return()=>window.cancelIdleCallback(e)}return function(t){const n=(0,i.x3)(),e=(0,r.wg)((()=>{t({didTimeout:!1,timeRemaining:()=>Math.max(0,s-((0,i.x3)()-n))})}),0);return()=>(0,r.DJ)(e)}(t)}const s=50},35019:(t,n,e)=>{e.d(n,{NR:()=>s,Yn:()=>c,uC:()=>i});var r=e(42182),o=e(78476);function i(t){const n=new Error(t);let e;return n.name="HandlingStack",(0,r.um)((()=>{const t=(0,o.T)(n);t.stack=t.stack.slice(2),e=c(t)})),e}function c(t){let n=s(t);return t.stack.forEach((t=>{const e="?"===t.func?"<anonymous>":t.func,r=t.args&&t.args.length>0?`(${t.args.join(", ")})`:"",o=t.line?`:${t.line}`:"",i=t.line&&t.column?`:${t.column}`:"";n+=`\n  at ${e}${r} @ ${t.url}${o}${i}`})),n}function s(t){return`${t.name||"Error"}: ${t.message}`}},36271:(t,n,e)=>{e.d(n,{y:()=>o});var r=e(32234);function o(t,n){return(...e)=>{try{return t(...e)}catch(t){r.Vy.error(n,t)}}}},36289:(t,n,e)=>{e.d(n,{WW:()=>c,_m:()=>r,iH:()=>o,wh:()=>s});const r=1024,o=1024*r,i=/[^\u0000-\u007F]/;function c(t){return i.test(t)?void 0!==window.TextEncoder?(new TextEncoder).encode(t).length:new Blob([t]).size:t.length}function s(t){const n=t.reduce(((t,n)=>t+n.length),0),e=new Uint8Array(n);let r=0;for(const n of t)e.set(n,r),r+=n.length;return e}},42182:(t,n,e)=>{e.d(n,{Bd:()=>c,Dx:()=>l,dm:()=>u,oO:()=>f,pM:()=>s,um:()=>a});var r=e(32234);let o,i=!1;function c(t){o=t}function s(t){i=t}function u(t){return function(){return a(t,this,arguments)}}function a(t,n,e){try{return t.apply(n,e)}catch(t){l(t)}}function l(t){if(f(t),o)try{o(t)}catch(t){f(t)}}function f(...t){i&&r.Vy.error("[MONITOR]",...t)}},42995:(t,n,e)=>{e.d(n,{a:()=>a});var r=e(32234),o=e(36289),i=e(84601);const c=220*o._m,s="$",u=3;function a(t,n=c){const e=(0,i.M)(Object.prototype),r=(0,i.M)(Array.prototype),o=[],a=new WeakMap,f=l(t,s,void 0,o,a),h=JSON.stringify(f);let p=h?h.length:0;if(!(p>n)){for(;o.length>0&&p<n;){const e=o.shift();let r=0;if(Array.isArray(e.source))for(let i=0;i<e.source.length;i++){const c=l(e.source[i],e.path,i,o,a);if(p+=void 0!==c?JSON.stringify(c).length:4,p+=r,r=1,p>n){d(n,"truncated",t);break}e.target[i]=c}else for(const i in e.source)if(Object.prototype.hasOwnProperty.call(e.source,i)){const c=l(e.source[i],e.path,i,o,a);if(void 0!==c&&(p+=JSON.stringify(c).length+r+i.length+u,r=1),p>n){d(n,"truncated",t);break}e.target[i]=c}}return e(),r(),f}d(n,"discarded",t)}function l(t,n,e,r,o){const i=function(t){const n=t;if(n&&"function"==typeof n.toJSON)try{return n.toJSON()}catch(t){}return t}(t);if(!i||"object"!=typeof i)return"bigint"==typeof(c=i)?`[BigInt] ${c.toString()}`:"function"==typeof c?`[Function] ${c.name||"unknown"}`:"symbol"==typeof c?`[Symbol] ${c.description||c.toString()}`:c;var c;const s=f(i);if("[Object]"!==s&&"[Array]"!==s&&"[Error]"!==s)return s;const u=t;if(o.has(u))return`[Reference seen at ${o.get(u)}]`;const a=void 0!==e?`${n}.${e}`:n,l=Array.isArray(i)?[]:{};return o.set(u,a),r.push({source:i,target:l,path:a}),l}function f(t){try{if(t instanceof Event)return{type:(n=t).type,isTrusted:n.isTrusted,currentTarget:n.currentTarget?f(n.currentTarget):null,target:n.target?f(n.target):null};if(t instanceof RegExp)return`[RegExp] ${t.toString()}`;const e=Object.prototype.toString.call(t).match(/\[object (.*)\]/);if(e&&e[1])return`[${e[1]}]`}catch(t){}var n;return"[Unserializable]"}function d(t,n,e){r.Vy.warn(`The data provided has been ${n} as it is over the limit of ${t} characters:`,e)}},44193:(t,n,e)=>{e.d(n,{q:()=>l});var r=e(93001),o=e(72362),i=e(29336);const c=1/0,s=i.iW;let u=null;const a=new Set;function l({expireDelay:t,maxEntries:n}){let e=[];const l=[];u||(u=(0,r.yb)((()=>{a.forEach((t=>t()))}),s));const f=()=>{const n=(0,i.$S)()-t;for(;e.length>0&&e[e.length-1].endTime<n;){const t=e.pop();t&&l.push(t.startTime)}};return a.add(f),{add:function(t,r){const i={value:t,startTime:r,endTime:c,remove:()=>{(0,o.A)(e,i)},close:t=>{i.endTime=t}};return n&&e.length>=n&&e.pop(),e.unshift(i),i},find:function(t=c,n={returnInactive:!1}){for(const r of e)if(r.startTime<=t){if(n.returnInactive||t<=r.endTime)return r.value;break}},closeActive:function(t){const n=e[0];n&&n.endTime===c&&n.close(t)},findAll:function(t=c,n=0){const r=(0,i.Gw)(t,n);return e.filter((n=>n.startTime<=r&&t<=n.endTime)).map((t=>t.value))},reset:function(){e=[]},stop:function(){a.delete(f),0===a.size&&u&&((0,r.vG)(u),u=null)},getAllEntries:function(){return e.map((({startTime:t,endTime:n,value:e})=>({startTime:t,endTime:n===c?"Infinity":n,value:e})))},getDeletedEntries:function(){return l}}}},47197:(t,n,e)=>{e.d(n,{W:()=>o});var r=e(54564);function o(t,n){const e=(0,r.V)();let o;return e.Zone&&"function"==typeof e.Zone.__symbol__&&(o=t[e.Zone.__symbol__(n)]),o||(o=t[n]),o}},47978:(t,n,e)=>{e.d(n,{Go:()=>i,kg:()=>c});var r=e(31583);function o(t,n,e=function(){if("undefined"!=typeof WeakSet){const t=new WeakSet;return{hasAlreadyBeenSeen(n){const e=t.has(n);return e||t.add(n),e}}}const t=[];return{hasAlreadyBeenSeen(n){const e=t.indexOf(n)>=0;return e||t.push(n),e}}}()){if(void 0===n)return t;if("object"!=typeof n||null===n)return n;if(n instanceof Date)return new Date(n.getTime());if(n instanceof RegExp){const t=n.flags||[n.global?"g":"",n.ignoreCase?"i":"",n.multiline?"m":"",n.sticky?"y":"",n.unicode?"u":""].join("");return new RegExp(n.source,t)}if(e.hasAlreadyBeenSeen(n))return;if(Array.isArray(n)){const r=Array.isArray(t)?t:[];for(let t=0;t<n.length;++t)r[t]=o(r[t],n[t],e);return r}const i="object"===(0,r.P)(t)?t:{};for(const t in n)Object.prototype.hasOwnProperty.call(n,t)&&(i[t]=o(i[t],n[t],e));return i}function i(t){return o(void 0,t)}function c(...t){let n;for(const e of t)null!=e&&(n=o(n,e));return n}},48899:(t,n,e)=>{e.d(n,{Aq:()=>c,R9:()=>r,q7:()=>u,sr:()=>s});var r,o=e(78218);!function(t){t.WRITABLE_RESOURCE_GRAPHQL="writable_resource_graphql",t.MISSING_URL_CONTEXT_TELEMETRY="missing_url_context_telemetry"}(r||(r={}));const i=new Set;function c(t){Array.isArray(t)&&t.filter((t=>(0,o.Rj)(r,t))).forEach((t=>{i.add(t)}))}function s(t){return i.has(t)}function u(){return i}},50512:(t,n,e)=>{e.d(n,{N:()=>o});var r=e(36289);function o(){let t="",n=0;return{isAsync:!1,get isEmpty(){return!t},write(e,o){const i=(0,r.WW)(e);n+=i,t+=e,o&&o(i)},finish(t){t(this.finishSync())},finishSync(){const e={output:t,outputBytesCount:n,rawBytesCount:n,pendingData:""};return t="",n=0,e},estimateEncodedBytesCount:t=>t.length}}},51666:(t,n,e)=>{function r(t){return t>=500}function o(t){try{return t.clone()}catch(t){return}}e.d(n,{G:()=>r,i:()=>o})},52999:(t,n,e)=>{e.d(n,{l:()=>i,n:()=>o});var r=e(93001);function o(t,n,e){const o=!e||void 0===e.leading||e.leading,i=!e||void 0===e.trailing||e.trailing;let c,s,u=!1;return{throttled:(...e)=>{u?c=e:(o?t(...e):c=e,u=!0,s=(0,r.wg)((()=>{i&&c&&t(...c),u=!1,c=void 0}),n))},cancel:()=>{(0,r.DJ)(s),u=!1,c=void 0}}}function i(){}},54564:(t,n,e)=>{function r(){if("object"==typeof globalThis)return globalThis;Object.defineProperty(Object.prototype,"_dd_temp_",{get(){return this},configurable:!0});let t=_dd_temp_;return delete Object.prototype._dd_temp_,"object"!=typeof t&&(t="object"==typeof self?self:"object"==typeof window?window:{}),t}e.d(n,{V:()=>r})},59248:(t,n,e)=>{function r(t){return 0!==t&&100*Math.random()<=t}function o(t,n){return+t.toFixed(n)}function i(t){return c(t)&&t>=0&&t<=100}function c(t){return"number"==typeof t}e.d(n,{Et:()=>c,LI:()=>o,fp:()=>i,ic:()=>r})},68255:(t,n,e)=>{e.d(n,{O:()=>i});var r=e(72362);const o=500;function i(){const t=[];return{add:n=>{t.push(n)>o&&t.splice(0,1)},remove:n=>{(0,r.A)(t,n)},drain:n=>{t.forEach((t=>t(n))),t.length=0}}}},72362:(t,n,e)=>{function r(t,n){const e=t.indexOf(n);e>=0&&t.splice(e,1)}e.d(n,{A:()=>r})},74036:(t,n,e)=>{e.d(n,{K:()=>c,V:()=>i});var r=e(32234),o=e(31583);function i(t){const n=(0,o.P)(t);return"string"===n||"function"===n||t instanceof RegExp}function c(t,n,e=!1){return t.some((t=>{try{if("function"==typeof t)return t(n);if(t instanceof RegExp)return t.test(n);if("string"==typeof t)return e?n.startsWith(t):t===n}catch(t){r.Vy.error(t)}return!1}))}},75248:(t,n,e)=>{e.d(n,{AY:()=>i,L2:()=>c,c$:()=>s,l2:()=>o});var r=e(84601);function o(t){return s(t,location.href).href}function i(t){try{return!!s(t)}catch(t){return!1}}function c(t){const n=s(t).pathname;return"/"===n[0]?n:`/${n}`}function s(t,n){const e=function(){if(void 0===a)try{const t=new u("http://test/path");a="http://test/path"===t.href}catch(t){a=!1}return a?u:void 0}();if(e)try{return void 0!==n?new e(t,n):new e(t)}catch(e){throw new Error(`Failed to construct URL: ${String(e)} ${(0,r.s)({url:t,base:n})}`)}if(void 0===n&&!/:/.test(t))throw new Error(`Invalid URL: '${t}'`);let o=document;const i=o.createElement("a");if(void 0!==n){o=document.implementation.createHTMLDocument("");const t=o.createElement("base");t.href=n,o.head.appendChild(t),o.body.appendChild(i)}return i.href=t,i}const u=URL;let a},78218:(t,n,e)=>{function r(t){return{...t}}function o(t,n){return Object.keys(t).some((e=>t[e]===n))}function i(t){return 0===Object.keys(t).length}function c(t,n){const e={};for(const r of Object.keys(t))e[r]=n(t[r]);return e}e.d(n,{LG:()=>c,RI:()=>i,Rj:()=>o,yG:()=>r})},78476:(t,n,e)=>{e.d(n,{T:()=>o,h:()=>p});const r="?";function o(t){const n=[];let e=h(t,"stack");const o=String(t);return e&&e.startsWith(o)&&(e=e.slice(o.length)),e&&e.split("\n").forEach((t=>{const e=function(t){const n=s.exec(t);if(!n)return;const e=n[2]&&0===n[2].indexOf("native"),o=n[2]&&0===n[2].indexOf("eval"),i=u.exec(n[2]);return o&&i&&(n[2]=i[1],n[3]=i[2],n[4]=i[3]),{args:e?[n[2]]:[],column:n[4]?+n[4]:void 0,func:n[1]||r,line:n[3]?+n[3]:void 0,url:e?void 0:n[2]}}(t)||function(t){const n=a.exec(t);if(n)return{args:[],column:n[3]?+n[3]:void 0,func:r,line:n[2]?+n[2]:void 0,url:n[1]}}(t)||function(t){const n=l.exec(t);if(n)return{args:[],column:n[4]?+n[4]:void 0,func:n[1]||r,line:+n[3],url:n[2]}}(t)||function(t){const n=f.exec(t);if(!n)return;const e=n[3]&&n[3].indexOf(" > eval")>-1,o=d.exec(n[3]);return e&&o&&(n[3]=o[1],n[4]=o[2],n[5]=void 0),{args:n[2]?n[2].split(","):[],column:n[5]?+n[5]:void 0,func:n[1]||r,line:n[4]?+n[4]:void 0,url:n[3]}}(t);e&&(!e.func&&e.line&&(e.func=r),n.push(e))})),{message:h(t,"message"),name:h(t,"name"),stack:n}}const i="((?:file|https?|blob|chrome-extension|electron|native|eval|webpack|snippet|<anonymous>|\\w+\\.|\\/).*?)",c="(?::(\\d+))",s=new RegExp(`^\\s*at (.*?) ?\\(${i}${c}?${c}?\\)?\\s*$`,"i"),u=new RegExp(`\\((\\S*)${c}${c}\\)`),a=new RegExp(`^\\s*at ?${i}${c}?${c}??\\s*$`,"i"),l=/^\s*at (?:((?:\[object object\])?.+) )?\(?((?:file|ms-appx|https?|webpack|blob):.*?):(\d+)(?::(\d+))?\)?\s*$/i,f=/^\s*(.*?)(?:\((.*?)\))?(?:^|@)((?:file|https?|blob|chrome|webpack|resource|capacitor|\[native).*?|[^@]*bundle)(?::(\d+))?(?::(\d+))?\s*$/i,d=/(\S+) line (\d+)(?: > eval line \d+)* > eval/i;function h(t,n){if("object"!=typeof t||!t||!(n in t))return;const e=t[n];return"string"==typeof e?e:void 0}function p(t,n,e,r){const o=[{url:n,column:r,line:e}],{name:i,message:c}=function(t){let n,e;return"[object String]"==={}.toString.call(t)&&([,n,e]=g.exec(t)),{name:n,message:e}}(t);return{name:i,message:c,stack:o}}const g=/^(?:[Uu]ncaught (?:exception: )?)?(?:((?:Eval|Internal|Range|Reference|Syntax|Type|URI|)Error): )?([\s\S]*)$/},84601:(t,n,e)=>{e.d(n,{M:()=>i,s:()=>o});var r=e(52999);function o(t,n,e){if("object"!=typeof t||null===t)return JSON.stringify(t);const r=i(Object.prototype),o=i(Array.prototype),c=i(Object.getPrototypeOf(t)),s=i(t);try{return JSON.stringify(t,n,e)}catch(t){return"<error: unable to serialize object>"}finally{r(),o(),c(),s()}}function i(t){const n=t,e=n.toJSON;return e?(delete n.toJSON,()=>{n.toJSON=e}):r.l}},87967:(t,n,e)=>{e.d(n,{_:()=>i});var r=e(42182),o=e(52999);function i(t,n,e){const i=t.getReader(),c=[];let s=0;function u(){let t,r;if(i.cancel().catch(o.l),e.collectStreamBody){let n;if(1===c.length)n=c[0];else{n=new Uint8Array(s);let t=0;c.forEach((e=>{n.set(e,t),t+=e.length}))}t=n.slice(0,e.bytesLimit),r=n.length>e.bytesLimit}n(void 0,t,r)}!function t(){i.read().then((0,r.dm)((n=>{n.done?u():(e.collectStreamBody&&c.push(n.value),s+=n.value.length,s>e.bytesLimit?u():t())})),(0,r.dm)((t=>n(t))))}()}},92555:(t,n,e)=>{e.d(n,{F:()=>o,c:()=>r});class r{constructor(t){this.onFirstSubscribe=t,this.observers=[]}subscribe(t){return this.observers.push(t),1===this.observers.length&&this.onFirstSubscribe&&(this.onLastUnsubscribe=this.onFirstSubscribe(this)||void 0),{unsubscribe:()=>{this.observers=this.observers.filter((n=>t!==n)),!this.observers.length&&this.onLastUnsubscribe&&this.onLastUnsubscribe()}}}notify(t){this.observers.forEach((n=>n(t)))}}function o(...t){return new r((n=>{const e=t.map((t=>t.subscribe((t=>n.notify(t)))));return()=>e.forEach((t=>t.unsubscribe()))}))}},93001:(t,n,e)=>{e.d(n,{DJ:()=>s,vG:()=>a,wg:()=>c,yb:()=>u});var r=e(47197),o=e(42182),i=e(54564);function c(t,n){return(0,r.W)((0,i.V)(),"setTimeout")((0,o.dm)(t),n)}function s(t){(0,r.W)((0,i.V)(),"clearTimeout")(t)}function u(t,n){return(0,r.W)((0,i.V)(),"setInterval")((0,o.dm)(t),n)}function a(t){(0,r.W)((0,i.V)(),"clearInterval")(t)}},97228:(t,n,e)=>{e.d(n,{J:()=>s});var r=e(29336),o=e(34144);const i=r.OY,c=30;function s(){const t=[];function n(n){let r;if(n.didTimeout){const t=performance.now();r=()=>c-(performance.now()-t)}else r=n.timeRemaining.bind(n);for(;r()>0&&t.length;)t.shift()();t.length&&e()}function e(){(0,o.BB)(n,{timeout:i})}return{push(n){1===t.push(n)&&e()}}}}}]);
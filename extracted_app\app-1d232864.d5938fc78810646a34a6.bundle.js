"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[551],{86319:(e,t,o)=>{o.d(t,{T:()=>l});var i=o(15215),a=o("aurelia-framework"),r=o(3972),n=o("shared/dialogs/basic-dialog");let l=class{#e;#t;#o;constructor(e,t){this.#t=e,this.#o=t}async open(){if(!this.#e)try{this.#e=await this.#t.getUacIcon(24)}catch{}return"trainer_launcher.fix"===await this.#o.show({message:"trainer_launcher.cheats_missing",options:[{label:"trainer_launcher.close"},{label:"trainer_launcher.fix",icon:this.#e??""}]})}};l=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.<PERSON>z,n.BasicDialogService])],l)},"dialogs/resources/elements/desktop-with-overlay.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var i=o(14385),a=o.n(i),r=new URL(o(42475),o.b),n=new URL(o(80163),o.b);const l='<template> <require from="./desktop-with-overlay.scss"></require> <div class="image-wrapper"> <img attach-src="'+a()(r)+'" class="overlay"> <img attach-src="'+a()(n)+'" class="screenshot"> </div> </template> '},"dialogs/resources/elements/desktop-with-overlay.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r),l=o(4417),s=o.n(l),c=new URL(o(38321),o.b),d=n()(a()),p=s()(c);d.push([e.id,`desktop-with-overlay{display:block}desktop-with-overlay .image-wrapper{width:100%;padding-top:68.76%;position:relative;z-index:0}desktop-with-overlay .image-wrapper:before{content:"";display:block;width:100%;height:100%;background:url(${p}) top left/cover no-repeat;position:absolute;left:0;top:0;z-index:1}desktop-with-overlay .image-wrapper .screenshot{position:absolute;left:1.7%;top:2.1%;width:97.1%;z-index:0}desktop-with-overlay .image-wrapper .overlay{position:absolute;height:53.75%;top:4.5%;left:3.45%;z-index:1}`,""]);const g=d},"dialogs/resources/elements/maps-graphic.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <svg width="750" height="556" viewBox="0 0 750 556" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_5755_23449)"> <path d="M816.497 92.057L807.022 92.7285L798.68 98.6541C794.086 101.963 789.748 105.167 789.068 105.814C788.312 106.446 780.008 109.082 770.544 111.64L753.35 116.322L747.232 113.591L741.13 110.785L724.666 113.024C711.93 114.684 707.238 114.99 703.996 114.333C700.905 113.707 692.985 114.065 673.883 115.77C648.806 117.913 647.432 118.106 633.755 122.088C619.776 126.008 619.128 126.112 599.182 127.332L578.919 128.566L571.003 133.165C566.637 135.735 554.973 142.95 545.061 149.264L526.96 160.75L521.426 160.179L515.966 159.622L511.714 164.728C503.619 174.472 492.417 184.057 482.915 189.512C476.177 193.329 472.565 196.052 467.468 201.065C460.875 207.658 460.618 207.763 447.555 213.361C430.279 220.774 424.273 221.756 411.004 219.068C402.636 217.373 401.25 216.857 389.152 210.716C376.753 204.515 375.878 204.181 369.304 203.32L362.427 202.398L355.178 205.641C348.548 208.537 346.828 208.895 336.704 209.671C330.321 210.184 320.859 211.564 314.761 212.999L304.06 215.463L299.22 220.371C296.558 223.05 294.38 225.279 294.305 225.263C294.305 225.263 290.021 225.102 284.878 224.924L275.541 224.524L262.807 219.59L250.072 214.655L233.824 203.042L217.592 191.354L218.539 186.678C219.149 184.054 219.488 181.218 219.415 180.418C219.281 179.527 216.545 175.989 213.235 172.571C208.044 167.202 206.478 166.021 202.258 164.381C198.099 162.832 195.358 160.864 185.349 152.163C177.536 145.399 172.026 141.221 169.494 140.159C165.893 138.566 165.184 138.579 156.718 139.691C144.691 141.337 137.819 144.655 133.504 150.847C130.506 155.186 130.079 157.298 131.778 159.369C132.71 160.578 132.14 165.33 130.868 167.349C130.504 167.982 128.903 168.914 127.378 169.469C124.66 170.489 124.434 170.443 122.115 168.717L119.736 166.9L109.759 169.276C102.755 170.919 97.0627 173.063 90.8854 176.052C86.0068 178.439 77.9876 181.996 73.0347 183.977C61.5285 188.476 49.8794 189.806 32.0515 188.708L19.9703 187.909L10.511 182.303C5.42014 179.152 0.88658 176.35 0.646139 175.987C0.405692 175.624 2.31654 171.615 5.04336 167.064L9.86137 158.776L7.43329 152.553L4.94509 146.24L-1.83976 144.865C-8.17228 143.583 -8.95671 143.581 -13.275 145.14C-15.8116 146.039 -22.0318 148.077 -27.1192 149.558C-32.1313 151.055 -36.3885 152.313 -36.5698 152.433C-36.7359 152.478 -35.4662 157.446 -33.8673 163.501L-30.9081 174.463L-34.2922 176.447C-37.495 178.31 -46.7164 180.054 -47.83 178.965C-48.101 178.753 -48.7001 177.061 -49.2687 175.219L-50.1501 171.822L-52.3852 172.782C-53.6534 173.232 -55.8579 174.041 -57.3829 174.596L-60.2516 175.585L-63.9845 172.317L-67.7173 169.048L-70.6033 170.898C-74.7585 173.589 -83.4951 174.488 -88.7101 172.725C-92.0108 171.586 -93.7305 171.552 -98.1069 172.235C-102.981 173.054 -103.767 173.444 -106.626 176.712C-111.014 181.711 -112.458 186.129 -111.811 193.012C-111.326 197.978 -111.572 199.577 -114.314 210.405L-117.361 222.349L-115.714 233.203C-114.201 243.166 -113.587 245.174 -108.637 255.991L-103.266 267.834L-96.7285 271.592C-93.1435 273.653 -89.5126 275.487 -88.6833 275.655C-87.7787 275.839 -82.9968 275.865 -78.1089 275.756C-71.4861 275.606 -68.4982 275.19 -66.7909 274.123C-65.5369 273.356 -64.3135 272.741 -64.0873 272.787C-63.8611 272.832 -63.4128 274.493 -63.1162 276.516C-62.7442 278.554 -62.2204 280.23 -61.9189 280.292C-61.542 280.368 -58.6294 279.937 -55.3543 279.266C-52.0944 278.67 -48.7142 278.256 -47.8096 278.439C-44.4926 279.111 -26.4248 286.774 -18.0332 291.065L-9.29523 295.583L-0.435469 293.687L8.40902 291.868L17.8477 293.701C23.0494 294.755 32.9261 296.363 39.8475 297.451L52.4086 299.367L70.6547 308.087L88.8855 316.883L97.4072 317.431L105.989 318.071L111.849 321.299C115.088 323.132 117.875 324.482 117.89 324.407C117.996 324.271 119.435 322.207 121.115 319.721C124.567 314.69 131.627 308.897 137.546 306.406L141.488 304.77L145.884 308.252C148.654 310.461 152.234 314.484 155.24 319.018L160.23 326.153L160.574 333.367L160.933 340.505L166.413 344.834C169.394 347.165 174.86 351.177 178.504 353.721C184.362 357.733 185.867 359.216 190.105 365.413C193.667 370.688 194.838 373.045 194.547 374.478C194.333 375.533 194.885 378.236 195.769 380.456L197.298 384.534L203.847 383.584L210.381 382.709L223.558 391.659C242.864 404.834 249.382 410.236 265.031 425.967C285.57 446.615 284.488 444.982 291.581 467.695C294.858 478.25 298.899 490.845 300.532 495.572L303.497 504.181L310.8 509.193C314.806 511.889 323.693 517.222 330.457 521.025L342.779 527.996L353.503 528.52C365.373 529.197 363.743 529.495 387.928 522.068C402.738 517.531 406.014 516.86 417.028 515.951C423.758 515.273 432.357 515.052 435.992 515.317L442.628 515.876L447.384 519.901L452.126 524.002L461.013 523.133C470.534 522.235 469.341 522.7 494.37 509.948C498.312 507.921 503.718 505.64 506.466 504.862C509.138 504.068 511.568 503.304 511.84 503.124C512.112 502.944 511.517 499.683 510.577 495.802C508.994 489.28 508.982 488.178 510.143 482.446C511.395 476.262 511.653 475.765 519.814 463.758C527.96 451.826 528.233 451.253 529.455 445.22C530.311 440.997 530.53 437.587 530.086 434.357L529.464 429.677L535.148 419.052C541.484 407.146 540.591 408.064 558.622 394.602C564.471 390.134 571.44 384.401 573.936 381.767C576.522 379.072 581.799 374.331 585.653 371.186L592.562 365.363L599.639 364.52C607.108 363.677 616.861 360.471 630.649 354.393C635.391 352.291 639.439 350.52 639.711 350.34C639.967 350.235 638.901 348.527 637.412 346.577L634.676 343.039L645.959 337.317C652.167 334.178 661.907 329.87 667.509 327.786C676.327 324.548 679.227 323.016 687.479 317.15L697.105 310.307L698.14 300.939C699.035 293.033 699.706 290.108 702.304 282.705L705.343 273.899L717.152 263.259C728.31 253.115 729.232 252.438 736.133 249.753C749.768 244.429 765.644 235.79 776.541 227.712L786.803 220.056L796.327 205.969L805.851 191.883L808.803 181.961C812.393 170.048 812.499 169.912 833.292 149.787C843.046 140.379 849.032 135.232 854.457 131.699C861.513 127.083 862.706 126.618 873.815 124.079C886.419 121.137 887.822 121.185 895.478 124.071C899.848 125.741 899.848 125.741 902.84 123.756C905.741 121.832 905.756 121.756 905.752 117.516L905.763 113.2L880.01 108.219L854.182 103.223L840.846 97.3816C833.508 94.0895 827.209 91.4789 826.757 91.3873C826.365 91.3863 821.763 91.6319 816.497 92.057Z" fill="#0CF2F6" fill-opacity="0.05"/> <path d="M1454.38 686.051L1445.45 682.834L1435.42 684.88C1429.88 686.049 1424.62 687.225 1423.73 687.542C1422.79 687.814 1414.13 686.868 1404.44 685.38L1386.82 682.711L1382.33 677.739L1377.88 672.705L1361.92 668.096C1349.6 664.465 1345.18 662.847 1342.48 660.936C1339.91 659.113 1332.52 656.239 1314.36 650.074C1290.56 641.895 1289.22 641.516 1275.1 639.627C1260.73 637.56 1260.1 637.393 1241.36 630.444L1222.33 623.38L1213.23 624.385C1208.2 624.97 1194.61 626.853 1183 628.621L1161.8 631.807L1156.97 629.046L1152.2 626.33L1146.24 629.28C1134.9 634.919 1120.78 639.156 1109.88 640.303C1102.18 641.07 1097.77 642.1 1091.08 644.624C1082.39 647.988 1082.11 647.98 1067.9 647.818C1049.1 647.613 1043.21 646.082 1032.16 638.259C1025.19 633.325 1024.13 632.293 1015.55 621.785C1006.72 611.099 1006.05 610.44 1000.39 606.995L994.472 603.371L986.531 603.405C979.297 603.374 977.578 603.006 968.005 599.621C961.96 597.51 952.748 594.947 946.591 593.793L935.807 591.72L929.396 594.252C925.878 595.626 922.985 596.784 922.922 596.739C922.922 596.739 919.07 594.86 914.437 592.617L906.059 588.476L896.408 578.814L886.756 569.151L876.591 551.961L866.471 534.707L869.228 530.813C870.847 528.66 872.304 526.203 872.56 525.442C872.798 524.573 871.726 520.231 870.08 515.766C867.504 508.757 866.549 507.044 863.353 503.838C860.175 500.739 858.464 497.831 852.827 485.826C848.417 476.48 845.066 470.431 843.18 468.436C840.531 465.523 839.877 465.248 831.685 462.842C820.019 459.484 812.392 459.741 805.942 463.659C801.446 466.415 800.201 468.174 800.917 470.755C801.281 472.239 798.838 476.354 796.858 477.686C796.27 478.118 794.428 478.323 792.809 478.214C789.911 478.047 789.723 477.914 788.3 475.398L786.859 472.775L776.773 470.913C769.703 469.584 763.629 469.244 756.771 469.479C751.344 469.69 742.571 469.701 737.24 469.509C724.897 468.972 713.705 465.479 697.844 457.265L687.117 451.65L680.732 442.698C677.35 437.758 674.337 433.362 674.264 432.933C674.19 432.504 677.559 429.609 681.893 426.55L689.651 420.917L689.947 414.244L690.224 407.463L684.574 403.463C679.301 399.729 678.584 399.411 674.004 399.09C671.32 398.887 664.808 398.236 659.555 397.534C654.366 396.877 649.964 396.305 649.749 396.342C649.579 396.316 648.732 401.373 647.746 407.557L646.02 418.78L642.123 419.226C638.44 419.636 629.301 417.502 628.723 416.055C628.561 415.752 628.697 413.962 628.922 412.048L629.489 408.584L627.057 408.559C625.715 408.457 623.371 408.306 621.752 408.197L618.729 407.942L616.636 403.443L614.543 398.944L611.156 399.469C606.268 400.25 597.913 397.54 593.856 393.819C591.298 391.442 589.739 390.716 585.46 389.571C580.671 388.349 579.795 388.388 575.858 390.221C569.824 393.019 566.717 396.476 564.525 403.032C562.961 407.77 562.09 409.134 555.203 417.928L547.587 427.62L544.705 438.213C542.061 447.938 541.81 450.023 541.964 461.917L542.087 474.921L546.547 481.001C548.993 484.336 551.572 487.482 552.262 487.971C553.016 488.504 557.378 490.462 561.893 492.338C568.011 494.879 570.912 495.707 572.905 495.421C574.362 495.227 575.73 495.159 575.918 495.292C576.106 495.425 575.845 497.126 575.298 499.096C574.814 501.11 574.616 502.855 574.867 503.033C575.181 503.255 578.019 504.039 581.285 504.749C584.508 505.522 587.767 506.51 588.52 507.043C591.282 508.999 604.709 523.314 610.649 530.631L616.814 538.296L625.683 540.145L634.509 542.057L642.4 547.55C646.731 550.617 655.115 556.081 661.005 559.875L671.719 566.706L684.881 582.06L697.998 597.476L705.571 601.423L713.161 605.478L717.215 610.8C719.436 613.786 721.44 616.148 721.484 616.085C721.636 616.004 723.786 614.698 726.328 613.104C731.52 609.897 740.319 607.454 746.74 607.569L751.006 607.667L753.62 612.628C755.26 615.769 756.908 620.896 757.823 626.258L759.502 634.801L756.9 641.538L754.342 648.212L757.604 654.387C759.388 657.725 762.765 663.604 765.069 667.404C768.804 673.443 769.581 675.407 770.952 682.789C772.077 689.054 772.195 691.683 771.35 692.876C770.727 693.755 770.139 696.45 770.05 698.838L769.799 703.186L776.173 704.965L782.503 706.807L790.936 720.32C803.266 740.176 807.043 747.753 814.995 768.468C825.432 795.657 825.102 793.726 822.406 817.368C821.135 828.347 819.739 841.5 819.32 846.484L818.552 855.556L823.205 863.093C825.779 867.179 831.75 875.649 836.398 881.863L844.85 893.221L854.446 898.036C865.029 903.455 863.418 903.069 888.541 906.055C903.921 907.894 907.188 908.604 917.629 912.226C924.059 914.327 932.013 917.602 935.23 919.314L941.073 922.509L943.796 928.114L946.475 933.781L954.955 936.58C964.026 939.609 962.747 939.551 990.794 938.009C995.219 937.748 1001.09 937.848 1003.91 938.247C1006.68 938.602 1009.21 938.886 1009.53 938.831C1009.85 938.776 1010.63 935.553 1011.34 931.624C1012.53 925.018 1012.96 924.005 1016.34 919.233C1019.99 914.083 1020.42 913.733 1032.74 906.051C1045.02 898.431 1045.5 898.018 1049.06 892.994C1051.55 889.477 1053.13 886.447 1054.02 883.313L1055.35 878.782L1064.84 871.363C1075.45 863.035 1074.26 863.514 1096.2 858.491C1103.35 856.77 1112.05 854.345 1115.39 852.944C1118.85 851.525 1125.59 849.323 1130.39 848.005L1139.06 845.472L1145.88 847.563C1153.05 849.813 1163.27 850.824 1178.33 850.839C1183.52 850.835 1187.94 850.852 1188.26 850.797C1188.54 850.804 1188.25 848.811 1187.68 846.426L1186.61 842.084L1199.24 841.412C1206.19 841.051 1216.84 841.05 1222.8 841.409C1232.18 842.012 1235.45 841.784 1245.37 839.755L1256.94 837.389L1261.67 829.239C1265.69 822.371 1267.49 819.966 1272.86 814.246L1279.2 807.421L1294.3 802.464C1308.61 797.698 1309.72 797.451 1317.12 797.786C1331.74 798.43 1349.76 796.948 1362.99 793.966L1375.47 791.113L1389.88 782.08L1404.28 773.048L1411 765.166C1419.1 755.722 1419.25 755.641 1446.4 745.641C1459.13 740.981 1466.68 738.694 1473.07 737.656C1481.39 736.287 1482.67 736.345 1493.86 738.514C1506.58 740.919 1507.84 741.531 1513.68 747.265C1517 750.56 1517 750.56 1520.54 749.955C1523.97 749.368 1524.01 749.305 1525.72 745.425L1527.48 741.482L1505.94 726.513L1484.34 711.501L1474.5 700.766C1469.12 694.788 1464.42 689.853 1464.04 689.586C1463.68 689.427 1459.37 687.791 1454.38 686.051Z" fill="#0CF2F6" fill-opacity="0.05"/> <path d="M118.384 341.05L109.447 337.833L99.4212 339.88C93.8814 341.048 88.6188 342.225 87.7348 342.542C86.7879 342.814 78.1276 341.867 68.4367 340.38L50.8179 337.71L46.3269 332.739L41.8802 327.705L25.9171 323.096C13.5975 319.464 9.18231 317.847 6.48295 315.935C3.90917 314.113 -3.47999 311.238 -21.6403 305.074C-45.4425 296.894 -46.7765 296.515 -60.8956 294.626C-75.2657 292.56 -75.9013 292.393 -94.6371 285.444L-113.668 278.38L-122.768 279.385C-127.801 279.97 -141.386 281.853 -153.004 283.62L-174.204 286.806L-179.035 284.045L-183.803 281.329L-189.756 284.279C-201.1 289.918 -215.222 294.156 -226.118 295.303C-233.824 296.069 -238.229 297.099 -244.917 299.623C-253.613 302.988 -253.891 302.98 -268.101 302.818C-286.9 302.612 -292.79 301.082 -303.839 293.259C-310.807 288.325 -311.866 287.292 -320.448 276.784C-329.281 266.099 -329.945 265.44 -335.611 261.994L-341.528 258.371L-349.469 258.405C-356.703 258.373 -358.422 258.005 -367.995 254.621C-374.04 252.509 -383.252 249.946 -389.409 248.792L-400.193 246.719L-406.604 249.251C-410.122 250.626 -413.015 251.783 -413.078 251.739C-413.078 251.739 -416.93 249.859 -421.563 247.616L-429.941 243.476L-439.592 233.813L-449.244 224.151L-459.409 206.96L-469.529 189.706L-466.772 185.813C-465.153 183.66 -463.696 181.203 -463.44 180.441C-463.202 179.573 -464.274 175.231 -465.92 170.766C-468.496 163.756 -469.451 162.043 -472.647 158.837C-475.825 155.738 -477.536 152.83 -483.173 140.825C-487.583 131.48 -490.934 125.43 -492.82 123.435C-495.469 120.522 -496.123 120.248 -504.315 117.841C-515.981 114.484 -523.608 114.74 -530.058 118.659C-534.554 121.415 -535.799 123.173 -535.083 125.755C-534.719 127.238 -537.162 131.354 -539.142 132.686C-539.73 133.118 -541.572 133.322 -543.191 133.213C-546.089 133.047 -546.277 132.913 -547.7 130.397L-549.141 127.774L-559.227 125.913C-566.297 124.584 -572.371 124.243 -579.229 124.478C-584.656 124.69 -593.429 124.701 -598.76 124.509C-611.103 123.972 -622.295 120.478 -638.156 112.265L-648.883 106.65L-655.268 97.6976C-658.65 92.7573 -661.663 88.3611 -661.736 87.9321C-661.81 87.5031 -658.441 84.6087 -654.107 81.5491L-646.349 75.9169L-646.053 69.2436L-645.776 62.463L-651.426 58.4627C-656.699 54.729 -657.416 54.4101 -661.996 54.09C-664.68 53.8869 -671.192 53.2353 -676.445 52.5335C-681.634 51.8761 -686.036 51.3048 -686.251 51.3415C-686.421 51.3154 -687.268 56.3722 -688.254 62.5566L-689.98 73.7795L-693.877 74.2256C-697.56 74.635 -706.699 72.5011 -707.277 71.0547C-707.439 70.7513 -707.303 68.962 -707.078 67.047L-706.511 63.5834L-708.943 63.5581C-710.285 63.4565 -712.629 63.3056 -714.248 63.1963L-717.271 62.9411L-719.364 58.4424L-721.457 53.9438L-724.844 54.4682C-729.732 55.2495 -738.087 52.5398 -742.144 48.8187C-744.702 46.4418 -746.261 45.7151 -750.54 44.5709C-755.329 43.3483 -756.205 43.3879 -760.142 45.2204C-766.176 48.019 -769.283 51.4757 -771.475 58.032C-773.039 62.77 -773.91 64.1332 -780.797 72.9278L-788.413 82.6198L-791.295 93.2127C-793.939 102.937 -794.19 105.022 -794.036 116.917L-793.913 129.92L-789.453 136.001C-787.007 139.335 -784.428 142.481 -783.738 142.97C-782.984 143.503 -778.622 145.461 -774.107 147.338C-767.989 149.878 -765.088 150.707 -763.095 150.421C-761.638 150.227 -760.27 150.158 -760.082 150.291C-759.894 150.425 -760.155 152.125 -760.702 154.095C-761.186 156.11 -761.384 157.854 -761.133 158.032C-760.819 158.255 -757.981 159.038 -754.715 159.749C-751.492 160.522 -748.233 161.509 -747.48 162.043C-744.718 163.999 -731.291 178.313 -725.351 185.631L-719.186 193.296L-710.317 195.145L-701.491 197.056L-693.6 202.549C-689.269 205.616 -680.885 211.081 -674.995 214.874L-664.281 221.706L-651.119 237.059L-638.002 252.475L-630.429 256.423L-622.839 260.477L-618.785 265.799C-616.564 268.786 -614.56 271.147 -614.516 271.084C-614.364 271.003 -612.214 269.697 -609.672 268.103C-604.48 264.897 -595.681 262.453 -589.26 262.569L-584.994 262.666L-582.38 267.628C-580.74 270.769 -579.092 275.896 -578.177 281.258L-576.498 289.801L-579.1 296.538L-581.658 303.212L-578.396 309.387C-576.612 312.724 -573.235 318.604 -570.931 322.404C-567.196 328.443 -566.419 330.407 -565.048 337.788C-563.923 344.053 -563.805 346.682 -564.65 347.875C-565.273 348.754 -565.861 351.449 -565.95 353.837L-566.201 358.185L-559.827 359.964L-553.497 361.806L-545.064 375.32C-532.734 395.175 -528.957 402.752 -521.005 423.468C-510.568 450.656 -510.898 448.726 -513.594 472.367C-514.865 483.346 -516.261 496.5 -516.68 501.483L-517.448 510.556L-512.795 518.093C-510.221 522.178 -504.25 530.649 -499.602 536.863L-491.15 548.221L-481.554 553.036C-470.971 558.455 -472.582 558.068 -447.459 561.054C-432.079 562.893 -428.812 563.604 -418.371 567.226C-411.941 569.326 -403.987 572.601 -400.77 574.314L-394.927 577.508L-392.204 583.113L-389.525 588.781L-381.045 591.579C-371.974 594.608 -373.253 594.551 -345.206 593.008C-340.781 592.748 -334.914 592.847 -332.086 593.247C-329.322 593.601 -326.79 593.886 -326.468 593.831C-326.146 593.776 -325.372 590.552 -324.664 586.623C-323.474 580.018 -323.039 579.005 -319.66 574.232C-316.014 569.083 -315.577 568.732 -303.258 561.05C-290.983 553.431 -290.502 553.017 -286.945 547.993C-284.455 544.477 -282.875 541.447 -281.975 538.313L-280.652 533.782L-271.157 526.362C-260.548 518.034 -261.736 518.514 -239.802 513.491C-232.645 511.77 -223.954 509.344 -220.606 507.944C-217.15 506.525 -210.408 504.322 -205.61 503.005L-196.937 500.472L-190.123 502.562C-182.951 504.812 -172.735 505.823 -157.666 505.839C-152.48 505.834 -148.062 505.851 -147.74 505.796C-147.463 505.804 -147.748 503.811 -148.321 501.425L-149.393 497.083L-136.76 496.412C-129.813 496.051 -119.162 496.049 -113.196 496.408C-103.822 497.012 -100.549 496.783 -90.6311 494.755L-79.0597 492.389L-74.3256 484.238C-70.3107 477.37 -68.5139 474.965 -63.1442 469.245L-56.804 462.421L-41.7016 457.463C-27.3944 452.697 -26.2775 452.451 -18.8805 452.786C-4.25667 453.429 13.757 451.947 26.9893 448.966L39.4709 446.113L53.8776 437.08L68.2841 428.047L74.9957 420.166C83.096 410.722 83.2477 410.641 110.403 400.641C123.128 395.98 130.684 393.694 137.074 392.655C145.394 391.287 146.673 391.344 157.86 393.514C170.577 395.919 171.841 396.53 177.676 402.265C180.998 405.56 180.998 405.56 184.537 404.954C187.969 404.367 188.013 404.304 189.724 400.424L191.479 396.481L169.939 381.513L148.336 366.5L138.501 355.765C133.121 349.787 128.415 344.853 128.039 344.586C127.68 344.426 123.372 342.79 118.384 341.05Z" fill="#0CF2F6" fill-opacity="0.05"/> <g filter="url(#filter0_d_5755_23449)"> <path d="M152 321C152 340.629 141.389 357.523 126.154 365.073C110.737 357.523 100 340.629 100 321C100 306.641 111.641 295 126 295C140.359 295 152 306.641 152 321Z" fill="#56CBAF"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M117.523 308.66C123.129 307.641 128.873 307.641 134.479 308.66C135.519 308.849 136.397 309.54 136.826 310.505L139.395 316.285C139.524 316.574 139.67 316.856 139.832 317.127C140.171 316.869 140.601 316.74 141.043 316.784L143.595 317.039C143.958 317.075 144.26 317.336 144.349 317.691L144.761 319.339C144.909 319.93 144.417 320.485 143.813 320.409L141.804 320.158C141.769 320.154 141.734 320.147 141.7 320.139C142.203 321.317 142.468 322.591 142.468 323.888V331.399C142.468 332.835 141.304 333.999 139.868 333.999H136.23C134.973 333.999 133.939 333.045 133.814 331.821C133.485 331.573 133.233 331.235 133.095 330.845C128.399 331.64 123.603 331.64 118.907 330.845C118.77 331.235 118.517 331.573 118.188 331.821C118.063 333.045 117.03 333.999 115.773 333.999H112.135C110.699 333.999 109.535 332.835 109.535 331.399V323.888C109.535 322.582 109.803 321.298 110.314 320.113C110.211 320.146 110.104 320.17 109.995 320.184L108.19 320.409C107.585 320.485 107.094 319.93 107.241 319.339L107.653 317.691C107.742 317.336 108.044 317.075 108.408 317.039L110.959 316.784C111.401 316.74 111.832 316.869 112.17 317.127C112.333 316.856 112.479 316.574 112.607 316.285L115.176 310.505C115.605 309.54 116.484 308.849 117.523 308.66ZM137.34 318.185C129.814 317.181 122.187 317.181 114.661 318.185L113.115 318.391C112.641 318.454 112.205 318.121 112.142 317.646C112.078 317.172 112.412 316.736 112.886 316.673L114.432 316.467C122.11 315.443 129.891 315.443 137.569 316.467L139.115 316.673C139.59 316.736 139.923 317.172 139.86 317.646C139.796 318.121 139.361 318.454 138.886 318.391L137.34 318.185ZM115.436 321.655C115.533 321.269 115.88 320.999 116.277 320.999H119.934C120.413 320.999 120.801 321.387 120.801 321.865V323.599C120.801 324.077 120.413 324.465 119.934 324.465H115.844C115.28 324.465 114.866 323.936 115.003 323.389L115.436 321.655ZM122.579 325.925C122.73 325.471 123.221 325.225 123.675 325.377L124.082 325.513C125.328 325.928 126.674 325.928 127.919 325.513L128.327 325.377C128.781 325.225 129.272 325.471 129.423 325.925C129.574 326.379 129.329 326.87 128.875 327.021L128.467 327.157C126.866 327.691 125.135 327.691 123.534 327.157L123.127 327.021C122.673 326.87 122.427 326.379 122.579 325.925ZM136.565 321.655C136.468 321.269 136.122 320.999 135.724 320.999H132.067C131.589 320.999 131.201 321.387 131.201 321.865V323.599C131.201 324.077 131.589 324.465 132.067 324.465H136.157C136.721 324.465 137.135 323.936 136.998 323.389L136.565 321.655Z" fill="white"/> </g> <g filter="url(#filter1_d_5755_23449)"> <path d="M524 206C524 225.629 513.389 242.523 498.154 250.073C482.737 242.523 472 225.629 472 206C472 191.641 483.641 180 498 180C512.359 180 524 191.641 524 206Z" fill="#56CBAF"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M489.523 193.66C495.129 192.641 500.873 192.641 506.479 193.66C507.519 193.849 508.397 194.54 508.826 195.505L511.395 201.285C511.524 201.574 511.67 201.856 511.832 202.127C512.171 201.869 512.601 201.74 513.043 201.784L515.595 202.039C515.958 202.075 516.26 202.336 516.349 202.691L516.761 204.339C516.909 204.93 516.417 205.485 515.813 205.409L513.804 205.158C513.769 205.154 513.734 205.147 513.7 205.139C514.203 206.317 514.468 207.591 514.468 208.888V216.399C514.468 217.835 513.304 218.999 511.868 218.999H508.23C506.973 218.999 505.939 218.045 505.814 216.821C505.485 216.573 505.233 216.235 505.095 215.845C500.399 216.64 495.603 216.64 490.907 215.845C490.77 216.235 490.517 216.573 490.188 216.821C490.063 218.045 489.03 218.999 487.773 218.999H484.135C482.699 218.999 481.535 217.835 481.535 216.399V208.888C481.535 207.582 481.803 206.298 482.314 205.113C482.211 205.146 482.104 205.17 481.995 205.184L480.19 205.409C479.585 205.485 479.094 204.93 479.241 204.339L479.653 202.691C479.742 202.336 480.044 202.075 480.408 202.039L482.959 201.784C483.401 201.74 483.832 201.869 484.17 202.127C484.333 201.856 484.479 201.574 484.607 201.285L487.176 195.505C487.605 194.54 488.484 193.849 489.523 193.66ZM509.34 203.185C501.814 202.181 494.187 202.181 486.661 203.185L485.115 203.391C484.641 203.454 484.205 203.121 484.142 202.646C484.078 202.172 484.412 201.736 484.886 201.673L486.432 201.467C494.11 200.443 501.891 200.443 509.569 201.467L511.115 201.673C511.59 201.736 511.923 202.172 511.86 202.646C511.796 203.121 511.361 203.454 510.886 203.391L509.34 203.185ZM487.436 206.655C487.533 206.269 487.88 205.999 488.277 205.999H491.934C492.413 205.999 492.801 206.387 492.801 206.865V208.599C492.801 209.077 492.413 209.465 491.934 209.465H487.844C487.28 209.465 486.866 208.936 487.003 208.389L487.436 206.655ZM494.579 210.925C494.73 210.471 495.221 210.225 495.675 210.377L496.082 210.513C497.328 210.928 498.674 210.928 499.919 210.513L500.327 210.377C500.781 210.225 501.272 210.471 501.423 210.925C501.574 211.379 501.329 211.87 500.875 212.021L500.467 212.157C498.866 212.691 497.135 212.691 495.534 212.157L495.127 212.021C494.673 211.87 494.427 211.379 494.579 210.925ZM508.565 206.655C508.468 206.269 508.122 205.999 507.724 205.999H504.067C503.589 205.999 503.201 206.387 503.201 206.865V208.599C503.201 209.077 503.589 209.465 504.067 209.465H508.157C508.721 209.465 509.135 208.936 508.998 208.389L508.565 206.655Z" fill="white"/> </g> <g filter="url(#filter2_d_5755_23449)"> <path d="M677 263.571C677 273.386 671.695 281.833 664.077 285.608C656.369 281.833 651 273.386 651 263.571C651 256.392 656.82 250.571 664 250.571C671.18 250.571 677 256.392 677 263.571Z" fill="#B36B78"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M656.199 263.571C656.199 259.263 659.691 255.771 663.999 255.771C668.307 255.771 671.799 259.263 671.799 263.571V269.016C671.799 269.623 671.569 270.177 671.192 270.595C670.946 270.866 670.612 270.952 670.306 270.907C670.011 270.863 669.737 270.7 669.545 270.469L668.665 269.414C668.492 269.206 668.173 269.206 668 269.414L667.165 270.416C666.645 271.04 665.687 271.04 665.167 270.416L664.332 269.414C664.159 269.206 663.84 269.206 663.666 269.414L662.831 270.416C662.312 271.04 661.354 271.04 660.834 270.416L659.999 269.414C659.826 269.206 659.506 269.206 659.333 269.414L658.454 270.469C658.261 270.7 657.988 270.863 657.692 270.907C657.387 270.952 657.052 270.866 656.807 270.595C656.43 270.177 656.199 269.623 656.199 269.016V263.571ZM662.789 262.361C662.958 262.192 662.958 261.917 662.789 261.748C662.62 261.579 662.345 261.579 662.176 261.748L661.183 262.742L660.189 261.748C660.02 261.579 659.745 261.579 659.576 261.748C659.407 261.917 659.407 262.192 659.576 262.361L660.57 263.354L659.576 264.348C659.407 264.517 659.407 264.792 659.576 264.961C659.745 265.13 660.02 265.13 660.189 264.961L661.183 263.967L662.176 264.961C662.345 265.13 662.62 265.13 662.789 264.961C662.958 264.792 662.958 264.517 662.789 264.348L661.795 263.354L662.789 262.361ZM668.422 262.361C668.592 262.192 668.592 261.917 668.422 261.748C668.253 261.579 667.979 261.579 667.809 261.748L666.816 262.742L665.822 261.748C665.653 261.579 665.379 261.579 665.209 261.748C665.04 261.917 665.04 262.192 665.209 262.361L666.203 263.354L665.209 264.348C665.04 264.517 665.04 264.792 665.209 264.961C665.379 265.13 665.653 265.13 665.822 264.961L666.816 263.967L667.809 264.961C667.979 265.13 668.253 265.13 668.422 264.961C668.592 264.792 668.592 264.517 668.422 264.348L667.429 263.354L668.422 262.361Z" fill="white"/> </g> <g filter="url(#filter3_d_5755_23449)"> <path d="M610 377C610 396.629 599.389 413.523 584.154 421.073C568.737 413.523 558 396.629 558 377C558 362.641 569.641 351 584 351C598.359 351 610 362.641 610 377Z" fill="#8A84BF"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M578.249 363.077C578.544 362.25 578.902 361.608 579.339 361.18C579.753 360.775 580.263 360.533 580.968 360.533C581.577 360.533 581.952 360.77 582.211 361.157C582.501 361.589 582.682 362.272 582.682 363.168C582.682 365.172 581.851 367.015 580.972 368.393C580.538 369.076 580.102 369.627 579.777 370.006C579.614 370.196 579.48 370.341 579.389 370.437C579.343 370.485 579.308 370.521 579.285 370.544L579.261 370.568L579.255 370.573C579.002 370.818 578.922 371.193 579.054 371.521C579.186 371.848 579.504 372.063 579.858 372.063L600.506 372.063C601.438 372.063 602.198 372.821 602.198 373.763V374.084C602.198 374.588 601.707 375.464 600.468 375.464L588.703 375.464C588.224 375.464 587.836 375.852 587.836 376.33C587.836 376.809 588.224 377.197 588.703 377.197H597.864C598.795 377.197 599.555 377.956 599.555 378.898V379.219C599.555 380.161 598.795 380.919 597.864 380.919H588.335C587.856 380.919 587.468 381.307 587.468 381.786C587.468 382.264 587.856 382.652 588.335 382.652L592.375 382.653C592.801 382.962 593.077 383.464 593.077 384.032V384.353C593.077 385.295 592.316 386.053 591.385 386.053H587.424C586.945 386.053 586.557 386.441 586.557 386.92C586.557 387.398 586.945 387.786 587.424 387.786H589.816C590.242 388.096 590.519 388.598 590.519 389.166C590.519 390.108 589.759 390.867 588.827 390.867L572.655 390.867C570.311 390.867 568.406 388.959 568.406 386.599V380.656C568.406 380.46 568.405 380.27 568.404 380.086C568.398 378.823 568.393 377.823 568.68 376.772C568.998 375.608 569.697 374.329 571.337 372.683C571.764 372.255 572.163 371.859 572.537 371.489L572.537 371.489C573.803 370.237 574.779 369.27 575.579 368.251C576.649 366.889 577.41 365.436 578.249 363.077Z" fill="white"/> <path d="M597.871 369.199C597.871 368.481 598.453 367.899 599.171 367.899C599.889 367.899 600.471 368.481 600.471 369.199V370.499H597.871V369.199Z" fill="white"/> </g> <g opacity="0.5" filter="url(#filter4_d_5755_23449)"> <path d="M65 227.571C65 237.386 59.6946 245.833 52.0769 249.608C44.3686 245.833 39 237.386 39 227.571C39 220.392 44.8203 214.571 52 214.571C59.1797 214.571 65 220.392 65 227.571Z" fill="#8A84BF"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M49.1227 220.61C49.2698 220.196 49.4488 219.875 49.6676 219.661C49.8744 219.459 50.1295 219.338 50.4821 219.338C50.7865 219.338 50.974 219.456 51.1037 219.65C51.2486 219.866 51.3393 220.207 51.3393 220.655C51.3393 221.657 50.9234 222.579 50.4843 223.268C50.2668 223.609 50.0491 223.885 49.8864 224.074C49.8052 224.169 49.7382 224.242 49.6923 224.29C49.6694 224.314 49.6519 224.332 49.6405 224.343L49.6283 224.355L49.6258 224.358C49.4988 224.48 49.4589 224.668 49.525 224.832C49.5911 224.996 49.7501 225.103 49.9268 225.103L60.2512 225.103C60.7169 225.103 61.0969 225.482 61.0969 225.953V226.114C61.0969 226.365 60.8518 226.803 60.2322 226.803L54.3495 226.803C54.1102 226.803 53.9162 226.997 53.9162 227.237C53.9162 227.476 54.1102 227.67 54.3495 227.67L58.9302 227.67C59.3956 227.67 59.7758 228.049 59.7758 228.52V228.681C59.7758 229.152 59.3956 229.531 58.9302 229.531H54.1656C53.9263 229.531 53.7323 229.725 53.7322 229.964C53.7322 230.203 53.9263 230.397 54.1656 230.397L56.1856 230.398C56.3983 230.552 56.5363 230.803 56.5363 231.087V231.248C56.5363 231.719 56.1562 232.098 55.6907 232.098H53.7101C53.4707 232.098 53.2767 232.292 53.2767 232.531C53.2767 232.77 53.4707 232.964 53.7101 232.964H54.9061C55.1192 233.119 55.2574 233.37 55.2574 233.654C55.2574 234.125 54.8774 234.505 54.4116 234.505L46.3254 234.505C45.1536 234.505 44.2009 233.551 44.2009 232.371V229.399C44.2009 229.301 44.2004 229.206 44.2 229.114L44.2 229.114C44.1971 228.483 44.1948 227.983 44.3382 227.457C44.4969 226.875 44.8465 226.236 45.6665 225.413C45.8801 225.199 46.0797 225.001 46.2666 224.816L46.2666 224.816C46.8993 224.19 47.3876 223.706 47.7877 223.197C48.3227 222.516 48.7031 221.789 49.1227 220.61Z" fill="white"/> <path d="M58.9336 223.671C58.9336 223.312 59.2246 223.021 59.5836 223.021C59.9426 223.021 60.2336 223.312 60.2336 223.671V224.321H58.9336V223.671Z" fill="white"/> </g> <g opacity="0.5" filter="url(#filter5_d_5755_23449)"> <path d="M718 146C718 155.815 712.695 164.262 705.077 168.037C697.369 164.262 692 155.815 692 146C692 138.82 697.82 133 705 133C712.18 133 718 138.82 718 146Z" fill="#8A84BF"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M702.123 139.039C702.27 138.625 702.449 138.304 702.668 138.09C702.874 137.887 703.13 137.767 703.482 137.767C703.787 137.767 703.974 137.885 704.104 138.078C704.249 138.294 704.339 138.636 704.339 139.084C704.339 140.086 703.923 141.007 703.484 141.697C703.267 142.038 703.049 142.314 702.886 142.503C702.805 142.598 702.738 142.67 702.692 142.719C702.669 142.743 702.652 142.761 702.641 142.772L702.628 142.784L702.626 142.787C702.499 142.909 702.459 143.097 702.525 143.26C702.591 143.424 702.75 143.532 702.927 143.532L713.251 143.532C713.717 143.532 714.097 143.911 714.097 144.382V144.542C714.097 144.794 713.852 145.232 713.232 145.232L707.35 145.232C707.11 145.232 706.916 145.426 706.916 145.665C706.916 145.905 707.11 146.099 707.35 146.099L711.93 146.099C712.396 146.099 712.776 146.478 712.776 146.949V147.109C712.776 147.58 712.396 147.959 711.93 147.959H707.166C706.926 147.959 706.732 148.153 706.732 148.393C706.732 148.632 706.926 148.826 707.166 148.826L709.186 148.826C709.398 148.981 709.536 149.232 709.536 149.516V149.676C709.536 150.147 709.156 150.526 708.691 150.526H706.71C706.471 150.526 706.277 150.721 706.277 150.96C706.277 151.199 706.471 151.393 706.71 151.393H707.906C708.119 151.548 708.257 151.799 708.257 152.083C708.257 152.554 707.877 152.933 707.412 152.933L699.325 152.933C698.154 152.933 697.201 151.979 697.201 150.8V147.828C697.201 147.73 697.2 147.635 697.2 147.543L697.2 147.543C697.197 146.912 697.195 146.411 697.338 145.886C697.497 145.304 697.846 144.665 698.667 143.842C698.88 143.627 699.08 143.43 699.267 143.245L699.267 143.245C699.899 142.618 700.388 142.135 700.788 141.626C701.323 140.945 701.703 140.218 702.123 139.039Z" fill="white"/> <path d="M711.934 142.1C711.934 141.741 712.225 141.45 712.584 141.45C712.943 141.45 713.234 141.741 713.234 142.1V142.75H711.934V142.1Z" fill="white"/> </g> <g filter="url(#filter6_d_5755_23449)"> <path d="M365 257C365 296.259 343.778 330.046 313.308 345.146C282.474 330.046 261 296.259 261 257C261 228.281 284.281 205 313 205C341.719 205 365 228.281 365 257Z" fill="#7CCB8D"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M279.195 247.466C279.195 237.893 286.956 230.132 296.529 230.132H329.462C339.035 230.132 346.795 237.893 346.795 247.466V278.666C346.795 281.538 344.467 283.866 341.595 283.866H284.395C281.523 283.866 279.195 281.538 279.195 278.666V247.466ZM282.662 278.666V252.666H311.262V278.666C311.262 279.623 310.486 280.399 309.529 280.399H284.395C284.335 280.399 284.276 280.396 284.218 280.39C283.344 280.301 282.662 279.563 282.662 278.666ZM282.662 247.899V249.199H311.262V247.899C311.262 240.001 304.86 233.599 296.962 233.599C289.064 233.599 282.662 240.001 282.662 247.899ZM331.195 249.632V254.399H326.862V249.632C326.862 248.436 327.832 247.466 329.029 247.466C330.225 247.466 331.195 248.436 331.195 249.632Z" fill="white"/> </g> <text fill="#7CCB8D" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="18" font-weight="500" letter-spacing="0px" text-anchor="middle"> <tspan x="312.359" y="373.545">${\'maps_graphic.item\' | i18n}</tspan> </text> <text opacity="0.5" fill="#56CBAF" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px" text-anchor="middle"> <tspan x="126.3428" y="382.591">${\'maps_graphic.vehicle\' | i18n}</tspan> </text> <text opacity="0.5" fill="#8A84BF" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px" text-anchor="middle"> <tspan x="584.41" y="438.591">${\'maps_graphic.gun\' | i18n}</tspan> </text> <text opacity="0.5" fill="#56CBAF" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px" text-anchor="middle"> <tspan x="498.343" y="267.591">${\'maps_graphic.vehicle\' | i18n}</tspan> </text> <text opacity="0.5" fill="#B36B78" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px" text-anchor="middle"> <tspan x="664.078" y="300.864">${\'maps_graphic.enemy_camp\' | i18n}</tspan> </text> <text opacity="0.5" fill="#8A84BF" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px" text-anchor="middle"> <tspan x="51.3516" y="262.864">${\'maps_graphic.gun\' | i18n}</tspan> </text> <text opacity="0.5" fill="#8A84BF" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px" text-anchor="middle"> <tspan x="705.352" y="181.292">${\'maps_graphic.gun\' | i18n}</tspan> </text> </g> <defs> <filter id="filter0_d_5755_23449" x="90" y="285" width="72" height="94.2856" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="5"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0.5 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5755_23449"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5755_23449" result="shape"/> </filter> <filter id="filter1_d_5755_23449" x="462" y="170" width="72" height="94.2856" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="5"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0.5 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5755_23449"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5755_23449" result="shape"/> </filter> <filter id="filter2_d_5755_23449" x="646" y="245.571" width="36" height="47.1431" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="2.5"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0.5 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5755_23449"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5755_23449" result="shape"/> </filter> <filter id="filter3_d_5755_23449" x="548" y="341" width="72" height="94.2856" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="5"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0.5 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5755_23449"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5755_23449" result="shape"/> </filter> <filter id="filter4_d_5755_23449" x="34" y="209.571" width="36" height="47.1431" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="2.5"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0.5 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5755_23449"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5755_23449" result="shape"/> </filter> <filter id="filter5_d_5755_23449" x="687" y="128" width="36" height="47.1431" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="2.5"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0.5 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5755_23449"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5755_23449" result="shape"/> </filter> <filter id="filter6_d_5755_23449" x="241" y="185" width="144" height="188.571" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="10"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0 0.0666667 0 0 0 0.5 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_5755_23449"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_5755_23449" result="shape"/> </filter> <clippath id="clip0_5755_23449"> <rect width="750" height="556" fill="white"/> </clippath> </defs> </svg> </template> '},"dialogs/resources/elements/nps-score-selector":(e,t,o)=>{o.r(t),o.d(t,{NpsScoreSelector:()=>r});var i=o(15215),a=o("aurelia-framework");let r=class{};(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Function)],r.prototype,"setRating",void 0),r=(0,i.Cg)([(0,a.autoinject)()],r)},"dialogs/resources/elements/nps-score-selector.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template class="nps-score-selector"> <require from="./nps-score-selector.scss"></require> <div class="score-selector"> <div class="nps-buttons"> <button repeat.for="i of 11" click.delegate="setRating({rating: i})">${i}</button> </div> <div class="legend"> <span> <slot name="lowScoreLabel"></slot> </span> <span> <slot name="highScoreLabel"></slot> </span> </div> </div> </template> '},"dialogs/resources/elements/nps-score-selector.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r)()(a());n.push([e.id,".nps-score-selector{display:block}.nps-score-selector .score-selector{display:inline-block}.nps-score-selector .nps-buttons{display:inline-block}.nps-score-selector .nps-buttons button{font-weight:800;font-size:21px;line-height:30px;font-weight:500;color:#fff;display:inline-block;width:37px;height:37px;border-radius:50%;border:1px solid var(--theme--highlight);background:rgba(0,0,0,0);outline:none;line-height:35px;text-align:center;transition:color .15s,background-color .15s;padding:0}.nps-score-selector .nps-buttons button+button{margin-left:10px}.nps-score-selector .nps-buttons button:hover{background:var(--theme--highlight);color:#000}.nps-score-selector .legend{display:flex;width:100%;font-size:13px;line-height:12px;color:rgba(255,255,255,.4);margin-top:6px}.nps-score-selector .legend>span{display:inline-block;width:50%}.nps-score-selector .legend>span>span{text-align:center;display:inline-block;max-width:80px}.nps-score-selector .legend>span:first-child{text-align:left}.nps-score-selector .legend>span:first-child>span{transform:translateX(calc(-50% + 18.5px))}.nps-score-selector .legend>span:last-child{text-align:right}.nps-score-selector .legend>span:last-child>span{transform:translateX(calc(50% - 18.5px))}",""]);const l=n},"dialogs/resources/elements/remote-platforms.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});var i=o(14385),a=o.n(i),r=new URL(o(26991),o.b);const n='<template bindable="highlight" class="${highlight ? \'highlight\' : \'\'}"> <require from="./remote-platforms.scss"></require> <div class="icons"> <inline-svg src="'+a()(r)+'"></inline-svg> </div> <div class="label" innerhtml.bind="\'remote_platforms.available_on_mobile\' | i18n | markdown"></div> </template> '},"dialogs/resources/elements/remote-platforms.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r)()(a());n.push([e.id,"remote-platforms{display:block;border-radius:10px;background:linear-gradient(180deg, rgba(var(--color--accent--rgb), 0.25) 0%, transparent 100%);width:158px;padding:15px 18px 14px 19px;overflow:hidden;text-align:center}remote-platforms .icons{margin:0 0 7px 0}remote-platforms .label{font-size:14px;line-height:22px;color:rgba(255,255,255,.8)}remote-platforms .label em{font-style:normal;color:#fff}remote-platforms.highlight{background:rgba(var(--theme--default--background--rgb), 0.4)}remote-platforms.highlight svg *{fill:var(--theme--highlight)}",""]);const l=n},"dialogs/resources/elements/support-wemod-footer":(e,t,o)=>{o.r(t),o.d(t,{SupportWemodFooter:()=>n});var i=o(15215),a=o("aurelia-framework"),r=o(33700);let n=class{constructor(e){this.supportedGameCount=e}};n=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.q])],n)},"dialogs/resources/elements/support-wemod-footer.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});var i=o(14385),a=o.n(i),r=new URL(o(2761),o.b);const n='<template> <require from="./support-wemod-footer.scss"></require> <img class="support-icon" src="'+a()(r)+'"> <div class="support-message" innerhtml.bind="\'support_wemod_footer.members_like_you_make_wemod_possible\' | i18n:{x: supportedGameCount.formattedCount} | markdown"></div> </template> '},"dialogs/resources/elements/support-wemod-footer.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r)()(a());n.push([e.id,"support-wemod-footer{display:flex;flex-direction:column;align-items:center;justify-content:center}support-wemod-footer .support-icon{display:block;width:56px;height:43px;margin:0 0 21px 0}support-wemod-footer .support-message{font-size:16px;line-height:24px;font-weight:500;color:rgba(255,255,255,.6);width:290px;text-align:center}",""]);const l=n},"dialogs/resources/elements/time-limit-graphic.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});var i=o(14385),a=o.n(i),r=new URL(o(12124),o.b);const n='<template> <require from="./time-limit-graphic.scss"></require> <require from="../../../shared/resources/elements/pro-badge"></require> <div> <inline-svg src="'+a()(r)+'"></inline-svg> </div> <span class="unlimited-label"> <span class="text">${\'time_limit_graphic.unlimited\' | i18n}</span> <pro-badge></pro-badge> </span> </template> '},"dialogs/resources/elements/time-limit-graphic.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r)()(a());n.push([e.id,"time-limit-graphic{display:inline-block;position:relative}time-limit-graphic .unlimited-label{font-weight:700;display:inline-flex;align-items:center;font-size:27px;position:absolute;right:157px;top:240px;transform:translateX(50%)}time-limit-graphic .unlimited-label .text{margin-right:10px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);-webkit-background-clip:text;-webkit-text-fill-color:rgba(0,0,0,0)}",""]);const l=n},"dialogs/resources/elements/time-limit-reached-alternate-graphic":(e,t,o)=>{o.r(t),o.d(t,{TimeLimitReachedAlternateGraphic:()=>r});var i=o(15215),a=o("aurelia-framework");let r=class{constructor(){this.isInFreeMobileExperiment=!1}};(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Boolean)],r.prototype,"isInFreeMobileExperiment",void 0),r=(0,i.Cg)([(0,a.autoinject)()],r)},"dialogs/resources/elements/time-limit-reached-alternate-graphic.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var i=o(14385),a=o.n(i),r=new URL(o(54352),o.b),n=new URL(o(88174),o.b);const l='<template class="${isInFreeMobileExperiment ? \'mobile-free\' : \'\'}"> <require from="./time-limit-reached-alternate-graphic.scss"></require> <template if.bind="!isInFreeMobileExperiment"> <img src="'+a()(r)+'"> <span class="label left">${\'time_limit_reached_alternate_graphic.unlimited\' | i18n}</span> <span class="label right">${\'time_limit_reached_alternate_graphic.save_mods\' | i18n}</span> </template> <template else> <img src="'+a()(n)+'"> <span class="label label-box"> <span class="left">${\'time_limit_reached_alternate_graphic.ad_free\' | i18n}</span> </span> <span class="label middle">${\'time_limit_reached_alternate_graphic.unlimited\' | i18n}</span> <span class="label right">${\'time_limit_reached_alternate_graphic.save_mods\' | i18n}</span> </template> </template> '},"dialogs/resources/elements/time-limit-reached-alternate-graphic.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r)()(a());n.push([e.id,"time-limit-reached-alternate-graphic{display:inline-block;position:relative;width:520px}time-limit-reached-alternate-graphic.mobile-free{width:100%}time-limit-reached-alternate-graphic.mobile-free .label.label-box{display:flex;flex-direction:row;justify-content:center;align-items:center;padding:7px 10px;gap:10px;backdrop-filter:blur(10.4664px);border-radius:6px;transform:rotate(-15deg);background:radial-gradient(circle at 100% 100%, var(--theme--background) 0, var(--theme--background) 7px, transparent 7px) 0% 0%/8px 8px no-repeat,radial-gradient(circle at 0 100%, var(--theme--background) 0, var(--theme--background) 7px, transparent 7px) 100% 0%/8px 8px no-repeat,radial-gradient(circle at 100% 0, var(--theme--background) 0, var(--theme--background) 7px, transparent 7px) 0% 100%/8px 8px no-repeat,radial-gradient(circle at 0 0, var(--theme--background) 0, var(--theme--background) 7px, transparent 7px) 100% 100%/8px 8px no-repeat,linear-gradient(var(--theme--background), var(--theme--background)) 50% 50%/calc(100% - 2px) calc(100% - 16px) no-repeat,linear-gradient(var(--theme--background), var(--theme--background)) 50% 50%/calc(100% - 16px) calc(100% - 2px) no-repeat,linear-gradient(221deg, #0bf2f6 0%, rgba(146, 0, 255, 0.5) 100%);border-radius:8px;box-sizing:border-box;left:135px;bottom:65px}time-limit-reached-alternate-graphic.mobile-free .label.label-box .left{font-weight:600;line-height:120%;background:linear-gradient(252.08deg, #13cfff 16.16%, #c5f3ff 93.53%);-webkit-background-clip:text;-webkit-text-fill-color:rgba(0,0,0,0);background-clip:text;mix-blend-mode:normal}time-limit-reached-alternate-graphic.mobile-free .label.middle{font-weight:600;line-height:34px;text-align:center;background:linear-gradient(225deg, #0bf2f6 0%, rgba(255, 255, 255, 0.5) 100%);-webkit-background-clip:text;-webkit-text-fill-color:rgba(0,0,0,0);background-clip:text;mix-blend-mode:normal;right:325px;bottom:60px}time-limit-reached-alternate-graphic.mobile-free .label.right{font-weight:600;line-height:25px;background:linear-gradient(225deg, #0bf2f6 0%, rgba(0, 0, 0, 0.2) 100%);-webkit-background-clip:text;-webkit-text-fill-color:rgba(0,0,0,0);background-clip:text;mix-blend-mode:normal;right:96px;top:89px}time-limit-reached-alternate-graphic img{width:100%}time-limit-reached-alternate-graphic .label{font-size:14px;line-height:22px;font-weight:700;color:var(--theme--highlight);position:absolute;bottom:45px}time-limit-reached-alternate-graphic .label.left{left:82.5px;transform:translateX(-50%)}time-limit-reached-alternate-graphic .label.right{right:82.5px;transform:translateX(50%)}",""]);const l=n},"dialogs/reward-claim-code-dialog":(e,t,o)=>{o.r(t),o.d(t,{RewardClaimCodeDialog:()=>d,RewardClaimCodeDialogService:()=>p});var i=o(15215),a=o("aurelia-dialog"),r=o("aurelia-framework"),n=o(19072),l=o(92465),s=o(62914),c=o(17275);let d=class{#i;#a;constructor(e,t,o){this.controller=e,this.textCopied=!1,this.#i=t,this.#a=o}activate(e){this.config=e}attached(){this.#i.event("reward_claim_code_dialog_open",{reward:this.config.reward},s.Io)}detached(){this.copiedTextTimeout?.dispose()}async handleRedeemClick(){this.#i.event("reward_claim_code_dialog_copy_click",{reward:this.config.reward},s.Io),await this.config.redeemCallback(this.config.code)}async close(){await this.controller.close(!0)}copyClaimCode(){this.#i.event("reward_claim_code_dialog_copy_click",{reward:this.config.reward},s.Io),this.#a.copyText(this.config.code),this.textCopied=!0,this.copiedTextTimeout=(0,l.Ix)((()=>{this.textCopied=!1}),3e3)}};d=(0,i.Cg)([(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[a.DialogController,s.j0,n.s])],d);let p=class extends c.C{constructor(){super(...arguments),this.viewModelClass="dialogs/reward-claim-code-dialog"}};p=(0,i.Cg)([(0,r.autoinject)()],p)},"dialogs/reward-claim-code-dialog.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./reward-claim-code-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="reward-claim-code-dialog align-center"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <i class="reward-icon"></i> <header>${config.headerCopy}</header> </ux-dialog-header> <ux-dialog-body> <hr> <div class="body-content"> <span>${config.descriptionCopy}</span> <span class="redemption-code">${\'rewards.redemption_code\' | i18n}</span> <div class="redeem-code"> <div class="input-container"> <input class="code" type="text" value.bind="config.code" readonly="readonly"> <button click.delegate="copyClaimCode()" class="copy-button"> <i if.bind="textCopied" class="check-icon"></i> ${textCopied ? \'rewards.copied\' : \'rewards.copy\' | i18n} </button> </div> </div> </div> </ux-dialog-body> <ux-dialog-footer> <div class="buttons"> <button click.delegate="handleRedeemClick()">${\'rewards.redeem\' | i18n}</button> </div> </ux-dialog-footer> </ux-dialog> </template> '},"dialogs/reward-claim-code-dialog.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r),l=o(4417),s=o.n(l),c=new URL(o(83959),o.b),d=n()(a()),p=s()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,.reward-claim-code-dialog ux-dialog-header .reward-icon,.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button .check-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.reward-claim-code-dialog{padding:0px 16px 24px 16px !important;border-radius:16px;background:linear-gradient(0deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0.05) 100%),rgba(15,18,21,.2) !important;box-shadow:0px 10px 20px 0px rgba(0,0,0,.05);backdrop-filter:blur(25px)}.reward-claim-code-dialog ux-dialog-header{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;display:flex;align-items:center;padding-top:6px;height:48px}.reward-claim-code-dialog ux-dialog-header .dialog-header-content{display:flex;align-items:center;gap:12px}.reward-claim-code-dialog ux-dialog-header .dialog-header-content header{margin:0;color:rgba(255,255,255,.8)}.reward-claim-code-dialog ux-dialog-header .reward-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;display:flex;color:rgba(255,255,255,.8);font-size:16px}.reward-claim-code-dialog ux-dialog-header .reward-icon:before{font-family:inherit;content:"celebration"}.reward-claim-code-dialog ux-dialog-body hr{position:absolute;width:100%;left:0;border:none;border-top:1px solid rgba(0,0,0,.2)}.reward-claim-code-dialog ux-dialog-body .body-content{display:flex;flex-direction:column;gap:16px;text-align:left;padding-top:24px}.reward-claim-code-dialog ux-dialog-body .body-content p{margin-top:10px}.reward-claim-code-dialog ux-dialog-body .body-content br{display:block;padding:10px 0;content:""}.reward-claim-code-dialog ux-dialog-body .body-content .input-container{display:flex;width:100%;height:48px;padding:12px;align-items:center;gap:12px;border-radius:8px;background:rgba(255,255,255,.1)}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .code{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;display:flex;width:100%;background:none;outline:0;border:none;padding:0;color:rgba(255,255,255,.8)}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);display:flex;align-items:center;color:#000;background:#fff;margin-left:auto}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button,.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button{border:1px solid #fff}}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button>*:first-child{padding-left:0}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button>*:last-child{padding-right:0}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button svg *{fill:CanvasText}}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button svg{opacity:1}}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button img{height:50%}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button:disabled{opacity:.3}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button:disabled,.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button:not(:disabled):hover svg{opacity:1}}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button:not(:disabled):hover{background:rgba(255,255,255,.3)}}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button .check-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;display:flex;font-size:16px}.reward-claim-code-dialog ux-dialog-body .body-content .input-container .copy-button .check-icon:before{font-family:inherit;content:"check"}`,""]);const g=d},"dialogs/secure-account-dialog":(e,t,o)=>{o.r(t),o.d(t,{SecureAccountDialog:()=>m,SecureAccountDialogService:()=>h});var i=o(15215),a=o("aurelia-dialog"),r=o("aurelia-framework"),n=o(20770),l=o(68663),s=o(62914),c=o(67064),d=o(17275),p=o(54995),g=o(70236),u=o(48881);let m=class{#r;#n;#l;#i;constructor(e,t,o,i,a){this.controller=e,this.saving=!1,this.#r=t,this.#n=o,this.#l=i,this.#i=a}activate(e={}){this.config=e}attached(){this.accountChanged(),this.#i.event("secure_account_dialog_open",{passwordSet:this.passwordSet,emailSet:this.emailSet},s.Io)}accountChanged(){this.passwordSet=(0,g.Lt)(this.account.flags,8),this.emailSet=(0,g.Lt)(this.account.flags,2),this.emailSet&&(this.config.emailOnly||this.passwordSet)&&this.controller.close(!0)}async save(){if(!this.canSave)return;this.saving=!0;let e=null;if(this.#i.event("secure_account_dialog_submit",{passwordSet:this.passwordSet,emailSet:this.emailSet},s.Io),!this.passwordSet&&!this.config.emailOnly)try{e=await this.#r.changeAccountPassword(this.password,null),this.#n.toast({content:"secure_account_dialog.set_password_success_toast",type:"ok"})}catch{this.#n.toast({content:"secure_account_dialog.set_password_problem_toast",type:"alert"})}!this.emailSet&&this.accountEmail&&await this.accountEmail.submit()&&this.#n.toast({content:"secure_account_dialog.set_email_success_toast",type:"ok"}),null!==e&&await this.#l.dispatch(u.Ui,e),this.saving=!1}get passwordOk(){return!!this.password&&this.password.length>=7}get passwordConfirmOk(){return this.passwordOk&&this.password===this.passwordConfirm}get canSave(){return(this.passwordSet||this.config.emailOnly||this.passwordOk&&this.passwordConfirmOk)&&(this.emailSet||["valid","unsure"].includes(this.accountEmailStatus))&&!this.saving}};(0,i.Cg)([(0,r.computedFrom)("password"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],m.prototype,"passwordOk",null),(0,i.Cg)([(0,r.computedFrom)("password","passwordConfirm"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],m.prototype,"passwordConfirmOk",null),(0,i.Cg)([(0,r.computedFrom)("passwordSet","passwordOk","passwordConfirmOk","emailSet","accountEmailStatus","saving"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],m.prototype,"canSave",null),m=(0,i.Cg)([(0,p.m6)({selectors:{account:(0,p.$t)((e=>e.account))}}),(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[a.DialogController,l.x,c.l,n.il,s.j0])],m);let h=class extends d.C{constructor(){super(...arguments),this.viewModelClass="dialogs/secure-account-dialog"}};h=(0,i.Cg)([(0,r.autoinject)()],h)},"dialogs/secure-account-dialog.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var i=o(14385),a=o.n(i),r=new URL(o(16280),o.b),n=a()(r);const l='<template> <require from="./secure-account-dialog.scss"></require> <require from="../resources/elements/account-email"></require> <ux-dialog class="secure-account-dialog"> <ux-dialog-body> <h1>${\'secure_account_dialog.create_your_login_credentials\' | i18n}</h1> <p if.bind="config.emailOnly" class="message"> <span>${\'secure_account_dialog.email_only_message\' | i18n}</span> </p> <p else class="message"> <span if.bind="!emailSet && !passwordSet">${\'secure_account_dialog.no_email_or_password_message\' | i18n}</span> <span if.bind="emailSet && !passwordSet">${\'secure_account_dialog.no_password_message\' | i18n}</span> <span if.bind="!emailSet && passwordSet">${\'secure_account_dialog.no_email_message\' | i18n}</span> </p> <form class="form" submit.delegate="save()"> <div class="form-row" if.bind="!emailSet"> <account-email view-model.ref="accountEmail" status.bind="accountEmailStatus" location="secure_account_dialog"></account-email> <label>${\'secure_account_dialog.email_address\' | i18n}</label> </div> <template if.bind="!passwordSet && !config.emailOnly"> <div class="form-row"> <div class="input-wrapper"> <input class="${passwordOk ? \'with-icon\' : \'\'}" type="password" value.bind="password"> <i if.bind="passwordOk" class="ok"><inline-svg src="'+n+'"></inline-svg></i> </div> <label> ${\'secure_account_dialog.password\' | i18n} </label> </div> <div class="form-row"> <div class="input-wrapper"> <input class="${passwordConfirmOk ? \'with-icon\' : \'\'}" type="password" value.bind="passwordConfirm"> <i if.bind="passwordConfirmOk" class="ok"><inline-svg src="'+n+'"></inline-svg></i> </div> <label> ${\'secure_account_dialog.password_again\' | i18n} </label> </div> <div class="error" if.bind="password"> <div if.bind="!passwordOk">${\'secure_account_dialog.password_requirements_error\' | i18n}</div> <div if.bind="password !== passwordConfirm"> ${\'secure_account_dialog.password_confirm_error\' | i18n} </div> </div> </template> <button disabled.bind="!canSave" click.delegate="save()">${\'secure_account_dialog.save\' | i18n}</button> </form> </ux-dialog-body> </ux-dialog> </template> '},"dialogs/secure-account-dialog.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>g});var i=o(31601),a=o.n(i),r=o(76314),n=o.n(r),l=o(4417),s=o.n(l),c=new URL(o(83959),o.b),d=n()(a()),p=s()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.secure-account-dialog{width:400px;max-width:100%}.secure-account-dialog .form{display:flex;flex-direction:column;display:flex;flex-direction:column;align-items:flex-start;width:100%;max-width:334px}.secure-account-dialog .form>*{margin-top:10px;margin-bottom:10px}.secure-account-dialog .form>*:first-child{margin-top:0}.secure-account-dialog .form>*:last-child{margin-bottom:0}.secure-account-dialog .form .form-row{display:flex;flex-direction:column-reverse;width:100%}.secure-account-dialog .form .form-row label{font-size:12px;line-height:18px;font-weight:500;--input__label--color: rgba(255, 255, 255, 0.4);color:var(--input__label--color);display:block;margin:0 0 5px 9px}.secure-account-dialog .form .form-row input{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%}.secure-account-dialog .form .form-row input::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.secure-account-dialog .form .form-row input::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.secure-account-dialog .form .form-row input:disabled{opacity:.5}.secure-account-dialog .form .form-row input:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.secure-account-dialog .form .form-row input:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.secure-account-dialog .form .form-row input.with-icon{padding-right:30px}.secure-account-dialog .form .form-row .input-wrapper{position:relative;width:100%}.secure-account-dialog .form .form-row .input-wrapper i{--input__icon--color: rgba(255, 255, 255, 0.4);position:absolute;right:0;top:0;height:100%;width:30px;display:inline-flex;align-items:center;justify-content:center;pointer-events:none}.secure-account-dialog .form .form-row .input-wrapper i svg *{fill:var(--input__icon--color)}.secure-account-dialog .form .form-row .input-wrapper i.ok{--input__icon--color: var(--color--accent)}.secure-account-dialog .form button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:800;font-size:21px;line-height:30px;font-weight:800;--cta--padding: 18px;--cta--height: 39px;--cta--hover--border-width: 2px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}.secure-account-dialog .form button,.secure-account-dialog .form button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .secure-account-dialog .form button{border:1px solid #fff}}.secure-account-dialog .form button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.secure-account-dialog .form button>*:first-child{padding-left:0}.secure-account-dialog .form button>*:last-child{padding-right:0}.secure-account-dialog .form button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .secure-account-dialog .form button svg *{fill:CanvasText}}.secure-account-dialog .form button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .secure-account-dialog .form button svg{opacity:1}}.secure-account-dialog .form button img{height:50%}.secure-account-dialog .form button:disabled{opacity:.3}.secure-account-dialog .form button:disabled,.secure-account-dialog .form button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.secure-account-dialog .form button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.secure-account-dialog .form button:not(:disabled):hover svg{opacity:1}}.secure-account-dialog .form button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.secure-account-dialog .form button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.secure-account-dialog .form button:not(:disabled):active{background-color:var(--theme--highlight)}.secure-account-dialog h1{font-size:30px;line-height:36px;color:#fff;margin:0 0 11px}.secure-account-dialog .message{font-size:18px;line-height:30px;font-weight:600;display:block;color:rgba(255,255,255,.5);margin:0 0 27px}.secure-account-dialog .error{font-size:13px;line-height:20px;font-weight:500;color:var(--color--alert);margin-left:14px;margin-top:3px}`,""]);const g=d}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1239],{58534:(e,t,o)=>{o.d(t,{A:()=>a});const a=""},68849:(e,t,o)=>{o.d(t,{H:()=>r});var a=o(15215),i=o("aurelia-framework"),s=o(19072);let r=class{#e;constructor(e){this.#e=e}async getRepoDirectories(){return(await this.#t())?.repos||[]}#t(){return this.#e.getCreatorConfiguration()}};r=(0,a.Cg)([(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[s.s])],r)},80525:(e,t,o)=>{o.d(t,{W:()=>c});var a=o(15215),i=o(16928),s=o("aurelia-framework"),r=o(35392),n=o(19072),l=o(68849);let c=class{#o;#e;constructor(e,t){this.#o=e,this.#e=t}async getTrainerBinaryPath(e){const t=await this.#a(e);if(null===t)return null;const o=i.normalize(`${t}/Build/Trainer.dll`);if(!await r.promises.stat(o).then((e=>e.isFile())).catch((()=>!1)))return null;if(o.startsWith("\\\\")){const e=i.join(this.#e.getTempFolder(),"NetworkTrainer.dll");return await r.promises.writeFile(e,await r.promises.readFile(o)),e}return o}async#a(e){const t=await this.#o.getRepoDirectories();for(const o of t){const t=i.join(o,e);if(await r.promises.stat(t).then((e=>e.isDirectory())).catch((()=>!1)))return t}return null}};c=(0,a.Cg)([(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[l.H,n.s])],c)},"cheats/game":(e,t,o)=>{o.r(t),o.d(t,{Game:()=>$});var a=o(15215),i=o("aurelia-event-aggregator"),s=o("aurelia-framework"),r=o(18776),n=o(20770),l=o(45953),c=o(68865),d=o(86867),g=o(21795),m=o(24008),p=o(54995),f=o(70236),h=o(88849),u=o(48881),b=o(38777),y=o(16928),v=o(35392),_=o("dialogs/selection-dialog"),w=o(19072),x=o("shared/dialogs/basic-dialog"),F=o(20057),C=o(68849);let T=class{#o;constructor(e){this.#o=e}async createProject(e,t,o,a){if(!(await this.#o.getRepoDirectories()).includes(e))throw new Error("The given path is not a registered repo directory.");const i=`${e}\\${t}`;if(await v.promises.stat(i).then((e=>e.isDirectory())).catch((()=>!1)))return i;await this.#i(e,i);const s=`${i}\\Trainer.sln`,r=`${i}\\Trainer.vcxproj`;"x64"!==o&&await Promise.all([this.#s(r,o),this.#s(s,o)]),await this.#r(r,t);try{await v.promises.rename(s,`${i}\\${a}.sln`)}catch{}return i}async#s(e,t){try{const o=await v.promises.readFile(e,"utf8");await v.promises.writeFile(e,o.replaceAll("x64",t))}catch{}}async#r(e,t){try{const o=`$1$(ProjectDir)${"..\\".repeat(t.split("\\").length)}Include;`,a=await v.promises.readFile(e,"utf8");await v.promises.writeFile(e,a.replace(/(<AdditionalIncludeDirectories>)/g,o))}catch{}}async#i(e,t){const o=`${e}\\Template`;await this.#n(t),await Promise.all((await v.promises.readdir(o,{withFileTypes:!0})).filter((e=>e.isFile())).map((e=>v.promises.copyFile(`${o}\\${e.name}`,`${t}\\${e.name}`,v.constants.COPYFILE_EXCL))))}async#n(e){const t=e.split("\\");let o=t.shift()??"";for(const e of t)o+=`\\${e}`,await v.promises.stat(o).then((e=>e.isDirectory())).catch((()=>!1))||await v.promises.mkdir(o)}};T=(0,a.Cg)([(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[C.H])],T);let S=class{#e;#o;#l;#c;#d;constructor(e,t,o,a,i){this.#e=e,this.#o=t,this.#l=o,this.#c=a,this.#d=i}editTrainer(e){window.open(e.blueprintEditUrl,"_blank")}async openLocalProject(e){const t=await this.#o.getRepoDirectories(),o=[];for(const a of t){const t=y.join(a,e.repoPath);await v.promises.stat(t).then((e=>e.isDirectory())).catch((()=>!1))&&o.push(t)}if(0===o.length)return void this.#c.ok(F.F2.literal("Project not found. Make sure it was created."));let a;if(1!==o.length){const e=await this.#d.open({title:F.F2.literal("Select a Project"),options:o.map(F.F2.literal),submitLabel:F.F2.literal("Open")});if(e.wasCancelled)return;a=e.output.selection}else a=o[0];await this.#e.openFilePath(a)}async createLocalProject(e){const t=await this.#o.getRepoDirectories();let o;if(0===t.length)return void this.#c.ok(F.F2.literal("You don’t have any local repositories registered."));if(1===t.length)o=t[0];else{const e=await this.#d.open({title:F.F2.literal("Select a Repository"),options:t.map(F.F2.literal),submitLabel:F.F2.literal("Continue")});if(e.wasCancelled)return;o=e.output.selection}const a=await this.#c.show({message:F.F2.literal("Which architecture should the project target?"),options:[F.F2.literal("x86"),F.F2.literal("x64")],cancelable:!0});if(null===a)return;const i=await this.#l.createProject(o,e.repoPath,"x86"===a?"Win32":"x64",e.fileSafeTitleName);await this.#e.openFilePath(i)}};S=(0,a.Cg)([(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[w.s,C.H,T,x.BasicDialogService,_.SelectionDialogService])],S);var k=o(10110);let $=class{#g;#m;#p;#f;#h;#u;#b;#y;#v;#_;constructor(e,t,o,a,i,s,r,n){this.creatorUtil=a,this.notesCollapsed=!0,this.freeInAppControlsExperimentReady=!1,this.#v=!1,this.#g=e,this.#m=t,this.#p=o,this.#f=i,this.#h=s,this.#u=r,this.#b=n}attached(){this.freeInAppControlsExperimentReady=!0,this.#_=new b.Vd([this.#h.subscribe("load-trainer",(e=>this.loadTrainer(e)))])}detached(){this.#_?.dispose(),this.#_=null}async bind(){this.#w(),await this.loadTrainer(this.trainerId),this.#v=!0,this.notesCollapsed=!!this.selectedTrainerNotesRead}unbind(){this.#v=!1,this.#y?.cancel(),this.#y=null,this.#f.cancelVisibleTrainer()}async creatorModeChanged(e,t){if(this.#v&&void 0!==t){const e=this.selectedTrainer?.id;!1===await this.loadTrainer(void 0,!1)&&(this.creatorMode&&e?await this.loadTrainer(e):this.#x())}}catalogChanged(){this.#w()}#w(){this.game=this.catalog.games[this.gameId]}#x(){this.errorMessage="game.cant_connect_to_wemod"}async loadTrainer(e,t=!0){this.#y?.cancel();const o=new b.HL;this.#y=o,this.selectedTrainer=null;const a=await this.#p.fetch(this.gameId,e);return o.canceled?null:a?(this.errorMessage=null,this.selectedTrainer=a,this.#m.dispatch(u.p1,this.game.titleId),this.#g.navigateToRoute("title",{titleId:this.game.titleId,gameId:this.gameId,trainerId:a.id},{replace:!0,trigger:!1}),this.#f.setVisibleTrainer(a),this.#h.publish(new g.pA(a)),!0):(t&&this.#x(),!1)}get showCreatorTools(){return(this.selectedTrainer&&"trainerlib-local"===this.selectedTrainer.loader)??!1}toggleNotes(){this.notesCollapsed?this.notesCollapsed=!1:this.readNotes()}async readNotes(){await this.#m.dispatch(u.ah,this.selectedTrainer?.id,(0,h.YZ)(this.selectedTrainer?.blueprint.notes??"")),this.notesCollapsed=!0}selectedTrainerChanged(e){e&&(e.blueprint.cheats.some((e=>e.parent))?this.#u.showDialog().then((e=>{e||this.#b.showDialog()})):this.#b.showDialog())}get betaModsEnabled(){return(this.selectedTrainer?.blueprint.cheats.filter((e=>(0,f.Lt)(e.flags,2)))??[]).length>0}installedGameVersionsChanged(e,t){const o=new Set(e[this.gameId]?.map((e=>e.version)).filter((e=>null!==e))),a=new Set(t[this.gameId]?.map((e=>e.version)).filter((e=>null!==e)));o.size===a.size&&[...o].every((e=>a.has(e)))||this.loadTrainer()}get showMapMods(){return(this.maps??[]).some((e=>e.titleId===this.game.titleId&&(0,f.Lt)(e.flags,m.nC.HasGameCoordinates)))}};(0,a.Cg)([s.bindable,(0,a.Sn)("design:type",String)],$.prototype,"gameId",void 0),(0,a.Cg)([s.bindable,(0,a.Sn)("design:type",String)],$.prototype,"trainerId",void 0),(0,a.Cg)([s.bindable,(0,a.Sn)("design:type",Boolean)],$.prototype,"selectedTrainerNotesRead",void 0),(0,a.Cg)([s.bindable,s.observable,(0,a.Sn)("design:type",Object)],$.prototype,"game",void 0),(0,a.Cg)([s.bindable,s.observable,(0,a.Sn)("design:type",Object)],$.prototype,"selectedTrainer",void 0),(0,a.Cg)([s.bindable,(0,a.Sn)("design:type",String)],$.prototype,"trainerPlayButtonState",void 0),(0,a.Cg)([s.bindable,(0,a.Sn)("design:type",Object)],$.prototype,"suggestedMods",void 0),(0,a.Cg)([s.bindable,(0,a.Sn)("design:type",Function)],$.prototype,"suggestMod",void 0),(0,a.Cg)([(0,s.computedFrom)("selectedTrainer"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],$.prototype,"showCreatorTools",null),(0,a.Cg)([(0,s.computedFrom)("selectedTrainer"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],$.prototype,"betaModsEnabled",null),(0,a.Cg)([(0,s.computedFrom)("maps","game.titleId"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],$.prototype,"showMapMods",null),$=(0,a.Cg)([(0,p.m6)({selectors:{account:(0,p.$t)((e=>e.account)),catalog:(0,p.$t)((e=>e.catalog)),trainerNotesRead:(0,p.$t)((e=>e.trainerNotesRead)),creatorMode:(0,p.$t)((e=>e.settings?.creatorMode)),updatePendingCollapsed:(0,p.$t)((e=>e.settings?.updatePendingCollapsed)),installedGameVersions:(0,p.$t)((e=>e.installedGameVersions)),maps:(0,p.$t)((e=>e.catalog.maps))}}),(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[r.Ix,n.il,k.x,S,l.m,i.EventAggregator,d.k,c.V])],$)},"cheats/game-collection":(e,t,o)=>{o.r(t),o.d(t,{GameCollection:()=>F});var a=o(15215),i=o("aurelia-event-aggregator"),s=o("aurelia-framework"),r=o(18776),n=o(20770),l=o(20057),c=o(62914),d=o(43050),g=o(21795),m=o("resources/elements/layout-toggle"),p=o(92465),f=o(54995),h=o(48881),u=o(64415);const b=[{titleLabel:"game_collection.add_games_to_your_favorites",location:"recently_played",feedConfig:d.qA,filter:{maxItems:10},metadataTypes:["platform-icons","status-badges","genres"]},{titleLabel:"game_collection.add_games_to_your_favorites",location:"my_games",feedConfig:d.TQ,filter:{maxItems:10},metadataTypes:["platform-icons","status-badges","genres"]},{titleLabel:"game_collection.add_games_to_your_favorites",location:"free_games",feedConfig:d.Vr,filter:{maxItems:10,tags:["free"]},metadataTypes:["platform-icons","status-badges","genres"]}],y=new Date(0).toISOString(),v={recentlyPlayed:{label:"game_collection.recently_played",key:"recently_played",filter:{sort:(e,t)=>(t.lastPlayedAt??y).localeCompare(e.lastPlayedAt??y)}},alpha:{label:"game_collection.a_z",key:"alpha",filter:{sort:(e,t)=>e.titleName.localeCompare(t.titleName)}},popular:{label:"game_collection.popular",key:"popular",filter:{sort:(e,t)=>e.rank-t.rank}}},_=[v.recentlyPlayed,v.alpha],w=[v.popular,v.recentlyPlayed,v.alpha],x=[{slug:"free-games",titleKey:"game_collection.free_games",analyticsName:"Free Games",location:"free_games",feeds:[{feedConfig:d.Vr,filterOptions:[{filter:{tags:["free"]}}],groups:["all"],metadataTypes:["platform-icons","status-badges","genres"]}]},{slug:"my-games",titleKey:"game_collection.my_games",analyticsName:"My Games",location:"my_games",feeds:[{location:"all",analyticsName:"All",navLabel:"game_collection.all",searchPlaceholder:"game_collection.search_your_game_library",navIcon:"all",feedConfig:d.TQ,groups:["playable","launch_without_mods","favorites_not_installed","free_games_to_install"],metadataTypes:["platform-icons","status-badges","genres"],emptyMessage:{headerKey:"game_collection.you_have_no_installed_games",messageKey:"game_collection.install_a_free_game_to_get_started"},alternateFeeds:[{titleLabel:"game_collection.free_games_to_install",location:"free_games",feedConfig:d.Vr,filter:{maxItems:10,tags:["free"]},metadataTypes:["platform-icons","status-badges","genres"]}],filterOptions:_},{location:"favorites",analyticsName:"Favorites",navLabel:"game_collection.favorites",searchPlaceholder:"game_collection.search_your_favorites",navIcon:"favorite",feedConfig:d.pK,groups:["playable","launch_without_mods","favorites_not_installed"],metadataTypes:["platform-icons","status-badges","genres"],emptyMessage:{headerKey:"game_collection.you_have_no_favorites",messageKey:"game_collection.add_games_to_your_favorites_by_selecting_the_star_icon",className:"favorite-icon"},alternateFeeds:b,filterOptions:_}],enableSearch:!0},{slug:"most-popular",titleKey:"game_collection.most_popular",analyticsName:"Most Popular",location:"most_popular",feeds:[{feedConfig:d.dL,filterOptions:[{filter:{maxItems:100}}],groups:["all"],metadataTypes:["platform-icons","status-badges"]}]},{slug:"recently-played",titleKey:"game_collection.recently_played",analyticsName:"Recently Played",location:"recently_played",feeds:[{feedConfig:d._4,groups:["playable","launch_without_mods"],metadataTypes:["platform-icons","status-badges","genres"]}]},{slug:"game-pass",titleKey:"game_collection.game_pass",analyticsName:"Game Pass",location:"game_pass",feeds:[{feedConfig:d.Vr,filterOptions:[{filter:{tags:["game-pass"]}}],groups:["all"],metadataTypes:["platform-icons","status-badges","genres"]}]},{slug:"community-choice",titleKey:"game_collection.community_choice",analyticsName:"Community Choice",location:"community_choice",feeds:[{feedConfig:d.Vr,filterOptions:[{filter:{tags:["choice"]}}],groups:["all"],metadataTypes:["platform-icons","status-badges","genres"]}]},{slug:"supported-games",titleKey:"game_collection.all_supported_games",analyticsName:"All Supported Games",location:"all_supported_games",feeds:[{feedConfig:d.gu,groups:["all"],metadataTypes:["platform-icons","status-badges","genres"]}]},{slug:"maps",titleKey:"game_collection.maps",analyticsName:"Maps",location:"maps",defaultLayout:m.Layout.Thumbnail,feeds:[{feedConfig:d.iA,navLabel:"game_collection.all",navIcon:"all",groups:["my_games","favorites_not_installed","all"],metadataTypes:[],filterOptions:w,showMaps:!0},{feedConfig:d.no,navLabel:"game_collection.favorites",navIcon:"favorite",groups:["playable","all"],metadataTypes:[],filterOptions:w,showMaps:!0,emptyMessage:{headerKey:"game_collection.you_have_no_favorites",messageKey:"game_collection.add_games_to_your_favorites_by_selecting_the_star_icon",className:"favorite-icon"}},{feedConfig:d.b$,navLabel:"game_collection.teleport",navIcon:"teleport",groups:["my_games","favorites_not_installed","all"],metadataTypes:[],filterOptions:w,showMaps:!0},{feedConfig:d.uP,navLabel:"game_collection.live_location",navIcon:"live-location",groups:["my_games","favorites_not_installed","all"],metadataTypes:[],filterOptions:w,showMaps:!0}],enableSearch:!0},{slug:"new-and-updated",titleKey:"game_collection.new_and_updated_games",analyticsName:"New and Updated Games",location:"new_and_updated_games",feeds:[{feedConfig:d.dZ,filterOptions:[{filter:{maxItems:100}}],groups:["all"],metadataTypes:["platform-icons","status-badges","genres"]}]},{slug:"games-by-genre",titleKey:"",analyticsName:"Games by Genre",location:"games_by_genre",feeds:[{feedConfig:d.Vr,filterOptions:[],groups:["all"],metadataTypes:["status-badges","genres"]}]},{slug:"games-with-maps",titleKey:"game_collection.games_with_maps",analyticsName:"Games with Maps",location:"games_with_maps",enableSearch:!0,feeds:[{feedConfig:d.iA,filterOptions:[v.popular,v.alpha],groups:["all"],metadataTypes:["status-badges","genres"]}]},{slug:"games-with-teleport",titleKey:"game_collection.teleport",analyticsName:"Games with Teleport",location:"games_with_teleport",enableSearch:!0,feeds:[{feedConfig:d.b$,filterOptions:[v.popular,v.alpha],groups:["all"],metadataTypes:["status-badges","genres"]}]},{slug:"games-with-precision-mods",titleKey:"game_collection.precision_mods",analyticsName:"Games with Precision Mods",location:"games_with_precision_mods",enableSearch:!0,feeds:[{feedConfig:d.Jo,filterOptions:[v.popular,v.alpha],groups:["all"],metadataTypes:["status-badges","genres"]}]},{slug:"games-with-overlay-support",titleKey:"game_collection.overlay",analyticsName:"Games with Overlay Support",location:"games_with_overlay_support",enableSearch:!0,feeds:[{feedConfig:d.DH,filterOptions:[v.popular,v.alpha],groups:["all"],metadataTypes:["status-badges","genres"]}]}];let F=class{#F;#C;#T;#m;#S;#k;#$;constructor(e,t,o,a,i,s){this.router=t,this.selectedFilterOption=null,this.#$=[],this.#F=e,this.#C=o,this.#T=a,this.#m=i,this.#S=s}activate(e,t,o){this.#k=e.slug;const a=x.find((e=>e.slug===this.#k));if(a&&(this.config=a,e?.genre)){const t=this.#S.getValue(`genres.${e.genre.slug.replaceAll("-","_")}`);this.config={...this.config,feeds:[{...this.config.feeds[0],filterOptions:[{filter:{genres:[e.genre.id]}}]}],titleString:this.#S.getValue("titles.$genre_games",{genre:t})}}o.queryParams.search||this.#I()}deactivate(){this.feed?.dispose(),this.#$.forEach((e=>e.dispose()));const e=this.scrollEl?.scrollTop??0;this.#m.dispatch(h.T1,this.config.slug,{selectedFeed:this.selectedFeed?.feedConfig.id,scrollPos:e})}attached(){const e=this.gameCollectionPreferences[this.config.slug];e?.scrollPos&&(this.scrollEl.scrollTop=e.scrollPos??0)}bind(){if(this.config){const e=this.gameCollectionPreferences[this.config.slug],t=this.router.currentInstruction.queryParams,o=t?.selectedFeed||e?.selectedFeed;o&&(this.selectedFeed=this.config.feeds.find((e=>e.feedConfig.id===o))),this.selectedFeed||(this.selectedFeed=this.config.feeds[0])}}onSearch(){this.appliedSearchTerms=this.searchTerms,this.#T.publish(new g.Pq(`game_collection_${this.config.slug}`,this.appliedSearchTerms)),this.#I()}onClear(){this.appliedSearchTerms&&(this.appliedSearchTerms="",this.searchTerms="",this.#I())}get title(){return this.config.titleString??this.#S.getValue(this.config.titleKey)}get items(){let e=(0,u.$)(this.feed.items,this.appliedSearchTerms,["titleName","titleTerms"],"titleName");return this.selectedFilterOption?.filter.sort&&(e=e.slice(0).sort(this.selectedFilterOption.filter.sort)),e}selectedFeedChanged(e,t){const o=!!this.appliedSearchTerms||!t,a=this.gameCollectionPreferences[this.config.slug]?.selectedFilterOptions?.[this.selectedFeed?.feedConfig.id??""];this.selectedFilterOption=this.selectedFeed?.filterOptions?.find((e=>e.key===a))??null,this.selectedFilterOption||(this.selectedFilterOption=this.selectedFeed?.filterOptions?.[0]||null),this.gameSearchInput?.clear(!1),this.#P(),o||this.#I()}#P(){this.selectedFeed?(this.feed=this.#C.getFilteredFeed(this.selectedFeed.feedConfig,this.selectedFilterOption?.filter||{}),this.#m.dispatch(h.T1,this.config.slug,{selectedFeed:this.selectedFeed.feedConfig.id,selectedFilterOptions:{[this.selectedFeed.feedConfig.id]:this.selectedFilterOption?.key},scrollPos:0}),this.#$.forEach((e=>e.dispose())),this.#$=this.selectedFeed.alternateFeeds?.map((e=>this.#C.getFilteredFeed(e.feedConfig,e.filter??{})))||[]):this.#$=[]}get alternateFeedConfig(){const e=this.#$.findIndex((e=>e.items.length>0));return e>-1&&this.selectedFeed?.alternateFeeds?this.selectedFeed.alternateFeeds[e]:null}get alternateFeed(){if(!this.alternateFeedConfig)return null;const e=this.selectedFeed?.alternateFeeds?.indexOf(this.alternateFeedConfig)??-1;return e>=0?this.#$[e]:null}get location(){return this.config.location+(this.config.feeds.length>0&&this.selectedFeed?.location?`:${this.selectedFeed?.location}`:"")}async#I(){await(0,p.Wn)();const e={state:this.searchTerms?"Search Results":"All",installed_games:Object.keys(this.installedGameVersions).length>0,layout:this.layout};this.#F.screenView({name:this.selectedFeed?.analyticsName||this.config.analyticsName,class:this.config.analyticsName,params:e})}layoutChanged(e,t){t&&this.#I()}selectFilter(e){this.selectedFilterOption=e,this.#P(),this.#F.event("game_collection_apply_filter",{collection:this.selectedFeed?.analyticsName,filter:this.selectedFilterOption.key},c.Io)}};(0,a.Cg)([s.observable,(0,a.Sn)("design:type",String)],F.prototype,"layout",void 0),(0,a.Cg)([s.observable,(0,a.Sn)("design:type",Object)],F.prototype,"selectedFeed",void 0),(0,a.Cg)([(0,s.computedFrom)("config.titleString","config.titleKey"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],F.prototype,"title",null),(0,a.Cg)([(0,s.computedFrom)("feed.items","appliedSearchTerms","selectedFilterOption"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],F.prototype,"items",null),(0,a.Cg)([(0,s.computedFrom)("alternateFeeds"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],F.prototype,"alternateFeedConfig",null),(0,a.Cg)([(0,s.computedFrom)("alternateFeedConfig"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],F.prototype,"alternateFeed",null),(0,a.Cg)([(0,s.computedFrom)("config"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],F.prototype,"location",null),F=(0,a.Cg)([(0,s.autoinject)(),(0,f.m6)({selectors:{gameCollectionPreferences:(0,f.$t)((e=>e.gameCollectionPreferences)),installedGameVersions:(0,f.$t)((e=>e.installedGameVersions))}}),(0,a.Sn)("design:paramtypes",[c.j0,r.Ix,d.Y2,i.EventAggregator,n.il,l.F2])],F)},"cheats/game-collection.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>a});const a='<template> <require from="./game-collection.scss"></require> <require from="./resources/elements/horizontal-game-feed"></require> <require from="./resources/elements/game-feed-item"></require> <require from="./resources/elements/map-feed-item"></require> <require from="./resources/elements/game-search-input"></require> <require from="./resources/elements/list-game-feed"></require> <require from="./resources/elements/list-map-feed"></require> <require from="./resources/value-converters/group-feed-items"></require> <require from="../resources/elements/layout-toggle"></require> <require from="../resources/elements/toast-anchor"></require> <require from="../resources/elements/lazy-render"></require> <require from="../shared/resources/elements/tabs"></require> <require from="../shared/resources/elements/tab"></require> <section class="game-collection view-background au-animate"> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <toast-anchor class-names="scroll-to-top" scroll-el.bind="scrollEl"></toast-anchor> <div class="view-scrollable" overflow-fade="vertical" ref="scrollEl"> <div class="top"> <header> <span class="name">${title}</span> <layout-toggle layout.bind="layout" default.bind="config.defaultLayout || \'list\'" settings-key="${config.analyticsName.replaceAll(\' \', \'\')}CollectionLayout" source="game_collection" wide.bind="true" if.bind="feed.items.length"></layout-toggle> </header> </div> <section class="search" if.bind="config.enableSearch"> <game-search-input on-clear.call="onClear()" on-search.call="onSearch()" search-terms.bind="searchTerms" placeholder-key.bind="selectedFeed.searchPlaceholder" view-model.ref="gameSearchInput" disabled.bind="!feed.items.length"></game-search-input> </section> <div class="filters"> <tabs if.bind="config.feeds.length > 1"> <tab repeat.for="feed of config.feeds" href="#" active.bind="selectedFeed === feed" click.delegate="selectedFeed = feed"> <span if.bind="feed.navIcon === \'favorite\'" class="icon icon-favorite">kid_star</span> <span if.bind="feed.navIcon === \'all\'" class="icon icon-all">browse</span> <span if.bind="feed.navIcon === \'teleport\'" class="icon icon-teleport tab-extra"> sprint </span> <span if.bind="feed.navIcon === \'live-location\'" class="icon icon-live-location tab-extra"> track_changes </span> <span>${feed.navLabel | i18n}</span> </tab> </tabs> <span class="filter-options" if.bind="selectedFeed.filterOptions.length > 1"> <span class="label">${\'game_collection.sort_by\' | i18n}</span> <tabs if.bind="selectedFeed.filterOptions.length"> <tab repeat.for="filter of selectedFeed.filterOptions" active.bind="selectedFilterOption === filter" href="#" click.delegate="selectFilter(filter)"> <span>${filter.label | i18n}</span> </tab> </tabs> </span> </div> <section class="group" repeat.for="group of items | groupFeedItems:selectedFeed.groups"> <template if.bind="selectedFeed.groups.length > 1"> <toast-anchor name.bind="group.group" scroll-el.bind="scrollEl" label-key="game_collection.${group.group}"></toast-anchor> <div class="title-count-container"> <h2>${`game_collection.${group.group}` | i18n}</h2> <span class="count">${group.items.length}</span> </div> </template> <div class="grid ${selectedFeed.showMaps ? \'maps\' : \'\'}" if.bind="layout === \'thumbnail\'"> <div class="grid-item" repeat.for="item of group.items"> <lazy-render> <template replace-part="content"> <map-feed-item if.bind="selectedFeed.showMaps" item.bind="item" location.bind="location" previous-route.bind="config.location" parent-route="collection/${config.slug}"></map-feed-item> <game-feed-item else item.bind="item" location.bind="location" metadata-types.bind="selectedFeed.metadataTypes" large-thumbnails.bind="true" previous-route.bind="config.location" parent-route="collection/${config.slug}"></game-feed-item> </template> <template replace-part="placeholder"> <div class="game-feed-item-placeholder"></div> </template> </lazy-render> </div> </div> <div class="list" if.bind="layout === \'list\'"> <list-map-feed if.bind="selectedFeed.showMaps" items.bind="group.items" location.bind="location" previous-route.bind="config.location" parent-route="collection/${config.slug}"></list-map-feed> <list-game-feed else items.bind="group.items" location.bind="location" metadata-types.bind="selectedFeed.metadataTypes" previous-route.bind="config.location" parent-route="collection/${config.slug}"></list-game-feed> </div> </section> <div if.bind="feed.items.length && !items.length" class="no-results-message"> <h4>${\'game_collection.no_results\' | i18n}</h4> <p>${\'game_collection.no_results_advice\' | i18n}</p> </div> <section if.bind="!feed.items.length" class="empty"> <div class="empty-message ${selectedFeed.emptyMessage.className ? selectedFeed.emptyMessage.className : \'\'}" if.bind="selectedFeed.emptyMessage"> <h2>${selectedFeed.emptyMessage.headerKey | i18n}</h2> <p innerhtml.bind="selectedFeed.emptyMessage.messageKey | i18n | markdown"></p> </div> <div class="alternate-feed" if.bind="alternateFeed"> <header> <h2>${alternateFeedConfig.titleLabel | i18n}</h2> </header> <article> <horizontal-game-feed items.bind="alternateFeed.items" location="${location}:${alternateFeedConfig.location}" previous-route.bind="config.location" metadata-types.bind="alternateFeedConfig.metadataTypes" metadata-position="on-card"></horizontal-game-feed> </article> </div> </section> </div> </div> </section> </template> '},"cheats/game-collection.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>f});var a=o(31601),i=o.n(a),s=o(76314),r=o.n(s),n=o(4417),l=o.n(n),c=new URL(o(83959),o.b),d=new URL(o(93041),o.b),g=r()(i()),m=l()(c),p=l()(d);g.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,section.game-collection .filters .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.app-content>router-view section.game-collection .view-scrollable{padding-bottom:150px}section.game-collection .view-scrollable{gap:16px}section.game-collection .top{position:relative;display:flex;flex-direction:column;padding-top:8px}section.game-collection .top header{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;padding-top:4px;display:flex;align-items:flex-start}.theme-default section.game-collection .top header{color:#fff}.theme-purple-pro section.game-collection .top header{color:#fff}.theme-green-pro section.game-collection .top header{color:#fff}.theme-orange-pro section.game-collection .top header{color:#fff}.theme-pro section.game-collection .top header{color:#fff}section.game-collection .top header .name{flex:1;min-height:40px}section.game-collection .top header layout-toggle{margin-left:10px}section.game-collection .group{display:flex;flex-direction:column}section.game-collection .group h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;margin:0}.theme-default section.game-collection .group h2{color:#fff}.theme-purple-pro section.game-collection .group h2{color:#fff}.theme-green-pro section.game-collection .group h2{color:#fff}.theme-orange-pro section.game-collection .group h2{color:#fff}.theme-pro section.game-collection .group h2{color:#fff}section.game-collection .group .count{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px}.theme-default section.game-collection .group .count{color:rgba(255,255,255,.6)}.theme-purple-pro section.game-collection .group .count{color:rgba(255,255,255,.6)}.theme-green-pro section.game-collection .group .count{color:rgba(255,255,255,.6)}.theme-orange-pro section.game-collection .group .count{color:rgba(255,255,255,.6)}.theme-pro section.game-collection .group .count{color:rgba(255,255,255,.6)}section.game-collection .group .title-count-container{display:flex;align-items:baseline;gap:8px;margin-bottom:16px}section.game-collection .grid{display:grid;grid-template-columns:repeat(auto-fill, minmax(198px, 1fr));gap:16px}section.game-collection .grid.maps{grid-template-columns:repeat(auto-fill, minmax(366px, 1fr))}section.game-collection .grid .game-feed-item-placeholder:before{width:100%;border-radius:12px;aspect-ratio:200/95;margin-bottom:45px;display:block;content:"";background:rgba(255,255,255,.04)}section.game-collection .empty .empty-message{display:inline-block;border-radius:10px;background:rgba(255,255,255,.04);padding:12px 20px 15px;margin:0 0 30px;max-width:445px;width:100%}section.game-collection .empty .empty-message h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;margin:0}.theme-default section.game-collection .empty .empty-message h2{color:#fff}.theme-purple-pro section.game-collection .empty .empty-message h2{color:#fff}.theme-green-pro section.game-collection .empty .empty-message h2{color:#fff}.theme-orange-pro section.game-collection .empty .empty-message h2{color:#fff}.theme-pro section.game-collection .empty .empty-message h2{color:#fff}section.game-collection .empty .empty-message p{font-size:14px;line-height:21px;color:rgba(255,255,255,.6);margin:0}section.game-collection .empty .empty-message.favorite-icon p em{position:relative;color:rgba(0,0,0,0);display:inline-block;width:15px;height:15px;vertical-align:text-top;background:url(${p}) center/contain no-repeat;opacity:.6}section.game-collection .empty .alternate-feed{display:flex;flex-direction:column;gap:16px}section.game-collection .empty .alternate-feed h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;margin:0}.theme-default section.game-collection .empty .alternate-feed h2{color:#fff}.theme-purple-pro section.game-collection .empty .alternate-feed h2{color:#fff}.theme-green-pro section.game-collection .empty .alternate-feed h2{color:#fff}.theme-orange-pro section.game-collection .empty .alternate-feed h2{color:#fff}.theme-pro section.game-collection .empty .alternate-feed h2{color:#fff}section.game-collection .search+.group,section.game-collection .search+.empty,section.game-collection .search+.no-results-message{margin-top:38px}section.game-collection .filters{display:flex;align-items:center;gap:12px}section.game-collection .filters .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}section.game-collection .filters>*+*:before{content:"";display:inline-block;width:1px;opacity:.5;background:rgba(255,255,255,.15);height:100%;height:32px}section.game-collection .filters .filter-options{display:inline-flex;align-items:center;gap:12px}section.game-collection .filters .label{font-weight:700;font-size:14px;line-height:21px;color:rgba(255,255,255,.5)}section.game-collection .no-results-message h4{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px}.theme-default section.game-collection .no-results-message h4{color:#fff}.theme-purple-pro section.game-collection .no-results-message h4{color:#fff}.theme-green-pro section.game-collection .no-results-message h4{color:#fff}.theme-orange-pro section.game-collection .no-results-message h4{color:#fff}.theme-pro section.game-collection .no-results-message h4{color:#fff}section.game-collection .no-results-message p{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default section.game-collection .no-results-message p{color:rgba(255,255,255,.6)}.theme-purple-pro section.game-collection .no-results-message p{color:rgba(255,255,255,.6)}.theme-green-pro section.game-collection .no-results-message p{color:rgba(255,255,255,.6)}.theme-orange-pro section.game-collection .no-results-message p{color:rgba(255,255,255,.6)}.theme-pro section.game-collection .no-results-message p{color:rgba(255,255,255,.6)}`,""]);const f=g},"cheats/game.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>a});const a='<template> <require from="./game.scss"></require> <require from="./resources/elements/trainer-mods-list"></require> <require from="./resources/elements/trainer-mods-list-placeholder.html"></require> <require from="./resources/elements/trainer-meta"></require> <require from="./resources/elements/trainer-info-callout"></require> <require from="./resources/value-converters/blueprint-translation"></require> <require from="./resources/elements/map-mods-list"></require> <require from="../resources/elements/alert-icon.html"></require> <require from="./mod-suggestions/mod-suggestion-list"></require> <section class="game ${selectedTrainer ? \'trainer-selected\' : \'\'}"> <div class="game-layout"> <div class="error-message-wrapper" if.bind="errorMessage"> <div class="error-message"> <div class="message"> <alert-icon></alert-icon> ${errorMessage | i18n} </div> <button click.delegate="loadTrainer(trainerId)">${\'game.retry\' | i18n}</button> </div> </div> <template else> <template if.bind="!selectedTrainer || !freeInAppControlsExperimentReady"> <trainer-mods-list-placeholder></trainer-mods-list-placeholder> </template> <template else> <div class="info-sections"> <template if.bind="game | gameFlags:\'Retired\'"> <trainer-info-callout callout-type="retired"> <template replace-part="header"> ${\'game.incompatible_and_retired\' | i18n} <p>${\'game.these_mods_have_been_retired\' | i18n}</p> </template> </trainer-info-callout> </template> </div> <div class="creator-tools" if.bind="showCreatorTools"> <section> <button class="creator-action-button" click.delegate="creatorUtil.createLocalProject(selectedTrainer.loaderArgs)"> Create Project </button> <button class="creator-action-button" click.delegate="creatorUtil.openLocalProject(selectedTrainer.loaderArgs)"> Open Project </button> <button class="creator-action-button" click.delegate="loadTrainer(selectedTrainer.id)"> Reload Blueprint </button> <button class="creator-action-button" click.delegate="creatorUtil.editTrainer(selectedTrainer.loaderArgs)"> Edit Trainer </button> </section> </div> <trainer-mods-list trainer.bind="selectedTrainer" trainer-play-button-state.bind="trainerPlayButtonState" beta-mods-enabled.bind="betaModsEnabled"></trainer-mods-list> <map-mods-list if.bind="showMapMods" title-id.bind="game.titleId"></map-mods-list> <mod-suggestion-list if.bind="suggestedMods" suggested-mods.bind="suggestedMods" account.bind="account" game-id.bind="gameId" suggest-mod.call="suggestMod()"></mod-suggestion-list> <div class="game-info"> <trainer-meta game.bind="game"></trainer-meta> </div> </template> </template> </div> </section> </template> '},"cheats/game.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});var a=o(31601),i=o.n(a),s=o(76314),r=o.n(s)()(i());r.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}ux-dialog.basic-dialog.trainer-notes-dialog ux-dialog-body img{max-width:70%}section.game .game-layout{min-height:100%}section.game .game-info,section.game .creator-tools{display:flex;flex-direction:column;gap:12px}section.game .game-info section,section.game .creator-tools section{display:flex;padding:16px 24px;background:rgba(255,255,255,.03);border-radius:16px;gap:12px;align-items:center}section.game .game-info button,section.game .creator-tools button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}section.game .game-info button,section.game .game-info button *,section.game .creator-tools button,section.game .creator-tools button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .game-info button,body:not(.override-contrast-mode) section.game .creator-tools button{border:1px solid #fff}}section.game .game-info button>*,section.game .creator-tools button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}section.game .game-info button>*:first-child,section.game .creator-tools button>*:first-child{padding-left:0}section.game .game-info button>*:last-child,section.game .creator-tools button>*:last-child{padding-right:0}section.game .game-info button svg,section.game .creator-tools button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .game-info button svg *,body:not(.override-contrast-mode) section.game .creator-tools button svg *{fill:CanvasText}}section.game .game-info button svg *,section.game .creator-tools button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .game-info button svg,body:not(.override-contrast-mode) section.game .creator-tools button svg{opacity:1}}section.game .game-info button img,section.game .creator-tools button img{height:50%}section.game .game-info button:disabled,section.game .creator-tools button:disabled{opacity:.3}section.game .game-info button:disabled,section.game .game-info button:disabled *,section.game .creator-tools button:disabled,section.game .creator-tools button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){section.game .game-info button:not(:disabled):hover,section.game .creator-tools button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}section.game .game-info button:not(:disabled):hover svg,section.game .creator-tools button:not(:disabled):hover svg{opacity:1}}section.game .game-info button:not(:disabled):active,section.game .creator-tools button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){section.game .game-info button:not(:disabled):hover,section.game .creator-tools button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}section.game .game-info button:not(:disabled):active,section.game .creator-tools button:not(:disabled):active{background-color:var(--theme--highlight)}section.game .creator-tools section{margin-bottom:12px}section.game .error-message-wrapper{display:flex;flex:1;align-items:center;justify-content:center;text-align:center}section.game .error-message-wrapper .error-message{margin:100px 0}section.game .error-message-wrapper .message{font-size:14px;line-height:21px;font-weight:700;color:#fff;margin-bottom:10px}section.game .error-message-wrapper .message alert-icon{margin-right:10px}section.game .error-message-wrapper button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--color--alert);--cta__icon--color: var(--color--alert)}section.game .error-message-wrapper button,section.game .error-message-wrapper button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .error-message-wrapper button{border:1px solid #fff}}section.game .error-message-wrapper button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}section.game .error-message-wrapper button>*:first-child{padding-left:0}section.game .error-message-wrapper button>*:last-child{padding-right:0}section.game .error-message-wrapper button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .error-message-wrapper button svg *{fill:CanvasText}}section.game .error-message-wrapper button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .error-message-wrapper button svg{opacity:1}}section.game .error-message-wrapper button img{height:50%}section.game .error-message-wrapper button:disabled{opacity:.3}section.game .error-message-wrapper button:disabled,section.game .error-message-wrapper button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){section.game .error-message-wrapper button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}section.game .error-message-wrapper button:not(:disabled):hover svg{opacity:1}}section.game .error-message-wrapper button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){section.game .error-message-wrapper button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--alert);background-color:rgba(0,0,0,0)}}section.game .error-message-wrapper button:not(:disabled):active{background-color:var(--color--alert)}section.game .info-sections{display:none}section.game .info-sections:has(section){display:flex;flex-direction:column;gap:10px;overflow:hidden;margin:0px 0px 20px 0px}",""]);const n=r}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6693],{"overlay/resources/elements/overlay-header":(e,o,t)=>{t.r(o),t.d(o,{OverlayHeader:()=>c});var r=t(15215),a=t("aurelia-event-aggregator"),i=t("aurelia-framework"),n=t(43544),l=t(96555),d=t(6745),g=t("overlay/overlay");let c=class{#e;constructor(e,o,t,r){this.host=o,this.overlay=t,this.overlayHotkeyService=r,this.logoError=!1,this.#e=e}attached(){this.#o()}#o(){"steam"===this.host.trainerInfo?.game.platformId&&(this.steamAppId=this.host.trainerInfo?.game.correlationIds.map(l.o.parse).find((e=>"steam"===e.platform))?.sku)}pin(){this.#e.publish(g.EVENT_TOGGLE_PINNED,{trigger:"close_button",pinned:!0})}};c=(0,r.Cg)([(0,i.autoinject)(),(0,r.Sn)("design:paramtypes",[a.EventAggregator,d.xr,g.Overlay,n.u])],c)},"overlay/resources/elements/overlay-header.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var r=t(14385),a=t.n(r),i=new URL(t(18285),t.b);const n='<template> <require from="./overlay-header.scss"></require> <require from="../../../shared/cheats/resources/value-converters/proper-hotkey"></require> <div class="logo"> <img src="'+a()(i)+'"> <span class="copy">wemod</span> </div> <div class="title-logo ${steamAppId ? \'has-logo\' : \'\'} ${logoError ? \'logo-error\' : \'\'}"> <img if.bind="steamAppId" src="https://steamcdn-a.akamaihd.net/steam/apps/${steamAppId}/logo.png" error.trigger="logoError = true"> <div class="title-name">${host.trainerInfo.title.name}</div> </div> <div class="close-button-wrapper"> <button class="close-button" click.delegate="pin()"> <span class="labels"> <b>${\'overlay_header.back_to_game\' | i18n}</b> <span class="hotkey">${overlayHotkeyService.displayHotkey}</span> </span> <span class="x"></span> </button> </div> </template> '},"overlay/resources/elements/overlay-header.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>b});var r=t(31601),a=t.n(r),i=t(76314),n=t.n(i),l=t(4417),d=t.n(l),g=new URL(t(83959),t.b),c=n()(a()),h=d()(g);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${h}) format("woff2")}.material-symbols-outlined,overlay-header .close-button .x{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}overlay-header{display:flex;align-items:center;gap:24px}overlay-header .logo{flex:1;display:inline-flex;align-items:center;opacity:.5;pointer-events:none}overlay-header .logo img{height:16px;margin-right:8px}overlay-header .logo .copy{font-style:normal;font-weight:600;font-size:18.6667px;line-height:32px;color:#fff}overlay-header .close-button-wrapper{flex:1;display:flex;justify-content:flex-end}overlay-header .close-button{flex:1;display:flex;align-items:center;background:rgba(0,0,0,0);border:0;outline:none;padding:0;z-index:1;justify-self:flex-end;max-width:260px}overlay-header .close-button .x{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;display:inline-flex;align-items:center;justify-content:center;width:48px;height:48px;border-radius:50%;background:rgba(255,255,255,.15);transition:background-color .15s;color:#fff}overlay-header .close-button .x:before{font-family:inherit;content:"close"}overlay-header .close-button .labels{flex:1 1 auto;display:flex;flex-direction:column;align-items:flex-end;justify-content:center;margin-right:12px}overlay-header .close-button .labels b{font-weight:800;font-size:16px;line-height:18px;letter-spacing:-0.67px;margin-bottom:2px;color:#fff;white-space:nowrap}overlay-header .close-button .labels span{font-size:14px;line-height:20px;color:rgba(255,255,255,.8)}overlay-header .close-button:hover .x{background:rgba(255,255,255,.25)}overlay-header .title-logo{flex:0 1 auto;display:flex;align-items:center;justify-content:center;pointer-events:none;height:48px}overlay-header .title-logo img{width:100%;max-width:280px;height:100px;object-fit:contain}overlay-header .title-logo .title-name{font-weight:800;font-size:24px;line-height:100%;letter-spacing:-1px;color:#fff;-webkit-line-clamp:2;-webkit-box-orient:vertical;text-align:center;overflow:hidden;text-overflow:ellipsis;display:none}overlay-header .title-logo:not(.has-logo) img,overlay-header .title-logo.logo-error img{display:none}overlay-header .title-logo:not(.has-logo) .title-name,overlay-header .title-logo.logo-error .title-name{display:-webkit-box}overlay-header .title-logo.logo-error img{display:none}`,""]);const b=c},"overlay/resources/elements/overlay-instant-highlight-announcement-notification":(e,o,t)=>{t.r(o),t.d(o,{OverlayInstantHighlightAnnouncementNotification:()=>d});var r=t(15215),a=t("aurelia-framework"),i=t(40127),n=t("shared/cheats/resources/value-converters/proper-hotkey"),l=t(54995);let d=class{#t;constructor(e){this.defaultCaptureBufferSeconds=i.ye,this.#t=e}bind(){this.hotkey=(0,i.Dd)(this.#t)}};d=(0,r.Cg)([(0,l.m6)({selectors:{captureBufferSeconds:(0,l.$t)((e=>e.settings.captureBufferSeconds))}}),(0,a.autoinject)(),(0,r.Sn)("design:paramtypes",[n.ProperHotkeyValueConverter])],d)},"overlay/resources/elements/overlay-instant-highlight-announcement-notification.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>r});const r='<template> <require from="./overlay-notification"></require> <require from="./overlay-instant-highlight-announcement-notification.scss"></require> <overlay-notification size="m" icon="videocam"> <span slot="labels"> <h1 innerhtml.bind="\'overlay_instant_highlights_announcement_notification.new_instant_hightlights\' | i18n | markdown"></h1> <h2 innerhtml.bind="\'overlay_instant_highlights_announcement_notification.press_$hotkey_to_clip_the_last_$secondss\' | i18n: {\n                hotkey: hotkey, seconds: captureBufferSeconds || defaultCaptureBufferSeconds} | markdown"></h2> </span> </overlay-notification> </template> '},"overlay/resources/elements/overlay-instant-highlight-announcement-notification.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>b});var r=t(31601),a=t.n(r),i=t(76314),n=t.n(i),l=t(4417),d=t.n(l),g=new URL(t(83959),t.b),c=n()(a()),h=d()(g);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${h}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}overlay-instant-highlight-announcement-notification .overlay-notification{--overlay-notification-highlight-color: #ee343f;--overlay-notification-icon-color: #fff}overlay-instant-highlight-announcement-notification .overlay-notification .icon{font-variation-settings:"FILL" 1,"wght" 400 !important}`,""]);const b=c},"overlay/resources/elements/overlay-launch-notification":(e,o,t)=>{t.r(o),t.d(o,{OverlayLaunchNotification:()=>n});var r=t(15215),a=t("aurelia-framework"),i=t(43544);let n=class{constructor(e){this.overlayHotkeyService=e}};n=(0,r.Cg)([(0,a.autoinject)(),(0,r.Sn)("design:paramtypes",[i.u])],n)},"overlay/resources/elements/overlay-launch-notification.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var r=t(14385),a=t.n(r),i=new URL(t(438),t.b);const n='<template> <require from="./overlay-notification"></require> <overlay-notification size="xl"> <span slot="icon"> <inline-svg src="'+a()(i)+'"></inline-svg> </span> <span slot="labels"> <h1> <b innerhtml.bind="\'overlay_launch_notification.press_$hotkey\' | i18n: { hotkey: overlayHotkeyService.displayHotkey } | markdown"></b> </h1> <h2>${\'overlay_launch_notification.to_toggle_overlay\' | i18n}</h2> </span> </overlay-notification> </template> '},"overlay/resources/elements/overlay-menu":(e,o,t)=>{t.r(o),t.d(o,{OverlayMenu:()=>b});var r=t(15215),a=t("aurelia-event-aggregator"),i=t("aurelia-framework"),n=t(40127),l=t("shared/cheats/resources/value-converters/proper-hotkey"),d=t(54995),g=t(6745),c=t("overlay/overlay"),h=t(66811);let b=class{#e;#t;constructor(e,o,t,r){this.windowManager=e,this.host=o,this.defaultCaptureBufferSeconds=n.ye,this.overlaySettingsOpen=!1,this.#t=t,this.#e=r}bind(){this.hotkey=(0,n.Dd)(this.#t)}saveCaptureHighlight(){this.#e.publish(c.EVENT_TOGGLE_PINNED,{trigger:"overlay_capture_highlight_button",pinned:!0}),this.host.saveCaptureHighlight()}};b=(0,r.Cg)([(0,d.m6)({selectors:{captureBufferSeconds:(0,d.$t)((e=>e.settings.captureBufferSeconds)),enableCapture:(0,d.$t)((e=>e.settings.enableCapture))}}),(0,i.autoinject)(),(0,r.Sn)("design:paramtypes",[h.o,g.xr,l.ProperHotkeyValueConverter,a.EventAggregator])],b)},"overlay/resources/elements/overlay-menu.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var r=t(14385),a=t.n(r),i=new URL(t(438),t.b);const n='<template> <require from="./overlay-menu.scss"></require> <require from="../../../resources/custom-attributes/detach-el"></require> <require from="../../../resources/custom-attributes/close-if-press-escape"></require> <require from="./overlay-settings-menu"></require> <nav> <button repeat.for="config of windowManager.windows" click.delegate="windowManager.toggle(config, \'window_menu_button\')" if.bind="config.enabled" class="${windowManager.gameWindowSettings[config.id].open ? \'open\' : \'\'}"> <i class="icon" if.bind="config.icon">${config.icon}</i> <i class="icon" else><inline-svg src="'+a()(i)+'"></inline-svg></i> <span class="label">${config.titleKey | i18n}</span> </button> <button class="alert" click.delegate="saveCaptureHighlight()" if.bind="enableCapture"> <i class="icon">content_cut</i> <span class="label"> <span>${\'overlay_window_menu.clip_last_$seconds_s\' | i18n: { seconds: captureBufferSeconds || defaultCaptureBufferSeconds }}</span> <span class="sub-label" innerhtml.bind="\'overlay_window_menu.or_press_$hotkey\' | i18n: { hotkey: hotkey } | markdown"></span> </span> </button> </nav> <div class="settings-button-container"> <button class="settings-button" click.delegate="overlaySettingsOpen = true"> <i class="icon">settings</i> <span class="label">${\'overlay_window_menu.settings\' | i18n}</span> </button> <overlay-settings-menu detach-el.bind="document.body" if.bind="overlaySettingsOpen" open.bind="overlaySettingsOpen" close-if-press-escape="open.bind: overlaySettingsOpen"></overlay-settings-menu> </div> </template> '},"overlay/resources/elements/overlay-menu.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>he});var r=t(31601),a=t.n(r),i=t(76314),n=t.n(i),l=t(98896),d=t(4417),g=t.n(d),c=new URL(t(83959),t.b),h=new URL(t(44013),t.b),b=new URL(t(3427),t.b),p=new URL(t(45993),t.b),u=new URL(t(68955),t.b),s=new URL(t(61543),t.b),m=new URL(t(30550),t.b),f=new URL(t(47281),t.b),x=new URL(t(44580),t.b),y=new URL(t(81556),t.b),v=new URL(t(51234),t.b),w=new URL(t(21379),t.b),k=new URL(t(50437),t.b),_=new URL(t(74188),t.b),$=new URL(t(30410),t.b),z=new URL(t(85311),t.b),L=new URL(t(65244),t.b),S=new URL(t(24809),t.b),R=new URL(t(2012),t.b),U=new URL(t(64943),t.b),I=new URL(t(17579),t.b),C=new URL(t(82051),t.b),H=new URL(t(89392),t.b),j=new URL(t(40730),t.b),O=new URL(t(80240),t.b),q=new URL(t(17820),t.b),X=new URL(t(88442),t.b),M=n()(a());M.i(l.A);var E=g()(c),A=g()(h),B=g()(b),N=g()(p),T=g()(u),D=g()(s),F=g()(m),G=g()(f),P=g()(x),V=g()(y),Y=g()(v),W=g()(w),K=g()(k),J=g()(_),Q=g()($),Z=g()(z),ee=g()(L),oe=g()(S),te=g()(R),re=g()(U),ae=g()(I),ie=g()(C),ne=g()(H),le=g()(j),de=g()(O),ge=g()(q),ce=g()(X);M.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${E}) format("woff2")}.material-symbols-outlined,overlay-menu>*>button .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}@keyframes cta--boost--hover{0%{background-color:rgba(0,0,0,0)}100%{background-color:rgba(var(--theme--highlight--rgb), 0.5)}}@keyframes cta--pulse{from{margin:0}to{margin:-5px;width:calc(100% + 10px);height:calc(100% + 10px)}}@keyframes cta--inner-pulse{from{opacity:0}to{opacity:.5}}@keyframes favorite-bump{0%{transform:scale(1)}50%{transform:scale(1.4)}100%{transform:scale(1)}}body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}#fullscreen-dialogs ux-dialog-container>div{margin:0 !important}#fullscreen-dialogs ux-dialog-overlay{background:var(--theme--secondary-background);opacity:1}#dialogs ux-dialog{animation:dialog-pop .2s ease-in-out}#dialogs ux-dialog-container.active:not(:last-of-type){opacity:.0001}ux-dialog-overlay{background:rgba(var(--theme--background--rgb), 0.75);transition:opacity .2s !important}ux-dialog-overlay:nth-of-type(1n + 2){display:none}ux-dialog-container{display:flex;overflow:hidden auto;transition-duration:.2s !important}ux-dialog-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}ux-dialog-container::-webkit-scrollbar-thumb:window-inactive,ux-dialog-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}ux-dialog-container::-webkit-scrollbar-thumb:window-inactive:hover,ux-dialog-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}ux-dialog-container>div{padding:0;display:flex;flex:1 1 auto;justify-content:center;align-items:center}@media(max-width: 500px){ux-dialog-container>div{margin:15px !important}}@media(min-width: 501px){ux-dialog-container>div{margin:50px !important}}ux-dialog{box-shadow:0 0 5px rgba(var(--theme--background--rgb), 0.5);position:relative;border-radius:10px;border:1px solid rgba(255,255,255,.05);padding:11px 15px;text-align:left;display:inline-flex;align-items:center;padding:18px 26px 26px 26px;width:550px;display:block;border-radius:20px;max-width:calc(100vw - 126px)}.theme-default ux-dialog{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro ux-dialog{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro ux-dialog{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro ux-dialog{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro ux-dialog{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}ux-dialog>*+*{margin-left:9px}ux-dialog,ux-dialog a{font-size:14px;line-height:21px;line-height:19px;font-weight:500;color:rgba(255,255,255,.5)}ux-dialog strong,ux-dialog em{font-weight:700;color:#fff;font-style:normal}ux-dialog p{margin:0;padding:0}ux-dialog p+p{margin-top:10px}ux-dialog>*+*{margin-left:0}ux-dialog.dialog-loaded{animation:dialog-pop2 .2s ease-in-out}ux-dialog.align-center ux-dialog-header,ux-dialog.align-center ux-dialog-body,ux-dialog.align-center ux-dialog-footer{text-align:center}ux-dialog.align-left ux-dialog-header,ux-dialog.align-left ux-dialog-body,ux-dialog.align-left ux-dialog-footer{text-align:left}ux-dialog.scrollable{max-height:calc(100vh - 100px);padding:0}ux-dialog.scrollable>*+*{margin:0}ux-dialog.scrollable .dialog-scroll-wrapper{width:100%;max-height:inherit;overflow-x:hidden;overflow-y:overlay}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-button:single-button:vertical:decrement{height:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-button:single-button:vertical:increment{height:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar{width:25px;border-top-left-radius:0;border-top-right-radius:20px;border-bottom-right-radius:20px;border-bottom-left-radius:0;background:rgba(0,0,0,0)}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb{border:10px solid rgba(0,0,0,0)}@media(max-width: 500px){ux-dialog.scrollable{max-height:calc(100vh - 30px)}}ux-dialog.secondary-gradient-bg{border:0;background:linear-gradient(180deg, var(--theme--secondary-background) 0%, #000 100%) !important}.theme-pro ux-dialog.secondary-gradient-bg{background:linear-gradient(180deg, #2b2b2b 0%, #000 100%) !important}ux-dialog header{font-weight:800;color:#fff;margin-bottom:20px}ux-dialog ux-dialog-header{border:0;padding:0}ux-dialog ux-dialog-header h1{font-weight:800;font-size:21px;line-height:30px;font-weight:700;color:#fff;margin:0}ux-dialog ux-dialog-header h1 sub{color:rgba(255,255,255,.5);vertical-align:unset}ux-dialog ux-dialog-header .dialog-close{display:none}ux-dialog ux-dialog-body{font-weight:700;font-size:15px;line-height:24px;font-weight:500;color:rgba(255,255,255,.6);padding:0}ux-dialog ux-dialog-body em{font-style:normal;font-weight:bold;color:rgba(255,255,255,.8)}ux-dialog ux-dialog-body p{margin:0}ux-dialog ux-dialog-body p+p{margin-top:12px}ux-dialog ux-dialog-footer{border:0;padding:20px 0 0 0;white-space:nowrap}ux-dialog ux-dialog-footer button{margin:0 15px 0 0}ux-dialog ux-dialog-footer button:last-child{margin-right:0}ux-dialog ux-dialog-footer button.secondary{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}ux-dialog ux-dialog-footer button.secondary,ux-dialog ux-dialog-footer button.secondary *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary{border:1px solid #fff}}ux-dialog ux-dialog-footer button.secondary>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}ux-dialog ux-dialog-footer button.secondary>*:first-child{padding-left:0}ux-dialog ux-dialog-footer button.secondary>*:last-child{padding-right:0}ux-dialog ux-dialog-footer button.secondary svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary svg *{fill:CanvasText}}ux-dialog ux-dialog-footer button.secondary svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary svg{opacity:1}}ux-dialog ux-dialog-footer button.secondary img{height:50%}ux-dialog ux-dialog-footer button.secondary:disabled{opacity:.3}ux-dialog ux-dialog-footer button.secondary:disabled,ux-dialog ux-dialog-footer button.secondary:disabled *{cursor:default;pointer-events:none}@media(hover: hover){ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover svg{opacity:1}}ux-dialog ux-dialog-footer button.secondary:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}ux-dialog ux-dialog-footer button.cancel{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:rgba(255,255,255,.4);text-decoration:none}ux-dialog ux-dialog-footer button.cancel svg *{fill:rgba(255,255,255,.4)}@media(hover: hover){ux-dialog ux-dialog-footer button.cancel:hover{color:#fff}ux-dialog ux-dialog-footer button.cancel:hover svg *{fill:#fff}}ux-dialog ux-dialog-footer button.cancel:after{content:"";display:inline-block;width:5px;height:9px;-webkit-mask-box-image:url(${A});margin-left:5px;background:rgba(255,255,255,.4);transition:transform .15s}@media(hover: hover){ux-dialog ux-dialog-footer button.cancel:hover{color:#fff}ux-dialog ux-dialog-footer button.cancel:hover:after{transform:translateX(3px);background:#fff}}ux-dialog ux-dialog-footer button.primary,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel){--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}ux-dialog ux-dialog-footer button.primary,ux-dialog ux-dialog-footer button.primary *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel),ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel){border:1px solid #fff}}ux-dialog ux-dialog-footer button.primary>*,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}ux-dialog ux-dialog-footer button.primary>*:first-child,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*:first-child{padding-left:0}ux-dialog ux-dialog-footer button.primary>*:last-child,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*:last-child{padding-right:0}ux-dialog ux-dialog-footer button.primary svg,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary svg *,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg *{fill:CanvasText}}ux-dialog ux-dialog-footer button.primary svg *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary svg,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg{opacity:1}}ux-dialog ux-dialog-footer button.primary img,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) img{height:50%}ux-dialog ux-dialog-footer button.primary:disabled,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled{opacity:.3}ux-dialog ux-dialog-footer button.primary:disabled,ux-dialog ux-dialog-footer button.primary:disabled *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled *{cursor:default;pointer-events:none}@media(hover: hover){ux-dialog ux-dialog-footer button.primary:not(:disabled):hover,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}ux-dialog ux-dialog-footer button.primary:not(:disabled):hover svg,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover svg{opacity:1}}ux-dialog ux-dialog-footer button.primary:not(:disabled):active,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){ux-dialog ux-dialog-footer button.primary:not(:disabled):hover,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}ux-dialog ux-dialog-footer button.primary:not(:disabled):active,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):active{background-color:var(--theme--highlight)}ux-dialog close-button{position:absolute;right:-13px;top:-13px}ux-dialog.fullscreen-dialog{background:var(--theme--secondary-background);max-width:initial}ux-dialog.fullscreen-dialog close-button{position:fixed;right:20px;top:40px}@media(max-width: 500px){ux-dialog{max-width:calc(100vw - 30px)}ux-dialog close-button{right:-10px;top:-10px}}.dialog-loading-indicator{padding:60px 150px}.dialog-loading-indicator svg{width:40px;height:40px}@keyframes dialog-fade{0%{opacity:0}100%{opacity:1}}@keyframes dialog-pop{0%{opacity:0;transform:scale(0.8)}25%{opacity:1}50%{transform:scale(1.05)}100%{opacity:1;transform:scale(1)}}@keyframes dialog-pop2{0%{opacity:0;transform:scale(0)}25%{opacity:1}50%{transform:scale(1.05)}100%{transform:scale(1)}}.fullscreen-dialog{width:100%;height:100%;border:0;padding:0;background:rgba(0,0,0,0);max-height:100%;border-radius:0}@keyframes dropdown-menu-transition-right{from{opacity:0;transform:translateX(-10px)}to{opacity:1;transform:translateX(0)}}@keyframes dropdown-menu-transition-left{from{opacity:0;transform:translateX(10px)}to{opacity:1;transform:translateX(0)}}@keyframes dropdown-menu-transition-down{from{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.wemod-icon,i{display:inline-block}.wemod-icon:after,i:after{content:"";display:block;clear:both}.wemod-icon svg,i svg{float:left}.wemod-icon--xs svg{max-width:8px;max-height:8px}.wemod-icon--xs svg *{color:#fff}.wemod-icon--s svg{max-width:15px;max-height:15px}.wemod-icon--s svg *{color:#fff}.wemod-icon--m svg{max-width:20px;max-height:20px}.wemod-icon--m svg *{color:#fff}.wemod-icon--l svg{max-width:75px;max-height:75px}.wemod-icon--l svg *{color:#fff}.wemod-payment-method--american-express,.wemod-payment-method--amex{background-image:url(${B}) !important;background-color:#fff}.wemod-payment-method--diners{background-image:url(${N}) !important;background-color:#fff}.wemod-payment-method--discover{background-image:url(${T}) !important;background-color:#fff}.wemod-payment-method--jcb{background-image:url(${D}) !important;background-color:#fff}.wemod-payment-method--mastercard{background-image:url(${F}) !important;background-color:#fff}.wemod-payment-method--visa{background-image:url(${G}) !important;background-color:#fff}.wemod-payment-method--paypal{background-image:url(${P}) !important;background-color:#fff}.wemod-payment-method--amazon-pay{background-image:url(${V}) !important;background-color:#000}.wemod-payment-method--google-pay{background-image:url(${Y}) !important;background-color:#fff;width:48px !important;background-size:auto 12px;background-color:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.1)}.wemod-payment-method--apple-pay{background-image:url(${W}) !important;background-color:#fff;width:48px !important;background-size:auto 12px;background-color:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.1)}.wemod-payment-method--alipay{background-image:url(${K}) !important;background-color:#fff}.wemod-payment-method--direct-debit{background-image:url(${J}) !important;background-color:#fff}.wemod-payment-method--kr-market{background-image:url(${Q}) !important;background-color:#fff}.wemod-payment-method--kakao-pay{background-image:url(${Z}) !important;background-color:#000}.wemod-payment-method--kr-card{background-image:url(${ee}) !important;background-color:#fff}.wemod-payment-method--naver-pay{background-image:url(${oe}) !important;background-color:#fff}@keyframes placeholder__el--initial-fade{0%{opacity:0}100%{opacity:1}}@keyframes placeholder__el--fade{0%{background:rgba(255,255,255,.02)}100%{background:rgba(255,255,255,.15)}}.wemod-tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal}.wemod-tag--pro{background-color:var(--color--pro)}.wemod-tag--new{background-color:#ddf00c;color:#3b5209}.wemod-tag--updated{background-color:rgba(255,255,255,.1);color:var(--theme--text-primary)}.wemod-tag--alert{background-color:var(--color--alert)}.wemod-tag--warn{background-color:rgba(var(--color--accent-yellow--rgb), 0.2);color:var(--color--accent-yellow)}.theme-default{--theme--background:#0d0f12;--theme--background--rgb:13,15,18;--theme--secondary-background:#18293a;--theme--secondary-background--rgb:24,41,58;--theme--background-accent:#131417;--theme--background-accent--rgb:19,20,23;--theme--highlight:#00c7f2;--theme--highlight--rgb:0,199,242;--theme--highlight-darker:#009fc2;--theme--highlight-darker--rgb:0,159,194;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0d0f12;--theme--text-inverse--rgb:13,15,18}.theme-purple-pro{--theme--background:#0e0e15;--theme--background--rgb:14,14,21;--theme--secondary-background:#191f48;--theme--secondary-background--rgb:25,31,72;--theme--background-accent:#131319;--theme--background-accent--rgb:19,19,25;--theme--highlight:#a341ff;--theme--highlight--rgb:163,65,255;--theme--highlight-darker:#8234cc;--theme--highlight-darker--rgb:130,52,204;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0e0e15;--theme--text-inverse--rgb:14,14,21}.theme-green-pro{--theme--background:#0b1114;--theme--background--rgb:11,17,20;--theme--secondary-background:#0f2b27;--theme--secondary-background--rgb:15,43,39;--theme--background-accent:#10171a;--theme--background-accent--rgb:16,23,26;--theme--highlight:#00e7c6;--theme--highlight--rgb:0,231,198;--theme--highlight-darker:#00b99e;--theme--highlight-darker--rgb:0,185,158;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0b1114;--theme--text-inverse--rgb:11,17,20}.theme-orange-pro{--theme--background:#0f0f12;--theme--background--rgb:15,15,18;--theme--secondary-background:#212c38;--theme--secondary-background--rgb:33,44,56;--theme--background-accent:#15151a;--theme--background-accent--rgb:21,21,26;--theme--highlight:#ff6368;--theme--highlight--rgb:255,99,104;--theme--highlight-darker:#cc4f53;--theme--highlight-darker--rgb:204,79,83;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0f0f12;--theme--text-inverse--rgb:15,15,18}.theme-pro{--theme--background:#0a0a0a;--theme--background--rgb:10,10,10;--theme--secondary-background:#000000;--theme--secondary-background--rgb:0,0,0;--theme--background-accent:#161617;--theme--background-accent--rgb:22,22,23;--theme--highlight:#1293ff;--theme--highlight--rgb:18,147,255;--theme--highlight-darker:#0e76cc;--theme--highlight-darker--rgb:14,118,204;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0a0a0a;--theme--text-inverse--rgb:10,10,10}:root,:before,:after{--color--accent:#acff35;--color--accent--rgb:172,255,53;--color--accent-yellow:#ffaa2b;--color--accent-yellow--rgb:255,170,43;--color--alert:#ff0056;--color--alert--rgb:255,0,86;--color--secondary-purple:#834bff;--color--secondary-purple--rgb:131,75,255;--color--pro:#9200ff;--color--pro--rgb:146,0,255;--color--brand-blue:#13cfff;--color--brand-blue--rgb:19,207,255;--color--brand-green:#0bf2f6;--color--brand-green--rgb:11,242,246;--color--warning:#ffed48;--color--warning--rgb:255,237,72;--theme--default--background:#0d0f12;--theme--default--background--rgb:13,15,18;--theme--default--secondary-background:#18293a;--theme--default--secondary-background--rgb:24,41,58;--theme--default--background-accent:#131417;--theme--default--background-accent--rgb:19,20,23;--theme--default--highlight:#00c7f2;--theme--default--highlight--rgb:0,199,242;--theme--default--highlight-darker:#009fc2;--theme--default--highlight-darker--rgb:0,159,194;--theme--default--text-highlight:white;--theme--default--text-highlight--rgb:255,255,255;--theme--default--text-primary:rgba(255, 255, 255, 0.8);--theme--default--text-primary--rgb:255,255,255;--theme--default--text-secondary:rgba(255, 255, 255, 0.6);--theme--default--text-secondary--rgb:255,255,255;--theme--default--text-disabled:rgba(255, 255, 255, 0.4);--theme--default--text-disabled--rgb:255,255,255;--theme--default--text-placeholder:rgba(255, 255, 255, 0.3);--theme--default--text-placeholder--rgb:255,255,255;--theme--default--text-inverse:#0d0f12;--theme--default--text-inverse--rgb:13,15,18;--theme--purple-pro--background:#0e0e15;--theme--purple-pro--background--rgb:14,14,21;--theme--purple-pro--secondary-background:#191f48;--theme--purple-pro--secondary-background--rgb:25,31,72;--theme--purple-pro--background-accent:#131319;--theme--purple-pro--background-accent--rgb:19,19,25;--theme--purple-pro--highlight:#a341ff;--theme--purple-pro--highlight--rgb:163,65,255;--theme--purple-pro--highlight-darker:#8234cc;--theme--purple-pro--highlight-darker--rgb:130,52,204;--theme--purple-pro--text-highlight:white;--theme--purple-pro--text-highlight--rgb:255,255,255;--theme--purple-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--purple-pro--text-primary--rgb:255,255,255;--theme--purple-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--purple-pro--text-secondary--rgb:255,255,255;--theme--purple-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--purple-pro--text-disabled--rgb:255,255,255;--theme--purple-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--purple-pro--text-placeholder--rgb:255,255,255;--theme--purple-pro--text-inverse:#0e0e15;--theme--purple-pro--text-inverse--rgb:14,14,21;--theme--green-pro--background:#0b1114;--theme--green-pro--background--rgb:11,17,20;--theme--green-pro--secondary-background:#0f2b27;--theme--green-pro--secondary-background--rgb:15,43,39;--theme--green-pro--background-accent:#10171a;--theme--green-pro--background-accent--rgb:16,23,26;--theme--green-pro--highlight:#00e7c6;--theme--green-pro--highlight--rgb:0,231,198;--theme--green-pro--highlight-darker:#00b99e;--theme--green-pro--highlight-darker--rgb:0,185,158;--theme--green-pro--text-highlight:white;--theme--green-pro--text-highlight--rgb:255,255,255;--theme--green-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--green-pro--text-primary--rgb:255,255,255;--theme--green-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--green-pro--text-secondary--rgb:255,255,255;--theme--green-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--green-pro--text-disabled--rgb:255,255,255;--theme--green-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--green-pro--text-placeholder--rgb:255,255,255;--theme--green-pro--text-inverse:#0b1114;--theme--green-pro--text-inverse--rgb:11,17,20;--theme--orange-pro--background:#0f0f12;--theme--orange-pro--background--rgb:15,15,18;--theme--orange-pro--secondary-background:#212c38;--theme--orange-pro--secondary-background--rgb:33,44,56;--theme--orange-pro--background-accent:#15151a;--theme--orange-pro--background-accent--rgb:21,21,26;--theme--orange-pro--highlight:#ff6368;--theme--orange-pro--highlight--rgb:255,99,104;--theme--orange-pro--highlight-darker:#cc4f53;--theme--orange-pro--highlight-darker--rgb:204,79,83;--theme--orange-pro--text-highlight:white;--theme--orange-pro--text-highlight--rgb:255,255,255;--theme--orange-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--orange-pro--text-primary--rgb:255,255,255;--theme--orange-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--orange-pro--text-secondary--rgb:255,255,255;--theme--orange-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--orange-pro--text-disabled--rgb:255,255,255;--theme--orange-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--orange-pro--text-placeholder--rgb:255,255,255;--theme--orange-pro--text-inverse:#0f0f12;--theme--orange-pro--text-inverse--rgb:15,15,18;--theme--pro--background:#0a0a0a;--theme--pro--background--rgb:10,10,10;--theme--pro--secondary-background:#000000;--theme--pro--secondary-background--rgb:0,0,0;--theme--pro--background-accent:#161617;--theme--pro--background-accent--rgb:22,22,23;--theme--pro--highlight:#1293ff;--theme--pro--highlight--rgb:18,147,255;--theme--pro--highlight-darker:#0e76cc;--theme--pro--highlight-darker--rgb:14,118,204;--theme--pro--text-highlight:white;--theme--pro--text-highlight--rgb:255,255,255;--theme--pro--text-primary:rgba(255, 255, 255, 0.8);--theme--pro--text-primary--rgb:255,255,255;--theme--pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--pro--text-secondary--rgb:255,255,255;--theme--pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--pro--text-disabled--rgb:255,255,255;--theme--pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--pro--text-placeholder--rgb:255,255,255;--theme--pro--text-inverse:#0a0a0a;--theme--pro--text-inverse--rgb:10,10,10}@keyframes thumbnail-loading{0%{background-color:rgba(255,255,255,.02)}100%{background-color:rgba(255,255,255,.15)}}@keyframes trainer-play-button-loading-scroll{0%{transform:translateX(0)}100%{transform:translateX(-100%)}}@keyframes trainer-play-button-line{from{transform:translateX(215px)}to{transform:translateX(-215px)}}.overflow-fade__wrapper{position:relative;z-index:0}.overflow-fade__wrapper:before,.overflow-fade__wrapper:after{content:"";display:block;position:absolute;pointer-events:none;z-index:9999;opacity:0;transition:opacity .15s}.overflow-fade__wrapper--vertical{height:100%}.overflow-fade__wrapper--vertical:before{top:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to bottom, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/calc(100% - 7px) var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--vertical--no-scrollbar{height:100%}.overflow-fade__wrapper--vertical--no-scrollbar:before{top:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to bottom, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/100% var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--vertical--no-scrollbar.overflow-fade-bottom:after{opacity:1;bottom:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to top, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) bottom left/100% var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--horizontal{width:100%}.overflow-fade__wrapper--horizontal:before{top:0;left:0;width:var(--overflow-fade--width, 25px);height:100%;background:linear-gradient(to right, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/var(--overflow-fade--width, 25px) calc(100% - 7px) no-repeat}.overflow-fade__wrapper--horizontal:after{top:0;right:0;width:var(--overflow-fade--width, 25px);height:100%;background:linear-gradient(to left, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top right/var(--overflow-fade--width, 25px) calc(100% - 7px) no-repeat}.overflow-fade-left:before{opacity:1}.overflow-fade-right:after{opacity:1}.overflow-fade-top:before{opacity:1}.overflow-fade-bottom:after{opacity:1;bottom:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to top, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) bottom left/calc(100% - 7px) var(--overflow-fade--width, 25px) no-repeat}@font-face{font-family:"Inter";font-style:normal;font-weight:100;font-display:block;src:url(${te}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:200;font-display:block;src:url(${re}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:300;font-display:block;src:url(${ae}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:400;font-display:block;src:url(${ie}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:500;font-display:block;src:url(${ne}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:600;font-display:block;src:url(${le}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:700;font-display:block;src:url(${de}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:800;font-display:block;src:url(${ge}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:900;font-display:block;src:url(${ce}) format("woff2")}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${E}) format("woff2")}.material-symbols-outlined,overlay-menu>*>button .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}body{--constant--trainerHeaderHeight: 80px;--constant--appHeaderHeight: 36px}ux-dialog-container>div{margin-top:36px}ux-dialog.scrollable{max-height:calc(100vh - 98px) !important}ux-dialog.fullscreen-dialog close-button{top:36px !important}ux-dialog.fullscreen-dialog .app-content{background-color:var(--theme--background)}ux-dialog.fullscreen-dialog .app-content>router-view .view-background>.overflow-fade__wrapper:before,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background>.overflow-fade__wrapper:before{top:36px}ux-dialog.fullscreen-dialog .app-content>router-view .view-background .view-scrollable,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background .view-scrollable{padding-top:40px}ux-dialog.fullscreen-dialog .app-content>router-view .view-background .view-scrollable::-webkit-scrollbar,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent 36px, rgba(255, 255, 255, 0.1) 36px)}ux-dialog.fullscreen-dialog .app-content>router-view .view-background .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement{height:36px}ux-dialog.fullscreen-dialog .app-content>router-view .view-background .view-scrollable:before,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background .view-scrollable:before{display:none}@keyframes cta--boost--hover{0%{background-color:rgba(0,0,0,0)}100%{background-color:rgba(var(--theme--highlight--rgb), 0.5)}}@keyframes cta--pulse{from{margin:0}to{margin:-5px;width:calc(100% + 10px);height:calc(100% + 10px)}}@keyframes cta--inner-pulse{from{opacity:0}to{opacity:.5}}@keyframes favorite-bump{0%{transform:scale(1)}50%{transform:scale(1.4)}100%{transform:scale(1)}}body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}#fullscreen-dialogs ux-dialog-container>div{margin:0 !important}#fullscreen-dialogs ux-dialog-overlay{background:var(--theme--secondary-background);opacity:1}#dialogs ux-dialog{animation:dialog-pop .2s ease-in-out}#dialogs ux-dialog-container.active:not(:last-of-type){opacity:.0001}ux-dialog-overlay{background:rgba(var(--theme--background--rgb), 0.75);transition:opacity .2s !important}ux-dialog-overlay:nth-of-type(1n + 2){display:none}ux-dialog-container{display:flex;overflow:hidden auto;transition-duration:.2s !important}ux-dialog-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}ux-dialog-container::-webkit-scrollbar-thumb:window-inactive,ux-dialog-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}ux-dialog-container::-webkit-scrollbar-thumb:window-inactive:hover,ux-dialog-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}ux-dialog-container>div{padding:0;display:flex;flex:1 1 auto;justify-content:center;align-items:center}@media(max-width: 500px){ux-dialog-container>div{margin:15px !important}}@media(min-width: 501px){ux-dialog-container>div{margin:50px !important}}ux-dialog{box-shadow:0 0 5px rgba(var(--theme--background--rgb), 0.5);position:relative;border-radius:10px;border:1px solid rgba(255,255,255,.05);padding:11px 15px;text-align:left;display:inline-flex;align-items:center;padding:18px 26px 26px 26px;width:550px;display:block;border-radius:20px;max-width:calc(100vw - 126px)}.theme-default ux-dialog{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro ux-dialog{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro ux-dialog{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro ux-dialog{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro ux-dialog{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}ux-dialog>*+*{margin-left:9px}ux-dialog,ux-dialog a{font-size:14px;line-height:21px;line-height:19px;font-weight:500;color:rgba(255,255,255,.5)}ux-dialog strong,ux-dialog em{font-weight:700;color:#fff;font-style:normal}ux-dialog p{margin:0;padding:0}ux-dialog p+p{margin-top:10px}ux-dialog>*+*{margin-left:0}ux-dialog.dialog-loaded{animation:dialog-pop2 .2s ease-in-out}ux-dialog.align-center ux-dialog-header,ux-dialog.align-center ux-dialog-body,ux-dialog.align-center ux-dialog-footer{text-align:center}ux-dialog.align-left ux-dialog-header,ux-dialog.align-left ux-dialog-body,ux-dialog.align-left ux-dialog-footer{text-align:left}ux-dialog.scrollable{max-height:calc(100vh - 100px);padding:0}ux-dialog.scrollable>*+*{margin:0}ux-dialog.scrollable .dialog-scroll-wrapper{width:100%;max-height:inherit;overflow-x:hidden;overflow-y:overlay}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-button:single-button:vertical:decrement{height:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-button:single-button:vertical:increment{height:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar{width:25px;border-top-left-radius:0;border-top-right-radius:20px;border-bottom-right-radius:20px;border-bottom-left-radius:0;background:rgba(0,0,0,0)}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb{border:10px solid rgba(0,0,0,0)}@media(max-width: 500px){ux-dialog.scrollable{max-height:calc(100vh - 30px)}}ux-dialog.secondary-gradient-bg{border:0;background:linear-gradient(180deg, var(--theme--secondary-background) 0%, #000 100%) !important}.theme-pro ux-dialog.secondary-gradient-bg{background:linear-gradient(180deg, #2b2b2b 0%, #000 100%) !important}ux-dialog header{font-weight:800;color:#fff;margin-bottom:20px}ux-dialog ux-dialog-header{border:0;padding:0}ux-dialog ux-dialog-header h1{font-weight:800;font-size:21px;line-height:30px;font-weight:700;color:#fff;margin:0}ux-dialog ux-dialog-header h1 sub{color:rgba(255,255,255,.5);vertical-align:unset}ux-dialog ux-dialog-header .dialog-close{display:none}ux-dialog ux-dialog-body{font-weight:700;font-size:15px;line-height:24px;font-weight:500;color:rgba(255,255,255,.6);padding:0}ux-dialog ux-dialog-body em{font-style:normal;font-weight:bold;color:rgba(255,255,255,.8)}ux-dialog ux-dialog-body p{margin:0}ux-dialog ux-dialog-body p+p{margin-top:12px}ux-dialog ux-dialog-footer{border:0;padding:20px 0 0 0;white-space:nowrap}ux-dialog ux-dialog-footer button{margin:0 15px 0 0}ux-dialog ux-dialog-footer button:last-child{margin-right:0}ux-dialog ux-dialog-footer button.secondary{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}ux-dialog ux-dialog-footer button.secondary,ux-dialog ux-dialog-footer button.secondary *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary{border:1px solid #fff}}ux-dialog ux-dialog-footer button.secondary>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}ux-dialog ux-dialog-footer button.secondary>*:first-child{padding-left:0}ux-dialog ux-dialog-footer button.secondary>*:last-child{padding-right:0}ux-dialog ux-dialog-footer button.secondary svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary svg *{fill:CanvasText}}ux-dialog ux-dialog-footer button.secondary svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary svg{opacity:1}}ux-dialog ux-dialog-footer button.secondary img{height:50%}ux-dialog ux-dialog-footer button.secondary:disabled{opacity:.3}ux-dialog ux-dialog-footer button.secondary:disabled,ux-dialog ux-dialog-footer button.secondary:disabled *{cursor:default;pointer-events:none}@media(hover: hover){ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover svg{opacity:1}}ux-dialog ux-dialog-footer button.secondary:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}ux-dialog ux-dialog-footer button.cancel{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:rgba(255,255,255,.4);text-decoration:none}ux-dialog ux-dialog-footer button.cancel svg *{fill:rgba(255,255,255,.4)}@media(hover: hover){ux-dialog ux-dialog-footer button.cancel:hover{color:#fff}ux-dialog ux-dialog-footer button.cancel:hover svg *{fill:#fff}}ux-dialog ux-dialog-footer button.cancel:after{content:"";display:inline-block;width:5px;height:9px;-webkit-mask-box-image:url(${A});margin-left:5px;background:rgba(255,255,255,.4);transition:transform .15s}@media(hover: hover){ux-dialog ux-dialog-footer button.cancel:hover{color:#fff}ux-dialog ux-dialog-footer button.cancel:hover:after{transform:translateX(3px);background:#fff}}ux-dialog ux-dialog-footer button.primary,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel){--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}ux-dialog ux-dialog-footer button.primary,ux-dialog ux-dialog-footer button.primary *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel),ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel){border:1px solid #fff}}ux-dialog ux-dialog-footer button.primary>*,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}ux-dialog ux-dialog-footer button.primary>*:first-child,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*:first-child{padding-left:0}ux-dialog ux-dialog-footer button.primary>*:last-child,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*:last-child{padding-right:0}ux-dialog ux-dialog-footer button.primary svg,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary svg *,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg *{fill:CanvasText}}ux-dialog ux-dialog-footer button.primary svg *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary svg,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg{opacity:1}}ux-dialog ux-dialog-footer button.primary img,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) img{height:50%}ux-dialog ux-dialog-footer button.primary:disabled,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled{opacity:.3}ux-dialog ux-dialog-footer button.primary:disabled,ux-dialog ux-dialog-footer button.primary:disabled *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled *{cursor:default;pointer-events:none}@media(hover: hover){ux-dialog ux-dialog-footer button.primary:not(:disabled):hover,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}ux-dialog ux-dialog-footer button.primary:not(:disabled):hover svg,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover svg{opacity:1}}ux-dialog ux-dialog-footer button.primary:not(:disabled):active,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){ux-dialog ux-dialog-footer button.primary:not(:disabled):hover,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}ux-dialog ux-dialog-footer button.primary:not(:disabled):active,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):active{background-color:var(--theme--highlight)}ux-dialog close-button{position:absolute;right:-13px;top:-13px}ux-dialog.fullscreen-dialog{background:var(--theme--secondary-background);max-width:initial}ux-dialog.fullscreen-dialog close-button{position:fixed;right:20px;top:40px}@media(max-width: 500px){ux-dialog{max-width:calc(100vw - 30px)}ux-dialog close-button{right:-10px;top:-10px}}.dialog-loading-indicator{padding:60px 150px}.dialog-loading-indicator svg{width:40px;height:40px}@keyframes dialog-fade{0%{opacity:0}100%{opacity:1}}@keyframes dialog-pop{0%{opacity:0;transform:scale(0.8)}25%{opacity:1}50%{transform:scale(1.05)}100%{opacity:1;transform:scale(1)}}@keyframes dialog-pop2{0%{opacity:0;transform:scale(0)}25%{opacity:1}50%{transform:scale(1.05)}100%{transform:scale(1)}}.fullscreen-dialog{width:100%;height:100%;border:0;padding:0;background:rgba(0,0,0,0);max-height:100%;border-radius:0}@keyframes dropdown-menu-transition-right{from{opacity:0;transform:translateX(-10px)}to{opacity:1;transform:translateX(0)}}@keyframes dropdown-menu-transition-left{from{opacity:0;transform:translateX(10px)}to{opacity:1;transform:translateX(0)}}@keyframes dropdown-menu-transition-down{from{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.wemod-icon,i{display:inline-block}.wemod-icon:after,i:after{content:"";display:block;clear:both}.wemod-icon svg,i svg{float:left}.wemod-icon--xs svg{max-width:8px;max-height:8px}.wemod-icon--xs svg *{color:#fff}.wemod-icon--s svg{max-width:15px;max-height:15px}.wemod-icon--s svg *{color:#fff}.wemod-icon--m svg{max-width:20px;max-height:20px}.wemod-icon--m svg *{color:#fff}.wemod-icon--l svg{max-width:75px;max-height:75px}.wemod-icon--l svg *{color:#fff}.wemod-payment-method--american-express,.wemod-payment-method--amex{background-image:url(${B}) !important;background-color:#fff}.wemod-payment-method--diners{background-image:url(${N}) !important;background-color:#fff}.wemod-payment-method--discover{background-image:url(${T}) !important;background-color:#fff}.wemod-payment-method--jcb{background-image:url(${D}) !important;background-color:#fff}.wemod-payment-method--mastercard{background-image:url(${F}) !important;background-color:#fff}.wemod-payment-method--visa{background-image:url(${G}) !important;background-color:#fff}.wemod-payment-method--paypal{background-image:url(${P}) !important;background-color:#fff}.wemod-payment-method--amazon-pay{background-image:url(${V}) !important;background-color:#000}.wemod-payment-method--google-pay{background-image:url(${Y}) !important;background-color:#fff;width:48px !important;background-size:auto 12px;background-color:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.1)}.wemod-payment-method--apple-pay{background-image:url(${W}) !important;background-color:#fff;width:48px !important;background-size:auto 12px;background-color:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.1)}.wemod-payment-method--alipay{background-image:url(${K}) !important;background-color:#fff}.wemod-payment-method--direct-debit{background-image:url(${J}) !important;background-color:#fff}.wemod-payment-method--kr-market{background-image:url(${Q}) !important;background-color:#fff}.wemod-payment-method--kakao-pay{background-image:url(${Z}) !important;background-color:#000}.wemod-payment-method--kr-card{background-image:url(${ee}) !important;background-color:#fff}.wemod-payment-method--naver-pay{background-image:url(${oe}) !important;background-color:#fff}@keyframes placeholder__el--initial-fade{0%{opacity:0}100%{opacity:1}}@keyframes placeholder__el--fade{0%{background:rgba(255,255,255,.02)}100%{background:rgba(255,255,255,.15)}}.wemod-tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal}.wemod-tag--pro{background-color:var(--color--pro)}.wemod-tag--new{background-color:#ddf00c;color:#3b5209}.wemod-tag--updated{background-color:rgba(255,255,255,.1);color:var(--theme--text-primary)}.wemod-tag--alert{background-color:var(--color--alert)}.wemod-tag--warn{background-color:rgba(var(--color--accent-yellow--rgb), 0.2);color:var(--color--accent-yellow)}.theme-default{--theme--background:#0d0f12;--theme--background--rgb:13,15,18;--theme--secondary-background:#18293a;--theme--secondary-background--rgb:24,41,58;--theme--background-accent:#131417;--theme--background-accent--rgb:19,20,23;--theme--highlight:#00c7f2;--theme--highlight--rgb:0,199,242;--theme--highlight-darker:#009fc2;--theme--highlight-darker--rgb:0,159,194;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0d0f12;--theme--text-inverse--rgb:13,15,18}.theme-purple-pro{--theme--background:#0e0e15;--theme--background--rgb:14,14,21;--theme--secondary-background:#191f48;--theme--secondary-background--rgb:25,31,72;--theme--background-accent:#131319;--theme--background-accent--rgb:19,19,25;--theme--highlight:#a341ff;--theme--highlight--rgb:163,65,255;--theme--highlight-darker:#8234cc;--theme--highlight-darker--rgb:130,52,204;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0e0e15;--theme--text-inverse--rgb:14,14,21}.theme-green-pro{--theme--background:#0b1114;--theme--background--rgb:11,17,20;--theme--secondary-background:#0f2b27;--theme--secondary-background--rgb:15,43,39;--theme--background-accent:#10171a;--theme--background-accent--rgb:16,23,26;--theme--highlight:#00e7c6;--theme--highlight--rgb:0,231,198;--theme--highlight-darker:#00b99e;--theme--highlight-darker--rgb:0,185,158;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0b1114;--theme--text-inverse--rgb:11,17,20}.theme-orange-pro{--theme--background:#0f0f12;--theme--background--rgb:15,15,18;--theme--secondary-background:#212c38;--theme--secondary-background--rgb:33,44,56;--theme--background-accent:#15151a;--theme--background-accent--rgb:21,21,26;--theme--highlight:#ff6368;--theme--highlight--rgb:255,99,104;--theme--highlight-darker:#cc4f53;--theme--highlight-darker--rgb:204,79,83;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0f0f12;--theme--text-inverse--rgb:15,15,18}.theme-pro{--theme--background:#0a0a0a;--theme--background--rgb:10,10,10;--theme--secondary-background:#000000;--theme--secondary-background--rgb:0,0,0;--theme--background-accent:#161617;--theme--background-accent--rgb:22,22,23;--theme--highlight:#1293ff;--theme--highlight--rgb:18,147,255;--theme--highlight-darker:#0e76cc;--theme--highlight-darker--rgb:14,118,204;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0a0a0a;--theme--text-inverse--rgb:10,10,10}:root,:before,:after{--color--accent:#acff35;--color--accent--rgb:172,255,53;--color--accent-yellow:#ffaa2b;--color--accent-yellow--rgb:255,170,43;--color--alert:#ff0056;--color--alert--rgb:255,0,86;--color--secondary-purple:#834bff;--color--secondary-purple--rgb:131,75,255;--color--pro:#9200ff;--color--pro--rgb:146,0,255;--color--brand-blue:#13cfff;--color--brand-blue--rgb:19,207,255;--color--brand-green:#0bf2f6;--color--brand-green--rgb:11,242,246;--color--warning:#ffed48;--color--warning--rgb:255,237,72;--theme--default--background:#0d0f12;--theme--default--background--rgb:13,15,18;--theme--default--secondary-background:#18293a;--theme--default--secondary-background--rgb:24,41,58;--theme--default--background-accent:#131417;--theme--default--background-accent--rgb:19,20,23;--theme--default--highlight:#00c7f2;--theme--default--highlight--rgb:0,199,242;--theme--default--highlight-darker:#009fc2;--theme--default--highlight-darker--rgb:0,159,194;--theme--default--text-highlight:white;--theme--default--text-highlight--rgb:255,255,255;--theme--default--text-primary:rgba(255, 255, 255, 0.8);--theme--default--text-primary--rgb:255,255,255;--theme--default--text-secondary:rgba(255, 255, 255, 0.6);--theme--default--text-secondary--rgb:255,255,255;--theme--default--text-disabled:rgba(255, 255, 255, 0.4);--theme--default--text-disabled--rgb:255,255,255;--theme--default--text-placeholder:rgba(255, 255, 255, 0.3);--theme--default--text-placeholder--rgb:255,255,255;--theme--default--text-inverse:#0d0f12;--theme--default--text-inverse--rgb:13,15,18;--theme--purple-pro--background:#0e0e15;--theme--purple-pro--background--rgb:14,14,21;--theme--purple-pro--secondary-background:#191f48;--theme--purple-pro--secondary-background--rgb:25,31,72;--theme--purple-pro--background-accent:#131319;--theme--purple-pro--background-accent--rgb:19,19,25;--theme--purple-pro--highlight:#a341ff;--theme--purple-pro--highlight--rgb:163,65,255;--theme--purple-pro--highlight-darker:#8234cc;--theme--purple-pro--highlight-darker--rgb:130,52,204;--theme--purple-pro--text-highlight:white;--theme--purple-pro--text-highlight--rgb:255,255,255;--theme--purple-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--purple-pro--text-primary--rgb:255,255,255;--theme--purple-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--purple-pro--text-secondary--rgb:255,255,255;--theme--purple-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--purple-pro--text-disabled--rgb:255,255,255;--theme--purple-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--purple-pro--text-placeholder--rgb:255,255,255;--theme--purple-pro--text-inverse:#0e0e15;--theme--purple-pro--text-inverse--rgb:14,14,21;--theme--green-pro--background:#0b1114;--theme--green-pro--background--rgb:11,17,20;--theme--green-pro--secondary-background:#0f2b27;--theme--green-pro--secondary-background--rgb:15,43,39;--theme--green-pro--background-accent:#10171a;--theme--green-pro--background-accent--rgb:16,23,26;--theme--green-pro--highlight:#00e7c6;--theme--green-pro--highlight--rgb:0,231,198;--theme--green-pro--highlight-darker:#00b99e;--theme--green-pro--highlight-darker--rgb:0,185,158;--theme--green-pro--text-highlight:white;--theme--green-pro--text-highlight--rgb:255,255,255;--theme--green-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--green-pro--text-primary--rgb:255,255,255;--theme--green-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--green-pro--text-secondary--rgb:255,255,255;--theme--green-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--green-pro--text-disabled--rgb:255,255,255;--theme--green-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--green-pro--text-placeholder--rgb:255,255,255;--theme--green-pro--text-inverse:#0b1114;--theme--green-pro--text-inverse--rgb:11,17,20;--theme--orange-pro--background:#0f0f12;--theme--orange-pro--background--rgb:15,15,18;--theme--orange-pro--secondary-background:#212c38;--theme--orange-pro--secondary-background--rgb:33,44,56;--theme--orange-pro--background-accent:#15151a;--theme--orange-pro--background-accent--rgb:21,21,26;--theme--orange-pro--highlight:#ff6368;--theme--orange-pro--highlight--rgb:255,99,104;--theme--orange-pro--highlight-darker:#cc4f53;--theme--orange-pro--highlight-darker--rgb:204,79,83;--theme--orange-pro--text-highlight:white;--theme--orange-pro--text-highlight--rgb:255,255,255;--theme--orange-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--orange-pro--text-primary--rgb:255,255,255;--theme--orange-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--orange-pro--text-secondary--rgb:255,255,255;--theme--orange-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--orange-pro--text-disabled--rgb:255,255,255;--theme--orange-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--orange-pro--text-placeholder--rgb:255,255,255;--theme--orange-pro--text-inverse:#0f0f12;--theme--orange-pro--text-inverse--rgb:15,15,18;--theme--pro--background:#0a0a0a;--theme--pro--background--rgb:10,10,10;--theme--pro--secondary-background:#000000;--theme--pro--secondary-background--rgb:0,0,0;--theme--pro--background-accent:#161617;--theme--pro--background-accent--rgb:22,22,23;--theme--pro--highlight:#1293ff;--theme--pro--highlight--rgb:18,147,255;--theme--pro--highlight-darker:#0e76cc;--theme--pro--highlight-darker--rgb:14,118,204;--theme--pro--text-highlight:white;--theme--pro--text-highlight--rgb:255,255,255;--theme--pro--text-primary:rgba(255, 255, 255, 0.8);--theme--pro--text-primary--rgb:255,255,255;--theme--pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--pro--text-secondary--rgb:255,255,255;--theme--pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--pro--text-disabled--rgb:255,255,255;--theme--pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--pro--text-placeholder--rgb:255,255,255;--theme--pro--text-inverse:#0a0a0a;--theme--pro--text-inverse--rgb:10,10,10}@keyframes thumbnail-loading{0%{background-color:rgba(255,255,255,.02)}100%{background-color:rgba(255,255,255,.15)}}@keyframes trainer-play-button-loading-scroll{0%{transform:translateX(0)}100%{transform:translateX(-100%)}}@keyframes trainer-play-button-line{from{transform:translateX(215px)}to{transform:translateX(-215px)}}.overflow-fade__wrapper{position:relative;z-index:0}.overflow-fade__wrapper:before,.overflow-fade__wrapper:after{content:"";display:block;position:absolute;pointer-events:none;z-index:9999;opacity:0;transition:opacity .15s}.overflow-fade__wrapper--vertical{height:100%}.overflow-fade__wrapper--vertical:before{top:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to bottom, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/calc(100% - 7px) var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--vertical--no-scrollbar{height:100%}.overflow-fade__wrapper--vertical--no-scrollbar:before{top:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to bottom, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/100% var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--vertical--no-scrollbar.overflow-fade-bottom:after{opacity:1;bottom:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to top, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) bottom left/100% var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--horizontal{width:100%}.overflow-fade__wrapper--horizontal:before{top:0;left:0;width:var(--overflow-fade--width, 25px);height:100%;background:linear-gradient(to right, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/var(--overflow-fade--width, 25px) calc(100% - 7px) no-repeat}.overflow-fade__wrapper--horizontal:after{top:0;right:0;width:var(--overflow-fade--width, 25px);height:100%;background:linear-gradient(to left, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top right/var(--overflow-fade--width, 25px) calc(100% - 7px) no-repeat}.overflow-fade-left:before{opacity:1}.overflow-fade-right:after{opacity:1}.overflow-fade-top:before{opacity:1}.overflow-fade-bottom:after{opacity:1;bottom:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to top, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) bottom left/calc(100% - 7px) var(--overflow-fade--width, 25px) no-repeat}html{background:rgba(0,0,0,.5);-webkit-font-smoothing:antialiased}body{margin:0;padding:0;width:100%;overflow:hidden}body.override-contrast-mode,body.override-contrast-mode *{forced-color-adjust:none}*,a{cursor:default;box-sizing:border-box;-webkit-user-select:none;font-family:"Inter"}a{text-decoration:none}a,img{-webkit-user-drag:none}a,a *,button,button *{cursor:pointer}a:not(:focus-visible):not(input):not(textarea),a *:not(:focus-visible):not(input):not(textarea),button:not(:focus-visible):not(input):not(textarea),button *:not(:focus-visible):not(input):not(textarea){outline:none}input[type=text],input[type=password]{cursor:initial}input,textarea{outline:none}textarea{cursor:initial}*:focus-visible{outline:2px solid var(--theme--highlight) !important}html,body{background:rgba(0,0,0,0) !important}.overlay .backdrop{content:"";display:block;position:absolute;left:0;top:0;width:100%;height:100%;background:linear-gradient(360deg, var(--theme--background) 0%, rgba(var(--theme--background--rgb), 0.25) 25%, rgba(var(--theme--background--rgb), 0) 50%, rgba(var(--theme--background--rgb), 0.25) 75%, var(--theme--background) 100%),rgba(0,0,0,.25)}.overlay.pinned .overlay-layout>.backdrop,.overlay.pinned .overlay-layout>overlay-header,.overlay.pinned .overlay-layout>overlay-menu,.overlay.pinned .overlay-layout>overlay-windows overlay-window:not(.pinned),.overlay.pinned .overlay-layout>ad-popup{opacity:0}.overlay .overlay-layout{width:100vw;height:100vh;position:relative}.overlay .overlay-layout>.backdrop,.overlay .overlay-layout>overlay-header,.overlay .overlay-layout>overlay-menu,.overlay .overlay-layout>overlay-windows overlay-window,.overlay .overlay-layout>ad-popup{transition:opacity .25s}.overlay overlay-windows{position:absolute;left:0;top:0;width:100%;height:100%;z-index:1}.overlay overlay-header{position:absolute;left:40px;top:40px;right:40px}.overlay overlay-notifications{position:absolute;right:0;top:0;z-index:2;height:100%}.overlay ad-popup{position:absolute;left:0;bottom:0;z-index:3}overlay-menu>nav{position:absolute;bottom:40px;left:40px;right:40px;display:flex;align-items:center;justify-content:center;gap:16px;z-index:2;pointer-events:none}overlay-menu>nav *{pointer-events:auto}overlay-menu>*>button{font-weight:700;display:inline-flex;align-items:center;justify-content:center;gap:8px;padding:0 16px;height:44px;border:none;outline:0;background:#fff;box-shadow:0px 2px 8px rgba(0,0,0,.33);transition:color .15s,background-color .15s,border-color .15s;border:1px solid rgba(0,0,0,0);border-radius:16px}overlay-menu>*>button .label{color:#141518;font-size:14px;line-height:24px;letter-spacing:-0.5px;display:flex;flex-direction:column}overlay-menu>*>button .label .sub-label{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;color:var(--theme--text-primary);margin:-4px 0 4px}overlay-menu>*>button .label:has(.sub-label){margin-top:-6px;margin-bottom:-6px}@media(max-width: 720px){overlay-menu>*>button .label{display:none}}overlay-menu>*>button .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;display:inline-flex;width:20px;height:20px;align-items:center;justify-content:center;color:#141518}overlay-menu>*>button .icon svg *{fill:currentColor}overlay-menu>*>button:hover{background:rgba(255,255,255,.8)}overlay-menu>*>button:not(.open),overlay-menu>*>button.settings-button{background-color:rgba(19,20,23,.9);border-color:rgba(255,255,255,.05)}overlay-menu>*>button:not(.open) .label,overlay-menu>*>button.settings-button .label{color:rgba(255,255,255,.8)}overlay-menu>*>button:not(.open) .icon,overlay-menu>*>button.settings-button .icon{color:rgba(255,255,255,.6)}overlay-menu>*>button:not(.open):hover,overlay-menu>*>button.settings-button:hover{background-color:rgba(31,32,37,.9)}overlay-menu>*>button:not(.open):hover .label,overlay-menu>*>button.settings-button:hover .label{color:#fff}overlay-menu>*>button:not(.open):hover .icon,overlay-menu>*>button.settings-button:hover .icon{color:#fff}overlay-menu>*>button.alert{background-color:#ee343f;border:1px solid rgba(255,255,255,.15)}overlay-menu>*>button.alert .label{color:#fff}overlay-menu>*>button.alert .icon{color:#fff}overlay-menu>*>button.alert:hover{background-color:#b91c25}overlay-menu .settings-button-container{position:absolute;right:40px;bottom:40px;z-index:0}overlay-menu overlay-settings-menu{position:absolute;right:0;bottom:60px}`,""]);const he=M}}]);
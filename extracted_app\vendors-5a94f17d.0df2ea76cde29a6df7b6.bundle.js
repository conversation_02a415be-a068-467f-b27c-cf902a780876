/*! For license information please see vendors-5a94f17d.0df2ea76cde29a6df7b6.bundle.js.LICENSE.txt */
(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5758],{223:(e,t,r)=>{var n=r(55272),o=r(99087),i={scaleh:"scaleH",scalew:"scaleW",stretchh:"stretchH",lineheight:"lineHeight",alphachnl:"alphaChnl",redchnl:"redChnl",greenchnl:"greenChnl",bluechnl:"blueChnl"};function a(e){var t=function(e){for(var t=[],r=0;r<e.attributes.length;r++)t.push(e.attributes[r]);return t}(e);return t.reduce((function(e,t){var r;return e[(r=t.nodeName,i[r.toLowerCase()]||r)]=t.nodeValue,e}),{})}e.exports=function(e){e=e.toString();var t=o(e),r={pages:[],chars:[],kernings:[]};["info","common"].forEach((function(e){var o=t.getElementsByTagName(e)[0];o&&(r[e]=n(a(o)))}));var i=t.getElementsByTagName("pages")[0];if(!i)throw new Error("malformed file -- no <pages> element");for(var s=i.getElementsByTagName("page"),f=0;f<s.length;f++){var u=s[f],c=parseInt(u.getAttribute("id"),10),l=u.getAttribute("file");if(isNaN(c))throw new Error('malformed file -- page "id" attribute is NaN');if(!l)throw new Error('malformed file -- needs page "file" attribute');r.pages[parseInt(c,10)]=l}return["chars","kernings"].forEach((function(e){var o=t.getElementsByTagName(e)[0];if(o)for(var i=e.substring(0,e.length-1),s=o.getElementsByTagName(i),f=0;f<s.length;f++){var u=s[f];r[e].push(n(a(u)))}})),r}},30791:e=>{var t=[66,77,70];function r(e,t,r){if(r>t.length-1)return 0;var o=t.readUInt8(r++),i=t.readInt32LE(r);switch(r+=4,o){case 1:e.info=function(e,t){var r={};r.size=e.readInt16LE(t);var o=e.readUInt8(t+2);return r.smooth=o>>7&1,r.unicode=o>>6&1,r.italic=o>>5&1,r.bold=o>>4&1,o>>3&1&&(r.fixedHeight=1),r.charset=e.readUInt8(t+3)||"",r.stretchH=e.readUInt16LE(t+4),r.aa=e.readUInt8(t+6),r.padding=[e.readInt8(t+7),e.readInt8(t+8),e.readInt8(t+9),e.readInt8(t+10)],r.spacing=[e.readInt8(t+11),e.readInt8(t+12)],r.outline=e.readUInt8(t+13),r.face=function(e,t){return n(e,t).toString("utf8")}(e,t+14),r}(t,r);break;case 2:e.common=function(e,t){var r={};return r.lineHeight=e.readUInt16LE(t),r.base=e.readUInt16LE(t+2),r.scaleW=e.readUInt16LE(t+4),r.scaleH=e.readUInt16LE(t+6),r.pages=e.readUInt16LE(t+8),e.readUInt8(t+10),r.packed=0,r.alphaChnl=e.readUInt8(t+11),r.redChnl=e.readUInt8(t+12),r.greenChnl=e.readUInt8(t+13),r.blueChnl=e.readUInt8(t+14),r}(t,r);break;case 3:e.pages=function(e,t,r){for(var o=[],i=n(e,t),a=i.length+1,s=r/a,f=0;f<s;f++)o[f]=e.slice(t,t+i.length).toString("utf8"),t+=a;return o}(t,r,i);break;case 4:e.chars=function(e,t,r){for(var n=[],o=r/20,i=0;i<o;i++){var a={},s=20*i;a.id=e.readUInt32LE(t+0+s),a.x=e.readUInt16LE(t+4+s),a.y=e.readUInt16LE(t+6+s),a.width=e.readUInt16LE(t+8+s),a.height=e.readUInt16LE(t+10+s),a.xoffset=e.readInt16LE(t+12+s),a.yoffset=e.readInt16LE(t+14+s),a.xadvance=e.readInt16LE(t+16+s),a.page=e.readUInt8(t+18+s),a.chnl=e.readUInt8(t+19+s),n[i]=a}return n}(t,r,i);break;case 5:e.kernings=function(e,t,r){for(var n=[],o=r/10,i=0;i<o;i++){var a={},s=10*i;a.first=e.readUInt32LE(t+0+s),a.second=e.readUInt32LE(t+4+s),a.amount=e.readInt16LE(t+8+s),n[i]=a}return n}(t,r,i)}return 5+i}function n(e,t){for(var r=t;r<e.length&&0!==e[r];r++);return e.slice(t,r)}e.exports=function(e){if(e.length<6)throw new Error("invalid buffer length for BMFont");var n=t.every((function(t,r){return e.readUInt8(r)===t}));if(!n)throw new Error("BMFont missing BMF byte header");var o=3;if(e.readUInt8(o++)>3)throw new Error("Only supports BMFont Binary v3 (BMFont App v1.10)");for(var i={kernings:[],chars:[]},a=0;a<5;a++)o+=r(i,e,o);return i}},36066:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.ReadStreamTokenizer=void 0;const n=r(98632),o=r(48705);class i extends n.AbstractTokenizer{constructor(e,t){super(t),this.streamReader=new o.StreamReader(e)}async getFileInfo(){return this.fileInfo}async readBuffer(e,t){const r=this.normalizeOptions(e,t),n=r.position-this.position;if(n>0)return await this.ignore(n),this.readBuffer(e,t);if(n<0)throw new Error("`options.position` must be equal or greater than `tokenizer.position`");if(0===r.length)return 0;const i=await this.streamReader.read(e,r.offset,r.length);if(this.position+=i,(!t||!t.mayBeLess)&&i<r.length)throw new o.EndOfStreamError;return i}async peekBuffer(e,t){const r=this.normalizeOptions(e,t);let n=0;if(r.position){const t=r.position-this.position;if(t>0){const o=new Uint8Array(r.length+t);return n=await this.peekBuffer(o,{mayBeLess:r.mayBeLess}),e.set(o.subarray(t),r.offset),n-t}if(t<0)throw new Error("Cannot peek from a negative offset in a stream")}if(r.length>0){try{n=await this.streamReader.peek(e,r.offset,r.length)}catch(e){if(t&&t.mayBeLess&&e instanceof o.EndOfStreamError)return 0;throw e}if(!r.mayBeLess&&n<r.length)throw new o.EndOfStreamError}return n}async ignore(e){const t=Math.min(256e3,e),r=new Uint8Array(t);let n=0;for(;n<e;){const o=e-n,i=await this.readBuffer(r,{length:Math.min(t,o)});if(i<0)return i;n+=i}return n}}t.ReadStreamTokenizer=i},48379:(e,t,r)=>{"use strict";r.d(t,{tl:()=>d});const n=2147483647,o=/[^\0-\x7F]/,i=/[\x2E\u3002\uFF0E\uFF61]/g,a={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},s=Math.floor,f=String.fromCharCode;function u(e){throw new RangeError(a[e])}const c=function(e,t){return e+22+75*(e<26)-((0!=t)<<5)},l=function(e,t,r){let n=0;for(e=r?s(e/700):e>>1,e+=s(e/t);e>455;n+=36)e=s(e/35);return s(n+36*e/(e+38))},d=function(e){return function(e,t){const r=e.split("@");let n="";r.length>1&&(n=r[0]+"@",e=r[1]);const o=function(e,t){const r=[];let n=e.length;for(;n--;)r[n]=t(e[n]);return r}((e=e.replace(i,".")).split("."),t).join(".");return n+o}(e,(function(e){return o.test(e)?"xn--"+function(e){const t=[],r=(e=function(e){const t=[];let r=0;const n=e.length;for(;r<n;){const o=e.charCodeAt(r++);if(o>=55296&&o<=56319&&r<n){const n=e.charCodeAt(r++);56320==(64512&n)?t.push(((1023&o)<<10)+(1023&n)+65536):(t.push(o),r--)}else t.push(o)}return t}(e)).length;let o=128,i=0,a=72;for(const r of e)r<128&&t.push(f(r));const d=t.length;let h=d;for(d&&t.push("-");h<r;){let r=n;for(const t of e)t>=o&&t<r&&(r=t);const p=h+1;r-o>s((n-i)/p)&&u("overflow"),i+=(r-o)*p,o=r;for(const r of e)if(r<o&&++i>n&&u("overflow"),r===o){let e=i;for(let r=36;;r+=36){const n=r<=a?1:r>=a+26?26:r-a;if(e<n)break;const o=e-n,i=36-n;t.push(f(c(n+o%i,0))),e=s(o/i)}t.push(f(c(e,0))),a=l(i,p,h===d),i=0,++h}++i,++o}return t.join("")}(e):e}))}},48705:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StreamReader=t.EndOfStreamError=void 0;var n=r(75523);Object.defineProperty(t,"EndOfStreamError",{enumerable:!0,get:function(){return n.EndOfStreamError}});var o=r(51510);Object.defineProperty(t,"StreamReader",{enumerable:!0,get:function(){return o.StreamReader}})},51510:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.StreamReader=t.EndOfStreamError=void 0;const n=r(75523),o=r(78122);var i=r(75523);Object.defineProperty(t,"EndOfStreamError",{enumerable:!0,get:function(){return i.EndOfStreamError}}),t.StreamReader=class{constructor(e){if(this.s=e,this.deferred=null,this.endOfStream=!1,this.peekQueue=[],!e.read||!e.once)throw new Error("Expected an instance of stream.Readable");this.s.once("end",(()=>this.reject(new n.EndOfStreamError))),this.s.once("error",(e=>this.reject(e))),this.s.once("close",(()=>this.reject(new Error("Stream closed"))))}async peek(e,t,r){const n=await this.read(e,t,r);return this.peekQueue.push(e.subarray(t,t+n)),n}async read(e,t,r){if(0===r)return 0;if(0===this.peekQueue.length&&this.endOfStream)throw new n.EndOfStreamError;let o=r,i=0;for(;this.peekQueue.length>0&&o>0;){const r=this.peekQueue.pop();if(!r)throw new Error("peekData should be defined");const n=Math.min(r.length,o);e.set(r.subarray(0,n),t+i),i+=n,o-=n,n<r.length&&this.peekQueue.push(r.subarray(n))}for(;o>0&&!this.endOfStream;){const r=Math.min(o,1048576),n=await this.readFromStream(e,t+i,r);if(i+=n,n<r)break;o-=n}return i}async readFromStream(e,t,r){const n=this.s.read(r);if(n)return e.set(n,t),n.length;{const n={buffer:e,offset:t,length:r,deferred:new o.Deferred};return this.deferred=n.deferred,this.s.once("readable",(()=>{this.readDeferred(n)})),n.deferred.promise}}readDeferred(e){const t=this.s.read(e.length);t?(e.buffer.set(t,e.offset),e.deferred.resolve(t.length),this.deferred=null):this.s.once("readable",(()=>{this.readDeferred(e)}))}reject(e){this.endOfStream=!0,this.deferred&&(this.deferred.reject(e),this.deferred=null)}}},55272:e=>{var t="chasrset";e.exports=function(e){for(var r in e=Object.assign({},e),t in e&&(e.charset=e[t],delete e[t]),e)"face"!==r&&"charset"!==r&&(e[r]="padding"===r||"spacing"===r?e[r].split(",").map((function(e){return parseInt(e,10)})):parseInt(e[r],10));return e}},65802:(e,t,r)=>{"use strict";var n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},o=r(58611),i=r(65692),a=r(87016),s=r(83480),f=r(43106),u=r(39023),c=function(e,t){if("string"!=typeof e&&!e.hasOwnProperty("url"))throw new Error("Missing url option from options for request method.");var r="object"===(void 0===e?"undefined":n(e))?a.parse(e.url):a.parse(e),u={hostname:r.hostname,port:r.port||("http:"===r.protocol.toLowerCase()?80:443),path:r.path,method:"GET",headers:{},auth:r.auth||null,parse:"none",stream:!1};if("object"===(void 0===e?"undefined":n(e))&&(u=Object.assign(u,e)),u.port=Number(u.port),u.hasOwnProperty("timeout")&&delete u.timeout,!0===u.compressed&&(u.headers["accept-encoding"]="gzip, deflate"),e.hasOwnProperty("form")){if("object"!==n(e.form))throw new Error("phin 'form' option must be of type Object if present.");var c=s.stringify(e.form);u.headers["Content-Type"]="application/x-www-form-urlencoded",u.headers["Content-Length"]=Buffer.byteLength(c),e.data=c}var l=void 0,d=function(e){var r=e;!0===u.compressed&&("gzip"===e.headers["content-encoding"]?r=e.pipe(f.createGunzip()):"deflate"===e.headers["content-encoding"]&&(r=e.pipe(f.createInflate()))),!0===u.stream?(e.stream=r,t&&t(null,e)):(e.body=new Buffer([]),r.on("data",(function(t){e.body=Buffer.concat([e.body,t])})),r.on("end",(function(){if(t){if("json"===u.parse)try{e.body=JSON.parse(e.body.toString())}catch(r){return void t("Invalid JSON received.",e)}t(null,e)}})))};switch(r.protocol.toLowerCase()){case"http:":l=o.request(u,d);break;case"https:":l=i.request(u,d);break;default:return void(t&&t(new Error("Invalid / unknown URL protocol. Expected HTTP or HTTPS."),null))}if("number"==typeof e.timeout&&l.setTimeout(e.timeout,(function(){l.abort(),t&&t(new Error("Timeout has been reached."),null),t=null})),l.on("error",(function(e){t&&t(e,null)})),e.hasOwnProperty("data")){var h=e.data;if(!(e.data instanceof Buffer)&&"object"===n(e.data))if("application/x-www-form-urlencoded"===(u.headers["content-type"]||u.headers["Content-Type"]))h=s.stringify(e.data);else try{h=JSON.stringify(e.data)}catch(e){t&&t(new Error("Couldn't stringify object. (Likely due to a circular reference.)"),null)}l.write(h)}l.end()};c.promisified=function(e,t){return new Promise((function(t,r){c(e,(function(e,n){e?r(e):t(n)}))}))},u.promisify&&(c[u.promisify.custom]=c.promisified),e.exports=c},68160:e=>{"use strict";function t(e,n,o,i,a,s){for(var f,u,c,l,d=Math.max(n-1,0),h=Math.max(o-1,0),p=Math.min(n+1,i-1),m=Math.min(o+1,a-1),g=4*(o*i+n),y=0,w=0,v=0,b=0,E=0,O=d;O<=p;O++)for(var S=h;S<=m;S++)if(O!==n||S!==o){var k=r(e,e,g,4*(S*i+O),!0);if(0===k?y++:k<0?v++:k>0&&w++,y>2)return!1;s&&(k<b&&(b=k,f=O,u=S),k>E&&(E=k,c=O,l=S))}return!s||0!==v&&0!==w&&(!t(e,f,u,i,a)&&!t(s,f,u,i,a)||!t(e,c,l,i,a)&&!t(s,c,l,i,a))}function r(e,t,r,s,f){var u=e[r+3]/255,c=t[s+3]/255,l=a(e[r+0],u),d=a(e[r+1],u),h=a(e[r+2],u),p=a(t[s+0],c),m=a(t[s+1],c),g=a(t[s+2],c),y=n(l,d,h)-n(p,m,g);if(f)return y;var w=o(l,d,h)-o(p,m,g),v=i(l,d,h)-i(p,m,g);return.5053*y*y+.299*w*w+.1957*v*v}function n(e,t,r){return.29889531*e+.58662247*t+.11448223*r}function o(e,t,r){return.59597799*e-.2741761*t-.32180189*r}function i(e,t,r){return.21147017*e-.52261711*t+.31114694*r}function a(e,t){return 255+(e-255)*t}function s(e,t,r,n,o){e[t+0]=r,e[t+1]=n,e[t+2]=o,e[t+3]=255}e.exports=function(e,o,i,f,u,c){c||(c={});for(var l=void 0===c.threshold?.1:c.threshold,d=35215*l*l,h=0,p=0;p<u;p++)for(var m=0;m<f;m++){var g=4*(p*f+m);if(r(e,o,g,g)>d)c.includeAA||!t(e,m,p,f,u,o)&&!t(o,m,p,f,u,e)?(i&&s(i,g,255,0,0),h++):i&&s(i,g,255,255,0);else if(i){var y=a((void 0,b=(w=e)[(v=g)+3]/255,n(a(w[v+0],b),a(w[v+1],b),a(w[v+2],b))),.1);s(i,g,y,y,y)}}var w,v,b;return h}},74361:e=>{function t(e,t){if(!(e=e.replace(/\t+/g," ").trim()))return null;var n=e.indexOf(" ");if(-1===n)throw new Error("no named row at line "+t);var o=e.substring(0,n);e=(e=(e=(e=e.substring(n+1)).replace(/letter=[\'\"]\S+[\'\"]/gi,"")).split("=")).map((function(e){return e.trim().match(/(".*?"|[^"\s]+)+(?=\s*|\s*$)/g)}));for(var i=[],a=0;a<e.length;a++){var s=e[a];0===a?i.push({key:s[0],data:""}):a===e.length-1?i[i.length-1].data=r(s[0]):(i[i.length-1].data=r(s[0]),i.push({key:s[1],data:""}))}var f={key:o,data:{}};return i.forEach((function(e){f.data[e.key]=e.data})),f}function r(e){return e&&0!==e.length?0===e.indexOf('"')||0===e.indexOf("'")?e.substring(1,e.length-1):-1!==e.indexOf(",")?function(e){return e.split(",").map((function(e){return parseInt(e,10)}))}(e):parseInt(e,10):""}e.exports=function(e){if(!e)throw new Error("no data provided");var r={pages:[],chars:[],kernings:[]},n=(e=e.toString().trim()).split(/\r\n?|\n/g);if(0===n.length)throw new Error("no data in BMFont file");for(var o=0;o<n.length;o++){var i=t(n[o],o);if(i)if("page"===i.key){if("number"!=typeof i.data.id)throw new Error("malformed file at line "+o+" -- needs page id=N");if("string"!=typeof i.data.file)throw new Error("malformed file at line "+o+' -- needs page file="path"');r.pages[i.data.id]=i.data.file}else"chars"===i.key||"kernings"===i.key||("char"===i.key?r.chars.push(i.data):"kerning"===i.key?r.kernings.push(i.data):r[i.key]=i.data)}return r}},75523:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.EndOfStreamError=t.defaultMessages=void 0,t.defaultMessages="End-Of-Stream";class r extends Error{constructor(){super(t.defaultMessages)}}t.EndOfStreamError=r},78023:e=>{var t=function(e){return e.replace(/^\s+|\s+$/g,"")};e.exports=function(e){if(!e)return{};for(var r,n={},o=t(e).split("\n"),i=0;i<o.length;i++){var a=o[i],s=a.indexOf(":"),f=t(a.slice(0,s)).toLowerCase(),u=t(a.slice(s+1));void 0===n[f]?n[f]=u:(r=n[f],"[object Array]"===Object.prototype.toString.call(r)?n[f].push(u):n[f]=[n[f],u])}return n}},78122:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.Deferred=void 0,t.Deferred=class{constructor(){this.resolve=()=>null,this.reject=()=>null,this.promise=new Promise(((e,t)=>{this.reject=t,this.resolve=e}))}}},80363:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fromStream=t.fromBuffer=t.EndOfStreamError=t.fromFile=void 0;const n=r(91343),o=r(96452);var i=r(91456);Object.defineProperty(t,"fromFile",{enumerable:!0,get:function(){return i.fromFile}});var a=r(96452);Object.defineProperty(t,"EndOfStreamError",{enumerable:!0,get:function(){return a.EndOfStreamError}}),Object.defineProperty(t,"fromBuffer",{enumerable:!0,get:function(){return a.fromBuffer}}),t.fromStream=async function(e,t){if(t=t||{},e.path){const r=await n.stat(e.path);t.path=e.path,t.size=r.size}return o.fromStream(e,t)}},91343:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.readFile=t.writeFileSync=t.writeFile=t.read=t.open=t.close=t.stat=t.createReadStream=t.pathExists=void 0;const n=r(79896);t.pathExists=n.existsSync,t.createReadStream=n.createReadStream,t.stat=async function(e){return new Promise(((t,r)=>{n.stat(e,((e,n)=>{e?r(e):t(n)}))}))},t.close=async function(e){return new Promise(((t,r)=>{n.close(e,(e=>{e?r(e):t()}))}))},t.open=async function(e,t){return new Promise(((r,o)=>{n.open(e,t,((e,t)=>{e?o(e):r(t)}))}))},t.read=async function(e,t,r,o,i){return new Promise(((a,s)=>{n.read(e,t,r,o,i,((e,t,r)=>{e?s(e):a({bytesRead:t,buffer:r})}))}))},t.writeFile=async function(e,t){return new Promise(((r,o)=>{n.writeFile(e,t,(e=>{e?o(e):r()}))}))},t.writeFileSync=function(e,t){n.writeFileSync(e,t)},t.readFile=async function(e){return new Promise(((t,r)=>{n.readFile(e,((e,n)=>{e?r(e):t(n)}))}))}},91456:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fromFile=t.FileTokenizer=void 0;const n=r(98632),o=r(48705),i=r(91343);class a extends n.AbstractTokenizer{constructor(e,t){super(t),this.fd=e}async readBuffer(e,t){const r=this.normalizeOptions(e,t);this.position=r.position;const n=await i.read(this.fd,e,r.offset,r.length,r.position);if(this.position+=n.bytesRead,n.bytesRead<r.length&&(!t||!t.mayBeLess))throw new o.EndOfStreamError;return n.bytesRead}async peekBuffer(e,t){const r=this.normalizeOptions(e,t),n=await i.read(this.fd,e,r.offset,r.length,r.position);if(!r.mayBeLess&&n.bytesRead<r.length)throw new o.EndOfStreamError;return n.bytesRead}async close(){return i.close(this.fd)}}t.FileTokenizer=a,t.fromFile=async function(e){const t=await i.stat(e);if(!t.isFile)throw new Error(`File not a file: ${e}`);const r=await i.open(e,"r");return new a(r,{path:e,size:t.size})}},93492:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.BufferTokenizer=void 0;const n=r(48705),o=r(98632);class i extends o.AbstractTokenizer{constructor(e,t){super(t),this.uint8Array=e,this.fileInfo.size=this.fileInfo.size?this.fileInfo.size:e.length}async readBuffer(e,t){if(t&&t.position){if(t.position<this.position)throw new Error("`options.position` must be equal or greater than `tokenizer.position`");this.position=t.position}const r=await this.peekBuffer(e,t);return this.position+=r,r}async peekBuffer(e,t){const r=this.normalizeOptions(e,t),o=Math.min(this.uint8Array.length-r.position,r.length);if(!r.mayBeLess&&o<r.length)throw new n.EndOfStreamError;return e.set(this.uint8Array.subarray(r.position,r.position+o),r.offset),o}async close(){}}t.BufferTokenizer=i},96452:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.fromBuffer=t.fromStream=t.EndOfStreamError=void 0;const n=r(36066),o=r(93492);var i=r(48705);Object.defineProperty(t,"EndOfStreamError",{enumerable:!0,get:function(){return i.EndOfStreamError}}),t.fromStream=function(e,t){return t=t||{},new n.ReadStreamTokenizer(e,t)},t.fromBuffer=function(e,t){return new o.BufferTokenizer(e,t)}},98632:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AbstractTokenizer=void 0;const n=r(48705);t.AbstractTokenizer=class{constructor(e){this.position=0,this.numBuffer=new Uint8Array(8),this.fileInfo=e||{}}async readToken(e,t=this.position){const r=Buffer.alloc(e.len);if(await this.readBuffer(r,{position:t})<e.len)throw new n.EndOfStreamError;return e.get(r,0)}async peekToken(e,t=this.position){const r=Buffer.alloc(e.len);if(await this.peekBuffer(r,{position:t})<e.len)throw new n.EndOfStreamError;return e.get(r,0)}async readNumber(e){if(await this.readBuffer(this.numBuffer,{length:e.len})<e.len)throw new n.EndOfStreamError;return e.get(this.numBuffer,0)}async peekNumber(e){if(await this.peekBuffer(this.numBuffer,{length:e.len})<e.len)throw new n.EndOfStreamError;return e.get(this.numBuffer,0)}async ignore(e){if(void 0!==this.fileInfo.size){const t=this.fileInfo.size-this.position;if(e>t)return this.position+=t,t}return this.position+=e,e}async close(){}normalizeOptions(e,t){if(t&&void 0!==t.position&&t.position<this.position)throw new Error("`options.position` must be equal or greater than `tokenizer.position`");return t?{mayBeLess:!0===t.mayBeLess,offset:t.offset?t.offset:0,length:t.length?t.length:e.length-(t.offset?t.offset:0),position:t.position?t.position:this.position}:{mayBeLess:!1,offset:0,length:e.length,position:this.position}}}},99027:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.clone=s,t.addLast=u,t.addFirst=c,t.removeLast=l,t.removeFirst=d,t.insert=h,t.removeAt=p,t.replaceAt=m,t.getIn=g,t.set=y,t.setIn=w,t.update=b,t.updateIn=E,t.merge=O,t.mergeDeep=S,t.mergeIn=k,t.omit=I,t.addDefaults=B,t.default=void 0;const r="INVALID_ARGS",n=!1;function o(e){throw new Error(e)}function i(e){const t=Object.keys(e);return Object.getOwnPropertySymbols?t.concat(Object.getOwnPropertySymbols(e)):t}const a={}.hasOwnProperty;function s(e){if(Array.isArray(e))return e.slice();const t=e,r=i(t),n={};for(let e=0;e<r.length;e++){const o=r[e];n[o]=t[o]}return n}function f(e){return null!=e&&"object"==typeof e}function u(e,t){return Array.isArray(t)?e.concat(t):e.concat([t])}function c(e,t){return Array.isArray(t)?t.concat(e):[t].concat(e)}function l(e){return e.length?e.slice(0,e.length-1):e}function d(e){return e.length?e.slice(1):e}function h(e,t,r){return e.slice(0,t).concat(Array.isArray(r)?r:[r]).concat(e.slice(t))}function p(e,t){return t>=e.length||t<0?e:e.slice(0,t).concat(e.slice(t+1))}function m(e,t,r){if(e[t]===r)return e;const n=e.length,o=Array(n);for(let t=0;t<n;t++)o[t]=e[t];return o[t]=r,o}function g(e,t){if(Array.isArray(t)||o(n?"A path array should be provided when calling getIn()":r),null==e)return;let i=e;for(let e=0;e<t.length;e++){const r=t[e];if(i=null!=i?i[r]:void 0,void 0===i)return i}return i}function y(e,t,r){let n=e;if(null==n&&(n="number"==typeof t?[]:{}),n[t]===r)return n;const o=s(n);return o[t]=r,o}function w(e,t,r){return t.length?v(e,t,r,0):r}function v(e,t,r,n){let o;const i=t[n];return o=n===t.length-1?r:v(f(e)&&f(e[i])?e[i]:"number"==typeof t[n+1]?[]:{},t,r,n+1),y(e,i,o)}function b(e,t,r){return y(e,t,r(null==e?void 0:e[t]))}function E(e,t,r){return w(e,t,r(g(e,t)))}function O(e,t,r,n,o,i,...a){return a.length?j.call(null,!1,!1,e,t,r,n,o,i,...a):j(!1,!1,e,t,r,n,o,i)}function S(e,t,r,n,o,i,...a){return a.length?j.call(null,!1,!0,e,t,r,n,o,i,...a):j(!1,!0,e,t,r,n,o,i)}function k(e,t,r,n,o,i,a,...s){let f,u=g(e,t);return null==u&&(u={}),f=s.length?j.call(null,!1,!1,u,r,n,o,i,a,...s):j(!1,!1,u,r,n,o,i,a),w(e,t,f)}function I(e,t){const r=Array.isArray(t)?t:[t];let n=!1;for(let t=0;t<r.length;t++)if(a.call(e,r[t])){n=!0;break}if(!n)return e;const o={},s=i(e);for(let t=0;t<s.length;t++){const n=s[t];r.indexOf(n)>=0||(o[n]=e[n])}return o}function B(e,t,r,n,o,i,...a){return a.length?j.call(null,!0,!1,e,t,r,n,o,i,...a):j(!0,!1,e,t,r,n,o,i)}function j(e,t,a,...u){let c=a;null==c&&o(n?"At least one object should be provided to merge()":r);let l=!1;for(let r=0;r<u.length;r++){const n=u[r];if(null==n)continue;const o=i(n);if(o.length)for(let r=0;r<=o.length;r++){const i=o[r];if(e&&void 0!==c[i])continue;let a=n[i];t&&f(c[i])&&f(a)&&(a=j(e,t,c[i],a)),void 0!==a&&a!==c[i]&&(l||(l=!0,c=s(c)),c[i]=a)}}return c}var L={clone:s,addLast:u,addFirst:c,removeLast:l,removeFirst:d,insert:h,removeAt:p,replaceAt:m,getIn:g,set:y,setIn:w,update:b,updateIn:E,merge:O,mergeDeep:S,mergeIn:k,omit:I,addDefaults:B};t.default=L}}]);
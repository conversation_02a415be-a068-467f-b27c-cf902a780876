"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3319],{10699:(t,e,s)=>{s.d(e,{G:()=>m});var i=s(15215),a=s("aurelia-framework"),r=s(20770),n=s(68663),h=s(19072),o=s(57503),c=s("shared/pusher/index"),l=s(54995),u=s(48881),d=s(38777),p=s(86824),g=s(27958);let m=class{#t;#e;#s;#i;#a;#r;#n;#h;constructor(t,e,s,i,a,r){this.#t=t,this.#e=e,this.#s=s,this.#i=i,this.#a=a,this.#h=r}attached(){this.#a.setDeauthorizedHandler((()=>this.signOut())),this.#r=this.#s.onRestoredFromTray(this.#o.bind(this)),this.#c(this.token),this.#n=(0,d.SO)((()=>this.refreshAccount()),(0,p.H)(15,20))}detached(){this.#r?.dispose(),this.#r=null,this.#n?.dispose(),this.#n=null}#o(){this.refreshAccount()}tokenChanged(t){this.#c(t)}async signOut(){return await this.#e.dispatch(u.NX,"signOutOnStartup",!0),this.#h.router.navigateToRoute("dashboard",{},{replace:!0,trigger:!1}),this.#s.reload()}#c(t){this.#i.setAuthHeader(`Bearer ${t.accessToken}`)}async refreshAccount(){try{const t=await this.#t.getUserAccount();return await this.#e.dispatch(u.Ui,t),t}catch{return null}}};m=(0,i.Cg)([(0,a.autoinject)(),(0,l.m6)({setup:"attached",teardown:"detached",selectors:{token:(0,l.$t)((t=>t.token))}}),(0,i.Sn)("design:paramtypes",[n.x,r.il,h.s,c.Pusher,o.Z,g.L])],m)},27958:(t,e,s)=>{s.d(e,{L:()=>i});class i{attached(t){this.router=t}detached(){}getCurrentInternalUri(){const t=this.router.currentInstruction;if(t.config.settings?.supportedAsInternalUri){const e=new URL(`wemod:${t.fragment}`);return e.search=t.queryString,e.toString()}return"wemod:"}}},40127:(t,e,s)=>{s.d(e,{Dd:()=>k,ye:()=>b});var i=s(15215),a=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(96610),h=s(83802),o=s(19072),c=s(3972),l=s("services/bugsnag/index"),u=s("shared/cheats/resources/value-converters/proper-hotkey"),d=s(54995),p=s(38777),g=s(26700),m=s(62679),y=s(67064),v=s(62914),f=s(19648);const w=(0,n.getLogger)("capture"),C={"1080p30":{width:1920,height:1080,fps:30,bitrate:4e3,recFormat:"mp4",resolutionLabel:"1080p"},"720p30":{width:1280,height:720,fps:30,bitrate:4e3,recFormat:"mp4",resolutionLabel:"720p"},"360p24":{width:640,height:360,fps:24,bitrate:4e3,recFormat:"mp4",resolutionLabel:"360p"}},b=15,I=[17,220];let A=class{#l;#u;#d;#p;#g;#m;#y;#v;#s;#f;#w;#C;#b;#I;constructor(t,e,s,i,a,r,n,h){this.#l=!1,this.#u=0,this.#y=t,this.#v=e,this.#s=s,this.#f=i,this.#w=a,this.#C=r,this.#b=n,this.#I=h}attached(){this.#p=new p.Vd([this.#y.onTrainerActivated(this.#A.bind(this)),this.#y.onTrainerEnded(this.#k.bind(this)),this.#s.onCaptureReplayBufferSaved(this.#S.bind(this))])}detached(){this.#p?.dispose(),this.#d?.dispose(),this.#P()}async#A(t){this.#O(t),this.#L()}#k(t){this.#l&&this.#P()}async#L(){if(!this.enableCapture)return;await this.#P();const t=this.#y.trainer?.process?.id;if(t)try{const e=await this.getCaptureConfig();await this.#s.startCapture(e,t),await this.#s.startReplayBuffer(),this.#l=!0,this.#d=this.#v.onHotkeyPress((t=>this.#_(t))),this.#w.event("capture_start",this.#H(),v.Jb)}catch(t){this.#F(t),this.#w.event("capture_start_error",this.#H(),v.Io)}}async#P(){this.#l=!1,this.#u=0,this.#d?.dispose(),this.#g=null;try{await this.#s.stopCapture()}catch(t){this.#F(t)}this.#d?.dispose(),this.#w.event("capture_stop",this.#H(),v.Jb)}get captureVideoParams(){return(this.captureVideoPresetName?C[this.captureVideoPresetName]:void 0)??C["720p30"]}captureVideoPresetNameChanged(){this.#N()}captureAudioOutputDeviceIdChanged(){this.#N()}captureBufferSecondsChanged(){this.#N()}#N(){this.#l&&this.#L()}enableCaptureChanged(){!this.enableCapture&&this.#l&&this.#P(),this.enableCapture&&!this.#l&&this.#L()}async getCaptureConfig(){const t=await this.#T(this.titleName);return{videoParams:this.captureVideoParams,audioParams:{inputDeviceId:"disabled",outputDeviceId:this.captureAudioOutputDeviceId??"default"},bufferSeconds:(this.captureBufferSeconds??b)+2,recPath:t}}#O(t){const e=t.getMetadata(h.vO),s=e?.info?.titleId,i=e?.info?.gameId??"";s&&this.titles[s]?this.titleName=this.titles[s].name:this.titleName=void 0,i&&this.games[i]?this.#m=i:this.#m=void 0}#_(t){this.#l&&t.length===I.length&&t.every(((t,e)=>t===I[e]))&&this.saveHighlight("hotkey")}async saveHighlight(t){try{await this.#s.saveReplayBuffer(),this.#w.event("capture_highlight_save_intent",{...this.#H(),trigger:t},v.Io)}catch(t){this.#F(t)}}#F(t){(0,l.report)(t),w.error(t.toString())}#H(){return{titleName:this.titleName,gameId:this.#m,platformId:this.#m?this.games[this.#m]?.platformId:"",videoPreset:this.captureVideoPresetName,audioOutputDeviceId:this.captureAudioOutputDeviceId,captureBufferSeconds:this.captureBufferSeconds??b}}async#T(t){return await(0,g.B)(this.#I.getVideosDirectory(),t)}#V(){const t={content:1===this.#u?"capture.one_$title_highlight_saved":"capture.$count_$title_highlights_saved",i18nParams:{count:this.#u,title:this.titleName??""},icon:"replay_30",iconColor:"#ee343f",persist:!0,actions:[{label:"capture.view_captures",onclick:()=>this.#I.openCaptureFolder("toast",this.titleName)}],onremove:()=>{this.#g=null}};this.#g?this.#f.update(this.#g,t):this.#g=this.#f.toast(t)}async#S(t){t.success?(this.#C.publish("capture_highlight_saved"),this.#u++,this.#V(),(0,m.I6)(this.#I.getVideosDirectory(),t.filePath)):this.#w.event("capture_highlight_save_error",this.#H(),v.Io)}get displayHotkey(){return k(this.#b)}};function k(t){return I.map((e=>{const s=t.toView(e);return s?s.replace(/\\/g,"\\\\"):""})).join("+")}(0,i.Cg)([r.observable,(0,i.Sn)("design:type",Object)],A.prototype,"titleName",void 0),(0,i.Cg)([(0,r.computedFrom)("captureVideoPresetName"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],A.prototype,"captureVideoParams",null),A=(0,i.Cg)([(0,d.m6)({setup:"attached",teardown:"detached",selectors:{captureVideoPresetName:(0,d.$t)((t=>t.settings.captureVideoPresetName)),enableCapture:(0,d.$t)((t=>t.settings.enableCapture)),captureBufferSeconds:(0,d.$t)((t=>t.settings.captureBufferSeconds)),captureAudioOutputDeviceId:(0,d.$t)((t=>t.settings.captureAudioOutputDeviceId)),titles:(0,d.$t)((t=>t.catalog.titles)),games:(0,d.$t)((t=>t.catalog.games))}}),(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[h.jR,c.Mz,o.s,y.l,v.j0,a.EventAggregator,u.ProperHotkeyValueConverter,f.Z])],A)},49765:(t,e,s)=>{s.d(e,{I:()=>h});var i=s(15215),a=s("aurelia-framework"),r=s("shared/api/index"),n=s(20057);let h=class{#t;#E;#U;#j;constructor(t,e){this.#t=t,this.#E=e}#$(){this.#j=this.#E.getEffectiveLocale().baseName}activate(){this.#U=this.#E.onLocaleChanged((()=>this.#$())),this.#$(),this.#t.addInterceptor({request:t=>(this.#j&&t.headers.set("Accept-Language",this.#j),t)})}deactivate(){this.#U?.dispose(),this.#U=null}};h=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.WeModApiClient,n.F2])],h)},62914:(t,e,s)=>{s.d(e,{j0:()=>C,Io:()=>v,Jb:()=>f});var i=s(15215),a=s("aurelia-framework"),r=s(96610),n=s(16953),h=s("services/bugsnag/index"),o=s(17019),c=s(29864),l=s(19072),u=s(20057),d=s(54995),p=s(49442),g=s(64980);let m=class{#s;#E;#M;#z;constructor(t,e){this.#M=null,this.#s=t,this.#E=e}activate(){n.A.services.amplitude.apiKey&&(this.#M=(0,o.Q)(),this.account||this.#M.reset(),this.#M.init(n.A.services.amplitude.apiKey,this.account?.uuid,{optOut:!this.enabled,appVersion:this.#s.info.version,deviceId:this.deviceId,disableCookies:!0,domain:"app.wemod.com",defaultTracking:!1}),this.#M.add({name:"wemod_metadata",type:c.Q.ENRICHMENT,setup:async t=>{},execute:async t=>(t.os_name={win32:"Windows"}[this.#s.info.osPlatform]??this.#s.info.osPlatform,t.os_version=this.#s.info.osVersion,t.language=this.#E.getEffectiveLocale().toString(),this.#z&&(t.user_properties=Object.assign(t.user_properties??{},this.#z),this.#z=void 0),t)}))}accountChanged(){this.account&&this.#M?.setUserId(this.account.uuid)}async deactivate(){await this.flush().catch(p.Y)}async flush(){await(this.#M?.flush().promise)}enabledChanged(){this.#M?.setOptOut(!this.enabled)}track(t,e){if(this.#M){const s="object"==typeof e?(0,g.u)(e):{};this.#M.track(t,s)}}setUserProperty(t,e){this.#z||(this.#z={}),this.#z[t]=e}screenView(t){this.#s.isInTraySinceStartup||this.track("screen_view",{screen_type:t.name??null,screen_class:t.class??null,...t.params||null})}};m=(0,i.Cg)([(0,a.autoinject)(),(0,d.m6)({setup:"activate",teardown:"deactivate",selectors:{account:(0,d.$t)((t=>t.account)),deviceId:(0,d.$t)((t=>t.installation?.id)),enabled:(0,d.$t)((t=>t.settings?.analytics))}}),(0,i.Sn)("design:paramtypes",[l.s,u.F2])],m);var y=s(28747);const v=Object.freeze({ga:!0,amplitude:!0}),f=(Object.freeze({ga:!1,amplitude:!0}),Object.freeze({ga:!0,amplitude:!1})),w=(0,r.getLogger)("events");let C=class{#D;#x;constructor(t,e){this.#D=t,this.#x=e}async activate(){await Promise.allSettled([this.#D.activate(),this.#x.activate()])}async deactivate(){await this.#x.deactivate()}async flush(){await this.#x.flush()}user(t,e,s){n.A.debug&&w.debug(t,e),0===Object.keys(s??{}).length&&((0,h.report)(new Error("No dispatch specified for user event")),s=v),s.ga&&this.#D.user(t,e),s.amplitude&&this.#x.setUserProperty(t,e)}event(t,e,s=v){n.A.debug&&w.debug(t,e,s),s.ga&&this.#D.event(t,e),s.amplitude&&this.#x.track(t,e)}adConversion(t,e={}){this.#D.adEvent(t,e)}screenView(t,e=v){n.A.debug&&w.debug("screenView",t.name,t.class,t.params,e),e.ga&&this.#D.screenView(t),e.amplitude&&this.#x.screenView(t)}};C=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[y.a,m])],C)},78268:(t,e,s)=>{s.d(e,{s:()=>d});var i=s(15215),a=s("aurelia-framework"),r=s(20770),n=s(68663),h=s(54995),o=s(70236),c=s(48881),l=s(38777),u=s(86824);let d=class{#t;#e;#B;#R;constructor(t,e){this.#B=[],this.#t=t,this.#e=e}attached(){}detached(){this.#R?.dispose(),this.#R=null,this.#B=[]}accountFlagsChanged(){this.#B=this.#B.filter((t=>!(0,o.Lt)(this.accountFlags,t.flag)))}watchFlag(t,e,s=!1){if((0,o.Lt)(this.accountFlags,t))return l.lE;{const i={flag:t,remainingTries:e};return this.#B.push(i),this.#q(s),(0,l.nm)((()=>{const t=this.#B.indexOf(i);t>=0&&this.#B.splice(t,1)}))}}#q(t){t&&this.#R?.dispose(),!t&&this.#R||(this.#R=(0,l.Ix)((async()=>{this.#R=null;try{await this.#W()}finally{this.#R||0===this.#B.length||this.#q(!1)}}),t?0:(0,u.V)(4,7)))}async#W(){let t;try{const e=await this.#t.getUserAccountFlags(this.#B.reduce(((t,e)=>t|e.flag),0));this.#B=this.#B.filter((t=>--t.remainingTries>0&&!(0,o.Lt)(this.accountFlags,t.flag))),e&&(t=await this.#t.getUserAccount())}catch{}t&&await this.#e.dispatch(c.Ui,t)}};d=(0,i.Cg)([(0,a.autoinject)(),(0,h.m6)({setup:"attached",teardown:"detached",selectors:{accountFlags:(0,h.$t)((t=>t.account?.flags))}}),(0,i.Sn)("design:paramtypes",[n.x,r.il])],d)},78563:(t,e,s)=>{s.d(e,{s:()=>m,v:()=>g});var i=s(15215),a=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),h=s(16953),o=s(22920),c=s(64706),l=s(20057),u=s(54995),d=s(14046),p=s(48881);class g{constructor(t,e){this.titleId=t,this.historyItem=e}}let m=class{#G;#J;#K;#e;#Q;#E;constructor(t,e,s,i){this.#G=new Map,this.#J=new Map,this.#K=t,this.#e=e,this.#Q=s,this.#E=i,this.persistItem=this.persistItem.bind(this)}attached(){}detached(){}getAssistant(t){let e=this.#G.get(t);const s=(this.assistantHistory[t]||[]).map((t=>c.gG.deserialize(t))),i=new c.fz(t,s),a=this.catalog.titles[t]?.name;return this.#J.get(t)||this.#J.set(t,i.onItemFinalized((({titleId:t,item:e})=>this.#Z(t,e)))),e||(e=this.#K.createAssistant({titleId:t,titleName:a,baseUrl:h.A.services.assistant.baseUrl,client:"desktop",userProfileImageUrl:this.account?.profileImage,userProfileName:this.account?.username},i),this.#G.set(t,e)),e}async persistItem(t,e){if(e.persist){const s=e.serialize();await this.#e.dispatch(p.WS,t,[s]),this.#Q.publish(new g(t,e))}}async#Z(t,e){await this.persistItem(t,e);const s=this.getAssistant(t);s&&this.#X(s)}#X(t){const e=t=>"answer"===t.type&&"desktop"===t.client&&!!t.requestId,s=t.history.items[t.history.items.length-1],i=Object.values(this.assistantHistory).flat().filter((t=>e(t)));if(e(s)){const e=(0,d.c_)(Date.now(),new Date(this.lastOverlayAssistantMessage)),s=i.length>=1&&!this.timesOverlayAssistantMessageSeen,a=i.length>=15&&1===this.timesOverlayAssistantMessageSeen&&e>=1;if(s||a){const e=this.#E.getValue("assistant_manager.overlay_prompt_message"),s=new c.gG("answer",e,[],null,null,!0,!1,"desktop");this.#e.dispatch(p.Ew,"timesOverlayAssistantMessageSeen",1),this.#e.dispatch(p.vk,"lastOverlayAssistantMessage"),setTimeout((()=>t?.history.add(s)),2e3)}}}assistantHistoryChanged(t,e){Object.keys(e).forEach((s=>{const i=e[s],a=t[s];if(i?.length!==a?.length&&!a?.length){const t=this.getAssistant(s);t?.history.clear()}}))}get questionsAsked(){return Object.values(this.assistantHistory).flat().filter((t=>"question"===t.type))}};(0,i.Cg)([(0,r.computedFrom)("assistantHistory"),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],m.prototype,"questionsAsked",null),m=(0,i.Cg)([(0,u.m6)({setup:"attached",teardown:"detached",selectors:{assistantHistory:(0,u.$t)((t=>t.assistantHistory)),catalog:(0,u.$t)((t=>t.catalog)),timesOverlayAssistantMessageSeen:(0,u.$t)((t=>t.counters?.timesOverlayAssistantMessageSeen)),lastOverlayAssistantMessage:(0,u.$t)((t=>t.timestamps?.lastOverlayAssistantMessage)),account:(0,u.$t)((t=>t.account))}}),(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[o.h,n.il,a.EventAggregator,l.F2])],m)}}]);
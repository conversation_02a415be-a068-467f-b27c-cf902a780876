"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4025],{5964:(e,t,n)=>{n.d(t,{do:()=>r,Ks:()=>l,s5:()=>p});var r,o=n(92555),i=n(42182),s=n(93001),a=n(80886),c=n(51260),u=n(29336);function l(e,t){return new o.c((n=>{if(!window.PerformanceObserver)return;const o=e=>{const t=function(e){return e.filter((e=>!function(e){return!(e.entryType!==r.RESOURCE||(0,c.$3)(e.name)&&(0,c.ZU)(e))}(e)))}(e);t.length>0&&n.notify(t)};let l,f=!0;const m=new PerformanceObserver((0,i.dm)((e=>{f?l=(0,s.wg)((()=>o(e.getEntries()))):o(e.getEntries())})));try{m.observe(t)}catch(e){if([r.RESOURCE,r.NAVIGATION,r.LONG_TASK,r.PAINT].includes(t.type)){t.buffered&&(l=(0,s.wg)((()=>o(performance.getEntriesByType(t.type)))));try{m.observe({entryTypes:[t.type]})}catch(e){return}}}let v;return f=!1,function(e){!d&&void 0!==window.performance&&"getEntries"in performance&&"addEventListener"in performance&&(d=(0,a.q)(e,performance,"resourcetimingbufferfull",(()=>{performance.clearResourceTimings()})))}(e),p(r.FIRST_INPUT)||t.type!==r.FIRST_INPUT||({stop:v}=function(e,t){const n=(0,u.x3)();let r=!1;const{stop:o}=(0,a.l)(e,window,["click","mousedown","keydown","touchstart","pointerdown"],(t=>{if(!t.cancelable)return;const n={entryType:"first-input",processingStart:(0,u.$S)(),processingEnd:(0,u.$S)(),startTime:t.timeStamp,duration:0,name:"",cancelable:!1,target:null,toJSON:()=>({})};"pointerdown"===t.type?function(e,t){(0,a.l)(e,window,["pointerup","pointercancel"],(e=>{"pointerup"===e.type&&i(t)}),{once:!0})}(e,n):i(n)}),{passive:!0,capture:!0});return{stop:o};function i(e){if(!r){r=!0,o();const i=e.processingStart-e.startTime;i>=0&&i<(0,u.x3)()-n&&t(e)}}}(e,(e=>{o([e])}))),()=>{m.disconnect(),v&&v(),(0,s.DJ)(l)}}))}let d;function p(e){return window.PerformanceObserver&&void 0!==PerformanceObserver.supportedEntryTypes&&PerformanceObserver.supportedEntryTypes.includes(e)}!function(e){e.EVENT="event",e.FIRST_INPUT="first-input",e.LARGEST_CONTENTFUL_PAINT="largest-contentful-paint",e.LAYOUT_SHIFT="layout-shift",e.LONG_TASK="longtask",e.LONG_ANIMATION_FRAME="long-animation-frame",e.NAVIGATION="navigation",e.PAINT="paint",e.RESOURCE="resource"}(r||(r={}))},8561:(e,t,n)=>{n.d(t,{QR:()=>a});var r=n(80886),o=n(92832),i=n(32234);const s="v1";function a(e,t){!function(e,t){const n=new XMLHttpRequest;(0,r.q)(e,n,"load",(function(){if(200===n.status){const e=JSON.parse(n.responseText);t(e.rum)}else c()})),(0,r.q)(e,n,"error",(function(){c()})),n.open("GET",function(e){return`https://sdk-configuration.${(0,o.G)("rum",e)}/${s}/${encodeURIComponent(e.remoteConfigurationId)}.json`}(e)),n.send()}(e,(n=>{t(function(e,t){return{...e,...t}}(e,n))}))}function c(){i.Vy.error("Error fetching the remote configuration.")}},10611:(e,t,n)=>{n.d(t,{O:()=>i});var r=n(92555),o=n(23054);function i(){const e=new r.c,{stop:t}=(0,o.H)(window,"open",(()=>e.notify()));return{observable:e,stop:t}}},17142:(e,t,n)=>{n.d(t,{Y:()=>s});var r=n(72765),o=n(44193);const i=r.AQ;function s(e){const t=(0,o.q)({expireDelay:i});return e.subscribe(1,(e=>{t.add(function(e){return{service:e.service,version:e.version,context:e.context,id:e.id,name:e.name,startClocks:e.startClocks}}(e),e.startClocks.relative)})),e.subscribe(6,(({endClocks:e})=>{t.closeActive(e.relative)})),e.subscribe(3,(e=>{const n=t.find(e.startClocks.relative);n&&e.name&&(n.name=e.name),n&&e.context&&(n.context=e.context)})),e.subscribe(10,(()=>{t.reset()})),{findView:e=>t.find(e),getAllEntries:()=>t.getAllEntries(),getDeletedEntries:()=>t.getDeletedEntries(),stop:()=>{t.stop()}}}},18368:(e,t,n)=>{n.d(t,{d:()=>o});var r=n(30956);function o(e,{session:t,viewContext:n,errorType:o}){const i=t?t.id:"no-session-id",s=[];void 0!==o&&s.push(`error-type=${o}`),n&&(s.push(`seed=${n.id}`),s.push(`from=${n.startClocks.timeStamp}`));const a=function(e){const t=e.site,n=e.subdomain||function(e){switch(e.site){case r.NW:case r.dV:return"app";case r.Bb:return"dd";default:return}}(e);return`https://${n?`${n}.`:""}${t}`}(e);return`${a}/rum/replay/sessions/${i}?${s.join("&")}`}},20272:(e,t,n)=>{n.d(t,{r:()=>f});var r=n(92555),o=n(42661),i=n(47978),s=n(63502),a=n(78476),c=n(68393),u=n(1356),l=n(21655),d=n(32234),p=n(16105);function f(e,t){const n=new r.c;return function(e){(0,l.l)([d.bP.error]).subscribe((t=>e.notify(t.error)))}(n),(0,o.mj)(n),function(e,t){(0,p.u)(e,[p.V.cspViolation,p.V.intervention]).subscribe((e=>t.notify(e)))}(t,n),n.subscribe((t=>e.notify(14,{error:t}))),function(e){return e.subscribe(14,(({error:t,customerContext:n,savedCommonContext:r})=>{n=(0,i.kg)(t.context,n),e.notify(12,{customerContext:n,savedCommonContext:r,...m(t)})})),{addError:({error:t,handlingStack:n,componentStack:r,startClocks:o,context:i},u)=>{const l=(0,s.bJ)(t)?(0,a.T)(t):void 0,d=(0,s.As)({stackTrace:l,originalError:t,handlingStack:n,componentStack:r,startClocks:o,nonErrorPrefix:"Provided",source:c.g.CUSTOM,handling:"handled"});e.notify(14,{customerContext:i,savedCommonContext:u,error:d})}}}(e)}function m(e){const t={date:e.startClocks.timeStamp,error:{id:(0,u.lk)(),message:e.message,source:e.source,stack:e.stack,handling_stack:e.handlingStack,component_stack:e.componentStack,type:e.type,handling:e.handling,causes:e.causes,source_type:"browser",fingerprint:e.fingerprint,csp:e.csp},type:"error"},n={error:e.originalError,handlingStack:e.handlingStack};return{rawRumEvent:t,startTime:e.startClocks.relative,domainContext:n}}},23912:(e,t,n)=>{n.d(t,{k:()=>i});var r=n(42182),o=n(77705);function i(e){let t;const n=requestAnimationFrame((0,r.dm)((()=>{t=(0,o.pB)()}))),i=(0,o.g1)(e).subscribe((e=>{t=e})).unsubscribe;return{get:()=>t?{viewport:t}:void 0,stop:()=>{i(),n&&cancelAnimationFrame(n)}}}},24261:(e,t,n)=>{n.d(t,{d9:()=>l});var r=n(72765),o=n(44193),i=n(29336),s=n(80886);const a=4e3,c=500,u=r.AQ;function l(e,t,n=c){const r=(0,o.q)({expireDelay:u,maxEntries:a});let l;m(p(),(0,i.$S)());const{stop:f}=(0,s.l)(t,window,["pageshow","focus","blur","visibilitychange","resume","freeze","pagehide"],(e=>{m(function(e){return"freeze"===e.type?"frozen":"pagehide"===e.type?e.persisted?"frozen":"terminated":p()}(e),e.timeStamp)}),{capture:!0});function m(e,t=(0,i.$S)()){e!==l&&(l=e,r.closeActive(t),r.add({state:l,startTime:t},t))}function v(e,t,n){return r.findAll(t,n).some((t=>t.state===e))}return e.register(0,(({startTime:e,duration:t=0,eventType:o})=>"view"===o?{type:o,_dd:{page_states:d(r.findAll(e,t),e,n)}}:"action"===o||"error"===o?{type:o,view:{in_foreground:v("active",e,0)}}:void 0)),{wasInPageStateDuringPeriod:v,addPageState:m,stop:()=>{f(),r.stop()}}}function d(e,t,n){if(0!==e.length)return e.slice(-n).reverse().map((({state:e,startTime:n})=>({state:e,start:(0,i.Zj)((0,i.vk)(t,n))})))}function p(){return"hidden"===document.visibilityState?"hidden":document.hasFocus()?"active":"passive"}},26250:(e,t,n)=>{n.d(t,{b:()=>w});var r=n(48899),o=n(95844),i=n(7328),s=n(29336),a=n(59248),c=n(55407),u=n(90970),l=n(47978),d=n(78218),p=n(32234),f=n(2843);const m={"view.name":"string","view.url":"string","view.referrer":"string"},v={context:"object"},g={service:"string",version:"string"};let y;function w(e,t,n,w,b,h,S,k,A){y={view:{"view.performance.lcp.resource_url":"string",...v,...m},error:{"error.message":"string","error.stack":"string","error.resource.url":"string","error.fingerprint":"string",...v,...m,...g},resource:{"resource.url":"string",...(0,r.sr)(r.R9.WRITABLE_RESOURCE_GRAPHQL)?{"resource.graphql":"object"}:{},...v,...m,...g},action:{"action.target.name":"string",...v,...m,...g},long_task:{"long_task.scripts[].source_url":"string","long_task.scripts[].invoker":"string",...v,...m},vital:{...v,...m}};const T={error:(0,o.$)("error",e.eventRateLimiterThreshold,A),action:(0,o.$)("action",e.eventRateLimiterThreshold,A),vital:(0,o.$)("vital",e.eventRateLimiterThreshold,A)};t.subscribe(12,(({startTime:o,duration:m,rawRumEvent:v,domainContext:g,savedCommonContext:A,customerContext:_})=>{const E=b.findView(o),C=h.findUrl(o),R=w.findTrackedSession(o);if(R&&E&&!C&&(0,r.sr)(r.R9.MISSING_URL_CONTEXT_TELEMETRY)&&(0,i.A2)("Missing URL entry",{debug:{eventType:v.type,startTime:o,urlEntries:h.getAllEntries(),urlDeletedEntries:h.getDeletedEntries(),viewEntries:b.getAllEntries(),viewDeletedEntries:b.getDeletedEntries()}}),R&&E&&C){const r=A||k(),i={_dd:{format_version:2,drift:(0,s.TP)(),configuration:{session_sample_rate:(0,a.LI)(e.sessionSampleRate,3),session_replay_sample_rate:(0,a.LI)(e.sessionReplaySampleRate,3)},browser_sdk_version:(0,c.d0)()?"6.5.1":void 0},application:{id:e.applicationId},date:(0,s.nx)(),source:"browser",session:{id:R.id,type:"user"},display:S.get(),connectivity:(0,u.q)(),context:r.context},w=(0,l.kg)(i,n.triggerHook(0,{eventType:v.type,startTime:o,duration:m}),{context:_},v);"has_replay"in w.session||(w.session.has_replay=r.hasReplay),"view"===w.type&&(w.session.sampled_for_replay=1===R.sessionReplay),R.anonymousId&&!r.user.anonymous_id&&e.trackAnonymousUser&&(r.user.anonymous_id=R.anonymousId),(0,d.RI)(r.user)||(w.usr=r.user),!(0,d.RI)(r.account)&&r.account.id&&(w.account=r.account),function(e,t,n,r){var o;if(t){const r=(0,f.g)(e,y[e.type],(e=>t(e,n)));if(!1===r&&"view"!==e.type)return!1;!1===r&&p.Vy.warn("Can't dismiss view events using beforeSend!")}return!(null===(o=r[e.type])||void 0===o?void 0:o.isLimitReached())}(w,e.beforeSend,g,T)&&((0,d.RI)(w.context)&&delete w.context,t.notify(13,w))}}))}},27328:(e,t,n)=>{n.d(t,{rb:()=>i,wG:()=>a});var r=n(1356),o=n(2472);const i="data-dd-action-name",s="Masked Element";function a(e,{enablePrivacyForActionName:t,actionNameAttribute:n},r){const a=c(e,i)||n&&c(e,n);return a?{name:a,nameSource:"custom_attribute"}:r===o.$m.MASK?{name:s,nameSource:"mask_placeholder"}:p(e,n,u,t)||p(e,n,l,t)||{name:"",nameSource:"blank"}}function c(e,t){const n=e.closest(`[${t}]`);if(n)return m(f(n.getAttribute(t).trim()))}const u=[(e,t)=>{if("labels"in e&&e.labels&&e.labels.length>0)return g(e.labels[0],t)},e=>{if("INPUT"===e.nodeName){const t=e,n=t.getAttribute("type");if("button"===n||"submit"===n||"reset"===n)return{name:t.value,nameSource:"text_content"}}},(e,t,n)=>{if("BUTTON"===e.nodeName||"LABEL"===e.nodeName||"button"===e.getAttribute("role"))return g(e,t,n)},e=>v(e,"aria-label"),(e,t,n)=>{const r=e.getAttribute("aria-labelledby");if(r)return{name:r.split(/\s+/).map((t=>function(e,t){return e.ownerDocument?e.ownerDocument.getElementById(t):null}(e,t))).filter((e=>Boolean(e))).map((e=>y(e,t,n))).join(" "),nameSource:"text_content"}},e=>v(e,"alt"),e=>v(e,"name"),e=>v(e,"title"),e=>v(e,"placeholder"),(e,t)=>{if("options"in e&&e.options.length>0)return g(e.options[0],t)}],l=[(e,t,n)=>g(e,t,n)],d=10;function p(e,t,n,r){let o=e,i=0;for(;i<=d&&o&&"BODY"!==o.nodeName&&"HTML"!==o.nodeName&&"HEAD"!==o.nodeName;){for(const e of n){const n=e(o,t,r);if(n){const{name:e,nameSource:t}=n,r=e&&e.trim();if(r)return{name:m(f(r)),nameSource:t}}}if("FORM"===o.nodeName)break;o=o.parentElement,i+=1}}function f(e){return e.replace(/\s+/g," ")}function m(e){return e.length>100?`${(0,r._R)(e,100)} [...]`:e}function v(e,t){return{name:e.getAttribute(t)||"",nameSource:"standard_attribute"}}function g(e,t,n){return{name:y(e,t,n)||"",nameSource:"text_content"}}function y(e,t,n){if(!e.isContentEditable){if("innerText"in e){let r=e.innerText;const s=t=>{const n=e.querySelectorAll(t);for(let e=0;e<n.length;e+=1){const t=n[e];if("innerText"in t){const e=t.innerText;e&&e.trim().length>0&&(r=r.replace(e,""))}}};return s(`[${i}]`),t&&s(`[${t}]`),n&&s(`${(0,o.CU)(o.$m.HIDDEN)}, ${(0,o.CU)(o.$m.MASK)}`),r}return e.textContent}}},27410:(e,t,n)=>{n.d(t,{_G:()=>s,fL:()=>a,kf:()=>o});var r=n(29336);const o=10*r.OY,i=new Map;function s(e){const t=i.get(e);return i.delete(e),t}function a(e,t){i.set(e,t),i.forEach(((e,t)=>{(0,r.vk)(t,(0,r.$S)())>o&&i.delete(t)}))}},30544:(e,t,n)=>{n.d(t,{v:()=>a});var r=n(78218),o=n(92555),i=n(23054),s=n(80886);function a(e,t){let n=(0,r.yG)(t);return new o.c((o=>{const{stop:a}=function(e,t){const{stop:n}=(0,i.H)(c("pushState"),"pushState",(({onPostCall:e})=>{e(t)})),{stop:r}=(0,i.H)(c("replaceState"),"replaceState",(({onPostCall:e})=>{e(t)})),{stop:o}=(0,s.q)(e,window,"popstate",t);return{stop:()=>{n(),r(),o()}}}(e,l),{stop:u}=function(e,t){return(0,s.q)(e,window,"hashchange",t)}(e,l);function l(){if(n.href===t.href)return;const e=(0,r.yG)(t);o.notify({newLocation:e,oldLocation:n}),n=e}return()=>{a(),u()}}))}function c(e){return Object.prototype.hasOwnProperty.call(history,e)?history:History.prototype}},33425:(e,t,n)=>{n.d(t,{K:()=>o});var r=n(59248);function o(e){return(0,r.Et)(e)&&e<0?void 0:e}},35405:(e,t,n)=>{function r(e,t,n,r){return{context:e.getContext(),user:t.getContext(),account:n.getContext(),hasReplay:!!r.isRecording()||void 0}}n.d(t,{f:()=>r})},38499:(e,t,n)=>{n.d(t,{PW:()=>f,Wr:()=>d});var r=n(32234),o=n(69986),i=n(59248),s=n(78218),a=n(74036),c=n(31583),u=n(80967);const l=["tracecontext","datadog"];function d(e){var t,n,c,d,p,f;if(void 0===e.trackFeatureFlagsForEvents||Array.isArray(e.trackFeatureFlagsForEvents)||r.Vy.warn("trackFeatureFlagsForEvents should be an array"),!e.applicationId)return void r.Vy.error("Application ID is not configured, no RUM data will be collected.");if(!(0,o.bX)(e.sessionReplaySampleRate,"Session Replay")||!(0,o.bX)(e.traceSampleRate,"Trace"))return;if(void 0!==e.excludedActivityUrls&&!Array.isArray(e.excludedActivityUrls))return void r.Vy.error("Excluded Activity Urls should be an array");const m=function(e){if(void 0===e.allowedTracingUrls)return[];if(!Array.isArray(e.allowedTracingUrls))return void r.Vy.error("Allowed Tracing URLs should be an array");if(0!==e.allowedTracingUrls.length&&void 0===e.service)return void r.Vy.error("Service needs to be configured when tracing is enabled");const t=[];return e.allowedTracingUrls.forEach((e=>{(0,a.V)(e)?t.push({match:e,propagatorTypes:l}):(0,u.WS)(e)?t.push(e):r.Vy.warn("Allowed Tracing Urls parameters should be a string, RegExp, function, or an object. Ignoring parameter",e)})),t}(e);if(!m)return;const v=(0,o.Sz)(e);if(!v)return;const g=null!==(t=e.sessionReplaySampleRate)&&void 0!==t?t:0;return{applicationId:e.applicationId,version:e.version||void 0,actionNameAttribute:e.actionNameAttribute,sessionReplaySampleRate:g,startSessionReplayRecordingManually:void 0!==e.startSessionReplayRecordingManually?!!e.startSessionReplayRecordingManually:0===g,traceSampleRate:null!==(n=e.traceSampleRate)&&void 0!==n?n:100,rulePsr:(0,i.Et)(e.traceSampleRate)?e.traceSampleRate/100:void 0,allowedTracingUrls:m,excludedActivityUrls:null!==(c=e.excludedActivityUrls)&&void 0!==c?c:[],workerUrl:e.workerUrl,compressIntakeRequests:!!e.compressIntakeRequests,trackUserInteractions:!(null!==(d=e.trackUserInteractions)&&void 0!==d&&!d),trackViewsManually:!!e.trackViewsManually,trackResources:!(null!==(p=e.trackResources)&&void 0!==p&&!p),trackLongTasks:!(null!==(f=e.trackLongTasks)&&void 0!==f&&!f),subdomain:e.subdomain,defaultPrivacyLevel:(0,s.Rj)(o.WA,e.defaultPrivacyLevel)?e.defaultPrivacyLevel:o.WA.MASK,enablePrivacyForActionName:!!e.enablePrivacyForActionName,customerDataTelemetrySampleRate:1,traceContextInjection:(0,s.Rj)(o.uT,e.traceContextInjection)?e.traceContextInjection:o.uT.SAMPLED,plugins:e.plugins||[],trackFeatureFlagsForEvents:e.trackFeatureFlagsForEvents||[],...v}}function p(e){const t=new Set;return Array.isArray(e.allowedTracingUrls)&&e.allowedTracingUrls.length>0&&e.allowedTracingUrls.forEach((e=>{(0,a.V)(e)?l.forEach((e=>t.add(e))):"object"===(0,c.P)(e)&&Array.isArray(e.propagatorTypes)&&e.propagatorTypes.forEach((e=>t.add(e)))})),Array.from(t)}function f(e){var t;const n=(0,o.hO)(e);return{session_replay_sample_rate:e.sessionReplaySampleRate,start_session_replay_recording_manually:e.startSessionReplayRecordingManually,trace_sample_rate:e.traceSampleRate,trace_context_injection:e.traceContextInjection,action_name_attribute:e.actionNameAttribute,use_allowed_tracing_urls:Array.isArray(e.allowedTracingUrls)&&e.allowedTracingUrls.length>0,selected_tracing_propagators:p(e),default_privacy_level:e.defaultPrivacyLevel,enable_privacy_for_action_name:e.enablePrivacyForActionName,use_excluded_activity_urls:Array.isArray(e.excludedActivityUrls)&&e.excludedActivityUrls.length>0,use_worker_url:!!e.workerUrl,compress_intake_requests:e.compressIntakeRequests,track_views_manually:e.trackViewsManually,track_user_interactions:e.trackUserInteractions,track_resources:e.trackResources,track_long_task:e.trackLongTasks,plugins:null===(t=e.plugins)||void 0===t?void 0:t.map((e=>{var t;return{name:e.name,...null===(t=e.getConfigurationTelemetry)||void 0===t?void 0:t.call(e)}})),track_feature_flags_for_events:e.trackFeatureFlagsForEvents,...n}}},39042:(e,t,n)=>{function r(e){return e.nodeType===Node.TEXT_NODE}function o(e){return e.nodeType===Node.COMMENT_NODE}function i(e){return e.nodeType===Node.ELEMENT_NODE}function s(e){return i(e)&&Boolean(e.shadowRoot)}function a(e){const t=e;return!!t.host&&t.nodeType===Node.DOCUMENT_FRAGMENT_NODE&&i(t.host)}function c(e){return e.childNodes.length>0||s(e)}function u(e,t){let n=e.firstChild;for(;n;)t(n),n=n.nextSibling;s(e)&&t(e.shadowRoot)}function l(e){return a(e)?e.host:e.parentNode}n.d(t,{$4:()=>l,QU:()=>o,XS:()=>s,ir:()=>r,nS:()=>i,p_:()=>a,wI:()=>u,wR:()=>c})},39930:(e,t,n)=>{n.d(t,{dQ:()=>a});var r=n(72765),o=n(44193),i=n(78218);const s=r.AQ;function a(e,t,n,r){const a=(0,o.q)({expireDelay:s});return e.subscribe(1,(({startClocks:e})=>{a.add({},e.relative),r.resetCustomerData()})),e.subscribe(6,(({endClocks:e})=>{a.closeActive(e.relative)})),t.register(0,(({startTime:e,eventType:t})=>{if(!n.trackFeatureFlagsForEvents.concat(["view","error"]).includes(t))return;const r=a.find(e);return r&&!(0,i.RI)(r)?{type:t,feature_flags:r}:void 0})),{addFeatureFlagEvaluation:(e,t)=>{const n=a.find();n&&(n[e]=t,r.updateCustomerData(n))},stop:()=>r.stop()}}},40431:(e,t,n)=>{n.d(t,{d:()=>R});var r=n(52999),o=n(29336),i=n(47978),s=n(1356),a=n(33425),c=n(44193),u=n(92555),l=n(59371),d=n(82224),p=n(48457),f=n(2472),m=n(93001);const v=o.OY,g=100;var y=n(27328),w=n(80886);function b(){const e=window.getSelection();return!e||e.isCollapsed}function h(e){return e.target instanceof Element&&!1!==e.isPrimary}const S=3;const k='input:not([type="checkbox"]):not([type="radio"]):not([type="button"]):not([type="submit"]):not([type="reset"]):not([type="range"]),textarea,select,[contenteditable],[contenteditable] *,canvas,a[href],a[href] *';function A(e){return!(e.hasPageActivity||e.getUserActivity().input||e.getUserActivity().scroll||e.event.target.matches(k))}var T=n(27410);const _=5*o.iW;function E(e,t,n,r){const i=(0,c.q)({expireDelay:_}),s=new u.c;let a;e.subscribe(10,(()=>{i.reset()})),e.subscribe(5,E);const{stop:l}=function(e,{onPointerDown:t,onPointerUp:n}){let r,o,i={selection:!1,input:!1,scroll:!1};const s=[(0,w.q)(e,window,"pointerdown",(e=>{h(e)&&(r=b(),i={selection:!1,input:!1,scroll:!1},o=t(e))}),{capture:!0}),(0,w.q)(e,window,"selectionchange",(()=>{r&&b()||(i.selection=!0)}),{capture:!0}),(0,w.q)(e,window,"scroll",(()=>{i.scroll=!0}),{capture:!0,passive:!0}),(0,w.q)(e,window,"pointerup",(e=>{if(h(e)&&o){const t=i;n(o,e,(()=>t)),o=void 0}}),{capture:!0}),(0,w.q)(e,window,"input",(()=>{i.input=!0}),{capture:!0})];return{stop:()=>{s.forEach((e=>e.stop()))}}}(r,{onPointerDown:o=>function(e,t,n,r,o){const i=e.enablePrivacyForActionName?(0,f.PJ)(r.target,e.defaultPrivacyLevel):f.$m.ALLOW;if(i===f.$m.HIDDEN)return;const s=function(e,t,n){const r=e.target.getBoundingClientRect(),o=(0,p.ab)(e.target,n.actionNameAttribute);o&&(0,T.fL)(e.timeStamp,o);const i=(0,y.wG)(e.target,n,t);return{type:"click",target:{width:Math.round(r.width),height:Math.round(r.height),selector:o},position:{x:Math.round(e.clientX-r.left),y:Math.round(e.clientY-r.top)},name:i.name,nameSource:i.nameSource}}(r,i,e);let a=!1;return(0,d.t$)(t,n,o,e,(e=>{a=e.hadActivity}),d.kn),{clickActionBase:s,hadActivityOnPointerDown:()=>a}}(r,e,t,o,n),onPointerUp:({clickActionBase:o,hadActivityOnPointerDown:a},c,u)=>{!function(e,t,n,r,o,i,s,a,c,u,l){var p;const f=C(t,o,u,a,c);s(f);const m=null===(p=null==a?void 0:a.target)||void 0===p?void 0:p.selector;m&&(0,T.fL)(c.timeStamp,m);const{stop:v}=(0,d.t$)(t,n,r,e,(e=>{e.hadActivity&&e.end<f.startClocks.timeStamp?f.discard():e.hadActivity?f.stop(e.end):l()?f.stop(f.startClocks.timeStamp):f.stop()}),T.kf),g=t.subscribe(5,(({endClocks:e})=>{f.stop(e.timeStamp)})),y=i.subscribe((()=>{f.stop()}));f.stopObservable.subscribe((()=>{g.unsubscribe(),v(),y.unsubscribe()}))}(r,e,t,n,i,s,k,o,c,u,a)}});return{stop:()=>{E(),s.notify(),l()},actionContexts:{findActionId:e=>i.findAll(e)}};function k(e){if(!a||!a.tryAppend(e)){const t=e.clone();a=function(e,t){const n=[];let r,o=0;function i(e){e.stopObservable.subscribe(s),n.push(e),(0,m.DJ)(r),r=(0,m.wg)(a,v)}function s(){1===o&&n.every((e=>e.isStopped()))&&(o=2,t(n))}function a(){(0,m.DJ)(r),0===o&&(o=1,s())}return i(e),{tryAppend:e=>{return 0===o&&(n.length>0&&(t=n[n.length-1].event,r=e.event,!(t.target===r.target&&(s=t,c=r,Math.sqrt(Math.pow(s.clientX-c.clientX,2)+Math.pow(s.clientY-c.clientY,2))<=g)&&t.timeStamp-r.timeStamp<=v))?(a(),!1):(i(e),!0));var t,r,s,c},stop:()=>{a()}}}(e,(e=>{!function(e,t){const{isRage:n}=function(e,t){if(function(e){if(e.some((e=>e.getUserActivity().selection||e.getUserActivity().scroll)))return!1;for(let t=0;t<e.length-(S-1);t+=1)if(e[t+S-1].event.timeStamp-e[t].event.timeStamp<=o.OY)return!0;return!1}(e))return t.addFrustration("rage_click"),e.some(A)&&t.addFrustration("dead_click"),t.hasError&&t.addFrustration("error_click"),{isRage:!0};const n=e.some((e=>e.getUserActivity().selection));return e.forEach((e=>{e.hasError&&e.addFrustration("error_click"),A(e)&&!n&&e.addFrustration("dead_click")})),{isRage:!1}}(e,t);n?(e.forEach((e=>e.discard())),t.stop((0,o.nx)()),t.validate(e.map((e=>e.event)))):(t.discard(),e.forEach((e=>e.validate())))}(e,t)}))}}function E(){a&&a.stop()}}function C(e,t,n,r,i){const a=(0,s.lk)(),c=(0,o.M8)(),d=t.add(a,c.relative),p=(0,l.O)({lifeCycle:e,isChildEvent:e=>void 0!==e.action&&(Array.isArray(e.action.id)?e.action.id.includes(a):e.action.id===a)});let f,m=0;const v=[],g=new u.c;function y(e){0===m&&(f=e,m=1,f?d.close((0,o.gs)(f)):d.remove(),p.stop(),g.notify())}return{event:i,stop:y,stopObservable:g,get hasError(){return p.eventCounts.errorCount>0},get hasPageActivity(){return void 0!==f},getUserActivity:n,addFrustration:e=>{v.push(e)},startClocks:c,isStopped:()=>1===m||2===m,clone:()=>C(e,t,n,r,i),validate:t=>{if(y(),1!==m)return;const{resourceCount:n,errorCount:s,longTaskCount:u}=p.eventCounts,l={duration:f&&(0,o.vk)(c.timeStamp,f),startClocks:c,id:a,frustrationTypes:v,counts:{resourceCount:n,errorCount:s,longTaskCount:u},events:null!=t?t:[i],event:i,...r};e.notify(0,l),m=2},discard:()=>{y(),m=2}}}function R(e,t,n,o,i){e.subscribe(0,(t=>e.notify(12,x(t)))),t.register(0,(({startTime:e,eventType:t})=>{if("error"!==t&&"resource"!==t&&"long_task"!==t)return;const n=s.findActionId(e);return n?{type:t,action:{id:n}}:void 0}));let s={findActionId:r.l},a=r.l;return i.trackUserInteractions&&({actionContexts:s,stop:a}=E(e,n,o,i)),{addAction:(t,n)=>{e.notify(12,{savedCommonContext:n,...x(t)})},actionContexts:s,stop:a}}function x(e){const t=N(e)?{action:{id:e.id,loading_time:(0,a.K)((0,o.Zj)(e.duration)),frustration:{type:e.frustrationTypes},error:{count:e.counts.errorCount},long_task:{count:e.counts.longTaskCount},resource:{count:e.counts.resourceCount}},_dd:{action:{target:e.target,position:e.position,name_source:e.nameSource}}}:void 0,n=(0,i.kg)({action:{id:(0,s.lk)(),target:{name:e.name},type:e.type},date:e.startClocks.timeStamp,type:"action"},t),r=N(e)?e.duration:void 0,c=N(e)?void 0:e.context,u=N(e)?{events:e.events}:{handlingStack:e.handlingStack};return{customerContext:c,rawRumEvent:n,duration:r,startTime:e.startClocks.relative,domainContext:u}}function N(e){return"custom"!==e.type}},48457:(e,t,n)=>{n.d(t,{ab:()=>s,yF:()=>r});const r=[n(27328).rb,"data-testid","data-test","data-qa","data-cy","data-test-id","data-qa-id","data-testing","data-component","data-element","data-source-file"],o=[c,function(e){if(e.id&&!a(e.id))return`#${CSS.escape(e.id)}`}],i=[c,function(e){if("BODY"===e.tagName)return;const t=e.classList;for(let n=0;n<t.length;n+=1){const r=t[n];if(!a(r))return`${CSS.escape(e.tagName)}.${CSS.escape(r)}`}},function(e){return CSS.escape(e.tagName)}];function s(e,t){if(!e.isConnected)return;let n,r=e;for(;r&&"HTML"!==r.nodeName;){const e=l(r,o,d,t,n);if(e)return e;n=l(r,i,p,t,n)||f(u(r),n),r=r.parentElement}return n}function a(e){return/[0-9]/.test(e)}function c(e,t){if(t){const e=n(t);if(e)return e}for(const e of r){const t=n(e);if(t)return t}function n(t){if(e.hasAttribute(t))return`${CSS.escape(e.tagName)}[${t}="${CSS.escape(e.getAttribute(t))}"]`}}function u(e){let t=e.parentElement.firstElementChild,n=1;for(;t&&t!==e;)t.tagName===e.tagName&&(n+=1),t=t.nextElementSibling;return`${CSS.escape(e.tagName)}:nth-of-type(${n})`}function l(e,t,n,r,o){for(const i of t){const t=i(e,r);if(t&&n(e,t,o))return f(t,o)}}function d(e,t,n){return 1===e.ownerDocument.querySelectorAll(f(t,n)).length}function p(e,t,n){let r;if(void 0===n)r=e=>e.matches(t);else{const e=f(`${t}:scope`,n);r=t=>null!==t.querySelector(e)}let o=e.parentElement.firstElementChild;for(;o;){if(o!==e&&r(o))return!1;o=o.nextElementSibling}return!0}function f(e,t){return t?`${e}>${t}`:e}},51537:(e,t,n)=>{n.d(t,{E:()=>p});var r=n(84409),o=n(92555),i=n(80886),s=n(29336),a=n(1356),c=n(93001);const u=s.OY;function l(e,t){const n=(0,a.rx)(document.cookie,e),r=(0,c.yb)((()=>{const r=(0,a.rx)(document.cookie,e);r!==n&&t(r)}),u);return()=>{(0,c.vG)(r)}}const d="datadog-ci-visibility-test-execution-id";function p(e,t,n=function(e,t){const n=window.cookieStore?function(e){return(t,n)=>(0,i.q)(e,window.cookieStore,"change",(e=>{const r=e.changed.find((e=>e.name===t))||e.deleted.find((e=>e.name===t));r&&n(r.value)})).stop}(e):l;return new o.c((e=>n(t,(t=>e.notify(t)))))}(e,d)){var s;let a=(0,r.B9)(d)||(null===(s=window.Cypress)||void 0===s?void 0:s.env("traceId"));const c=n.subscribe((e=>{a=e}));return t.register(0,(({eventType:e})=>{if("string"==typeof a)return{type:e,session:{type:"ci_test"},ci_test:{test_execution_id:a}}})),{stop:()=>{c.unsubscribe()}}}},57522:(e,t,n)=>{n.d(t,{W:()=>c,l:()=>a});var r=n(92555),o=n(42182),i=n(47197),s=n(52999);function a(){const e=c();return new r.c((t=>{if(!e)return;const n=new e((0,o.dm)((()=>t.notify())));return n.observe(document,{attributes:!0,characterData:!0,childList:!0,subtree:!0}),()=>n.disconnect()}))}function c(){let e;const t=window;if(t.Zone&&(e=(0,i.W)(t,"MutationObserver"),t.MutationObserver&&e===t.MutationObserver)){const n=new t.MutationObserver(s.l),r=(0,i.W)(n,"originalInstance");e=r&&r.constructor}return e||(e=t.MutationObserver),e}},59110:(e,t,n)=>{n.d(t,{w:()=>o});var r=n(54991);function o(e){e.register(0,(({eventType:e})=>{const t=(0,r.aj)(),n=(0,r.X6)();if(t&&n)return{type:e,session:{type:"synthetics"},synthetics:{test_id:t,result_id:n,injected:(0,r.ao)()}}}))}},71481:(e,t,n)=>{function r(e,t,n,r,o){return{get:i=>{const s=n.findView(i),a=o.findUrl(i),c=t.findTrackedSession(i);if(c&&s&&a){const t=r.findActionId(i);return{application_id:e,session_id:c.id,user_action:t?{id:t}:void 0,view:{id:s.id,name:s.name,referrer:a.referrer,url:a.url}}}}}}n.d(t,{d:()=>r})},77705:(e,t,n)=>{n.d(t,{g1:()=>a,pB:()=>c});var r=n(92555),o=n(52999),i=n(80886);let s;function a(e){return s||(s=function(e){return new r.c((t=>{const{throttled:n}=(0,o.n)((()=>{t.notify(c())}),200);return(0,i.q)(e,window,"resize",n,{capture:!0,passive:!0}).stop}))}(e)),s}function c(){const e=window.visualViewport;return e?{width:Number(e.width*e.scale),height:Number(e.height*e.scale)}:{width:Number(window.innerWidth||0),height:Number(window.innerHeight||0)}}},81464:(e,t,n)=>{n.d(t,{s:()=>a});var r=n(72765),o=n(44193),i=n(29336);const s=r.AQ;function a(e,t,n,r){const a=(0,o.q)({expireDelay:s});let c;e.subscribe(1,(({startClocks:e})=>{const t=r.href;a.add(l({url:t,referrer:c||document.referrer}),e.relative),c=t})),e.subscribe(6,(({endClocks:e})=>{a.closeActive(e.relative)}));const u=n.subscribe((({newLocation:e})=>{const t=a.find();if(t){const n=(0,i.$S)();a.closeActive(n),a.add(l({url:e.href,referrer:t.referrer}),n)}}));function l({url:e,referrer:t}){return{url:e,referrer:t}}return t.register(0,(({startTime:e,eventType:t})=>{const{url:n,referrer:r}=a.find(e);return{type:t,view:{url:n,referrer:r}}})),{findUrl:e=>a.find(e),getAllEntries:()=>a.getAllEntries(),getDeletedEntries:()=>a.getDeletedEntries(),stop:()=>{u.unsubscribe(),a.stop()}}}},85107:(e,t,n)=>{function r(){let e;const t=window.visualViewport;return e=t?t.pageLeft-t.offsetLeft:void 0!==window.scrollX?window.scrollX:window.pageXOffset||0,Math.round(e)}function o(){let e;const t=window.visualViewport;return e=t?t.pageTop-t.offsetTop:void 0!==window.scrollY?window.scrollY:window.pageYOffset||0,Math.round(e)}n.d(t,{G:()=>r,z:()=>o})},99769:(e,t,n)=>{n.d(t,{z:()=>s});var r=n(59248),o=n(29336),i=n(5964);function s(){if((0,i.s5)(i.do.NAVIGATION)){const e=performance.getEntriesByType(i.do.NAVIGATION)[0];if(e)return e}const e=function(){const e={},t=performance.timing;for(const n in t)if((0,r.Et)(t[n])){const r=n,i=t[r];e[r]=0===i?0:(0,o.gs)(i)}return e}(),t={entryType:i.do.NAVIGATION,initiatorType:"navigation",name:window.location.href,startTime:0,duration:e.loadEventEnd,decodedBodySize:0,encodedBodySize:0,transferSize:0,workerStart:0,toJSON:()=>({...t,toJSON:void 0}),...e};return t}}}]);
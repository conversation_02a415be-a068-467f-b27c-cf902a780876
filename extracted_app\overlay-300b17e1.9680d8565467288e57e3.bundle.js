"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8107,9144],{"shared/cheats/resources/elements/number-input":(t,i,e)=>{e.r(i),e.d(i,{NumberInput:()=>a});var n=e(15215),o=e("aurelia-framework");let a=class{constructor(){}matchPrecision(t,i){const e=t.toString(),n=e.indexOf(".");return parseFloat(i.toFixed(-1===n?0:e.length-n-1))}get canDecrease(){return void 0===this.internalValue||parseFloat(this.internalValue.toString())-this.step>=this.min}get canIncrease(){return void 0===this.internalValue||parseFloat(this.internalValue.toString())+this.step<=this.max}add(){this.#t(+this.step)}subtract(){this.#t(-this.step)}#t(t){let i=this.internalValue?parseFloat(this.internalValue.toString()):0;i+=t,i=this.matchPrecision(this.step,i),i<this.min&&(i=this.min),i>this.max&&(i=this.max),this.internalValue=i}setValue(){this.#i()}internalValueChanged(){this.button||this.internalValue===this.value||this.#i()}valueChanged(t){"string"==typeof t&&t.length>0?t=parseFloat(t):"number"!=typeof t&&(t=Math.min(this.max,Math.max(0,this.min))),this.internalValue=t}#i(){this.internalValue<this.min&&(this.internalValue=this.min,!this.button)||this.internalValue>this.max&&(this.internalValue=this.max,!this.button)||(this.change?this.change({value:this.internalValue}):this.value=this.internalValue)}};(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Number)],a.prototype,"value",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Number)],a.prototype,"min",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Number)],a.prototype,"max",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Number)],a.prototype,"step",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Boolean)],a.prototype,"disabled",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Boolean)],a.prototype,"button",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Function)],a.prototype,"change",void 0),(0,n.Cg)([o.observable,(0,n.Sn)("design:type",Number)],a.prototype,"internalValue",void 0),(0,n.Cg)([(0,o.computedFrom)("internalValue","step","min"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],a.prototype,"canDecrease",null),(0,n.Cg)([(0,o.computedFrom)("internalValue","step","max"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],a.prototype,"canIncrease",null),a=(0,n.Cg)([(0,o.autoinject)(),(0,n.Sn)("design:paramtypes",[])],a)},"shared/cheats/resources/elements/number-input.html":(t,i,e)=>{e.r(i),e.d(i,{default:()=>n});const n='<template class="${disabled ? \'disabled\' : \'\'}"> <require from="./number-input.scss"></require> <div class="input-wrapper"> <button click.delegate="subtract()" class="update" disabled.bind="disabled || !canDecrease" haptic-touch> <i class="subtract-icon"></i> </button> <input type="number" value.bind="internalValue | i18nNativeNumber:{useGrouping: false}" disabled.bind="disabled"> <button click.delegate="add()" class="update" disabled.bind="disabled || !canIncrease" haptic-touch> <i class="add-icon"></i> </button> </div> <button if.bind="button" class="set" click.delegate="setValue()" disabled.bind="disabled" haptic-touch> <i class="apply-icon"></i> </button> </template> '},"shared/cheats/resources/elements/number-input.scss":(t,i,e)=>{e.r(i),e.d(i,{default:()=>c});var n=e(31601),o=e.n(n),a=e(76314),r=e.n(a),s=e(4417),p=e.n(s),l=new URL(e(83959),e.b),d=r()(o()),u=p()(l);d.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${u}) format("woff2")}.material-symbols-outlined,number-input .apply-icon,number-input .add-icon,number-input .subtract-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}number-input{position:relative;display:flex;align-content:center;height:28px}number-input.disabled{position:relative;z-index:0}number-input.disabled:before{content:"";position:absolute;left:0;top:0;right:0;bottom:0;z-index:999;opacity:0;cursor:not-allowed}number-input.disabled *{pointer-events:none}number-input .input-wrapper{display:flex;flex-direction:row;background-color:rgba(255,255,255,.05);border-radius:8px;gap:4px;padding:2px;flex-grow:1}number-input input{cursor:text;display:flex;flex:1;background:none;line-height:12px;border:0;border-radius:6px;padding:0 5px;color:#fff;text-align:center;outline:none;min-width:0;width:100%;height:100%;font-size:12px}number-input input:hover,number-input input:focus,number-input input:active{background-color:rgba(255,255,255,.1)}number-input input::-webkit-inner-spin-button{-webkit-appearance:none;opacity:0}number-input .apply-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}number-input .apply-icon:before{font-family:inherit;content:"check"}number-input .add-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}number-input .add-icon:before{font-family:inherit;content:"add"}number-input .subtract-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}number-input .subtract-icon:before{font-family:inherit;content:"remove"}number-input button{display:flex;border:none;border-radius:8px;min-height:28px;min-width:28px;padding:0;background-color:var(--mods-theme-color, var(--theme--highlight-darker));align-items:center}number-input button.update{min-height:24px;min-width:24px;background:none}number-input button.update i{color:rgba(255,255,255,.9);font-size:16px;margin:0 auto}number-input button.update:hover{background-color:rgba(255,255,255,.1)}number-input button.set{margin-left:6px;background-color:var(--mods-theme-color, var(--theme--highlight-darker))}number-input button.set:hover{opacity:.8}number-input button.set i{color:rgba(255,255,255,.9);font-size:16px;margin:0 auto}`,""]);const c=d},"shared/cheats/resources/elements/pin-mod-icon":(t,i,e)=>{e.r(i),e.d(i,{PinModIcon:()=>a});var n=e(15215),o=e("aurelia-framework");let a=class{constructor(){this.dialogDisabled=!1,this.canUsePinnedMods=!1,this.placement="bottom-start"}get isActiveAutoPin(){return this.isAutoPin&&this.active}get disableProCta(){return this.canUsePinnedMods||this.dialogDisabled||this.isActiveAutoPin}get showTooltip(){return this.hasClickedModPin&&!this.canUsePinnedMods&&!this.isActiveAutoPin}};(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Object)],a.prototype,"tooltipId",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Boolean)],a.prototype,"active",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Boolean)],a.prototype,"dialogDisabled",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Boolean)],a.prototype,"canUsePinnedMods",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Boolean)],a.prototype,"hasClickedModPin",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Boolean)],a.prototype,"isAutoPin",void 0),(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",Function)],a.prototype,"handlePinClick",void 0),(0,n.Cg)([(0,o.computedFrom)("isAutoPin","active"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],a.prototype,"isActiveAutoPin",null),(0,n.Cg)([(0,o.computedFrom)("canUsePinnedMods","dialogDisabled","isActiveAutoPin"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],a.prototype,"disableProCta",null),(0,n.Cg)([(0,o.computedFrom)("hasClickedModPin","canUsePinnedMods","isActiveAutoPin"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],a.prototype,"showTooltip",null),a=(0,n.Cg)([(0,o.autoinject)()],a)},"shared/cheats/resources/elements/pin-mod-icon.html":(t,i,e)=>{e.r(i),e.d(i,{default:()=>n});const n='<template pro-cta="trigger: pin_mod_icon; disabled.bind: disableProCta; feature: pin_mods;" class="${active ? \'active\' : \'\'}"> <require from="./pin-mod-icon.scss"></require> <require from="../../../resources/elements/pro-badge"></require> <div class="pin"> <div data-tooltip-trigger-for.bind="tooltipId" click.delegate="handlePinClick(cheat)"> <i class="pin-icon"></i> </div> <wm-tooltip-small-promo if.bind="showTooltip" tooltip-id.bind="tooltipId" placement.bind="placement"> <span slot="title" class="pin-mods-tooltip-pro-promo-title"> ${\'pinned_mods_tooltip.unlock_pinned_mods\' | i18n}<pro-badge class="small pro-badge"></pro-badge> </span> <span slot="cta-copy" class="pin-mods-tooltip-pro-promo-cta" pro-cta="trigger: pin_mod_icon; disabled.bind: disableProCta; feature: pin_mods;">${\'pro_general.join_now\' | i18n} <i class="arrow-icon"></i></span> </wm-tooltip-small-promo> </div> </template> '},"shared/cheats/resources/elements/pin-mod-icon.scss":(t,i,e)=>{e.r(i),e.d(i,{default:()=>c});var n=e(31601),o=e.n(n),a=e(76314),r=e.n(a),s=e(4417),p=e.n(s),l=new URL(e(83959),e.b),d=r()(o()),u=p()(l);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${u}) format("woff2")}.material-symbols-outlined,pin-mod-icon .pin i.pin-icon,.pin-mods-tooltip-pro-promo-cta i.arrow-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}pin-mod-icon .pin{min-width:32px}pin-mod-icon .pin,pin-mod-icon .pin *{cursor:pointer}pin-mod-icon .pin i.pin-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:rgba(255,255,255,.15);border-radius:8px;padding:4px;transition:all .15s}pin-mod-icon .pin i.pin-icon:before{font-family:inherit;content:"keep"}pin-mod-icon .pin i.pin-icon:hover{color:rgba(255,255,255,.25);background-color:rgba(255,255,255,.05)}pin-mod-icon.active i{color:rgba(255,255,255,.25)}.pin-mods-tooltip-pro-promo-title,.pin-mods-tooltip-pro-promo-cta{display:flex;align-items:center;gap:4px}.pin-mods-tooltip-pro-promo-cta{gap:2px;height:16px}.pin-mods-tooltip-pro-promo-cta i.arrow-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:inherit !important;font-size:14px;width:16px;height:16px;vertical-align:middle}.pin-mods-tooltip-pro-promo-cta i.arrow-icon:before{font-family:inherit;content:"arrow_forward"}`,""]);const c=d}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6217],{"cheats/resources/elements/game-feed-item":(e,t,a)=>{a.r(t),a.d(t,{GameFeedItem:()=>l});var i=a(15215),o=a("aurelia-framework"),r=a(20770),n=a(48100),m=a(33511);let l=class{#e;#t;constructor(e){this.largeThumbnails=!1,this.searchResult=!1,this.metadataPosition="below-card",this.thumbnailLoaded=!1,this.#t=e}itemChanged(){this.#e&&this.#e?.unsubscribe(),this.#e=this.#t.state.pipe((0,n.T)((e=>!0===e.favoriteTitles[this.item.titleId])),(0,m.M)((e=>this.isFavorite=e))).subscribe()}detached(){this.#e?.unsubscribe()}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Object)],l.prototype,"item",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],l.prototype,"location",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],l.prototype,"metadataTypes",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],l.prototype,"previousRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],l.prototype,"parentRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Boolean)],l.prototype,"largeThumbnails",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Boolean)],l.prototype,"searchResult",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],l.prototype,"metadataPosition",void 0),l=(0,i.Cg)([(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.il])],l)},"cheats/resources/elements/game-feed-item.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var i=a(14385),o=a.n(i),r=new URL(a(84222),a.b),n=new URL(a(5261),a.b);const m='<template class="${largeThumbnails ? \'large-thumbnails\' : \'\'}"> <require from="./game-feed-item.scss"></require> <require from="../../../shared/cheats/resources/value-converters/important-genres"></require> <require from="../../../resources/elements/favorite-button"></require> <require from="../../../resources/elements/title-thumbnail"></require> <a class="game-feed-item ${metadataPosition === \'on-card\' ? \'game-feed-item--metadata-on-card\' : \'\'} ${isFavorite ? \'game-feed-item--favorite\' : \'\'}" route-href="route.bind: \'title\'; params.bind: {titleId: item.titleId, gameId: item.gameId, previousRoute: previousRoute, parentRoute: parentRoute}" title-link="value.bind: location; title-id.bind: item.titleId; game-id.bind: item.gameId; search-result.bind: searchResult;"> <div class="thumbnail-wrapper ${!item.isAvailable ? \'unavailable\' : \'\'}"> <title-thumbnail class="thumbnail" src.bind="item.titleThumbnail" width="${largeThumbnails ? 460 : 260}"></title-thumbnail> <div class="badge-backdrop"> <img class="choice-badge" if.bind="item.isChoice" title.bind="\'game_feed.community_choice_award_winner\' | i18n" src="'+o()(r)+'"> </div> <div class="badge-backdrop"> <span class="unavailable-badge" if.bind="!item.isAvailable" title.bind="\'game_feed.no_cheats_available\' | i18n"> <inline-svg src="'+o()(n)+'"></inline-svg> </span> </div> </div> <div class="title-row"> <span class="title-name">${item.titleName}</span> <favorite-button if.bind="item.isAvailable || item.isInstalled" title-id.bind="item.titleId"></favorite-button> </div> </a> </template> '},"cheats/resources/elements/game-feed-item.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r)()(o());n.push([e.id,'game-feed-item .game-feed-item{position:relative}game-feed-item .game-feed-item:nth-child(1n+0) img{animation-delay:0.1s}game-feed-item .game-feed-item:nth-child(2n+0) img{animation-delay:0.2s}game-feed-item .game-feed-item:nth-child(3n+0) img{animation-delay:0.3s}game-feed-item .game-feed-item:nth-child(4n+0) img{animation-delay:0.4s}game-feed-item .game-feed-item:nth-child(5n+0) img{animation-delay:0.5s}game-feed-item .game-feed-item:nth-child(6n+0) img{animation-delay:0.6s}game-feed-item .game-feed-item:nth-child(7n+0) img{animation-delay:0.7s}game-feed-item .game-feed-item:nth-child(8n+0) img{animation-delay:0.8s}game-feed-item .game-feed-item:nth-child(9n+0) img{animation-delay:0.9s}game-feed-item .game-feed-item .thumbnail{border-radius:12px;overflow:hidden;width:100%;height:100%;aspect-ratio:200/95}game-feed-item .game-feed-item .thumbnail-wrapper{position:relative;overflow:hidden;display:flex}game-feed-item .game-feed-item .thumbnail-wrapper .choice-badge{width:16px;height:16px}game-feed-item .game-feed-item .thumbnail-wrapper .badge-backdrop{overflow:hidden;display:flex;align-items:center;justify-content:center;position:absolute;top:0;right:0;z-index:1;border-radius:0px 12px 0px 6px;width:20px;height:20px;padding:2px}game-feed-item .game-feed-item .thumbnail-wrapper .badge-backdrop:has(.choice-badge){background-color:#acff35}game-feed-item .game-feed-item .thumbnail-wrapper .badge-backdrop:has(.unavailable-badge){background-color:var(--theme--background);width:24px;height:24px;padding:4px}game-feed-item .game-feed-item favorite-button:not(:hover) button{background:none}game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button{opacity:1}game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{filter:drop-shadow(0px 0px 4px var(--theme--background))}.theme-default game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}.theme-purple-pro game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}.theme-green-pro game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}.theme-orange-pro game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}.theme-pro game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}game-feed-item .game-feed-item--metadata-on-card .title-row{position:absolute;bottom:0;padding:8px 4px 4px 8px;z-index:2}.theme-default game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}.theme-purple-pro game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}.theme-green-pro game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}.theme-orange-pro game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}.theme-pro game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}game-feed-item .game-feed-item--metadata-on-card .title-row .title-name,game-feed-item .game-feed-item--metadata-on-card .title-row favorite-button{opacity:0;transition:opacity .2s}game-feed-item .game-feed-item--metadata-on-card::after{content:"";position:absolute;top:0;bottom:0;width:100%;height:100%;opacity:0;transition:opacity .2s;background:linear-gradient(180deg, rgba(13, 15, 18, 0) 0%, rgba(13, 15, 18, 0.7) 100%)}game-feed-item .game-feed-item--metadata-on-card:hover::after{opacity:1}game-feed-item .game-feed-item--metadata-on-card:hover .title-row .title-name,game-feed-item .game-feed-item--metadata-on-card:hover .title-row favorite-button{opacity:1}game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .thumbnail{border:2px solid rgba(0,0,0,0);transition:border .15s}.theme-default game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-purple-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-green-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-orange-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .thumbnail{border:2px solid var(--theme--highlight)}game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row{margin-top:12px;padding:2px}game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{transition:color .2s}.theme-default game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-default game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-purple-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-purple-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-green-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-green-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-orange-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-orange-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}game-feed-item .game-feed-item .title-row{display:flex;justify-content:space-between;align-items:center;width:100%;gap:10px}game-feed-item .game-feed-item .title-row .title-name{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}',""]);const m=n},"cheats/resources/elements/game-search-input":(e,t,a)=>{a.r(t),a.d(t,{GameSearchInput:()=>n});var i=a(15215),o=a("aurelia-framework"),r=a(18776);let n=class{#a;constructor(e){this.searchTerms="",this.onClear=()=>{},this.onReset=()=>{},this.onSearch=()=>{},this.#a=e}bind(){this.#a.currentInstruction.queryParams.search&&(this.searchTerms=this.#a.currentInstruction.queryParams.search)}attached(){this.search()}searchTermsChanged(){this.onReset(),this.searchTerms||this.clear(!1)}search(){this.searchTerms&&(this.onSearch(),this.#i())}clear(e=!0){this.searchTerms="",e&&this.inputEl?.focus(),this.#i(),this.onClear()}#i(){this.#a.navigateToRoute(this.#a.currentInstruction.config.name??"",{...this.#a.currentInstruction.params,search:this.searchTerms},{trigger:!1,replace:!0})}};(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.fromView}),(0,i.Sn)("design:type",String)],n.prototype,"searchTerms",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],n.prototype,"searching",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Object)],n.prototype,"onClear",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Object)],n.prototype,"onReset",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Object)],n.prototype,"onSearch",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",String)],n.prototype,"placeholderKey",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.twoWay}),(0,i.Sn)("design:type",Boolean)],n.prototype,"disabled",void 0),n=(0,i.Cg)([(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.Ix])],n)},"cheats/resources/elements/game-search-input.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template> <require from="./game-search-input.scss"></require> <require from="../../../resources/elements/search-input"></require> <search-input placeholder-key="game_search_input.search" button-text-key="game_search_input.go" search-terms.bind="searchTerms" searching.bind="searching" disabled.bind="disabled" on-clear.call="onClear()" on-search.call="onSearch()"></search-input> </template> '},"cheats/resources/elements/game-search-input.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r),m=a(4417),l=a.n(m),d=new URL(a(83959),a.b),s=n()(o()),g=l()(d);s.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}game-search-input form{display:inline-flex;align-items:center}game-search-input .input-wrapper{position:relative;width:360px}game-search-input input{width:100%;min-width:40px;height:40px;border:0;border-radius:28px;padding:8px 8px 8px 36px;background:rgba(255,255,255,.1);transition:background-color .15s}.theme-default game-search-input input{color:rgba(255,255,255,.8)}.theme-purple-pro game-search-input input{color:rgba(255,255,255,.8)}.theme-green-pro game-search-input input{color:rgba(255,255,255,.8)}.theme-orange-pro game-search-input input{color:rgba(255,255,255,.8)}.theme-pro game-search-input input{color:rgba(255,255,255,.8)}game-search-input input,game-search-input input::placeholder{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px}.theme-default game-search-input input,.theme-default game-search-input input::placeholder{color:rgba(255,255,255,.8)}.theme-purple-pro game-search-input input,.theme-purple-pro game-search-input input::placeholder{color:rgba(255,255,255,.8)}.theme-green-pro game-search-input input,.theme-green-pro game-search-input input::placeholder{color:rgba(255,255,255,.8)}.theme-orange-pro game-search-input input,.theme-orange-pro game-search-input input::placeholder{color:rgba(255,255,255,.8)}.theme-pro game-search-input input,.theme-pro game-search-input input::placeholder{color:rgba(255,255,255,.8)}game-search-input input:disabled{opacity:.5}game-search-input .clear-button{position:absolute;right:12px;top:16px;background:rgba(0,0,0,0);border:0;padding:0;opacity:.2;transition:opacity .15s;display:inline-flex}game-search-input .clear-button:hover{opacity:1}game-search-input .search-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;height:40px;box-shadow:none;line-height:24px;font-weight:700;color:#000;border-radius:56px;padding:10px 16px;background:#fff;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;margin-left:10px}game-search-input .search-button,game-search-input .search-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) game-search-input .search-button{border:1px solid #fff}}game-search-input .search-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}game-search-input .search-button>*:first-child{padding-left:0}game-search-input .search-button>*:last-child{padding-right:0}game-search-input .search-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) game-search-input .search-button svg *{fill:CanvasText}}game-search-input .search-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) game-search-input .search-button svg{opacity:1}}game-search-input .search-button img{height:50%}game-search-input .search-button:disabled{opacity:.3}game-search-input .search-button:disabled,game-search-input .search-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){game-search-input .search-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}game-search-input .search-button:not(:disabled):hover svg{opacity:1}}game-search-input .search-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}game-search-input .search-button:hover{background:rgba(255,255,255,.8) !important;color:rgba(0,0,0,.8) !important}`,""]);const f=s},"cheats/resources/elements/game-selector":(e,t,a)=>{a.r(t),a.d(t,{GameSelector:()=>p});var i=a(15215),o=a("aurelia-framework"),r=a(20770),n=a(79810),m=a(35030),l=a(24008),d=a(54995),s=a(70236),g=a("shared/utility/resources/value-converters/platform"),f=a(48881);let p=class{#o;#t;#r;#n;constructor(e,t,a,i){this.showHeader=!0,this.showCustomExeInfo=!1,this.#o=e,this.#t=t,this.#r=a,this.#n=i}async attached(){try{this.model=await this.#o.watch(this.titleId)}catch{}}detached(){this.#o.unwatch(this.titleId)}selectGame(e){this.#t.dispatch(f.IH,this.titleId,{selectedGameId:e.id}),this.open=!1}platformName(e){return this.#r.toView(e.platformId)}isInstallable(e){return this.games[e.id]?.purchaseUris?.length>0}installGame(e,t){const a=this.games[e.id];t.preventDefault(),e&&this.#n.installGame(a,"game_selector")}get showCustomInstallationSelector(){const e=this.titlePreferences[this.titleId]?.selectedGameId;if(e){const t=this.model?.games.find((t=>t.id===e)),a=t?.flags??0,i=(0,s.Lt)(a,l.rT.Available),o=(0,s.Lt)(a,l.rT.Queued),r=(0,s.Lt)(this.account.flags,64);return i||o&&r}return!1}};(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",String)],p.prototype,"titleId",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],p.prototype,"showHeader",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],p.prototype,"showCustomExeInfo",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.twoWay}),(0,i.Sn)("design:type",Boolean)],p.prototype,"open",void 0),(0,i.Cg)([(0,o.computedFrom)("model","titlePreferences","titleId"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"showCustomInstallationSelector",null),p=(0,i.Cg)([(0,o.autoinject)(),(0,d.m6)({selectors:{account:(0,d.$t)((e=>e.account)),titlePreferences:(0,d.$t)((e=>e.titlePreferences)),games:(0,d.$t)((e=>e.catalog?.games))}}),(0,i.Sn)("design:paramtypes",[m.U,r.il,g.PlatformNameValueConverter,n.r])],p)},"cheats/resources/elements/game-selector-menu":(e,t,a)=>{a.r(t),a.d(t,{GameSelectorMenu:()=>r});var i=a(15215),o=a("aurelia-framework");class r{constructor(){this.showCustomExeInfo=!1,this.showHeader=!1}}(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",String)],r.prototype,"titleId",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],r.prototype,"showCustomExeInfo",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],r.prototype,"showHeader",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.twoWay}),(0,i.Sn)("design:type",Boolean)],r.prototype,"open",void 0)},"cheats/resources/elements/game-selector-menu.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template class="au-animate"> <require from="./game-selector-menu.scss"></require> <require from="./game-selector"></require> <div class="container"> <game-selector title-id.bind="titleId" show-header.bind="showHeader" show-custom-exe-info.bind="showCustomExeInfo" open.bind="open"></game-selector> </div> </template> '},"cheats/resources/elements/game-selector-menu.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r),m=a(4417),l=a.n(m),d=new URL(a(83959),a.b),s=n()(o()),g=l()(d);s.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}game-selector-menu .container{display:flex;flex-direction:column;justify-content:flex-start;padding:8px;position:relative;width:280px;background:rgba(128,128,128,.1);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:16px}`,""]);const f=s},"cheats/resources/elements/game-selector.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template class="au-animate"> <require from="./game-selector.scss"></require> <require from="./custom-installation-selector"></require> <nav> <header if.bind="showHeader"> <i>sports_esports</i> <span class="label">${\'game_selector.select_game_source\' | i18n}</span> </header> <button repeat.for="game of model.games" click.delegate="selectGame(game)" class="${titlePreferences[titleId].selectedGameId === game.id ? \'active\' : \'\'}"> <i> ${titlePreferences[titleId].selectedGameId === game.id ? \'check\' : \'\'} </i> <i><inline-svg src.bind="game.platformId | platformIconSvg"></inline-svg></i> <span class="label"> ${game.platformId | platformName} <template if.bind="game.edition">${game.edition}</template> </span> <span class="badge" if.bind="game.gameInstalled && !game.customGameInstalled">${\'game_selector.installed\' | i18n}</span> <span class="badge" if.bind="game.gameInstalled && game.customGameInstalled">${\'game_selector.added\' | i18n}</span> <a href="#" class="install-link" click.delegate="installGame(game, $event)" if.bind="!game.gameInstalled && isInstallable(game)"> <div class="inner"> <span class="label">${\'game_selector.view_on_$platform\' | i18n:{platform: platformName(game)}}</span> <i></i> </div> </a> </button> </nav> <template if.bind="showCustomInstallationSelector"> <hr> <custom-installation-selector game-id.bind="titlePreferences[titleId].selectedGameId" show-custom-exe-info.bind="showCustomExeInfo" open.bind="open"></custom-installation-selector> </template> </template> '},"cheats/resources/elements/game-selector.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r),m=a(4417),l=a.n(m),d=new URL(a(83959),a.b),s=n()(o()),g=l()(d);s.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,game-selector>nav button i,game-selector>nav header i,game-selector>nav button .install-link .inner i,game-selector>nav header .install-link .inner i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}game-selector>nav{display:flex;flex-direction:column;gap:1px}game-selector>nav header{pointer-events:none}game-selector>nav header .label{font-weight:700;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.8));--menu__item__label--color: #fff}game-selector>nav button,game-selector>nav header{display:flex;align-items:center;gap:8px;padding:8px;background:rgba(0,0,0,0);border:none;transition:background-color .15s;border-radius:8px;text-align:left}game-selector>nav button:hover,game-selector>nav header:hover{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}game-selector>nav button i,game-selector>nav header i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;width:20px;height:20px;display:inline-flex;align-items:center;justify-content:center;color:var(--menu__item__icon--color, rgba(255, 255, 255, 0.6));transition:color .15s}game-selector>nav button .label,game-selector>nav header .label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.6));padding:2px 0;transition:color .15s;font-size:14px;line-height:20px}game-selector>nav button .badge,game-selector>nav header .badge{font-weight:700;flex:0 0 auto;font-size:10px;line-height:16px;text-transform:uppercase;color:var(--menu__item__badge--color, rgba(255, 255, 255, 0.8));transition:color .15s;background:var(--menu__item__badge--background-color, rgba(255, 255, 255, 0.1));padding:0 4px;border-radius:4px}game-selector>nav button .install-link,game-selector>nav header .install-link{display:inline-flex;margin:-1.5px 0}game-selector>nav button .install-link .inner,game-selector>nav header .install-link .inner{display:inline-flex;align-items:center;background:rgba(255,255,255,.15);border-radius:8px}game-selector>nav button .install-link .inner i,game-selector>nav header .install-link .inner i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;width:28px;height:28px;display:inline-flex;align-items:center;justify-content:center;color:rgba(255,255,255,.8);transition:color .15s}game-selector>nav button .install-link .inner i:before,game-selector>nav header .install-link .inner i:before{font-family:inherit;content:"arrow_outward"}game-selector>nav button .install-link .inner .label,game-selector>nav header .install-link .inner .label{font-weight:700;flex:0 0 auto;font-size:10px;text-transform:uppercase;color:var(--menu__item__tag--color, rgba(255, 255, 255, 0.8));transition:color .15s;display:inline-flex;align-items:center;gap:4px;max-width:0;opacity:0;overflow:hidden;padding-left:0;transition:opacity .15s,max-width .15s,padding .15s}game-selector>nav button .install-link:hover .inner .label,game-selector>nav button .install-link:hover .inner i,game-selector>nav header .install-link:hover .inner .label,game-selector>nav header .install-link:hover .inner i{color:#fff}game-selector>nav button .install-link:hover .inner .label,game-selector>nav header .install-link:hover .inner .label{opacity:1;max-width:200px;padding-left:6px}game-selector>nav button.active,game-selector>nav header.active{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}game-selector>hr{margin:12px -8px;padding:0;border:none;border-top:1px solid rgba(255,255,255,.15)}`,""]);const f=s},"cheats/resources/elements/grid-game-feed":(e,t,a)=>{a.r(t),a.d(t,{GridGameFeed:()=>n});var i=a(15215),o=a("aurelia-framework");const r="--grid-game-feed--max-columns";let n=class{#m;constructor(e){this.#m=e}attached(){this.maxRows?this.#m.style.setProperty(r,this.maxRows.toString()):this.#m.style.removeProperty(r)}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],n.prototype,"items",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],n.prototype,"location",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],n.prototype,"previousRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],n.prototype,"metadataTypes",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Number)],n.prototype,"maxRows",void 0),n=(0,i.Cg)([(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[Element])],n)},"cheats/resources/elements/grid-game-feed.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template> <require from="./grid-game-feed.scss"></require> <require from="./horizontal-game-feed-item"></require> <div class="wrapper"> <horizontal-game-feed-item repeat.for="item of items" item.bind="item" location.bind="location" metadata-types.bind="metadataTypes" previous-route.bind="previousRoute" layout.bind="grid"></horizontal-game-feed-item> </div> </template> '},"cheats/resources/elements/grid-game-feed.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r)()(o());n.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}horizontal-game-feed-item{position:relative;z-index:0}horizontal-game-feed-item .item{--game__row--color: rgba(255, 255, 255, 0.7);--game__meta--color: rgba(255, 255, 255, 0.4);--game__thumbnail--border-color: transparent;display:flex;flex-direction:row;display:flex;flex-direction:row;align-items:flex-start;padding:10px;overflow:visible}horizontal-game-feed-item .item,horizontal-game-feed-item .item *{cursor:pointer}horizontal-game-feed-item .item i{display:inline-block;width:15px;height:15px;display:inline-flex;justify-content:center;align-items:center}horizontal-game-feed-item .item i:after{content:"";display:block;clear:both}horizontal-game-feed-item .item i svg{float:left}horizontal-game-feed-item .item i svg{max-width:15px;max-height:15px}horizontal-game-feed-item .item i svg *{color:#fff}horizontal-game-feed-item .item i svg *{fill:rgba(255,255,255,.4);transition:fill .15s}horizontal-game-feed-item .item>*:nth-child(1){margin-right:20px;flex:0 0 auto}horizontal-game-feed-item .item>*:nth-child(2){display:flex;flex-direction:column;flex:1 1 auto;position:relative;padding:5px 0;overflow:hidden}horizontal-game-feed-item .item:hover{--game__row--color: #fff;--game__meta--color: rgba(255, 255, 255, 0.6);--game__thumbnail--border-color: var(--theme--highlight)}horizontal-game-feed-item .item:hover i svg *{fill:rgba(255,255,255,.6)}horizontal-game-feed-item .item,horizontal-game-feed-item .item *{text-decoration:none}horizontal-game-feed-item .item .thumbnail{width:var(--game__thumbnail--width);height:var(--game__thumbnail--height);border-radius:var(--game__thumbnail--border-radius);border:1px solid var(--game__thumbnail--border-color);transition:border-color .15s;display:inline-block;background:rgba(0,0,0,.1);width:114px;height:53px;border-radius:5px}horizontal-game-feed-item .item .info{padding:0}horizontal-game-feed-item .item .info .row{font-size:14px;line-height:21px;line-height:19px;font-weight:500;flex:0 0 auto;display:flex;align-items:center;color:var(--game__row--color);transition:color .15s;overflow:hidden}horizontal-game-feed-item .item .info .row>*+*{margin-left:8px}horizontal-game-feed-item .item .info .row>*>*+*{margin-left:10px}horizontal-game-feed-item .item .info .row>i:last-of-type:not(:first-of-type){margin-left:18px}horizontal-game-feed-item .item .info .row b{font-weight:700}horizontal-game-feed-item .item .info .row>*{flex:0 1 auto;display:flex;align-items:center;min-width:0}horizontal-game-feed-item .item .info .row+.row{margin-top:12px}horizontal-game-feed-item .item .info .row .ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}horizontal-game-feed-item .item .info .row .meta{font-size:12px;line-height:18px;font-weight:500;color:var(--game__meta--color);transition:color .15s;line-height:17px}horizontal-game-feed-item .item .info .row .fill{flex:1;overflow:hidden}horizontal-game-feed-item .item .info .row .tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;background-color:rgba(0,0,0,0);color:rgba(255,255,255,.5);border:1px solid rgba(255,255,255,.4);line-height:11px;text-transform:uppercase;font-size:11px;font-weight:bold;padding:1.5px 4px}horizontal-game-feed-item .item .info .row .avatar{width:16px;height:16px;border-radius:50%}horizontal-game-feed-item .item .info .row .genres>*+*{margin:0}horizontal-game-feed-item .item .info .row .genres>*+*:before{content:", "}horizontal-game-feed-item .item:not(:hover) .info favorite-button{visibility:hidden;width:0;margin-left:0}horizontal-game-feed-item .item:hover:before{box-shadow:0 0 5px rgba(var(--theme--background--rgb), 0.5);position:relative;content:"";background:var(--theme--secondary-background);border-radius:5px;position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1;pointer-events:none}grid-game-feed{--grid-game-feed--max-columns: 1;display:block;padding:10px;background:rgba(255,255,255,.04);border-radius:10px}grid-game-feed .wrapper{display:grid;justify-content:start;grid-template-columns:repeat(auto-fit, minmax(350px, 1fr));width:100%;max-height:calc(73px*var(--grid-game-feed--max-columns));overflow:hidden}',""]);const m=n},"cheats/resources/elements/horizontal-game-feed":(e,t,a)=>{a.r(t),a.d(t,{HorizontalGameFeed:()=>r});var i=a(15215),o=a("aurelia-framework");let r=class{constructor(){this.largeThumbnails=!1}itemsChanged(){this.horizontalScrollContainer?.reset()}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],r.prototype,"items",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],r.prototype,"metadataTypes",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"location",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"previousRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"parentRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Boolean)],r.prototype,"largeThumbnails",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"metadataPosition",void 0),r=(0,i.Cg)([(0,o.autoinject)()],r)},"cheats/resources/elements/horizontal-game-feed-item":(e,t,a)=>{a.r(t),a.d(t,{HorizontalGameFeedItem:()=>r});var i=a(15215),o=a("aurelia-framework");class r{constructor(){this.largeThumbnails=!1}}(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Object)],r.prototype,"item",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"location",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],r.prototype,"metadataTypes",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"previousRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Boolean)],r.prototype,"largeThumbnails",void 0)},"cheats/resources/elements/horizontal-game-feed-item.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var i=a(14385),o=a.n(i),r=new URL(a(57149),a.b),n=new URL(a(85928),a.b),m=o()(r);const l='<template> <require from="./horizontal-game-feed-item.scss"></require> <require from="../../../shared/cheats/resources/value-converters/important-genres"></require> <require from="../../../resources/elements/favorite-button"></require> <require from="../../../resources/elements/title-thumbnail"></require> <a class="item" route-href="route.bind: \'title\'; params.bind: {titleId: item.titleId, gameId: item.gameId, previousRoute: previousRoute}" title-link="value.bind: location; title-id.bind: item.titleId; game-id.bind: item.gameId;"> <title-thumbnail class="thumbnail" src.bind="item.titleThumbnail" width="120"></title-thumbnail> <div class="info"> <div class="row" title.bind="item.titleName"> <b class="fill" title.bind="item.titleName"> <i if.bind="metadataTypes.includes(\'platform-icons\')" repeat.for="platformId of item.platformIds" class="platform-icon" title.bind="platformId | platformName"> <inline-svg src.bind="platformId | platformIconSvg"></inline-svg> </i> <span class="ellipsis">${item.titleName}<template if.bind="item.gameEdition">&nbsp;(${item.gameEdition})</template></span> </b> <favorite-button if.bind="item.isAvailable" title-id.bind="item.titleId"></favorite-button> </div> <div class="row"> <span if.bind="metadataTypes.includes(\'status-badges\') && (item.isNew || item.isUpdated)"> <span if.bind="item.isNew" class="tag">${\'game_feed.new\' | i18n}</span> <span if.bind="item.isUpdated" class="tag">${\'game_feed.updated\' | i18n}</span> </span> <span if.bind="metadataTypes.includes(\'genres\')"> <span class="meta genres ellipsis"> <span repeat.for="genre of item.genres | importantGenres:2">${`genres.${genre}` | i18n}</span> </span> </span> <template if.bind="metadataTypes.includes(\'updated-at\') && item.updatedAt"> <span> <i><inline-svg src="'+m+'"></inline-svg></i> <span title.bind="item.updatedAt | i18nDateTime:{dateStyle:\'short\'} | i18nParam:\'game_feed.released_on_$date\':\'date\'" class="label">${item.updatedAt | i18nElaspedTime}</span> </span> </template> <template if.bind="metadataTypes.includes(\'creator\')"> <span> <img class="avatar" src.bind="item.creatorAvatar | cdn:{size: 16}"> <span class="meta ellipsis">${item.creator}</span> </span> </template> <template if.bind="metadataTypes.includes(\'players\') && item.players"> <span> <i><inline-svg src="'+o()(n)+"\"></inline-svg></i> <span class=\"meta\">${item.players | i18nNumber:{notation: 'compact', maximumFractionDigits: 1, maximumSignificantDigits: 3, roundingPriority: 'lessPrecision'}}</span> </span> </template> <template if.bind=\"item.lastPlayedAt && (!metadataTypes.length || metadataTypes.includes('last-played-at'))\"> <span> <i><inline-svg src=\""+m+"\"></inline-svg></i> <span title.bind=\"item.lastPlayedAt | i18nDateTime:{dateStyle:'short'} | i18nParam:'game_feed.last_played_$date':'date'\" class=\"meta\">${item.lastPlayedAt | i18nElaspedTime:true}</span> </span> </template> </div> </div> </a> </template> "},"cheats/resources/elements/horizontal-game-feed-item.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r)()(o());n.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}horizontal-game-feed-item{position:relative;z-index:0}horizontal-game-feed-item .item{--game__row--color: rgba(255, 255, 255, 0.7);--game__meta--color: rgba(255, 255, 255, 0.4);--game__thumbnail--border-color: transparent;display:flex;flex-direction:row;display:flex;flex-direction:row;align-items:flex-start;padding:10px;overflow:visible}horizontal-game-feed-item .item,horizontal-game-feed-item .item *{cursor:pointer}horizontal-game-feed-item .item i{display:inline-block;width:15px;height:15px;display:inline-flex;justify-content:center;align-items:center}horizontal-game-feed-item .item i:after{content:"";display:block;clear:both}horizontal-game-feed-item .item i svg{float:left}horizontal-game-feed-item .item i svg{max-width:15px;max-height:15px}horizontal-game-feed-item .item i svg *{color:#fff}horizontal-game-feed-item .item i svg *{fill:rgba(255,255,255,.4);transition:fill .15s}horizontal-game-feed-item .item>*:nth-child(1){margin-right:20px;flex:0 0 auto}horizontal-game-feed-item .item>*:nth-child(2){display:flex;flex-direction:column;flex:1 1 auto;position:relative;padding:5px 0;overflow:hidden}horizontal-game-feed-item .item:hover{--game__row--color: #fff;--game__meta--color: rgba(255, 255, 255, 0.6);--game__thumbnail--border-color: var(--theme--highlight)}horizontal-game-feed-item .item:hover i svg *{fill:rgba(255,255,255,.6)}horizontal-game-feed-item .item,horizontal-game-feed-item .item *{text-decoration:none}horizontal-game-feed-item .item .thumbnail{width:var(--game__thumbnail--width);height:var(--game__thumbnail--height);border-radius:var(--game__thumbnail--border-radius);border:1px solid var(--game__thumbnail--border-color);transition:border-color .15s;display:inline-block;background:rgba(0,0,0,.1);width:114px;height:53px;border-radius:5px}horizontal-game-feed-item .item .info{padding:0}horizontal-game-feed-item .item .info .row{font-size:14px;line-height:21px;line-height:19px;font-weight:500;flex:0 0 auto;display:flex;align-items:center;color:var(--game__row--color);transition:color .15s;overflow:hidden}horizontal-game-feed-item .item .info .row>*+*{margin-left:8px}horizontal-game-feed-item .item .info .row>*>*+*{margin-left:10px}horizontal-game-feed-item .item .info .row>i:last-of-type:not(:first-of-type){margin-left:18px}horizontal-game-feed-item .item .info .row b{font-weight:700}horizontal-game-feed-item .item .info .row>*{flex:0 1 auto;display:flex;align-items:center;min-width:0}horizontal-game-feed-item .item .info .row+.row{margin-top:12px}horizontal-game-feed-item .item .info .row .ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}horizontal-game-feed-item .item .info .row .meta{font-size:12px;line-height:18px;font-weight:500;color:var(--game__meta--color);transition:color .15s;line-height:17px}horizontal-game-feed-item .item .info .row .fill{flex:1;overflow:hidden}horizontal-game-feed-item .item .info .row .tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;background-color:rgba(0,0,0,0);color:rgba(255,255,255,.5);border:1px solid rgba(255,255,255,.4);line-height:11px;text-transform:uppercase;font-size:11px;font-weight:bold;padding:1.5px 4px}horizontal-game-feed-item .item .info .row .avatar{width:16px;height:16px;border-radius:50%}horizontal-game-feed-item .item .info .row .genres>*+*{margin:0}horizontal-game-feed-item .item .info .row .genres>*+*:before{content:", "}horizontal-game-feed-item .item:not(:hover) .info favorite-button{visibility:hidden;width:0;margin-left:0}horizontal-game-feed-item .item:hover:before{box-shadow:0 0 5px rgba(var(--theme--background--rgb), 0.5);position:relative;content:"";background:var(--theme--secondary-background);border-radius:5px;position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1;pointer-events:none}',""]);const m=n},"cheats/resources/elements/horizontal-game-feed.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template class="${largeThumbnails ? \'large-thumbnails\' : \'\'} ${metadataPosition !== \'on-card\' ? \'metadata-below-card\' : \'\'}"> <require from="./horizontal-game-feed.scss"></require> <require from="./game-feed-item"></require> <require from="../../../shared/resources/elements/horizontal-scroll-container"></require> <horizontal-scroll-container view-model.ref="horizontalScrollContainer"> <game-feed-item repeat.for="item of items" item.bind="item" location.bind="location" metadata-types.bind="metadataTypes" previous-route.bind="previousRoute" parent-route.bind="parentRoute" large-thumbnails.bind="largeThumbnails" metadata-position.bind="metadataPosition"></game-feed-item> </horizontal-scroll-container> </template> '},"cheats/resources/elements/horizontal-game-feed.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r)()(o());n.push([e.id,'game-feed-item .game-feed-item{position:relative}game-feed-item .game-feed-item:nth-child(1n+0) img{animation-delay:0.1s}game-feed-item .game-feed-item:nth-child(2n+0) img{animation-delay:0.2s}game-feed-item .game-feed-item:nth-child(3n+0) img{animation-delay:0.3s}game-feed-item .game-feed-item:nth-child(4n+0) img{animation-delay:0.4s}game-feed-item .game-feed-item:nth-child(5n+0) img{animation-delay:0.5s}game-feed-item .game-feed-item:nth-child(6n+0) img{animation-delay:0.6s}game-feed-item .game-feed-item:nth-child(7n+0) img{animation-delay:0.7s}game-feed-item .game-feed-item:nth-child(8n+0) img{animation-delay:0.8s}game-feed-item .game-feed-item:nth-child(9n+0) img{animation-delay:0.9s}game-feed-item .game-feed-item .thumbnail{border-radius:12px;overflow:hidden;width:100%;height:100%;aspect-ratio:200/95}game-feed-item .game-feed-item .thumbnail-wrapper{position:relative;overflow:hidden;display:flex}game-feed-item .game-feed-item .thumbnail-wrapper .choice-badge{width:16px;height:16px}game-feed-item .game-feed-item .thumbnail-wrapper .badge-backdrop{overflow:hidden;display:flex;align-items:center;justify-content:center;position:absolute;top:0;right:0;z-index:1;border-radius:0px 12px 0px 6px;width:20px;height:20px;padding:2px}game-feed-item .game-feed-item .thumbnail-wrapper .badge-backdrop:has(.choice-badge){background-color:#acff35}game-feed-item .game-feed-item .thumbnail-wrapper .badge-backdrop:has(.unavailable-badge){background-color:var(--theme--background);width:24px;height:24px;padding:4px}game-feed-item .game-feed-item favorite-button:not(:hover) button{background:none}game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button{opacity:1}game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{filter:drop-shadow(0px 0px 4px var(--theme--background))}.theme-default game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}.theme-purple-pro game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}.theme-green-pro game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}.theme-orange-pro game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}.theme-pro game-feed-item .game-feed-item--metadata-on-card.game-feed-item--favorite .title-row favorite-button .favorite-icon.favorite{color:#fff}game-feed-item .game-feed-item--metadata-on-card .title-row{position:absolute;bottom:0;padding:8px 4px 4px 8px;z-index:2}.theme-default game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}.theme-purple-pro game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}.theme-green-pro game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}.theme-orange-pro game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}.theme-pro game-feed-item .game-feed-item--metadata-on-card .title-row .title-name{color:#fff}game-feed-item .game-feed-item--metadata-on-card .title-row .title-name,game-feed-item .game-feed-item--metadata-on-card .title-row favorite-button{opacity:0;transition:opacity .2s}game-feed-item .game-feed-item--metadata-on-card::after{content:"";position:absolute;top:0;bottom:0;width:100%;height:100%;opacity:0;transition:opacity .2s;background:linear-gradient(180deg, rgba(13, 15, 18, 0) 0%, rgba(13, 15, 18, 0.7) 100%)}game-feed-item .game-feed-item--metadata-on-card:hover::after{opacity:1}game-feed-item .game-feed-item--metadata-on-card:hover .title-row .title-name,game-feed-item .game-feed-item--metadata-on-card:hover .title-row favorite-button{opacity:1}game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .thumbnail{border:2px solid rgba(0,0,0,0);transition:border .15s}.theme-default game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-purple-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-green-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-orange-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}.theme-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .title-row .title-name{color:rgba(255,255,255,.8)}game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card):hover .thumbnail{border:2px solid var(--theme--highlight)}game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row{margin-top:12px;padding:2px}game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{transition:color .2s}.theme-default game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-default game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-purple-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-purple-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-green-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-green-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-orange-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-orange-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}.theme-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row .title-name,.theme-pro game-feed-item .game-feed-item:not(.game-feed-item--metadata-on-card) .title-row favorite-button:not(.favorite) .favorite-icon{color:rgba(255,255,255,.6)}game-feed-item .game-feed-item .title-row{display:flex;justify-content:space-between;align-items:center;width:100%;gap:10px}game-feed-item .game-feed-item .title-row .title-name{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}horizontal-game-feed{--overflow-fade--background: var(--theme--background);display:block}horizontal-game-feed.large-thumbnails horizontal-scroll-container .scroll-wrapper{grid-auto-columns:1fr}horizontal-game-feed:not(.large-thumbnails).metadata-below-card horizontal-scroll-container .scroll-wrapper{gap:12px}horizontal-game-feed horizontal-scroll-container .scroll-wrapper{display:grid;grid-auto-columns:198px;gap:16px;grid-auto-flow:column;height:110px}',""]);const m=n},"cheats/resources/elements/launch-without-mods-button":(e,t,a)=>{a.r(t),a.d(t,{LaunchWithoutModsButton:()=>d});var i=a(15215),o=a("aurelia-framework"),r=a(58293),n=a(24008),m=a(54995),l=a(70236);let d=class{#l;#d;constructor(e){this.#d=e}detached(){clearTimeout(this.#l),this.launching=!1}launch(){this.launching||(this.launching=!0,clearTimeout(this.#l),this.#l=setTimeout((()=>{this.launching=!1}),5e3),this.#d.launch(this.app,this.gameId,this.trigger))}get hasPlayedUnavailableGame(){return!!this.gameHistory&&Object.entries(this.gameHistory).some((([e,t])=>t.lastPlayedAt&&(!this.games[e]||!(0,l.Lt)(this.games[e].flags,n.rT.Available))))}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Object)],d.prototype,"app",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],d.prototype,"gameId",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],d.prototype,"trigger",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Boolean)],d.prototype,"primary",void 0),(0,i.Cg)([(0,o.computedFrom)("gameHistory","games"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],d.prototype,"hasPlayedUnavailableGame",null),d=(0,i.Cg)([(0,m.m6)({selectors:{games:(0,m.$t)((e=>e.catalog?.games)),gameHistory:(0,m.$t)((e=>e.gameHistory))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[r.L])],d)},"cheats/resources/elements/launch-without-mods-button.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template> <require from="./launch-without-mods-button.scss"></require> <button click.delegate="launch()" disabled.bind="launching" class="${primary && !hasPlayedUnavailableGame ? \'pulse\' : \'\'}"> <i></i> <span>${\'launch_without_mods_button.launch_without_mods\' | i18n}</span> </button> </template> '},"cheats/resources/elements/launch-without-mods-button.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r),m=a(4417),l=a.n(m),d=new URL(a(83959),a.b),s=n()(o()),g=l()(d);s.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,launch-without-mods-button button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}launch-without-mods-button button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);position:relative;align-items:center}launch-without-mods-button button,launch-without-mods-button button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) launch-without-mods-button button{border:1px solid #fff}}launch-without-mods-button button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}launch-without-mods-button button>*:first-child{padding-left:0}launch-without-mods-button button>*:last-child{padding-right:0}launch-without-mods-button button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) launch-without-mods-button button svg *{fill:CanvasText}}launch-without-mods-button button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) launch-without-mods-button button svg{opacity:1}}launch-without-mods-button button img{height:50%}launch-without-mods-button button:disabled{opacity:.3}launch-without-mods-button button:disabled,launch-without-mods-button button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){launch-without-mods-button button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}launch-without-mods-button button:not(:disabled):hover svg{opacity:1}}launch-without-mods-button button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){launch-without-mods-button button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}launch-without-mods-button button:not(:disabled):active{background-color:var(--theme--highlight)}launch-without-mods-button button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;display:inherit;font-size:20px;margin-right:-3px;color:rgba(255,255,255,.8)}launch-without-mods-button button i:before{font-family:inherit;content:"play_arrow"}launch-without-mods-button button:not(:disabled):active{background:rgba(0,0,0,0)}launch-without-mods-button button.pulse::before{content:"";display:block;position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1;background:var(--theme--highlight);opacity:.2;animation:cta--pulse .5s ease-in-out infinite alternate;border-radius:99px}launch-without-mods-button button.pulse::before{background-color:var(--theme--highlight);animation:cta--inner-pulse 1s ease-in-out infinite alternate}launch-without-mods-button.large button{font-weight:800;font-size:21px;line-height:30px;font-weight:800;--cta--padding: 20px;--cta--height: 49px}launch-without-mods-button.large button i{font-size:28px}launch-without-mods-button:not(.large) button{font-weight:800;font-size:21px;line-height:30px;font-weight:800;--cta--padding: 18px;--cta--height: 39px;--cta--hover--border-width: 2px;font-weight:700;font-size:15px;line-height:24px;font-weight:600}`,""]);const f=s},"cheats/resources/elements/list-game-feed":(e,t,a)=>{a.r(t),a.d(t,{ListGameFeed:()=>r});var i=a(15215),o=a("aurelia-framework");class r{constructor(){this.searchResult=!1}}(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],r.prototype,"items",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],r.prototype,"metadataTypes",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"location",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"previousRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],r.prototype,"parentRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Boolean)],r.prototype,"searchResult",void 0)},"cheats/resources/elements/list-game-feed.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>d});var i=a(14385),o=a.n(i),r=new URL(a(57149),a.b),n=new URL(a(85928),a.b),m=o()(r),l=o()(n);const d='<template> <require from="./list-game-feed.scss"></require> <require from="../../../resources/elements/favorite-button"></require> <require from="../../../resources/elements/lazy-render"></require> <require from="../../../resources/elements/title-thumbnail"></require> <a class="game" repeat.for="item of items" route-href="route.bind: \'title\'; params.bind: {titleId: item.titleId, gameId: item.gameId, previousRoute: previousRoute, parentRoute: parentRoute}" title-link="value.bind: location; title-id.bind: item.titleId; game-id.bind: item.gameId; search-result.bind: searchResult;"> <lazy-render> <template replace-part="content"> <title-thumbnail class="thumbnail" src.bind="item.titleThumbnail" width="40"></title-thumbnail> <span class="name"> ${item.titleName}<template if.bind="item.gameEdition">&nbsp;(${item.gameEdition})</template> </span> <span class="favorite-column"> <favorite-button if.bind="item.isAvailable || item.isInstalled" title-id.bind="item.titleId"></favorite-button> </span> <span class="meta"> <div if.bind="metadataTypes.includes(\'genres\')" class="genres"> <span repeat.for="genre of item.genres">${`genres.${genre}` | i18n}</span> </div> <template if.bind="metadataTypes.includes(\'updated-at\') && item.updatedAt"> <i><inline-svg src="'+m+"\"></inline-svg></i> <span title.bind=\"item.updatedAt | i18nDateTime:{dateStyle:'short'} | i18nParam:'game_feed.released_on_$date':'date'\" class=\"meta\">${item.updatedAt | i18nElaspedTime}</span> </template> <template if.bind=\"metadataTypes.includes('players') && item.players\"> <i><inline-svg src=\""+l+"\"></inline-svg></i> <span class=\"meta\">${item.players | i18nNumber:{notation: 'compact', maximumFractionDigits: 1, maximumSignificantDigits: 3, roundingPriority: 'lessPrecision'}}</span> </template> <template if.bind=\"metadataTypes.includes('creator')\"> <i><inline-svg src=\""+l+'"></inline-svg></i> <span class="meta">${item.creator}</span> </template> <template if.bind="item.lastPlayedAt && (!metadataTypes.length || metadataTypes.includes(\'last-played-at\'))"> <i><inline-svg src="'+m+'"></inline-svg></i> <span title.bind="item.lastPlayedAt | i18nDateTime:{dateStyle:\'short\'} | i18nParam:\'game_feed.last_played_$date\':\'date\'" class="meta">${item.lastPlayedAt | i18nElaspedTime:true}</span> </template> </span> <span class="tags"> <i if.bind="metadataTypes.includes(\'platform-icons\')" repeat.for="platformId of item.platformIds" class="platform-icon" title.bind="platformId | platformName"> <inline-svg src.bind="platformId | platformIconSvg"></inline-svg> </i> <template if.bind="metadataTypes.includes(\'status-badges\')"> <span if.bind="item.isNew" class="tag">${\'game_feed.new\' | i18n}</span> <span if.bind="item.isUpdated" class="tag">${\'game_feed.updated\' | i18n}</span> </template> </span> </template> <template replace-part="placeholder"> <span class="placeholder"></span> </template> </lazy-render> </a> </template> '},"cheats/resources/elements/list-game-feed.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r)()(o());n.push([e.id,'list-game-feed .game{background:rgba(var(--theme--background-accent--rgb), 0.4);backdrop-filter:blur(25px);display:flex;width:100%;padding:8px;gap:8px;align-items:center}list-game-feed .game:first-of-type{border-radius:12px 12px 0 0}list-game-feed .game:last-of-type{border-radius:0 0 12px 12px}list-game-feed .game,list-game-feed .game *{cursor:pointer}list-game-feed .game .placeholder{flex:1 1 auto;height:40px}list-game-feed .game .thumbnail{flex:0 0 auto}list-game-feed .game .thumbnail img{display:inline-block;background:rgba(0,0,0,.1);width:40px;height:19px;border-radius:2.5px}list-game-feed .game .name{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;transition:color .15s;min-width:240px;width:25%}.theme-default list-game-feed .game .name{color:rgba(255,255,255,.6)}.theme-purple-pro list-game-feed .game .name{color:rgba(255,255,255,.6)}.theme-green-pro list-game-feed .game .name{color:rgba(255,255,255,.6)}.theme-orange-pro list-game-feed .game .name{color:rgba(255,255,255,.6)}.theme-pro list-game-feed .game .name{color:rgba(255,255,255,.6)}list-game-feed .game .favorite-column{width:100px}list-game-feed .game .favorite-column favorite-button .favorite-icon{transition:color .15s}.theme-default list-game-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-purple-pro list-game-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-green-pro list-game-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-orange-pro list-game-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-pro list-game-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}list-game-feed .game .meta{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;transition:color .15s;overflow:hidden;flex:1 1 0}.theme-default list-game-feed .game .meta{color:rgba(255,255,255,.4)}.theme-purple-pro list-game-feed .game .meta{color:rgba(255,255,255,.4)}.theme-green-pro list-game-feed .game .meta{color:rgba(255,255,255,.4)}.theme-orange-pro list-game-feed .game .meta{color:rgba(255,255,255,.4)}.theme-pro list-game-feed .game .meta{color:rgba(255,255,255,.4)}list-game-feed .game .meta .genres{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}list-game-feed .game .meta .genres>*+*:before{content:", "}list-game-feed .game .meta i{margin-right:10px}list-game-feed .game .meta i svg{transition:opacity .15s;opacity:.5}list-game-feed .game .meta i svg *{fill:#fff}list-game-feed .game .tags{display:flex;align-items:center;gap:10px;flex:1 1 0}list-game-feed .game .tags svg{opacity:.5;transition:opacity .15s}list-game-feed .game .tags svg *{fill:var(--theme--text-highlight)}list-game-feed .game .tags .tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;background-color:rgba(0,0,0,0);color:rgba(255,255,255,.5);border:1px solid rgba(255,255,255,.4);line-height:11px;text-transform:uppercase;font-size:11px;font-weight:bold;padding:1.5px 4px;transition:border-color .15s,color .15s}list-game-feed .game:hover{background:rgba(var(--theme--background-accent--rgb), 0.9)}.theme-default list-game-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-purple-pro list-game-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-green-pro list-game-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-orange-pro list-game-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-pro list-game-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-default list-game-feed .game:hover .meta{color:rgba(255,255,255,.6)}.theme-purple-pro list-game-feed .game:hover .meta{color:rgba(255,255,255,.6)}.theme-green-pro list-game-feed .game:hover .meta{color:rgba(255,255,255,.6)}.theme-orange-pro list-game-feed .game:hover .meta{color:rgba(255,255,255,.6)}.theme-pro list-game-feed .game:hover .meta{color:rgba(255,255,255,.6)}list-game-feed .game:hover .meta svg{opacity:.8}.theme-default list-game-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-purple-pro list-game-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-green-pro list-game-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-orange-pro list-game-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-pro list-game-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}list-game-feed .game:hover .tags svg{opacity:.8}list-game-feed .game:hover .tags .tag{border-color:var(--theme--text-secondary)}.theme-default list-game-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-purple-pro list-game-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-green-pro list-game-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-orange-pro list-game-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-pro list-game-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}list-game-feed .game favorite-button:not(:hover) button{background:none}list-game-feed .game favorite-button:hover .favorite-icon{color:#fff !important}',""]);const m=n},"cheats/resources/elements/list-map-feed":(e,t,a)=>{a.r(t),a.d(t,{ListMapFeed:()=>m});var i=a(15215),o=a("aurelia-framework"),r=a(96555),n=a(54995);let m=class{itemMaps(e){return this.catalog?.maps.filter((t=>t.titleId===e.titleId))}itemSteamAppId(e){const t=this.catalog.titles[e.titleId];return t?.gameIds?.flatMap((e=>this.catalog.games[e].correlationIds))?.map(r.o.parse)?.find((e=>"steam"===e.platform))?.sku??null}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],m.prototype,"items",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],m.prototype,"location",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],m.prototype,"previousRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],m.prototype,"parentRoute",void 0),m=(0,i.Cg)([(0,n.m6)({selectors:{catalog:(0,n.$t)((e=>e.catalog))}}),(0,o.autoinject)()],m)},"cheats/resources/elements/list-map-feed.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var i=a(14385),o=a.n(i),r=new URL(a(98593),a.b);const n='<template> <require from="./list-map-feed.scss"></require> <require from="../../../resources/elements/favorite-button"></require> <require from="../../../resources/elements/lazy-render"></require> <template repeat.for="item of items"> <let maps.bind="itemMaps(item)"></let> <a class="game" href="wemod://maps/${item.titleId}/${maps[0].id}"> <lazy-render> <template replace-part="content"> <span class="thumbnail"> <img fallback-src="'+o()(r)+'" src.bind="maps[0].thumbnail | cdn"> ${item.thumbnail} </span> <span class="name">${item.titleName}</span> <span class="favorite-column"> <favorite-button if.bind="item.isAvailable || item.isInstalled" title-id.bind="item.titleId"></favorite-button> </span> <span class="tags"> <span class="tag" if.bind="maps.length > 1"> ${\'map_feed_item.$count_maps\' | i18n:{count: maps.length}} </span> </span> </template> <template replace-part="placeholder"> <span class="placeholder"></span> </template> </lazy-render> </a> </template> </template> '},"cheats/resources/elements/list-map-feed.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>m});var i=a(31601),o=a.n(i),r=a(76314),n=a.n(r)()(o());n.push([e.id,'list-map-feed .game{background:rgba(var(--theme--background-accent--rgb), 0.4);backdrop-filter:blur(25px);display:flex;width:100%;padding:8px;gap:8px;align-items:center}list-map-feed .game:first-of-type{border-radius:12px 12px 0 0}list-map-feed .game:last-of-type{border-radius:0 0 12px 12px}list-map-feed .game,list-map-feed .game *{cursor:pointer}list-map-feed .game .placeholder{flex:1 1 auto;height:40px}list-map-feed .game .thumbnail{flex:0 0 auto}list-map-feed .game .thumbnail img{display:inline-block;background:rgba(0,0,0,.1);width:40px;height:19px;border-radius:2.5px}list-map-feed .game .name{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;transition:color .15s;min-width:240px;width:25%}.theme-default list-map-feed .game .name{color:rgba(255,255,255,.6)}.theme-purple-pro list-map-feed .game .name{color:rgba(255,255,255,.6)}.theme-green-pro list-map-feed .game .name{color:rgba(255,255,255,.6)}.theme-orange-pro list-map-feed .game .name{color:rgba(255,255,255,.6)}.theme-pro list-map-feed .game .name{color:rgba(255,255,255,.6)}list-map-feed .game .favorite-column{width:100px}list-map-feed .game .favorite-column favorite-button .favorite-icon{transition:color .15s}.theme-default list-map-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-purple-pro list-map-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-green-pro list-map-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-orange-pro list-map-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}.theme-pro list-map-feed .game .favorite-column favorite-button .favorite-icon{color:rgba(255,255,255,.4)}list-map-feed .game .meta{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;transition:color .15s;overflow:hidden;flex:1 1 0}.theme-default list-map-feed .game .meta{color:rgba(255,255,255,.4)}.theme-purple-pro list-map-feed .game .meta{color:rgba(255,255,255,.4)}.theme-green-pro list-map-feed .game .meta{color:rgba(255,255,255,.4)}.theme-orange-pro list-map-feed .game .meta{color:rgba(255,255,255,.4)}.theme-pro list-map-feed .game .meta{color:rgba(255,255,255,.4)}list-map-feed .game .meta .genres{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}list-map-feed .game .meta .genres>*+*:before{content:", "}list-map-feed .game .meta i{margin-right:10px}list-map-feed .game .meta i svg{transition:opacity .15s;opacity:.5}list-map-feed .game .meta i svg *{fill:#fff}list-map-feed .game .tags{display:flex;align-items:center;gap:10px;flex:1 1 0}list-map-feed .game .tags svg{opacity:.5;transition:opacity .15s}list-map-feed .game .tags svg *{fill:var(--theme--text-highlight)}list-map-feed .game .tags .tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;background-color:rgba(0,0,0,0);color:rgba(255,255,255,.5);border:1px solid rgba(255,255,255,.4);line-height:11px;text-transform:uppercase;font-size:11px;font-weight:bold;padding:1.5px 4px;transition:border-color .15s,color .15s}list-map-feed .game:hover{background:rgba(var(--theme--background-accent--rgb), 0.9)}.theme-default list-map-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-purple-pro list-map-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-green-pro list-map-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-orange-pro list-map-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-pro list-map-feed .game:hover .name{color:rgba(255,255,255,.8)}.theme-default list-map-feed .game:hover .meta{color:rgba(255,255,255,.6)}.theme-purple-pro list-map-feed .game:hover .meta{color:rgba(255,255,255,.6)}.theme-green-pro list-map-feed .game:hover .meta{color:rgba(255,255,255,.6)}.theme-orange-pro list-map-feed .game:hover .meta{color:rgba(255,255,255,.6)}.theme-pro list-map-feed .game:hover .meta{color:rgba(255,255,255,.6)}list-map-feed .game:hover .meta svg{opacity:.8}.theme-default list-map-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-purple-pro list-map-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-green-pro list-map-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-orange-pro list-map-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}.theme-pro list-map-feed .game:hover favorite-button .favorite-icon{color:rgba(255,255,255,.6)}list-map-feed .game:hover .tags svg{opacity:.8}list-map-feed .game:hover .tags .tag{border-color:var(--theme--text-secondary)}.theme-default list-map-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-purple-pro list-map-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-green-pro list-map-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-orange-pro list-map-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}.theme-pro list-map-feed .game:hover .tags .tag{color:rgba(255,255,255,.6)}list-map-feed .game favorite-button:not(:hover) button{background:none}list-map-feed .game favorite-button:hover .favorite-icon{color:#fff !important}',""]);const m=n}}]);
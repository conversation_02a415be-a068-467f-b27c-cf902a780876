"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1936],{"onboarding/game-onboarding":(e,a,n)=>{n.r(a),n.d(a,{GameOnboarding:()=>l});var o=n(15215),t=n(7530),i=n("aurelia-event-aggregator"),r=n("aurelia-framework"),g=n(62914),d=n(92465);const s=[{name:"welcome",titleKey:"onboarding_game_tutorial.welcome_to_game_page",subTitleKey:"onboarding_game_tutorial.play_mod_enjoy",backButtonKey:"onboarding_game_tutorial.skip",nextButton<PERSON>ey:"onboarding_game_tutorial.next_arrow"},{name:"add-game",titleKey:"onboarding_game_tutorial.add_your_game",subTitleKey:"onboarding_game_tutorial.use_add_game_button",backButtonKey:"onboarding_game_tutorial.back_arrow",nextButtonKey:"onboarding_game_tutorial.next_arrow"},{name:"launch-game",titleKey:"onboarding_game_tutorial.launch_your_game",subTitle<PERSON>ey:"onboarding_game_tutorial.use_play_game_button",backButtonKey:"onboarding_game_tutorial.back_arrow",nextButtonKey:"onboarding_game_tutorial.next_arrow"},{name:"customize-mods",titleKey:"onboarding_game_tutorial.customize_with_mods",subTitleKey:"onboarding_game_tutorial.toggle_and_adjust_mods",backButtonKey:"onboarding_game_tutorial.back_arrow",nextButtonKey:"onboarding_game_tutorial.next_arrow"},{name:"hotkeys",titleKey:"onboarding_game_tutorial.use_hotkeys_for_convenience",subTitleKey:"onboarding_game_tutorial.customizable_keyboard_shortcuts",backButtonKey:"onboarding_game_tutorial.back_arrow",nextButtonKey:"onboarding_game_tutorial.done"}];let l=class{#e;#a;#n;constructor(e,a){this.onboardingTitle=null,this.onboardingSteps=s,this.currentStepIndex=0,this.#n=e,this.#a=a}attached(){this.#e=(0,d.yB)(window,"keydown",this.#o.bind(this)),this.onboardingTitle?.isInstalled&&(this.onboardingSteps=this.onboardingSteps.filter((e=>"add-game"!==e.name)))}detached(){this.#e?.dispose()}handleSkip(){this.#a.event("onboarding_user_skip",{titleId:this.onboardingTitle?.id??""},g.Io),this.#n.publish("onboarding-complete",{titleId:this.onboardingTitle?.id})}handleNextStep(){if(this.currentStepIndex===this.onboardingSteps.length-1)return this.#a.event("onboarding_user_complete",{titleId:this.onboardingTitle?.id??""},g.Io),void this.#n.publish("onboarding-complete",{titleId:this.onboardingTitle?.id});this.currentStepIndex=Math.min(this.currentStepIndex+1,this.onboardingSteps.length-1)}handleBackStep(){0!==this.currentStepIndex?this.currentStepIndex=Math.max(this.currentStepIndex-1,0):this.handleSkip()}#o(e){switch(e.key){case" ":case"ArrowRight":e.preventDefault(),this.handleNextStep();break;case"ArrowLeft":this.handleBackStep()}}get currentStep(){return this.onboardingSteps?.[this.currentStepIndex]??this.onboardingSteps[0]}get showAddButton(){return"add-game"===this.currentStep.name||"welcome"===this.currentStep.name&&!this.onboardingTitle?.isInstalled}get buttonState(){return 0===this.currentStepIndex?"install":"play"}};(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",Object)],l.prototype,"onboardingTitle",void 0),(0,o.Cg)([(0,t.Kj)("currentStepIndex"),(0,o.Sn)("design:type",Object),(0,o.Sn)("design:paramtypes",[])],l.prototype,"currentStep",null),(0,o.Cg)([(0,t.Kj)("currentStep"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],l.prototype,"showAddButton",null),(0,o.Cg)([(0,t.Kj)("currentStepIndex"),(0,o.Sn)("design:type",String),(0,o.Sn)("design:paramtypes",[])],l.prototype,"buttonState",null),l=(0,o.Cg)([(0,r.autoinject)(),(0,o.Sn)("design:paramtypes",[i.EventAggregator,g.j0])],l)},"onboarding/game-onboarding.html":(e,a,n)=>{n.r(a),n.d(a,{default:()=>r});var o=n(14385),t=n.n(o),i=new URL(n(18285),n.b);const r='<template> <require from="./game-onboarding.scss"></require> <require from="./resources/elements/fake-game-controls"></require> <require from="./resources/elements/fake-save-mods"></require> <require from="./resources/elements/fake-add-game"></require> <require from="./resources/elements/fake-play-game"></require> <require from="./resources/elements/fake-title-actions"></require> <require from="shared/cheats/resources/custom-attributes/steam-capsule-bg"></require> <require from="../cheats/resources/custom-attributes/steam-hero-bg"></require> <div class="game-background ${currentStep.name}" steam-hero-bg="steam-id.bind: onboardingTitle.steamAppId; title-thumbnail.bind: onboardingTitle.thumbnail"></div> <div class="onboarding-header"> <div class="logo"> <div class="wemod-logo"><img src="'+t()(i)+'"></div> </div> </div> <div class="game-onboarding ${currentStep.name}"> <button class="skip" if.bind="currentStep.name !== \'welcome\'" click.delegate="handleSkip()"> ${\'onboarding_game_tutorial.skip\' | i18n} </button> <div class="fake-game-page"> <div class="highlight-border"></div> <div class="fake-header"> <div if.bind="onboardingTitle.steamAppId" class="game-image" steam-capsule-bg="steam-id.bind: onboardingTitle.steamAppId"></div> <div class="game-title">${onboardingTitle.name}</div> <fake-title-actions></fake-title-actions> <div class="spacer"></div> <fake-save-mods></fake-save-mods> <fake-add-game if.bind="showAddButton"></fake-add-game> <div else class="play-button"> <span class="gradient"></span> <fake-play-game></fake-play-game> </div> </div> <div class="fake-controls"> <fake-game-controls></fake-game-controls> </div> </div> <div class="onboarding-steps"> <div if.bind="currentStep.name !== \'welcome\'" class="step-indicators"> <div repeat.for="step of onboardingSteps" class="step-circle ${step.name === currentStep.name ? \'active\' : \'\'}"></div> </div> <h1 class="title">${currentStep.titleKey | i18n}</h1> <div class="subtitle" innerhtml.bind="currentStep.subTitleKey | i18n | markdown:false"></div> <div class="step-buttons"> <button class="back" click.delegate="handleBackStep()">${currentStep.backButtonKey | i18n}</button> <button class="next" click.delegate="handleNextStep()">${currentStep.nextButtonKey | i18n}</button> </div> </div> <div if.bind="currentStep.name !== \'welcome\'" class="skip-hint" innerhtml.bind="\'onboarding_game_tutorial.press_spacebar_to_continue\' | i18n | markdown"></div> </div> </template> '},"onboarding/game-onboarding.scss":(e,a,n)=>{n.r(a),n.d(a,{default:()=>b});var o=n(31601),t=n.n(o),i=n(76314),r=n.n(i),g=n(4417),d=n.n(g),s=new URL(n(83959),n.b),l=r()(t()),m=d()(s);l.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}game-onboarding{--gradient-fade-direction: bottom;--onboarding-animation-easing: all 0.2 ease-in}game-onboarding .game-background{position:absolute;width:150%;height:150%;transform:translate(-25%, -25%);background-size:cover;background-position:center;filter:blur(16px)}game-onboarding .game-background.is-fallback{background:radial-gradient(80.12% 47.09% at 102.58% 10.64%, #1fbaf8 0%, #2a9bf9 0.01%, #3874fb 21.4%, #4743fc 38.79%, #2a1257 68.95%, #1c1625 79.41%, #0d0f12 100%) !important}game-onboarding .game-background:not(.is-fallback):after{content:"";z-index:1;width:100%;height:100%;position:absolute;top:0;left:0;right:0;bottom:0;background:rgba(var(--theme--default--background--rgb), 0.5)}game-onboarding .onboarding-header{display:flex;flex-direction:row;justify-content:center;align-items:end;width:100%;padding:0px 40px 16px 40px;z-index:1;position:fixed;top:0;min-height:150px}game-onboarding .onboarding-header .logo{position:absolute;top:40px;display:flex;flex-direction:column;align-items:center;justify-content:center;gap:28px}game-onboarding .onboarding-header .logo .wemod-logo img{height:30px}game-onboarding .game-onboarding{position:relative;z-index:1;display:flex;flex-direction:column;align-items:center;padding-top:140px;justify-content:center;gap:36px;height:100%;transition:var(--onboarding-animation-easing)}game-onboarding .game-onboarding:not(.welcome){flex-direction:row;justify-content:center;align-items:start;padding:26px;padding-top:14%}game-onboarding .game-onboarding:not(.welcome) .highlight-border{position:absolute;width:100%;height:100%;top:0;left:0}game-onboarding .game-onboarding:not(.welcome) .highlight-border:after{content:"";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:26px;border:2px solid var(--theme--default--highlight);transition:var(--onboarding-animation-easing)}game-onboarding .game-onboarding:not(.welcome) .fake-game-page{align-items:start;max-height:230px;border-radius:26px;transition:var(--onboarding-animation-easing)}game-onboarding .game-onboarding:not(.welcome).add-game .fake-game-page .fake-controls,game-onboarding .game-onboarding:not(.welcome).launch-game .fake-game-page .fake-controls{mask:none}game-onboarding .game-onboarding:not(.welcome).customize-mods .fake-game-page,game-onboarding .game-onboarding:not(.welcome).hotkeys .fake-game-page{padding:0px}game-onboarding .game-onboarding:not(.welcome).customize-mods .fake-header{display:none}game-onboarding .game-onboarding:not(.welcome).customize-mods .fake-game-page{--gradient-fade-direction: right;justify-content:start;max-height:360px}game-onboarding .game-onboarding:not(.welcome).customize-mods .fake-game-page .highlight-border:after{width:640px}game-onboarding .game-onboarding:not(.welcome).hotkeys .fake-header{display:none}game-onboarding .game-onboarding:not(.welcome).hotkeys .fake-game-page{--gradient-fade-direction: left;justify-content:start;align-items:end;max-height:360px;max-width:600px}game-onboarding .game-onboarding:not(.welcome).hotkeys .fake-game-page .highlight-border:after{width:210px;left:320px}game-onboarding .game-onboarding:not(.welcome) .onboarding-steps{align-items:start;max-width:280px}game-onboarding .game-onboarding .skip{position:absolute;top:80px;right:28px;border:none;outline:none;box-shadow:none;display:flex;flex-direction:column;justify-content:center;align-items:center;padding:10px 24px;height:44px;width:80px;border-radius:56px;font-weight:800;font-size:24px;background:rgba(255,255,255,.1);color:#fff}game-onboarding .game-onboarding .skip:hover,game-onboarding .game-onboarding .skip:focus-visible{background:rgba(255,255,255,.3) !important}game-onboarding .game-onboarding .skip-hint{position:absolute;bottom:40px;display:flex;font-weight:500;font-size:14px;line-height:20px;text-align:center;color:rgba(255,255,255,.5);gap:4px}game-onboarding .game-onboarding .skip-hint strong{font-weight:500;display:flex;flex-direction:row;justify-content:center;align-items:center;padding:0px 4px;gap:10px;background:rgba(255,255,255,.04);border-radius:4px;color:rgba(255,255,255,.9)}game-onboarding .game-onboarding .fake-game-page{display:flex;flex-direction:column;justify-content:start;align-items:center;gap:20px;max-height:350px;overflow:hidden;padding:14px;position:relative}game-onboarding .game-onboarding .fake-game-page,game-onboarding .game-onboarding .fake-game-page *{pointer-events:none}game-onboarding .game-onboarding .fake-game-page .fake-header{display:flex;gap:8px;align-items:center;width:100%}game-onboarding .game-onboarding .fake-game-page .fake-header .spacer{flex:1}game-onboarding .game-onboarding .fake-game-page .fake-header .game-image{display:flex;min-width:40px;height:48px;background-size:cover;background-position:center center;background-repeat:no-repeat;border-radius:12px;position:relative;margin-right:8px}game-onboarding .game-onboarding .fake-game-page .fake-header .game-image:after{content:"";position:absolute;border-radius:12px;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);mix-blend-mode:overlay;z-index:1}game-onboarding .game-onboarding .fake-game-page .fake-header .game-title{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;font-weight:800;font-size:24px;align-items:center;text-align:center;letter-spacing:-1px;color:#fff;margin-right:8px}game-onboarding .game-onboarding .fake-game-page .fake-header fake-save-mods{height:44px}game-onboarding .game-onboarding .fake-game-page .fake-header .play-button{position:relative;height:44px}game-onboarding .game-onboarding .fake-game-page .fake-header .play-button .gradient::before{content:"";display:block;position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);opacity:.4;animation:cta--pulse .5s ease-in-out infinite alternate;border-radius:99px}game-onboarding .game-onboarding .fake-game-page .fake-controls{border-radius:16px;background:rgba(var(--theme--default--background-accent--rgb), 0.15);mask:linear-gradient(to var(--gradient-fade-direction), rgb(0, 0, 0) 0, rgb(0, 0, 0) 80%, rgba(0, 0, 0, 0) 95%, rgba(0, 0, 0, 0) 0) 100% 50%/100% 100% repeat-x}game-onboarding .game-onboarding .onboarding-steps{display:flex;flex-direction:column;align-items:center;justify-content:center;justify-self:end;padding-bottom:60px;min-width:250px}game-onboarding .game-onboarding .onboarding-steps .step-indicators{display:flex;gap:4px;padding-bottom:24px}game-onboarding .game-onboarding .onboarding-steps .step-indicators .step-circle{width:6px;height:6px;display:flex;border-radius:100%;background-color:rgba(255,255,255,.2)}game-onboarding .game-onboarding .onboarding-steps .step-indicators .step-circle.active{background-color:#fff}game-onboarding .game-onboarding .onboarding-steps .title{font-weight:800;font-size:32px;line-height:100%;display:flex;letter-spacing:-2px;color:#fff;margin:0}game-onboarding .game-onboarding .onboarding-steps .subtitle{font-weight:500;font-size:16px;line-height:24px;color:rgba(255,255,255,.8);margin:0}game-onboarding .game-onboarding .onboarding-steps .subtitle strong{color:var(--theme--default--highlight)}game-onboarding .game-onboarding .onboarding-steps .subtitle em{font-style:normal;font-weight:bold;color:rgba(255,255,255,.8)}game-onboarding .game-onboarding .onboarding-steps .step-buttons{display:flex;gap:10px;padding-top:16px}game-onboarding .game-onboarding .onboarding-steps .step-buttons button{border:none;outline:none;box-shadow:none;display:flex;flex-direction:column;justify-content:center;align-items:center;padding:10px 24px;height:44px;width:80px;border-radius:56px;font-weight:800;font-size:24px}game-onboarding .game-onboarding .onboarding-steps .step-buttons button.back{background:rgba(255,255,255,.1);color:#fff}game-onboarding .game-onboarding .onboarding-steps .step-buttons button.next{background:#fff;color:#000}game-onboarding .game-onboarding .onboarding-steps .step-buttons button:hover,game-onboarding .game-onboarding .onboarding-steps .step-buttons button:focus-visible{background:rgba(255,255,255,.3) !important}`,""]);const b=l},"onboarding/onboarding":(e,a,n)=>{n.r(a),n.d(a,{Onboarding:()=>d});var o=n(15215),t=n("aurelia-event-aggregator"),i=n("aurelia-framework"),r=n(62914),g=n(92465);let d=class{#e;#t;#n;#a;constructor(e,a){this.selectedOnboardingTitle=null,this.loading=!0,this.#n=e,this.#a=a}attached(){this.#e=(0,g.yB)(window,"keydown",this.#i.bind(this)),this.#t=(0,g.Ix)((()=>this.loading=!1),3500)}detached(){this.#e?.dispose(),this.#t?.dispose()}#i(e){"Escape"===e.key&&(this.#a.event("onboarding_user_exit",{},r.Io),this.#n.publish("onboarding-complete"))}};(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",Object)],d.prototype,"selectedOnboardingTitle",void 0),d=(0,o.Cg)([(0,i.autoinject)(),(0,o.Sn)("design:paramtypes",[t.EventAggregator,r.j0])],d)},"onboarding/onboarding.html":(e,a,n)=>{n.r(a),n.d(a,{default:()=>o});const o='<template> <require from="./onboarding.scss"></require> <require from="./select-game"></require> <require from="./game-onboarding"></require> <require from="./resources/elements/loading"></require> <require from="../app/resources/elements/app-header"></require> <loading loading.two-way="loading" if.bind="loading"></loading> <div else class="onboarding-layout"> <app-header></app-header> <select-game if.bind="!selectedOnboardingTitle" selected-onboarding-title.two-way="selectedOnboardingTitle"></select-game> <game-onboarding if.bind="selectedOnboardingTitle" onboarding-title.bind="selectedOnboardingTitle"></game-onboarding> </div> </template> '},"onboarding/onboarding.scss":(e,a,n)=>{n.r(a),n.d(a,{default:()=>b});var o=n(31601),t=n.n(o),i=n(76314),r=n.n(i),g=n(4417),d=n.n(g),s=new URL(n(83959),n.b),l=r()(t()),m=d()(s);l.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.onboarding-layout{display:block;width:100vw;height:100vh;background:var(--theme--background)}.onboarding-layout app-header{position:absolute;top:0;left:0}.onboarding-layout app-header .logo{display:none}`,""]);const b=l}}]);
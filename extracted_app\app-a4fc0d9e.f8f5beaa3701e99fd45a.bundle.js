"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5473],{73592:(e,t,a)=>{a.d(t,{b:()=>o});var s=a(15215),n=a("aurelia-framework"),i=a(55816),c=a.n(i);let o=class{play({loop:e=!1,autoplay:t=!0,path:a}){const s=document.createElement("div"),n=c().loadAnimation({container:s,renderer:"svg",loop:e,autoplay:t,path:a});return s.style.cssText="\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 9999;\n      pointer-events: none;\n    ",document.body.appendChild(s),n.addEventListener("complete",(()=>{document.body.removeChild(s)})),n}};o=(0,s.Cg)([(0,n.singleton)()],o)},76861:(e,t,a)=>{a.d(t,{P:()=>Je});var s=a(15215),n=a("aurelia-framework"),i=a(90415),c=a(78268),o=a(10699),r=a(97928),d=a(27958),h=a(78563),l=a(40127),v=a(2427),p=a(62079),u=a(72208),y=a(68368),m=a(61116),f=a(69005),g=a(60284),b=a(2690),S=a(75115),k=a(70763),w=a(50654),C=a(16128),L=a(98300),E=a(43050),T=a(23655),Y=a(92380),Z=a(79810),x=a(39835),G=a(41032),O=a(82174),R=a(10704),V=a(85818),D=a(58293),J=a(11717),K=a(13472),M=a(43544),N=a(10191),Q=a(97170),W=a(68225),j=a(43861),q=a(78576),z=a(96276),A=a(811),B=a(85805),F=a(56705),H=a(29879),I=a(43570),P=a(33700),U=a(31974),X=a(96111),$=a(35030),_=a(98120),ee=a(7892),te=a(91736),ae=a(84847),se=a(60796),ne=a(49857),ie=a(13625),ce=a(50260),oe=a(17703),re=a(87286),de=a(1774),he=a(59327),le=a(56436),ve=a(68865),pe=a(43305),ue=a(72510),ye=a(31051),me=a(90017),fe=a(86867),ge=a(94101),be=a(45053),Se=a(733),ke=a(8712),we=a(24697),Ce=a(29347),Le=a(80901),Ee=a(69983),Te=a(13101),Ye=a(29865),Ze=a(75246),xe=a(75513),Ge=a(19648),Oe=a(5177),Re=a(71006);const Ve=[Ze.R,J.i,r.s,y.u,_.p,z.T,B.e],De=[U.Z,o.G,c.s,v.K,E.Y2,R.r,Ye.Z,O.u,C.X,L.Hy,M.u,N.s,u.u,F.Q,r.s,xe.N,i.u,K.K,W.c,g.Y,I.L,G.O,te.S,ae.R,ne.b,ie.E,ce.m,se.Q,ee.p,j.Z,q.G,d.L,A.n,f.L,m.c,P.q,D.L,w.O,p.L,x.I,X.Y,Q.V,k.i,h.s,$.U,Z.r,H.m,T.g,Oe.G,Re.T,l.Re,Ge.Z,ue.B,re.O,ge._,ke.J,he.t,de.D,oe.T,me.v,be.D,we.v,Ee.i,Ce.y,Le.v,Te.F,pe.Y,ye.o,le.c,Se.V,fe.k,ve.V,Y.$,S.N,b.J,V.o];let Je=class{#e;#t;#a;constructor(e){this.#e=e}async activate(){this.#t=Ve.map((e=>this.#e.get(e)));for(const e of this.#t)await e.activate()}async deactivate(){if(this.#t)for(const e of this.#t)await e.deactivate();this.#t=null}attached(e){this.#a=De.map((e=>this.#e.get(e))),this.#a.forEach((t=>t.attached(e)))}detached(){this.#a?.forEach((e=>e.detached())),this.#a=null}};Je=(0,s.Cg)([(0,n.autoinject)(),(0,s.Sn)("design:paramtypes",[n.Container])],Je)}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[372],{4826:(e,t,o)=>{o.d(t,{R:()=>a,Y:()=>n});const n=[{key:87,alt:!0,ctrl:!1,shift:!1},{key:192,alt:!0,ctrl:!1,shift:!1}],a=n[0]},6745:(e,t,o)=>{o.d(t,{Ei:()=>d,J4:()=>x,KD:()=>l,Kc:()=>u,Mc:()=>k,Pn:()=>w,Pp:()=>P,Q6:()=>f,TJ:()=>p,TS:()=>b,_Z:()=>g,a3:()=>v,aY:()=>c,dQ:()=>y,jZ:()=>m,lx:()=>h,pd:()=>S,qL:()=>$,sx:()=>i,tk:()=>C,wk:()=>_,zY:()=>L});var n=o(15215),a=o("aurelia-event-aggregator"),r=o("aurelia-framework"),s=o(84157);const i="get_app_info",l="request_settings",p="set_settings",c="toggle_pinned",d="get_trainer_info",u="analytics_event",h="set_trainer_value",m="mark_cheat_instructions_read",g="dismiss_mod_timer_message",f="set_cheat_states",_="get_auth_code",b="set_account",v="request_account",y="get_player_coordinates",x="set_player_coordinates",w="set_pinned_mod",k="set_mod_timer",C="get_mod_timer_messages_dismissed",P="set_mod_timer_messages_dismissed",L="on_overlay_hotkey_pressed",$="save_capture_highlight",S="add_overlay_notification",T="set_settings",M="toggle_pinned",q="trainer_value_set",I="cheat_states_set",z="set_account",D="mod_timer_messages_dismissed",O="overlay_hotkey_pressed",H="add_overlay_notification";(0,n.Cg)([r.observable,(0,n.Sn)("design:type",Object)],class{#e;#t;constructor(e){this.#t=new a.EventAggregator,this.#e=e}async initialize(){[this.info,this.trainerInfo,this.modTimerMessagesDismissed]=await Promise.all([await(this.#e?.execute(i,null)),await(this.#e?.execute(d,null)),await(this.#e?.execute(C,null))]),this.#e.setHandler(p,(async e=>this.#t.publish(T,e))),this.#e.setHandler(c,(async e=>this.#t.publish(M,e))),this.#e.setHandler(h,(async e=>this.#t.publish(q,e))),this.#e.setHandler(f,(async e=>{this.cheatStates=e,this.#t.publish(I,e)})),this.#e.setHandler(p,(async e=>this.#t.publish(T,e))),this.#e.setHandler(b,(async e=>this.handleAccountChange(e))),this.#e.setHandler(P,(async e=>{this.setModTimerMessagesDismissed(e)})),this.#e.setHandler(L,(async()=>this.#t.publish(O))),this.#e.setHandler(S,(async e=>this.#t.publish(H,e)))}handleAccountChange(e){this.account=e,this.#t.publish(z,e)}requestOverlaySettings(){this.#e.execute(l,null)}requestAccount(){this.#e.execute(v,null)}event(e,t,o){const n={name:e,params:t,dispatch:o};this.#e.execute(u,n)}setTrainerValue(e){this.#e.execute(h,e)}markCheatInstructionsRead(e){this.#e.execute(m,e)}onSettingsChanged(e){return this.#t.subscribe(T,e)}onTogglePinned(e){return this.#t.subscribe(M,e)}onAccountChanged(e){return this.#t.subscribe(z,e)}onTrainerValueSet(e){return this.#t.subscribe(q,e)}onCheatStatesSet(e){return this.#t.subscribe(I,e)}onModTimerMessagesDismissed(e){return this.#t.subscribe(D,e)}onOverlayHotkeyPressed(e){return this.#t.subscribe(O,e)}onOverlayNotificationRequested(e){return this.#t.subscribe(H,e)}async setGlobalBlockInput(e){await s.ipcRenderer.invoke("ACTION_OVERLAY_BLOCK_INPUT",e)}async getAuthCode(){return await this.#e.execute(_,null)||null}async refreshTrainerInfo(){this.trainerInfo=await(this.#e?.execute(d,null)),this.cheatStates=this.trainerInfo?.cheatStates}async getPlayerCoordinates(){return await this.#e.execute(y,null)}async setPlayerCoordinates(e){return await this.#e.execute(x,e)}pinMod(e,t,o,n){this.#e.execute(w,{gameId:e,trainerId:t,mod:o,pin:n})}async dismissModTimerMessage(e){await this.#e.execute(g,e),this.modTimerMessagesDismissed=await this.#e.execute(C,null),this.#t.publish(D,this.modTimerMessagesDismissed)}async getModTimerMessagesDismissed(){return await this.#e.execute(C,null)}setModTimerMessagesDismissed(e){this.modTimerMessagesDismissed=e,this.#t.publish(D,e)}setModTimer(e,t){this.#e.execute(k,{config:e,firstCall:t})}async saveCaptureHighlight(){await this.#e.execute($,null)}}.prototype,"account",void 0)},"pro-promos/choose-plan-promo/choose-plan-promo":(e,t,o)=>{o.r(t),o.d(t,{ChoosePlanPromo:()=>r});var n=o(15215),a=o("aurelia-framework");class r{}(0,n.Cg)([a.bindable,(0,n.Sn)("design:type",Function)],r.prototype,"onProCtaClick",void 0)},"pro-promos/choose-plan-promo/choose-plan-promo.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});const n='<template> <require from="./choose-plan-promo.scss"></require> <require from="pro-promos/resources/elements/pro-promo-copy"></require> <require from="./resources/elements/plan-selector"></require> <div class="choose-plan-promo-container"> <pro-promo-copy tagline="${\'choose_plan_promo.tagline\' | i18n}" description="${\'choose_plan_promo.description\' | i18n}" heading="${\'choose_plan_promo.heading\' | i18n}"> </pro-promo-copy> <plan-selector on-pro-cta-click.call="onProCtaClick()"> </plan-selector> </div> </template> '},"pro-promos/choose-plan-promo/choose-plan-promo.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>u});var n=o(31601),a=o.n(n),r=o(76314),s=o.n(r),i=o(4417),l=o.n(i),p=new URL(o(62590),o.b),c=s()(a()),d=l()(p);c.push([e.id,`choose-plan-promo .choose-plan-promo-container{display:flex;flex-direction:row;align-items:center;min-height:584px;padding:32px;gap:48px;background-image:url(${d});border-radius:20px;overflow:hidden}`,""]);const u=c},"pro-promos/choose-plan-promo/resources/elements/plan-feature":(e,t,o)=>{o.r(t),o.d(t,{PlanFeature:()=>n});class n{}},"pro-promos/choose-plan-promo/resources/elements/plan-feature.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});const n='<template> <require from="./plan-feature.scss"></require> <span class="plan-feature-icon"> <slot name="icon"></slot> </span> <span class="plan-feature-description"> <slot></slot> </span> </template> '},"pro-promos/choose-plan-promo/resources/elements/plan-feature.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});var n=o(31601),a=o.n(n),r=o(76314),s=o.n(r)()(a());s.push([e.id,'plan-feature{gap:8px}plan-feature .plan-feature-description{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px}plan-feature .plan-feature-icon{display:inline-flex}',""]);const i=s},"pro-promos/choose-plan-promo/resources/elements/plan-selector":(e,t,o)=>{o.r(t),o.d(t,{PlanSelector:()=>p});var n=o(15215),a=o(96111),r=o("aurelia-framework"),s=o(20770),i=o(71341),l=o(68539);let p=class{#o;constructor(e,t,o,n){this.currentPlan="pro",this.#o=e,this.timeLimitEnforcer=n}get hasTimeLimit(){return false}get hasInteractiveControls(){return true}handleOptionClick(e){this.currentPlan=e}handleProCtaClick(){this.onProCtaClick?.(),"pro"===this.currentPlan&&this.#o.open({trigger:"choose_plan_promo"})}};(0,n.Cg)([r.bindable,(0,n.Sn)("design:type",Function)],p.prototype,"onProCtaClick",void 0),(0,n.Cg)([(0,r.computedFrom)("timeLimitEnforcer.isEnabled"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],p.prototype,"hasTimeLimit",null),(0,n.Cg)([(0,r.computedFrom)("timeLimitEnforcer.canUseInAppControls"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],p.prototype,"hasInteractiveControls",null),p=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.U,s.il,l.z,a.Y])],p)},"pro-promos/choose-plan-promo/resources/elements/plan-selector.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});var n=o(14385),a=o.n(n),r=new URL(o(62152),o.b),s=new URL(o(77103),o.b);const i='<template> <require from="./plan-selector.scss"></require> <require from="./plan-feature"></require> <require from="resources/elements/pro-cta-label"></require> <div class="plan-selector-container" role="radiogroup" aria-labelledby="plan-selector-group-label"> <h2 id="plan-selector-group-label" class="plan-selector-heading"> ${\'choose_plan_promo.choose_your_plan\' | i18n} </h2> <div class="plan-selector-option ${currentPlan === \'free\' ? \'plan-selector-option--selected\' : \'\'}" click.delegate="handleOptionClick(\'free\')" tabindex="0"> <label for="free"> <div class="plan-selector-option-heading"> ${\'choose_plan_promo.free\' | i18n} <input type="radio" value="free" model.bind="currentPlan" name="plan-group" tabindex="-1"> </div> <div class="plan-selector-feature-grid"> <plan-feature if.bind="!hasTimeLimit"> <span class="plan-selector-feature-icon" slot="icon">all_inclusive</span> ${\'choose_plan_promo.pro_unlimited_modding\' | i18n} </plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon"><inline-svg src="'+a()(r)+'"></inline-svg></span> ${\'choose_plan_promo.free_mod_count\' | i18n} </plan-feature> <plan-feature if.bind="hasInteractiveControls"> <span class="plan-selector-feature-icon" slot="icon">page_info</span> ${\'choose_plan_promo.free_interactive_controls\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">layers</span> ${\'choose_plan_promo.free_overlay\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">map</span> ${\'choose_plan_promo.free_maps\' | i18n}</plan-feature> </div> </label> </div> <div class="plan-selector-option plan-selector-option--pro ${currentPlan === \'pro\' ? \'plan-selector-option--selected\' : \'\'}" click.delegate="handleOptionClick(\'pro\')" tabindex="0"> <label for="pro"> <div class="plan-selector-option-heading plan-selector-option-heading"> ${\'choose_plan_promo.pro\' | i18n} <input type="radio" value="pro" model.bind="currentPlan" name="plan-group" tabindex="-1"> </div> <div class="plan-selector-option-pro-badge"> <span class="plan-selector-option-pro-badge-icon"></span> ${\'choose_plan_promo.everything_in_free\' | i18n} </div> <div class="plan-selector-feature-grid"> <plan-feature if.bind="hasTimeLimit"> <span class="plan-selector-feature-icon" slot="icon">all_inclusive</span> ${\'choose_plan_promo.pro_unlimited_modding\' | i18n} </plan-feature> <plan-feature if.bind="!hasInteractiveControls"> <span class="plan-selector-feature-icon" slot="icon">page_info</span> ${\'choose_plan_promo.free_interactive_controls\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">keep</span> ${\'choose_plan_promo.pro_pin_mods\' | i18n} </plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon"><inline-svg src="'+a()(s)+'"></inline-svg></span>${\'choose_plan_promo.pro_save_mods\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">smartphone</span> ${\'choose_plan_promo.pro_remote_app\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">colors</span> ${\'choose_plan_promo.pro_custom_themes\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">ad_off</span> ${\'choose_plan_promo.pro_no_ads\' | i18n} </plan-feature> </div> </label> </div> <wm-button color.bind="currentPlan === \'free\' ? \'inverse\' : \'primary\'" click.delegate="handleProCtaClick()" full-width.bind="true"> <pro-cta-label if.bind="currentPlan === \'pro\'"></pro-cta-label> <span else>${\'choose_plan_promo.continue_with_free\' | i18n}</span> </wm-button> </div> </template> '},"pro-promos/choose-plan-promo/resources/elements/plan-selector.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>u});var n=o(31601),a=o.n(n),r=o(76314),s=o.n(r),i=o(4417),l=o.n(i),p=new URL(o(83959),o.b),c=s()(a()),d=l()(p);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${d}) format("woff2")}.material-symbols-outlined,plan-selector .plan-selector-feature-icon,plan-selector .plan-selector-option-pro-badge-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}plan-selector .plan-selector-container{display:flex;flex-direction:column;align-items:center;gap:12px}plan-selector .plan-selector-container wm-button{width:100%}plan-selector .plan-selector-heading{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;margin:0;color:var(--theme--text-primary)}plan-selector .plan-selector-feature-grid{display:grid;grid-template-columns:repeat(2, 1fr);row-gap:16px;column-gap:8px}plan-selector .plan-selector-feature-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--text-highlight);height:20px;width:20px}plan-selector .plan-selector-option{cursor:pointer}plan-selector .plan-selector-option *{cursor:pointer}plan-selector .plan-selector-option{width:100%;padding:20px;display:flex;border:1px solid rgba(255,255,255,.15);background-color:rgba(255,255,255,.05);border-radius:16px;backdrop-filter:blur(25px);color:var(--theme--text-primary)}plan-selector .plan-selector-option-pro-badge{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-primary);display:flex;align-items:center;justify-content:center;gap:8px;width:100%;padding:8px 0;border-radius:8px;background-color:rgba(255,255,255,.05)}plan-selector .plan-selector-option-pro-badge-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--text-highlight)}plan-selector .plan-selector-option-pro-badge-icon:before{font-family:inherit;content:"new_releases"}plan-selector .plan-selector-option--pro{background:radial-gradient(141.43% 141.42% at 100% 0%, rgba(31, 186, 248, 0.49) 0%, rgba(42, 155, 249, 0.48) 9.99%, rgba(56, 116, 251, 0.47) 21.4%, rgba(71, 67, 252, 0.4) 38.79%, rgba(97, 0, 255, 0.15) 58.28%, rgba(50, 0, 87, 0.05) 79.41%, rgba(50, 0, 87, 0) 100%),rgba(255,255,255,.03)}plan-selector .plan-selector-option--pro .plan-selector-option-heading{font-feature-settings:"liga" off,"clig" off;font-size:36px;font-style:italic;font-weight:900;line-height:100%;letter-spacing:-1.5px}plan-selector .plan-selector-option label{width:100%;display:flex;flex-direction:column;gap:16px}plan-selector .plan-selector-option--selected{border:1px solid #fff;background-color:rgba(255,255,255,.15);color:var(--theme--text-highlight)}plan-selector .plan-selector-option--selected.plan-selector-option--pro{background:radial-gradient(141.43% 141.42% at 100% 0%, rgba(31, 186, 248, 0.97) 0%, rgba(42, 155, 249, 0.95) 9.99%, rgba(56, 116, 251, 0.93) 21.4%, rgba(71, 67, 252, 0.8) 38.79%, rgba(97, 0, 255, 0.3) 58.28%, rgba(50, 0, 87, 0.1) 79.41%, rgba(50, 0, 87, 0) 100%),rgba(255,255,255,.03)}plan-selector .plan-selector-option--selected .plan-selector-option-heading::after{border:5px solid var(--theme--text-highlight);width:6px;height:6px}plan-selector .plan-selector-option-heading{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:24px;line-height:28px;letter-spacing:-1px;display:flex;align-items:center;justify-content:space-between;color:var(--theme--text-highlight)}plan-selector .plan-selector-option-heading input{appearance:none;position:absolute;opacity:0}plan-selector .plan-selector-option-heading::after{content:"";display:block;width:14px;height:14px;border-radius:50%;border:1px solid var(--theme--text-primary)}plan-selector .plan-selector label:has(input:checked){color:red}plan-selector plan-feature{display:flex;align-items:center}`,""]);const u=c}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8516],{12511:(e,t,s)=>{s.d(t,{M:()=>a,u:()=>o});var n=s(58534);const a=["firstRun","signOutOnStartup","giftOnStartup","proOnStartup","appRatingShownAfterTrainer","runningInTrayNotificationShown","hasUsedInteractiveControls","hasUsedHotkeys","hasUsedOverlay","hasUsedMaps","hasVisitedFacebook","hasVisitedTwitter","hasVisitedYouTube","hasClickedInstallGame","hasClickedModPin","remoteEducationShown","assistantPopoutHidden","brokenModDialogHidden","overlayAnnouncementShown","liveLocationAnnouncementShown","myVideosSeen","hasSeenUnsupportedTooltip","hideTrainerInstructionsNote","hasSeenModSuggestionsTooltip","instantHighlightAnnouncementShown","hasSeenMyVideosEducationCard"],o={catalog:null,catalogCacheKey:null,account:null,token:null,favoriteTitles:{},titleHistory:{},gameHistory:{},installedApps:{},installedGameVersions:{},trainers:{},trainerNotesRead:{},cheatBlueprintInstructionsRead:{},precisionModsSectionsViewed:{},modTimerMessagesDismissed:{},gameTranslations:{},gamePreferences:{},titlePreferences:{},correlatedUnavailableTitles:{},correlatedUnavailableTitleRefreshes:{},trainerFeedbackRequests:{},mapSettings:{},titleMapSettings:{},followedGames:[],settings:{theme:"default",analytics:!0,closeToTray:!0,cheatSounds:!0,cheatSoundVolume:50,confirmDisablingSaveCheats:!0,enableSaveCheatsByDefault:!0,useWindowsContrastMode:!0,allowDesktopNotifications:!0,preventCatalogRefresh:!1,reduceMotion:!1,showModHotkeys:!0,isTitleSidebarCollapsed:!1,enableOverlay:!0,lastChangelogIdSeen:n.A},flags:{firstRun:!0,overlayAnnouncementShown:!0,hasUsedInteractiveControls:!1,hasUsedHotkeys:!1,hasUsedOverlay:!1,hasUsedMaps:!1,hasClickedModPin:!1,hideTrainerInstructionsNote:!1},timestamps:{},installation:{installedAt:(new Date).toISOString()},promotionHistory:{},coachingTipHistory:{},gameCollectionPreferences:{},counters:{secondsPlayedToday:0,timesAssistantPopoutSeen:0},sidebarCollapsedLists:{},assistantHistory:{},pinnedMods:{},modTimers:{},acknowledgedRewards:{},favoriteVideos:[],stateVersion:null}},"store/index":(e,t,s)=>{s.r(t),s.d(t,{configure:()=>ue}),s(16566);var n=s(48881),a=s(16953);function o(e){const t=localStorage.getItem(`${a.A.storageNamespace}:${e}`);if(t)try{const e=JSON.parse(t);return i(e),e}catch{}}function r(e){localStorage.removeItem(`${a.A.storageNamespace}:${e}`)}function i(e){if("object"==typeof e&&null!==e)if(Array.isArray(e))e.forEach((e=>i(e)));else for(const t of Object.keys(e))t.endsWith("__date")?(e[t.substring(0,t.length-6)]=new Date(e[t]),delete e[t]):i(e[t])}var l=s(41882);function c(){const e={catalog:{titles:{},games:{},projects:{},creators:{}},favoriteTitles:{},gameHistory:{},projectHistory:{},installedApps:{},installedGameVersions:{},trainers:{},projectPreferences:{},playHistory:[],reportedApps:{},settings:{theme:"default",analytics:!0,closeToTray:!0,cheatSounds:!0,cheatSoundPack:null},mixpanelData:{},appInstalledAt:(new Date).toISOString(),stateVersion:1};{const t=o("appSettings")||{};t.hasOwnProperty("cheat-sounds")&&(t.cheatSounds=t["cheat-sounds"],delete t["cheat-sounds"]),t.notFirstRun=!0,o("choosePlanShownAfterTrainer")&&(t.choosePlanShownAfterTrainer=!0),delete t["recording-mode"],delete t["trainer-external-console"],delete t["cheat-sounds"],e.settings={...e.settings,...t}}{const t=o("selectedCheatSoundPack");t&&(e.settings={...e.settings,cheatSoundPack:t})}{const t=o("userUuid");t&&(e.mixpanelData={...e.mixpanelData,trackingId:t})}{const t=(o("timestamps")||{}).installedAt;t&&(e.appInstalledAt=t.toISOString())}{const t=o("usageTimestamps");t&&Object.keys(t).forEach((s=>{const n=s.split(":");if(3!==n.length)return;const[a,o,r]=n[1].split(",");if(!r)return;const i=t[s].toISOString();if("view"===n[2]){e.gameHistory={...e.gameHistory,[r]:{lastViewedAt:i}};const t=e.projectHistory[a]||{lastPlayedAt:null};e.projectHistory={...e.projectHistory,[a]:{...t,lastViewedAt:i}}}else if("play"===n[2]){const t=e.projectHistory[a]||{lastViewedAt:i};e.projectHistory={...e.projectHistory,[a]:{...t,lastPlayedAt:i}}}}))}(o("customLocations")||[]).forEach((t=>{const s=t.gameId,n=`${t.location}\\${t.command}`,a=`${s}_${n.toLocaleLowerCase()}`,o=`${l.u}:${a}`,r=[...e.installedGameVersions[s]||[]];r.some((e=>e.correlationId===o))||(r.push({gameId:s,correlationId:o,snapshotId:null,version:null,modifiedAt:null}),e.installedGameVersions={...e.installedGameVersions,[s]:r}),e.installedApps={...e.installedApps,[o]:{platform:l.u,sku:a,location:n}}}));{const t={};(o("favorites")||[]).forEach((e=>t[e]=!0)),e.favoriteTitles=t}{const t=o("customHotkeys");if(t){const s={};Object.keys(t).forEach((e=>{Object.keys(t[e]).forEach((n=>{const a=t[e][n][0];a&&["0","1"].forEach((t=>{const o=a[t];o&&Array.isArray(o)&&0!==o.length&&(s.hasOwnProperty(e)||(s[e]={}),s[e].hasOwnProperty(n)||(s[e][n]={}),s[e][n][t]=o)}))}))}));const n={};Object.keys(s).forEach((e=>{n[e]={preferredInstallation:null,customHotkeys:s[e]}})),e.projectPreferences=n}}return["activityFeed","cachedGameVersions","cachedGameVersions2","installedGames","installedGames2","titleItems","titleItems2","trainerItems","favorites","preferredCustomLocations","preferredSnapshots","lastPlayedTitle","customHotkeys","appSettings","selectedCheatSoundPack","trainerBlueprints","trainerBlueprints2","choosePlanShownAfterTrainer","referrerId","hasSeenHub","checkoutVariant","featuredContent","userUuid","lastViewedTrainerId","customLocations","usageTimestamps","notFirstRun"].forEach((e=>r(e))),e}function u(e){const t={...e,stateVersion:2};return delete t.mixpanelData,t}function d(e){const t={};Object.keys(e.installedGameVersions).forEach((s=>{t[s]=e.installedGameVersions[s].filter((e=>null===e.snapshotId)).map((e=>({gameId:e.gameId,correlationId:e.correlationId,version:e.version,modifiedAt:e.modifiedAt})))}));const s={};return Object.keys(e.gamePreferences).forEach((t=>{s[t]={customHotkeys:e.gamePreferences[t].customHotkeys}})),{...e,installedGameVersions:t,gamePreferences:s,stateVersion:3}}function g(e){return{...e,settings:{...e.settings,preferLocalCurrency:!0},stateVersion:4}}function f(e){return{...e,i18ns:{},stateVersion:5}}function m(e){return{...e,trainerFeedbackRequests:{},stateVersion:6}}var p=s(88849);function h(e){const t=new Map(Object.keys(e.catalog.projects).map((t=>[t,e.catalog.projects[t].gameId]))),s={},n=o("trainerNoteHistory")||{};Object.keys(n).forEach((e=>{n[e]&&(s[e]=(0,p.YZ)(n[e]))}));const i={};Object.keys(e.projectPreferences).forEach((s=>{const n=t.get(s);"string"==typeof n&&(i[n]=e.projectPreferences[s])}));const l=e.playHistory.map((e=>({gameId:t.get(e.projectId),playedAt:e.playedAt,duration:e.duration}))),c=o("authToken"),u=c?{accessToken:c.accessToken.value,refreshToken:c.refreshToken,userId:c.accessToken.userId,channel:`accounts.${c.accessToken.userId}`,expiresAt:Math.floor(c.accessToken.expiresAt.getTime()/1e3)}:null,d=o("installationId"),g=o("installationToken"),f={firstRun:!1,runningInTrayNotificationShown:!!e.settings.hideRunningInTrayNotification,choosePlanShownAfterTrainer:!!e.settings.choosePlanShownAfterTrainer,appRatingShownAfterTrainer:!!e.settings.appRatingShownAfterTrainer},m={...e.i18ns};delete m["en-US"];const h={...e,catalog:null,catalogCacheKey:null,gamePreferences:i,gameTranslations:{},playHistory:l,correlatedUnavailableTitles:{},correlatedUnavailableTitleRefreshes:{},trainers:{},trainerNotesRead:s,token:u,account:null,flags:f,i18ns:m,timestamps:{},changelog:null,installation:{id:"string"==typeof d?d:void 0,token:"string"==typeof g&&"string"!=typeof d?g:void 0,installedAt:e.appInstalledAt,lastVersion:"6.3.12"},settings:{cheatSoundVolume:75,...e.settings},stateVersion:7};delete h.appInstalledAt,delete h.projectHistory,delete h.projectPreferences,delete h.reportedApps,delete h.settings.userUuid,delete h.settings.notFirstRun,delete h.settings.hideRunningInTrayNotification,delete h.settings.choosePlanShownAfterTrainer,delete h.settings.appRatingShownAfterTrainer,delete h.settings.hasSeenHub,["guest","installationToken","installationId","authToken","referrerId","timestamps","changelog","choosePlanDialogShown","featuredContent2","standalonePlatformConfigs","objectives2","hasSeenReactivationDialog","trainerNoteHistory","onboardingItemStatuses"].forEach((e=>r(e)));const y=["userProfiles:","notifications:","objectives2:"];for(const e of function*(){const e=`${a.A.storageNamespace}:`;for(let t=0;t<localStorage.length;t++){const s=localStorage.key(t);"string"==typeof s&&s.startsWith(e)&&(yield s.substring(e.length))}}())y.some((t=>e.startsWith(t)))&&r(e);return h}function y(e){r("trainerProPopupHistory");const t=localStorage.getItem("gaClientId")??e.installation.id,s={...e,installation:{...e.installation,analyticsId:t},stateVersion:8};return localStorage.removeItem("gaClientId"),s}function S(e){return{...e,installedGameVersions:Object.fromEntries(Object.entries(e.installedGameVersions||{}).filter((([e])=>/^[0-9]+$/.test(e)))),stateVersion:9}}function V(e){return{...e,catalog:null,catalogCacheKey:null,correlatedUnavailableTitles:{},correlatedUnavailableTitleRefreshes:{},stateVersion:10}}function P(e){const t=e.catalog?.games;return{...e,titleHistory:t&&e.gameHistory?Object.fromEntries(Object.entries(e.gameHistory).filter((([e])=>t.hasOwnProperty(e))).map((([e,s])=>[t[e].titleId,{lastViewedAt:s.lastViewedAt??null}]))):{},gameHistory:e.gameHistory?Object.fromEntries(Object.entries(e.gameHistory).map((([e,t])=>[e,{lastPlayedAt:t.lastPlayedAt??null}]))):{},stateVersion:11}}function k(e){return{...e,stateVersion:12}}function b(e){const t={...e,stateVersion:13};return t.timestamps.lastProPopup=t.timestamps.lastEnhanceWithProPopup,delete t.timestamps.lastEnhanceWithProPopup,t}function w(e){return{...e,catalog:{...e.catalog,promotions:e.catalog?.promotions||{}},promotionHistory:{},stateVersion:14}}function v(e){const t=e.catalog.games;return Object.keys(t).forEach((e=>{const s=t[e];s.trainer&&(s.cheatCount=0)})),{...e,catalog:{...e.catalog,games:t},stateVersion:15}}function T(e){return{...e,trainers:{},cheatBlueprintInstructionsRead:{},stateVersion:16}}function I(e){const t={...e.gamePreferences};Object.keys(t).forEach((e=>{t[e].saveCheats={enabled:!1,trainerState:{}}}));const s={...e.settings,confirmDisablingSaveCheats:!0,enableSaveCheatsByDefault:!1};return{...e,gamePreferences:t,settings:s,stateVersion:17}}function A(e){return{...e,catalog:{...e.catalog,featuredGames:e.catalog?.featuredGames||[]},stateVersion:18}}function H(e){const t={...e.settings},s=["myGamesLayout","freeGamesLayout","mostPopularLayout","recentlyPlayedLayout","favoritesPlayedLayout","gamePassLayout","communityChoiceLayout"],n=Object.keys(t).find((e=>s.includes(e))),a=n?t[n]:void 0;return a&&!t.hasOwnProperty("gamesLayout")&&(t.gamesLayout=a),s.forEach((e=>{delete t[e]})),{...e,settings:t,stateVersion:19}}function C(e){const t={...e};return delete t.settings.queueAnnouncementHidden,{...t,stateVersion:20}}function j(e){return{...e,coachingTipHistory:{},stateVersion:21}}function O(e){return{...e,stateVersion:22}}function D(e){const t={...e.settings};return delete t.preferLocalCurrency,{...e,settings:t,stateVersion:23}}function E(e){const t={...e.settings};return delete t.sidebarPinned,{...e,settings:t,stateVersion:24}}function N(e){const t=e.playHistory,s=e.gameHistory;return t.forEach((e=>{const t=s[e.gameId];t&&(t.playDuration=(t.playDuration||0)+e.duration)})),{...e,gameHistory:s,stateVersion:25}}function R(e){const t={...e,settings:{...e.settings,trendiExperimentVariant:null},stateVersion:26};return delete t.flags.wasInFirstTrendiExperiment,t}function U(e){const t={...e,settings:{...e.settings},installation:{...e.installation},stateVersion:27};return delete t.settings.region,delete t.installation.analyticsId,t}function M(e){return{...e,gameCollectionPreferences:{},stateVersion:28}}function G(e){const t={...e,flags:{...e.flags},timestamps:{...e.timestamps},stateVersion:29};return delete t.flags.newUserProDiscountDialogShown,delete t.timestamps.newUserProDiscountPromoStarted,t}function L(e){const t={...e,stateVersion:30};return delete t.experiments,t}function x(e){return{...e,settings:{...e.settings,useWindowsContrastMode:!0},stateVersion:31}}function $(e){return{...e,settings:{...e.settings,language:null},i18ns:{},stateVersion:32}}function F(e){return{...e,followedGames:[],settings:{...e.settings,allowDesktopNotifications:!0},stateVersion:33}}function W(e){const t={...e,installation:{...e.installation},stateVersion:34};return delete e.installation.lastVersion,t}function B(e){return{...e,mapSettings:{},stateVersion:35}}function K(e){const t={...e,stateVersion:36};return delete t.flags.trendiExperimentVariant,t}function q(e){return{...e,titleMapSettings:{},stateVersion:37}}function _(e){const t={...e,timestamps:{...e.timestamps},flags:{...e.flags},settings:{...e.settings,enableSaveCheatsByDefault:!(e.account?.subscription&&!e.settings.enableSaveCheatsByDefault)},stateVersion:38};return delete t.flags.disableSaveCheatsEnableConfirmDialog,delete t.timestamps.lastSaveCheatsEnableConfirmDialog,t}function Y(e){return{...e,counters:{secondsPlayedToday:0},stateVersion:39}}function J(e){const t={...e.timestamps};return t.lastNewUserNPSDialog&&(t.lastNPSDialog=t.lastNewUserNPSDialog,delete t.lastNewUserNPSDialog),t.lastNewUserNPSDialogCheck&&(t.lastNPSDialogCheck=t.lastNewUserNPSDialogCheck,delete t.lastNewUserNPSDialogCheck),{...e,timestamps:t,stateVersion:40}}function Z(e){return{...e,assistantHistory:{},stateVersion:41}}function z(e){return{...e,titlePreferences:{},settings:{...e.settings,showModHotkeys:!0},stateVersion:42}}function Q(e){return{...e,sidebarCollapsedLists:{},stateVersion:43}}function X(e){const t={...e.gamePreferences};return Object.values(t).forEach((e=>e.overlaySeen=e.overlaySeen??!1)),{...e,gamePreferences:t,stateVersion:44}}function ee(e){const t={...e.settings};return t.enableOverlay=!0,{...e,settings:t,stateVersion:45}}function te(e){return{...e,objectivesWithClaimedReward:[],stateVersion:46}}function se(e){return{...e,acknowledgedRewards:{},stateVersion:47}}function ne(e){return{...e,pinnedMods:{},stateVersion:48}}function ae(e){return{...e,precisionModsSectionsViewed:{},stateVersion:49}}const oe={key:87,alt:!0,ctrl:!1,shift:!1};function re(e){const t={...e.settings};t.useAlternateOverlayHotkey&&(t.overlayHotkey=oe),delete t.useAlternateOverlayHotkey;const s={...e.gamePreferences};return Object.keys(s).forEach((e=>{s[e].overlaySeen=!1})),{...e,settings:t,gamePreferences:s,stateVersion:50}}function ie(e){return{...e,modTimerMessagesDismissed:{},stateVersion:51}}function le(e){return{...e,favoriteVideos:[],stateVersion:52}}var ce=s(12511);function ue(e,t){e.feature("shared/store/index",{debug:t.debug,storageKey:t.storageKey,initialState:ce.u,migrations:[c,u,d,g,f,m,h,y,S,V,P,k,b,w,v,T,I,A,H,C,j,O,D,E,N,R,U,M,G,L,x,$,F,W,B,K,q,_,Y,J,Z,z,Q,X,ee,te,se,ne,ae,re,ie,le],actions:(0,n.ko)(),getFallbackState:()=>o("notFirstRun")?{stateVersion:0}:null})}}}]);
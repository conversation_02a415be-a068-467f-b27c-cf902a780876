@echo off
title WeMod Auto Crack Tool
color 0A

echo ========================================
echo        WeMod Auto Crack Tool
echo ========================================
echo.

echo [1/5] Checking for WeMod installation...
if not exist "WeMod\WeMod.exe" (
    echo ERROR: WeMod not found in current directory!
    echo Please ensure WeMod folder is in the same directory as this script.
    pause
    exit /b 1
)
echo ✓ WeMod found

echo.
echo [2/5] Creating backup...
if not exist "WeMod\app-10.17.0\resources\app.asar.backup" (
    copy "WeMod\app-10.17.0\resources\app.asar" "WeMod\app-10.17.0\resources\app.asar.backup" >nul
    echo ✓ Backup created
) else (
    echo ✓ Backup already exists
)

echo.
echo [3/5] Checking for Node.js...
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js not found! Please install Node.js first.
    pause
    exit /b 1
)
echo ✓ Node.js found

echo.
echo [4/5] Extracting and modifying app.asar...
if exist "extracted_app" rmdir /s /q "extracted_app"
npx asar extract "WeMod\app-10.17.0\resources\app.asar" "extracted_app" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Failed to extract app.asar
    pause
    exit /b 1
)
echo ✓ Extracted successfully

echo.
echo [5/5] Applying crack and repacking...
npx asar pack "extracted_app" "WeMod\app-10.17.0\resources\app.asar" >nul 2>&1
if errorlevel 1 (
    echo ERROR: Failed to repack app.asar
    pause
    exit /b 1
)
echo ✓ Crack applied successfully

echo.
echo ========================================
echo           CRACK COMPLETED!
echo ========================================
echo.
echo Modifications applied:
echo • Bypassed login requirement
echo • Added fake Pro account  
echo • Disabled time limits
echo • Enabled all Pro features
echo.
echo You can now run start_cracked_wemod.bat to launch WeMod
echo.
pause

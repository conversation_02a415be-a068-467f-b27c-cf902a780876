"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[9292],{"cheats/resources/elements/custom-installation-selector":(e,t,a)=>{a.r(t),a.d(t,{CustomInstallationSelector:()=>m});var o=a(15215),i=a(16928),r=a("aurelia-event-aggregator"),l=a("aurelia-framework"),n=a(20770),s=a(84157),d=a(67064),c=a(21795),p=a(19072),g=a(89356),u=a(41882),b=a(20057),h=a(54995),f=a(48881);let m=class{#e;#t;#a;#o;#i;#r;constructor(e,t,a,o,i,r){this.showCustomExeInfo=!1,this.dragging=!1,this.#e=e,this.#t=t,this.#a=a,this.#o=o,this.#i=i,this.#r=r}attached(){this.#i.publish(new c.a8(this.gameId)),this.refresh()}gameIdChanged(){this.refresh()}stateChanged(){this.refresh()}refresh(){const e=this.state.installedGameVersions[this.gameId];this.installation=e?.map((e=>this.state.installedApps[e.correlationId])).find((e=>e?.platform===u.u))??null}removeInstallation(){this.#o.dispatch(f.HK,this.gameId)}startDrag(e){e.dataTransfer&&(e.dataTransfer.dropEffect="link"),this.dragging=!0}stopDrag(){this.dragging=!1}drop(e){const t=e.dataTransfer?.files[0];if(t){const e=s.webUtils.getPathForFile(t);this.#l(e)}}async selectInstallation(){const e=this.#a.isMacOS?["app"]:["exe"],t=await this.#a.showOpenFileDialog({title:this.#t.getValue("custom_installation_selector.select_your_game_exe"),buttonLabel:this.#t.getValue("custom_installation_selector.pick_this_game_exe"),filters:[{name:this.#t.getValue("custom_installation_selector.exe_files"),extensions:e}],properties:["openFile"]});t.filePaths&&t.filePaths.length>0&&(this.open=!1,await this.#l(t.filePaths[0]))}async#l(e){const t=this.#a.isMacOS?".app":".exe";e.toLocaleLowerCase().endsWith(".lnk")&&(e=(await this.#a.readShortcutLink(e))?.target??""),e?.toLocaleLowerCase().endsWith(t)?await this.#o.dispatch(f.SQ,this.gameId,e,await this.#n(e)):this.#e.toast({content:"custom_installation_selector.not_an_exe_toast",type:"alert"}),this.stopDrag()}async#n(e){try{return await this.#r.getGameVersion(i.dirname(e),i.basename(e),u.u)}catch{return null}}};(0,o.Cg)([l.observable,(0,o.Sn)("design:type",Object)],m.prototype,"installation",void 0),(0,o.Cg)([(0,l.bindable)({defaultBindingMode:l.bindingMode.twoWay}),(0,o.Sn)("design:type",Boolean)],m.prototype,"open",void 0),(0,o.Cg)([(0,l.bindable)({defaultBindingMode:l.bindingMode.toView}),(0,o.Sn)("design:type",String)],m.prototype,"gameId",void 0),(0,o.Cg)([(0,l.bindable)({defaultBindingMode:l.bindingMode.toView}),(0,o.Sn)("design:type",Boolean)],m.prototype,"showCustomExeInfo",void 0),m=(0,o.Cg)([(0,h.m6)(),(0,l.autoinject)(),(0,o.Sn)("design:paramtypes",[d.l,b.F2,p.s,n.il,r.EventAggregator,g.Q])],m)},"cheats/resources/elements/custom-installation-selector.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});const o='<template> <require from="./custom-installation-selector.scss"></require> <div class="installed-app" if.bind="installation"> <b>${\'custom_installation_selector.custom_game_exe\' | i18n}</b> <span title.bind="installation.location">${installation.location}</span> <button class="close" click.delegate="removeInstallation()"></button> </div> <template else> <button class="drop-target ${dragging ? \'dragging\' : \'\'}" click.delegate="selectInstallation()" dragenter.delegate="startDrag($event)" dragover.delegate="startDrag($event)" dragleave.delegate="stopDrag()" drop.delegate="drop($event)" dragend.delegate="stopDrag($event)"> <b> <i></i> ${\'custom_installation_selector.add_game_exe\' | i18n} </b> <span>${\'custom_installation_selector.or_drag_the_file_here\' | i18n}</span> </button> <p if.bind="showCustomExeInfo">${\'custom_installation_selector.custom_exe_info\' | i18n}</p> </template> </template> '},"cheats/resources/elements/custom-installation-selector.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var o=a(31601),i=a.n(o),r=a(76314),l=a.n(r),n=a(4417),s=a.n(n),d=new URL(a(83959),a.b),c=l()(i()),p=s()(d);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,custom-installation-selector .installed-app .close,custom-installation-selector .drop-target b i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}custom-installation-selector{display:block}custom-installation-selector .installed-app{background:rgba(255,255,255,.05);margin:8px;padding:8px 36px 8px 12px;border-radius:6px;display:flex;flex-direction:column;position:relative}custom-installation-selector .installed-app b{font-size:14px;line-height:21px;color:rgba(255,255,255,.8)}custom-installation-selector .installed-app span{font-size:12px;line-height:18px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;color:rgba(255,255,255,.6)}custom-installation-selector .installed-app .close{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:flex;align-items:center;justify-content:center;width:20px;height:20px;background:rgba(0,0,0,0);border:none;font-size:20px;color:#fff;opacity:.3;transition:opacity .15s;position:absolute;right:12px;top:50%;margin-top:-10px}custom-installation-selector .installed-app .close:before{font-family:inherit;content:"close"}custom-installation-selector .installed-app .close:hover{opacity:1}custom-installation-selector>p{font-weight:500;font-size:14px;line-height:20px;color:rgba(255,255,255,.6);margin:0;padding:8px}custom-installation-selector .drop-target{background:rgba(255,255,255,.05);border-radius:8px;display:flex;flex-direction:column;align-items:center;justify-content:center;width:100%;padding:20px 12px;gap:4px;border:1px dashed rgba(255,255,255,.5);backdrop-filter:blur(12px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);transition:background-color .15s}custom-installation-selector .drop-target b{font-weight:700;font-size:14px;line-height:21px;color:rgba(255,255,255,.8);display:inline-flex;align-items:center}custom-installation-selector .drop-target b i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:inline-block;width:20px;font-size:20px}custom-installation-selector .drop-target b i:before{font-family:inherit;content:"add"}custom-installation-selector .drop-target span{font-size:12px;line-height:18px;font-weight:500;color:rgba(255,255,255,.6)}custom-installation-selector .drop-target.dragging,custom-installation-selector .drop-target:hover{background-color:rgba(255,255,255,.15)}`,""]);const g=c},"cheats/resources/elements/display-options-menu":(e,t,a)=>{a.r(t),a.d(t,{DisplayOptionsMenu:()=>s});var o=a(15215),i=a("aurelia-framework"),r=a(20770),l=a(54995),n=a(48881);let s=class{#o;constructor(e){this.#o=e}handleShowModHotkeysChange(e){this.#o.dispatch(n.Kc,{showModHotkeys:e},"display_options")}};(0,o.Cg)([i.observable,(0,o.Sn)("design:type",Boolean)],s.prototype,"showModHotkeys",void 0),s=(0,o.Cg)([(0,i.autoinject)(),(0,l.m6)({selectors:{showModHotkeys:(0,l.$t)((e=>e.settings?.showModHotkeys))}}),(0,o.Sn)("design:paramtypes",[r.il])],s)},"cheats/resources/elements/display-options-menu.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});const o='<template class="au-animate"> <require from="./display-options-menu.scss"></require> <require from="../../../shared/resources/elements/toggle.html"></require> <div class="container"> <nav> <label> <i>keyboard_alt</i> <span class="label"> <div>${\'display_options_menu.show_hotkeys\' | i18n}</div> <div class="description">${\'display_options_menu.show_hotkeys_description\' | i18n}</div> </span> <toggle value.bind="showModHotkeys" change.call="handleShowModHotkeysChange(value)"></toggle> </label> </nav> </div> </template> '},"cheats/resources/elements/display-options-menu.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var o=a(31601),i=a.n(o),r=a(76314),l=a.n(r),n=a(4417),s=a.n(n),d=new URL(a(83959),a.b),c=l()(i()),p=s()(d);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,display-options-menu>.container>nav>label i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}display-options-menu{position:relative}display-options-menu>.container{display:flex;flex-direction:column;justify-content:flex-start;padding:8px;position:relative;width:280px;background:rgba(128,128,128,.1);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:16px}display-options-menu>.container>nav{display:flex;flex-direction:column;gap:1px}display-options-menu>.container>nav>label{display:flex;align-items:center;gap:8px;padding:8px;background:rgba(0,0,0,0);border:none;transition:background-color .15s;border-radius:8px;text-align:left}display-options-menu>.container>nav>label:hover{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}display-options-menu>.container>nav>label,display-options-menu>.container>nav>label *{cursor:pointer}display-options-menu>.container>nav>label i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;width:20px;height:20px;display:inline-flex;align-items:center;justify-content:center;color:var(--menu__item__icon--color, rgba(255, 255, 255, 0.6));transition:color .15s}display-options-menu>.container>nav>label .label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.6));padding:2px 0;transition:color .15s;font-size:14px;line-height:20px;font-weight:700;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.8))}display-options-menu>.container>nav>label .label .description{font-weight:500;white-space:initial}`,""]);const g=c},"cheats/resources/elements/featured-game-feed":(e,t,a)=>{a.r(t),a.d(t,{FeaturedGameFeed:()=>p});var o=a(15215),i=a("aurelia-event-aggregator"),r=a("aurelia-framework"),l=a(27958),n=a(43050),s=a(21795),d=a(54995),c=a(85975);let p=class{#s;#i;constructor(e,t){this.#s=e,this.#i=t}attached(){this.#d()}#d(){this.items=this.state.catalog.featuredGames.map((e=>{if(!e.titleId)return null;const t=this.state.catalog.titles[e.titleId],a=(0,n.ZT)(this.state,t);return{...e,feedItem:a}})).filter((e=>null!==e&&null!==e.feedItem)),this.itemCount=this.items.length}openItem(e){if(e.url)window.open(e.url,"_blank");else{if(!e.feedItem)return;this.#i.publish(new s.dY(this.location,e.feedItem.titleId,e.feedItem.gameId)),this.#s.router.navigateToRoute("title",{titleId:e.feedItem.titleId,gameId:e.feedItem.gameId,trainerId:"",previousRoute:this.previousRoute})}}stateChanged(){this.#d()}};(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",Array)],p.prototype,"metadataTypes",void 0),(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",String)],p.prototype,"location",void 0),(0,o.Cg)([r.bindable,(0,o.Sn)("design:type",String)],p.prototype,"previousRoute",void 0),(0,o.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.fromView}),(0,o.Sn)("design:type",Number)],p.prototype,"itemCount",void 0),p=(0,o.Cg)([(0,r.autoinject)(),(0,d.m6)((e=>e.state.pipe((0,c.r)("catalog")))),(0,o.Sn)("design:paramtypes",[l.L,i.EventAggregator])],p)},"cheats/resources/elements/featured-game-feed.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});const o='<template> <require from="./featured-game-feed.scss"></require> <require from="../../../resources/elements/favorite-button"></require> <require from="../../../shared/cheats/resources/value-converters/important-genres"></require> <div class="wrapper"> <a repeat.for="item of items" class="item" href="#" click.delegate="openItem(item)" css="background-image: url(\'${item.image | cdn}\')"> <div class="title-row"> <span class="title-name">${item.feedItem.titleName}</span> <favorite-button if.bind="item.feedItem.isAvailable || item.feedItem.isInstalled" title-id.bind="item.titleId"></favorite-button> </div> </a> </div> </template> '},"cheats/resources/elements/featured-game-feed.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});var o=a(31601),i=a.n(o),r=a(76314),l=a.n(r)()(i());l.push([e.id,'featured-game-feed .wrapper{display:grid;grid-auto-columns:1fr;gap:16px;grid-auto-flow:column}featured-game-feed .wrapper .item{display:flex;flex:1;flex-direction:column;position:relative;z-index:0;background-position:center center;background-size:cover;background-repeat:no-repeat;border-radius:16px;height:248px;border:2px solid rgba(0,0,0,0);transition:border .15s}featured-game-feed .wrapper .item:hover{border:2px solid var(--theme--highlight)}featured-game-feed .wrapper .item favorite-button button:not(:hover){background:none}featured-game-feed .wrapper .item:before{content:"";display:block;position:absolute;width:calc(100% + 4px);height:calc(100% + 4px);background:radial-gradient(355.47% 141.42% at 100% 0%, rgba(var(--theme--background-accent--rgb), 0) 49.93%, var(--theme--background-accent) 100%);z-index:0;border-radius:16px}featured-game-feed .wrapper .item .title-row{position:absolute;display:flex;width:100%;justify-content:space-between;align-items:flex-end;bottom:0;padding:16px;z-index:2}featured-game-feed .wrapper .item .title-row .title-name{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:24px;line-height:28px;letter-spacing:-1px;text-shadow:0px 4px 4px rgba(0,0,0,.5);padding:4px}.theme-default featured-game-feed .wrapper .item .title-row .title-name{color:#fff}.theme-purple-pro featured-game-feed .wrapper .item .title-row .title-name{color:#fff}.theme-green-pro featured-game-feed .wrapper .item .title-row .title-name{color:#fff}.theme-orange-pro featured-game-feed .wrapper .item .title-row .title-name{color:#fff}.theme-pro featured-game-feed .wrapper .item .title-row .title-name{color:#fff}featured-game-feed .wrapper .item .title-row .title-name,featured-game-feed .wrapper .item .title-row favorite-button{transition:opacity .2s}featured-game-feed .wrapper .item .title-row .title-name button .favorite,featured-game-feed .wrapper .item .title-row favorite-button button .favorite{color:#fff}',""]);const n=l},"cheats/resources/elements/feedback-dialog":(e,t,a)=>{a.r(t),a.d(t,{FeedbackDialog:()=>b,FeedbackDialogService:()=>h});var o=a(15215),i=a("aurelia-dialog"),r=a("aurelia-framework"),l=a(20770),n=a(45660),s=a(19072),d=a(56669),c=a(20057),p=a(88849),g=a(48881),u=a("cheats/resources/value-converters/blueprint-translation");let b=class{#c;#p;#g;#t;constructor(e,t,a){this.confirmedNotes=!1,this.success=null,this.crashLocationOptions=[d.BW.Other,d.BW.MainMenu,d.BW.Loading,d.BW.InGamePlaying,d.BW.InGameMenu],this.crashLocationLabels={undefined:"feedback_dialog.select_a_location",[d.BW.Other]:"feedback_dialog.other",[d.BW.MainMenu]:"feedback_dialog.main_menu_or_before",[d.BW.Loading]:"feedback_dialog.cutscene_or_loading_screen",[d.BW.InGamePlaying]:"feedback_dialog.in_game_playing",[d.BW.InGameMenu]:"feedback_dialog.in_game_menu_inventory"},this.cheatIssueTypeOptions=[d.h1.Other,d.h1.GameCrash,d.h1.NoEffect,d.h1.NotAsExpected],this.cheatIssueTypeLabels={undefined:"feedback_dialog.select_a_reason",[d.h1.Other]:"feedback_dialog.other",[d.h1.GameCrash]:"feedback_dialog.game_crash",[d.h1.NoEffect]:"feedback_dialog.didnt_work_no_effect",[d.h1.NotAsExpected]:"feedback_dialog.not_as_expected"},this.successReport={feedback:null,allowPublic:!0},this.failureReport={crashLocation:null,allCheatsBroken:!1,cheats:[],notes:null},this.canSubmitChangeFlag=!1,this.#p=e,this.#g=t,this.#t=a}activate(e){this.success=e.success,this.trainer=e.trainer,this.gameName=e.gameName,this.username=e.username,this.profileImage=e.profileImage,this.cheats=e.cheatUuids.map((t=>e.trainer.blueprint.cheats.find((e=>e.uuid===t)))).filter((e=>!!e)),this.#c=e.requireSuccessFeedback,this.#u()}reevaluateCanSubmit(){this.canSubmitChangeFlag=!this.canSubmitChangeFlag}selectSuccess(){this.success=!0,this.#c||this.submit()}get canSubmit(){if(!0===this.success)return!this.#c||"string"==typeof this.successReport.feedback&&this.successReport.feedback.trim().length>=5;if(!1!==this.success)return!1;switch(this.failureReason){case"crash":return void 0!==this.failureReport.crashLocation;case"cheats":return this.failureReport.cheats.length>0&&this.failureReport.cheats.every((e=>void 0!==e.type));case"other":return null!==(0,p.jD)(this.failureReport.notes);default:return!1}}cancel(){this.#p.cancel()}submit(){if(!this.canSubmit)return;let e=null;!0===this.success&&(e=this.#c?this.successReport:null),!1===this.success&&("crash"!==this.failureReason&&(this.failureReport.crashLocation=null),"cheats"!==this.failureReason&&(this.failureReport.cheats=[],this.failureReport.allCheatsBroken=!1),e=this.failureReport),this.#p.close(!0,e)}deactivate(e){let t=null;!0===this.success&&(t=d.M3.Success),!1!==this.success||this.trainer.blueprint.notes&&!this.confirmedNotes||(t=d.M3.Failure);const a={shown:!0,reportType:t,report:e.wasCancelled?null:e.output};e.output=a}cheatToAddChanged(){void 0!==this.cheatToAdd&&(this.#b(this.cheatToAdd),setTimeout((()=>this.cheatToAdd=void 0)))}#u(){const e=this.cheats.filter((e=>!this.failureReport.cheats.find((t=>t.uuid===e.uuid))));this.availableCheats=e,this.availableCheatsOptions=Object.keys(this.availableCheats),this.availableCheatsLabels={undefined:this.#t.getValue("feedback_dialog.select_a_cheat")},this.availableCheats.forEach(((e,t)=>{this.availableCheatsLabels[t]=this.#g.toView(e.name,this.trainer)}))}#b(e){const t=this.availableCheats[e];this.failureReport.cheats.push({type:void 0,notes:null,uuid:t.uuid}),this.#u(),this.availableCheatsOptions.length||this.failureReport.allCheatsBroken||(this.failureReport.allCheatsBroken=!0),this.reevaluateCanSubmit()}removeCheat(e){this.failureReport.cheats.splice(e,1),this.#u(),this.failureReport.allCheatsBroken&&(this.failureReport.allCheatsBroken=!1)}addAllCheats(){this.cheats.forEach((e=>{this.failureReport.cheats.find((t=>t.uuid===e.uuid))||this.failureReport.cheats.push({type:void 0,notes:null,uuid:e.uuid})})),this.#u()}getCheatNameFromUuid(e){const t=this.cheats.find((t=>t.uuid===e));return t?this.#g.toView(t.name,this.trainer):""}};(0,o.Cg)([r.observable,(0,o.Sn)("design:type",Object)],b.prototype,"cheatToAdd",void 0),(0,o.Cg)([(0,r.computedFrom)("success","successReport.feedback","failureReason","failureReport.notes","failureReport.crashLocation","failureReport.cheats","canSubmitChangeFlag"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],b.prototype,"canSubmit",null),b=(0,o.Cg)([(0,r.autoinject)(),(0,o.Sn)("design:paramtypes",[i.DialogController,u.BlueprintTranslationValueConverter,c.F2])],b);let h=class{#o;#h;#f;constructor(e,t,a){this.#o=e,this.#h=t,this.#f=a}async collectFeedback(e,t,a,o,i,r=null,l=!1){if(!this.#f.info.locale.startsWith("en"))return{shown:!1};const s=await this.#o.state.pipe((0,n.$)()).toPromise(),c=s.trainerFeedbackRequests||{},p=`${t.id}:${a||""}:${o??""}`,u=c.hasOwnProperty(p),b=c[p]||[],h=i.filter((e=>!b.includes(e))),f=(s.gameHistory[t.gameId]||{}).lastPositiveFeedbackAt,m="string"!=typeof f||f.localeCompare(new Date(Date.now()-2592e6).toISOString())<0;if(!l&&u&&0===h.length)return{shown:!1};const x=await this.#h.open({viewModel:"cheats/resources/elements/feedback-dialog",host:document.querySelector("#dialogs")??document.body,model:{username:s.account?.username,profileImage:s.account?.profileImage,trainer:t,cheatUuids:h,gameName:e,success:r,requireSuccessFeedback:m}}).whenClosed((e=>e));this.#o.dispatch(g.ul,t.id,a,o,h);const v=x.output;return v.reportType===d.M3.Success&&v.report&&this.#o.dispatch(g.H,t.gameId),v}};h=(0,o.Cg)([(0,r.autoinject)(),(0,o.Sn)("design:paramtypes",[l.il,i.DialogService,s.s])],h)},"cheats/resources/elements/feedback-dialog.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>u});var o=a(14385),i=a.n(o),r=new URL(a(56815),a.b),l=new URL(a(94637),a.b),n=new URL(a(36052),a.b),s=new URL(a(89247),a.b),d=new URL(a(37237),a.b),c=new URL(a(7061),a.b),p=new URL(a(27685),a.b),g=new URL(a(34635),a.b);const u='<template> <require from="./feedback-dialog.scss"></require> <require from="../value-converters/blueprint-translation"></require> <require from="../../../shared/resources/elements/close-button"></require> <require from="../../../shared/resources/elements/selection-input"></require> <ux-dialog class="feedback-dialog"> <close-button click.delegate="cancel()" tabindex="0"></close-button> <ux-dialog-body> <template if.bind="success === null"> <p class="message">${\'feedback_dialog.how_was_playing_$game_with_wemod\' | i18n:{game: gameName}}</p> <div class="switch-to-report-buttons"> <button click.delegate="selectSuccess()"> <inline-svg src="'+i()(r)+'"></inline-svg> </button> <button click.delegate="success = false"> <inline-svg src="'+i()(l)+'"></inline-svg> </button> </div> </template> <div class="success-report" if.bind="success === true"> <header> <span class="header-icon"> <inline-svg src="'+i()(n)+'"></inline-svg> </span> <span class="label">${\'feedback_dialog.say_thanks_to_the_developer\' | i18n}</span> </header> <textarea value.bind="successReport.feedback | nullIfEmpty" placeholder="${\'feedback_dialog.type_here\' | i18n}"></textarea> <footer class="buttons"> <button class="cta" disabled.bind="!canSubmit" click.delegate="submit()"> ${\'feedback_dialog.send\' | i18n} </button> <label class="public-toggle ${successReport.allowPublic ? \'checked\' : \'\'}" click.delegate="successReport.allowPublic = !!!successReport.allowPublic"> <span class="checkbox"></span> <span class="label">${\'feedback_dialog.allow_posting_publicly\' | i18n}</span> </label> <span class="user"> <img fallback-src="'+i()(s)+'" src.bind="profileImage | cdn:{size: 32}"> <span>${username}</span> </span> </footer> </div> <div class="failure-report" show.bind="success === false"> <section class="confirm-notes" if.bind="trainer.blueprint.notes && !confirmedNotes"> <header> <span class="label">${\'feedback_dialog.did_you_read_the_notes\' | i18n}</span> </header> <hr> <p innerhtml.bind="trainer.blueprint.notes | blueprintTranslation:trainer | markdown"></p> <div class="buttons"> <button class="cta" click.delegate="confirmedNotes = true"> ${\'feedback_dialog.report_problem\' | i18n} </button> <button class="secondary" click.delegate="cancel()">${\'feedback_dialog.cancel\' | i18n}</button> </div> </section> <section class="feedback" show.bind="!trainer.blueprint.notes || confirmedNotes"> <header> <span class="label">${\'feedback_dialog.which_best_describes_experience\' | i18n}</span> </header> <div class="reason-selector"> <button class="${failureReason === \'crash\' ? \'selected\' : \'\'}" click.delegate="failureReason = \'crash\'"> <i><inline-svg src="'+i()(d)+"\"></inline-svg></i> <span>${'feedback_dialog.my_game_crashed' | i18n}</span> </button> <button if.bind=\"cheats.length\" class=\"${failureReason === 'cheats' ? 'selected' : ''}\" click.delegate=\"failureReason = 'cheats'\"> <i><inline-svg src=\""+i()(c)+"\"></inline-svg></i> <span>${'feedback_dialog.cheats_broken_or_confusing' | i18n}</span> </button> <button class=\"${failureReason === 'other' ? 'selected' : ''}\" click.delegate=\"failureReason = 'other'\"> <i><inline-svg src=\""+i()(p)+'"></inline-svg></i> <span>${\'feedback_dialog.other_issue\' | i18n}</span> </button> </div> <div show.bind="failureReason"> <hr> <div class="reason" if.bind="failureReason === \'crash\'"> <h1>${\'feedback_dialog.where_were_you_in_the_game_when_it_crashed\' | i18n}</h1> <selection-input value.two-way="failureReport.crashLocation" options.bind="crashLocationOptions" labels.bind="crashLocationLabels"> </selection-input> <h1>${\'feedback_dialog.any_extra_details_that_can_help_us\' | i18n}</h1> <textarea value.bind="failureReport.notes | nullIfEmpty" placeholder="${\'feedback_dialog.extra_details_example\' | i18n}"></textarea> </div> <div class="reason" if.bind="failureReason === \'cheats\'"> <h1>${\'feedback_dialog.which_cheats_experienced_trouble\' | i18n}</h1> <div repeat.for="cheat of failureReport.cheats" class="cheat"> <div class="name">${getCheatNameFromUuid(cheat.uuid)}</div> <selection-input on-value-changed.call="reevaluateCanSubmit()" value.two-way="cheat.type" options.bind="cheatIssueTypeOptions" labels.bind="cheatIssueTypeLabels" open-initially.bind="openNextIssueTypeSelectInitially" class="required"> </selection-input> <textarea placeholder="${\'feedback_dialog.optional_additional_info\' | i18n}" value.bind="cheat.notes | nullIfEmpty"></textarea> <button class="close-button" click.delegate="removeCheat($index)"> <i><inline-svg src="'+i()(g)+'"></inline-svg></i> </button> </div> <selection-input if.bind="availableCheats.length" value.two-way="cheatToAdd" options.bind="availableCheatsOptions" labels.bind="availableCheatsLabels" enable-i18n.bind="false"> </selection-input> <a if.bind="!failureReport.cheats.length" href="#" click.delegate="addAllCheats()">${\'feedback_dialog.all_of_the_cheats\' | i18n}</a> <h1>${\'feedback_dialog.any_extra_details_that_can_help_us\' | i18n}</h1> <textarea value.bind="failureReport.notes | nullIfEmpty" placeholder="${\'feedback_dialog.other_details_example\' | i18n}"></textarea> </div> <div class="reason" if.bind="failureReason === \'other\'"> <h1>${\'feedback_dialog.please_describe_the_problem\' | i18n}</h1> <textarea value.bind="failureReport.notes | nullIfEmpty" placeholder="${\'feedback_dialog.other_details_example\' | i18n}"></textarea> </div> <footer> <p innerhtml.bind="\'feedback_dialog.cheat_log_will_be_sent\' | i18n | markdown"></p> <div class="buttons"> <button class="cancel" click.delegate="cancel()"> ${\'feedback_dialog.cancel\' | i18n} </button> <button class="cta" disabled.bind="!canSubmit" click.delegate="submit()"> ${\'feedback_dialog.submit_feedback\' | i18n} </button> </div> </footer> </div> </section> </div> </ux-dialog-body> </ux-dialog> </template> '},"cheats/resources/elements/feedback-dialog.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var o=a(31601),i=a.n(o),r=a(76314),l=a.n(r),n=a(4417),s=a.n(n),d=new URL(a(83959),a.b),c=new URL(a(81206),a.b),p=l()(i()),g=s()(d),u=s()(c);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.feedback-dialog{width:auto;max-width:550px;margin-bottom:30px}.feedback-dialog .message{text-align:center;color:rgba(255,255,255,.8)}.feedback-dialog .switch-to-report-buttons{margin-top:20px;text-align:center}.feedback-dialog .switch-to-report-buttons button{padding:0;background:rgba(0,0,0,0);border:0;transition:transform .15s}.feedback-dialog .switch-to-report-buttons button:hover{transform:scale(1.05)}.feedback-dialog .switch-to-report-buttons button:hover svg path:first-child{fill-opacity:.2}.feedback-dialog .switch-to-report-buttons button:first-child{margin-right:20px}.feedback-dialog header{margin-bottom:15px;display:flex;align-items:center}.feedback-dialog header img{width:23px;vertical-align:middle;margin-right:10px}.feedback-dialog header .label{font-weight:700;font-size:15px;line-height:24px;font-weight:700;color:#fff}.feedback-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none}.feedback-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.feedback-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.feedback-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.feedback-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.feedback-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.feedback-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.feedback-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.feedback-dialog textarea:disabled{opacity:.5}.feedback-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.feedback-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.feedback-dialog .buttons{display:flex;align-items:center;margin-top:15px}.feedback-dialog .buttons .cta{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:800;font-size:21px;line-height:30px;font-weight:800;--cta--padding: 18px;--cta--height: 39px;--cta--hover--border-width: 2px}.feedback-dialog .buttons .cta,.feedback-dialog .buttons .cta *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .cta{border:1px solid #fff}}.feedback-dialog .buttons .cta>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.feedback-dialog .buttons .cta>*:first-child{padding-left:0}.feedback-dialog .buttons .cta>*:last-child{padding-right:0}.feedback-dialog .buttons .cta svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .cta svg *{fill:CanvasText}}.feedback-dialog .buttons .cta svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .cta svg{opacity:1}}.feedback-dialog .buttons .cta img{height:50%}.feedback-dialog .buttons .cta:disabled{opacity:.3}.feedback-dialog .buttons .cta:disabled,.feedback-dialog .buttons .cta:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.feedback-dialog .buttons .cta:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.feedback-dialog .buttons .cta:not(:disabled):hover svg{opacity:1}}.feedback-dialog .buttons .cta:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.feedback-dialog .buttons .cta:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.feedback-dialog .buttons .cta:not(:disabled):active{background-color:var(--theme--highlight)}.feedback-dialog .buttons .secondary{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:800;font-size:21px;line-height:30px;font-weight:800;--cta--padding: 18px;--cta--height: 39px;--cta--hover--border-width: 2px;background-color:rgba(255,255,255,.6);box-shadow:none;color:var(--theme--background)}.feedback-dialog .buttons .secondary,.feedback-dialog .buttons .secondary *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .secondary{border:1px solid #fff}}.feedback-dialog .buttons .secondary>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.feedback-dialog .buttons .secondary>*:first-child{padding-left:0}.feedback-dialog .buttons .secondary>*:last-child{padding-right:0}.feedback-dialog .buttons .secondary svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .secondary svg *{fill:CanvasText}}.feedback-dialog .buttons .secondary svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .secondary svg{opacity:1}}.feedback-dialog .buttons .secondary img{height:50%}.feedback-dialog .buttons .secondary:disabled{opacity:.3}.feedback-dialog .buttons .secondary:disabled,.feedback-dialog .buttons .secondary:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.feedback-dialog .buttons .secondary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.feedback-dialog .buttons .secondary:not(:disabled):hover svg{opacity:1}}.feedback-dialog .buttons .secondary:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.feedback-dialog .buttons .secondary svg{opacity:1}.feedback-dialog .buttons .secondary svg *{--cta__icon--color: var(--theme--background)}@media(hover: hover){.feedback-dialog .buttons .secondary:not(:disabled):hover{background-color:var(--theme--highlight)}}.feedback-dialog .buttons .cancel{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:800;font-size:21px;line-height:30px;font-weight:800;--cta--padding: 18px;--cta--height: 39px;--cta--hover--border-width: 2px;box-shadow:none}.feedback-dialog .buttons .cancel,.feedback-dialog .buttons .cancel *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .cancel{border:1px solid #fff}}.feedback-dialog .buttons .cancel>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.feedback-dialog .buttons .cancel>*:first-child{padding-left:0}.feedback-dialog .buttons .cancel>*:last-child{padding-right:0}.feedback-dialog .buttons .cancel svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .cancel svg *{fill:CanvasText}}.feedback-dialog .buttons .cancel svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .cancel svg{opacity:1}}.feedback-dialog .buttons .cancel img{height:50%}.feedback-dialog .buttons .cancel:disabled{opacity:.3}.feedback-dialog .buttons .cancel:disabled,.feedback-dialog .buttons .cancel:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.feedback-dialog .buttons .cancel:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.feedback-dialog .buttons .cancel:not(:disabled):hover svg{opacity:1}}.feedback-dialog .buttons .cancel:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .buttons .cancel{border:0 !important}}.feedback-dialog .buttons button+button{margin-left:15px !important}.feedback-dialog .public-toggle{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;width:280px;display:flex;align-items:center;margin-left:20px}.feedback-dialog .public-toggle,.feedback-dialog .public-toggle *{cursor:pointer}.feedback-dialog .public-toggle>*:first-child{margin-right:9px}.feedback-dialog .public-toggle .checkbox{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;width:15px;height:15px;background:rgba(0,0,0,0);border-color:rgba(255,255,255,.25)}.feedback-dialog .public-toggle .checkbox,.feedback-dialog .public-toggle .checkbox *{cursor:pointer}.feedback-dialog .public-toggle .checkbox:checked:before{opacity:1}.feedback-dialog .public-toggle .checkbox:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${u});mask:url(${u})}.feedback-dialog .public-toggle .checkbox:before{left:1px;top:0;width:15px;height:11px;transform:scale(1)}.feedback-dialog .public-toggle .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color)}.feedback-dialog .public-toggle .label,.feedback-dialog .public-toggle .label *{cursor:pointer}.feedback-dialog .public-toggle.checked .checkbox:before{opacity:1}.feedback-dialog .user{display:flex;align-items:center;justify-content:flex-end}.feedback-dialog .user img{display:inline-block;border-radius:50%;overflow:hidden;width:26px;height:26px;margin-right:9px}.feedback-dialog .user span{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.6)}.feedback-dialog .success-report textarea{height:130px}.feedback-dialog .success-report .header-icon{margin-right:9px}.feedback-dialog .success-report .header-icon svg *{fill:var(--theme--highlight)}.feedback-dialog .failure-report .feedback header{display:block;text-align:center;margin-top:10px}.feedback-dialog .failure-report hr{border-width:0;border-top:1px solid rgba(255,255,255,.1);margin:0 0 15px 0}.feedback-dialog .failure-report p{margin:0;padding:0}.feedback-dialog .failure-report p+p{margin-top:15px}.feedback-dialog .failure-report h1{font-weight:600;font-size:16px;line-height:25px;font-weight:700;color:#fff}.feedback-dialog .failure-report textarea{height:80px}.feedback-dialog .failure-report .buttons{justify-content:flex-end}.feedback-dialog .failure-report .cheat{position:relative;border:1px solid rgba(255,255,255,.1);padding:20px 20px 10px 20px;margin-top:15px;margin-bottom:15px}.feedback-dialog .failure-report .cheat .name{font-size:13px;line-height:20px;position:absolute;left:10px;top:-15px;padding:5px;color:rgba(255,255,255,.6)}.theme-default .feedback-dialog .failure-report .cheat .name{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro .feedback-dialog .failure-report .cheat .name{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro .feedback-dialog .failure-report .cheat .name{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro .feedback-dialog .failure-report .cheat .name{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro .feedback-dialog .failure-report .cheat .name{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.feedback-dialog .failure-report .cheat .close-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)) !important;display:inline-flex;width:26px;height:26px;box-shadow:none;padding:0;border-radius:50%;border:0;align-items:center;justify-content:center;transition:background-color .15s;position:absolute;right:-12px;top:-12px}.feedback-dialog .failure-report .cheat .close-button,.feedback-dialog .failure-report .cheat .close-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .failure-report .cheat .close-button{border:1px solid #fff}}.feedback-dialog .failure-report .cheat .close-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.feedback-dialog .failure-report .cheat .close-button>*:first-child{padding-left:0}.feedback-dialog .failure-report .cheat .close-button>*:last-child{padding-right:0}.feedback-dialog .failure-report .cheat .close-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .failure-report .cheat .close-button svg *{fill:CanvasText}}.feedback-dialog .failure-report .cheat .close-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .failure-report .cheat .close-button svg{opacity:1}}.feedback-dialog .failure-report .cheat .close-button img{height:50%}.feedback-dialog .failure-report .cheat .close-button:disabled{opacity:.3}.feedback-dialog .failure-report .cheat .close-button:disabled,.feedback-dialog .failure-report .cheat .close-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.feedback-dialog .failure-report .cheat .close-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.feedback-dialog .failure-report .cheat .close-button:not(:disabled):hover svg{opacity:1}}.feedback-dialog .failure-report .cheat .close-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(forced-colors: active){body:not(.override-contrast-mode) .feedback-dialog .failure-report .cheat .close-button{border:1px solid #fff}}.feedback-dialog .failure-report .cheat .close-button svg{opacity:1}@media(hover: hover){.feedback-dialog .failure-report .cheat .close-button:hover{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.25)) !important}}.feedback-dialog .failure-report .cheat selection-input{margin-bottom:15px}.feedback-dialog .failure-report .cheat textarea{margin-bottom:0;height:auto}.feedback-dialog .failure-report selection-input{width:300px;margin-bottom:5px;padding-right:15px}.feedback-dialog .failure-report selection-input.required{position:relative}.feedback-dialog .failure-report selection-input.required:before{content:"*";position:absolute;right:0;top:5px;color:var(--color--alert)}.feedback-dialog .failure-report a{font-size:13px;line-height:20px;color:rgba(255,255,255,.5);text-decoration:underline}.feedback-dialog .failure-report a:hover{color:#fff}.feedback-dialog .failure-report .reason-selector{display:flex;justify-content:center;margin-bottom:15px}.feedback-dialog .failure-report .reason-selector button{background:rgba(0,0,0,0);border:0;display:flex;flex-direction:column;width:110px;align-items:center}.feedback-dialog .failure-report .reason-selector button span{font-size:13px;line-height:20px;font-weight:700;color:rgba(255,255,255,.5);text-align:center;transition:color .15s}.feedback-dialog .failure-report .reason-selector button i{display:flex;align-items:center;justify-content:center;border:1px solid rgba(255,255,255,.1);border-radius:4px;width:50px;height:50px;margin-bottom:10px;transition:border-color .15s}.feedback-dialog .failure-report .reason-selector button svg{width:30px}.feedback-dialog .failure-report .reason-selector button svg *{fill:rgba(255,255,255,.1);transition:fill .15s}.feedback-dialog .failure-report .reason-selector button+button{margin-left:10px}.feedback-dialog .failure-report .reason-selector button:hover i,.feedback-dialog .failure-report .reason-selector button.selected i{border-color:var(--theme--highlight)}.feedback-dialog .failure-report .reason-selector button:hover i svg *,.feedback-dialog .failure-report .reason-selector button.selected i svg *{fill:var(--theme--highlight)}.feedback-dialog .failure-report .reason-selector button:hover span,.feedback-dialog .failure-report .reason-selector button.selected span{color:#fff}.feedback-dialog .failure-report footer p{font-size:13px;line-height:20px;margin:15px 0 25px 0}`,""]);const b=p},"cheats/resources/elements/first-play-upgrade-prompt-dialog":(e,t,a)=>{a.r(t),a.d(t,{FirstPlayUpgradePromptDialog:()=>b,FirstPlayUpgradePromptDialogService:()=>h});var o=a(15215),i=a("aurelia-dialog"),r=a("aurelia-framework"),l=a(20770),n=a(62914),s=a(17275),d=a("shared/i18n/resources/value-converters"),c=a(54995),p=a(48881);const g=[{imageClass:"jump-right-back-into-playing",title:"first_play_upgrade_prompt_dialog.jump_into_playing",subtitle:"first_play_upgrade_prompt_dialog.level_up_with_pro_to_get",bulletOne:"first_play_upgrade_prompt_dialog.save_mods",bulletTwo:"first_play_upgrade_prompt_dialog.no_ads",bulletThree:"first_play_upgrade_prompt_dialog.and_much_more",bitwiseId:2}],u=g.reduce(((e,t)=>e+t.bitwiseId),0);let b=class{#m;#o;constructor(e,t,a,o){this.controller=e,this.i18nNumber=a,this.#m=t,this.#o=o}async setFirstPlayModalData(){let e=this.seenFirstPlayModals??0;e>u&&(e=0);let t=g.filter((t=>!(t.bitwiseId&e)));t.length||(t=g);const a=t[0];let o=e+a.bitwiseId;o>=u&&(o=0),await this.#o.dispatch(p.TU,"seenFirstPlayModals",o),this.firstPlayModalData=a}async activate(){this.#o.dispatch(p.vk,"lastFirstPlayUpgradePrompt"),await this.setFirstPlayModalData(),this.#m.event("pro_firstplay_upgrade_modal_show",{modalVariation:this.firstPlayModalData?.imageClass},n.Io)}deactivate(e){e.wasCancelled&&this.#m.event("pro_firstplay_upgrade_modal_dismiss",{method:e.output||"escape",modalVariation:this.firstPlayModalData?.imageClass},n.Io)}handleUpgradeClick(){this.controller.close(!0),this.#m.event("pro_firstplay_upgrade_modal_cta_click",{modalVariation:this.firstPlayModalData?.imageClass},n.Io)}};b=(0,o.Cg)([(0,r.autoinject)(),(0,c.m6)({setup:"activate",selectors:{trustpilotReviews:(0,c.$t)((e=>e.catalog?.stats?.trustpilotReviews)),seenFirstPlayModals:(0,c.$t)((e=>e.counters?.seenFirstPlayModals))}}),(0,o.Sn)("design:paramtypes",[i.DialogController,n.j0,d.I18nNumberValueConverter,l.il])],b);class h extends s.C{constructor(){super(...arguments),this.viewModelClass="cheats/resources/elements/first-play-upgrade-prompt-dialog"}}},"cheats/resources/elements/first-play-upgrade-prompt-dialog.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var o=a(14385),i=a.n(o),r=new URL(a(10059),a.b);const l='<template> <require from="./first-play-upgrade-prompt-dialog.scss"></require> <require from="../../../resources/elements/pro-cta-label"></require> <require from="../../../shared/resources/elements/close-button"></require> <let features.bind="[\'controls\', \'boosting\', \'save\', \'ads\', \'overlay\', \'remote\']"></let> <ux-dialog class="first-play-upgrade-prompt-dialog ${firstPlayModalData.imageClass}"> <ux-dialog-header> <close-button click.trigger="controller.cancel(\'close_button\')"></close-button> </ux-dialog-header> <ux-dialog-body> <h1 innerhtml.bind="firstPlayModalData.title | i18n | markdown"></h1> <p innerhtml.bind="firstPlayModalData.subtitle | i18n | markdown"></p> <ul> <li>${firstPlayModalData.bulletOne | i18n}</li> <li>${firstPlayModalData.bulletTwo | i18n}</li> <li>${firstPlayModalData.bulletThree | i18n}</li> </ul> <div class="actions"> <button class="primary" pro-cta="trigger: pro_firstplay_upgrade_modal" click.delegate="handleUpgradeClick()"> <pro-cta-label></pro-cta-label> </button> <br> <a click.delegate="controller.cancel(\'stay_free_cta\')">${\'first_play_upgrade_prompt_dialog.stay_free_and_start_game\' | i18n} <i><inline-svg src="'+i()(r)+'"></inline-svg></i></a> </div> </ux-dialog-body> </ux-dialog> </template> '},"cheats/resources/elements/first-play-upgrade-prompt-dialog.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>f});var o=a(31601),i=a.n(o),r=a(76314),l=a.n(r),n=a(4417),s=a.n(n),d=new URL(a(88037),a.b),c=new URL(a(76663),a.b),p=new URL(a(16280),a.b),g=l()(i()),u=s()(d),b=s()(c),h=s()(p);g.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}ux-dialog.first-play-upgrade-prompt-dialog{border:0;width:900px;padding:43px 48px 40px;background:url(${u}) calc(100% - 35px) 20px no-repeat,linear-gradient(0deg, var(--theme--secondary-background) 0%, #000 100%)}ux-dialog.first-play-upgrade-prompt-dialog h1{font-size:30px;line-height:36px;color:#fff;margin:0 0 13px}ux-dialog.first-play-upgrade-prompt-dialog h1 em{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;vertical-align:middle;margin:-2px 0 0 5px}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog.first-play-upgrade-prompt-dialog h1 em{border:1px solid #fff}}ux-dialog.first-play-upgrade-prompt-dialog.jump-right-back-into-playing{background:url(${b}) calc(100% - 35px) 40px/50% no-repeat,linear-gradient(0deg, var(--theme--secondary-background) 0%, #000 100%)}ux-dialog.first-play-upgrade-prompt-dialog p{font-size:18px;line-height:30px;color:rgba(255,255,255,.5);margin:0 0 25px;display:inline-block;max-width:330px}ux-dialog.first-play-upgrade-prompt-dialog p em{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;vertical-align:middle}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog.first-play-upgrade-prompt-dialog p em{border:1px solid #fff}}ux-dialog.first-play-upgrade-prompt-dialog ul{list-style:none;margin:0 0 27px;padding:0}ux-dialog.first-play-upgrade-prompt-dialog ul li{font-size:14px;line-height:21px;font-weight:600;color:#fff}ux-dialog.first-play-upgrade-prompt-dialog ul li:before{content:"";display:inline-block;width:15px;height:11px;-webkit-mask-box-image:url(${h});margin-right:9px;background:var(--color--accent);transition:transform .15s}ux-dialog.first-play-upgrade-prompt-dialog ul li i{display:inline-block;vertical-align:middle;margin:-9px 0 0 5px}ux-dialog.first-play-upgrade-prompt-dialog ul li+li{margin-top:15px}ux-dialog.first-play-upgrade-prompt-dialog .actions{display:grid;grid-template-columns:1fr 1fr;width:fit-content;min-width:610px}ux-dialog.first-play-upgrade-prompt-dialog .actions button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:600;--cta--padding: 20px;--cta--height: 52px;--cta--hover--border-width: 2px;font-size:22px}ux-dialog.first-play-upgrade-prompt-dialog .actions button,ux-dialog.first-play-upgrade-prompt-dialog .actions button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog.first-play-upgrade-prompt-dialog .actions button{border:1px solid #fff}}ux-dialog.first-play-upgrade-prompt-dialog .actions button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}ux-dialog.first-play-upgrade-prompt-dialog .actions button>*:first-child{padding-left:0}ux-dialog.first-play-upgrade-prompt-dialog .actions button>*:last-child{padding-right:0}ux-dialog.first-play-upgrade-prompt-dialog .actions button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog.first-play-upgrade-prompt-dialog .actions button svg *{fill:CanvasText}}ux-dialog.first-play-upgrade-prompt-dialog .actions button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog.first-play-upgrade-prompt-dialog .actions button svg{opacity:1}}ux-dialog.first-play-upgrade-prompt-dialog .actions button img{height:50%}ux-dialog.first-play-upgrade-prompt-dialog .actions button:disabled{opacity:.3}ux-dialog.first-play-upgrade-prompt-dialog .actions button:disabled,ux-dialog.first-play-upgrade-prompt-dialog .actions button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){ux-dialog.first-play-upgrade-prompt-dialog .actions button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}ux-dialog.first-play-upgrade-prompt-dialog .actions button:not(:disabled):hover svg{opacity:1}}ux-dialog.first-play-upgrade-prompt-dialog .actions button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}ux-dialog.first-play-upgrade-prompt-dialog .actions button+button{margin-left:10px}ux-dialog.first-play-upgrade-prompt-dialog .actions button.primary{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){ux-dialog.first-play-upgrade-prompt-dialog .actions button.primary:hover{filter:brightness(1.1)}}ux-dialog.first-play-upgrade-prompt-dialog .actions button.secondary{box-shadow:none;color:rgba(255,255,255,.8);background:var(--theme--secondary-background)}ux-dialog.first-play-upgrade-prompt-dialog .actions a{text-align:center;margin-top:21px}`,""]);const f=g}}]);
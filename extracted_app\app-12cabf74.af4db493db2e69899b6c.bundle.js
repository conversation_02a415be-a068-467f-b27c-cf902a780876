"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1245],{18165:(i,e,t)=>{t.d(e,{v:()=>r});var a=t(15215),o=t("aurelia-framework"),n=t(43544),l=t("dialogs/feature-announcement-dialog");let r=class{#i;constructor(i,e){this.overlayHotkeyService=e,this.#i=i}open(){return this.#i.open({featureName:"overlay",headerKey:"overlay_announcement_dialog.introducing_the_all_new",featureTitleKey:"overlay_announcement_dialog.wemod_overlay",messageKey:"overlay_announcement_dialog.a_faster_more_responsive_way_to_enhance_your_gameplay",messageParams:{hotkey:this.overlayHotkeyService.displayHotkey},videoUrl:"https://media.wemod.com/videos/feature-announcements/overlay.mp4",loopVideo:!1})}};r=(0,a.Cg)([(0,o.autoinject)(),(0,a.Sn)("design:paramtypes",[l.FeatureAnnouncementDialogService,n.u])],r)},"dialogs/overlay-nps-dialog":(i,e,t)=>{t.r(e),t.d(e,{OverlayNpsDialog:()=>d,OverlayNpsDialogService:()=>c});var a=t(15215),o=t("aurelia-dialog"),n=t("aurelia-framework"),l=t(62914),r=t(17275),s=t(54995);let d=class{#e;constructor(i,e){this.controller=i,this.feedback="",this.#e=e}setRating(i){this.rating=i}attached(){this.#e.event("overlay_nps_dialog_open",{userId:this.account.uuid},l.Io)}async close(){void 0!==this.rating&&this.#e.event("overlay_nps_dialog_submit",{score:this.rating,userId:this.account.uuid,feedback:this.feedback??""},l.Io),await this.controller.close(!0,this.rating)}async skipFeedback(){this.feedback="",await this.close()}async sendFeedback(){await this.close()}};d=(0,a.Cg)([(0,n.autoinject)(),(0,s.m6)({selectors:{account:(0,s.$t)((i=>i.account))}}),(0,a.Sn)("design:paramtypes",[o.DialogController,l.j0])],d);let c=class extends r.C{constructor(){super(...arguments),this.viewModelClass="dialogs/overlay-nps-dialog"}};c=(0,a.Cg)([(0,n.autoinject)()],c)},"dialogs/overlay-nps-dialog.html":(i,e,t)=>{t.r(e),t.d(e,{default:()=>a});const a='<template> <require from="./overlay-nps-dialog.scss"></require> <require from="./resources/elements/nps-score-selector"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="overlay-nps-dialog align-center"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <template if.bind="rating === undefined"> <header>${\'overlay_nps_dialog.how_satisfied_are_you_with_the_overlay\' | i18n}</header> <nps-score-selector set-rating.call="setRating(rating)"> <span slot="lowScoreLabel"> ${\'overlay_nps_dialog.not_satisfied_at_all\' | i18n} </span> <span slot="highScoreLabel"> ${\'overlay_nps_dialog.extremely_satisfied\' | i18n} </span> </nps-score-selector> </template> <template else> <header>${\'overlay_nps_dialog.thank_you_for_your_feedback\' | i18n}</header> <p>${\'overlay_nps_dialog.let_us_know_how_to_improve\' | i18n}</p> <textarea placeholder="${\'overlay_nps_dialog.feedback_placeholder\' | i18n}" value.bind="feedback" rows="5"></textarea> </template> </ux-dialog-body> <ux-dialog-footer if.bind="rating !== undefined"> <div class="buttons"> <button click.delegate="sendFeedback()">${\'overlay_nps_dialog.send\' | i18n}</button> <button class="secondary" click.delegate="skipFeedback()"> ${\'overlay_nps_dialog.skip_feedback\' | i18n} </button> </div> </ux-dialog-footer> </ux-dialog> </template> '},"dialogs/overlay-nps-dialog.scss":(i,e,t)=>{t.r(e),t.d(e,{default:()=>p});var a=t(31601),o=t.n(a),n=t(76314),l=t.n(n),r=t(4417),s=t.n(r),d=new URL(t(83959),t.b),c=l()(o()),g=s()(d);c.push([i.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.nps-dialog nps-score-selector{margin:23px auto 8px}.nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea:disabled{opacity:.5}.nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.nps-dialog header+p{margin-top:12px}.nps-dialog .score-selector{display:inline-block}.nps-dialog .buttons{display:flex;align-items:start}.overlay-nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.overlay-nps-dialog nps-score-selector{margin:23px auto 8px}.overlay-nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.overlay-nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.overlay-nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.overlay-nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.overlay-nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.overlay-nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.overlay-nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.overlay-nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.overlay-nps-dialog textarea:disabled{opacity:.5}.overlay-nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.overlay-nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.overlay-nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.overlay-nps-dialog header+p{margin-top:12px}.overlay-nps-dialog .score-selector{display:inline-block}.overlay-nps-dialog .buttons{display:flex;align-items:start}`,""]);const p=c},"dialogs/payment-processing-dialog":(i,e,t)=>{t.r(e),t.d(e,{PaymentProcessingDialog:()=>l,PaymentProcessingDialogService:()=>r});var a=t(15215),o=t("aurelia-dialog"),n=t("aurelia-framework");class l{}let r=class{#t;constructor(i){this.#t=i}async show(){this.#a()||await this.#t.open({viewModel:"dialogs/payment-processing-dialog",host:document.querySelector("#dialogs")||document.body,startingZIndex:1002,lock:!0,keyboard:!1})}async hide(){await(this.#a()?.close(!0))}#a(){for(const i of this.#t.controllers)if(i.controller.viewModel instanceof l)return i;return null}};r=(0,a.Cg)([(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[o.DialogService])],r)},"dialogs/payment-processing-dialog.html":(i,e,t)=>{t.r(e),t.d(e,{default:()=>a});const a='<template> <require from="../shared/resources/elements/payment-processing"></require> <payment-processing loading.bind="true" modal.bind="true"></payment-processing> </template> '},"dialogs/plan-details-dialog":(i,e,t)=>{t.r(e),t.d(e,{PlanDetailsDialog:()=>h,PlanDetailsDialogService:()=>u});var a=t(15215),o=t("aurelia-dialog"),n=t("aurelia-framework"),l=t(32534),r=t(10699),s=t(62914),d=t("dialogs/webview-dialog"),c=t(17275),g=t(20057),p=t(54995),b=t("dialogs/pro-onboarding-dialog");let h=class{#o;#e;#n;#l;constructor(i,e,t,a,o){this.controller=i,this.busy=!1,this.#o=e,this.#e=t,this.#n=a,this.#l=o}activate(){this.#e.event("plan_details_open",{},s.Io)}async cancelSubscription(){this.#e.event("plan_details_cancel_click",{},s.Io);const i=await this.#o.open({route:"cancel-flow"});this.controller.close(!0,{subscriptionCanceled:!i.wasCancelled})}get isValidPeriod(){return!!this.account.subscription&&["weekly","monthly","quarterly","yearly"].includes(this.account.subscription.period)}get trialDaysLeft(){if(this.account.subscription&&this.account.subscription.trialEndsAt)return(0,l.A)(Date.now(),new Date(this.account.subscription.trialEndsAt))+1}get nextInvoiceAmount(){const i=this.account?.subscription?.nextInvoice;if(!i)return 0;const e=parseFloat(i.amount);return(0,g.rx)(i.currency)?e:100*e}handleLinkClicks(i){let e=i.target;for(;e;){if("A"===e.tagName){this.controller.cancel();break}e=e.parentElement}return!0}openProOnboardingDialog(){this.controller.cancel(),this.#n.open({trigger:"plan_details_dialog"})}async openSwitchToAnnualDialog(){(await this.#o.open({route:"switch-to-annual",params:{trigger:"plan_details"}})).wasCancelled||await this.#l.refreshAccount()}};(0,a.Cg)([(0,n.computedFrom)("account.subscription.period"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],h.prototype,"isValidPeriod",null),(0,a.Cg)([(0,n.computedFrom)("account.subscription.trialing","account.subscription.trialEndsAt"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],h.prototype,"trialDaysLeft",null),(0,a.Cg)([(0,n.computedFrom)("account.subscription.nextInvoice"),(0,a.Sn)("design:type",Number),(0,a.Sn)("design:paramtypes",[])],h.prototype,"nextInvoiceAmount",null),h=(0,a.Cg)([(0,p.m6)({selectors:{account:(0,p.$t)((i=>i.account))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[o.DialogController,d.WebviewDialogService,s.j0,b.ProOnboardingDialogService,r.G])],h);let u=class extends c.C{constructor(){super(...arguments),this.viewModelClass="dialogs/plan-details-dialog"}};u=(0,a.Cg)([(0,n.autoinject)()],u)},"dialogs/plan-details-dialog.html":(i,e,t)=>{t.r(e),t.d(e,{default:()=>l});var a=t(14385),o=t.n(a),n=new URL(t(76386),t.b);const l='<template> <require from="./plan-details-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="plan-details-dialog scrollable"> <div class="dialog-scroll-wrapper"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <span class="pro-badge">${\'plan_details_dialog.pro\' | i18n}</span> <h1>${\'plan_details_dialog.plan_details\' | i18n}</h1> </ux-dialog-header> <ux-dialog-body> <div class="info" if.bind="account.subscription"> <div class="row" if.bind="isValidPeriod || account.subscription.nextInvoice"> <div class="col highlight" if.bind="isValidPeriod"> ${`plan_details_dialog.billed_${account.subscription.period}` | i18n} </div> <div class="col highlight" if.bind="account.subscription.nextInvoice"> ${nextInvoiceAmount | i18nCurrency:account.subscription.nextInvoice.currency} </div> </div> <div class="meta" if.bind="account.subscription.trialEndsAt"> ${\'plan_details_dialog.$x_days_left_in_trial\' | i18n: {x: trialDaysLeft}} </div> <div class="meta" if.bind="account.subscription.endsAt"> ${\'plan_details_dialog.until\' | i18n} <em>${account.subscription.endsAt | i18nDateTime:{dateStyle:\'medium\'}}</em> </div> <div class="meta" if.bind="account.subscription.nextInvoice && !account.subscription.pastDueInvoice"> ${\'plan_details_dialog.next_billing\' | i18n} <em>${account.subscription.nextInvoice.date | i18nDateTime:{dateStyle:\'medium\'}}</em> </div> </div> <div class="bubble" if.bind="(account.subscription.period === \'monthly\' && account.subscription.state === \'active\') || account.subscription.pastDueInvoice"> <div class="warning" if.bind="account.subscription.pastDueInvoice"> <span>${\'plan_details_dialog.payment_overdue\' | i18n}</span><em>${account.subscription.pastDueInvoice.date | i18nDateTime:{dateStyle:\'medium\'}}</em> </div> <div class="switch-to-annual" if.bind="account.subscription.period === \'monthly\' && account.subscription.state === \'active\'"> <a class="button" href="#" click.delegate="openSwitchToAnnualDialog()">${\'plan_details_dialog.switch_to_yearly\' | i18n}</a> <span class="promo"> ${\'plan_details_dialog.save_$x_or_more\' | i18n:{x: 25}} <i><inline-svg src="'+o()(n)+"\"></inline-svg></i> </span> </div> </div> <div class=\"features\"> <header>${'plan_details_dialog.your_pro_plan_includes' | i18n}</header> <ul> <li>${'plan_details_dialog.save_cheats' | i18n}</li> <li>${'plan_details_dialog.remote_mobile_app' | i18n}</li> <li>${'plan_details_dialog.game_boosting' | i18n}</li> <li>${'plan_details_dialog.exclusive_themes' | i18n}</li> <li>${'plan_details_dialog.discord_role' | i18n}</li> <li>${'plan_details_dialog.priority_support' | i18n}</li> </ul> <a href=\"#\" click.delegate=\"openProOnboardingDialog()\">${'plan_details_dialog.learn_more' | i18n}</a> </div> <div class=\"help bubble\"> <h2>${'plan_details_dialog.need_help' | i18n}</h2> <p innerhtml.bind=\"'plan_details_dialog.help_message' | i18n | markdown\" click.delegate=\"handleLinkClicks($event)\" tabindex=\"0\"></p> </div> <div class=\"actions\"> <button click.delegate=\"cancelSubscription()\" disabled.bind=\"busy\"> ${'plan_details_dialog.cancel_subscription' | i18n} </button> </div> </ux-dialog-body> </div> </ux-dialog> </template> "},"dialogs/plan-details-dialog.scss":(i,e,t)=>{t.r(e),t.d(e,{default:()=>y});var a=t(31601),o=t.n(a),n=t(76314),l=t.n(n),r=t(4417),s=t.n(r),d=new URL(t(96369),t.b),c=new URL(t(76048),t.b),g=new URL(t(81206),t.b),p=new URL(t(79948),t.b),b=new URL(t(16280),t.b),h=l()(o()),u=s()(d),m=s()(c),f=s()(g),x=s()(p),v=s()(b);h.push([i.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.billing-settings section{padding:20px;border-radius:10px;border:1px solid rgba(255,255,255,.04);transition:opacity .15s}.billing-settings section.filled{background:rgba(255,255,255,.04);border:none;padding-top:18px}.billing-settings section.filled h5{color:rgba(255,255,255,.6)}.billing-settings section.filled h5.plan-header{margin-bottom:16px;color:#fff}.billing-settings section+section{margin-top:20px}.billing-settings section.loading{opacity:.4}.billing-settings section.loading,.billing-settings section.loading *{pointer-events:none}.billing-settings section.layout{display:flex}.billing-settings section.layout>*:first-child{flex:1 1 auto}.billing-settings section.layout>*:last-child{flex:0 0 auto}.billing-settings .details{display:flex;align-items:center}.billing-settings .details .meta{display:flex;align-items:center;color:rgba(255,255,255,.5)}.billing-settings .details .meta>*+*{margin-left:7px}.billing-settings .details .meta,.billing-settings .details .meta strong,.billing-settings .details .meta em{font-size:13px;line-height:20px;font-weight:500}.billing-settings .details .meta em{font-style:normal;color:rgba(255,255,255,.8)}.billing-settings .details .card-type{width:34px;height:22px;border-radius:4px;background-position:center;background-repeat:no-repeat;background-image:url(${u});display:inline-block;flex:0 0 auto}.billing-settings .details .meta.payment{display:flex;flex-direction:column;align-items:flex-start;justify-content:center}.billing-settings .details .meta.payment>*+*{margin-left:0}.billing-settings .details .meta.payment.canceled{border-left:1px solid rgba(255,255,255,.1);padding-left:20px;flex-direction:row;align-items:center;gap:10px}.billing-settings .details .meta.warning,.billing-settings .details .meta.warning *{color:var(--color--accent-yellow)}.billing-settings .details .row-actions{display:flex;align-items:center}.billing-settings .details .row-actions .links{display:flex;align-items:center;margin-left:20px;padding-left:20px;border-left:1px solid rgba(255,255,255,.1)}.billing-settings .details .row-actions .links .remove{color:rgba(var(--color--alert--rgb), 0.8)}.billing-settings .details .row-actions .links .remove:hover{color:#fff}.billing-settings .link{font-size:13px;line-height:20px;font-weight:700;background:rgba(0,0,0,0);border:0;color:rgba(255,255,255,.25);padding:0}.billing-settings .link:hover{color:#fff}.billing-settings .resume-btn{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700;padding:20px 16px}.billing-settings .resume-btn,.billing-settings .resume-btn *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn{border:1px solid #fff}}.billing-settings .resume-btn>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .resume-btn>*:first-child{padding-left:0}.billing-settings .resume-btn>*:last-child{padding-right:0}.billing-settings .resume-btn svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn svg *{fill:CanvasText}}.billing-settings .resume-btn svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn svg{opacity:1}}.billing-settings .resume-btn img{height:50%}.billing-settings .resume-btn:disabled{opacity:.3}.billing-settings .resume-btn:disabled,.billing-settings .resume-btn:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .resume-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .resume-btn:not(:disabled):hover svg{opacity:1}}.billing-settings .resume-btn:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.billing-settings .resume-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.billing-settings .resume-btn:not(:disabled):active{background-color:var(--theme--highlight)}.billing-settings h5{font-weight:600;font-size:16px;line-height:25px;font-weight:700;color:#fff;margin:0 0 11px}.billing-settings h5 em{font-style:normal;color:var(--theme--highlight)}.billing-settings h5 strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;margin-right:4px;vertical-align:middle}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings h5 strong{border:1px solid #fff}}.billing-settings h5+.details{margin-top:2px}.billing-settings h5.warning{color:var(--color--accent-yellow) !important;display:inline-flex;align-items:center}.billing-settings h5.warning:before{display:inline-block;content:"";width:15px;height:15px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${m}) 6px 3px no-repeat}.billing-settings h5.warning:before{margin-right:8px}.billing-settings .canceled-details{display:flex;align-items:center;border-left:1px solid rgba(255,255,255,.1);padding-left:20px;margin-left:20px}.billing-settings .canceled-details .meta{display:flex;align-items:center;color:rgba(255,255,255,.5)}.billing-settings .canceled-details .meta>*+*{margin-left:7px}.billing-settings .canceled-details .meta,.billing-settings .canceled-details .meta strong,.billing-settings .canceled-details .meta em{font-size:13px;line-height:20px;font-weight:500}.billing-settings .canceled-details .meta em{font-style:normal;color:rgba(255,255,255,.8)}.billing-settings .canceled-details .card-type{width:34px;height:22px;border-radius:4px;background-position:center;background-repeat:no-repeat;background-image:url(${u});display:inline-block;flex:0 0 auto}.billing-settings .main-actions{margin:17px 0 0 0}.billing-settings .main-actions>*+*{margin-left:15px}.billing-settings .main-actions .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent);font-weight:800;--cta--padding: 18px;--cta--height: 40px;--cta--hover--border-width: 2px;font-size:18px}.billing-settings .main-actions .button,.billing-settings .main-actions .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button{border:1px solid #fff}}.billing-settings .main-actions .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .main-actions .button>*:first-child{padding-left:0}.billing-settings .main-actions .button>*:last-child{padding-right:0}.billing-settings .main-actions .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button svg *{fill:CanvasText}}.billing-settings .main-actions .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button svg{opacity:1}}.billing-settings .main-actions .button img{height:50%}.billing-settings .main-actions .button:disabled{opacity:.3}.billing-settings .main-actions .button:disabled,.billing-settings .main-actions .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .main-actions .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .main-actions .button:not(:disabled):hover svg{opacity:1}}.billing-settings .main-actions .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.billing-settings .main-actions .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}.billing-settings .main-actions .button:not(:disabled):active{background-color:var(--color--accent)}.billing-settings .main-actions .button:not(:disabled):active{--cta__icon--color: #000;color:#000}.billing-settings .main-actions .button.main{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.billing-settings .main-actions .button.main:hover{filter:brightness(1.1)}}.billing-settings .main-actions .button.secondary{background-color:rgba(255,255,255,.6);box-shadow:none;color:var(--theme--background)}.billing-settings .main-actions .button.secondary svg{opacity:1}.billing-settings .main-actions .button.secondary svg *{--cta__icon--color: var(--theme--background)}@media(hover: hover){.billing-settings .main-actions .button.secondary:not(:disabled):hover{background-color:var(--theme--highlight)}}.billing-settings .main-actions .button.accent{background-color:rgba(var(--color--accent--rgb), 0.08) !important;color:var(--color--accent) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.billing-settings .main-actions .button.accent:hover{filter:brightness(1.1)}}.billing-settings .main-actions .button.small{font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}.billing-settings .main-actions .divider{border-left:1px solid rgba(255,255,255,.1);height:28px}.billing-settings .main-actions .promo{font-size:13px;line-height:20px;color:var(--color--accent);display:inline-flex;align-items:center}.billing-settings .main-actions .promo i{margin-left:7px}.billing-settings .main-actions .promo i svg *{fill:var(--color--accent)}.billing-settings .alert{margin-top:8px}.billing-settings .info{font-size:14px;line-height:21px;font-weight:500;line-height:19px;color:rgba(255,255,255,.5)}.billing-settings .disclaimer{font-size:13px;line-height:20px;font-weight:500;color:rgba(255,255,255,.3);display:flex;align-items:center;margin:20px 0 0 0}.billing-settings .disclaimer .icon{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;--cta--height: 15px;--cta--hover--border-width: 1px;min-width:var(--cta--height);width:var(--cta--height);border-radius:50%;justify-content:center;align-items:center;position:relative;background:rgba(255,255,255,.1);box-shadow:none !important;pointer-events:none;margin-right:7px}.billing-settings .disclaimer .icon,.billing-settings .disclaimer .icon *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon{border:1px solid #fff}}.billing-settings .disclaimer .icon>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .disclaimer .icon>*:first-child{padding-left:0}.billing-settings .disclaimer .icon>*:last-child{padding-right:0}.billing-settings .disclaimer .icon svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon svg *{fill:CanvasText}}.billing-settings .disclaimer .icon svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon svg{opacity:1}}.billing-settings .disclaimer .icon img{height:50%}.billing-settings .disclaimer .icon:disabled{opacity:.3}.billing-settings .disclaimer .icon:disabled,.billing-settings .disclaimer .icon:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .disclaimer .icon:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .disclaimer .icon:not(:disabled):hover svg{opacity:1}}.billing-settings .disclaimer .icon:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.billing-settings .disclaimer .icon,.billing-settings .disclaimer .icon>*{padding:0 !important}.billing-settings .disclaimer .icon:active{background-color:rgba(0,0,0,0) !important;color:rgba(255,255,255,.8) !important}.billing-settings .disclaimer .icon svg{opacity:.5}.billing-settings .disclaimer a{color:rgba(255,255,255,.5)}.billing-settings .disclaimer a:hover{color:#fff}.billing-settings .graphic{margin:0 0 -20px 0;justify-self:flex-end}.billing-settings .subscribed{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;--checkbox--checked-color: var(--color--accent);border:1px solid rgba(255,255,255,.15);border-radius:100px;padding:8px 15px;transition:background-color .15s,border-color .15s;background-color:rgba(var(--color--accent--rgb), 0.08);cursor:initial;border-color:rgba(0,0,0,0);--checkbox__label--color: var(--checkbox--checked-color);margin-right:15px}.billing-settings .subscribed,.billing-settings .subscribed *{cursor:pointer}.billing-settings .subscribed,.billing-settings .subscribed *{cursor:pointer}.billing-settings .subscribed>*:first-child{margin-right:9px}.billing-settings .subscribed:hover>*{--checkbox__label--color: #fff}.billing-settings .subscribed,.billing-settings .subscribed *:not(info-tooltip){cursor:default !important}.billing-settings .subscribed .checkbox{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;width:15px;height:15px;background:rgba(0,0,0,0);border-color:rgba(255,255,255,.25);border-color:rgba(0,0,0,0)}.billing-settings .subscribed .checkbox,.billing-settings .subscribed .checkbox *{cursor:pointer}.billing-settings .subscribed .checkbox:checked:before{opacity:1}.billing-settings .subscribed .checkbox:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${f});mask:url(${f})}.billing-settings .subscribed .checkbox:before{left:1px;top:0;width:15px;height:11px;transform:scale(1)}.billing-settings .subscribed .checkbox:before{opacity:1}.billing-settings .subscribed>.icon{margin-right:9px}.billing-settings .subscribed>.icon svg *{fill:var(--color--accent)}.billing-settings .subscribed .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color);transition:color .15s;color:var(--color--accent) !important}.billing-settings .subscribed .label,.billing-settings .subscribed .label *{cursor:pointer}.billing-settings .subscribed info-tooltip{margin-left:10px}.billing-settings .subscribed info-tooltip,.billing-settings .subscribed info-tooltip *{cursor:pointer}.billing-settings .subscribed.warning{background:rgba(var(--color--accent-yellow--rgb), 0.08)}.billing-settings .subscribed.warning .label{color:var(--color--accent-yellow) !important}.billing-settings .subscribed.warning .checkbox{display:none}.billing-settings .subscribed.warning:before{display:inline-block;content:"";width:19px;height:19px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${x}) center no-repeat;margin-right:11px}.billing-settings .subscribed.canceled{background:rgba(var(--theme--highlight--rgb), 0.08)}.billing-settings .subscribed.canceled .checkbox:before{background:var(--theme--highlight) !important}.billing-settings .subscribed.canceled .label{color:var(--theme--highlight) !important}.billing-settings .wemod-tag{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:normal;text-transform:capitalize;white-space:nowrap;padding:1px 6px}.billing-settings hr{border:0;border-top:1px solid rgba(255,255,255,.1);margin:20px 0 16px}.plan-details-dialog{background:var(--theme--secondary-background) !important;width:450px;padding:0}.plan-details-dialog .dialog-scroll-wrapper{padding:40px;height:535px}.plan-details-dialog ux-dialog-header .pro-badge{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;margin-bottom:8px}@media(forced-colors: active){body:not(.override-contrast-mode) .plan-details-dialog ux-dialog-header .pro-badge{border:1px solid #fff}}.plan-details-dialog ux-dialog-header h1{font-size:30px;line-height:36px;color:#fff;margin:0 0 30px}.plan-details-dialog .info .row{font-size:16px;line-height:24px;font-weight:600;display:flex;align-items:center}.plan-details-dialog .info .row+.row{margin-top:8px}.plan-details-dialog .info .row+.meta{margin-top:4px}.plan-details-dialog .info .col:first-child{flex:1}.plan-details-dialog .info .col:last-child{justify-content:flex-end}.plan-details-dialog .info .highlight{color:var(--theme--highlight)}.plan-details-dialog .info .meta{font-size:13px;line-height:20px;color:rgba(255,255,255,.5)}.plan-details-dialog .info .meta em{font-weight:500;color:rgba(255,255,255,.8)}.plan-details-dialog .info+.bubble{margin-top:10px}.plan-details-dialog .bubble{padding:15px;background:rgba(255,255,255,.04);border-radius:10px}.plan-details-dialog .bubble .warning{font-size:13px;line-height:20px;font-weight:500;color:rgba(var(--color--accent-yellow--rgb), 0.5);display:inline-flex;align-items:center}.plan-details-dialog .bubble .warning:before{display:inline-block;content:"";width:15px;height:15px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${m}) 6px 3px no-repeat}.plan-details-dialog .bubble .warning:before{margin-right:8px}.plan-details-dialog .bubble .warning em{color:var(--color--accent-yellow);margin-left:8px}.plan-details-dialog .bubble .switch-to-annual{display:flex;align-items:center}.plan-details-dialog .bubble .switch-to-annual .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent);font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;font-weight:800;background-color:rgba(var(--color--accent--rgb), 0.08) !important;color:var(--color--accent) !important;transition:filter .15s;box-shadow:none !important}.plan-details-dialog .bubble .switch-to-annual .button,.plan-details-dialog .bubble .switch-to-annual .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .plan-details-dialog .bubble .switch-to-annual .button{border:1px solid #fff}}.plan-details-dialog .bubble .switch-to-annual .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.plan-details-dialog .bubble .switch-to-annual .button>*:first-child{padding-left:0}.plan-details-dialog .bubble .switch-to-annual .button>*:last-child{padding-right:0}.plan-details-dialog .bubble .switch-to-annual .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .plan-details-dialog .bubble .switch-to-annual .button svg *{fill:CanvasText}}.plan-details-dialog .bubble .switch-to-annual .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .plan-details-dialog .bubble .switch-to-annual .button svg{opacity:1}}.plan-details-dialog .bubble .switch-to-annual .button img{height:50%}.plan-details-dialog .bubble .switch-to-annual .button:disabled{opacity:.3}.plan-details-dialog .bubble .switch-to-annual .button:disabled,.plan-details-dialog .bubble .switch-to-annual .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.plan-details-dialog .bubble .switch-to-annual .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.plan-details-dialog .bubble .switch-to-annual .button:not(:disabled):hover svg{opacity:1}}.plan-details-dialog .bubble .switch-to-annual .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.plan-details-dialog .bubble .switch-to-annual .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}.plan-details-dialog .bubble .switch-to-annual .button:not(:disabled):active{background-color:var(--color--accent)}.plan-details-dialog .bubble .switch-to-annual .button:not(:disabled):active{--cta__icon--color: #000;color:#000}@media(hover: hover){.plan-details-dialog .bubble .switch-to-annual .button:hover{filter:brightness(1.1)}}.plan-details-dialog .bubble .switch-to-annual .promo{font-size:13px;line-height:20px;color:var(--color--accent);display:inline-flex;align-items:center;margin-left:15px}.plan-details-dialog .bubble .switch-to-annual .promo i{margin-left:7px}.plan-details-dialog .bubble .switch-to-annual .promo i svg *{fill:var(--color--accent)}.plan-details-dialog .bubble .warning+.switch-to-annual{margin-top:13px}.plan-details-dialog .features{margin:20px 0;padding:20px 0;border-top:1px solid rgba(255,255,255,.1);border-bottom:1px solid rgba(255,255,255,.1)}.plan-details-dialog .features header{font-size:11px;line-height:17px;letter-spacing:1px;font-weight:600;color:var(--color--accent);margin:0 0 17px}.plan-details-dialog .features ul{list-style:none;margin:0 0 20px;padding:0}.plan-details-dialog .features ul li{font-size:14px;line-height:21px;line-height:19px;color:#fff}.plan-details-dialog .features ul li:before{content:"";display:inline-block;width:15px;height:11px;vertical-align:middle;background:var(--color--accent);-webkit-mask-box-image:url(${v});margin-right:15px}.plan-details-dialog .features ul li+li{margin-top:9px}.plan-details-dialog .features a{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;display:flex;width:100%}.plan-details-dialog .features a,.plan-details-dialog .features a *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .plan-details-dialog .features a{border:1px solid #fff}}.plan-details-dialog .features a>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.plan-details-dialog .features a>*:first-child{padding-left:0}.plan-details-dialog .features a>*:last-child{padding-right:0}.plan-details-dialog .features a svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .plan-details-dialog .features a svg *{fill:CanvasText}}.plan-details-dialog .features a svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .plan-details-dialog .features a svg{opacity:1}}.plan-details-dialog .features a img{height:50%}.plan-details-dialog .features a:disabled{opacity:.3}.plan-details-dialog .features a:disabled,.plan-details-dialog .features a:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.plan-details-dialog .features a:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.plan-details-dialog .features a:not(:disabled):hover svg{opacity:1}}.plan-details-dialog .features a:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.plan-details-dialog .help{margin:0 0 30px}.plan-details-dialog .help h2{font-weight:700;font-size:15px;line-height:24px;font-weight:700;color:rgba(255,255,255,.8);margin:0 0 2px}.plan-details-dialog .help p{color:rgba(255,255,255,.5)}.plan-details-dialog .help p,.plan-details-dialog .help p a{font-size:13px;line-height:20px}.plan-details-dialog .help p a{color:var(--theme--highlight)}.plan-details-dialog .help p a:hover{color:#fff}.plan-details-dialog .actions{text-align:center}.plan-details-dialog .actions button{font-size:11px;line-height:17px;letter-spacing:1px;font-weight:600;display:inline-block;background:rgba(0,0,0,0);border:0;padding:0;color:rgba(255,255,255,.4)}.plan-details-dialog .actions button:hover{color:#fff}`,""]);const y=h},"dialogs/post-assistant-nps-dialog":(i,e,t)=>{t.r(e),t.d(e,{PostAssistantNpsDialog:()=>s,PostAssistantNpsDialogService:()=>d});var a=t(15215),o=t("aurelia-dialog"),n=t("aurelia-framework"),l=t(62914),r=t(17275);let s=class{#r;#e;constructor(i,e){this.controller=i,this.feedback="",this.#e=e}setRating(i){this.rating=i}activate(i){this.#r=i}attached(){this.#e.event("post_assistant_nps_dialog_open",{trigger:this.#r.trigger,titleId:this.#r.titleId},l.Io)}async close(){void 0!==this.rating&&this.#e.event("post_assistant_nps_dialog_submit",{score:this.rating,feedback:this.feedback??"",titleId:this.#r.titleId},l.Io),await this.controller.close(!0,this.rating)}async skipFeedback(){this.feedback="",await this.close()}async sendFeedback(){await this.close()}};s=(0,a.Cg)([(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[o.DialogController,l.j0])],s);let d=class extends r.C{constructor(){super(...arguments),this.viewModelClass="dialogs/post-assistant-nps-dialog"}};d=(0,a.Cg)([(0,n.autoinject)()],d)},"dialogs/post-assistant-nps-dialog.html":(i,e,t)=>{t.r(e),t.d(e,{default:()=>a});const a='<template> <require from="./post-assistant-nps-dialog.scss"></require> <require from="./resources/elements/nps-score-selector"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="post-assistant-nps-dialog align-center"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <template if.bind="rating === undefined"> <header>${\'post_assistant_nps_dialog.how_satisfied_are_you\' | i18n}</header> <nps-score-selector set-rating.call="setRating(rating)"> <span slot="lowScoreLabel"> ${\'post_assistant_nps_dialog.not_satisfied_at_all\' | i18n} </span> <span slot="highScoreLabel"> ${\'post_assistant_nps_dialog.extremely_satisfied\' | i18n} </span> </nps-score-selector> </template> <template else> <header>${\'post_assistant_nps_dialog.thank_you_for_feedback\' | i18n}</header> <p>${\'post_assistant_nps_dialog.how_can_we_improve\' | i18n}</p> <textarea placeholder="${\'post_assistant_nps_dialog.feedback_placeholder\' | i18n}" value.bind="feedback" rows="5"></textarea> </template> </ux-dialog-body> <ux-dialog-footer if.bind="rating !== undefined"> <div class="buttons"> <button click.delegate="sendFeedback()">${\'post_assistant_nps_dialog.send\' | i18n}</button> <button class="secondary" click.delegate="skipFeedback()"> ${\'post_assistant_nps_dialog.skip_feedback\' | i18n} </button> </div> </ux-dialog-footer> </ux-dialog> </template> '},"dialogs/post-assistant-nps-dialog.scss":(i,e,t)=>{t.r(e),t.d(e,{default:()=>p});var a=t(31601),o=t.n(a),n=t(76314),l=t.n(n),r=t(4417),s=t.n(r),d=new URL(t(83959),t.b),c=l()(o()),g=s()(d);c.push([i.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.nps-dialog nps-score-selector{margin:23px auto 8px}.nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea:disabled{opacity:.5}.nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.nps-dialog header+p{margin-top:12px}.nps-dialog .score-selector{display:inline-block}.nps-dialog .buttons{display:flex;align-items:start}.post-assistant-nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.post-assistant-nps-dialog nps-score-selector{margin:23px auto 8px}.post-assistant-nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.post-assistant-nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.post-assistant-nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.post-assistant-nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.post-assistant-nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.post-assistant-nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.post-assistant-nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.post-assistant-nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.post-assistant-nps-dialog textarea:disabled{opacity:.5}.post-assistant-nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.post-assistant-nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.post-assistant-nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.post-assistant-nps-dialog header+p{margin-top:12px}.post-assistant-nps-dialog .score-selector{display:inline-block}.post-assistant-nps-dialog .buttons{display:flex;align-items:start}`,""]);const p=c},"dialogs/precision-mods-education-dialog":(i,e,t)=>{t.r(e),t.d(e,{PrecisionModsEducationDialog:()=>c,PrecisionModsEducationDialogService:()=>g});var a=t(15215),o=t("aurelia-dialog"),n=t("aurelia-event-aggregator"),l=t("aurelia-framework"),r=t(62914),s=t(17275),d=t(54995);let c=class{#e;#s;constructor(i,e,t){this.controller=i,this.#e=e,this.#s=t}attached(){this.#e.event("precision_mods_education_dialog_open",{},r.Io)}detached(){this.#s.publish("precision-mods-education-dialog-closed",{})}handleProCtaClick(){this.#e.event("precision_mods_education_pro_cta_click",{isUserPro:!!this.subscription},r.Io),this.controller.close(!0)}};c=(0,a.Cg)([(0,d.m6)({selectors:{subscription:(0,d.$t)((i=>i.account?.subscription))}}),(0,l.autoinject)(),(0,a.Sn)("design:paramtypes",[o.DialogController,r.j0,n.EventAggregator])],c);let g=class extends s.C{constructor(){super(...arguments),this.viewModelClass="dialogs/precision-mods-education-dialog"}};g=(0,a.Cg)([(0,l.autoinject)()],g)},"dialogs/precision-mods-education-dialog.html":(i,e,t)=>{t.r(e),t.d(e,{default:()=>l});var a=t(14385),o=t.n(a),n=new URL(t(15781),t.b);const l='<template> <require from="./precision-mods-education-dialog.scss"></require> <require from="../pro-promos/pro-showcase/resources/elements/pro-showcase-feature"></require> <require from="shared/resources/elements/close-button"></require> <require from="shared/pro-promos/precision-mods-illustration.html"></require> <ux-dialog class="precision-mods-education-dialog"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <pro-showcase-feature feature="${\'pro_showcase.precision_mod_controls\' | i18n}" description="${\'pro_showcase.precision_mod_controls_description\' | i18n}" pro-user-cta-key="pro_showcase.try_now" on-pro-cta-click.call="handleProCtaClick()"> <img class="bg" slot="bg" src="'+o()(n)+'"> <precision-mods-illustration></precision-mods-illustration> </pro-showcase-feature> </ux-dialog> </template> '},"dialogs/precision-mods-education-dialog.scss":(i,e,t)=>{t.r(e),t.d(e,{default:()=>r});var a=t(31601),o=t.n(a),n=t(76314),l=t.n(n)()(o());l.push([i.id,".precision-mods-education-dialog{width:900px;max-width:100%;padding:0;position:relative}.precision-mods-education-dialog close-button{z-index:2}.precision-mods-education-dialog pro-showcase-feature .bg{border-radius:20px}.precision-mods-education-dialog .precision-mods-graphic .mods-graphic{width:400px;backdrop-filter:blur(10px);position:relative}",""]);const r=l}}]);
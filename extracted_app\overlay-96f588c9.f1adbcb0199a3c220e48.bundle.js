"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3668,8259],{"shared/cheats/resources/elements/save-cheats-icon":(e,t,i)=>{i.r(t),i.d(t,{SaveCheatsIcon:()=>s});var o=i(15215),a=i("aurelia-framework"),n=i(70236);let s=class{get supported(){return(0,n.Lt)(this.cheat.flags,1)}};(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Object)],s.prototype,"cheat",void 0),(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Boolean)],s.prototype,"canUseSaveCheats",void 0),(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Boolean)],s.prototype,"enabled",void 0),(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Boolean)],s.prototype,"dialogDisabled",void 0),(0,o.Cg)([(0,a.computedFrom)("cheat.flags"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],s.prototype,"supported",null),s=(0,o.Cg)([(0,a.autoinject)()],s)},"shared/cheats/resources/elements/save-cheats-icon.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});var o=i(14385),a=i.n(o),n=new URL(i(51091),i.b);const s='<template class="${enabled ? \'enabled\' : \'\'} ${canUseSaveCheats ? \'can-use\' : \'\'}" pro-cta="trigger: save_cheats_icon; disabled.bind: canUseSaveCheats || dialogDisabled; feature: save_cheats;"> <require from="./save-cheats-icon.scss"></require> <require from="./save-cheats-tooltip.html"></require> <require from="../../../resources/elements/tooltip"></require> <require from="../../../resources/elements/pro-badge"></require> <template if.bind="supported"> <i data-tooltip-trigger-for="save-mods-tooltip-small-pro-promo"> ><inline-svg src="'+a()(n)+'"></inline-svg></i> <tooltip if.bind="canUseSaveCheats" class="info" direction="top-left"> <div slot="content"> ${enabled ? \'save_cheats_icon.save_cheats_is_active\' : \'save_cheats_icon.save_cheats_is_inactive\' | i18n} </div> </tooltip> </template> </template> '},"shared/cheats/resources/elements/save-cheats-icon.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>h});var o=i(31601),a=i.n(o),n=i(76314),s=i.n(n),r=i(4417),l=i.n(r),d=new URL(i(83959),i.b),p=s()(a()),c=l()(d);p.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}save-cheats-icon{display:inline-flex;position:relative;width:8.6px}save-cheats-icon:not(.can-use),save-cheats-icon:not(.can-use) *{cursor:pointer}save-cheats-icon i{color:rgba(255,255,255,.15);transition:color .15s}save-cheats-icon i svg{width:8.6px;height:14px}save-cheats-icon i svg *{fill:currentColor}save-cheats-icon.enabled i{color:var(--mods-theme-color, var(--theme--highlight))}`,""]);const h=p},"shared/cheats/resources/elements/save-cheats-tooltip-graphic.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});const o='<template> <require from="./save-cheats-tooltip-graphic.scss"></require> <svg width="274" height="227" viewBox="0 0 274 227" fill="none" xmlns="http://www.w3.org/2000/svg"> <g clip-path="url(#clip0_514_85290)"> <g clip-path="url(#clip1_514_85290)"> <path d="M0 0.84375H261.768C268.523 0.84375 274 6.32027 274 13.0759V256.817C274 263.573 268.523 269.049 261.768 269.049H0V0.84375Z" fill="url(#paint0_linear_514_85290)" fill-opacity="0.15"/> <path d="M0 0.84375H261.768C268.523 0.84375 274 6.32027 274 13.0759V256.817C274 263.573 268.523 269.049 261.768 269.049H0V0.84375Z" fill="black"/> <path d="M146.614 41.097C148.124 38.6522 146.365 35.499 143.492 35.499H130.797C129.824 35.499 128.89 35.8857 128.202 36.5738L-68.7355 233.511C-71.0473 235.823 -69.41 239.776 -66.1407 239.776H-47.7745C-46.8012 239.776 -45.8678 239.389 -45.1796 238.701L136.925 56.5962C137.126 56.3952 137.303 56.1715 137.452 55.9297L146.614 41.097Z" fill="#ACFF35" fill-opacity="0.08"/> <path d="M57.4971 70.6719C59.0226 68.2277 57.2652 65.0593 54.384 65.0593H47.0795C46.1129 65.0593 45.1853 65.4407 44.4982 66.1207L-99.6563 208.781C-101.987 211.088 -100.354 215.059 -97.075 215.059H-86.18C-85.2134 215.059 -84.2858 214.678 -83.5987 213.998L51.1653 80.6304C51.3678 80.43 51.5463 80.2067 51.6971 79.965L57.4971 70.6719Z" fill="#ACFF35" fill-opacity="0.08"/> <path d="M222.499 66.6714C224.024 64.2271 222.266 61.0593 219.385 61.0593H214.759C213.792 61.0593 212.865 61.4409 212.177 62.1212L94.3407 178.782C92.0111 181.088 93.6443 185.059 96.9225 185.059H104.116C105.083 185.059 106.01 184.678 106.698 183.997L217.82 73.9837C218.023 73.7833 218.201 73.56 218.352 73.3183L222.499 66.6714Z" fill="#ACFF35" fill-opacity="0.08"/> <path d="M160.076 7.05933L128.665 36.3018H142.423L131.205 62.104L162.616 32.8615H148.858L160.076 7.05933Z" fill="black" stroke="#ACFF35" stroke-linejoin="round"/> <path d="M68.0549 43.0593L44 64.3093H54.5361L45.9451 83.0593L70 61.8093H59.4639L68.0549 43.0593Z" fill="black" stroke="#ACFF35" stroke-opacity="0.3" stroke-linejoin="round"/> <path d="M232.354 43.0593L212 60.5906H220.915L213.646 76.0593L234 58.5281H225.085L232.354 43.0593Z" fill="black" stroke="#ACFF35" stroke-opacity="0.3" stroke-linejoin="round"/> <mask id="mask0_514_85290" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="0" y="79" width="279" height="184"> <rect y="79.0593" width="278.893" height="183.804" fill="url(#paint1_linear_514_85290)"/> </mask> <g mask="url(#mask0_514_85290)"> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="13" font-weight="bold" letter-spacing="0px"> <tspan x="34.2764" y="98.9472">${\'save_cheats_tooltip_graphic.jump_height\' | i18n}</tspan> </text> <path d="M26.386 90.1218L20.2427 95.8409H22.9335L20.7394 100.887L26.8827 95.1681H24.1919L26.386 90.1218Z" fill="#00C7F2" stroke="#00C7F2" stroke-width="0.768958" stroke-linejoin="round"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="18.455" font-weight="800" letter-spacing="0px"> <tspan x="211.351" y="131.436">100</tspan> </text> <path fill-rule="evenodd" clip-rule="evenodd" d="M157.742 113.959C151.796 113.959 146.977 118.779 146.977 124.725C146.977 130.67 151.796 135.49 157.742 135.49C163.688 135.49 168.507 130.67 168.507 124.725C168.507 118.779 163.688 113.959 157.742 113.959ZM163.894 124.725C163.894 123.876 163.205 123.187 162.356 123.187H153.128C152.279 123.187 151.59 123.876 151.59 124.725C151.59 125.574 152.279 126.263 153.128 126.263H162.356C163.205 126.263 163.894 125.574 163.894 124.725Z" fill="white" fill-opacity="0.6"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="13" font-weight="bold" letter-spacing="0px"> <tspan x="34.2764" y="128.168">${\'save_cheats_tooltip_graphic.extra_ammo\' | i18n}</tspan> </text> <path d="M26.386 119.342L20.2427 125.061H22.9335L20.7394 130.108L26.8827 124.389H24.1919L26.386 119.342Z" fill="#00C7F2" stroke="#00C7F2" stroke-width="0.768958" stroke-linejoin="round"/> <g clip-path="url(#clip2_514_85290)"> <g clip-path="url(#clip3_514_85290)"> <rect x="147.23" y="146.256" width="41.5237" height="15.3792" rx="7.68958" fill="white" fill-opacity="0.2"/> <g clip-path="url(#clip4_514_85290)"> <path d="M147.23 153.945C147.23 149.698 150.673 146.256 154.92 146.256H170.299C174.546 146.256 177.989 149.698 177.989 153.945C177.989 158.192 174.546 161.635 170.299 161.635H154.92C150.673 161.635 147.23 158.192 147.23 153.945Z" fill="white" fill-opacity="0.5"/> <path d="M159.525 153.945C159.525 151.704 158.083 150.498 156.261 150.498C154.426 150.498 152.998 151.704 152.998 153.945C152.998 156.173 154.426 157.392 156.261 157.392C158.083 157.392 159.525 156.187 159.525 153.945ZM157.651 153.945C157.651 155.151 157.166 155.806 156.261 155.806C155.357 155.806 154.872 155.151 154.872 153.945C154.872 152.739 155.357 152.084 156.261 152.084C157.166 152.084 157.651 152.739 157.651 153.945Z" fill="#111111"/> <path d="M160.752 157.301H162.574V154.679H165.183V153.211H162.574V152.058H165.471V150.59H160.752V157.301Z" fill="#111111"/> <path d="M166.526 157.301H168.347V154.679H170.956V153.211H168.347V152.058H171.244V150.59H166.526V157.301Z" fill="#111111"/> </g> </g> </g> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="13" font-weight="bold" letter-spacing="0px"> <tspan x="34.2764" y="157.388">${\'save_cheats_tooltip_graphic.super_fast\' | i18n}</tspan> </text> <path d="M26.386 148.562L20.2427 154.282H22.9335L20.7394 159.328L26.8827 153.609H24.1919L26.386 148.562Z" fill="#00C7F2" stroke="#00C7F2" stroke-width="0.768958" stroke-linejoin="round"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="24" font-weight="800" letter-spacing="0px"> <tspan x="217.073" y="189.877">25</tspan> </text> <path fill-rule="evenodd" clip-rule="evenodd" d="M157.742 172.4C151.796 172.4 146.977 177.22 146.977 183.166C146.977 189.111 151.796 193.931 157.742 193.931C163.688 193.931 168.507 189.111 168.507 183.166C168.507 177.22 163.688 172.4 157.742 172.4ZM163.894 183.166C163.894 182.316 163.205 181.628 162.356 181.628H153.128C152.279 181.628 151.59 182.316 151.59 183.166C151.59 184.015 152.279 184.704 153.128 184.704H162.356C163.205 184.704 163.894 184.015 163.894 183.166Z" fill="white" fill-opacity="0.6"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="13" font-weight="bold" letter-spacing="0px"> <tspan x="34.2764" y="186.608">${\'save_cheats_tooltip_graphic.unlimited_gold\' | i18n}</tspan> </text> <path d="M26.386 177.783L20.2427 183.502H22.9335L20.7394 188.548L26.8827 182.829H24.1919L26.386 177.783Z" fill="#00C7F2" stroke="#00C7F2" stroke-width="0.768958" stroke-linejoin="round"/> <g clip-path="url(#clip5_514_85290)"> <g clip-path="url(#clip6_514_85290)"> <rect x="147.23" y="204.697" width="41.5237" height="15.3792" rx="7.68958" fill="white" fill-opacity="0.2"/> <rect x="162.989" y="204.697" width="25.7654" height="15.3792" rx="7.68958" fill="#00C7F2"/> <text fill="#111111" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="9" font-weight="900" letter-spacing="0.5px"> <tspan x="168.372" y="215.509">ON</tspan> </text> </g> </g> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="13" font-weight="bold" letter-spacing="0px"> <tspan x="34.2764" y="215.829">${\'save_cheats_tooltip_graphic.fly_mode\' | i18n}</tspan> </text> <path d="M26.386 207.003L20.2427 212.723H22.9335L20.7394 217.769L26.8827 212.05H24.1919L26.386 207.003Z" fill="#00C7F2" stroke="#00C7F2" stroke-width="0.768958" stroke-linejoin="round"/> </g> <rect x="147.652" y="92.6423" width="86.082" height="6.04084" rx="3.02042" fill="white" fill-opacity="0.2"/> <rect x="147.652" y="92.6423" width="48.2914" height="6.04084" rx="3.02042" fill="#00C7F2"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M190.908 102.081C194.453 102.081 197.327 99.2076 197.327 95.6628C197.327 92.118 194.453 89.2444 190.908 89.2444C187.363 89.2444 184.49 92.118 184.49 95.6628C184.49 99.2076 187.363 102.081 190.908 102.081Z" fill="white"/> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="11" font-weight="900" letter-spacing="1px"> <tspan x="241.571" y="98.6831">56</tspan> </text> </g> </g> <defs> <lineargradient id="paint0_linear_514_85290" x1="274" y1="0.843736" x2="42.0105" y2="99.5655" gradientUnits="userSpaceOnUse"> <stop stop-color="white"/> <stop offset="1" stop-color="#13CFFF" stop-opacity="0.4"/> </lineargradient> <lineargradient id="paint1_linear_514_85290" x1="139.446" y1="225.585" x2="139.446" y2="262.863" gradientUnits="userSpaceOnUse"> <stop stop-color="white"/> <stop offset="1" stop-color="white" stop-opacity="0"/> </lineargradient> <clippath id="clip0_514_85290"> <path d="M0 0.84375H265C269.971 0.84375 274 4.87319 274 9.84375V217.156C274 222.127 269.971 226.156 265 226.156H0V0.84375Z" fill="white"/> </clippath> <clippath id="clip1_514_85290"> <path d="M0 0.84375H261.768C268.523 0.84375 274 6.32027 274 13.0759V256.817C274 263.573 268.523 269.049 261.768 269.049H0V0.84375Z" fill="white"/> </clippath> <clippath id="clip2_514_85290"> <rect width="41.5237" height="29.2204" fill="white" transform="translate(147.23 139.335)"/> </clippath> <clippath id="clip3_514_85290"> <rect x="147.23" y="146.256" width="41.5237" height="15.3792" rx="7.68958" fill="white"/> </clippath> <clippath id="clip4_514_85290"> <rect x="147.23" y="146.256" width="54.3877" height="15.3792" rx="7.68958" fill="white"/> </clippath> <clippath id="clip5_514_85290"> <rect width="41.5237" height="29.2204" fill="white" transform="translate(147.23 197.776)"/> </clippath> <clippath id="clip6_514_85290"> <rect x="147.23" y="204.697" width="41.5237" height="15.3792" rx="7.68958" fill="white"/> </clippath> </defs> </svg> </template> '},"shared/cheats/resources/elements/save-cheats-tooltip-graphic.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>r});var o=i(31601),a=i.n(o),n=i(76314),s=i.n(n)()(a());s.push([e.id,"save-cheats-tooltip-graphic svg clipPath,save-cheats-tooltip-graphic svg mask{display:none}",""]);const r=s},"shared/cheats/resources/elements/save-cheats-tooltip.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});const o='<template class="custom-tooltip"> <require from="./save-cheats-tooltip.scss"></require> <require from="./save-cheats-tooltip-graphic.html"></require> <require from="../../../resources/elements/pro-badge"></require> <require from="../../../resources/elements/tooltip"></require> <tooltip direction.bind="direction" click-to-open="true" open.bind="open"> <div slot="content"> <div class="layout"> <div> <h1> <span innerhtml.bind="\'save_cheats_tooltip.save_cheats\' | i18n | markdown"></span> <pro-badge class="small"></pro-badge> </h1> <p innerhtml.bind="\'save_cheats_tooltip.save_cheats_info\' | i18n | markdown"></p> </div> <div> <save-cheats-tooltip-graphic></save-cheats-tooltip-graphic> </div> </div> </div> </tooltip> </template> '},"shared/cheats/resources/elements/save-cheats-tooltip.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>r});var o=i(31601),a=i.n(o),n=i(76314),s=i.n(n)()(a());s.push([e.id,"save-cheats-tooltip .layout{width:550px;display:flex;background:var(--theme--secondary-background)}save-cheats-tooltip .layout>*{flex:0 0 50%}save-cheats-tooltip .layout>*:first-child{padding:17px 24px}save-cheats-tooltip .layout>*:last-child{background:#000}save-cheats-tooltip .layout h1{font-weight:800;font-size:21px;line-height:30px;margin:0 0 9px;display:inline-flex;align-items:center;color:#fff}save-cheats-tooltip .layout h1 em{color:var(--color--accent) !important}save-cheats-tooltip .layout pro-badge{margin-left:9px}save-cheats-tooltip .layout p{font-weight:700;font-size:15px;line-height:24px;font-weight:500;color:rgba(255,255,255,.6)}save-cheats-tooltip .layout p em{font-weight:700;color:rgba(255,255,255,.8)}save-cheats-tooltip .layout save-cheats-tooltip-graphic{display:flex;width:100%;z-index:999;position:relative}",""]);const r=s},"shared/cheats/resources/elements/selection-input":(e,t,i)=>{i.r(t),i.d(t,{SelectionInput:()=>n});var o=i(15215),a=i("aurelia-framework");let n=class{#e;constructor(e){this.opened=!1,this.#e=e,this.closeIfClickOutside=this.closeIfClickOutside.bind(this)}open(){this.opened=!0,document.addEventListener("click",this.closeIfClickOutside)}close(){this.opened=!1,document.removeEventListener("click",this.closeIfClickOutside)}closeIfClickOutside(e){this.#e.contains(e.target)||this.close()}setIndex(e){this.options&&(this.internalValue=e,this.opened=!1)}detached(){this.close()}setValue(){this.#t()}internalValueChanged(){this.button||this.internalValue===this.value||this.#t()}valueChanged(e){this.internalValue=e}#t(){this.change?this.change({value:this.internalValue}):this.value=this.internalValue}};(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Number)],n.prototype,"value",void 0),(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Array)],n.prototype,"options",void 0),(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Boolean)],n.prototype,"disabled",void 0),(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Boolean)],n.prototype,"button",void 0),(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Function)],n.prototype,"change",void 0),(0,o.Cg)([a.observable,(0,o.Sn)("design:type",Number)],n.prototype,"internalValue",void 0),n=(0,o.Cg)([(0,a.inject)(Element),(0,o.Sn)("design:paramtypes",[HTMLElement])],n)},"shared/cheats/resources/elements/selection-input.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});const o='<template class="${opened ? \'opened\' : \'\'} ${disabled ? \'disabled\' : \'\'}"> <require from="../../../resources/elements/selection-input.scss"></require> <require from="./selection-input.scss"></require> <div class="wrapper"> <div click.delegate="open()" class="selection-value" haptic-touch tabindex.bind="disabled ? -1 : 0"> ${options[value] || \'---\'} </div> <ul class="selection-options"> <li repeat.for="option of options" click.delegate="setIndex($index)" class="${$index == value ? \'selected\' : \'\'}" haptic-touch tabindex.bind="disabled || !opened ? -1 : 0"> ${option} </li> </ul> </div> <button if.bind="button" class="set" click.delegate="setValue()">${button | i18n}</button> </template> '},"shared/cheats/resources/elements/selection-input.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>r});var o=i(31601),a=i.n(o),n=i(76314),s=i.n(n)()(a());s.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}selection-input{display:flex;align-items:center}selection-input button.set{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;flex:0;margin-left:10px}selection-input button.set,selection-input button.set *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) selection-input button.set{border:1px solid #fff}}selection-input button.set>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}selection-input button.set>*:first-child{padding-left:0}selection-input button.set>*:last-child{padding-right:0}selection-input button.set svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) selection-input button.set svg *{fill:CanvasText}}selection-input button.set svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) selection-input button.set svg{opacity:1}}selection-input button.set img{height:50%}selection-input button.set:disabled{opacity:.3}selection-input button.set:disabled,selection-input button.set:disabled *{cursor:default;pointer-events:none}@media(hover: hover){selection-input button.set:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}selection-input button.set:not(:disabled):hover svg{opacity:1}}selection-input button.set:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}",""]);const r=s},"shared/cheats/resources/elements/trainer-hotkey":(e,t,i)=>{i.r(t),i.d(t,{TrainerHotkey:()=>d});var o=i(15215),a=i("aurelia-event-aggregator"),n=i("aurelia-framework");const s={button:["apply"],toggle:["toggle"],number:["decrease","increase"],slider:["decrease","increase"],selection:["previous","next"],scalar:["decrease","increase"],incremental:[]},r={button:[""],toggle:[""],number:["↓","↑"],slider:["↓","↑"],selection:["←","→"],scalar:["←","→"],incremental:[]},l=(e,t)=>!!e&&!!t&&!(e<t||t<e);class d{constructor(){this.editing=!1,this.hotkeyNames=s,this.condensedHotkeyNames=r}bind(){this.#i()}defaultKeysChanged(){this.#i()}editingChanged(e){e&&(this.onHotkeyPress(),this.ea.subscribeOnce("customHotkeyPressed",(e=>{this.onHotkeyPressCb(e)})))}gamePreferencesChanged(){this.#i()}syncedKeysChanged(){this.#i()}onHotkeyPressCb(e){let t=!1;const i=this?.gamePreferences?.[this.gameId],o=i?.customHotkeys?.[this.cheatUuid];t=o?Object.values(o).some((t=>l(t,e))):(this.cheatBlueprints?.find((e=>e.uuid===this.cheatUuid))?.hotkeys??[]).some((t=>l(t,e)));let a=e;if(1===e?.length)switch(e[0]){case 8:a=void 0;break;case 46:a=null}t||(this.setCustomHotkey(),this.ea.publish("setCustomHotkey",{gameId:this.gameId,cheatUuid:this.cheatUuid,hotkeyIndex:this.hotkeyIndex,newHotkey:a})),setTimeout((()=>{this.editing=!1}),100)}handleClick(){this.disabled||(this.editing=!0)}#i(){if(this.disabled)this.syncedKeys&&(this.keys=[...this.syncedKeys]);else{const e=this?.gamePreferences?.[this.gameId];if(!e)return void(this.keys=[...this.defaultKeys]);const t=e.customHotkeys[this.cheatUuid];if(!t)return void(this.keys=[...this.defaultKeys]);const i=t[this.hotkeyIndex];if(null===i)return void(this.keys=null);this.keys=[...i||this.defaultKeys]}}}(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",Object)],d.prototype,"keys",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",Array)],d.prototype,"cheatBlueprints",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",String)],d.prototype,"cheatType",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",String)],d.prototype,"cheatUuid",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",String)],d.prototype,"gameId",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",Boolean)],d.prototype,"disabled",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",Number)],d.prototype,"hotkeyIndex",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",a.EventAggregator)],d.prototype,"ea",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",Function)],d.prototype,"onHotkeyPress",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",Function)],d.prototype,"setCustomHotkey",void 0),(0,o.Cg)([n.bindable,n.observable,(0,o.Sn)("design:type",Array)],d.prototype,"defaultKeys",void 0),(0,o.Cg)([n.bindable,n.observable,(0,o.Sn)("design:type",Object)],d.prototype,"editing",void 0),(0,o.Cg)([n.bindable,n.observable,(0,o.Sn)("design:type",Object)],d.prototype,"gamePreferences",void 0),(0,o.Cg)([n.bindable,n.observable,(0,o.Sn)("design:type",Object)],d.prototype,"syncedKeys",void 0)},"shared/cheats/resources/elements/trainer-hotkey.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});const o='<template class="${editing ? \'editing\' : \'\'}" tabindex="0"> <require from="./trainer-hotkey.scss"></require> <require from="../value-converters/proper-hotkey"></require> <require from="../value-converters/sort-keys"></require> <require from="../../../resources/elements/tooltip"></require> <button focus.bind="editing" click.delegate="handleClick()" tabindex="-1" disabled.bind="disabled"> <div class="keys"> <span if.bind="keys && keys.length > 0" class="condensed-label" title="${(\'trainer_hotkey.action_\' + hotkeyNames[cheatType][hotkeyIndex]) | i18n}"> ${condensedHotkeyNames[cheatType][hotkeyIndex]} </span> <div if.bind="keys && keys.length > 0" repeat.for="key of keys | sortKeys" class="key"> ${key | properHotkey} </div> <div else class="key">${\'trainer_hotkey.not_set\' | i18n}</div> <div if.bind="!disabled" class="overlay"><i class="edit-icon"></i></div> <div show.bind="editing" class="editing-indicator"> <span></span> <span></span> <span></span> </div> <tooltip class="info" direction="bottom-center" open.bind="editing" enable-click-outside.bind="false"> <div slot="content">${\'trainer_hotkey.hotkey_tooltip\' | i18n}</div> </tooltip> </div> </button> </template> '},"shared/cheats/resources/elements/trainer-hotkey.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>h});var o=i(31601),a=i.n(o),n=i(76314),s=i.n(n),r=i(4417),l=i.n(r),d=new URL(i(83959),i.b),p=s()(a()),c=l()(d);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,trainer-hotkey .keys .overlay .edit-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}trainer-hotkey{position:relative}trainer-hotkey button{border-radius:8px;display:flex;flex:1;flex-direction:row;height:28px;border:0;background:rgba(255,255,255,.04);padding:0;transition:background-color .15s}trainer-hotkey:hover .keys .overlay,trainer-hotkey.editing .keys .overlay{opacity:1}trainer-hotkey .condensed-label{font-weight:500;padding:0 2px;color:rgba(255,255,255,.8)}trainer-hotkey .condensed-label:empty{display:none}trainer-hotkey .keys{display:flex;flex-direction:row;position:relative;outline:none;height:100%;align-items:center;padding:0px 3px;justify-content:space-between;width:100%;font-size:14px;border:1px solid rgba(0,0,0,0);border-radius:8px}trainer-hotkey .keys .overlay{background:rgba(0,0,0,.9);border:.5px solid rgba(255,255,255,.3);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:8px;position:absolute;left:0;top:0;right:0;bottom:0;opacity:0;display:flex;align-items:center;justify-content:center;color:rgba(255,255,255,.8)}trainer-hotkey .keys .overlay .edit-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:14px}trainer-hotkey .keys .overlay .edit-icon:before{font-family:inherit;content:"edit"}trainer-hotkey .keys .key{font-size:12px;font-weight:400;flex:0;color:rgba(255,255,255,.5);white-space:nowrap;padding:0 2px;letter-spacing:.5px}trainer-hotkey.editing .keys{border-color:rgba(255,255,255,.3)}trainer-hotkey.editing .key,trainer-hotkey.editing .overlay,trainer-hotkey.editing .condensed-label{visibility:hidden}trainer-hotkey .editing-indicator{display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -2px);white-space:nowrap;width:15px}trainer-hotkey .editing-indicator span{width:3px;height:3px;background:#fff;float:left;margin-right:3px;border-radius:100%;animation:editing-indicator .5s infinite alternate}trainer-hotkey .editing-indicator span:last-child{margin-right:0}trainer-hotkey .editing-indicator span:nth-child(0){animation-delay:0s}trainer-hotkey .editing-indicator span:nth-child(1){animation-delay:.1s}trainer-hotkey .editing-indicator span:nth-child(2){animation-delay:.2s}trainer-hotkey .editing-indicator span:nth-child(3){animation-delay:.3s}trainer-hotkey .editing-indicator span:nth-child(4){animation-delay:.4s}@keyframes editing-indicator{0%{transform:translate(0, 0)}100%{transform:translate(0, 2px)}}`,""]);const h=p}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7042],{75060:(t,e,o)=>{o.d(e,{A:()=>Q});var r=o(74929),a=o(90842),n=o(98324),i=o(13230),s=o(50512),d=o(42182),c=o(7328),u=o(78243),l=o(47978),p=o(35019),m=o(42995),g=o(29336),C=o(1039),f=o(35405),x=o(20915),v=o(95156),y=o(68255),V=o(55407),b=o(32234),w=o(52999),k=o(62650),D=o(48899),P=o(54991),T=o(38499),A=o(8561);const E="rum";function Q(t,e,o={}){const Q=(0,r.qR)(0),I=(0,a.D)("global context",{customerDataTracker:Q.getOrCreateTracker(2)}),R=(0,a.D)("user",{customerDataTracker:Q.getOrCreateTracker(1),propertiesConfig:{id:{type:"string"},name:{type:"string"},email:{type:"string"}}}),S=(0,a.D)("account",{customerDataTracker:Q.getOrCreateTracker(1),propertiesConfig:{id:{type:"string",required:!0},name:{type:"string"}}}),h=(0,n.D)(),F=(0,x.eZ)();function M(){return(0,f.f)(I,R,S,e)}let N=function({ignoreInitIfSyntheticsWillInjectRum:t,startDeflateWorker:e},o,r,a,n){const i=(0,y.O)();let s,d,u,l;const p=r.observable.subscribe(f),m={};function f(){if(!u||!l||!r.isGranted())return;let t;if(p.unsubscribe(),l.trackViewsManually){if(!s)return;i.remove(s.callback),t=s.options}const e=n(l,d,t);i.drain(e)}function E(t){const o=(0,V.d0)();if(o&&(t=function(t){var e,o;return{...t,applicationId:"********-aaaa-0000-aaaa-************",clientToken:"empty",sessionSampleRate:100,defaultPrivacyLevel:null!==(e=t.defaultPrivacyLevel)&&void 0!==e?e:null===(o=(0,V.Y9)())||void 0===o?void 0:o.getPrivacyLevel()}}(t)),u=t,(0,c.Rr)((0,T.PW)(t)),l)return void(0,C.$)("DD_RUM",t);const a=(0,T.Wr)(t);a&&(o||a.sessionStoreStrategyType?a.compressIntakeRequests&&!o&&e&&(d=e(a,"Datadog RUM",w.l),!d)||(l=a,(0,k.i)().subscribe(w.l),r.tryToInit(a.trackingConsent),f()):b.Vy.warn("No storage available for session. We will not send any data."))}const Q=t=>{i.add((e=>e.addDurationVital(t)))},I={init(e,o){e?((0,D.Aq)(e.enableExperimentalFeatures),u=e,t&&(0,P.ao)()||((0,v.q)(e.plugins,"onInit",{initConfiguration:e,publicApi:o}),e.remoteConfigurationId?(0,A.QR)(e,E):E(e))):b.Vy.error("Missing configuration")},get initConfiguration(){return u},getInternalContext:w.l,stopSession:w.l,addTiming(t,e=(0,g.nx)()){i.add((o=>o.addTiming(t,e)))},startView(t,e=(0,g.M8)()){const o=o=>{o.startView(t,e)};i.add(o),s||(s={options:t,callback:o},f())},setViewName(t){i.add((e=>e.setViewName(t)))},setViewContext(t){i.add((e=>e.setViewContext(t)))},setViewContextProperty(t,e){i.add((o=>o.setViewContextProperty(t,e)))},getViewContext:()=>m,addAction(t,e=o()){i.add((o=>o.addAction(t,e)))},addError(t,e=o()){i.add((o=>o.addError(t,e)))},addFeatureFlagEvaluation(t,e){i.add((o=>o.addFeatureFlagEvaluation(t,e)))},startDurationVital:(t,e)=>(0,x.vW)(a,t,e),stopDurationVital(t,e){(0,x.nv)(Q,a,t,e)},addDurationVital:Q};return I}(o,M,h,F,((r,a,n)=>{r.storeContextsAcrossPages&&((0,i.Ck)(r,I,E,2),(0,i.Ck)(r,R,E,1),(0,i.Ck)(r,S,E,4)),Q.setCompressionStatus(a?1:2);const d=t(r,e,Q,M,n,a&&o.createDeflateEncoder?t=>o.createDeflateEncoder(r,a,t):s.N,h,F);return e.onRumStart(d.lifeCycle,r,d.session,d.viewHistory,a),N=function(t,e){return{init:t=>{(0,C.$)("DD_RUM",t)},initConfiguration:t.initConfiguration,...e}}(N,d),(0,v.q)(r.plugins,"onRumStart",{strategy:N}),d}));const O=(0,d.dm)((t=>{const e="object"==typeof t?t:{name:t};e.context&&Q.getOrCreateTracker(3).updateCustomerData(e.context),N.startView(e),(0,c.Q6)({feature:"start-view"})})),G=(0,u.m)({init:(0,d.dm)((t=>{N.init(t,G)})),setTrackingConsent:(0,d.dm)((t=>{h.update(t),(0,c.Q6)({feature:"set-tracking-consent",tracking_consent:t})})),setViewName:(0,d.dm)((t=>{N.setViewName(t),(0,c.Q6)({feature:"set-view-name"})})),setViewContext:(0,d.dm)((t=>{N.setViewContext(t),(0,c.Q6)({feature:"set-view-context"})})),setViewContextProperty:(0,d.dm)(((t,e)=>{N.setViewContextProperty(t,e),(0,c.Q6)({feature:"set-view-context-property"})})),getViewContext:(0,d.dm)((()=>((0,c.Q6)({feature:"set-view-context-property"}),N.getViewContext()))),setGlobalContext:(0,d.dm)((t=>{I.setContext(t),(0,c.Q6)({feature:"set-global-context"})})),getGlobalContext:(0,d.dm)((()=>I.getContext())),setGlobalContextProperty:(0,d.dm)(((t,e)=>{I.setContextProperty(t,e),(0,c.Q6)({feature:"set-global-context"})})),removeGlobalContextProperty:(0,d.dm)((t=>I.removeContextProperty(t))),clearGlobalContext:(0,d.dm)((()=>I.clearContext())),getInternalContext:(0,d.dm)((t=>N.getInternalContext(t))),getInitConfiguration:(0,d.dm)((()=>(0,l.Go)(N.initConfiguration))),addAction:(t,e)=>{const o=(0,p.uC)("action");(0,d.um)((()=>{N.addAction({name:(0,m.a)(t),context:(0,m.a)(e),startClocks:(0,g.M8)(),type:"custom",handlingStack:o}),(0,c.Q6)({feature:"add-action"})}))},addError:(t,e)=>{const o=(0,p.uC)("error");(0,d.um)((()=>{N.addError({error:t,handlingStack:o,context:(0,m.a)(e),startClocks:(0,g.M8)()}),(0,c.Q6)({feature:"add-error"})}))},addTiming:(0,d.dm)(((t,e)=>{N.addTiming((0,m.a)(t),e)})),setUser:(0,d.dm)((t=>{R.setContext(t),(0,c.Q6)({feature:"set-user"})})),getUser:(0,d.dm)(R.getContext),setUserProperty:(0,d.dm)(((t,e)=>{R.setContextProperty(t,e),(0,c.Q6)({feature:"set-user"})})),removeUserProperty:(0,d.dm)(R.removeContextProperty),clearUser:(0,d.dm)(R.clearContext),setAccount:(0,d.dm)(S.setContext),getAccount:(0,d.dm)(S.getContext),setAccountProperty:(0,d.dm)(S.setContextProperty),removeAccountProperty:(0,d.dm)(S.removeContextProperty),clearAccount:(0,d.dm)(S.clearContext),startView:O,stopSession:(0,d.dm)((()=>{N.stopSession(),(0,c.Q6)({feature:"stop-session"})})),addFeatureFlagEvaluation:(0,d.dm)(((t,e)=>{N.addFeatureFlagEvaluation((0,m.a)(t),(0,m.a)(e)),(0,c.Q6)({feature:"add-feature-flag-evaluation"})})),getSessionReplayLink:(0,d.dm)((()=>e.getSessionReplayLink())),startSessionReplayRecording:(0,d.dm)((t=>{e.start(t),(0,c.Q6)({feature:"start-session-replay-recording",force:t&&t.force})})),stopSessionReplayRecording:(0,d.dm)((()=>e.stop())),addDurationVital:(0,d.dm)(((t,e)=>{(0,c.Q6)({feature:"add-duration-vital"}),N.addDurationVital({name:(0,m.a)(t),type:"duration",startClocks:(0,g.jR)(e.startTime),duration:e.duration,context:(0,m.a)(e&&e.context),description:(0,m.a)(e&&e.description)})})),startDurationVital:(0,d.dm)(((t,e)=>((0,c.Q6)({feature:"start-duration-vital"}),N.startDurationVital((0,m.a)(t),{context:(0,m.a)(e&&e.context),description:(0,m.a)(e&&e.description)})))),stopDurationVital:(0,d.dm)(((t,e)=>{(0,c.Q6)({feature:"stop-duration-vital"}),N.stopDurationVital("string"==typeof t?(0,m.a)(t):t,{context:(0,m.a)(e&&e.context),description:(0,m.a)(e&&e.description)})}))});return G}},87946:(t,e,o)=>{o.d(e,{r:()=>F});var r=o(12452),a=o(7328),n=o(39377),i=o(55407),s=o(57522),d=o(10611),c=o(26250),u=o(71481),l=o(6672),p=o(17142),m=o(67945),g=o(40431),C=o(20272),f=o(15270),x=o(90888),v=o(52451),y=o(15761),V=o(66996),b=o(81464),w=o(30544),k=o(39930),D=o(98709),P=o(24261),T=o(23912),A=o(20915),E=o(51537),Q=o(2095),I=o(5964),R=o(75911),S=o(13323),h=o(59110);function F(t,e,o,F,M,N,O,G){var L;const U=[],W=new l.$,q=(0,S.F)();W.subscribe(13,(t=>(0,r.b)("rum",t)));const _=function(t){const e=(0,a.a5)("browser-rum-sdk",t);if((0,i.d0)()){const t=(0,i.Y9)();e.observable.subscribe((e=>t.send("internal_telemetry",e)))}return e}(t);_.setContextProvider((()=>{var e,o;return{application:{id:t.applicationId},session:{id:null===(e=$.findTrackedSession())||void 0===e?void 0:e.id},view:{id:null===(o=z.findView())||void 0===o?void 0:o.id},action:{id:ot.findActionId()}}}));const Y=t=>{W.notify(14,{error:t}),(0,a.A2)("Error reported to customer",{"error.message":t.message})},j=(0,n.hL)(t),H=j.subscribe((t=>{W.notify(11,t)}));U.push((()=>H.unsubscribe()));const $=(0,i.d0)()?(0,v.uv)():(0,v.Yw)(t,W,O);if((0,i.d0)())(0,V.c)(W);else{const e=(0,y.N)(t,W,_.observable,Y,j,$.expireObservable,N);U.push((()=>e.stop())),(0,D.Y)(t,_,W,o,e.flushObservable)}const J=(0,s.l)(),K=(0,w.v)(t,location),Z=(0,P.d9)(q,t),z=(0,p.Y)(W),B=(0,b.s)(W,q,K,location),X=(0,k.dQ)(W,q,t,o.getOrCreateTracker(0));U.push((()=>X.stop()));const{observable:tt,stop:et}=(0,d.O)();U.push(et);const{actionContexts:ot,addAction:rt,stop:at}=function(t,e,o,r,a,n,i,s,d,u,l){const p=(0,g.d)(t,e,n,i,o),m=(0,T.k)(o),C=(0,E.E)(o,e);return(0,h.w)(e),(0,c.b)(o,t,e,r,d,s,m,u,l),{pageStateHistory:a,addAction:p.addAction,actionContexts:p.actionContexts,stop:()=>{p.stop(),C.stop(),m.stop(),d.stop(),a.stop(),s.stop()}}}(W,q,t,$,Z,J,tt,B,z,F,Y);U.push(at),(0,a.JK)();const{addTiming:nt,startView:it,setViewName:st,setViewContext:dt,setViewContextProperty:ct,getViewContext:ut,stop:lt}=(0,x.s)(W,q,t,location,J,tt,K,e,z,M);U.push(lt);const{stop:pt}=(0,f.j)(W,t,Z);if(U.push(pt),t.trackLongTasks)if(null===(L=PerformanceObserver.supportedEntryTypes)||void 0===L?void 0:L.includes(I.do.LONG_ANIMATION_FRAME)){const{stop:e}=(0,Q.i)(W,t);U.push(e)}else(0,R.I)(W,t);const{addError:mt}=(0,C.r)(W,t);(0,m.qE)(W,t,$);const gt=(0,A.t5)(W,Z,G),Ct=(0,u.d)(t.applicationId,$,z,ot,B);return{addAction:rt,addError:mt,addTiming:nt,addFeatureFlagEvaluation:X.addFeatureFlagEvaluation,startView:it,setViewContext:dt,setViewContextProperty:ct,getViewContext:ut,setViewName:st,lifeCycle:W,viewHistory:z,session:$,stopSession:()=>$.expire(),getInternalContext:Ct.get,startDurationVital:gt.startDurationVital,stopDurationVital:gt.stopDurationVital,addDurationVital:gt.addDurationVital,stop:()=>{U.forEach((t=>t()))}}}}}]);
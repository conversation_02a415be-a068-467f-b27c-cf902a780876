"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2129,8946],{4585:(l,n,u)=>{u.d(n,{I:()=>t});const e=["toggle"];function t(l){return e.includes(l)}},56188:(l,n,u)=>{function e(l,n){const u=new Set,e=l.reduce(((l,e)=>(n?.[e.uuid]?.pinned&&!u.has(e.uuid)&&(u.add(e.uuid),l.pinned.push({...e,category:"pinned"})),"pinned"!==e.category&&l.rest.push(e),l)),{pinned:[],rest:[]});return[...e.pinned.sort(((l,u)=>(n?.[l.uuid]?.pinnedOrder??0)-(n?.[u.uuid]?.pinnedOrder??0))),...e.rest]}u.d(n,{x:()=>e})},72934:(l,n,u)=>{u.d(n,{o:()=>t});var e=u(70236);function t(l){return(0,e.Lt)(l?.flags,1)}},"shared/cheats/resources/value-converters/blueprint-translation":(l,n,u)=>{u.r(n),u.d(n,{BlueprintTranslationValueConverter:()=>o});var e=u(15215),t=u("aurelia-framework"),r=u(20057),a=u(15448);let o=class{#l;constructor(l){this.signals=[a.p],this.#l=l}toView(l,n){return l&&n&&n?.locale===this.#l.getEffectiveLocale().baseName?n.strings[l]??l:l}};o=(0,e.Cg)([(0,t.autoinject)(),(0,e.Sn)("design:paramtypes",[r.F2])],o)},"shared/cheats/resources/value-converters/group-cheats":(l,n,u)=>{u.r(n),u.d(n,{ExtractChallengeModsValueConverter:()=>s,GroupCheatsValueConverter:()=>o,RemovePrecisionModsValueConverter:()=>i});var e=u(15215),t=u("aurelia-framework"),r=u(70236),a=u(29278);let o=class{#n;constructor(l){this.#n=l}toView(l,n){const u=this.#n.toView(l,"category");return 1===u.length&&(u[0][n]="cheats"),u}};o=(0,e.Cg)([(0,t.autoinject)(),(0,e.Sn)("design:paramtypes",[a.H])],o);let s=class{toView(l){const n=l.flatMap((l=>l.values.filter((l=>(0,r.Lt)(l.flags,4)&&"pinned"!==l.category))));return n.length?[...l.map((l=>({category:l.category,values:l.values.filter((l=>!(0,r.Lt)(l.flags,4)||"pinned"===l.category))}))),{category:"challenge",values:n}]:l}};s=(0,e.Cg)([(0,t.autoinject)()],s);class i{toView(l){return[...l.map((l=>({category:l.category,values:l.values.filter((l=>!l.parent))})))]}}},"shared/cheats/resources/value-converters/important-genres":(l,n,u)=>{u.r(n),u.d(n,{ImportantGenresValueConverter:()=>t});const e=["puzzle","racing","fighting","rpg","simulation","survival","platformer","sports","fps","horror","indie","open-world","open_world","casual","strategy","action","adventure","shooter"].reverse();class t{toView(l,n=-1){return l.slice(0).sort(((l,n)=>e.indexOf(n)-e.indexOf(l))).slice(0,-1===n?l.length:n)}}},"shared/cheats/resources/value-converters/proper-hotkey":(l,n,u)=>{u.r(n),u.d(n,{ProperHotkeyValueConverter:()=>s});var e=u(15215),t=u("aurelia-framework"),r=u(20057),a=u(15448);const o=new Map([[106,"*"],[107,"+"],[109,"-"],[111,"/"]]);let s=class{#l;constructor(l){this.signals=[a.p],this.#l=l}toView(l){if("number"!=typeof l)return null;if(l>=96&&l<=105)return this.#l.getValue("trainer_hotkey.numpad_$x",{x:(l-96).toString(10)});if(o.has(l))return this.#l.getValue("trainer_hotkey.numpad_$x",{x:o.get(l)??""});const n=i[l];return"string"==typeof n?this.#l.getValue(`trainer_hotkey.${n}`):c[l]||null}};s=(0,e.Cg)([(0,t.autoinject)(),(0,e.Sn)("design:paramtypes",[r.F2])],s);const i=["not_set",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,"key_control","key_alt","key_pause",null,null,null,null,null,null,null,null,null,null,null,null,null,"key_page_up","key_page_down","key_end","key_home","key_left","key_up","key_right","key_down",null,null,null,"key_print_screen","key_insert","key_delete",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,"key_scroll_lock",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null],c=["Not Set","LClick","RClick","Control-break","MClick","XButton1","XButton2","Guide","Backspace","Tab","WheelUp","WheelDown","Clear","Enter",null,null,"Shift","Ctrl","Alt","Pause","Caps",null,null,null,null,null,null,"Esc",null,null,null,null,"Space","PgUp","PgDn","End","Home","Left","Up","Right","Down","Select","Print","Execute","PrtSc","Ins","Del","Help","0","1","2","3","4","5","6","7","8","9",null,null,null,null,null,null,null,"A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z",null,null,"Apps",null,null,"Numpad 0","Numpad 1","Numpad 2","Numpad 3","Numpad 4","Numpad 5","Numpad 6","Numpad 7","Numpad 8","Numpad 9","Numpad *","Numpad +","Separator","Numpad -","Decimal","Numpad /","F1","F2","F3","F4","F5","F6","F7","F8","F9","F10","F11","F12","F13","F14","F15","F16","F17","F18","F19","F20","F21","F22","F23","F24",null,null,null,null,null,null,null,null,"NumLk","SrcLk",null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,null,"Back","Forward","Refresh","Stop","Search","Favorites","Start","VolMute","VolDown","VolUp","Next","Previous","Stop Media","Play/Pause","Start Mail","Select Media","App 1","App 2",null,null,";","+",",","-",".","/","`","LMenu","LGrip","LUp","LDown","LLeft","LRight","LTrigger",null,null,null,null,null,null,null,null,null,null,null,null,"RMenu","RGrip","RUp","RDown","RLeft","RRight","RTrigger","[","\\","]","'","OEM8",null,null,"OEM102","LT","RT",null,null,"Guide","D-Up","D-Down","D-Left","D-Right","Start","Back","LStick","RStick","LB","RB","A","B","X","Y",null,null,null,null,"Play","Zoom",null,null,null,null]},"shared/cheats/resources/value-converters/sort-keys":(l,n,u)=>{u.r(n),u.d(n,{SortKeysValueConverter:()=>t});const e=[17,16,18];class t{toView(l){return l?l.sort(((l,n)=>{const u=e.indexOf(l);l=-1===u?l+e.length:u;const t=e.indexOf(n);return l-(-1===t?n+e.length:t)})):l}}}}]);
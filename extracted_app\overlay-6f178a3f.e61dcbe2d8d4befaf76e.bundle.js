"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5392],{42518:(t,e,n)=>{n.d(e,{Js:()=>a,KU:()=>i,LP:()=>o,U3:()=>r,fn:()=>c,so:()=>s});const o={1:{width:320,height:300},2:{width:601,height:300}},i={id:"mods",titleKey:"overlay.mods",size:1,enabled:!0,supportsOpacity:!0},s={id:"maps",titleKey:"overlay.maps",size:2,icon:"location_on",enabled:!0,supportsOpacity:!1},r={id:"gameGuide",titleKey:"overlay.game_guide",size:1,icon:"stadia_controller",enabled:!0,supportsOpacity:!1},a=[i,s,r],c={pinned:!1,resolutionGeometry:{},open:!0,opacity:100}},79522:(t,e,n)=>{n.d(e,{C_:()=>r,HB:()=>d,I3:()=>p,Kc:()=>_,WS:()=>W,Yh:()=>l,ZS:()=>c,hL:()=>I,ko:()=>s,sR:()=>E,xQ:()=>y,y0:()=>m});var o=n(64706),i=n(42518);function s(){return{[r]:a,[l]:S,[c]:u,[d]:g,[y]:T,[_]:f,[p]:O,[I]:v,[E]:N,[m]:A,[W]:w}}const r="ACTION_SET_OVERLAY_WINDOW_SETTINGS";function a(t,e,n,o){let i={...t.overlayWindowSettings};const s={...i[e]},r={...s[n]};return i={...i,[e]:{...s,[n]:{...r,...o}}},{...t,overlayWindowSettings:i}}const c="ACTION_MOVE_OVERLAY_WINDOW_TO_TOP";function u(t,e,n,o){const i={...t.overlayWindowSettings},s={...i[e]},r=Object.entries(s).sort((([t,e],[n,i])=>(e.resolutionGeometry[o]?.zIndex??0)-(i.resolutionGeometry[o]?.zIndex??0)));return r.forEach((([t,e],i)=>{const a=e.resolutionGeometry[o];if(a){const c=n===t?r.length:i;s[t]={...e,resolutionGeometry:{...e.resolutionGeometry,[o]:{...a,zIndex:c}}}}})),{...t,overlayWindowSettings:{...i,[e]:s}}}const l="ACTION_RESET_OVERLAY_GAME_WINDOW_SETTINGS";function S(t,e){let n={...t};for(const t of i.Js)n=a(n,e,t.id,i.fn);return n}const d="ACTION_SET_OVERLAY_SETTINGS";function g(t,e){return{...t,overlaySettings:{...t.overlaySettings,...e}}}const y="ACTION_SET_ACCESS_TOKEN";function T(t,e){return{...t,token:e}}const _="ACTION_SET_SETTINGS";function f(t,e){return{...t,settings:{...t.settings,...e}}}const p="ACTION_SET_ACCOUNT";function O(t,e){return{...t,account:e}}const I="ACTION_SET_MAP_SETTINGS";function v(t,e,n){return{...t,mapSettings:{...t.mapSettings,[e]:n}}}const E="ACTION_SET_TITLE_MAP_SETTINGS";function N(t,e,n){return{...t,titleMapSettings:{...t.titleMapSettings,[e]:n}}}const m="ACTION_CLEAR_MAP_SETTINGS";function A(t){return{...t,mapSettings:{},titleMapSettings:{}}}const W="ACTION_PERSIST_ASSISTANT_HISTORY";function w(t,e,n){const i=[...t.assistantHistory[e]||[],...n];return i.length>o.$H&&i.splice(0,i.length-o.$H),{...t,assistantHistory:{...t.assistantHistory,[e]:[...i]}}}},"overlay/store/index":(t,e,n)=>{n.r(e),n.d(e,{configure:()=>y}),n("aurelia-framework");var o=n(79522);function i(t){return{...t,overlayWindowSettings:{},stateVersion:1}}function s(t){return{...t,overlaySettings:{modsWindowMode:"inputs"},stateVersion:2}}function r(t){return{...t,settings:{},stateVersion:3}}function a(t){return{...t,overlaySettings:{...t.overlaySettings,enableNotifications:!0,notificationsPositionVertical:"center"},stateVersion:4}}function c(t){return{...t,mapSettings:{},titleMapSettings:{},stateVersion:5}}function u(t){const e=Object.keys(t.overlayWindowSettings).reduce(((e,n)=>{const o=t.overlayWindowSettings[n],i=Object.values(o).some((t=>Object.keys(t.resolutionGeometry).length>0));return e[n]={...o,gameGuide:{...o.gameGuide,open:!i}},e}),{});return{...t,assistantHistory:{},overlayWindowSettings:e,stateVersion:6}}function l(t){return{...t,assistantHistory:{},overlayWindowSettings:{},stateVersion:7}}function S(t){const e={...t.overlaySettings};return e.modsWindowMode||(e.modsWindowMode="inputs"),{...t,overlaySettings:e,stateVersion:8}}function d(t){const e={...t.overlaySettings};return void 0===e.enableNotifications&&(e.enableNotifications=!0),e.notificationsPositionVertical||(e.notificationsPositionVertical="center"),{...t,overlaySettings:e,stateVersion:9}}const g={token:null,overlayWindowSettings:{},overlaySettings:{modsWindowMode:"inputs",enableNotifications:!0,notificationsPositionVertical:"center"},settings:{},account:null,mapSettings:{},titleMapSettings:{},assistantHistory:{},stateVersion:null};function y(t,e){t.feature("shared/store/index",{debug:e.debug,storageKey:e.storageKey,initialState:g,migrations:[i,s,r,a,c,u,l,S,d],actions:(0,o.ko)(),getFallbackState:()=>null})}}}]);
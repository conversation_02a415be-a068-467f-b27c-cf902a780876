"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7104],{43542:(e,t,n)=>{n.d(t,{x:()=>d});var r=n(15215),i=n(46257),o=n(29864),a=n(70442),l=n(69544),s=n(31145),u=n(39113),c=function(e){var t=e.split(".");return t.length<=2?e:t.slice(t.length-2,t.length).join(".")},d=function(){for(var e,t,n=this,d=[],p=0;p<arguments.length;p++)d[p]=arguments[p];var v={},g=(0,r.zs)(d,2),f=g[0],b=g[1];f&&"init"in f?(t=f,b&&(v=b)):f&&(v=f);var y=null!==(e=v.excludeReferrers)&&void 0!==e?e:[];"undefined"!=typeof location&&y.unshift(location.hostname),v=(0,r.Cl)((0,r.Cl)({disabled:!1,initialEmptyValue:"EMPTY",resetSessionOnNewCampaign:!1},v),{excludeReferrers:y});var w={name:"web-attribution",type:o.Q.BEFORE,setup:function(e,n){var o;return(0,r.sH)(this,void 0,void 0,(function(){var d,p,g,b,y,w,h,m;return(0,r.YH)(this,(function(k){switch(k.label){case 0:return(t=null!=t?t:n)?v.disabled?(e.loggerProvider.log("@amplitude/plugin-web-attribution-browser is disabled. Attribution is not tracked."),[2]):(e.loggerProvider.log("Installing @amplitude/plugin-web-attribution-browser."),n||(null===(o=e.attribution)||void 0===o?void 0:o.disabled)||(e.loggerProvider.warn("@amplitude/plugin-web-attribution-browser overrides web attribution behavior defined in @amplitude/analytics-browser. Resolve by disabling web attribution tracking in @amplitude/analytics-browser."),e.attribution={disabled:!0}),p=e.cookieStorage,_=e.apiKey,void 0===(O="MKTG")&&(O=""),void 0===H&&(H=10),g=[l.r,O,_.substring(0,H)].filter(Boolean).join("_"),[4,Promise.all([(new i.E).parse(),p.get(g)])]):(d=f?"Options":"undefined",e.loggerProvider.error("Argument of type '".concat(d,"' is not assignable to parameter of type 'BrowserClient'.")),[2]);case 1:return b=r.zs.apply(void 0,[k.sent(),2]),y=b[0],w=b[1],(null!=(h=this.__pluginEnabledOverride)?h:function(e,t,n){e.referrer;var i,o=e.referring_domain,a=(0,r.Tt)(e,["referrer","referring_domain"]),l=t||{},s=(l.referrer,l.referring_domain),u=(0,r.Tt)(l,["referrer","referring_domain"]);if(e.referring_domain&&(null===(i=n.excludeReferrers)||void 0===i?void 0:i.includes(e.referring_domain)))return!1;var d=JSON.stringify(a)!==JSON.stringify(u),p=c(o||"")!==c(s||"");return!t||d||p}(y,w,v))&&(v.resetSessionOnNewCampaign&&(t.setSessionId(Date.now()),e.loggerProvider.log("Created a new session for new campaign.")),e.loggerProvider.log("Tracking attribution."),m=function(e,t){var n=(0,r.Cl)((0,r.Cl)({},a.F3),e),i=Object.entries(n).reduce((function(e,n){var i,o=(0,r.zs)(n,2),a=o[0],l=o[1];return e.setOnce("initial_".concat(a),null!==(i=null!=l?l:t.initialEmptyValue)&&void 0!==i?i:"EMPTY"),l?e.set(a,l):e.unset(a)}),new s.T);return(0,u.rA)(i)}(y,v),t.track(m),p.set(g,y)),[2]}var _,O,H}))}))},execute:function(e){return(0,r.sH)(n,void 0,void 0,(function(){return(0,r.YH)(this,(function(t){return[2,e]}))}))},__pluginEnabledOverride:void 0};return w}},78440:(e,t,n)=>{n.d(t,{D:()=>c});var r=n(15215),i=n(73579),o=n(46257),a=n(29864),l=n(34039),s=n(70442),u=function(e){var t={};for(var n in e){var r=e[n];r&&(t[n]=r)}return t},c=function(){for(var e,t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var o,l={},s=(0,i.m)(),u=void 0,c=(0,r.zs)(t,2),g=c[0],f=c[1];g&&"init"in g?(e=g,f&&(l=f)):g&&(l=g);var b=function(){return(0,r.sH)(void 0,void 0,void 0,(function(){var e,t,n;return(0,r.YH)(this,(function(i){switch(i.label){case 0:return t={event_type:null!==(n=l.eventType)&&void 0!==n?n:"Page View"},e=[{}],[4,d()];case 1:return[2,(t.event_properties=r.Cl.apply(void 0,[r.Cl.apply(void 0,e.concat([i.sent()])),{page_domain:"undefined"!=typeof location&&location.hostname||"",page_location:"undefined"!=typeof location&&location.href||"",page_path:"undefined"!=typeof location&&location.pathname||"",page_title:"undefined"!=typeof document&&document.title||"",page_url:"undefined"!=typeof location&&location.href.split("?")[0]||""}]),t)]}}))}))},y=function(){return void 0===l.trackOn||"function"==typeof l.trackOn&&l.trackOn()},w="undefined"!=typeof location?location.href:null,h=function(){return(0,r.sH)(void 0,void 0,void 0,(function(){var t,n,i,o;return(0,r.YH)(this,(function(r){switch(r.label){case 0:return t=location.href,n=v(l.trackHistoryChanges,t,w||"")&&y(),w=t,n?(null==u||u.log("Tracking page view event"),null!=e?[3,1]:[3,3]):[3,4];case 1:return o=(i=e).track,[4,b()];case 2:o.apply(i,[r.sent()]),r.label=3;case 3:r.label=4;case 4:return[2]}}))}))},m=function(){h()},k={name:"page-view-tracking",type:a.Q.ENRICHMENT,setup:function(t,n){return(0,r.sH)(void 0,void 0,void 0,(function(){var i,a,c,d,p;return(0,r.YH)(this,(function(v){switch(v.label){case 0:return(e=null!=e?e:n)?((u=t.loggerProvider).log("Installing @amplitude/plugin-page-view-tracking-browser"),l.trackOn=(null===(d=t.attribution)||void 0===d?void 0:d.trackPageViews)?"attribution":l.trackOn,!n&&(null===(p=t.attribution)||void 0===p?void 0:p.trackPageViews)&&(u.warn("@amplitude/plugin-page-view-tracking-browser overrides page view tracking behavior defined in @amplitude/analytics-browser. Resolve by disabling page view tracking in @amplitude/analytics-browser."),t.attribution.trackPageViews=!1),l.trackHistoryChanges&&s&&(s.addEventListener("popstate",m),o=s.history.pushState,s.history.pushState=new Proxy(s.history.pushState,{apply:function(e,t,n){var i=(0,r.zs)(n,3),o=i[0],a=i[1],l=i[2];e.apply(t,[o,a,l]),h()}})),y()?(u.log("Tracking page view event"),c=(a=e).track,[4,b()]):[3,2]):(i=g?"Options":"undefined",t.loggerProvider.error("Argument of type '".concat(i,"' is not assignable to parameter of type 'BrowserClient'.")),[2]);case 1:c.apply(a,[v.sent()]),v.label=2;case 2:return[2]}}))}))},execute:function(e){return(0,r.sH)(void 0,void 0,void 0,(function(){var t;return(0,r.YH)(this,(function(n){switch(n.label){case 0:return"attribution"===l.trackOn&&p(e)?(null==u||u.log("Enriching campaign event to page view event with campaign parameters"),[4,b()]):[3,2];case 1:t=n.sent(),e.event_type=t.event_type,e.event_properties=(0,r.Cl)((0,r.Cl)({},e.event_properties),t.event_properties),n.label=2;case 2:return[2,e]}}))}))},teardown:function(){return(0,r.sH)(void 0,void 0,void 0,(function(){return(0,r.YH)(this,(function(e){return s&&(s.removeEventListener("popstate",m),o&&(s.history.pushState=o)),[2]}))}))}};return k.__trackHistoryPageView=h,k},d=function(){return(0,r.sH)(void 0,void 0,void 0,(function(){var e;return(0,r.YH)(this,(function(t){switch(t.label){case 0:return e=u,[4,(new o.E).parse()];case 1:return[2,e.apply(void 0,[t.sent()])]}}))}))},p=function(e){if("$identify"===e.event_type&&e.user_properties){var t=e.user_properties,n=t[l.eX.SET]||{},i=t[l.eX.UNSET]||{},o=(0,r.fX)((0,r.fX)([],(0,r.zs)(Object.keys(n)),!1),(0,r.zs)(Object.keys(i)),!1);return Object.keys(s.F3).every((function(e){return o.includes(e)}))}return!1},v=function(e,t,n){return"pathOnly"===e?t.split("?")[0]!==n.split("?")[0]:t!==n}}}]);
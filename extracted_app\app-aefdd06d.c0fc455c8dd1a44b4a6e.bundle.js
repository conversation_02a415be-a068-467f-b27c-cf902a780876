"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[9871],{10191:(e,t,s)=>{s.d(t,{s:()=>D});var i=s(15215),a=s(16928),n=s("aurelia-event-aggregator"),r=s("aurelia-framework"),o=s(20770),l=s(89045),h=s(84157),d=s(56188),c=s(68663),y=s(20489),g=s(83802),p=s("dialogs/overlay-nps-dialog"),v=s(16953),u=s(19072),m=s(3972),I=s(6745),f=s(24008),k=s(54995),H=s(49442),C=s(70236),S=s(29944),O=s(48881),T=s(38777),b=s(62914),M=s(40127),_=s(72208),w=s(43544),P=s(7892);let D=class{#e;#t;#s;#i;#a;#n;#r;#o;#l;#h;#d;#c;#y;#g;#p;#v;#u;#m;constructor(e,t,s,i,a,n,r,o,l,h,d,c){this.hasUsedInteractiveControlsThisSession=!1,this.#g=!1,this.#p=!1,this.#m=!1,this.#e=e,this.host=t,this.#t=s,this.#s=i,this.#i=a,this.#a=n,this.#n=r,this.#r=o,this.#o=l,this.#l=h,this.#h=d,this.#d=c}attached(){this.#c=new T.Vd([this.#e.onTrainerActivated((e=>this.#I(e))),this.#e.onTrainerEnded((()=>this.#f())),this.#i.onCheatStatesChanged((e=>this.#k(e.states))),this.host.onOverlayHotkeyPressed((()=>this.#H())),this.host.onOverlayGraphicsInitialized((e=>this.#C(e))),this.host.onOverlayGraphicsHooked((e=>this.#S(e))),this.#l.onHotkeyChanged((e=>this.host.updateOverlayHotkey(e))),this.#d.subscribe(M.$7,this.#O.bind(this))])}detached(){this.#c.dispose(),this.#u?.dispose(),this.#T("app")}#I(e){const t=e.getMetadata(g.vO);this.#u?.dispose(),e.process&&(this.#u=e.onValueSet(this.#b.bind(this)),this.#M(t.info.gameId,e.process))}#f(){this.#u?.dispose(),this.#T("trainer")}gameSupportsOverlay(e){const t=this.catalog.games[e];return(0,C.Lt)(t?.flags,f.rT.OverlaySupported)}async#M(e,t){if(!this.settings.enableOverlay)return;if(this.#p)return;if(this.#g)return;if(!this.canUseOverlay)return;const s=(0,C.br)(this.account?.flags,[64,16384]);if((this.gameSupportsOverlay(e)||s)&&!this.gamePreferences[e]?.overlayDisabled&&t)try{this.#p=!0,this.#m=!1;const s=this.#l.hotkey,i=await this.host.createOverlayWindow(t.id,s);if(this.#_(i),this.#g=!0,this.#v=this.host.onOverlayWindowClosed((()=>this.#T("window"))),t){const s=this.catalog.games[e],i=this.catalog.titles[s.titleId];this.#s.event("overlay_inject_intent",{gameId:e,titleId:i?.id},b.Jb),await this.#w(t)}}finally{this.#p=!1}}async#w(e){const t=a.normalize(`${this.host.info.paths.assets}/overlay/overlay_game${e.x64?"_x64":""}.dll`);await this.#t.grantFilePermissions(t,[m.Ro.everyone,m.Ro.appContainer],m.TB.Read|m.TB.Execute).catch(H.Y);const s=this.#P();try{await this.#t.injectDll(e.id,t),this.#s.event("overlay_inject_success",{gameId:s?.game?.id,titleId:s?.title?.id},b.Jb)}catch{this.#s.event("overlay_inject_error",{gameId:s?.game?.id,titleId:s?.title?.id},b.Jb)}}#T(e){this.#g&&(this.setGlobalBlockInput(!1),this.host.destroyOverlayWindow(),this.#D(),this.#s.event("overlay_end",{trigger:e},b.Jb)),this.#g=!1,this.#y?.dispose(),this.#y=null,this.#v?.dispose(),this.hasUsedInteractiveControlsThisSession=!1}#_(e){const t=new S.Td(new S.kk(e));t.setHandler(I.sx,(()=>({version:this.host.info.version,releaseChannel:this.host.info.releaseChannel,locale:this.host.info.locale,region:this.host.info.region,paths:this.host.info.paths,osPlatform:this.host.info.osPlatform,osArch:this.host.info.osArch,osVersion:this.host.info.osVersion,osHostname:this.host.info.osHostname,deviceCpuModel:this.host.info.deviceCpuModel,deviceCpuCount:this.host.info.deviceCpuCount,env:v.A}))),t.setHandler(I.KD,(async()=>this.#z())),t.setHandler(I.a3,(async()=>this.#U())),t.setHandler(I.Ei,(()=>this.#P())),t.setHandler(I.Kc,(async e=>this.#s.event(e.name,e.params,e.dispatch))),t.setHandler(I.lx,(async e=>this.#e.trainer?.setValue(e.name,e.value,6,e.cheatId))),t.setHandler(I.jZ,(async e=>this.#a.markRead("overlay_native",e.gameId,e.cheatId,e.instructions??""))),t.setHandler(I._Z,(async e=>this.#x(e))),t.setHandler(I.Pn,(async e=>this.#A(e))),t.setHandler(I.Mc,(async e=>this.#j(e.config,e.firstCall))),t.setHandler(I.tk,(async()=>this.#N())),t.setHandler(I.Pp,(async e=>this.#L(e))),t.setHandler(I.wk,this.#$.bind(this)),t.setHandler(I.dQ,(async()=>await(this.#e.trainer?.getPlayerCoordinates()))),t.setHandler(I.J4,(async e=>await(this.#e.trainer?.setPlayerCoordinates(e)))),t.setHandler(I.qL,(async()=>await this.#h.saveHighlight("overlay_button"))),this.#y=t,e.start()}#z(){this.#E(I.TJ,this.settings)}settingsChanged(e,t){if(this.#z(),e.enableOverlay!==t.enableOverlay)if(!this.#g||this.settings.enableOverlay){if(!this.#g){const e=this.#e.trainer,t=e?.getMetadata(g.vO)?.info.gameId;e?.process&&t&&this.#M(t,e.process)}}else this.#T("setting")}gamePreferencesChanged(e,t){const s=this.#P();if(!s)return;const i=!!e[s.game?.id]?.overlayDisabled;if(i!==!!t[s.game?.id]?.overlayDisabled&&(this.#g&&i&&this.#T("setting"),!this.#g&&!i)){const e=this.#e.trainer;e?.process&&this.#M(s.game.id,e.process)}}#U(){this.#E(I.TS,this.account)}accountChanged(){this.#U()}modTimerMessagesDismissedChanged(e){this.#L(e)}#L(e){this.#E(I.Pp,e)}#P(){if(this.#e.trainer?.state===y.FX.Active){const e=this.#e.trainer,t=e.getMetadata(g.vO),s=t.info.blueprint,i=this.catalog.titles[t.info.titleId],a=this.catalog.games[t.info.gameId],n=this.catalog.maps.filter((e=>e.titleId===i?.id)),r=(0,d.x)(s.cheats,this.#i.cheatStates);return{trainer:{...t.info,blueprint:{...s,cheats:r}},blueprint:s,translations:this.gameTranslations[a.id]??{},values:Object.fromEntries(e.values.entries()),title:i,game:a,maps:n,cheatStates:this.#i.cheatStates}}return null}#H(){this.#g&&(this.#R(),this.#E(I.zY))}#A(e){const t=null!==this.account?.subscription;if(!e||!t&&e?.pin)return;const s=e.pin?"pin_mod_click":"unpin_mod_click",i=this.pinnedMods?.[e.gameId]||[],a=e.pin?[...i,e.mod]:i.filter((t=>t.uuid!==e.mod.uuid));this.#n.dispatch(O.oz,e.gameId,a),this.#s.event(s,{source:"overlay",gameId:e.gameId,trainerId:e.trainerId,modId:e.mod.uuid},b.Io)}#j(e,t){const s=null!==this.account?.subscription;if(!e||!s)return;const i=e.cancel?"mod_timer_cancel_click":"mod_timer_start_click",a=this.#e.trainer?.getMetadata(g.vO);this.#n.dispatch(O.$Z,e),t&&this.#s.event(i,{duration:"loop"===e.type?String(e.start):String(e.duration),durationLoop:String(e.end),titleId:a?.info.titleId,modId:e.modId,modType:e.modType,type:e.type,source:"overlay"},b.Io)}#N(){return this.modTimerMessagesDismissed}#x(e){this.#n.dispatch(O.Yt,e.timerType)}async#E(e,t){try{return await(this.#y?.execute(e,t))}catch(e){if(e instanceof S.v)return!1;throw e}}#k(e){this.#E(I.Q6,e)}get canUseOverlay(){return!!this.host.info.overlayAvailable}#R(){this.#m=!this.#m,this.#m&&this.#V(),this.#E(I.aY)}async#$(){return await this.#r.requestOverlayAuthCode().catch(H.Y)||null}#D(){const e=!!this.lastOverlayNPSDialog&&(0,l.A)(Date.now(),new Date(this.lastOverlayNPSDialog))>30;!this.hasUsedInteractiveControlsThisSession||this.lastOverlayNPSDialog&&!e||(this.#n.dispatch(O.vk,"lastOverlayNPSDialog"),this.#o.open())}#b(e){6===e.source&&(this.hasUsedInteractiveControlsThisSession=!0),this.#E(I.lx,{name:e.name,value:e.value,cheatId:e.cheatId,source:e.source})}async setGlobalBlockInput(e){await h.ipcRenderer.invoke("ACTION_OVERLAY_BLOCK_INPUT",e)}#V(){const e=this.#e.trainer,t=e?.getMetadata(g.vO);t&&this.#n.dispatch(O.LQ,t.info.gameId)}#C(e){const t=this.#e.trainer?.getMetadata(g.vO);this.#s.event("overlay_graphics_initialized",{width:e.width,height:e.height,windowed:e.windowed,gameId:t?.info.gameId,titleId:t?.info.titleId},b.Jb)}#S(e){const t=this.#e.trainer?.getMetadata(g.vO);this.#s.event("overlay_graphics_hooked",{adapterName:e.adapter_desc,api:e.api,gameId:t?.info.gameId,titleId:t?.info.titleId},b.Jb)}addNotification(e){this.#E(I.pd,e)}#O(){this.addNotification({type:"simple",data:{size:"m",icon:"videocam",highlightColor:"#ee343f",iconColor:"#fff",labelKey:"overlay_highlight_save_success_notification.highlight_saved"}})}};(0,i.Cg)([(0,r.computedFrom)("host.info.overlayAvailable"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],D.prototype,"canUseOverlay",null),D=(0,i.Cg)([(0,k.m6)({setup:"attached",teardown:"detached",selectors:{settings:(0,k.$t)((e=>e.settings)),catalog:(0,k.$t)((e=>e.catalog)),gameTranslations:(0,k.$t)((e=>e.gameTranslations)),account:(0,k.$t)((e=>e.account)),gamePreferences:(0,k.$t)((e=>e.gamePreferences)),lastOverlayNPSDialog:(0,k.$t)((e=>e.timestamps?.lastOverlayNPSDialog)),pinnedMods:(0,k.$t)((e=>e.pinnedMods)),modTimerMessagesDismissed:(0,k.$t)((e=>e.modTimerMessagesDismissed))}}),(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[g.jR,u.s,m.Mz,b.j0,P.p,_.u,o.il,c.x,p.OverlayNpsDialogService,w.u,M.Re,n.EventAggregator])],D)},13472:(e,t,s)=>{s.d(t,{K:()=>l});var i=s(15215),a=s("aurelia-framework"),n=s(92694),r=s(38777),o=s(86824);let l=class{#G;#J;constructor(e){this.#G=e}attached(){this.#B()}detached(){this.#J.dispose()}#B(){this.#J=(0,r.Ix)((()=>{this.#Y(),this.#B()}),(0,o.H)(30,60))}#Y(){this.#G.releaseCollected()}};l=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[n.k])],l)},43544:(e,t,s)=>{s.d(t,{u:()=>d});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),r=s(4826),o=s("shared/cheats/resources/value-converters/proper-hotkey"),l=s(54995),h=s(62914);let d=class{#F;#K;#s;constructor(e,t){this.#K=new a.EventAggregator,this.#F=e,this.#s=t}attached(){this.#W()}detached(){}get hotkey(){return{...this.overlayHotkey??r.R,name:"wemod",on_keyup:!1}}get displayHotkey(){return this.getProperHotkey(this.overlayHotkey??r.R)}getProperHotkey(e){const t=[e.key];return e.alt&&t.unshift(18),e.ctrl&&t.unshift(17),e.shift&&t.unshift(16),t.map((e=>this.#F.toView(e))).join("+")}onHotkeyChanged(e){return this.#K.subscribe("hotkey-changed",e)}overlayHotkeyChanged(e,t){e!==t&&(this.#K.publish("hotkey-changed",this.hotkey),this.#s.event("overlay_hotkey_change",{hotkey:this.displayHotkey}),this.#W())}#W(){this.#s.user("overlay_hotkey",this.displayHotkey,h.Io)}};(0,i.Cg)([n.observable,(0,i.Sn)("design:type",Object)],d.prototype,"overlayHotkey",void 0),(0,i.Cg)([(0,n.computedFrom)("overlayHotkey"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],d.prototype,"hotkey",null),(0,i.Cg)([(0,n.computedFrom)("overlayHotkey"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],d.prototype,"displayHotkey",null),d=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{overlayHotkey:(0,l.$t)((e=>e.settings.overlayHotkey))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[o.ProperHotkeyValueConverter,h.j0])],d)}}]);
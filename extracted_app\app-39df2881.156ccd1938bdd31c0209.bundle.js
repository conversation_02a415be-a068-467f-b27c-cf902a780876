"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4160],{"cheats/mod-suggestions/mod-suggestion-list":(e,s,o)=>{o.r(s),o.d(s,{ModSuggestionList:()=>f});var t,i=o(15215),n=o(68663),a=o(62914),g=o(67064),d=o("aurelia-framework"),r=o(20770),l=o(48100),p=o(88120),u=o(41572),c=o(51808),m=o(20057),b=o(9374),h=o(70236),v=o(48881);let f=t=class{#e;#s;#o;#t;#i;#n;static{this.MAX_POPULAR_SUGGESTIONS=5}constructor(e,s,o,t,i){this.collapsed=!0,this.boosting=!1,this.modSuggestionsInfoRead=!1,this.#s=e,this.#o=s,this.#t=o,this.#i=t,this.#n=i}bind(){this.#e=this.#o.state.pipe((0,l.T)((e=>e.flags.hasSeenModSuggestionsTooltip||!1)),(0,p.F)()).subscribe((e=>{this.modSuggestionsInfoRead=e}))}unbind(){this.#e&&this.#e.unsubscribe()}get modSuggestionsWithFlags(){return this.suggestedMods.suggested.sort(((e,s)=>(e.rank??Number.POSITIVE_INFINITY)-(s.rank??Number.POSITIVE_INFINITY))).map((e=>({isNew:(0,h.Lt)(e.flags,c.V.isNew),needsReview:!(0,h.Lt)(e.flags,c.V.approved),...e})))}get userModSuggestions(){return this.modSuggestionsWithFlags.filter((e=>e.timesBoosted>0)).reduce(((e,s)=>(s.needsReview?e.needsReview.push(s):e.approved.push(s),e)),{approved:[],needsReview:[]})}get topModSuggestions(){return this.modSuggestionsWithFlags.filter((e=>!e.needsReview)).slice(0,t.MAX_POPULAR_SUGGESTIONS)}get isPro(){return(0,u.a)(this.account)}handleCollapseClick(){this.collapsed=!this.collapsed}setModSuggestionsInfoRead(){this.modSuggestionsInfoRead||(0,v.JD)(this.#o,v.NX,"hasSeenModSuggestionsTooltip",!0)}async boost(e,s){if(!(this.account.boosts<=0)){this.boosting=!0;try{const{remainingBoosts:o,suggested:t}=await this.#s.boostModSuggestion(e,s);this.#i.event("suggest_mod_boost_suggestion_click",{gameId:e,suggestionId:String(s),suggestion:t.suggestion}),(0,v.JD)(this.#o,v.OP,o),this.suggestedMods={...this.suggestedMods,suggested:this.suggestedMods.suggested.map((e=>e.id===s?t:e))}}catch(e){this.#t.toast({content:"suggest_mod.boost_error_message",type:"alert"})}this.boosting=!1}}get userBoostButtonDisabledHints(){return this.userModSuggestions.approved.map((e=>this.getBoostButtonDisabledHint(e)))}get topBoostButtonDisabledHints(){return this.topModSuggestions.map((e=>this.getBoostButtonDisabledHint(e)))}getBoostButtonDisabledHint(e){return e.needsReview?"suggest_mod.boosting_disabled_for_suggestions_under_review":0===this.account.boosts?this.isPro?"boost_button.no_boosts":"boost_button.no_boosts_free":""}get headerSubtitle(){const[e,s,...o]=this.suggestedMods.suggested;return`${[e?.suggestion,s?.suggestion].filter((e=>void 0!==e)).join(", ")}${o.length>0?`, ${this.#n.getValue("suggest_mod.and_$x_more",{x:Math.min(o.length,t.MAX_POPULAR_SUGGESTIONS-2)})}`:""}`}};(0,i.Cg)([d.bindable,(0,i.Sn)("design:type",Object)],f.prototype,"suggestedMods",void 0),(0,i.Cg)([d.bindable,(0,i.Sn)("design:type",Object)],f.prototype,"account",void 0),(0,i.Cg)([d.bindable,(0,i.Sn)("design:type",String)],f.prototype,"gameId",void 0),(0,i.Cg)([d.bindable,(0,i.Sn)("design:type",Boolean)],f.prototype,"isGameRecentlyUpdated",void 0),(0,i.Cg)([d.bindable,(0,i.Sn)("design:type",Function)],f.prototype,"suggestMod",void 0),(0,i.Cg)([(0,b._)((e=>[e.suggestedMods])),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],f.prototype,"modSuggestionsWithFlags",null),(0,i.Cg)([(0,b._)((e=>[e.modSuggestionsWithFlags])),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],f.prototype,"userModSuggestions",null),(0,i.Cg)([(0,b._)((e=>[e.modSuggestionsWithFlags])),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],f.prototype,"topModSuggestions",null),(0,i.Cg)([(0,b._)((e=>[e.account])),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],f.prototype,"isPro",null),(0,i.Cg)([(0,b._)((e=>[e.userModSuggestions,e.isPro,e.account.boosts])),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],f.prototype,"userBoostButtonDisabledHints",null),(0,i.Cg)([(0,b._)((e=>[e.topModSuggestions,e.isPro,e.account.boosts])),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],f.prototype,"topBoostButtonDisabledHints",null),(0,i.Cg)([(0,b._)((e=>[e.suggestedMods.suggested])),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],f.prototype,"headerSubtitle",null),f=t=(0,i.Cg)([(0,d.autoinject)(),(0,i.Sn)("design:paramtypes",[n.x,r.il,g.l,a.j0,m.F2])],f)},"cheats/mod-suggestions/mod-suggestion-list.html":(e,s,o)=>{o.r(s),o.d(s,{default:()=>g});var t=o(14385),i=o.n(t),n=new URL(o(89247),o.b),a=i()(n);const g='<template> <require from="./mod-suggestion-list.scss"></require> <require from="shared/resources/elements/tooltip"></require> <require from="shared/cheats/resources/elements/pin-mod-icon"></require> <div class="category ${collapsed ? \'collapsed\' : \'\'}"> <div class="category-header primary" click.delegate="handleCollapseClick()"> <i class="icon suggested-mods-icon"></i> <div class="category-header-content-wrapper"> <div class="category-header-title"> <span class="label">${\'suggest_mod.mod_suggestions\' | i18n}</span> <i class="icon tooltip-icon ${modSuggestionsInfoRead ? \'read\' : \'\'}" data-tooltip-trigger-for="mod-suggestions-info"> </i> <wm-tooltip tooltip-id="mod-suggestions-info" placement="bottom" on-show.call="setModSuggestionsInfoRead()" style-variant="primary"> <div slot="content" id="mod-suggestions-tooltip-content"> ${\'suggest_mod.creator_review_note\' | i18n} </div> </wm-tooltip> </div> <span if.bind="collapsed" class="mod-suggestion-list-header-subtitle">${headerSubtitle}</span> </div> <i class="icon collapse-category"></i> </div> <div class="cheats mod-suggestion-list-row-bg"> <div class="cheats-wrapper"> <div if.bind="topModSuggestions.length > 0" class="cheat" repeat.for="i of topModSuggestions.length"> <div class="name-input-container"> <div class="cheat-name"> <div class="cheat-name-inner"> <span class="suggestion-rank row-leading label"> ${topModSuggestions[i].rank ? topModSuggestions[i].rank : \'--\'} </span> <span class="label-wrapper"> <span class="label">${topModSuggestions[i].suggestion}</span> </span> </div> <wm-badge if.bind="topModSuggestions[i].isNew" size="s" color="yellow">${\'suggest_mod.new\' | i18n}</wm-badge> </div> <div class="input"> <div class="input-inner"> <div class="input-shrinkwrap"> <wm-boost-button color="yellow" boosting.bind="boosting" user-boost-count.bind="account.boosts" is-pro.bind="isPro" on-click.call="boost(gameId, topModSuggestions[i].id)" times-boosted.bind="topModSuggestions[i].timesBoosted" show-boost-count.bind="!topModSuggestions[i].needsReview" disabled-hint-token.bind="topBoostButtonDisabledHints[i]"></wm-boost-button> </div> </div> </div> </div> <div class="hotkeys"> <div class="hotkeys-inner"> <div class="mod-suggestion-avatar-badge"> <wm-image-overlay-border> <img src.bind="topModSuggestions[i].originalBoosterAvatarUrl | cdn: {size: 48}" fallback-src="'+a+'" alt="${topModSuggestions[i].originalBoosterUsername} avatar"> </wm-image-overlay-border> <span class="mod-suggestion-username">${topModSuggestions[i].originalBoosterUsername}</span> </div> </div> </div> <div class="pin-mod-icon-placeholder"></div> <div class="swipe-menu"></div> </div> <div else class="no-suggestions-message">${\'suggest_mod.no_suggestions\' | i18n}</div> </div> <div class="category-header"> <wm-image-overlay-border> <img src.bind="account.profileImage | cdn:{size: 48}" fallback-src="'+a+'" alt="${account.username} avatar"> </wm-image-overlay-border> <span class="label">${\'suggest_mod.your_suggestions\' | i18n}</span> </div> <div class="cheats"> <div class="cheats-wrapper"> <div if.bind="userModSuggestions.approved.length > 0" class="cheat" repeat.for="i of userModSuggestions.approved.length"> <div class="name-input-container"> <div class="cheat-name"> <div class="cheat-name-inner"> <span class="suggestion-rank row-leading label"> ${userModSuggestions.approved[i].rank ? userModSuggestions.approved[i].rank : \'--\'} </span> <span class="label-wrapper"> <span class="label">${userModSuggestions.approved[i].suggestion}</span> </span> </div> </div> <div class="input"> <div class="input-inner"> <div class="input-shrinkwrap"> <wm-boost-button color="yellow" boosting.bind="boosting" user-boost-count.bind="account.boosts" is-pro.bind="isPro" on-click.call="boost(gameId, userModSuggestions.approved[i].id)" disabled.bind="boosting || userModSuggestions.approved[i].needsReview" times-boosted.bind="userModSuggestions.approved[i].timesBoosted" show-boost-count.bind="!userModSuggestions.approved[i].needsReview" disabled-hint-token.bind="userBoostButtonDisabledHints[i]"></wm-boost-button> </div> </div> </div> </div> <div class="hotkeys"> <div class="hotkeys-inner"> <div class="mod-suggestion-avatar-badge"> <wm-image-overlay-border> <img src.bind="userModSuggestions.approved[i].originalBoosterAvatarUrl | cdn: {size: 48}" fallback-src="'+a+'" alt="${userModSuggestions.approved[i].originalBoosterUsername} avatar"> </wm-image-overlay-border> <span class="mod-suggestion-username">${userModSuggestions.approved[i].originalBoosterUsername}</span> </div> </div> </div> <div class="pin-mod-icon-placeholder"></div> <div class="swipe-menu"></div> </div> <div else class="no-suggestions-message">${\'suggest_mod.no_suggestions\' | i18n}</div> <div class="category-header no-icon" if.bind="userModSuggestions.needsReview.length > 0"> <span class="label">${\'suggest_mod.needs_review\' | i18n}</span> </div> <div class="cheats-wrapper"> <div class="cheat" repeat.for="modSuggestion of userModSuggestions.needsReview"> <div class="name-input-container"> <div class="cheat-name"> <div class="cheat-name-inner"> <span class="suggestion-rank row-leading label"> ${modSuggestion.rank ? modSuggestion.rank : \'--\'} </span> <span class="label-wrapper"> <span class="label">${modSuggestion.suggestion}</span> </span> </div> </div> <div class="input"> <div class="input-inner"> <div class="input-shrinkwrap"> <wm-boost-button color="yellow" boosting.bind="boosting" user-boost-count.bind="account.boosts" is-pro.bind="isPro" disabled.bind="true" times-boosted.bind="modSuggestion.timesBoosted" show-boost-count.bind="false" disabled-hint-token.bind="\'suggest_mod.boosting_disabled_for_suggestions_under_review\'"></wm-boost-button> </div> </div> </div> </div> <div class="hotkeys"> <div class="hotkeys-inner"> <div class="mod-suggestion-avatar-badge"> <wm-image-overlay-border> <img src.bind="modSuggestion.originalBoosterAvatarUrl | cdn: {size: 48}" fallback-src="'+a+'" alt="${modSuggestion.originalBoosterUsername} avatar"> </wm-image-overlay-border> <span class="mod-suggestion-username">${modSuggestion.originalBoosterUsername}</span> </div> </div> </div> <div class="pin-mod-icon-placeholder"></div> <div class="swipe-menu"></div> </div> <div class="cheat suggest-a-mod-row-container" click.delegate="suggestMod()" pro-cta="trigger: suggest_mod; disabled.bind: isPro"> <i class="add-icon"></i> <span class="text-button">${\'suggest_mod.title\' | i18n} <wm-pro-badge if.bind="!isPro"></wm-pro-badge></span> </div> </div> </div> </div> </div> </div> </template> '},"cheats/mod-suggestions/mod-suggestion-picker":(e,s,o)=>{o.r(s),o.d(s,{ModSuggestionPicker:()=>r,isSuggestedModNameOption:()=>d});var t=o(15215),i=o("aurelia-framework"),n=o(51808),a=o(9374),g=o(70236);const d=e=>"string"==typeof e.value;class r{constructor(){this.suggestedOptions=[],this.searchOptions=[],this.searchValue="",this.selectedOption=null,this.filteredOptions=[]}attached(){this.filteredOptions=this.suggestedOptions.slice(0,3),this.focusInput()}searchValueChanged(){this.selectedOption&&this.searchValue!==this.selectedOption.label&&(this.selectedOption=null),this.filteredOptions=this.searchValue.length>0?this.searchOptions.filter((e=>e.label.toLowerCase().includes(this.searchValue.toLowerCase()))).slice(0,5):this.suggestedOptions.slice(0,3)}focusInput(){this.inputEl?.focus()}get open(){return!this.selectedOption&&this.filteredOptions.length>0}get optionSource(){return this.searchValue.length>0?"search":"suggested"}selectOption(e){this.selectedOption=this.filteredOptions[e],this.searchValue=this.selectedOption.label}isNew(e){return!d(e)&&(0,g.Lt)(e.value.flags,n.V.isNew)}}(0,t.Cg)([(0,i.bindable)(),(0,t.Sn)("design:type",Array)],r.prototype,"suggestedOptions",void 0),(0,t.Cg)([(0,i.bindable)(),(0,t.Sn)("design:type",Array)],r.prototype,"searchOptions",void 0),(0,t.Cg)([(0,i.observable)(),(0,t.Sn)("design:type",String)],r.prototype,"searchValue",void 0),(0,t.Cg)([(0,i.bindable)(),(0,t.Sn)("design:type",Object)],r.prototype,"selectedOption",void 0),(0,t.Cg)([(0,a._)((e=>[e.filteredOptions,e.selectedOption])),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],r.prototype,"open",null),(0,t.Cg)([(0,a._)((e=>[e.searchValue])),(0,t.Sn)("design:type",String),(0,t.Sn)("design:paramtypes",[])],r.prototype,"optionSource",null)},"cheats/mod-suggestions/mod-suggestion-picker.html":(e,s,o)=>{o.r(s),o.d(s,{default:()=>t});const t='<template> <require from="./mod-suggestion-picker.scss"></require> <div class="mod-suggestion-picker ${open ? \'mod-suggestion-picker--open\' : \'\'}"> <div class="mod-suggestion-picker-input-wrapper" click.delegate="focusInput()"> <input list="mod-suggestion-picker-options" class="mod-suggestion-picker-input" type="text" value.bind="searchValue" ref="inputEl" placeholder="${\'suggest_mod.start_typing\' | i18n}"> </div> <div if.bind="open" class="mod-suggestion-picker-suggestions-wrapper" role="listbox"> <ul class="mod-suggestion-picker-option-group"> <div class="mod-suggestion-picker-option-group-label"> <span class="mod-suggestion-picker-option-group-icon">${optionSource === \'search\' ? \'search\' : \'thumb_up\'}</span> ${(optionSource === \'search\' ? \'suggest_mod.related_suggestions\' : \'suggest_mod.most_popular_suggestions\') | i18n} </div> <li class="mod-suggestion-picker-option" tabindex="0" repeat.for="i of filteredOptions.length" role="option" click.trigger="selectOption(i)"> ${filteredOptions[i].label} <wm-badge if.bind="isNew(filteredOptions[i])" size="s" color="yellow"> ${\'suggest_mod.new\' | i18n} </wm-badge> </li> </ul> </div> <span if.bind="searchValue && !selectedOption && filteredOptions.length === 0" class="mod-suggestion-picker-note" innerhtml.bind="\'suggest_mod.no_freeform_note\' | i18n | markdown"></span> <span else if.bind="selectedOption" class="mod-suggestion-picker-note" innerhtml.bind="\'suggest_mod.refund_note\' | i18n | markdown"></span> </div> </template> '},"cheats/mod-suggestions/mod-suggestion-picker.scss":(e,s,o)=>{o.r(s),o.d(s,{default:()=>u});var t=o(31601),i=o.n(t),n=o(76314),a=o.n(n),g=o(4417),d=o.n(g),r=new URL(o(83959),o.b),l=a()(i()),p=d()(r);l.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,mod-suggestion-picker .mod-suggestion-picker-option-group-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}mod-suggestion-picker{--mod-suggestion-picker-background-color: rgba(255, 255, 255, 0.05)}mod-suggestion-picker .mod-suggestion-picker{display:flex;position:relative;flex-direction:column}mod-suggestion-picker .mod-suggestion-picker--open .mod-suggestion-picker-input-wrapper{border-radius:12px 12px 0px 0px}mod-suggestion-picker .mod-suggestion-picker-input-wrapper{display:flex;border-radius:12px;display:flex;align-items:center;backdrop-filter:blur(25px);background-color:var(--mod-suggestion-picker-background-color);overflow:hidden}mod-suggestion-picker .mod-suggestion-picker-input-wrapper input{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-highlight);width:100%;height:100%;background:none;border:none;padding:12px 20px 12px 16px}mod-suggestion-picker .mod-suggestion-picker-input-wrapper input::placeholder{color:var(--theme--text-disabled)}mod-suggestion-picker .mod-suggestion-picker-suggestions-wrapper{display:flex;flex-direction:column;border-radius:0px 0px 12px 12px;backdrop-filter:blur(12px);background-color:var(--mod-suggestion-picker-background-color);border-top:1px solid rgba(255,255,255,.15)}mod-suggestion-picker .mod-suggestion-picker-option-group{display:flex;flex-direction:column;padding:0;margin:0;list-style:none;padding:8px}mod-suggestion-picker .mod-suggestion-picker-option-group-label{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;color:var(--theme--text-secondary);display:flex;gap:8px;padding:8px 12px 4px}mod-suggestion-picker .mod-suggestion-picker-option-group-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;font-size:16px;color:var(--theme--text-secondary)}mod-suggestion-picker .mod-suggestion-picker-option{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-primary);padding:8px 12px 8px 36px;border-radius:8px;display:flex;align-items:center;gap:4px}mod-suggestion-picker .mod-suggestion-picker-option--focused,mod-suggestion-picker .mod-suggestion-picker-option:focus{background-color:rgba(255,255,255,.1)}mod-suggestion-picker .mod-suggestion-picker-option:hover{cursor:pointer;background-color:rgba(255,255,255,.15)}mod-suggestion-picker .mod-suggestion-picker-note{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;padding:12px 16px;color:var(--theme--text-secondary);background-color:var(--mod-suggestion-picker-background-color);border-radius:12px;margin-top:16px}`,""]);const u=l},"cheats/mod-suggestions/suggest-mod-dialog-title":(e,s,o)=>{o.r(s),o.d(s,{SuggestModDialogTitle:()=>t});class t{}},"cheats/mod-suggestions/suggest-mod-dialog-title.html":(e,s,o)=>{o.r(s),o.d(s,{default:()=>t});const t="<template> <require from=\"./suggest-mod-dialog-title.scss\"></require> <header>${'suggest_mod.title' | i18n}</header> <wm-pro-badge></wm-pro-badge> </template> "},"cheats/mod-suggestions/suggest-mod-dialog-title.scss":(e,s,o)=>{o.r(s),o.d(s,{default:()=>u});var t=o(31601),i=o.n(t),n=o(76314),a=o.n(n),g=o(4417),d=o.n(g),r=new URL(o(83959),o.b),l=a()(i()),p=d()(r);l.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,suggest-mod-dialog-title::before{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}suggest-mod-dialog-title{display:flex;align-items:center}suggest-mod-dialog-title::before{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;content:"lightbulb";color:var(--theme--text-secondary);font-size:16px}suggest-mod-dialog-title header{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;color:var(--theme--text-primary);margin:0px 6px 0px 12px}`,""]);const u=l}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1497],{"app/resources/elements/game-bar-overlay-button":(t,e,i)=>{i.r(e),i.d(e,{GameBarOverlayButton:()=>s});var o=i(15215),a=i("aurelia-event-aggregator"),l=i("aurelia-framework"),n=i(38777),r=i(98300);let s=class{#t;#e;constructor(t,e){this.tooltipOpen=!1,this.#e=t,this.overlay=e}attached(){this.#t=(new n.Vd).push(this.#e.subscribe("open-overlay-tooltip",(()=>this.openTooltip()))),this.#i()}detached(){this.#t.dispose(),this.tooltipOpen=!1}openTooltip(){this.tooltipOpen=!0}tooltipOpenChanged(t,e){!0===e&&!1===t&&this.#e.publish("overlay-tooltip-closed")}async#i(){this.status=await this.overlay.refreshFeatureStatus()}get isConnected(){return"connected"===this.overlay.status}};(0,o.Cg)([l.observable,(0,o.Sn)("design:type",Boolean)],s.prototype,"tooltipOpen",void 0),(0,o.Cg)([(0,l.computedFrom)("overlay.status"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],s.prototype,"isConnected",null),s=(0,o.Cg)([(0,l.autoinject)(),(0,o.Sn)("design:paramtypes",[a.EventAggregator,r.Hy])],s)},"app/resources/elements/game-bar-overlay-button.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>o});const o='<template class="${isConnected ? \'connected\' : \'\'} ${tooltipOpen ? \'tooltip-open\' : \'\'} ${status == \'not-installed\' ? \'not-installed\' : \'\'}"> <require from="./game-bar-overlay-button.scss"></require> <require from="./game-bar-overlay-tooltip"></require> <div class="action-button"> <button class="overlay-button" click.delegate="openTooltip()"> <span class="overlay-icon"> <span if.bind="status != \'not-installed\'" class="status-indicator"></span> <i>dock_to_right</i> </span> <div class="overlay-label-container"> <span if.bind="status == \'not-installed\'" class="label"> ${\'overlay_button.install_overlay\' | i18n} </span> <template else> <span class="label"> ${\'overlay_button.overlay\' | i18n} </span> <span class="shortcut"> <i class="windows-hotkey"> </i> <span class="label">${\'overlay_button.shortcut_key\' | i18n}</span> </span> </template> </div> </button> </div> <game-bar-overlay-tooltip class="overlay-tooltip" open.bind="tooltipOpen"></game-bar-overlay-tooltip> </template> '},"app/resources/elements/game-bar-overlay-button.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>m});var o=i(31601),a=i.n(o),l=i(76314),n=i.n(l),r=i(4417),s=i.n(r),g=new URL(i(83959),i.b),c=new URL(i(60247),i.b),b=new URL(i(18265),i.b),d=n()(a()),p=s()(g),h=s()(c),u=s()(b);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,game-bar-overlay-button .action-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}game-bar-overlay-button{position:relative;flex:1 1 auto}game-bar-overlay-button .action-button{border-radius:16px;overflow:hidden;position:relative;display:block}game-bar-overlay-button .action-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;height:16px}game-bar-overlay-button .action-button .icon-container{height:16px;width:16px;position:relative}game-bar-overlay-button .action-button button,game-bar-overlay-button .action-button .button{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;display:flex;align-items:center;transition:color .3s ease-in-out;background:rgba(var(--theme--background-accent--rgb), 0.9);border:1px solid rgba(255,255,255,.1);border-radius:16px;gap:12px;padding:10px 10px 10px 16px;color:rgba(255,255,255,.8);height:44px;width:100%;min-width:170px}game-bar-overlay-button .action-button button i,game-bar-overlay-button .action-button .button i{color:rgba(255,255,255,.6)}game-bar-overlay-button .action-button .label{line-height:14px;text-align:left;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;text-overflow:ellipsis;overflow:hidden}game-bar-overlay-button .action-button.disabled button,game-bar-overlay-button .action-button.disabled .button{background-color:rgba(var(--theme--background-accent--rgb), 0.4);color:var(--theme--text-secondary)}game-bar-overlay-button i{font-variation-settings:"FILL" 1,"wght" 400 !important}game-bar-overlay-button .overlay-label-container{display:flex;align-items:center;justify-content:space-between;width:100%}game-bar-overlay-button .overlay-icon{position:relative;display:flex;align-items:center}game-bar-overlay-button .status-indicator{position:absolute;top:-1px;right:0;border-radius:100px;width:6px;height:6px;background:var(--color--accent);outline:2px solid rgba(var(--theme--background-accent--rgb)) !important}game-bar-overlay-button:not(.connected):hover .overlay-button:hover{color:#fff;background:linear-gradient(0deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.03) 100%),rgba(var(--theme--background-accent--rgb), 0.9)}game-bar-overlay-button .shortcut{display:flex;justify-content:center;align-items:center;font-size:12px;gap:2px;position:relative}game-bar-overlay-button .shortcut .close{-webkit-mask-box-image:url(${h});background-color:rgba(255,255,255,.6);width:12px;height:12px}game-bar-overlay-button .shortcut i.windows-hotkey{-webkit-mask-box-image:url(${u});background-color:rgba(255,255,255,.8);width:10px;height:10px}game-bar-overlay-button .custom-tooltip .tooltip,game-bar-overlay-button .initial-tooltip .tooltip{right:10%}game-bar-overlay-button .custom-tooltip .tooltip .tooltip-arrow,game-bar-overlay-button .initial-tooltip .tooltip .tooltip-arrow{margin-right:-10px;right:15%}`,""]);const m=d},"app/resources/elements/game-bar-overlay-tooltip":(t,e,i)=>{i.r(e),i.d(e,{GameBarOverlayTooltip:()=>r});var o=i(15215),a=i("aurelia-framework"),l=i(38777),n=i(98300);let r=class{#o;#a;#l;#n;constructor(t,e){this.open=!1,this.helpVisible=!1,this.minimumWindowsVersion=n.sg,this.#n=null,this.#o=t,this.#a=e}attached(){const t=this.#o.parentElement;t&&(this.#l=(new l.Vd).pushEventListener(t,"mouseenter",this.#r.bind(this)).pushEventListener(t,"mouseleave",this.#s.bind(this))),this.#i()}detached(){this.#l?.dispose(),this.#l=null,null!==this.#n&&(clearInterval(this.#n),this.#n=null)}#r(){this.open||(this.helpVisible=!0)}#s(){this.helpVisible=!1}openChanged(t){t?(this.#i(),this.#n=setInterval((()=>this.#i()),5e3)):(clearInterval(this.#n),this.#n=null)}openInStore(){return this.#a.openInStore("tooltip")}openGameBarSettings(){return this.#a.openGameBarSettings()}async#i(){this.status=await this.#a.refreshFeatureStatus()}};(0,o.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.twoWay}),(0,o.Sn)("design:type",Boolean)],r.prototype,"open",void 0),r=(0,o.Cg)([(0,a.autoinject)(),(0,o.Sn)("design:paramtypes",[Element,n.Hy])],r)},"app/resources/elements/game-bar-overlay-tooltip.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>o});const o='<template> <require from="./game-bar-overlay-tooltip.scss"></require> <require from="../../../dialogs/resources/elements/desktop-with-overlay.html"></require> <require from="../../../shared/resources/elements/tooltip"></require> <tooltip class="initial-tooltip" direction="top-right" open.bind="helpVisible"> <div slot="content">${\'overlay_tooltip.click_to_use_the_overlay\' | i18n}</div> </tooltip> <tooltip class="overlay-tooltip custom-tooltip" direction="top-right" open.bind="open" click-to-open="true"> <div slot="content"> <div class="layout"> <div class="left"> <template if.bind="status == \'unsupported\'"> <h1> ${\'overlay_tooltip.unsupported\' | i18n} <span class="pulse alert"></span> </h1> <p>${\'overlay_tooltip.your_pc_does_not_meet_the_requirements\' | i18n}</p> <div class="requirements"> ${\'overlay_tooltip.requires_version\' | i18n:{version: minimumWindowsVersion}} </div> </template> <template if.bind="status == \'not-installed\'"> <h1> ${\'overlay_tooltip.not_installed\' | i18n} <span class="pulse alert"></span> </h1> <p>${\'overlay_tooltip.install_the_overlay_from_the_microsoft_store\' | i18n}</p> <button click.delegate="openInStore()">${\'overlay_tooltip.install_the_overlay\' | i18n}</button> </template> <template if.bind="status == \'game-bar-disabled\'"> <h1> ${\'overlay_tooltip.enable_game_bar\' | i18n} <span class="warning"></span> </h1> <p>${\'overlay_tooltip.the_overlay_requires_game_bar\' | i18n}</p> <button class="accent" click.delegate="openGameBarSettings()"> ${\'overlay_tooltip.open_game_bar_settings\' | i18n} </button> </template> <template if.bind="status == \'installed\'"> <h1> ${\'overlay_tooltip.overlay\' | i18n} <span class="pulse regular"></span> </h1> <p innerhtml.bind="\'overlay_tooltip.the_overlay_is_installed\' | i18n | markdown"></p> </template> </div> <div class="right"> <desktop-with-overlay if.bind="open"></desktop-with-overlay> </div> </div> </div> </tooltip> </template> '},"app/resources/elements/game-bar-overlay-tooltip.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>x});var o=i(31601),a=i.n(o),l=i(76314),n=i.n(l),r=i(4417),s=i.n(r),g=new URL(i(96369),i.b),c=new URL(i(76048),i.b),b=new URL(i(81206),i.b),d=new URL(i(79948),i.b),p=new URL(i(40551),i.b),h=n()(a()),u=s()(g),m=s()(c),v=s()(b),f=s()(d),y=s()(p);h.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@keyframes -pulse{0%{transform:scale(0.3)}50%{transform:scale(0.5)}100%{transform:scale(0.3)}}@keyframes pulse-2{0%{transform:scale(1);opacity:0}70%{transform:scale(1.5);opacity:1}100%{transform:scale(2);opacity:0}}.billing-settings section{padding:20px;border-radius:10px;border:1px solid rgba(255,255,255,.04);transition:opacity .15s}.billing-settings section.filled{background:rgba(255,255,255,.04);border:none;padding-top:18px}.billing-settings section.filled h5{color:rgba(255,255,255,.6)}.billing-settings section.filled h5.plan-header{margin-bottom:16px;color:#fff}.billing-settings section+section{margin-top:20px}.billing-settings section.loading{opacity:.4}.billing-settings section.loading,.billing-settings section.loading *{pointer-events:none}.billing-settings section.layout{display:flex}.billing-settings section.layout>*:first-child{flex:1 1 auto}.billing-settings section.layout>*:last-child{flex:0 0 auto}.billing-settings .details{display:flex;align-items:center}.billing-settings .details .meta{display:flex;align-items:center;color:rgba(255,255,255,.5)}.billing-settings .details .meta>*+*{margin-left:7px}.billing-settings .details .meta,.billing-settings .details .meta strong,.billing-settings .details .meta em{font-size:13px;line-height:20px;font-weight:500}.billing-settings .details .meta em{font-style:normal;color:rgba(255,255,255,.8)}.billing-settings .details .card-type{width:34px;height:22px;border-radius:4px;background-position:center;background-repeat:no-repeat;background-image:url(${u});display:inline-block;flex:0 0 auto}.billing-settings .details .meta.payment{display:flex;flex-direction:column;align-items:flex-start;justify-content:center}.billing-settings .details .meta.payment>*+*{margin-left:0}.billing-settings .details .meta.payment.canceled{border-left:1px solid rgba(255,255,255,.1);padding-left:20px;flex-direction:row;align-items:center;gap:10px}.billing-settings .details .meta.warning,.billing-settings .details .meta.warning *{color:var(--color--accent-yellow)}.billing-settings .details .row-actions{display:flex;align-items:center}.billing-settings .details .row-actions .links{display:flex;align-items:center;margin-left:20px;padding-left:20px;border-left:1px solid rgba(255,255,255,.1)}.billing-settings .details .row-actions .links .remove{color:rgba(var(--color--alert--rgb), 0.8)}.billing-settings .details .row-actions .links .remove:hover{color:#fff}.billing-settings .link{font-size:13px;line-height:20px;font-weight:700;background:rgba(0,0,0,0);border:0;color:rgba(255,255,255,.25);padding:0}.billing-settings .link:hover{color:#fff}.billing-settings .resume-btn{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700;padding:20px 16px}.billing-settings .resume-btn,.billing-settings .resume-btn *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn{border:1px solid #fff}}.billing-settings .resume-btn>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .resume-btn>*:first-child{padding-left:0}.billing-settings .resume-btn>*:last-child{padding-right:0}.billing-settings .resume-btn svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn svg *{fill:CanvasText}}.billing-settings .resume-btn svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn svg{opacity:1}}.billing-settings .resume-btn img{height:50%}.billing-settings .resume-btn:disabled{opacity:.3}.billing-settings .resume-btn:disabled,.billing-settings .resume-btn:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .resume-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .resume-btn:not(:disabled):hover svg{opacity:1}}.billing-settings .resume-btn:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.billing-settings .resume-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.billing-settings .resume-btn:not(:disabled):active{background-color:var(--theme--highlight)}.billing-settings h5{font-weight:600;font-size:16px;line-height:25px;font-weight:700;color:#fff;margin:0 0 11px}.billing-settings h5 em{font-style:normal;color:var(--theme--highlight)}.billing-settings h5 strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;margin-right:4px;vertical-align:middle}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings h5 strong{border:1px solid #fff}}.billing-settings h5+.details{margin-top:2px}.billing-settings h5.warning{color:var(--color--accent-yellow) !important;display:inline-flex;align-items:center}.billing-settings h5.warning:before{display:inline-block;content:"";width:15px;height:15px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${m}) 6px 3px no-repeat}.billing-settings h5.warning:before{margin-right:8px}.billing-settings .canceled-details{display:flex;align-items:center;border-left:1px solid rgba(255,255,255,.1);padding-left:20px;margin-left:20px}.billing-settings .canceled-details .meta{display:flex;align-items:center;color:rgba(255,255,255,.5)}.billing-settings .canceled-details .meta>*+*{margin-left:7px}.billing-settings .canceled-details .meta,.billing-settings .canceled-details .meta strong,.billing-settings .canceled-details .meta em{font-size:13px;line-height:20px;font-weight:500}.billing-settings .canceled-details .meta em{font-style:normal;color:rgba(255,255,255,.8)}.billing-settings .canceled-details .card-type{width:34px;height:22px;border-radius:4px;background-position:center;background-repeat:no-repeat;background-image:url(${u});display:inline-block;flex:0 0 auto}.billing-settings .main-actions{margin:17px 0 0 0}.billing-settings .main-actions>*+*{margin-left:15px}.billing-settings .main-actions .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent);font-weight:800;--cta--padding: 18px;--cta--height: 40px;--cta--hover--border-width: 2px;font-size:18px}.billing-settings .main-actions .button,.billing-settings .main-actions .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button{border:1px solid #fff}}.billing-settings .main-actions .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .main-actions .button>*:first-child{padding-left:0}.billing-settings .main-actions .button>*:last-child{padding-right:0}.billing-settings .main-actions .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button svg *{fill:CanvasText}}.billing-settings .main-actions .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button svg{opacity:1}}.billing-settings .main-actions .button img{height:50%}.billing-settings .main-actions .button:disabled{opacity:.3}.billing-settings .main-actions .button:disabled,.billing-settings .main-actions .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .main-actions .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .main-actions .button:not(:disabled):hover svg{opacity:1}}.billing-settings .main-actions .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.billing-settings .main-actions .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}.billing-settings .main-actions .button:not(:disabled):active{background-color:var(--color--accent)}.billing-settings .main-actions .button:not(:disabled):active{--cta__icon--color: #000;color:#000}.billing-settings .main-actions .button.main{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.billing-settings .main-actions .button.main:hover{filter:brightness(1.1)}}.billing-settings .main-actions .button.secondary{background-color:rgba(255,255,255,.6);box-shadow:none;color:var(--theme--background)}.billing-settings .main-actions .button.secondary svg{opacity:1}.billing-settings .main-actions .button.secondary svg *{--cta__icon--color: var(--theme--background)}@media(hover: hover){.billing-settings .main-actions .button.secondary:not(:disabled):hover{background-color:var(--theme--highlight)}}.billing-settings .main-actions .button.accent{background-color:rgba(var(--color--accent--rgb), 0.08) !important;color:var(--color--accent) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.billing-settings .main-actions .button.accent:hover{filter:brightness(1.1)}}.billing-settings .main-actions .button.small{font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}.billing-settings .main-actions .divider{border-left:1px solid rgba(255,255,255,.1);height:28px}.billing-settings .main-actions .promo{font-size:13px;line-height:20px;color:var(--color--accent);display:inline-flex;align-items:center}.billing-settings .main-actions .promo i{margin-left:7px}.billing-settings .main-actions .promo i svg *{fill:var(--color--accent)}.billing-settings .alert{margin-top:8px}.billing-settings .info{font-size:14px;line-height:21px;font-weight:500;line-height:19px;color:rgba(255,255,255,.5)}.billing-settings .disclaimer{font-size:13px;line-height:20px;font-weight:500;color:rgba(255,255,255,.3);display:flex;align-items:center;margin:20px 0 0 0}.billing-settings .disclaimer .icon{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;--cta--height: 15px;--cta--hover--border-width: 1px;min-width:var(--cta--height);width:var(--cta--height);border-radius:50%;justify-content:center;align-items:center;position:relative;background:rgba(255,255,255,.1);box-shadow:none !important;pointer-events:none;margin-right:7px}.billing-settings .disclaimer .icon,.billing-settings .disclaimer .icon *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon{border:1px solid #fff}}.billing-settings .disclaimer .icon>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .disclaimer .icon>*:first-child{padding-left:0}.billing-settings .disclaimer .icon>*:last-child{padding-right:0}.billing-settings .disclaimer .icon svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon svg *{fill:CanvasText}}.billing-settings .disclaimer .icon svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon svg{opacity:1}}.billing-settings .disclaimer .icon img{height:50%}.billing-settings .disclaimer .icon:disabled{opacity:.3}.billing-settings .disclaimer .icon:disabled,.billing-settings .disclaimer .icon:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .disclaimer .icon:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .disclaimer .icon:not(:disabled):hover svg{opacity:1}}.billing-settings .disclaimer .icon:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.billing-settings .disclaimer .icon,.billing-settings .disclaimer .icon>*{padding:0 !important}.billing-settings .disclaimer .icon:active{background-color:rgba(0,0,0,0) !important;color:rgba(255,255,255,.8) !important}.billing-settings .disclaimer .icon svg{opacity:.5}.billing-settings .disclaimer a{color:rgba(255,255,255,.5)}.billing-settings .disclaimer a:hover{color:#fff}.billing-settings .graphic{margin:0 0 -20px 0;justify-self:flex-end}.billing-settings .subscribed{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;--checkbox--checked-color: var(--color--accent);border:1px solid rgba(255,255,255,.15);border-radius:100px;padding:8px 15px;transition:background-color .15s,border-color .15s;background-color:rgba(var(--color--accent--rgb), 0.08);cursor:initial;border-color:rgba(0,0,0,0);--checkbox__label--color: var(--checkbox--checked-color);margin-right:15px}.billing-settings .subscribed,.billing-settings .subscribed *{cursor:pointer}.billing-settings .subscribed,.billing-settings .subscribed *{cursor:pointer}.billing-settings .subscribed>*:first-child{margin-right:9px}.billing-settings .subscribed:hover>*{--checkbox__label--color: #fff}.billing-settings .subscribed,.billing-settings .subscribed *:not(info-tooltip){cursor:default !important}.billing-settings .subscribed .checkbox{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;width:15px;height:15px;background:rgba(0,0,0,0);border-color:rgba(255,255,255,.25);border-color:rgba(0,0,0,0)}.billing-settings .subscribed .checkbox,.billing-settings .subscribed .checkbox *{cursor:pointer}.billing-settings .subscribed .checkbox:checked:before{opacity:1}.billing-settings .subscribed .checkbox:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${v});mask:url(${v})}.billing-settings .subscribed .checkbox:before{left:1px;top:0;width:15px;height:11px;transform:scale(1)}.billing-settings .subscribed .checkbox:before{opacity:1}.billing-settings .subscribed>.icon{margin-right:9px}.billing-settings .subscribed>.icon svg *{fill:var(--color--accent)}.billing-settings .subscribed .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color);transition:color .15s;color:var(--color--accent) !important}.billing-settings .subscribed .label,.billing-settings .subscribed .label *{cursor:pointer}.billing-settings .subscribed info-tooltip{margin-left:10px}.billing-settings .subscribed info-tooltip,.billing-settings .subscribed info-tooltip *{cursor:pointer}.billing-settings .subscribed.warning{background:rgba(var(--color--accent-yellow--rgb), 0.08)}.billing-settings .subscribed.warning .label{color:var(--color--accent-yellow) !important}.billing-settings .subscribed.warning .checkbox{display:none}.billing-settings .subscribed.warning:before{display:inline-block;content:"";width:19px;height:19px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${f}) center no-repeat;margin-right:11px}.billing-settings .subscribed.canceled{background:rgba(var(--theme--highlight--rgb), 0.08)}.billing-settings .subscribed.canceled .checkbox:before{background:var(--theme--highlight) !important}.billing-settings .subscribed.canceled .label{color:var(--theme--highlight) !important}.billing-settings .wemod-tag{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:normal;text-transform:capitalize;white-space:nowrap;padding:1px 6px}.billing-settings hr{border:0;border-top:1px solid rgba(255,255,255,.1);margin:20px 0 16px}game-bar-overlay-tooltip .initial-tooltip .tooltip .tooltip-content{white-space:nowrap}game-bar-overlay-tooltip .overlay-tooltip .tooltip{background:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.05);border-radius:10px}game-bar-overlay-tooltip .overlay-tooltip .tooltip .tooltip-content{padding:0;border:0 !important}.theme-default game-bar-overlay-tooltip .overlay-tooltip .tooltip .tooltip-content{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro game-bar-overlay-tooltip .overlay-tooltip .tooltip .tooltip-content{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro game-bar-overlay-tooltip .overlay-tooltip .tooltip .tooltip-content{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro game-bar-overlay-tooltip .overlay-tooltip .tooltip .tooltip-content{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro game-bar-overlay-tooltip .overlay-tooltip .tooltip .tooltip-content{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}game-bar-overlay-tooltip .overlay-tooltip .tooltip .tooltip-content>*+*{margin-left:0}game-bar-overlay-tooltip .overlay-tooltip .layout{display:flex;width:466px}game-bar-overlay-tooltip .overlay-tooltip .layout .left{flex:1 1 auto;padding:17px 25px 25px 25px;background-color:var(--theme--secondary-background)}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1{font-weight:800;font-size:21px;line-height:30px;font-weight:700;display:flex;align-items:center;margin:0 0 9px;color:#fff}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .pulse{position:relative;margin-left:14px}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .pulse.regular{width:6px;height:6px}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .pulse.regular:after{background:rgba(var(--color--accent--rgb), 0.8);content:"";position:absolute;left:0;top:0;width:6px;height:6px;display:inline-block;border-radius:50%;animation:pulse 1s infinite ease-in-out}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .pulse.regular:before{background:rgba(var(--color--accent--rgb), 0.5);content:"";display:block;position:absolute;left:0;top:0;width:6px;height:6px;border-radius:50%;animation:pulse-2 1s infinite}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .pulse.alert{width:6px;height:6px}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .pulse.alert:after{background:rgba(255,99,104,.8);content:"";position:absolute;left:0;top:0;width:6px;height:6px;display:inline-block;border-radius:50%;animation:pulse 1s infinite ease-in-out}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .pulse.alert:before{background:rgba(255,99,104,.5);content:"";display:block;position:absolute;left:0;top:0;width:6px;height:6px;border-radius:50%;animation:pulse-2 1s infinite}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .warning{position:relative;margin-left:9px}game-bar-overlay-tooltip .overlay-tooltip .layout .left h1 .warning:before{display:inline-block;content:"";width:15px;height:15px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${m}) 6px 3px no-repeat}game-bar-overlay-tooltip .overlay-tooltip .layout .left p{font-weight:700;font-size:15px;line-height:24px;font-weight:500;color:rgba(255,255,255,.8);margin:0 0 12px}game-bar-overlay-tooltip .overlay-tooltip .layout .left p strong{font-size:11px;line-height:16px;display:inline-flex;line-height:24px;text-transform:uppercase;border:1px solid var(--color--brand-blue);vertical-align:middle}game-bar-overlay-tooltip .overlay-tooltip .layout .left p strong>*{padding:0 8px;font-weight:inherit;color:var(--color--brand-blue);display:inline-block}game-bar-overlay-tooltip .overlay-tooltip .layout .left p strong>*+*{border-left:1px solid var(--color--brand-blue)}game-bar-overlay-tooltip .overlay-tooltip .layout .left p strong>*:first-child:before{content:"";display:inline-block;width:16px;height:16px;background:var(--color--brand-blue);-webkit-mask-box-image:url(${y});margin-right:6px;vertical-align:middle}game-bar-overlay-tooltip .overlay-tooltip .layout .left .requirements{font-size:13px;line-height:20px;color:rgba(255,255,255,.5)}game-bar-overlay-tooltip .overlay-tooltip .layout .left button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}game-bar-overlay-tooltip .overlay-tooltip .layout .left button,game-bar-overlay-tooltip .overlay-tooltip .layout .left button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) game-bar-overlay-tooltip .overlay-tooltip .layout .left button{border:1px solid #fff}}game-bar-overlay-tooltip .overlay-tooltip .layout .left button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}game-bar-overlay-tooltip .overlay-tooltip .layout .left button>*:first-child{padding-left:0}game-bar-overlay-tooltip .overlay-tooltip .layout .left button>*:last-child{padding-right:0}game-bar-overlay-tooltip .overlay-tooltip .layout .left button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) game-bar-overlay-tooltip .overlay-tooltip .layout .left button svg *{fill:CanvasText}}game-bar-overlay-tooltip .overlay-tooltip .layout .left button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) game-bar-overlay-tooltip .overlay-tooltip .layout .left button svg{opacity:1}}game-bar-overlay-tooltip .overlay-tooltip .layout .left button img{height:50%}game-bar-overlay-tooltip .overlay-tooltip .layout .left button:disabled{opacity:.3}game-bar-overlay-tooltip .overlay-tooltip .layout .left button:disabled,game-bar-overlay-tooltip .overlay-tooltip .layout .left button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){game-bar-overlay-tooltip .overlay-tooltip .layout .left button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}game-bar-overlay-tooltip .overlay-tooltip .layout .left button:not(:disabled):hover svg{opacity:1}}game-bar-overlay-tooltip .overlay-tooltip .layout .left button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}game-bar-overlay-tooltip .overlay-tooltip .layout .left button.accent{box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent)}@media(hover: hover){game-bar-overlay-tooltip .overlay-tooltip .layout .left button.accent:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}game-bar-overlay-tooltip .overlay-tooltip .layout .left button.accent:not(:disabled):active{background-color:var(--color--accent)}game-bar-overlay-tooltip .overlay-tooltip .layout .left button.accent:not(:disabled):active{--cta__icon--color: #000;color:#000}game-bar-overlay-tooltip .overlay-tooltip .layout .right{flex:0 0 213px;padding:19px 0 0 35px;overflow:hidden;position:relative}game-bar-overlay-tooltip .overlay-tooltip .layout .right desktop-with-overlay{width:128%;position:absolute;bottom:0}game-bar-overlay-tooltip .overlay-tooltip .layout .right desktop-with-overlay .video-wrapper .overlay{top:5.5%}`,""]);const x=h}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5102],{13323:(t,e,n)=>{n.d(e,{F:()=>o});var i=n(47978);function o(){const t={};return{register:(e,n)=>(t[e]||(t[e]=[]),t[e].push(n),{unregister:()=>{t[e]=t[e].filter((t=>t!==n))}}),triggerHook(e,n){const o=(t[e]||[]).map((t=>t(n)));return(0,i.kg)(...o)}}}},15761:(t,e,n)=>{n.d(e,{N:()=>s});var i=n(85090),o=n(47978),r=n(7328);function s(t,e,n,s,a,c,u){const l=t.replica,d=(0,i.Z)(t,{endpoint:t.rumEndpointBuilder,encoder:u(2)},l&&{endpoint:l.rumEndpointBuilder,transformMessage:t=>(0,o.kg)(t,{application:{id:l.applicationId}}),encoder:u(3)},s,a,c);return e.subscribe(13,(t=>{"view"===t.type?d.upsert(t,t.view.id):d.add(t)})),n.subscribe((e=>d.add(e,(0,r.Wb)(t)))),d}},20915:(t,e,n)=>{n.d(e,{eZ:()=>s,nv:()=>u,t5:()=>a,vW:()=>c});var i=n(29336),o=n(47978),r=n(1356);function s(){return{vitalsByName:new Map,vitalsByReference:new WeakMap}}function a(t,e,n){function o(n){(function(t){return!e.wasInPageStateDuringPeriod("frozen",t.startClocks.relative,t.duration)})(n)&&t.notify(12,function(t){const e={date:t.startClocks.timeStamp,vital:{id:(0,r.lk)(),type:t.type,name:t.name,duration:(0,i.Zj)(t.duration),description:t.description},type:"vital",_dd:{vital:{computed_value:!0}}};return{rawRumEvent:e,startTime:t.startClocks.relative,duration:t.duration,customerContext:t.context,domainContext:{}}}(n))}return{addDurationVital:o,startDurationVital:(t,e={})=>c(n,t,e),stopDurationVital:(t,e={})=>{u(o,n,t,e)}}}function c({vitalsByName:t,vitalsByReference:e},n,o={}){const r={name:n,startClocks:(0,i.M8)(),context:o.context,description:o.description},s={__dd_vital_reference:!0};return t.set(n,r),e.set(s,r),s}function u(t,{vitalsByName:e,vitalsByReference:n},r,s={}){const a="string"==typeof r?e.get(r):n.get(r);a&&(t(function(t,e,n,r){var s;return{name:t.name,type:"duration",startClocks:e,duration:(0,i.vk)(e.timeStamp,r.timeStamp),context:(0,o.kg)(t.context,n.context),description:null!==(s=n.description)&&void 0!==s?s:t.description}}(a,a.startClocks,s,(0,i.M8)())),"string"==typeof r?e.delete(r):n.delete(r))}},37642:(t,e,n)=>{n.d(e,{O1:()=>a});var i=n(29336),o=n(1356),r=n(39042);const s=2*i.iW;function a(t){const e=function(t){const e=t.querySelector("meta[name=dd-trace-id]"),n=t.querySelector("meta[name=dd-trace-time]");return c(e&&e.content,n&&n.content)}(t)||function(t){const e=function(t){for(let e=0;e<t.childNodes.length;e+=1){const n=u(t.childNodes[e]);if(n)return n}if(t.body)for(let e=t.body.childNodes.length-1;e>=0;e-=1){const n=t.body.childNodes[e],i=u(n);if(i)return i;if(!(0,r.ir)(n))break}}(t);if(e)return c((0,o.rx)(e,"trace-id"),(0,o.rx)(e,"trace-time"))}(t);if(e&&!(e.traceTime<=(0,i.x3)()-s))return e.traceId}function c(t,e){const n=e&&Number(e);if(t&&n)return{traceId:t,traceTime:n}}function u(t){if(t&&(0,r.QU)(t)){const e=/^\s*DATADOG;(.*?)\s*$/.exec(t.data);if(e)return e[1]}}},52451:(t,e,n)=>{n.d(e,{Yw:()=>u,uv:()=>l});var i=n(87363),o=n(55407),r=n(52999),s=n(92555),a=n(59248);const c="rum";function u(t,e,n){const o=(0,i.ox)(t,c,(e=>function(t,e){let n;return n=function(t){return"0"===t||"1"===t||"2"===t}(e)?e:(0,a.ic)(t.sessionSampleRate)?(0,a.ic)(t.sessionReplaySampleRate)?"1":"2":"0",{trackingType:n,isTracked:d(n)}}(t,e)),n);return o.expireObservable.subscribe((()=>{e.notify(9)})),o.renewObservable.subscribe((()=>{e.notify(10)})),o.sessionStateUpdateObservable.subscribe((({previousState:t,newState:e})=>{if(!t.forcedReplay&&e.forcedReplay){const t=o.findSession();t&&(t.isReplayForced=!0)}})),{findTrackedSession:t=>{const e=o.findSession(t);if(e&&d(e.trackingType))return{id:e.id,sessionReplay:"1"===e.trackingType?1:e.isReplayForced?2:0,anonymousId:e.anonymousId}},expire:o.expire,expireObservable:o.expireObservable,setForcedReplay:()=>o.updateSessionState({forcedReplay:"1"})}}function l(){const t={id:"00000000-aaaa-0000-aaaa-000000000000",sessionReplay:(0,o.Ww)("records")?1:0};return{findTrackedSession:()=>t,expire:r.l,expireObservable:new s.c,setForcedReplay:r.l}}function d(t){return"2"===t||"1"===t}},59371:(t,e,n)=>{n.d(e,{O:()=>o});var i=n(52999);function o({lifeCycle:t,isChildEvent:e,onChange:n=i.l}){const o={errorCount:0,longTaskCount:0,resourceCount:0,actionCount:0,frustrationCount:0},r=t.subscribe(13,(t=>{var i;if("view"!==t.type&&"vital"!==t.type&&e(t))switch(t.type){case"error":o.errorCount+=1,n();break;case"action":o.actionCount+=1,t.action.frustration&&(o.frustrationCount+=t.action.frustration.type.length),n();break;case"long_task":o.longTaskCount+=1,n();break;case"resource":(null===(i=t._dd)||void 0===i?void 0:i.discarded)||(o.resourceCount+=1,n())}}));return{stop:()=>{r.unsubscribe()},eventCounts:o}}},66996:(t,e,n)=>{n.d(e,{c:()=>o});var i=n(55407);function o(t){const e=(0,i.Y9)();t.subscribe(13,(t=>{e.send("rum",t)}))}},80967:(t,e,n)=>{n.d(e,{WS:()=>d,a1:()=>p});var i=n(31583),o=n(74036),r=n(78218),s=n(19642),a=n(69986),c=n(91198),u=n(59248);let l;function d(t){const e=t;return"object"===(0,i.P)(e)&&(0,o.V)(e.match)&&Array.isArray(e.propagatorTypes)}function m(t){0!==t.status||t.isAborted||(t.traceId=void 0,t.spanId=void 0,t.traceSampled=void 0)}function p(t,e){return{clearTracingIfNeeded:m,traceFetch:n=>v(t,n,e,(t=>{var e;if(n.input instanceof Request&&!(null===(e=n.init)||void 0===e?void 0:e.headers))n.input=new Request(n.input),Object.keys(t).forEach((e=>{n.input.headers.append(e,t[e])}));else{n.init=(0,r.yG)(n.init);const e=[];n.init.headers instanceof Headers?n.init.headers.forEach(((t,n)=>{e.push([n,t])})):Array.isArray(n.init.headers)?n.init.headers.forEach((t=>{e.push(t)})):n.init.headers&&Object.keys(n.init.headers).forEach((t=>{e.push([t,n.init.headers[t]])})),n.init.headers=e.concat((0,s.WP)(t))}})),traceXhr:(n,i)=>v(t,n,e,(t=>{Object.keys(t).forEach((e=>{i.setRequestHeader(e,t[e])}))}))}}function v(t,e,n,i){const r=n.findTrackedSession();if(!r)return;const s=t.allowedTracingUrls.find((t=>(0,o.K)([t.match],e.url,!0)));if(!s)return;const d=function(t,e){if(100===e)return!0;if(0===e)return!1;if(l&&t===l.sessionId)return l.decision;let n;return n=window.BigInt?function(t,e){const n=BigInt("1111111111111111111"),i=BigInt("0x10000000000000000");return Number(t*n%i)<=e/100*Number(i)}(BigInt(`0x${t.split("-")[4]}`),e):(0,u.ic)(e),l={sessionId:t,decision:n},n}(r.id,t.traceSampleRate);(d||t.traceContextInjection===a.uT.ALL)&&(e.traceSampled=d,e.traceId=(0,c.N8)(),e.spanId=(0,c.ul)(),i(function(t,e,n,i){const o={};return i.forEach((i=>{switch(i){case"datadog":Object.assign(o,{"x-datadog-origin":"rum","x-datadog-parent-id":e.toString(),"x-datadog-sampling-priority":n?"1":"0","x-datadog-trace-id":t.toString()});break;case"tracecontext":Object.assign(o,{traceparent:`00-0000000000000000${(0,c.Qp)(t)}-${(0,c.Qp)(e)}-0${n?"1":"0"}`,tracestate:`dd=s:${n?"1":"0"};o:rum`});break;case"b3":Object.assign(o,{b3:`${(0,c.Qp)(t)}-${(0,c.Qp)(e)}-${n?"1":"0"}`});break;case"b3multi":Object.assign(o,{"X-B3-TraceId":(0,c.Qp)(t),"X-B3-SpanId":(0,c.Qp)(e),"X-B3-Sampled":n?"1":"0"})}})),o}(e.traceId,e.spanId,e.traceSampled,s.propagatorTypes)))}},82224:(t,e,n)=>{n.d(e,{kn:()=>u,t$:()=>d});var i=n(93001),o=n(42182),r=n(29336),s=n(92555),a=n(74036),c=n(5964);const u=100,l=100;function d(t,e,n,a,d,p){const v=function(t,e,n,i){return new s.c((o=>{const r=[];let s,a=0;return r.push(e.subscribe(u),n.subscribe(u),(0,c.Ks)(i,{type:c.do.RESOURCE}).subscribe((t=>{t.some((t=>!m(i,t.name)))&&u()})),t.subscribe(7,(t=>{m(i,t.url)||(void 0===s&&(s=t.requestIndex),a+=1,u())})),t.subscribe(8,(t=>{m(i,t.url)||void 0===s||t.requestIndex<s||(a-=1,u())}))),()=>{r.forEach((t=>t.unsubscribe()))};function u(){o.notify({isBusy:a>0})}}))}(t,e,n,a);return function(t,e,n){let s,a=!1;const c=(0,i.wg)((0,o.dm)((()=>v({hadActivity:!1}))),u),d=void 0!==n?(0,i.wg)((0,o.dm)((()=>v({hadActivity:!0,end:(0,r.nx)()}))),n):void 0,m=t.subscribe((({isBusy:t})=>{(0,i.DJ)(c),(0,i.DJ)(s);const e=(0,r.nx)();t||(s=(0,i.wg)((0,o.dm)((()=>v({hadActivity:!0,end:e}))),l))})),p=()=>{a=!0,(0,i.DJ)(c),(0,i.DJ)(s),(0,i.DJ)(d),m.unsubscribe()};function v(t){a||(p(),e(t))}return{stop:p}}(v,d,p)}function m(t,e){return(0,a.K)(t.excludedActivityUrls,e)}},84411:(t,e,n)=>{n.d(e,{$4:()=>u.$4,$m:()=>m.$m,AB:()=>i.A,Gn:()=>a.G,Ie:()=>m.Ie,NT:()=>m.NT,PJ:()=>m.PJ,W3:()=>r.W,Wd:()=>m.Wd,XS:()=>u.XS,YR:()=>d.YR,dT:()=>m.dT,dx:()=>l.d,eT:()=>m.eT,g1:()=>s.g1,jK:()=>d.jK,jR:()=>m.jR,o:()=>m.o,pB:()=>s.pB,p_:()=>u.p_,rJ:()=>o.r,rf:()=>m.rf,wI:()=>u.wI,wR:()=>u.wR,yF:()=>c.yF,zL:()=>a.z});var i=n(75060),o=n(87946),r=n(57522),s=n(77705),a=n(85107),c=(n(27328),n(48457)),u=n(39042),l=n(18368),d=n(51260),m=n(2472)},90888:(t,e,n)=>{n.d(e,{s:()=>z});var i=n(29336),o=n(78218),r=n(33425),s=n(39377),a=n(1356),c=n(92555),u=n(90842),l=n(52999),d=n(93001),m=n(32234),p=n(59371),v=n(5964);const f=10*i.iW;var g=n(39042),b=n(48457),y=n(71511),h=n(99769);var x=n(80886),w=n(19642);const C=10*i.iW;function T(t,e=window){let n,i;return"hidden"===document.visibilityState?n=0:(n=1/0,({stop:i}=(0,x.l)(t,e,["pagehide","visibilitychange"],(t=>{"pagehide"!==t.type&&"hidden"!==document.visibilityState||(n=t.timeStamp,i())}),{capture:!0}))),{get timeStamp(){return n},stop(){null==i||i()}}}function _(t,e,n){const o={},{stop:r}=function(t,e,n=h.z){return function(t,e){let n;const{stop:i}=(0,y.H)(t,"complete",(()=>{n=(0,d.wg)((()=>e()))}));return{stop:()=>{i(),(0,d.DJ)(n)}}}(t,(()=>{const t=n();(function(t){return t.loadEventEnd<=0})(t)||e(function(t){return{domComplete:t.domComplete,domContentLoaded:t.domContentLoadedEventEnd,domInteractive:t.domInteractive,loadEvent:t.loadEventEnd,firstByte:t.responseStart>=0&&t.responseStart<=(0,i.$S)()?t.responseStart:void 0}}(t))}))}(t,(t=>{e(t.loadEvent),o.navigationTimings=t,n()})),s=T(t),{stop:a}=function(t,e){return{stop:(0,v.Ks)(t,{type:v.do.PAINT,buffered:!0}).subscribe((t=>{const i=t.find((t=>"first-contentful-paint"===t.name&&t.startTime<e.timeStamp&&t.startTime<f));var r;i&&(r=i.startTime,o.firstContentfulPaint=r,n())})).unsubscribe}}(t,s),{stop:c}=function(t,e,i){let r=1/0;const{stop:s}=(0,x.l)(t,i,["pointerdown","keydown"],(t=>{r=t.timeStamp}),{capture:!0,once:!0});let a=0;const c=(0,v.Ks)(t,{type:v.do.LARGEST_CONTENTFUL_PAINT,buffered:!0}).subscribe((i=>{const s=(0,w.Uk)(i,(t=>t.entryType===v.do.LARGEST_CONTENTFUL_PAINT&&t.startTime<r&&t.startTime<e.timeStamp&&t.startTime<C&&t.size>a));if(s){let e;s.element&&(e=(0,b.ab)(s.element,t.actionNameAttribute)),c={value:s.startTime,targetSelector:e,resourceUrl:(u=s,""===u.url?void 0:u.url)},o.largestContentfulPaint=c,n(),a=s.size}var c,u}));return{stop:()=>{s(),c.unsubscribe()}}}(t,s,window),{stop:u}=function(t,e){const r=(0,v.Ks)(t,{type:v.do.FIRST_INPUT,buffered:!0}).subscribe((r=>{const s=r.find((t=>t.startTime<e.timeStamp));if(s){const e=(0,i.vk)(s.startTime,s.processingStart);let r;s.target&&(0,g.nS)(s.target)&&(r=(0,b.ab)(s.target,t.actionNameAttribute)),a={delay:e>=0?e:0,time:s.startTime,targetSelector:r},o.firstInput=a,n()}var a}));return{stop:()=>{r.unsubscribe()}}}(t,s);return{stop:function(){r(),a(),c(),u(),s.stop()},initialViewMetrics:o}}var S=n(59248);function I({x:t,y:e,width:n,height:i}){return{x:t,y:e,width:n,height:i}}const M=5*i.OY,k=i.OY;var V=n(27410),R=n(42182);let E,N=0,B=1/0,P=0;const O=()=>E?N:window.performance.interactionCount||0,j=10,A=1*i.iW;var L=n(82224),Z=n(85107),D=n(77705);const W=i.OY;function $(t,e,n,o,r,s,a){const u={},{stop:d,setLoadEvent:m}=function(t,e,n,o,s,a){let c="initial_load"===s,l=!0;const d=[],m=T(o);function p(){if(!l&&!c&&d.length>0){const e=Math.max(...d);e<m.timeStamp&&(t=e,u.loadingTime=t,r())}var t}const{stop:v}=(0,L.t$)(t,e,n,o,(t=>{l&&(l=!1,t.hadActivity&&d.push((0,i.vk)(a.timeStamp,t.end)),p())}));return{stop:()=>{v(),m.stop()},setLoadEvent:t=>{c&&(c=!1,d.push(t),p())}}}(t,e,n,o,s,a),{stop:p}=function(t,e,n,o=function(t,e=W){return new c.c((n=>{if(window.ResizeObserver){const i=(0,l.n)((function(){n.notify(function(){const t=(0,Z.z)(),{height:e}=(0,D.pB)();return{scrollHeight:Math.round((document.scrollingElement||document.documentElement).scrollHeight),scrollDepth:Math.round(e+t),scrollTop:t}}())}),e,{leading:!1,trailing:!0}),o=document.scrollingElement||document.documentElement,r=new ResizeObserver((0,R.dm)(i.throttled));o&&r.observe(o);const s=(0,x.q)(t,window,"scroll",i.throttled,{passive:!0});return()=>{i.cancel(),r.disconnect(),s.stop()}}}))}(t)){let r=0,s=0,a=0;const u=o.subscribe((({scrollDepth:t,scrollTop:o,scrollHeight:c})=>{let u=!1;if(t>r&&(r=t,u=!0),c>s){s=c;const t=(0,i.$S)();a=(0,i.vk)(e.relative,t),u=!0}u&&n({maxDepth:Math.min(r,s),maxDepthScrollTop:o,maxScrollHeight:s,maxScrollHeightTime:a})}));return{stop:()=>u.unsubscribe()}}(o,a,(t=>{u.scroll=t})),{stop:f}=function(t,e,n){if(!(0,v.s5)(v.do.LAYOUT_SHIFT)||!("WeakRef"in window))return{stop:l.l};let o,r=0;n({value:0});const s=function(){let t,e,n=0,i=0;return{update:o=>{let r;return void 0===t||o.startTime-e>=k||o.startTime-t>=M?(t=e=o.startTime,i=n=o.value,r=!0):(n+=o.value,e=o.startTime,r=o.value>i,r&&(i=o.value)),{cumulatedValue:n,isMaxValue:r}}}}(),a=(0,v.Ks)(t,{type:v.do.LAYOUT_SHIFT,buffered:!0}).subscribe((a=>{var c;for(const u of a){if(u.hadRecentInput||u.startTime<e)continue;const{cumulatedValue:a,isMaxValue:l}=s.update(u);if(l){const t=u.sources.find((t=>!!t.node&&(0,g.nS)(t.node)));o={target:(null==t?void 0:t.node)?new WeakRef(t.node):void 0,time:(0,i.vk)(e,u.startTime),previousRect:null==t?void 0:t.previousRect,currentRect:null==t?void 0:t.currentRect,devicePixelRatio:window.devicePixelRatio}}if(a>r){r=a;const e=null===(c=null==o?void 0:o.target)||void 0===c?void 0:c.deref();n({value:(0,S.LI)(r,4),targetSelector:e&&(0,b.ab)(e,t.actionNameAttribute),time:null==o?void 0:o.time,previousRect:(null==o?void 0:o.previousRect)?I(o.previousRect):void 0,currentRect:(null==o?void 0:o.currentRect)?I(o.currentRect):void 0,devicePixelRatio:null==o?void 0:o.devicePixelRatio})}}}));return{stop:()=>{a.unsubscribe()}}}(o,a.relative,(t=>{u.cumulativeLayoutShift=t,r()})),{stop:y,getInteractionToNextPaint:h,setViewEnd:w}=function(t,e,n){if(!((0,v.s5)(v.do.EVENT)&&window.PerformanceEventTiming&&"interactionId"in PerformanceEventTiming.prototype))return{getInteractionToNextPaint:()=>{},setViewEnd:l.l,stop:l.l};const{getViewInteractionCount:o,stopViewInteractionCount:r}=function(t){"interactionCount"in performance||E||(E=new window.PerformanceObserver((0,R.dm)((t=>{t.getEntries().forEach((t=>{const e=t;e.interactionId&&(B=Math.min(B,e.interactionId),P=Math.max(P,e.interactionId),N=(P-B)/7+1)}))}))),E.observe({type:"event",buffered:!0,durationThreshold:0}));const e="initial_load"===t?0:O();let n={stopped:!1};function i(){return O()-e}return{getViewInteractionCount:()=>n.stopped?n.interactionCount:i(),stopViewInteractionCount:()=>{n={stopped:!0,interactionCount:i()}}}}(n);let s=1/0;const a=function(t){const e=[];function n(){e.sort(((t,e)=>e.duration-t.duration)).splice(j)}return{process(t){const i=e.findIndex((e=>t.interactionId===e.interactionId)),o=e[e.length-1];-1!==i?t.duration>e[i].duration&&(e[i]=t,n()):(e.length<j||t.duration>o.duration)&&(e.push(t),n())},estimateP98Interaction(){const n=Math.min(e.length-1,Math.floor(t()/50));return e[n]}}}(o);let c,u,d=-1;function m(n){for(const t of n)t.interactionId&&t.startTime>=e&&t.startTime<=s&&a.process(t);const o=a.estimateP98Interaction();o&&o.duration!==d&&(d=o.duration,u=(0,i.vk)(e,o.startTime),c=(0,V._G)(o.startTime),!c&&o.target&&(0,g.nS)(o.target)&&(c=(0,b.ab)(o.target,t.actionNameAttribute)))}const p=(0,v.Ks)(t,{type:v.do.FIRST_INPUT,buffered:!0}).subscribe(m),f=(0,v.Ks)(t,{type:v.do.EVENT,durationThreshold:40,buffered:!0}).subscribe(m);return{getInteractionToNextPaint:()=>d>=0?{value:Math.min(d,A),targetSelector:c,time:u}:o()?{value:0}:void 0,setViewEnd:t=>{s=t,r()},stop:()=>{f.unsubscribe(),p.unsubscribe()}}}(o,a.relative,s);return{stop:()=>{d(),f(),p()},stopINPTracking:y,setLoadEvent:m,setViewEnd:w,getCommonViewMetrics:()=>(u.interactionToNextPaint=h(),u)}}const F=3e3,U=5*i.iW,H=5*i.iW;function K(t){const e=t.indexOf("?");return e<0?t:t.slice(0,e)}function z(t,e,n,v,f,g,b,y,h,x){return t.subscribe(4,(e=>t.notify(12,function(t,e,n){var s,a,c,u,l,d,m,p,v,f,g,b,y,h,x,w,C,T;const _=n.getReplayStats(t.id),S=null===(a=null===(s=t.commonViewMetrics)||void 0===s?void 0:s.cumulativeLayoutShift)||void 0===a?void 0:a.devicePixelRatio,I={_dd:{document_version:t.documentVersion,replay_stats:_,cls:S?{device_pixel_ratio:S}:void 0,configuration:{start_session_replay_recording_manually:e.startSessionReplayRecordingManually}},date:t.startClocks.timeStamp,type:"view",view:{action:{count:t.eventCounts.actionCount},frustration:{count:t.eventCounts.frustrationCount},cumulative_layout_shift:null===(c=t.commonViewMetrics.cumulativeLayoutShift)||void 0===c?void 0:c.value,cumulative_layout_shift_time:(0,i.Zj)(null===(u=t.commonViewMetrics.cumulativeLayoutShift)||void 0===u?void 0:u.time),cumulative_layout_shift_target_selector:null===(l=t.commonViewMetrics.cumulativeLayoutShift)||void 0===l?void 0:l.targetSelector,first_byte:(0,i.Zj)(null===(d=t.initialViewMetrics.navigationTimings)||void 0===d?void 0:d.firstByte),dom_complete:(0,i.Zj)(null===(m=t.initialViewMetrics.navigationTimings)||void 0===m?void 0:m.domComplete),dom_content_loaded:(0,i.Zj)(null===(p=t.initialViewMetrics.navigationTimings)||void 0===p?void 0:p.domContentLoaded),dom_interactive:(0,i.Zj)(null===(v=t.initialViewMetrics.navigationTimings)||void 0===v?void 0:v.domInteractive),error:{count:t.eventCounts.errorCount},first_contentful_paint:(0,i.Zj)(t.initialViewMetrics.firstContentfulPaint),first_input_delay:(0,i.Zj)(null===(f=t.initialViewMetrics.firstInput)||void 0===f?void 0:f.delay),first_input_time:(0,i.Zj)(null===(g=t.initialViewMetrics.firstInput)||void 0===g?void 0:g.time),first_input_target_selector:null===(b=t.initialViewMetrics.firstInput)||void 0===b?void 0:b.targetSelector,interaction_to_next_paint:(0,i.Zj)(null===(y=t.commonViewMetrics.interactionToNextPaint)||void 0===y?void 0:y.value),interaction_to_next_paint_time:(0,i.Zj)(null===(h=t.commonViewMetrics.interactionToNextPaint)||void 0===h?void 0:h.time),interaction_to_next_paint_target_selector:null===(x=t.commonViewMetrics.interactionToNextPaint)||void 0===x?void 0:x.targetSelector,is_active:t.isActive,name:t.name,largest_contentful_paint:(0,i.Zj)(null===(w=t.initialViewMetrics.largestContentfulPaint)||void 0===w?void 0:w.value),largest_contentful_paint_target_selector:null===(C=t.initialViewMetrics.largestContentfulPaint)||void 0===C?void 0:C.targetSelector,load_event:(0,i.Zj)(null===(T=t.initialViewMetrics.navigationTimings)||void 0===T?void 0:T.loadEvent),loading_time:(0,r.K)((0,i.Zj)(t.commonViewMetrics.loadingTime)),loading_type:t.loadingType,long_task:{count:t.eventCounts.longTaskCount},performance:G(t.commonViewMetrics,t.initialViewMetrics),resource:{count:t.eventCounts.resourceCount},time_spent:(0,i.Zj)(t.duration)},display:t.commonViewMetrics.scroll?{scroll:{max_depth:t.commonViewMetrics.scroll.maxDepth,max_depth_scroll_top:t.commonViewMetrics.scroll.maxDepthScrollTop,max_scroll_height:t.commonViewMetrics.scroll.maxScrollHeight,max_scroll_height_time:(0,i.Zj)(t.commonViewMetrics.scroll.maxScrollHeightTime)}}:void 0,session:{has_replay:!!_||void 0,is_active:!!t.sessionIsActive&&void 0},privacy:{replay_level:e.defaultPrivacyLevel}};return(0,o.RI)(t.customTimings)||(I.view.custom_timings=(0,o.LG)(t.customTimings,i.Zj)),{rawRumEvent:I,startTime:t.startClocks.relative,duration:t.duration,domainContext:{location:t.location}}}(e,n,y)))),e.register(0,(({startTime:t,eventType:e})=>{const{service:n,version:i,id:o,name:r,context:s}=h.findView(t);return{type:e,service:n,version:i,context:s,view:{id:o,name:r}}})),function(t,e,n,r,v,f,g,b){const y=new Set;let h,x=w("initial_load",(0,i.Oc)(),b);function w(s,f,g){const b=function(t,e,n,r,s,v,f=(0,i.M8)(),g){const b=(0,a.lk)(),y=new c.c,h={};let x,w=0;const C=(0,o.yG)(s),T=(0,u.D)();let S=!0,I=null==g?void 0:g.name;const M=(null==g?void 0:g.service)||r.service,k=(null==g?void 0:g.version)||r.version,V=null==g?void 0:g.context;V&&T.setContext(V);const R={id:b,name:I,startClocks:f,service:M,version:k,context:V};t.notify(1,R),t.notify(2,R);const{throttled:E,cancel:N}=(0,l.n)(Y,F,{leading:!1}),{setLoadEvent:B,setViewEnd:P,stop:O,stopINPTracking:j,getCommonViewMetrics:A}=$(t,e,n,r,G,v,f),{stop:L,initialViewMetrics:Z}="initial_load"===v?_(r,B,G):{stop:l.l,initialViewMetrics:{}},{stop:D,eventCounts:W}=function(t,e,n){const{stop:i,eventCounts:o}=(0,p.O)({lifeCycle:t,isChildEvent:t=>t.view.id===e,onChange:n});return{stop:i,eventCounts:o}}(t,b,G),K=(0,d.yb)(Y,U);function z(){t.notify(3,{id:b,name:I,context:T.getContext(),startClocks:f})}function G(){z(),E()}function Y(){N(),z(),w+=1;const e=void 0===x?(0,i.nx)():x.timeStamp;t.notify(4,{customTimings:h,documentVersion:w,id:b,name:I,service:M,version:k,context:T.getContext(),loadingType:v,location:C,startClocks:f,commonViewMetrics:A(),initialViewMetrics:Z,duration:(0,i.vk)(f.timeStamp,e),isActive:void 0===x,sessionIsActive:S,eventCounts:W})}return Y(),T.changeObservable.subscribe(G),{get name(){return I},service:M,version:k,contextManager:T,stopObservable:y,end(e={}){var n,o;x||(x=null!==(n=e.endClocks)&&void 0!==n?n:(0,i.M8)(),S=null===(o=e.sessionIsActive)||void 0===o||o,t.notify(5,{endClocks:x}),t.notify(6,{endClocks:x}),(0,d.vG)(K),P(x.relative),O(),Y(),(0,d.wg)((()=>{this.stop()}),H))},stop(){L(),D(),j(),y.notify()},addTiming(t,e){if(x)return;const n=(0,i.pu)(e)?e:(0,i.vk)(f.timeStamp,e);h[function(t){const e=t.replace(/[^a-zA-Z0-9-_.@$]/g,"_");return e!==t&&m.Vy.warn(`Invalid timing name: ${t}, sanitized to: ${e}`),e}(t)]=n,G()},setViewName(t){I=t,Y()}}}(e,n,r,v,t,s,f,g);return y.add(b),b.stopObservable.subscribe((()=>{y.delete(b)})),b}return e.subscribe(10,(()=>{x=w("route_change",void 0,{name:x.name,service:x.service,version:x.version,context:x.contextManager.getContext()})})),e.subscribe(9,(()=>{x.end({sessionIsActive:!1})})),e.subscribe(11,(t=>{t.reason===s.y5.UNLOADING&&x.end()})),g&&(h=function(t){return t.subscribe((({oldLocation:t,newLocation:e})=>{var n,i;i=e,((n=t).pathname!==i.pathname||!function(t){const e=t.substring(1);return""!==e&&!!document.getElementById(e)}(i.hash)&&K(i.hash)!==K(n.hash))&&(x.end(),x=w("route_change"))}))}(f)),{addTiming:(t,e=(0,i.nx)())=>{x.addTiming(t,e)},startView:(t,e)=>{x.end({endClocks:e}),x=w("route_change",e,t)},setViewContext:t=>{x.contextManager.setContext(t)},setViewContextProperty:(t,e)=>{x.contextManager.setContextProperty(t,e)},setViewName:t=>{x.setViewName(t)},getViewContext:()=>x.contextManager.getContext(),stop:()=>{h&&h.unsubscribe(),x.end(),y.forEach((t=>t.stop()))}}}(v,t,f,g,n,b,!n.trackViewsManually,x)}function G({cumulativeLayoutShift:t,interactionToNextPaint:e},{firstContentfulPaint:n,firstInput:o,largestContentfulPaint:r}){return{cls:t&&{score:t.value,timestamp:(0,i.Zj)(t.time),target_selector:t.targetSelector,previous_rect:t.previousRect,current_rect:t.currentRect},fcp:n&&{timestamp:(0,i.Zj)(n)},fid:o&&{duration:(0,i.Zj)(o.delay),timestamp:(0,i.Zj)(o.time),target_selector:o.targetSelector},inp:e&&{duration:(0,i.Zj)(e.value),timestamp:(0,i.Zj)(e.time),target_selector:e.targetSelector},lcp:r&&{timestamp:(0,i.Zj)(r.value),target_selector:r.targetSelector,resource_url:r.resourceUrl}}}},91198:(t,e,n)=>{function i(){return r(64)}function o(){return r(63)}function r(t){const e=crypto.getRandomValues(new Uint32Array(2));return 63===t&&(e[e.length-1]>>>=1),{toString(t=10){let n=e[1],i=e[0],o="";do{const e=n%t*4294967296+i;n=Math.floor(n/t),i=Math.floor(e/t),o=(e%t).toString(t)+o}while(n||i);return o}}}function s(t){return t.toString(16).padStart(16,"0")}n.d(e,{N8:()=>i,Qp:()=>s,ul:()=>o})},98709:(t,e,n)=>{n.d(e,{Y:()=>d});var i=n(29336),o=n(59248),r=n(93001),s=n(7328);const a=10*i.OY;let c,u,l;function d(t,e,n,i,s){e.enabled&&(0,o.ic)(t.customerDataTelemetrySampleRate)&&(f(),g(),n.subscribe(13,(t=>{l=!0,p(u.globalContextBytes,i.getOrCreateTracker(2).getBytesCount()),p(u.userContextBytes,i.getOrCreateTracker(1).getBytesCount()),p(u.featureFlagBytes,["view","error"].includes(t.type)?i.getOrCreateTracker(0).getBytesCount():0)})),s.subscribe((({bytesCount:t,messagesCount:e})=>{l&&(c.batchCount+=1,p(c.batchBytesCount,t),p(c.batchMessagesCount,e),v(c.globalContextBytes,u.globalContextBytes),v(c.userContextBytes,u.userContextBytes),v(c.featureFlagBytes,u.featureFlagBytes),g())})),(0,r.yb)(m,a))}function m(){0!==c.batchCount&&((0,s.A2)("Customer data measures",c),f())}function p(t,e){t.sum+=e,t.min=Math.min(t.min,e),t.max=Math.max(t.max,e)}function v(t,e){t.sum+=e.sum,t.min=Math.min(t.min,e.min),t.max=Math.max(t.max,e.max)}function f(){c={batchCount:0,batchBytesCount:{min:1/0,max:0,sum:0},batchMessagesCount:{min:1/0,max:0,sum:0},globalContextBytes:{min:1/0,max:0,sum:0},userContextBytes:{min:1/0,max:0,sum:0},featureFlagBytes:{min:1/0,max:0,sum:0}}}function g(){l=!1,u={globalContextBytes:{min:1/0,max:0,sum:0},userContextBytes:{min:1/0,max:0,sum:0},featureFlagBytes:{min:1/0,max:0,sum:0}}}}}]);
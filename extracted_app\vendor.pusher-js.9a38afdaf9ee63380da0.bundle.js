/*! For license information please see vendor.pusher-js.9a38afdaf9ee63380da0.bundle.js.LICENSE.txt */
(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2548],{86479:t=>{var e;window,e=function(){return function(t){var e={};function n(i){if(e[i])return e[i].exports;var r=e[i]={i,l:!1,exports:{}};return t[i].call(r.exports,r,r.exports,n),r.l=!0,r.exports}return n.m=t,n.c=e,n.d=function(t,e,i){n.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},n.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},n.t=function(t,e){if(1&e&&(t=n(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var i=Object.create(null);if(n.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var r in t)n.d(i,r,function(e){return t[e]}.bind(null,r));return i},n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,"a",e),e},n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},n.p="",n(n.s=2)}([function(t,e,n){"use strict";var i,r=this&&this.__extends||(i=function(t,e){return i=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])},i(t,e)},function(t,e){function n(){this.constructor=t}i(t,e),t.prototype=null===e?Object.create(e):(n.prototype=e.prototype,new n)});Object.defineProperty(e,"__esModule",{value:!0});var s=256,o=function(){function t(t){void 0===t&&(t="="),this._paddingCharacter=t}return t.prototype.encodedLength=function(t){return this._paddingCharacter?(t+2)/3*4|0:(8*t+5)/6|0},t.prototype.encode=function(t){for(var e="",n=0;n<t.length-2;n+=3){var i=t[n]<<16|t[n+1]<<8|t[n+2];e+=this._encodeByte(i>>>18&63),e+=this._encodeByte(i>>>12&63),e+=this._encodeByte(i>>>6&63),e+=this._encodeByte(i>>>0&63)}var r=t.length-n;return r>0&&(i=t[n]<<16|(2===r?t[n+1]<<8:0),e+=this._encodeByte(i>>>18&63),e+=this._encodeByte(i>>>12&63),e+=2===r?this._encodeByte(i>>>6&63):this._paddingCharacter||"",e+=this._paddingCharacter||""),e},t.prototype.maxDecodedLength=function(t){return this._paddingCharacter?t/4*3|0:(6*t+7)/8|0},t.prototype.decodedLength=function(t){return this.maxDecodedLength(t.length-this._getPaddingLength(t))},t.prototype.decode=function(t){if(0===t.length)return new Uint8Array(0);for(var e=this._getPaddingLength(t),n=t.length-e,i=new Uint8Array(this.maxDecodedLength(n)),r=0,o=0,a=0,c=0,h=0,u=0,l=0;o<n-4;o+=4)c=this._decodeChar(t.charCodeAt(o+0)),h=this._decodeChar(t.charCodeAt(o+1)),u=this._decodeChar(t.charCodeAt(o+2)),l=this._decodeChar(t.charCodeAt(o+3)),i[r++]=c<<2|h>>>4,i[r++]=h<<4|u>>>2,i[r++]=u<<6|l,a|=c&s,a|=h&s,a|=u&s,a|=l&s;if(o<n-1&&(c=this._decodeChar(t.charCodeAt(o)),h=this._decodeChar(t.charCodeAt(o+1)),i[r++]=c<<2|h>>>4,a|=c&s,a|=h&s),o<n-2&&(u=this._decodeChar(t.charCodeAt(o+2)),i[r++]=h<<4|u>>>2,a|=u&s),o<n-3&&(l=this._decodeChar(t.charCodeAt(o+3)),i[r++]=u<<6|l,a|=l&s),0!==a)throw new Error("Base64Coder: incorrect characters for decoding");return i},t.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-15,e+=62-t>>>8&3,String.fromCharCode(e)},t.prototype._decodeChar=function(t){var e=s;return e+=(42-t&t-44)>>>8&-256+t-43+62,e+=(46-t&t-48)>>>8&-256+t-47+63,e+=(47-t&t-58)>>>8&-256+t-48+52,(e+=(64-t&t-91)>>>8&-256+t-65+0)+((96-t&t-123)>>>8&-256+t-97+26)},t.prototype._getPaddingLength=function(t){var e=0;if(this._paddingCharacter){for(var n=t.length-1;n>=0&&t[n]===this._paddingCharacter;n--)e++;if(t.length<4||e>2)throw new Error("Base64Coder: incorrect padding")}return e},t}();e.Coder=o;var a=new o;e.encode=function(t){return a.encode(t)},e.decode=function(t){return a.decode(t)};var c=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return r(e,t),e.prototype._encodeByte=function(t){var e=t;return e+=65,e+=25-t>>>8&6,e+=51-t>>>8&-75,e+=61-t>>>8&-13,e+=62-t>>>8&49,String.fromCharCode(e)},e.prototype._decodeChar=function(t){var e=s;return e+=(44-t&t-46)>>>8&-256+t-45+62,e+=(94-t&t-96)>>>8&-256+t-95+63,e+=(47-t&t-58)>>>8&-256+t-48+52,(e+=(64-t&t-91)>>>8&-256+t-65+0)+((96-t&t-123)>>>8&-256+t-97+26)},e}(o);e.URLSafeCoder=c;var h=new c;e.encodeURLSafe=function(t){return h.encode(t)},e.decodeURLSafe=function(t){return h.decode(t)},e.encodedLength=function(t){return a.encodedLength(t)},e.maxDecodedLength=function(t){return a.maxDecodedLength(t)},e.decodedLength=function(t){return a.decodedLength(t)}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var i="utf8: invalid string",r="utf8: invalid source encoding";function s(t){for(var e=0,n=0;n<t.length;n++){var r=t.charCodeAt(n);if(r<128)e+=1;else if(r<2048)e+=2;else if(r<55296)e+=3;else{if(!(r<=57343))throw new Error(i);if(n>=t.length-1)throw new Error(i);n++,e+=4}}return e}e.encode=function(t){for(var e=new Uint8Array(s(t)),n=0,i=0;i<t.length;i++){var r=t.charCodeAt(i);r<128?e[n++]=r:r<2048?(e[n++]=192|r>>6,e[n++]=128|63&r):r<55296?(e[n++]=224|r>>12,e[n++]=128|r>>6&63,e[n++]=128|63&r):(i++,r=(1023&r)<<10,r|=1023&t.charCodeAt(i),r+=65536,e[n++]=240|r>>18,e[n++]=128|r>>12&63,e[n++]=128|r>>6&63,e[n++]=128|63&r)}return e},e.encodedLength=s,e.decode=function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(128&i){var s=void 0;if(i<224){if(n>=t.length)throw new Error(r);if(128!=(192&(o=t[++n])))throw new Error(r);i=(31&i)<<6|63&o,s=128}else if(i<240){if(n>=t.length-1)throw new Error(r);var o=t[++n],a=t[++n];if(128!=(192&o)||128!=(192&a))throw new Error(r);i=(15&i)<<12|(63&o)<<6|63&a,s=2048}else{if(!(i<248))throw new Error(r);if(n>=t.length-2)throw new Error(r);o=t[++n],a=t[++n];var c=t[++n];if(128!=(192&o)||128!=(192&a)||128!=(192&c))throw new Error(r);i=(15&i)<<18|(63&o)<<12|(63&a)<<6|63&c,s=65536}if(i<s||i>=55296&&i<=57343)throw new Error(r);if(i>=65536){if(i>1114111)throw new Error(r);i-=65536,e.push(String.fromCharCode(55296|i>>10)),i=56320|1023&i}}e.push(String.fromCharCode(i))}return e.join("")}},function(t,e,n){t.exports=n(3).default},function(t,e,n){"use strict";n.r(e);class i{constructor(t,e){this.lastId=0,this.prefix=t,this.name=e}create(t){this.lastId++;var e=this.lastId,n=this.prefix+e,i=this.name+"["+e+"]",r=!1,s=function(){r||(t.apply(null,arguments),r=!0)};return this[e]=s,{number:e,id:n,name:i,callback:s}}remove(t){delete this[t.number]}}var r=new i("_pusher_script_","Pusher.ScriptReceivers"),s="8.3.0",o=7,a=80,c=443,h="",u="sockjs.pusher.com",l=80,d=443,p="/pusher",g="stats.pusher.com",f="/pusher/auth",b="ajax",m=12e4,v=3e4,y=1e4,w={endpoint:"/pusher/user-auth",transport:"ajax"},S={endpoint:"/pusher/auth",transport:"ajax"},_="http://js.pusher.com",k="https://js.pusher.com",C="",T=new i("_pusher_dependencies","Pusher.DependenciesReceivers"),P=new class{constructor(t){this.options=t,this.receivers=t.receivers||r,this.loading={}}load(t,e,n){var i=this;if(i.loading[t]&&i.loading[t].length>0)i.loading[t].push(n);else{i.loading[t]=[n];var r=Ce.createScriptRequest(i.getPath(t,e)),s=i.receivers.create((function(e){if(i.receivers.remove(s),i.loading[t]){var n=i.loading[t];delete i.loading[t];for(var o=function(t){t||r.cleanup()},a=0;a<n.length;a++)n[a](e,o)}}));r.send(s)}}getRoot(t){var e=Ce.getDocument().location.protocol;return(t&&t.useTLS||"https:"===e?this.options.cdn_https:this.options.cdn_http).replace(/\/*$/,"")+"/"+this.options.version}getPath(t,e){return this.getRoot(e)+"/"+t+this.options.suffix+".js"}}({cdn_http:_,cdn_https:k,version:s,suffix:C,receivers:T});const E={baseUrl:"https://pusher.com",urls:{authenticationEndpoint:{path:"/docs/channels/server_api/authenticating_users"},authorizationEndpoint:{path:"/docs/channels/server_api/authorizing-users/"},javascriptQuickStart:{path:"/docs/javascript_quick_start"},triggeringClientEvents:{path:"/docs/client_api_guide/client_events#trigger-events"},encryptedChannelSupport:{fullUrl:"https://github.com/pusher/pusher-js/tree/cc491015371a4bde5743d1c87a0fbac0feb53195#encrypted-channel-support"}}};var x,O=function(t){const e=E.urls[t];if(!e)return"";let n;return e.fullUrl?n=e.fullUrl:e.path&&(n=E.baseUrl+e.path),n?`See: ${n}`:""};!function(t){t.UserAuthentication="user-authentication",t.ChannelAuthorization="channel-authorization"}(x||(x={}));class L extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class A extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class R extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class I extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class D extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class j extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class N extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class U extends Error{constructor(t){super(t),Object.setPrototypeOf(this,new.target.prototype)}}class M extends Error{constructor(t,e){super(e),this.status=t,Object.setPrototypeOf(this,new.target.prototype)}}for(var H=function(t,e,n,i,r){const s=Ce.createXHR();for(var o in s.open("POST",n.endpoint,!0),s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),n.headers)s.setRequestHeader(o,n.headers[o]);if(null!=n.headersProvider){let t=n.headersProvider();for(var o in t)s.setRequestHeader(o,t[o])}return s.onreadystatechange=function(){if(4===s.readyState)if(200===s.status){let t,e=!1;try{t=JSON.parse(s.responseText),e=!0}catch(t){r(new M(200,`JSON returned from ${i.toString()} endpoint was invalid, yet status code was 200. Data was: ${s.responseText}`),null)}e&&r(null,t)}else{let t="";switch(i){case x.UserAuthentication:t=O("authenticationEndpoint");break;case x.ChannelAuthorization:t=`Clients must be authorized to join private or presence channels. ${O("authorizationEndpoint")}`}r(new M(s.status,`Unable to retrieve auth string from ${i.toString()} endpoint - received status: ${s.status} from ${n.endpoint}. ${t}`),null)}},s.send(e),s},z=String.fromCharCode,q="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",B={},$=0;$<64;$++)B[q.charAt($)]=$;var F=function(t){var e=t.charCodeAt(0);return e<128?t:e<2048?z(192|e>>>6)+z(128|63&e):z(224|e>>>12&15)+z(128|e>>>6&63)+z(128|63&e)},X=function(t){var e=[0,2,1][t.length%3],n=t.charCodeAt(0)<<16|(t.length>1?t.charCodeAt(1):0)<<8|(t.length>2?t.charCodeAt(2):0);return[q.charAt(n>>>18),q.charAt(n>>>12&63),e>=2?"=":q.charAt(n>>>6&63),e>=1?"=":q.charAt(63&n)].join("")},J=window.btoa||function(t){return t.replace(/[\s\S]{1,3}/g,X)},W=class{constructor(t,e,n,i){this.clear=e,this.timer=t((()=>{this.timer&&(this.timer=i(this.timer))}),n)}isRunning(){return null!==this.timer}ensureAborted(){this.timer&&(this.clear(this.timer),this.timer=null)}};function G(t){window.clearTimeout(t)}function Y(t){window.clearInterval(t)}class Q extends W{constructor(t,e){super(setTimeout,G,t,(function(t){return e(),null}))}}class V extends W{constructor(t,e){super(setInterval,Y,t,(function(t){return e(),t}))}}var K={now:()=>Date.now?Date.now():(new Date).valueOf(),defer:t=>new Q(0,t),method(t,...e){var n=Array.prototype.slice.call(arguments,1);return function(e){return e[t].apply(e,n.concat(arguments))}}},Z=K;function tt(t,...e){for(var n=0;n<e.length;n++){var i=e[n];for(var r in i)i[r]&&i[r].constructor&&i[r].constructor===Object?t[r]=tt(t[r]||{},i[r]):t[r]=i[r]}return t}function et(){for(var t=["Pusher"],e=0;e<arguments.length;e++)"string"==typeof arguments[e]?t.push(arguments[e]):t.push(lt(arguments[e]));return t.join(" : ")}function nt(t,e){var n=Array.prototype.indexOf;if(null===t)return-1;if(n&&t.indexOf===n)return t.indexOf(e);for(var i=0,r=t.length;i<r;i++)if(t[i]===e)return i;return-1}function it(t,e){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&e(t[n],n,t)}function rt(t){var e=[];return it(t,(function(t,n){e.push(n)})),e}function st(t,e,n){for(var i=0;i<t.length;i++)e.call(n||window,t[i],i,t)}function ot(t,e){for(var n=[],i=0;i<t.length;i++)n.push(e(t[i],i,t,n));return n}function at(t,e){e=e||function(t){return!!t};for(var n=[],i=0;i<t.length;i++)e(t[i],i,t,n)&&n.push(t[i]);return n}function ct(t,e){var n={};return it(t,(function(i,r){(e&&e(i,r,t,n)||Boolean(i))&&(n[r]=i)})),n}function ht(t,e){for(var n=0;n<t.length;n++)if(e(t[n],n,t))return!0;return!1}function ut(t){return e=function(t){return"object"==typeof t&&(t=lt(t)),encodeURIComponent((e=t.toString(),J(e.replace(/[^\x00-\x7F]/g,F))));var e},n={},it(t,(function(t,i){n[i]=e(t)})),n;var e,n}function lt(t){try{return JSON.stringify(t)}catch(i){return JSON.stringify((e=[],n=[],function t(i,r){var s,o,a;switch(typeof i){case"object":if(!i)return null;for(s=0;s<e.length;s+=1)if(e[s]===i)return{$ref:n[s]};if(e.push(i),n.push(r),"[object Array]"===Object.prototype.toString.apply(i))for(a=[],s=0;s<i.length;s+=1)a[s]=t(i[s],r+"["+s+"]");else for(o in a={},i)Object.prototype.hasOwnProperty.call(i,o)&&(a[o]=t(i[o],r+"["+JSON.stringify(o)+"]"));return a;case"number":case"string":case"boolean":return i}}(t,"$")))}var e,n}var dt=new class{constructor(){this.globalLog=t=>{window.console&&window.console.log&&window.console.log(t)}}debug(...t){this.log(this.globalLog,t)}warn(...t){this.log(this.globalLogWarn,t)}error(...t){this.log(this.globalLogError,t)}globalLogWarn(t){window.console&&window.console.warn?window.console.warn(t):this.globalLog(t)}globalLogError(t){window.console&&window.console.error?window.console.error(t):this.globalLogWarn(t)}log(t,...e){var n=et.apply(this,arguments);qe.log?qe.log(n):qe.logToConsole&&t.bind(this)(n)}},pt=function(t,e,n,i,r){void 0===n.headers&&null==n.headersProvider||dt.warn(`To send headers with the ${i.toString()} request, you must use AJAX, rather than JSONP.`);var s=t.nextAuthCallbackID.toString();t.nextAuthCallbackID++;var o=t.getDocument(),a=o.createElement("script");t.auth_callbacks[s]=function(t){r(null,t)};var c="Pusher.auth_callbacks['"+s+"']";a.src=n.endpoint+"?callback="+encodeURIComponent(c)+"&"+e;var h=o.getElementsByTagName("head")[0]||o.documentElement;h.insertBefore(a,h.firstChild)};class gt{constructor(t){this.src=t}send(t){var e=this,n="Error loading "+e.src;e.script=document.createElement("script"),e.script.id=t.id,e.script.src=e.src,e.script.type="text/javascript",e.script.charset="UTF-8",e.script.addEventListener?(e.script.onerror=function(){t.callback(n)},e.script.onload=function(){t.callback(null)}):e.script.onreadystatechange=function(){"loaded"!==e.script.readyState&&"complete"!==e.script.readyState||t.callback(null)},void 0===e.script.async&&document.attachEvent&&/opera/i.test(navigator.userAgent)?(e.errorScript=document.createElement("script"),e.errorScript.id=t.id+"_error",e.errorScript.text=t.name+"('"+n+"');",e.script.async=e.errorScript.async=!1):e.script.async=!0;var i=document.getElementsByTagName("head")[0];i.insertBefore(e.script,i.firstChild),e.errorScript&&i.insertBefore(e.errorScript,e.script.nextSibling)}cleanup(){this.script&&(this.script.onload=this.script.onerror=null,this.script.onreadystatechange=null),this.script&&this.script.parentNode&&this.script.parentNode.removeChild(this.script),this.errorScript&&this.errorScript.parentNode&&this.errorScript.parentNode.removeChild(this.errorScript),this.script=null,this.errorScript=null}}class ft{constructor(t,e){this.url=t,this.data=e}send(t){if(!this.request){var e=(i=this.data,ot((r=ut(ct(i,(function(t){return void 0!==t}))),s=[],it(r,(function(t,e){s.push([e,t])})),s),Z.method("join","=")).join("&")),n=this.url+"/"+t.number+"?"+e;this.request=Ce.createScriptRequest(n),this.request.send(t)}var i,r,s}cleanup(){this.request&&this.request.cleanup()}}var bt={name:"jsonp",getAgent:function(t,e){return function(n,i){var s="http"+(e?"s":"")+"://"+(t.host||t.options.host)+t.options.path,o=Ce.createJSONPRequest(s,n),a=Ce.ScriptReceivers.create((function(e,n){r.remove(a),o.cleanup(),n&&n.host&&(t.host=n.host),i&&i(e,n)}));o.send(a)}}};function mt(t,e,n){return t+(e.useTLS?"s":"")+"://"+(e.useTLS?e.hostTLS:e.hostNonTLS)+n}function vt(t,e){return"/app/"+t+"?protocol="+o+"&client=js&version="+s+(e?"&"+e:"")}var yt={getInitial:function(t,e){return mt("ws",e,(e.httpPath||"")+vt(t,"flash=false"))}},wt={getInitial:function(t,e){return mt("http",e,(e.httpPath||"/pusher")+vt(t))}},St={getInitial:function(t,e){return mt("http",e,e.httpPath||"/pusher")},getPath:function(t,e){return vt(t)}};class _t{constructor(){this._callbacks={}}get(t){return this._callbacks[kt(t)]}add(t,e,n){var i=kt(t);this._callbacks[i]=this._callbacks[i]||[],this._callbacks[i].push({fn:e,context:n})}remove(t,e,n){if(t||e||n){var i=t?[kt(t)]:rt(this._callbacks);e||n?this.removeCallback(i,e,n):this.removeAllCallbacks(i)}else this._callbacks={}}removeCallback(t,e,n){st(t,(function(t){this._callbacks[t]=at(this._callbacks[t]||[],(function(t){return e&&e!==t.fn||n&&n!==t.context})),0===this._callbacks[t].length&&delete this._callbacks[t]}),this)}removeAllCallbacks(t){st(t,(function(t){delete this._callbacks[t]}),this)}}function kt(t){return"_"+t}class Ct{constructor(t){this.callbacks=new _t,this.global_callbacks=[],this.failThrough=t}bind(t,e,n){return this.callbacks.add(t,e,n),this}bind_global(t){return this.global_callbacks.push(t),this}unbind(t,e,n){return this.callbacks.remove(t,e,n),this}unbind_global(t){return t?(this.global_callbacks=at(this.global_callbacks||[],(e=>e!==t)),this):(this.global_callbacks=[],this)}unbind_all(){return this.unbind(),this.unbind_global(),this}emit(t,e,n){for(var i=0;i<this.global_callbacks.length;i++)this.global_callbacks[i](t,e);var r=this.callbacks.get(t),s=[];if(n?s.push(e,n):e&&s.push(e),r&&r.length>0)for(i=0;i<r.length;i++)r[i].fn.apply(r[i].context||window,s);else this.failThrough&&this.failThrough(t,e);return this}}class Tt extends Ct{constructor(t,e,n,i,r){super(),this.initialize=Ce.transportConnectionInitializer,this.hooks=t,this.name=e,this.priority=n,this.key=i,this.options=r,this.state="new",this.timeline=r.timeline,this.activityTimeout=r.activityTimeout,this.id=this.timeline.generateUniqueID()}handlesActivityChecks(){return Boolean(this.hooks.handlesActivityChecks)}supportsPing(){return Boolean(this.hooks.supportsPing)}connect(){if(this.socket||"initialized"!==this.state)return!1;var t=this.hooks.urls.getInitial(this.key,this.options);try{this.socket=this.hooks.getSocket(t,this.options)}catch(t){return Z.defer((()=>{this.onError(t),this.changeState("closed")})),!1}return this.bindListeners(),dt.debug("Connecting",{transport:this.name,url:t}),this.changeState("connecting"),!0}close(){return!!this.socket&&(this.socket.close(),!0)}send(t){return"open"===this.state&&(Z.defer((()=>{this.socket&&this.socket.send(t)})),!0)}ping(){"open"===this.state&&this.supportsPing()&&this.socket.ping()}onOpen(){this.hooks.beforeOpen&&this.hooks.beforeOpen(this.socket,this.hooks.urls.getPath(this.key,this.options)),this.changeState("open"),this.socket.onopen=void 0}onError(t){this.emit("error",{type:"WebSocketError",error:t}),this.timeline.error(this.buildTimelineMessage({error:t.toString()}))}onClose(t){t?this.changeState("closed",{code:t.code,reason:t.reason,wasClean:t.wasClean}):this.changeState("closed"),this.unbindListeners(),this.socket=void 0}onMessage(t){this.emit("message",t)}onActivity(){this.emit("activity")}bindListeners(){this.socket.onopen=()=>{this.onOpen()},this.socket.onerror=t=>{this.onError(t)},this.socket.onclose=t=>{this.onClose(t)},this.socket.onmessage=t=>{this.onMessage(t)},this.supportsPing()&&(this.socket.onactivity=()=>{this.onActivity()})}unbindListeners(){this.socket&&(this.socket.onopen=void 0,this.socket.onerror=void 0,this.socket.onclose=void 0,this.socket.onmessage=void 0,this.supportsPing()&&(this.socket.onactivity=void 0))}changeState(t,e){this.state=t,this.timeline.info(this.buildTimelineMessage({state:t,params:e})),this.emit(t,e)}buildTimelineMessage(t){return tt({cid:this.id},t)}}class Pt{constructor(t){this.hooks=t}isSupported(t){return this.hooks.isSupported(t)}createConnection(t,e,n,i){return new Tt(this.hooks,t,e,n,i)}}var Et=new Pt({urls:yt,handlesActivityChecks:!1,supportsPing:!1,isInitialized:function(){return Boolean(Ce.getWebSocketAPI())},isSupported:function(){return Boolean(Ce.getWebSocketAPI())},getSocket:function(t){return Ce.createWebSocket(t)}}),xt={urls:wt,handlesActivityChecks:!1,supportsPing:!0,isInitialized:function(){return!0}},Ot=tt({getSocket:function(t){return Ce.HTTPFactory.createStreamingSocket(t)}},xt),Lt=tt({getSocket:function(t){return Ce.HTTPFactory.createPollingSocket(t)}},xt),At={isSupported:function(){return Ce.isXHRSupported()}},Rt={ws:Et,xhr_streaming:new Pt(tt({},Ot,At)),xhr_polling:new Pt(tt({},Lt,At))},It=new Pt({file:"sockjs",urls:St,handlesActivityChecks:!0,supportsPing:!1,isSupported:function(){return!0},isInitialized:function(){return void 0!==window.SockJS},getSocket:function(t,e){return new window.SockJS(t,null,{js_path:P.getPath("sockjs",{useTLS:e.useTLS}),ignore_null_origin:e.ignoreNullOrigin})},beforeOpen:function(t,e){t.send(JSON.stringify({path:e}))}}),Dt={isSupported:function(t){return Ce.isXDRSupported(t.useTLS)}},jt=new Pt(tt({},Ot,Dt)),Nt=new Pt(tt({},Lt,Dt));Rt.xdr_streaming=jt,Rt.xdr_polling=Nt,Rt.sockjs=It;var Ut=Rt,Mt=new class extends Ct{constructor(){super();var t=this;void 0!==window.addEventListener&&(window.addEventListener("online",(function(){t.emit("online")}),!1),window.addEventListener("offline",(function(){t.emit("offline")}),!1))}isOnline(){return void 0===window.navigator.onLine||window.navigator.onLine}};class Ht{constructor(t,e,n){this.manager=t,this.transport=e,this.minPingDelay=n.minPingDelay,this.maxPingDelay=n.maxPingDelay,this.pingDelay=void 0}createConnection(t,e,n,i){i=tt({},i,{activityTimeout:this.pingDelay});var r=this.transport.createConnection(t,e,n,i),s=null,o=function(){r.unbind("open",o),r.bind("closed",a),s=Z.now()},a=t=>{if(r.unbind("closed",a),1002===t.code||1003===t.code)this.manager.reportDeath();else if(!t.wasClean&&s){var e=Z.now()-s;e<2*this.maxPingDelay&&(this.manager.reportDeath(),this.pingDelay=Math.max(e/2,this.minPingDelay))}};return r.bind("open",o),r}isSupported(t){return this.manager.isAlive()&&this.transport.isSupported(t)}}const zt={decodeMessage:function(t){try{var e=JSON.parse(t.data),n=e.data;if("string"==typeof n)try{n=JSON.parse(e.data)}catch(t){}var i={event:e.event,channel:e.channel,data:n};return e.user_id&&(i.user_id=e.user_id),i}catch(e){throw{type:"MessageParseError",error:e,data:t.data}}},encodeMessage:function(t){return JSON.stringify(t)},processHandshake:function(t){var e=zt.decodeMessage(t);if("pusher:connection_established"===e.event){if(!e.data.activity_timeout)throw"No activity timeout specified in handshake";return{action:"connected",id:e.data.socket_id,activityTimeout:1e3*e.data.activity_timeout}}if("pusher:error"===e.event)return{action:this.getCloseAction(e.data),error:this.getCloseError(e.data)};throw"Invalid handshake"},getCloseAction:function(t){return t.code<4e3?t.code>=1002&&t.code<=1004?"backoff":null:4e3===t.code?"tls_only":t.code<4100?"refused":t.code<4200?"backoff":t.code<4300?"retry":"refused"},getCloseError:function(t){return 1e3!==t.code&&1001!==t.code?{type:"PusherError",data:{code:t.code,message:t.reason||t.message}}:null}};var qt=zt;class Bt extends Ct{constructor(t,e){super(),this.id=t,this.transport=e,this.activityTimeout=e.activityTimeout,this.bindListeners()}handlesActivityChecks(){return this.transport.handlesActivityChecks()}send(t){return this.transport.send(t)}send_event(t,e,n){var i={event:t,data:e};return n&&(i.channel=n),dt.debug("Event sent",i),this.send(qt.encodeMessage(i))}ping(){this.transport.supportsPing()?this.transport.ping():this.send_event("pusher:ping",{})}close(){this.transport.close()}bindListeners(){var t={message:t=>{var e;try{e=qt.decodeMessage(t)}catch(e){this.emit("error",{type:"MessageParseError",error:e,data:t.data})}if(void 0!==e){switch(dt.debug("Event recd",e),e.event){case"pusher:error":this.emit("error",{type:"PusherError",data:e.data});break;case"pusher:ping":this.emit("ping");break;case"pusher:pong":this.emit("pong")}this.emit("message",e)}},activity:()=>{this.emit("activity")},error:t=>{this.emit("error",t)},closed:t=>{e(),t&&t.code&&this.handleCloseEvent(t),this.transport=null,this.emit("closed")}},e=()=>{it(t,((t,e)=>{this.transport.unbind(e,t)}))};it(t,((t,e)=>{this.transport.bind(e,t)}))}handleCloseEvent(t){var e=qt.getCloseAction(t),n=qt.getCloseError(t);n&&this.emit("error",n),e&&this.emit(e,{action:e,error:n})}}class $t{constructor(t,e){this.transport=t,this.callback=e,this.bindListeners()}close(){this.unbindListeners(),this.transport.close()}bindListeners(){this.onMessage=t=>{var e;this.unbindListeners();try{e=qt.processHandshake(t)}catch(t){return this.finish("error",{error:t}),void this.transport.close()}"connected"===e.action?this.finish("connected",{connection:new Bt(e.id,this.transport),activityTimeout:e.activityTimeout}):(this.finish(e.action,{error:e.error}),this.transport.close())},this.onClosed=t=>{this.unbindListeners();var e=qt.getCloseAction(t)||"backoff",n=qt.getCloseError(t);this.finish(e,{error:n})},this.transport.bind("message",this.onMessage),this.transport.bind("closed",this.onClosed)}unbindListeners(){this.transport.unbind("message",this.onMessage),this.transport.unbind("closed",this.onClosed)}finish(t,e){this.callback(tt({transport:this.transport,action:t},e))}}class Ft{constructor(t,e){this.timeline=t,this.options=e||{}}send(t,e){this.timeline.isEmpty()||this.timeline.send(Ce.TimelineTransport.getAgent(this,t),e)}}class Xt extends Ct{constructor(t,e){super((function(e,n){dt.debug("No callbacks on "+t+" for "+e)})),this.name=t,this.pusher=e,this.subscribed=!1,this.subscriptionPending=!1,this.subscriptionCancelled=!1}authorize(t,e){return e(null,{auth:""})}trigger(t,e){if(0!==t.indexOf("client-"))throw new L("Event '"+t+"' does not start with 'client-'");if(!this.subscribed){var n=O("triggeringClientEvents");dt.warn(`Client event triggered before channel 'subscription_succeeded' event . ${n}`)}return this.pusher.send_event(t,e,this.name)}disconnect(){this.subscribed=!1,this.subscriptionPending=!1}handleEvent(t){var e=t.event,n=t.data;"pusher_internal:subscription_succeeded"===e?this.handleSubscriptionSucceededEvent(t):"pusher_internal:subscription_count"===e?this.handleSubscriptionCountEvent(t):0!==e.indexOf("pusher_internal:")&&this.emit(e,n,{})}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):this.emit("pusher:subscription_succeeded",t.data)}handleSubscriptionCountEvent(t){t.data.subscription_count&&(this.subscriptionCount=t.data.subscription_count),this.emit("pusher:subscription_count",t.data)}subscribe(){this.subscribed||(this.subscriptionPending=!0,this.subscriptionCancelled=!1,this.authorize(this.pusher.connection.socket_id,((t,e)=>{t?(this.subscriptionPending=!1,dt.error(t.toString()),this.emit("pusher:subscription_error",Object.assign({},{type:"AuthError",error:t.message},t instanceof M?{status:t.status}:{}))):this.pusher.send_event("pusher:subscribe",{auth:e.auth,channel_data:e.channel_data,channel:this.name})})))}unsubscribe(){this.subscribed=!1,this.pusher.send_event("pusher:unsubscribe",{channel:this.name})}cancelSubscription(){this.subscriptionCancelled=!0}reinstateSubscription(){this.subscriptionCancelled=!1}}class Jt extends Xt{authorize(t,e){return this.pusher.config.channelAuthorizer({channelName:this.name,socketId:t},e)}}class Wt{constructor(){this.reset()}get(t){return Object.prototype.hasOwnProperty.call(this.members,t)?{id:t,info:this.members[t]}:null}each(t){it(this.members,((e,n)=>{t(this.get(n))}))}setMyID(t){this.myID=t}onSubscription(t){this.members=t.presence.hash,this.count=t.presence.count,this.me=this.get(this.myID)}addMember(t){return null===this.get(t.user_id)&&this.count++,this.members[t.user_id]=t.user_info,this.get(t.user_id)}removeMember(t){var e=this.get(t.user_id);return e&&(delete this.members[t.user_id],this.count--),e}reset(){this.members={},this.count=0,this.myID=null,this.me=null}}class Gt extends Jt{constructor(t,e){super(t,e),this.members=new Wt}authorize(t,e){super.authorize(t,((t,n)=>{return i=this,r=void 0,o=function*(){if(!t)if(null!=n.channel_data){var i=JSON.parse(n.channel_data);this.members.setMyID(i.user_id)}else{if(yield this.pusher.user.signinDonePromise,null==this.pusher.user.user_data){let t=O("authorizationEndpoint");return dt.error(`Invalid auth response for channel '${this.name}', expected 'channel_data' field. ${t}, or the user should be signed in.`),void e("Invalid auth response")}this.members.setMyID(this.pusher.user.user_data.id)}e(t,n)},new((s=void 0)||(s=Promise))((function(t,e){function n(t){try{c(o.next(t))}catch(t){e(t)}}function a(t){try{c(o.throw(t))}catch(t){e(t)}}function c(e){var i;e.done?t(e.value):(i=e.value,i instanceof s?i:new s((function(t){t(i)}))).then(n,a)}c((o=o.apply(i,r||[])).next())}));var i,r,s,o}))}handleEvent(t){var e=t.event;if(0===e.indexOf("pusher_internal:"))this.handleInternalEvent(t);else{var n=t.data,i={};t.user_id&&(i.user_id=t.user_id),this.emit(e,n,i)}}handleInternalEvent(t){var e=t.event,n=t.data;switch(e){case"pusher_internal:subscription_succeeded":this.handleSubscriptionSucceededEvent(t);break;case"pusher_internal:subscription_count":this.handleSubscriptionCountEvent(t);break;case"pusher_internal:member_added":var i=this.members.addMember(n);this.emit("pusher:member_added",i);break;case"pusher_internal:member_removed":var r=this.members.removeMember(n);r&&this.emit("pusher:member_removed",r)}}handleSubscriptionSucceededEvent(t){this.subscriptionPending=!1,this.subscribed=!0,this.subscriptionCancelled?this.pusher.unsubscribe(this.name):(this.members.onSubscription(t.data),this.emit("pusher:subscription_succeeded",this.members))}disconnect(){this.members.reset(),super.disconnect()}}var Yt=n(1),Qt=n(0);class Vt extends Jt{constructor(t,e,n){super(t,e),this.key=null,this.nacl=n}authorize(t,e){super.authorize(t,((t,n)=>{if(t)return void e(t,n);let i=n.shared_secret;i?(this.key=Object(Qt.decode)(i),delete n.shared_secret,e(null,n)):e(new Error(`No shared_secret key in auth payload for encrypted channel: ${this.name}`),null)}))}trigger(t,e){throw new j("Client events are not currently supported for encrypted channels")}handleEvent(t){var e=t.event,n=t.data;0!==e.indexOf("pusher_internal:")&&0!==e.indexOf("pusher:")?this.handleEncryptedEvent(e,n):super.handleEvent(t)}handleEncryptedEvent(t,e){if(!this.key)return void dt.debug("Received encrypted event before key has been retrieved from the authEndpoint");if(!e.ciphertext||!e.nonce)return void dt.error("Unexpected format for encrypted event, expected object with `ciphertext` and `nonce` fields, got: "+e);let n=Object(Qt.decode)(e.ciphertext);if(n.length<this.nacl.secretbox.overheadLength)return void dt.error(`Expected encrypted event ciphertext length to be ${this.nacl.secretbox.overheadLength}, got: ${n.length}`);let i=Object(Qt.decode)(e.nonce);if(i.length<this.nacl.secretbox.nonceLength)return void dt.error(`Expected encrypted event nonce length to be ${this.nacl.secretbox.nonceLength}, got: ${i.length}`);let r=this.nacl.secretbox.open(n,i,this.key);if(null===r)return dt.debug("Failed to decrypt an event, probably because it was encrypted with a different key. Fetching a new key from the authEndpoint..."),void this.authorize(this.pusher.connection.socket_id,((e,s)=>{e?dt.error(`Failed to make a request to the authEndpoint: ${s}. Unable to fetch new key, so dropping encrypted event`):(r=this.nacl.secretbox.open(n,i,this.key),null!==r?this.emit(t,this.getDataToEmit(r)):dt.error("Failed to decrypt event with new key. Dropping encrypted event"))}));this.emit(t,this.getDataToEmit(r))}getDataToEmit(t){let e=Object(Yt.decode)(t);try{return JSON.parse(e)}catch(t){return e}}}class Kt extends Ct{constructor(t,e){super(),this.state="initialized",this.connection=null,this.key=t,this.options=e,this.timeline=this.options.timeline,this.usingTLS=this.options.useTLS,this.errorCallbacks=this.buildErrorCallbacks(),this.connectionCallbacks=this.buildConnectionCallbacks(this.errorCallbacks),this.handshakeCallbacks=this.buildHandshakeCallbacks(this.errorCallbacks);var n=Ce.getNetwork();n.bind("online",(()=>{this.timeline.info({netinfo:"online"}),"connecting"!==this.state&&"unavailable"!==this.state||this.retryIn(0)})),n.bind("offline",(()=>{this.timeline.info({netinfo:"offline"}),this.connection&&this.sendActivityCheck()})),this.updateStrategy()}connect(){this.connection||this.runner||(this.strategy.isSupported()?(this.updateState("connecting"),this.startConnecting(),this.setUnavailableTimer()):this.updateState("failed"))}send(t){return!!this.connection&&this.connection.send(t)}send_event(t,e,n){return!!this.connection&&this.connection.send_event(t,e,n)}disconnect(){this.disconnectInternally(),this.updateState("disconnected")}isUsingTLS(){return this.usingTLS}startConnecting(){var t=(e,n)=>{e?this.runner=this.strategy.connect(0,t):"error"===n.action?(this.emit("error",{type:"HandshakeError",error:n.error}),this.timeline.error({handshakeError:n.error})):(this.abortConnecting(),this.handshakeCallbacks[n.action](n))};this.runner=this.strategy.connect(0,t)}abortConnecting(){this.runner&&(this.runner.abort(),this.runner=null)}disconnectInternally(){this.abortConnecting(),this.clearRetryTimer(),this.clearUnavailableTimer(),this.connection&&this.abandonConnection().close()}updateStrategy(){this.strategy=this.options.getStrategy({key:this.key,timeline:this.timeline,useTLS:this.usingTLS})}retryIn(t){this.timeline.info({action:"retry",delay:t}),t>0&&this.emit("connecting_in",Math.round(t/1e3)),this.retryTimer=new Q(t||0,(()=>{this.disconnectInternally(),this.connect()}))}clearRetryTimer(){this.retryTimer&&(this.retryTimer.ensureAborted(),this.retryTimer=null)}setUnavailableTimer(){this.unavailableTimer=new Q(this.options.unavailableTimeout,(()=>{this.updateState("unavailable")}))}clearUnavailableTimer(){this.unavailableTimer&&this.unavailableTimer.ensureAborted()}sendActivityCheck(){this.stopActivityCheck(),this.connection.ping(),this.activityTimer=new Q(this.options.pongTimeout,(()=>{this.timeline.error({pong_timed_out:this.options.pongTimeout}),this.retryIn(0)}))}resetActivityCheck(){this.stopActivityCheck(),this.connection&&!this.connection.handlesActivityChecks()&&(this.activityTimer=new Q(this.activityTimeout,(()=>{this.sendActivityCheck()})))}stopActivityCheck(){this.activityTimer&&this.activityTimer.ensureAborted()}buildConnectionCallbacks(t){return tt({},t,{message:t=>{this.resetActivityCheck(),this.emit("message",t)},ping:()=>{this.send_event("pusher:pong",{})},activity:()=>{this.resetActivityCheck()},error:t=>{this.emit("error",t)},closed:()=>{this.abandonConnection(),this.shouldRetry()&&this.retryIn(1e3)}})}buildHandshakeCallbacks(t){return tt({},t,{connected:t=>{this.activityTimeout=Math.min(this.options.activityTimeout,t.activityTimeout,t.connection.activityTimeout||1/0),this.clearUnavailableTimer(),this.setConnection(t.connection),this.socket_id=this.connection.id,this.updateState("connected",{socket_id:this.socket_id})}})}buildErrorCallbacks(){let t=t=>e=>{e.error&&this.emit("error",{type:"WebSocketError",error:e.error}),t(e)};return{tls_only:t((()=>{this.usingTLS=!0,this.updateStrategy(),this.retryIn(0)})),refused:t((()=>{this.disconnect()})),backoff:t((()=>{this.retryIn(1e3)})),retry:t((()=>{this.retryIn(0)}))}}setConnection(t){for(var e in this.connection=t,this.connectionCallbacks)this.connection.bind(e,this.connectionCallbacks[e]);this.resetActivityCheck()}abandonConnection(){if(this.connection){for(var t in this.stopActivityCheck(),this.connectionCallbacks)this.connection.unbind(t,this.connectionCallbacks[t]);var e=this.connection;return this.connection=null,e}}updateState(t,e){var n=this.state;if(this.state=t,n!==t){var i=t;"connected"===i&&(i+=" with new socket ID "+e.socket_id),dt.debug("State changed",n+" -> "+i),this.timeline.info({state:t,params:e}),this.emit("state_change",{previous:n,current:t}),this.emit(t,e)}}shouldRetry(){return"connecting"===this.state||"connected"===this.state}}class Zt{constructor(){this.channels={}}add(t,e){return this.channels[t]||(this.channels[t]=function(t,e){if(0===t.indexOf("private-encrypted-")){if(e.config.nacl)return te.createEncryptedChannel(t,e,e.config.nacl);let n="Tried to subscribe to a private-encrypted- channel but no nacl implementation available",i=O("encryptedChannelSupport");throw new j(`${n}. ${i}`)}if(0===t.indexOf("private-"))return te.createPrivateChannel(t,e);if(0===t.indexOf("presence-"))return te.createPresenceChannel(t,e);if(0===t.indexOf("#"))throw new A('Cannot create a channel with name "'+t+'".');return te.createChannel(t,e)}(t,e)),this.channels[t]}all(){return t=this.channels,e=[],it(t,(function(t){e.push(t)})),e;var t,e}find(t){return this.channels[t]}remove(t){var e=this.channels[t];return delete this.channels[t],e}disconnect(){it(this.channels,(function(t){t.disconnect()}))}}var te={createChannels:()=>new Zt,createConnectionManager:(t,e)=>new Kt(t,e),createChannel:(t,e)=>new Xt(t,e),createPrivateChannel:(t,e)=>new Jt(t,e),createPresenceChannel:(t,e)=>new Gt(t,e),createEncryptedChannel:(t,e,n)=>new Vt(t,e,n),createTimelineSender:(t,e)=>new Ft(t,e),createHandshake:(t,e)=>new $t(t,e),createAssistantToTheTransportManager:(t,e,n)=>new Ht(t,e,n)};class ee{constructor(t){this.options=t||{},this.livesLeft=this.options.lives||1/0}getAssistant(t){return te.createAssistantToTheTransportManager(this,t,{minPingDelay:this.options.minPingDelay,maxPingDelay:this.options.maxPingDelay})}isAlive(){return this.livesLeft>0}reportDeath(){this.livesLeft-=1}}class ne{constructor(t,e){this.strategies=t,this.loop=Boolean(e.loop),this.failFast=Boolean(e.failFast),this.timeout=e.timeout,this.timeoutLimit=e.timeoutLimit}isSupported(){return ht(this.strategies,Z.method("isSupported"))}connect(t,e){var n=this.strategies,i=0,r=this.timeout,s=null,o=(a,c)=>{c?e(null,c):(i+=1,this.loop&&(i%=n.length),i<n.length?(r&&(r*=2,this.timeoutLimit&&(r=Math.min(r,this.timeoutLimit))),s=this.tryStrategy(n[i],t,{timeout:r,failFast:this.failFast},o)):e(!0))};return s=this.tryStrategy(n[i],t,{timeout:r,failFast:this.failFast},o),{abort:function(){s.abort()},forceMinPriority:function(e){t=e,s&&s.forceMinPriority(e)}}}tryStrategy(t,e,n,i){var r=null,s=null;return n.timeout>0&&(r=new Q(n.timeout,(function(){s.abort(),i(!0)}))),s=t.connect(e,(function(t,e){t&&r&&r.isRunning()&&!n.failFast||(r&&r.ensureAborted(),i(t,e))})),{abort:function(){r&&r.ensureAborted(),s.abort()},forceMinPriority:function(t){s.forceMinPriority(t)}}}}class ie{constructor(t){this.strategies=t}isSupported(){return ht(this.strategies,Z.method("isSupported"))}connect(t,e){return function(t,n){var i=ot(t,(function(t,i,r,s){return t.connect(n,function(t,n){return function(i,r){n[t].error=i,i?function(t){return function(t,e){for(var n=0;n<t.length;n++)if(!e(t[n],n,t))return!1;return!0}(t,(function(t){return Boolean(t.error)}))}(n)&&e(!0):(st(n,(function(t){t.forceMinPriority(r.transport.priority)})),e(null,r))}}(i,s))}));return{abort:function(){st(i,re)},forceMinPriority:function(t){st(i,(function(e){e.forceMinPriority(t)}))}}}(this.strategies,t)}}function re(t){t.error||t.aborted||(t.abort(),t.aborted=!0)}class se{constructor(t,e,n){this.strategy=t,this.transports=e,this.ttl=n.ttl||18e5,this.usingTLS=n.useTLS,this.timeline=n.timeline}isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.usingTLS,i=function(t){var e=Ce.getLocalStorage();if(e)try{var n=e[oe(t)];if(n)return JSON.parse(n)}catch(e){ae(t)}return null}(n),r=i&&i.cacheSkipCount?i.cacheSkipCount:0,s=[this.strategy];if(i&&i.timestamp+this.ttl>=Z.now()){var o=this.transports[i.transport];o&&(["ws","wss"].includes(i.transport)||r>3?(this.timeline.info({cached:!0,transport:i.transport,latency:i.latency}),s.push(new ne([o],{timeout:2*i.latency+1e3,failFast:!0}))):r++)}var a=Z.now(),c=s.pop().connect(t,(function i(o,h){o?(ae(n),s.length>0?(a=Z.now(),c=s.pop().connect(t,i)):e(o)):(function(t,e,n,i){var r=Ce.getLocalStorage();if(r)try{r[oe(t)]=lt({timestamp:Z.now(),transport:e,latency:n,cacheSkipCount:i})}catch(t){}}(n,h.transport.name,Z.now()-a,r),e(null,h))}));return{abort:function(){c.abort()},forceMinPriority:function(e){t=e,c&&c.forceMinPriority(e)}}}}function oe(t){return"pusherTransport"+(t?"TLS":"NonTLS")}function ae(t){var e=Ce.getLocalStorage();if(e)try{delete e[oe(t)]}catch(t){}}class ce{constructor(t,{delay:e}){this.strategy=t,this.options={delay:e}}isSupported(){return this.strategy.isSupported()}connect(t,e){var n,i=this.strategy,r=new Q(this.options.delay,(function(){n=i.connect(t,e)}));return{abort:function(){r.ensureAborted(),n&&n.abort()},forceMinPriority:function(e){t=e,n&&n.forceMinPriority(e)}}}}class he{constructor(t,e,n){this.test=t,this.trueBranch=e,this.falseBranch=n}isSupported(){return(this.test()?this.trueBranch:this.falseBranch).isSupported()}connect(t,e){return(this.test()?this.trueBranch:this.falseBranch).connect(t,e)}}class ue{constructor(t){this.strategy=t}isSupported(){return this.strategy.isSupported()}connect(t,e){var n=this.strategy.connect(t,(function(t,i){i&&n.abort(),e(t,i)}));return n}}function le(t){return function(){return t.isSupported()}}var de,pe={getRequest:function(t){var e=new window.XDomainRequest;return e.ontimeout=function(){t.emit("error",new R),t.close()},e.onerror=function(e){t.emit("error",e),t.close()},e.onprogress=function(){e.responseText&&e.responseText.length>0&&t.onChunk(200,e.responseText)},e.onload=function(){e.responseText&&e.responseText.length>0&&t.onChunk(200,e.responseText),t.emit("finished",200),t.close()},e},abortRequest:function(t){t.ontimeout=t.onerror=t.onprogress=t.onload=null,t.abort()}};class ge extends Ct{constructor(t,e,n){super(),this.hooks=t,this.method=e,this.url=n}start(t){this.position=0,this.xhr=this.hooks.getRequest(this),this.unloader=()=>{this.close()},Ce.addUnloadListener(this.unloader),this.xhr.open(this.method,this.url,!0),this.xhr.setRequestHeader&&this.xhr.setRequestHeader("Content-Type","application/json"),this.xhr.send(t)}close(){this.unloader&&(Ce.removeUnloadListener(this.unloader),this.unloader=null),this.xhr&&(this.hooks.abortRequest(this.xhr),this.xhr=null)}onChunk(t,e){for(;;){var n=this.advanceBuffer(e);if(!n)break;this.emit("chunk",{status:t,data:n})}this.isBufferTooLong(e)&&this.emit("buffer_too_long")}advanceBuffer(t){var e=t.slice(this.position),n=e.indexOf("\n");return-1!==n?(this.position+=n+1,e.slice(0,n)):null}isBufferTooLong(t){return this.position===t.length&&t.length>262144}}!function(t){t[t.CONNECTING=0]="CONNECTING",t[t.OPEN=1]="OPEN",t[t.CLOSED=3]="CLOSED"}(de||(de={}));var fe=de,be=1;function me(t){var e=-1===t.indexOf("?")?"?":"&";return t+e+"t="+ +new Date+"&n="+be++}function ve(t){return Ce.randomInt(t)}var ye,we=class{constructor(t,e){this.hooks=t,this.session=ve(1e3)+"/"+function(){for(var t=[],e=0;e<8;e++)t.push(ve(32).toString(32));return t.join("")}(),this.location=function(t){var e=/([^\?]*)\/*(\??.*)/.exec(t);return{base:e[1],queryString:e[2]}}(e),this.readyState=fe.CONNECTING,this.openStream()}send(t){return this.sendRaw(JSON.stringify([t]))}ping(){this.hooks.sendHeartbeat(this)}close(t,e){this.onClose(t,e,!0)}sendRaw(t){if(this.readyState!==fe.OPEN)return!1;try{return Ce.createSocketRequest("POST",me((e=this.location,n=this.session,e.base+"/"+n+"/xhr_send"))).start(t),!0}catch(t){return!1}var e,n}reconnect(){this.closeStream(),this.openStream()}onClose(t,e,n){this.closeStream(),this.readyState=fe.CLOSED,this.onclose&&this.onclose({code:t,reason:e,wasClean:n})}onChunk(t){var e;if(200===t.status)switch(this.readyState===fe.OPEN&&this.onActivity(),t.data.slice(0,1)){case"o":e=JSON.parse(t.data.slice(1)||"{}"),this.onOpen(e);break;case"a":e=JSON.parse(t.data.slice(1)||"[]");for(var n=0;n<e.length;n++)this.onEvent(e[n]);break;case"m":e=JSON.parse(t.data.slice(1)||"null"),this.onEvent(e);break;case"h":this.hooks.onHeartbeat(this);break;case"c":e=JSON.parse(t.data.slice(1)||"[]"),this.onClose(e[0],e[1],!0)}}onOpen(t){var e,n,i;this.readyState===fe.CONNECTING?(t&&t.hostname&&(this.location.base=(e=this.location.base,n=t.hostname,(i=/(https?:\/\/)([^\/:]+)((\/|:)?.*)/.exec(e))[1]+n+i[3])),this.readyState=fe.OPEN,this.onopen&&this.onopen()):this.onClose(1006,"Server lost session",!0)}onEvent(t){this.readyState===fe.OPEN&&this.onmessage&&this.onmessage({data:t})}onActivity(){this.onactivity&&this.onactivity()}onError(t){this.onerror&&this.onerror(t)}openStream(){this.stream=Ce.createSocketRequest("POST",me(this.hooks.getReceiveURL(this.location,this.session))),this.stream.bind("chunk",(t=>{this.onChunk(t)})),this.stream.bind("finished",(t=>{this.hooks.onFinished(this,t)})),this.stream.bind("buffer_too_long",(()=>{this.reconnect()}));try{this.stream.start()}catch(t){Z.defer((()=>{this.onError(t),this.onClose(1006,"Could not start streaming",!1)}))}}closeStream(){this.stream&&(this.stream.unbind_all(),this.stream.close(),this.stream=null)}},Se={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr_streaming"+t.queryString},onHeartbeat:function(t){t.sendRaw("[]")},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){t.onClose(1006,"Connection interrupted ("+e+")",!1)}},_e={getReceiveURL:function(t,e){return t.base+"/"+e+"/xhr"+t.queryString},onHeartbeat:function(){},sendHeartbeat:function(t){t.sendRaw("[]")},onFinished:function(t,e){200===e?t.reconnect():t.onClose(1006,"Connection interrupted ("+e+")",!1)}},ke={getRequest:function(t){var e=new(Ce.getXHRAPI());return e.onreadystatechange=e.onprogress=function(){switch(e.readyState){case 3:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText);break;case 4:e.responseText&&e.responseText.length>0&&t.onChunk(e.status,e.responseText),t.emit("finished",e.status),t.close()}},e},abortRequest:function(t){t.onreadystatechange=null,t.abort()}},Ce={nextAuthCallbackID:1,auth_callbacks:{},ScriptReceivers:r,DependenciesReceivers:T,getDefaultStrategy:function(t,e,n){var i={};function r(e,r,s,o,a){var c=n(t,e,r,s,o,a);return i[e]=c,c}var s,o=Object.assign({},e,{hostNonTLS:t.wsHost+":"+t.wsPort,hostTLS:t.wsHost+":"+t.wssPort,httpPath:t.wsPath}),a=Object.assign({},o,{useTLS:!0}),c=Object.assign({},e,{hostNonTLS:t.httpHost+":"+t.httpPort,hostTLS:t.httpHost+":"+t.httpsPort,httpPath:t.httpPath}),h={loop:!0,timeout:15e3,timeoutLimit:6e4},u=new ee({minPingDelay:1e4,maxPingDelay:t.activityTimeout}),l=new ee({lives:2,minPingDelay:1e4,maxPingDelay:t.activityTimeout}),d=r("ws","ws",3,o,u),p=r("wss","ws",3,a,u),g=r("sockjs","sockjs",1,c),f=r("xhr_streaming","xhr_streaming",1,c,l),b=r("xdr_streaming","xdr_streaming",1,c,l),m=r("xhr_polling","xhr_polling",1,c),v=r("xdr_polling","xdr_polling",1,c),y=new ne([d],h),w=new ne([p],h),S=new ne([g],h),_=new ne([new he(le(f),f,b)],h),k=new ne([new he(le(m),m,v)],h),C=new ne([new he(le(_),new ie([_,new ce(k,{delay:4e3})]),k)],h),T=new he(le(C),C,S);return s=e.useTLS?new ie([y,new ce(T,{delay:2e3})]):new ie([y,new ce(w,{delay:2e3}),new ce(T,{delay:5e3})]),new se(new ue(new he(le(d),s,T)),i,{ttl:18e5,timeline:e.timeline,useTLS:e.useTLS})},Transports:Ut,transportConnectionInitializer:function(){var t=this;t.timeline.info(t.buildTimelineMessage({transport:t.name+(t.options.useTLS?"s":"")})),t.hooks.isInitialized()?t.changeState("initialized"):t.hooks.file?(t.changeState("initializing"),P.load(t.hooks.file,{useTLS:t.options.useTLS},(function(e,n){t.hooks.isInitialized()?(t.changeState("initialized"),n(!0)):(e&&t.onError(e),t.onClose(),n(!1))}))):t.onClose()},HTTPFactory:{createStreamingSocket(t){return this.createSocket(Se,t)},createPollingSocket(t){return this.createSocket(_e,t)},createSocket:(t,e)=>new we(t,e),createXHR(t,e){return this.createRequest(ke,t,e)},createRequest:(t,e,n)=>new ge(t,e,n),createXDR:function(t,e){return this.createRequest(pe,t,e)}},TimelineTransport:bt,getXHRAPI:()=>window.XMLHttpRequest,getWebSocketAPI:()=>window.WebSocket||window.MozWebSocket,setup(t){window.Pusher=t;var e=()=>{this.onDocumentBody(t.ready)};window.JSON?e():P.load("json2",{},e)},getDocument:()=>document,getProtocol(){return this.getDocument().location.protocol},getAuthorizers:()=>({ajax:H,jsonp:pt}),onDocumentBody(t){document.body?t():setTimeout((()=>{this.onDocumentBody(t)}),0)},createJSONPRequest:(t,e)=>new ft(t,e),createScriptRequest:t=>new gt(t),getLocalStorage(){try{return window.localStorage}catch(t){return}},createXHR(){return this.getXHRAPI()?this.createXMLHttpRequest():this.createMicrosoftXHR()},createXMLHttpRequest(){return new(this.getXHRAPI())},createMicrosoftXHR:()=>new ActiveXObject("Microsoft.XMLHTTP"),getNetwork:()=>Mt,createWebSocket(t){return new(this.getWebSocketAPI())(t)},createSocketRequest(t,e){if(this.isXHRSupported())return this.HTTPFactory.createXHR(t,e);if(this.isXDRSupported(0===e.indexOf("https:")))return this.HTTPFactory.createXDR(t,e);throw"Cross-origin HTTP requests are not supported"},isXHRSupported(){var t=this.getXHRAPI();return Boolean(t)&&void 0!==(new t).withCredentials},isXDRSupported(t){var e=t?"https:":"http:",n=this.getProtocol();return Boolean(window.XDomainRequest)&&n===e},addUnloadListener(t){void 0!==window.addEventListener?window.addEventListener("unload",t,!1):void 0!==window.attachEvent&&window.attachEvent("onunload",t)},removeUnloadListener(t){void 0!==window.addEventListener?window.removeEventListener("unload",t,!1):void 0!==window.detachEvent&&window.detachEvent("onunload",t)},randomInt:t=>Math.floor((window.crypto||window.msCrypto).getRandomValues(new Uint32Array(1))[0]/Math.pow(2,32)*t)};!function(t){t[t.ERROR=3]="ERROR",t[t.INFO=6]="INFO",t[t.DEBUG=7]="DEBUG"}(ye||(ye={}));var Te=ye;class Pe{constructor(t,e,n){this.key=t,this.session=e,this.events=[],this.options=n||{},this.sent=0,this.uniqueID=0}log(t,e){t<=this.options.level&&(this.events.push(tt({},e,{timestamp:Z.now()})),this.options.limit&&this.events.length>this.options.limit&&this.events.shift())}error(t){this.log(Te.ERROR,t)}info(t){this.log(Te.INFO,t)}debug(t){this.log(Te.DEBUG,t)}isEmpty(){return 0===this.events.length}send(t,e){var n=tt({session:this.session,bundle:this.sent+1,key:this.key,lib:"js",version:this.options.version,cluster:this.options.cluster,features:this.options.features,timeline:this.events},this.options.params);return this.events=[],t(n,((t,n)=>{t||this.sent++,e&&e(t,n)})),!0}generateUniqueID(){return this.uniqueID++,this.uniqueID}}class Ee{constructor(t,e,n,i){this.name=t,this.priority=e,this.transport=n,this.options=i||{}}isSupported(){return this.transport.isSupported({useTLS:this.options.useTLS})}connect(t,e){if(!this.isSupported())return xe(new U,e);if(this.priority<t)return xe(new I,e);var n=!1,i=this.transport.createConnection(this.name,this.priority,this.options.key,this.options),r=null,s=function(){i.unbind("initialized",s),i.connect()},o=function(){r=te.createHandshake(i,(function(t){n=!0,h(),e(null,t)}))},a=function(t){h(),e(t)},c=function(){var t;h(),t=lt(i),e(new D(t))},h=function(){i.unbind("initialized",s),i.unbind("open",o),i.unbind("error",a),i.unbind("closed",c)};return i.bind("initialized",s),i.bind("open",o),i.bind("error",a),i.bind("closed",c),i.initialize(),{abort:()=>{n||(h(),r?r.close():i.close())},forceMinPriority:t=>{n||this.priority<t&&(r?r.close():i.close())}}}}function xe(t,e){return Z.defer((function(){e(t)})),{abort:function(){},forceMinPriority:function(){}}}const{Transports:Oe}=Ce;var Le=function(t,e,n,i,r,s){var o,a=Oe[n];if(!a)throw new N(n);return t.enabledTransports&&-1===nt(t.enabledTransports,e)||t.disabledTransports&&-1!==nt(t.disabledTransports,e)?o=Ae:(r=Object.assign({ignoreNullOrigin:t.ignoreNullOrigin},r),o=new Ee(e,i,s?s.getAssistant(a):a,r)),o},Ae={isSupported:function(){return!1},connect:function(t,e){var n=Z.defer((function(){e(new U)}));return{abort:function(){n.ensureAborted()},forceMinPriority:function(){}}}};function Re(t){return t.httpHost?t.httpHost:t.cluster?`sockjs-${t.cluster}.pusher.com`:u}function Ie(t){return t.wsHost?t.wsHost:`ws-${t.cluster}.pusher.com`}function De(t){return"https:"===Ce.getProtocol()||!1!==t.forceTLS}function je(t){return"enableStats"in t?t.enableStats:"disableStats"in t&&!t.disableStats}function Ne(t){const e=Object.assign(Object.assign({},w),t.userAuthentication);return"customHandler"in e&&null!=e.customHandler?e.customHandler:(t=>{if(void 0===Ce.getAuthorizers()[t.transport])throw`'${t.transport}' is not a recognized auth transport`;return(e,n)=>{const i=((t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var i in e.params)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(e.params[i]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var i in t)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(t[i])}return n})(e,t);Ce.getAuthorizers()[t.transport](Ce,i,t,x.UserAuthentication,n)}})(e)}function Ue(t,e){const n=function(t,e){let n;return"channelAuthorization"in t?n=Object.assign(Object.assign({},S),t.channelAuthorization):(n={transport:t.authTransport||b,endpoint:t.authEndpoint||f},"auth"in t&&("params"in t.auth&&(n.params=t.auth.params),"headers"in t.auth&&(n.headers=t.auth.headers)),"authorizer"in t&&(n.customHandler=((t,e,n)=>{const i={authTransport:e.transport,authEndpoint:e.endpoint,auth:{params:e.params,headers:e.headers}};return(e,r)=>{const s=t.channel(e.channelName);n(s,i).authorize(e.socketId,r)}})(e,n,t.authorizer))),n}(t,e);return"customHandler"in n&&null!=n.customHandler?n.customHandler:(t=>{if(void 0===Ce.getAuthorizers()[t.transport])throw`'${t.transport}' is not a recognized auth transport`;return(e,n)=>{const i=((t,e)=>{var n="socket_id="+encodeURIComponent(t.socketId);for(var i in n+="&channel_name="+encodeURIComponent(t.channelName),e.params)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(e.params[i]);if(null!=e.paramsProvider){let t=e.paramsProvider();for(var i in t)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(t[i])}return n})(e,t);Ce.getAuthorizers()[t.transport](Ce,i,t,x.ChannelAuthorization,n)}})(n)}class Me extends Ct{constructor(t){super((function(t,e){dt.debug(`No callbacks on watchlist events for ${t}`)})),this.pusher=t,this.bindWatchlistInternalEvent()}handleEvent(t){t.data.events.forEach((t=>{this.emit(t.name,t)}))}bindWatchlistInternalEvent(){this.pusher.connection.bind("message",(t=>{"pusher_internal:watchlist_events"===t.event&&this.handleEvent(t)}))}}class He extends Ct{constructor(t){super((function(t,e){dt.debug("No callbacks on user for "+t)})),this.signin_requested=!1,this.user_data=null,this.serverToUserChannel=null,this.signinDonePromise=null,this._signinDoneResolve=null,this._onAuthorize=(t,e)=>{if(t)return dt.warn(`Error during signin: ${t}`),void this._cleanup();this.pusher.send_event("pusher:signin",{auth:e.auth,user_data:e.user_data})},this.pusher=t,this.pusher.connection.bind("state_change",(({previous:t,current:e})=>{"connected"!==t&&"connected"===e&&this._signin(),"connected"===t&&"connected"!==e&&(this._cleanup(),this._newSigninPromiseIfNeeded())})),this.watchlist=new Me(t),this.pusher.connection.bind("message",(t=>{"pusher:signin_success"===t.event&&this._onSigninSuccess(t.data),this.serverToUserChannel&&this.serverToUserChannel.name===t.channel&&this.serverToUserChannel.handleEvent(t)}))}signin(){this.signin_requested||(this.signin_requested=!0,this._signin())}_signin(){this.signin_requested&&(this._newSigninPromiseIfNeeded(),"connected"===this.pusher.connection.state&&this.pusher.config.userAuthenticator({socketId:this.pusher.connection.socket_id},this._onAuthorize))}_onSigninSuccess(t){try{this.user_data=JSON.parse(t.user_data)}catch(e){return dt.error(`Failed parsing user data after signin: ${t.user_data}`),void this._cleanup()}if("string"!=typeof this.user_data.id||""===this.user_data.id)return dt.error(`user_data doesn't contain an id. user_data: ${this.user_data}`),void this._cleanup();this._signinDoneResolve(),this._subscribeChannels()}_subscribeChannels(){this.serverToUserChannel=new Xt(`#server-to-user-${this.user_data.id}`,this.pusher),this.serverToUserChannel.bind_global(((t,e)=>{0!==t.indexOf("pusher_internal:")&&0!==t.indexOf("pusher:")&&this.emit(t,e)})),(t=>{t.subscriptionPending&&t.subscriptionCancelled?t.reinstateSubscription():t.subscriptionPending||"connected"!==this.pusher.connection.state||t.subscribe()})(this.serverToUserChannel)}_cleanup(){this.user_data=null,this.serverToUserChannel&&(this.serverToUserChannel.unbind_all(),this.serverToUserChannel.disconnect(),this.serverToUserChannel=null),this.signin_requested&&this._signinDoneResolve()}_newSigninPromiseIfNeeded(){if(!this.signin_requested)return;if(this.signinDonePromise&&!this.signinDonePromise.done)return;const{promise:t,resolve:e,reject:n}=function(){let t,e;return{promise:new Promise(((n,i)=>{t=n,e=i})),resolve:t,reject:e}}();t.done=!1;const i=()=>{t.done=!0};t.then(i).catch(i),this.signinDonePromise=t,this._signinDoneResolve=e}}class ze{static ready(){ze.isReady=!0;for(var t=0,e=ze.instances.length;t<e;t++)ze.instances[t].connect()}static getClientFeatures(){return rt(ct({ws:Ce.Transports.ws},(function(t){return t.isSupported({})})))}constructor(t,e){!function(t){if(null==t)throw"You must pass your app key when you instantiate Pusher."}(t),function(t){if(null==t)throw"You must pass an options object";if(null==t.cluster)throw"Options object must provide a cluster";"disableStats"in t&&dt.warn("The disableStats option is deprecated in favor of enableStats")}(e),this.key=t,this.config=function(t,e){let n={activityTimeout:t.activityTimeout||m,cluster:t.cluster,httpPath:t.httpPath||p,httpPort:t.httpPort||l,httpsPort:t.httpsPort||d,pongTimeout:t.pongTimeout||v,statsHost:t.statsHost||g,unavailableTimeout:t.unavailableTimeout||y,wsPath:t.wsPath||h,wsPort:t.wsPort||a,wssPort:t.wssPort||c,enableStats:je(t),httpHost:Re(t),useTLS:De(t),wsHost:Ie(t),userAuthenticator:Ne(t),channelAuthorizer:Ue(t,e)};return"disabledTransports"in t&&(n.disabledTransports=t.disabledTransports),"enabledTransports"in t&&(n.enabledTransports=t.enabledTransports),"ignoreNullOrigin"in t&&(n.ignoreNullOrigin=t.ignoreNullOrigin),"timelineParams"in t&&(n.timelineParams=t.timelineParams),"nacl"in t&&(n.nacl=t.nacl),n}(e,this),this.channels=te.createChannels(),this.global_emitter=new Ct,this.sessionID=Ce.randomInt(1e9),this.timeline=new Pe(this.key,this.sessionID,{cluster:this.config.cluster,features:ze.getClientFeatures(),params:this.config.timelineParams||{},limit:50,level:Te.INFO,version:s}),this.config.enableStats&&(this.timelineSender=te.createTimelineSender(this.timeline,{host:this.config.statsHost,path:"/timeline/v2/"+Ce.TimelineTransport.name})),this.connection=te.createConnectionManager(this.key,{getStrategy:t=>Ce.getDefaultStrategy(this.config,t,Le),timeline:this.timeline,activityTimeout:this.config.activityTimeout,pongTimeout:this.config.pongTimeout,unavailableTimeout:this.config.unavailableTimeout,useTLS:Boolean(this.config.useTLS)}),this.connection.bind("connected",(()=>{this.subscribeAll(),this.timelineSender&&this.timelineSender.send(this.connection.isUsingTLS())})),this.connection.bind("message",(t=>{var e=0===t.event.indexOf("pusher_internal:");if(t.channel){var n=this.channel(t.channel);n&&n.handleEvent(t)}e||this.global_emitter.emit(t.event,t.data)})),this.connection.bind("connecting",(()=>{this.channels.disconnect()})),this.connection.bind("disconnected",(()=>{this.channels.disconnect()})),this.connection.bind("error",(t=>{dt.warn(t)})),ze.instances.push(this),this.timeline.info({instances:ze.instances.length}),this.user=new He(this),ze.isReady&&this.connect()}channel(t){return this.channels.find(t)}allChannels(){return this.channels.all()}connect(){if(this.connection.connect(),this.timelineSender&&!this.timelineSenderTimer){var t=this.connection.isUsingTLS(),e=this.timelineSender;this.timelineSenderTimer=new V(6e4,(function(){e.send(t)}))}}disconnect(){this.connection.disconnect(),this.timelineSenderTimer&&(this.timelineSenderTimer.ensureAborted(),this.timelineSenderTimer=null)}bind(t,e,n){return this.global_emitter.bind(t,e,n),this}unbind(t,e,n){return this.global_emitter.unbind(t,e,n),this}bind_global(t){return this.global_emitter.bind_global(t),this}unbind_global(t){return this.global_emitter.unbind_global(t),this}unbind_all(t){return this.global_emitter.unbind_all(),this}subscribeAll(){var t;for(t in this.channels.channels)this.channels.channels.hasOwnProperty(t)&&this.subscribe(t)}subscribe(t){var e=this.channels.add(t,this);return e.subscriptionPending&&e.subscriptionCancelled?e.reinstateSubscription():e.subscriptionPending||"connected"!==this.connection.state||e.subscribe(),e}unsubscribe(t){var e=this.channels.find(t);e&&e.subscriptionPending?e.cancelSubscription():(e=this.channels.remove(t))&&e.subscribed&&e.unsubscribe()}send_event(t,e,n){return this.connection.send_event(t,e,n)}shouldUseTLS(){return this.config.useTLS}signin(){this.user.signin()}}ze.instances=[],ze.isReady=!1,ze.logToConsole=!1,ze.Runtime=Ce,ze.ScriptReceivers=Ce.ScriptReceivers,ze.DependenciesReceivers=Ce.DependenciesReceivers,ze.auth_callbacks=Ce.auth_callbacks;var qe=e.default=ze;Ce.setup(ze)}])},t.exports=e()}}]);
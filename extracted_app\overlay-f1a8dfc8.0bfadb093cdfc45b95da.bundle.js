"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2313],{"overlay/resources/elements/ad-popup":(e,t,i)=>{i.r(t),i.d(t,{AdPopup:()=>a});var o=i(92465);class a{constructor(){this.showAd=!1}#e;attached(){this.#t(),this.#e=(0,o.yB)(window,"resize",this.#t.bind(this))}detached(){this.#e?.dispose(),this.#e=null}#t(){this.showAd=window.innerWidth*window.devicePixelRatio>1600&&window.innerHeight*window.devicePixelRatio>900}}},"overlay/resources/elements/ad-popup.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});const o='<template> <require from="./house-ad"></require> <house-ad if.bind="showAd"></house-ad> </template> '},"overlay/resources/elements/assistant-window":(e,t,i)=>{i.r(t),i.d(t,{AssistantWindow:()=>g});var o=i(15215),a=i("aurelia-event-aggregator"),n=i("aurelia-framework"),s=i(20770),r=i(62914),d=i(59255),l=i(22920),c=i(64706),w=i(92465),p=i(54995),h=i(6745),b=i(79522);let g=class{#i;#o;#a;#n;#s;constructor(e,t,i,o){this.#o=e,this.#a=t,this.#n=i,this.#s=o}bind(){this.#i=new w.Vd([this.#s.subscribe(d.d,(e=>{this.#o.event(e.name,{client:e.client,...e.params,titleId:this.#o.trainerInfo.title.id,titleName:this.#o.trainerInfo.title.name},r.Io)}))]),this.#r()}unbind(){this.#i?.dispose()}#r(){const e=this.#o.trainerInfo.title,t=(this.assistantHistory[e.id]||[]).map((e=>c.gG.deserialize(e))),i=new c.fz(e.id,t),o={titleId:e.id,titleName:e.name,baseUrl:this.#o.info.env.services.assistant.baseUrl,client:"overlay_native",userProfileImageUrl:this.#o.account?.profileImage,userProfileName:this.#o.account?.username};i.onItemFinalized((({titleId:e,item:t})=>this.#d(e,t))),this.assistant=this.#a.createAssistant(o,i)}async persistItem(e,t){if(t.persist){const i=t.serialize();await this.#n.dispatch(b.WS,e,[i])}}async#d(e,t){await this.persistItem(e,t)}};g=(0,o.Cg)([(0,n.autoinject)(),(0,p.m6)({selectors:{assistantHistory:(0,p.$t)((e=>e.assistantHistory))}}),(0,o.Sn)("design:paramtypes",[h.xr,l.h,s.il,a.EventAggregator])],g)},"overlay/resources/elements/assistant-window.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});const o='<template> <require from="./assistant-window.scss"></require> <require from="../../../shared/assistant/assistant-chat"></require> <assistant-chat assistant.bind="assistant" title-id.bind="titleInfo.id" client="overlay_native"></assistant-chat> </template> '},"overlay/resources/elements/assistant-window.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>p});var o=i(31601),a=i.n(o),n=i(76314),s=i.n(n),r=i(4417),d=i.n(r),l=new URL(i(83959),i.b),c=s()(a()),w=d()(l);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${w}) format("woff2")}.material-symbols-outlined,overlay-window>header .icon,overlay-window>header .actions .action,overlay-window>header .actions .action.opacity{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}overlay-window{pointer-events:none;position:relative;z-index:0;display:flex;flex-direction:column;border-radius:16px;overflow:hidden}overlay-window>header,overlay-window>.content,overlay-window>.background,overlay-window>.resize-handle{pointer-events:auto}overlay-window>header{padding:12px;transition:all .15s cubic-bezier(0.27, 1, 0.36, 1);display:flex;align-items:center;gap:10px;position:relative;z-index:1;filter:drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.33));flex:0 0 auto}overlay-window>header .header-background{position:absolute;left:0;top:0;width:100%;height:100%;z-index:0}overlay-window>header .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;flex:0 0 auto;width:16px;height:16px;display:flex;align-items:center;justify-content:center;color:rgba(255,255,255,.8);pointer-events:none}overlay-window>header .icon img{opacity:.8;width:13px}overlay-window>header .title{font-weight:800;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:#fff;font-size:16px;letter-spacing:.67px;pointer-events:none}overlay-window>header .actions{flex:0 1 auto;position:relative;z-index:2}overlay-window>header .actions,overlay-window>header .actions>*{display:inline-flex;align-items:center}overlay-window>header .actions .action{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;border:none;outline:0;padding:0;background:rgba(0,0,0,0);display:inline-flex;flex:0 0 auto;width:28px;height:28px;align-items:center;justify-content:center;color:rgba(255,255,255,.6);border-radius:6px;transition:color .15s,background-color .15s,opacity .5s,max-width .15s cubic-bezier(0.27, 1, 0.36, 1)}overlay-window>header .actions .action:hover{color:rgba(255,255,255,.8);background-color:rgba(255,255,255,.15)}overlay-window>header .actions .action.toggle-off{opacity:.5}overlay-window>header .actions .action.opacity{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;position:relative;overflow:hidden;display:flex;align-items:center;flex:1 1 auto;width:100%;max-width:28px}overlay-window>header .actions .action.opacity:before{font-family:inherit;content:"opacity"}overlay-window>header .actions .action.opacity:before{width:28px;flex:0 0 auto}overlay-window>header .actions .action.opacity .opacity-slider{flex:1 1 auto;opacity:0;transition:opacity .15s cubic-bezier(0.27, 1, 0.36, 1);padding-right:2px}overlay-window>header .actions .action.opacity .opacity-slider range-input{--range-input--height: 24px}overlay-window>header .actions .action.opacity .opacity-slider range-input input[type=number]{display:none}overlay-window>header .actions .action.opacity:hover{max-width:138px}overlay-window>header .actions .action.opacity:hover .opacity-slider{opacity:1}overlay-window>header .actions .action.pin.pinned{background-color:#fff;color:var(--theme--background)}overlay-window>.background{position:absolute;top:0;right:0;bottom:0;left:0;outline:.5px solid rgba(255,255,255,.25);box-shadow:0px 2px 8px rgba(0,0,0,.33);border-radius:16px;transition:top .15s cubic-bezier(0.27, 1, 0.36, 1);z-index:-1}overlay-window>.background .fill{display:block;width:100%;height:100%;background:var(--theme--background);border-radius:16px}overlay-window>.resize-handle{width:24px;height:24px;position:absolute;right:0;bottom:0;padding:0 4px 4px 0;cursor:se-resize;z-index:2}overlay-window>.resize-handle svg{pointer-events:none}overlay-window>.content{filter:drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.33));flex:1 1 auto;position:relative;z-index:0}overlay-window.overlay-pinned>.resize-handle{display:none}overlay-window.overlay-pinned.pinned>header{transform:translateY(52px);opacity:0;pointer-events:none}overlay-window.overlay-pinned.pinned>.background{top:52px}overlay-window.overlay-pinned.pinned>.content{border-radius:16px;overflow:hidden}overlay-window.custom-geometry{position:fixed}assistant-window{background:var(--theme--background) linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%);display:block;height:calc(100% + 52px);overflow-x:hidden;overflow-y:overlay;container-type:size;padding-top:52px;margin-top:-52px}assistant-window::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}assistant-window::-webkit-scrollbar-thumb:window-inactive,assistant-window::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}assistant-window::-webkit-scrollbar-thumb:window-inactive:hover,assistant-window::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}assistant-window::-webkit-scrollbar-button:single-button:vertical:decrement{height:20px}assistant-window::-webkit-scrollbar-button:single-button:vertical:increment{height:30px}assistant-window::-webkit-scrollbar{background:rgba(0,0,0,0)}assistant-window:not(:hover)::-webkit-scrollbar-thumb{background-color:rgba(0,0,0,0)}assistant-window assistant-chat{--assistant-bg: transparent !important;--assistant-bg-color: #0e1014 !important;display:block;height:100%;width:100%}assistant-window assistant-chat .close-button{display:none !important}`,""]);const p=c}}]);
/*! For license information please see vendor.aurelia-dependency-injection.dea4886f61ef69b312e6.bundle.js.LICENSE.txt */
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1032],{27884:(t,e,n)=>{n.d(e,{$7:()=>d,$t:()=>m,An:()=>T,Ed:()=>K,FS:()=>W,Gr:()=>Q,HH:()=>x,L2:()=>f,P9:()=>b,Q7:()=>R,Qu:()=>l,RZ:()=>w,UI:()=>O,WQ:()=>u,Xx:()=>v,bO:()=>A,dF:()=>g,dY:()=>E,do:()=>M,fQ:()=>S,l9:()=>a,lg:()=>_,lq:()=>j,mc:()=>P,n$:()=>y,qY:()=>c,u8:()=>p,uP:()=>k,vx:()=>H});var r=n(38468),i=n(16566);function o(t,e,n,r){var i,o=arguments.length,s=o<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,n):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,n,r);else for(var a=t.length-1;a>=0;a--)(i=t[a])&&(s=(o<3?i(s):o>3?i(e,n,s):i(e,n))||s);return o>3&&s&&Object.defineProperty(e,n,s),s}function s(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function a(t){var e=function(t){t.hasOwnProperty("inject")||(t.inject=(r.yu.getOwn(r.yu.paramTypes,t)||A).slice(),t.inject&&t.inject.length>0&&t.inject[t.inject.length-1]===Object&&t.inject.splice(-1,1))};return function(t){return!!t}(t)?e(t):e}function u(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];return function(e,n,r){if("number"==typeof r)return a(e),void(1===t.length&&(e.inject[r]=t[0]));r?r.value.inject=t:e.inject=t}}var c,f=r.TB.create("aurelia:resolver",(function(t){return"function"==typeof t.get||"Resolvers must implement: get(container: Container, key: any): any"}));function h(t,e,n){return t===e}!function(t){t[t.instance=0]="instance",t[t.singleton=1]="singleton",t[t.transient=2]="transient",t[t.function=3]="function",t[t.array=4]="array",t[t.alias=5]="alias"}(c||(c={}));var l=function(){function t(t,e){this.strategy=t,this.state=e}return t.prototype.get=function(t,e){if(h(this.strategy,c.instance,this.state))return this.state;if(h(this.strategy,c.singleton,this.state)){var n=t.invoke(this.state);return this.state=n,this.strategy=0,n}if(h(this.strategy,c.transient,this.state))return t.invoke(this.state);if(h(this.strategy,c.function,this.state))return this.state(t,e,this);if(h(this.strategy,c.array,this.state))return this.state[0].get(t,e);if(h(this.strategy,c.alias,this.state))return t.get(this.state);throw new Error("Invalid strategy: "+this.strategy)},o([f(),s("design:paramtypes",[Number,Object])],t)}(),g=function(){function t(t){this._key=t}var e;return e=t,t.prototype.get=function(t){var e=this;return function(){return t.get(e._key)}},t.of=function(t){return new e(t)},e=o([f(),s("design:paramtypes",[Object])],t)}(),p=function(){function t(t){this._key=t}var e;return e=t,t.prototype.get=function(t){return t.getAll(this._key)},t.of=function(t){return new e(t)},e=o([f(),s("design:paramtypes",[Object])],t)}(),v=function(){function t(t,e){void 0===e&&(e=!0),this._key=t,this._checkParent=e}var e;return e=t,t.prototype.get=function(t){return t.hasResolver(this._key,this._checkParent)?t.get(this._key):null},t.of=function(t,n){return void 0===n&&(n=!0),new e(t,n)},e=o([f(),s("design:paramtypes",[Object,Boolean])],t)}(),y=function(){function t(t){this._key=t}var e;return e=t,t.prototype.get=function(t){return t.parent?t.parent.get(this._key):null},t.of=function(t){return new e(t)},e=o([f(),s("design:paramtypes",[Object])],t)}(),d=function(){function t(t){this._key=t}var e;return e=t,t.prototype.get=function(t){var e=this._key,n=t.getResolver(e);return n&&n.strategy===c.function&&(e=n.state),function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return t.invoke(e,n)}},t.of=function(t){return new e(t)},e=o([f(),s("design:paramtypes",[Object])],t)}(),k=function(){function t(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];this.key=t,this.asKey=t,this.dynamicDependencies=e}var e;return e=t,t.prototype.get=function(t){var e=this.dynamicDependencies.length>0?this.dynamicDependencies.map((function(e){return e["protocol:aurelia:resolver"]?e.get(t):t.get(e)})):void 0,n=this.key,r=t.getResolver(n);r&&3===r.strategy&&(n=r.state);var i=t.invoke(n,e);return t.registerInstance(this.asKey,i),i},t.prototype.as=function(t){return this.asKey=t,this},t.of=function(t){for(var n=[],r=1;r<arguments.length;r++)n[r-1]=arguments[r];return new(e.bind.apply(e,[void 0,t].concat(n)))},e=o([f(),s("design:paramtypes",[Object,Object])],t)}();function _(t){return a(t),t.inject}function w(t){return function(e,n,r){_(e)[r]=g.of(t)}}function R(t){return function(e,n,r){_(e)[r]=p.of(t)}}function j(t){void 0===t&&(t=!0);var e;return e="boolean"!=typeof t||t,function(t,n,r){var i=_(t);i[r]=v.of(i[r],e)}}function m(t,e,n){var r=_(t);r[n]=y.of(r[n])}function b(t){return function(e,n,r){_(e)[r]=d.of(t)}}function O(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var r=function(t){return function(n,r,i){var o=_(n);o[i]=k.of.apply(k,[o[i]].concat(e)),t&&o[i].as(t)}};return arguments.length>=1?r(t):r()}function C(t){if(null==t)throw new Error("key/value cannot be null or undefined. Are you trying to inject/register something that doesn't exist with DI?")}var A=Object.freeze([]);r.yu.registration="aurelia:registration",r.yu.invoker="aurelia:invoker";var D=f.decorates,H=function(){function t(t,e,n){this.fn=t,this.invoker=e,this.dependencies=n}return t.prototype.invoke=function(t,e){return void 0!==e?this.invoker.invokeWithDynamicDependencies(t,this.fn,this.dependencies,e):this.invoker.invoke(t,this.fn,this.dependencies)},t}(),I={invoke:function(t,e,n){var r=n.map((function(e){return t.get(e)}));return Reflect.construct(e,r)},invokeWithDynamicDependencies:function(t,e,n,r){for(var i,o=n.length,s=new Array(o);o--;){if(null==(i=n[o]))throw new Error("Constructor Parameter with index "+o+" cannot be null or undefined. Are you trying to inject/register something that doesn't exist with DI?");s[o]=t.get(i)}return void 0!==r&&(s=s.concat(r)),Reflect.construct(e,s)}},P=function(){function t(t){void 0===t&&(t={}),this._configuration=t,this._onHandlerCreated=t.onHandlerCreated,this._handlers=t.handlers||(t.handlers=new Map),this._resolvers=new Map,this.root=this,this.parent=null}return t.prototype.makeGlobal=function(){return t.instance=this,this},t.prototype.setHandlerCreatedCallback=function(t){this._onHandlerCreated=t,this._configuration.onHandlerCreated=t},t.prototype.registerInstance=function(t,e){return this.registerResolver(t,new l(0,void 0===e?t:e))},t.prototype.registerSingleton=function(t,e){return this.registerResolver(t,new l(1,void 0===e?t:e))},t.prototype.registerTransient=function(t,e){return this.registerResolver(t,new l(2,void 0===e?t:e))},t.prototype.registerHandler=function(t,e){return this.registerResolver(t,new l(3,e))},t.prototype.registerAlias=function(t,e){return this.registerResolver(e,new l(5,t))},t.prototype.registerResolver=function(t,e){C(t);var n=this._resolvers,r=n.get(t);return void 0===r?n.set(t,e):4===r.strategy?r.state.push(e):n.set(t,new l(4,[r,e])),e},t.prototype.autoRegister=function(t,e){if("function"==typeof(e=void 0===e?t:e)){var n=r.yu.get(r.yu.registration,e);return void 0===n?this.registerResolver(t,new l(1,e)):n.registerResolver(this,t,e)}return this.registerResolver(t,new l(0,e))},t.prototype.autoRegisterAll=function(t){for(var e=t.length;e--;)this.autoRegister(t[e])},t.prototype.unregister=function(t){this._resolvers.delete(t)},t.prototype.hasResolver=function(t,e){return void 0===e&&(e=!1),C(t),this._resolvers.has(t)||e&&null!==this.parent&&this.parent.hasResolver(t,e)},t.prototype.getResolver=function(t){return this._resolvers.get(t)},t.prototype.get=function(e){if(C(e),e===t)return this;if(D(e))return e.get(this,e);var n=this._resolvers.get(e);if(void 0===n){if(null===this.parent)return this.autoRegister(e).get(this,e);var i=r.yu.get(r.yu.registration,e);return void 0===i?this.parent._get(e):i.registerResolver(this,e,e).get(this,e)}return n.get(this,e)},t.prototype._get=function(t){var e=this._resolvers.get(t);return void 0===e?null===this.parent?this.autoRegister(t).get(this,t):this.parent._get(t):e.get(this,t)},t.prototype.getAll=function(t){C(t);var e=this._resolvers.get(t);if(void 0===e)return null===this.parent?A:this.parent.getAll(t);if(4===e.strategy){for(var n=e.state,r=n.length,i=new Array(r);r--;)i[r]=n[r].get(this,t);return i}return[e.get(this,t)]},t.prototype.createChild=function(){var e=new t(this._configuration);return e.root=this.root,e.parent=this,e},t.prototype.invoke=function(t,e){try{var n=this._handlers.get(t);return void 0===n&&(n=this._createInvocationHandler(t),this._handlers.set(t,n)),n.invoke(this,e)}catch(e){throw new i.Ym("Error invoking "+t.name+". Check the inner error for details.",e,!0)}},t.prototype._createInvocationHandler=function(t){var e,n;if(void 0===t.inject)e=r.yu.getOwn(r.yu.paramTypes,t)||A;else{e=[];for(var i=t;"function"==typeof i;)e.push.apply(e,(n=i).hasOwnProperty("inject")?"function"==typeof n.inject?n.inject():n.inject:[]),i=Object.getPrototypeOf(i)}var o=r.yu.getOwn(r.yu.invoker,t)||I,s=new H(t,o,e);return void 0!==this._onHandlerCreated?this._onHandlerCreated(s):s},t}();function W(t){return function(e){r.yu.define(r.yu.invoker,t,e)}}function x(t){var e=function(t){r.yu.define(r.yu.invoker,E.instance,t)};return t?e(t):e}var E=function(){function t(){}return t.prototype.invoke=function(t,e,n){for(var r=n.length,i=new Array(r);r--;)i[r]=t.get(n[r]);return e.apply(void 0,i)},t.prototype.invokeWithDynamicDependencies=function(t,e,n,r){for(var i=n.length,o=new Array(i);i--;)o[i]=t.get(n[i]);return void 0!==r&&(o=o.concat(r)),e.apply(void 0,o)},t}();function T(t){return function(e){r.yu.define(r.yu.registration,t,e)}}function M(t){return T(new K(t))}function Q(t,e){return void 0===e&&(e=!1),T(new S(t,e))}E.instance=new E;var K=function(){function t(t){this._key=t}return t.prototype.registerResolver=function(t,e,n){var r=t.getResolver(this._key||e);return void 0===r?t.registerTransient(this._key||e,n):r},t}(),S=function(){function t(t,e){void 0===e&&(e=!1),"boolean"==typeof t?this._registerInChild=t:(this._key=t,this._registerInChild=e)}return t.prototype.registerResolver=function(t,e,n){var r=this._registerInChild?t:t.root,i=r.getResolver(this._key||e);return void 0===i?r.registerSingleton(this._key||e,n):i},t}()}}]);
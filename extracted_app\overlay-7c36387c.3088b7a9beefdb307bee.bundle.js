"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8036],{19072:(e,t,r)=>{r.d(t,{S:()=>d,s:()=>h});var s=r("aurelia-event-aggregator"),i=r(96610),n=r(84157),a=r(16953),o=r(49442),c=r(38777);const l=(0,i.getLogger)("host");let p;function d(){return p}class h{constructor(){this.#e=new s.EventAggregator,this.#t=[],this.#r=[]}#e;#s;#t;#r;#i;#n;#a;#o;#c;static async create(){return p||(p=new h,await p.#l()),p}async#l(){const e=await this.#p("ACTION_INITIALIZE_APP");if(this.info=e.app,this.updateState=e.updateState,this.isInTraySinceStartup=e.startedInTray,this.#d(e.window),e.envOverride&&(0,a.h)(e.envOverride),this.#i=a.A.releaseFeed,this.#n=`${a.A.storageNamespace}:squirrelChannel`,this.info.updaterAvailable){const e=localStorage.getItem(this.#n)||this.info.releaseChannel;e&&this.setUpdateChannel(e)}this.#h("EVENT_APP_ACTIVATED"),this.#h("EVENT_CLOSED_TO_TRAY"),this.#h("EVENT_WEBVIEW_WINDOW_LOAD_FINISH"),this.#h("EVENT_WEBVIEW_WINDOW_LOAD_ERROR"),this.#h("EVENT_WEBVIEW_WINDOW_CLOSED"),this.#h("EVENT_OVERLAY_WINDOW_CLOSED"),this.#h("EVENT_OVERLAY_HOTKEY_PRESSED"),this.#h("EVENT_OVERLAY_GRAPHICS_INITIALIZED"),this.#h("EVENT_OVERLAY_GRAPHICS_HOOKED"),this.#h("EVENT_CAPTURE_REPLAY_BUFFER_SAVED"),this.#u("EVENT_APP_QUIT_REQUESTED",(()=>this.#E())),this.#u("EVENT_RESTORED_FROM_TRAY",(()=>this.#I())),this.#u("EVENT_NEW_WINDOW_REQUESTED",(e=>e?this.#_(e):null)),this.#u("EVENT_WINDOW_STATE_CHANGED",(e=>e?this.#d(e):null)),this.#u("EVENT_UPDATE_STATE_CHANGED",(e=>e?this.#T(e):null)),this.#u("EVENT_WINDOW_MESSAGE",(e=>e?this.#A(e):null)),document.addEventListener("keydown",(e=>{e.ctrlKey&&e.shiftKey&&("D"!==e.key&&"d"!==e.key||this.#p("ACTION_OPEN_DEV_TOOLS"),"W"!==e.key&&"w"!==e.key||this.#p("ACTION_DEFAULT_WINDOW"))}))}#h(e){this.#u(e,(t=>this.#e.publish(e,t)))}#u(e,t){n.ipcRenderer.removeAllListeners(e),n.ipcRenderer.addListener(e,((r,s)=>{l.debug(e,s),t(s,r.ports)}))}#d(e){this.minimized=e.minimized,this.maximized=e.maximized,this.visible=e.visible}addNewWindowInterceptor(e){return this.#w(this.#t,e)}setNewWindowErrorHandler(e){return this.#s=e,(0,c.nm)((()=>{this.#s===e&&(this.#s=null)}))}async#_(e){let t=e.uri;for(const r of this.#t){const s=await r(t,e.source);if(!0===s)return;"string"==typeof s&&(t=s)}if(!await this.launchExternal(t)){if(!this.#s)throw new Error(`Failed to launch URL "${t}"`);this.#s(t)}}#A(e){window.postMessage(e.message,e.targetOrigin,e.transfer||void 0)}addAppQuitInterceptor(e){return this.#w(this.#r,e)}async#O(){for(const e of this.#r)if(!0===await e())return!1;return!0}async#E(){await this.#O()&&await this.quit()}#w(e,t){return e.push(t),{dispose:()=>{const r=e.indexOf(t);-1!==r&&e.splice(r,1)}}}async#p(e,...t){l.debug(e,...t);try{const r=await n.ipcRenderer.invoke(e,...t);return void 0!==r&&l.debug(`-> ${e}`,r),r}catch(t){throw l.error(`-> ${e}`,t),t}}getWindowsFolder(){return this.info.env.SYSTEMROOT}getNativeSystemFolder(){return this.info.wow64?`${this.getWindowsFolder()}\\Sysnative`:`${this.getWindowsFolder()}\\System32`}getTempFolder(){const e=this.info.env;let t=e.TEMP||e.TEMP||`${e.SYSTEMROOT}\\Temp`;return t.length>1&&t.endsWith("\\")&&!t.endsWith(":\\")&&(t=t.slice(0,-1)),t}expandEnvironmentStrings(e){return e.replace(/%([^%]+)%/g,((e,t)=>this.info.env[t.toUpperCase()]||`%${t}%`))}getMemoryInfo(){return this.#p("ACTION_GET_MEMORY_INFO")}getInstalledAvProducts(){return this.#p("ACTION_GET_INSTALLED_AV_PRODUCTS")}setInstallationInfo(e,t){return this.#p("ACTION_SET_INSTALLATION_INFO",e,t)}getCreatorConfiguration(){return this.#p("ACTION_GET_CREATOR_CONFIGURATION")}readShortcutLink(e){return this.#p("ACTION_READ_SHORTCUT_LINK",e)}showOpenFileDialog(e){return this.#p("ACTION_SHOW_OPEN_FILE_DIALOG",e)}showSaveFileDialog(e){return this.#p("ACTION_SHOW_SAVE_FILE_DIALOG",e)}launchExternal(e){return this.#p("ACTION_OPEN_EXTERNAL",e)}openFilePath(e){return this.#p("ACTION_OPEN_FILE_PATH",e)}showFileLocation(e){return this.#p("ACTION_SHOW_FILE_LOCATION",e)}setCloseBehavior(e){return this.#p("ACTION_SET_CLOSE_BEHAVIOR",e)}close(){return this.#p("ACTION_CLOSE_WINDOW")}quit(){return this.#p("ACTION_QUIT_APP")}async show(){return this.#p("ACTION_SHOW_WINDOW")}focus(){return this.#p("ACTION_FOCUS_WINDOW")}minimize(){return this.#p("ACTION_MINIMIZE_WINDOW")}maximize(){return this.#p("ACTION_MAXIMIZE_WINDOW")}unmaximize(){return this.#p("ACTION_UNMAXIMIZE_WINDOW")}async reload(){return await this.#p("ACTION_RELOAD_WINDOW"),new Promise(o.Y)}async showToast(e){return this.#p("ACTION_SHOW_TOAST",e)}activate(e,t){this.#e.publish("EVENT_APP_ACTIVATED",{uri:e,source:t})}onActivated(e){return this.#e.subscribe("EVENT_APP_ACTIVATED",e)}onClosedToTray(e){return this.#e.subscribe("EVENT_CLOSED_TO_TRAY",e)}onRestoredFromTray(e){return this.#e.subscribe("EVENT_RESTORED_FROM_TRAY",e)}whenVisible(e){return this.visible?(e(),c.lE):this.#e.subscribeOnce("EVENT_RESTORED_FROM_TRAY",e)}#I(){this.isInTraySinceStartup=!1,this.#e.publish("EVENT_RESTORED_FROM_TRAY")}onWebviewWindowLoadFinish(e){return this.#e.subscribe("EVENT_WEBVIEW_WINDOW_LOAD_FINISH",e)}onWebviewWindowLoadError(e){return this.#e.subscribe("EVENT_WEBVIEW_WINDOW_LOAD_ERROR",e)}onWebviewWindowClosed(e){return this.#e.subscribe("EVENT_WEBVIEW_WINDOW_CLOSED",e)}showWebviewWindow(e){return this.#p("ACTION_SHOW_WEBVIEW_WINDOW",e)}onUpdateStateChanged(e){return this.#e.subscribe("EVENT_UPDATE_STATE_CHANGED",e)}async captureScreenshot(e){return await this.#p("ACTION_CAPTURE_SCREENSHOT",e)}#T(e){"applying"===e&&(this.#o=Date.now(),this.#c=null),"applied"!==e&&"apply-error"!==e||(this.#c=Date.now()),this.updateState=e,this.#e.publish("EVENT_UPDATE_STATE_CHANGED",e)}fakeApplyUpdate(){return this.#p("ACTION_FAKE_APPLY_UPDATE")}fakeApplyUpdateError(){return this.#p("ACTION_FAKE_APPLY_UPDATE_ERROR")}checkForUpdate(){return this.#a?this.#p("ACTION_CHECK_FOR_UPDATE",this.#a):Promise.resolve("not-checked")}applyUpdate(){return this.#a?this.#p("ACTION_APPLY_UPDATE",this.#a):Promise.resolve("not-checked")}async restartForUpdate(e,t=!1){return!(!t&&!await this.#O())&&await this.#p("ACTION_RESTART_APP_FOR_UPDATE",e)}async setUpdateChannel(e){if("string"!=typeof e)return!1;if(e===this.updateChannel)return!0;this.updateChannel=e;const t=new URL(this.#i.replace("{channel}",e));return t.searchParams.set("osVersion",this.info.osVersion),this.#a=t.toString(),localStorage.setItem(this.#n,e),!0}get manualUpdateUrl(){if(!this.updateChannel)return null;const e=new URL(this.#a);return e.pathname+="/releases/latest",e.toString()}copyText(e){return this.#p("ACTION_COPY_TEXT",e)}createDesktopShortcut(e,t,r){return this.#p("ACTION_CREATE_DESKTOP_SHORTCUT",{uri:e,label:t,icon:r})}async getSystemIdleTime(){return await this.#p("ACTION_GET_SYSTEM_IDLE_TIME")}async postAnalyticsEvent(e,t={}){return await this.#p("ACTION_POST_ANALYTICS_EVENT",{name:e,params:t})}#m(){return"win32"!==this.info.osPlatform?null:this.info.osVersion.split(".").map((e=>parseInt(e,10)))}get isWindows10OrGreater(){const e=this.#m();return!!e&&e.length>=1&&e[0]>=10}get isWindows11OrGreater(){const e=this.#m();return!!e&&!(e.length<3)&&(10===e[0]?e[2]>=22e3:e[0]>10)}get isMacOS(){return"darwin"===this.info.osPlatform}get applyUpdateDuration(){return this.#o?((this.#c??Date.now())-this.#o)/1e3:null}async flashWindow(){await this.#p("ACTION_FLASH_WINDOW")}async getDisplayCount(){return await this.#p("ACTION_GET_DISPLAY_COUNT")}async createOverlayWindow(e,t){const r=new MessageChannel;return await this.#p("ACTION_CREATE_OVERLAY_WINDOW",{processId:e,hotkey:t}),n.ipcRenderer.postMessage("ACTION_SET_OVERLAY_PORT",null,[r.port2]),r.port1}async destroyOverlayWindow(){return await this.#p("ACTION_DESTROY_OVERLAY_WINDOW")}async updateOverlayHotkey(e){return this.#p("ACTION_UPDATE_OVERLAY_HOTKEY",e)}onOverlayWindowClosed(e){return this.#e.subscribe("EVENT_OVERLAY_WINDOW_CLOSED",e)}onOverlayHotkeyPressed(e){return this.#e.subscribe("EVENT_OVERLAY_HOTKEY_PRESSED",e)}onOverlayGraphicsInitialized(e){return this.#e.subscribe("EVENT_OVERLAY_GRAPHICS_INITIALIZED",e)}onOverlayGraphicsHooked(e){return this.#e.subscribe("EVENT_OVERLAY_GRAPHICS_HOOKED",e)}onCaptureReplayBufferSaved(e){return this.#e.subscribe("EVENT_CAPTURE_REPLAY_BUFFER_SAVED",e)}async startCapture(e,t){return await this.#p("ACTION_START_CAPTURE",{config:e,processId:t})}async stopCapture(){return await this.#p("ACTION_STOP_CAPTURE")}async startReplayBuffer(){return await this.#p("ACTION_START_CAPTURE_REPLAY_BUFFER")}async saveReplayBuffer(){return await this.#p("ACTION_SAVE_CAPTURE_REPLAY_BUFFER")}async stopReplayBuffer(){return await this.#p("ACTION_STOP_CAPTURE_REPLAY_BUFFER")}}},41882:(e,t,r)=>{r.d(t,{u:()=>s});const s="custom"},62614:(e,t,r)=>{r.d(t,{d_:()=>n});var s=r(96610);r(35392),r(19072),s.logLevel.debug,s.logLevel.info,s.logLevel.warn,s.logLevel.error;const i=[];class n{#g(e,t,...r){t===s.logLevel.debug&&"native"===e.id&&"string"==typeof r[0]&&r[0].startsWith("query-registry")||i.push({logger:e,level:t,message:r[0],args:r.slice(1).map(((e,t)=>{return`\t${t} ${r=e,void 0===r?"undefined":JSON.stringify(r)}`;var r})).join("\r\n")})}debug(e,...t){this.#g(e,s.logLevel.debug,...t)}info(e,...t){this.#g(e,s.logLevel.info,...t)}warn(e,...t){this.#g(e,s.logLevel.warn,...t)}error(e,...t){this.#g(e,s.logLevel.error,...t)}}},64931:(e,t,r)=>{r.d(t,{L:()=>s});class s{constructor(e,t,r,s,i){this.name=e,this.version=t,this.arch=r,this.locale=s,this.publisherId=i,""===s&&(s=null)}toString(){return`${this.name}_${this.version}_${this.arch}_${this.locale||""}_${this.publisherId}`}get pfn(){return`${this.name}_${this.publisherId}`}static parse(e){const t=e.split("_");if(5!==t.length)throw new Error(`Invalid package full name '${e}'.`);return new s(...t)}}},80252:(e,t,r)=>{r.d(t,{k:()=>l});var s=r(15215),i=r(79896),n=r("aurelia-framework"),a=r(20770),o=r(45660),c=r(59239);class l{constructor(e,t){this.gameId=e,this.path=t}static parse(e){const t=e.indexOf("_");if(-1===t)throw new Error(`Invalid custom platform SKU '${e}'.`);return new l(e.substring(0,t),e.substring(t+1))}toString(){return`${this.gameId}_${this.path}`}}let p=class{#f;constructor(e){this.#f=e}async findApps(e){return(await this.getApps()).filter((t=>e.includes(t.sku)))}async getApps(){const e=Object.values(await this.#v()).filter((e=>"custom"===e.platform)),t=[];return await Promise.all(e.map((async e=>{await i.promises.stat(e.location).then((e=>e.isFile())).catch((()=>!1))&&t.push(e)}))),t}getLaunchConfiguration(e,t){const r=l.parse(e).path;return Promise.resolve({command:`"${r}" ${t||""}`.trim(),cwd:r.substring(0,r.lastIndexOf("\\"))})}#v(){return this.#f.state.pipe((0,o.$)(),(0,c.E)("installedApps")).toPromise()}async getIcon(e){return null}};p=(0,s.Cg)([(0,n.autoinject)(),(0,s.Sn)("design:paramtypes",[a.il])],p)},89356:(e,t,r)=>{r.d(t,{Q:()=>h});var s=r(15215),i=r(16928),n=r("aurelia-framework"),a=r(35392),o=r(3972),c=r(29844),l=r(77372),p=r(54700);let d=class{#N;constructor(e){this.#N=e}async read(e){if(null===e||!await a.promises.stat(e).then((e=>e.isFile())).catch((()=>!1)))return null;switch((0,c.LC)(e).toLocaleLowerCase()){case".exe":case".dll":return await this.#C(e);case".p7x":return await this.#y(e);case".zip":case".jar":return await this.#S(e);default:return null}}async readProtectedFileTimes(e){try{const t=await this.#N.statFile(e);return null===t?null:{createdAt:t.creationTime,modifiedAt:t.lastWriteTime}}catch{return null}}async#C(e){try{return(await l.E3.load(e)).ntHeaders.fileHeader.timestamp}catch{return null}}async#y(e){try{return(await this.#N.getFileSignatureInfo(e)).timestamp}catch{return null}}async#S(e){const t=await(0,p.Q)(e);return t?Math.floor(t.getTime()/1e3):null}};d=(0,s.Cg)([(0,n.autoinject)(),(0,s.Sn)("design:paramtypes",[o.Mz])],d);let h=class{#L;constructor(e){this.#L=e}async getGameVersion(e,t,r,s){const n=i.join(e,t),o=await a.promises.stat(n).catch((()=>null));if(null===o)return"uwp"===r?t.toLocaleLowerCase().endsWith(".p7x")?await this.#W(i.join(e,"AppxManifest.xml"),s):await this.#W(n,s):null;const c=Math.floor(o.mtimeMs/1e3),l=Math.floor(o.birthtimeMs/1e3);if(s&&s.modifiedAt===c&&s.createdAt===l)return s;let p=await this.#L.read(n);return null!==p||"uwp"!==r||t.toLocaleLowerCase().endsWith(".p7x")||(p=c),null!==p?{version:p,modifiedAt:c,createdAt:l}:null}async#W(e,t){const r=await this.#L.readProtectedFileTimes(e);if(!r)return null;const{modifiedAt:s,createdAt:i}=r;return s?t?.modifiedAt!==s?{version:s,modifiedAt:s,createdAt:i}:t:null}};h=(0,s.Cg)([(0,n.autoinject)(),(0,s.Sn)("design:paramtypes",[d])],h)},90231:(e,t,r)=>{r.d(t,{D:()=>_});var s=r(15215),i=r(35317),n=r("aurelia-framework"),a=r(20770),o=r(45660),c=r(59239),l=r(19072),p=r(83974),d=r("services/bugsnag/index"),h=r(96555),u=r(38777);function E(e){return e&&(e=e.replaceAll("/","\\")).endsWith("\\")&&(e=e.substring(0,e.length-1)),e}class I extends Error{constructor(e){super(`App launched failed with code ${e}.`),this.errorCode=e,Object.setPrototypeOf(this,I.prototype)}}let _=class{#R;#f;#D;constructor(e,t){this.#R=e,this.#f=t}setPlatforms(e){this.#D=e}setIconCacheDirectory(e){this.iconCacheDirectory=e}async findApps(e){const t=new Map,r=new Map;await this.#P(e,r,t);const s=new Map;for(const i of e){const e=t.get(i);if(e){const t=e.redirect?this.#k(e.location??"",e.redirect,r):e.location;t&&s.set(i,{platform:e.platform,sku:e.sku,location:t,alternateLocations:e.alternateLocations})}}return s}async#P(e,t,r){const s=new Set,i=this.#F(e);for(const[e,n]of i){const i=t.get(e)??new Map,a=n.filter((e=>!i.has(e)));if(a.length>0){const n=await(this.#D[e]?.findApps(a,await this.#V(e)))??[];for(const t of a){const s=new h.o(e,t).toString(),a=n.find((e=>e.sku===t)),o=a?{correlationId:s,platform:e,...a}:null;i.set(t,o),r.set(s,o)}t.set(e,i);for(const e of n)e.redirect&&s.add(new h.o(e.redirect.platform,e.redirect.sku).toString())}}s.size>0&&await this.#P(Array.from(s),t,r)}#F(e){const t=new Map;for(const r of e){const{platform:e,sku:s}=h.o.parse(r),i=t.get(e);void 0===i?t.set(e,[s]):i.push(s)}return t}async getInstalledApps(){const e=new Map,t=await this.#U();await Promise.all(Object.keys(this.#D).map((async r=>{try{const s=await this.#D[r].getApps(t[r]);e.set(r,new Map(s.map((e=>(e.location=E(e.location??""),e.alternateLocations&&(e.alternateLocations=e.alternateLocations.map(E)),[e.sku,e])))))}catch(e){e instanceof p.M||(0,d.report)(new Error(`${e?.constructor?.name??"Unknown error"} occured while reading ${r} apps. Message: ${e?.message}`))}})));const r=[];for(const[t,s]of e)for(const[i,n]of s){const s=n.redirect?this.#k(n.location??"",n.redirect,e):n.location;s&&r.push({platform:t,sku:i,location:s,alternateLocations:n.alternateLocations})}return r}#k(e,t,r,s=new Set){const i=r.get(t.platform);if(!i)return e;const n=i.get(t.sku);return n?n.redirect?s.has(n.redirect)?n.location??null:(s.add(n.redirect),this.#k(n.location??"",n.redirect,r,s)):n.location??null:e}async launchApp(e,t,r,s,n){"string"==typeof r&&(r=r.replaceAll("&","^&"));const a=await this.#b(e,t,r,s);if(null===a)throw new Error("App not found.");await new Promise(((e,t)=>{const r=this.#R.isMacOS?"/bin/bash":`${this.#R.getNativeSystemFolder()}\\cmd.exe`,s=(0,i.spawn)(a.command,{detached:!0,windowsHide:!0,stdio:"ignore",env:this.#R.info.env,cwd:a.cwd,shell:r}),o=new u.Vd([(0,u.$U)(s,"error",(e=>{o.dispose(),t(e)})),(0,u.$U)(s,"exit",(r=>{o.dispose(),"number"!=typeof r||0!==r&&100010!==r&&!a.successExitCodes?.includes(r)?t(new I(r)):e()})),n.onCancel((()=>{o.dispose(),t(new u._T)}))])}))}async#b(e,t,r,s){const i=await this.#V(e);return await(this.#D[e]?.getLaunchConfiguration(t,r,i,s))??null}async#U(){return await this.#f.state.pipe((0,o.$)(),(0,c.E)("catalog","platforms")).toPromise()}async#V(e){return await this.#f.state.pipe((0,o.$)(),(0,c.E)("catalog","platforms",e)).toPromise()??{}}async getIcon(e,t){return await(this.#D[e]?.getIcon(t,await this.#V(e)))??null}};_=(0,s.Cg)([(0,n.autoinject)(),(0,s.Sn)("design:paramtypes",[l.s,a.il])],_)}}]);
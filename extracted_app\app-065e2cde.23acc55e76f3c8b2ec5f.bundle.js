"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8861],{"cheats/resources/elements/map-banner":(e,t,a)=>{a.r(t),a.d(t,{MapBanner:()=>s});var i=a(15215),o=a("aurelia-framework"),n=a(39835),r=a("shared/api/value-converters"),l=a(54995);let s=class{#e;#t;constructor(e,t){this.loading={},this.#e=e,this.#t=t}attached(){this.selectedMap=this.maps[0]}async openMap(e){if(!this.loading[e.id]){this.loading[e.id]=!0;try{await this.#e.openMap(e.titleId,e.id)}finally{this.loading[e.id]=!1}}}async selectMap(e){if(!this.loading[e.id]){this.loading[e.id]=!0;try{this.selectedMap=e,await this.#e.openMap(e.titleId,e.id)}finally{this.loading[e.id]=!1}}}openMenu(e){this.maps.length>1&&(e.stopPropagation(),this.mapMenuOpen=!0)}selectedMapChanged(){this.mapMenuOpen=!1}get mapThumbnailCdnUrl(){return this.selectedMap?this.#t.toView(this.selectedMap.thumbnail):""}get title(){return this.maps.length>0?this.catalog.titles[this.maps[0].titleId]:void 0}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Boolean)],s.prototype,"collapsed",void 0),(0,i.Cg)([o.observable,(0,i.Sn)("design:type",Object)],s.prototype,"selectedMap",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Array)],s.prototype,"maps",void 0),(0,i.Cg)([(0,o.computedFrom)("selectedMap"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],s.prototype,"mapThumbnailCdnUrl",null),(0,i.Cg)([(0,o.computedFrom)("maps"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],s.prototype,"title",null),s=(0,i.Cg)([(0,l.m6)({selectors:{account:(0,l.$t)((e=>e.account)),catalog:(0,l.$t)((e=>e.catalog))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[n.I,r.CdnValueConverter])],s)},"cheats/resources/elements/map-banner.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var i=a(14385),o=a.n(i),n=new URL(a(18285),a.b);const r='<template> <require from="./map-banner.scss"></require> <require from="../../../shared/resources/elements/loading-indicator"></require> <require from="../../../shared/resources/custom-attributes/close-if-click-outside"></require> <require from="../../../resources/custom-attributes/detach-el"></require> <div class="map-tile ${loading[selectedMap.id] ? \'loading\' : \'\'} ${collapsed ? \'collapsed\' : \'\'}" click.delegate="openMap(selectedMap)"> <div class="map-header"> <div class="header-content"> <span class="link ${maps.length > 1 ? \'multiple-maps\' : \'\'}" click.delegate="openMenu($event)" tabindex="0"> <i class="map-icon"></i> <span>${\'map_banner.$name_map\' | i18n: { name: selectedMap.name }}</span> <i if.bind="maps.length > 1" class="menu-icon"></i> </span> <i click.delegate="openMap(selectedMap)" class="expand-icon" tabindex="0"></i> </div> </div> <div class="thumbnail ${thumbnailLoaded ? \'loaded\' : \'\'}" style.bind="{ \'--thumb-url\': \'url(\' + (mapThumbnailCdnUrl) + \')\' }"> <img class="map" src.bind="mapThumbnailCdnUrl" load.trigger="thumbnailLoaded = true"> <div class="wemod-logo"><img src="'+o()(n)+'"></div> </div> <loading-indicator if.bind="loading[selectedMap.id]"></loading-indicator> </div> <div class="map-menu-wrapper"> <div if.bind="mapMenuOpen" class="map-banner-map-menu" close-if-click-outside.two-way="mapMenuOpen" detach-el> <div class="map-menu-item" repeat.for="map of maps" click.delegate="selectMap(map)" tabindex="1"> <i class="check-icon ${selectedMap.id === map.id ? \'current\' : \'\'}"></i><span>${\'map_banner.$name_map\' | i18n: { name: map.name }}</span> </div> </div> </div> </template> '},"cheats/resources/elements/map-banner.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var i=a(31601),o=a.n(i),n=a(76314),r=a.n(n),l=a(4417),s=a.n(l),d=new URL(a(83959),a.b),p=r()(o()),m=s()(d);p.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,map-banner .map-tile .expand-icon,map-banner .map-tile .link i.map-icon,map-banner .map-tile .link i.menu-icon,.map-banner-map-menu .map-menu-item i.check-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}title-sidebar{--sidebar--content-top: calc(var(--constant--appHeaderHeight) + 24px);display:block;position:relative}title-sidebar.collapsed{width:0 !important}title-sidebar.collapsed :not(.collapse-button){display:none}title-sidebar.collapsed .collapse-button{position:absolute}title-sidebar.active{width:400px}@media(min-width: 1360px){title-sidebar{width:400px}}title-sidebar .title-sidebar-wrapper{--sidebar--base-max-height: calc( 100vh - var(--sidebar--content-top) - var(--ad-popup--safe-height) - var(--promotion-banner-height, 0) + 12px );display:flex;flex-direction:column;z-index:999;position:fixed;right:24px;top:44px;width:inherit;max-height:var(--sidebar--base-max-height);height:100%;overflow-x:visible !important}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:window-inactive,title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,title-sidebar .title-sidebar-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}@media(forced-colors: active){body:not(.override-contrast-mode) title-sidebar .title-sidebar-wrapper{border:1px solid #fff}}title-sidebar .scrollable-container{overflow-y:auto;overflow-x:clip;gap:20px;display:flex;flex-direction:column}title-sidebar .scrollable-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}title-sidebar .scrollable-container::-webkit-scrollbar-thumb:window-inactive,title-sidebar .scrollable-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}title-sidebar .scrollable-container::-webkit-scrollbar-thumb:window-inactive:hover,title-sidebar .scrollable-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}@media(forced-colors: active){body:not(.override-contrast-mode) title-sidebar .scrollable-container{border:1px solid #fff}}title-sidebar .action-buttons{position:relative;display:flex;gap:8px;width:100%;justify-content:space-between;overflow:visible}title-sidebar .action-buttons>*{margin-bottom:20px;flex:1}map-banner{display:block;position:relative}map-banner,map-banner *{cursor:pointer}map-banner .map-tile{display:block;width:100%;outline:none;border:0;border-radius:16px;overflow:hidden;position:relative;min-width:0;height:140px;transition:height .3s}map-banner .map-tile:before{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);border-radius:16px;z-index:1;mix-blend-mode:overlay;pointer-events:none}map-banner .map-tile.collapsed{height:44px;contain:content}map-banner .map-tile.collapsed .map-header{height:44px}map-banner .map-tile loading-indicator{position:absolute;left:50%;top:61.5px;margin-left:-15px;opacity:1}map-banner .map-tile loading-indicator circle{stroke:var(--color--accent)}map-banner .map-tile.loading{opacity:.7;pointer-events:none}map-banner .map-tile.loading .thumbnail{opacity:.5}map-banner .map-tile.loading .link{opacity:.5}map-banner .map-tile .thumbnail{display:block;border-radius:16px;height:auto;transition:opacity .15s;overflow:hidden;position:relative}map-banner .map-tile .thumbnail:nth-child(1n+0) img{animation-delay:0.1s}map-banner .map-tile .thumbnail:nth-child(2n+0) img{animation-delay:0.2s}map-banner .map-tile .thumbnail:nth-child(3n+0) img{animation-delay:0.3s}map-banner .map-tile .thumbnail:nth-child(4n+0) img{animation-delay:0.4s}map-banner .map-tile .thumbnail:nth-child(5n+0) img{animation-delay:0.5s}map-banner .map-tile .thumbnail:nth-child(6n+0) img{animation-delay:0.6s}map-banner .map-tile .thumbnail:nth-child(7n+0) img{animation-delay:0.7s}map-banner .map-tile .thumbnail:nth-child(8n+0) img{animation-delay:0.8s}map-banner .map-tile .thumbnail:nth-child(9n+0) img{animation-delay:0.9s}map-banner .map-tile .thumbnail .map{display:block;width:100%;height:140px;object-fit:cover}map-banner .map-tile .thumbnail .wemod-logo{position:absolute;display:flex;width:32px;height:32px;padding:10px;flex-direction:column;justify-content:center;align-items:center;gap:10px;flex-shrink:0;bottom:9px;right:9px;border-radius:8px;background:rgba(13,15,18,.3)}map-banner .map-tile .thumbnail .wemod-logo img{height:12px}map-banner .map-tile .thumbnail:not(.loaded) img{animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box;visibility:hidden}map-banner .map-tile .thumbnail.loaded:before{content:"";position:absolute;top:0;right:0;bottom:0;left:0;background:rgba(0,0,0,.1)}map-banner .map-tile .thumbnail.loaded:after{content:"";position:absolute;left:0;top:0;width:100%;height:80px;background:linear-gradient(180deg, rgba(var(--theme--background--rgb), 0.9) 0%, rgba(var(--theme--background--rgb), 0.85) 10%, rgba(var(--theme--background--rgb), 0.82) 40%, rgba(var(--theme--background--rgb), 0.7) 60%, rgba(var(--theme--background--rgb), 0) 100%)}map-banner .map-tile .map-header{position:absolute;top:0;left:0;z-index:1;width:100%;height:48px}map-banner .map-tile .map-header *{cursor:pointer}map-banner .map-tile .header-content{width:100%;height:100%;display:flex;align-items:center}map-banner .map-tile .expand-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;color:rgba(255,255,255,.8);display:flex;margin-left:auto;margin-right:16px;padding:6px;border-radius:8px;z-index:1}map-banner .map-tile .expand-icon:before{font-family:inherit;content:"pan_zoom"}map-banner .map-tile .expand-icon:hover{background:rgba(255,255,255,.1);color:#fff;cursor:pointer}map-banner .map-tile .link{font-size:14px;font-weight:700;line-height:24px;position:absolute;z-index:3;color:rgba(255,255,255,.8);display:inline-flex;align-items:center;transition:color .15s;max-width:100%;gap:12px;padding:6px 8px;margin:6px 8px;border-radius:8px;cursor:pointer}map-banner .map-tile .link i:hover,map-banner .map-tile .link span:hover{cursor:pointer}map-banner .map-tile .link span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:0 1 auto}map-banner .map-tile .link i.map-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;color:rgba(255,255,255,.8)}map-banner .map-tile .link i.map-icon:before{font-family:inherit;content:"location_on"}map-banner .map-tile .link i.menu-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;color:rgba(255,255,255,.8)}map-banner .map-tile .link i.menu-icon:before{font-family:inherit;content:"keyboard_arrow_down"}map-banner .map-tile .link.multiple-maps:hover{background:rgba(255,255,255,.1);color:#fff}map-banner .map-menu-wrapper{position:absolute;left:0;top:50px}.map-banner-map-menu{display:flex;flex-direction:column;gap:8px;padding:8px;align-items:center;border-radius:16px;background:rgba(0,0,0,.2);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);width:280px;max-height:400px;overflow-y:auto}.map-banner-map-menu::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.map-banner-map-menu::-webkit-scrollbar-thumb:window-inactive,.map-banner-map-menu::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.map-banner-map-menu::-webkit-scrollbar-thumb:window-inactive:hover,.map-banner-map-menu::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.map-banner-map-menu .map-menu-item{display:flex;gap:8px;padding:8px;color:rgba(255,255,255,.8);font-size:14px;font-weight:500;line-height:24px;width:100%;align-items:center;border-radius:8px}.map-banner-map-menu .map-menu-item i.check-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:rgba(255,255,255,.8);font-size:14px}.map-banner-map-menu .map-menu-item i.check-icon:before{font-family:inherit;content:"check"}.map-banner-map-menu .map-menu-item i.check-icon:not(.current){visibility:hidden}.map-banner-map-menu .map-menu-item:hover,.map-banner-map-menu .map-menu-item span:hover,.map-banner-map-menu .map-menu-item i:hover{cursor:pointer}.map-banner-map-menu .map-menu-item:hover{background:rgba(255,255,255,.15);color:#fff}`,""]);const b=p},"cheats/resources/elements/map-feed-item":(e,t,a)=>{a.r(t),a.d(t,{MapFeedItem:()=>m});var i=a(15215),o=a("aurelia-framework"),n=a(20770),r=a(48100),l=a(33511),s=a(39835),d=a(96555),p=a(54995);let m=class{#a;#i;#e;constructor(e,t){this.thumbnailLoaded=!1,this.loading=!1,this.#i=e,this.#e=t}itemChanged(){this.#a&&this.#a?.unsubscribe(),this.#a=this.#i.state.pipe((0,r.T)((e=>!0===e.favoriteTitles[this.item.titleId])),(0,l.M)((e=>this.isFavorite=e))).subscribe()}detached(){this.#a?.unsubscribe()}async openMap(e,t){if(!this.loading){this.loading=!0;try{await this.#e.openMap(e,t)}finally{this.loading=!1}}}get itemMaps(){return this.catalog?.maps.filter((e=>e.titleId===this.item.titleId))}get steamAppId(){const e=this.catalog.titles[this.item.titleId];return e?.gameIds?.flatMap((e=>this.catalog.games[e].correlationIds))?.map(d.o.parse)?.find((e=>"steam"===e.platform))?.sku??null}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",Object)],m.prototype,"item",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],m.prototype,"location",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],m.prototype,"previousRoute",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],m.prototype,"parentRoute",void 0),(0,i.Cg)([(0,o.computedFrom)("catalog","item"),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],m.prototype,"itemMaps",null),(0,i.Cg)([(0,o.computedFrom)("catalog","item"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],m.prototype,"steamAppId",null),m=(0,i.Cg)([(0,p.m6)({selectors:{catalog:(0,p.$t)((e=>e.catalog))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[n.il,s.I])],m)},"cheats/resources/elements/map-feed-item.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var i=a(14385),o=a.n(i),n=new URL(a(98593),a.b);const r='<template> <require from="./map-feed-item.scss"></require> <require from="resources/elements/favorite-button"></require> <require from="shared/cheats/resources/custom-attributes/steam-capsule-bg"></require> <require from="shared/resources/elements/loading-indicator"></require> <a class="map-feed-item ${isFavorite ? \'map-feed-item--favorite\' : \'\'} ${loading ? \'loading\' : \'\'}" click.delegate="openMap(item.titleId, itemMaps[0].id)"> <div class="thumbnail-wrapper"> <img class="thumbnail ${thumbnailLoaded ? \'loaded\' : \'\'}" fallback-src="'+o()(n)+'" src.bind="itemMaps[0].thumbnail | cdn" load.trigger="thumbnailLoaded = true"> <loading-indicator if.bind="loading"></loading-indicator> </div> <span class="tags"> <span if.bind="itemMaps.length > 1">${\'map_feed_item.$count_maps\' | i18n:{count: itemMaps.length}}</span> </span> <div class="title-row"> <span class="capsule" steam-capsule-bg="steam-id.bind: steamAppId"></span> <span class="title-name">${item.titleName}</span> <favorite-button if.bind="item.isAvailable || item.isInstalled" title-id.bind="item.titleId"></favorite-button> </div> </a> </template> '},"cheats/resources/elements/map-feed-item.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var i=a(31601),o=a.n(i),n=a(76314),r=a.n(n)()(o());r.push([e.id,'map-feed-item .map-feed-item{position:relative;aspect-ratio:486/182;display:flex;flex-direction:column;padding:16px;justify-content:space-between;z-index:0;overflow:hidden;transition:opacity .15s}map-feed-item .map-feed-item loading-indicator{position:absolute;left:50%;top:50%;transform:translate(-50%, -50%);opacity:1}map-feed-item .map-feed-item loading-indicator circle{stroke:var(--color--accent)}map-feed-item .map-feed-item.loading{opacity:.7;pointer-events:none}map-feed-item .map-feed-item.loading .thumbnail{opacity:.5}map-feed-item .map-feed-item.loading .link{opacity:.5}map-feed-item .map-feed-item:hover{opacity:.8}map-feed-item .map-feed-item:before{content:"";position:absolute;border-radius:16px;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);mix-blend-mode:overlay;z-index:1;pointer-events:none}map-feed-item .map-feed-item .thumbnail-wrapper{width:100%;height:100%;position:absolute;left:0;top:0;z-index:-1;border-radius:16px;overflow:hidden}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(1n+0) img{animation-delay:0.1s}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(2n+0) img{animation-delay:0.2s}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(3n+0) img{animation-delay:0.3s}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(4n+0) img{animation-delay:0.4s}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(5n+0) img{animation-delay:0.5s}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(6n+0) img{animation-delay:0.6s}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(7n+0) img{animation-delay:0.7s}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(8n+0) img{animation-delay:0.8s}map-feed-item .map-feed-item .thumbnail-wrapper:nth-child(9n+0) img{animation-delay:0.9s}map-feed-item .map-feed-item .thumbnail-wrapper:before{content:"";position:absolute;left:0;top:0;width:100%;height:100%;background:linear-gradient(178.52deg, rgba(19, 20, 23, 0) 25.27%, rgba(19, 20, 23, 0.2) 43.31%, #131417 98.38%)}map-feed-item .map-feed-item .thumbnail-wrapper .thumbnail{width:100%;height:100%}map-feed-item .map-feed-item .tags{display:flex;align-items:center;gap:6px}map-feed-item .map-feed-item .tags>*{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;background:#fff}map-feed-item .map-feed-item .title-row{display:flex;gap:8px;align-items:flex-end}map-feed-item .map-feed-item .capsule{display:inline-block;aspect-ratio:32/40;border-radius:8px;overflow:hidden;width:40px;background-size:cover;background-repeat:no-repeat}map-feed-item .map-feed-item .capsule.is-fallback{display:none}map-feed-item .map-feed-item .title-name{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;flex:1;color:#fff;text-shadow:0 2px 2px rgba(0,0,0,.25);display:-webkit-box;-webkit-line-clamp:3;-webkit-box-orient:vertical;overflow:hidden;text-overflow:ellipsis}map-feed-item favorite-button .favorite-icon.favorite{filter:drop-shadow(0px 0px 4px var(--theme--background))}.theme-default map-feed-item favorite-button .favorite-icon.favorite{color:#fff}.theme-purple-pro map-feed-item favorite-button .favorite-icon.favorite{color:#fff}.theme-green-pro map-feed-item favorite-button .favorite-icon.favorite{color:#fff}.theme-orange-pro map-feed-item favorite-button .favorite-icon.favorite{color:#fff}.theme-pro map-feed-item favorite-button .favorite-icon.favorite{color:#fff}map-feed-item favorite-button:not(:hover) button{background:none}',""]);const l=r},"cheats/resources/elements/map-mods-list":(e,t,a)=>{a.r(t),a.d(t,{MapModsList:()=>s});var i=a(15215),o=a("aurelia-framework"),n=a(62914),r=a(39835),l=a(54995);let s=class{#e;#o;constructor(e,t){this.collapsed=!1,this.#e=e,this.#o=t}handleTeleportClick(){const e=this.maps.filter((e=>e.titleId===this.titleId))[0];e&&(this.#e.openMap(this.titleId,e.id),this.#o.event("mods_list_teleport_click",{titleId:this.titleId},n.Io))}handleCategoryCollapseClick(){this.collapsed=!this.collapsed}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],s.prototype,"titleId",void 0),s=(0,i.Cg)([(0,o.autoinject)(),(0,l.m6)({selectors:{maps:(0,l.$t)((e=>e.catalog.maps)),showModHotkeys:(0,l.$t)((e=>e.settings?.showModHotkeys))}}),(0,i.Sn)("design:paramtypes",[r.I,n.j0])],s)},"cheats/resources/elements/map-mods-list.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template> <require from="./map-mods-list.scss"></require> <div class="category ${collapsed ? \'collapsed\' : \'\'}"> <div class="category-header" click.delegate="handleCategoryCollapseClick()"> <i class="icon map-icon"></i> <span class="label">${\'map_mods_list.category_maps\' | i18n}</span> <i class="icon collapse-category"></i> </div> <div class="cheats"> <div class="cheats-wrapper"> <div class="cheat"> <div class="name-input-container"> <div class="cheat-name"> <div class="cheat-name-inner"> <span class="save-cheats-icon"> </span> <span class="label-wrapper"> <span class="label">${\'map_mods_list.key_location_teleport\' | i18n}</span> </span> </div> </div> <div class="input"> <div class="input-inner"> <button click.delegate="handleTeleportClick()" class="button-input"> ${\'map_mods_list.open_in_map\' | i18n} </button> </div> </div> </div> <div class="hotkeys" if.bind="showModHotkeys"> </div> </div> </div> </div> </div> </template> '},"cheats/resources/elements/mod-onboarding":(e,t,a)=>{a.r(t),a.d(t,{ModOnboarding:()=>l});var i=a(15215),o=a("aurelia-framework"),n=a(96276),r=a(54995);let l=class{#n;constructor(e){this.#n=e}attached(){this.#r()}gameChanged(){this.#r()}installedGameVersionsChanged(){this.#r()}#l(e){return!!e&&e.tags.includes("free")}#s(e){return!!e&&(e.purchaseUris??[]).length>0}#d(e){return!!this.#n.getPreferredInstallationInfo(e.id).app}#r(){!this.#d(this.game)&&this.#s(this.game)?this.state=this.#l(this.game)?"install_for_free":"install":this.state="play"}};(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",Object)],l.prototype,"game",void 0),l=(0,i.Cg)([(0,o.autoinject)(),(0,r.m6)({selectors:{installedGameVersions:(0,r.$t)((e=>e.installedGameVersions))}}),(0,i.Sn)("design:paramtypes",[n.T])],l)},"cheats/resources/elements/mod-onboarding-button-graphic":(e,t,a)=>{a.r(t),a.d(t,{ModOnboardingButtonGraphic:()=>n});var i=a(15215),o=a("aurelia-framework");class n{}(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",String)],n.prototype,"type",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",String)],n.prototype,"platformId",void 0)},"cheats/resources/elements/mod-onboarding-button-graphic.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template class="${(type || \'\') | dashCase}"> <require from="./mod-onboarding-button-graphic.scss"></require> <template if.bind="type === \'play\'"> <img src.bind="platformId | playIconSrc"> <span>${\'trainer_play_button.play\' | i18n}</span> </template> <template else> <template if.bind="type === \'install_for_free\'"> <img src.bind="platformId | playIconSrc"> <span>${\'mod_onboarding_button_graphic.install\' | i18n}</span> </template> <span else>${\'trainer_play_button.add_game\' | i18n}</span> </template> <i class="caret"></i> <i class="pointer-hand"></i> </template> '},"cheats/resources/elements/mod-onboarding-button-graphic.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var i=a(31601),o=a.n(i),n=a(76314),r=a.n(n),l=a(4417),s=a.n(l),d=new URL(a(83959),a.b),p=new URL(a(80627),a.b),m=r()(o()),b=s()(d),c=s()(p);m.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${b}) format("woff2")}.material-symbols-outlined,mod-onboarding-button-graphic .caret{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}mod-onboarding-button-graphic{display:inline-flex;align-items:center;padding:6px 12px;border-radius:100px;position:relative;max-width:170px}mod-onboarding-button-graphic:after{content:"";position:absolute;left:0;top:6px;width:100%;height:100%;border-radius:inherit;opacity:.4;z-index:-2;filter:blur(16px)}mod-onboarding-button-graphic img{width:17px;margin:0 4px 0 0}mod-onboarding-button-graphic span{font-weight:900;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;font-size:18px;line-height:18px;color:#fff;margin:0 5px 0 0}mod-onboarding-button-graphic .caret{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:rgba(255,255,255,.8);font-size:15px}mod-onboarding-button-graphic .caret:before{font-family:inherit;content:"expand_more"}mod-onboarding-button-graphic.play{background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%)}mod-onboarding-button-graphic.play:before{content:"";position:absolute;top:-2.25px;right:-2.25px;bottom:-2.25px;left:-2.25px;border-radius:inherit;opacity:.2;z-index:-1;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%)}mod-onboarding-button-graphic.play:after{background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%)}mod-onboarding-button-graphic.install,mod-onboarding-button-graphic.install:after,mod-onboarding-button-graphic.install-for-free,mod-onboarding-button-graphic.install-for-free:after{background:rgba(var(--color--accent--rgb), 0.1);border:1px solid var(--color--accent)}mod-onboarding-button-graphic .pointer-hand{display:inline-block;width:33px;height:34px;background:url(${c}) center/contain no-repeat;position:absolute;bottom:-22px;right:4px}`,""]);const g=m},"cheats/resources/elements/mod-onboarding-mods-graphic":(e,t,a)=>{a.r(t),a.d(t,{ModOnboardingModsGraphic:()=>i});class i{}},"cheats/resources/elements/mod-onboarding-mods-graphic.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>l});var i=a(14385),o=a.n(i),n=new URL(a(51091),a.b),r=o()(n);const l='<template> <require from="./mod-onboarding-mods-graphic.scss"></require> <require from="../../../shared/resources/elements/toggle.html"></require> <div class="header"> <i></i> <span>${\'mod_onboarding_mods_graphic.player\' | i18n}</span> </div> <div class="mod"> <i><inline-svg src="'+r+'"></inline-svg></i> <span>${\'mod_onboarding_mods_graphic.unlimited_health\' | i18n}</span> <toggle value.bind="false" inert></toggle> </div> <div class="mod"> <i><inline-svg src="'+r+'"></inline-svg></i> <span>${\'mod_onboarding_mods_graphic.unlimited_stamina\' | i18n}</span> <toggle value.bind="true" inert></toggle> </div> <i class="pointer-hand"></i> </template> '},"cheats/resources/elements/mod-onboarding-mods-graphic.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var i=a(31601),o=a.n(i),n=a(76314),r=a.n(n),l=a(4417),s=a.n(l),d=new URL(a(83959),a.b),p=new URL(a(80627),a.b),m=r()(o()),b=s()(d),c=s()(p);m.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${b}) format("woff2")}.material-symbols-outlined,mod-onboarding-mods-graphic .header i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}mod-onboarding-mods-graphic{display:block}mod-onboarding-mods-graphic .header{background:rgba(255,255,255,.1);margin-bottom:1px;padding:5px 0;display:flex;align-items:center;border-top-left-radius:10px}mod-onboarding-mods-graphic .header i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:rgba(255,255,255,.6);font-size:15px}mod-onboarding-mods-graphic .header i:before{font-family:inherit;content:"person"}mod-onboarding-mods-graphic .header span{font-weight:700;font-size:9px;line-height:16px;color:#fff}mod-onboarding-mods-graphic .mod{background:rgba(255,255,255,.1);display:flex;align-items:center;height:32px}mod-onboarding-mods-graphic .mod>span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;font-size:9px;line-height:13px;flex:1 1 auto;color:rgba(255,255,255,.8);margin-right:8px}mod-onboarding-mods-graphic .mod toggle{transform:scale(65%);margin:0 -12px}mod-onboarding-mods-graphic .mod svg{height:10px}mod-onboarding-mods-graphic .mod svg *{fill:var(--theme--highlight)}mod-onboarding-mods-graphic .mod:last-of-type{border-bottom-left-radius:10px}mod-onboarding-mods-graphic .mod i,mod-onboarding-mods-graphic .header i{flex:0 0 26px;display:inline-flex !important;align-items:center;justify-content:center}mod-onboarding-mods-graphic .pointer-hand{display:inline-block;width:33px;height:34px;background:url(${c}) center/contain no-repeat;position:absolute;bottom:-15px;right:4px}`,""]);const g=m},"cheats/resources/elements/mod-onboarding.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template> <require from="./mod-onboarding.scss"></require> <require from="./mod-onboarding-button-graphic"></require> <require from="./mod-onboarding-mods-graphic"></require> <h1> <i></i> ${\'mod_onboarding.get_started_in_$x_steps\' | i18n:{x: state === \'play\' ? 2 : 3}} </h1> <template if.bind="[\'install\', \'install_for_free\'].includes(state)"> <div class="card"> <h2>${\'mod_onboarding.step_1\' | i18n}</h2> <p if.bind="state === \'install_for_free\'" innerhtml.bind="\'mod_onboarding.game_not_installed_step_1_instructions_free\' | i18n | markdown"></p> <p else innerhtml.bind="\'mod_onboarding.game_not_installed_step_1_instructions\' | i18n | markdown"></p> <mod-onboarding-button-graphic platform-id.bind="game.platformId" type.bind="state"></mod-onboarding-button-graphic> </div> <div class="card"> <h2>${\'mod_onboarding.step_2\' | i18n}</h2> <p innerhtml.bind="\'mod_onboarding.game_not_installed_step_2_instructions\' | i18n | markdown"></p> <mod-onboarding-button-graphic platform-id.bind="game.platformId" type="play"></mod-onboarding-button-graphic> </div> <div class="card"> <h2>${\'mod_onboarding.step_3\' | i18n}</h2> <p innerhtml.bind="\'mod_onboarding.game_not_installed_step_3_instructions\' | i18n | markdown"></p> <mod-onboarding-mods-graphic></mod-onboarding-mods-graphic> </div> </template> <template else> <div class="card"> <h2>${\'mod_onboarding.step_1\' | i18n}</h2> <p innerhtml.bind="\'mod_onboarding.game_installed_step_1_instructions\' | i18n | markdown"></p> <mod-onboarding-button-graphic platform-id.bind="game.platformId" type="play"></mod-onboarding-button-graphic> </div> <div class="card"> <h2>${\'mod_onboarding.step_2\' | i18n}</h2> <p innerhtml.bind="\'mod_onboarding.game_installed_step_2_instructions\' | i18n | markdown"></p> <mod-onboarding-mods-graphic></mod-onboarding-mods-graphic> </div> </template> <footer innerhtml.bind="\'mod_onboarding.modding_tip\' | i18n | markdown"></footer> </template> '},"cheats/resources/elements/mod-onboarding.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var i=a(31601),o=a.n(i),n=a(76314),r=a.n(n),l=a(4417),s=a.n(l),d=new URL(a(83959),a.b),p=r()(o()),m=s()(d);p.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,mod-onboarding h1 i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}mod-onboarding{display:block;background:rgba(255,255,255,.025);backdrop-filter:blur(50px);padding:16px;border-radius:16px}mod-onboarding h1{font-size:14px;line-height:21px;font-weight:700;color:#fff;margin:0 0 14px;display:inline-flex;align-items:center}mod-onboarding h1 i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:rgba(255,255,255,.8);font-size:14px;margin-right:12px}mod-onboarding h1 i:before{font-family:inherit;content:"check_circle"}mod-onboarding .card{background:rgba(255,255,255,.05);border:1px solid rgba(255,255,255,.1);border-radius:16px;padding:26px 16px;position:relative}mod-onboarding .card h2{font-weight:800;font-size:20px;line-height:27px;color:#fff;margin:0 0 4px;max-width:150px}mod-onboarding .card p{font-size:14px;line-height:21px;font-weight:600;color:rgba(255,255,255,.8);margin:0;padding:0;display:inline-block;max-width:150px}mod-onboarding .card p strong{font-weight:600;color:var(--theme--highlight)}mod-onboarding .card p em{color:var(--color--accent);font-style:normal}mod-onboarding .card+.card{margin-top:12px}mod-onboarding mod-onboarding-button-graphic{position:absolute;right:93.5px;top:50%;transform:translate(50%, -50%)}mod-onboarding mod-onboarding-mods-graphic{position:absolute;right:0;top:50%;width:190px;transform:translateY(-50%)}mod-onboarding footer{font-size:14px;line-height:21px;margin-top:12px;color:rgba(255,255,255,.8)}mod-onboarding footer em{font-weight:700;font-style:normal}`,""]);const b=p},"cheats/resources/elements/overlay-button":(e,t,a)=>{a.r(t),a.d(t,{OverlayButton:()=>g});var i=a(15215),o=a("aurelia-framework"),n=a(20770),r=a(62914),l=a(43544),s=a(10191),d=a(24697),p=a(18165),m=a(54995),b=a(70236),c=a(48881);let g=class{#p;#m;#i;#o;#b;#c;constructor(e,t,a,i,o,n){this.overlayHotkey=n,this.#m=e,this.#i=t,this.#o=a,this.#b=i,this.#c=o}attached(){this.#p=this.#b.onComplete((e=>this.#g(e)))}detached(){this.#p?.dispose()}async#g(e){e||this.overlayAnnouncementShown||this.#c.open()}get isCreatorOrTester(){return(0,b.br)(this.account?.flags,[64,16384])}get overlaySupported(){return this.isCreatorOrTester||this.#m.gameSupportsOverlay(this.gameId)}get enabled(){return!this.gamePreferences[this.gameId]?.overlayDisabled}toggleOverlayTitle(){const e=!this.gamePreferences[this.gameId]?.overlayDisabled;this.#i.dispatch(c.IF,this.gameId,e),this.#o.event("overlay_disable_toggle_click",{disabled:e,gameId:this.gameId},r.Io)}markUnsupportedTooltipSeen(){this.#i.dispatch(c.NX,"hasSeenUnsupportedTooltip",!0)}};(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],g.prototype,"gameId",void 0),(0,i.Cg)([o.bindable,(0,i.Sn)("design:type",String)],g.prototype,"titleName",void 0),(0,i.Cg)([(0,o.computedFrom)("account.flags"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],g.prototype,"isCreatorOrTester",null),(0,i.Cg)([(0,o.computedFrom)("gameId"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],g.prototype,"overlaySupported",null),(0,i.Cg)([(0,o.computedFrom)("gameId","gamePreferences"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],g.prototype,"enabled",null),g=(0,i.Cg)([(0,o.autoinject)(),(0,m.m6)({selectors:{account:(0,m.$t)((e=>e.account)),gamePreferences:(0,m.$t)((e=>e.gamePreferences)),overlayAnnouncementShown:(0,m.$t)((e=>e.flags.overlayAnnouncementShown)),hasSeenUnsupportedTooltip:(0,m.$t)((e=>e.flags.hasSeenUnsupportedTooltip)),overlayEnabledGlobally:(0,m.$t)((e=>e.settings.enableOverlay))}}),(0,i.Sn)("design:paramtypes",[s.s,n.il,r.j0,d.v,p.v,l.u])],g)},"cheats/resources/elements/overlay-button.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template> <require from="./overlay-button.scss"></require> <require from="../../../shared/resources/elements/tooltip"></require> <template if.bind="overlayEnabledGlobally && overlaySupported"> <div class="overlay-button split"> <span class="button left" click.delegate="toggleOverlayTitle()"> <span class="label"> <span>${\'overlay_button.overlay\' | i18n}</span> </span> <div class="overlay-toggle-wrapper ${enabled ? \'enabled\' : \'\'}" tabindex="0"> <span class="toggle"> <span class="handle"></span> </span> </div> </span> </div> <tooltip direction="top-right"> <div slot="content"> <span innerhtml.bind="\'overlay_button.tooltip_message\' | i18n:{hotkey: overlayHotkey.displayHotkey} |\n                markdown"></span> </div> </tooltip> </template> <template else> <div class="overlay-button split unsupported" mouseover.delegate="markUnsupportedTooltipSeen()"> <span class="button left"> <span class="icon-container ${!hasSeenUnsupportedTooltip ? \'warning\' : \'\'}"> <i>warning</i> </span> <div class="label">${\'overlay_button.overlay\' | i18n}</div> <div class="overlay-toggle-wrapper" tabindex="0"> <span class="toggle"> <span class="handle"></span> </span> </div> </span> </div> <tooltip direction="top-right"> <div slot="content"> <template if.bind="!overlayEnabledGlobally"> <b>${\'overlay_button.disabled_tooltip_header\' | i18n}</b> <div>${\'overlay_button.disabled_tooltip_message\' | i18n}</div> </template> <template else> <b>${\'overlay_button.unsupported_tooltip_header\' | i18n}</b> <div>${\'overlay_button.unsupported_tooltip_message\' | i18n:{game: titleName}}</div> </template> </div> </tooltip> </template> </template> '},"cheats/resources/elements/overlay-button.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var i=a(31601),o=a.n(i),n=a(76314),r=a.n(n),l=a(4417),s=a.n(l),d=new URL(a(83959),a.b),p=r()(o()),m=s()(d);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,overlay-button .overlay-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}overlay-button{position:relative}overlay-button>.overlay{position:fixed;top:0;right:0;left:0;bottom:0;background:#0d0f12;opacity:.5;z-index:-1}overlay-button .overlay-button{border-radius:16px;overflow:hidden;position:relative;display:block}overlay-button .overlay-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;height:16px}overlay-button .overlay-button .icon-container{height:16px;width:16px;position:relative}overlay-button .overlay-button button,overlay-button .overlay-button .button{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;display:flex;align-items:center;transition:color .3s ease-in-out;background:rgba(var(--theme--background-accent--rgb), 0.9);border:1px solid rgba(255,255,255,.1);border-radius:16px;gap:12px;padding:10px 10px 10px 16px;color:rgba(255,255,255,.8);height:44px;width:100%;min-width:170px}overlay-button .overlay-button button i,overlay-button .overlay-button .button i{color:rgba(255,255,255,.6)}overlay-button .overlay-button .label{line-height:14px;text-align:left;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;text-overflow:ellipsis;overflow:hidden}overlay-button .overlay-button.disabled button,overlay-button .overlay-button.disabled .button{background-color:rgba(var(--theme--background-accent--rgb), 0.4);color:var(--theme--text-secondary)}overlay-button .overlay-button i{font-variation-settings:"FILL" 1,"wght" 400 !important}overlay-button .overlay-button i.small{font-size:14px}overlay-button .overlay-button.split{display:flex;flex-direction:row;min-width:170px}overlay-button .overlay-button.split .button{min-width:0;padding-right:14px;white-space:nowrap}overlay-button .overlay-button.split .button,overlay-button .overlay-button.split .button *{cursor:pointer}overlay-button .overlay-button.split .button .label{flex:1}overlay-button .overlay-button.split .button.left,overlay-button .overlay-button.split .button.right{min-width:0;white-space:nowrap;transition:all .3s ease-in-out}overlay-button .overlay-button.split .button.left:hover,overlay-button .overlay-button.split .button.right:hover{color:#fff;border-color:rgba(255,255,255,.15);background:linear-gradient(0deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.03) 100%),rgba(var(--theme--background-accent--rgb), 0.9)}.theme-default overlay-button .overlay-button.split .button.left:hover i,.theme-default overlay-button .overlay-button.split .button.right:hover i{color:rgba(255,255,255,.8)}.theme-purple-pro overlay-button .overlay-button.split .button.left:hover i,.theme-purple-pro overlay-button .overlay-button.split .button.right:hover i{color:rgba(255,255,255,.8)}.theme-green-pro overlay-button .overlay-button.split .button.left:hover i,.theme-green-pro overlay-button .overlay-button.split .button.right:hover i{color:rgba(255,255,255,.8)}.theme-orange-pro overlay-button .overlay-button.split .button.left:hover i,.theme-orange-pro overlay-button .overlay-button.split .button.right:hover i{color:rgba(255,255,255,.8)}.theme-pro overlay-button .overlay-button.split .button.left:hover i,.theme-pro overlay-button .overlay-button.split .button.right:hover i{color:rgba(255,255,255,.8)}overlay-button .overlay-button.split .button.left{border-top-right-radius:0;border-bottom-right-radius:0;border-right-width:.5px;padding-right:16px;display:flex;flex-direction:row;flex-wrap:nowrap;justify-content:space-between;min-width:min-content}overlay-button .overlay-button.split .button.left .overlay-toggle-wrapper{position:relative;display:inline-flex;height:44px;border-radius:100px;align-items:center}@media(forced-colors: active){body:not(.override-contrast-mode) overlay-button .overlay-button.split .button.left .overlay-toggle-wrapper{border:1px solid #fff}}overlay-button .overlay-button.split .button.left .overlay-toggle-wrapper .toggle{position:relative;width:31px;height:20px;border-radius:12px;background:rgba(255,255,255,.15);flex:0 0 auto}@media(forced-colors: active){body:not(.override-contrast-mode) overlay-button .overlay-button.split .button.left .overlay-toggle-wrapper .toggle{border:1px solid #fff}}overlay-button .overlay-button.split .button.left .overlay-toggle-wrapper .toggle .handle{width:16px;height:16px;border-radius:50%;display:flex;align-items:center;justify-content:center;background:rgba(255,255,255,.5);position:absolute;left:2px;top:2px;transition:left .15s ease-in-out,background-color .15s}overlay-button .overlay-button.split .button.left .overlay-toggle-wrapper.enabled .toggle{background:linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),var(--theme--highlight)}overlay-button .overlay-button.split .button.left .overlay-toggle-wrapper.enabled .toggle .handle{left:calc(100% - 18px);background:#fff}overlay-button .overlay-button.split .button.right{border-top-left-radius:0;border-bottom-left-radius:0;border-left-width:.5px;max-width:max-content}overlay-button .overlay-button.unsupported .button,overlay-button .overlay-button.unsupported .button *{cursor:not-allowed}overlay-button .overlay-button.unsupported .button.left .label{color:rgba(255,255,255,.35)}overlay-button .overlay-button.unsupported .button.left .icon-container i{color:rgba(255,255,255,.4)}overlay-button .overlay-button.unsupported .button.left .icon-container.warning i{color:var(--color--warning)}overlay-button>tooltip .tooltip-content{min-width:300px}overlay-button>tooltip .tooltip-content b{color:#fff}overlay-button>tooltip .tooltip-content [slot=content]{flex-direction:column;align-items:flex-start;gap:4px}overlay-button>tooltip .tooltip-content [slot=content]>*+*{margin-left:0}`,""]);const b=p},"cheats/resources/elements/play-game-menu":(e,t,a)=>{a.r(t),a.d(t,{PlayGameMenu:()=>l});var i=a(15215),o=a("aurelia-framework"),n=a(96276),r=a(54995);let l=class{#n;constructor(e){this.open=!1,this.#n=e}get gameId(){return this.titlePreferences[this.titleId]?.selectedGameId}get preferredInstallation(){return this.#n.getPreferredInstallationInfo(this.gameId).app}close(){this.open=!1}};(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.twoWay}),(0,i.Sn)("design:type",Boolean)],l.prototype,"open",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",String)],l.prototype,"titleId",void 0),(0,i.Cg)([(0,o.computedFrom)("titlePreferences"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],l.prototype,"gameId",null),(0,i.Cg)([(0,o.computedFrom)("gameId","installations"),(0,i.Sn)("design:type",Object),(0,i.Sn)("design:paramtypes",[])],l.prototype,"preferredInstallation",null),l=(0,i.Cg)([(0,r.m6)({selectors:{titlePreferences:(0,r.$t)((e=>e.titlePreferences))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[n.T])],l)},"cheats/resources/elements/play-game-menu.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template class="au-animate"> <require from="./play-game-menu.scss"></require> <require from="./game-selector"></require> <require from="./launch-without-mods-button"></require> <div class="overlay" click.delegate="open = false"></div> <div class="container"> <launch-without-mods-button game-id.bind="gameId" app.bind="preferredInstallation" trigger="play_game_menu" click.delegate="close()" tabindex="0"></launch-without-mods-button> <hr> <game-selector title-id.bind="titleId" show-custom-exe-info.bind="true" open.bind="open"></game-selector> </div> </template> '},"cheats/resources/elements/play-game-menu.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>b});var i=a(31601),o=a.n(i),n=a(76314),r=a.n(n),l=a(4417),s=a.n(l),d=new URL(a(83959),a.b),p=r()(o()),m=s()(d);p.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}play-game-menu{position:relative}play-game-menu>.overlay{position:fixed;top:0;right:0;left:0;bottom:0;background:#0d0f12;opacity:.5;z-index:-1}play-game-menu>.container{display:flex;flex-direction:column;justify-content:flex-start;padding:8px;position:relative;width:280px;background:rgba(128,128,128,.1);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:16px}play-game-menu>.container launch-without-mods-button button{width:100%}play-game-menu>.container>hr{margin:12px -8px;padding:0;border:none;border-top:1px solid rgba(255,255,255,.15)}`,""]);const b=p}}]);
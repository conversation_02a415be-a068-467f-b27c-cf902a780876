/*! For license information please see vendor.async.aurelia-dialog.5996dbe81b947c137178.chunk.js.LICENSE.txt */
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2185],{1348:(t,e,n)=>{n.r(e),n.d(e,{DialogRenderer:()=>p,UxDialogRenderer:()=>p,hasTransition:()=>h,transitionEvent:()=>g});var o,i,r,a,s,l,d=n(16566),c=n(27884),u="ux-dialog-container",g=function(){if(o)return o;var t=d.dv.createElement("fakeelement"),e={transition:"transitionend",OTransition:"oTransitionEnd",MozTransition:"transitionend",WebkitTransition:"webkitTransitionEnd"};for(var n in e)if(void 0!==t.style[n])return o=e[n];return""},h=(a="transitionDuration",s=["webkitTransitionDuration","oTransitionDuration"],function(t){return i||(i=d.dv.createElement("fakeelement"),r=a in i.style?a:s.find((function(t){return t in i.style}))),!!r&&!!d.dv.getComputedStyle(t)[r].split(",").find((function(t){return!!parseFloat(t)}))}),p=function(){function t(){}return t.keyboardEventHandler=function(e){var n=function(t){return"Escape"===(t.code||t.key)||27===t.keyCode?"Escape":"Enter"===(t.code||t.key)||13===t.keyCode?"Enter":void 0}(e);if(n){var o=t.dialogControllers[t.dialogControllers.length-1];if(o&&o.settings.keyboard){var i=o.settings.keyboard;"Escape"===n&&(!0===i||i===n||Array.isArray(i)&&i.indexOf(n)>-1)?o.cancel():"Enter"===n&&(i===n||Array.isArray(i)&&i.indexOf(n)>-1)&&o.ok()}}},t.trackController=function(e){var n=t.dialogControllers;n.length||d.dv.addEventListener(e.settings.keyEvent||"keyup",t.keyboardEventHandler,!1),n.push(e)},t.untrackController=function(e){var n=t.dialogControllers,o=n.indexOf(e);-1!==o&&n.splice(o,1),n.length||d.dv.removeEventListener(e.settings.keyEvent||"keyup",t.keyboardEventHandler,!1)},t.prototype.getOwnElements=function(t,e){for(var n=t.querySelectorAll(e),o=[],i=0;i<n.length;i++)n[i].parentElement===t&&o.push(n[i]);return o},t.prototype.attach=function(t){t.settings.restoreFocus&&(this.lastActiveElement=d.dv.activeElement);var e=d.dv.createElement("div");e.appendChild(this.anchor);var n=this.dialogContainer=d.dv.createElement(u);n.appendChild(e);var o=this.dialogOverlay=d.dv.createElement("ux-dialog-overlay"),i="number"==typeof t.settings.startingZIndex?t.settings.startingZIndex+"":"auto";o.style.zIndex=i,n.style.zIndex=i;var r=this.host,a=this.getOwnElements(r,u).pop();a&&a.parentElement?(r.insertBefore(n,a.nextSibling),r.insertBefore(o,a.nextSibling)):(r.insertBefore(n,r.firstChild),r.insertBefore(o,r.firstChild)),t.controller.attached(),r.classList.add("ux-dialog-open")},t.prototype.detach=function(e){var n=this.host;n.removeChild(this.dialogOverlay),n.removeChild(this.dialogContainer),e.controller.detached(),t.dialogControllers.length||n.classList.remove("ux-dialog-open"),e.settings.restoreFocus&&e.settings.restoreFocus(this.lastActiveElement)},t.prototype.setAsActive=function(){this.dialogOverlay.classList.add("active"),this.dialogContainer.classList.add("active")},t.prototype.setAsInactive=function(){this.dialogOverlay.classList.remove("active"),this.dialogContainer.classList.remove("active")},t.prototype.setupEventHandling=function(t){this.stopPropagation=function(t){t._aureliaDialogHostClicked=!0},this.closeDialogClick=function(e){t.settings.overlayDismiss&&!e._aureliaDialogHostClicked&&t.cancel()};var e=t.settings.mouseEvent||"click";this.dialogContainer.addEventListener(e,this.closeDialogClick),this.anchor.addEventListener(e,this.stopPropagation)},t.prototype.clearEventHandling=function(t){var e=t.settings.mouseEvent||"click";this.dialogContainer.removeEventListener(e,this.closeDialogClick),this.anchor.removeEventListener(e,this.stopPropagation)},t.prototype.centerDialog=function(){var t=this.dialogContainer.children[0],e=Math.max(d.dv.querySelectorAll("html")[0].clientHeight,window.innerHeight||0);t.style.marginTop=Math.max((e-t.offsetHeight)/2,30)+"px",t.style.marginBottom=Math.max((e-t.offsetHeight)/2,30)+"px"},t.prototype.awaitTransition=function(t,e){var n=this;return new Promise((function(o){var i=n,r=g();e||!h(n.dialogContainer)?o():n.dialogContainer.addEventListener(r,(function t(e){e.target===i.dialogContainer&&(i.dialogContainer.removeEventListener(r,t),o())})),t()}))},t.prototype.getDialogContainer=function(){return this.anchor||(this.anchor=d.dv.createElement("div"))},t.prototype.showDialog=function(e){var n=this;l||(l=d.dv.querySelector("body")),e.settings.host?this.host=e.settings.host:this.host=l;var o=e.settings;return this.attach(e),"function"==typeof o.position?o.position(this.dialogContainer,this.dialogOverlay):o.centerHorizontalOnly||this.centerDialog(),t.trackController(e),this.setupEventHandling(e),this.awaitTransition((function(){return n.setAsActive()}),e.settings.ignoreTransitions)},t.prototype.hideDialog=function(e){var n=this;return this.clearEventHandling(e),t.untrackController(e),this.awaitTransition((function(){return n.setAsInactive()}),e.settings.ignoreTransitions).then((function(){n.detach(e)}))},t.dialogControllers=[],t}();(0,c.do)()(p)},5786:(t,e,n)=>{n.r(e),n.d(e,{NativeDialogRenderer:()=>l});var o,i=n(16566),r=n(27884),a=n(1348),s="dialog",l=function(){function t(){}var e;return e=t,t.keyboardEventHandler=function(t){var n="Enter"===(t.code||t.key)||13===t.keyCode?"Enter":void 0;if(n){var o=e.dialogControllers[e.dialogControllers.length-1];if(o&&o.settings.keyboard){var i=o.settings.keyboard;"Enter"===n&&(i===n||Array.isArray(i)&&i.indexOf(n)>-1)&&o.ok()}}},t.trackController=function(t){e.dialogControllers.length||i.dv.addEventListener("keyup",e.keyboardEventHandler,!1),e.dialogControllers.push(t)},t.untrackController=function(t){var n=e.dialogControllers.indexOf(t);-1!==n&&e.dialogControllers.splice(n,1),e.dialogControllers.length||i.dv.removeEventListener("keyup",e.keyboardEventHandler,!1)},t.prototype.getOwnElements=function(t,e){for(var n=t.querySelectorAll(e),o=[],i=0;i<n.length;i++)n[i].parentElement===t&&o.push(n[i]);return o},t.prototype.attach=function(t){t.settings.restoreFocus&&(this.lastActiveElement=i.dv.activeElement);var e=i.dv.createElement("div");e.appendChild(this.anchor),this.dialogContainer=i.dv.createElement(s),window.dialogPolyfill&&window.dialogPolyfill.registerDialog(this.dialogContainer),this.dialogContainer.appendChild(e);var n=this.getOwnElements(this.host,s).pop();n&&n.parentElement?this.host.insertBefore(this.dialogContainer,n.nextSibling):this.host.insertBefore(this.dialogContainer,this.host.firstChild),t.controller.attached(),this.host.classList.add("ux-dialog-open")},t.prototype.detach=function(t){this.dialogContainer.hasAttribute("open")&&this.dialogContainer.close(),this.host.removeChild(this.dialogContainer),t.controller.detached(),e.dialogControllers.length||this.host.classList.remove("ux-dialog-open"),t.settings.restoreFocus&&t.settings.restoreFocus(this.lastActiveElement)},t.prototype.setAsActive=function(){this.dialogContainer.showModal(),this.dialogContainer.classList.add("active")},t.prototype.setAsInactive=function(){this.dialogContainer.classList.remove("active")},t.prototype.setupEventHandling=function(t){this.stopPropagation=function(t){t._aureliaDialogHostClicked=!0},this.closeDialogClick=function(e){t.settings.overlayDismiss&&!e._aureliaDialogHostClicked&&t.cancel()},this.dialogCancel=function(e){var n=t.settings.keyboard,o="Escape";!0===n||n===o||Array.isArray(n)&&n.indexOf(o)>-1?t.cancel():e.preventDefault()};var e=t.settings.mouseEvent||"click";this.dialogContainer.addEventListener(e,this.closeDialogClick),this.dialogContainer.addEventListener("cancel",this.dialogCancel),this.anchor.addEventListener(e,this.stopPropagation)},t.prototype.clearEventHandling=function(t){var e=t.settings.mouseEvent||"click";this.dialogContainer.removeEventListener(e,this.closeDialogClick),this.dialogContainer.removeEventListener("cancel",this.dialogCancel),this.anchor.removeEventListener(e,this.stopPropagation)},t.prototype.awaitTransition=function(t,e){var n=this;return new Promise((function(o){var i=n,r=(0,a.transitionEvent)();e||!(0,a.hasTransition)(n.dialogContainer)?o():n.dialogContainer.addEventListener(r,(function t(e){e.target===i.dialogContainer&&(i.dialogContainer.removeEventListener(r,t),o())})),t()}))},t.prototype.getDialogContainer=function(){return this.anchor||(this.anchor=i.dv.createElement("div"))},t.prototype.showDialog=function(t){var n=this;o||(o=i.dv.querySelector("body")),t.settings.host?this.host=t.settings.host:this.host=o;var r=t.settings;return this.attach(t),"function"==typeof r.position&&r.position(this.dialogContainer),e.trackController(t),this.setupEventHandling(t),this.awaitTransition((function(){return n.setAsActive()}),t.settings.ignoreTransitions)},t.prototype.hideDialog=function(t){var n=this;return this.clearEventHandling(t),e.untrackController(t),this.awaitTransition((function(){return n.setAsInactive()}),t.settings.ignoreTransitions).then((function(){n.detach(t)}))},t.dialogControllers=[],t=e=function(t,e,n,o){var i,r=arguments.length,a=r<3?e:null===o?o=Object.getOwnPropertyDescriptor(e,n):o;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,n,o);else for(var s=t.length-1;s>=0;s--)(i=t[s])&&(a=(r<3?i(a):r>3?i(e,n,a):i(e,n))||a);return r>3&&a&&Object.defineProperty(e,n,a),a}([(0,r.do)()],t),t}()},6100:(t,e,n)=>{n.r(e),n.d(e,{AttachFocus:()=>i});var o=n(16566),i=function(){function t(t){this.element=t,this.value=!0}return t.inject=function(){return[o.dv.Element]},t.prototype.attached=function(){(""===this.value||this.value&&"false"!==this.value)&&this.element.focus()},t.$resource={type:"attribute",name:"attach-focus"},t}()},8169:(t,e,n)=>{n.r(e),n.d(e,{UxDialog:()=>o});var o=function(){function t(){}return t.$view="<template><slot></slot></template>",t.$resource="ux-dialog",t}()},21899:(t,e,n)=>{n.r(e),n.d(e,{UxDialogBody:()=>o});var o=function(){function t(){}return t.$view="<template><slot></slot></template>",t.$resource="ux-dialog-body",t}()},56494:(t,e,n)=>{n.r(e),n.d(e,{default:()=>o});const o="ux-dialog-overlay{bottom:0;left:0;position:fixed;top:0;right:0;opacity:0}ux-dialog-overlay.active{opacity:1}ux-dialog-container{display:block;transition:opacity .2s linear;opacity:0;overflow-x:hidden;overflow-y:auto;position:fixed;top:0;right:0;bottom:0;left:0;-webkit-overflow-scrolling:touch}ux-dialog-container.active{opacity:1}ux-dialog-container>div{padding:30px}ux-dialog-container>div>div{width:100%;display:block;min-width:300px;width:-moz-fit-content;width:-webkit-fit-content;width:fit-content;height:-moz-fit-content;height:-webkit-fit-content;height:fit-content;margin:auto}ux-dialog-container,ux-dialog-container>div,ux-dialog-container>div>div{outline:0}ux-dialog{width:100%;display:table;box-shadow:0 5px 15px rgba(0,0,0,.5);border:1px solid rgba(0,0,0,.2);border-radius:5px;padding:3;min-width:300px;width:-moz-fit-content;width:-webkit-fit-content;width:fit-content;height:-moz-fit-content;height:-webkit-fit-content;height:fit-content;margin:auto;border-image-source:none;border-image-slice:100%;border-image-width:1;border-image-outset:0;border-image-repeat:initial;background:#fff}ux-dialog>ux-dialog-header{display:block;padding:16px;border-bottom:1px solid #e5e5e5}ux-dialog>ux-dialog-header>button{float:right;border:none;display:block;width:32px;height:32px;background:none;font-size:22px;line-height:16px;margin:-14px -16px 0 0;padding:0;cursor:pointer}ux-dialog>ux-dialog-body{display:block;padding:16px}ux-dialog>ux-dialog-footer{display:block;padding:6px;border-top:1px solid #e5e5e5;text-align:right}ux-dialog>ux-dialog-footer button{color:#333;background-color:#fff;padding:6px 12px;font-size:14px;text-align:center;white-space:nowrap;vertical-align:middle;-ms-touch-action:manipulation;touch-action:manipulation;cursor:pointer;background-image:none;border:1px solid #ccc;border-radius:4px;margin:5px 0 5px 5px}ux-dialog>ux-dialog-footer button:disabled{cursor:default;opacity:.45}ux-dialog>ux-dialog-footer button:hover:enabled{color:#333;background-color:#e6e6e6;border-color:#adadad}.ux-dialog-open{overflow:hidden}"},73878:(t,e,n)=>{n.r(e),n.d(e,{UxDialogHeader:()=>i});var o=n(39671),i=function(){function t(t){this.controller=t}return t.prototype.bind=function(){"boolean"!=typeof this.showCloseButton&&(this.showCloseButton=!this.controller.settings.lock)},t.inject=[o.d],t.$view='<template>\n  <button\n    type="button"\n    class="dialog-close"\n    aria-label="Close"\n    if.bind="showCloseButton"\n    click.trigger="controller.cancel()">\n    <span aria-hidden="true">&times;</span>\n  </button>\n\n  <div class="dialog-header-content">\n    <slot></slot>\n  </div>\n</template>',t.$resource={name:"ux-dialog-header",bindables:["showCloseButton"]},t}()},81272:(t,e,n)=>{n.r(e),n.d(e,{UxDialogFooter:()=>i});var o=n(39671),i=function(){function t(t){this.controller=t,this.buttons=[],this.useDefaultButtons=!1}return t.isCancelButton=function(t){return"Cancel"===t},t.prototype.close=function(e){t.isCancelButton(e)?this.controller.cancel(e):this.controller.ok(e)},t.prototype.useDefaultButtonsChanged=function(t){t&&(this.buttons=["Cancel","Ok"])},t.inject=[o.d],t.$view='<template>\n  <slot></slot>\n  <template if.bind="buttons.length > 0">\n    <button type="button"\n      class="btn btn-default"\n      repeat.for="button of buttons"\n      click.trigger="close(button)">\n      ${button}\n    </button>\n  </template>\n</template>',t.$resource={name:"ux-dialog-footer",bindables:["buttons","useDefaultButtons"]},t}()}}]);
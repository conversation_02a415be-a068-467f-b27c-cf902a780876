"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4235],{"cheats/resources/custom-attributes/steam-client-icon-bg":(e,t,a)=>{a.r(t),a.d(t,{SteamClientIconBgCustomAttribute:()=>r});var n=a(15215),i=a("aurelia-framework"),o=a("shared/api/value-converters"),s=a(38777);let r=class{#e;#t;#a;constructor(e,t){this.#e=e,this.#t=t}bind(){if(this.steamId){const e=this.#t.toView(`/steam_community/${this.steamId}/client_icon/96.webp`),t=document.createElement("img");this.#a=(new s.Vd).pushEventListener(t,"error",(()=>this.#n())).pushEventListener(t,"load",(()=>this.#i(e))),t.src=e}else this.#n()}unbind(){this.#a&&this.#a.dispose()}#n(){this.#e.style.display="none",this.#e.classList.add("is-fallback")}#i(e){this.#e.style.backgroundImage=`url("${e}")`}};(0,n.Cg)([i.bindable,(0,n.Sn)("design:type",String)],r.prototype,"steamId",void 0),r=(0,n.Cg)([(0,i.inject)(Element,o.CdnValueConverter),(0,i.noView)(),(0,n.Sn)("design:paramtypes",[HTMLElement,o.CdnValueConverter])],r)},"cheats/resources/custom-attributes/steam-hero-bg":(e,t,a)=>{a.r(t),a.d(t,{SteamHeroBgCustomAttribute:()=>r});var n=a(15215),i=a("aurelia-framework"),o=a("shared/api/value-converters"),s=a(38777);let r=class{#e;#t;#a;constructor(e,t){this.#e=e,this.#t=t}bind(){if(this.steamId){const e=this.#t.toView(`/steam_apps/${this.steamId}/library_hero.webp`),t=document.createElement("img");this.#a=(new s.Vd).pushEventListener(t,"error",(()=>this.#n())).pushEventListener(t,"load",(()=>this.#i(e))),t.src=e}else this.#n()}unbind(){this.#a&&this.#a.dispose()}#n(){this.#i(this.#t.toView(this.titleThumbnail,{size:460})),this.#e.classList.add("is-fallback")}#i(e){this.#e.style.backgroundImage=`url("${e}")`}};(0,n.Cg)([i.bindable,(0,n.Sn)("design:type",String)],r.prototype,"steamId",void 0),(0,n.Cg)([i.bindable,(0,n.Sn)("design:type",String)],r.prototype,"titleThumbnail",void 0),r=(0,n.Cg)([(0,i.inject)(Element,o.CdnValueConverter),(0,i.noView)(),(0,n.Sn)("design:paramtypes",[HTMLElement,o.CdnValueConverter])],r)},"cheats/resources/elements/add-game-menu":(e,t,a)=>{a.r(t),a.d(t,{AddGameMenu:()=>m});var n=a(15215),i=a("aurelia-framework"),o=a(18776),s=a(79810),r=a(35030),l=a(54995),d=a("shared/utility/resources/value-converters/platform");let m=class{#o;#s;#r;#l;#e;constructor(e,t,a,n,i){this.gameSelectorOpen=!1,this.submenuPosition="left",this.#o=e,this.#s=t,this.#r=a,this.#l=n,this.#e=i}async attached(){this.gameSelectorOpen=!1;try{this.model=await this.#l.watch(this.titleId)}catch{}}detached(){this.#l.unwatch(this.titleId)}handleGameClick(e){const t=this.state.catalog.games[e.id];!e.gameInstalled&&t&&this.#o.installGame(t,"game_selector"),this.#s.navigateToRoute("title",{titleId:t?.titleId,gameId:e.id}),this.open=!1}platformName(e){return this.#r.toView(e.platformId)}toggleGameSelector(){const e=this.#e.getBoundingClientRect();this.submenuPosition=window.innerWidth-e.right<e.width?"left":"right",this.gameSelectorOpen=!this.gameSelectorOpen}};(0,n.Cg)([(0,i.bindable)({defaultBindingMode:i.bindingMode.twoWay}),(0,n.Sn)("design:type",Boolean)],m.prototype,"open",void 0),(0,n.Cg)([(0,i.bindable)({defaultBindingMode:i.bindingMode.toView}),(0,n.Sn)("design:type",String)],m.prototype,"titleId",void 0),m=(0,n.Cg)([(0,l.m6)(),(0,i.autoinject)(),(0,n.Sn)("design:paramtypes",[s.r,o.Ix,d.PlatformNameValueConverter,r.U,Element])],m)},"cheats/resources/elements/add-game-menu.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>n});const n='<template class="au-animate"> <require from="./add-game-menu.scss"></require> <require from="./game-selector-menu"></require> <require from="../../../shared/resources/custom-attributes/close-if-click-outside"></require> <div class="overlay" click.delegate="open = false"></div> <div class="container"> <nav> <button repeat.for="game of model.games" click.delegate="handleGameClick(game)"> <i><inline-svg src.bind="game.platformId | platformIconSvg"></inline-svg></i> <template if.bind="game.gameInstalled"> <span class="label"> ${game.platformId | platformName} <template if.bind="game.edition">${game.edition}</template> </span> <span class="badge" if.bind="game.gameInstalled && !game.customGameInstalled">${\'add_game_menu.installed\' | i18n}</span> <span class="badge" if.bind="game.gameInstalled && game.customGameInstalled">${\'add_game_menu.added\' | i18n}</span> </template> <template else> <span class="label"> ${\'add_game_menu.get_on_$platform\' | i18n:{platform: platformName(game)}} <template if.bind="game.edition">(${game.edition})</template> </span> <span class="free-badge" if.bind="game.tags.includes(\'free\')">${\'add_game_menu.free\' | i18n}</span> <i>arrow_outward</i> </template> </button> </nav> <hr> <nav> <button click.delegate="toggleGameSelector()" class="${gameSelectorOpen ? \'active\' : \'\'}"> <span class="label">${\'add_game_menu.add_game_manually\' | i18n}</span> <i>keyboard_arrow_right</i> </button> </nav> <p>${\'add_game_menu.not_detected_message\' | i18n}</p> </div> <game-selector-menu class="submenu ${submenuPosition}" if.bind="gameSelectorOpen" title-id.bind="titleId" close-if-click-outside.two-way="gameSelectorOpen" show-header.bind="true" open.bind="open"></game-selector-menu> </template> '},"cheats/resources/elements/add-game-menu.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>c});var n=a(31601),i=a.n(n),o=a(76314),s=a.n(o),r=a(4417),l=a.n(r),d=new URL(a(83959),a.b),m=s()(i()),g=l()(d);m.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,add-game-menu>.container>nav button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}add-game-menu{position:relative}add-game-menu>.overlay{position:fixed;top:0;right:0;left:0;bottom:0;background:#0d0f12;opacity:.5;z-index:-1}add-game-menu>.container{display:flex;flex-direction:column;justify-content:flex-start;padding:8px;position:relative;width:280px;background:rgba(128,128,128,.1);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:16px}add-game-menu>.container>nav{display:flex;flex-direction:column;gap:1px}add-game-menu>.container>nav button{display:flex;align-items:center;gap:8px;padding:8px;background:rgba(0,0,0,0);border:none;transition:background-color .15s;border-radius:8px;text-align:left}add-game-menu>.container>nav button:hover{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}add-game-menu>.container>nav button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;width:20px;height:20px;display:inline-flex;align-items:center;justify-content:center;color:var(--menu__item__icon--color, rgba(255, 255, 255, 0.6));transition:color .15s}add-game-menu>.container>nav button .label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.6));padding:2px 0;transition:color .15s;font-size:14px;line-height:20px;font-weight:700;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.8))}add-game-menu>.container>nav button .badge{font-weight:700;flex:0 0 auto;font-size:10px;line-height:16px;text-transform:uppercase;color:var(--menu__item__badge--color, rgba(255, 255, 255, 0.8));transition:color .15s;background:var(--menu__item__badge--background-color, rgba(255, 255, 255, 0.1));padding:0 4px;border-radius:4px}add-game-menu>.container>nav button .free-badge{font-weight:700;flex:0 0 auto;font-size:10px;line-height:16px;text-transform:uppercase;color:var(--menu__item__badge--color, rgba(255, 255, 255, 0.8));transition:color .15s;background:var(--menu__item__badge--background-color, rgba(255, 255, 255, 0.1));padding:0 4px;border-radius:4px;--menu__item__badge--color: var(--color--accent);--menu__item__badge--background-color: rgba(var(--color--accent--rgb), 0.1)}add-game-menu>.container>nav button.active{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}add-game-menu>.container>hr{margin:12px -8px;padding:0;border:none;border-top:1px solid rgba(255,255,255,.15)}add-game-menu>.container>p{font-weight:500;font-size:14px;line-height:20px;color:rgba(255,255,255,.6);margin:0;padding:8px}add-game-menu>.submenu{position:absolute;top:0}add-game-menu>.submenu.left{right:calc(100% + 8px)}add-game-menu>.submenu.right{left:calc(100% + 8px)}`,""]);const c=m}}]);
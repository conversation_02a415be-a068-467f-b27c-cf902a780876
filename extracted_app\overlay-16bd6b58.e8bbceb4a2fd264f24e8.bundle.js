"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1614,4009],{"shared/cheats/resources/elements/mods-list.html":(t,e,o)=>{o.r(e),o.d(e,{default:()=>i});const i='<template class="${isRemote ? \'mobile\' : \'\'} ${trainer.blueprint.notes ? \'has-notes\' : \'\'} ${showHotkeys ? \'show-hotkeys\' : \'\'} ${showInputs ? \'show-inputs\' : \'\'}"> <require from="./mods-list.scss"></require> <require from="./mod-timer"></require> <require from="./number-input"></require> <require from="./save-cheats-icon"></require> <require from="./selection-input"></require> <require from="./trainer-hotkey"></require> <require from="./pin-mod-icon"></require> <require from="../value-converters/blueprint-translation"></require> <require from="../value-converters/group-cheats"></require> <require from="../../../resources/elements/range-input"></require> <require from="../../../resources/elements/sheet"></require> <require from="../../../resources/elements/toggle.html"></require> <require from="../../../resources/elements/tooltip"></require> <div class="category ${category.category}" repeat.for="category of trainer.blueprint.cheats | groupCheats | extractChallengeMods | removePrecisionMods" id="${category.category}"> <div class="category-header" click.delegate="handleCategoryCollapse(category.category)"> <i class="icon ${getCategoryIcon(category.category)}"></i> <span class="label">${\'trainer_cheats_list.category_\' + category.category | i18n}</span> <i class="icon reset-icon" if.bind="category.category === \'pinned\' && resetAutoPins" click.delegate="handleResetPins($event)"></i> <i class="icon collapse-category"></i> </div> <div class="cheats"> <div repeat.for="cheat of category.values" class="cheats-wrapper ${precisionModsSectionsOpen[getModSectionName(cheat.uuid, category.category)] ? \'precision-mods-open\' : \'\'}"> <compose view="./mod-row.html" class="mod-row"></compose> <div if.bind="childMods[cheat.uuid].length" class="precision-mods-list"> <compose class="mod-row" repeat.for="cheat of childMods[cheat.uuid]" view="./mod-row.html"></compose> </div> </div> </div> </div> <sheet if.bind="isRemote" open.bind="isTimerPaneOpen" config.one-way="timerPaneConfig" on-close.call="handleSheetClosed()"> <div class="mod-timer-sheet"> <span class="mod-timer-sheet-title">${\'trainer_cheats_list.set_timer\' | i18n}</span> <span class="mod-timer-sheet-subtitle">${selectedCheatConfig.cheat.name | blueprintTranslation:translations}</span> <div class="mod-timer-sheet-content"> <mod-timer content-only="true" trainer.bind="trainer" cheat.bind="selectedCheatConfig.cheat" category.bind="selectedCheatConfig.category" cheat-states.bind="cheatStates" mod-timers.bind="modTimers" mod-timer-messages-dismissed.bind="modTimerMessagesDismissed" is-pro.bind="isPro" on-timer-change.call="handleModTimerChange($event)" on-timer-expired.call="handleModTimerExpired(cheat, loopSequence)" handle-dismiss-message.call="handleDismissMessage(timerType)" active-intervals.bind="activeIntervals" loop-phase-states.bind="loopPhaseStates" ea-parent.bind="ea"></mod-timer> </div> </div> </sheet> </template> '},"shared/cheats/resources/elements/mods-list.scss":(t,e,o)=>{o.r(e),o.d(e,{default:()=>b});var i=o(31601),r=o.n(i),a=o(76314),s=o.n(a),n=o(4417),l=o.n(n),d=new URL(o(83959),o.b),c=new URL(o(54835),o.b),h=s()(r()),m=l()(d),p=l()(c);h.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.theme-default tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}.theme-purple-pro tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}.theme-green-pro tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}.theme-orange-pro tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}.theme-pro tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}tooltip.alert .tooltip [slot=content]{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}tooltip.alert .tooltip [slot=content],tooltip.alert .tooltip [slot=content] a{color:rgba(var(--color--alert--rgb), 0.9)}tooltip.alert .tooltip .tooltip-arrow{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}tooltip.info .tooltip .tooltip-content{max-width:300px;width:max-content}.custom-tooltip .tooltip [slot=content]{margin:0 !important;padding:0 !important;overflow:hidden;display:block !important}.tooltip{position:absolute;opacity:0;visibility:hidden;transition:visiblity 0s linear .15s,opacity .15s,transform .15s,margin .15s;z-index:99}.tooltip .tooltip-content{border:1px solid rgba(255,255,255,.05);border-radius:10px;position:relative;text-align:left;display:block}.theme-default .tooltip .tooltip-content{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro .tooltip .tooltip-content{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro .tooltip .tooltip-content{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro .tooltip .tooltip-content{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro .tooltip .tooltip-content{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.tooltip .tooltip-content>*+*{margin-left:0}.tooltip [slot=content]{border-radius:10px;border:1px solid rgba(255,255,255,.05);padding:11px 15px;text-align:left;display:inline-flex;align-items:center;border:0 !important;position:relative;z-index:1}.theme-default .tooltip [slot=content]{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro .tooltip [slot=content]{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro .tooltip [slot=content]{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro .tooltip [slot=content]{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro .tooltip [slot=content]{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.tooltip [slot=content]>*+*{margin-left:9px}.tooltip [slot=content],.tooltip [slot=content] a{font-size:14px;line-height:21px;line-height:19px;font-weight:500;color:rgba(255,255,255,.5)}.tooltip [slot=content] strong,.tooltip [slot=content] em{font-weight:700;color:#fff;font-style:normal}.tooltip [slot=content] p{margin:0;padding:0}.tooltip [slot=content] p+p{margin-top:10px}.tooltip .tooltip-arrow{display:block;width:20px;height:20px;position:absolute;border-radius:2px;border:1px solid rgba(255,255,255,.05);transform:rotate(45deg)}.theme-default .tooltip .tooltip-arrow{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro .tooltip .tooltip-arrow{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro .tooltip .tooltip-arrow{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro .tooltip .tooltip-arrow{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro .tooltip .tooltip-arrow{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.tooltip.arrow-left-top{left:100%;top:calc(50% - 24px)}.tooltip.arrow-left-top .tooltip-arrow{left:-2px;top:10px}.tooltip.arrow-left-center{left:100%;top:50%}.tooltip.arrow-left-center .tooltip-arrow{left:-5px;top:50%;margin-top:-10px}.tooltip.arrow-right-center{right:100%;top:50%}.tooltip.arrow-right-center .tooltip-arrow{right:-5px;top:50%;margin-top:-10px}.tooltip.arrow-right-top{right:100%;top:calc(50% - 24px)}.tooltip.arrow-right-top .tooltip-arrow{right:-5px;top:10px}.tooltip.arrow-top-center{top:100%;left:50%}.tooltip.arrow-top-center .tooltip-arrow{left:50%;top:-5px;margin-left:-10px}.tooltip.arrow-bottom-center{bottom:100%;left:50%}.tooltip.arrow-bottom-center .tooltip-arrow{left:50%;bottom:-5px;margin-left:-10px}.tooltip.arrow-top-left{top:100%;left:50%;margin-left:-25px}.tooltip.arrow-top-left .tooltip-arrow{left:14px;top:-5px}.tooltip.arrow-bottom-left{bottom:100%;left:50%;margin-left:-25px}.tooltip.arrow-bottom-left .tooltip-arrow{left:14px;bottom:-5px}.tooltip.arrow-top-right{top:100%;right:50%;margin-right:-25px}.tooltip.arrow-top-right .tooltip-arrow{right:14px;top:-5px}.tooltip.arrow-bottom-right{bottom:100%;right:50%;margin-right:-25px}.tooltip.arrow-bottom-right .tooltip-arrow{right:14px;bottom:-5px}*:hover>.tooltip:not(.hidden),*:hover>tooltip .tooltip:not(.hidden),.tooltip.visible{opacity:1;visibility:visible;transition-delay:0s}*:hover>.tooltip:not(.hidden).arrow-left-top,*:hover>.tooltip:not(.hidden).arrow-left-center,*:hover>tooltip .tooltip:not(.hidden).arrow-left-top,*:hover>tooltip .tooltip:not(.hidden).arrow-left-center,.tooltip.visible.arrow-left-top,.tooltip.visible.arrow-left-center{margin-left:10px}*:hover>.tooltip:not(.hidden).arrow-right-top,*:hover>.tooltip:not(.hidden).arrow-right-center,*:hover>tooltip .tooltip:not(.hidden).arrow-right-top,*:hover>tooltip .tooltip:not(.hidden).arrow-right-center,.tooltip.visible.arrow-right-top,.tooltip.visible.arrow-right-center{margin-right:10px}*:hover>.tooltip:not(.hidden).arrow-top-center,*:hover>.tooltip:not(.hidden).arrow-top-left,*:hover>.tooltip:not(.hidden).arrow-top-right,*:hover>tooltip .tooltip:not(.hidden).arrow-top-center,*:hover>tooltip .tooltip:not(.hidden).arrow-top-left,*:hover>tooltip .tooltip:not(.hidden).arrow-top-right,.tooltip.visible.arrow-top-center,.tooltip.visible.arrow-top-left,.tooltip.visible.arrow-top-right{margin-top:10px}*:hover>.tooltip:not(.hidden).arrow-bottom-left,*:hover>.tooltip:not(.hidden).arrow-bottom-center,*:hover>.tooltip:not(.hidden).arrow-bottom-right,*:hover>tooltip .tooltip:not(.hidden).arrow-bottom-left,*:hover>tooltip .tooltip:not(.hidden).arrow-bottom-center,*:hover>tooltip .tooltip:not(.hidden).arrow-bottom-right,.tooltip.visible.arrow-bottom-left,.tooltip.visible.arrow-bottom-center,.tooltip.visible.arrow-bottom-right{margin-bottom:10px}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,mods-list .category.collapsed .category-header .icon.collapse-category,trainer-mods-list-placeholder .category.collapsed .category-header .icon.collapse-category,mods-list .category .category-header .icon.cheats-icon,trainer-mods-list-placeholder .category .category-header .icon.cheats-icon,mods-list .category .category-header .icon.pinned-icon,trainer-mods-list-placeholder .category .category-header .icon.pinned-icon,mods-list .category .category-header .icon.enemies-icon,trainer-mods-list-placeholder .category .category-header .icon.enemies-icon,mods-list .category .category-header .icon.game-icon,trainer-mods-list-placeholder .category .category-header .icon.game-icon,mods-list .category .category-header .icon.inventory-icon,trainer-mods-list-placeholder .category .category-header .icon.inventory-icon,mods-list .category .category-header .icon.physics-icon,trainer-mods-list-placeholder .category .category-header .icon.physics-icon,mods-list .category .category-header .icon.player-icon,trainer-mods-list-placeholder .category .category-header .icon.player-icon,mods-list .category .category-header .icon.stats-icon,trainer-mods-list-placeholder .category .category-header .icon.stats-icon,mods-list .category .category-header .icon.teleport-icon,trainer-mods-list-placeholder .category .category-header .icon.teleport-icon,mods-list .category .category-header .icon.vehicles-icon,trainer-mods-list-placeholder .category .category-header .icon.vehicles-icon,mods-list .category .category-header .icon.weapons-icon,trainer-mods-list-placeholder .category .category-header .icon.weapons-icon,mods-list .category .category-header .icon.challenge-icon,trainer-mods-list-placeholder .category .category-header .icon.challenge-icon,mods-list .category .category-header .icon.reset-icon,trainer-mods-list-placeholder .category .category-header .icon.reset-icon,mods-list .category .category-header .icon.collapse-category,trainer-mods-list-placeholder .category .category-header .icon.collapse-category,mods-list .cheats .precision-mods-button-icon.precision,mods-list .cheats .mod-timer-button-icon.precision,trainer-mods-list-placeholder .cheats .precision-mods-button-icon.precision,trainer-mods-list-placeholder .cheats .mod-timer-button-icon.precision,mods-list .cheats .precision-mods-button-icon.timer,mods-list .cheats .mod-timer-button-icon.timer,trainer-mods-list-placeholder .cheats .precision-mods-button-icon.timer,trainer-mods-list-placeholder .cheats .mod-timer-button-icon.timer,mods-list .cheats .precision-mods-button.timer-active .icon-loop,mods-list .cheats .mod-timer-button.timer-active .icon-loop,trainer-mods-list-placeholder .cheats .precision-mods-button.timer-active .icon-loop,trainer-mods-list-placeholder .cheats .mod-timer-button.timer-active .icon-loop,mods-list .cheats .cheat .mod-disabled-hint .mod-disabled-info-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .mod-disabled-info-icon,mods-list .cheats .cheat .mod-disabled-hint.add-game .mod-disabled-hint-icon,mods-list .cheats .cheat .mod-disabled-hint.install-for-free .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.add-game .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.install-for-free .mod-disabled-hint-icon,mods-list .cheats .cheat .mod-disabled-hint.play .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.play .mod-disabled-hint-icon,mods-list .cheats .cheat .mod-broken-hint .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .mod-disabled-hint-icon,mods-list .cheats .cheat.mod-broken .broken-icon,trainer-mods-list-placeholder .cheats .cheat.mod-broken .broken-icon,mods-list .cheats .cheat .info-icon i.icon,trainer-mods-list-placeholder .cheats .cheat .info-icon i.icon,mods-list.mobile .mod-row .cheat .swipe-menu-btn-icon,.mod-timer-tooltip-content-container .pro-overlay .join-now-text .icon-arrow-forward,.mod-timer-tooltip .icon-loop{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}mods-list,trainer-mods-list-placeholder{display:block}mods-list .category,trainer-mods-list-placeholder .category{display:flex;flex-direction:column;gap:1px;margin-bottom:16px;width:100%;position:relative;border-radius:16px}mods-list .category:last-of-type .category-header .spacer,trainer-mods-list-placeholder .category:last-of-type .category-header .spacer{margin-bottom:0}mods-list .category:last-of-type .cheats,trainer-mods-list-placeholder .category:last-of-type .cheats{margin-bottom:0;padding-bottom:0}mods-list .category:last-of-type .cheats:before,trainer-mods-list-placeholder .category:last-of-type .cheats:before{display:none}mods-list .category.collapsed .category-header,trainer-mods-list-placeholder .category.collapsed .category-header{border-radius:16px;background:rgba(var(--mods-theme-bg-color--rgb, var(--theme--background-accent--rgb)), var(--mods-collapsed-category-header-bg-opacity, 0.9))}mods-list .category.collapsed .category-header .icon.collapse-category,trainer-mods-list-placeholder .category.collapsed .category-header .icon.collapse-category{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category.collapsed .category-header .icon.collapse-category:before,trainer-mods-list-placeholder .category.collapsed .category-header .icon.collapse-category:before{font-family:inherit;content:"unfold_more"}mods-list .category.collapsed .cheats,trainer-mods-list-placeholder .category.collapsed .cheats{display:none}mods-list .category.challenge,trainer-mods-list-placeholder .category.challenge{--mods-theme-color: #ff7324;--mods-theme-color--rgb: 255, 115, 36;--mods-theme-text-color: #ffe3d3;--mods-theme-bg-color--rgb: 32, 25, 25}mods-list .category .category-header,trainer-mods-list-placeholder .category .category-header{position:relative;background:rgba(var(--mods-theme-bg-color--rgb, var(--theme--background-accent--rgb)), var(--mods-category-header-bg-opacity, 0.4));height:40px;padding:10px 18px 10px 12px;border-top-left-radius:16px;border-top-right-radius:16px;display:flex;flex-direction:row;align-items:center;gap:12px;z-index:0}mods-list .category .category-header:hover,trainer-mods-list-placeholder .category .category-header:hover{cursor:pointer}mods-list .category .category-header:hover *,trainer-mods-list-placeholder .category .category-header:hover *{cursor:pointer}mods-list .category .category-header:hover:before,trainer-mods-list-placeholder .category .category-header:hover:before{content:"";position:absolute;top:0;left:0;background:rgba(255,255,255,.025);width:100%;height:100%;border-radius:inherit}mods-list .category .category-header:hover .label,trainer-mods-list-placeholder .category .category-header:hover .label{color:#fff}mods-list .category .category-header .label,trainer-mods-list-placeholder .category .category-header .label{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700;color:var(--mods-theme-text-color, rgba(255, 255, 255, 0.8));text-transform:capitalize}mods-list .category .category-header .icon,trainer-mods-list-placeholder .category .category-header .icon{font-variation-settings:"FILL" 1,"wght" 400 !important;align-items:center;color:var(--mods-theme-color, rgba(255, 255, 255, 0.6));display:flex;flex:0;font-size:16px !important;margin:12px 0;position:relative}mods-list .category .category-header .icon.cheats-icon,trainer-mods-list-placeholder .category .category-header .icon.cheats-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.cheats-icon:before,trainer-mods-list-placeholder .category .category-header .icon.cheats-icon:before{font-family:inherit;content:"person"}mods-list .category .category-header .icon.pinned-icon,trainer-mods-list-placeholder .category .category-header .icon.pinned-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.pinned-icon:before,trainer-mods-list-placeholder .category .category-header .icon.pinned-icon:before{font-family:inherit;content:"keep"}mods-list .category .category-header .icon.enemies-icon,trainer-mods-list-placeholder .category .category-header .icon.enemies-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.enemies-icon:before,trainer-mods-list-placeholder .category .category-header .icon.enemies-icon:before{font-family:inherit;content:"heart_broken"}mods-list .category .category-header .icon.game-icon,trainer-mods-list-placeholder .category .category-header .icon.game-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.game-icon:before,trainer-mods-list-placeholder .category .category-header .icon.game-icon:before{font-family:inherit;content:"landscape_2"}mods-list .category .category-header .icon.inventory-icon,trainer-mods-list-placeholder .category .category-header .icon.inventory-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.inventory-icon:before,trainer-mods-list-placeholder .category .category-header .icon.inventory-icon:before{font-family:inherit;content:"browse"}mods-list .category .category-header .icon.physics-icon,trainer-mods-list-placeholder .category .category-header .icon.physics-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.physics-icon:before,trainer-mods-list-placeholder .category .category-header .icon.physics-icon:before{font-family:inherit;content:"directions_run"}mods-list .category .category-header .icon.player-icon,trainer-mods-list-placeholder .category .category-header .icon.player-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.player-icon:before,trainer-mods-list-placeholder .category .category-header .icon.player-icon:before{font-family:inherit;content:"person"}mods-list .category .category-header .icon.stats-icon,trainer-mods-list-placeholder .category .category-header .icon.stats-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.stats-icon:before,trainer-mods-list-placeholder .category .category-header .icon.stats-icon:before{font-family:inherit;content:"bar_chart_4_bars"}mods-list .category .category-header .icon.teleport-icon,trainer-mods-list-placeholder .category .category-header .icon.teleport-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.teleport-icon:before,trainer-mods-list-placeholder .category .category-header .icon.teleport-icon:before{font-family:inherit;content:"assistant_navigation"}mods-list .category .category-header .icon.vehicles-icon,trainer-mods-list-placeholder .category .category-header .icon.vehicles-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.vehicles-icon:before,trainer-mods-list-placeholder .category .category-header .icon.vehicles-icon:before{font-family:inherit;content:"directions_car"}mods-list .category .category-header .icon.weapons-icon,trainer-mods-list-placeholder .category .category-header .icon.weapons-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.weapons-icon:before,trainer-mods-list-placeholder .category .category-header .icon.weapons-icon:before{font-family:inherit;content:"swords"}mods-list .category .category-header .icon.challenge-icon,trainer-mods-list-placeholder .category .category-header .icon.challenge-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .category .category-header .icon.challenge-icon:before,trainer-mods-list-placeholder .category .category-header .icon.challenge-icon:before{font-family:inherit;content:"local_fire_department"}mods-list .category .category-header .icon.reset-icon,trainer-mods-list-placeholder .category .category-header .icon.reset-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:rgba(255,255,255,.3);margin-left:-8px;border-radius:8px;padding:4px;transition:all .15s}mods-list .category .category-header .icon.reset-icon:before,trainer-mods-list-placeholder .category .category-header .icon.reset-icon:before{font-family:inherit;content:"restore"}mods-list .category .category-header .icon.reset-icon:hover,trainer-mods-list-placeholder .category .category-header .icon.reset-icon:hover{color:rgba(255,255,255,.25);background-color:rgba(255,255,255,.05)}mods-list .category .category-header .icon.collapse-category,trainer-mods-list-placeholder .category .category-header .icon.collapse-category{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:rgba(255,255,255,.3);margin-left:auto}mods-list .category .category-header .icon.collapse-category:before,trainer-mods-list-placeholder .category .category-header .icon.collapse-category:before{font-family:inherit;content:"unfold_less"}mods-list .cheats,trainer-mods-list-placeholder .cheats{background:rgba(var(--mods-theme-bg-color--rgb, var(--theme--background-accent--rgb)), 0.4);padding:0px;border-bottom-left-radius:16px;border-bottom-right-radius:16px;flex:1;min-width:0;margin-bottom:0px;position:relative}mods-list .cheats .cheats-wrapper,trainer-mods-list-placeholder .cheats .cheats-wrapper{display:flex;flex-direction:column}mods-list .cheats .cheats-wrapper .cheat:not(:hover) pin-mod-icon:not(.active),trainer-mods-list-placeholder .cheats .cheats-wrapper .cheat:not(:hover) pin-mod-icon:not(.active){visibility:hidden}mods-list .cheats .mod-row-controls,trainer-mods-list-placeholder .cheats .mod-row-controls{margin-left:-14px;display:flex;flex-direction:row;align-items:center;gap:8px}@container (max-width: 500px){mods-list .cheats .precision-mods .precision-mods-button-text,mods-list .cheats .precision-mods .mod-timer-button-text,mods-list .cheats .mod-timer .precision-mods-button-text,mods-list .cheats .mod-timer .mod-timer-button-text,trainer-mods-list-placeholder .cheats .precision-mods .precision-mods-button-text,trainer-mods-list-placeholder .cheats .precision-mods .mod-timer-button-text,trainer-mods-list-placeholder .cheats .mod-timer .precision-mods-button-text,trainer-mods-list-placeholder .cheats .mod-timer .mod-timer-button-text{display:none !important}}mods-list .cheats .precision-mods-button-container,mods-list .cheats .mod-timer-button-container,trainer-mods-list-placeholder .cheats .precision-mods-button-container,trainer-mods-list-placeholder .cheats .mod-timer-button-container{position:relative}mods-list .cheats .precision-mods-button,mods-list .cheats .mod-timer-button,trainer-mods-list-placeholder .cheats .precision-mods-button,trainer-mods-list-placeholder .cheats .mod-timer-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;position:relative;color:rgba(255,255,255,.8);background:rgba(255,255,255,.05);font-size:12px;line-height:12px;border-radius:8px;padding:6px;gap:4px;box-shadow:none;justify-content:center;align-items:center;overflow:hidden}mods-list .cheats .precision-mods-button,mods-list .cheats .precision-mods-button *,mods-list .cheats .mod-timer-button,mods-list .cheats .mod-timer-button *,trainer-mods-list-placeholder .cheats .precision-mods-button,trainer-mods-list-placeholder .cheats .precision-mods-button *,trainer-mods-list-placeholder .cheats .mod-timer-button,trainer-mods-list-placeholder .cheats .mod-timer-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) mods-list .cheats .precision-mods-button,body:not(.override-contrast-mode) mods-list .cheats .mod-timer-button,body:not(.override-contrast-mode) trainer-mods-list-placeholder .cheats .precision-mods-button,body:not(.override-contrast-mode) trainer-mods-list-placeholder .cheats .mod-timer-button{border:1px solid #fff}}mods-list .cheats .precision-mods-button>*,mods-list .cheats .mod-timer-button>*,trainer-mods-list-placeholder .cheats .precision-mods-button>*,trainer-mods-list-placeholder .cheats .mod-timer-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}mods-list .cheats .precision-mods-button>*:first-child,mods-list .cheats .mod-timer-button>*:first-child,trainer-mods-list-placeholder .cheats .precision-mods-button>*:first-child,trainer-mods-list-placeholder .cheats .mod-timer-button>*:first-child{padding-left:0}mods-list .cheats .precision-mods-button>*:last-child,mods-list .cheats .mod-timer-button>*:last-child,trainer-mods-list-placeholder .cheats .precision-mods-button>*:last-child,trainer-mods-list-placeholder .cheats .mod-timer-button>*:last-child{padding-right:0}mods-list .cheats .precision-mods-button svg,mods-list .cheats .mod-timer-button svg,trainer-mods-list-placeholder .cheats .precision-mods-button svg,trainer-mods-list-placeholder .cheats .mod-timer-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) mods-list .cheats .precision-mods-button svg *,body:not(.override-contrast-mode) mods-list .cheats .mod-timer-button svg *,body:not(.override-contrast-mode) trainer-mods-list-placeholder .cheats .precision-mods-button svg *,body:not(.override-contrast-mode) trainer-mods-list-placeholder .cheats .mod-timer-button svg *{fill:CanvasText}}mods-list .cheats .precision-mods-button svg *,mods-list .cheats .mod-timer-button svg *,trainer-mods-list-placeholder .cheats .precision-mods-button svg *,trainer-mods-list-placeholder .cheats .mod-timer-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) mods-list .cheats .precision-mods-button svg,body:not(.override-contrast-mode) mods-list .cheats .mod-timer-button svg,body:not(.override-contrast-mode) trainer-mods-list-placeholder .cheats .precision-mods-button svg,body:not(.override-contrast-mode) trainer-mods-list-placeholder .cheats .mod-timer-button svg{opacity:1}}mods-list .cheats .precision-mods-button img,mods-list .cheats .mod-timer-button img,trainer-mods-list-placeholder .cheats .precision-mods-button img,trainer-mods-list-placeholder .cheats .mod-timer-button img{height:50%}mods-list .cheats .precision-mods-button:disabled,mods-list .cheats .mod-timer-button:disabled,trainer-mods-list-placeholder .cheats .precision-mods-button:disabled,trainer-mods-list-placeholder .cheats .mod-timer-button:disabled{opacity:.3}mods-list .cheats .precision-mods-button:disabled,mods-list .cheats .precision-mods-button:disabled *,mods-list .cheats .mod-timer-button:disabled,mods-list .cheats .mod-timer-button:disabled *,trainer-mods-list-placeholder .cheats .precision-mods-button:disabled,trainer-mods-list-placeholder .cheats .precision-mods-button:disabled *,trainer-mods-list-placeholder .cheats .mod-timer-button:disabled,trainer-mods-list-placeholder .cheats .mod-timer-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){mods-list .cheats .precision-mods-button:not(:disabled):hover,mods-list .cheats .mod-timer-button:not(:disabled):hover,trainer-mods-list-placeholder .cheats .precision-mods-button:not(:disabled):hover,trainer-mods-list-placeholder .cheats .mod-timer-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}mods-list .cheats .precision-mods-button:not(:disabled):hover svg,mods-list .cheats .mod-timer-button:not(:disabled):hover svg,trainer-mods-list-placeholder .cheats .precision-mods-button:not(:disabled):hover svg,trainer-mods-list-placeholder .cheats .mod-timer-button:not(:disabled):hover svg{opacity:1}}mods-list .cheats .precision-mods-button:not(:disabled):active,mods-list .cheats .mod-timer-button:not(:disabled):active,trainer-mods-list-placeholder .cheats .precision-mods-button:not(:disabled):active,trainer-mods-list-placeholder .cheats .mod-timer-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}mods-list .cheats .precision-mods-button.shimmer,mods-list .cheats .mod-timer-button.shimmer,trainer-mods-list-placeholder .cheats .precision-mods-button.shimmer,trainer-mods-list-placeholder .cheats .mod-timer-button.shimmer{position:relative}mods-list .cheats .precision-mods-button.shimmer::after,mods-list .cheats .mod-timer-button.shimmer::after,trainer-mods-list-placeholder .cheats .precision-mods-button.shimmer::after,trainer-mods-list-placeholder .cheats .mod-timer-button.shimmer::after{content:"";position:absolute;height:100%;width:100%;top:0;left:0;pointer-events:none;background:linear-gradient(96deg, rgba(255, 255, 255, 0) 20.4%, rgba(255, 255, 255, 0.08) 46.92%, rgba(255, 255, 255, 0) 73.43%);animation:1s ease-in-out 2s infinite precision-mods-shimmer;transform:translateX(-100%)}@keyframes precision-mods-shimmer{from{transform:translateX(-100%)}to{transform:translateX(100%)}}mods-list .cheats .precision-mods-button:hover,mods-list .cheats .precision-mods-button:focus-visible,mods-list .cheats .mod-timer-button:hover,mods-list .cheats .mod-timer-button:focus-visible,trainer-mods-list-placeholder .cheats .precision-mods-button:hover,trainer-mods-list-placeholder .cheats .precision-mods-button:focus-visible,trainer-mods-list-placeholder .cheats .mod-timer-button:hover,trainer-mods-list-placeholder .cheats .mod-timer-button:focus-visible{background:rgba(255,255,255,.2) !important}mods-list .cheats .precision-mods-button:hover .precision-mods-button-text,mods-list .cheats .precision-mods-button:hover .mod-timer-button-text,mods-list .cheats .precision-mods-button:focus-visible .precision-mods-button-text,mods-list .cheats .precision-mods-button:focus-visible .mod-timer-button-text,mods-list .cheats .mod-timer-button:hover .precision-mods-button-text,mods-list .cheats .mod-timer-button:hover .mod-timer-button-text,mods-list .cheats .mod-timer-button:focus-visible .precision-mods-button-text,mods-list .cheats .mod-timer-button:focus-visible .mod-timer-button-text,trainer-mods-list-placeholder .cheats .precision-mods-button:hover .precision-mods-button-text,trainer-mods-list-placeholder .cheats .precision-mods-button:hover .mod-timer-button-text,trainer-mods-list-placeholder .cheats .precision-mods-button:focus-visible .precision-mods-button-text,trainer-mods-list-placeholder .cheats .precision-mods-button:focus-visible .mod-timer-button-text,trainer-mods-list-placeholder .cheats .mod-timer-button:hover .precision-mods-button-text,trainer-mods-list-placeholder .cheats .mod-timer-button:hover .mod-timer-button-text,trainer-mods-list-placeholder .cheats .mod-timer-button:focus-visible .precision-mods-button-text,trainer-mods-list-placeholder .cheats .mod-timer-button:focus-visible .mod-timer-button-text{display:inline-block;text-transform:uppercase}mods-list .cheats .precision-mods-button-icon,mods-list .cheats .mod-timer-button-icon,trainer-mods-list-placeholder .cheats .precision-mods-button-icon,trainer-mods-list-placeholder .cheats .mod-timer-button-icon{padding:0;font-size:16px !important;width:16px;height:16px}mods-list .cheats .precision-mods-button-icon.precision,mods-list .cheats .mod-timer-button-icon.precision,trainer-mods-list-placeholder .cheats .precision-mods-button-icon.precision,trainer-mods-list-placeholder .cheats .mod-timer-button-icon.precision{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}mods-list .cheats .precision-mods-button-icon.precision:before,mods-list .cheats .mod-timer-button-icon.precision:before,trainer-mods-list-placeholder .cheats .precision-mods-button-icon.precision:before,trainer-mods-list-placeholder .cheats .mod-timer-button-icon.precision:before{font-family:inherit;content:"page_info"}mods-list .cheats .precision-mods-button-icon.timer,mods-list .cheats .mod-timer-button-icon.timer,trainer-mods-list-placeholder .cheats .precision-mods-button-icon.timer,trainer-mods-list-placeholder .cheats .mod-timer-button-icon.timer{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .cheats .precision-mods-button-icon.timer:before,mods-list .cheats .mod-timer-button-icon.timer:before,trainer-mods-list-placeholder .cheats .precision-mods-button-icon.timer:before,trainer-mods-list-placeholder .cheats .mod-timer-button-icon.timer:before{font-family:inherit;content:"schedule"}mods-list .cheats .precision-mods-button-text,mods-list .cheats .mod-timer-button-text,trainer-mods-list-placeholder .cheats .precision-mods-button-text,trainer-mods-list-placeholder .cheats .mod-timer-button-text{font-style:italic;font-weight:900;letter-spacing:-0.5px;display:none;padding:0}mods-list .cheats .precision-mods-button-open,mods-list .cheats .precision-mods-button.active,mods-list .cheats .mod-timer-button-open,mods-list .cheats .mod-timer-button.active,trainer-mods-list-placeholder .cheats .precision-mods-button-open,trainer-mods-list-placeholder .cheats .precision-mods-button.active,trainer-mods-list-placeholder .cheats .mod-timer-button-open,trainer-mods-list-placeholder .cheats .mod-timer-button.active{background:var(--theme--highlight-darker)}mods-list .cheats .precision-mods-button-indicator,mods-list .cheats .mod-timer-button-indicator,trainer-mods-list-placeholder .cheats .precision-mods-button-indicator,trainer-mods-list-placeholder .cheats .mod-timer-button-indicator{position:absolute;top:-1px;right:-2px;width:6px;height:6px;border-radius:100%;background-color:var(--color--warning);padding:0}mods-list .cheats .precision-mods-button.timer-active,mods-list .cheats .mod-timer-button.timer-active,trainer-mods-list-placeholder .cheats .precision-mods-button.timer-active,trainer-mods-list-placeholder .cheats .mod-timer-button.timer-active{background:var(--theme--highlight-darker);min-width:42px}mods-list .cheats .precision-mods-button.timer-active .timer-cancel,mods-list .cheats .mod-timer-button.timer-active .timer-cancel,trainer-mods-list-placeholder .cheats .precision-mods-button.timer-active .timer-cancel,trainer-mods-list-placeholder .cheats .mod-timer-button.timer-active .timer-cancel{display:none}mods-list .cheats .precision-mods-button.timer-active .icon-loop,mods-list .cheats .mod-timer-button.timer-active .icon-loop,trainer-mods-list-placeholder .cheats .precision-mods-button.timer-active .icon-loop,trainer-mods-list-placeholder .cheats .mod-timer-button.timer-active .icon-loop{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:14px;width:14px;height:14px}mods-list .cheats .precision-mods-button.timer-active:hover .timer-display,mods-list .cheats .mod-timer-button.timer-active:hover .timer-display,trainer-mods-list-placeholder .cheats .precision-mods-button.timer-active:hover .timer-display,trainer-mods-list-placeholder .cheats .mod-timer-button.timer-active:hover .timer-display{display:none}mods-list .cheats .precision-mods-button.timer-active:hover .timer-cancel,mods-list .cheats .mod-timer-button.timer-active:hover .timer-cancel,trainer-mods-list-placeholder .cheats .precision-mods-button.timer-active:hover .timer-cancel,trainer-mods-list-placeholder .cheats .mod-timer-button.timer-active:hover .timer-cancel{display:inline-block}mods-list .cheats .mod-timer-button-container.disabled,trainer-mods-list-placeholder .cheats .mod-timer-button-container.disabled{pointer-events:none;cursor:not-allowed}mods-list .cheats .mod-timer-button.active,trainer-mods-list-placeholder .cheats .mod-timer-button.active{background:var(--theme--highlight-darker)}mods-list .cheats .mod-timer-button.active .mod-timer-button-text,trainer-mods-list-placeholder .cheats .mod-timer-button.active .mod-timer-button-text{display:inline-block;text-transform:uppercase}mods-list .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last,trainer-mods-list-placeholder .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last{transition:border-radius .2s ease-in-out,left .3s ease;padding-top:10px;padding-bottom:10px;border-bottom-left-radius:16px;border-bottom-right-radius:16px}mods-list .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last.mod-disabled:hover .mod-disabled-hint,mods-list .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last.mod-disabled:hover .mod-broken-hint,mods-list .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last.mod-broken:hover .mod-disabled-hint,mods-list .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last.mod-broken:hover .mod-broken-hint,trainer-mods-list-placeholder .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last.mod-disabled:hover .mod-disabled-hint,trainer-mods-list-placeholder .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last.mod-disabled:hover .mod-broken-hint,trainer-mods-list-placeholder .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last.mod-broken:hover .mod-disabled-hint,trainer-mods-list-placeholder .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last.mod-broken:hover .mod-broken-hint{border-bottom-left-radius:16px}mods-list .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last .pin-btn,trainer-mods-list-placeholder .cheats .cheats-wrapper:not(.precision-mods-open) .cheat.last .pin-btn{border-bottom-right-radius:16px}mods-list .cheats .precision-mods-open .cheat,trainer-mods-list-placeholder .cheats .precision-mods-open .cheat{background:rgba(255,255,255,.03)}mods-list .cheats .precision-mods-open .precision-mods-list,trainer-mods-list-placeholder .cheats .precision-mods-open .precision-mods-list{max-height:500px;opacity:1;visibility:visible;transition:max-height .5s ease-in-out,opacity .3s ease-in-out,visibility 0s linear 0s}mods-list .cheats .precision-mods-open .precision-mods-list .precision-mods-button-container,trainer-mods-list-placeholder .cheats .precision-mods-open .precision-mods-list .precision-mods-button-container{display:none}mods-list .cheats .precision-mods-open .precision-mods-list pin-mod-icon,trainer-mods-list-placeholder .cheats .precision-mods-open .precision-mods-list pin-mod-icon{visibility:hidden !important}mods-list .cheats .precision-mods-open .precision-mods-button,trainer-mods-list-placeholder .cheats .precision-mods-open .precision-mods-button{background:var(--theme--highlight-darker)}mods-list .cheats .precision-mods-open .precision-mods-button-text,trainer-mods-list-placeholder .cheats .precision-mods-open .precision-mods-button-text{display:flex;text-transform:uppercase}mods-list .cheats .precision-mods-list,trainer-mods-list-placeholder .cheats .precision-mods-list{max-height:0;opacity:0;visibility:hidden;transition:max-height .3s ease-in-out,opacity .2s ease-in-out,visibility 0s linear .3s}mods-list .cheats .precision-mods-list .cheat .cheat-name .label-wrapper .label,trainer-mods-list-placeholder .cheats .precision-mods-list .cheat .cheat-name .label-wrapper .label{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;margin-left:12px}mods-list .cheats .precision-mods-list .precison-mods-button,trainer-mods-list-placeholder .cheats .precision-mods-list .precison-mods-button{display:none}mods-list .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod,trainer-mods-list-placeholder .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod{transition:border-radius .2s ease-in-out,left .3s ease;border-bottom-left-radius:16px;border-bottom-right-radius:16px}mods-list .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod .mod-disabled:hover .mod-disabled-hint,mods-list .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod .mod-disabled:hover .mod-broken-hint,mods-list .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod .mod-broken:hover .mod-disabled-hint,mods-list .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod .mod-broken:hover .mod-broken-hint,trainer-mods-list-placeholder .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod .mod-disabled:hover .mod-disabled-hint,trainer-mods-list-placeholder .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod .mod-disabled:hover .mod-broken-hint,trainer-mods-list-placeholder .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod .mod-broken:hover .mod-disabled-hint,trainer-mods-list-placeholder .cheats .precision-mods-list .mod-row:last-child .cheat.last-child-mod .mod-broken:hover .mod-broken-hint{border-bottom-left-radius:16px}mods-list .cheats .cheat,trainer-mods-list-placeholder .cheats .cheat{position:relative;display:flex;flex-direction:row;min-height:48px;gap:20px;padding:10px 8px 10px 16px;align-items:center;transition:background-color .1s cubic-bezier(0.22, 1, 0.36, 1),left .3s ease}mods-list .cheats .cheat .name-input-container,trainer-mods-list-placeholder .cheats .cheat .name-input-container{display:flex;flex-basis:100%;height:100%}mods-list .cheats .cheat .mod-disabled-hint,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint{display:none;width:66.6666666667%}mods-list .cheats .cheat .mod-disabled-hint .mod-info,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .mod-info{display:flex;flex-direction:column}mods-list .cheats .cheat .mod-disabled-hint .mod-info .mod-name-wrapper,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .mod-info .mod-name-wrapper{display:flex;align-items:center;gap:4px}mods-list .cheats .cheat .mod-disabled-hint .mod-info .mod-name-wrapper .mod-name,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .mod-info .mod-name-wrapper .mod-name{font-weight:500}mods-list .cheats .cheat .mod-disabled-hint .mod-info .mod-name-wrapper .mod-tooltip,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .mod-info .mod-name-wrapper .mod-tooltip{display:flex;position:relative}mods-list .cheats .cheat .mod-disabled-hint .hint-text,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .hint-text{text-shadow:0px 1px 2px rgba(0,0,0,.25);color:#fff;position:relative;display:flex;align-items:center;font-weight:600;font-size:14px}mods-list .cheats .cheat .mod-disabled-hint .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .mod-disabled-hint-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;text-shadow:0px 1px 2px rgba(0,0,0,.25);color:#fff;font-size:24px}mods-list .cheats .cheat .mod-disabled-hint .mod-disabled-info-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .mod-disabled-info-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;text-shadow:0px 1px 2px rgba(0,0,0,.25);color:#fff;font-size:20px;opacity:.8}mods-list .cheats .cheat .mod-disabled-hint .mod-disabled-info-icon:before,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint .mod-disabled-info-icon:before{font-family:inherit;content:"info"}mods-list .cheats .cheat .mod-disabled-hint.add-game,mods-list .cheats .cheat .mod-disabled-hint.install-for-free,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.add-game,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.install-for-free{background:linear-gradient(87.55deg, rgba(172, 255, 53, 0.6) -4.94%, rgba(62, 180, 145, 0.6) 27.74%, rgba(31, 33, 92, 0.6) 66.46%, rgba(31, 33, 92, 0) 97.95%)}mods-list .cheats .cheat .mod-disabled-hint.add-game .mod-disabled-hint-icon,mods-list .cheats .cheat .mod-disabled-hint.install-for-free .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.add-game .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.install-for-free .mod-disabled-hint-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .cheats .cheat .mod-disabled-hint.add-game .mod-disabled-hint-icon:before,mods-list .cheats .cheat .mod-disabled-hint.install-for-free .mod-disabled-hint-icon:before,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.add-game .mod-disabled-hint-icon:before,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.install-for-free .mod-disabled-hint-icon:before{font-family:inherit;content:"add"}mods-list .cheats .cheat .mod-disabled-hint.play,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.play{background:linear-gradient(90deg, rgba(11, 242, 246, 0.6) 0%, rgba(11, 242, 246, 0.6) 0.01%, rgba(56, 116, 251, 0.56) 30%, rgba(97, 0, 255, 0.18) 57.5%, rgba(50, 0, 87, 0) 99.32%)}mods-list .cheats .cheat .mod-disabled-hint.play .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.play .mod-disabled-hint-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}mods-list .cheats .cheat .mod-disabled-hint.play .mod-disabled-hint-icon:before,trainer-mods-list-placeholder .cheats .cheat .mod-disabled-hint.play .mod-disabled-hint-icon:before{font-family:inherit;content:"play_arrow"}mods-list .cheats .cheat .mod-broken-hint,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint{display:none;width:66.6666666667%;background:linear-gradient(90deg, rgba(84, 110, 255, 0.6) 0%, rgba(57, 15, 126, 0.6) 57%, rgba(74, 1, 32, 0) 99.32%),linear-gradient(90deg, rgba(255, 255, 255, 0.025) 0%, rgba(255, 255, 255, 0) 100%)}mods-list .cheats .cheat .mod-broken-hint .mod-disabled-hint-icon,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .mod-disabled-hint-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:#fff;font-size:16px;padding-left:6px}mods-list .cheats .cheat .mod-broken-hint .mod-disabled-hint-icon:before,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .mod-disabled-hint-icon:before{font-family:inherit;content:"construction"}mods-list .cheats .cheat .mod-broken-hint .mod-info,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .mod-info{display:flex;flex-direction:column}mods-list .cheats .cheat .mod-broken-hint .mod-info .mod-name-wrapper,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .mod-info .mod-name-wrapper{display:flex;align-items:center;gap:4px}mods-list .cheats .cheat .mod-broken-hint .mod-info .mod-name-wrapper .mod-name,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .mod-info .mod-name-wrapper .mod-name{font-weight:500}mods-list .cheats .cheat .mod-broken-hint .mod-info .mod-name-wrapper .mod-tooltip,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .mod-info .mod-name-wrapper .mod-tooltip{display:flex;position:relative}mods-list .cheats .cheat .mod-broken-hint .hint-text,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .hint-text{text-shadow:0px 1px 2px rgba(0,0,0,.25);color:#fff;font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;position:relative;display:flex;align-items:center}mods-list .cheats .cheat .mod-broken-hint .hint-text.broken-text,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .hint-text.broken-text{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}@media(max-width: 1440px){mods-list .cheats .cheat .mod-broken-hint .hint-text.broken-text,trainer-mods-list-placeholder .cheats .cheat .mod-broken-hint .hint-text.broken-text{max-width:200px}}mods-list .cheats .cheat.beta:hover,trainer-mods-list-placeholder .cheats .cheat.beta:hover{background:linear-gradient(-178deg, rgba(var(--color--brand-green--rgb), 0.18) 0%, rgba(var(--color--pro--rgb), 0.18) 100%);border-radius:4px}mods-list .cheats .cheat.beta:not(:hover) .cheat-name .cheat-name-inner .label,trainer-mods-list-placeholder .cheats .cheat.beta:not(:hover) .cheat-name .cheat-name-inner .label{color:rgba(255,255,255,.3)}mods-list .cheats .cheat.mod-disabled,trainer-mods-list-placeholder .cheats .cheat.mod-disabled{min-height:48px;position:relative}@media(max-width: 1350px){mods-list .cheats .cheat.mod-disabled:hover .precision-mods-button-container,mods-list .cheats .cheat.mod-disabled:hover .mod-timer-button-container,trainer-mods-list-placeholder .cheats .cheat.mod-disabled:hover .precision-mods-button-container,trainer-mods-list-placeholder .cheats .cheat.mod-disabled:hover .mod-timer-button-container{visibility:hidden}}mods-list .cheats .cheat.mod-disabled:hover .cheat-name,mods-list .cheats .cheat.mod-disabled:hover .input,mods-list .cheats .cheat.mod-disabled:hover .mod-broken-hint,trainer-mods-list-placeholder .cheats .cheat.mod-disabled:hover .cheat-name,trainer-mods-list-placeholder .cheats .cheat.mod-disabled:hover .input,trainer-mods-list-placeholder .cheats .cheat.mod-disabled:hover .mod-broken-hint{visibility:hidden}mods-list .cheats .cheat.mod-disabled:hover save-cheats-icon,trainer-mods-list-placeholder .cheats .cheat.mod-disabled:hover save-cheats-icon{pointer-events:none}mods-list .cheats .cheat.mod-disabled:hover .mod-disabled-hint,trainer-mods-list-placeholder .cheats .cheat.mod-disabled:hover .mod-disabled-hint{position:absolute;left:0;top:0;display:flex;flex-basis:100%;height:100%;gap:10px;min-height:48px;align-items:center;padding-left:10px}mods-list .cheats .cheat.mod-broken,trainer-mods-list-placeholder .cheats .cheat.mod-broken{min-height:48px;position:relative;background:linear-gradient(90deg, rgba(255, 255, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%)}mods-list .cheats .cheat.mod-broken .name-input-container,trainer-mods-list-placeholder .cheats .cheat.mod-broken .name-input-container{min-height:48px}mods-list .cheats .cheat.mod-broken .broken-icon,trainer-mods-list-placeholder .cheats .cheat.mod-broken .broken-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;margin-right:8px;color:rgba(255,255,255,.6)}mods-list .cheats .cheat.mod-broken .broken-icon:before,trainer-mods-list-placeholder .cheats .cheat.mod-broken .broken-icon:before{font-family:inherit;content:"construction"}mods-list .cheats .cheat.mod-broken .cheat-name .cheat-name-inner .label,trainer-mods-list-placeholder .cheats .cheat.mod-broken .cheat-name .cheat-name-inner .label{text-decoration:rgba(255,255,255,.6) line-through;color:rgba(255,255,255,.6)}mods-list .cheats .cheat.mod-broken:hover .cheat-name,trainer-mods-list-placeholder .cheats .cheat.mod-broken:hover .cheat-name{visibility:hidden}mods-list .cheats .cheat.mod-broken:hover .mod-broken-hint,trainer-mods-list-placeholder .cheats .cheat.mod-broken:hover .mod-broken-hint{position:absolute;left:0;top:0;display:flex;flex-basis:100%;height:100%;gap:10px;min-height:48px;align-items:center;padding-left:10px}mods-list .cheats .cheat.mod-broken:hover .input,trainer-mods-list-placeholder .cheats .cheat.mod-broken:hover .input{visibility:hidden}.theme-default mods-list .cheats .cheat.is-unread .cheat-name-inner .label,.theme-default trainer-mods-list-placeholder .cheats .cheat.is-unread .cheat-name-inner .label{color:#fff}.theme-purple-pro mods-list .cheats .cheat.is-unread .cheat-name-inner .label,.theme-purple-pro trainer-mods-list-placeholder .cheats .cheat.is-unread .cheat-name-inner .label{color:#fff}.theme-green-pro mods-list .cheats .cheat.is-unread .cheat-name-inner .label,.theme-green-pro trainer-mods-list-placeholder .cheats .cheat.is-unread .cheat-name-inner .label{color:#fff}.theme-orange-pro mods-list .cheats .cheat.is-unread .cheat-name-inner .label,.theme-orange-pro trainer-mods-list-placeholder .cheats .cheat.is-unread .cheat-name-inner .label{color:#fff}.theme-pro mods-list .cheats .cheat.is-unread .cheat-name-inner .label,.theme-pro trainer-mods-list-placeholder .cheats .cheat.is-unread .cheat-name-inner .label{color:#fff}mods-list .cheats .cheat.is-unread .cheat-name-inner .info-icon i,trainer-mods-list-placeholder .cheats .cheat.is-unread .cheat-name-inner .info-icon i{color:var(--color--warning)}mods-list .cheats .cheat:hover .beta-upgrade,trainer-mods-list-placeholder .cheats .cheat:hover .beta-upgrade{opacity:1}mods-list .cheats .cheat:hover .beta-upgrade+*,trainer-mods-list-placeholder .cheats .cheat:hover .beta-upgrade+*{opacity:0;pointer-events:none}mods-list .cheats .cheat:hover .beta-badge,trainer-mods-list-placeholder .cheats .cheat:hover .beta-badge{opacity:1}mods-list .cheats .cheat:hover trainer-hotkey .label,trainer-mods-list-placeholder .cheats .cheat:hover trainer-hotkey .label{color:#fff}mods-list .cheats .cheat:hover:not(.beta):after,trainer-mods-list-placeholder .cheats .cheat:hover:not(.beta):after{content:"";position:absolute;top:0;left:0;width:100%;height:100%;background:rgba(255,255,255,.025);pointer-events:none;border-radius:inherit}mods-list .cheats .cheat:has(.hotkeys):not(:has(.input)) .hotkeys,trainer-mods-list-placeholder .cheats .cheat:has(.hotkeys):not(:has(.input)) .hotkeys{justify-content:flex-end}mods-list .cheats .cheat .info-icon,trainer-mods-list-placeholder .cheats .cheat .info-icon{display:inline-flex;flex:0 0 auto;align-items:center;justify-content:center;position:relative;margin-right:6px}mods-list .cheats .cheat .info-icon tooltip,trainer-mods-list-placeholder .cheats .cheat .info-icon tooltip{white-space:normal}mods-list .cheats .cheat .info-icon i.icon,trainer-mods-list-placeholder .cheats .cheat .info-icon i.icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:inline-flex;font-size:20px;color:rgba(255,255,255,.6);align-self:center}mods-list .cheats .cheat .info-icon i.icon:before,trainer-mods-list-placeholder .cheats .cheat .info-icon i.icon:before{font-family:inherit;content:"info"}mods-list .cheats .cheat .info-content.wide,trainer-mods-list-placeholder .cheats .cheat .info-content.wide{width:450px}mods-list .cheats .cheat .info-content .message,trainer-mods-list-placeholder .cheats .cheat .info-content .message{font-size:14px;line-height:21px;line-height:19px;color:rgba(255,255,255,.5)}mods-list .cheats .cheat .info-content .header,trainer-mods-list-placeholder .cheats .cheat .info-content .header{font-size:11px;line-height:16px;font-weight:700;display:flex;align-items:center;color:rgba(255,255,255,.25);margin:0 0 5px}mods-list .cheats .cheat .info-content hr,trainer-mods-list-placeholder .cheats .cheat .info-content hr{border:0;border-top:1px solid rgba(255,255,255,.05);margin:11px 0}mods-list .cheats .cheat .cheat-name,trainer-mods-list-placeholder .cheats .cheat .cheat-name{min-width:var(--max-cheat-name-width, 33.3%);display:flex;align-items:center}mods-list .cheats .cheat .cheat-name .cheat-name-inner,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner{display:flex;align-items:center;padding-right:10px;min-width:0}mods-list .cheats .cheat .cheat-name .cheat-name-inner save-cheats-icon,mods-list .cheats .cheat .cheat-name .cheat-name-inner .save-cheats-icon,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner save-cheats-icon,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner .save-cheats-icon{margin-right:16px;min-width:0;flex:0 0 auto}mods-list .cheats .cheat .cheat-name .cheat-name-inner .label-wrapper,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner .label-wrapper{display:flex;align-items:center;min-width:0;text-overflow:ellipsis}mods-list .cheats .cheat .cheat-name .cheat-name-inner .label-wrapper>.info-icon,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner .label-wrapper>.info-icon{margin-left:9.5px}mods-list .cheats .cheat .cheat-name .cheat-name-inner .label,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner .label{font-size:14px;font-weight:500;word-break:break-word;color:var(--mods-theme-text-color, rgba(255, 255, 255, 0.8))}mods-list .cheats .cheat .cheat-name .cheat-name-inner.has-tooltip .label-wrapper:hover .label,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner.has-tooltip .label-wrapper:hover .label{color:#fff}mods-list .cheats .cheat .input-inner,trainer-mods-list-placeholder .cheats .cheat .input-inner{position:relative;display:flex;align-items:center;justify-content:end}mods-list .cheats .cheat .input-inner>*,trainer-mods-list-placeholder .cheats .cheat .input-inner>*{transition:opacity .15s}mods-list .cheats .cheat .input,trainer-mods-list-placeholder .cheats .cheat .input{position:relative;display:flex;align-items:center;justify-content:flex-end;margin-left:auto;flex-grow:1;max-width:200px}mods-list .cheats .cheat .input>*,trainer-mods-list-placeholder .cheats .cheat .input>*{transition:opacity .15s}mods-list .cheats .cheat .input>*:not(button),trainer-mods-list-placeholder .cheats .cheat .input>*:not(button){flex-grow:1}mods-list .cheats .cheat .input:hover .pro-upgrade,trainer-mods-list-placeholder .cheats .cheat .input:hover .pro-upgrade{opacity:1}mods-list .cheats .cheat .input:hover .pro-upgrade+*,trainer-mods-list-placeholder .cheats .cheat .input:hover .pro-upgrade+*{opacity:0;pointer-events:none}mods-list .cheats .cheat .input-shrinkwrap,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap{position:relative;display:flex;justify-content:end;min-width:0;flex-grow:1}mods-list .cheats .cheat .input-shrinkwrap>*:not(tooltip),trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap>*:not(tooltip){transition:opacity .15s}mods-list .cheats .cheat .input-shrinkwrap:not(.force-enable).disabled>*:not(tooltip),trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap:not(.force-enable).disabled>*:not(tooltip){opacity:.2;cursor:not-allowed;pointer-events:none}mods-list .cheats .cheat .input-shrinkwrap.force-enable.disabled .button-input,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap.force-enable.disabled .button-input{opacity:1}mods-list .cheats .cheat .input-shrinkwrap range-input,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap range-input{min-width:160px;max-width:200px}mods-list .cheats .cheat .input-shrinkwrap number-input,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap number-input{min-width:160px;max-width:200px}mods-list .cheats .cheat .input-shrinkwrap scalar-input,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap scalar-input{min-width:160px;max-width:200px}mods-list .cheats .cheat .input-shrinkwrap incremental-input,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap incremental-input{min-width:160px;max-width:200px}mods-list .cheats .cheat .input-shrinkwrap selection-input,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap selection-input{min-width:160px;max-width:200px}mods-list .cheats .cheat .input-shrinkwrap .button-input,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap .button-input{max-width:fit-content}mods-list .cheats .cheat .input-shrinkwrap tooltip .tooltip-content,trainer-mods-list-placeholder .cheats .cheat .input-shrinkwrap tooltip .tooltip-content{max-width:initial}mods-list .cheats .cheat .hotkeys,trainer-mods-list-placeholder .cheats .cheat .hotkeys{min-width:var(--max-hotkeys-width, 33.3%);display:flex;justify-content:flex-start}mods-list .cheats .cheat .hotkeys .hotkeys-inner,trainer-mods-list-placeholder .cheats .cheat .hotkeys .hotkeys-inner{display:flex;flex:1 1 auto;flex-direction:row;position:relative;flex-wrap:wrap;flex-basis:100%;gap:8px}mods-list .cheats .cheat .hotkeys .hotkeys-inner trainer-hotkey,trainer-mods-list-placeholder .cheats .cheat .hotkeys .hotkeys-inner trainer-hotkey{display:flex}mods-list .cheats .cheat .hotkeys .hotkeys-inner trainer-hotkey:nth-child(1),trainer-mods-list-placeholder .cheats .cheat .hotkeys .hotkeys-inner trainer-hotkey:nth-child(1){order:2}mods-list .cheats .cheat .hotkeys .hotkeys-inner trainer-hotkey:nth-child(2),trainer-mods-list-placeholder .cheats .cheat .hotkeys .hotkeys-inner trainer-hotkey:nth-child(2){order:1}mods-list .cheats .cheat .hotkeys trainer-hotkey,trainer-mods-list-placeholder .cheats .cheat .hotkeys trainer-hotkey{display:block}mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden),mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden),trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden),trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden){opacity:1;visibility:visible;transition-delay:0s}mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-left-top,mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-left-center,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-left-top,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-left-center,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-left-top,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-left-center,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-left-top,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-left-center{margin-left:10px}mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-right-top,mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-right-center,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-right-top,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-right-center,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-right-top,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-right-center,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-right-top,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-right-center{margin-right:10px}mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-top-center,mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-top-left,mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-top-right,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-top-center,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-top-left,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-top-right,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-top-center,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-top-left,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-top-right,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-top-center,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-top-left,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-top-right{margin-top:10px}mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-bottom-left,mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-bottom-center,mods-list .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-bottom-right,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-bottom-left,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-bottom-center,mods-list .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-bottom-right,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-bottom-left,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-bottom-center,trainer-mods-list-placeholder .cheats .cheat .mod-name-wrapper.has-tooltip:hover tooltip .tooltip:not(.hidden).arrow-bottom-right,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-bottom-left,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-bottom-center,trainer-mods-list-placeholder .cheats .cheat .cheat-name .has-tooltip .label-wrapper:hover tooltip .tooltip:not(.hidden).arrow-bottom-right{margin-bottom:10px}mods-list .pro-upgrade,mods-list .beta-upgrade,trainer-mods-list-placeholder .pro-upgrade,trainer-mods-list-placeholder .beta-upgrade{font-size:11px;line-height:16px;font-weight:700;display:flex;line-height:15px;align-items:center;justify-items:flex-start;position:absolute;left:-10px;top:-10px;width:calc(100% + 20px);height:calc(100% + 20px);overflow:hidden;background:rgba(0,0,0,0);color:var(--theme--highlight);opacity:0;transition:opacity .15s;border:0;padding:0;text-align:left}mods-list .pro-upgradebutton,mods-list .pro-upgradebutton *,mods-list .beta-upgradebutton,mods-list .beta-upgradebutton *,trainer-mods-list-placeholder .pro-upgradebutton,trainer-mods-list-placeholder .pro-upgradebutton *,trainer-mods-list-placeholder .beta-upgradebutton,trainer-mods-list-placeholder .beta-upgradebutton *{cursor:pointer}mods-list .pro-upgrade span,mods-list .beta-upgrade span,trainer-mods-list-placeholder .pro-upgrade span,trainer-mods-list-placeholder .beta-upgrade span{white-space:nowrap}mods-list .pro-upgrade span.loading,mods-list .beta-upgrade span.loading,trainer-mods-list-placeholder .pro-upgrade span.loading,trainer-mods-list-placeholder .beta-upgrade span.loading{display:flex;width:100%;justify-content:end;align-items:center;padding-right:8px}mods-list .pro-upgrade strong,mods-list .beta-upgrade strong,trainer-mods-list-placeholder .pro-upgrade strong,trainer-mods-list-placeholder .beta-upgrade strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;margin:0 2px;flex:0 0 auto}@media(forced-colors: active){body:not(.override-contrast-mode) mods-list .pro-upgrade strong,body:not(.override-contrast-mode) mods-list .beta-upgrade strong,body:not(.override-contrast-mode) trainer-mods-list-placeholder .pro-upgrade strong,body:not(.override-contrast-mode) trainer-mods-list-placeholder .beta-upgrade strong{border:1px solid #fff}}mods-list .pro-upgrade i,mods-list .beta-upgrade i,trainer-mods-list-placeholder .pro-upgrade i,trainer-mods-list-placeholder .beta-upgrade i{margin-left:5px}mods-list .pro-upgrade i svg *,mods-list .beta-upgrade i svg *,trainer-mods-list-placeholder .pro-upgrade i svg *,trainer-mods-list-placeholder .beta-upgrade i svg *{fill:var(--theme--highlight)}mods-list .button-input,trainer-mods-list-placeholder .button-input{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;background-color:rgba(255,255,255,.6);box-shadow:none;color:var(--theme--background);font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;background-color:var(--mods-theme-color, var(--theme--highlight-darker));color:rgba(255,255,255,.9);border-radius:8px;font-weight:normal;height:28px;max-width:100%;display:inline-block}mods-list .button-input,mods-list .button-input *,trainer-mods-list-placeholder .button-input,trainer-mods-list-placeholder .button-input *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) mods-list .button-input,body:not(.override-contrast-mode) trainer-mods-list-placeholder .button-input{border:1px solid #fff}}mods-list .button-input>*,trainer-mods-list-placeholder .button-input>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}mods-list .button-input>*:first-child,trainer-mods-list-placeholder .button-input>*:first-child{padding-left:0}mods-list .button-input>*:last-child,trainer-mods-list-placeholder .button-input>*:last-child{padding-right:0}mods-list .button-input svg,trainer-mods-list-placeholder .button-input svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) mods-list .button-input svg *,body:not(.override-contrast-mode) trainer-mods-list-placeholder .button-input svg *{fill:CanvasText}}mods-list .button-input svg *,trainer-mods-list-placeholder .button-input svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) mods-list .button-input svg,body:not(.override-contrast-mode) trainer-mods-list-placeholder .button-input svg{opacity:1}}mods-list .button-input img,trainer-mods-list-placeholder .button-input img{height:50%}mods-list .button-input:disabled,trainer-mods-list-placeholder .button-input:disabled{opacity:.3}mods-list .button-input:disabled,mods-list .button-input:disabled *,trainer-mods-list-placeholder .button-input:disabled,trainer-mods-list-placeholder .button-input:disabled *{cursor:default;pointer-events:none}@media(hover: hover){mods-list .button-input:not(:disabled):hover,trainer-mods-list-placeholder .button-input:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}mods-list .button-input:not(:disabled):hover svg,trainer-mods-list-placeholder .button-input:not(:disabled):hover svg{opacity:1}}mods-list .button-input:not(:disabled):active,trainer-mods-list-placeholder .button-input:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}mods-list .button-input svg,trainer-mods-list-placeholder .button-input svg{opacity:1}mods-list .button-input svg *,trainer-mods-list-placeholder .button-input svg *{--cta__icon--color: var(--theme--background)}@media(hover: hover){mods-list .button-input:not(:disabled):hover,trainer-mods-list-placeholder .button-input:not(:disabled):hover{background-color:var(--theme--highlight)}}mods-list .lock-icon,trainer-mods-list-placeholder .lock-icon{opacity:.4;margin-left:5px}mods-list .beta-badge,trainer-mods-list-placeholder .beta-badge{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;text-transform:none;background:rgba(255,255,255,.5);opacity:.5;transition:opacity .15s;margin-left:9px;flex:0 0 auto;min-width:0}mods-list .context-menu-item-pin,trainer-mods-list-placeholder .context-menu-item-pin{display:flex;align-items:center;justify-content:center;gap:4px;cursor:pointer}mods-list .context-menu-item-pin>*,trainer-mods-list-placeholder .context-menu-item-pin>*{cursor:pointer}mods-list .context-menu-item-pin.disabled,trainer-mods-list-placeholder .context-menu-item-pin.disabled{opacity:.5;cursor:not-allowed}mods-list .context-menu-item-pin.disabled>*,trainer-mods-list-placeholder .context-menu-item-pin.disabled>*{cursor:not-allowed}mods-list:not(.show-hotkeys) .cheats .cheat .inputs .input-wrapper .input-inner,trainer-mods-list-placeholder:not(.show-hotkeys) .cheats .cheat .inputs .input-wrapper .input-inner{justify-content:flex-end}@media(max-width: 480px){mods-list .cheats .cheat .name-input-container.cheat--compact-mobile,trainer-mods-list-placeholder .cheats .cheat .name-input-container.cheat--compact-mobile{flex-direction:column;align-items:flex-start;row-gap:8px}mods-list .cheats .cheat .name-input-container.cheat--compact-mobile .input,trainer-mods-list-placeholder .cheats .cheat .name-input-container.cheat--compact-mobile .input{margin-left:0;width:100%}mods-list .cheats .cheat .name-input-container.cheat--compact-mobile .input .input-inner .input-shrinkwrap>*,trainer-mods-list-placeholder .cheats .cheat .name-input-container.cheat--compact-mobile .input .input-inner .input-shrinkwrap>*{width:100%;max-width:unset}mods-list .cheats .cheat .input,trainer-mods-list-placeholder .cheats .cheat .input{max-width:unset;min-width:unset}mods-list .cheats .cheat .cheat-name,trainer-mods-list-placeholder .cheats .cheat .cheat-name{padding-right:0;width:100%}mods-list .cheats .cheat .cheat-name .cheat-name-inner:not(:has(.save-cheats-icon.enabled)) .label,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner:not(:has(.save-cheats-icon.enabled)) .label{margin-left:0px}mods-list .cheats .cheat .cheat-name .cheat-name-inner .save-cheats-icon,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner .save-cheats-icon{display:none}mods-list .cheats .cheat .cheat-name .cheat-name-inner .label,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner .label{overflow:hidden;white-space:normal;word-break:normal;text-overflow:ellipsis}mods-list .cheats .cheat+.cheat,trainer-mods-list-placeholder .cheats .cheat+.cheat{margin-top:8px}mods-list .cheats .mod-row-controls,trainer-mods-list-placeholder .cheats .mod-row-controls{flex-wrap:wrap}mods-list .cheats .mod-row-controls .mod-timer:not(:has(.timer-active)),trainer-mods-list-placeholder .cheats .mod-row-controls .mod-timer:not(:has(.timer-active)){display:none}}@media(max-width: 480px)and (max-width: 480px){mods-list .tooltip,mods-list .tooltip .tooltip-content,trainer-mods-list-placeholder .tooltip,trainer-mods-list-placeholder .tooltip .tooltip-content{max-width:calc(100vw - 84px) !important}}@media(max-width: 480px){@container (max-width: 480px){mods-list .tooltip,mods-list .tooltip .tooltip-content,trainer-mods-list-placeholder .tooltip,trainer-mods-list-placeholder .tooltip .tooltip-content{max-width:calc(100cqw - 44px) !important}}mods-list number-input input,trainer-mods-list-placeholder number-input input{max-width:initial}mods-list mod-timer,trainer-mods-list-placeholder mod-timer{width:100%}}@container (max-width: 480px){mods-list .cheats .cheat .name-input-container.cheat--compact-mobile,trainer-mods-list-placeholder .cheats .cheat .name-input-container.cheat--compact-mobile{flex-direction:column;align-items:flex-start;row-gap:8px}mods-list .cheats .cheat .name-input-container.cheat--compact-mobile .input,trainer-mods-list-placeholder .cheats .cheat .name-input-container.cheat--compact-mobile .input{margin-left:0;width:100%}mods-list .cheats .cheat .name-input-container.cheat--compact-mobile .input .input-inner .input-shrinkwrap>*,trainer-mods-list-placeholder .cheats .cheat .name-input-container.cheat--compact-mobile .input .input-inner .input-shrinkwrap>*{width:100%;max-width:unset}mods-list .cheats .cheat .input,trainer-mods-list-placeholder .cheats .cheat .input{max-width:unset;min-width:unset}mods-list .cheats .cheat .cheat-name,trainer-mods-list-placeholder .cheats .cheat .cheat-name{padding-right:0;width:100%}mods-list .cheats .cheat .cheat-name .cheat-name-inner:not(:has(.save-cheats-icon.enabled)) .label,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner:not(:has(.save-cheats-icon.enabled)) .label{margin-left:0px}mods-list .cheats .cheat .cheat-name .cheat-name-inner .save-cheats-icon,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner .save-cheats-icon{display:none}mods-list .cheats .cheat .cheat-name .cheat-name-inner .label,trainer-mods-list-placeholder .cheats .cheat .cheat-name .cheat-name-inner .label{overflow:hidden;white-space:normal;word-break:normal;text-overflow:ellipsis}mods-list .cheats .cheat+.cheat,trainer-mods-list-placeholder .cheats .cheat+.cheat{margin-top:8px}mods-list .cheats .mod-row-controls,trainer-mods-list-placeholder .cheats .mod-row-controls{flex-wrap:wrap}mods-list .cheats .mod-row-controls .mod-timer:not(:has(.timer-active)),trainer-mods-list-placeholder .cheats .mod-row-controls .mod-timer:not(:has(.timer-active)){display:none}@media(max-width: 480px){mods-list .tooltip,mods-list .tooltip .tooltip-content,trainer-mods-list-placeholder .tooltip,trainer-mods-list-placeholder .tooltip .tooltip-content{max-width:calc(100vw - 84px) !important}}@container (max-width: 480px){mods-list .tooltip,mods-list .tooltip .tooltip-content,trainer-mods-list-placeholder .tooltip,trainer-mods-list-placeholder .tooltip .tooltip-content{max-width:calc(100cqw - 44px) !important}}mods-list number-input input,trainer-mods-list-placeholder number-input input{max-width:initial}mods-list mod-timer,trainer-mods-list-placeholder mod-timer{width:100%}}mods-list.mobile{height:100%;padding:0 12px;overflow-y:scroll;overflow-x:hidden}mods-list.mobile.has-notes{padding-bottom:75px}mods-list.mobile trainer-notes-button{position:fixed;right:20px;bottom:20px;z-index:1}mods-list.mobile .hotkeys-inner{justify-content:flex-end}mods-list.mobile .cheats .cheat-name .cheat-name-inner .label{max-width:100%;text-overflow:unset}mods-list.mobile .mod-row{overflow-x:clip;overflow-y:visible}mods-list.mobile .mod-row .cheat .swipe-menu{position:absolute;right:-60px;transition:right .3s ease;display:flex;height:100%}mods-list.mobile .mod-row .cheat .swipe-menu-btn{display:flex;flex-direction:column;align-items:center;justify-content:center;width:60px;height:100%;color:#fff}mods-list.mobile .mod-row .cheat .swipe-menu-btn .label{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px}mods-list.mobile .mod-row .cheat .swipe-menu-btn.disabled{opacity:.5;cursor:not-allowed;pointer-events:none}mods-list.mobile .mod-row .cheat .swipe-menu-btn.pin-btn{background-color:var(--theme--highlight-darker)}mods-list.mobile .mod-row .cheat .swipe-menu-btn.timer-btn{background-color:#3f4043}mods-list.mobile .mod-row .cheat .swipe-menu-btn-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;padding-top:2px;font-size:16px}mods-list.mobile .mod-row .cheat .swipe-menu-btn-icon.pin{font-variation-settings:"FILL" 1,"wght" 400 !important}mods-list.mobile .mod-row .cheat.swiped-left{left:-60px;background-color:rgba(255,255,255,.05)}mods-list.mobile .mod-row .cheat.swiped-left.last{border-bottom-right-radius:0px}mods-list.mobile .mod-row .cheat.swiped-right{left:0}mods-list.mobile .mod-row .cheat.mod-timer-enabled .swipe-menu{right:-120px}mods-list.mobile .mod-row .cheat.mod-timer-enabled.swiped-left{left:-120px}mods-list.mobile .timer-pane .mod-timer-sheet{display:flex;flex-direction:column;padding:0px 16px 16px 16px}mods-list.mobile .timer-pane .mod-timer-sheet-title{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:20px;line-height:24px;letter-spacing:-0.75px;padding-left:12px;pointer-events:none}mods-list.mobile .timer-pane .mod-timer-sheet-subtitle{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-secondary);padding-left:12px;pointer-events:none}mods-list.mobile .timer-pane .mod-timer-sheet-content{display:flex;flex-direction:column;justify-content:center;align-items:center;margin-top:8px}mods-list.mobile .timer-pane .mod-timer-sheet .mod-timer-tooltip{width:100%}.mod-timer-tooltip{width:280px;padding:12px;border-radius:16px;display:flex;flex-direction:column;row-gap:6px}.mod-timer-tooltip-content-container{display:flex;flex-direction:column;row-gap:6px;position:relative;margin:-12px;padding:12px}.mod-timer-tooltip-content-container .pro-overlay{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;display:flex;flex-direction:column;justify-content:center;align-items:center;text-align:center;row-gap:8px;padding:8px;position:absolute;top:6px;left:0;width:100%;height:100%;background-color:rgba(0,0,0,.1);backdrop-filter:blur(10px);z-index:1;opacity:0;visibility:hidden;transition:opacity .15s ease,visibility .15s ease}.mod-timer-tooltip-content-container .pro-overlay .unlock-mod-timers-text{display:flex;align-items:center;gap:4px;font-weight:bold}.mod-timer-tooltip-content-container .pro-overlay .join-now-text{display:flex;align-items:center;gap:4px;color:var(--theme--highlight);cursor:pointer}.mod-timer-tooltip-content-container .pro-overlay .join-now-text .icon-arrow-forward{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;font-size:16px;width:16px;height:16px}.mod-timer-tooltip-content-container:hover .pro-overlay{opacity:1;visibility:visible}.mod-timer-tooltip hr{border:0;border-top:1px solid rgba(255,255,255,.15);margin:12px 0 8px 0}.mod-timer-tooltip .icon-loop{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-secondary)}.mod-timer-tooltip-message{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-secondary)}.mod-timer-tooltip-message .dismiss-button{color:var(--theme--highlight);cursor:pointer}.mod-timer-tooltip-presets{display:flex;gap:4px}.mod-timer-tooltip-presets .preset-timer-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;background-color:rgba(255,255,255,.6);box-shadow:none;color:var(--theme--background);font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;background-color:rgba(255,255,255,.05);color:rgba(255,255,255,.75);border-radius:8px;padding:8px;flex:1;width:100%;height:32px}.mod-timer-tooltip-presets .preset-timer-button,.mod-timer-tooltip-presets .preset-timer-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .mod-timer-tooltip-presets .preset-timer-button{border:1px solid #fff}}.mod-timer-tooltip-presets .preset-timer-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.mod-timer-tooltip-presets .preset-timer-button>*:first-child{padding-left:0}.mod-timer-tooltip-presets .preset-timer-button>*:last-child{padding-right:0}.mod-timer-tooltip-presets .preset-timer-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .mod-timer-tooltip-presets .preset-timer-button svg *{fill:CanvasText}}.mod-timer-tooltip-presets .preset-timer-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .mod-timer-tooltip-presets .preset-timer-button svg{opacity:1}}.mod-timer-tooltip-presets .preset-timer-button img{height:50%}.mod-timer-tooltip-presets .preset-timer-button:disabled{opacity:.3}.mod-timer-tooltip-presets .preset-timer-button:disabled,.mod-timer-tooltip-presets .preset-timer-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.mod-timer-tooltip-presets .preset-timer-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.mod-timer-tooltip-presets .preset-timer-button:not(:disabled):hover svg{opacity:1}}.mod-timer-tooltip-presets .preset-timer-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.mod-timer-tooltip-presets .preset-timer-button svg{opacity:1}.mod-timer-tooltip-presets .preset-timer-button svg *{--cta__icon--color: var(--theme--background)}@media(hover: hover){.mod-timer-tooltip-presets .preset-timer-button:not(:disabled):hover{background-color:var(--theme--highlight)}}.mod-timer-tooltip-presets .preset-timer-button.active{background-color:var(--theme--highlight-darker)}.mod-timer-tooltip-presets .preset-timer-button:hover{background-color:rgba(255,255,255,.2) !important;box-shadow:none !important}.mod-timer-tooltip-loop-label{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;color:var(--theme--text-primary);height:32px;display:flex;align-items:center}.mod-timer-tooltip-loop-selected-label{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;background-color:rgba(255,255,255,.15);color:var(--theme--text-primary);height:32px;display:flex;align-items:center;justify-content:center;border-radius:8px;padding:8px;text-align:center}.mod-timer-tooltip-loop-selected-container{display:flex;align-items:center;gap:4px;padding:8px;background-color:rgba(255,255,255,.05);border-radius:8px}.mod-timer-tooltip-loop-start-time,.mod-timer-tooltip-loop-end-time{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-placeholder);background-color:rgba(255,255,255,.05);background-image:url(${p});height:32px;width:66px;padding:8px;border-radius:8px;display:flex;align-items:center;justify-content:center;text-align:center;flex:1}.mod-timer-tooltip-loop-start-time.active,.mod-timer-tooltip-loop-end-time.active{background-color:var(--theme--highlight-darker);color:var(--theme--text-highlight);border:none}.mod-timer-tooltip-loop-start-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;height:40px;box-shadow:none;line-height:24px;font-weight:700;color:#000;border-radius:56px;padding:10px 16px;background:#fff;margin-top:4px}.mod-timer-tooltip-loop-start-button,.mod-timer-tooltip-loop-start-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .mod-timer-tooltip-loop-start-button{border:1px solid #fff}}.mod-timer-tooltip-loop-start-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.mod-timer-tooltip-loop-start-button>*:first-child{padding-left:0}.mod-timer-tooltip-loop-start-button>*:last-child{padding-right:0}.mod-timer-tooltip-loop-start-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .mod-timer-tooltip-loop-start-button svg *{fill:CanvasText}}.mod-timer-tooltip-loop-start-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .mod-timer-tooltip-loop-start-button svg{opacity:1}}.mod-timer-tooltip-loop-start-button img{height:50%}.mod-timer-tooltip-loop-start-button:disabled{opacity:.3}.mod-timer-tooltip-loop-start-button:disabled,.mod-timer-tooltip-loop-start-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.mod-timer-tooltip-loop-start-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.mod-timer-tooltip-loop-start-button:not(:disabled):hover svg{opacity:1}}.mod-timer-tooltip-loop-start-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.mod-timer-tooltip-loop-start-button:hover{background:rgba(255,255,255,.8) !important;color:rgba(0,0,0,.8) !important}`,""]);const b=h}}]);
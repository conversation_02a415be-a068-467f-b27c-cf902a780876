"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6172],{64504:(t,e,n)=>{n.d(e,{Ay:()=>J,Mm:()=>q});var i=n(2784),o=n(16607),r="tippy-content",a="tippy-backdrop",s="tippy-arrow",p="tippy-svg-arrow",c={passive:!0,capture:!0},u=function(){return document.body};function d(t,e,n){if(Array.isArray(t)){var i=t[e];return null==i?Array.isArray(n)?n[e]:n:i}return t}function f(t,e){var n={}.toString.call(t);return 0===n.indexOf("[object")&&n.indexOf(e+"]")>-1}function l(t,e){return"function"==typeof t?t.apply(void 0,e):t}function v(t,e){return 0===e?t:function(i){clearTimeout(n),n=setTimeout((function(){t(i)}),e)};var n}function m(t){return[].concat(t)}function b(t,e){-1===t.indexOf(e)&&t.push(e)}function h(t){return[].slice.call(t)}function g(t){return Object.keys(t).reduce((function(e,n){return void 0!==t[n]&&(e[n]=t[n]),e}),{})}function y(){return document.createElement("div")}function x(t){return["Element","Fragment"].some((function(e){return f(t,e)}))}function w(t,e){t.forEach((function(t){t&&(t.style.transitionDuration=e+"ms")}))}function E(t,e){t.forEach((function(t){t&&t.setAttribute("data-state",e)}))}function T(t,e,n){var i=e+"EventListener";["transitionend","webkitTransitionEnd"].forEach((function(e){t[i](e,n)}))}function A(t,e){for(var n=e;n;){var i;if(t.contains(n))return!0;n=null==n.getRootNode||null==(i=n.getRootNode())?void 0:i.host}return!1}var O={isTouch:!1},L=0;function C(){O.isTouch||(O.isTouch=!0,window.performance&&document.addEventListener("mousemove",k))}function k(){var t=performance.now();t-L<20&&(O.isTouch=!1,document.removeEventListener("mousemove",k)),L=t}function D(){var t,e=document.activeElement;if((t=e)&&t._tippy&&t._tippy.reference===t){var n=e._tippy;e.blur&&!n.state.isVisible&&e.blur()}}var j=!("undefined"==typeof window||"undefined"==typeof document||!window.msCrypto),V=Object.assign({appendTo:u,aria:{content:"auto",expanded:"auto"},delay:0,duration:[300,250],getReferenceClientRect:null,hideOnClick:!0,ignoreAttributes:!1,interactive:!1,interactiveBorder:2,interactiveDebounce:0,moveTransition:"",offset:[0,10],onAfterUpdate:function(){},onBeforeUpdate:function(){},onCreate:function(){},onDestroy:function(){},onHidden:function(){},onHide:function(){},onMount:function(){},onShow:function(){},onShown:function(){},onTrigger:function(){},onUntrigger:function(){},onClickOutside:function(){},placement:"top",plugins:[],popperOptions:{},render:null,showOnCreate:!1,touch:!0,trigger:"mouseenter focus",triggerTarget:null},{animateFill:!1,followCursor:!1,inlinePositioning:!1,sticky:!1},{allowHTML:!1,animation:"fade",arrow:!0,content:"",inertia:!1,maxWidth:350,role:"tooltip",theme:"",zIndex:9999}),M=Object.keys(V);function S(t){var e=(t.plugins||[]).reduce((function(e,n){var i,o=n.name,r=n.defaultValue;return o&&(e[o]=void 0!==t[o]?t[o]:null!=(i=V[o])?i:r),e}),{});return Object.assign({},t,e)}function I(t,e){var n=Object.assign({},e,{content:l(e.content,[t])},e.ignoreAttributes?{}:function(t,e){return(e?Object.keys(S(Object.assign({},V,{plugins:e}))):M).reduce((function(e,n){var i=(t.getAttribute("data-tippy-"+n)||"").trim();if(!i)return e;if("content"===n)e[n]=i;else try{e[n]=JSON.parse(i)}catch(t){e[n]=i}return e}),{})}(t,e.plugins));return n.aria=Object.assign({},V.aria,n.aria),n.aria={expanded:"auto"===n.aria.expanded?e.interactive:n.aria.expanded,content:"auto"===n.aria.content?e.interactive?null:"describedby":n.aria.content},n}function N(t,e){t.innerHTML=e}function H(t){var e=y();return!0===t?e.className=s:(e.className=p,x(t)?e.appendChild(t):N(e,t)),e}function U(t,e){x(e.content)?(N(t,""),t.appendChild(e.content)):"function"!=typeof e.content&&(e.allowHTML?N(t,e.content):t.textContent=e.content)}function R(t){var e=t.firstElementChild,n=h(e.children);return{box:e,content:n.find((function(t){return t.classList.contains(r)})),arrow:n.find((function(t){return t.classList.contains(s)||t.classList.contains(p)})),backdrop:n.find((function(t){return t.classList.contains(a)}))}}function W(t){var e=y(),n=y();n.className="tippy-box",n.setAttribute("data-state","hidden"),n.setAttribute("tabindex","-1");var i=y();function o(n,i){var o=R(e),r=o.box,a=o.content,s=o.arrow;i.theme?r.setAttribute("data-theme",i.theme):r.removeAttribute("data-theme"),"string"==typeof i.animation?r.setAttribute("data-animation",i.animation):r.removeAttribute("data-animation"),i.inertia?r.setAttribute("data-inertia",""):r.removeAttribute("data-inertia"),r.style.maxWidth="number"==typeof i.maxWidth?i.maxWidth+"px":i.maxWidth,i.role?r.setAttribute("role",i.role):r.removeAttribute("role"),n.content===i.content&&n.allowHTML===i.allowHTML||U(a,t.props),i.arrow?s?n.arrow!==i.arrow&&(r.removeChild(s),r.appendChild(H(i.arrow))):r.appendChild(H(i.arrow)):s&&r.removeChild(s)}return i.className=r,i.setAttribute("data-state","hidden"),U(i,t.props),e.appendChild(n),n.appendChild(i),o(t.props,t.props),{popper:e,onUpdate:o}}W.$$tippy=!0;var _=1,B=[],P=[];function z(t,e){var n,o,r,a,s,p,x,L,C=I(t,Object.assign({},V,S(g(e)))),k=!1,D=!1,M=!1,N=!1,H=[],U=v(gt,C.interactiveDebounce),W=_++,z=(L=C.plugins).filter((function(t,e){return L.indexOf(t)===e})),F={id:W,reference:t,popper:y(),popperInstance:null,props:C,state:{isEnabled:!0,isVisible:!1,isDestroyed:!1,isMounted:!1,isShown:!1},plugins:z,clearDelayTimeouts:function(){clearTimeout(n),clearTimeout(o),cancelAnimationFrame(r)},setProps:function(e){if(!F.state.isDestroyed){ot("onBeforeUpdate",[F,e]),bt();var n=F.props,i=I(t,Object.assign({},n,g(e),{ignoreAttributes:!0}));F.props=i,mt(),n.interactiveDebounce!==i.interactiveDebounce&&(st(),U=v(gt,i.interactiveDebounce)),n.triggerTarget&&!i.triggerTarget?m(n.triggerTarget).forEach((function(t){t.removeAttribute("aria-expanded")})):i.triggerTarget&&t.removeAttribute("aria-expanded"),at(),it(),J&&J(n,i),F.popperInstance&&(Et(),At().forEach((function(t){requestAnimationFrame(t._tippy.popperInstance.forceUpdate)}))),ot("onAfterUpdate",[F,e])}},setContent:function(t){F.setProps({content:t})},show:function(){var t=F.state.isVisible,e=F.state.isDestroyed,n=!F.state.isEnabled,i=O.isTouch&&!F.props.touch,o=d(F.props.duration,0,V.duration);if(!(t||e||n||i||Z().hasAttribute("disabled")||(ot("onShow",[F],!1),!1===F.props.onShow(F)))){if(F.state.isVisible=!0,Q()&&(q.style.visibility="visible"),it(),dt(),F.state.isMounted||(q.style.transition="none"),Q()){var r=et();w([r.box,r.content],0)}var a,s,c;p=function(){var t;if(F.state.isVisible&&!N){if(N=!0,q.offsetHeight,q.style.transition=F.props.moveTransition,Q()&&F.props.animation){var e=et(),n=e.box,i=e.content;w([n,i],o),E([n,i],"visible")}rt(),at(),b(P,F),null==(t=F.popperInstance)||t.forceUpdate(),ot("onMount",[F]),F.props.animation&&Q()&&function(t){lt(t,(function(){F.state.isShown=!0,ot("onShown",[F])}))}(o)}},s=F.props.appendTo,c=Z(),(a=F.props.interactive&&s===u||"parent"===s?c.parentNode:l(s,[c])).contains(q)||a.appendChild(q),F.state.isMounted=!0,Et()}},hide:function(){var t=!F.state.isVisible,e=F.state.isDestroyed,n=!F.state.isEnabled,i=d(F.props.duration,1,V.duration);if(!(t||e||n)&&(ot("onHide",[F],!1),!1!==F.props.onHide(F))){if(F.state.isVisible=!1,F.state.isShown=!1,N=!1,k=!1,Q()&&(q.style.visibility="hidden"),st(),ft(),it(!0),Q()){var o=et(),r=o.box,a=o.content;F.props.animation&&(w([r,a],i),E([r,a],"hidden"))}rt(),at(),F.props.animation?Q()&&function(t,e){lt(t,(function(){!F.state.isVisible&&q.parentNode&&q.parentNode.contains(q)&&e()}))}(i,F.unmount):F.unmount()}},hideWithInteractivity:function(t){tt().addEventListener("mousemove",U),b(B,U),U(t)},enable:function(){F.state.isEnabled=!0},disable:function(){F.hide(),F.state.isEnabled=!1},unmount:function(){F.state.isVisible&&F.hide(),F.state.isMounted&&(Tt(),At().forEach((function(t){t._tippy.unmount()})),q.parentNode&&q.parentNode.removeChild(q),P=P.filter((function(t){return t!==F})),F.state.isMounted=!1,ot("onHidden",[F]))},destroy:function(){F.state.isDestroyed||(F.clearDelayTimeouts(),F.unmount(),bt(),delete t._tippy,F.state.isDestroyed=!0,ot("onDestroy",[F]))}};if(!C.render)return F;var $=C.render(F),q=$.popper,J=$.onUpdate;q.setAttribute("data-tippy-root",""),q.id="tippy-"+F.id,F.popper=q,t._tippy=F,q._tippy=F;var X=z.map((function(t){return t.fn(F)})),Y=t.hasAttribute("aria-expanded");return mt(),at(),it(),ot("onCreate",[F]),C.showOnCreate&&Ot(),q.addEventListener("mouseenter",(function(){F.props.interactive&&F.state.isVisible&&F.clearDelayTimeouts()})),q.addEventListener("mouseleave",(function(){F.props.interactive&&F.props.trigger.indexOf("mouseenter")>=0&&tt().addEventListener("mousemove",U)})),F;function G(){var t=F.props.touch;return Array.isArray(t)?t:[t,0]}function K(){return"hold"===G()[0]}function Q(){var t;return!(null==(t=F.props.render)||!t.$$tippy)}function Z(){return x||t}function tt(){var t,e,n=Z().parentNode;return n?null!=(e=m(n)[0])&&null!=(t=e.ownerDocument)&&t.body?e.ownerDocument:document:document}function et(){return R(q)}function nt(t){return F.state.isMounted&&!F.state.isVisible||O.isTouch||a&&"focus"===a.type?0:d(F.props.delay,t?0:1,V.delay)}function it(t){void 0===t&&(t=!1),q.style.pointerEvents=F.props.interactive&&!t?"":"none",q.style.zIndex=""+F.props.zIndex}function ot(t,e,n){var i;void 0===n&&(n=!0),X.forEach((function(n){n[t]&&n[t].apply(n,e)})),n&&(i=F.props)[t].apply(i,e)}function rt(){var e=F.props.aria;if(e.content){var n="aria-"+e.content,i=q.id;m(F.props.triggerTarget||t).forEach((function(t){var e=t.getAttribute(n);if(F.state.isVisible)t.setAttribute(n,e?e+" "+i:i);else{var o=e&&e.replace(i,"").trim();o?t.setAttribute(n,o):t.removeAttribute(n)}}))}}function at(){!Y&&F.props.aria.expanded&&m(F.props.triggerTarget||t).forEach((function(t){F.props.interactive?t.setAttribute("aria-expanded",F.state.isVisible&&t===Z()?"true":"false"):t.removeAttribute("aria-expanded")}))}function st(){tt().removeEventListener("mousemove",U),B=B.filter((function(t){return t!==U}))}function pt(e){if(!O.isTouch||!M&&"mousedown"!==e.type){var n=e.composedPath&&e.composedPath()[0]||e.target;if(!F.props.interactive||!A(q,n)){if(m(F.props.triggerTarget||t).some((function(t){return A(t,n)}))){if(O.isTouch)return;if(F.state.isVisible&&F.props.trigger.indexOf("click")>=0)return}else ot("onClickOutside",[F,e]);!0===F.props.hideOnClick&&(F.clearDelayTimeouts(),F.hide(),D=!0,setTimeout((function(){D=!1})),F.state.isMounted||ft())}}}function ct(){M=!0}function ut(){M=!1}function dt(){var t=tt();t.addEventListener("mousedown",pt,!0),t.addEventListener("touchend",pt,c),t.addEventListener("touchstart",ut,c),t.addEventListener("touchmove",ct,c)}function ft(){var t=tt();t.removeEventListener("mousedown",pt,!0),t.removeEventListener("touchend",pt,c),t.removeEventListener("touchstart",ut,c),t.removeEventListener("touchmove",ct,c)}function lt(t,e){var n=et().box;function i(t){t.target===n&&(T(n,"remove",i),e())}if(0===t)return e();T(n,"remove",s),T(n,"add",i),s=i}function vt(e,n,i){void 0===i&&(i=!1),m(F.props.triggerTarget||t).forEach((function(t){t.addEventListener(e,n,i),H.push({node:t,eventType:e,handler:n,options:i})}))}function mt(){var t;K()&&(vt("touchstart",ht,{passive:!0}),vt("touchend",yt,{passive:!0})),(t=F.props.trigger,t.split(/\s+/).filter(Boolean)).forEach((function(t){if("manual"!==t)switch(vt(t,ht),t){case"mouseenter":vt("mouseleave",yt);break;case"focus":vt(j?"focusout":"blur",xt);break;case"focusin":vt("focusout",xt)}}))}function bt(){H.forEach((function(t){var e=t.node,n=t.eventType,i=t.handler,o=t.options;e.removeEventListener(n,i,o)})),H=[]}function ht(t){var e,n=!1;if(F.state.isEnabled&&!wt(t)&&!D){var i="focus"===(null==(e=a)?void 0:e.type);a=t,x=t.currentTarget,at(),!F.state.isVisible&&f(t,"MouseEvent")&&B.forEach((function(e){return e(t)})),"click"===t.type&&(F.props.trigger.indexOf("mouseenter")<0||k)&&!1!==F.props.hideOnClick&&F.state.isVisible?n=!0:Ot(t),"click"===t.type&&(k=!n),n&&!i&&Lt(t)}}function gt(t){var e=t.target,n=Z().contains(e)||q.contains(e);if("mousemove"!==t.type||!n){var i=At().concat(q).map((function(t){var e,n=null==(e=t._tippy.popperInstance)?void 0:e.state;return n?{popperRect:t.getBoundingClientRect(),popperState:n,props:C}:null})).filter(Boolean);(function(t,e){var n=e.clientX,i=e.clientY;return t.every((function(t){var e=t.popperRect,o=t.popperState,r=t.props.interactiveBorder,a=o.placement.split("-")[0],s=o.modifiersData.offset;if(!s)return!0;var p="bottom"===a?s.top.y:0,c="top"===a?s.bottom.y:0,u="right"===a?s.left.x:0,d="left"===a?s.right.x:0,f=e.top-i+p>r,l=i-e.bottom-c>r,v=e.left-n+u>r,m=n-e.right-d>r;return f||l||v||m}))})(i,t)&&(st(),Lt(t))}}function yt(t){wt(t)||F.props.trigger.indexOf("click")>=0&&k||(F.props.interactive?F.hideWithInteractivity(t):Lt(t))}function xt(t){F.props.trigger.indexOf("focusin")<0&&t.target!==Z()||F.props.interactive&&t.relatedTarget&&q.contains(t.relatedTarget)||Lt(t)}function wt(t){return!!O.isTouch&&K()!==t.type.indexOf("touch")>=0}function Et(){Tt();var e=F.props,n=e.popperOptions,o=e.placement,r=e.offset,a=e.getReferenceClientRect,s=e.moveTransition,c=Q()?R(q).arrow:null,u=a?{getBoundingClientRect:a,contextElement:a.contextElement||Z()}:t,d=[{name:"offset",options:{offset:r}},{name:"preventOverflow",options:{padding:{top:2,bottom:2,left:5,right:5}}},{name:"flip",options:{padding:5}},{name:"computeStyles",options:{adaptive:!s}},{name:"$$tippy",enabled:!0,phase:"beforeWrite",requires:["computeStyles"],fn:function(t){var e=t.state;if(Q()){var n=et().box;["placement","reference-hidden","escaped"].forEach((function(t){"placement"===t?n.setAttribute("data-placement",e.placement):e.attributes.popper["data-popper-"+t]?n.setAttribute("data-"+t,""):n.removeAttribute("data-"+t)})),e.attributes.popper={}}}}];Q()&&c&&d.push({name:"arrow",options:{element:c,padding:3}}),d.push.apply(d,(null==n?void 0:n.modifiers)||[]),F.popperInstance=(0,i.n4)(u,q,Object.assign({},n,{placement:o,onFirstUpdate:p,modifiers:d}))}function Tt(){F.popperInstance&&(F.popperInstance.destroy(),F.popperInstance=null)}function At(){return h(q.querySelectorAll("[data-tippy-root]"))}function Ot(t){F.clearDelayTimeouts(),t&&ot("onTrigger",[F,t]),dt();var e=nt(!0),i=G(),o=i[0],r=i[1];O.isTouch&&"hold"===o&&r&&(e=r),e?n=setTimeout((function(){F.show()}),e):F.show()}function Lt(t){if(F.clearDelayTimeouts(),ot("onUntrigger",[F,t]),F.state.isVisible){if(!(F.props.trigger.indexOf("mouseenter")>=0&&F.props.trigger.indexOf("click")>=0&&["mouseleave","mousemove"].indexOf(t.type)>=0&&k)){var e=nt(!1);e?o=setTimeout((function(){F.state.isVisible&&F.hide()}),e):r=requestAnimationFrame((function(){F.hide()}))}}else ft()}}function F(t,e){void 0===e&&(e={});var n=V.plugins.concat(e.plugins||[]);document.addEventListener("touchstart",C,c),window.addEventListener("blur",D);var i,o=Object.assign({},e,{plugins:n}),r=(i=t,x(i)?[i]:function(t){return f(t,"NodeList")}(i)?h(i):Array.isArray(i)?i:h(document.querySelectorAll(i))).reduce((function(t,e){var n=e&&z(e,o);return n&&t.push(n),t}),[]);return x(t)?r[0]:r}F.defaultProps=V,F.setDefaultProps=function(t){Object.keys(t).forEach((function(e){V[e]=t[e]}))},F.currentInput=O,Object.assign({},o.A,{effect:function(t){var e=t.state,n={popper:{position:e.options.strategy,left:"0",top:"0",margin:"0"},arrow:{position:"absolute"},reference:{}};Object.assign(e.elements.popper.style,n.popper),e.styles=n,e.elements.arrow&&Object.assign(e.elements.arrow.style,n.arrow)}});var $={mouseover:"mouseenter",focusin:"focus",click:"click"};function q(t,e){var n,i,o,r=[],a=[],s=!1,p=e.target,u=(n=e,i=["target"],o=Object.assign({},n),i.forEach((function(t){delete o[t]})),o),d=Object.assign({},u,{trigger:"manual",touch:!1}),f=Object.assign({touch:V.touch},u,{showOnCreate:!0}),l=F(t,d);function v(t){if(t.target&&!s){var n=t.target.closest(p);if(n){var i=n.getAttribute("data-tippy-trigger")||e.trigger||V.trigger;if(!n._tippy&&!("touchstart"===t.type&&"boolean"==typeof f.touch||"touchstart"!==t.type&&i.indexOf($[t.type])<0)){var o=F(n,f);o&&(a=a.concat(o))}}}}function b(t,e,n,i){void 0===i&&(i=!1),t.addEventListener(e,n,i),r.push({node:t,eventType:e,handler:n,options:i})}return m(l).forEach((function(t){var e=t.destroy,n=t.enable,i=t.disable;t.destroy=function(t){void 0===t&&(t=!0),t&&a.forEach((function(t){t.destroy()})),a=[],r.forEach((function(t){var e=t.node,n=t.eventType,i=t.handler,o=t.options;e.removeEventListener(n,i,o)})),r=[],e()},t.enable=function(){n(),a.forEach((function(t){return t.enable()})),s=!1},t.disable=function(){i(),a.forEach((function(t){return t.disable()})),s=!0},function(t){var e=t.reference;b(e,"touchstart",v,c),b(e,"mouseover",v),b(e,"focusin",v),b(e,"click",v)}(t)})),l}F.setDefaultProps({render:W});const J=F},71690:(t,e,n)=>{n.d(e,{A:()=>s});var i=n(31601),o=n.n(i),r=n(76314),a=n.n(r)()(o());a.push([t.id,'.tippy-box[data-animation=fade][data-state=hidden]{opacity:0}[data-tippy-root]{max-width:calc(100vw - 10px)}.tippy-box{position:relative;background-color:#333;color:#fff;border-radius:4px;font-size:14px;line-height:1.4;white-space:normal;outline:0;transition-property:transform,visibility,opacity}.tippy-box[data-placement^=top]>.tippy-arrow{bottom:0}.tippy-box[data-placement^=top]>.tippy-arrow:before{bottom:-7px;left:0;border-width:8px 8px 0;border-top-color:initial;transform-origin:center top}.tippy-box[data-placement^=bottom]>.tippy-arrow{top:0}.tippy-box[data-placement^=bottom]>.tippy-arrow:before{top:-7px;left:0;border-width:0 8px 8px;border-bottom-color:initial;transform-origin:center bottom}.tippy-box[data-placement^=left]>.tippy-arrow{right:0}.tippy-box[data-placement^=left]>.tippy-arrow:before{border-width:8px 0 8px 8px;border-left-color:initial;right:-7px;transform-origin:center left}.tippy-box[data-placement^=right]>.tippy-arrow{left:0}.tippy-box[data-placement^=right]>.tippy-arrow:before{left:-7px;border-width:8px 8px 8px 0;border-right-color:initial;transform-origin:center right}.tippy-box[data-inertia][data-state=visible]{transition-timing-function:cubic-bezier(.54,1.5,.38,1.11)}.tippy-arrow{width:16px;height:16px;color:#333}.tippy-arrow:before{content:"";position:absolute;border-color:transparent;border-style:solid}.tippy-content{position:relative;padding:5px 9px;z-index:1}',""]);const s=a}}]);
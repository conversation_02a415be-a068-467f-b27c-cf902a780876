(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8145],{4866:t=>{t.exports=function(){return"undefined"!=typeof window&&"object"==typeof window.process&&"renderer"===window.process.type||!("undefined"==typeof process||"object"!=typeof process.versions||!process.versions.electron)||"object"==typeof navigator&&"string"==typeof navigator.userAgent&&navigator.userAgent.indexOf("Electron")>=0}},23527:t=>{"use strict";var e={};function r(t,n){var o;return"string"!=typeof n&&(n=r.defaultChars),o=function(t){var r,n,o=e[t];if(o)return o;for(o=e[t]=[],r=0;r<128;r++)n=String.fromCharCode(r),o.push(n);for(r=0;r<t.length;r++)o[n=t.charCodeAt(r)]="%"+("0"+n.toString(16).toUpperCase()).slice(-2);return o}(n),t.replace(/(%[a-f0-9]{2})+/gi,(function(t){var e,r,n,i,a,s,f,h="";for(e=0,r=t.length;e<r;e+=3)(n=parseInt(t.slice(e+1,e+3),16))<128?h+=o[n]:192==(224&n)&&e+3<r&&128==(192&(i=parseInt(t.slice(e+4,e+6),16)))?(h+=(f=n<<6&1984|63&i)<128?"��":String.fromCharCode(f),e+=3):224==(240&n)&&e+6<r&&(i=parseInt(t.slice(e+4,e+6),16),a=parseInt(t.slice(e+7,e+9),16),128==(192&i)&&128==(192&a))?(h+=(f=n<<12&61440|i<<6&4032|63&a)<2048||f>=55296&&f<=57343?"���":String.fromCharCode(f),e+=6):240==(248&n)&&e+9<r&&(i=parseInt(t.slice(e+4,e+6),16),a=parseInt(t.slice(e+7,e+9),16),s=parseInt(t.slice(e+10,e+12),16),128==(192&i)&&128==(192&a)&&128==(192&s))?((f=n<<18&1835008|i<<12&258048|a<<6&4032|63&s)<65536||f>1114111?h+="����":(f-=65536,h+=String.fromCharCode(55296+(f>>10),56320+(1023&f))),e+=9):h+="�";return h}))}r.defaultChars=";/?:@&=+$,#",r.componentChars="",t.exports=r},24994:t=>{"use strict";function e(){this.protocol=null,this.slashes=null,this.auth=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.pathname=null}var r=/^([a-z0-9.+-]+:)/i,n=/:[0-9]*$/,o=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,i=["{","}","|","\\","^","`"].concat(["<",">",'"',"`"," ","\r","\n","\t"]),a=["'"].concat(i),s=["%","/","?",";","#"].concat(a),f=["/","?","#"],h=/^[+a-z0-9A-Z_-]{0,63}$/,u=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,l={javascript:!0,"javascript:":!0},c={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0};e.prototype.parse=function(t,e){var n,i,a,p,d,v=t;if(v=v.trim(),!e&&1===t.split("#").length){var g=o.exec(v);if(g)return this.pathname=g[1],g[2]&&(this.search=g[2]),this}var w=r.exec(v);if(w&&(a=(w=w[0]).toLowerCase(),this.protocol=w,v=v.substr(w.length)),(e||w||v.match(/^\/\/[^@\/]+@[^@\/]+/))&&(!(d="//"===v.substr(0,2))||w&&l[w]||(v=v.substr(2),this.slashes=!0)),!l[w]&&(d||w&&!c[w])){var m,y,b=-1;for(n=0;n<f.length;n++)-1!==(p=v.indexOf(f[n]))&&(-1===b||p<b)&&(b=p);for(-1!==(y=-1===b?v.lastIndexOf("@"):v.lastIndexOf("@",b))&&(m=v.slice(0,y),v=v.slice(y+1),this.auth=m),b=-1,n=0;n<s.length;n++)-1!==(p=v.indexOf(s[n]))&&(-1===b||p<b)&&(b=p);-1===b&&(b=v.length),":"===v[b-1]&&b--;var x=v.slice(0,b);v=v.slice(b),this.parseHost(x),this.hostname=this.hostname||"";var E="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!E){var C=this.hostname.split(/\./);for(n=0,i=C.length;n<i;n++){var k=C[n];if(k&&!k.match(h)){for(var T="",I=0,A=k.length;I<A;I++)k.charCodeAt(I)>127?T+="x":T+=k[I];if(!T.match(h)){var U=C.slice(0,n),O=C.slice(n+1),S=k.match(u);S&&(U.push(S[1]),O.unshift(S[2])),O.length&&(v=O.join(".")+v),this.hostname=U.join(".");break}}}}this.hostname.length>255&&(this.hostname=""),E&&(this.hostname=this.hostname.substr(1,this.hostname.length-2))}var _=v.indexOf("#");-1!==_&&(this.hash=v.substr(_),v=v.slice(0,_));var B=v.indexOf("?");return-1!==B&&(this.search=v.substr(B),v=v.slice(0,B)),v&&(this.pathname=v),c[a]&&this.hostname&&!this.pathname&&(this.pathname=""),this},e.prototype.parseHost=function(t){var e=n.exec(t);e&&(":"!==(e=e[0])&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)},t.exports=function(t,r){if(t&&t instanceof e)return t;var n=new e;return n.parse(t,r),n}},34399:(t,e)=>{"use strict";function r(t,e,r,n){for(var o=t[e++],i=1<<o,a=i+1,s=a+1,f=o+1,h=(1<<f)-1,u=0,l=0,c=0,p=t[e++],d=new Int32Array(4096),v=null;;){for(;u<16&&0!==p;)l|=t[e++]<<u,u+=8,1===p?p=t[e++]:--p;if(u<f)break;var g=l&h;if(l>>=f,u-=f,g!==i){if(g===a)break;for(var w=g<s?g:v,m=0,y=w;y>i;)y=d[y]>>8,++m;var b=y;if(c+m+(w!==g?1:0)>n)return void console.log("Warning, gif stream longer than expected.");r[c++]=b;var x=c+=m;for(w!==g&&(r[c++]=b),y=w;m--;)y=d[y],r[--x]=255&y,y>>=8;null!==v&&s<4096&&(d[s++]=v<<8|b,s>=h+1&&f<12&&(++f,h=h<<1|1)),v=g}else s=a+1,h=(1<<(f=o+1))-1,v=null}return c!==n&&console.log("Warning, gif stream shorter than expected."),r}try{e.GifWriter=function(t,e,r,n){var o=0,i=void 0===(n=void 0===n?{}:n).loop?null:n.loop,a=void 0===n.palette?null:n.palette;if(e<=0||r<=0||e>65535||r>65535)throw new Error("Width/Height invalid.");function s(t){var e=t.length;if(e<2||e>256||e&e-1)throw new Error("Invalid code/color length, must be power of 2 and 2 .. 256.");return e}t[o++]=71,t[o++]=73,t[o++]=70,t[o++]=56,t[o++]=57,t[o++]=97;var f=0,h=0;if(null!==a){for(var u=s(a);u>>=1;)++f;if(u=1<<f,--f,void 0!==n.background){if((h=n.background)>=u)throw new Error("Background index out of range.");if(0===h)throw new Error("Background index explicitly passed as 0.")}}if(t[o++]=255&e,t[o++]=e>>8&255,t[o++]=255&r,t[o++]=r>>8&255,t[o++]=(null!==a?128:0)|f,t[o++]=h,t[o++]=0,null!==a)for(var l=0,c=a.length;l<c;++l){var p=a[l];t[o++]=p>>16&255,t[o++]=p>>8&255,t[o++]=255&p}if(null!==i){if(i<0||i>65535)throw new Error("Loop count invalid.");t[o++]=33,t[o++]=255,t[o++]=11,t[o++]=78,t[o++]=69,t[o++]=84,t[o++]=83,t[o++]=67,t[o++]=65,t[o++]=80,t[o++]=69,t[o++]=50,t[o++]=46,t[o++]=48,t[o++]=3,t[o++]=1,t[o++]=255&i,t[o++]=i>>8&255,t[o++]=0}var d=!1;this.addFrame=function(e,r,n,i,f,h){if(!0===d&&(--o,d=!1),h=void 0===h?{}:h,e<0||r<0||e>65535||r>65535)throw new Error("x/y invalid.");if(n<=0||i<=0||n>65535||i>65535)throw new Error("Width/Height invalid.");if(f.length<n*i)throw new Error("Not enough pixels for the frame size.");var u=!0,l=h.palette;if(null==l&&(u=!1,l=a),null==l)throw new Error("Must supply either a local or global palette.");for(var c=s(l),p=0;c>>=1;)++p;c=1<<p;var v=void 0===h.delay?0:h.delay,g=void 0===h.disposal?0:h.disposal;if(g<0||g>3)throw new Error("Disposal out of range.");var w=!1,m=0;if(void 0!==h.transparent&&null!==h.transparent&&(w=!0,(m=h.transparent)<0||m>=c))throw new Error("Transparent color index.");if((0!==g||w||0!==v)&&(t[o++]=33,t[o++]=249,t[o++]=4,t[o++]=g<<2|(!0===w?1:0),t[o++]=255&v,t[o++]=v>>8&255,t[o++]=m,t[o++]=0),t[o++]=44,t[o++]=255&e,t[o++]=e>>8&255,t[o++]=255&r,t[o++]=r>>8&255,t[o++]=255&n,t[o++]=n>>8&255,t[o++]=255&i,t[o++]=i>>8&255,t[o++]=!0===u?128|p-1:0,!0===u)for(var y=0,b=l.length;y<b;++y){var x=l[y];t[o++]=x>>16&255,t[o++]=x>>8&255,t[o++]=255&x}return o=function(t,e,r,n){t[e++]=r;var o=e++,i=1<<r,a=i-1,s=i+1,f=s+1,h=r+1,u=0,l=0;function c(r){for(;u>=r;)t[e++]=255&l,l>>=8,u-=8,e===o+256&&(t[o]=255,o=e++)}function p(t){l|=t<<u,u+=h,c(8)}var d=n[0]&a,v={};p(i);for(var g=1,w=n.length;g<w;++g){var m=n[g]&a,y=d<<8|m,b=v[y];if(void 0===b){for(l|=d<<u,u+=h;u>=8;)t[e++]=255&l,l>>=8,u-=8,e===o+256&&(t[o]=255,o=e++);4096===f?(p(i),f=s+1,h=r+1,v={}):(f>=1<<h&&++h,v[y]=f++),d=m}else d=b}return p(d),p(s),c(1),o+1===e?t[o]=0:(t[o]=e-o-1,t[e++]=0),e}(t,o,p<2?2:p,f),o},this.end=function(){return!1===d&&(t[o++]=59,d=!0),o},this.getOutputBuffer=function(){return t},this.setOutputBuffer=function(e){t=e},this.getOutputBufferPosition=function(){return o},this.setOutputBufferPosition=function(t){o=t}},e.GifReader=function(t){var e=0;if(71!==t[e++]||73!==t[e++]||70!==t[e++]||56!==t[e++]||56!=(t[e++]+1&253)||97!==t[e++])throw new Error("Invalid GIF 87a/89a header.");var n=t[e++]|t[e++]<<8,o=t[e++]|t[e++]<<8,i=t[e++],a=i>>7,s=1<<1+(7&i);t[e++],t[e++];var f=null,h=null;a&&(f=e,h=s,e+=3*s);var u=!0,l=[],c=0,p=null,d=0,v=null;for(this.width=n,this.height=o;u&&e<t.length;)switch(t[e++]){case 33:switch(t[e++]){case 255:if(11!==t[e]||78==t[e+1]&&69==t[e+2]&&84==t[e+3]&&83==t[e+4]&&67==t[e+5]&&65==t[e+6]&&80==t[e+7]&&69==t[e+8]&&50==t[e+9]&&46==t[e+10]&&48==t[e+11]&&3==t[e+12]&&1==t[e+13]&&0==t[e+16])e+=14,v=t[e++]|t[e++]<<8,e++;else for(e+=12;;){if(!((U=t[e++])>=0))throw Error("Invalid block size");if(0===U)break;e+=U}break;case 249:if(4!==t[e++]||0!==t[e+4])throw new Error("Invalid graphics extension block.");var g=t[e++];c=t[e++]|t[e++]<<8,p=t[e++],1&g||(p=null),d=g>>2&7,e++;break;case 254:for(;;){if(!((U=t[e++])>=0))throw Error("Invalid block size");if(0===U)break;e+=U}break;default:throw new Error("Unknown graphic control label: 0x"+t[e-1].toString(16))}break;case 44:var w=t[e++]|t[e++]<<8,m=t[e++]|t[e++]<<8,y=t[e++]|t[e++]<<8,b=t[e++]|t[e++]<<8,x=t[e++],E=x>>6&1,C=1<<1+(7&x),k=f,T=h,I=!1;x>>7&&(I=!0,k=e,T=C,e+=3*C);var A=e;for(e++;;){var U;if(!((U=t[e++])>=0))throw Error("Invalid block size");if(0===U)break;e+=U}l.push({x:w,y:m,width:y,height:b,has_local_palette:I,palette_offset:k,palette_size:T,data_offset:A,data_length:e-A,transparent_index:p,interlaced:!!E,delay:c,disposal:d});break;case 59:u=!1;break;default:throw new Error("Unknown gif block: 0x"+t[e-1].toString(16))}this.numFrames=function(){return l.length},this.loopCount=function(){return v},this.frameInfo=function(t){if(t<0||t>=l.length)throw new Error("Frame index out of range.");return l[t]},this.decodeAndBlitFrameBGRA=function(e,o){var i=this.frameInfo(e),a=i.width*i.height,s=new Uint8Array(a);r(t,i.data_offset,s,a);var f=i.palette_offset,h=i.transparent_index;null===h&&(h=256);var u=i.width,l=n-u,c=u,p=4*(i.y*n+i.x),d=4*((i.y+i.height)*n+i.x),v=p,g=4*l;!0===i.interlaced&&(g+=4*n*7);for(var w=8,m=0,y=s.length;m<y;++m){var b=s[m];if(0===c&&(c=u,(v+=g)>=d&&(g=4*l+4*n*(w-1),v=p+(u+l)*(w<<1),w>>=1)),b===h)v+=4;else{var x=t[f+3*b],E=t[f+3*b+1],C=t[f+3*b+2];o[v++]=C,o[v++]=E,o[v++]=x,o[v++]=255}--c}},this.decodeAndBlitFrameRGBA=function(e,o){var i=this.frameInfo(e),a=i.width*i.height,s=new Uint8Array(a);r(t,i.data_offset,s,a);var f=i.palette_offset,h=i.transparent_index;null===h&&(h=256);var u=i.width,l=n-u,c=u,p=4*(i.y*n+i.x),d=4*((i.y+i.height)*n+i.x),v=p,g=4*l;!0===i.interlaced&&(g+=4*n*7);for(var w=8,m=0,y=s.length;m<y;++m){var b=s[m];if(0===c&&(c=u,(v+=g)>=d&&(g=4*l+4*n*(w-1),v=p+(u+l)*(w<<1),w>>=1)),b===h)v+=4;else{var x=t[f+3*b],E=t[f+3*b+1],C=t[f+3*b+2];o[v++]=x,o[v++]=E,o[v++]=C,o[v++]=255}--c}}}}catch(t){}},37056:t=>{t.exports=function(t){if(!t)return!1;var r=e.call(t);return"[object Function]"===r||"function"==typeof t&&"[object RegExp]"!==r||"undefined"!=typeof window&&(t===window.setTimeout||t===window.alert||t===window.confirm||t===window.prompt)};var e=Object.prototype.toString},43331:t=>{"use strict";var e={};function r(t,n,o){var i,a,s,f,h,u="";for("string"!=typeof n&&(o=n,n=r.defaultChars),void 0===o&&(o=!0),h=function(t){var r,n,o=e[t];if(o)return o;for(o=e[t]=[],r=0;r<128;r++)n=String.fromCharCode(r),/^[0-9a-z]$/i.test(n)?o.push(n):o.push("%"+("0"+r.toString(16).toUpperCase()).slice(-2));for(r=0;r<t.length;r++)o[t.charCodeAt(r)]=t[r];return o}(n),i=0,a=t.length;i<a;i++)if(s=t.charCodeAt(i),o&&37===s&&i+2<a&&/^[0-9a-f]{2}$/i.test(t.slice(i+1,i+3)))u+=t.slice(i,i+3),i+=2;else if(s<128)u+=h[s];else if(s>=55296&&s<=57343){if(s>=55296&&s<=56319&&i+1<a&&(f=t.charCodeAt(i+1))>=56320&&f<=57343){u+=encodeURIComponent(t[i]+t[i+1]),i++;continue}u+="%EF%BF%BD"}else u+=encodeURIComponent(t[i]);return u}r.defaultChars=";/?:@&=+$,-_.!~*'()#",r.componentChars="-_.!~*'()",t.exports=r},43480:(t,e,r)=>{var n=r(16928),o=r(79896),i=parseInt("0777",8);function a(t,e,r,s){"function"==typeof e?(r=e,e={}):e&&"object"==typeof e||(e={mode:e});var f=e.mode,h=e.fs||o;void 0===f&&(f=i),s||(s=null);var u=r||function(){};t=n.resolve(t),h.mkdir(t,f,(function(r){if(!r)return u(null,s=s||t);if("ENOENT"===r.code){if(n.dirname(t)===t)return u(r);a(n.dirname(t),e,(function(r,n){r?u(r,n):a(t,e,u,n)}))}else h.stat(t,(function(t,e){t||!e.isDirectory()?u(r,s):u(null,s)}))}))}t.exports=a.mkdirp=a.mkdirP=a,a.sync=function t(e,r,a){r&&"object"==typeof r||(r={mode:r});var s=r.mode,f=r.fs||o;void 0===s&&(s=i),a||(a=null),e=n.resolve(e);try{f.mkdirSync(e,s),a=a||e}catch(o){if("ENOENT"===o.code)a=t(n.dirname(e),r,a),t(e,r,a);else{var h;try{h=f.statSync(e)}catch(t){throw o}if(!h.isDirectory())throw o}}return a}},43626:t=>{var e=Math.pow(2,32);t.exports={getUint64:function(t){var r,n=new DataView(t.buffer,t.byteOffset,t.byteLength);return n.getBigUint64?(r=n.getBigUint64(0))<Number.MAX_SAFE_INTEGER?Number(r):r:n.getUint32(0)*e+n.getUint32(4)},MAX_UINT32:e}},79622:t=>{var e,r,n,o,i,a,s,f=9e4;e=function(t){return t*f},r=function(t,e){return t*e},n=function(t){return t/f},o=function(t,e){return t/e},i=function(t,r){return e(o(t,r))},a=function(t,e){return r(n(t),e)},s=function(t,e,r){return n(r?t:t-e)},t.exports={ONE_SECOND_IN_TS:f,secondsToVideoTs:e,secondsToAudioTs:r,videoTsToSeconds:n,audioTsToSeconds:o,audioTsToVideoTs:i,videoTsToAudioTs:a,metadataTsToSeconds:s}},86781:(t,e,r)=>{"use strict";t.exports.encode=r(43331),t.exports.decode=r(23527),t.exports.format=r(86998),t.exports.parse=r(24994)},86998:t=>{"use strict";t.exports=function(t){var e="";return e+=t.protocol||"",e+=t.slashes?"//":"",e+=t.auth?t.auth+"@":"",t.hostname&&-1!==t.hostname.indexOf(":")?e+="["+t.hostname+"]":e+=t.hostname||"",e+=t.port?":"+t.port:"",e+=t.pathname||"",(e+=t.search||"")+(t.hash||"")}},89690:(t,e,r)=>{var n=r(33922),o=Buffer.from([66,77,70,3]);t.exports=function(t){return"string"==typeof t?"BMF"===t.substring(0,3):t.length>4&&n(t.slice(0,4),o)}},92908:(t,e,r)=>{var n=r(91386),o=function(){},i=r(74361),a=r(223),s=r(30791),f=r(89690),h=r(57510),u=self.XMLHttpRequest&&"withCredentials"in new XMLHttpRequest;t.exports=function(t,e){e="function"==typeof e?e:o,"string"==typeof t?t={uri:t}:t||(t={}),t.binary&&(t=function(t){if(u)return h(t,{responseType:"arraybuffer"});if(void 0===self.XMLHttpRequest)throw new Error("your browser does not support XHR loading");var e=new self.XMLHttpRequest;return e.overrideMimeType("text/plain; charset=x-user-defined"),h({xhr:e},t)}(t)),n(t,(function(r,n,h){if(r)return e(r);if(!/^2/.test(n.statusCode))return e(new Error("http status code: "+n.statusCode));if(!h)return e(new Error("no body result"));var u,l,c=!1;if(u=h,"[object ArrayBuffer]"===Object.prototype.toString.call(u)){var p=new Uint8Array(h);h=Buffer.from(p,"binary")}f(h)&&(c=!0,"string"==typeof h&&(h=Buffer.from(h,"binary"))),c||(Buffer.isBuffer(h)&&(h=h.toString(t.encoding)),h=h.trim());try{var d=n.headers["content-type"];l=c?s(h):/json/.test(d)||"{"===h.charAt(0)?JSON.parse(h):/xml/.test(d)||"<"===h.charAt(0)?a(h):i(h)}catch(t){e(new Error("error parsing font "+t.message)),e=o}e(null,l)}))}},98962:(t,e,r)=>{var n=r(43626).getUint64;t.exports=function(t){var e=new DataView(t.buffer,t.byteOffset,t.byteLength),r={version:t[0],flags:new Uint8Array(t.subarray(1,4)),references:[],referenceId:e.getUint32(4),timescale:e.getUint32(8)},o=12;0===r.version?(r.earliestPresentationTime=e.getUint32(o),r.firstOffset=e.getUint32(o+4),o+=8):(r.earliestPresentationTime=n(t.subarray(o)),r.firstOffset=n(t.subarray(o+8)),o+=16),o+=2;var i=e.getUint16(o);for(o+=2;i>0;o+=12,i--)r.references.push({referenceType:(128&t[o])>>>7,referencedSize:2147483647&e.getUint32(o),subsegmentDuration:e.getUint32(o+4),startsWithSap:!!(128&t[o+8]),sapType:(112&t[o+8])>>>4,sapDeltaTime:268435455&e.getUint32(o+8)});return r}}}]);
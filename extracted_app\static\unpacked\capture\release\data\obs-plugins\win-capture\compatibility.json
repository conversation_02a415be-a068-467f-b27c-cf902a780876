{"$schema": "schema/compatibility-schema-v1.json", "format_version": 1, "entries": [{"name": "CS:GO", "translation_key": "Compatibility.Application.CSGO", "severity": 1, "executable": "csgo.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "CS:GO may require the <code>-allow_third_party_software</code> launch option to use Game Capture.", "url": "https://help.steampowered.com/faqs/view/09A0-4879-4353-EF95#whitelist"}, {"name": "Counter-Strike 2", "translation_key": "Compatibility.Application.CSGO", "severity": 1, "executable": "cs2.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Counter-Strike 2 may require the <code>-allow_third_party_software</code> launch option to use Game Capture.", "url": "https://help.steampowered.com/faqs/view/09A0-4879-4353-EF95#whitelist"}, {"name": "Chromium", "translation_key": "Compatibility.WindowCapture.BitBlt.Applications.Based", "severity": 0, "executable": "", "window_class": "Chrome_WidgetWin_0", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Applications based on Chromium may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Chromium", "translation_key": "Compatibility.WindowCapture.BitBlt.Applications.Based", "severity": 0, "executable": "", "window_class": "Chrome_WidgetWin_1", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Applications based on Chromium may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Chromium", "translation_key": "Compatibility.GameCapture.Blocked.Applications.Built", "severity": 2, "executable": "", "window_class": "Chrome_WidgetWin_0", "window_title": "", "match_flags": 4, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Games built on Chromium cannot be captured using Game Capture. Use Window Capture or Display Capture instead.", "url": ""}, {"name": "Chromium", "translation_key": "Compatibility.GameCapture.Blocked.Applications.Built", "severity": 2, "executable": "", "window_class": "Chrome_WidgetWin_1", "window_title": "", "match_flags": 4, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Games built on Chromium cannot be captured using Game Capture. Use Window Capture or Display Capture instead.", "url": ""}, {"name": "UWP", "translation_key": "Compatibility.WindowCapture.BitBlt.Applications", "severity": 0, "executable": "", "window_class": "ApplicationFrameWindow", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "UWP applications may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "UWP", "translation_key": "Compatibility.WindowCapture.BitBlt.Applications", "severity": 0, "executable": "", "window_class": "Windows.UI.Core.CoreWindow", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "UWP applications may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "WinUI 3", "translation_key": "Compatibility.WindowCapture.BitBlt.Applications", "severity": 0, "executable": "", "window_class": "WinUIDesktopWin32WindowClass", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "WinUI 3 applications may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Gaming Services", "translation_key": "Compatibility.WindowCapture.BitBlt.Applications", "severity": 0, "executable": "", "window_class": "GAMINGSERVICESUI_HOSTING_WINDOW_CLASS", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Gaming Services applications may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Gaming Services", "translation_key": "Compatibility.GameCapture.Blocked.Applications", "severity": 2, "executable": "", "window_class": "GAMINGSERVICESUI_HOSTING_WINDOW_CLASS", "window_title": "", "match_flags": 4, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Gaming Services applications cannot be captured using Game Capture. Use Window Capture or Display Capture instead.", "url": ""}, {"name": "Minecraft: Java Edition", "translation_key": "Compatibility.Application.Minecraft", "severity": 0, "executable": "javaw.exe", "window_class": "", "window_title": "Minecraft", "match_flags": 3, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "If you're having issues capturing Minecraft: Java Edition check our troubleshooting guide.", "url": "https://obsproject.com/kb/minecraft-java-edition-troubleshooting"}, {"name": "Call of Duty", "translation_key": "Compatibility.GameCapture.Admin", "severity": 1, "executable": "cod.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Call of Duty may require OBS to be run as admin to use Game Capture.", "url": "https://obsproject.com/kb/game-capture-troubleshooting"}, {"name": "Genshin Impact", "translation_key": "Compatibility.GameCapture.Admin", "severity": 1, "executable": "GenshinImpact.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Genshin Impact may require OBS to be run as admin to use Game Capture.", "url": "https://obsproject.com/kb/game-capture-troubleshooting"}, {"name": "Destiny 2", "translation_key": "Compatibility.GameCapture.Blocked", "severity": 2, "executable": "destiny2.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Destiny 2 cannot be captured using Game Capture. Use Window Capture or Display Capture instead.", "url": "https://www.bungie.net/en/Help/Article/46101"}, {"name": "Grand Theft Auto: San Andreas", "translation_key": "Compatibility.GameCapture.Blocked", "severity": 2, "executable": "gta-sa.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Grand Theft Auto: San Andreas cannot be captured using Game Capture. Use Window Capture or Display Capture instead.", "url": ""}, {"name": "League of Legends Launcher", "translation_key": "Compatibility.GameCapture.Blocked", "severity": 1, "executable": "LeagueClientUx.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "League of Legends Launcher cannot be captured using Game Capture. Use Window Capture or Display Capture instead.", "url": ""}, {"name": "<PERSON> Multiplayer", "translation_key": "Compatibility.GameCapture.Blocked", "severity": 2, "executable": "samp.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "San Andreas Multiplayer cannot be captured using Game Capture. Use Window Capture or Display Capture instead.", "url": ""}, {"name": "Terraria", "translation_key": "Compatibility.GameCapture.WrongGPU", "severity": 0, "executable": "terraria.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Terraria must run on the same GPU as OBS. If you are not seeing a preview, follow our troubleshooting guide.", "url": "https://obsproject.com/kb/gpu-selection-guide"}, {"name": "Microsoft Excel", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "XLMAIN", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft Excel may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft PowerPoint", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "PPTFrameClass", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft PowerPoint may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft PowerPoint", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "screenClass", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft PowerPoint may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft PowerPoint", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "PodiumParent", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft PowerPoint may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft Word", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "OpusApp", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft Word may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft Access", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "<PERSON><PERSON><PERSON>", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft Access may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft OneNote", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "Framework::CFrame", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft OneNote may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft Outlook", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "rctrl_renwnd32", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft Outlook may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft Publisher", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "MSWinPub", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft Publisher may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Microsoft 365", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "OfficeApp-Frame", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Microsoft 365 may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Adobe After Effects", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "AfterFX.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Adobe After Effects may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Adobe Character Animator", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "Character Animator.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Adobe Character Animator may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Adobe Photoshop", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "Photoshop.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Adobe Photoshop may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Adobe Premiere Pro", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "Adobe Premiere Pro.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Adobe Premiere Pro may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Steam", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "", "window_class": "SDL_app", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Steam may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Epic Games Launcher", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "EpicGamesLauncher.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Epic Games Launcher may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Ubisoft Connect", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "upc.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Ubisoft Connect may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Tencent GUI", "translation_key": "Compatibility.WindowCapture.BitBlt.Applications.Based", "severity": 0, "executable": "", "window_class": "TXGuiFoundation", "window_title": "", "match_flags": 4, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "Applications based on Tencent GUI may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "WeChat", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "WeChat.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "WeChat may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "YY", "translation_key": "Compatibility.WindowCapture.BitBlt", "severity": 0, "executable": "YY.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": false, "window_capture": true, "window_capture_wgc": false, "message": "YY may not be capturable using the selected Capture Method (BitBlt).", "url": ""}, {"name": "Honkai: Star Rail", "translation_key": "Compatibility.GameCapture.Admin", "severity": 1, "executable": "StarRail.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Honkai: Star Rail may require OBS to be run as admin to use Game Capture.", "url": "https://obsproject.com/kb/game-capture-troubleshooting"}, {"name": "Zenless Zone Zero", "translation_key": "Compatibility.GameCapture.Admin", "severity": 1, "executable": "ZenlessZoneZero.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Zenless Zone Zero may require OBS to be run as admin to use Game Capture.", "url": "https://obsproject.com/kb/game-capture-troubleshooting"}, {"name": "Marvel Rivals", "translation_key": "Compatibility.GameCapture.Admin", "severity": 1, "executable": "Marvel-Win64-Shipping.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "Marvel Rivals may require OBS to be run as admin to use Game Capture.", "url": "https://obsproject.com/kb/game-capture-troubleshooting"}, {"name": "The Bazaar", "translation_key": "Compatibility.GameCapture.Admin", "severity": 1, "executable": "TheBazaar.exe", "window_class": "", "window_title": "", "match_flags": 1, "game_capture": true, "window_capture": false, "window_capture_wgc": false, "message": "The Bazaar may require OBS to be run as admin to use Game Capture.", "url": "https://obsproject.com/kb/game-capture-troubleshooting"}]}
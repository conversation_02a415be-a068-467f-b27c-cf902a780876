(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[339],{7713:(e,r,t)=>{!function(){var r={};function i(){"undefined"==typeof process&&console.log.apply(console,arguments)}e.exports=r,function(e,r){var t,n,a;t="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n=function(){function e(e){this.message="JPEG error: "+e}return e.prototype=Error(),e.prototype.name="JpegError",e.constructor=e}(),a=function(){function e(e,r){this.message=e,this.g=r}return e.prototype=Error(),e.prototype.name="DNLMarkerError",e.constructor=e}(),function(){function r(){this.M=null,this.B=-1}function i(e,r){for(var t,i,n=0,a=[],o=16;0<o&&!e[o-1];)o--;a.push({children:[],index:0});var f,l=a[0];for(t=0;t<o;t++){for(i=0;i<e[t];i++){for((l=a.pop()).children[l.index]=r[n];0<l.index;)l=a.pop();for(l.index++,a.push(l);a.length<=t;)a.push(f={children:[],index:0}),l.children[l.index]=f.children,l=f;n++}t+1<o&&(a.push(f={children:[],index:0}),l.children[l.index]=f.children,l=f)}return a[0].children}function o(e,r,i,o,f,u,d,c,h){function v(){if(0<U)return U--,S>>U&1;if(255===(S=e[r++])){var t=e[r++];if(t){if(220===t&&D){r+=2;var o=e[r++]<<8|e[r++];if(0<o&&o!==i.g)throw new a("Found DNL marker (0xFFDC) while parsing scan data",o)}throw new n("unexpected marker "+(S<<8|t).toString(16))}}return U=7,S>>>7}function g(e){for(;;){if("number"==typeof(e=e[v()]))return e;if("object"!==(void 0===e?"undefined":t(e)))throw new n("invalid huffman sequence")}}function p(e){for(var r=0;0<e;)r=r<<1|v(),e--;return r}function w(e){if(1===e)return 1===v()?1:-1;var r=p(e);return r>=1<<e-1?r:r+(-1<<e)+1}for(var m,b,_,y,E,P,I,D=9<arguments.length&&void 0!==arguments[9]&&arguments[9],A=i.P,C=r,S=0,U=0,B=0,M=0,k=o.length,F=i.S?0===u?0===c?function(e,r){var t=g(e.D);t=0===t?0:w(t)<<h,e.a[r]=e.m+=t}:function(e,r){e.a[r]|=v()<<h}:0===c?function(e,r){if(0<B)B--;else for(var t=u;t<=d;){var i=g(e.o),n=15&i;if(i>>=4,0===n){if(15>i){B=p(i)+(1<<i)-1;break}t+=16}else t+=i,e.a[r+s[t]]=w(n)*(1<<h),t++}}:function(e,r){for(var t,i=u,a=0;i<=d;){t=r+s[i];var o=0>e.a[t]?-1:1;switch(M){case 0:if(t=15&(a=g(e.o)),a>>=4,0===t)15>a?(B=p(a)+(1<<a),M=4):(a=16,M=1);else{if(1!==t)throw new n("invalid ACn encoding");m=w(t),M=a?2:3}continue;case 1:case 2:e.a[t]?e.a[t]+=o*(v()<<h):0==--a&&(M=2===M?3:0);break;case 3:e.a[t]?e.a[t]+=o*(v()<<h):(e.a[t]=m<<h,M=0);break;case 4:e.a[t]&&(e.a[t]+=o*(v()<<h))}i++}4===M&&0==--B&&(M=0)}:function(e,r){var t=g(e.D);for(t=0===t?0:w(t),e.a[r]=e.m+=t,t=1;64>t;){var i=g(e.o),n=15&i;if(i>>=4,0===n){if(15>i)break;t+=16}else t+=i,e.a[r+s[t]]=w(n),t++}},G=0,T=1===k?o[0].c*o[0].l:A*i.O;G<T;){var x=f?Math.min(T-G,f):T;for(b=0;b<k;b++)o[b].m=0;if(B=0,1===k){var N=o[0];for(E=0;E<x;E++)F(N,64*((N.c+1)*(G/N.c|0)+G%N.c)),G++}else for(E=0;E<x;E++){for(b=0;b<k;b++)for(P=(N=o[b]).h,I=N.j,_=0;_<I;_++)for(y=0;y<P;y++)F(N,64*((N.c+1)*((G/A|0)*N.j+_)+(G%A*N.h+y)));G++}if(U=0,(N=l(e,r))&&N.f&&((0,_util.warn)("decodeScan - unexpected MCU data, current marker is: "+N.f),r=N.offset),!(N=N&&N.F)||65280>=N)throw new n("marker was not found");if(!(65488<=N&&65495>=N))break;r+=2}return(N=l(e,r))&&N.f&&((0,_util.warn)("decodeScan - unexpected Scan data, current marker is: "+N.f),r=N.offset),r-C}function f(e,r){for(var t=r.c,i=r.l,a=new Int16Array(64),o=0;o<i;o++)for(var f=0;f<t;f++){var l=64*((r.c+1)*o+f),s=a,u=r.G,d=r.a;if(!u)throw new n("missing required Quantization Table.");for(var c=0;64>c;c+=8){var h=d[l+c],v=d[l+c+1],g=d[l+c+2],p=d[l+c+3],w=d[l+c+4],m=d[l+c+5],b=d[l+c+6],_=d[l+c+7];if(h*=u[c],v|g|p|w|m|b|_){v*=u[c+1],g*=u[c+2],p*=u[c+3],w*=u[c+4],m*=u[c+5];var y=5793*h+128>>8,E=5793*w+128>>8,P=g,I=b*=u[c+6];E=(y=y+E+1>>1)-E,h=3784*P+1567*I+128>>8,P=1567*P-3784*I+128>>8,m=(w=(w=2896*(v-(_*=u[c+7]))+128>>8)+(m<<=4)+1>>1)-m,p=(_=(_=2896*(v+_)+128>>8)+(p<<=4)+1>>1)-p,I=(y=y+(I=h)+1>>1)-I,P=(E=E+P+1>>1)-P,h=2276*w+3406*_+2048>>12,w=3406*w-2276*_+2048>>12,_=h,h=799*p+4017*m+2048>>12,p=4017*p-799*m+2048>>12,m=h,s[c]=y+_,s[c+7]=y-_,s[c+1]=E+m,s[c+6]=E-m,s[c+2]=P+p,s[c+5]=P-p,s[c+3]=I+w,s[c+4]=I-w}else h=5793*h+512>>10,s[c]=h,s[c+1]=h,s[c+2]=h,s[c+3]=h,s[c+4]=h,s[c+5]=h,s[c+6]=h,s[c+7]=h}for(u=0;8>u;++u)h=s[u],(v=s[u+8])|(g=s[u+16])|(p=s[u+24])|(w=s[u+32])|(m=s[u+40])|(b=s[u+48])|(_=s[u+56])?(y=5793*h+2048>>12,E=5793*w+2048>>12,h=3784*(P=g)+1567*(I=b)+2048>>12,P=1567*P-3784*I+2048>>12,I=h,m=(w=(w=2896*(v-_)+2048>>12)+m+1>>1)-m,p=(_=(_=2896*(v+_)+2048>>12)+p+1>>1)-p,h=2276*w+3406*_+2048>>12,w=3406*w-2276*_+2048>>12,_=h,h=799*p+4017*m+2048>>12,p=4017*p-799*m+2048>>12,v=(E=(E=(y=4112+(y+E+1>>1))-E)+P+1>>1)+(m=h),b=E-m,m=(P=E-P)-p,h=16>(h=(y=y+I+1>>1)+_)?0:4080<=h?255:h>>4,v=16>v?0:4080<=v?255:v>>4,g=16>(g=P+p)?0:4080<=g?255:g>>4,p=16>(p=(I=y-I)+w)?0:4080<=p?255:p>>4,w=16>(w=I-w)?0:4080<=w?255:w>>4,m=16>m?0:4080<=m?255:m>>4,b=16>b?0:4080<=b?255:b>>4,_=16>(_=y-_)?0:4080<=_?255:_>>4,d[l+u]=h,d[l+u+8]=v,d[l+u+16]=g,d[l+u+24]=p,d[l+u+32]=w,d[l+u+40]=m,d[l+u+48]=b,d[l+u+56]=_):(h=-2040>(h=5793*h+8192>>14)?0:2024<=h?255:h+2056>>4,d[l+u]=h,d[l+u+8]=h,d[l+u+16]=h,d[l+u+24]=h,d[l+u+32]=h,d[l+u+40]=h,d[l+u+48]=h,d[l+u+56]=h)}return r.a}function l(e,r){var t=2<arguments.length&&void 0!==arguments[2]?arguments[2]:r,i=e.length-1;if(t=t<r?t:r,r>=i)return null;var n=e[r]<<8|e[r+1];if(65472<=n&&65534>=n)return{f:null,F:n,offset:r};for(var a=e[t]<<8|e[t+1];!(65472<=a&&65534>=a);){if(++t>=i)return null;a=e[t]<<8|e[t+1]}return{f:n.toString(16),F:a,offset:t}}var s=new Uint8Array([0,1,8,16,9,2,3,10,17,24,32,25,18,11,4,5,12,19,26,33,40,48,41,34,27,20,13,6,7,14,21,28,35,42,49,56,57,50,43,36,29,22,15,23,30,37,44,51,58,59,52,45,38,31,39,46,53,60,61,54,47,55,62,63]);r.prototype={parse:function(e){function r(){var r=e[h]<<8|e[h+1];return h+=2,r}function t(){var t=r(),i=l(e,t=h+t-2,h);return i&&i.f&&((0,_util.warn)("readDataBlock - incorrect length, current marker is: "+i.f),t=i.offset),t=e.subarray(h,t),h+=t.length,t}function u(e){for(var r=Math.ceil(e.v/8/e.s),t=Math.ceil(e.g/8/e.u),i=0;i<e.b.length;i++){C=e.b[i];var n=Math.ceil(Math.ceil(e.v/8)*C.h/e.s),a=Math.ceil(Math.ceil(e.g/8)*C.j/e.u);C.a=new Int16Array(64*t*C.j*(r*C.h+1)),C.c=n,C.l=a}e.P=r,e.O=t}var d=(1<arguments.length&&void 0!==arguments[1]?arguments[1]:{}).N,c=void 0===d?null:d,h=0,v=null,g=0;d=[];var p=[],w=[],m=r();if(65496!==m)throw new n("SOI not found");for(m=r();65497!==m;){switch(m){case 65504:case 65505:case 65506:case 65507:case 65508:case 65509:case 65510:case 65511:case 65512:case 65513:case 65514:case 65515:case 65516:case 65517:case 65518:case 65519:case 65534:var b=t();65518===m&&65===b[0]&&100===b[1]&&111===b[2]&&98===b[3]&&101===b[4]&&(v={version:b[5]<<8|b[6],Y:b[7]<<8|b[8],Z:b[9]<<8|b[10],W:b[11]});break;case 65499:m=r()+h-2;for(var _;h<m;){var y=e[h++],E=new Uint16Array(64);if(y>>4){if(1!=y>>4)throw new n("DQT - invalid table spec");for(b=0;64>b;b++)E[_=s[b]]=r()}else for(b=0;64>b;b++)E[_=s[b]]=e[h++];d[15&y]=E}break;case 65472:case 65473:case 65474:if(P)throw new n("Only single frame JPEGs supported");r();var P={};for(P.X=65473===m,P.S=65474===m,P.precision=e[h++],m=r(),P.g=c||m,P.v=r(),P.b=[],P.C={},b=e[h++],m=E=y=0;m<b;m++){_=e[h];var I=e[h+1]>>4,D=15&e[h+1];y<I&&(y=I),E<D&&(E=D),I=P.b.push({h:I,j:D,T:e[h+2],G:null}),P.C[_]=I-1,h+=3}P.s=y,P.u=E,u(P);break;case 65476:for(_=r(),m=2;m<_;){for(y=e[h++],E=new Uint8Array(16),b=I=0;16>b;b++,h++)I+=E[b]=e[h];for(D=new Uint8Array(I),b=0;b<I;b++,h++)D[b]=e[h];m+=17+I,(y>>4?p:w)[15&y]=i(E,D)}break;case 65501:r();var A=r();break;case 65498:for(b=1==++g&&!c,r(),y=e[h++],_=[],m=0;m<y;m++){E=P.C[e[h++]];var C=P.b[E];E=e[h++],C.D=w[E>>4],C.o=p[15&E],_.push(C)}m=e[h++],y=e[h++],E=e[h++];try{var S=o(e,h,P,_,A,m,y,E>>4,15&E,b);h+=S}catch(r){if(r instanceof a)return(0,_util.warn)('Attempting to re-parse JPEG image using "scanLines" parameter found in DNL marker (0xFFDC) segment.'),this.parse(e,{N:r.g});throw r}break;case 65500:h+=4;break;case 65535:255!==e[h]&&h--;break;default:if(255===e[h-3]&&192<=e[h-2]&&254>=e[h-2])h-=3;else{if(!(b=l(e,h-2))||!b.f)throw new n("unknown marker "+m.toString(16));(0,_util.warn)("JpegImage.parse - unexpected data, current marker is: "+b.f),h=b.offset}}m=r()}for(this.width=P.v,this.height=P.g,this.A=v,this.b=[],m=0;m<P.b.length;m++)(A=d[(C=P.b[m]).T])&&(C.G=A),this.b.push({R:f(0,C),U:C.h/P.s,V:C.j/P.u,c:C.c,l:C.l});this.i=this.b.length},L:function(e,r){var t,i,n=this.width/e,a=this.height/r,o=this.b.length,f=e*r*o,l=new Uint8ClampedArray(f),s=new Uint32Array(e);for(i=0;i<o;i++){var u=this.b[i],d=u.U*n,c=u.V*a,h=i,v=u.R,g=u.c+1<<3;for(t=0;t<e;t++)u=0|t*d,s[t]=(4294967288&u)<<3|7&u;for(d=0;d<r;d++)for(u=g*(4294967288&(u=0|d*c))|(7&u)<<3,t=0;t<e;t++)l[h]=v[u+s[t]],h+=o}if(a=this.M)for(i=0;i<f;)for(n=u=0;u<o;u++,i++,n+=2)l[i]=(l[i]*a[n]>>8)+a[n+1];return l},w:function(){return this.A?!!this.A.W:3===this.i?0!==this.B:1===this.B},I:function(e){for(var r,t,i,n=0,a=e.length;n<a;n+=3)r=e[n],t=e[n+1],i=e[n+2],e[n]=r-179.456+1.402*i,e[n+1]=r+135.459-.344*t-.714*i,e[n+2]=r-226.816+1.772*t;return e},K:function(e){for(var r,t,i,n,a=0,o=0,f=e.length;o<f;o+=4)r=e[o],t=e[o+1],i=e[o+2],n=e[o+3],e[a++]=t*(-660635669420364e-19*t+.000437130475926232*i-54080610064599e-18*r+.00048449797120281*n-.154362151871126)-122.67195406894+i*(-.000957964378445773*i+.000817076911346625*r-.00477271405408747*n+1.53380253221734)+r*(.000961250184130688*r-.00266257332283933*n+.48357088451265)+n*(-.000336197177618394*n+.484791561490776),e[a++]=107.268039397724+t*(219927104525741e-19*t-.000640992018297945*i+.000659397001245577*r+.000426105652938837*n-.176491792462875)+i*(-.000778269941513683*i+.00130872261408275*r+.000770482631801132*n-.151051492775562)+r*(.00126935368114843*r-.00265090189010898*n+.25802910206845)+n*(-.000318913117588328*n-.213742400323665),e[a++]=t*(-.000570115196973677*t-263409051004589e-19*i+.0020741088115012*r-.00288260236853442*n+.814272968359295)-20.810012546947+i*(-153496057440975e-19*i-.000132689043961446*r+.000560833691242812*n-.195152027534049)+r*(.00174418132927582*r-.00255243321439347*n+.116935020465145)+n*(-.000343531996510555*n+.24165260232407);return e.subarray(0,a)},J:function(e){for(var r,t,i,n=0,a=e.length;n<a;n+=4)r=e[n],t=e[n+1],i=e[n+2],e[n]=434.456-r-1.402*i,e[n+1]=119.541-r+.344*t+.714*i,e[n+2]=481.816-r-1.772*t;return e},H:function(e){for(var r,t,i,n,a=0,o=1/255,f=0,l=e.length;f<l;f+=4)r=e[f]*o,t=e[f+1]*o,i=e[f+2]*o,n=e[f+3]*o,e[a++]=255+r*(-4.387332384609988*r+54.48615194189176*t+18.82290502165302*i+212.25662451639585*n-285.2331026137004)+t*(1.7149763477362134*t-5.6096736904047315*i-17.873870861415444*n-5.497006427196366)+i*(-2.5217340131683033*i-21.248923337353073*n+17.5119270841813)-n*(21.86122147463605*n+189.48180835922747),e[a++]=255+r*(8.841041422036149*r+60.118027045597366*t+6.871425592049007*i+31.159100130055922*n-79.2970844816548)+t*(-15.310361306967817*t+17.575251261109482*i+131.35250912493976*n-190.9453302588951)+i*(4.444339102852739*i+9.8632861493405*n-24.86741582555878)-n*(20.737325471181034*n+187.80453709719578),e[a++]=255+r*(.8842522430003296*r+8.078677503112928*t+30.89978309703729*i-.23883238689178934*n-14.183576799673286)+t*(10.49593273432072*t+63.02378494754052*i+50.606957656360734*n-112.23884253719248)+i*(.03296041114873217*i+115.60384449646641*n-193.58209356861505)-n*(22.33816807309886*n+180.12613974708367);return e.subarray(0,a)},getData:function(e,r,t){if(4<this.i)throw new n("Unsupported color mode");if(e=this.L(e,r),1===this.i&&t){t=e.length,r=new Uint8ClampedArray(3*t);for(var i=0,a=0;a<t;a++){var o=e[a];r[i++]=o,r[i++]=o,r[i++]=o}return r}if(3===this.i&&this.w())return this.I(e);if(4===this.i){if(this.w())return t?this.K(e):this.J(e);if(t)return this.H(e)}return e}},e.JpegDecoder=r}(),e.encodeImage=function(r,t,i,n){var a={t256:[t],t257:[i],t258:[8,8,8,8],t259:[1],t262:[2],t273:[1e3],t277:[4],t278:[i],t279:[t*i*4],t282:[1],t283:[1],t284:[1],t286:[0],t287:[0],t296:[1],t305:["Photopea (UTIF.js)"],t338:[1]};if(n)for(var o in n)a[o]=n[o];var f=new Uint8Array(e.encode([a])),l=new Uint8Array(r),s=new Uint8Array(1e3+t*i*4);for(o=0;o<f.length;o++)s[o]=f[o];for(o=0;o<l.length;o++)s[1e3+o]=l[o];return s.buffer},e.encode=function(r){var t=new Uint8Array(2e4),i=4,n=e._binBE;t[0]=77,t[1]=77,t[3]=42;var a=8;n.writeUint(t,i,a),i+=4;for(var o=0;o<r.length;o++){var f=e._writeIFD(n,t,a,r[o]);a=f[1],o<r.length-1&&n.writeUint(t,f[0],a)}return t.slice(0,a).buffer},e.decode=function(r){e.decode._decodeG3.allow2D=null;var t=new Uint8Array(r),i=0,n=e._binBE.readASCII(t,i,2);i+=2;var a="II"==n?e._binLE:e._binBE;a.readUshort(t,i),i+=2;var o=a.readUint(t,i);i+=4;for(var f=[];;){var l=e._readIFD(a,t,o,f);if(0==(o=a.readUint(t,l)))break}return f},e.decodeImages=function(r,t){for(var n=new Uint8Array(r),a=e._binBE.readASCII(n,0,2),o=0;o<t.length;o++){var f=t[o];if(null!=f.t256){f.isLE="II"==a,f.width=f.t256[0],f.height=f.t257[0];var l=f.t259?f.t259[0]:1,s=f.t266?f.t266[0]:1;f.t284&&2==f.t284[0]&&i("PlanarConfiguration 2 should not be used!");var u=(f.t258?Math.min(32,f.t258[0]):1)*(f.t277?f.t277[0]:1),d=8*Math.ceil(f.width*u/8),c=f.t273;null==c&&(c=f.t324);var h=f.t279;1==l&&1==c.length&&(h=[f.height*(d>>>3)]),null==h&&(h=f.t325);var v=new Uint8Array(f.height*(d>>>3)),g=0;if(null!=f.t322){for(var p=f.t322[0],w=f.t323[0],m=Math.floor((f.width+p-1)/p),b=Math.floor((f.height+w-1)/w),_=new Uint8Array(0|Math.ceil(p*w*u/8)),y=0;y<b;y++)for(var E=0;E<m;E++){for(var P=y*m+E,I=0;I<_.length;I++)_[I]=0;e.decode._decompress(f,n,c[P],h[P],l,_,0,s),6==l?v=_:e._copyTile(_,0|Math.ceil(p*u/8),w,v,0|Math.ceil(f.width*u/8),f.height,0|Math.ceil(E*p*u/8),y*w)}g=8*v.length}else{var D=f.t278?f.t278[0]:f.height;for(D=Math.min(D,f.height),P=0;P<c.length;P++)e.decode._decompress(f,n,c[P],h[P],l,v,0|Math.ceil(g/8),s),g+=d*D;g=Math.min(g,8*v.length)}f.data=new Uint8Array(v.buffer,0,0|Math.ceil(g/8))}}},e.decode._decompress=function(t,n,a,o,f,l,s,u){if(1==f)for(var d=0;d<o;d++)l[s+d]=n[a+d];else if(3==f)e.decode._decodeG3(n,a,o,l,s,t.width,u);else if(4==f)e.decode._decodeG4(n,a,o,l,s,t.width,u);else if(5==f)e.decode._decodeLZW(n,a,l,s);else if(6==f)e.decode._decodeOldJPEG(t,n,a,o,l,s);else if(7==f)e.decode._decodeNewJPEG(t,n,a,o,l,s);else if(8==f)for(var c=new Uint8Array(n.buffer,a,o),h=r.inflate(c),v=0;v<h.length;v++)l[s+v]=h[v];else 32773==f?e.decode._decodePackBits(n,a,o,l,s):32809==f?e.decode._decodeThunder(n,a,o,l,s):i("Unknown compression",f);if(t.t317&&2==t.t317[0])for(var g=t.t277?t.t277[0]:1,p=t.t278?t.t278[0]:t.height,w=t.width*g,m=0;m<p;m++){var b=s+m*w;if(3==g)for(d=3;d<w;d+=3)l[b+d]=l[b+d]+l[b+d-3]&255,l[b+d+1]=l[b+d+1]+l[b+d-2]&255,l[b+d+2]=l[b+d+2]+l[b+d-1]&255;else for(d=g;d<w;d++)l[b+d]=l[b+d]+l[b+d-g]&255}},e.decode._decodeNikon=function(e,r,t,n,a){var o,f;i(e.slice(r,r+100)),o=e[r],f=e[++r],r++,i(o.toString(16),f.toString(16),t)},e.decode._decodeNewJPEG=function(r,t,i,n,a,o){var f=r.t347,l=f?f.length:0,s=new Uint8Array(l+n);if(f){for(var u=0,d=0;d<l-1&&(255!=f[d]||217!=f[d+1]);d++)s[u++]=f[d];var c=t[i],h=t[i+1];for(255==c&&216==h||(s[u++]=c,s[u++]=h),d=2;d<n;d++)s[u++]=t[i+d]}else for(d=0;d<n;d++)s[d]=t[i+d];if(32803==r.t262){var v=r.t258[0],g=(new LosslessJpegDecoder).decode(s),p=g.length;if(16==v)for(d=0;d<p;d++)a[o++]=255&g[d],a[o++]=g[d]>>>8;else{if(12!=v)throw new Error("unsupported bit depth "+v);for(d=0;d<p;d+=2)a[o++]=g[d]>>>4,a[o++]=255&(g[d]<<4|g[d+1]>>>8),a[o++]=255&g[d+1]}}else{var w=new e.JpegDecoder;w.parse(s);var m=w.getData(w.width,w.height);for(d=0;d<m.length;d++)a[o+d]=m[d]}6==r.t262[0]&&(r.t262[0]=2)},e.decode._decodeOldJPEGInit=function(e,r,t,n){var a,o,f,l,s,u=216,d=0,c=0,h=!1,v=e.t513,g=v?v[0]:0,p=e.t514,w=p?p[0]:0,m=e.t324||e.t273||v,b=e.t530,_=0,y=0,E=e.t277?e.t277[0]:1,P=e.t515;if(m&&(c=m[0],h=m.length>1),!h){if(255==r[t]&&r[t+1]==u)return{jpegOffset:t};if(null!=v&&(255==r[t+g]&&r[t+g+1]==u?d=t+g:i("JPEGInterchangeFormat does not point to SOI"),null==p?i("JPEGInterchangeFormatLength field is missing"):(g>=c||g+w<=c)&&i("JPEGInterchangeFormatLength field value is invalid"),null!=d))return{jpegOffset:d}}if(null!=b&&(_=b[0],y=b[1]),null!=v&&null!=p)if(w>=2&&g+w<=c){for(a=255==r[t+g+w-2]&&r[t+g+w-1]==u?new Uint8Array(w-2):new Uint8Array(w),f=0;f<a.length;f++)a[f]=r[t+g+f];i("Incorrect JPEG interchange format: using JPEGInterchangeFormat offset to derive tables")}else i("JPEGInterchangeFormat+JPEGInterchangeFormatLength > offset to first strip or tile");if(null==a){var I=0,D=[];D[I++]=255,D[I++]=u;var A=e.t519;if(null==A)throw new Error("JPEGQTables tag is missing");for(f=0;f<A.length;f++)for(D[I++]=255,D[I++]=219,D[I++]=0,D[I++]=67,D[I++]=f,l=0;l<64;l++)D[I++]=r[t+A[f]+l];for(s=0;s<2;s++){var C=e[0==s?"t520":"t521"];if(null==C)throw new Error((0==s?"JPEGDCTables":"JPEGACTables")+" tag is missing");for(f=0;f<C.length;f++){D[I++]=255,D[I++]=196;var S=19;for(l=0;l<16;l++)S+=r[t+C[f]+l];for(D[I++]=S>>>8,D[I++]=255&S,D[I++]=f|s<<4,l=0;l<16;l++)D[I++]=r[t+C[f]+l];for(l=0;l<S;l++)D[I++]=r[t+C[f]+16+l]}}if(D[I++]=255,D[I++]=192,D[I++]=0,D[I++]=8+3*E,D[I++]=8,D[I++]=e.height>>>8&255,D[I++]=255&e.height,D[I++]=e.width>>>8&255,D[I++]=255&e.width,D[I++]=E,1==E)D[I++]=1,D[I++]=17,D[I++]=0;else for(f=0;f<3;f++)D[I++]=f+1,D[I++]=0!=f?17:(15&_)<<4|15&y,D[I++]=f;null!=P&&0!=P[0]&&(D[I++]=255,D[I++]=221,D[I++]=0,D[I++]=4,D[I++]=P[0]>>>8&255,D[I++]=255&P[0]),a=new Uint8Array(D)}var U=-1;for(f=0;f<a.length-1;){if(255==a[f]&&192==a[f+1]){U=f;break}f++}if(-1==U){var B=new Uint8Array(a.length+10+3*E);B.set(a);var M=a.length;if(U=a.length,(a=B)[M++]=255,a[M++]=192,a[M++]=0,a[M++]=8+3*E,a[M++]=8,a[M++]=e.height>>>8&255,a[M++]=255&e.height,a[M++]=e.width>>>8&255,a[M++]=255&e.width,a[M++]=E,1==E)a[M++]=1,a[M++]=17,a[M++]=0;else for(f=0;f<3;f++)a[M++]=f+1,a[M++]=0!=f?17:(15&_)<<4|15&y,a[M++]=f}if(255==r[c]&&218==r[c+1]){var k=r[c+2]<<8|r[c+3];for((o=new Uint8Array(k+2))[0]=r[c],o[1]=r[c+1],o[2]=r[c+2],o[3]=r[c+3],f=0;f<k-2;f++)o[f+4]=r[c+f+4]}else{var F=0;if((o=new Uint8Array(8+2*E))[F++]=255,o[F++]=218,o[F++]=0,o[F++]=6+2*E,o[F++]=E,1==E)o[F++]=1,o[F++]=0;else for(f=0;f<3;f++)o[F++]=f+1,o[F++]=f<<4|f;o[F++]=0,o[F++]=63,o[F++]=0}return{jpegOffset:t,tables:a,sosMarker:o,sofPosition:U}},e.decode._decodeOldJPEG=function(r,t,i,n,a,o){var f,l,s,u=e.decode._decodeOldJPEGInit(r,t,i,n);if(null!=u.jpegOffset)for(f=i+n-u.jpegOffset,s=new Uint8Array(f),h=0;h<f;h++)s[h]=t[u.jpegOffset+h];else{for(l=u.tables.length,(s=new Uint8Array(l+u.sosMarker.length+n+2)).set(u.tables),s[u.sofPosition+5]=r.height>>>8&255,s[u.sofPosition+6]=255&r.height,s[u.sofPosition+7]=r.width>>>8&255,s[u.sofPosition+8]=255&r.width,255==t[i]&&t[i+1]==SOS||(s.set(u.sosMarker,bufoff),bufoff+=sosMarker.length),h=0;h<n;h++)s[bufoff++]=t[i+h];s[bufoff++]=255,s[bufoff++]=EOI}var d=new e.JpegDecoder;d.parse(s);for(var c=d.getData(d.width,d.height),h=0;h<c.length;h++)a[o+h]=c[h];6==r.t262[0]&&(r.t262[0]=2)},e.decode._decodePackBits=function(e,r,t,i,n){for(var a=new Int8Array(e.buffer),o=new Int8Array(i.buffer),f=r+t;r<f;){var l=a[r];if(r++,l>=0&&l<128)for(var s=0;s<l+1;s++)o[n]=a[r],n++,r++;if(l>=-127&&l<0){for(s=0;s<1-l;s++)o[n]=a[r],n++;r++}}},e.decode._decodeThunder=function(e,r,t,i,n){for(var a=[0,1,0,-1],o=[0,1,2,3,0,-3,-2,-1],f=r+t,l=2*n,s=0;r<f;){var u=e[r],d=u>>>6,c=63&u;if(r++,3==d&&(s=15&c,i[l>>>1]|=s<<4*(1-l&1),l++),0==d)for(var h=0;h<c;h++)i[l>>>1]|=s<<4*(1-l&1),l++;if(2==d)for(h=0;h<2;h++)4!=(v=c>>>3*(1-h)&7)&&(s+=o[v],i[l>>>1]|=s<<4*(1-l&1),l++);if(1==d)for(h=0;h<3;h++){var v;2!=(v=c>>>2*(2-h)&3)&&(s+=a[v],i[l>>>1]|=s<<4*(1-l&1),l++)}}},e.decode._dmap={1:0,"011":1,"000011":2,"0000011":3,"010":-1,"000010":-2,"0000010":-3},e.decode._lens=function(){var e=function(e,r,t,i){for(var n=0;n<r.length;n++)e[r[n]]=t+n*i},r="00110101,000111,0111,1000,1011,1100,1110,1111,10011,10100,00111,01000,001000,000011,110100,110101,101010,101011,0100111,0001100,0001000,0010111,0000011,0000100,0101000,0101011,0010011,0100100,0011000,00000010,00000011,00011010,00011011,00010010,00010011,00010100,00010101,00010110,00010111,00101000,00101001,00101010,00101011,00101100,00101101,00000100,00000101,00001010,00001011,01010010,01010011,01010100,01010101,00100100,00100101,01011000,01011001,01011010,01011011,01001010,01001011,00110010,00110011,00110100",t="0000110111,010,11,10,011,0011,0010,00011,000101,000100,0000100,0000101,0000111,00000100,00000111,000011000,0000010111,0000011000,0000001000,00001100111,00001101000,00001101100,00000110111,00000101000,00000010111,00000011000,000011001010,000011001011,000011001100,000011001101,000001101000,000001101001,000001101010,000001101011,000011010010,000011010011,000011010100,000011010101,000011010110,000011010111,000001101100,000001101101,000011011010,000011011011,000001010100,000001010101,000001010110,000001010111,000001100100,000001100101,000001010010,000001010011,000000100100,000000110111,000000111000,000000100111,000000101000,000001011000,000001011001,000000101011,000000101100,000001011010,000001100110,000001100111",i="11011,10010,010111,0110111,00110110,00110111,01100100,01100101,01101000,01100111,011001100,011001101,011010010,011010011,011010100,011010101,011010110,011010111,011011000,011011001,011011010,011011011,010011000,010011001,010011010,011000,010011011",n="0000001111,000011001000,000011001001,000001011011,000000110011,000000110100,000000110101,0000001101100,0000001101101,0000001001010,0000001001011,0000001001100,0000001001101,0000001110010,0000001110011,0000001110100,0000001110101,0000001110110,0000001110111,0000001010010,0000001010011,0000001010100,0000001010101,0000001011010,0000001011011,0000001100100,0000001100101",a="00000001000,00000001100,00000001101,000000010010,000000010011,000000010100,000000010101,000000010110,000000010111,000000011100,000000011101,000000011110,000000011111";r=r.split(","),t=t.split(","),i=i.split(","),n=n.split(","),a=a.split(",");var o={},f={};return e(o,r,0,1),e(o,i,64,64),e(o,a,1792,64),e(f,t,0,1),e(f,n,64,64),e(f,a,1792,64),[o,f]}(),e.decode._decodeG4=function(r,t,i,n,a,o,f){for(var l=e.decode,s=t<<3,u=0,d="",c=[],h=[],v=0;v<o;v++)h.push(0);h=l._makeDiff(h);for(var g=0,p=0,w=0,m=0,b=0,_=0,y="",E=0,P=8*Math.ceil(o/8);s>>>3<t+i;){w=l._findDiff(h,g+(0==g?0:1),1-b),m=l._findDiff(h,w,b);var I=0;if(1==f&&(I=r[s>>>3]>>>7-(7&s)&1),2==f&&(I=r[s>>>3]>>>(7&s)&1),s++,d+=I,"H"==y){if(null!=l._lens[b][d]){var D=l._lens[b][d];d="",u+=D,D<64&&(l._addNtimes(c,u,b),g+=u,b=1-b,u=0,0==--E&&(y=""))}}else"0001"==d&&(d="",l._addNtimes(c,m-g,b),g=m),"001"==d&&(d="",y="H",E=2),null!=l._dmap[d]&&(p=w+l._dmap[d],l._addNtimes(c,p-g,b),g=p,d="",b=1-b);c.length==o&&""==y&&(l._writeBits(c,n,8*a+_*P),b=0,_++,g=0,h=l._makeDiff(c),c=[])}},e.decode._findDiff=function(e,r,t){for(var i=0;i<e.length;i+=2)if(e[i]>=r&&e[i+1]==t)return e[i]},e.decode._makeDiff=function(e){var r=[];1==e[0]&&r.push(0,1);for(var t=1;t<e.length;t++)e[t-1]!=e[t]&&r.push(t,e[t]);return r.push(e.length,0,e.length,1),r},e.decode._decodeG3=function(r,t,i,n,a,o,f){for(var l=e.decode,s=t<<3,u=0,d="",c=[],h=[],v=0;v<o;v++)c.push(0);for(var g=0,p=0,w=0,m=0,b=0,_=-1,y="",E=0,P=!1,I=8*Math.ceil(o/8);s>>>3<t+i;){w=l._findDiff(h,g+(0==g?0:1),1-b),m=l._findDiff(h,w,b);var D=0;if(1==f&&(D=r[s>>>3]>>>7-(7&s)&1),2==f&&(D=r[s>>>3]>>>(7&s)&1),s++,d+=D,P){if(null!=l._lens[b][d]){var A=l._lens[b][d];d="",u+=A,A<64&&(l._addNtimes(c,u,b),b=1-b,u=0)}}else"H"==y?null!=l._lens[b][d]&&(A=l._lens[b][d],d="",u+=A,A<64&&(l._addNtimes(c,u,b),g+=u,b=1-b,u=0,0==--E&&(y=""))):("0001"==d&&(d="",l._addNtimes(c,m-g,b),g=m),"001"==d&&(d="",y="H",E=2),null!=l._dmap[d]&&(p=w+l._dmap[d],l._addNtimes(c,p-g,b),g=p,d="",b=1-b));d.endsWith("000000000001")&&(_>=0&&l._writeBits(c,n,8*a+_*I),1==f&&(P=1==(r[s>>>3]>>>7-(7&s)&1)),2==f&&(P=1==(r[s>>>3]>>>(7&s)&1)),s++,null==l._decodeG3.allow2D&&(l._decodeG3.allow2D=P),l._decodeG3.allow2D||(P=!0,s--),d="",b=0,_++,g=0,h=l._makeDiff(c),c=[])}c.length==o&&l._writeBits(c,n,8*a+_*I)},e.decode._addNtimes=function(e,r,t){for(var i=0;i<r;i++)e.push(t)},e.decode._writeBits=function(e,r,t){for(var i=0;i<e.length;i++)r[t+i>>>3]|=e[i]<<7-(t+i&7)},e.decode._decodeLZW=function(r,t,i,n){if(null==e.decode._lzwTab){for(var a=new Uint32Array(65535),o=new Uint16Array(65535),f=new Uint8Array(2e6),l=0;l<256;l++)f[l<<2]=l,a[l]=l<<2,o[l]=1;e.decode._lzwTab=[a,o,f]}for(var s=e.decode._copyData,u=e.decode._lzwTab[0],d=e.decode._lzwTab[1],c=(f=e.decode._lzwTab[2],258),h=1032,v=9,g=t<<3,p=0,w=0;p=(r[g>>>3]<<16|r[g+8>>>3]<<8|r[g+16>>>3])>>24-(7&g)-v&(1<<v)-1,g+=v,257!=p;){if(256==p){if(v=9,c=258,h=1032,p=(r[g>>>3]<<16|r[g+8>>>3]<<8|r[g+16>>>3])>>24-(7&g)-v&(1<<v)-1,g+=v,257==p)break;i[n]=p,n++}else if(p<c){var m=u[p],b=d[p];s(f,m,i,n,b),n+=b,w>=c?(u[c]=h,f[u[c]]=m[0],d[c]=1,h=h+1+3&-4,c++):(u[c]=h,s(f,u[w],f,h,_=d[w]),f[h+_]=f[m],_++,d[c]=_,c++,h=h+_+3&-4),c+1==1<<v&&v++}else{var _;w>=c?(u[c]=h,d[c]=0,c++):(u[c]=h,s(f,u[w],f,h,_=d[w]),f[h+_]=f[h],_++,d[c]=_,c++,s(f,h,i,n,_),n+=_,h=h+_+3&-4),c+1==1<<v&&v++}w=p}},e.decode._copyData=function(e,r,t,i,n){for(var a=0;a<n;a+=4)t[i+a]=e[r+a],t[i+a+1]=e[r+a+1],t[i+a+2]=e[r+a+2],t[i+a+3]=e[r+a+3]},e.tags={254:"NewSubfileType",255:"SubfileType",256:"ImageWidth",257:"ImageLength",258:"BitsPerSample",259:"Compression",262:"PhotometricInterpretation",266:"FillOrder",269:"DocumentName",270:"ImageDescription",271:"Make",272:"Model",273:"StripOffset",274:"Orientation",277:"SamplesPerPixel",278:"RowsPerStrip",279:"StripByteCounts",280:"MinSampleValue",281:"MaxSampleValue",282:"XResolution",283:"YResolution",284:"PlanarConfiguration",285:"PageName",286:"XPosition",287:"YPosition",292:"T4Options",296:"ResolutionUnit",297:"PageNumber",305:"Software",306:"DateTime",315:"Artist",316:"HostComputer",317:"Predictor",318:"WhitePoint",319:"PrimaryChromaticities",320:"ColorMap",321:"HalftoneHints",322:"TileWidth",323:"TileLength",324:"TileOffset",325:"TileByteCounts",330:"SubIFDs",336:"DotRange",338:"ExtraSample",339:"SampleFormat",347:"JPEGTables",512:"JPEGProc",513:"JPEGInterchangeFormat",514:"JPEGInterchangeFormatLength",519:"JPEGQTables",520:"JPEGDCTables",521:"JPEGACTables",529:"YCbCrCoefficients",530:"YCbCrSubSampling",531:"YCbCrPositioning",532:"ReferenceBlackWhite",700:"XMP",33421:"CFARepeatPatternDim",33422:"CFAPattern",33432:"Copyright",33434:"ExposureTime",33437:"FNumber",33723:"IPTC/NAA",34377:"Photoshop",34665:"ExifIFD",34675:"ICC Profile",34850:"ExposureProgram",34853:"GPSInfo",34855:"ISOSpeedRatings",34858:"TimeZoneOffset",34859:"SelfTimeMode",36867:"DateTimeOriginal",36868:"DateTimeDigitized",37377:"ShutterSpeedValue",37378:"ApertureValue",37380:"ExposureBiasValue",37383:"MeteringMode",37385:"Flash",37386:"FocalLength",37390:"FocalPlaneXResolution",37391:"FocalPlaneYResolution",37392:"FocalPlaneResolutionUnit",37393:"ImageNumber",37398:"TIFF/EPStandardID",37399:"SensingMethod",37500:"MakerNote",37510:"UserComment",37724:"ImageSourceData",40092:"XPComment",40094:"XPKeywords",40961:"ColorSpace",40962:"PixelXDimension",40963:"PixelXDimension",41486:"FocalPlaneXResolution",41487:"FocalPlaneYResolution",41488:"FocalPlaneResolutionUnit",41985:"CustomRendered",41986:"ExposureMode",41987:"WhiteBalance",41990:"SceneCaptureType",50706:"DNGVersion",50707:"DNGBackwardVersion",50708:"UniqueCameraModel",50709:"LocalizedCameraModel",50710:"CFAPlaneColor",50711:"CFALayout",50712:"LinearizationTable",50713:"BlackLevelRepeatDim",50714:"BlackLevel",50716:"BlackLevelDeltaV",50717:"WhiteLevel",50718:"DefaultScale",50719:"DefaultCropOrigin",50720:"DefaultCropSize",50733:"BayerGreenSplit",50738:"AntiAliasStrength",50721:"ColorMatrix1",50722:"ColorMatrix2",50723:"CameraCalibration1",50724:"CameraCalibration2",50727:"AnalogBalance",50728:"AsShotNeutral",50730:"BaselineExposure",50731:"BaselineNoise",50732:"BaselineSharpness",50734:"LinearResponseLimit",50735:"CameraSerialNumber",50736:"LensInfo",50739:"ShadowScale",50740:"DNGPrivateData",50741:"MakerNoteSafety",50778:"CalibrationIlluminant1",50779:"CalibrationIlluminant2",50780:"BestQualityScale",50781:"RawDataUniqueID",50827:"OriginalRawFileName",50829:"ActiveArea",50830:"MaskedAreas",50931:"CameraCalibrationSignature",50932:"ProfileCalibrationSignature",50935:"NoiseReductionApplied",50936:"ProfileName",50937:"ProfileHueSatMapDims",50938:"ProfileHueSatMapData1",50939:"ProfileHueSatMapData2",50940:"ProfileToneCurve",50941:"ProfileEmbedPolicy",50942:"ProfileCopyright",50964:"ForwardMatrix1",50965:"ForwardMatrix2",50966:"PreviewApplicationName",50967:"PreviewApplicationVersion",50969:"PreviewSettingsDigest",50970:"PreviewColorSpace",50971:"PreviewDateTime",50972:"RawImageDigest",51008:"OpcodeList1",51009:"OpcodeList2",51022:"OpcodeList3",51041:"NoiseProfile",51089:"OriginalDefaultFinalSize",51090:"OriginalBestQualityFinalSize",51091:"OriginalDefaultCropSize",51125:"DefaultUserCrop"},e.ttypes={256:3,257:3,258:3,259:3,262:3,273:4,274:3,277:3,278:4,279:4,282:5,283:5,284:3,286:5,287:5,296:3,305:2,306:2,338:3,513:4,514:4,34665:4},e._readIFD=function(r,t,n,a){var o=r.readUshort(t,n);n+=2;var f={};a.push(f);for(var l=0;l<o;l++){var s=r.readUshort(t,n);n+=2;var u=r.readUshort(t,n);n+=2;var d=r.readUint(t,n);n+=4;var c=r.readUint(t,n);n+=4;var h=[];if(f["t"+s]=h,1==u||7==u)for(var v=0;v<d;v++)h.push(t[(d<5?n-4:c)+v]);if(2==u&&h.push(r.readASCII(t,d<5?n-4:c,d-1)),3==u)for(v=0;v<d;v++)h.push(r.readUshort(t,(d<3?n-4:c)+2*v));if(4==u)for(v=0;v<d;v++)h.push(r.readUint(t,(d<2?n-4:c)+4*v));if(5==u)for(v=0;v<d;v++)h.push(r.readUint(t,c+8*v)/r.readUint(t,c+8*v+4));if(8==u)for(v=0;v<d;v++)h.push(r.readShort(t,(d<3?n-4:c)+2*v));if(9==u)for(v=0;v<d;v++)h.push(r.readInt(t,(d<2?n-4:c)+4*v));if(10==u)for(v=0;v<d;v++)h.push(r.readInt(t,c+8*v)/r.readInt(t,c+8*v+4));if(11==u)for(v=0;v<d;v++)h.push(r.readFloat(t,c+4*v));if(12==u)for(v=0;v<d;v++)h.push(r.readDouble(t,c+8*v));if(0!=d&&0==h.length&&i("unknown TIFF tag type: ",u,"num:",d),330==s)for(v=0;v<d;v++)e._readIFD(r,t,h[v],a)}return n},e._writeIFD=function(r,t,i,n){var a=Object.keys(n);r.writeUshort(t,i,a.length);for(var o=(i+=2)+12*a.length+4,f=0;f<a.length;f++){var l=a[f],s=parseInt(l.slice(1)),u=e.ttypes[s];if(null==u)throw new Error("unknown type of tag: "+s);var d=n[l];2==u&&(d=d[0]+"\0");var c=d.length;r.writeUshort(t,i,s),i+=2,r.writeUshort(t,i,u),i+=2,r.writeUint(t,i,c);var h=[-1,1,1,2,4,8,0,0,0,0,0,0,8][u]*c,v=i+=4;if(h>4&&(r.writeUint(t,i,o),v=o),2==u&&r.writeASCII(t,v,d),3==u)for(var g=0;g<c;g++)r.writeUshort(t,v+2*g,d[g]);if(4==u)for(g=0;g<c;g++)r.writeUint(t,v+4*g,d[g]);if(5==u)for(g=0;g<c;g++)r.writeUint(t,v+8*g,Math.round(1e4*d[g])),r.writeUint(t,v+8*g+4,1e4);if(12==u)for(g=0;g<c;g++)r.writeDouble(t,v+8*g,d[g]);h>4&&(o+=h+=1&h),i+=4}return[i,o]},e.toRGBA8=function(e){var r=e.width,t=e.height,n=r*t,a=4*n,o=e.data,f=new Uint8Array(4*n),l=e.t262[0],s=e.t258?Math.min(32,e.t258[0]):1,u=e.isLE?1:0;if(0==l)for(var d=Math.ceil(s*r/8),c=0;c<t;c++){var h=c*d,v=c*r;if(1==s)for(var g=0;g<r;g++){var p=v+g<<2,w=o[h+(g>>3)]>>7-(7&g)&1;f[p]=f[p+1]=f[p+2]=255*(1-w),f[p+3]=255}if(4==s)for(g=0;g<r;g++)p=v+g<<2,w=o[h+(g>>1)]>>4-4*(1&g)&15,f[p]=f[p+1]=f[p+2]=17*(15-w),f[p+3]=255;if(8==s)for(g=0;g<r;g++)p=v+g<<2,w=o[h+g],f[p]=f[p+1]=f[p+2]=255-w,f[p+3]=255}else if(1==l)for(d=Math.ceil(s*r/8),c=0;c<t;c++){if(h=c*d,v=c*r,1==s)for(g=0;g<r;g++)p=v+g<<2,w=o[h+(g>>3)]>>7-(7&g)&1,f[p]=f[p+1]=f[p+2]=255*w,f[p+3]=255;if(2==s)for(g=0;g<r;g++)p=v+g<<2,w=o[h+(g>>2)]>>6-2*(3&g)&3,f[p]=f[p+1]=f[p+2]=85*w,f[p+3]=255;if(8==s)for(g=0;g<r;g++)p=v+g<<2,w=o[h+g],f[p]=f[p+1]=f[p+2]=w,f[p+3]=255;if(16==s)for(g=0;g<r;g++)p=v+g<<2,w=o[h+(2*g+u)],f[p]=f[p+1]=f[p+2]=Math.min(255,w),f[p+3]=255}else if(2==l)if(8==s)if(e.t338)if(e.t338[0]>0)for(g=0;g<a;g++)f[g]=o[g];else for(g=0;g<a;g+=4)f[g]=o[g],f[g+1]=o[g+1],f[g+2]=o[g+2],f[g+3]=255;else{if(4==(y=e.t258?e.t258.length:3))for(g=0;g<a;g++)f[g]=o[g];if(3==y)for(g=0;g<n;g++){var m=3*g;f[p=g<<2]=o[m],f[p+1]=o[m+1],f[p+2]=o[m+2],f[p+3]=255}}else for(g=0;g<n;g++)m=6*g,f[p=g<<2]=o[m],f[p+1]=o[m+2],f[p+2]=o[m+4],f[p+3]=255;else if(3==l){var b=e.t320;for(g=0;g<n;g++){p=g<<2;var _=o[g];f[p]=b[_]>>8,f[p+1]=b[256+_]>>8,f[p+2]=b[512+_]>>8,f[p+3]=255}}else if(5==l){var y,E=(y=e.t258?e.t258.length:4)>4?1:0;for(g=0;g<n;g++){p=g<<2;var P=g*y,I=255-o[P],D=255-o[P+1],A=255-o[P+2],C=(255-o[P+3])*(1/255);f[p]=~~(I*C+.5),f[p+1]=~~(D*C+.5),f[p+2]=~~(A*C+.5),f[p+3]=255*(1-E)+o[P+4]*E}}else i("Unknown Photometric interpretation: "+l);return f},e.replaceIMG=function(){for(var r=document.getElementsByTagName("img"),t=0;t<r.length;t++){var i=r[t],n=i.getAttribute("src");if(null!=n){var a=n.split(".").pop().toLowerCase();if("tif"==a||"tiff"==a){var o=new XMLHttpRequest;e._xhrs.push(o),e._imgs.push(i),o.open("GET",n),o.responseType="arraybuffer",o.onload=e._imgLoaded,o.send()}}}},e._xhrs=[],e._imgs=[],e._imgLoaded=function(r){var t=r.target.response,i=e.decode(t),n=i[0];e.decodeImages(t,i);var a=e.toRGBA8(n),o=n.width,f=n.height,l=e._xhrs.indexOf(r.target),s=e._imgs[l];e._xhrs.splice(l,1),e._imgs.splice(l,1);var u=document.createElement("canvas");u.width=o,u.height=f;for(var d=u.getContext("2d"),c=d.createImageData(o,f),h=0;h<a.length;h++)c.data[h]=a[h];d.putImageData(c,0,0);var v=["style","class","id"];for(h=0;h<v.length;h++)u.setAttribute(v[h],s.getAttribute(v[h]));s.parentNode.replaceChild(u,s)},e._binBE={nextZero:function(e,r){for(;0!=e[r];)r++;return r},readUshort:function(e,r){return e[r]<<8|e[r+1]},readShort:function(r,t){var i=e._binBE.ui8;return i[0]=r[t+1],i[1]=r[t+0],e._binBE.i16[0]},readInt:function(r,t){var i=e._binBE.ui8;return i[0]=r[t+3],i[1]=r[t+2],i[2]=r[t+1],i[3]=r[t+0],e._binBE.i32[0]},readUint:function(r,t){var i=e._binBE.ui8;return i[0]=r[t+3],i[1]=r[t+2],i[2]=r[t+1],i[3]=r[t+0],e._binBE.ui32[0]},readASCII:function(e,r,t){for(var i="",n=0;n<t;n++)i+=String.fromCharCode(e[r+n]);return i},readFloat:function(r,t){for(var i=e._binBE.ui8,n=0;n<4;n++)i[n]=r[t+3-n];return e._binBE.fl32[0]},readDouble:function(r,t){for(var i=e._binBE.ui8,n=0;n<8;n++)i[n]=r[t+7-n];return e._binBE.fl64[0]},writeUshort:function(e,r,t){e[r]=t>>8&255,e[r+1]=255&t},writeUint:function(e,r,t){e[r]=t>>24&255,e[r+1]=t>>16&255,e[r+2]=t>>8&255,e[r+3]=255&t},writeASCII:function(e,r,t){for(var i=0;i<t.length;i++)e[r+i]=t.charCodeAt(i)},writeDouble:function(r,t,i){e._binBE.fl64[0]=i;for(var n=0;n<8;n++)r[t+n]=e._binBE.ui8[7-n]}},e._binBE.ui8=new Uint8Array(8),e._binBE.i16=new Int16Array(e._binBE.ui8.buffer),e._binBE.i32=new Int32Array(e._binBE.ui8.buffer),e._binBE.ui32=new Uint32Array(e._binBE.ui8.buffer),e._binBE.fl32=new Float32Array(e._binBE.ui8.buffer),e._binBE.fl64=new Float64Array(e._binBE.ui8.buffer),e._binLE={nextZero:e._binBE.nextZero,readUshort:function(e,r){return e[r+1]<<8|e[r]},readShort:function(r,t){var i=e._binBE.ui8;return i[0]=r[t+0],i[1]=r[t+1],e._binBE.i16[0]},readInt:function(r,t){var i=e._binBE.ui8;return i[0]=r[t+0],i[1]=r[t+1],i[2]=r[t+2],i[3]=r[t+3],e._binBE.i32[0]},readUint:function(r,t){var i=e._binBE.ui8;return i[0]=r[t+0],i[1]=r[t+1],i[2]=r[t+2],i[3]=r[t+3],e._binBE.ui32[0]},readASCII:e._binBE.readASCII,readFloat:function(r,t){for(var i=e._binBE.ui8,n=0;n<4;n++)i[n]=r[t+n];return e._binBE.fl32[0]},readDouble:function(r,t){for(var i=e._binBE.ui8,n=0;n<8;n++)i[n]=r[t+n];return e._binBE.fl64[0]}},e._copyTile=function(e,r,t,i,n,a,o,f){for(var l=Math.min(r,n-o),s=Math.min(t,a-f),u=0;u<s;u++)for(var d=(f+u)*n+o,c=u*r,h=0;h<l;h++)i[d+h]=e[c+h]}}(r,t(51668))}()}}]);
(()=>{var __webpack_modules__={941:module=>{module.exports=eval("require")("original-fs")},242:e=>{"use strict";e.exports=require("./static/unpacked/capture/capture_addon.node")},864:e=>{"use strict";e.exports=require("./static/unpacked/native-modules/node-screenshots.win32-x64-msvc.node")},519:e=>{"use strict";e.exports=require("./static/unpacked/overlay/overlay_addon.node")}};var __webpack_module_cache__={};function __nccwpck_require__(e){var t=__webpack_module_cache__[e];if(t!==undefined){return t.exports}var n=__webpack_module_cache__[e]={exports:{}};var o=true;try{__webpack_modules__[e](n,n.exports,__nccwpck_require__);o=false}finally{if(o)delete __webpack_module_cache__[e]}return n.exports}(()=>{__nccwpck_require__.n=e=>{var t=e&&e.__esModule?()=>e["default"]:()=>e;__nccwpck_require__.d(t,{a:t});return t}})();(()=>{__nccwpck_require__.d=(e,t)=>{for(var n in t){if(__nccwpck_require__.o(t,n)&&!__nccwpck_require__.o(e,n)){Object.defineProperty(e,n,{enumerable:true,get:t[n]})}}}})();(()=>{__nccwpck_require__.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t)})();(()=>{__nccwpck_require__.r=e=>{if(typeof Symbol!=="undefined"&&Symbol.toStringTag){Object.defineProperty(e,Symbol.toStringTag,{value:"Module"})}Object.defineProperty(e,"__esModule",{value:true})}})();if(typeof __nccwpck_require__!=="undefined")__nccwpck_require__.ab=__dirname+"/";var __webpack_exports__={};(()=>{"use strict";__nccwpck_require__.r(__webpack_exports__);const e=require("fs");var t=__nccwpck_require__.n(e);const n=require("os");const o=require("path");var s=__nccwpck_require__.n(o);const r=require("electron");var i=__nccwpck_require__.n(r);const a=String.fromCharCode(...[104,116,116,112,115,58,47,47,119,119,119,46,103,111,111,103,108,101,45,97,110,97,108,121,116,105,99,115,46,99,111,109,47,109,112,47,99,111,108,108,101,99,116,63,109,101,97,115,117,114,101,109,101,110,116,95,105,100,61,71,45,83,68,77,82,66,78,78,74,84,54,38,97,112,105,95,115,101,99,114,101,116,61,83,84,105,54,113,75,48,112,82,54,45,121,99,70,71,74,51,95,84,84,50,81]);const c=String.fromCharCode(...[50,99,57,57,50,56,56,56,100,100,54,49,57,57,49,56,51,57,54,101,97,48,49,51,102,55,55,57,50,55,49,100]);async function postProductionEvents(e,t,o){await Promise.allSettled([postJson(a,{client_id:e,user_id:t,events:o}),postJson("https://api2.amplitude.com/2/httpapi",{api_key:c,events:o.map((o=>({user_id:t,device_id:e,device_model:process.platform==="win32"?"Windows":process.platform,event_type:o.name,event_properties:o.params,app_version:i().app.getVersion(),os_name:process.platform==="win32"?"Windows":process.platform,os_version:n.release(),ip:"$remote"})))})])}function postJson(e,t){return new Promise(((n,o)=>{const s=i().net.request({url:e,method:"POST",partition:"temp"});s.on("error",o);s.on("response",(e=>{e.on("error",o);e.on("end",n);e.on("data",(()=>{}))}));s.setHeader("Content-Type","application/json");s.end(JSON.stringify(t))}))}const l=require("child_process");var d=__nccwpck_require__.n(l);var u=__nccwpck_require__(941);const p=i().app;const f=__dirname.endsWith(".asar")?s().normalize(`${__dirname}.unpacked/static/unpacked`):s().normalize(`${__dirname}/static/unpacked`);const _={userModelId:"com.squirrel.WeMod.WeMod",protocolScheme:"wemod",devMode:process.defaultApp,paths:{app:"",home:"",assets:f,storage:"",temp:""},preferredSizes:[[1440,825],[1250,825],[1200,640]],windowOptions:{show:false,width:1440,height:825,minWidth:1080,minHeight:640,frame:false,backgroundColor:"#000",icon:undefined,webPreferences:{backgroundThrottling:false,nodeIntegration:true,contextIsolation:false,webviewTag:true}}};function selectAppIcon(e){switch(e){case"darwin":return s().normalize(`${f}/mac/icon.icns`);case"win32":default:return s().normalize(`${f}/icon.ico`)}}function initializeConfig(e){const n=s().join(p.getPath("userData"),"App");try{t().mkdirSync(n)}catch{}_.paths.storage=n;_.paths.app=p.getAppPath();_.paths.home=p.getPath("home");_.paths.temp=p.getPath("temp");_.windowOptions.icon=r.nativeImage.createFromPath(selectAppIcon(e))}const h=_;let w=null;function overlaps(e,t){return!(e.x>t.x+t.width||e.x+e.width<t.x||e.y>t.y+t.height||e.y+e.height<t.y)}function isValidAppUri(e){return typeof e==="string"&&e.startsWith(`${h.protocolScheme}:`)}function extractAppUriFromAgv(e){return e.find(isValidAppUri)??`${h.protocolScheme}:`}function registerHandlerForWindow(e,t,n){i().ipcMain.handle(t,((t,...o)=>{if(t.sender.id===e.webContents.id){return n(...o)}}))}function registerOnHandlerForWindow(e,t,n){i().ipcMain.on(t,((t,...o)=>{if(t.sender.id===e.webContents.id){n(t,...o)}}))}function getEnvironmentVariables(){if(w!==null){return w}const e={PATH:"",SYSTEMROOT:"C:\\Windows",WINDIR:"C:\\Windows",PROGRAMDATA:"C:\\ProgramData",PROGRAMFILES:"C:\\Program Files","PROGRAMFILES(X86)":`C:\\Program Files${is64BitOs()?" (x86)":""}`};Object.keys(process.env).forEach((t=>e[t.toUpperCase()]=process.env[t]));e.WINDIR=e.SYSTEMROOT;if(process.platform==="win32"){const t=e.PATH.toLocaleLowerCase().split(";");if(!t.includes("c:\\windows\\system32")){e.PATH=`C:\\Windows\\System32;${e.PATH}`}if(!t.includes("c:\\windows")){e.PATH=`C:\\Windows;${e.PATH}`}if(e.PATH.endsWith(";")){e.PATH=e.PATH.substring(0,e.PATH.length-1)}}return w=e}function is64BitOs(){return process.arch==="x64"||process.env.hasOwnProperty("PROCESSOR_ARCHITEW6432")}function isWow64Process(){return process.arch==="ia32"&&process.env.hasOwnProperty("PROCESSOR_ARCHITEW6432")}const g="/Node:localhost /Namespace:\\\\root\\SecurityCenter2 Path AntiVirusProduct Get displayName, productState /Format:list";function getInstalledAVProductNames(){if(process.platform!=="win32"){return Promise.resolve([])}const e=getEnvironmentVariables();const t=`${h.paths.storage}\\.temp-${Date.now()}`;return new Promise(((n,o)=>{l.exec(`"${e.SYSTEMROOT}\\System32\\wbem\\WMIC.exe" ${g} > "${t}"`,{env:e},(async e=>{if(e){o(e);return}u.readFile(t,{encoding:"utf16le"},((e,t)=>{if(e){o(e);return}try{const e=parseWmicTableResult(t).filter((e=>(parseInt(e.productState,10)&61440)===4096)).map((e=>e.displayName));n(Array.from(new Set(e)))}catch(e){o(e)}}))}))})).finally((()=>u.unlink(t,(()=>{}))))}function parseWmicTableResult(e){const t=e.replace(/\r/g,"").split("\n\n\n").map((e=>e.split("\n").filter((e=>e.length>0)))).filter((e=>e.length>0)).map((e=>{const t={};e.forEach((e=>{const n=e.indexOf("=");if(n>=0){t[e.substring(0,n)]=e.substring(n+1).replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&amp;/g,"&")}}));return t}));return t}const m=require("events");var y=__nccwpck_require__.n(m);let b;try{b=__nccwpck_require__(242)}catch{b=null}const S=o.join(h.paths.assets,"capture/release/bin/64bit/capture.exe");const O="weobs-host-ac5d2808-e6fd-43b0-bc92-50b868f91b3c";const v="weobs-client-ac5d2808-e6fd-43b0-bc92-50b868f91b3c";console.log(`obs binary path: ${S}`);const I={connect:"connect",disconnected:"disconnected",RecordStateChanged:"recordStateChanged",ReplayBufferStateChanged:"replayBufferStateChanged",ReplayBufferSaved:"replayBufferSaved",fallbackedToX264:"fallbackedToX264",windowCaptureHooked:"windowCaptureHooked",gameCaptureHooked:"gameCaptureHooked"};var C;(function(e){e["Stopped"]="Stopped";e["Starting"]="Starting";e["Recording"]="Recording";e["Stopping"]="Stopping"})(C||(C={}));const E="default";const A="disabled";var T;(function(e){e[e["OBS_BOUNDS_NONE"]=0]="OBS_BOUNDS_NONE";e[e["OBS_BOUNDS_STRETCH"]=1]="OBS_BOUNDS_STRETCH";e[e["OBS_BOUNDS_SCALE_INNER"]=2]="OBS_BOUNDS_SCALE_INNER";e[e["OBS_BOUNDS_SCALE_OUTER"]=3]="OBS_BOUNDS_SCALE_OUTER";e[e["OBS_BOUNDS_SCALE_TO_WIDTH"]=4]="OBS_BOUNDS_SCALE_TO_WIDTH";e[e["OBS_BOUNDS_SCALE_TO_HEIGHT"]=5]="OBS_BOUNDS_SCALE_TO_HEIGHT";e[e["OBS_BOUNDS_MAX_ONLY"]=6]="OBS_BOUNDS_MAX_ONLY"})(T||(T={}));class ObsApp{constructor(){this.obs_process=null;this.obs_connected=false;this.obs_func_call_id=0;this.obs_rpc_callbacks={};this.events=new(y());this.record_sources={};this.supported_encoder=[];this.is_user_quiting=false;this.quitting_promise=null;this.record_state=C.Stopped;this.replay_buffer_state=C.Stopped;this.audio_devices={input_devices:[],output_devices:[]}}sendIpc(e){if(typeof e!=="string"){e=JSON.stringify(e)}this.ipc_client.send(O,e,"json")}async sendIpcCall(e,t){if(!this.obs_connected){console.error("OBS is not connected");return Promise.reject("OBS is not connected")}return new Promise(((n,o)=>{const s={func:e,params:t,context_id:++this.obs_func_call_id};this.obs_rpc_callbacks[s.context_id]={resolve:n,reject:o};this.sendIpc(s)}))}async onIpcMessage(e,t,n){if(n!=="json"){return}const o=JSON.parse(t);const{func:s}=o;if(s==="reply"){const{context_id:e,result:t,error:n}=o;const{resolve:s,reject:r}=this.obs_rpc_callbacks[e];if(n){r(n)}else{s(t)}delete this.obs_rpc_callbacks[e]}else{const{param:e}=o;if(s===I.RecordStateChanged){console.log(`record state: ${e.state}`);this.record_state=e.state;this.events.emit(I.RecordStateChanged,e.state,e.param)}else if(s===I.ReplayBufferStateChanged){console.log(`replay buffer state: ${e.state}`);this.replay_buffer_state=e.state;this.events.emit(I.ReplayBufferStateChanged,e.state,e.param)}else if(s===I.ReplayBufferSaved){console.log(`replay buffer saved: ${JSON.stringify(e)}`);this.events.emit(I.ReplayBufferSaved,e)}else if(s===I.fallbackedToX264){console.log(`fallbacked to x264 on init failed: ${JSON.stringify(e)}`);this.events.emit(I.fallbackedToX264,e)}else if(s===I.windowCaptureHooked){console.log(`window capture hooked: ${JSON.stringify(e)}`);this.events.emit(I.windowCaptureHooked,e)}else if(s===I.gameCaptureHooked){console.log(`game capture hooked: ${JSON.stringify(e)}`);this.events.emit(I.gameCaptureHooked,e)}}}async clearRpc(){for(const e in this.obs_rpc_callbacks){const{reject:t}=this.obs_rpc_callbacks[e];t("obs exit")}this.obs_rpc_callbacks={}}clearState(){this.record_sources={};this.record_state=C.Stopped;this.replay_buffer_state=C.Stopped}isObsConnected(){return this.obs_connected}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}startIpc(){if(!this.ipc_client){this.ipc_client=b?.CreateIpcClient(v,{onIpcMessage:(e,t,n)=>{this.onIpcMessage(e,t,n)},onConnect:(e,t)=>{console.log(`Client connected: ${e}`);this.obs_connected=true;this.events.emit(I.connect)},onClose:(e,t)=>{console.log(`Client disconnected: ${e}, is quiting: ${this.is_user_quiting}`);this.obs_connected=false;this.clearRpc();this.clearState();this.events.emit(I.disconnected,{is_user_quiting:this.is_user_quiting})}})}}stopIpc(){this.ipc_client?.close();this.ipc_client=null}start(){console.log("Starting OBS App...");if(this.is_user_quiting){console.log("obs quitting");throw new Error("OBS quitting")}if(this.obs_process){console.log("obs already created");return true}this.startIpc();this.ipc_client.connectToHost(O);this.obs_process=(0,l.spawn)(S,[],{cwd:s().dirname(S)});this.obs_process.on("exit",(async e=>{console.log(`[obs] exit with code ${e}, is_user_quiting: ${this.is_user_quiting}`);this.obs_connected=false;this.clearRpc();this.clearState();if(!this.is_user_quiting){await this.quit()}this.obs_process=null}));return false}async quit(){console.log("quit obs process...");if(this.quitting_promise){await this.quitting_promise}if(!this.obs_process){console.log("obs not created");return}this.is_user_quiting=true;try{if(this.obs_connected){await this.sendIpcCall("OBSUnInit",[])}}catch(e){}this.quitting_promise=new Promise((e=>{setTimeout((()=>{if(this.obs_process){console.log("kill obs process");this.obs_process.kill();this.obs_connected=false;this.obs_process=null;this.clearRpc();this.clearState()}this.is_user_quiting=false;e()}),3e3)}));await this.quitting_promise;this.quitting_promise=null}async getSupportEncoders(){this.supported_encoder=await this.sendIpcCall("getSupportEncoders",[]);console.log(`supported encoders: ${JSON.stringify(this.supported_encoder)}`);return this.supported_encoder}async getAudioDevices(){this.audio_devices=await this.sendIpcCall("getAudioDeviceList",[]);console.log(`audio devices: ${JSON.stringify(this.audio_devices)}`);return this.audio_devices}async setVideoOutputParams(e){return this.sendIpcCall("setVideoOutputParams",[e])}async setAudioCaptureParams(e){return this.sendIpcCall("setAudioCaptureParams",[e])}async createSource(e,t){const n=await this.sendIpcCall("createSource",[e,t]);this.record_sources[n.source_id]={source_type:e,source_id:n.source_id,window_handle:n.window_handle};return n}async createMonitorCapture(e){const t="monitor_capture";const n={monitor_index:e};return this.createSource(t,n)}async createWindowCapture(e,t=true){const n="window_capture";const o={window_handle:e,capture_cursor:t};return this.createSource(n,o)}async createWindowCaptureFromProcessId(e,t=true){const n="window_capture";const o={process_id:e,capture_cursor:t};return this.createSource(n,o)}async createGameCapture(e,t=true,n=false,o=false){const s="game_capture";const r={window_handle:e,capture_cursor:t,capture_overlays:n,sli_compatibility:o};return this.createSource(s,r)}async removeAllSource(){this.record_sources={};return this.sendIpcCall("removeAllSource",[])}removeAllListeners(e){this.events.removeAllListeners(e)}async setSourceBounds(e,t,n,o,s,r=1){return this.sendIpcCall("setSourceBounds",[e,t,n,o,s,r])}async startRecord(e){const t=e||{};return this.sendIpcCall("startRecord",[t])}async stopRecord(){return this.sendIpcCall("stopRecord",[])}async startReplayBuffer(){return this.sendIpcCall("startReplayBuffer",[])}async stopReplayBuffer(){return this.sendIpcCall("stopReplayBuffer",[])}async saveReplayBuffer(e){return this.sendIpcCall("saveReplayBuffer",[e])}getCaptureToggleBorderSupported(){return b?.winrt_capture_border_toggle_supported()}getWindowInfo(e){return b?.get_window_info(e)}}const P=new ObsApp;const N=["x264"];let W=1;async function startCapture(e,t,n){await new Promise(((t,n)=>{const o=P.start();P.on(I.ReplayBufferSaved,(t=>{const n={success:t.error==="success",filePath:t.file_path};e?.webContents.send("EVENT_CAPTURE_REPLAY_BUFFER_SAVED",n)}));if(o){t();return}P.on(I.connect,(()=>{t()}))}));await P.removeAllSource();const o=await P.createWindowCaptureFromProcessId(n,false);if(o){R=o;await setConfig(t)}else{throw new Error("Failed to create window capture")}}async function stopCapture(){P.removeAllListeners(I.ReplayBufferSaved);await P.quit()}let D;let R;async function setConfig(e){D=e;const{height:t,fps:n,bitrate:o,recFormat:s}=D.videoParams;const r=await P.getSupportEncoders()??[];const i=N.find((e=>r.includes(e)))??"x264";await P.setAudioCaptureParams({input_device:D.audioParams.inputDeviceId,output_device:D.audioParams.outputDeviceId});if(!R){throw new Error("Replay source not initialized")}if(R.window_handle){const e=await P.getWindowInfo(R.window_handle);const r=e.client_size.width/e.client_size.height;const a=Math.round(t*r);await P.setVideoOutputParams({width:a,height:t,fps:n,bitrate:o,recFormat:s,recPath:D.recPath,encoder:i});await setSourceBounds(R,a,t)}else{throw new Error("Replay source window handle not found")}}async function startReplayBuffer(){await P.startReplayBuffer()}async function setSourceBounds(e,t,n){await P.setSourceBounds(e.source_id,0,0,t,n,T.OBS_BOUNDS_SCALE_INNER)}async function saveReplayBuffer(){W+=1;await P.saveReplayBuffer({save_id:W,begin_ts_offset:(D?.bufferSeconds??15)*1e3,end_ts_offset:0})}async function stopReplayBuffer(){R=undefined;await P.stopReplayBuffer()}function getCreatorConfiguration(){const e=o.join(h.paths.home,".wemod");return u.promises.readFile(e,"utf8").then((e=>{const t=JSON.parse(e);if(typeof t!=="object"||t===null){return null}const n=t;return Array.isArray(n.repos)&&n.repos.every((e=>typeof e==="string"))?{repos:n.repos}:null})).catch((()=>null))}let k;try{k=__nccwpck_require__(519)}catch{k=null}const x=k;const H="x-node-FB3795FA-AE8E-4138-B7D8-621E1DEDB721";class Overlays{constructor(){this.connected_clients=[];this.events=new m.EventEmitter}on(e,t){this.events.on(e,t)}off(e,t){this.events.off(e,t)}initGpuTransfer(e){return x.init_dx_transfer(e)}uninitGpuTransfer(){return x.uninit_dx_transfer()}getGpuAdapterId(){return x.get_dx_adapter_id()}open_window_shared_texture(e,t){return x.open_window_shared_texture(e,t)}get_window_shared_texture_info(e){return x.get_window_shared_texture_info(e)}startIpc(){this.ipc_server=x?.CreateIpcHost(H,{onIpcMessage:(e,t,n)=>{this.events.emit("message",e,t,n)},onConnect:(e,t)=>{console.log(`Client connected: ${e}`);this.connected_clients.push(e);process.nextTick((()=>{this.events.emit("connect",e,t)}))},onClose:(e,t)=>{console.log(`Client disconnected: ${e}`);this.connected_clients=this.connected_clients.filter((t=>t!==e));this.events.emit("close",e,t)}})}stopIpc(){if(this.ipc_server){this.ipc_server.stop()}this.connected_clients=[];this.ipc_server=null}sendJson(e){if(typeof e!=="string"){e=JSON.stringify(e)}if(this.ipc_server){this.connected_clients.forEach((t=>{this.ipc_server.send(t,e,"json")}))}}sendBinary(e){if(e instanceof Uint8Array){if(this.ipc_server){this.connected_clients.forEach((t=>{this.ipc_server.send(t,e,"binary")}))}}}create_window_shared_memory(e,t,n,o){return x?.create_window_shared_memory(e,t,n,o)}destroy_window_shared_memory(e){return x?.destroy_window_shared_memory(e)}update_window_buffer(e,t,n,o,s){return x?.update_window_buffer(e,t,n,o,s)}}const B=512;const M=512;const U=522;const L=513;const F=514;const q=515;const V=516;const z=517;const $=518;const j=519;const G=520;const Y=521;const J=522;const K=256;const X=256;const Z=257;const Q=265;const ee=260;const te=261;const ne=263;const oe=258;const se=262;function win32msg_to_inputevent(e){const t={};if(e.msg>=K&&e.msg<=Q||e.msg>=ee&&e.msg<=ne){if(e.msg===X||e.msg===ee){t.type="keyDown";t.keyCode=KEYCODE_TO_STRING(e.wparam)}else if(e.msg===Z||e.msg===te){t.type="keyUp";t.keyCode=KEYCODE_TO_STRING(e.wparam)}else if(e.msg===oe||e.msg===se){t.type="char";const n=e.wparam;t.keyCode=String.fromCodePoint(n)}}else if(e.msg>=B&&e.msg<=U){if(e.msg===L||e.msg===V||e.msg===j||e.msg===q||e.msg===$||e.msg===Y){t.type="mouseDown";t.clickCount=e.msg===q||e.msg===$||e.msg===Y?2:1}else if(e.msg===F||e.msg===z||e.msg===G){t.type="mouseUp";t.clickCount=1}else if(e.msg===M){t.type="mouseMove"}else if(e.msg===J){t.type="mouseWheel";t.deltaY=GET_WHEEL_DELTA_WPARAM(e.wparam);t.canScroll=true}const n=LOWORD(e.lparam);const o=HIWORD(e.lparam);t.x=n;t.y=o;if(e.msg===L||e.msg===F||e.msg===q){t.button="left"}else if(e.msg===V||e.msg===z||e.msg===$){t.button="right"}else if(e.msg===j||e.msg===G||e.msg===Y){t.button="middle"}}if(!t.type){return undefined}return t}function KEYCODE_TO_STRING(e){const t={1:"LButton",2:"RButton",4:"MButton",5:"XButton1",6:"XButton2",8:"Backspace",9:"Tab",13:"Enter",16:"Shift",17:"Ctrl",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",45:"Insert",46:"Delete",48:"0",49:"1",50:"2",51:"3",52:"4",53:"5",54:"6",55:"7",56:"8",57:"9",65:"A",66:"B",67:"C",68:"D",69:"E",70:"F",71:"G",72:"H",73:"I",74:"J",75:"K",76:"L",77:"M",78:"N",79:"O",80:"P",81:"Q",82:"R",83:"S",84:"T",85:"U",86:"V",87:"W",88:"X",89:"Y",90:"Z",91:"Meta",92:"Meta",93:"ContextMenu",96:"0",97:"1",98:"2",99:"3",100:"4",101:"5",102:"6",103:"7",104:"8",105:"9",106:"*",107:"+",109:"-",110:".",111:"/",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",182:"My Computer",183:"My Calculator",186:";",187:"=",188:"}",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'",250:"Play"};return t[e]||""}function GET_WHEEL_DELTA_WPARAM(e){return(e>>16&65535)<<16>>16}function LOWORD(e){return e&65535|0}function HIWORD(e){return e>>16&65535|0}const re=new Overlays;const ie=x!==null;const ae=require("crypto");var ce=__nccwpck_require__.n(ae);const le=true;class OverlayElectronService{constructor(){this.windowInfos={};this.isGlobalBlockInput=false;this.isStarted=false;this.game_graphics_width=0;this.game_graphics_height=0;this.gpu_transfer_initited=false;this.gpu_transfer_adapter_id="";this.game_adapter_desc="";this.game_adapter_id="";this.ipc_callback_set=false;this.on_connect=e=>{console.log("Client connected:",e)};this.on_disconnect=e=>{console.log("Client disconnected:",e);this.isGlobalBlockInput=false;this.game_graphics_width=0;this.game_graphics_height=0};this.on_message=(e,t,n)=>{if(n==="json"){this.onOverlayIpcMessage(t)}}}start(){if(this.isStarted){return}if(!this.ipc_callback_set){re.on("connect",this.on_connect);re.on("disconnect",this.on_disconnect);re.on("message",this.on_message);this.ipc_callback_set=true}re.startIpc();if(le&&!this.gpu_transfer_initited){try{this.gpu_transfer_initited=re.initGpuTransfer("0")}catch(e){console.error("Failed to init GPU transfer:",e)}if(this.gpu_transfer_initited){console.log("GPU transfer inited");this.gpu_transfer_adapter_id=re.getGpuAdapterId()}}this.isStarted=true}stop(){this.closeAllWindows();re.stopIpc();re.uninitGpuTransfer();this.game_adapter_desc="";this.game_adapter_id="";this.gpu_transfer_initited=false;this.gpu_transfer_adapter_id="";this.isGlobalBlockInput=false;this.isStarted=false}unloadInGameOverlay(e="app-quit"){const t={type:"unload-overlay",reason:e};re.sendJson(t)}closeAllWindows(){const e=Object.keys(this.windowInfos);e.forEach((e=>{this.closeOverlayWindow(Number(e))}))}onOverlayIpcMessage(e){const t=JSON.parse(e);const{type:n}=t;if(n==="offscreen-window-input"){const{window_id:e,msg:n}=t;const o=r.BrowserWindow.fromId(e);if(o){const e=7;const s=8;if(n===e){o.focus()}else if(n===s){o.blur()}else{const e=win32msg_to_inputevent(t);const n=o.getBounds();const s=r.screen.getDisplayMatching(n).scaleFactor;if(e){e.modifiers=t.modifiers}if(e&&e.type){if("x"in e)e["x"]=Math.round((e["x"]??0)/s);if("y"in e)e["y"]=Math.round((e["y"]??0)/s);o.webContents.sendInputEvent(e)}}}}else if(n==="global-block-state"){this.isGlobalBlockInput=t.block;console.log("Global block state updated:",this.isGlobalBlockInput)}else if(n==="offscreen-window-moved"){const{window_id:e,x:n,y:o}=t;console.log("Overlay window moved:",e,n,o);const s=this.windowInfos[e];if(s){s.rect.x=n;s.rect.y=o}}else if(n==="graphics-window-info"){const{width:e,height:n}=t;console.log("Graphics window info:",e,n);this.game_graphics_width=e;this.game_graphics_height=n}else if(n==="graphics-hook-info"){const n=t;this.game_adapter_desc=n.adapter_desc;this.game_adapter_id=n.adapter_id;console.log("Graphics hook info:",e)}}createOffscreenWindow(e,t,n){const o=r.screen.getDisplayNearestPoint({x:0,y:0}).scaleFactor;const s=Math.floor(t/o);const i=Math.floor(n/o);const a=new r.BrowserWindow({x:0,y:0,width:s,height:i,frame:false,show:false,transparent:true,backgroundColor:"#00000000",webPreferences:{offscreen:{useSharedTexture:true},webviewTag:true}});a.loadURL(e);return a}addOverlayWindow(e,t){const n=e.id;e.webContents.setFrameRate(30);const o=ce().randomBytes(20).toString("hex").substring(0,8);const s=`o-${o}-win-${process.pid}-${n}`;const i=3840+4;const a=2160+4;if(!re.create_window_shared_memory(n,s,i,a)){console.log("Failed to create shared memory for window:",n);return false}const c=e.getBounds();const l=r.screen.getDisplayMatching(c).scaleFactor;c.width=Math.round(c.width*l);c.height=Math.round(c.height*l);c.x=t.position.x;c.y=t.position.y;console.log(`Overlay window created: ${n}, real pixel bounds: ${JSON.stringify(c)}`);const d={type:"offscreen-window-info",window_id:n,share_mem_name:s,rect:c,accept_input:t.accept_input,block_input:t.block_input,transparent_test:t.transparent_test,visible:t.visible,titlebar_height:t.titlebar_height??0,layout:t.layout};this.windowInfos[n]=d;re.sendJson(d);e.webContents.on("paint",((e,t,o)=>{if(e.texture){if(re.open_window_shared_texture(n,e.texture.textureInfo.sharedTextureHandle)){const e=re.get_window_shared_texture_info(n);const o={type:"offscreen-window-frame-update",window_id:n,width:e.width,height:e.height,dirty_rect:t,shared_handle:e.shared_handle,adapter_id:this.gpu_transfer_adapter_id};re.sendJson(o)}e.texture.release()}else{const{width:e,height:s}=o.getSize();if(e==0||s==0){return}re.update_window_buffer(n,o.getBitmap(),e,s,t);const r={type:"offscreen-window-frame-update",window_id:n,width:e,height:s,dirty_rect:t,shared_handle:0,adapter_id:""};re.sendJson(r)}}));e.on("closed",(()=>{console.log("Overlay window closed:",n);this.removeOverlayWindow(n)}));if(t.accept_input){const t=new Map([["default","IDC_ARROW"],["pointer","IDC_ARROW"],["hand","IDC_HAND"],["crosshair","IDC_CROSS"],["text","IDC_IBEAM"],["wait","IDC_WAIT"],["help","IDC_HELP"],["move","IDC_SIZEALL"],["se-resize","IDC_SIZENWSE"],["nwse-resize","IDC_SIZENWSE"],["nesw-resize","IDC_SIZENESW"],["ns-resize","IDC_SIZENS"],["ew-resize","IDC_SIZEWE"],["none",""]]);e.webContents.on("cursor-changed",((e,o)=>{const s={type:"offscreen-window-cursor-update",window_id:n,cursor:t.get(o)??"IDC_ARROW"};re.sendJson(s)}));e.webContents.on("did-attach-webview",((e,o)=>{o.on("cursor-changed",((e,o)=>{const s={type:"offscreen-window-cursor-update",window_id:n,cursor:t.get(o)??"IDC_ARROW"};re.sendJson(s)}))}))}e.webContents.invalidate();return true}closeOverlayWindow(e){const t=this.windowInfos[e];if(t){const t=r.BrowserWindow.fromId(e);if(t){t.close()}this.removeOverlayWindow(e)}}removeOverlayWindow(e){const t=this.windowInfos[e];if(t){const t={type:"close-offscreen-window",window_id:e};re.sendJson(t);re.destroy_window_shared_memory(e);this.changeGlobalBlockInput(false);delete this.windowInfos[e]}}changeOverlayVisibility(e,t){const n=this.windowInfos[e];if(n){n.visible=t;re.sendJson(n)}}resizeOverlayWindow(e,t,n){const o=this.windowInfos[e];const s=r.BrowserWindow.fromId(e);if(o&&s){const e=r.screen.getDisplayMatching(s.getBounds()).scaleFactor;const i=Math.floor(t/e);const a=Math.floor(n/e);s.setBounds({...s.getBounds(),width:i,height:a});o.rect.width=t;o.rect.height=n;console.log("Resize window:",o);re.sendJson(o)}}getOverlayWindowInfo(e){return this.windowInfos[e]}changeOverlayBlockInput(e,t){const n=this.windowInfos[e];if(n){n.block_input=t;re.sendJson(n)}}changeGlobalBlockInput(e){this.isGlobalBlockInput=e;const t={type:"global-block",block:e};re.sendJson(t);console.log("request global block:",e)}getGlobalBlockInput(){return this.isGlobalBlockInput}takeScreenshot(e,t,n){const o={type:"screenshot",save_dir:e,context_id:t,with_overlay:n};re.sendJson(o)}}const de=new OverlayElectronService;const ue={x:0,y:0,width:800,height:600,frame:false,backgroundColor:"#00000000",fullscreenable:false,skipTaskbar:true,transparent:true};let pe=null;let fe=false;function initializeIpc(){if(fe){return}r.ipcMain.handle("ACTION_OVERLAY_BLOCK_INPUT",((e,t)=>{const n=r.BrowserWindow.fromWebContents(e.sender);if(!n||n!==pe){return}de.changeGlobalBlockInput(t)}));r.ipcMain.handle("ACTION_OVERLAY_UPDATE_HOTKEY",((e,t)=>{sendGlobalInitConfig(t)}));fe=true}async function createOverlayWindow(e,t,n){initializeIpc();const o={...ue,show:!t,webPreferences:{nodeIntegration:true,sandbox:false,contextIsolation:false,backgroundThrottling:false,webviewTag:true,partition:h.windowOptions.webPreferences?.partition,offscreen:t?{useSharedTexture:true}:false}};destroyOverlayWindow();pe=new r.BrowserWindow(o);pe.setMenu(null);pe.setMenuBarVisibility(false);pe.webContents.setWindowOpenHandler((t=>{const n=isValidAppUri(t.url)?"EVENT_APP_ACTIVATED":"EVENT_NEW_WINDOW_REQUESTED";e.webContents.send(n,{uri:t.url,source:"overlay"});return{action:"deny"}}));const onParentNavigation=e=>{if(e.isMainFrame&&!e.isSameDocument){destroyOverlayWindow()}};const onMessage=(t,n,o)=>{if(o==="json"){const t=JSON.parse(n);const{type:o}=t;if(o=="graphics-window-info"){const{width:n,height:o}=t;if(pe){console.log("Graphics window info:",n,o);de.resizeOverlayWindow(pe.id,n,o);e.webContents.send("EVENT_OVERLAY_GRAPHICS_INITIALIZED",t)}}else if(o=="graphics-hook-info"){console.log("graphics hooked:",t.api);e.webContents.send("EVENT_OVERLAY_GRAPHICS_HOOKED",t)}else if(o=="hotkey-trigger"){e.webContents.send("EVENT_OVERLAY_HOTKEY_PRESSED")}}else if(o==="binary"){const e=new Uint8Array(n);console.log("[in] Binary message:",e,e.length)}};const onConnect=(e,t)=>{console.log(`Overlay connected. Client: ${e}, PID: ${t}`);sendGlobalInitConfig(n);setTimeout((()=>{if(!pe){return}de.addOverlayWindow(pe,{position:{x:0,y:0},accept_input:true,block_input:true,transparent_test:true,visible:true});if(de.game_graphics_width>0&&de.game_graphics_height>0){de.resizeOverlayWindow(pe.id,de.game_graphics_width,de.game_graphics_height)}console.log("Overlay window created.")}))};const onClosed=()=>{if(pe){e.webContents.send("EVENT_OVERLAY_WINDOW_CLOSED",pe.id);pe.off("closed",onClosed);if(!pe.isDestroyed()){pe.webContents.closeDevTools()}}e.webContents.off("did-start-navigation",onParentNavigation);re.off("message",onMessage);re.off("connect",onConnect);de.stop()};e.webContents.on("did-start-navigation",onParentNavigation);pe.on("closed",onClosed);if(h.devMode){pe.webContents.openDevTools({mode:"detach"});if(!t){pe.setAlwaysOnTop(true)}}if(h.devMode){await pe.loadURL("http://localhost:8080/overlay.html")}else{await pe.loadFile(`${__dirname}/overlay.html`)}if(t){re.on("message",onMessage);re.on("connect",onConnect);de.start()}}function destroyOverlayWindow(){if(!pe||pe.isDestroyed()){return}pe.close();pe=null}function setOverlayMessagePort(e){if(!pe){return false}pe.webContents.postMessage("MESSAGE_PORT",null,[e]);return true}function sendGlobalInitConfig(e){const t={type:"global-init-config",global_block_color:0,global_block_hotkey:e};re.sendJson(t)}const _e=350;const he=500;const we=575;const ge=300;const me=620;function makeBaseOptions(e){const t=e.getBounds();return{x:Math.round(t.x+t.width/2-he/2),y:Math.round(t.y+t.height/2-me/2),minWidth:_e,maxWidth:we,width:he,minHeight:ge,height:me,icon:r.nativeImage.createFromPath(o.join(h.paths.assets,"lock.ico")),title:"Loading...",backgroundColor:"#ffffff",modal:true,parent:e,fullscreenable:false,maximizable:false,minimizable:false,skipTaskbar:true}}function createSetupIntentWindow(e,t){const n={...makeBaseOptions(e),webPreferences:{nodeIntegration:false,contextIsolation:true,partition:h.windowOptions.webPreferences?.partition}};const windowHandler=(n,o)=>{if(t.frameName!==o.frameName){return}n.setMenu(null);n.setMenuBarVisibility(false);n.webContents.setWindowOpenHandler((()=>({action:"deny"})));n.on("close",(()=>{e.webContents.off("did-create-window",windowHandler)}));if(h.devMode){n.webContents.openDevTools({mode:"detach"})}};e.webContents.on("did-create-window",windowHandler);return n}function getAppSettingsPath(){return s().join(h.paths.storage,"init.json")}function loadAppSettings(){try{const e=JSON.parse(t().readFileSync(getAppSettingsPath(),"utf8"));if(typeof e==="object"&&e!==null){if(typeof e.window==="object"&&e.window!==null){e.windows.app=e.window;delete e.window}return e}else{return{}}}catch{return{}}}function saveAppSettings(e){try{t().writeFileSync(getAppSettingsPath(),JSON.stringify(e),"utf8")}catch{}}const ye=s().resolve(s().dirname(process.execPath),"..","Update.exe");let be=[];let Se;let Oe="not-checked";let ve;let Ie;function isSameArgs(e){return e.length===be.length&&e.every(((e,t)=>e===be[t]))}function setState(e){if(e!==Oe){Oe=e;if(ve){ve(e)}}return e}function setUpdateStateChangedHandler(e){ve=e}function getUpdateState(){return Oe}function isUpdaterAvailable(){if(process.platform!=="win32"){return false}try{t().accessSync(ye,t().constants.R_OK);return true}catch{return false}}function checkForUpdate(e){if(!isUpdaterAvailable()){return Promise.resolve(Oe)}if(Oe==="checking"&&Ie){return Ie}if(!["not-checked","not-available","available"].includes(Oe)){return Promise.resolve(Oe)}const t=Oe;setState("checking");Ie=runSquirrel(["--checkForUpdate",e]).then((e=>{Ie=null;const t=JSON.parse(e.trim().split("\n").pop()??"{}");const n=typeof t==="object"&&t!==null&&Array.isArray(t.releasesToApply)&&t.releasesToApply.length>0;return setState(n?"available":"not-available")})).catch((e=>{Ie=null;setState(t);throw e}));return Ie}function fakeApplyUpdate(){return setState("applied")}function fakeApplyUpdateEror(){setState("checking");return setState("apply-error")}async function downloadAndApplyUpdate(e){if(Oe!=="available"&&Oe!=="apply-error"){return Promise.resolve(Oe)}setState("applying");try{await runSquirrel(["--update",e]);return setState("applied")}catch(e){setState("apply-error");throw e}}function restartApp(e){const t=s().basename(process.execPath);const n=["--processStartAndWait",t];if(typeof e==="string"){n.push("--process-start-args",e)}return runSquirrelDetached(n)}function runSquirrelDetached(e){return new Promise(((t,n)=>{const o=(0,l.spawn)(ye,e,{detached:true,windowsHide:true,stdio:"ignore"});o.unref();if(o.pid){t()}else{o.on("error",n)}}))}function runSquirrel(e){return new Promise(((t,n)=>{if(Se&&!isSameArgs(e)){throw new Error(`Updater process with arguments ${e} is already running`)}if(!Se){Se=(0,l.spawn)(ye,e,{windowsHide:true});be=e||[]}let o=false;Se.on("error",(e=>{o=true;n(e)}));let s="";Se?.stdout?.on("data",(e=>{s+=e}));let r="";Se?.stderr?.on("data",(e=>{r+=e}));Se.on("exit",((e,i)=>{Se=undefined;be=[];if(o){return}if(e!==0){return n(new Error(`Command failed: ${i!=null?i:e}\n${r}`))}t(s)}))}))}const Ce=i().app;const Ee=s().resolve(process.execPath,"..","..","Update.exe");const Ae=s().basename(process.execPath);function isSquirrelEvent(e){return e.length>=2&&e[1].startsWith("--squirrel-")}async function handleSquirrelEvent(e){const t={install:handleInstall,updated:handleUpdate,uninstall:handleUninstall,firstrun:handleFirstRun}[e[1].substring("--squirrel-".length)];if(t){await t(e)}}function removeDesktopShortcut(){d().spawnSync(Ee,["--removeShortcut",Ae])}function createDesktopShortcut(){d().spawnSync(Ee,["--createShortcut",Ae])}function handleInstall(){createDesktopShortcut()}function handleFirstRun(e){Ce.relaunch({args:e.slice(2)})}function handleUpdate(){try{const e=Ce.getPath("desktop");try{t().statSync(s().join(e,Ae.replace(".exe","")+".lnk"));removeDesktopShortcut();createDesktopShortcut()}catch{}}catch{createDesktopShortcut()}}async function handleUninstall(){await Ce.whenReady();initializeConfig(n.platform());const e=loadAppSettings();if(typeof e.installationId==="string"&&typeof e.userId==="string"){const t=Ce.getSystemLocale().substring(0,2);const n=["en","zh","de","es","fr","ja","ko","pt","pl"].includes(t)?await requestUninstallFeedback(t).catch((()=>null)):null;uninstallLog("Feedback",n);await postUninstallEvent(n,e.installationId,e.userId).catch((()=>null));if(n?.result==="cancel"){await restartApp().catch((()=>!h.devMode?i().shell.openExternal(`${h.protocolScheme}:`):null)).catch((()=>null));process.exitCode=1337;return}}if(!h.devMode){Ce.removeAsDefaultProtocolClient(h.protocolScheme);removeDesktopShortcut();try{recursiveDeleteDirectory(Ce.getPath("userData"))}catch{}}}async function postUninstallEvent(e,t,o){const s={name:e?.result==="cancel"?"app_uninstall_cancel":"app_uninstall",params:{app_version:Ce.getVersion(),os_locale:Ce.getSystemLocale(),os_arch:is64BitOs()?"x64":"ia32",os_version:n.release(),...e?{reason:e.reason||undefined,reason_other:e.otherReason||undefined,reason_notes:e.additionalNotes||undefined}:{}}};if(h.devMode){uninstallLog("Event",s)}else{await postProductionEvents(t,o,[s])}}function uninstallLog(e,...t){if(h.devMode){console.log(`[Uninstall] ${e}`,...t)}}async function requestUninstallFeedback(e){let t=new r.BrowserWindow({show:false,width:700,height:600,maximizable:false,minimizable:false,resizable:false,icon:h.windowOptions.icon,webPreferences:{backgroundThrottling:false,nodeIntegration:false,partition:"temp"}});t.setMenu(null);t.setMenuBarVisibility(false);const n={result:"continue",reason:null,otherReason:null,additionalNotes:null};let o;const s=new Promise((e=>{o=e}));t.on("closed",(()=>{t=null;o(n)}));t.webContents.setWindowOpenHandler((e=>{uninstallLog(`Navigating to ${e.url}`);const o=new URL(e.url);if(o.protocol==="https:"||o.protocol==="mailto:"){i().shell.openExternal(e.url).catch((()=>null))}if(o.protocol==="wemod:"){const e=o.searchParams;if(o.pathname==="resize"){const n=e.get("height")??"";if(/^\d+$/.test(n)){const e=Number(n);uninstallLog(`Resizing to ${e}px`);t?.setContentSize(t?.getContentSize()[0],e,true)}if(!t?.isVisible()){t?.show()}}if(o.pathname==="progress"){n.reason=e.get("reason")||null;n.otherReason=e.get("otherReason")||null;n.additionalNotes=e.get("additionalNotes")||null}if(o.pathname==="close"){const o=e.get("result");if(o==="continue"||o==="cancel"){n.result=o}t?.close()}}return{action:"deny"}}));try{await t.loadURL(`https://www.wemod.${h.devMode?"test":"com"}/uninstall?app=1`,{extraHeaders:`Accept-Language: ${e}`})}catch{t.close();return null}if(h.devMode){t.webContents.openDevTools({mode:"detach"})}return await s}function recursiveDeleteDirectory(e){if(!t().existsSync(e)){return}try{t().readdirSync(e).forEach((n=>{const o=s().join(e,n);try{if(t().lstatSync(o).isDirectory()){recursiveDeleteDirectory(o)}else{t().unlinkSync(o)}}catch{}}))}catch{}try{t().rmdirSync(e)}catch{}}const Te=new Set;function getWindowState(e){return{visible:e.isVisible(),minimized:e.isMinimized(),maximized:Te.has(e)||e.isMaximized()}}function makeEventDispatcher(e){return()=>e.webContents.send("EVENT_WINDOW_STATE_CHANGED",getWindowState(e))}function showWindow(e){if(Te.has(e)){Te.delete(e);if(!e.isMaximized()){e.maximize();return}}e.show()}function attachWindowStateObserver(e,t,n,o,s){if(typeof t.windows!=="object"||t.windows===null){t.windows={}}const r=t.windows[n]||{};t.windows[n]=r;if(r.bounds){const e=i().screen.getDisplayMatching(r.bounds);if(!e||!overlaps(r.bounds,e.bounds)){r.bounds=undefined}}if(r.bounds){e.setBounds(r.bounds)}if(r.maximized){Te.add(e)}if(!r.bounds&&!r.maximized){e.setBounds(getDefaultWindowBounds(e,o,s))}const a=makeEventDispatcher(e);e.on("minimize",a);e.on("maximize",a);e.on("unmaximize",a);e.on("restore",a);e.on("hide",a);e.on("show",a);const captureBounds=t=>{if(e.isVisible()){if(e.isNormal()||t&&r.maximized&&e.isMaximized()){r.bounds=e.getBounds()}}};e.on("close",(()=>captureBounds(false)));e.on("moved",(()=>captureBounds(false)));e.on("resize",(()=>captureBounds(true)));e.on("maximize",(()=>r.maximized=true));e.on("unmaximize",(()=>r.maximized=false));e.on("closed",(()=>{Te.delete(e);saveAppSettings(t)}));e.on("focus",(()=>e.flashFrame(false)))}function getDefaultWindowBounds(e,t,n){const o=n?r.screen.getDisplayMatching(n.getBounds()):r.screen.getPrimaryDisplay();const s=o.workAreaSize;const i=t.find((e=>e[0]<=s.width));const a=i?i[0]:e.getMinimumSize()[0];let c;if(i&&i[1]<=s.height){c=i[1]}else{const n=t.find((e=>e[1]<=s.height));c=n?n[1]:e.getMinimumSize()[1]}const l={width:a,height:c};if(n){l.x=Math.round(o.bounds.x+s.width/2-a/2);l.y=Math.round(o.bounds.y+s.height/2-c/2)}return l}const Pe="#18293a";const Ne=[[1440,825],[1250,825],[1200,640]];const We=800;const De=700;function createWebviewWindow(e,t,n){let o=Pe;let s=Ne;let r=We;let i=De;if(t.features){const e=t.features.replaceAll(",","&");const n=new URLSearchParams(e);const a=Object.fromEntries(n);if(a.titleColor){o=a.titleColor}if(a.preferredWidth&&a.preferredHeight){s=[[parseInt(a.preferredWidth,10),parseInt(a.preferredHeight,10)]]}if(a.minWidth){r=parseInt(a.minWidth,10)}if(a.minHeight){i=parseInt(a.minHeight,10)}}const a={titleBarStyle:"hidden",titleBarOverlay:{height:50,color:o,symbolColor:"white"},frame:false,parent:undefined,show:false,width:s[0][0],height:s[0][1],minWidth:r||We,minHeight:i||De,backgroundColor:h.windowOptions.backgroundColor,icon:h.windowOptions.icon,webPreferences:{backgroundThrottling:false,nodeIntegration:false,sandbox:true,disableDialogs:true,partition:h.windowOptions.webPreferences?.partition}};const windowHandler=(o,r)=>{if(t.frameName!==r.frameName){return}e.webContents.off("did-create-window",windowHandler);o.setMenu(null);o.setMenuBarVisibility(false);attachWindowStateObserver(o,n,r.frameName,s,e);const onParentNavigation=e=>{if(e.isMainFrame&&!e.isSameDocument){o.destroy()}};e.webContents.on("did-start-navigation",onParentNavigation);o.on("closed",(()=>{e.webContents.off("did-start-navigation",onParentNavigation);e.webContents.send("EVENT_WEBVIEW_WINDOW_CLOSED",r.frameName)}));o.webContents.setWindowOpenHandler((n=>{const o=isValidAppUri(n.url)?"EVENT_APP_ACTIVATED":"EVENT_NEW_WINDOW_REQUESTED";e.webContents.send(o,{uri:n.url,source:t.frameName});return{action:"deny"}}));o.webContents.on("will-navigate",((e,t)=>{if(t!==r.url){e.preventDefault()}}));let i=false;o.webContents.on("did-fail-load",((t,n,s,a,c)=>{if(c){i=true;e.webContents.send("EVENT_WEBVIEW_WINDOW_LOAD_ERROR",r.frameName);o.destroy()}}));o.webContents.on("did-finish-load",(()=>{if(!i){e.webContents.send("EVENT_WEBVIEW_WINDOW_LOAD_FINISH",r.frameName)}}));if(h.devMode){o.webContents.openDevTools({mode:"detach"})}};e.webContents.on("did-create-window",windowHandler);return a}function showWebviewWindow(e){const t=r.BrowserWindow.getAllWindows().find((t=>t.webContents.mainFrame.name===e));if(t){showWindow(t);return true}else{return false}}const Re=i().app;let ke="quit";let xe;let He;let Be;let Me;let Ue=false;let Le;let Fe;let qe;bootstrap();function bootstrap(){Re.commandLine.appendSwitch("no-proxy-server");Re.commandLine.appendSwitch("force-ui-direction","ltr");Re.commandLine.appendSwitch("disable-gpu-process-crash-limit");Re.commandLine.appendSwitch("disable-site-isolation-trials");Re.disableDomainBlockingFor3DAPIs();if(h.devMode){Re.commandLine.appendSwitch("ignore-certificate-errors","true");if(Re.commandLine.hasSwitch("wemod-temp")&&h.windowOptions.webPreferences?.partition!==undefined){h.windowOptions.webPreferences.partition="temp"}}qe=Re.commandLine.getSwitchValue("wemod-user-data");if(qe){Re.setPath("userData",`${Re.getPath("userData")}-${qe}`)}if(isSquirrelEvent(process.argv)){Re.on("window-all-closed",(()=>{}));const exitApp=()=>Re.exit(process.exitCode);handleSquirrelEvent(process.argv).then(exitApp,exitApp);return}if(!Re.requestSingleInstanceLock()){Re.quit();return}if(Re.commandLine.hasSwitch("disable-gpu")){Re.disableHardwareAcceleration()}He=Re.commandLine.hasSwitch("start-in-tray");Fe=Re.commandLine.getSwitchValue("wemod-env")||null;Re.whenReady().then(run)}function run(){Re.setAppUserModelId(h.userModelId);Re.on("window-all-closed",(()=>Re.quit()));Re.on("will-quit",(()=>{if(Ue){restartApp(Le)}}));Re.on("second-instance",((e,t)=>{if(xe){xe.webContents.send("EVENT_APP_ACTIVATED",{uri:extractAppUriFromAgv(t),source:"external"});restoreWindow()}}));Re.on("web-contents-created",((e,t)=>{if(t.session!==i().session.fromPartition("persist:ads")){return}let n=0;const o=["mouseMove","pointerMove","pointerRawUpdate"];t.on("input-event",(async(e,s)=>{if(o.includes(s.type)){return}const r=await t.executeJavaScript("navigator.userActivation.isActive").catch((()=>false));if(r){n=Date.now()}}));t.setWindowOpenHandler((e=>{const t=new URL(e.url);if(["https:","http:"].includes(t.protocol)&&Date.now()-n<=1e3){xe?.webContents.send("EVENT_NEW_WINDOW_REQUESTED",{uri:e.url,source:"ad"})}return{action:"deny"}}));let s=null;t.on("will-navigate",((e,t)=>{s??=t;if(t!==s){e.preventDefault()}}));t.on("did-navigate",(()=>{t.setAudioMuted(true)}))}));if(h.devMode){const e=[process.cwd()];if(qe){e.push(`--wemod-user-data=${qe}`)}Re.setAsDefaultProtocolClient(h.protocolScheme,process.execPath,e)}else{Re.setAsDefaultProtocolClient(h.protocolScheme)}i().nativeTheme.on("updated",updateTrayIconImage);initializeConfig(n.platform());Me=loadAppSettings();initializeAppWindow();registerAppChannels();setUpdateStateChangedHandler((e=>{if(xe){xe.webContents.send("EVENT_UPDATE_STATE_CHANGED",e)}}));if(h.devMode){xe?.loadURL("http://localhost:8080")}else{blockHttpRequests();xe?.loadFile(`${__dirname}/index.html`)}blockMaliciousAdRequests();if(h.devMode){openDevTools();setFileOrigin()}if(n.platform()==="darwin"){const e=r.nativeImage.createFromPath(o.join(h.paths.assets,"mac","icon.png"));Re.dock.setIcon(e);Re.dock.show()}}function blockHttpRequests(){i().session.fromPartition(h.windowOptions.webPreferences?.partition??"").webRequest.onBeforeRequest({urls:["http://*/*"]},((e,t)=>{t({cancel:e.webContents&&e.webContents.getURL().startsWith("file://")})}))}const Ve=/^(?:https?|wss?):\/\/(?:localhost|127\.\d+\.\d+\.\d+)(?:$|\/.*)/;function blockMaliciousAdRequests(){i().session.fromPartition("persist:ads").webRequest.onBeforeRequest({urls:["*://*/*"]},((e,t)=>{if(Ve.test(e.url)){t({cancel:true})}else{t({})}}))}function restoreWindow(){if(!xe){return}showWindow(xe);if(xe.isMinimized()){xe.restore()}destroyTrayIcon()}function destroyTrayIcon(){if(Be){Be.destroy();Be=null;xe?.webContents.send("EVENT_RESTORED_FROM_TRAY")}}function closeToTray(){if(!Be){Be=createTrayIcon();xe?.hide();xe?.webContents.send("EVENT_CLOSED_TO_TRAY")}}function createTrayIcon(){const e=new r.Tray(getTrayIconImageForTheme());e.setToolTip("WeMod");e.setContextMenu(r.Menu.buildFromTemplate([{label:"Open WeMod",type:"normal",click:()=>restoreWindow()},{type:"separator"},{label:"Exit",type:"normal",click:()=>xe?.webContents.send("EVENT_APP_QUIT_REQUESTED")}]));e.on("click",(()=>restoreWindow()));return e}function updateTrayIconImage(){Be?.setImage(getTrayIconImageForTheme())}function getTrayIconImageForTheme(){if(n.platform()==="darwin"){const e=r.nativeImage.createFromPath(o.join(h.paths.assets,"mac","tray_mac.png")).resize({width:24});e.setTemplateImage(true);return e}else if(n.platform()==="win32"){const e=i().nativeTheme.shouldUseDarkColors?"tray_dark.ico":"tray_light.ico";return r.nativeImage.createFromPath(o.join(h.paths.assets,e))}else{return r.nativeImage.createFromNamedImage("NSInfo").resize({width:24})}}function registerHandler(e,t){if(!xe){return}registerHandlerForWindow(xe,e,((...n)=>{if(h.devMode){console.debug(`[IPC] ${e}`,n)}return t(...n)}))}function registerOnHandler(e,t){if(!xe){return}registerOnHandlerForWindow(xe,e,((n,...o)=>{if(h.devMode){console.debug(`[IPC] ${e}`,o)}t(n,...o)}))}function registerAppChannels(){registerHandler("ACTION_INITIALIZE_APP",(async()=>{if(!xe){return}const e=n.cpus();const t=h.devMode?null:Re.getVersion().split("-")[1]?.split(".")[0]||"stable";return{app:{hwnd:xe.getNativeWindowHandle().readUInt32LE(0),version:Re.getVersion()+(h.devMode?"-dev":""),releaseChannel:t,updaterAvailable:isUpdaterAvailable(),overlayAvailable:isOverlayAvailable(),locale:Re.getSystemLocale(),region:Re.getLocaleCountryCode()||null,launchUri:extractAppUriFromAgv(process.argv),paths:{app:h.paths.app,assets:h.paths.assets,storage:h.paths.storage,temp:h.paths.temp},osPlatform:process.platform,osArch:is64BitOs()?"x64":"ia32",osVersion:n.release(),osHostname:n.hostname(),osHomeDir:n.homedir(),deviceCpuModel:e.length>0?e[0].model:null,deviceCpuCount:e.length,wow64:isWow64Process(),env:getEnvironmentVariables(),devMode:h.devMode,isLoggedIn:true,userAccount:{uuid:"cracked-user-12345",email:"<EMAIL>",username:"CrackedUser",subscription:{state:"active",period:"yearly",trialEndsAt:null,endsAt:new Date(Date.now()+365*24*60*60*1000).toISOString(),nextInvoice:null,pastDueInvoice:null},flags:7,featurebaseJwt:"fake-jwt-token"}},envOverride:typeof t==="string"&&!["stable","beta"].includes(t)?await readEnvironmentOverride():null,updateState:getUpdateState(),window:getWindowState(xe),startedInTray:He}}));registerHandler("ACTION_SET_INSTALLATION_INFO",((e,t)=>{if(typeof e!=="string"||typeof t!=="string"){return Promise.reject(new TypeError("Expected string."))}else{Me.installationId=e;Me.userId=t;saveAppSettings(Me)}}));registerHandler("ACTION_GET_INSTALLED_AV_PRODUCTS",(()=>getInstalledAVProductNames().catch((()=>null))));registerHandler("ACTION_GET_CREATOR_CONFIGURATION",(()=>getCreatorConfiguration()));registerHandler("ACTION_GET_WINDOW_STATE",(()=>xe?getWindowState(xe):null));registerHandler("ACTION_OPEN_DEV_TOOLS",openDevTools);registerHandler("ACTION_OPEN_EXTERNAL",(e=>expectString(e,(e=>Re.getApplicationNameForProtocol(e)?i().shell.openExternal(e).then((()=>true)).catch((()=>false)):Promise.resolve(false)))));registerHandler("ACTION_OPEN_FILE_PATH",(e=>expectString(e,(e=>i().shell.openPath(e).then((()=>true)).catch((()=>false))))));registerHandler("ACTION_SHOW_FILE_LOCATION",(e=>expectString(e,(e=>i().shell.showItemInFolder(e)))));registerHandler("ACTION_QUIT_APP",(()=>{ke="quit-no-confirm";Re.quit()}));registerHandler("ACTION_CLOSE_WINDOW",(()=>xe?.close()));registerHandler("ACTION_MINIMIZE_WINDOW",(()=>xe?.minimize()));registerHandler("ACTION_MAXIMIZE_WINDOW",(()=>xe?.maximize()));registerHandler("ACTION_UNMAXIMIZE_WINDOW",(()=>xe?.unmaximize()));registerHandler("ACTION_DEFAULT_WINDOW",(()=>{if(!xe){return}if(xe.isMinimized()){xe.restore()}if(xe.isMaximized()){xe.unmaximize()}const e=getDefaultWindowBounds(xe,h.preferredSizes,xe);if(e?.width&&e?.height){xe.setSize(e.width,e.height)}}));registerHandler("ACTION_SHOW_WINDOW",(()=>{if(He){He=false;Be=createTrayIcon()}else if(!Be&&xe){showWindow(xe)}}));registerHandler("ACTION_FOCUS_WINDOW",(()=>{restoreWindow();xe?.focus();xe?.setAlwaysOnTop(true);xe?.setAlwaysOnTop(false)}));registerHandler("ACTION_RELOAD_WINDOW",(()=>{setTimeout((()=>xe?.loadURL(xe.webContents.getURL())),100);return new Promise((()=>null))}));registerHandler("ACTION_SET_CLOSE_BEHAVIOR",(e=>{if(typeof e!=="string"||!["tray","quit","quit-no-confirm"].includes(e)){return Promise.reject(new TypeError("Invalid close behavior type."))}ke=e;return Promise.resolve()}));registerHandler("ACTION_GET_MEMORY_INFO",(()=>({free:n.freemem(),total:n.totalmem()})));registerHandler("ACTION_READ_SHORTCUT_LINK",(e=>expectString(e,(e=>{try{return i().shell.readShortcutLink(e)}catch{return null}}))));registerHandler("ACTION_SHOW_SAVE_FILE_DIALOG",(e=>xe?i().dialog.showSaveDialog(xe,e):null));registerHandler("ACTION_SHOW_OPEN_FILE_DIALOG",(e=>xe?i().dialog.showOpenDialog(xe,e):null));registerHandler("ACTION_CHECK_FOR_UPDATE",(e=>expectUpdateFeedUrl(e,(e=>checkForUpdate(e)))));registerHandler("ACTION_APPLY_UPDATE",(e=>expectUpdateFeedUrl(e,(e=>downloadAndApplyUpdate(e)))));registerHandler("ACTION_FAKE_APPLY_UPDATE",(()=>fakeApplyUpdate()));registerHandler("ACTION_FAKE_APPLY_UPDATE_ERROR",(()=>fakeApplyUpdateEror()));registerHandler("ACTION_RESTART_APP_FOR_UPDATE",(e=>expectString(e,(e=>{if(getUpdateState()==="applied"){ke="quit-no-confirm";Ue=true;const t=[];if(Be||He){t.push("--start-in-tray")}if(isValidAppUri(e)){t.push(e)}Le=t.length>0?t.join(" "):undefined;Re.quit();return true}else{return false}}))));registerHandler("ACTION_COPY_TEXT",(e=>{expectString(e,(e=>r.clipboard.writeText(e)))}));registerHandler("ACTION_CREATE_DESKTOP_SHORTCUT",(e=>{expectDesktopShortcutOptions(e,((e,t,n)=>main_createDesktopShortcut(e,t,n)))}));registerHandler("ACTION_SHOW_TOAST",(e=>expectString(e,(e=>new Promise((t=>{if(r.Notification.isSupported()&&process.platform==="win32"&&n.release().match(/^1\d\./)){try{const n=new r.Notification({toastXml:e});n.once("show",(()=>t(true)));n.once("failed",(()=>t(false)));n.show()}catch{t(false)}}else{t(false)}}))))));registerHandler("ACTION_GET_SYSTEM_IDLE_TIME",(()=>r.powerMonitor.getSystemIdleTime()));registerHandler("ACTION_SHOW_WEBVIEW_WINDOW",(e=>expectString(e,showWebviewWindow)));registerHandler("ACTION_POST_ANALYTICS_EVENT",(e=>expectAnalyticsEvent(e,postAnalyticsEvent)));registerHandler("ACTION_FLASH_WINDOW",(()=>xe?.flashFrame(true)));registerHandler("ACTION_GET_DISPLAY_COUNT",(()=>i().screen.getAllDisplays().length));registerHandler("ACTION_CAPTURE_SCREENSHOT",(e=>expectString(e,captureScreenshot)));registerHandler("ACTION_CREATE_OVERLAY_WINDOW",(async e=>{if(xe&&isOverlayHotkey(e?.hotkey)){return await createOverlayWindow(xe,typeof e.processId==="number"?e.processId:undefined,e.hotkey)}return false}));registerOnHandler("ACTION_SET_OVERLAY_PORT",(e=>{if(e.ports.length===1){setOverlayMessagePort(e.ports[0])}}));registerHandler("ACTION_DESTROY_OVERLAY_WINDOW",(()=>destroyOverlayWindow()));registerHandler("ACTION_UPDATE_OVERLAY_HOTKEY",(e=>{if(isOverlayHotkey(e)){sendGlobalInitConfig(e)}}));registerHandler("ACTION_START_CAPTURE",(async e=>{if(!xe){return Promise.reject(new TypeError("App window not initialized before starting capture."))}if(!isCaptureConfig(e?.config)){return Promise.reject(new TypeError("Invalid capture config."))}if(typeof e?.processId!=="number"){return Promise.reject(new TypeError("Invalid process ID."))}return await startCapture(xe,e.config,e.processId)}));registerHandler("ACTION_STOP_CAPTURE",(async()=>await stopCapture()));registerHandler("ACTION_START_CAPTURE_REPLAY_BUFFER",(async()=>await startReplayBuffer()));registerHandler("ACTION_SAVE_CAPTURE_REPLAY_BUFFER",(async()=>await saveReplayBuffer()));registerHandler("ACTION_STOP_CAPTURE_REPLAY_BUFFER",(async()=>await stopReplayBuffer()))}function expectUpdateFeedUrl(e,t){return typeof e==="string"&&e.startsWith("https://")?t(e):Promise.reject(new TypeError("Invalid update feed URL string."))}function expectString(e,t){return typeof e==="string"?t(e):Promise.reject(new TypeError("Expected string."))}function expectDesktopShortcutOptions(e,t){return typeof e==="object"&&typeof e.uri==="string"&&e.uri.startsWith(h.protocolScheme+":")&&typeof e.label==="string"&&(typeof e.icon==="undefined"||typeof e.icon==="string"||e.icon===null)?t(e.uri,e.label,e.icon):Promise.reject(new TypeError("Invalid desktop shortcut options."))}function expectAnalyticsEvent(e,t){return typeof e==="object"&&e!==null&&typeof e.name==="string"&&typeof e.params==="object"&&e.params!==null?t(e):Promise.reject(new TypeError("Invalid analytics event arguments."))}function isOverlayHotkey(e){return typeof e==="object"&&typeof e.name==="string"&&typeof e.key==="number"&&typeof e.ctrl==="boolean"&&typeof e.alt==="boolean"&&typeof e.shift==="boolean"&&typeof e.on_keyup==="boolean"}function isCaptureConfig(e){return typeof e==="object"&&!!e.videoParams&&typeof e.videoParams==="object"&&typeof e.videoParams.width==="number"&&typeof e.videoParams.height==="number"&&typeof e.videoParams.fps==="number"&&typeof e.videoParams.bitrate==="number"&&!!e.audioParams&&typeof e.audioParams==="object"&&typeof e.audioParams.inputDeviceId==="string"&&typeof e.audioParams.outputDeviceId==="string"&&typeof e.bufferSeconds==="number"}async function readEnvironmentOverride(){if(Fe){try{const t=JSON.parse(await e.promises.readFile(Fe,"utf-8"));if(typeof t==="object"&&t!==null){return t}}catch{}}return null}function openDevTools(){xe?.webContents.openDevTools({mode:"detach"})}function setFileOrigin(){xe?.webContents.session.webRequest.onBeforeSendHeaders(((e,t)=>{if(!e.url.includes("api-cdn.wemod")){t(e);return}t({requestHeaders:{origin:"*",...e.requestHeaders}})}));xe?.webContents.session.webRequest.onHeadersReceived(((e,t)=>{if(!e.url.includes("api-cdn.wemod")){t(e);return}t({responseHeaders:{...e.responseHeaders,"access-control-allow-origin":["*"]}})}))}function initializeAppWindow(){xe=new r.BrowserWindow(h.windowOptions);xe.setMenu(null);xe.setMenuBarVisibility(false);attachWindowStateObserver(xe,Me,"app",h.preferredSizes);xe.webContents.setWindowOpenHandler(handleNewWindow);xe.on("closed",(()=>{xe=null}));xe.on("close",(e=>{if(ke==="tray"&&!Be){e.preventDefault();closeToTray()}if(ke==="quit"){e.preventDefault();xe?.webContents.send("EVENT_APP_QUIT_REQUESTED")}if(ke==="quit-no-confirm"){xe?.hide()}}))}function handleNewWindow(e){if(e.frameName==="setup_intent"&&xe){return{action:"allow",overrideBrowserWindowOptions:createSetupIntentWindow(xe,e)}}else if(e.frameName.startsWith("webview:")&&xe){return{action:"allow",overrideBrowserWindowOptions:createWebviewWindow(xe,e,Me)}}else{const t=isValidAppUri(e.url)?"EVENT_APP_ACTIVATED":"EVENT_NEW_WINDOW_REQUESTED";xe?.webContents.send(t,{uri:e.url,source:"app"});return{action:"deny"}}}async function main_createDesktopShortcut(t,n,s){if(process.platform!=="win32"){return Promise.resolve()}n=n.replace(/[\\/:"*?<>|]+/g,"").replace(/  +/g," ");if(!n.length){return Promise.resolve()}s??=getDefaultIconFile();const r=Re.getPath("desktop");const i=o.join(r,`${n}.url`);const a=`[{000214A0-0000-0000-C000-000000000046}]\r\nProp3=19,11\r\n[InternetShortcut]\r\nIDList=\r\nIconIndex=0\r\nURL=${t}\r\nIconFile=${s}`;try{return await e.promises.writeFile(i,a,"utf8")}catch{throw new Error("Failed to create shortcut file.")}}let ze=null;function getDefaultIconFile(){if(ze===null){try{const t=n.platform()==="darwin"?"WeMod.app":"WeMod.exe";ze=o.resolve(o.dirname(process.execPath),"..",t);e.accessSync(ze,e.constants.R_OK)}catch{const e=n.platform()==="darwin"?o.join("mac","icon.icns"):"icon.ico";ze=o.join(h.paths.assets,e)}}return ze}async function postAnalyticsEvent(e){if(!Me.installationId){return false}await postProductionEvents(Me.installationId,Me.userId,[e]);return true}let $e;async function captureScreenshot(t){let s;let r;$e??=__nccwpck_require__(864);try{const i=$e.Window.all();const a=i.find((e=>e.appName===t));if(!a){return}s=await a.captureImage();if(s){const i=o.join(n.tmpdir(),`${t}.png`);r=await s.toPng();await e.promises.writeFile(i,r)}}catch{}s=null;r=null}function isOverlayAvailable(){return ie}})();module.exports=__webpack_exports__})();
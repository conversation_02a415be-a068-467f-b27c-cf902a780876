"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4151],{"settings/resources/elements/release-channel-selector":(e,t,a)=>{a.r(t),a.d(t,{ReleaseChannelSelector:()=>d});var i=a(15215),n=a("aurelia-framework"),s=a(19072),o=a("shared/dialogs/basic-dialog"),r=a(20057),l=a(54995),c=a(49442);let d=class{#e;#t;#a;constructor(e,t){this.#e=e,this.#t=t,this.selectedChannel=e.updateChannel||"disabled"}bind(){this.#i()}accountChanged(){this.#i()}catalogChanged(){this.#i()}#i(){const e=[...this.catalog.releaseChannels,...this.account.releaseChannels??[]];this.channelIds=e.map((e=>e.id)),this.channelLabels={disabled:r.F2.literal("—"),...Object.fromEntries(e.map((e=>[e.id,r.F2.literal(e.name)])))},this.channelLabels[this.selectedChannel]??=r.F2.literal("Unknown")}async selectedChannelChanged(e,t){if(this.#a)return void(this.#a=!1);if(void 0===t)return;if(e===t)return;const a=this.channelIds.indexOf(e),i=this.channelIds.indexOf(t);let n=null;a>i&&(n=await this.#t.show({headerLabel:"release_channel_selector.app_upgrade",message:"release_channel_selector.channel_may_include_unstable_features",options:[{label:"release_channel_selector.continue",style:"primary"},{label:"release_channel_selector.cancel"}]})),a<i&&(n=await this.#t.show({headerLabel:"release_channel_selector.app_downgrade",message:"release_channel_selector.youll_have_to_wait",options:[{label:"release_channel_selector.continue",style:"primary"},{label:"release_channel_selector.cancel"}]})),"release_channel_selector.continue"===n?await this.#e.setUpdateChannel(e)&&this.#e.checkForUpdate().catch(c.Y):(this.#a=!0,this.selectedChannel=t)}};(0,i.Cg)([n.observable,(0,i.Sn)("design:type",String)],d.prototype,"selectedChannel",void 0),d=(0,i.Cg)([(0,l.m6)({selectors:{catalog:(0,l.$t)((e=>e.catalog)),account:(0,l.$t)((e=>e.account))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[s.s,o.BasicDialogService])],d)},"settings/resources/elements/release-channel-selector.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>i});const i='<template> <require from="../../../shared/resources/elements/selection-input"></require> <selection-input value.two-way="selectedChannel" options.bind="channelIds" labels.bind="channelLabels" disabled.bind="selectedChannel === \'disabled\'" enable-i18n="true"> </selection-input> </template> '},"settings/resources/elements/setting":(e,t,a)=>{a.r(t),a.d(t,{Setting:()=>l});var i=a(15215),n=a("aurelia-framework"),s=a(20770),o=a(54995),r=a(48881);let l=class{#n;#s;constructor(e){this.#s=!1,this.#n=e}bind(){this.settingsChanged()}attached(){this.#s=!0}settingsChanged(){const e=this.settings[this.setting.key];this.value=void 0!==e?e:this.setting.default}activate(e){this.setting=e}valueChanged(e){this.#s&&this.#n.dispatch(r.Kc,{[this.setting.key]:e},"settings")}};(0,i.Cg)([n.bindable,(0,i.Sn)("design:type",Object)],l.prototype,"value",void 0),l=(0,i.Cg)([(0,o.m6)({selectors:{settings:(0,o.$t)((e=>e.settings))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[s.il])],l)},"settings/resources/elements/sound-pack":(e,t,a)=>{a.r(t),a.d(t,{SoundPack:()=>r});var i=a(15215),n=a("aurelia-framework"),s=a(43570),o=a(38777);let r=class{#o;constructor(e){this.soundPlayer=e,this.#o=!1,this.selectedSoundPack=e.selectedPack}async selectedSoundPackChanged(e,t){void 0!==t&&(await this.soundPlayer.setSoundPack(e,"settings"),await this.soundPlayer.preload())}async preview(){if(!this.#o){this.#o=!0;try{await this.soundPlayer.play(s.A.Enable),await(0,o.Wn)(1e3),await this.soundPlayer.play(s.A.Disable)}finally{this.#o=!1}}}};(0,i.Cg)([n.observable,(0,i.Sn)("design:type",String)],r.prototype,"selectedSoundPack",void 0),r=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[s.L])],r)},"settings/resources/elements/sound-pack.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var i=a(14385),n=a.n(i),s=new URL(a(19899),a.b);const o='<template> <require from="./sound-pack.scss"></require> <require from="../../../shared/resources/elements/selection-input"></require> <div class="sound-pack"> <selection-input value.two-way="selectedSoundPack" options.bind="soundPlayer.availablePacks" enable-i18n.bind="false"> </selection-input> <a href="#" class="preview" click.trigger="preview()" disabled.bind="disablePreview"> <i><inline-svg src="'+n()(s)+'"></inline-svg></i> </a> </div> </template> '},"settings/resources/elements/sound-pack.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var i=a(31601),n=a.n(i),s=a(76314),o=a.n(s)()(n());o.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.sound-pack{display:flex;align-items:center}.sound-pack .preview{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;background-color:rgba(255,255,255,.6);box-shadow:none;color:var(--theme--background);min-width:var(--cta--height);width:var(--cta--height);border-radius:50%;justify-content:center;align-items:center;margin-left:10px}.sound-pack .preview,.sound-pack .preview *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .sound-pack .preview{border:1px solid #fff}}.sound-pack .preview>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.sound-pack .preview>*:first-child{padding-left:0}.sound-pack .preview>*:last-child{padding-right:0}.sound-pack .preview svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .sound-pack .preview svg *{fill:CanvasText}}.sound-pack .preview svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .sound-pack .preview svg{opacity:1}}.sound-pack .preview img{height:50%}.sound-pack .preview:disabled{opacity:.3}.sound-pack .preview:disabled,.sound-pack .preview:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.sound-pack .preview:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.sound-pack .preview:not(:disabled):hover svg{opacity:1}}.sound-pack .preview:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.sound-pack .preview svg{opacity:1}.sound-pack .preview svg *{--cta__icon--color: var(--theme--background)}@media(hover: hover){.sound-pack .preview:not(:disabled):hover{background-color:var(--theme--highlight)}}.sound-pack .preview,.sound-pack .preview>*{padding:0 !important}",""]);const r=o},"settings/resources/elements/text.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});var i=a(14385),n=a.n(i),s=new URL(a(34635),a.b);const o='<template bindable="value"> <require from="./text.scss"></require> <div class="text-setting"> <input type="text" value.two-way="value" maxlength.bind="setting.maxLength || 9999"> <button class="clear-button" click.delegate="value = \'\'" if.bind="value"> <inline-svg src="'+n()(s)+'"></inline-svg> </button> </div> </template> '},"settings/resources/elements/text.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>p});var i=a(31601),n=a.n(i),s=a(76314),o=a.n(s),r=a(4417),l=a.n(r),c=new URL(a(83959),a.b),d=o()(n()),h=l()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${h}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.text-setting{position:relative;width:100%}.text-setting input{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%}.text-setting input::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.text-setting input::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.text-setting input:disabled{opacity:.5}.text-setting input:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.text-setting input:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.text-setting input[maxlength="2"]{max-width:70px}.text-setting .clear-button{position:absolute;right:12px;top:11px;background:rgba(0,0,0,0);border:0;padding:0;opacity:.2;transition:opacity .15s;display:inline-flex}.text-setting .clear-button:hover{opacity:1}`,""]);const p=d}}]);
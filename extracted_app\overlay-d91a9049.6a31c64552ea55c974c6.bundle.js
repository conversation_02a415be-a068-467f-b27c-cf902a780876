"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[163],{733:(e,t,i)=>{i.d(t,{V:()=>b});var a=i(15215),s=i("aurelia-dialog"),n=i("aurelia-framework"),r=i(20770),o=i("dialogs/choose-plan-promo-dialog"),l=i(30770),h=i(15182),c=i("dialogs/pro-showcase-columns-dialog"),d=i("dialogs/pro-showcase-dialog"),u=i(60692),m=i(68539),p=i(54995),g=i(14046),y=i(48881),v=i(62914);let b=class{#e;#t;#i;#a;#s;#n;#r;#o;constructor(e,t,i,a,s,n,r,o){this.#e=e,this.#t=t,this.#a=i,this.#s=a,this.#n=s,this.#i=n,this.#r=r,this.#o=o}attached(){}detached(){}async#l(){return this.account?.subscription||this.#r.assignments.has(u.n.ProShowcaseModal_old)?null:await this.#r.trigger(u.n.ProShowcaseModal).catch((()=>null))}#h(){if(this.timestamps.lastAutoProShowcaseDialogSeen)return!1;if(!(this.flags.hasUsedHotkeys||this.flags.hasUsedInteractiveControls||this.flags.hasUsedOverlay))return!1;const e=Object.values(this.gameHistory).filter((e=>!!e.lastPlayedAt)).sort(((e,t)=>(e.lastPlayedAt??"").localeCompare(t.lastPlayedAt??"")))[0];return!(e?.lastPlayedAt&&(0,g.Ov)(Date.now(),new Date(e.lastPlayedAt))/60/60<6)}#c(){return!!this.timestamps.lastAutoProShowcaseDialogSeen&&(!this.timestamps.lastAutoProShowcaseDialogSeenAgain&&(!((0,g.c_)(Date.now(),new Date(this.timestamps.lastAutoProShowcaseDialogSeen))<7)&&!(Object.values(this.gameHistory).filter((e=>!!e.lastPlayedAt&&(0,g.c_)(Date.now(),new Date(e.lastPlayedAt))<7)).filter((e=>e.playDuration&&e.playDuration>300)).length<1)))}async autoShowDialog(){if(this.#t.hasOpenDialog)return!1;const e=await this.#l();if(null!==e&&0!==e){if(this.#h())return this.#e.dispatch(y.vk,"lastAutoProShowcaseDialogSeen"),this.#d(e),!0;if(this.#c())return this.#e.dispatch(y.vk,"lastAutoProShowcaseDialogSeenAgain"),this.#d(e),!0}return!1}async#d(e,t="auto",i){null!==e&&0!==e||this.openOldProDialog(t),1===e&&this.openStandardDialog(t,(0,h.Xz)(i)?i:"save_mods"),2===e&&this.openColumnsDialog(t,(0,h.pF)(i)?i:"save_mods"),3===e&&this.openChoosePlanPromoDialog(t)}async openDialog(e="auto",t){if(this.#t.hasOpenDialog)return!1;const i=await this.#l();return this.#d(i,e,t),!0}async openStandardDialog(e,t){this.#o.event("pro_showcase_dialog_open",{defaultFeature:t,trigger:e});const i=await this.#a.open({defaultFeature:t},!0);this.#o.event("pro_showcase_dialog_close",{defaultFeature:t,currentFeature:i.output,trigger:e})}async openColumnsDialog(e,t){this.#o.event("pro_showcase_columns_dialog_open",{defaultFeature:t,trigger:e});const i=await this.#s.open({defaultFeature:t},!0);this.#o.event("pro_showcase_columns_dialog_close",{defaultFeature:t,currentFeature:i.output})}async openChoosePlanPromoDialog(e){this.#o.event("choose_plan_promo_dialog_open",{trigger:e}),await this.#n.open(void 0,!0),this.#o.event("choose_plan_promo_dialog_close",{})}async openOldProDialog(e){await this.#i.open({trigger:e,nonInteraction:!1})}};b=(0,a.Cg)([(0,p.m6)({setup:"attached",teardown:"detached",selectors:{flags:(0,p.$t)((e=>e.flags)),timestamps:(0,p.$t)((e=>e.timestamps)),account:(0,p.$t)((e=>e.account)),gameHistory:(0,p.$t)((e=>e.gameHistory))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[r.il,s.DialogService,d.ProShowcaseDialogService,c.ProShowcaseColumnsDialogService,o.ChoosePlanPromoDialogService,l.f,m.z,v.j0])],b)},7892:(e,t,i)=>{i.d(t,{p:()=>m});var a=i(15215),s=i("aurelia-event-aggregator"),n=i("aurelia-framework"),r=i(83802),o=i(20057),l=i(54995),h=i(38777),c=i(72208),d=i(56705),u=i(45953);let m=class{#u;#m;#p;#g;#y;#v;#b;#f;constructor(e,t,i,a,s,n){this.#u=e,this.#m=t,this.#p=i,this.#g=a,this.#y=s,this.#v=n}attached(){this.#f=(new h.Vd).push(this.#v.onVisibleTrainerChanged((e=>{this.#b=e,this.#w()}))).push(this.#u.onNewTrainer((e=>{const t=e.getMetadata(r.vO);this.#b=t.info,this.#w()}))).push(this.#p.onLocaleChanged((()=>this.#w())))}detached(){this.#f?.dispose(),this.#f=null}cheatBlueprintInstructionsReadChanged(){this.#w()}gamePreferencesChanged(){this.#w()}pinnedModsChanged(){this.#w("pinnedMods")}modTimersChanged(){this.#w()}#w(e){const t={};if(!this.#b)return;const i=(0,d.o)(this.#b.blueprint),a=i&&this.#y.enabledForGame(this.#b.gameId),s=this.gamePreferences[this.#b.gameId]?.saveCheats,n=this.gamePreferences[this.#b.gameId]?.customHotkeys;this.#b.blueprint.cheats.forEach((e=>{const r=i&&this.#y.cheatSupportsActivateOnLoad(e),o=this.modTimers?.[this.#b.gameId]?.[e.uuid];t[e.uuid]={pinned:this.pinnedMods?.[this.#b.gameId]?.some((t=>t?.uuid===e?.uuid))||!1,pinnedOrder:this.pinnedMods?.[this.#b.gameId]?.findIndex((t=>t?.uuid===e?.uuid))??-1,instructionsRead:this.#g.areInstructionsRead(e.uuid,e.instructions??""),saveCheats:{enabled:r&&a,supported:r,value:r&&a&&s?s.trainerState[e.target]:void 0},hotkeys:e.hotkeys.map(((t,i)=>n?n[e.uuid]?.[i]??t:t)),timer:o?{timestamp:o.timestamp,duration:o.duration,type:o.type,start:o.start,end:o.end}:void 0}})),this.cheatStates=t,this.#m.publish("cheat-states",{gameId:this.#b.gameId,states:t,dep:e})}onCheatStatesChanged(e){return this.#m.subscribe("cheat-states",e)}};m=(0,a.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{cheatBlueprintInstructionsRead:(0,l.$t)((e=>e.cheatBlueprintInstructionsRead)),gamePreferences:(0,l.$t)((e=>e.gamePreferences)),pinnedMods:(0,l.$t)((e=>e.pinnedMods)),modTimers:(0,l.$t)((e=>e.modTimers))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[r.jR,s.EventAggregator,o.F2,c.u,d.Q,u.m])],m)},19648:(e,t,i)=>{i.d(t,{Z:()=>w});var a=i(15215),s=i(79896),n=i(16928),r=i("aurelia-event-aggregator"),o=i("aurelia-framework"),l=i(96610),h=i(19072),c=i("services/bugsnag/index"),d=i(54995),u=i(48335),m=i(26700),p=i(62679),g=i(62914);const y=(0,l.getLogger)("video-library"),v="_thumbnail.webp",b=[".mp4",".mkv"];function f(e){return b.some((t=>t.toLowerCase()===e.toLowerCase()))}let w=class{#S;#P;#m;#_;#C;#o;constructor(e,t,i){this.#S=null,this.#C=null,this.#_=e,this.#m=t,this.#o=i,this.#P=(0,u.s)((()=>{this.#m.publish("EVENT_VIDEOS_CHANGED")}),500)}attached(){this.#D()}detached(){this.#T()}enableCaptureChanged(e,t){!1===e&&!0===t?this.#T():!0===e&&!1===t&&this.#D()}async#D(){try{if(!this.enableCapture)return;const e=await this.#V();this.#T();const t=s.watch(e,{recursive:!0},this.#E.bind(this));this.#S=t,y.info("Started watching WeMod videos directory")}catch(e){this.#L(e)}}#T(){this.#S?.close(),this.#S=null,y.info("Stopped watching WeMod videos directory")}#E(e,t){t&&!t.includes(v)&&t&&f(n.extname(t))&&(y.info(`Video directory change detected: ${e} - ${t}`),this.#P(),this.synchronizeThumbnails(t))}onVideosChanged(e){return this.#m.subscribe("EVENT_VIDEOS_CHANGED",e)}async showVideoFileLocation(e){try{this.#o.event("show_video_location",{},g.Io),await this.#_.showFileLocation(await this.#k(e))}catch(e){this.#L(e)}}async openCaptureFolder(e,t){const i=await this.#V(t);try{await s.promises.mkdir(i,{recursive:!0}),this.#o.event("capture_folder_open",{trigger:e},g.Io),await this.#_.openFilePath(i)}catch(e){this.#L(e)}}async getAllVideos(){try{const e=await this.#V(),t=(await s.promises.readdir(e,{withFileTypes:!0})).filter((e=>e.isDirectory()&&e.name!==p.og)),i=[];for(const a of t){const t=n.join(e,a.name),r=await s.promises.readdir(t,{withFileTypes:!0}),o=[];let l=0;for(const i of r){l++;try{if(i.isFile()&&f(n.extname(i.name))){const r=n.join(t,i.name),h=n.relative(e,r).replace(/\\/g,"/"),c=await s.promises.stat(r),d=this.getVideoUuid(a.name,c,l),u=await this.getThumbnailPath(a.name,i.name);let m="";try{m=`data:image/webp;base64,${(await s.promises.readFile(u)).toString("base64")}`}catch(e){}const p={id:d,path:h,filename:i.name,createdAt:c.birthtime,thumbnailDataUrl:m,titleName:a.name};o.push(p)}}catch(e){y.error(`Error processing video file ${i.name}: ${e}`)}}i.push({titleName:a.name,folderPath:t,videos:o})}return i}catch(e){return this.#L(e),[]}}async synchronizeThumbnails(e){try{const t=await this.#V(),i=await this.#V(p.og);if(e)return this.#A(e,t);const a=new Set,s=await this.#F(t,a);await this.#I(s);const n=await this.#$(i,a);return y.info(`Thumbnail synchronization complete: ${s.length} generated, ${n} deleted`),s.length>0||n>0}catch(e){return!1}}async#A(e,t){const i=n.normalize(e).split(n.sep);if(2!==i.length)return!1;const[a,r]=i,o=await this.getThumbnailPath(a,r),l=n.join(t,a,r);return await(0,m.h)(l)?!await(0,m.h)(o)&&(await(0,p.I6)(this.getVideosDirectory(),l),!0):!!await(0,m.h)(o)&&(await s.promises.unlink(o),y.info(`Deleted orphaned thumbnail for missing video: ${o}`),!0)}async#F(e,t){const i=(await s.promises.readdir(e,{withFileTypes:!0})).filter((e=>e.isDirectory()&&e.name!==p.og)),a=[];for(const r of i){const i=n.join(e,r.name),o=await s.promises.readdir(i,{withFileTypes:!0});for(const e of o)if(e.isFile()&&f(n.extname(e.name))){const o=e.name,l=`${r.name}_${o}`;t.add(l);const h=await this.getThumbnailPath(r.name,o);try{await s.promises.access(h,s.constants.F_OK)}catch(e){const t=n.join(i,o);a.push((0,p.I6)(this.getVideosDirectory(),t))}}}return a}async#I(e){for(let t=0;t<e.length;t+=3){const i=e.slice(t,t+3);await Promise.all(i)}}async#$(e,t){const i=await s.promises.readdir(e),a=[];for(const r of i)if(r.endsWith(v)){const i=r.substring(0,r.length-15);t.has(i)||a.push(s.promises.unlink(n.join(e,r)).catch((e=>y.error(`Failed to delete orphaned thumbnail: ${e}`))))}return await Promise.all(a),a.length}getVideosDirectory(){return this.#C||(this.#C=n.join(this.#_.info.osHomeDir,"Videos","WeMod")),this.#C}getVideoUuid(e,t,i){return`${e}_${t.birthtimeMs.toString().replaceAll(".","")}${i?`_${i}`:""}`}async getThumbnailPath(e,t){const i=await this.#V(p.og),a=`${e}_${t}`;return n.join(i,`${a}${v}`)}renameVideoFile(e,t){const i=t.trim();if(!t||""===i)return{success:!1,errorMessageKey:"my_videos.invalid_filename"};try{const t=this.getVideosDirectory(),a=n.join(t,e.path),r=n.dirname(a),o=n.extname(e.filename),l=i.endsWith(o)?i:`${i}${o}`,h=n.join(r,l);if(l===e.filename)return{success:!0};if(s.existsSync(h))return{success:!1,errorMessageKey:"my_videos.file_already_exists"};s.renameSync(a,h);const c={...e,filename:l,path:n.normalize(n.relative(t,h))};return this.#P(),{success:!0,updatedVideo:c}}catch(e){return y.error(`Failed to rename video: ${e}`),{success:!1,errorMessageKey:"my_videos.file_rename_failed"}}}async#V(e){return await(0,m.B)(this.getVideosDirectory(),e)}async#k(e){return n.join(this.getVideosDirectory(),e.path)}#L(e){(0,c.report)(e),y.error(e.toString())}dispose(){this.#T()}};(0,a.Cg)([o.observable,(0,a.Sn)("design:type",Object)],w.prototype,"enableCapture",void 0),w=(0,a.Cg)([(0,d.m6)({setup:"attached",teardown:"detached",selectors:{enableCapture:(0,d.$t)((e=>e.settings.enableCapture))}}),(0,o.autoinject)(),(0,a.Sn)("design:paramtypes",[h.s,r.EventAggregator,g.j0])],w)},33700:(e,t,i)=>{i.d(t,{q:()=>h});var a=i(15215),s=i("aurelia-framework"),n=i(24008),r=i(20057),o=i(54995),l=i(70236);let h=class{#p;constructor(e){this.#p=e}attached(){}detached(){}get count(){return this.games?Object.values(this.games).filter((e=>(0,l.Lt)(e.flags,n.rT.Available))).length:0}get formattedCount(){return this.#p.formatNumber(this.count)}};(0,a.Cg)([(0,s.computedFrom)("games"),(0,a.Sn)("design:type",Number),(0,a.Sn)("design:paramtypes",[])],h.prototype,"count",null),(0,a.Cg)([(0,s.computedFrom)("count"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],h.prototype,"formattedCount",null),h=(0,a.Cg)([(0,o.m6)({setup:"attached",teardown:"detached",selectors:{games:(0,o.$t)((e=>e.catalog?.games))}}),(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[r.F2])],h)},40930:(e,t,i)=>{i.d(t,{EW:()=>r,FE:()=>o,XO:()=>l,px:()=>n,rf:()=>s,xH:()=>a});const a=7200,s=0,n=0,r=1e4,o=1e4;class l{constructor(e){this.wasEndedByTimeLimit=e}}},43570:(e,t,i)=>{i.d(t,{A:()=>a,L:()=>m});var a,s=i(15215),n=i(16928),r=i("aurelia-framework"),o=i(20770),l=i(83802),h=i(54995),c=i(48881),d=i(38777);!function(e){e.Execute="execute",e.Enable="enable",e.Disable="disable",e.Increase="increase",e.Decrease="decrease",e.Limit="limit",e.Error="error"}(a||(a={}));const u=["Percussive","Boopie","Melodic","Retro"];let m=class{#e;#u;#x;#j;constructor(e,t){this.availablePacks=u,this.#x=null,this.#e=e,this.#u=t}attached(){this.selectedPack||this.setSoundPack(u[0],"sound_player",!0),this.#j=this.#u.onNewTrainer((e=>{this.preload(),e.onValueSetError((()=>this.play(a.Error)))}))}detached(){this.#j?.dispose(),this.#j=null}selectedPackChanged(){this.#x=null}async setSoundPack(e,t,i){this.availablePacks.includes(e)&&(this.#x=null,await this.#e.dispatch(c.Kc,{cheatSoundPack:e},t,i))}async setVolume(e,t){e>=0&&e<=100&&(await this.#e.dispatch(c.Kc,{cheatSoundVolume:e},t),this.#x?.setVolume(e))}async preload(){if(null!==this.#x)return!0;const e=this.selectedPack,t=await p.load(e,Number(this.volume));return e===this.selectedPack&&(this.#x=t,!0)}async play(e){this.enabled&&(null!==this.#x||await this.preload())&&await(this.#x?.play(e).catch((()=>null)))}async playAtFixedVolume(e,t){(null!==this.#x||await this.preload())&&this.#x?.playAtFixedVolume(e,t)}};m=(0,s.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{enabled:(0,h.$t)((e=>e.settings?.cheatSounds)),selectedPack:(0,h.$t)((e=>e.settings?.cheatSoundPack)),volume:(0,h.$t)((e=>e.settings?.cheatSoundVolume))}}),(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[o.il,l.jR])],m);class p{#O;constructor(e){this.#O=e}play(e){const t=this.#O[e];return t.currentTime=0,t.play()}playAtFixedVolume(e,t){const i=this.#O[e],a=i.volume;return i.currentTime=0,i.volume=t/100,i.addEventListener("ended",(()=>{i.volume=a}),{once:!0}),i.play()}setVolume(e){"number"==typeof e&&Object.values(this.#O).forEach((t=>p.#R(t,e)))}static async load(e,t){const i={};return await Promise.all(Object.values(a).map((async a=>{i[a]=await this.#M(function(e,t){return n.normalize(`./static/audio/cheats/${e}/${t}.mp3`)}(e,a),t)}))),new p(i)}static#M(e,t){return new Promise(((i,a)=>{const s=new Audio(e),n=(new d.Vd).pushEventListener(s,"error",(e=>{n.dispose(),a(e)})).pushEventListener(s,"canplaythrough",(()=>{n.dispose(),"number"==typeof t&&p.#R(s,t),i(s)}))}))}static#R(e,t){e.volume=t/100}}},45953:(e,t,i)=>{i.d(t,{m:()=>l});var a=i(15215),s=i("aurelia-framework"),n=i(27958),r=i(83802),o=i(38777);let l=class{#m;#W;constructor(e,t){this.#m=new o._M,e.onNewTrainer(this.#H.bind(this)),e.onTrainerEnded(this.#N.bind(this)),this.#W=t}attached(){}detached(){this.#m.dispose()}setVisibleTrainer(e){this.visibleTrainer=e,this.#m.publish("visible",this.visibleTrainer),this.runningTrainer||(this.displayTrainer=e,this.#m.publish("display",this.displayTrainer))}#H(e){const t=e.getMetadata(r.vO).info;this.runningTrainer={info:t,instance:e},this.#m.publish("running",this.runningTrainer),this.displayTrainer=t,this.#m.publish("display",this.displayTrainer)}#N(){this.runningTrainer=null,this.displayTrainer=this.visibleTrainer,this.#m.publish("display",this.displayTrainer),this.#m.publish("running",null)}onDisplayTrainerChanged(e){return this.#m.subscribe("display",e)}onVisibleTrainerChanged(e){return this.#m.subscribe("visible",e)}onRunningTrainerChanged(e){return this.#m.subscribe("running",e)}async cancelVisibleTrainer(){await(0,o.Wn)(),"title"===this.#W.router.currentInstruction.config.name&&this.#W.router.currentInstruction.queryParams.trainerId?(await(0,o.Wn)(500),this.visibleTrainer?.id||this.setVisibleTrainer(null)):this.setVisibleTrainer(null)}};l=(0,a.Cg)([(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[r.jR,n.L])],l)},56705:(e,t,i)=>{i.d(t,{Q:()=>m,o:()=>p});var a=i(15215),s=i("aurelia-event-aggregator"),n=i("aurelia-framework"),r=i(20770),o=i(83802),l=i(21795),h=i(54995),c=i(70236),d=i(48881),u=i(38777);let m=class{#u;#e;#B;#f;#m;constructor(e,t,i){this.#m=new u._M,this.#u=e,this.#e=t,this.#B=i}attached(){this.#f=(new u.Vd).push(this.#u.onTrainerActivated((e=>this.#U(e)))),this.#G()}detached(){this.#f?.dispose(),this.#f=null}#U(e){const t=e.getMetadata(o.vO).info,i=p(t.blueprint),a=this.enabledForGame(t.gameId);if(!i)return;const s=this.gamePreferences[t.gameId]?.saveCheats?.trainerState;a&&s&&t.blueprint.cheats.forEach((async i=>{if(!(t?.brokenCheatUuids??[]).includes(i.uuid)&&this.cheatSupportsActivateOnLoad(i)){const t=s[i.target];void 0!==t&&await e.setValue(i.target,t,5)}})),e.onValueSet((e=>{this.saveValue(t.gameId,e.name,e.value)}))}async saveValue(e,t,i){this.#m.publish("value-saved",{gameId:e,target:t,value:i}),await this.#e.dispatch(d.iC,e,t,i)}onValueSaved(e){return this.#m.subscribe("value-saved",e)}async enable(e,t,i){i??=this.#z(e),await this.#e.dispatch(d.Vz,e,!0,i),this.#B.publish(new l.Ij(t,e,!0))}#z(e){const t=this.#u.trainer;return t&&t.getMetadata(o.vO).info.gameId===e?Object.fromEntries(t.values.entries()):null}async disable(e,t){await this.#e.dispatch(d.Vz,e,!1),this.#B.publish(new l.Ij(t,e,!1))}enableSaveCheatsByDefaultChanged(e,t){!1===e&&!0===t&&this.#e.dispatch(d.Gi,!0)}cheatSupportsActivateOnLoad(e){return(0,c.Lt)(e?.flags,1)}#K(e){return this.gamePreferences&&this.gamePreferences[e]?.saveCheats?.enabled}enabledForGame(e){return!!this.canUse&&(this.#K(e)??this.enableSaveCheatsByDefault)}enabledByDefault(){return this.enableSaveCheatsByDefault}#G(){this.canUse||(this.gamePreferences&&Object.values(this.gamePreferences).some((e=>{if("boolean"==typeof e.saveCheats?.enabled)return!0;const t=e.saveCheats?.trainerState;return t&&Object.keys(t).length>0}))||this.enableSaveCheatsByDefault)&&this.#e.dispatch(d.Gi,!1)}accountChanged(){this.#G()}get canUse(){return this.account&&!!this.account.subscription}};function p(e){return(0,c.Lt)(e?.flags,1)}(0,a.Cg)([(0,n.computedFrom)("account.subscription"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],m.prototype,"canUse",null),m=(0,a.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{gamePreferences:(0,h.$t)((e=>e.gamePreferences)),enableSaveCheatsByDefault:(0,h.$t)((e=>e.settings?.enableSaveCheatsByDefault)),account:(0,h.$t)((e=>e.account))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[o.jR,r.il,s.EventAggregator])],m)},58534:(e,t,i)=>{i.d(t,{A:()=>a});const a=""},67064:(e,t,i)=>{i.d(t,{l:()=>r});var a=i(15215),s=i("aurelia-framework"),n=i(38777);let r=class{constructor(){this.messages=[]}toast(e){return this.messages.push(e),e.persist||this.removeAfterDelay(e),e}remove(e){const t=this.messages.findIndex((t=>t===e));-1!==t&&(this.messages.splice(t,1),e.onremove?.())}removeAfterDelay(e,t=!1){e.persist||(this.cancelRemoval(e),e.removeTimeout=(0,n.Ix)((()=>this.remove(e)),t?2500:5e3))}cancelRemoval(e){e.removeTimeout&&(e.removeTimeout?.dispose(),e.removeTimeout=null)}update(e,t){const i=this.messages.findIndex((t=>t===e));if(-1!==i){const e=[...this.messages];e[i]=t,this.messages=e}}};r=(0,a.Cg)([(0,s.singleton)()],r)},96111:(e,t,i)=>{i.d(t,{Y:()=>C});var a=i(15215),s=i(16928),n=i("aurelia-framework"),r=i(20770),o=i(55648),l=i(81810),h=i(83802),c=i("dialogs/time-limit-reached-post-game-dialog"),d=i(60692),u=i(19072),m=i(68539),p=i(20057),g=i(54995),y=i(49442),v=i(14046),b=i(48881),f=i(38777),w=i(50643),S=i(62914),P=i(43570),_=i(40930);let C=class{#Y;#X;#q;#e;#u;#r;#Q;#_;#o;#Z;#p;constructor(e,t,i,a,s,n,r,o){this.hasSeenTimeWarning=!1,this.#e=e,this.#u=t,this.#r=i,this.#Q=a,this.#_=s,this.#o=n,this.#Z=r,this.#p=o}attached(){this.e39Variant=this.#r.assignments.get(d.n.E39)??null,this.#X=this.#r.onVariantChanged((e=>this.#J(e))),this.#Y=this.#u.onTrainerActivated((e=>this.#U(e))),this.#q=(0,f.SO)((()=>this.#ee()),_.EW),this.#te().then((()=>{this.#ee()}))}detached(){this.#X?.dispose(),this.#X=null,this.#Y?.dispose(),this.#Y=null,this.#q?.dispose(),this.#q=null}#J(e){e.key===d.n.E39&&(this.e39Variant=e.variant)}async#U(e){if(!this.isEnabled||e.isEnding())return;let t=this.dailyPlayLimitSeconds-this.secondsPlayedToday;if(t>0){let i=new Date;const a=setInterval((async()=>{if(!this.isEnabled)return;const e=await this.#ie(i);i=new Date,t-=e,t<=0&&(this.#u.trainer?.addMetadata(new _.XO(!0)),this.#u.endTrainer())}),_.FE);e.onEnded((()=>{this.isEnabled&&this.#ie(i),clearInterval(a)}))}}async#ie(e){const t=new Date,i=Math.round((0,v.bu)(t,e)/1e3);return i>0?(await this.#e.dispatch(b.Ew,"secondsPlayedToday",i),i):0}get isOverDailyLimit(){return this.secondsPlayedToday>=this.dailyPlayLimitSeconds}secondsPlayedTodayChanged(e,t){const i=(t||0)<this.dailyPlayLimitSeconds,a=e<this.dailyPlayLimitSeconds;i&&!a&&this.#o.event("time_limit_exceeded",{type:"daily",limitHours:this.dailyPlayLimitHours},S.Io),!i&&a&&this.#o.event("time_limit_reset",{type:"daily",limitHours:this.dailyPlayLimitHours},S.Io);const s=this.dailyPlayLimitSeconds-e,n=_.FE/1e3;if(s>0&&s<=300+n&&!this.hasSeenTimeWarning){const e=this.#p.getValue("time_limit_enforcer.five_minutes_left"),t=this.#p.getValue("time_limit_enforcer.upgrade_to_pro_to_use_mods_beyond_your_daily_limit"),i=this.#p.getValue("time_limit_enforcer.upgrade_to_pro");this.#ae(e,t,i,"five_minutes_remaining_notification","five_minutes_remaining_notification_show").catch(y.Y),this.hasSeenTimeWarning=!0}}async#ae(e,t,i,a,n){const r=s.join(this.#_.info.paths.assets,`time-limit-toast-icon-${this.dailyPlayLimitHours}-hour${this.dailyPlayLimitHours>1?"s":""}.png`),o=`wemod://pro?trigger=${a}`,l=new w.c(e).addText(this.#p.getValue(t)).setActivationType("protocol").setLaunchString(o).addImage(r,"appLogoOverride").setScenario("alarm").setAudio({silent:!0}).addAction({content:i,arguments:o,activationType:"protocol"}).toXml();this.#Z.playAtFixedVolume(P.A.Error,75),await this.#_.showToast(l)&&this.#o.event(n,{type:"daily",limitHours:this.dailyPlayLimitHours},S.Io)}async triggerPostTrainerEvents(){const e=this.isEnabled&&this.isOverDailyLimit;if(e){const e=this.#p.getValue("time_limit_enforcer.times_up"),t=this.e39Variant?"time_limit_enforcer.upgrade_to_pro_to_use_mods_beyond_your_daily_limit":`time_limit_enforcer.upgrade_to_pro_to_play_beyond_your_daily_${this.dailyPlayLimitHours}_hour_limit`,i=this.#p.getValue("time_limit_enforcer.upgrade_to_pro");this.#_.flashWindow().catch(y.Y),this.#ae(e,t,i,"time_limit_exceeded_notification","time_limit_exceeded_notification_show").catch(y.Y),await this.#Q.open({perGame:!1,limitHours:this.dailyPlayLimitHours})}return e}async#te(){this.lastResetDailyPlayLimit||await this.resetTimeLimit()}async#ee(){if(!this.isEnabled)return;const e=new Date,t=(0,o.A)().setHours(_.rf,_.px),i=(0,v.dS)(e,t),a=this.secondsPlayedToday>0,s=(0,l.A)(e,new Date(this.lastResetDailyPlayLimit));i&&a&&!s&&this.resetTimeLimit()}async resetTimeLimit(){this.hasSeenTimeWarning=!1,await this.#e.dispatch(b.TU,"secondsPlayedToday",0),await this.#e.dispatch(b.vk,"lastResetDailyPlayLimit")}get isInVariant(){return!!this.e39Variant}get isEnabledForUser(){return!(!1===this.account.features?.timeLimit?.enabled)}get isEnabled(){return(this.isEnabledForUser||this.isInVariant)&&!this.account.subscription}get canUseInAppControls(){if(this.account.subscription)return!0;let e=!!this.account.features?.modControls?.enabled;return this.e39Variant&&(e=[2,3].includes(this.e39Variant)),e}get dailyPlayLimitSeconds(){return this.e39Variant?7200:_.xH}get dailyPlayLimitHours(){return Math.floor(this.dailyPlayLimitSeconds/60/60)}async triggerForegroundModTimeLimitExperiment(){this.account.subscription||(this.e39Variant=await this.#r.trigger(d.n.E39).catch(y.Y)||null)}};(0,a.Cg)([n.observable,(0,a.Sn)("design:type",Object)],C.prototype,"e39Variant",void 0),(0,a.Cg)([(0,n.computedFrom)("secondsPlayedToday"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],C.prototype,"isOverDailyLimit",null),(0,a.Cg)([(0,n.computedFrom)("e39Variant"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],C.prototype,"isInVariant",null),(0,a.Cg)([(0,n.computedFrom)("account"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],C.prototype,"isEnabledForUser",null),(0,a.Cg)([(0,n.computedFrom)("isEnabledForUser","isInVariant","account.subscription"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],C.prototype,"isEnabled",null),(0,a.Cg)([(0,n.computedFrom)("account.subscription","account.features.modControls","e39Variant"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],C.prototype,"canUseInAppControls",null),(0,a.Cg)([(0,n.computedFrom)("e39Variant"),(0,a.Sn)("design:type",Number),(0,a.Sn)("design:paramtypes",[])],C.prototype,"dailyPlayLimitSeconds",null),(0,a.Cg)([(0,n.computedFrom)("dailyPlayLimitSeconds"),(0,a.Sn)("design:type",Number),(0,a.Sn)("design:paramtypes",[])],C.prototype,"dailyPlayLimitHours",null),C=(0,a.Cg)([(0,g.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,g.$t)((e=>e.account)),secondsPlayedToday:(0,g.$t)((e=>e.counters?.secondsPlayedToday)),lastResetDailyPlayLimit:(0,g.$t)((e=>e.timestamps?.lastResetDailyPlayLimit))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[r.il,h.jR,m.z,c.TimeLimitReachedPostGameDialogService,u.s,S.j0,P.L,p.F2])],C)}}]);
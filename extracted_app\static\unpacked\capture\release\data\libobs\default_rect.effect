#include "color.effect"

uniform float4x4 ViewProj;
uniform texture_rect image;

sampler_state def_sampler {
	Filter   = Linear;
	AddressU = Clamp;
	AddressV = Clamp;
};

struct VertInOut {
	float4 pos : POSITION;
	float2 uv  : TEXCOORD0;
};

VertInOut VSDefault(VertInOut vert_in)
{
	VertInOut vert_out;
	vert_out.pos = mul(float4(vert_in.pos.xyz, 1.0), ViewProj);
	vert_out.uv  = vert_in.uv;
	return vert_out;
}

float4 PSDrawBare(VertInOut vert_in) : TARGET
{
	return image.Sample(def_sampler, vert_in.uv);
}

float4 PSDrawD65P3(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = srgb_nonlinear_to_linear(rgba.rgb);
	rgba.rgb = d65p3_to_rec709(rgba.rgb);
	return rgba;
}

float4 PSDrawOpaque(VertInOut vert_in) : TARGET
{
	return float4(image.Sample(def_sampler, vert_in.uv).rgb, 1.0);
}

float4 PSDrawSrgbDecompress(VertInOut vert_in) : TARGET
{
	float4 rgba = image.Sample(def_sampler, vert_in.uv);
	rgba.rgb = srgb_nonlinear_to_linear(rgba.rgb);
	return rgba;
}

technique Draw
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawBare(vert_in);
	}
}

technique DrawD65P3
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawD65P3(vert_in);
	}
}

technique DrawOpaque
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawOpaque(vert_in);
	}
}

technique DrawSrgbDecompress
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawSrgbDecompress(vert_in);
	}
}

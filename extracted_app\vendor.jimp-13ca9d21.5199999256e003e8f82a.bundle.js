"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1226],{4696:t=>{var e=t.exports=function(t){this._buffer=t,this._reads=[]};e.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e})},e.prototype.process=function(){for(;this._reads.length>0&&this._buffer.length;){var t=this._reads[0];if(!this._buffer.length||!(this._buffer.length>=t.length||t.allowLess))break;this._reads.shift();var e=this._buffer;this._buffer=e.slice(t.length),t.func.call(this,e.slice(0,t.length))}return this._reads.length>0?new Error("There are some read requests waitng on finished stream"):this._buffer.length>0?new Error("unrecognised content at end of stream"):void 0}},7988:(t,e,r)=>{var i=r(60940),n=r(30251);e.read=function(t,e){return i(t,e||{})},e.write=function(t,e){return n(t,e)}},11811:t=>{var e=[];!function(){for(var t=0;t<256;t++){for(var r=t,i=0;i<8;i++)1&r?r=3988292384^r>>>1:r>>>=1;e[t]=r}}();var r=t.exports=function(){this._crc=-1};r.prototype.write=function(t){for(var r=0;r<t.length;r++)this._crc=e[255&(this._crc^t[r])]^this._crc>>>8;return!0},r.prototype.crc32=function(){return~this._crc},r.crc32=function(t){for(var r=-1,i=0;i<t.length;i++)r=e[255&(r^t[i])]^r>>>8;return~r}},17316:(t,e,r)=>{var i=r(39023),n=r(69976),a=r(44645),s=t.exports=function(t){n.call(this);var e=[],r=this;this._filter=new a(t,{read:this.read.bind(this),write:function(t){e.push(t)},complete:function(){r.emit("complete",Buffer.concat(e))}}),this._filter.start()};i.inherits(s,n)},17709:(t,e,r)=>{var i=r(84398),n={0:function(t,e,r,i,n){for(var a=0;a<r;a++)i[n+a]=t[e+a]},1:function(t,e,r,i,n,a){for(var s=0;s<r;s++){var h=s>=a?t[e+s-a]:0,o=t[e+s]-h;i[n+s]=o}},2:function(t,e,r,i,n){for(var a=0;a<r;a++){var s=e>0?t[e+a-r]:0,h=t[e+a]-s;i[n+a]=h}},3:function(t,e,r,i,n,a){for(var s=0;s<r;s++){var h=s>=a?t[e+s-a]:0,o=e>0?t[e+s-r]:0,f=t[e+s]-(h+o>>1);i[n+s]=f}},4:function(t,e,r,n,a,s){for(var h=0;h<r;h++){var o=h>=s?t[e+h-s]:0,f=e>0?t[e+h-r]:0,l=e>0&&h>=s?t[e+h-(r+s)]:0,p=t[e+h]-i(o,f,l);n[a+h]=p}}},a={0:function(t,e,r){for(var i=0,n=e+r,a=e;a<n;a++)i+=Math.abs(t[a]);return i},1:function(t,e,r,i){for(var n=0,a=0;a<r;a++){var s=a>=i?t[e+a-i]:0,h=t[e+a]-s;n+=Math.abs(h)}return n},2:function(t,e,r){for(var i=0,n=e+r,a=e;a<n;a++){var s=e>0?t[a-r]:0,h=t[a]-s;i+=Math.abs(h)}return i},3:function(t,e,r,i){for(var n=0,a=0;a<r;a++){var s=a>=i?t[e+a-i]:0,h=e>0?t[e+a-r]:0,o=t[e+a]-(s+h>>1);n+=Math.abs(o)}return n},4:function(t,e,r,n){for(var a=0,s=0;s<r;s++){var h=s>=n?t[e+s-n]:0,o=e>0?t[e+s-r]:0,f=e>0&&s>=n?t[e+s-(r+n)]:0,l=t[e+s]-i(h,o,f);a+=Math.abs(l)}return a}};t.exports=function(t,e,r,i,s){var h;if("filterType"in i&&-1!==i.filterType){if("number"!=typeof i.filterType)throw new Error("unrecognised filter types");h=[i.filterType]}else h=[0,1,2,3,4];16===i.bitDepth&&(s*=2);for(var o=e*s,f=0,l=0,p=new Buffer((o+1)*r),u=h[0],d=0;d<r;d++){if(h.length>1)for(var c=1/0,_=0;_<h.length;_++){var b=a[h[_]](t,l,o,s);b<c&&(u=h[_],c=b)}p[f]=u,f++,n[u](t,l,o,p,f,s),f+=o,l+=o}return p}},21613:(t,e,r)=>{var i=r(4696),n=r(44645);e.process=function(t,e){var r=[],a=new i(t);return new n(e,{read:a.read.bind(a),write:function(t){r.push(t)},complete:function(){}}).start(),a.process(),Buffer.concat(r)}},24052:(t,e)=>{var r=[{x:[0],y:[0]},{x:[4],y:[0]},{x:[0,4],y:[4]},{x:[2,6],y:[0,4]},{x:[0,2,4,6],y:[2,6]},{x:[1,3,5,7],y:[0,2,4,6]},{x:[0,1,2,3,4,5,6,7],y:[1,3,5,7]}];e.getImagePasses=function(t,e){for(var i=[],n=t%8,a=e%8,s=(t-n)/8,h=(e-a)/8,o=0;o<r.length;o++){for(var f=r[o],l=s*f.x.length,p=h*f.y.length,u=0;u<f.x.length&&f.x[u]<n;u++)l++;for(u=0;u<f.y.length&&f.y[u]<a;u++)p++;l>0&&p>0&&i.push({width:l,height:p,index:o})}return i},e.getInterlaceIterator=function(t){return function(e,i,n){var a=e%r[n].x.length,s=(e-a)/r[n].x.length*8+r[n].x[a],h=i%r[n].y.length;return 4*s+((i-h)/r[n].y.length*8+r[n].y[h])*t*4}}},24875:t=>{t.exports=function(t,e){var r=e.depth,i=e.width,n=e.height,a=e.colorType,s=e.transColor,h=e.palette,o=t;return 3===a?function(t,e,r,i,n){for(var a=0,s=0;s<i;s++)for(var h=0;h<r;h++){var o=n[t[a]];if(!o)throw new Error("index "+t[a]+" not in palette");for(var f=0;f<4;f++)e[a+f]=o[f];a+=4}}(t,o,i,n,h):(s&&function(t,e,r,i,n){for(var a=0,s=0;s<i;s++)for(var h=0;h<r;h++){var o=!1;if(1===n.length?n[0]===t[a]&&(o=!0):n[0]===t[a]&&n[1]===t[a+1]&&n[2]===t[a+2]&&(o=!0),o)for(var f=0;f<4;f++)e[a+f]=0;a+=4}}(t,o,i,n,s),8!==r&&(16===r&&(o=new Buffer(i*n*4)),function(t,e,r,i,n){for(var a=Math.pow(2,n)-1,s=0,h=0;h<i;h++)for(var o=0;o<r;o++){for(var f=0;f<4;f++)e[s+f]=Math.floor(255*t[s+f]/a+.5);s+=4}}(t,o,i,n,r))),o}},30251:(t,e,r)=>{var i=!0,n=r(43106);n.deflateSync||(i=!1);var a=r(55148),s=r(31579);t.exports=function(t,e){if(!i)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");var r=new s(e||{}),h=[];h.push(new Buffer(a.PNG_SIGNATURE)),h.push(r.packIHDR(t.width,t.height)),t.gamma&&h.push(r.packGAMA(t.gamma));var o=r.filterData(t.data,t.width,t.height),f=n.deflateSync(o,r.getDeflateOptions());if(o=null,!f||!f.length)throw new Error("bad png - invalid compressed data response");return h.push(r.packIDAT(f)),h.push(r.packIEND()),Buffer.concat(h)}},31579:(t,e,r)=>{var i=r(55148),n=r(11811),a=r(48144),s=r(17709),h=r(43106),o=t.exports=function(t){if(this._options=t,t.deflateChunkSize=t.deflateChunkSize||32768,t.deflateLevel=null!=t.deflateLevel?t.deflateLevel:9,t.deflateStrategy=null!=t.deflateStrategy?t.deflateStrategy:3,t.inputHasAlpha=null==t.inputHasAlpha||t.inputHasAlpha,t.deflateFactory=t.deflateFactory||h.createDeflate,t.bitDepth=t.bitDepth||8,t.colorType="number"==typeof t.colorType?t.colorType:i.COLORTYPE_COLOR_ALPHA,t.inputColorType="number"==typeof t.inputColorType?t.inputColorType:i.COLORTYPE_COLOR_ALPHA,-1===[i.COLORTYPE_GRAYSCALE,i.COLORTYPE_COLOR,i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(t.colorType))throw new Error("option color type:"+t.colorType+" is not supported at present");if(-1===[i.COLORTYPE_GRAYSCALE,i.COLORTYPE_COLOR,i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(t.inputColorType))throw new Error("option input color type:"+t.inputColorType+" is not supported at present");if(8!==t.bitDepth&&16!==t.bitDepth)throw new Error("option bit depth:"+t.bitDepth+" is not supported at present")};o.prototype.getDeflateOptions=function(){return{chunkSize:this._options.deflateChunkSize,level:this._options.deflateLevel,strategy:this._options.deflateStrategy}},o.prototype.createDeflate=function(){return this._options.deflateFactory(this.getDeflateOptions())},o.prototype.filterData=function(t,e,r){var n=a(t,e,r,this._options),h=i.COLORTYPE_TO_BPP_MAP[this._options.colorType];return s(n,e,r,this._options,h)},o.prototype._packChunk=function(t,e){var r=e?e.length:0,i=new Buffer(r+12);return i.writeUInt32BE(r,0),i.writeUInt32BE(t,4),e&&e.copy(i,8),i.writeInt32BE(n.crc32(i.slice(4,i.length-4)),i.length-4),i},o.prototype.packGAMA=function(t){var e=new Buffer(4);return e.writeUInt32BE(Math.floor(t*i.GAMMA_DIVISION),0),this._packChunk(i.TYPE_gAMA,e)},o.prototype.packIHDR=function(t,e){var r=new Buffer(13);return r.writeUInt32BE(t,0),r.writeUInt32BE(e,4),r[8]=this._options.bitDepth,r[9]=this._options.colorType,r[10]=0,r[11]=0,r[12]=0,this._packChunk(i.TYPE_IHDR,r)},o.prototype.packIDAT=function(t){return this._packChunk(i.TYPE_IDAT,t)},o.prototype.packIEND=function(){return this._packChunk(i.TYPE_IEND,null)}},41022:(t,e,r)=>{var i=r(39023),n=r(2203),a=r(55148),s=r(31579),h=t.exports=function(t){n.call(this);var e=t||{};this._packer=new s(e),this._deflate=this._packer.createDeflate(),this.readable=!0};i.inherits(h,n),h.prototype.pack=function(t,e,r,i){this.emit("data",new Buffer(a.PNG_SIGNATURE)),this.emit("data",this._packer.packIHDR(e,r)),i&&this.emit("data",this._packer.packGAMA(i));var n=this._packer.filterData(t,e,r);this._deflate.on("error",this.emit.bind(this,"error")),this._deflate.on("data",function(t){this.emit("data",this._packer.packIDAT(t))}.bind(this)),this._deflate.on("end",function(){this.emit("data",this._packer.packIEND()),this.emit("end")}.bind(this)),this._deflate.end(n)}},41432:(t,e,r)=>{var i=r(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=i(r(43693)),a=i(r(7713)),s="image/tiff";e.default=function(){return{mime:(0,n.default)({},s,["tiff","tif"]),constants:{MIME_TIFF:s},decoders:(0,n.default)({},s,(function(t){var e=a.default.decode(t),r=e[0];a.default.decodeImages(t,e);var i=a.default.toRGBA8(r);return{data:Buffer.from(i),width:r.t256[0],height:r.t257[0]}})),encoders:(0,n.default)({},s,(function(t){var e=a.default.encodeImage(t.bitmap.data,t.bitmap.width,t.bitmap.height);return Buffer.from(e)}))}},t.exports=e.default},44295:(t,e,r)=>{var i=r(39023),n=r(43106),a=r(69976),s=r(17316),h=r(90762),o=r(78875),f=r(24875),l=t.exports=function(t){a.call(this),this._parser=new h(t,{read:this.read.bind(this),error:this._handleError.bind(this),metadata:this._handleMetaData.bind(this),gamma:this.emit.bind(this,"gamma"),palette:this._handlePalette.bind(this),transColor:this._handleTransColor.bind(this),finished:this._finished.bind(this),inflateData:this._inflateData.bind(this),simpleTransparency:this._simpleTransparency.bind(this),headersFinished:this._headersFinished.bind(this)}),this._options=t,this.writable=!0,this._parser.start()};i.inherits(l,a),l.prototype._handleError=function(t){this.emit("error",t),this.writable=!1,this.destroy(),this._inflate&&this._inflate.destroy&&this._inflate.destroy(),this._filter&&(this._filter.destroy(),this._filter.on("error",(function(){}))),this.errord=!0},l.prototype._inflateData=function(t){if(!this._inflate)if(this._bitmapInfo.interlace)this._inflate=n.createInflate(),this._inflate.on("error",this.emit.bind(this,"error")),this._filter.on("complete",this._complete.bind(this)),this._inflate.pipe(this._filter);else{var e=(1+(this._bitmapInfo.width*this._bitmapInfo.bpp*this._bitmapInfo.depth+7>>3))*this._bitmapInfo.height,r=Math.max(e,n.Z_MIN_CHUNK);this._inflate=n.createInflate({chunkSize:r});var i=e,a=this.emit.bind(this,"error");this._inflate.on("error",(function(t){i&&a(t)})),this._filter.on("complete",this._complete.bind(this));var s=this._filter.write.bind(this._filter);this._inflate.on("data",(function(t){i&&(t.length>i&&(t=t.slice(0,i)),i-=t.length,s(t))})),this._inflate.on("end",this._filter.end.bind(this._filter))}this._inflate.write(t)},l.prototype._handleMetaData=function(t){this._metaData=t,this._bitmapInfo=Object.create(t),this._filter=new s(this._bitmapInfo)},l.prototype._handleTransColor=function(t){this._bitmapInfo.transColor=t},l.prototype._handlePalette=function(t){this._bitmapInfo.palette=t},l.prototype._simpleTransparency=function(){this._metaData.alpha=!0},l.prototype._headersFinished=function(){this.emit("metadata",this._metaData)},l.prototype._finished=function(){this.errord||(this._inflate?this._inflate.end():this.emit("error","No Inflate block"),this.destroySoon())},l.prototype._complete=function(t){if(!this.errord){try{var e=o.dataToBitMap(t,this._bitmapInfo),r=f(e,this._bitmapInfo);e=null}catch(t){return void this._handleError(t)}this.emit("parsed",r)}}},44645:(t,e,r)=>{var i=r(24052),n=r(84398);function a(t,e,r){var i=t*e;return 8!==r&&(i=Math.ceil(i/(8/r))),i}var s=t.exports=function(t,e){var r=t.width,n=t.height,s=t.interlace,h=t.bpp,o=t.depth;if(this.read=e.read,this.write=e.write,this.complete=e.complete,this._imageIndex=0,this._images=[],s)for(var f=i.getImagePasses(r,n),l=0;l<f.length;l++)this._images.push({byteWidth:a(f[l].width,h,o),height:f[l].height,lineIndex:0});else this._images.push({byteWidth:a(r,h,o),height:n,lineIndex:0});this._xComparison=8===o?h:16===o?2*h:1};s.prototype.start=function(){this.read(this._images[this._imageIndex].byteWidth+1,this._reverseFilterLine.bind(this))},s.prototype._unFilterType1=function(t,e,r){for(var i=this._xComparison,n=i-1,a=0;a<r;a++){var s=t[1+a],h=a>n?e[a-i]:0;e[a]=s+h}},s.prototype._unFilterType2=function(t,e,r){for(var i=this._lastLine,n=0;n<r;n++){var a=t[1+n],s=i?i[n]:0;e[n]=a+s}},s.prototype._unFilterType3=function(t,e,r){for(var i=this._xComparison,n=i-1,a=this._lastLine,s=0;s<r;s++){var h=t[1+s],o=a?a[s]:0,f=s>n?e[s-i]:0,l=Math.floor((f+o)/2);e[s]=h+l}},s.prototype._unFilterType4=function(t,e,r){for(var i=this._xComparison,a=i-1,s=this._lastLine,h=0;h<r;h++){var o=t[1+h],f=s?s[h]:0,l=h>a?e[h-i]:0,p=h>a&&s?s[h-i]:0,u=n(l,f,p);e[h]=o+u}},s.prototype._reverseFilterLine=function(t){var e,r=t[0],i=this._images[this._imageIndex],n=i.byteWidth;if(0===r)e=t.slice(1,n+1);else switch(e=new Buffer(n),r){case 1:this._unFilterType1(t,e,n);break;case 2:this._unFilterType2(t,e,n);break;case 3:this._unFilterType3(t,e,n);break;case 4:this._unFilterType4(t,e,n);break;default:throw new Error("Unrecognised filter type - "+r)}this.write(e),i.lineIndex++,i.lineIndex>=i.height?(this._lastLine=null,this._imageIndex++,i=this._images[this._imageIndex]):this._lastLine=e,i?this.read(i.byteWidth+1,this._reverseFilterLine.bind(this)):(this._lastLine=null,this.complete())}},48144:(t,e,r)=>{var i=r(55148);t.exports=function(t,e,r,n){var a,s=-1!==[i.COLORTYPE_COLOR_ALPHA,i.COLORTYPE_ALPHA].indexOf(n.colorType);if(n.colorType===n.inputColorType){var h=(a=new ArrayBuffer(2),new DataView(a).setInt16(0,256,!0),256!==new Int16Array(a)[0]);if(8===n.bitDepth||16===n.bitDepth&&h)return t}var o=16!==n.bitDepth?t:new Uint16Array(t.buffer),f=255,l=i.COLORTYPE_TO_BPP_MAP[n.inputColorType];4!==l||n.inputHasAlpha||(l=3);var p=i.COLORTYPE_TO_BPP_MAP[n.colorType];16===n.bitDepth&&(f=65535,p*=2);var u=new Buffer(e*r*p),d=0,c=0,_=n.bgColor||{};function b(){var t,e,r,a=f;switch(n.inputColorType){case i.COLORTYPE_COLOR_ALPHA:a=o[d+3],t=o[d],e=o[d+1],r=o[d+2];break;case i.COLORTYPE_COLOR:t=o[d],e=o[d+1],r=o[d+2];break;case i.COLORTYPE_ALPHA:a=o[d+1],e=t=o[d],r=t;break;case i.COLORTYPE_GRAYSCALE:e=t=o[d],r=t;break;default:throw new Error("input color type:"+n.inputColorType+" is not supported at present")}return n.inputHasAlpha&&(s||(a/=f,t=Math.min(Math.max(Math.round((1-a)*_.red+a*t),0),f),e=Math.min(Math.max(Math.round((1-a)*_.green+a*e),0),f),r=Math.min(Math.max(Math.round((1-a)*_.blue+a*r),0),f))),{red:t,green:e,blue:r,alpha:a}}void 0===_.red&&(_.red=f),void 0===_.green&&(_.green=f),void 0===_.blue&&(_.blue=f);for(var g=0;g<r;g++)for(var w=0;w<e;w++){var v=b();switch(n.colorType){case i.COLORTYPE_COLOR_ALPHA:case i.COLORTYPE_COLOR:8===n.bitDepth?(u[c]=v.red,u[c+1]=v.green,u[c+2]=v.blue,s&&(u[c+3]=v.alpha)):(u.writeUInt16BE(v.red,c),u.writeUInt16BE(v.green,c+2),u.writeUInt16BE(v.blue,c+4),s&&u.writeUInt16BE(v.alpha,c+6));break;case i.COLORTYPE_ALPHA:case i.COLORTYPE_GRAYSCALE:var y=(v.red+v.green+v.blue)/3;8===n.bitDepth?(u[c]=y,s&&(u[c+1]=v.alpha)):(u.writeUInt16BE(y,c),s&&u.writeUInt16BE(v.alpha,c+2));break;default:throw new Error("unrecognised color Type "+n.colorType)}d+=l,c+=p}return u}},55148:t=>{t.exports={PNG_SIGNATURE:[137,80,78,71,13,10,26,10],TYPE_IHDR:1229472850,TYPE_IEND:1229278788,TYPE_IDAT:1229209940,TYPE_PLTE:1347179589,TYPE_tRNS:1951551059,TYPE_gAMA:1732332865,COLORTYPE_GRAYSCALE:0,COLORTYPE_PALETTE:1,COLORTYPE_COLOR:2,COLORTYPE_ALPHA:4,COLORTYPE_PALETTE_COLOR:3,COLORTYPE_COLOR_ALPHA:6,COLORTYPE_TO_BPP_MAP:{0:1,2:3,3:1,4:2,6:4},GAMMA_DIVISION:1e5}},60114:(t,e,r)=>{var i=r(39023),n=r(2203),a=r(44295),s=r(41022),h=r(7988),o=e.PNG=function(t){n.call(this),t=t||{},this.width=0|t.width,this.height=0|t.height,this.data=this.width>0&&this.height>0?new Buffer(4*this.width*this.height):null,t.fill&&this.data&&this.data.fill(0),this.gamma=0,this.readable=this.writable=!0,this._parser=new a(t),this._parser.on("error",this.emit.bind(this,"error")),this._parser.on("close",this._handleClose.bind(this)),this._parser.on("metadata",this._metadata.bind(this)),this._parser.on("gamma",this._gamma.bind(this)),this._parser.on("parsed",function(t){this.data=t,this.emit("parsed",t)}.bind(this)),this._packer=new s(t),this._packer.on("data",this.emit.bind(this,"data")),this._packer.on("end",this.emit.bind(this,"end")),this._parser.on("close",this._handleClose.bind(this)),this._packer.on("error",this.emit.bind(this,"error"))};i.inherits(o,n),o.sync=h,o.prototype.pack=function(){return this.data&&this.data.length?(process.nextTick(function(){this._packer.pack(this.data,this.width,this.height,this.gamma)}.bind(this)),this):(this.emit("error","No data provided"),this)},o.prototype.parse=function(t,e){var r,i;return e&&(r=function(t){this.removeListener("error",i),this.data=t,e(null,this)}.bind(this),i=function(t){this.removeListener("parsed",r),e(t,null)}.bind(this),this.once("parsed",r),this.once("error",i)),this.end(t),this},o.prototype.write=function(t){return this._parser.write(t),!0},o.prototype.end=function(t){this._parser.end(t)},o.prototype._metadata=function(t){this.width=t.width,this.height=t.height,this.emit("metadata",t)},o.prototype._gamma=function(t){this.gamma=t},o.prototype._handleClose=function(){this._parser.writable||this._packer.readable||this.emit("close")},o.bitblt=function(t,e,r,i,n,a,s,h){if(i|=0,n|=0,a|=0,s|=0,h|=0,(r|=0)>t.width||i>t.height||r+n>t.width||i+a>t.height)throw new Error("bitblt reading outside image");if(s>e.width||h>e.height||s+n>e.width||h+a>e.height)throw new Error("bitblt writing outside image");for(var o=0;o<a;o++)t.data.copy(e.data,(h+o)*e.width+s<<2,(i+o)*t.width+r<<2,(i+o)*t.width+r+n<<2)},o.prototype.bitblt=function(t,e,r,i,n,a,s){return o.bitblt(this,t,e,r,i,n,a,s),this},o.adjustGamma=function(t){if(t.gamma){for(var e=0;e<t.height;e++)for(var r=0;r<t.width;r++)for(var i=t.width*e+r<<2,n=0;n<3;n++){var a=t.data[i+n]/255;a=Math.pow(a,1/2.2/t.gamma),t.data[i+n]=Math.round(255*a)}t.gamma=0}},o.prototype.adjustGamma=function(){o.adjustGamma(this)}},60940:(t,e,r)=>{var i=!0,n=r(43106),a=r(80524);n.deflateSync||(i=!1);var s=r(4696),h=r(21613),o=r(90762),f=r(78875),l=r(24875);t.exports=function(t,e){if(!i)throw new Error("To use the sync capability of this library in old node versions, please pin pngjs to v2.3.0");var r,p,u,d=[],c=new s(t),_=new o(e,{read:c.read.bind(c),error:function(t){r=t},metadata:function(t){p=t},gamma:function(t){u=t},palette:function(t){p.palette=t},transColor:function(t){p.transColor=t},inflateData:function(t){d.push(t)},simpleTransparency:function(){p.alpha=!0}});if(_.start(),c.process(),r)throw r;var b,g=Buffer.concat(d);if(d.length=0,p.interlace)b=n.inflateSync(g);else{var w=(1+(p.width*p.bpp*p.depth+7>>3))*p.height;b=a(g,{chunkSize:w,maxLength:w})}if(g=null,!b||!b.length)throw new Error("bad png - invalid inflate data response");var v=h.process(b,p);g=null;var y=f.dataToBitMap(v,p);v=null;var m=l(y,p);return p.data=m,p.gamma=u||0,p}},65414:(t,e,r)=>{var i=r(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.isNodePattern=function(t){if(void 0===t)return!1;if("function"!=typeof t)throw new TypeError("Callback must be a function");return!0},e.throwError=function(t,e){if("string"==typeof t&&(t=new Error(t)),"function"==typeof e)return e.call(this,t);throw t},e.scan=function(t,e,r,i,n,a){e=Math.round(e),r=Math.round(r),i=Math.round(i),n=Math.round(n);for(var s=r;s<r+n;s++)for(var h=e;h<e+i;h++){var o=t.bitmap.width*s+h<<2;a.call(t,h,s,o)}return t},e.scanIterator=s;var n=i(r(54756)),a=n.default.mark(s);function s(t,e,r,i,s){var h,o,f;return n.default.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:e=Math.round(e),r=Math.round(r),i=Math.round(i),s=Math.round(s),h=r;case 5:if(!(h<r+s)){n.next=17;break}o=e;case 7:if(!(o<e+i)){n.next=14;break}return f=t.bitmap.width*h+o<<2,n.next=11,{x:o,y:h,idx:f,image:t};case 11:o++,n.next=7;break;case 14:h++,n.next=5;break;case 17:case"end":return n.stop()}}),a)}},69976:(t,e,r)=>{var i=r(39023),n=r(2203),a=t.exports=function(){n.call(this),this._buffers=[],this._buffered=0,this._reads=[],this._paused=!1,this._encoding="utf8",this.writable=!0};i.inherits(a,n),a.prototype.read=function(t,e){this._reads.push({length:Math.abs(t),allowLess:t<0,func:e}),process.nextTick(function(){this._process(),this._paused&&this._reads.length>0&&(this._paused=!1,this.emit("drain"))}.bind(this))},a.prototype.write=function(t,e){return this.writable?(r=Buffer.isBuffer(t)?t:new Buffer(t,e||this._encoding),this._buffers.push(r),this._buffered+=r.length,this._process(),this._reads&&0===this._reads.length&&(this._paused=!0),this.writable&&!this._paused):(this.emit("error",new Error("Stream not writable")),!1);var r},a.prototype.end=function(t,e){t&&this.write(t,e),this.writable=!1,this._buffers&&(0===this._buffers.length?this._end():(this._buffers.push(null),this._process()))},a.prototype.destroySoon=a.prototype.end,a.prototype._end=function(){this._reads.length>0&&this.emit("error",new Error("Unexpected end of input")),this.destroy()},a.prototype.destroy=function(){this._buffers&&(this.writable=!1,this._reads=null,this._buffers=null,this.emit("close"))},a.prototype._processReadAllowingLess=function(t){this._reads.shift();var e=this._buffers[0];e.length>t.length?(this._buffered-=t.length,this._buffers[0]=e.slice(t.length),t.func.call(this,e.slice(0,t.length))):(this._buffered-=e.length,this._buffers.shift(),t.func.call(this,e))},a.prototype._processRead=function(t){this._reads.shift();for(var e=0,r=0,i=new Buffer(t.length);e<t.length;){var n=this._buffers[r++],a=Math.min(n.length,t.length-e);n.copy(i,e,0,a),e+=a,a!==n.length&&(this._buffers[--r]=n.slice(a))}r>0&&this._buffers.splice(0,r),this._buffered-=t.length,t.func.call(this,i)},a.prototype._process=function(){try{for(;this._buffered>0&&this._reads&&this._reads.length>0;){var t=this._reads[0];if(t.allowLess)this._processReadAllowingLess(t);else{if(!(this._buffered>=t.length))break;this._processRead(t)}}this._buffers&&!this.writable&&this._end()}catch(t){this.emit("error",t)}}},72314:(t,e,r)=>{var i=r(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(99027),a=i(r(45199)),s=i(r(88952)),h=i(r(75284)),o=i(r(41432)),f=i(r(62687));e.default=function(){return(0,n.mergeDeep)((0,a.default)(),(0,s.default)(),(0,h.default)(),(0,o.default)(),(0,f.default)())},t.exports=e.default},78875:(t,e,r)=>{var i=r(24052),n=[function(){},function(t,e,r,i){if(i===e.length)throw new Error("Ran out of data");var n=e[i];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=255},function(t,e,r,i){if(i+1>=e.length)throw new Error("Ran out of data");var n=e[i];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=e[i+1]},function(t,e,r,i){if(i+2>=e.length)throw new Error("Ran out of data");t[r]=e[i],t[r+1]=e[i+1],t[r+2]=e[i+2],t[r+3]=255},function(t,e,r,i){if(i+3>=e.length)throw new Error("Ran out of data");t[r]=e[i],t[r+1]=e[i+1],t[r+2]=e[i+2],t[r+3]=e[i+3]}],a=[function(){},function(t,e,r,i){var n=e[0];t[r]=n,t[r+1]=n,t[r+2]=n,t[r+3]=i},function(t,e,r){var i=e[0];t[r]=i,t[r+1]=i,t[r+2]=i,t[r+3]=e[1]},function(t,e,r,i){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=i},function(t,e,r){t[r]=e[0],t[r+1]=e[1],t[r+2]=e[2],t[r+3]=e[3]}];function s(t,e,r,i,a,s){for(var h=t.width,o=t.height,f=t.index,l=0;l<o;l++)for(var p=0;p<h;p++){var u=r(p,l,f);n[i](e,a,u,s),s+=i}return s}function h(t,e,r,i,n,s){for(var h=t.width,o=t.height,f=t.index,l=0;l<o;l++){for(var p=0;p<h;p++){var u=n.get(i),d=r(p,l,f);a[i](e,u,d,s)}n.resetAfterLine()}}e.dataToBitMap=function(t,e){var r,n=e.width,a=e.height,o=e.depth,f=e.bpp,l=e.interlace;if(8!==o)var p=function(t,e){var r=[],i=0;function n(){if(i===t.length)throw new Error("Ran out of data");var n,a,s,h,o,f,l,p,u=t[i];switch(i++,e){default:throw new Error("unrecognised depth");case 16:l=t[i],i++,r.push((u<<8)+l);break;case 4:l=15&u,p=u>>4,r.push(p,l);break;case 2:o=3&u,f=u>>2&3,l=u>>4&3,p=u>>6&3,r.push(p,l,f,o);break;case 1:n=1&u,a=u>>1&1,s=u>>2&1,h=u>>3&1,o=u>>4&1,f=u>>5&1,l=u>>6&1,p=u>>7&1,r.push(p,l,f,o,h,s,a,n)}}return{get:function(t){for(;r.length<t;)n();var e=r.slice(0,t);return r=r.slice(t),e},resetAfterLine:function(){r.length=0},end:function(){if(i!==t.length)throw new Error("extra data found")}}}(t,o);r=o<=8?new Buffer(n*a*4):new Uint16Array(n*a*4);var u,d,c=Math.pow(2,o)-1,_=0;if(l)u=i.getImagePasses(n,a),d=i.getInterlaceIterator(n,a);else{var b=0;d=function(){var t=b;return b+=4,t},u=[{width:n,height:a}]}for(var g=0;g<u.length;g++)8===o?_=s(u[g],r,d,f,t,_):h(u[g],r,d,f,p,c);if(8===o){if(_!==t.length)throw new Error("extra data found")}else p.end();return r}},80524:(t,e,r)=>{var i=r(42613).ok,n=r(43106),a=r(39023),s=r(20181).kMaxLength;function h(t){if(!(this instanceof h))return new h(t);t&&t.chunkSize<n.Z_MIN_CHUNK&&(t.chunkSize=n.Z_MIN_CHUNK),n.Inflate.call(this,t),this._offset=void 0===this._offset?this._outOffset:this._offset,this._buffer=this._buffer||this._outBuffer,t&&null!=t.maxLength&&(this._maxLength=t.maxLength)}function o(t,e){e&&process.nextTick(e),t._handle&&(t._handle.close(),t._handle=null)}function f(t,e){return function(t,e){if("string"==typeof e&&(e=Buffer.from(e)),!(e instanceof Buffer))throw new TypeError("Not a string or buffer");var r=t._finishFlushFlag;return null==r&&(r=n.Z_FINISH),t._processChunk(e,r)}(new h(e),t)}h.prototype._processChunk=function(t,e,r){if("function"==typeof r)return n.Inflate._processChunk.call(this,t,e,r);var a,h=this,f=t&&t.length,l=this._chunkSize-this._offset,p=this._maxLength,u=0,d=[],c=0;function _(t,e){if(!h._hadError){var r=l-e;if(i(r>=0,"have should not go down"),r>0){var n=h._buffer.slice(h._offset,h._offset+r);if(h._offset+=r,n.length>p&&(n=n.slice(0,p)),d.push(n),c+=n.length,0==(p-=n.length))return!1}return(0===e||h._offset>=h._chunkSize)&&(l=h._chunkSize,h._offset=0,h._buffer=Buffer.allocUnsafe(h._chunkSize)),0===e&&(u+=f-t,f=t,!0)}}this.on("error",(function(t){a=t})),i(this._handle,"zlib binding closed");do{var b=this._handle.writeSync(e,t,u,f,this._buffer,this._offset,l);b=b||this._writeState}while(!this._hadError&&_(b[0],b[1]));if(this._hadError)throw a;if(c>=s)throw o(this),new RangeError("Cannot create final Buffer. It would be larger than 0x"+s.toString(16)+" bytes");var g=Buffer.concat(d,c);return o(this),g},a.inherits(h,n.Inflate),t.exports=e=f,e.Inflate=h,e.createInflate=function(t){return new h(t)},e.inflateSync=f},84398:t=>{t.exports=function(t,e,r){var i=t+e-r,n=Math.abs(i-t),a=Math.abs(i-e),s=Math.abs(i-r);return n<=a&&n<=s?t:a<=s?e:r}},90762:(t,e,r)=>{var i=r(55148),n=r(11811),a=t.exports=function(t,e){this._options=t,t.checkCRC=!1!==t.checkCRC,this._hasIHDR=!1,this._hasIEND=!1,this._emittedHeadersFinished=!1,this._palette=[],this._colorType=0,this._chunks={},this._chunks[i.TYPE_IHDR]=this._handleIHDR.bind(this),this._chunks[i.TYPE_IEND]=this._handleIEND.bind(this),this._chunks[i.TYPE_IDAT]=this._handleIDAT.bind(this),this._chunks[i.TYPE_PLTE]=this._handlePLTE.bind(this),this._chunks[i.TYPE_tRNS]=this._handleTRNS.bind(this),this._chunks[i.TYPE_gAMA]=this._handleGAMA.bind(this),this.read=e.read,this.error=e.error,this.metadata=e.metadata,this.gamma=e.gamma,this.transColor=e.transColor,this.palette=e.palette,this.parsed=e.parsed,this.inflateData=e.inflateData,this.finished=e.finished,this.simpleTransparency=e.simpleTransparency,this.headersFinished=e.headersFinished||function(){}};a.prototype.start=function(){this.read(i.PNG_SIGNATURE.length,this._parseSignature.bind(this))},a.prototype._parseSignature=function(t){for(var e=i.PNG_SIGNATURE,r=0;r<e.length;r++)if(t[r]!==e[r])return void this.error(new Error("Invalid file signature"));this.read(8,this._parseChunkBegin.bind(this))},a.prototype._parseChunkBegin=function(t){for(var e=t.readUInt32BE(0),r=t.readUInt32BE(4),a="",s=4;s<8;s++)a+=String.fromCharCode(t[s]);var h=Boolean(32&t[4]);if(this._hasIHDR||r===i.TYPE_IHDR){if(this._crc=new n,this._crc.write(new Buffer(a)),this._chunks[r])return this._chunks[r](e);h?this.read(e+4,this._skipChunk.bind(this)):this.error(new Error("Unsupported critical chunk type "+a))}else this.error(new Error("Expected IHDR on beggining"))},a.prototype._skipChunk=function(){this.read(8,this._parseChunkBegin.bind(this))},a.prototype._handleChunkEnd=function(){this.read(4,this._parseChunkEnd.bind(this))},a.prototype._parseChunkEnd=function(t){var e=t.readInt32BE(0),r=this._crc.crc32();this._options.checkCRC&&r!==e?this.error(new Error("Crc error - "+e+" - "+r)):this._hasIEND||this.read(8,this._parseChunkBegin.bind(this))},a.prototype._handleIHDR=function(t){this.read(t,this._parseIHDR.bind(this))},a.prototype._parseIHDR=function(t){this._crc.write(t);var e=t.readUInt32BE(0),r=t.readUInt32BE(4),n=t[8],a=t[9],s=t[10],h=t[11],o=t[12];if(8===n||4===n||2===n||1===n||16===n)if(a in i.COLORTYPE_TO_BPP_MAP)if(0===s)if(0===h)if(0===o||1===o){this._colorType=a;var f=i.COLORTYPE_TO_BPP_MAP[this._colorType];this._hasIHDR=!0,this.metadata({width:e,height:r,depth:n,interlace:Boolean(o),palette:Boolean(a&i.COLORTYPE_PALETTE),color:Boolean(a&i.COLORTYPE_COLOR),alpha:Boolean(a&i.COLORTYPE_ALPHA),bpp:f,colorType:a}),this._handleChunkEnd()}else this.error(new Error("Unsupported interlace method"));else this.error(new Error("Unsupported filter method"));else this.error(new Error("Unsupported compression method"));else this.error(new Error("Unsupported color type"));else this.error(new Error("Unsupported bit depth "+n))},a.prototype._handlePLTE=function(t){this.read(t,this._parsePLTE.bind(this))},a.prototype._parsePLTE=function(t){this._crc.write(t);for(var e=Math.floor(t.length/3),r=0;r<e;r++)this._palette.push([t[3*r],t[3*r+1],t[3*r+2],255]);this.palette(this._palette),this._handleChunkEnd()},a.prototype._handleTRNS=function(t){this.simpleTransparency(),this.read(t,this._parseTRNS.bind(this))},a.prototype._parseTRNS=function(t){if(this._crc.write(t),this._colorType===i.COLORTYPE_PALETTE_COLOR){if(0===this._palette.length)return void this.error(new Error("Transparency chunk must be after palette"));if(t.length>this._palette.length)return void this.error(new Error("More transparent colors than palette size"));for(var e=0;e<t.length;e++)this._palette[e][3]=t[e];this.palette(this._palette)}this._colorType===i.COLORTYPE_GRAYSCALE&&this.transColor([t.readUInt16BE(0)]),this._colorType===i.COLORTYPE_COLOR&&this.transColor([t.readUInt16BE(0),t.readUInt16BE(2),t.readUInt16BE(4)]),this._handleChunkEnd()},a.prototype._handleGAMA=function(t){this.read(t,this._parseGAMA.bind(this))},a.prototype._parseGAMA=function(t){this._crc.write(t),this.gamma(t.readUInt32BE(0)/i.GAMMA_DIVISION),this._handleChunkEnd()},a.prototype._handleIDAT=function(t){this._emittedHeadersFinished||(this._emittedHeadersFinished=!0,this.headersFinished()),this.read(-t,this._parseIDAT.bind(this,t))},a.prototype._parseIDAT=function(t,e){if(this._crc.write(e),this._colorType===i.COLORTYPE_PALETTE_COLOR&&0===this._palette.length)throw new Error("Expected palette not found");this.inflateData(e);var r=t-e.length;r>0?this._handleIDAT(r):this._handleChunkEnd()},a.prototype._handleIEND=function(t){this.read(t,this._parseIEND.bind(this))},a.prototype._parseIEND=function(t){this._crc.write(t),this._hasIEND=!0,this._handleChunkEnd(),this.finished&&this.finished()}}}]);
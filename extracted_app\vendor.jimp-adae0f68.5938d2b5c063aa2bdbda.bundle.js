"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1155],{21325:(t,a)=>{Object.defineProperty(a,"__esModule",{value:!0}),a.shgTable=a.mulTable=void 0,a.mulTable=[1,57,41,21,203,34,97,73,227,91,149,62,105,45,39,137,241,107,3,173,39,71,65,238,219,101,187,87,81,151,141,133,249,117,221,209,197,187,177,169,5,153,73,139,133,127,243,233,223,107,103,99,191,23,177,171,165,159,77,149,9,139,135,131,253,245,119,231,224,109,211,103,25,195,189,23,45,175,171,83,81,79,155,151,147,9,141,137,67,131,129,251,123,30,235,115,113,221,217,53,13,51,50,49,193,189,185,91,179,175,43,169,83,163,5,79,155,19,75,147,145,143,35,69,17,67,33,65,255,251,247,243,239,59,29,229,113,111,219,27,213,105,207,51,201,199,49,193,191,47,93,183,181,179,11,87,43,85,167,165,163,161,159,157,155,77,19,75,37,73,145,143,141,35,138,137,135,67,33,131,129,255,63,250,247,61,121,239,237,117,29,229,227,225,111,55,109,216,213,211,209,207,205,203,201,199,197,195,193,48,190,47,93,185,183,181,179,178,176,175,173,171,85,21,167,165,41,163,161,5,79,157,78,154,153,19,75,149,74,147,73,144,143,71,141,140,139,137,17,135,134,133,66,131,65,129,1],a.shgTable=[0,9,10,10,14,12,14,14,16,15,16,15,16,15,15,17,18,17,12,18,16,17,17,19,19,18,19,18,18,19,19,19,20,19,20,20,20,20,20,20,15,20,19,20,20,20,21,21,21,20,20,20,21,18,21,21,21,21,20,21,17,21,21,21,22,22,21,22,22,21,22,21,19,22,22,19,20,22,22,21,21,21,22,22,22,18,22,22,21,22,22,23,22,20,23,22,22,23,23,21,19,21,21,21,23,23,23,22,23,23,21,23,22,23,18,22,23,20,22,23,23,23,21,22,20,22,21,22,24,24,24,24,24,22,21,24,23,23,24,21,24,23,24,22,24,24,22,24,24,22,23,24,24,24,20,23,22,23,24,24,24,24,24,24,24,23,21,23,22,23,24,24,24,22,24,24,24,23,22,24,24,25,23,25,25,23,24,25,25,24,22,25,25,25,24,23,24,25,25,25,25,25,25,25,25,25,25,25,25,23,25,23,24,25,25,25,25,25,25,25,25,25,24,22,25,25,23,25,25,20,24,25,24,25,25,22,24,25,24,25,24,25,25,24,25,25,25,25,22,25,25,25,24,25,24,25,18]},57931:(t,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=i(65414);a.default=function(){return{circle:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},a=arguments.length>1?arguments[1]:void 0;"function"==typeof t&&(a=t,t={});var i=t.radius||(this.bitmap.width>this.bitmap.height?this.bitmap.height:this.bitmap.width)/2,h="number"==typeof t.x?t.x:this.bitmap.width/2,e="number"==typeof t.y?t.y:this.bitmap.height/2;return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,a,r){var s=Math.sqrt(Math.pow(t-h,2)+Math.pow(a-e,2));i-s<=0?this.bitmap.data[r+3]=0:i-s<1&&(this.bitmap.data[r+3]=255*(i-s))})),(0,r.isNodePattern)(a)&&a.call(this,null,this),this}}},t.exports=a.default},75148:(t,a,i)=>{Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var r=i(65414),h=i(21325);a.default=function(){return{blur:function(t,a){if("number"!=typeof t)return r.throwError.call(this,"r must be a number",a);if(t<1)return r.throwError.call(this,"r must be greater than 0",a);for(var i,e,s,n,u,b,l,o,m,p,d,f,c,g,w=this.bitmap.width-1,y=this.bitmap.height-1,v=t+1,E=h.mulTable[t],M=h.shgTable[t],P=[],x=[],N=[],Q=[],_=[],T=[],j=2;j-- >0;){for(f=0,c=0,b=0;b<this.bitmap.height;b++){for(i=this.bitmap.data[c]*v,e=this.bitmap.data[c+1]*v,s=this.bitmap.data[c+2]*v,n=this.bitmap.data[c+3]*v,l=1;l<=t;l++)o=c+((l>w?w:l)<<2),i+=this.bitmap.data[o++],e+=this.bitmap.data[o++],s+=this.bitmap.data[o++],n+=this.bitmap.data[o];for(u=0;u<this.bitmap.width;u++)P[f]=i,x[f]=e,N[f]=s,Q[f]=n,0===b&&(_[u]=((o=u+v)<w?o:w)<<2,T[u]=(o=u-t)>0?o<<2:0),m=c+_[u],p=c+T[u],i+=this.bitmap.data[m++]-this.bitmap.data[p++],e+=this.bitmap.data[m++]-this.bitmap.data[p++],s+=this.bitmap.data[m++]-this.bitmap.data[p++],n+=this.bitmap.data[m]-this.bitmap.data[p],f++;c+=this.bitmap.width<<2}for(u=0;u<this.bitmap.width;u++){for(i=P[d=u]*v,e=x[d]*v,s=N[d]*v,n=Q[d]*v,l=1;l<=t;l++)i+=P[d+=l>y?0:this.bitmap.width],e+=x[d],s+=N[d],n+=Q[d];for(f=u<<2,b=0;b<this.bitmap.height;b++)g=n*E>>>M,this.bitmap.data[f+3]=g,g>255&&(this.bitmap.data[f+3]=255),g>0?(g=255/g,this.bitmap.data[f]=(i*E>>>M)*g,this.bitmap.data[f+1]=(e*E>>>M)*g,this.bitmap.data[f+2]=(s*E>>>M)*g):(this.bitmap.data[f+2]=0,this.bitmap.data[f+1]=0,this.bitmap.data[f]=0),0===u&&(_[b]=((o=b+v)<y?o:y)*this.bitmap.width,T[b]=(o=b-t)>0?o*this.bitmap.width:0),m=u+_[b],p=u+T[b],i+=P[m]-P[p],e+=x[m]-x[p],s+=N[m]-N[p],n+=Q[m]-Q[p],f+=this.bitmap.width<<2}}return(0,r.isNodePattern)(a)&&a.call(this,null,this),this}}},t.exports=a.default},85330:(t,a,i)=>{var r=i(2613);Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var h=r(i(73738)),e=i(65414);a.default=function(){return{blit:function(t,a,i,r,s,n,u,b){if(!(t instanceof this.constructor))return e.throwError.call(this,"The source must be a Jimp image",b);if("number"!=typeof a||"number"!=typeof i)return e.throwError.call(this,"x and y must be numbers",b);if("function"==typeof r)b=r,r=0,s=0,n=t.bitmap.width,u=t.bitmap.height;else{if((0,h.default)(r)!==(0,h.default)(s)||(0,h.default)(s)!==(0,h.default)(n)||(0,h.default)(n)!==(0,h.default)(u))return e.throwError.call(this,"srcx, srcy, srcw, srch must be numbers",b);r=r||0,s=s||0,n=n||t.bitmap.width,u=u||t.bitmap.height}a=Math.round(a),i=Math.round(i),r=Math.round(r),s=Math.round(s),n=Math.round(n),u=Math.round(u);var l=this.bitmap.width,o=this.bitmap.height,m=this;return t.scanQuiet(r,s,n,u,(function(t,h,e){var n=a+t-r,u=i+h-s;if(n>=0&&u>=0&&l-n>0&&o-u>0){var b=m.getPixelIndex(n,u),p={r:this.bitmap.data[e],g:this.bitmap.data[e+1],b:this.bitmap.data[e+2],a:this.bitmap.data[e+3]},d={r:m.bitmap.data[b],g:m.bitmap.data[b+1],b:m.bitmap.data[b+2],a:m.bitmap.data[b+3]};m.bitmap.data[b]=(p.a*(p.r-d.r)-d.r+255>>8)+d.r,m.bitmap.data[b+1]=(p.a*(p.g-d.g)-d.g+255>>8)+d.g,m.bitmap.data[b+2]=(p.a*(p.b-d.b)-d.b+255>>8)+d.b,m.bitmap.data[b+3]=this.constructor.limit255(d.a+p.a)}})),(0,e.isNodePattern)(b)&&b.call(this,null,this),this}}},t.exports=a.default},96358:(t,a,i)=>{var r=i(2613);Object.defineProperty(a,"__esModule",{value:!0}),a.default=void 0;var h=r(i(41132)),e=r(i(6535)),s=i(65414);function n(t,a,i,r){for(var h=[0,0,0],e=(a.length-1)/2,s=0;s<a.length;s+=1)for(var n=0;n<a[s].length;n+=1){var u=t.getPixelIndex(i+s-e,r+n-e);h[0]+=t.bitmap.data[u]*a[s][n],h[1]+=t.bitmap.data[u+1]*a[s][n],h[2]+=t.bitmap.data[u+2]*a[s][n]}return h}var u=function(t){return null!=t};function b(t){return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,a,i){var r=parseInt(.2126*this.bitmap.data[i]+.7152*this.bitmap.data[i+1]+.0722*this.bitmap.data[i+2],10);this.bitmap.data[i]=r,this.bitmap.data[i+1]=r,this.bitmap.data[i+2]=r})),(0,s.isNodePattern)(t)&&t.call(this,null,this),this}function l(t,a){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:50;return{r:(a.r-t.r)*(i/100)+t.r,g:(a.g-t.g)*(i/100)+t.g,b:(a.b-t.b)*(i/100)+t.b}}function o(t,a){var i=this;return t&&Array.isArray(t)?(t=t.map((function(t){return"xor"!==t.apply&&"mix"!==t.apply||(t.params[0]=(0,e.default)(t.params[0]).toRgb()),t})),this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(r,n,u){var b={r:i.bitmap.data[u],g:i.bitmap.data[u+1],b:i.bitmap.data[u+2]},o=function(t,a){return i.constructor.limit255(b[t]+a)};t.forEach((function(t){if("mix"===t.apply)b=l(b,t.params[0],t.params[1]);else if("tint"===t.apply)b=l(b,{r:255,g:255,b:255},t.params[0]);else if("shade"===t.apply)b=l(b,{r:0,g:0,b:0},t.params[0]);else if("xor"===t.apply)b={r:b.r^t.params[0].r,g:b.g^t.params[0].g,b:b.b^t.params[0].b};else if("red"===t.apply)b.r=o("r",t.params[0]);else if("green"===t.apply)b.g=o("g",t.params[0]);else if("blue"===t.apply)b.b=o("b",t.params[0]);else{var r;if("hue"===t.apply&&(t.apply="spin"),!(b=(0,e.default)(b))[t.apply])return s.throwError.call(i,"action "+t.apply+" not supported",a);b=(r=b)[t.apply].apply(r,(0,h.default)(t.params)).toRgb()}})),i.bitmap.data[u]=b.r,i.bitmap.data[u+1]=b.g,i.bitmap.data[u+2]=b.b})),(0,s.isNodePattern)(a)&&a.call(this,null,this),this):s.throwError.call(this,"actions must be an array",a)}a.default=function(){return{brightness:function(t,a){return"number"!=typeof t?s.throwError.call(this,"val must be numbers",a):t<-1||t>1?s.throwError.call(this,"val must be a number between -1 and +1",a):(this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(a,i,r){t<0?(this.bitmap.data[r]*=1+t,this.bitmap.data[r+1]*=1+t,this.bitmap.data[r+2]*=1+t):(this.bitmap.data[r]+=(255-this.bitmap.data[r])*t,this.bitmap.data[r+1]+=(255-this.bitmap.data[r+1])*t,this.bitmap.data[r+2]+=(255-this.bitmap.data[r+2])*t)})),(0,s.isNodePattern)(a)&&a.call(this,null,this),this)},contrast:function(t,a){if("number"!=typeof t)return s.throwError.call(this,"val must be numbers",a);if(t<-1||t>1)return s.throwError.call(this,"val must be a number between -1 and +1",a);var i=(t+1)/(1-t);function r(t){return(t=Math.floor(i*(t-127)+127))<0?0:t>255?255:t}return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,a,i){this.bitmap.data[i]=r(this.bitmap.data[i]),this.bitmap.data[i+1]=r(this.bitmap.data[i+1]),this.bitmap.data[i+2]=r(this.bitmap.data[i+2])})),(0,s.isNodePattern)(a)&&a.call(this,null,this),this},posterize:function(t,a){return"number"!=typeof t?s.throwError.call(this,"n must be numbers",a):(t<2&&(t=2),this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(a,i,r){this.bitmap.data[r]=Math.floor(this.bitmap.data[r]/255*(t-1))/(t-1)*255,this.bitmap.data[r+1]=Math.floor(this.bitmap.data[r+1]/255*(t-1))/(t-1)*255,this.bitmap.data[r+2]=Math.floor(this.bitmap.data[r+2]/255*(t-1))/(t-1)*255})),(0,s.isNodePattern)(a)&&a.call(this,null,this),this)},greyscale:b,grayscale:b,opacity:function(t,a){return"number"!=typeof t?s.throwError.call(this,"f must be a number",a):t<0||t>1?s.throwError.call(this,"f must be a number from 0 to 1",a):(this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(a,i,r){var h=this.bitmap.data[r+3]*t;this.bitmap.data[r+3]=h})),(0,s.isNodePattern)(a)&&a.call(this,null,this),this)},sepia:function(t){return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,a,i){var r=this.bitmap.data[i],h=this.bitmap.data[i+1],e=this.bitmap.data[i+2];e=.272*(r=.393*r+.769*h+.189*e)+.534*(h=.349*r+.686*h+.168*e)+.131*e,this.bitmap.data[i]=r<255?r:255,this.bitmap.data[i+1]=h<255?h:255,this.bitmap.data[i+2]=e<255?e:255})),(0,s.isNodePattern)(t)&&t.call(this,null,this),this},fade:function(t,a){return"number"!=typeof t?s.throwError.call(this,"f must be a number",a):t<0||t>1?s.throwError.call(this,"f must be a number from 0 to 1",a):(this.opacity(1-t),(0,s.isNodePattern)(a)&&a.call(this,null,this),this)},convolution:function(t,a,i){"function"==typeof a&&void 0===i&&(i=a,a=null),a||(a=this.constructor.EDGE_EXTEND);var r,h,e,n,u,b,l,o,m,p,d=Buffer.from(this.bitmap.data),f=t.length,c=t[0].length,g=Math.floor(f/2),w=Math.floor(c/2),y=-g,v=-w;return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(i,s,f){n=0,e=0,h=0;for(var c=y;c<=g;c++)for(var E=v;E<=w;E++)o=i+E,m=s+c,r=t[c+g][E+w],-1===(p=this.getPixelIndex(o,m,a))?(l=0,b=0,u=0):(u=this.bitmap.data[p+0],b=this.bitmap.data[p+1],l=this.bitmap.data[p+2]),h+=r*u,e+=r*b,n+=r*l;h<0&&(h=0),e<0&&(e=0),n<0&&(n=0),h>255&&(h=255),e>255&&(e=255),n>255&&(n=255),d[f+0]=h,d[f+1]=e,d[f+2]=n})),this.bitmap.data=d,(0,s.isNodePattern)(i)&&i.call(this,null,this),this},opaque:function(t){return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,a,i){this.bitmap.data[i+3]=255})),(0,s.isNodePattern)(t)&&t.call(this,null,this),this},pixelate:function(t,a,i,r,h,e){if("function"==typeof a)e=a,h=null,r=null,i=null,a=null;else{if("number"!=typeof t)return s.throwError.call(this,"size must be a number",e);if(u(a)&&"number"!=typeof a)return s.throwError.call(this,"x must be a number",e);if(u(i)&&"number"!=typeof i)return s.throwError.call(this,"y must be a number",e);if(u(r)&&"number"!=typeof r)return s.throwError.call(this,"w must be a number",e);if(u(h)&&"number"!=typeof h)return s.throwError.call(this,"h must be a number",e)}var b=[[1/16,2/16,1/16],[2/16,.25,2/16],[1/16,2/16,1/16]];a=a||0,i=i||0,r=u(r)?r:this.bitmap.width-a,h=u(h)?h:this.bitmap.height-i;var l=this.cloneQuiet();return this.scanQuiet(a,i,r,h,(function(a,i,r){a=t*Math.floor(a/t),i=t*Math.floor(i/t);var h=n(l,b,a,i);this.bitmap.data[r]=h[0],this.bitmap.data[r+1]=h[1],this.bitmap.data[r+2]=h[2]})),(0,s.isNodePattern)(e)&&e.call(this,null,this),this},convolute:function(t,a,i,r,h,e){if(!Array.isArray(t))return s.throwError.call(this,"the kernel must be an array",e);if("function"==typeof a)e=a,a=null,i=null,r=null,h=null;else{if(u(a)&&"number"!=typeof a)return s.throwError.call(this,"x must be a number",e);if(u(i)&&"number"!=typeof i)return s.throwError.call(this,"y must be a number",e);if(u(r)&&"number"!=typeof r)return s.throwError.call(this,"w must be a number",e);if(u(h)&&"number"!=typeof h)return s.throwError.call(this,"h must be a number",e)}var b=(t.length-1)/2;a=u(a)?a:b,i=u(i)?i:b,r=u(r)?r:this.bitmap.width-a,h=u(h)?h:this.bitmap.height-i;var l=this.cloneQuiet();return this.scanQuiet(a,i,r,h,(function(a,i,r){var h=n(l,t,a,i);this.bitmap.data[r]=this.constructor.limit255(h[0]),this.bitmap.data[r+1]=this.constructor.limit255(h[1]),this.bitmap.data[r+2]=this.constructor.limit255(h[2])})),(0,s.isNodePattern)(e)&&e.call(this,null,this),this},color:o,colour:o}},t.exports=a.default}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4461],{31974:(t,e,i)=>{i.d(e,{Z:()=>d});var s=i(15215),a=i("aurelia-framework"),n=i(20770),r=i(54995),o=i(48881);let d=class{#t;constructor(t){this.#t=t}attached(){this.subscriptionChanged()}detached(){}subscriptionChanged(){this.#e()||this.themeChanged(this.theme)}themeChanged(t,e){this.#e()||(e&&document.body.classList.remove(`theme-${e}`),document.body.classList.add(`theme-${t}`))}#e(){return false}};d=(0,s.Cg)([(0,r.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,r.$t)((t=>t.account?.subscription)),theme:(0,r.$t)((t=>t.settings?.theme))}}),(0,a.autoinject)(),(0,s.Sn)("design:paramtypes",[n.il])],d)},40930:(t,e,i)=>{i.d(e,{EW:()=>r,FE:()=>o,XO:()=>d,px:()=>n,rf:()=>a,xH:()=>s});const s=7200,a=0,n=0,r=1e4,o=1e4;class d{constructor(t){this.wasEndedByTimeLimit=t}}},96111:(t,e,i)=>{i.d(e,{Y:()=>T});var s=i(15215),a=i(16928),n=i("aurelia-framework"),r=i(20770),o=i(55648),d=i(81810),l=i(83802),c=i("dialogs/time-limit-reached-post-game-dialog"),h=i(60692),m=i(19072),y=i(68539),u=i(20057),p=i(54995),g=i(49442),_=i(14046),L=i(48881),P=i(38777),S=i(50643),v=i(62914),b=i(43570),f=i(40930);let T=class{#i;#s;#a;#t;#n;#r;#o;#d;#l;#c;#h;constructor(t,e,i,s,a,n,r,o){this.hasSeenTimeWarning=!1,this.#t=t,this.#n=e,this.#r=i,this.#o=s,this.#d=a,this.#l=n,this.#c=r,this.#h=o}attached(){this.e39Variant=this.#r.assignments.get(h.n.E39)??null,this.#s=this.#r.onVariantChanged((t=>this.#m(t))),this.#i=this.#n.onTrainerActivated((t=>this.#y(t))),this.#a=(0,P.SO)((()=>this.#u()),f.EW),this.#p().then((()=>{this.#u()}))}detached(){this.#s?.dispose(),this.#s=null,this.#i?.dispose(),this.#i=null,this.#a?.dispose(),this.#a=null}#m(t){t.key===h.n.E39&&(this.e39Variant=t.variant)}async#y(t){if(!this.isEnabled||t.isEnding())return;let e=this.dailyPlayLimitSeconds-this.secondsPlayedToday;if(e>0){let i=new Date;const s=setInterval((async()=>{if(!this.isEnabled)return;const t=await this.#g(i);i=new Date,e-=t,e<=0&&(this.#n.trainer?.addMetadata(new f.XO(!0)),this.#n.endTrainer())}),f.FE);t.onEnded((()=>{this.isEnabled&&this.#g(i),clearInterval(s)}))}}async#g(t){const e=new Date,i=Math.round((0,_.bu)(e,t)/1e3);return i>0?(await this.#t.dispatch(L.Ew,"secondsPlayedToday",i),i):0}get isOverDailyLimit(){return this.secondsPlayedToday>=this.dailyPlayLimitSeconds}secondsPlayedTodayChanged(t,e){const i=(e||0)<this.dailyPlayLimitSeconds,s=t<this.dailyPlayLimitSeconds;i&&!s&&this.#l.event("time_limit_exceeded",{type:"daily",limitHours:this.dailyPlayLimitHours},v.Io),!i&&s&&this.#l.event("time_limit_reset",{type:"daily",limitHours:this.dailyPlayLimitHours},v.Io);const a=this.dailyPlayLimitSeconds-t,n=f.FE/1e3;if(a>0&&a<=300+n&&!this.hasSeenTimeWarning){const t=this.#h.getValue("time_limit_enforcer.five_minutes_left"),e=this.#h.getValue("time_limit_enforcer.upgrade_to_pro_to_use_mods_beyond_your_daily_limit"),i=this.#h.getValue("time_limit_enforcer.upgrade_to_pro");this.#_(t,e,i,"five_minutes_remaining_notification","five_minutes_remaining_notification_show").catch(g.Y),this.hasSeenTimeWarning=!0}}async#_(t,e,i,s,n){const r=a.join(this.#d.info.paths.assets,`time-limit-toast-icon-${this.dailyPlayLimitHours}-hour${this.dailyPlayLimitHours>1?"s":""}.png`),o=`wemod://pro?trigger=${s}`,d=new S.c(t).addText(this.#h.getValue(e)).setActivationType("protocol").setLaunchString(o).addImage(r,"appLogoOverride").setScenario("alarm").setAudio({silent:!0}).addAction({content:i,arguments:o,activationType:"protocol"}).toXml();this.#c.playAtFixedVolume(b.A.Error,75),await this.#d.showToast(d)&&this.#l.event(n,{type:"daily",limitHours:this.dailyPlayLimitHours},v.Io)}async triggerPostTrainerEvents(){const t=this.isEnabled&&this.isOverDailyLimit;if(t){const t=this.#h.getValue("time_limit_enforcer.times_up"),e=this.e39Variant?"time_limit_enforcer.upgrade_to_pro_to_use_mods_beyond_your_daily_limit":`time_limit_enforcer.upgrade_to_pro_to_play_beyond_your_daily_${this.dailyPlayLimitHours}_hour_limit`,i=this.#h.getValue("time_limit_enforcer.upgrade_to_pro");this.#d.flashWindow().catch(g.Y),this.#_(t,e,i,"time_limit_exceeded_notification","time_limit_exceeded_notification_show").catch(g.Y),await this.#o.open({perGame:!1,limitHours:this.dailyPlayLimitHours})}return t}async#p(){this.lastResetDailyPlayLimit||await this.resetTimeLimit()}async#u(){if(!this.isEnabled)return;const t=new Date,e=(0,o.A)().setHours(f.rf,f.px),i=(0,_.dS)(t,e),s=this.secondsPlayedToday>0,a=(0,d.A)(t,new Date(this.lastResetDailyPlayLimit));i&&s&&!a&&this.resetTimeLimit()}async resetTimeLimit(){this.hasSeenTimeWarning=!1,await this.#t.dispatch(L.TU,"secondsPlayedToday",0),await this.#t.dispatch(L.vk,"lastResetDailyPlayLimit")}get isInVariant(){return!!this.e39Variant}get isEnabledForUser(){return!(!1===this.account.features?.timeLimit?.enabled)}get isEnabled(){return false}get canUseInAppControls(){return true}get dailyPlayLimitSeconds(){return this.e39Variant?7200:f.xH}get dailyPlayLimitHours(){return Math.floor(this.dailyPlayLimitSeconds/60/60)}async triggerForegroundModTimeLimitExperiment(){this.account.subscription||(this.e39Variant=await this.#r.trigger(h.n.E39).catch(g.Y)||null)}};(0,s.Cg)([n.observable,(0,s.Sn)("design:type",Object)],T.prototype,"e39Variant",void 0),(0,s.Cg)([(0,n.computedFrom)("secondsPlayedToday"),(0,s.Sn)("design:type",Boolean),(0,s.Sn)("design:paramtypes",[])],T.prototype,"isOverDailyLimit",null),(0,s.Cg)([(0,n.computedFrom)("e39Variant"),(0,s.Sn)("design:type",Boolean),(0,s.Sn)("design:paramtypes",[])],T.prototype,"isInVariant",null),(0,s.Cg)([(0,n.computedFrom)("account"),(0,s.Sn)("design:type",Boolean),(0,s.Sn)("design:paramtypes",[])],T.prototype,"isEnabledForUser",null),(0,s.Cg)([(0,n.computedFrom)("isEnabledForUser","isInVariant","account.subscription"),(0,s.Sn)("design:type",Boolean),(0,s.Sn)("design:paramtypes",[])],T.prototype,"isEnabled",null),(0,s.Cg)([(0,n.computedFrom)("account.subscription","account.features.modControls","e39Variant"),(0,s.Sn)("design:type",Boolean),(0,s.Sn)("design:paramtypes",[])],T.prototype,"canUseInAppControls",null),(0,s.Cg)([(0,n.computedFrom)("e39Variant"),(0,s.Sn)("design:type",Number),(0,s.Sn)("design:paramtypes",[])],T.prototype,"dailyPlayLimitSeconds",null),(0,s.Cg)([(0,n.computedFrom)("dailyPlayLimitSeconds"),(0,s.Sn)("design:type",Number),(0,s.Sn)("design:paramtypes",[])],T.prototype,"dailyPlayLimitHours",null),T=(0,s.Cg)([(0,p.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,p.$t)((t=>t.account)),secondsPlayedToday:(0,p.$t)((t=>t.counters?.secondsPlayedToday)),lastResetDailyPlayLimit:(0,p.$t)((t=>t.timestamps?.lastResetDailyPlayLimit))}}),(0,n.autoinject)(),(0,s.Sn)("design:paramtypes",[r.il,l.jR,y.z,c.TimeLimitReachedPostGameDialogService,m.s,v.j0,b.L,u.F2])],T)}}]);
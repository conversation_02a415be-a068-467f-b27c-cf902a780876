"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8215],{"profile/profile":(e,o,t)=>{t.r(o),t.d(o,{Profile:()=>r});var i=t(15215),a=t("aurelia-framework"),n=t(62914);let r=class{#e;constructor(e){this.#e=e}attached(){this.#e.screenView({name:"Profile",class:"Profile"})}};r=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[n.j0])],r)},"profile/profile.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>i});const i='<template> <require from="./profile.scss"></require> <require from="./resources/elements/profile-section"></require> <require from="./resources/elements/achievements-section"></require> <require from="./resources/elements/pro-section"></require> <require from="./resources/elements/followed-games-section"></require> <require from="../gamification/objectives"></require> <div class="profile-page overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" overflow-fade="vertical"> <div class="profile-page-content"> <div class="profile-info"> <profile-section></profile-section> <achievements-section></achievements-section> <pro-section></pro-section> </div> <div class="profile-sidebar"> <objectives></objectives> <followed-games-section></followed-games-section> </div> </div> </div> </div> </template> '},"profile/profile.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});var i=t(31601),a=t.n(i),n=t(76314),r=t.n(n)()(a());r.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.profile-page{--overflow-fade--background: var(--theme--background)}.profile-page [overflow-fade]{height:100%;overflow-y:overlay}.profile-page [overflow-fade]::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.profile-page [overflow-fade]::-webkit-scrollbar-thumb:window-inactive,.profile-page [overflow-fade]::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.profile-page [overflow-fade]::-webkit-scrollbar-thumb:window-inactive:hover,.profile-page [overflow-fade]::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.profile-page [overflow-fade]::-webkit-scrollbar-button:single-button:vertical:decrement{height:44px}.profile-page [overflow-fade]::-webkit-scrollbar-button:single-button:vertical:increment{height:10px}.profile-page [overflow-fade]::-webkit-scrollbar{background:rgba(0,0,0,0)}.profile-page.overflow-fade__wrapper::before{top:var(--constant--appHeaderHeight)}.profile-page.overflow-fade__wrapper .view-scrollable{padding:var(--constant--appHeaderHeight) 24px 20px}.profile-page.overflow-fade__wrapper .view-scrollable:before{content:"";position:absolute;left:0;top:0;width:100%;height:var(--constant--appHeaderHeight);background:rgba(var(--theme--background--rgb), 0.95);z-index:1}.profile-page .profile-page-content{display:grid;grid-template-columns:1fr 400px;padding:8px 0px 4px;gap:20px;width:100%}.profile-page .profile-page-content .profile-info,.profile-page .profile-page-content .profile-sidebar{display:flex;flex-direction:column;gap:16px;overflow:hidden}.profile-page .profile-page-content .profile-sidebar{max-width:400px}.profile-page .profile-page-content .profile-sidebar article{border-radius:16px}.profile-page .profile-page-content .profile-sidebar article.overflow-fade__wrapper--vertical--no-scrollbar.overflow-fade-bottom::before,.profile-page .profile-page-content .profile-sidebar article.overflow-fade__wrapper--vertical--no-scrollbar.overflow-fade-bottom::after{border-radius:16px}.profile-page .profile-page-content .profile-sidebar article [overflow-fade]{height:100%;overflow:hidden;border-radius:16px}.profile-page .profile-page-content .profile-sidebar article [overflow-fade]::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.profile-page .profile-page-content .profile-sidebar article [overflow-fade]::-webkit-scrollbar-thumb:window-inactive,.profile-page .profile-page-content .profile-sidebar article [overflow-fade]::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.profile-page .profile-page-content .profile-sidebar article [overflow-fade]::-webkit-scrollbar-thumb:window-inactive:hover,.profile-page .profile-page-content .profile-sidebar article [overflow-fade]::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.profile-page .profile-page-content .profile-sidebar article [overflow-fade]::-webkit-scrollbar-button:single-button:vertical:decrement{height:10px}.profile-page .profile-page-content .profile-sidebar article [overflow-fade]::-webkit-scrollbar-button:single-button:vertical:increment{height:10px}.profile-page .profile-page-content .profile-sidebar article [overflow-fade]::-webkit-scrollbar{background:rgba(0,0,0,0)}.profile-page .profile-page-content .profile-sidebar article [overflow-fade]:hover{overflow-y:overlay}',""]);const s=r},"profile/resources/elements/achievements-section":(e,o,t)=>{t.r(o),t.d(o,{AchievementsSection:()=>l});var i=t(15215),a=t("aurelia-framework"),n=t(89045),r=t(54995);const s=[{badgeValue:1,badgeTextKey:"achievements.$value_week",descriptionKey:"achievements.one_week_club",className:"one-week",minDays:7,maxDays:29},{badgeValue:1,badgeTextKey:"achievements.$value_month",descriptionKey:"achievements.one_month_club",className:"one-month",minDays:30,maxDays:89},{badgeValue:3,badgeTextKey:"achievements.$value_months",descriptionKey:"achievements.three_months_club",className:"three-months",minDays:90,maxDays:179},{badgeValue:6,badgeTextKey:"achievements.$value_months",descriptionKey:"achievements.six_months_club",className:"six-months",minDays:179,maxDays:364},{badgeValue:1,badgeTextKey:"achievements.$value_year",descriptionKey:"achievements.one_year_club",className:"one-year",minDays:365,maxDays:729},{badgeValue:2,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.two_years_club",className:"two-years",minDays:730,maxDays:1094},{badgeValue:3,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.three_years_club",className:"three-years",minDays:365,maxDays:1459},{badgeValue:4,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.four_years_club",className:"four-years",minDays:1460,maxDays:1824},{badgeValue:5,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.five_years_club",className:"five-years",minDays:1825,maxDays:2189},{badgeValue:6,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.six_years_club",className:"six-years",minDays:2190,maxDays:2554},{badgeValue:7,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.seven_years_club",className:"seven-years",minDays:2555,maxDays:2919},{badgeValue:8,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.eight_years_club",className:"eight-years",minDays:2920,maxDays:3284},{badgeValue:9,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.nine_years_club",className:"nine-years",minDays:3285,maxDays:3649},{badgeValue:10,badgeTextKey:"achievements.$value_years",descriptionKey:"achievements.ten_years_club",className:"ten-years",minDays:3650,maxDays:1/0}];let l=class{constructor(){this.achievements=[]}attached(){this.#o()}accountChanged(){this.#o()}#o(){this.achievements=[],this.achievements.push({descriptionKey:"achievements.joined_wemod",className:"joined-wemod"}),this.hasUsedMaps&&this.achievements.push({descriptionKey:"achievements.map_explorer",className:"map-explorer"});const e=(o=(0,n.A)(Date.now(),new Date(this.account.joinedAt)),s.find((e=>o>=e.minDays&&o<=e.maxDays))||null);var o;for(e&&this.achievements.push(e);this.achievements.length<5;)this.achievements.push({descriptionKey:"",className:"placeholder"})}};(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Object)],l.prototype,"account",void 0),l=(0,i.Cg)([(0,r.m6)({selectors:{account:(0,r.$t)((e=>e.account)),hasUsedMaps:(0,r.$t)((e=>e.flags?.hasUsedMaps))}}),(0,a.autoinject)()],l)},"profile/resources/elements/achievements-section.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>i});const i='<template> <require from="./achievements-section.scss"></require> <div class="achievements"> <div class="achievements-header"> <i class="trophy-icon"></i> <h2>${\'achievements.achievements\' | i18n}</h2> </div> <div class="achievements-content" tabindex="0"> <div class="achievement" repeat.for="achievement of achievements"> <div class="achievement-image ${achievement.className}"> <div class="badge-text" if.bind="achievement.badgeTextKey" innerhtml.bind="achievement.badgeTextKey | i18n:{ value: achievement.badgeValue || 0 } | markdown"></div> </div> <div class="achievement-name"> <span if.bind="achievement.descriptionKey">${achievement.descriptionKey | i18n}</span> </div> </div> </div> </div> </template> '},"profile/resources/elements/achievements-section.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>O});var i=t(31601),a=t.n(i),n=t(76314),r=t.n(n),s=t(4417),l=t.n(s),c=new URL(t(83959),t.b),d=new URL(t(68355),t.b),p=new URL(t(87871),t.b),m=new URL(t(64806),t.b),g=new URL(t(64643),t.b),f=new URL(t(47874),t.b),h=new URL(t(2250),t.b),v=new URL(t(61513),t.b),b=new URL(t(82084),t.b),u=new URL(t(23043),t.b),w=new URL(t(6942),t.b),x=new URL(t(86221),t.b),y=new URL(t(20376),t.b),k=new URL(t(95335),t.b),_=new URL(t(63439),t.b),L=new URL(t(9353),t.b),$=new URL(t(53115),t.b),z=r()(a()),D=l()(c),K=l()(d),S=l()(p),I=l()(m),R=l()(g),j=l()(f),C=l()(h),T=l()(v),U=l()(b),q=l()(u),G=l()(w),M=l()(x),V=l()(y),N=l()(k),A=l()(_),F=l()(L),H=l()($);z.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${D}) format("woff2")}.material-symbols-outlined,achievements-section .achievements-header .trophy-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}achievements-section .achievements{display:flex;flex-direction:column;position:relative;gap:1px;align-items:center}achievements-section .achievements-header,achievements-section .achievements-content{background:rgba(255,255,255,.03);width:100%}achievements-section .achievements-header{display:flex;align-items:center;align-self:start;gap:12px;height:44px;padding-left:16px;border-top-left-radius:16px;border-top-right-radius:16px}achievements-section .achievements-header h2{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px}.theme-default achievements-section .achievements-header h2{color:rgba(255,255,255,.8)}.theme-purple-pro achievements-section .achievements-header h2{color:rgba(255,255,255,.8)}.theme-green-pro achievements-section .achievements-header h2{color:rgba(255,255,255,.8)}.theme-orange-pro achievements-section .achievements-header h2{color:rgba(255,255,255,.8)}.theme-pro achievements-section .achievements-header h2{color:rgba(255,255,255,.8)}achievements-section .achievements-header .trophy-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;display:flex;font-size:16px}achievements-section .achievements-header .trophy-icon:before{font-family:inherit;content:"trophy"}.theme-default achievements-section .achievements-header .trophy-icon{color:rgba(255,255,255,.6)}.theme-purple-pro achievements-section .achievements-header .trophy-icon{color:rgba(255,255,255,.6)}.theme-green-pro achievements-section .achievements-header .trophy-icon{color:rgba(255,255,255,.6)}.theme-orange-pro achievements-section .achievements-header .trophy-icon{color:rgba(255,255,255,.6)}.theme-pro achievements-section .achievements-header .trophy-icon{color:rgba(255,255,255,.6)}achievements-section .achievements-content{display:flex;flex-direction:row;gap:14px;padding:20px 40px;border-bottom-left-radius:16px;border-bottom-right-radius:16px;overflow-x:auto}achievements-section .achievements-content::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}achievements-section .achievements-content::-webkit-scrollbar-thumb:window-inactive,achievements-section .achievements-content::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}achievements-section .achievements-content::-webkit-scrollbar-thumb:window-inactive:hover,achievements-section .achievements-content::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}achievements-section .achievements-content::-webkit-scrollbar-button:single-button:horizontal:decrement{width:8px}achievements-section .achievements-content::-webkit-scrollbar-button:single-button:horizontal:increment{width:8px}achievements-section .achievements-content::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent 8px, rgba(255, 255, 255, 0.1) 8px, rgba(255, 255, 255, 0.1) calc(100% - 8px), transparent calc(100% - 8px))}achievements-section .achievements .achievement:nth-child(0) .placeholder{opacity:.1}achievements-section .achievements .achievement:nth-child(0) .placeholder+.achievement-name{background:rgba(255,255,255,.1)}achievements-section .achievements .achievement:nth-child(1) .placeholder{opacity:.085}achievements-section .achievements .achievement:nth-child(1) .placeholder+.achievement-name{background:rgba(255,255,255,.085)}achievements-section .achievements .achievement:nth-child(2) .placeholder{opacity:.07}achievements-section .achievements .achievement:nth-child(2) .placeholder+.achievement-name{background:rgba(255,255,255,.07)}achievements-section .achievements .achievement:nth-child(3) .placeholder{opacity:.055}achievements-section .achievements .achievement:nth-child(3) .placeholder+.achievement-name{background:rgba(255,255,255,.055)}achievements-section .achievements .achievement:nth-child(4) .placeholder{opacity:.04}achievements-section .achievements .achievement:nth-child(4) .placeholder+.achievement-name{background:rgba(255,255,255,.04)}achievements-section .achievements .achievement:nth-child(5) .placeholder{opacity:.025}achievements-section .achievements .achievement:nth-child(5) .placeholder+.achievement-name{background:rgba(255,255,255,.025)}achievements-section .achievements .achievement:nth-child(6) .placeholder{opacity:.01}achievements-section .achievements .achievement:nth-child(6) .placeholder+.achievement-name{background:rgba(255,255,255,.01)}achievements-section .achievements .achievement{display:flex;flex-direction:column;align-items:center;gap:10px}achievements-section .achievements .achievement .one-week{background:url(${K})}achievements-section .achievements .achievement .one-month{background:url(${S})}achievements-section .achievements .achievement .three-months{background:url(${I})}achievements-section .achievements .achievement .six-months{background:url(${R})}achievements-section .achievements .achievement .one-year{background:url(${j})}achievements-section .achievements .achievement .two-years{background:url(${C})}achievements-section .achievements .achievement .three-years{background:url(${T})}achievements-section .achievements .achievement .four-years{background:url(${U})}achievements-section .achievements .achievement .five-years{background:url(${q})}achievements-section .achievements .achievement .six-years{background:url(${G})}achievements-section .achievements .achievement .seven-years{background:url(${M})}achievements-section .achievements .achievement .eight-years{background:url(${V})}achievements-section .achievements .achievement .nine-years{background:url(${N})}achievements-section .achievements .achievement .ten-years{background:url(${A})}achievements-section .achievements .achievement .placeholder{background:url(${K});filter:brightness(100)}achievements-section .achievements .achievement .joined-wemod{background:url(${F})}achievements-section .achievements .achievement .map-explorer{background:url(${H})}achievements-section .achievements .achievement .placeholder+.achievement-name{background:rgba(255,255,255,.1);width:42px;height:12px;border-radius:40px}achievements-section .achievements .achievement-image{display:flex;height:100px;width:100px;position:relative;background-size:cover !important}achievements-section .achievements .achievement .badge-text{position:absolute;flex-direction:column;display:flex;align-items:center;text-align:center;justify-content:center;height:100%;width:100%;color:#fff;font-weight:600;font-size:9px;letter-spacing:1.17647px;text-transform:uppercase}achievements-section .achievements .achievement .badge-text em{display:block;font-style:italic;font-weight:900;font-size:22px;letter-spacing:-0.784314px}achievements-section .achievements .achievement-name{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px}.theme-default achievements-section .achievements .achievement-name{color:rgba(255,255,255,.8)}.theme-purple-pro achievements-section .achievements .achievement-name{color:rgba(255,255,255,.8)}.theme-green-pro achievements-section .achievements .achievement-name{color:rgba(255,255,255,.8)}.theme-orange-pro achievements-section .achievements .achievement-name{color:rgba(255,255,255,.8)}.theme-pro achievements-section .achievements .achievement-name{color:rgba(255,255,255,.8)}`,""]);const O=z},"profile/resources/elements/followed-games-section":(e,o,t)=>{t.r(o),t.d(o,{FollowedGamesSection:()=>g});var i=t(15215),a=t(7530),n=t("aurelia-event-aggregator"),r=t("aurelia-framework"),s=t(96555),l=t(62914),c=t(27958),d=t(50654),p=t(21795),m=t(54995);let g=class{#t;#i;#e;constructor(e,o,t,i){this.follows=i,this.#t=e,this.#i=o,this.#e=t}attached(){}get followedGames(){return(this.follows?.followedGames?.map((e=>{const o=this.titles[e.titleId]??this.correlatedUnavailableTitles[e.titleId],t=[];if(o){const e=e=>!!(e?.games??[]).length;e(o)?t.push(...o.games.flatMap((e=>e.correlationIds))):t.push(...o.gameIds.reduce(((e,o)=>[...e,...this.games[o].correlationIds]),[]))}const i=t.map(s.o.parse).find((e=>"steam"===e.platform))?.sku??"";return{...e,steamAppId:i}}))??[]).slice(0,5)}handleEmptyGamesButtonClick(){this.#e.event("followed_games_explore_games_click",{},l.Io),this.#t.router.navigateToRoute("titles")}handleViewAllClick(){this.#e.event("followed_games_view_all_click",{},l.Io),this.#t.router.navigateToRoute("settings",{group:"notifications"})}navigateToFollowedGame(e){this.#t.router.navigateToRoute("title",{titleId:e.titleId,gameId:e.gameId}),this.#i.publish(new p.dY("profile_page_followed_games",e.titleId,e.gameId||null,null,!1))}};(0,i.Cg)([(0,a.Kj)("follows.followedGames","correlatedUnavailableTitles"),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],g.prototype,"followedGames",null),g=(0,i.Cg)([(0,m.m6)({selectors:{titles:(0,m.$t)((e=>e.catalog.titles)),games:(0,m.$t)((e=>e.catalog.games)),correlatedUnavailableTitles:(0,m.$t)((e=>e.correlatedUnavailableTitles))}}),(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[c.L,n.EventAggregator,l.j0,d.O])],g)},"profile/resources/elements/followed-games-section.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>i});const i='<template> <require from="./followed-games-section.scss"></require> <require from="../../../cheats/resources/custom-attributes/steam-client-icon-bg"></require> <div class="followed-games"> <div class="followed-games-header"> <i class="notification-icon"></i> <h2>${\'profile.followed_games\' | i18n}</h2> <div click.delegate="handleViewAllClick()" tabindex="0" class="view-all">${\'profile.view_all\' | i18n}</div> </div> <div class="followed-games-content ${!follows.followedGames.length ? \'empty\' : \'\'}"> <div if.bind="!follows.followedGames.length" class="empty-games"> <i class="empty-games-icon"></i> <div> <h2>${\'profile.no_followed_games\' | i18n}</h2> <p>${\'profile.no_followed_games_description\' | i18n}</p> </div> <button class="empty-games-button" click.delegate="handleEmptyGamesButtonClick()"> ${\'profile.explore_games\' | i18n} </button> </div> <div else class="followed-games-list"> <div repeat.for="followedGame of followedGames" class="followed-game"> <a href="#" class="title-link" click.delegate="navigateToFollowedGame(followedGame)"> <div class="followed-game-icon" steam-client-icon-bg="steam-id.bind: followedGame.steamAppId;"></div> <span class="name">${followedGame.titleName}</span> </a> </div> </div> </div> </div> </template> '},"profile/resources/elements/followed-games-section.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var i=t(31601),a=t.n(i),n=t(76314),r=t.n(n),s=t(4417),l=t.n(s),c=new URL(t(83959),t.b),d=r()(a()),p=l()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,followed-games-section .followed-games-header .notification-icon,followed-games-section .followed-games-content .empty-games .empty-games-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}followed-games-section .followed-games{display:flex;flex-direction:column;position:relative;gap:1px;align-items:start}followed-games-section .followed-games-header,followed-games-section .followed-games-content{background:rgba(255,255,255,.03);width:100%}followed-games-section .followed-games-header{display:flex;align-items:center;align-self:start;gap:12px;height:44px;padding:0px 16px;border-top-left-radius:16px;border-top-right-radius:16px}followed-games-section .followed-games-header h2{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px}.theme-default followed-games-section .followed-games-header h2{color:rgba(255,255,255,.8)}.theme-purple-pro followed-games-section .followed-games-header h2{color:rgba(255,255,255,.8)}.theme-green-pro followed-games-section .followed-games-header h2{color:rgba(255,255,255,.8)}.theme-orange-pro followed-games-section .followed-games-header h2{color:rgba(255,255,255,.8)}.theme-pro followed-games-section .followed-games-header h2{color:rgba(255,255,255,.8)}followed-games-section .followed-games-header .notification-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;display:flex;font-size:16px}followed-games-section .followed-games-header .notification-icon:before{font-family:inherit;content:"notifications"}.theme-default followed-games-section .followed-games-header .notification-icon{color:rgba(255,255,255,.6)}.theme-purple-pro followed-games-section .followed-games-header .notification-icon{color:rgba(255,255,255,.6)}.theme-green-pro followed-games-section .followed-games-header .notification-icon{color:rgba(255,255,255,.6)}.theme-orange-pro followed-games-section .followed-games-header .notification-icon{color:rgba(255,255,255,.6)}.theme-pro followed-games-section .followed-games-header .notification-icon{color:rgba(255,255,255,.6)}followed-games-section .followed-games-header .view-all{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;display:flex;margin-left:auto}followed-games-section .followed-games-header .view-all,followed-games-section .followed-games-header .view-all *{cursor:pointer}.theme-default followed-games-section .followed-games-header .view-all{color:rgba(255,255,255,.6)}.theme-purple-pro followed-games-section .followed-games-header .view-all{color:rgba(255,255,255,.6)}.theme-green-pro followed-games-section .followed-games-header .view-all{color:rgba(255,255,255,.6)}.theme-orange-pro followed-games-section .followed-games-header .view-all{color:rgba(255,255,255,.6)}.theme-pro followed-games-section .followed-games-header .view-all{color:rgba(255,255,255,.6)}followed-games-section .followed-games-header .view-all:hover{color:#fff}followed-games-section .followed-games-content{display:flex;flex-direction:row;gap:14px;border-bottom-left-radius:16px;border-bottom-right-radius:16px;overflow-x:auto;padding:12px 12px 16px 16px}followed-games-section .followed-games-content::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}followed-games-section .followed-games-content::-webkit-scrollbar-thumb:window-inactive,followed-games-section .followed-games-content::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}followed-games-section .followed-games-content::-webkit-scrollbar-thumb:window-inactive:hover,followed-games-section .followed-games-content::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}followed-games-section .followed-games-content.empty{padding:20px 40px}followed-games-section .followed-games-content .followed-games-list{display:flex;flex-direction:column;gap:12px;max-height:300px;width:100%}followed-games-section .followed-games-content .followed-games-list .followed-game{display:flex;align-items:center;max-height:32px;min-height:32px}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(1n+0) img{animation-delay:0.1s}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(2n+0) img{animation-delay:0.2s}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(3n+0) img{animation-delay:0.3s}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(4n+0) img{animation-delay:0.4s}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(5n+0) img{animation-delay:0.5s}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(6n+0) img{animation-delay:0.6s}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(7n+0) img{animation-delay:0.7s}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(8n+0) img{animation-delay:0.8s}followed-games-section .followed-games-content .followed-games-list .followed-game:nth-child(9n+0) img{animation-delay:0.9s}followed-games-section .followed-games-content .followed-games-list .followed-game-icon{min-width:32px;height:32px;border-radius:12px;margin:0 auto;background-size:cover}followed-games-section .followed-games-content .followed-games-list .followed-game-icon.is-fallback{display:flex !important;background:linear-gradient(180deg, #4743fc 0%, #dbe2e3 100%)}followed-games-section .followed-games-content .followed-games-list .followed-game .name{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;margin:auto}.theme-default followed-games-section .followed-games-content .followed-games-list .followed-game .name{color:rgba(255,255,255,.8)}.theme-purple-pro followed-games-section .followed-games-content .followed-games-list .followed-game .name{color:rgba(255,255,255,.8)}.theme-green-pro followed-games-section .followed-games-content .followed-games-list .followed-game .name{color:rgba(255,255,255,.8)}.theme-orange-pro followed-games-section .followed-games-content .followed-games-list .followed-game .name{color:rgba(255,255,255,.8)}.theme-pro followed-games-section .followed-games-content .followed-games-list .followed-game .name{color:rgba(255,255,255,.8)}followed-games-section .followed-games-content .followed-games-list .followed-game .title-link{display:flex;overflow:hidden;gap:12px}followed-games-section .followed-games-content .followed-games-list .followed-game .title-link:hover .name{color:#fff}followed-games-section .followed-games-content .empty-games{display:flex;flex-direction:column;justify-content:center;align-items:center;gap:12px;padding:12px 16px 16px 16px;text-align:center}followed-games-section .followed-games-content .empty-games .empty-games-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;display:flex;font-size:32px}followed-games-section .followed-games-content .empty-games .empty-games-icon:before{font-family:inherit;content:"notifications_paused"}.theme-default followed-games-section .followed-games-content .empty-games .empty-games-icon{color:rgba(255,255,255,.4)}.theme-purple-pro followed-games-section .followed-games-content .empty-games .empty-games-icon{color:rgba(255,255,255,.4)}.theme-green-pro followed-games-section .followed-games-content .empty-games .empty-games-icon{color:rgba(255,255,255,.4)}.theme-orange-pro followed-games-section .followed-games-content .empty-games .empty-games-icon{color:rgba(255,255,255,.4)}.theme-pro followed-games-section .followed-games-content .empty-games .empty-games-icon{color:rgba(255,255,255,.4)}followed-games-section .followed-games-content .empty-games h2{font-size:14px;line-height:21px;margin:0}.theme-default followed-games-section .followed-games-content .empty-games h2{color:rgba(255,255,255,.8)}.theme-purple-pro followed-games-section .followed-games-content .empty-games h2{color:rgba(255,255,255,.8)}.theme-green-pro followed-games-section .followed-games-content .empty-games h2{color:rgba(255,255,255,.8)}.theme-orange-pro followed-games-section .followed-games-content .empty-games h2{color:rgba(255,255,255,.8)}.theme-pro followed-games-section .followed-games-content .empty-games h2{color:rgba(255,255,255,.8)}followed-games-section .followed-games-content .empty-games p{font-size:12px;line-height:18px;margin:0}.theme-default followed-games-section .followed-games-content .empty-games p{color:rgba(255,255,255,.4)}.theme-purple-pro followed-games-section .followed-games-content .empty-games p{color:rgba(255,255,255,.4)}.theme-green-pro followed-games-section .followed-games-content .empty-games p{color:rgba(255,255,255,.4)}.theme-orange-pro followed-games-section .followed-games-content .empty-games p{color:rgba(255,255,255,.4)}.theme-pro followed-games-section .followed-games-content .empty-games p{color:rgba(255,255,255,.4)}followed-games-section .followed-games-content .empty-games .empty-games-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1)}followed-games-section .followed-games-content .empty-games .empty-games-button,followed-games-section .followed-games-content .empty-games .empty-games-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) followed-games-section .followed-games-content .empty-games .empty-games-button{border:1px solid #fff}}followed-games-section .followed-games-content .empty-games .empty-games-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}followed-games-section .followed-games-content .empty-games .empty-games-button>*:first-child{padding-left:0}followed-games-section .followed-games-content .empty-games .empty-games-button>*:last-child{padding-right:0}followed-games-section .followed-games-content .empty-games .empty-games-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) followed-games-section .followed-games-content .empty-games .empty-games-button svg *{fill:CanvasText}}followed-games-section .followed-games-content .empty-games .empty-games-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) followed-games-section .followed-games-content .empty-games .empty-games-button svg{opacity:1}}followed-games-section .followed-games-content .empty-games .empty-games-button img{height:50%}followed-games-section .followed-games-content .empty-games .empty-games-button:disabled{opacity:.3}followed-games-section .followed-games-content .empty-games .empty-games-button:disabled,followed-games-section .followed-games-content .empty-games .empty-games-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){followed-games-section .followed-games-content .empty-games .empty-games-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}followed-games-section .followed-games-content .empty-games .empty-games-button:not(:disabled):hover svg{opacity:1}}followed-games-section .followed-games-content .empty-games .empty-games-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){followed-games-section .followed-games-content .empty-games .empty-games-button:not(:disabled):hover{background:rgba(255,255,255,.3)}}`,""]);const m=d},"profile/resources/elements/pro-section":(e,o,t)=>{t.r(o),t.d(o,{ProSection:()=>l});var i=t(15215),a=t(62914),n=t("aurelia-framework"),r=t("dialogs/pro-onboarding-dialog"),s=t(54995);let l=class{#a;#e;constructor(e,o){this.#a=e,this.#e=o}attached(){}handleExploreProClick(){this.#e.event("profile_page_explore_pro_click",{},a.Io),this.#a.open({trigger:"profile_page_explore_pro"})}};l=(0,i.Cg)([(0,s.m6)({selectors:{account:(0,s.$t)((e=>e.account))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[r.ProOnboardingDialogService,a.j0])],l)},"profile/resources/elements/pro-section.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>r});var i=t(14385),a=t.n(i),n=new URL(t(77182),t.b);const r='<template> <require from="./pro-section.scss"></require> <require from="../../../resources/elements/pro-cta-label"></require> <div if.bind="account.subscription" class="pro-section"> <div class="tagline" innerhtml.bind="\'profile.youre_a_pro\' | i18n | markdown"></div> <div class="info" innerhtml.bind="\'profile.your_pro_subscription\' | i18n | markdown"></div> <button class="cta" click.delegate="handleExploreProClick()">${\'profile.explore_pro_features\' | i18n}</button> <div class="background"> <svg width="100%" height="100%" viewBox="0 0 752 208" fill="none" xmlns="http://www.w3.org/2000/svg"> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M865.615 -27.6617L882.165 -176.609L733.22 14.8922H813.603L797.053 163.84L945.998 -27.6617L865.615 -27.6617Z" fill="white"/> </g> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M240.614 16.4893L248.889 -57.9844L174.416 37.7666H214.608L206.333 112.24L280.805 16.4893L240.614 16.4893Z" fill="white"/> </g> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M475.809 3.16708L479.946 -34.0703L442.71 13.8046H462.806L458.669 51.042L495.905 3.16708L475.809 3.16708Z" fill="white"/> </g> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M27.4946 129.704L48.1817 -56.4805L-138 182.897H-37.5206L-58.2078 369.081L127.974 129.704L27.4946 129.704Z" fill="white"/> </g> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M607.413 120.01L617.757 26.918L524.666 146.607H574.906L564.562 239.699L657.653 120.01L607.413 120.01Z" fill="white"/> </g> </svg> </div> </div> <div else tabindex="0" class="go-pro-section" pro-cta="trigger: profile_page_go_pro_section"> <div class="cta-section"> <div class="tagline" innerhtml.bind="\'profile.support_wemod\' | i18n | markdown"></div> <button class="cta"> <pro-cta-label></pro-cta-label> <i><inline-svg src="'+a()(n)+'"></inline-svg></i> </button> </div> <div class="pro-features-section"> <div class="pro-feature"> <i class="icon save-mods"></i> <div class="pro-feature-text"> <div class="pro-feature-title">${\'profile.save_mods\' | i18n}</div> <div class="pro-feature-description">${\'profile.save_mods_desc\' | i18n}</div> </div> </div> <div class="pro-feature"> <i class="icon overlay"></i> <div class="pro-feature-text"> <div class="pro-feature-title">${\'profile.in_game_overlay\' | i18n}</div> <div class="pro-feature-description">${\'profile.in_game_overlay_desc\' | i18n}</div> </div> </div> <div class="pro-feature"> <i class="icon mobile-app"></i> <div class="pro-feature-text"> <div class="pro-feature-title">${\'profile.remote_mobile_app\' | i18n}</div> <div class="pro-feature-description">${\'profile.remote_mobile_app_desc\' | i18n}</div> </div> </div> <div class="pro-feature"> <i class="icon more"></i> <div class="pro-feature-text"> <div class="pro-feature-title">${\'profile.and_a_lot_more\' | i18n}</div> <div class="pro-feature-description">${\'profile.and_a_lot_more_desc\' | i18n}</div> </div> </div> </div> <div class="background"> <svg width="100%" height="100%" viewBox="0 0 752 208" fill="none" xmlns="http://www.w3.org/2000/svg"> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M865.615 -27.6617L882.165 -176.609L733.22 14.8922H813.603L797.053 163.84L945.998 -27.6617L865.615 -27.6617Z" fill="white"/> </g> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M240.614 16.4893L248.889 -57.9844L174.416 37.7666H214.608L206.333 112.24L280.805 16.4893L240.614 16.4893Z" fill="white"/> </g> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M475.809 3.16708L479.946 -34.0703L442.71 13.8046H462.806L458.669 51.042L495.905 3.16708L475.809 3.16708Z" fill="white"/> </g> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M27.4946 129.704L48.1817 -56.4805L-138 182.897H-37.5206L-58.2078 369.081L127.974 129.704L27.4946 129.704Z" fill="white"/> </g> <g opacity="0.03"> <path fill-rule="evenodd" clip-rule="evenodd" d="M607.413 120.01L617.757 26.918L524.666 146.607H574.906L564.562 239.699L657.653 120.01L607.413 120.01Z" fill="white"/> </g> </svg> </div> </div> </template> '},"profile/resources/elements/pro-section.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var i=t(31601),a=t.n(i),n=t(76314),r=t.n(n),s=t(4417),l=t.n(s),c=new URL(t(83959),t.b),d=r()(a()),p=l()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,pro-section .go-pro-section .pro-features-section .pro-feature .icon.save-mods,pro-section .go-pro-section .pro-features-section .pro-feature .icon.overlay,pro-section .go-pro-section .pro-features-section .pro-feature .icon.mobile-app,pro-section .go-pro-section .pro-features-section .pro-feature .icon.more{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}pro-section .pro-section{display:flex;flex-direction:column;align-items:center;justify-content:center;position:relative;border-radius:16px;overflow:hidden;padding:24px 0px;gap:12px;background:rgba(255,255,255,.03)}pro-section .pro-section .background{position:absolute;top:0;left:0;width:100%;height:100%;z-index:-1}pro-section .pro-section .tagline{display:flex;flex-direction:column;font-size:12px;display:flex;align-items:center;text-align:center;letter-spacing:2.5px;color:rgba(255,255,255,.8);text-transform:uppercase;font-weight:700}pro-section .pro-section .tagline em{font-style:italic;font-weight:900;font-size:48px;letter-spacing:-2.4px;line-height:100%}pro-section .pro-section .info{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:rgba(255,255,255,.6);width:500px;text-align:center}pro-section .pro-section .info em{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700;color:rgba(255,255,255,.8);font-style:normal}pro-section .pro-section .cta{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1)}pro-section .pro-section .cta,pro-section .pro-section .cta *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) pro-section .pro-section .cta{border:1px solid #fff}}pro-section .pro-section .cta>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}pro-section .pro-section .cta>*:first-child{padding-left:0}pro-section .pro-section .cta>*:last-child{padding-right:0}pro-section .pro-section .cta svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) pro-section .pro-section .cta svg *{fill:CanvasText}}pro-section .pro-section .cta svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) pro-section .pro-section .cta svg{opacity:1}}pro-section .pro-section .cta img{height:50%}pro-section .pro-section .cta:disabled{opacity:.3}pro-section .pro-section .cta:disabled,pro-section .pro-section .cta:disabled *{cursor:default;pointer-events:none}@media(hover: hover){pro-section .pro-section .cta:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}pro-section .pro-section .cta:not(:disabled):hover svg{opacity:1}}pro-section .pro-section .cta:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){pro-section .pro-section .cta:not(:disabled):hover{background:rgba(255,255,255,.3)}}pro-section .go-pro-section{display:flex;flex-direction:row;align-items:center;justify-content:start;height:242px;position:relative;border-radius:16px;overflow:hidden;gap:40px;background:rgba(255,255,255,.03);padding:24px}pro-section .go-pro-section,pro-section .go-pro-section *{cursor:pointer}pro-section .go-pro-section .background{position:absolute;top:0;left:0;width:100%;height:100%;z-index:-1;background:linear-gradient(265deg, rgba(31, 186, 248, 0.97) 0%, rgba(42, 155, 249, 0.95) 9.99%, rgba(56, 116, 251, 0.93) 21.4%, rgba(71, 67, 252, 0.8) 38.79%, rgba(97, 0, 255, 0.3) 58.28%, rgba(50, 0, 87, 0.1) 79.41%, rgba(50, 0, 87, 0) 100%)}pro-section .go-pro-section .cta-section{display:flex;flex-direction:column;align-items:center;gap:10px;min-width:175px}pro-section .go-pro-section .cta-section .tagline{display:flex;flex-direction:column;font-size:12px;display:flex;align-items:center;text-align:center;letter-spacing:2.5px;color:#fff;text-transform:uppercase;font-weight:700}pro-section .go-pro-section .cta-section .tagline em{font-style:italic;font-weight:900;font-size:48px;letter-spacing:-2.4px;line-height:100%}pro-section .go-pro-section .cta-section .cta{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);color:#000;background-color:#fff}pro-section .go-pro-section .cta-section .cta,pro-section .go-pro-section .cta-section .cta *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) pro-section .go-pro-section .cta-section .cta{border:1px solid #fff}}pro-section .go-pro-section .cta-section .cta>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}pro-section .go-pro-section .cta-section .cta>*:first-child{padding-left:0}pro-section .go-pro-section .cta-section .cta>*:last-child{padding-right:0}pro-section .go-pro-section .cta-section .cta svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) pro-section .go-pro-section .cta-section .cta svg *{fill:CanvasText}}pro-section .go-pro-section .cta-section .cta svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) pro-section .go-pro-section .cta-section .cta svg{opacity:1}}pro-section .go-pro-section .cta-section .cta img{height:50%}pro-section .go-pro-section .cta-section .cta:disabled{opacity:.3}pro-section .go-pro-section .cta-section .cta:disabled,pro-section .go-pro-section .cta-section .cta:disabled *{cursor:default;pointer-events:none}@media(hover: hover){pro-section .go-pro-section .cta-section .cta:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}pro-section .go-pro-section .cta-section .cta:not(:disabled):hover svg{opacity:1}}pro-section .go-pro-section .cta-section .cta:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){pro-section .go-pro-section .cta-section .cta:not(:disabled):hover{background:rgba(255,255,255,.3)}}pro-section .go-pro-section .cta-section .cta i{padding:4px}pro-section .go-pro-section .cta-section .cta svg{--cta__icon--color: black;opacity:1}pro-section .go-pro-section .cta-section .cta:hover svg{--cta__icon--color: white}pro-section .go-pro-section .pro-features-section{display:grid;grid-template-columns:repeat(2, auto);gap:16px;margin:0 auto}pro-section .go-pro-section .pro-features-section .pro-feature{display:flex;flex-direction:row;align-items:start;gap:8px}pro-section .go-pro-section .pro-features-section .pro-feature .icon{font-size:12px;color:#fff;display:flex;align-self:start}pro-section .go-pro-section .pro-features-section .pro-feature .icon.save-mods{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}pro-section .go-pro-section .pro-features-section .pro-feature .icon.save-mods:before{font-family:inherit;content:"bolt"}pro-section .go-pro-section .pro-features-section .pro-feature .icon.overlay{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}pro-section .go-pro-section .pro-features-section .pro-feature .icon.overlay:before{font-family:inherit;content:"layers"}pro-section .go-pro-section .pro-features-section .pro-feature .icon.mobile-app{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}pro-section .go-pro-section .pro-features-section .pro-feature .icon.mobile-app:before{font-family:inherit;content:"devices"}pro-section .go-pro-section .pro-features-section .pro-feature .icon.more{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}pro-section .go-pro-section .pro-features-section .pro-feature .icon.more:before{font-family:inherit;content:"check_circle"}pro-section .go-pro-section .pro-features-section .pro-feature .pro-feature-text{display:flex;flex-direction:column;justify-content:center;gap:4px;flex:1}pro-section .go-pro-section .pro-features-section .pro-feature .pro-feature-text .pro-feature-title{color:#fff;text-overflow:ellipsis;font-size:12px;font-weight:600;line-height:100%;text-transform:capitalize}pro-section .go-pro-section .pro-features-section .pro-feature .pro-feature-text .pro-feature-description{color:rgba(255,255,255,.6);font-size:12px;font-style:normal;font-weight:600;line-height:120%}@media(max-width: 1200px){pro-section .go-pro-section .pro-features-section .pro-feature .pro-feature-text .pro-feature-description{display:none}}`,""]);const m=d},"profile/resources/elements/profile-section":(e,o,t)=>{t.r(o),t.d(o,{ProfileSection:()=>l});var i=t(15215),a=t(62914),n=t("aurelia-framework"),r=t(18776),s=t(54995);let l=class{#n;#e;constructor(e,o){this.#n=e,this.#e=o}handleEditProfileClick(){this.#e.event("profile_page_edit_profile_click",{},a.Io),this.#n.navigateToRoute("settings",{group:"account/profile"})}handleMyGamesClick(){this.#e.event("profile_page_my_games_click",{},a.Io),this.#n.navigateToRoute("collection",{slug:"my-games"})}handleBoostsClick(){this.#e.event("profile_page_boosts_click",{},a.Io),this.#n.navigateToRoute("queue")}get installedGamesCount(){return Object.keys(this.installedGameVersions??{}).length??0}get memberSinceDate(){return this.account?.joinedAt?new Date(this.account.joinedAt).toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric"}):""}get proSinceDate(){return this.account?.subscription?.startedAt?new Date(this.account.subscription.startedAt).toLocaleDateString(void 0,{year:"numeric",month:"long",day:"numeric"}):""}};(0,i.Cg)([(0,n.computedFrom)("installedGameVersions"),(0,i.Sn)("design:type",Number),(0,i.Sn)("design:paramtypes",[])],l.prototype,"installedGamesCount",null),(0,i.Cg)([(0,n.computedFrom)("account.joinedAt"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],l.prototype,"memberSinceDate",null),(0,i.Cg)([(0,n.computedFrom)("account.subscription"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],l.prototype,"proSinceDate",null),l=(0,i.Cg)([(0,s.m6)({selectors:{account:(0,s.$t)((e=>e.account)),installedGameVersions:(0,s.$t)((e=>e.installedGameVersions))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[r.Ix,a.j0])],l)},"profile/resources/elements/profile-section.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});var i=t(14385),a=t.n(i),n=new URL(t(89247),t.b),r=new URL(t(77182),t.b);const s='<template> <require from="./profile-section.scss"></require> <require from="../../../resources/elements/pro-cta-label"></require> <require from="../../../shared/resources/elements/pro-badge"></require> <div class="profile-hero"> <button class="profile-edit-button" click.delegate="handleEditProfileClick()"> ${\'profile.edit_profile\' | i18n} </button> <div class="profile-name"> <img if.bind="account.profileImage" fallback-src="'+a()(n)+'" src.bind="account.profileImage | cdn: {size: 48}"> <div class="name"> ${account.username} <pro-badge if.bind="account.subscription"></pro-badge> </div> </div> <div class="profile-stats"> <div class="profile-stats-box"> <span class="profile-stats-title">${\'profile.member_since\' | i18n}</span> <span class="profile-stats-stat">${memberSinceDate}</span> </div> <div class="profile-stats-box"> <span class="profile-stats-title">${\'profile.pro_since\' | i18n}</span> <span if.bind="account.subscription" class="profile-stats-stat">${proSinceDate}</span> <span else tabindex="0" pro-cta="trigger: profile_page_profile_info_section" class="profile-stats-stat free-trial"> <pro-cta-label></pro-cta-label> <i><inline-svg src="'+a()(r)+'"></inline-svg></i> </span> </div> <div tabindex="0" class="profile-stats-box my-games" click.delegate="handleMyGamesClick()"> <span class="profile-stats-title">${\'profile.my_games\' | i18n}</span> <span class="profile-stats-stat"> ${installedGamesCount} <span class="navigate-arrow">-></span> </span> </div> <div tabindex="0" class="profile-stats-box boosts" click.delegate="handleBoostsClick()"> <span class="profile-stats-title">${\'profile.boosts\' | i18n}</span> <span class="profile-stats-stat"> ${account.boosts} <span class="navigate-arrow">-></span> </span> </div> </div> </div> </template> '},"profile/resources/elements/profile-section.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});var i=t(31601),a=t.n(i),n=t(76314),r=t.n(n)()(a());r.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}profile-section .profile-hero{display:flex;flex-direction:column;height:242px;position:relative;border-radius:16px;overflow:hidden;gap:12px}profile-section .profile-hero::before{content:"";position:absolute;position:absolute;width:100%;height:100%;left:0px;top:0px;background:radial-gradient(100% 310.74% at 100% 0%, #4139da 0%, #2a1257 58.36%, #1c1625 73.3%, #0d0f12 100%);z-index:-1}profile-section .profile-hero .profile-edit-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);position:absolute;top:16px;right:16px}profile-section .profile-hero .profile-edit-button,profile-section .profile-hero .profile-edit-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) profile-section .profile-hero .profile-edit-button{border:1px solid #fff}}profile-section .profile-hero .profile-edit-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}profile-section .profile-hero .profile-edit-button>*:first-child{padding-left:0}profile-section .profile-hero .profile-edit-button>*:last-child{padding-right:0}profile-section .profile-hero .profile-edit-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) profile-section .profile-hero .profile-edit-button svg *{fill:CanvasText}}profile-section .profile-hero .profile-edit-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) profile-section .profile-hero .profile-edit-button svg{opacity:1}}profile-section .profile-hero .profile-edit-button img{height:50%}profile-section .profile-hero .profile-edit-button:disabled{opacity:.3}profile-section .profile-hero .profile-edit-button:disabled,profile-section .profile-hero .profile-edit-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){profile-section .profile-hero .profile-edit-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}profile-section .profile-hero .profile-edit-button:not(:disabled):hover svg{opacity:1}}profile-section .profile-hero .profile-edit-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){profile-section .profile-hero .profile-edit-button:not(:disabled):hover{background:rgba(255,255,255,.3)}}profile-section .profile-hero .profile-name{display:flex;align-items:center;gap:8px;margin-top:auto;margin-left:16px}profile-section .profile-hero .profile-name img{display:inline-block;border-radius:50%;overflow:hidden;width:44px;height:44px}profile-section .profile-hero .profile-name .name{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:24px;line-height:28px;letter-spacing:-1px;color:#fff;display:inline-flex;align-items:center;gap:8px}profile-section .profile-hero .profile-stats{display:flex;flex-direction:row;padding:16px 20px;align-items:center;gap:20px;justify-content:space-between;background:rgba(255,255,255,.05)}profile-section .profile-hero .profile-stats-box{display:flex;flex-direction:column;width:-webkit-fill-available;margin-bottom:auto}profile-section .profile-hero .profile-stats-box.my-games,profile-section .profile-hero .profile-stats-box.my-games *,profile-section .profile-hero .profile-stats-box.boosts,profile-section .profile-hero .profile-stats-box.boosts *{cursor:pointer}profile-section .profile-hero .profile-stats-box.my-games .navigate-arrow,profile-section .profile-hero .profile-stats-box.boosts .navigate-arrow{display:none}profile-section .profile-hero .profile-stats-box.my-games:hover .profile-stats-stat,profile-section .profile-hero .profile-stats-box.boosts:hover .profile-stats-stat{color:rgba(255,255,255,.8)}profile-section .profile-hero .profile-stats-box.my-games:hover .navigate-arrow,profile-section .profile-hero .profile-stats-box.boosts:hover .navigate-arrow{display:flex;padding-left:4px}profile-section .profile-hero .profile-stats-title{font-weight:500;font-size:12px;line-height:16px;display:flex;align-items:center;color:rgba(255,255,255,.6)}profile-section .profile-hero .profile-stats-stat{font-weight:700;font-size:14px;line-height:20px;display:flex;align-items:center;color:#fff}profile-section .profile-hero .profile-stats-stat.free-trial{gap:4px}profile-section .profile-hero .profile-stats-stat.free-trial,profile-section .profile-hero .profile-stats-stat.free-trial *{cursor:pointer}profile-section .profile-hero .profile-stats-stat.free-trial:hover{color:rgba(255,255,255,.8)}',""]);const s=r}}]);
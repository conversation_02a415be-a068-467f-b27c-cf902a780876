"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1621,2428],{22920:(t,e,s)=>{s.d(e,{h:()=>c});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s("shared/api/index"),r=s(20057),l=s(84551),d=s(59255);let c=class{#t;#e;#s;#i;constructor(t,e,s,i){this.#t=t,this.#e=e,this.#s=s,this.#i=i}createAssistant(t,e){return new d.F(t,e,this.#t,this.#e,this.#s,this.#i)}};c=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[r.F2,o.WeModApiClient,l.Y,a.EventAggregator])],c)},59255:(t,e,s)=>{s.d(e,{F:()=>b,d:()=>c});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s(15563),r=s.n(o),l=s(14046),d=s(64706);class c{constructor(t,e,s,i){this.name=t,this.client=e,this.titleId=s,this.params=i}}class b{#a;#n;#t;#e;#o;#s;#i;constructor(t,e,s,i,n,o){this.#o=new a.EventAggregator,this.config=Object.freeze(t),this.history=e,this.#t=s,this.#e=i,this.#s=n,this.#i=o}dispose(){this.#r(),this.history.dispose()}async#l(t=!1){this.#r();const e=await this.#e.post("/v3/assistant/session",{titleId:this.config.titleId});if(this.isActive){if(!t)if(this.history.items.length){const t=this.history.items[this.history.items.length-1];if((0,l.Ov)(Date.now(),t?.timestamp??Date.now())>=43200){const t=Math.floor(4*Math.random())+1,e=this.#t.getValue(`assistant_chat.welcome_message_${t}`,{game:this.config.titleName}),s=new d.gG("welcome","",[],null,null,!1,!0,this.config.client,null);this.history.add(s),this.#d(s,e)}}else{const t=new d.gG("welcome",e.welcomeMessage,[],null,null,!0,!0,this.config.client,null);this.history.add(t)}this.#a=setTimeout((()=>this.#c()),1e3*(e.serviceTokenTtl-300)),this.#n=setTimeout((()=>this.#b()),1e3*e.serviceTokenTtl),this.session=e,this.#o.publish("session",this.session)}}async#c(){await this.#l(!0)}#b(){this.session=null}#r(){clearTimeout(this.#a),clearTimeout(this.#n)}async#u(t,e){const s=Date.now(),i=await fetch(`${this.config.baseUrl}/${t}`,{method:"POST",headers:{Accept:"application/x-ndjson","Content-Type":"application/json",Authorization:`Bearer ${this.session?.serviceToken}`,"Accept-Language":this.#t.getEffectiveLocale().toString()},body:JSON.stringify(e)});return this.#s.report({endpoint:`assistant:/${t}`,method:"post",responseTime:Date.now()-s}),i}async ask(t,e=null,s=!0,i){const a=new d.gG("answer","",[],null,null,!1,!0,this.config.client,e);let n=!0;s&&this.history.add(new d.gG("question",t,[],null,null,!0,!0,this.config.client,null)),this.history.add(a);try{this.isThinking=!0;const e={assistantId:this.session?.assistantId,question:t,...i&&{config:i}},s=await this.#u("ask",e);if(s.ok){const t=s.body?.getReader();if(!t)throw new Error("Response invalid.");const e=r()(t);let i;for(;!i||!i.done;)if(i=await e.next(),i.value&&("attribution"===i.value.type&&a.sources.push(...i.value.sources),"token"===i.value.type&&(a.text+=i.value.token),"metadata"===i.value.type&&(a.requestId=i.value.requestId),"error"===i.value.type)){this.#g(a),n=!1;break}}else{let t=!1;401===s.status&&await this.#c(),429===s.status&&(this.#h(a),t=!0),t||this.#g(a),n=!1}}catch{navigator.onLine?this.#g(a):this.#p(a),n=!1}finally{this.isThinking=!1}return a.persist&&a.finalize(),{ok:n,answer:a}}async submitFeedback(t,e,s){const i={requestId:t,message:e,type:"positive"===s?"up":"down"};await this.#u("feedback",i);const a=new d.gG("answer","",[],null,null,!1,!1,this.config.client,null);this.history.add(a),this.#d(a,this.#t.getValue(`assistant_chat.thanks_for_your_feedback_${s}`)),this.analyticsEvent("assistant_feedback",{type:s})}#m(t,e){t.type="error",t.text="",t.persist=!1,this.#d(t,e)}#h(t){const e=this.#t.getValue("assistant.rate_limit_error_message");this.#m(t,e)}#g(t){const e=this.#t.getValue("assistant.general_error_message");this.#m(t,e)}#p(t){const e=this.#t.getValue("assistant.offline_error_message");this.#m(t,e)}#d(t,e){this.isResponding=!0;const s=e.match(/\s+/),i=s?s[0]:"",a=i?e.split(i)[0]:e,n=e.slice(a.length).trim();if(t.text+=a+i,n.length){const e=100*Math.random()+50;setTimeout((()=>this.#d(t,n)),e)}else this.isResponding=!1,t.finalize()}onSessionChanged(t){return this.#o.subscribe("session",t)}onReady(t){return this.#o.subscribe("ready",t)}get isReady(){const t=!!this.session&&!!this.isActive;return t&&this.#o.publish("ready"),t}setActive(t){this.isActive=t,t&&!this.session&&this.#l(),t||clearTimeout(this.#a)}analyticsEvent(t,e){this.#i.publish(new c(t,this.config.client,this.config.titleId,e??{}))}}(0,i.Cg)([n.observable,(0,i.Sn)("design:type",Object)],b.prototype,"session",void 0),(0,i.Cg)([n.observable,(0,i.Sn)("design:type",Boolean)],b.prototype,"isResponding",void 0),(0,i.Cg)([n.observable,(0,i.Sn)("design:type",Boolean)],b.prototype,"isThinking",void 0),(0,i.Cg)([n.observable,(0,i.Sn)("design:type",Boolean)],b.prototype,"isActive",void 0),(0,i.Cg)([(0,n.computedFrom)("session","isActive"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],b.prototype,"isReady",null)},64706:(t,e,s)=>{s.d(e,{$H:()=>a,fz:()=>o,gG:()=>n});var i=s("aurelia-event-aggregator");const a=200;class n{#f;constructor(t,e="",s=[],i=null,a,n,o,r,l){this.type=t,this.text=e,this.sources=s,this.requestId=i,this.timestamp=a||Date.now(),this.#f=n,this.persist=o,this.client=r,this.style=l??null}static deserialize(t){return new n(t.type,t.text,t.sources,t.requestId,t.timestamp,!0,!0,t.client,t.style??null)}get finalized(){return this.#f}finalize(){this.#f=!0,this.onFinalized&&this.onFinalized(this)}serialize(){return{type:this.type,text:this.text,sources:this.sources,timestamp:this.timestamp,client:this.client,requestId:this.requestId,style:this.style}}}class o{#y;#o;constructor(t,e=[]){this.items=[],this.#o=new i.EventAggregator,this.#y=t,this.handleItemFinalized=this.handleItemFinalized.bind(this),e.forEach((t=>this.add(t)))}add(t){this.items.push(t),this.items.length>a&&this.items.splice(0,this.items.length-a),this.handleItemAdded(t),t.finalized?this.handleItemFinalized(t):t.onFinalized=this.handleItemFinalized}handleItemFinalized(t){this.#o.publish("item-finalized",{titleId:this.#y,item:t})}onItemFinalized(t){return this.#o.subscribe("item-finalized",t)}handleItemAdded(t){this.#o.publish("item-added",{titleId:this.#y,item:t})}onItemAdded(t){return this.#o.subscribe("item-added",t)}clear(){this.items=[]}dispose(){}}},"shared/api/value-converters":(t,e,s)=>{s.r(e),s.d(e,{CdnValueConverter:()=>n});var i=s(15215),a=s("aurelia-framework");let n=class{#x;constructor(t){this.#x=t}toView(t,e,s){if(!t)return s||"";if(t.includes(":")||!t.startsWith("/"))return t;if(e)for(const s of Object.keys(e))t=t.replace(`{${s}}`,e[s]);if(t.includes("{"))throw new Error(`Missing CDN parameter in "${t}".`);return this.#x+t}};n=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[String])],n)},"shared/assistant/assistant-chat":(t,e,s)=>{s.r(e),s.d(e,{AssistantChat:()=>o});var i=s(15215),a=s("aurelia-framework"),n=s(59255);let o=class{constructor(){this.showDebug=!1}attached(){this.assistant.setActive(!0),this.assistant.analyticsEvent("assistant_open")}detached(){this.assistant.setActive(!1),this.assistant.analyticsEvent("assistant_close")}};(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",n.F)],o.prototype,"assistant",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",String)],o.prototype,"client",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",String)],o.prototype,"titleId",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Function)],o.prototype,"close",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],o.prototype,"chatDisabled",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",String)],o.prototype,"disabledChatMessageKey",void 0),o=(0,i.Cg)([(0,a.autoinject)()],o)},"shared/assistant/assistant-chat.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>i});const i='<template> <require from="./assistant-chat.scss"></require> <require from="./resources/elements/assistant-debug"></require> <require from="./resources/elements/assistant-history-list"></require> <require from="./resources/elements/assistant-input"></require> <require from="../resources/elements/loading-indicator"></require> <div class="control-buttons" inert.bind="inputMode === \'feedback\' && !chatDisabled"> <button class="debug-button" if.bind="assistant.session.debug" click.delegate="showDebug = !showDebug"></button> <button class="close-button" click.delegate="close()"></button> </div> <div class="layout" show.bind="assistant.isReady"> <assistant-history-list history.bind="assistant.history" assistant.bind="assistant" inert.bind="inputMode === \'feedback\' && !chatDisabled"></assistant-history-list> <div class="input-wrapper"> <assistant-input assistant.bind="assistant" disabled.bind="chatDisabled" disabled-message-key.bind="disabledChatMessageKey" debug-config.bind="debugConfig" mode.bind="inputMode" view-model.ref="assistantInput"></assistant-input> </div> <assistant-debug if.bind="showDebug" config.bind="debugConfig" assistant.bind="assistant"></assistant-debug> <div class="overlay ${inputMode === \'feedback\' && !chatDisabled ? \'show\' : \'\'}" click.delegate="assistantInput.submit()"></div> </div> <div class="loading-container" show.bind="!assistant.isReady"> <loading-indicator></loading-indicator> </div> </template> '},"shared/assistant/assistant-chat.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>h});var i=s(31601),a=s.n(i),n=s(76314),o=s.n(n),r=s(4417),l=s.n(r),d=new URL(s(48789),s.b),c=new URL(s(2418),s.b),b=o()(a()),u=l()(d),g=l()(c);b.push([t.id,`assistant-chat{--assistant-color: #6046ff;--assistant-color--rgb: 96, 70, 255;--assistant-link-color: #5b8fff;--assistant-link-color--rgb: 91, 143, 255;background:var(--assistant-bg);color:#fff;position:relative;z-index:0}.theme-default assistant-chat{--assistant-bg-color: #15152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-purple-pro assistant-chat{--assistant-bg-color: #16142c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-green-pro assistant-chat{--assistant-bg-color: #14162c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-orange-pro assistant-chat{--assistant-bg-color: #17152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-pro assistant-chat{--assistant-bg-color: #131023;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}assistant-chat .loading-container{height:100%;display:flex;align-items:center;justify-content:center}assistant-chat .layout{display:flex;flex-direction:column;height:100%;gap:11px}assistant-chat .control-buttons{display:flex;position:absolute;right:20px;top:20px}assistant-chat .control-buttons button{background:#423757 center no-repeat;display:block;width:30px;height:30px;border-radius:50%;border:0;z-index:1;transition:background-color .15s}assistant-chat .control-buttons button+button{margin-left:10px}assistant-chat .control-buttons button:hover{background-color:#645b74}assistant-chat .control-buttons .close-button{background-image:url(${u})}assistant-chat .control-buttons .debug-button{background-image:url(${g});background-size:14px}assistant-chat assistant-history{flex:1 1 auto}assistant-chat .input-wrapper{position:relative;z-index:2}assistant-chat assistant-input{flex:0 0 auto}assistant-chat assistant-debug{flex:0 0 auto;margin-top:-20px}assistant-chat .overlay{position:absolute;left:0;top:0;width:100%;height:100%;z-index:1;background:var(--assistant-bg-color);opacity:0;visibility:hidden;transition:opacity .15s,visibility 0s .15s}assistant-chat .overlay.show{opacity:.8;visibility:visible;transition-delay:0s}`,""]);const h=b},"shared/assistant/resources/elements/assistant-debug":(t,e,s)=>{s.r(e),s.d(e,{AssistantDebug:()=>o});var i=s(15215),a=s("aurelia-framework"),n=s(59255);class o{constructor(){this.debugPrompt="",this.debugModel=""}attached(){this.debugPrompt=this.assistant?.session?.debug?.config.prompt,this.debugModel=this.assistant?.session?.debug?.config.model,this.#v()}debugPromptChanged(){this.#v()}debugModelChanged(){this.#v()}#v(){if(!this.assistant?.session?.debug)return void(this.config=null);const t={...this.assistant.session.debug.config};this.debugModel&&(t.model=this.debugModel),this.debugPrompt&&(t.prompt=this.debugPrompt),Object.entries(t).length?this.config=t:this.config=null}}(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",n.F)],o.prototype,"assistant",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.fromView}),(0,i.Sn)("design:type",Object)],o.prototype,"config",void 0),(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Object)],o.prototype,"debugPrompt",void 0),(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Object)],o.prototype,"debugModel",void 0)},"shared/assistant/resources/elements/assistant-debug.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>i});const i='<template> <require from="./assistant-debug.scss"></require> <select value.bind="debugModel"> <option repeat.for="model of assistant.session.debug.models" model.bind="model">${model}</option> </select> <textarea value.bind="debugPrompt" rows="6" placeholder="Override prompt"></textarea> </template> '},"shared/assistant/resources/elements/assistant-debug.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>u});var i=s(31601),a=s.n(i),n=s(76314),o=s.n(n),r=s(4417),l=s.n(r),d=new URL(s(83959),s.b),c=o()(a()),b=l()(d);c.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${b}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}assistant-debug{padding:0 20px 20px}assistant-debug textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:10px}assistant-debug textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-debug textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-debug textarea:disabled{opacity:.5}assistant-debug textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}assistant-debug textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}assistant-debug textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}assistant-debug textarea::-webkit-scrollbar-thumb:window-inactive,assistant-debug textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}assistant-debug textarea::-webkit-scrollbar-thumb:window-inactive:hover,assistant-debug textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}assistant-debug textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-debug textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-debug textarea:disabled{opacity:.5}assistant-debug textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}assistant-debug textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}assistant-debug select{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;outline:none;border:0;margin-top:10px}assistant-debug select::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-debug select::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-debug select:disabled{opacity:.5}assistant-debug select:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}assistant-debug select:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}assistant-debug select option{color:#000}`,""]);const u=c},"shared/assistant/resources/elements/assistant-history-list":(t,e,s)=>{s.r(e),s.d(e,{AssistantHistoryList:()=>b});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s(25095),r=s(92465),l=s(59255),d=s(64706),c=s("shared/assistant/resources/elements/assistant-input");let b=class{#w;#o;constructor(t){this.isScrolledToBottom=!0,this.#o=t}bind(){this.#k()}attached(){const t=(0,r.yB)(this.historyContainerEl,"scroll",(()=>this.#z()),{passive:!0}),e=new MutationObserver((()=>{this.isScrolledToBottom&&this.scrollToBottom()}));e.observe(this.historyContainerEl,{childList:!0,characterData:!0,subtree:!0});const s=(0,r.yB)(window,"resize",(()=>{this.isScrolledToBottom&&this.scrollToBottom()})),i=new ResizeObserver((()=>{this.isScrolledToBottom&&(this.historyContainerEl.scrollTop=this.historyContainerEl.scrollHeight)}));i.observe(this.historyContainerEl),this.#w=new r.Vd([t,s,(0,r.nm)((()=>i.disconnect())),(0,r.nm)((()=>e.disconnect())),this.history.onItemAdded((()=>this.#k())),this.#o.subscribe(c.SUBMIT_FEEDBACK_EVENT_NAME,(()=>this.scrollToBottom()))])}detached(){this.#w.dispose()}scrollToBottom(){this.historyContainerEl.scrollBy({top:this.historyContainerEl.scrollHeight,behavior:"smooth"}),this.isScrolledToBottom=!0}handleContainerClick(t){const e=t.target;if("A"===t.target.nodeName){const s=e.innerText,i=e.href?.toLowerCase().trim();if("wemod://ask"===i)return s.length&&this.assistant.ask(s),t.stopPropagation(),t.preventDefault(),this.assistant.analyticsEvent("assistant_initial_prompt_clicked"),this.scrollToBottom(),!1;this.assistant.analyticsEvent("assistant_external_link_open",{url:e.href})}return!0}#k(){const t={};this.history.items.forEach(((e,s,i)=>{if(0===s)t[0]=e.timestamp;else{const a=i[s-1];if(e.timestamp&&a.timestamp){const i=Math.abs((0,o.A)(a.timestamp,e.timestamp));e.timestamp&&i>0&&(t[s]=e.timestamp)}}})),this.dayDividers=t,this.showDayDividers=Object.keys(this.dayDividers).length>1,this.scrollToBottom()}#z(){const t=this.historyContainerEl;this.isScrolledToBottom=t.scrollHeight-t.clientHeight<=t.scrollTop+1}};(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",d.fz)],b.prototype,"history",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",l.F)],b.prototype,"assistant",void 0),b=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[a.EventAggregator])],b)},"shared/assistant/resources/elements/assistant-history-list.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>l});var i=s(14385),a=s.n(i),n=new URL(s(13930),s.b),o=new URL(s(80086),s.b),r=new URL(s(92831),s.b);const l='<template class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <require from="./assistant-history-list.scss"></require> <require from="./feedback-buttons"></require> <require from="./sources-button"></require> <require from="../../../utility/resources/value-converters/dates"></require> <div class="history-items" ref="historyContainerEl" overflow-fade="vertical" click.trigger="handleContainerClick($event)"> <template repeat.for="item of history.items"> <div class="day-divider" if.bind="dayDividers[$index] && showDayDividers"> ${item.timestamp | dateOrToday} </div> <div class="item ${item.type} ${!item.finalized && !item.text ? \'loading\' : \'\'} ${item.style ? item.style : \'\'}"> <span if.bind="!item.text" class="item-content"> <div class="ai-image"> <img src="'+a()(n)+'"> </div> <div class="response-placeholder"> <div></div> <div></div> <div></div> </div> </span> <span else class="item-content"> <div class="ai-image" if.bind="item.type !== \'question\'"> <img if.bind="item.type === \'welcome\'" src="'+a()(o)+'"> <img else src="'+a()(r)+'"> </div> <div else class="user-image" if.bind="assistant.config.userProfileImageUrl"> <img src="${assistant.config.userProfileImageUrl | cdn:{size: 32}}"> </div> <div if.bind="item.type === \'welcome\'" class="welcome-message"> <h1 if.bind="assistant.config.userProfileName"> ${\'assistant_chat.hello_$user\' | i18n: { user: assistant.config.userProfileName || \'\' }} </h1> </div> <div class="item-content-inner"> <header class="mission-help-header" if.bind="item.style === \'mission-help\'"> <svg class="icon" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M10 11C10.5523 11 11 10.5523 11 10C11 9.44772 10.5523 9 10 9C9.44772 9 9 9.44772 9 10C9 10.5523 9.44772 11 10 11Z" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M10.75 2V3.5428C13.7405 3.88638 16.1136 6.2595 16.4572 9.25H18V10.75H16.4572C16.1136 13.7405 13.7405 16.1136 10.75 16.4572V18H9.25V16.4572C6.2595 16.1136 3.88638 13.7405 3.5428 10.75H2V9.25H3.5428C3.88638 6.2595 6.2595 3.88638 9.25 3.5428V2H10.75ZM9.25 7V5.05588C7.08901 5.38094 5.38094 7.08901 5.05588 9.25H7V10.75H5.05588C5.38094 12.911 7.08901 14.6191 9.25 14.9441V13H10.75V14.9441C12.911 14.6191 14.6191 12.911 14.9441 10.75H13V9.25H14.9441C14.6191 7.08901 12.911 5.38094 10.75 5.05588V7H9.25Z" fill="white"/> </svg> <span class="label">${\'assistant_history_list.mission_helper\' | i18n}</span> </header> <div innerhtml.bind="item.text | trim | markdown:false"></div> </div> <div class="item-meta"> <feedback-buttons if.bind="item.requestId" request-id.bind="item.requestId" assistant.bind="assistant"></feedback-buttons> <sources-button if.bind="item.sources.length" sources.bind="item.sources" scroll-el.bind="historyContainerEl" assistant.bind="assistant"></sources-button> </div> </span> </div> </template> </div> <button class="scroll-to-bottom" if.bind="!isScrolledToBottom" click.delegate="scrollToBottom()"> <i class="down-icon"></i> </button> </template> '},"shared/assistant/resources/elements/assistant-history-list.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>m});var i=s(31601),a=s.n(i),n=s(76314),o=s.n(n),r=s(4417),l=s.n(r),d=new URL(s(83959),s.b),c=new URL(s(48789),s.b),b=new URL(s(2418),s.b),u=o()(a()),g=l()(d),h=l()(c),p=l()(b);u.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,assistant-history-list .scroll-to-bottom i.down-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}assistant-chat{--assistant-color: #6046ff;--assistant-color--rgb: 96, 70, 255;--assistant-link-color: #5b8fff;--assistant-link-color--rgb: 91, 143, 255;background:var(--assistant-bg);color:#fff;position:relative;z-index:0}.theme-default assistant-chat{--assistant-bg-color: #15152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-purple-pro assistant-chat{--assistant-bg-color: #16142c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-green-pro assistant-chat{--assistant-bg-color: #14162c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-orange-pro assistant-chat{--assistant-bg-color: #17152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-pro assistant-chat{--assistant-bg-color: #131023;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}assistant-chat .loading-container{height:100%;display:flex;align-items:center;justify-content:center}assistant-chat .layout{display:flex;flex-direction:column;height:100%;gap:11px}assistant-chat .control-buttons{display:flex;position:absolute;right:20px;top:20px}assistant-chat .control-buttons button{background:#423757 center no-repeat;display:block;width:30px;height:30px;border-radius:50%;border:0;z-index:1;transition:background-color .15s}assistant-chat .control-buttons button+button{margin-left:10px}assistant-chat .control-buttons button:hover{background-color:#645b74}assistant-chat .control-buttons .close-button{background-image:url(${h})}assistant-chat .control-buttons .debug-button{background-image:url(${p});background-size:14px}assistant-chat assistant-history{flex:1 1 auto}assistant-chat .input-wrapper{position:relative;z-index:2}assistant-chat assistant-input{flex:0 0 auto}assistant-chat assistant-debug{flex:0 0 auto;margin-top:-20px}assistant-chat .overlay{position:absolute;left:0;top:0;width:100%;height:100%;z-index:1;background:var(--assistant-bg-color);opacity:0;visibility:hidden;transition:opacity .15s,visibility 0s .15s}assistant-chat .overlay.show{opacity:.8;visibility:visible;transition-delay:0s}assistant-history-list{--overflow-fade--background: var(--assistant-bg-color);--overflow-fade--width: 16px;flex:1 1 auto;position:relative;justify-content:center;display:flex}assistant-history-list,assistant-history-list *{user-select:text;cursor:auto}assistant-history-list .scroll-to-bottom{appearance:none;border:none;position:absolute;padding:6px;bottom:16px;border-radius:100%;background:rgba(255,255,255,.1);backdrop-filter:blur(10px)}assistant-history-list .scroll-to-bottom,assistant-history-list .scroll-to-bottom *{cursor:pointer}assistant-history-list .scroll-to-bottom i.down-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:flex;color:#fff}assistant-history-list .scroll-to-bottom i.down-icon:before{font-family:inherit;content:"arrow_downward"}assistant-history-list .history-items{position:absolute;left:0;bottom:0;max-height:100%;width:100%;overflow-x:hidden;overflow-y:overlay;display:flex;flex-direction:column;gap:24px}assistant-history-list .history-items::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}assistant-history-list .history-items::-webkit-scrollbar-thumb:window-inactive,assistant-history-list .history-items::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}assistant-history-list .history-items::-webkit-scrollbar-thumb:window-inactive:hover,assistant-history-list .history-items::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}assistant-history-list .item{display:flex;flex-direction:column;align-items:flex-end;padding-left:16px;padding-right:16px}assistant-history-list .item .response-placeholder div{width:180px;height:14px;background:rgba(var(--assistant-color--rgb), 0.2);border-radius:10px;margin:5px 0;position:relative;overflow:hidden}assistant-history-list .item .response-placeholder div:nth-child(2){width:150px}assistant-history-list .item .response-placeholder div:nth-child(3){width:120px}assistant-history-list .item .response-placeholder div::after{content:"";position:absolute;top:0;right:0;bottom:0;left:0;transform:translateX(-100%);background-image:linear-gradient(84.74deg, rgba(255, 255, 255, 0.2) 0%, #6046ff 60%, rgba(255, 255, 255, 0.2) 100%);animation:aigg-response-shimmer 5s ease-in-out infinite;filter:blur(20px)}@keyframes aigg-response-shimmer{0%{transform:translate(0, 0);opacity:0}10%{opacity:.9}20%,100%{transform:translateX(100%);opacity:0}}assistant-history-list .item .ai-image img{width:32px;height:32px;border-radius:100%}assistant-history-list .item .item-content{border-radius:10px;display:inline-block;transition:background-color .15s}assistant-history-list .item .item-content .item-content-inner{font-size:14px;line-height:21px;word-break:break-word}assistant-history-list .item .item-content .item-content-inner p{margin:0}assistant-history-list .item .item-content .item-content-inner br{display:block;margin:5px 0;content:""}assistant-history-list .item .item-content .item-content-inner>br:last-child,assistant-history-list .item .item-content .item-content-inner>br:first-child{display:none}assistant-history-list .item .item-content .item-content-inner h1,assistant-history-list .item .item-content .item-content-inner h2,assistant-history-list .item .item-content .item-content-inner h3,assistant-history-list .item .item-content .item-content-inner h4,assistant-history-list .item .item-content .item-content-inner h5,assistant-history-list .item .item-content .item-content-inner h6{color:#fff}assistant-history-list .item .item-content .item-content-inner h1{font-weight:600;font-size:16px;line-height:25px}assistant-history-list .item .item-content .item-content-inner h2,assistant-history-list .item .item-content .item-content-inner h3,assistant-history-list .item .item-content .item-content-inner h4,assistant-history-list .item .item-content .item-content-inner h5,assistant-history-list .item .item-content .item-content-inner h6{font-weight:700;font-size:15px;line-height:24px}assistant-history-list .item .item-content .item-content-inner strong,assistant-history-list .item .item-content .item-content-inner b{font-weight:700;color:#fff}assistant-history-list .item .item-content .item-content-inner em{font-style:normal;color:#fff}assistant-history-list .item .item-content .item-content-inner a{color:var(--assistant-link-color)}assistant-history-list .item .item-content .item-content-inner a,assistant-history-list .item .item-content .item-content-inner a *{cursor:pointer}assistant-history-list .item .item-content .item-content-inner a:hover{color:#fff}assistant-history-list .item .item-content .item-content-inner a[href^="wemod://ask"]{font-size:14px;line-height:21px;display:flex;align-items:end;float:left;margin:8px 0px 0px 0px;width:50%;color:#fff;transition:color .15s;position:relative;border-radius:12px;background:rgba(96,70,255,.15);padding:8px 12px;height:80px}assistant-history-list .item .item-content .item-content-inner a[href^="wemod://ask"]:last-of-type:nth-of-type(odd){width:100%;margin-bottom:8px}assistant-history-list .item .item-content .item-content-inner a[href^="wemod://ask"]:nth-of-type(odd):not(:last-of-type){width:calc(50% - 4px);margin-right:4px}assistant-history-list .item .item-content .item-content-inner a[href^="wemod://ask"]:nth-of-type(even){width:calc(50% - 4px);margin-left:4px}assistant-history-list .item .item-content .item-content-inner a[href^="wemod://ask"]:nth-last-of-type(2),assistant-history-list .item .item-content .item-content-inner a[href^="wemod://ask"]:nth-last-of-type(1){margin-bottom:8px}assistant-history-list .item .item-content .item-content-inner a[href^="wemod://ask"]:hover{background:rgba(96,70,255,.4)}assistant-history-list .item .item-content .item-content-inner ul,assistant-history-list .item .item-content .item-content-inner ol{margin:5px 0;padding-left:25px}assistant-history-list .item .item-content .item-content-inner img{max-width:100%}assistant-history-list .item .item-content .item-content-inner table{border-spacing:1px;width:100%;table-layout:fixed;border-radius:10px}assistant-history-list .item .item-content .item-content-inner table,assistant-history-list .item .item-content .item-content-inner th,assistant-history-list .item .item-content .item-content-inner td{overflow:hidden}assistant-history-list .item .item-content .item-content-inner td,assistant-history-list .item .item-content .item-content-inner th{padding:9px 14px;background:#22214e}assistant-history-list .item .item-content .item-content-inner th{font-weight:700;color:#fff}assistant-history-list .item .item-content .mission-help-header{background:rgba(255,215,64,.15);color:rgba(255,255,255,.9);padding:6px;display:flex;align-items:center;border-radius:8px;margin-bottom:6px}assistant-history-list .item .item-content .mission-help-header .icon{flex:0 0 auto;margin-right:4px}assistant-history-list .item .item-content .mission-help-header .label{font-size:16px;font-weight:bold}assistant-history-list .item.question{padding-right:50px;align-items:flex-start}assistant-history-list .item.question .item-content{color:#fff}assistant-history-list .item.question .item-content .user-image img{width:32px;height:32px;border-radius:100%;border:1.333px solid rgba(255,255,255,.1)}assistant-history-list .item.answer,assistant-history-list .item.welcome{align-items:flex-start}assistant-history-list .item.answer .item-content,assistant-history-list .item.welcome .item-content{color:rgba(255,255,255,.8);font-size:14px;font-weight:500;line-height:20px}assistant-history-list .item.answer .item-meta{margin-top:6px;position:relative;display:flex;opacity:0;gap:4px;transition:opacity 500ms ease}assistant-history-list .item.answer:hover .item-meta{opacity:1}assistant-history-list .item:last-child.answer .item-meta{opacity:1}assistant-history-list .item.welcome .welcome-message h1{font-weight:800;font-size:24px;line-height:32px;color:#fff;margin:0}assistant-history-list .item.error{align-items:flex-start}assistant-history-list .item.loading .item-content{position:relative;z-index:0;background:rgba(0,0,0,0)}assistant-history-list .item:not(.loading):not(.error).mission-help .item-content-inner{background:rgba(255,215,64,.1);border:1px solid rgba(255,215,64,.5);color:rgba(255,255,255,.8);padding:10px;border-radius:10px;margin-top:2px}assistant-history-list .day-divider{font-size:12px;line-height:18px;display:flex;color:rgba(255,255,255,.25);text-transform:capitalize;margin:15px 0}assistant-history-list .day-divider:before,assistant-history-list .day-divider:after{content:"";flex:1 1 auto;border-top:1px solid rgba(255,255,255,.1);transform:translateY(10px)}assistant-history-list .day-divider:before{margin-right:3px}assistant-history-list .day-divider:after{margin-left:3px}assistant-history-list .alpha-banner{display:block;padding:10px 14px;border-radius:10px;font-size:10px;line-height:18px;color:rgba(255,255,255,.5);background:rgba(var(--assistant-color--rgb), 0.24);margin-bottom:6px}assistant-history-list .alpha-banner a{color:var(--assistant-link-color)}assistant-history-list .alpha-banner a,assistant-history-list .alpha-banner a *{cursor:pointer}assistant-history-list .alpha-banner a:hover{color:#fff}assistant-history-list .alpha-banner strong{font-weight:500;display:inline-block;border:1px solid rgba(255,255,255,.5);font-size:10px;line-height:12px;padding:0 3.5px;text-transform:uppercase;margin-right:3px;border-radius:1.75px}`,""]);const m=u},"shared/assistant/resources/elements/assistant-input":(t,e,s)=>{s.r(e),s.d(e,{AssistantInput:()=>c,START_FEEDBACK_EVENT_NAME:()=>l,SUBMIT_FEEDBACK_EVENT_NAME:()=>d});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s(92465),r=s(59255);const l="start_assistant_feedback",d="submit_assistant_feedback";let c=class{#w;#o;constructor(t){this.mode="ask",this.values={feedback:"",question:""},this.#o=t}attached(){this.#w=new o.Vd([this.#o.subscribe(l,(t=>this.startSubmittingFeedback(t)))])}detached(){this.#w?.dispose()}async submit(){this.loading||this.disabled||(this.loading=!0,"ask"===this.mode&&await this.#C(),"feedback"===this.mode&&await this.submitFeedback(),this.loading=!1,await(0,o.Wn)(),this.inputEl?.focus())}async#C(){if(!this.values.question||!this.assistant.isReady)return;const t=this.values.question;this.assistant.analyticsEvent("assistant_ask"),this.values.question="";const{ok:e,answer:s}=await this.assistant.ask(t,null,!0,this.debugConfig);e?this.assistant.analyticsEvent("assistant_answer",{sourcesCount:s.sources.length}):(this.values.question=t,this.assistant.analyticsEvent("assistant_error"))}async submitFeedback(){await this.assistant.submitFeedback(this.feedbackParams.requestId,this.values.feedback,this.feedbackParams.sentiment),this.#o.publish(d,this.feedbackParams),this.#S()}handleKeyDown(t){if("Enter"===t.key){if(!t.ctrlKey){const e=this.values.question;return this.values.question="",this.values.question=e,t.preventDefault(),this.submit(),!1}"ask"===this.mode&&(this.values[this.currentValueKey]+="\n")}return"Escape"===t.key&&"feedback"===this.mode&&this.submitFeedback(),!0}get currentValueKey(){return"feedback"===this.mode?"feedback":"question"}modeChanged(){this.inputEl?.focus()}startSubmittingFeedback(t){t.titleId===this.assistant?.config.titleId&&(this.mode="feedback",this.feedbackParams=t)}#S(){this.mode="ask",this.values.feedback=""}get isSubmitButtonDisabled(){return this.loading||this.disabled||"ask"===this.mode&&!this.values.question}get isSubmitButtonActive(){return"ask"===this.mode&&!!this.values.question||"feedback"===this.mode&&!!this.values.feedback}};(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",r.F)],c.prototype,"assistant",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],c.prototype,"disabled",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],c.prototype,"disabledMessageKey",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",Object)],c.prototype,"debugConfig",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.fromView}),(0,i.Sn)("design:type",String)],c.prototype,"mode",void 0),(0,i.Cg)([(0,n.computedFrom)("mode"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],c.prototype,"currentValueKey",null),(0,i.Cg)([(0,n.computedFrom)("loading","disabled","mode","values.question"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],c.prototype,"isSubmitButtonDisabled",null),(0,i.Cg)([(0,n.computedFrom)("mode","values.feedback","values.question"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],c.prototype,"isSubmitButtonActive",null),c=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[a.EventAggregator])],c)},"shared/assistant/resources/elements/assistant-input.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>o});var i=s(14385),a=s.n(i),n=new URL(s(24224),s.b);const o='<template class="${mode}-mode"> <require from="./assistant-input.scss"></require> <require from="../../../resources/elements/close-button"></require> <div class="form-disabled-placeholder" if.bind="disabled && disabledMessageKey">${disabledMessageKey | i18n}</div> <div class="form" else> <span class="feedback-icon" if.bind="mode === \'feedback\'"> <i class="${feedbackParams.sentiment === \'negative\' ? \'flip\' : \'\'}"> <inline-svg src="'+a()(n)+'"></inline-svg> </i> </span> <div class="input-wrapper"> <div class="input-sizer" innerhtml.bind="values[currentValueKey] || \'\\n\'"></div> <textarea type="text" value.bind="values[currentValueKey]" ref="inputEl" placeholder="${mode === \'feedback\' ? `assistant_chat.share_your_feedback_${feedbackParams.sentiment}` : \'assistant_chat.ask_anything\' | i18n}" disabled.bind="loading || disabled" rows="1" keydown.delegate="handleKeyDown($event)"></textarea> <button click.delegate="submit()" class="submit-button ${isSubmitButtonActive ? \'active\' : \'\'}" disabled.bind="isSubmitButtonDisabled"> ↑ </button> </div> <close-button if.bind="mode ===\'feedback\'" click.delegate="submitFeedback()"></close-button> <span class="disclaimer-text">${\'assistant.game_guide_may_make_mistakes\' | i18n}</span> </div> </template> '},"shared/assistant/resources/elements/assistant-input.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>m});var i=s(31601),a=s.n(i),n=s(76314),o=s.n(n),r=s(4417),l=s.n(r),d=new URL(s(83959),s.b),c=new URL(s(48789),s.b),b=new URL(s(2418),s.b),u=o()(a()),g=l()(d),h=l()(c),p=l()(b);u.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}assistant-chat{--assistant-color: #6046ff;--assistant-color--rgb: 96, 70, 255;--assistant-link-color: #5b8fff;--assistant-link-color--rgb: 91, 143, 255;background:var(--assistant-bg);color:#fff;position:relative;z-index:0}.theme-default assistant-chat{--assistant-bg-color: #15152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-purple-pro assistant-chat{--assistant-bg-color: #16142c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-green-pro assistant-chat{--assistant-bg-color: #14162c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-orange-pro assistant-chat{--assistant-bg-color: #17152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-pro assistant-chat{--assistant-bg-color: #131023;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}assistant-chat .loading-container{height:100%;display:flex;align-items:center;justify-content:center}assistant-chat .layout{display:flex;flex-direction:column;height:100%;gap:11px}assistant-chat .control-buttons{display:flex;position:absolute;right:20px;top:20px}assistant-chat .control-buttons button{background:#423757 center no-repeat;display:block;width:30px;height:30px;border-radius:50%;border:0;z-index:1;transition:background-color .15s}assistant-chat .control-buttons button+button{margin-left:10px}assistant-chat .control-buttons button:hover{background-color:#645b74}assistant-chat .control-buttons .close-button{background-image:url(${h})}assistant-chat .control-buttons .debug-button{background-image:url(${p});background-size:14px}assistant-chat assistant-history{flex:1 1 auto}assistant-chat .input-wrapper{position:relative;z-index:2}assistant-chat assistant-input{flex:0 0 auto}assistant-chat assistant-debug{flex:0 0 auto;margin-top:-20px}assistant-chat .overlay{position:absolute;left:0;top:0;width:100%;height:100%;z-index:1;background:var(--assistant-bg-color);opacity:0;visibility:hidden;transition:opacity .15s,visibility 0s .15s}assistant-chat .overlay.show{opacity:.8;visibility:visible;transition-delay:0s}assistant-input{--feedback-animation-speed: 0.3s}assistant-input .form-disabled-placeholder{font-size:14px;line-height:21px;padding:4px 9px 5px 9px;border:1px solid rgba(255,255,255,.05);flex:0 0 auto;display:flex;margin:20px;min-height:30px;align-items:center;color:rgba(255,255,255,.5)}assistant-input .form{display:flex;flex-direction:column;padding:0px 16px;overflow:hidden;align-items:flex-end;position:relative;z-index:0}assistant-input .form .input-wrapper{width:100%;min-height:48px;border-radius:28px;flex:1 1 auto;background:linear-gradient(0deg, rgba(96, 70, 255, 0.1) 0%, rgba(96, 70, 255, 0.1) 100%),rgba(255,255,255,.1);position:relative;transition:all var(--feedback-animation-speed) ease-in-out;overflow:hidden;padding-bottom:8px}assistant-input .form .input-wrapper:has(textarea:not(:placeholder-shown)){border-radius:12px}assistant-input .form .input-wrapper:has(textarea:not(:placeholder-shown)) textarea{padding-right:56px}assistant-input .form .input-sizer{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;visibility:hidden;white-space:pre-wrap;overflow-wrap:break-word;max-height:137px;padding:8px 56px 8px 16px}assistant-input .form .input-sizer::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}assistant-input .form .input-sizer::-webkit-scrollbar-thumb:window-inactive,assistant-input .form .input-sizer::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}assistant-input .form .input-sizer::-webkit-scrollbar-thumb:window-inactive:hover,assistant-input .form .input-sizer::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}assistant-input .form .input-sizer::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-input .form .input-sizer::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-input .form .input-sizer:disabled{opacity:.5}assistant-input .form .input-sizer:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}assistant-input .form .input-sizer:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}assistant-input .form textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;position:absolute;left:0px;top:0px;width:100%;height:100%;caret-color:var(--assistant-color) !important;transition:border-color var(--feedback-animation-speed) linear;padding:12px 0px 12px 16px;margin:0;background:rgba(0,0,0,0)}assistant-input .form textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}assistant-input .form textarea::-webkit-scrollbar-thumb:window-inactive,assistant-input .form textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}assistant-input .form textarea::-webkit-scrollbar-thumb:window-inactive:hover,assistant-input .form textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}assistant-input .form textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-input .form textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}assistant-input .form textarea:disabled{opacity:.5}assistant-input .form textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}assistant-input .form textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}assistant-input .form textarea::placeholder{color:rgba(255,255,255,.3);font-size:14px;font-style:normal;font-weight:500}assistant-input .form textarea:focus{border-color:rgba(0,0,0,0);outline-color:rgba(0,0,0,0) !important}assistant-input .form .submit-button{position:absolute;right:8px;bottom:8px;display:flex;justify-content:center;align-items:center;align-self:end;border-radius:28px;background:var(--assistant-color);color:#fff;font-size:20px;font-style:normal;font-weight:500;box-shadow:none;width:32px;border:none;height:32px;transition:all var(--feedback-animation-speed) ease-in-out}assistant-input .form .submit-button,assistant-input .form .submit-button *{cursor:pointer}assistant-input .form .submit-button:hover:not(:disabled){background:linear-gradient(0deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.15) 100%),var(--assistant-color)}assistant-input .form .submit-button:hover:not(:disabled),assistant-input .form .submit-button.active{box-shadow:inset 0 0 0 1px var(--assistant-color)}assistant-input .form .submit-button:hover:not(:disabled):before,assistant-input .form .submit-button.active:before{opacity:1}assistant-input .form .submit-button:disabled{pointer-events:none;filter:grayscale(0.5);opacity:.5}assistant-input .form close-button{position:absolute;right:8px;top:7.5px;z-index:2}assistant-input .form .disclaimer-text{opacity:.8;max-width:368px;flex-shrink:0;align-self:center;color:#fff;background:linear-gradient(90deg, rgba(56, 116, 251, 0.5) 0%, rgba(97, 0, 255, 0.5) 100%),#fff;-webkit-background-clip:text;background-clip:text;-webkit-text-fill-color:rgba(0,0,0,0);text-align:center;font-size:10px;font-style:normal;font-weight:500;line-height:12px;padding:6px 0px 10px 0px}assistant-input.feedback-mode .form .input-wrapper{min-height:72px;border-radius:12px}assistant-input.feedback-mode .form textarea{border:1px solid var(--assistant-link-color);border-radius:12px}assistant-input .feedback-icon{flex:0 0 auto;overflow:hidden;align-self:flex-start}assistant-input .feedback-icon i{width:20px;margin-right:5px}assistant-input .feedback-icon i svg{width:100%}assistant-input .feedback-icon i svg *{fill:rgba(var(--assistant-link-color--rgb), 0.12);stroke:var(--assistant-link-color)}assistant-input .feedback-icon i.flip{transform:scaleX(-1) scaleY(-1)}`,""]);const m=u},"shared/assistant/resources/elements/feedback-buttons":(t,e,s)=>{s.r(e),s.d(e,{FeedbackButtons:()=>l});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s(59255),r=s("shared/assistant/resources/elements/assistant-input");let l=class{#M;#o;constructor(t){this.#o=t}attached(){this.#M=this.#o.subscribe(r.SUBMIT_FEEDBACK_EVENT_NAME,(t=>{this.requestId===t.requestId&&(this.submittedSentiment=t.sentiment)}))}detached(){this.#M?.dispose()}handleClick(t){const e={titleId:this.assistant.config.titleId,requestId:this.requestId,sentiment:t};this.#o.publish(r.START_FEEDBACK_EVENT_NAME,e)}};(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],l.prototype,"requestId",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",o.F)],l.prototype,"assistant",void 0),l=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[a.EventAggregator])],l)},"shared/assistant/resources/elements/feedback-buttons.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>i});const i="<template> <require from=\"./feedback-buttons.scss\"></require> <button class=\"positive ${submittedSentiment === 'positive' ? 'submitted' : ''}\" click.delegate=\"handleClick('positive')\" aria-label.bind=\"'feedback_buttons.positive_feedback' | i18n\"> <i class=\"thumbs-up\"></i> </button> <button class=\"negative ${submittedSentiment === 'negative' ? 'submitted' : ''}\" click.delegate=\"handleClick('negative')\" aria-label.bind=\"'feedback_buttons.negative_feedback' | i18n\"> <i class=\"thumbs-down\"></i> </button> </template> "},"shared/assistant/resources/elements/feedback-buttons.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>m});var i=s(31601),a=s.n(i),n=s(76314),o=s.n(n),r=s(4417),l=s.n(r),d=new URL(s(83959),s.b),c=new URL(s(48789),s.b),b=new URL(s(2418),s.b),u=o()(a()),g=l()(d),h=l()(c),p=l()(b);u.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,feedback-buttons button i.thumbs-up,feedback-buttons button i.thumbs-down{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}assistant-chat{--assistant-color: #6046ff;--assistant-color--rgb: 96, 70, 255;--assistant-link-color: #5b8fff;--assistant-link-color--rgb: 91, 143, 255;background:var(--assistant-bg);color:#fff;position:relative;z-index:0}.theme-default assistant-chat{--assistant-bg-color: #15152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-purple-pro assistant-chat{--assistant-bg-color: #16142c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-green-pro assistant-chat{--assistant-bg-color: #14162c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-orange-pro assistant-chat{--assistant-bg-color: #17152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-pro assistant-chat{--assistant-bg-color: #131023;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}assistant-chat .loading-container{height:100%;display:flex;align-items:center;justify-content:center}assistant-chat .layout{display:flex;flex-direction:column;height:100%;gap:11px}assistant-chat .control-buttons{display:flex;position:absolute;right:20px;top:20px}assistant-chat .control-buttons button{background:#423757 center no-repeat;display:block;width:30px;height:30px;border-radius:50%;border:0;z-index:1;transition:background-color .15s}assistant-chat .control-buttons button+button{margin-left:10px}assistant-chat .control-buttons button:hover{background-color:#645b74}assistant-chat .control-buttons .close-button{background-image:url(${h})}assistant-chat .control-buttons .debug-button{background-image:url(${p});background-size:14px}assistant-chat assistant-history{flex:1 1 auto}assistant-chat .input-wrapper{position:relative;z-index:2}assistant-chat assistant-input{flex:0 0 auto}assistant-chat assistant-debug{flex:0 0 auto;margin-top:-20px}assistant-chat .overlay{position:absolute;left:0;top:0;width:100%;height:100%;z-index:1;background:var(--assistant-bg-color);opacity:0;visibility:hidden;transition:opacity .15s,visibility 0s .15s}assistant-chat .overlay.show{opacity:.8;visibility:visible;transition-delay:0s}feedback-buttons{display:inline-flex;align-items:center;gap:4px}feedback-buttons button{background:rgba(0,0,0,0);padding:0;border:0;outline:none}feedback-buttons button,feedback-buttons button *{cursor:pointer}feedback-buttons button i{font-size:20px;color:rgba(255,255,255,.6);padding:4px;border-radius:6px}feedback-buttons button i.thumbs-up{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}feedback-buttons button i.thumbs-up:before{font-family:inherit;content:"thumb_up"}feedback-buttons button i.thumbs-down{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}feedback-buttons button i.thumbs-down:before{font-family:inherit;content:"thumb_down"}feedback-buttons button i:hover{background:linear-gradient(0deg, rgba(96, 70, 255, 0.1) 0%, rgba(96, 70, 255, 0.1) 100%),rgba(255,255,255,.1);color:color-mix(in srgb, rgba(96, 70, 255, 0.6), rgba(255, 255, 255, 0.8))}feedback-buttons button.negative svg{transform:scaleX(-1) scaleY(-1)}feedback-buttons button svg{width:13px}feedback-buttons button svg *{fill:rgba(0,0,0,0);stroke:rgba(255,255,255,.4);transition:stroke .15s;stroke-opacity:1}feedback-buttons button:hover svg *,feedback-buttons button.submitted svg *{stroke:var(--assistant-link-color)}`,""]);const m=u},"shared/assistant/resources/elements/sources-button":(t,e,s)=>{s.r(e),s.d(e,{SourcesButton:()=>o});var i=s(15215),a=s("aurelia-framework"),n=s(59255);let o=class{#_;constructor(t){this.#_=t}handleOpen(){if(this.isOpen)return;this.isOpen=!0,this.assistant.analyticsEvent("assistant_sources_open");const t=this.scrollEl.getBoundingClientRect(),e=this.#_.getBoundingClientRect();this.showBelow=e.top-200<t.top}handleClose(){this.isOpen=!1}handleSourceClick(t){this.assistant.analyticsEvent("assistant_source_click",{url:t.url}),window.open(t.url,"_blank")}};(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",Array)],o.prototype,"sources",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",HTMLElement)],o.prototype,"scrollEl",void 0),(0,i.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.toView}),(0,i.Sn)("design:type",n.F)],o.prototype,"assistant",void 0),o=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[Element])],o)},"shared/assistant/resources/elements/sources-button.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>i});const i='<template> <require from="./sources-button.scss"></require> <require from="../../../resources/custom-attributes/close-if-click-outside"></require> <button close-if-click-outside.two-way="isOpen" focusin.delegate="handleOpen()" click.delegate="handleOpen()" class="${isOpen ? \'open\' : \'\'}"> <div class="inner"> <i class="info-icon"></i> <span>${\'sources_button.sources\' | i18n}</span> </div> <menu class="popup ${showBelow ? \'show-below\' : \'\'}" if.bind="isOpen"> <menu class="sources"> <li class="source" repeat.for="source of sources"> <a href="#" title.bind="source.url" click.delegate="handleSourceClick(source)"> <span class="title">${source.title || source.url}</span> <span class="asterisk">*</span> <span class="icon"></span> </a> </li> </menu> <footer class="license" innerhtml.bind="\'sources_button.all_under_$license\' | i18n:{license: sources[0].license.id, url: sources[0].license.url} | markdown"></footer> </menu> </button> </template> '},"shared/assistant/resources/elements/sources-button.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>y});var i=s(31601),a=s.n(i),n=s(76314),o=s.n(n),r=s(4417),l=s.n(r),d=new URL(s(83959),s.b),c=new URL(s(48789),s.b),b=new URL(s(2418),s.b),u=new URL(s(21704),s.b),g=o()(a()),h=l()(d),p=l()(c),m=l()(b),f=l()(u);g.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${h}) format("woff2")}.material-symbols-outlined,sources-button .inner .info-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}assistant-chat{--assistant-color: #6046ff;--assistant-color--rgb: 96, 70, 255;--assistant-link-color: #5b8fff;--assistant-link-color--rgb: 91, 143, 255;background:var(--assistant-bg);color:#fff;position:relative;z-index:0}.theme-default assistant-chat{--assistant-bg-color: #15152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-purple-pro assistant-chat{--assistant-bg-color: #16142c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-green-pro assistant-chat{--assistant-bg-color: #14162c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-orange-pro assistant-chat{--assistant-bg-color: #17152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-pro assistant-chat{--assistant-bg-color: #131023;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}assistant-chat .loading-container{height:100%;display:flex;align-items:center;justify-content:center}assistant-chat .layout{display:flex;flex-direction:column;height:100%;gap:11px}assistant-chat .control-buttons{display:flex;position:absolute;right:20px;top:20px}assistant-chat .control-buttons button{background:#423757 center no-repeat;display:block;width:30px;height:30px;border-radius:50%;border:0;z-index:1;transition:background-color .15s}assistant-chat .control-buttons button+button{margin-left:10px}assistant-chat .control-buttons button:hover{background-color:#645b74}assistant-chat .control-buttons .close-button{background-image:url(${p})}assistant-chat .control-buttons .debug-button{background-image:url(${m});background-size:14px}assistant-chat assistant-history{flex:1 1 auto}assistant-chat .input-wrapper{position:relative;z-index:2}assistant-chat assistant-input{flex:0 0 auto}assistant-chat assistant-debug{flex:0 0 auto;margin-top:-20px}assistant-chat .overlay{position:absolute;left:0;top:0;width:100%;height:100%;z-index:1;background:var(--assistant-bg-color);opacity:0;visibility:hidden;transition:opacity .15s,visibility 0s .15s}assistant-chat .overlay.show{opacity:.8;visibility:visible;transition-delay:0s}sources-button,sources-button *{user-select:none;cursor:initial}sources-button button{font-size:11px;line-height:20px;color:rgba(255,255,255,.4);background:rgba(0,0,0,0);padding:0;border:0;outline:none;transition:color .15s}sources-button button:hover,sources-button button.open{color:var(--assistant-link-color)}sources-button .inner{display:inline-flex;align-items:center;font-size:14px;font-style:normal;font-weight:500;line-height:20px;color:rgba(255,255,255,.6);padding:4px;border-radius:6px;gap:4px;transition:all 500ms ease}sources-button .inner,sources-button .inner *{cursor:pointer}sources-button .inner span{display:none;opacity:0}sources-button .inner .info-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:20px}sources-button .inner .info-icon:before{font-family:inherit;content:"article"}sources-button .inner:hover{background:linear-gradient(0deg, rgba(96, 70, 255, 0.1) 0%, rgba(96, 70, 255, 0.1) 100%),rgba(255,255,255,.1);color:color-mix(in srgb, rgba(96, 70, 255, 0.6), rgba(255, 255, 255, 0.8))}sources-button .inner:hover span{display:inline;opacity:1}sources-button .popup{box-shadow:0 0 5px rgba(var(--theme--background--rgb), 0.5);position:relative;position:absolute;left:-7px;bottom:calc(100% + 5px);max-width:100%;background:var(--theme--secondary-background);border:1px solid rgba(255,255,255,.1);border-radius:5px;list-style:none;padding:0;margin:0;text-align:left}sources-button .popup .sources{max-height:200px;overflow-x:hidden;overflow-y:auto;list-style:none;padding:0;margin:0;position:relative}sources-button .popup .sources::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}sources-button .popup .sources::-webkit-scrollbar-thumb:window-inactive,sources-button .popup .sources::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}sources-button .popup .sources::-webkit-scrollbar-thumb:window-inactive:hover,sources-button .popup .sources::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}sources-button .popup .source{font-size:13px;line-height:20px;position:relative;z-index:0}sources-button .popup .source,sources-button .popup .source *{cursor:pointer}sources-button .popup .source+.source{border-top:1px solid rgba(255,255,255,.1)}sources-button .popup .source>a{color:rgba(255,255,255,.6);padding:4px 9px;transition:color .15s,background-color .15s;display:flex;overflow:hidden;min-width:0}sources-button .popup .source>a:hover{background:var(--assistant-color);color:#fff}sources-button .popup .source>a:hover .icon{opacity:1}sources-button .popup .source .title{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:0 1 auto}sources-button .popup .source .asterisk{flex:1 0 auto}sources-button .popup .source .icon{display:inline-block;flex:0 0 auto;width:11px;background:url(${f}) center/contain no-repeat;opacity:.5;margin-left:10px;transition:opacity .15s}sources-button .popup .license{font-size:11px;line-height:16px;padding:4px 9px;border-top:1px solid rgba(255,255,255,.1)}sources-button .popup .license,sources-button .popup .license a{color:rgba(255,255,255,.4)}sources-button .popup .license a{text-decoration:underline;transition:color .15s}sources-button .popup .license a,sources-button .popup .license a *{cursor:pointer}sources-button .popup .license a:hover{color:#fff}sources-button .popup.show-below{bottom:auto;top:calc(100% + 5px)}`,""]);const y=g}}]);
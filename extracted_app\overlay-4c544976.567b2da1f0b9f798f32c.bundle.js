"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[9708],{3972:(e,t,r)=>{r.d(t,{Mz:()=>a,Ro:()=>i,TB:()=>s});var s,u=r(15215),n=r("aurelia-framework"),o=r(10351);!function(e){e[e.Read=1]="Read",e[e.Write=2]="Write",e[e.Execute=4]="Execute"}(s||(s={}));const i={appContainer:"S-1-15-2-1",everyone:"S-1-1-0"};let a=class{#e;constructor(e){this.#e=e}async initialize(){return await this.#e.initialize()}injectDll(e,t,r=0,s=5){return this.#e.run("InjectDll",{processId:e,dllPath:t,stackSize:r,timeout:s})}resolvePath(e){return this.#e.run("ResolvePath",e)}statFile(e){return this.#e.run("StatFile",e)}async getGpuDevices(){return await this.#e.run("GetGpuDevices")}onHotkeyPress(e){return this.#e.subscribe("Hotkey",e)}getRunningProcesses(e){return this.#e.run("GetProcesses",{argsFilter:e})}createRemoteThread(e,t,r,s,u){return this.#e.run("CreateRemoteThread",{processId:e,module:t,export:r,arg:s?s.toString("base64"):null,proxy:u||null})}grantFilePermissions(e,t,r){return this.#e.run("GrantFilePermission",{file:e,sids:t,permissions:r})}waitForInputIdle(e,t){return this.#e.run("WaitForInputIdle",{processId:e,timeout:t})}runLauncherBypass(e){return this.#e.run("RunLauncherBypass",e)}getFileSignatureInfo(e){return this.#e.run("GetFileSignatureInfo",{file:e})}addAvExclusion(e){return this.#e.run("AddAvExclusion",{directory:e})}getUacIcon(e){return this.#e.run("GetUacIcon",{size:e})}mountLauncherProxy(e){return this.#e.run("MountLauncherProxy",e)}unmountLauncherProxy(e){return this.#e.run("UnmountLauncherProxy",{launcher:e})}suspendProcess(e){return this.#e.run("SuspendProcess",{processId:e})}resumeProcess(e){return this.#e.run("ResumeProcess",{processId:e})}getInstalledPackageId(e){return this.#e.run("GetInstalledPackageId",e)}};a=(0,u.Cg)([(0,n.autoinject)(),(0,u.Sn)("design:paramtypes",[o.xx])],a)},10351:(e,t,r)=>{r.d(t,{Vn:()=>o,xx:()=>i});var s=r(15215),u=r("aurelia-framework");class n extends Error{constructor(e,t){super(e),Object.setPrototypeOf(this,n.prototype),this.data=t}}class o extends n{constructor(){super("The user denied required process elevation."),this.doNotReport=!0,Object.setPrototypeOf(this,o.prototype)}}let i=class{};i=(0,s.Cg)([(0,u.autoinject)()],i)},11087:(e,t,r)=>{r.d(t,{J:()=>a});var s,u=r(15215),n=r("aurelia-framework"),o=r(10351);function i(e){switch(e){case s.Registry64:return"64";case s.Registry32:return"32";default:return"default"}}!function(e){e[e.Default=0]="Default",e[e.Registry32=1]="Registry32",e[e.Registry64=2]="Registry64"}(s||(s={}));let a=class{#e;constructor(e){this.#e=e}#t(e){return this.#e.run("QueryRegistry",e)}queryValue(e,t=s.Default){const r=e.split("\\"),u=r.shift(),n=r.pop();return this.#t({type:"value",hive:u,subkey:r.join("\\"),value:n,view:i(t)})}querySubkeyValues(e,t=s.Default){const r=e.split("\\"),u=r.shift();return this.#t({type:"subkey-values",hive:u,subkey:r.join("\\"),view:i(t)})}querySubkeySubkeyValues(e,t=s.Default){const r=e.split("\\"),u=r.shift();return this.#t({type:"subkey-subkey-values",hive:u,subkey:r.join("\\"),view:i(t)})}};a=(0,u.Cg)([(0,n.autoinject)(),(0,u.Sn)("design:paramtypes",[o.xx])],a)},83974:(e,t,r)=>{r.d(t,{M:()=>u}),r(35317),r(69278);var s=r(96610);r(35392),r("services/bugsnag/index"),(0,s.getLogger)("native"),new Map([[1,"Process terminated with exit code 1."],[1073807364,"Process terminated for Windows shutdown."],[3221226091,"Process terminated for Windows logoff."],[4294967295,"Process terminated by task manager."]]);class u extends Error{constructor(e,t=null,r){super(e),this.code=t,this.reported=!1,Object.setPrototypeOf(this,u.prototype),r&&(this.doNotReport=!0)}}}}]);
(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5229],{31493:(e,t,n)=>{var r=n(46264);function i(e,t){var n=new r(e,t);return function(e){return n.convert(e)}}i.BIN="01",i.OCT="01234567",i.DEC="0123456789",i.HEX="0123456789abcdef",e.exports=i},37276:(e,t,n)=>{"use strict";n(18578);var r,i=n(16566),o=[],a=new Promise((function(e){return r=e})),s=i.i9.global,u="undefined"!=typeof process&&!process.browser&&process.version;function c(e,t,n){return n.host=e,n.configModuleId=t||null,t?n.loader.loadModule(t).then((function(e){if(!e.configure)throw new Error("Cannot initialize module '".concat(t,"' without a configure function."));return e.configure(n)})):(n.use.standardConfiguration().developmentLogging(),n.start().then((function(){return n.setRoot()})))}function l(e){var t=a.then((function(t){return e(t())}));return o&&o.push(t),t}(s.document&&"complete"!==s.document.readyState?new Promise((function(e){function t(){s.document.removeEventListener("DOMContentLoaded",t),s.removeEventListener("load",t),e()}s.document.addEventListener("DOMContentLoaded",t),s.addEventListener("load",t)})):Promise.resolve()).then((function(){return i.i9.Loader?Promise.resolve(new i.i9.Loader):Promise.reject("No PLATFORM.Loader is defined and there is neither a System API (ES6) or a Require API (AMD) globally available to load your app.")})).then((function(e){var t=function(t,n){return e.normalize(t,n).then((function(n){return e.map(t,n),n}))};return function(e){if(i.Dp)return Promise.resolve();var t,n=u&&("renderer"===process.type||process.versions["node-webkit"]);if(u&&!n)t="nodejs";else if("undefined"!=typeof window)t="browser";else{if("undefined"==typeof self)throw new Error("Could not determine platform implementation to load.");t="worker"}return e.loadModule("aurelia-pal-"+t).then((function(e){return"nodejs"===t&&!i.Dp&&e.globalize()||e.initialize()}))}(e).then((function(){return e.normalize("aurelia-bootstrapper")})).then((function(e){var n=t("aurelia-framework",e);return Promise.all([n,n.then((function(e){return t("aurelia-dependency-injection",e)})),t("aurelia-router",e),t("aurelia-logging-console",e)])})).then((function(t){var n=t[0];return e.loadModule(n)})).then((function(t){return r((function(){return new t.Aurelia(e)}))}))})).then((function(){for(var e=s.document.querySelectorAll("[aurelia-app],[data-aurelia-app]"),t=0,n=e.length;t<n;++t){var r=e[t],i=r.getAttribute("aurelia-app")||r.getAttribute("data-aurelia-app");l(c.bind(null,r,i))}var a=console.error.bind(console),u=o.map((function(e){return e.catch(a)}));return o=null,Promise.all(u)}))},39671:(e,t,n)=>{"use strict";n.d(t,{a:()=>r,b:()=>i,c:()=>a,d:()=>s,e:()=>o});var r=function(){function e(){}return e.prototype.getDialogContainer=function(){throw new Error("DialogRenderer must implement getDialogContainer().")},e.prototype.showDialog=function(e){throw new Error("DialogRenderer must implement showDialog().")},e.prototype.hideDialog=function(e){throw new Error("DialogRenderer must implement hideDialog().")},e}();function i(e){var t=new Error("Operation cancelled.");return t.wasCancelled=!0,t.output=e,t}function o(e){var t=new Error;return t.wasCancelled=!1,t.output=e,t}function a(e,t,n){return"function"==typeof e[t]?new Promise((function(r){r(e[t](n))})).then((function(e){return null==e||e})):Promise.resolve(!0)}var s=function(){function e(e,t,n,r){this.resolve=n,this.reject=r,this.settings=t,this.renderer=e}return e.prototype.releaseResources=function(e){var t=this;return a(this.controller.viewModel||{},"deactivate",e).then((function(){return t.renderer.hideDialog(t)})).then((function(){t.controller.unbind()}))},e.prototype.cancelOperation=function(){if(!this.settings.rejectOnCancel)return{wasCancelled:!0};throw i()},e.prototype.ok=function(e){return this.close(!0,e)},e.prototype.cancel=function(e){return this.close(!1,e)},e.prototype.error=function(e){var t=this,n=o(e);return this.releaseResources(n).then((function(){t.reject(n)}))},e.prototype.close=function(e,t){var n=this;if(this.closePromise)return this.closePromise;var r={wasCancelled:!e,output:t};return this.closePromise=a(this.controller.viewModel||{},"canDeactivate",r).catch((function(e){return n.closePromise=void 0,Promise.reject(e)})).then((function(o){return o?n.releaseResources(r).then((function(){return!n.settings.rejectOnCancel||e?n.resolve(r):n.reject(i(t)),{wasCancelled:!1}})).catch((function(e){return n.closePromise=void 0,Promise.reject(e)})):(n.closePromise=void 0,n.cancelOperation())}))},e.inject=[r],e}()},46264:e=>{"use strict";function t(e,t){if(!(e&&t&&e.length&&t.length))throw new Error("Bad alphabet");this.srcAlphabet=e,this.dstAlphabet=t}t.prototype.convert=function(e){var t,n,r,i={},o=this.srcAlphabet.length,a=this.dstAlphabet.length,s=e.length,u="string"==typeof e?"":[];if(!this.isValid(e))throw new Error('Number "'+e+'" contains of non-alphabetic digits ('+this.srcAlphabet+")");if(this.srcAlphabet===this.dstAlphabet)return e;for(t=0;t<s;t++)i[t]=this.srcAlphabet.indexOf(e[t]);do{for(n=0,r=0,t=0;t<s;t++)(n=n*o+i[t])>=a?(i[r++]=parseInt(n/a,10),n%=a):r>0&&(i[r++]=0);s=r,u=this.dstAlphabet.slice(n,n+1).concat(u)}while(0!==r);return u},t.prototype.isValid=function(e){for(var t=0;t<e.length;++t)if(-1===this.srcAlphabet.indexOf(e[t]))return!1;return!0},e.exports=t},58482:(e,t,n)=>{"use strict";function r(e){throw new Error("History must implement "+e+"().")}n.d(t,{B:()=>i});var i=function(){function e(){}return e.prototype.activate=function(e){r("activate")},e.prototype.deactivate=function(){r("deactivate")},e.prototype.getAbsoluteRoot=function(){r("getAbsoluteRoot")},e.prototype.navigate=function(e,t){r("navigate")},e.prototype.navigateBack=function(){r("navigateBack")},e.prototype.setTitle=function(e){r("setTitle")},e.prototype.setState=function(e,t){r("setState")},e.prototype.getState=function(e){r("getState")},e.prototype.getHistoryIndex=function(){r("getHistoryIndex")},e.prototype.go=function(e){r("go")},e}()},92126:(e,t,n)=>{"use strict";n.d(t,{Qq:()=>c});var r=n(16566),i={maxRetries:3,interval:1e3,strategy:0},o=function(){function e(e){if(this.retryConfig=Object.assign({},i,e||{}),2===this.retryConfig.strategy&&this.retryConfig.interval<=1e3)throw new Error("An interval less than or equal to 1 second is not allowed when using the exponential retry strategy")}return e.prototype.request=function(e){var t=e;return t.retryConfig||(t.retryConfig=Object.assign({},this.retryConfig),t.retryConfig.counter=0),t.retryConfig.requestClone=e.clone(),e},e.prototype.response=function(e,t){return delete t.retryConfig,e},e.prototype.responseError=function(e,t,n){var i=t.retryConfig,o=i.requestClone;return Promise.resolve().then((function(){if(i.counter<i.maxRetries){var s=!i.doRetry||i.doRetry(e,t);return Promise.resolve(s).then((function(s){if(s)return i.counter++,new Promise((function(e){return r.i9.global.setTimeout(e,function(e){var t=e.interval,n=e.strategy,r=e.minRandomInterval,i=e.maxRandomInterval,o=e.counter;if("function"==typeof n)return e.strategy(o);switch(n){case 0:return a[0](t);case 1:return a[1](o,t);case 2:return a[2](o,t);case 3:return a[3](o,t,r,i);default:throw new Error("Unrecognized retry strategy")}}(i)||0)})).then((function(){var e=o.clone();return"function"==typeof i.beforeRetry?i.beforeRetry(e,n):e})).then((function(e){return n.fetch(Object.assign(e,{retryConfig:i}))}));throw delete t.retryConfig,e}))}throw delete t.retryConfig,e}))},e}(),a=[function(e){return e},function(e,t){return t*e},function(e,t){return 1===e?t:Math.pow(t,e)/1e3},function(e,t,n,r){return void 0===n&&(n=0),void 0===r&&(r=6e4),Math.random()*(r-n)+n}],s=function(){function e(){this.baseUrl="",this.defaults={},this.interceptors=[]}return e.prototype.withBaseUrl=function(e){return this.baseUrl=e,this},e.prototype.withDefaults=function(e){return this.defaults=e,this},e.prototype.withInterceptor=function(e){return this.interceptors.push(e),this},e.prototype.useStandardConfiguration=function(){return Object.assign(this.defaults,{credentials:"same-origin"},this.defaults),this.rejectErrorResponses()},e.prototype.rejectErrorResponses=function(){return this.withInterceptor({response:u})},e.prototype.withRetry=function(e){var t=new o(e);return this.withInterceptor(t)},e}();function u(e){if(!e.ok)throw e;return e}var c=function(){function e(){if(this.activeRequestCount=0,this.isRequesting=!1,this.isConfigured=!1,this.baseUrl="",this.defaults=null,this.interceptors=[],"undefined"==typeof fetch)throw new Error("HttpClient requires a Fetch API implementation, but the current environment doesn't support it. You may need to load a polyfill such as https://github.com/github/fetch")}return e.prototype.configure=function(e){var t;if("object"==typeof e)t={defaults:e};else{if("function"!=typeof e)throw new Error("invalid config");(t=new s).baseUrl=this.baseUrl,t.defaults=Object.assign({},this.defaults),t.interceptors=this.interceptors;var n=e(t);s.prototype.isPrototypeOf(n)&&(t=n)}var r=t.defaults;if(r&&Headers.prototype.isPrototypeOf(r.headers))throw new Error("Default headers must be a plain object.");var i=t.interceptors;if(i&&i.length){if(i.filter((function(e){return o.prototype.isPrototypeOf(e)})).length>1)throw new Error("Only one RetryInterceptor is allowed.");var a=i.findIndex((function(e){return o.prototype.isPrototypeOf(e)}));if(a>=0&&a!==i.length-1)throw new Error("The retry interceptor must be the last interceptor defined.")}return this.baseUrl=t.baseUrl,this.defaults=r,this.interceptors=t.interceptors||[],this.isConfigured=!0,this},e.prototype.fetch=function(e,t){var n=this;!function(e){if(e.isRequesting=!!++e.activeRequestCount,e.isRequesting){var t=r.dv.createCustomEvent("aurelia-fetch-client-request-started",{bubbles:!0,cancelable:!0});setTimeout((function(){return r.dv.dispatchEvent(t)}),1)}}(this);var i=this.buildRequest(e,t);return function(e,t,n){return p(e,t,"request","requestError",n)}(i,this.interceptors,this).then((function(e){var t=null;if(Response.prototype.isPrototypeOf(e))t=Promise.resolve(e);else{if(!Request.prototype.isPrototypeOf(e))throw new Error("An invalid result was returned by the interceptor chain. Expected a Request or Response instance, but got ["+e+"]");i=e,t=fetch(e)}return function(e,t,n,r){return p(e,t,"response","responseError",n,r)}(t,n.interceptors,i,n)})).then((function(e){return Request.prototype.isPrototypeOf(e)?n.fetch(e):e})).then((function(e){return h(n),e}),(function(e){throw h(n),e}))},e.prototype.buildRequest=function(e,t){var n,r,i,o,a,s=this.defaults||{},u=function(e){var t={};for(var n in e||{})e.hasOwnProperty(n)&&(t[n]="function"==typeof e[n]?e[n]():e[n]);return t}(s.headers);if(Request.prototype.isPrototypeOf(e))n=e,i=new Headers(n.headers).get("Content-Type");else{t||(t={});var c=(r=t.body)?{body:r}:null,h=Object.assign({},s,{headers:{}},t,c);i=new Headers(h.headers).get("Content-Type"),n=new Request((o=this.baseUrl,a=e,l.test(a)?a:(o||"")+a),h)}return i||(new Headers(u).has("content-type")?n.headers.set("Content-Type",new Headers(u).get("content-type")):r&&function(e){try{JSON.parse(e)}catch(e){return!1}return!0}(r)&&n.headers.set("Content-Type","application/json")),function(e,t){for(var n in t||{})t.hasOwnProperty(n)&&!e.has(n)&&e.set(n,t[n])}(n.headers,u),r&&Blob.prototype.isPrototypeOf(r)&&r.type&&n.headers.set("Content-Type",r.type),n},e.prototype.get=function(e,t){return this.fetch(e,t)},e.prototype.post=function(e,t,n){return g(this,e,t,n,"POST")},e.prototype.put=function(e,t,n){return g(this,e,t,n,"PUT")},e.prototype.patch=function(e,t,n){return g(this,e,t,n,"PATCH")},e.prototype.delete=function(e,t,n){return g(this,e,t,n,"DELETE")},e}(),l=/^([a-z][a-z0-9+\-.]*:)?\/\//i;function h(e){if(e.isRequesting=!! --e.activeRequestCount,!e.isRequesting){var t=r.dv.createCustomEvent("aurelia-fetch-client-requests-drained",{bubbles:!0,cancelable:!0});setTimeout((function(){return r.dv.dispatchEvent(t)}),1)}}function p(e,t,n,r){for(var i=[],o=4;o<arguments.length;o++)i[o-4]=arguments[o];return(t||[]).reduce((function(e,t){var o=t[n],a=t[r];return e.then(o&&function(e){return o.call.apply(o,[t,e].concat(i))}||f,a&&function(e){return a.call.apply(a,[t,e].concat(i))}||d)}),Promise.resolve(e))}function f(e){return e}function d(e){throw e}function g(e,t,n,r,i){return r||(r={}),r.method=i,n&&(r.body=n),e.fetch(t,r)}},"aurelia-animator-css":(e,t,n)=>{"use strict";n.r(t),n.d(t,{CssAnimator:()=>o,configure:()=>a});var r=n(30960),i=n(16566),o=function(){function e(){this.useAnimationDoneClasses=!1,this.animationEnteredClass="au-entered",this.animationLeftClass="au-left",this.isAnimating=!1,this.verifyKeyframesExist=!0}return e.prototype._addMultipleEventListener=function(e,t,n){for(var r=t.split(" "),i=0,o=r.length;i<o;++i)e.addEventListener(r[i],n,!1)},e.prototype._removeMultipleEventListener=function(e,t,n){for(var r=t.split(" "),i=0,o=r.length;i<o;++i)e.removeEventListener(r[i],n,!1)},e.prototype._getElementAnimationDelay=function(e){var t=i.dv.getComputedStyle(e),n=void 0,r=void 0;if(t.getPropertyValue("animation-delay"))n="animation-delay";else if(t.getPropertyValue("-webkit-animation-delay"))n="-webkit-animation-delay";else{if(!t.getPropertyValue("-moz-animation-delay"))return 0;n="-moz-animation-delay"}return r=t.getPropertyValue(n),1e3*Number(r.replace(/[^\d\.]/g,""))},e.prototype._getElementAnimationNames=function(e){var t=i.dv.getComputedStyle(e),n=void 0;if(t.getPropertyValue("animation-name"))n="";else if(t.getPropertyValue("-webkit-animation-name"))n="-webkit-";else{if(!t.getPropertyValue("-moz-animation-name"))return[];n="-moz-"}var r=t.getPropertyValue(n+"animation-name");return r?r.split(" "):[]},e.prototype._performSingleAnimate=function(e,t){var n=this;return this._triggerDOMEvent(r.pH.animateBegin,e),this.addClass(e,t,!0).then((function(i){return n._triggerDOMEvent(r.pH.animateActive,e),!1!==i&&n.removeClass(e,t,!0).then((function(){n._triggerDOMEvent(r.pH.animateDone,e)}))})).catch((function(){n._triggerDOMEvent(r.pH.animateTimeout,e)}))},e.prototype._triggerDOMEvent=function(e,t){var n=i.dv.createCustomEvent(e,{bubbles:!0,cancelable:!0,detail:t});i.dv.dispatchEvent(n)},e.prototype._animationChangeWithValidKeyframe=function(e,t){var n=e.filter((function(e){return-1===t.indexOf(e)}));if(0===n.length)return!1;if(!this.verifyKeyframesExist)return!0;var r=window.CSSRule.KEYFRAMES_RULE||window.CSSRule.MOZ_KEYFRAMES_RULE||window.CSSRule.WEBKIT_KEYFRAMES_RULE,i=document.styleSheets;try{for(var o=0;o<i.length;++o){var a=null;try{a=i[o].cssRules}catch(e){}if(a)for(var s=0;s<a.length;++s){var u=a[s];if(u.type===r&&-1!==n.indexOf(u.name))return!0}}}catch(e){}return!1},e.prototype.animate=function(e,t){var n=this;return Array.isArray(e)?Promise.all(e.map((function(e){return n._performSingleAnimate(e,t)}))):this._performSingleAnimate(e,t)},e.prototype.runSequence=function(e){var t=this;return this._triggerDOMEvent(r.pH.sequenceBegin,null),e.reduce((function(e,n){return e.then((function(){return t.animate(n.element,n.className)}))}),Promise.resolve(!0)).then((function(){t._triggerDOMEvent(r.pH.sequenceDone,null)}))},e.prototype._stateAnim=function(e,t,n){var i=this,o="au-"+t,a=o+"-active";return new Promise((function(s,u){var c=e.classList;i._triggerDOMEvent(r.pH[t+"Begin"],e),i.useAnimationDoneClasses&&(c.remove(i.animationEnteredClass),c.remove(i.animationLeftClass)),c.add(o);var l=i._getElementAnimationNames(e),h=void 0,p=!1;i._addMultipleEventListener(e,"webkitAnimationStart animationstart",h=function(n){n.target===e&&(p=!0,i.isAnimating=!0,i._triggerDOMEvent(r.pH[t+"Active"],e),n.stopPropagation(),n.target.removeEventListener(n.type,h))},!1);var f=void 0;i._addMultipleEventListener(e,"webkitAnimationEnd animationend",f=function(u){p&&u.target===e&&(u.stopPropagation(),c.remove(a),c.remove(o),u.target.removeEventListener(u.type,f),i.useAnimationDoneClasses&&null!=n&&c.add(n),i.isAnimating=!1,i._triggerDOMEvent(r.pH[t+"Done"],e),s(!0))},!1);var d=e.parentElement,g="data-animator-pending"+t,v=function(){var n=i._getElementAnimationNames(e);i._animationChangeWithValidKeyframe(n,l)||(c.remove(a),c.remove(o),i._removeMultipleEventListener(e,"webkitAnimationEnd animationend",f),i._removeMultipleEventListener(e,"webkitAnimationStart animationstart",h),i._triggerDOMEvent(r.pH[t+"Timeout"],e),s(!1)),d&&d.setAttribute(g,+(d.getAttribute(g)||1)-1)};if(null!=d&&(d.classList.contains("au-stagger")||d.classList.contains("au-stagger-"+t))){var m=+(d.getAttribute(g)||0);d.setAttribute(g,m+1);var y=i._getElementAnimationDelay(d)*m;i._triggerDOMEvent(r.pH.staggerNext,e),setTimeout((function(){c.add(a),v()}),y)}else c.add(a),v()}))},e.prototype.enter=function(e){return this._stateAnim(e,"enter",this.animationEnteredClass)},e.prototype.leave=function(e){return this._stateAnim(e,"leave",this.animationLeftClass)},e.prototype.removeClass=function(e,t){var n=this,i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2];return new Promise((function(o,a){var s=e.classList;if(s.contains(t)||s.contains(t+"-add")){!0!==i&&n._triggerDOMEvent(r.pH.removeClassBegin,e),s.contains(t+"-add")&&(s.remove(t+"-add"),s.add(t)),s.remove(t);var u=n._getElementAnimationNames(e),c=void 0,l=!1;n._addMultipleEventListener(e,"webkitAnimationStart animationstart",c=function(t){t.target===e&&(l=!0,n.isAnimating=!0,!0!==i&&n._triggerDOMEvent(r.pH.removeClassActive,e),t.stopPropagation(),t.target.removeEventListener(t.type,c))},!1);var h=void 0;n._addMultipleEventListener(e,"webkitAnimationEnd animationend",h=function(a){l&&a.target===e&&(e.classList.contains(t+"-remove")||o(!0),a.stopPropagation(),s.remove(t),s.remove(t+"-remove"),a.target.removeEventListener(a.type,h),n.isAnimating=!1,!0!==i&&n._triggerDOMEvent(r.pH.removeClassDone,e),o(!0))},!1),s.add(t+"-remove");var p=n._getElementAnimationNames(e);n._animationChangeWithValidKeyframe(p,u)||(s.remove(t+"-remove"),s.remove(t),n._removeMultipleEventListener(e,"webkitAnimationEnd animationend",h),n._removeMultipleEventListener(e,"webkitAnimationStart animationstart",c),!0!==i&&n._triggerDOMEvent(r.pH.removeClassTimeout,e),o(!1))}else o(!1)}))},e.prototype.addClass=function(e,t){var n=this,i=!(arguments.length<=2||void 0===arguments[2])&&arguments[2];return new Promise((function(o,a){var s=e.classList;!0!==i&&n._triggerDOMEvent(r.pH.addClassBegin,e),s.contains(t+"-remove")&&(s.remove(t+"-remove"),s.remove(t));var u=void 0,c=!1;n._addMultipleEventListener(e,"webkitAnimationStart animationstart",u=function(t){t.target===e&&(c=!0,n.isAnimating=!0,!0!==i&&n._triggerDOMEvent(r.pH.addClassActive,e),t.stopPropagation(),t.target.removeEventListener(t.type,u))},!1);var l=void 0;n._addMultipleEventListener(e,"webkitAnimationEnd animationend",l=function(a){c&&a.target===e&&(e.classList.contains(t+"-add")||o(!0),a.stopPropagation(),s.add(t),s.remove(t+"-add"),a.target.removeEventListener(a.type,l),n.isAnimating=!1,!0!==i&&n._triggerDOMEvent(r.pH.addClassDone,e),o(!0))},!1);var h=n._getElementAnimationNames(e);s.add(t+"-add");var p=n._getElementAnimationNames(e);n._animationChangeWithValidKeyframe(p,h)||(s.remove(t+"-add"),s.add(t),n._removeMultipleEventListener(e,"webkitAnimationEnd animationend",l),n._removeMultipleEventListener(e,"webkitAnimationStart animationstart",u),!0!==i&&n._triggerDOMEvent(r.pH.addClassTimeout,e),o(!1))}))},e}();function a(e,t){var n=e.container.get(o);e.container.get(r.iN).configureAnimator(n),"function"==typeof t&&t(n)}},"aurelia-dialog":(e,t,n)=>{"use strict";n.r(t),n.d(t,{DefaultDialogSettings:()=>s,DialogConfiguration:()=>h,DialogController:()=>r.d,DialogService:()=>f,Renderer:()=>r.a,configure:()=>g,createDialogCancelError:()=>r.b,createDialogCloseError:()=>r.e});var r=n(39671),i=n(16566),o=n(27884),a=n(30960),s=function(){this.lock=!0,this.startingZIndex=1e3,this.centerHorizontalOnly=!1,this.rejectOnCancel=!1,this.ignoreTransitions=!1,this.restoreFocus=function(e){return e.focus()}},u={ux:function(){return n.e(2185).then(n.bind(n,1348)).then((function(e){return e.DialogRenderer}))},native:function(){return n.e(2185).then(n.bind(n,5786)).then((function(e){return e.NativeDialogRenderer}))}},c={"ux-dialog":function(){return n.e(2185).then(n.bind(n,8169)).then((function(e){return e.UxDialog}))},"ux-dialog-header":function(){return n.e(2185).then(n.bind(n,73878)).then((function(e){return e.UxDialogHeader}))},"ux-dialog-body":function(){return n.e(2185).then(n.bind(n,21899)).then((function(e){return e.UxDialogBody}))},"ux-dialog-footer":function(){return n.e(2185).then(n.bind(n,81272)).then((function(e){return e.UxDialogFooter}))},"attach-focus":function(){return n.e(2185).then(n.bind(n,6100)).then((function(e){return e.AttachFocus}))}},l=function(){return n.e(2185).then(n.bind(n,56494)).then((function(e){return e.default}))},h=function(){function e(e,t){var n=this;this.renderer="ux",this.cssText=l,this.resources=[],this.fwConfig=e,this.settings=e.container.get(s),t((function(){return n._apply()}))}return e.prototype._apply=function(){var e=this,t=this.renderer,n=this.cssText;return Promise.all(["string"==typeof t?u[t]():t,n?"string"==typeof n?n:n():""]).then((function(t){var n=t[0],o=t[1],a=e.fwConfig;return a.transient(r.a,n),o&&i.dv.injectStyles(o),Promise.all(e.resources.map((function(e){return c[e]()}))).then((function(e){a.globalResources(e)}))}))},e.prototype.useDefaults=function(){return this.useRenderer("ux").useCSS(l).useStandardResources()},e.prototype.useStandardResources=function(){return Object.keys(c).forEach(this.useResource,this),this},e.prototype.useResource=function(e){return this.resources.push(e),this},e.prototype.useRenderer=function(e,t){return this.renderer=e,t&&Object.assign(this.settings,t),this},e.prototype.useCSS=function(e){return this.cssText=e,this},e}();function p(e,t){return this.then((function(e){return e.wasCancelled?e:e.closeResult})).then(e,t)}var f=function(){function e(e,t,n){this.controllers=[],this.hasOpenDialog=!1,this.hasActiveDialog=!1,this.container=e,this.compositionEngine=t,this.defaultSettings=n}return e.prototype.validateSettings=function(e){if(!e.viewModel&&!e.view)throw new Error('Invalid Dialog Settings. You must provide "viewModel", "view" or both.')},e.prototype.createCompositionContext=function(e,t,n){return{container:e.parent,childContainer:e,bindingContext:null,viewResources:null,model:n.model,view:n.view,viewModel:n.viewModel,viewSlot:new a.eu(t,!0),host:t}},e.prototype.ensureViewModel=function(e){return"object"==typeof e.viewModel?Promise.resolve(e):this.compositionEngine.ensureViewModel(e)},e.prototype._cancelOperation=function(e){if(!e)return{wasCancelled:!0};throw(0,r.b)()},e.prototype.composeAndShowDialog=function(e,t){var n=this;return e.viewModel||(e.bindingContext={controller:t}),this.compositionEngine.compose(e).then((function(e){return t.controller=e,t.renderer.showDialog(t).then((function(){n.controllers.push(t),n.hasActiveDialog=n.hasOpenDialog=!!n.controllers.length}),(function(t){return e.viewModel&&(0,r.c)(e.viewModel,"deactivate"),Promise.reject(t)}))}))},e.prototype.createSettings=function(e){return"boolean"==typeof(e=Object.assign({},this.defaultSettings,e)).keyboard||e.keyboard||(e.keyboard=!e.lock),"boolean"!=typeof e.overlayDismiss&&(e.overlayDismiss=!e.lock),Object.defineProperty(e,"rejectOnCancel",{writable:!1,configurable:!0,enumerable:!0}),this.validateSettings(e),e},e.prototype.open=function(e){var t=this;void 0===e&&(e={});var n,i,o=(e=this.createSettings(e)).childContainer||this.container.createChild(),a=new Promise((function(e,t){n=e,i=t})),s=o.invoke(r.d,[e,n,i]);o.registerInstance(r.d,s),a.then((function(){d(t,s)}),(function(){d(t,s)}));var u,c=this.createCompositionContext(o,s.renderer.getDialogContainer(),s.settings);return(u=this.ensureViewModel(c).then((function(e){return!e.viewModel||(0,r.c)(e.viewModel,"canActivate",s.settings.model)})).then((function(e){return e?t.composeAndShowDialog(c,s).then((function(){return{controller:s,closeResult:a,wasCancelled:!1}})):t._cancelOperation(s.settings.rejectOnCancel)}))).whenClosed=p,u},e.prototype.closeAll=function(){return Promise.all(this.controllers.slice(0).map((function(e){return e.settings.rejectOnCancel?e.cancel().then((function(){return null})).catch((function(t){if(t.wasCancelled)return e;throw t})):e.cancel().then((function(t){return t.wasCancelled?e:null}))}))).then((function(e){return e.filter((function(e){return!!e}))}))},e.inject=[o.mc,a.vW,s],e}();function d(e,t){var n=e.controllers.indexOf(t);-1!==n&&(e.controllers.splice(n,1),e.hasActiveDialog=e.hasOpenDialog=!!e.controllers.length)}function g(e,t){var n=null,r=new h(e,(function(e){n=e}));return"function"==typeof t?t(r):r.useDefaults(),n()}},"aurelia-event-aggregator":(e,t,n)=>{"use strict";n.r(t),n.d(t,{EventAggregator:()=>s,configure:()=>c,includeEventsIn:()=>u});var r=n(96610).getLogger("event-aggregator"),i=function(){function e(e,t){this.messageType=e,this.callback=t}return e.prototype.handle=function(e){e instanceof this.messageType&&this.callback.call(null,e)},e}();function o(e,t,n){try{e(t,n)}catch(e){r.error(e)}}function a(e,t){try{e.handle(t)}catch(e){r.error(e)}}var s=function(){function e(){this.eventLookup={},this.messageHandlers=[]}return e.prototype.publish=function(e,t){var n=void 0,r=void 0;if(!e)throw new Error("Event was invalid.");if("string"==typeof e){if(n=this.eventLookup[e])for(r=(n=n.slice()).length;r--;)o(n[r],t,e)}else for(r=(n=this.messageHandlers.slice()).length;r--;)a(n[r],e)},e.prototype.subscribe=function(e,t){var n=void 0,r=void 0;if(!e)throw new Error("Event channel/type was invalid.");return"string"==typeof e?(n=t,r=this.eventLookup[e]||(this.eventLookup[e]=[])):(n=new i(e,t),r=this.messageHandlers),r.push(n),{dispose:function(){var e=r.indexOf(n);-1!==e&&r.splice(e,1)}}},e.prototype.subscribeOnce=function(e,t){var n=this.subscribe(e,(function(e,r){return n.dispose(),t(e,r)}));return n},e}();function u(e){var t=new s;return e.subscribeOnce=function(e,n){return t.subscribeOnce(e,n)},e.subscribe=function(e,n){return t.subscribe(e,n)},e.publish=function(e,n){t.publish(e,n)},t}function c(e){e.instance(s,u(e.aurelia))}},"aurelia-framework":(e,t,n)=>{"use strict";n.r(t),n.d(t,{AccessKeyed:()=>i.L2,AccessMember:()=>i.nL,AccessScope:()=>i.j3,AccessThis:()=>i.n6,AggregateError:()=>l.Ym,All:()=>r.u8,Animator:()=>a.yU,Assign:()=>i.aE,Aurelia:()=>b,BehaviorInstruction:()=>a.P5,BehaviorPropertyObserver:()=>a.Yj,Binary:()=>i.yI,BindableProperty:()=>a.dZ,Binding:()=>i.Of,BindingBehavior:()=>i.aI,BindingBehaviorResource:()=>i.w7,BindingEngine:()=>i.SY,BindingExpression:()=>i.sv,BindingLanguage:()=>a.Wy,BoundViewFactory:()=>a.gF,Call:()=>i.Je,CallExpression:()=>i.DG,CallFunction:()=>i.bj,CallMember:()=>i.xD,CallScope:()=>i.ro,CheckedObserver:()=>i.mQ,ClassObserver:()=>i.lm,CollectionLengthObserver:()=>i.nM,CompositionEngine:()=>a.vW,CompositionTransaction:()=>a.VP,CompositionTransactionNotifier:()=>a.F4,CompositionTransactionOwnershipToken:()=>a.V8,ComputedExpression:()=>i.OG,Conditional:()=>i.be,Container:()=>r.mc,Controller:()=>a.xI,ConventionalViewStrategy:()=>a.FF,DOM:()=>l.dv,DataAttributeObserver:()=>i.Re,DirtyCheckProperty:()=>i.I9,DirtyChecker:()=>i.UA,ElementConfigResource:()=>a.zH,ElementEvents:()=>a.IM,EventManager:()=>i.EU,EventSubscriber:()=>i.vP,Expression:()=>i.r4,ExpressionCloner:()=>i.mP,ExpressionObserver:()=>i.r5,FEATURE:()=>l.RI,Factory:()=>r.$7,FactoryInvoker:()=>r.dY,FrameworkConfiguration:()=>y,HtmlBehaviorResource:()=>a.sE,InlineViewStrategy:()=>a.i6,InvocationHandler:()=>r.vx,Lazy:()=>r.dF,Listener:()=>i.rZ,ListenerExpression:()=>i.Pc,LiteralArray:()=>i.B4,LiteralObject:()=>i.ns,LiteralPrimitive:()=>i.JE,LiteralString:()=>i.JI,LiteralTemplate:()=>i.Gx,Loader:()=>s.aH,LogManager:()=>h,ModifyCollectionObserver:()=>i.H5,ModuleAnalyzer:()=>a.RH,NameExpression:()=>i.mi,NewInstance:()=>r.uP,NoViewStrategy:()=>a.a7,ObjectObservationAdapter:()=>i.PG,ObserverLocator:()=>i.Zr,Optional:()=>r.Xx,Origin:()=>o.$e,PLATFORM:()=>l.i9,Parent:()=>r.n$,Parser:()=>i.iX,ParserImplementation:()=>i.Qb,PassThroughSlot:()=>a.nJ,PrimitiveObserver:()=>i.y,RelativeViewStrategy:()=>a.v3,ResourceDescription:()=>a.bV,ResourceLoadContext:()=>a.Ud,ResourceModule:()=>a.x4,SVGAnalyzer:()=>i.dJ,SelectValueObserver:()=>i.wr,SetterObserver:()=>i.Kg,ShadowDOM:()=>a.rM,ShadowSlot:()=>a.fp,SingletonRegistration:()=>r.fQ,SlotCustomAttribute:()=>a.kS,StaticViewStrategy:()=>a.Lq,Strategy:()=>r.qY,StrategyResolver:()=>r.Qu,StyleObserver:()=>i.k1,SwapStrategies:()=>a.$e,TargetInstruction:()=>a.Ij,TaskQueue:()=>u.P,TemplateDependency:()=>s.iZ,TemplateRegistryEntry:()=>s.sT,TemplateRegistryViewStrategy:()=>a.Og,TemplatingEngine:()=>a.iN,TransientRegistration:()=>r.Ed,Unary:()=>i._c,Unparser:()=>i.R6,ValueAttributeObserver:()=>i.Qf,ValueConverter:()=>i.i1,ValueConverterResource:()=>i.MC,View:()=>a.Ss,ViewCompileInstruction:()=>a.$,ViewCompiler:()=>a.BY,ViewEngine:()=>a.I6,ViewEngineHooksResource:()=>a.CJ,ViewFactory:()=>a.ev,ViewLocator:()=>a.EO,ViewResources:()=>a.zT,ViewSlot:()=>a.eu,XLinkAttributeObserver:()=>i.v_,_emptyParameters:()=>r.bO,_hyphenate:()=>a.OT,_isAllWhitespace:()=>a.QB,all:()=>r.Q7,animationEvent:()=>a.pH,autoinject:()=>r.l9,behavior:()=>a.h1,bindable:()=>a._t,bindingBehavior:()=>i.gz,bindingMode:()=>i.BG,buildQueryString:()=>c.Go,calcSplices:()=>i.PH,camelCase:()=>i.xQ,child:()=>a.jf,children:()=>a.Y_,cloneExpression:()=>i.Oe,computedFrom:()=>i.Kj,connectBindingToSignal:()=>i.Lo,connectable:()=>i.xM,containerless:()=>a.NY,createComputedObserver:()=>i.as,createOverrideContext:()=>i.iI,createScopeForTest:()=>i.oI,customAttribute:()=>a.U4,customElement:()=>a.EM,dataAttributeAccessor:()=>i.KO,declarePropertyDependencies:()=>i.Bw,decorators:()=>o.rE,delegationStrategy:()=>i.ar,deprecated:()=>o.io,disableConnectQueue:()=>i.Y0,dynamicOptions:()=>a.GO,elementConfig:()=>a.rL,elements:()=>i.Yt,enableConnectQueue:()=>i.XS,enqueueBindingConnect:()=>i.MZ,factory:()=>r.P9,getArrayObserver:()=>i.GF,getChangeRecords:()=>i.ZH,getConnectQueueSize:()=>i.PO,getContextFor:()=>i.L3,getDecoratorDependencies:()=>r.lg,getMapObserver:()=>i.vr,getSetObserver:()=>i.vu,hasDeclaredDependencies:()=>i._u,initializePAL:()=>l.QR,inject:()=>r.WQ,inlineView:()=>a.rr,invokeAsFactory:()=>r.HH,invoker:()=>r.FS,isInitialized:()=>l.Dp,join:()=>c.fj,lazy:()=>r.RZ,mergeSplice:()=>i.xj,metadata:()=>o.yu,mixin:()=>o.co,newInstance:()=>r.UI,noView:()=>a.hl,observable:()=>i.sH,optional:()=>r.lq,parent:()=>r.$t,parseQueryString:()=>c.JO,presentationAttributes:()=>i.Co,presentationElements:()=>i._w,processAttributes:()=>a.HI,processContent:()=>a.fo,projectArraySplices:()=>i.eK,propertyAccessor:()=>i.Zd,protocol:()=>o.TB,registration:()=>r.An,relativeToFile:()=>c.Yc,reset:()=>l.cL,resolver:()=>r.L2,resource:()=>a.Zc,setConnectQueueThreshold:()=>i.XF,signalBindings:()=>i.N9,singleton:()=>r.Gr,sourceContext:()=>i.RH,subscriberCollection:()=>i.rq,targetContext:()=>i.Tw,templateController:()=>a.hL,transient:()=>r.do,useShadowDOM:()=>a.Cu,useView:()=>a.lQ,useViewStrategy:()=>a.oX,validateBehaviorName:()=>a.Cv,valueConverter:()=>i.Yw,view:()=>a.Up,viewEngineHooks:()=>a.gG,viewResources:()=>a.FK,viewStrategy:()=>a.BI});var r=n(27884),i=n(7530),o=n(38468),a=n(30960),s=n(95260),u=n(40896),c=n(83260),l=n(16566),h=n(96610),p=h.getLogger("aurelia"),f=/\.[^/.]+$/;function d(e,t){var n,r=function(){return(n=t.shift())?Promise.resolve(n(e)).then(r):Promise.resolve()};return r()}function g(e){var t=e.match(f);if(t&&t.length>0)return t[0].split(".")[1]}function v(e){if(e.processed)throw new Error("This config instance has already been applied. To load more plugins or global resources, create a new FrameworkConfiguration instance.")}function m(e,t){return"Invalid ".concat(t," [").concat(e,"], ").concat(t," must be specified as functions or relative module IDs.")}var y=function(){function e(e){var t=this;this.aurelia=e,this.container=e.container,this.info=[],this.processed=!1,this.preTasks=[],this.postTasks=[],this.behaviorsToLoad=[],this.configuredPlugins=[],this.resourcesToLoad={},this.preTask((function(){return e.loader.normalize("aurelia-bootstrapper",void 0).then((function(e){return t.bootstrapperName=e}))})),this.postTask((function(){return function(e,t,n){if(0===Object.keys(t).length)return Promise.resolve();var r=e.container.get(a.I6);return Promise.all(Object.keys(t).map((function(n){return a=(r=t[n]).moduleId,u=g(a),i(a)&&(a=o(a)),e.loader.normalize(a,r.relativeTo).then((function(e){return{name:r.moduleId,importId:i(r.moduleId)?s(e,u):e}}));var r,a,u}))).then((function(e){var t=[],i=[];return e.forEach((function(e){t.push(void 0),i.push(e.importId)})),r.importViewResources(i,t,n)}));function i(e){var t=g(e);return!!t&&""!==t&&".js"!==t&&".ts"!==t}function o(e){return e.replace(f,"")}function s(e,t){return o(e)+"."+t}}(e,t.resourcesToLoad,e.resources)}))}return e.prototype.instance=function(e,t){return this.container.registerInstance(e,t),this},e.prototype.singleton=function(e,t){return this.container.registerSingleton(e,t),this},e.prototype.transient=function(e,t){return this.container.registerTransient(e,t),this},e.prototype.preTask=function(e){return v(this),this.preTasks.push(e),this},e.prototype.postTask=function(e){return v(this),this.postTasks.push(e),this},e.prototype.feature=function(e,t){switch(void 0===t&&(t={}),typeof e){case"string":var n=/\/index$/i.test(e),r=n||g(e)?e:e+"/index",i=n?e.slice(0,-6):e;this.info.push({moduleId:r,resourcesRelativeTo:[i,""],config:t});break;case"function":this.info.push({configure:e,config:t||{}});break;default:throw new Error(m(e,"feature"))}return this},e.prototype.globalResources=function(e){var t=this;v(this);for(var n,r=Array.isArray(e)?e:arguments,i=this.resourcesRelativeTo||["",""],o=0,s=r.length;o<s;++o)switch(typeof(n=r[o])){case"string":var u=i[0],l=i[1],h=n;(n.startsWith("./")||n.startsWith("../"))&&""!==u&&(h=(0,c.fj)(u,n)),this.resourcesToLoad[h]={moduleId:h,relativeTo:l};break;case"function":var p=this.aurelia.resources.autoRegister(this.container,n);p instanceof a.sE&&null!==p.elementName&&1===this.behaviorsToLoad.push(p)&&this.postTask((function(){return e=t,Promise.all(e.behaviorsToLoad.map((function(t){return t.load(e.container,t.target)}))).then((function(){e.behaviorsToLoad=null}));var e}));break;default:throw new Error(m(n,"resource"))}return this},e.prototype.globalName=function(e,t){return v(this),this.resourcesToLoad[e]={moduleId:t,relativeTo:""},this},e.prototype.plugin=function(e,t){var n;switch(v(this),typeof e){case"string":n={moduleId:e,resourcesRelativeTo:[e,""],config:t||{}};break;case"function":n={configure:e,config:t||{}};break;default:throw new Error(m(e,"plugin"))}return this.info.push(n),this},e.prototype._addNormalizedPlugin=function(e,t){var n=this,r={moduleId:e,resourcesRelativeTo:[e,""],config:t||{}};return this.info.push(r),this.preTask((function(){var t=[e,n.bootstrapperName];return r.moduleId=e,r.resourcesRelativeTo=t,Promise.resolve()})),this},e.prototype.defaultBindingLanguage=function(){return this._addNormalizedPlugin("aurelia-templating-binding")},e.prototype.router=function(){return this._addNormalizedPlugin("aurelia-templating-router")},e.prototype.history=function(){return this._addNormalizedPlugin("aurelia-history-browser")},e.prototype.defaultResources=function(){return this._addNormalizedPlugin("aurelia-templating-resources")},e.prototype.eventAggregator=function(){return this._addNormalizedPlugin("aurelia-event-aggregator")},e.prototype.basicConfiguration=function(){return this.defaultBindingLanguage().defaultResources().eventAggregator()},e.prototype.standardConfiguration=function(){return this.basicConfiguration().history().router()},e.prototype.developmentLogging=function(e){var t=this,n=e?h.logLevel[e]:void 0;return void 0===n&&(n=h.logLevel.debug),this.preTask((function(){return t.aurelia.loader.normalize("aurelia-logging-console",t.bootstrapperName).then((function(e){return t.aurelia.loader.loadModule(e).then((function(e){h.addAppender(new e.ConsoleAppender),h.setLevel(n)}))}))})),this},e.prototype.apply=function(){var e=this;return this.processed?Promise.resolve():d(this,this.preTasks).then((function(){var t,n=e.aurelia.loader,r=e.info,i=function(){return(t=r.shift())?function(e,t,n){if(p.debug("Loading plugin ".concat(n.moduleId,".")),"string"==typeof n.moduleId){e.resourcesRelativeTo=n.resourcesRelativeTo;var r=n.moduleId;return n.resourcesRelativeTo.length>1?t.normalize(n.moduleId,n.resourcesRelativeTo[1]).then((function(e){return i(e)})):i(r)}if("function"==typeof n.configure)return-1!==e.configuredPlugins.indexOf(n.configure)?Promise.resolve():(e.configuredPlugins.push(n.configure),Promise.resolve(n.configure.call(null,e,n.config||{})));throw new Error(m(n.moduleId||n.configure,"plugin"));function i(r){return t.loadModule(r).then((function(t){if("configure"in t)return-1!==e.configuredPlugins.indexOf(t.configure)?Promise.resolve():Promise.resolve(t.configure(e,n.config||{})).then((function(){e.configuredPlugins.push(t.configure),e.resourcesRelativeTo=null,p.debug("Configured plugin ".concat(n.moduleId,"."))}));e.resourcesRelativeTo=null,p.debug("Loaded plugin ".concat(n.moduleId,"."))}))}}(e,n,t).then(i):(e.processed=!0,e.configuredPlugins=null,Promise.resolve())};return i().then((function(){return d(e,e.postTasks)}))}))},e}(),b=function(){function e(t,n,i){this.loader=t||new l.i9.Loader,this.container=n||(new r.mc).makeGlobal(),this.resources=i||new a.zT,this.use=new y(this),this.logger=h.getLogger("aurelia"),this.hostConfigured=!1,this.host=null,this.use.instance(e,this),this.use.instance(s.aH,this.loader),this.use.instance(a.zT,this.resources)}return e.prototype.start=function(){var e=this;return this._started?this._started:(this.logger.info("Aurelia Starting"),this._started=this.use.apply().then((function(){if(l.dv.addEventListener("submit",(function(e){var t=e.target,n=t.action;"form"!==t.tagName.toLowerCase()||n||e.preventDefault()}),!1),!e.container.hasResolver(a.Wy)){var t="You must configure Aurelia with a BindingLanguage implementation.";throw e.logger.error(t),new Error(t)}e.logger.info("Aurelia Started");var n=l.dv.createCustomEvent("aurelia-started",{bubbles:!0,cancelable:!0});return l.dv.dispatchEvent(n),e})))},e.prototype.enhance=function(e,t){var n=this;return void 0===e&&(e={}),void 0===t&&(t=null),this._configureHost(t||l.dv.querySelectorAll("body")[0]),new Promise((function(t){var r=n.container.get(a.iN);n.root=r.enhance({container:n.container,element:n.host,resources:n.resources,bindingContext:e}),n.root.attached(),n._onAureliaComposed(),t(n)}))},e.prototype.setRoot=function(e,t){var n=this;void 0===e&&(e=null),void 0===t&&(t=null);var r={};this.root&&this.root.viewModel&&this.root.viewModel.router&&(this.root.viewModel.router.deactivate(),this.root.viewModel.router.reset()),this._configureHost(t);var i=this.container.get(a.iN);return delete this.container.get(a.VP).initialComposition,e||(e=this.configModuleId?(0,c.Yc)("./app",this.configModuleId):"app"),r.viewModel=e,r.container=r.childContainer=this.container,r.viewSlot=this.hostSlot,r.host=this.host,i.compose(r).then((function(e){return n.root=e,r.viewSlot.attached(),n._onAureliaComposed(),n}))},e.prototype._configureHost=function(e){if(!this.hostConfigured){if(e=e||this.host,this.host=e&&"string"!=typeof e?e:l.dv.getElementById(e||"applicationHost"),!this.host)throw new Error("No applicationHost was specified.");this.hostConfigured=!0,this.host.aurelia=this,this.hostSlot=new a.eu(this.host,!0),this.hostSlot.transformChildNodesIntoView(),this.container.registerInstance(l.dv.boundary,this.host)}},e.prototype._onAureliaComposed=function(){var e=l.dv.createCustomEvent("aurelia-composed",{bubbles:!0,cancelable:!0});setTimeout((function(){return l.dv.dispatchEvent(e)}),1)},e}()},"aurelia-history-browser":(e,t,n)=>{"use strict";n.r(t),n.d(t,{BrowserHistory:()=>l,DefaultLinkHandler:()=>u,LinkHandler:()=>s,configure:()=>g});var r=n(58482),i=n(16566),o=function(e,t){return o=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])},o(e,t)};function a(e,t){function n(){this.constructor=e}o(e,t),e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)}var s=function(){function e(){}return e.prototype.activate=function(e){},e.prototype.deactivate=function(){},e}(),u=function(e){function t(){var n=e.call(this)||this;return n.handler=function(e){var r=t.getEventInfo(e),i=r.shouldHandleEvent,o=r.href;i&&(e.preventDefault(),n.history.navigate(o))},n}return a(t,e),t.prototype.activate=function(e){e._hasPushState&&(this.history=e,i.dv.addEventListener("click",this.handler,!0))},t.prototype.deactivate=function(){i.dv.removeEventListener("click",this.handler,!0)},t.getEventInfo=function(e){var n=e,r={shouldHandleEvent:!1,href:null,anchor:null},i=t.findClosestAnchor(n.target);if(!i||!t.targetIsThisWindow(i))return r;if(c(i,"download")||c(i,"router-ignore")||c(i,"data-router-ignore"))return r;if(n.altKey||n.ctrlKey||n.metaKey||n.shiftKey)return r;var o=i.getAttribute("href");r.anchor=i,r.href=o;var a=1===n.which,s=o&&!("#"===o.charAt(0)||/^[a-z]+:/i.test(o));return r.shouldHandleEvent=a&&s,r},t.findClosestAnchor=function(e){for(;e;){if("A"===e.tagName)return e;e=e.parentNode}},t.targetIsThisWindow=function(e){var t=e.getAttribute("target"),n=i.i9.global;return!t||t===n.name||"_self"===t},t}(s),c=function(e,t){return e.hasAttribute(t)},l=function(e){function t(t){var n=e.call(this)||this;return n._isActive=!1,n._checkUrlCallback=n._checkUrl.bind(n),n.location=i.i9.location,n.history=i.i9.history,n.linkHandler=t,n}return a(t,e),t.prototype.activate=function(e){if(this._isActive)throw new Error("History has already been activated.");var t=this.history,n=!!e.pushState;this._isActive=!0;var r,o=this.options=Object.assign({},{root:"/"},this.options,e),a=this.root=("/"+o.root+"/").replace(p,"/"),s=this._wantsHashChange=!1!==o.hashChange,u=this._hasPushState=!!(o.pushState&&t&&t.pushState);if(u?r="popstate":s&&(r="hashchange"),i.i9.addEventListener(r,this._checkUrlCallback),s&&n){var c=this.location,l=c.pathname.replace(/[^\/]$/,"$&/")===a;if(!u&&!l){var f=this.fragment=this._getFragment(null,!0);return c.replace(a+c.search+"#"+f),!0}u&&l&&c.hash&&(f=this.fragment=this._getHash().replace(h,""),t.replaceState({},i.dv.title,a+f+c.search))}if(this.fragment||(this.fragment=this._getFragment("")),this.linkHandler.activate(this),!o.silent)return this._loadUrl("")},t.prototype.deactivate=function(){var e=this._checkUrlCallback;i.i9.removeEventListener("popstate",e),i.i9.removeEventListener("hashchange",e),this._isActive=!1,this.linkHandler.deactivate()},t.prototype.getAbsoluteRoot=function(){var e,t=this.location;return t.protocol+"//"+t.hostname+((e=t.port)?":"+e:"")+this.root},t.prototype.navigate=function(e,t){var n=void 0===t?{}:t,r=n.trigger,o=void 0===r||r,a=n.replace,s=void 0!==a&&a,u=this.location;if(e&&d.test(e))return u.href=e,!0;if(!this._isActive)return!1;if(e=this._getFragment(e||""),this.fragment===e&&!s)return!1;this.fragment=e;var c=this.root+e;return""===e&&"/"!==c&&(c=c.slice(0,-1)),this._hasPushState?(c=c.replace("//","/"),this.history[s?"replaceState":"pushState"]({},i.dv.title,c)):this._wantsHashChange?function(e,t,n){if(n){var r=e.href.replace(/(javascript:|#).*$/,"");e.replace(r+"#"+t)}else e.hash="#"+t}(u,e,s):u.assign(c),!o||this._loadUrl(e)},t.prototype.navigateBack=function(){this.history.back()},t.prototype.setTitle=function(e){i.dv.title=e},t.prototype.setState=function(e,t){var n=this.history,r=Object.assign({},n.state),i=this.location,o=i.pathname,a=i.search,s=i.hash;r[e]=t,n.replaceState(r,null,""+o+a+s)},t.prototype.getState=function(e){return Object.assign({},this.history.state)[e]},t.prototype.getHistoryIndex=function(){var e=this.getState("HistoryIndex");return void 0===e&&(e=this.history.length-1,this.setState("HistoryIndex",e)),e},t.prototype.go=function(e){this.history.go(e)},t.prototype._getHash=function(){return this.location.hash.substr(1)},t.prototype._getFragment=function(e,t){var n;if(!e)if(this._hasPushState||!this._wantsHashChange||t){var r=this.location;e=r.pathname+r.search,n=this.root.replace(f,""),e.indexOf(n)||(e=e.substr(n.length))}else e=this._getHash();return"/"+e.replace(h,"")},t.prototype._checkUrl=function(){this._getFragment("")!==this.fragment&&this._loadUrl("")},t.prototype._loadUrl=function(e){var t=this.fragment=this._getFragment(e);return!!this.options.routeHandler&&this.options.routeHandler(t)},t.inject=[s],t}(r.B),h=/^#?\/*|\s+$/g,p=/^\/+|\/+$/g,f=/\/$/,d=/^([a-z][a-z0-9+\-.]*:)?\/\//i;function g(e){var t=e;t.singleton(r.B,l),t.transient(s,u)}}}]);
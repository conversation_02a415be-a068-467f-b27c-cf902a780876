"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8340],{"overlay/resources/elements/house-ad":(e,t,s)=>{s.r(t),s.d(t,{HouseAd:()=>r});var i=s(15215),o=s("aurelia-framework"),a=s(92465);const n={"save-mods":20,"remote-app":20,"game-boosting":10,"exclusive-themes":5};let r=class{#e;bind(){this.#t(),this.#e=(0,a.SO)((()=>this.#t()),3e4)}unbind(){this.#e?.dispose()}#t(){const e=Object.values(n).reduce(((e,t)=>e+t),0),t=Math.random()*e;let s=null,i=0;for(const e of Object.entries(n)){const[o,a]=e;if(i+=a,t<i){s=o;break}}this.currentVariant=s}get currentVariantSnakeCase(){return(this.currentVariant??"").replace(/-/g,"_")}};(0,i.Cg)([o.observable,(0,i.Sn)("design:type",Object)],r.prototype,"currentVariant",void 0),(0,i.Cg)([(0,o.computedFrom)("currentVariant"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],r.prototype,"currentVariantSnakeCase",null),r=(0,i.Cg)([(0,o.autoinject)()],r)},"overlay/resources/elements/house-ad.html":(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});const i='<template class="house-ad ${currentVariant}"> <require from="./house-ad.scss"></require> <require from="../../../shared/resources/elements/pro-badge"></require> <header class="house-ad-header" innerhtml.bind="\'house_ad.remove_ads_with_wemod_pro\' | i18n | markdown"></header> <div class="house-ad-content" if.bind="currentVariant"> <div class="house-ad-title"> <span innerhtml.bind="`house_ad.${currentVariantSnakeCase}_title` | i18n | markdown"></span> <pro-badge class="small"></pro-badge> </div> <div class="house-ad-description" innerhtml.bind="`house_ad.${currentVariantSnakeCase}_description` | i18n | markdown"></div> </div> </template> '},"overlay/resources/elements/house-ad.scss":(e,t,s)=>{s.r(t),s.d(t,{default:()=>f});var i=s(31601),o=s.n(i),a=s(76314),n=s.n(a),r=s(4417),d=s.n(r),l=new URL(s(36869),s.b),h=new URL(s(17230),s.b),p=new URL(s(57376),s.b),c=new URL(s(66871),s.b),m=n()(o()),u=d()(l),g=d()(h),b=d()(p),w=d()(c);m.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.house-ad{display:flex;flex-direction:column;width:300px;box-shadow:0px 2px 8px rgba(0,0,0,.33);border:.5px solid rgba(255,255,255,.25);border-radius:0px 16px 0px 0px;overflow:hidden}.house-ad,.house-ad *{pointer-events:none}.house-ad-header{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;display:flex;flex:0 0 auto;align-items:center;justify-content:center;color:rgba(255,255,255,.8);background:var(--theme--background);padding:6px}.house-ad-header strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;vertical-align:middle;text-decoration:none;margin-left:4px}@media(forced-colors: active){body:not(.override-contrast-mode) .house-ad-header strong{border:1px solid #fff}}.house-ad-content{flex:0 0 auto;width:300px;height:225px;display:block;background:var(--theme--secondary-background);padding:9px 14px;box-sizing:border-box}.house-ad-title{font-size:16px;font-weight:700;line-height:19.5px;color:#fff;margin:2px 0}.house-ad-title strong{color:var(--color--brand-blue)}.house-ad pro-badge{margin-left:4.5px}.house-ad-description{font-size:12px;font-weight:400;line-height:18px;color:rgba(255,255,255,.6);margin-top:5px;width:136px}.house-ad-description strong{color:#fff;font-weight:400}.house-ad.save-mods .house-ad-content{background-image:url(${u});background-position:bottom right;background-size:auto 100%;background-repeat:no-repeat}.house-ad.save-mods .house-ad-content .house-ad-description{max-width:144px}.house-ad.save-mods .house-ad-content .house-ad-title{max-width:165px}.house-ad.remote-app .house-ad-content{background-image:url(${g});background-position:130px 60px;background-size:auto calc(100% - 50px);background-repeat:no-repeat}.house-ad.game-boosting .house-ad-content{background-image:url(${b});background-position:top right;background-size:auto 100%;background-repeat:no-repeat}.house-ad.exclusive-themes .house-ad-content{background-image:url(${w});background-position:top right;background-size:auto 100%;background-repeat:no-repeat}.house-ad.exclusive-themes .house-ad-content .house-ad-title{max-width:206px}`,""]);const f=m},"overlay/resources/elements/maps-window":(e,t,s)=>{s.r(t),s.d(t,{MapsWindow:()=>f});var i=s(15215),o=s("aurelia-framework"),a=s(20770),n=s("overlay/overlay"),r=s(68663),d=s(62914),l=s("dialogs/webview-dialog"),h=s(92465),p=s(20057),c=s(54995),m=s(29944),u=s(6745),g=s(66811),b=s(79522),w=s(42518);let f=class{#s;#i;#o;#a;#n;#r;#d;#l;#h;#p;#c;constructor(e,t,s,i,o,a,n){this.loading=!0,this.#i=t,this.#o=s,this.#a=e,this.#r=i,this.#s=o,this.#n=a,this.#d=n}async attached(){let e;this.loading=!0,this.#l=Date.now(),this.#h=(0,h.Ix)((()=>this.#m("load-timeout",`Timed out trying to load "${this.iframeEl.src}"`)),3e4),this.#c=new h.Vd([this.#h,this.#d.onSetPinned((e=>{this.#p?.execute("set_minimap_mode",{enable:e})}))]);try{e=await this.#o.createWebview({mode:"embed",type:"overlay",locale:this.#i.getEffectiveLocale().baseName,route:"game-map",params:{mapId:this.#s.trainerInfo.maps?.[0]?.id},settings:(0,l.getWebviewSupportedSettings)(this.settings)})}catch(e){return void this.#m("create-webview-error","Error creating map webview in overlay")}this.iframeEl?.contentWindow&&(this.iframeEl.src=e.url,this.#p=new m.Jx(this.iframeEl.contentWindow,e.origin),this.#p.setHandler("map_event",(e=>{switch(e){case"render_success":this.#h?.dispose(),this.loading=!1,this.#u(),this.#p?.execute("set_minimap_mode",{enable:this.#d.pinned});break;case"render_error":this.#n.close(w.so,"maps_error")}return!0})),this.#p.setHandler("event",(e=>this.#g(e))),this.#p.setHandler("error",(e=>this.#b(e))),this.#p.setHandler("router_event",(e=>this.#w(e))),this.#p.setHandler("resize",(()=>null)),this.#p.setHandler("set_current_map_id",(async e=>(this.mapId=e,!0))),this.#p.setHandler("get_player_coordinates",(async()=>await this.#s.getPlayerCoordinates())),this.#p.setHandler("set_player_coordinates",(async e=>await this.#s.setPlayerCoordinates(e))),this.#p.setHandler("get_map_settings",(async e=>{if("string"==typeof e){const t=this.mapSettings[e];return t?JSON.parse(t):null}})),this.#p.setHandler("set_map_settings",(async e=>{"object"==typeof e&&null!==e&&"string"==typeof e.mapId&&"object"==typeof e.settings&&null!==e.settings&&await this.#r.dispatch(b.hL,e.mapId,JSON.stringify(e.settings))})),this.#p.setHandler("get_title_map_settings",(async e=>{if("string"==typeof e){const t=this.titleMapSettings[e];return t?JSON.parse(t):null}})),this.#p.setHandler("set_title_map_settings",(async e=>{"object"==typeof e&&null!==e&&"string"==typeof e.titleId&&"object"==typeof e.settings&&null!==e.settings&&await this.#r.dispatch(b.sR,e.titleId,JSON.stringify(e.settings))})),this.#p.setHandler("open_uri",(async e=>(window.open(e.uri,"_blank"),!0))),this.#c.push(this.#p))}detached(){this.#c?.dispose(),this.#c=null,this.#h=null,this.#p=null}#g(e){return this.#s.event(e.name,e.data,e.dispatch??d.Io),!0}#w(e){return"router:navigation:complete"===e.event&&this.#a.event("overlay_maps_loaded",{duration:Date.now()-this.#l},d.Io),"router:navigation:error"===e.event&&this.#m("remote_maps_router_navigation",`Error navigating to "${e.name}"`),!0}#b(e){return this.#m(e.type,e.message),!0}#m(e,t){this.#s.event("overlay_maps_webview_error",{type:e,message:t},d.Io)}#f(){const e=this.#s.trainerInfo.maps.find((e=>e.id===this.mapId))?.name;this.#n.setCustomWindowTitle("maps",e?this.#i.getValue("overlay_maps_window.$name_map",{name:e}):void 0)}mapIdChanged(){this.#f()}settingsChanged(){this.#f()}async#u(){return!1!==await(this.#p?.execute("set_current_trainer",{titleId:this.#s.trainerInfo.title.id,flags:this.#s.trainerInfo.blueprint.flags}))}};(0,i.Cg)([o.observable,(0,i.Sn)("design:type",String)],f.prototype,"mapId",void 0),f=(0,i.Cg)([(0,o.autoinject)(),(0,c.m6)({selectors:{settings:(0,c.$t)((e=>e.settings)),mapSettings:(0,c.$t)((e=>e.mapSettings)),titleMapSettings:(0,c.$t)((e=>e.titleMapSettings))}}),(0,i.Sn)("design:paramtypes",[d.j0,p.F2,r.x,a.il,u.xr,g.o,n.Overlay])],f)},"overlay/resources/elements/maps-window.html":(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});const i='<template> <require from="./maps-window.scss"></require> <require from="shared/resources/elements/loading-indicator"></require> <loading-indicator if.bind="loading"></loading-indicator> <div class="iframe-wrapper ${loading ? \'is-loading\' : \'\'}"> <iframe frameborder="0" cellspacing="0" class="map-iframe" ref="iframeEl" sandbox="allow-same-origin allow-forms allow-scripts allow-popups allow-pointer-lock" allow="clipboard-read; clipboard-write"></iframe> </div> </template> '},"overlay/resources/elements/maps-window.scss":(e,t,s)=>{s.r(t),s.d(t,{default:()=>r});var i=s(31601),o=s.n(i),a=s(76314),n=s.n(a)()(o());n.push([e.id,"maps-window{display:flex;height:100%;overflow:hidden;container-type:size;align-items:center;justify-content:center;position:relative}maps-window iframe,maps-window .iframe-wrapper{display:block;width:100%;height:100%}maps-window .iframe-wrapper.is-loading{visibility:hidden;position:absolute}",""]);const r=n},"overlay/resources/elements/mods-window":(e,t,s)=>{s.r(t),s.d(t,{ModsWindow:()=>c});var i=s(15215),o=s("aurelia-event-aggregator"),a=s("aurelia-framework"),n=s(92465),r=s(20057),d=s(54995),l=s(6745),h=s("overlay/overlay"),p=s(66811);let c=class{#c;#y;#v;#i;constructor(e,t,s,i,a,n){this.element=e,this.host=t,this.overlay=a,this.windowManager=n,this.ea=new o.EventAggregator,this.variables={},this.#y=!1,this.#i=s,this.ea=i}bind(){this.#y=!0,this.#x()}attached(){}unbind(){this.#c?.dispose(),this.#y=!1}changeValue(e,t,s){this.variables[e]=t,this.host.setTrainerValue({name:e,value:t,cheatId:s,source:6})}markCheatInstructionsRead(e){e.instructions&&!this.host.trainerInfo.cheatStates[e.uuid]?.instructionsRead&&this.ea.subscribeOnce("markCheatInstructionsRead",(e=>{this.host.markCheatInstructionsRead({gameId:this.host.trainerInfo.game.id,cheatId:e.uuid,instructions:e.instructions})}))}dismissModTimerMessage(e){this.host.dismissModTimerMessage({timerType:e})}get isPinDisabled(){return this.element.offsetWidth<480}get isPro(){return!!this.host?.account?.subscription}get canUseSaveCheats(){return this.#v=this.isPro,this.#v}get pinnedModsList(){return this.trainer?this.trainer.blueprint.cheats.filter((e=>this.cheatStates?.[e.uuid]?.pinned)).map((e=>e?.uuid)):[]}async#x(){await this.host.refreshTrainerInfo(),this.#y&&(Object.entries(this.host.trainerInfo.values).forEach((([e,t])=>{this.variables[e]=t})),this.cheatStates=this.host.trainerInfo.cheatStates,this.trainer=this.host.trainerInfo.trainer,this.modTimerMessagesDismissed=this.host.modTimerMessagesDismissed,this.#k(),this.#c=new n.Vd([this.host.onTrainerValueSet((e=>this.variables[e.name]=e.value)),this.host.onCheatStatesSet((e=>this.cheatStates=e)),this.host.onModTimerMessagesDismissed((e=>this.modTimerMessagesDismissed=e)),this.#i.onLocaleChanged((()=>this.#k()))]))}#k(){const e=this.#i.getEffectiveLocale().baseName,t=this.host.trainerInfo.translations[e]??{};this.translations={locale:e,strings:t}}cheatStatesChanged(e,t){const s=Object.values(e||{})?.filter((e=>e?.pinned))||[],i=Object.values(t||{})?.filter((e=>e?.pinned))||[];s.length!==i.length&&this.host.refreshTrainerInfo()}handlePinClick(e,t,s){this.host.pinMod(e,this.trainer.id,t,s)}handleModTimerClick(e,t){const s={gameId:this.trainer.gameId,modId:e.modId,modType:e.modType,type:e.type,cancel:e.cancel,duration:e.duration,timestamp:e.timestamp,start:"loop"===e.type?e?.duration:void 0,end:"loop"===e.type?e?.durationLoop:void 0};this.host.setModTimer(s,t)}get shouldShowTooltip(){const e=this.windowManager.gameWindowSettings,t=!!e&&e.mods?.pinned;return!this.overlay.pinned||t}};(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Object)],c.prototype,"cheatStates",void 0),(0,i.Cg)([(0,a.computedFrom)("element.offsetWidth"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],c.prototype,"isPinDisabled",null),(0,i.Cg)([(0,a.computedFrom)("host.account.subscription"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],c.prototype,"isPro",null),(0,i.Cg)([(0,a.computedFrom)("isPro"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],c.prototype,"canUseSaveCheats",null),(0,i.Cg)([(0,a.computedFrom)("cheatStates","trainer.id"),(0,i.Sn)("design:type",Array),(0,i.Sn)("design:paramtypes",[])],c.prototype,"pinnedModsList",null),(0,i.Cg)([(0,a.computedFrom)("overlay.pinned","windowManager.gameWindowSettings"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],c.prototype,"shouldShowTooltip",null),c=(0,i.Cg)([(0,a.autoinject)(),(0,d.m6)({selectors:{enableSaveCheatsByDefault:(0,d.$t)((e=>e.settings?.enableSaveCheatsByDefault)),settings:(0,d.$t)((e=>e.overlaySettings))}}),(0,i.Sn)("design:paramtypes",[Element,l.xr,r.F2,o.EventAggregator,h.Overlay,p.o])],c)},"overlay/resources/elements/mods-window-actions":(e,t,s)=>{s.r(t),s.d(t,{ModsWindowActions:()=>r});var i=s(15215),o=s("aurelia-framework"),a=s(54995),n=s("overlay/overlay");let r=class{#d;constructor(e){this.#d=e}toggleModsWindowMode(){this.#d.setSetting("modsWindowMode","inputs"===this.settings.modsWindowMode?"hotkeys":"inputs","mods_window")}};r=(0,i.Cg)([(0,o.autoinject)(),(0,a.m6)({selectors:{settings:(0,a.$t)((e=>e.overlaySettings))}}),(0,i.Sn)("design:paramtypes",[n.Overlay])],r)},"overlay/resources/elements/mods-window-actions.html":(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});const i="<template> <button class=\"action ${settings.modsWindowMode !== 'inputs' ? 'toggle-off' : ''}\" click.delegate=\"toggleModsWindowMode()\"> page_info </button> <button class=\"action ${settings.modsWindowMode !== 'hotkeys' ? 'toggle-off' : ''}\" click.delegate=\"toggleModsWindowMode()\"> keyboard_alt </button> </template> "},"overlay/resources/elements/mods-window.html":(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});const i='<template> <require from="./mods-window.scss"></require> <require from="../../../shared/cheats/resources/elements/mods-list"></require> <require from="shared/resources/elements/pro-badge"></require> <mods-list data-tooltip-delegate-for="pro-precision-mod-tooltip" is-overlay.bind="true" variables.bind="variables" translations.bind="translations" trainer.bind="host.trainerInfo.trainer" value-changed.call="changeValue(name, value, cheatId)" cheat-instructions-read.call="markCheatInstructionsRead(cheat)" dismiss-mod-timer-message.call="dismissModTimerMessage(timerType)" handle-mod-timer.call="handleModTimerClick(data, firstCall)" handle-pin-mod.call="handlePinClick(gameId, cheat, pin)" pinned-mods-list.bind="pinnedModsList" mod-timer-messages-dismissed.bind="modTimerMessagesDismissed" is-save-cheats-enabled.bind="saveCheatsEnabled" show-hotkeys.bind="settings.modsWindowMode === \'hotkeys\'" show-inputs.bind="settings.modsWindowMode === \'inputs\'" cheat-states.bind="cheatStates" use-range-input-overlay.bind="true" dialog-disabled.bind="true" hotkey-edit-disabled.bind="true" pin-disabled.bind="isPinDisabled" ea.bind="ea" is-pro.bind="isPro" enabled.bind="true"></mods-list> <wm-tooltip if.bind="!isPro" tooltip-id="pro-precision-mod-tooltip" use-delegate="true" placement="bottom" style-variant="no-arrow-small"> <section slot="content" class="pro-precision-mods-tooltip-small-tooltip-content" show.bind="shouldShowTooltip"> <h3>${\'precision_mods_tooltip.unlock_precision_controls\' | i18n | markdown}</h3> <pro-badge class="small pro-badge"></pro-badge> </section> </wm-tooltip> </template> '},"overlay/resources/elements/mods-window.scss":(e,t,s)=>{s.r(t),s.d(t,{default:()=>c});var i=s(31601),o=s.n(i),a=s(76314),n=s.n(a),r=s(4417),d=s.n(r),l=new URL(s(83959),s.b),h=n()(o()),p=d()(l);h.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}mods-window{display:block;height:100%;overflow-x:hidden;overflow-y:overlay;padding:12px;container-type:size}mods-window::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}mods-window::-webkit-scrollbar-thumb:window-inactive,mods-window::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}mods-window::-webkit-scrollbar-thumb:window-inactive:hover,mods-window::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}mods-window::-webkit-scrollbar-button:single-button:vertical:decrement{height:20px}mods-window::-webkit-scrollbar-button:single-button:vertical:increment{height:30px}mods-window::-webkit-scrollbar{background:rgba(0,0,0,0)}mods-window:not(:hover)::-webkit-scrollbar-thumb{background-color:rgba(0,0,0,0)}mods-window mods-list{--mods-category-header-bg-opacity: 0.6}.pro-precision-mods-tooltip-small-tooltip-content{font-feature-settings:"liga" off,"clig" off;font-weight:700;max-width:300px;padding:8px;display:flex;align-items:center;gap:6px}.pro-precision-mods-tooltip-small-tooltip-content h3{font-size:12px;line-height:16px;margin:0}`,""]);const c=h}}]);
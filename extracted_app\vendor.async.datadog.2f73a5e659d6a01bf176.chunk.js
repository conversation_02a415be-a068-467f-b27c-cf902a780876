"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2043],{50606:(t,e,n)=>{n.r(e),n.d(e,{startRecording:()=>St});var o=n(7328),r=n(16059),s=n(55407),i=n(12452),a=n(80320),c=n(52999),u=n(80886),l=n(75248),d=n(84411);const p=new WeakMap;function f(t){return p.has(t)}function h(t){return p.get(t)}function m(t,e){const n=t.tagName,o=t.value;if((0,d.Ie)(t,e)){const e=t.type;if("INPUT"===n&&("button"===e||"submit"===e||"reset"===e))return o;if(!o||"OPTION"===n)return;return d.o}return"OPTION"===n||"SELECT"===n?t.value:"INPUT"===n||"TEXTAREA"===n?o:void 0}const g=/url\((?:(')([^']*)'|(")([^"]*)"|([^)]*))\)/gm,y=/^[A-Za-z]+:|^\/\//,v=/^data:.*,/i;const w=/[^a-z1-6-_]/;function S(t){const e=t.toLowerCase().trim();return w.test(e)?"div":e}function E(t,e){return`data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='${t}' height='${e}' style='background-color:silver'%3E%3C/svg%3E`}const N={FullSnapshot:2,IncrementalSnapshot:3,Meta:4,Focus:6,ViewEnd:7,VisualViewport:8,FrustrationRecord:9},T={Document:0,DocumentType:1,Element:2,Text:3,CDATA:4,DocumentFragment:11},b={Mutation:0,MouseMove:1,MouseInteraction:2,Scroll:3,ViewportResize:4,Input:5,TouchMove:6,MediaInteraction:7,StyleSheetRule:8},x={MouseUp:0,MouseDown:1,Click:2,ContextMenu:3,DblClick:4,Focus:5,Blur:6,TouchStart:7,TouchEnd:9},R={Play:0,Pause:1};function M(t){if(void 0!==t&&0!==t.length)return t.map((t=>{const e=t.cssRules||t.rules;return{cssRules:Array.from(e,(t=>t.cssText)),disabled:t.disabled||void 0,media:t.media.length>0?Array.from(t.media):void 0}}))}var C=n(14451);function I(t,e,n,o){if(e===d.$m.HIDDEN)return null;const r=t.getAttribute(n);if(e===d.$m.MASK&&n!==d.NT&&!d.yF.includes(n)&&n!==o.actionNameAttribute){const e=t.tagName;switch(n){case"title":case"alt":case"placeholder":return d.o}if("IMG"===e&&("src"===n||"srcset"===n)){const e=t;if(e.naturalWidth>0)return E(e.naturalWidth,e.naturalHeight);const{width:n,height:o}=t.getBoundingClientRect();return n>0||o>0?E(n,o):d.eT}if("SOURCE"===e&&("src"===n||"srcset"===n))return d.eT;if("A"===e&&"href"===n)return d.o;if(r&&n.startsWith("data-"))return d.o;if("IFRAME"===e&&"srcdoc"===n)return d.o}return r&&"string"==typeof r&&(0,d.YR)(r)?(0,d.jK)(r):r}function L(t){if(!t)return null;let e;try{e=t.rules||t.cssRules}catch(t){}return e?(n=Array.from(e,(0,C.nr)()?D:_).join(""),o=t.href,n.replace(g,((t,e,n,r,s,i)=>{const a=n||s||i;if(!o||!a||y.test(a)||v.test(a))return t;const c=e||r||"";return`url(${c}${function(t,e){try{return(0,l.c$)(t,e).href}catch(e){return t}}(a,o)}${c})`}))):null;var n,o}function D(t){if(function(t){return"selectorText"in t}(t)&&t.selectorText.includes(":")){const e=/(\[[\w-]+[^\\])(:[^\]]+\])/g;return t.cssText.replace(e,"$1\\$2")}return _(t)}function _(t){return function(t){return"styleSheet"in t}(t)&&L(t.styleSheet)||t.cssText}function O(t,e){const n=function(t,e){switch(t.nodeType){case t.DOCUMENT_NODE:return function(t,e){return{type:T.Document,childNodes:V(t,e),adoptedStyleSheets:M(t.adoptedStyleSheets)}}(t,e);case t.DOCUMENT_FRAGMENT_NODE:return function(t,e){const n=(0,d.p_)(t);return n&&e.serializationContext.shadowRootsController.addShadowRoot(t),{type:T.DocumentFragment,childNodes:V(t,e),isShadowRoot:n,adoptedStyleSheets:n?M(t.adoptedStyleSheets):void 0}}(t,e);case t.DOCUMENT_TYPE_NODE:return n=t,{type:T.DocumentType,name:n.name,publicId:n.publicId,systemId:n.systemId};case t.ELEMENT_NODE:return function(t,e){const n=S(t.tagName),o="svg"===(s=t).tagName||s instanceof SVGElement||void 0,r=(0,d.jR)((0,d.dT)(t),e.parentNodePrivacyLevel);var s;if(r===d.$m.HIDDEN){const{width:e,height:r}=t.getBoundingClientRect();return{type:T.Element,tagName:n,attributes:{rr_width:`${e}px`,rr_height:`${r}px`,[d.NT]:d.Wd},childNodes:[],isSVG:o}}if(r===d.$m.IGNORE)return;const i=function(t,e,n){if(e===d.$m.HIDDEN)return{};const o={},r=S(t.tagName),s=t.ownerDocument;for(let r=0;r<t.attributes.length;r+=1){const s=t.attributes.item(r).name,i=I(t,e,s,n.configuration);null!==i&&(o[s]=i)}if(t.value&&("textarea"===r||"select"===r||"option"===r||"input"===r)){const n=m(t,e);void 0!==n&&(o.value=n)}if("option"===r&&e===d.$m.ALLOW){const e=t;e.selected&&(o.selected=e.selected)}if("link"===r){const e=Array.from(s.styleSheets).find((e=>e.href===t.href)),n=L(e);n&&e&&(o._cssText=n)}if("style"===r&&t.sheet){const e=L(t.sheet);e&&(o._cssText=e)}const i=t;if("input"!==r||"radio"!==i.type&&"checkbox"!==i.type||(e===d.$m.ALLOW?o.checked=!!i.checked:(0,d.Ie)(i,e)&&delete o.checked),"audio"===r||"video"===r){const e=t;o.rr_mediaState=e.paused?"paused":"played"}let a,c;const u=n.serializationContext;switch(u.status){case 0:a=Math.round(t.scrollTop),c=Math.round(t.scrollLeft),(a||c)&&u.elementsScrollPositions.set(t,{scrollTop:a,scrollLeft:c});break;case 1:u.elementsScrollPositions.has(t)&&({scrollTop:a,scrollLeft:c}=u.elementsScrollPositions.get(t))}return c&&(o.rr_scrollLeft=c),a&&(o.rr_scrollTop=a),o}(t,r,e);let a=[];if((0,d.wR)(t)&&"style"!==n){let o;o=e.parentNodePrivacyLevel===r&&e.ignoreWhiteSpace===("head"===n)?e:{...e,parentNodePrivacyLevel:r,ignoreWhiteSpace:"head"===n},a=V(t,o)}return{type:T.Element,tagName:n,attributes:i,childNodes:a,isSVG:o}}(t,e);case t.TEXT_NODE:return function(t,e){const n=(0,d.rf)(t,e.ignoreWhiteSpace||!1,e.parentNodePrivacyLevel);if(void 0!==n)return{type:T.Text,textContent:n}}(t,e);case t.CDATA_SECTION_NODE:return{type:T.CDATA,textContent:""}}var n}(t,e);if(!n)return null;const o=h(t)||P++,r=n;return r.id=o,function(t,e){p.set(t,e)}(t,o),e.serializedNodeIds&&e.serializedNodeIds.add(o),r}let P=1;function V(t,e){const n=[];return(0,d.wI)(t,(t=>{const o=O(t,e);o&&n.push(o)})),n}function k(t,e,n){return O(t,{serializationContext:n,parentNodePrivacyLevel:e.defaultPrivacyLevel,configuration:e})}function $(t){return Boolean(t.changedTouches)}function A(t){return!0===t.composed&&(0,d.XS)(t.target)?t.composedPath()[0]:t.target}const H=(t,e)=>{const n=window.visualViewport,o={layoutViewportX:t,layoutViewportY:e,visualViewportX:t,visualViewportY:e};return n?(function(t){return Math.abs(t.pageTop-t.offsetTop-window.scrollY)>25||Math.abs(t.pageLeft-t.offsetLeft-window.scrollX)>25}(n)?(o.layoutViewportX=Math.round(t+n.offsetLeft),o.layoutViewportY=Math.round(e+n.offsetTop)):(o.visualViewportX=Math.round(t-n.offsetLeft),o.visualViewportY=Math.round(e-n.offsetTop)),o):o},F=t=>({scale:t.scale,offsetLeft:t.offsetLeft,offsetTop:t.offsetTop,pageLeft:t.pageLeft,pageTop:t.pageTop,height:t.height,width:t.width});var z=n(29336);function B(t,e){return{data:{source:t,...e},type:N.IncrementalSnapshot,timestamp:(0,z.nx)()}}const W=50;function G(t,e){const{throttled:n,cancel:o}=(0,c.n)((t=>{const n=A(t);if(f(n)){const o=J(t);if(!o)return;const r={id:h(n),timeOffset:0,x:o.x,y:o.y};e(B($(t)?b.TouchMove:b.MouseMove,{positions:[r]}))}}),W,{trailing:!1}),{stop:r}=(0,u.l)(t,document,["mousemove","touchmove"],n,{capture:!0,passive:!0});return{stop:()=>{r(),o()}}}function J(t){let{clientX:e,clientY:n}=$(t)?t.changedTouches[0]:t;if(window.visualViewport){const{visualViewportX:t,visualViewportY:o}=H(e,n);e=t,n=o}if(Number.isFinite(e)&&Number.isFinite(n))return{x:e,y:n};t.isTrusted&&(0,o.A2)("mouse/touch event without x/y")}const U={pointerup:x.MouseUp,mousedown:x.MouseDown,click:x.Click,contextmenu:x.ContextMenu,dblclick:x.DblClick,focus:x.Focus,blur:x.Blur,touchstart:x.TouchStart,touchend:x.TouchEnd};function Y(t,e,n){return(0,u.l)(t,document,Object.keys(U),(o=>{const r=A(o);if((0,d.PJ)(r,t.defaultPrivacyLevel)===d.$m.HIDDEN||!f(r))return;const s=h(r),i=U[o.type];let a;if(i!==x.Blur&&i!==x.Focus){const t=J(o);if(!t)return;a={id:s,type:i,x:t.x,y:t.y}}else a={id:s,type:i};const c={id:n.getIdForEvent(o),...B(b.MouseInteraction,a)};e(c)}),{capture:!0,passive:!0})}const X=100;function j(t,e,n,o=document){const{throttled:r,cancel:s}=(0,c.n)((o=>{const r=A(o);if(!r||(0,d.PJ)(r,t.defaultPrivacyLevel)===d.$m.HIDDEN||!f(r))return;const s=h(r),i=r===document?{scrollTop:(0,d.zL)(),scrollLeft:(0,d.Gn)()}:{scrollTop:Math.round(r.scrollTop),scrollLeft:Math.round(r.scrollLeft)};n.set(r,i),e(B(b.Scroll,{id:s,x:i.scrollLeft,y:i.scrollTop}))}),X),{stop:i}=(0,u.q)(t,o,"scroll",r,{capture:!0,passive:!0});return{stop:()=>{i(),s()}}}const K=200;function q(t,e){const n=(0,d.g1)(t).subscribe((t=>{e(B(b.ViewportResize,t))}));return{stop:()=>{n.unsubscribe()}}}function Z(t,e){const n=window.visualViewport;if(!n)return{stop:c.l};const{throttled:o,cancel:r}=(0,c.n)((()=>{e({data:F(n),type:N.VisualViewport,timestamp:(0,z.nx)()})}),K,{trailing:!1}),{stop:s}=(0,u.l)(t,n,["resize","scroll"],o,{capture:!0,passive:!0});return{stop:()=>{s(),r()}}}function Q(t,e){return(0,u.l)(t,document,["play","pause"],(n=>{const o=A(n);o&&(0,d.PJ)(o,t.defaultPrivacyLevel)!==d.$m.HIDDEN&&f(o)&&e(B(b.MediaInteraction,{id:h(o),type:"play"===n.type?R.Play:R.Pause}))}),{capture:!0,passive:!0})}var tt=n(23054);function et(t){function e(t,e){t&&f(t.ownerNode)&&e(h(t.ownerNode))}const n=[(0,tt.H)(CSSStyleSheet.prototype,"insertRule",(({target:n,parameters:[o,r]})=>{e(n,(e=>t(B(b.StyleSheetRule,{id:e,adds:[{rule:o,index:r}]}))))})),(0,tt.H)(CSSStyleSheet.prototype,"deleteRule",(({target:n,parameters:[o]})=>{e(n,(e=>t(B(b.StyleSheetRule,{id:e,removes:[{index:o}]}))))}))];function o(o){n.push((0,tt.H)(o.prototype,"insertRule",(({target:n,parameters:[o,r]})=>{e(n.parentStyleSheet,(e=>{const s=nt(n);s&&(s.push(r||0),t(B(b.StyleSheetRule,{id:e,adds:[{rule:o,index:s}]})))}))})),(0,tt.H)(o.prototype,"deleteRule",(({target:n,parameters:[o]})=>{e(n.parentStyleSheet,(e=>{const r=nt(n);r&&(r.push(o),t(B(b.StyleSheetRule,{id:e,removes:[{index:r}]})))}))})))}return"undefined"!=typeof CSSGroupingRule?o(CSSGroupingRule):(o(CSSMediaRule),o(CSSSupportsRule)),{stop:()=>{n.forEach((t=>t.stop()))}}}function nt(t){const e=[];let n=t;for(;n.parentRule;){const t=Array.from(n.parentRule.cssRules).indexOf(n);e.unshift(t),n=n.parentRule}if(!n.parentStyleSheet)return;const o=Array.from(n.parentStyleSheet.cssRules).indexOf(n);return e.unshift(o),e}function ot(t,e){return(0,u.l)(t,window,["focus","blur"],(()=>{e({data:{has_focus:document.hasFocus()},type:N.Focus,timestamp:(0,z.nx)()})}))}function rt(t,e,n){const o=t.subscribe(12,(t=>{var o,r;"action"===t.rawRumEvent.type&&"click"===t.rawRumEvent.action.type&&(null===(r=null===(o=t.rawRumEvent.action.frustration)||void 0===o?void 0:o.type)||void 0===r?void 0:r.length)&&"events"in t.domainContext&&t.domainContext.events&&t.domainContext.events.length&&e({timestamp:t.rawRumEvent.date,type:N.FrustrationRecord,data:{frustrationTypes:t.rawRumEvent.action.frustration.type,recordIds:t.domainContext.events.map((t=>n.getIdForEvent(t)))}})}));return{stop:()=>{o.unsubscribe()}}}function st(t,e){const n=t.subscribe(5,(()=>{e({timestamp:(0,z.nx)(),type:N.ViewEnd})}));return{stop:()=>{n.unsubscribe()}}}function it(t,e,n=document){const o=t.defaultPrivacyLevel,r=new WeakMap,s=n!==document,{stop:i}=(0,u.l)(t,n,s?["change"]:["input","change"],(t=>{const e=A(t);(e instanceof HTMLInputElement||e instanceof HTMLTextAreaElement||e instanceof HTMLSelectElement)&&l(e)}),{capture:!0,passive:!0});let a;if(s)a=c.l;else{const t=[(0,tt.t)(HTMLInputElement.prototype,"value",l),(0,tt.t)(HTMLInputElement.prototype,"checked",l),(0,tt.t)(HTMLSelectElement.prototype,"value",l),(0,tt.t)(HTMLTextAreaElement.prototype,"value",l),(0,tt.t)(HTMLSelectElement.prototype,"selectedIndex",l)];a=()=>{t.forEach((t=>t.stop()))}}return{stop:()=>{a(),i()}};function l(t){const e=(0,d.PJ)(t,o);if(e===d.$m.HIDDEN)return;const n=t.type;let r;if("radio"===n||"checkbox"===n){if((0,d.Ie)(t,e))return;r={isChecked:t.checked}}else{const n=m(t,e);if(void 0===n)return;r={text:n}}p(t,r);const s=t.name;"radio"===n&&s&&t.checked&&document.querySelectorAll(`input[type="radio"][name="${CSS.escape(s)}"]`).forEach((e=>{e!==t&&p(e,{isChecked:!1})}))}function p(t,n){if(!f(t))return;const o=r.get(t);o&&o.text===n.text&&o.isChecked===n.isChecked||(r.set(t,n),e(B(b.Input,{id:h(t),...n})))}}var at=n(42182),ct=n(34144);const ut=100,lt=16;function dt(t,e,n,o){const r=(0,d.W3)();if(!r)return{stop:c.l,flush:c.l};const s=function(){let o=c.l,r=[];function s(){o(),function(t,e,n,o){const r=new Map;t.filter((t=>"childList"===t.type)).forEach((t=>{t.removedNodes.forEach((t=>{pt(t,o.removeShadowRoot)}))}));const s=t.filter((t=>t.target.isConnected&&function(t){let e=t;for(;e;){if(!f(e)&&!(0,d.p_)(e))return!1;e=(0,d.$4)(e)}return!0}(t.target)&&(0,d.PJ)(t.target,n.defaultPrivacyLevel,r)!==d.$m.HIDDEN)),{adds:i,removes:a,hasBeenSerialized:c}=function(t,e,n,o){const r=new Set,s=new Map;for(const e of t)e.addedNodes.forEach((t=>{r.add(t)})),e.removedNodes.forEach((t=>{r.has(t)||s.set(t,e.target),r.delete(t)}));const i=Array.from(r);i.sort(((t,e)=>{const n=t.compareDocumentPosition(e);return n&Node.DOCUMENT_POSITION_CONTAINED_BY?-1:n&Node.DOCUMENT_POSITION_CONTAINS||n&Node.DOCUMENT_POSITION_FOLLOWING?1:n&Node.DOCUMENT_POSITION_PRECEDING?-1:0}));const a=new Set,c=[];for(const t of i){if(l(t))continue;const r=(0,d.PJ)(t.parentNode,e.defaultPrivacyLevel,o);if(r===d.$m.HIDDEN||r===d.$m.IGNORE)continue;const s=O(t,{serializedNodeIds:a,parentNodePrivacyLevel:r,serializationContext:{status:2,shadowRootsController:n},configuration:e});if(!s)continue;const i=(0,d.$4)(t);c.push({nextId:p(t),parentId:h(i),node:s})}const u=[];return s.forEach(((t,e)=>{f(e)&&u.push({parentId:h(t),id:h(e)})})),{adds:c,removes:u,hasBeenSerialized:l};function l(t){return f(t)&&a.has(h(t))}function p(t){let e=t.nextSibling;for(;e;){if(f(e))return h(e);e=e.nextSibling}return null}}(s.filter((t=>"childList"===t.type)),n,o,r),u=function(t,e,n){var o;const r=[],s=new Set,i=t.filter((t=>!s.has(t.target)&&(s.add(t.target),!0)));for(const t of i){if(t.target.textContent===t.oldValue)continue;const s=(0,d.PJ)((0,d.$4)(t.target),e.defaultPrivacyLevel,n);s!==d.$m.HIDDEN&&s!==d.$m.IGNORE&&r.push({id:h(t.target),value:null!==(o=(0,d.rf)(t.target,!1,s))&&void 0!==o?o:null})}return r}(s.filter((t=>"characterData"===t.type&&!c(t.target))),n,r),l=function(t,e,n){const o=[],r=new Map,s=t.filter((t=>{const e=r.get(t.target);return!(e&&e.has(t.attributeName)||(e?e.add(t.attributeName):r.set(t.target,new Set([t.attributeName])),0))})),i=new Map;for(const t of s){if(t.target.getAttribute(t.attributeName)===t.oldValue)continue;const r=(0,d.PJ)(t.target,e.defaultPrivacyLevel,n),s=I(t.target,r,t.attributeName,e);let a;if("value"===t.attributeName){const e=m(t.target,r);if(void 0===e)continue;a=e}else a="string"==typeof s?s:null;let c=i.get(t.target);c||(c={id:h(t.target),attributes:{}},o.push(c),i.set(t.target,c)),c.attributes[t.attributeName]=a}return o}(s.filter((t=>"attributes"===t.type&&!c(t.target))),n,r);(u.length||l.length||a.length||i.length)&&e(B(b.Mutation,{adds:i,removes:a,texts:u,attributes:l}))}(r.concat(i.takeRecords()),t,e,n),r=[]}const{throttled:a,cancel:u}=(0,c.n)(s,lt,{leading:!1});return{addMutations:t=>{0===r.length&&(o=(0,ct.BB)(a,{timeout:ut})),r.push(...t)},flush:s,stop:()=>{o(),u()}}}(),i=new r((0,at.dm)(s.addMutations));return i.observe(o,{attributeOldValue:!0,attributes:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),{stop:()=>{i.disconnect(),s.stop()},flush:()=>{s.flush()}}}function pt(t,e){(0,d.XS)(t)&&e(t.shadowRoot),(0,d.wI)(t,(t=>pt(t,e)))}const ft=(t,e,n)=>{const o=new Map,r={addShadowRoot:s=>{if(o.has(s))return;const i=dt(e,t,r,s),a=it(t,e,s),c=j(t,e,n,s);o.set(s,{flush:()=>i.flush(),stop:()=>{i.stop(),a.stop(),c.stop()}})},removeShadowRoot:t=>{const e=o.get(t);e&&(e.stop(),o.delete(t))},stop:()=>{o.forEach((({stop:t})=>t()))},flush:()=>{o.forEach((({flush:t})=>t()))}};return r};var ht=n(39377),mt=n(93001);function gt({context:t,creationReason:e,encoder:n}){let o=0;const r=t.view.id,s={start:1/0,end:-1/0,creation_reason:e,records_count:0,has_full_snapshot:!1,index_in_view:a.K_(r),source:"browser",...t};return a.H5(r),{addRecord:function(t,e){s.start=Math.min(s.start,t.timestamp),s.end=Math.max(s.end,t.timestamp),s.records_count+=1,s.has_full_snapshot||(s.has_full_snapshot=t.type===N.FullSnapshot);const r=n.isEmpty?'{"records":[':",";n.write(r+JSON.stringify(t),(t=>{o+=t,e(o)}))},flush:function(t){if(n.isEmpty)throw new Error("Empty segment flushed");n.write(`],${JSON.stringify(s).slice(1)}\n`),n.finish((e=>{a.L7(s.view.id,e.rawBytesCount),t(s,e)}))}}}const yt=5*z.OY;let vt=6e4;function wt(t,e,n,o,r,s){return function(t,r,s,i){let a={status:0,nextSegmentCreationReason:"init"};const{unsubscribe:c}=t.subscribe(2,(()=>{l("view_change")})),{unsubscribe:u}=t.subscribe(11,(t=>{l(t.reason)}));function l(t){1===a.status&&(a.segment.flush(((e,n)=>{const o=function(t,e,n){const o=new FormData;o.append("segment",new Blob([t],{type:"application/octet-stream"}),`${e.session.id}-${e.start}`);const r={raw_segment_size:n,compressed_segment_size:t.byteLength,...e},s=JSON.stringify(r);return o.append("event",new Blob([s],{type:"application/json"})),{data:o,bytesCount:t.byteLength}}(n.output,e,n.rawBytesCount);(0,ht.Kp)(t)?s.sendOnExit(o):s.send(o)})),(0,mt.DJ)(a.expirationTimeoutId)),a="stop"!==t?{status:0,nextSegmentCreationReason:t}:{status:2}}return{addRecord:t=>{if(2!==a.status){if(0===a.status){const t=function(t,e,n){const o=e.findTrackedSession(),r=n.findView();if(o&&r)return{application:{id:t},session:{id:o.id},view:{id:r.id}}}(e.applicationId,n,o);if(!t)return;a={status:1,segment:gt({encoder:i,context:t,creationReason:a.nextSegmentCreationReason}),expirationTimeoutId:(0,mt.wg)((()=>{l("segment_duration_limit")}),yt)}}a.segment.addRecord(t,(t=>{t>vt&&l("segment_bytes_limit")}))}},stop:()=>{l("stop"),c(),u()}}}(t,0,r,s)}function St(t,e,n,c,u,l){const p=[],f=l||(0,r.sA)(e.sessionReplayEndpointBuilder,vt,(e=>{t.notify(14,{error:e}),(0,o.A2)("Error reported to customer",{"error.message":e.message})}));let h;if((0,s.d0)())({addRecord:h}=function(t){const e=(0,s.Y9)();return{addRecord:n=>{const o=t.findView();e.send("record",n,o.id)}}}(c));else{const o=wt(t,e,n,c,f,u);h=o.addRecord,p.push(o.stop)}const{stop:m}=function(t){const{emit:e,configuration:n,lifeCycle:o}=t;if(!e)throw new Error("emit function is required");const r=n=>{e(n),(0,i.b)("record",{record:n});const o=t.viewHistory.findView();a.$1(o.id)},s=function(){const t=new WeakMap;return{set(e,n){(e!==document||document.scrollingElement)&&t.set(e===document?document.scrollingElement:e,n)},get:e=>t.get(e),has:e=>t.has(e)}}(),c=ft(n,r,s),{stop:u}=function(t,e,n,o,r,s){const i=(n=(0,z.nx)(),r={status:0,elementsScrollPositions:t,shadowRootsController:e})=>{const{width:s,height:i}=(0,d.pB)(),a=[{data:{height:i,href:window.location.href,width:s},type:N.Meta,timestamp:n},{data:{has_focus:document.hasFocus()},type:N.Focus,timestamp:n},{data:{node:k(document,o,r),initialOffset:{left:(0,d.Gn)(),top:(0,d.zL)()}},type:N.FullSnapshot,timestamp:n}];return window.visualViewport&&a.push({data:F(window.visualViewport),type:N.VisualViewport,timestamp:n}),a};s(i());const{unsubscribe:a}=n.subscribe(2,(n=>{r(),s(i(n.startClocks.timeStamp,{shadowRootsController:e,status:1,elementsScrollPositions:t}))}));return{stop:a}}(s,c,o,n,l,(t=>t.forEach((t=>r(t)))));function l(){c.flush(),f.flush()}const p=function(){const t=new WeakMap;let e=1;return{getIdForEvent:n=>(t.has(n)||t.set(n,e++),t.get(n))}}(),f=dt(r,n,c,document),h=[f,G(n,r),Y(n,r,p),j(n,r,s,document),q(n,r),it(n,r),Q(n,r),et(r),ot(n,r),Z(n,r),rt(o,r,p),st(o,(t=>{l(),r(t)}))];return{stop:()=>{c.stop(),h.forEach((t=>t.stop())),u()},flushMutations:l,shadowRootsController:c}}({emit:h,configuration:e,lifeCycle:t,viewHistory:c});return p.push(m),{stop:()=>{p.forEach((t=>t()))}}}}}]);
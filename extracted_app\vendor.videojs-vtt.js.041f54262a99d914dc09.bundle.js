(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8341],{35891:t=>{var e={"":!0,up:!0};function n(t){return"number"==typeof t&&t>=0&&t<=100}t.exports=function(){var t=100,r=3,i=0,o=100,a=0,s=100,c="";Object.defineProperties(this,{width:{enumerable:!0,get:function(){return t},set:function(e){if(!n(e))throw new Error("Width must be between 0 and 100.");t=e}},lines:{enumerable:!0,get:function(){return r},set:function(t){if("number"!=typeof t)throw new TypeError("Lines must be set to a number.");r=t}},regionAnchorY:{enumerable:!0,get:function(){return o},set:function(t){if(!n(t))throw new Error("RegionAnchorX must be between 0 and 100.");o=t}},regionAnchorX:{enumerable:!0,get:function(){return i},set:function(t){if(!n(t))throw new Error("RegionAnchorY must be between 0 and 100.");i=t}},viewportAnchorY:{enumerable:!0,get:function(){return s},set:function(t){if(!n(t))throw new Error("ViewportAnchorY must be between 0 and 100.");s=t}},viewportAnchorX:{enumerable:!0,get:function(){return a},set:function(t){if(!n(t))throw new Error("ViewportAnchorX must be between 0 and 100.");a=t}},scroll:{enumerable:!0,get:function(){return c},set:function(t){var n=function(t){return"string"==typeof t&&!!e[t.toLowerCase()]&&t.toLowerCase()}(t);!1===n?console.warn("Scroll: an invalid or illegal string was specified."):c=n}}})}},50184:t=>{var e={"":1,lr:1,rl:1},n={start:1,center:1,end:1,left:1,right:1,auto:1,"line-left":1,"line-right":1};function r(t){return"string"==typeof t&&!!n[t.toLowerCase()]&&t.toLowerCase()}function i(t,n,i){this.hasBeenReset=!1;var o="",a=!1,s=t,c=n,l=i,u=null,h="",f=!0,g="auto",p="start",d="auto",m="auto",v=100,b="center";Object.defineProperties(this,{id:{enumerable:!0,get:function(){return o},set:function(t){o=""+t}},pauseOnExit:{enumerable:!0,get:function(){return a},set:function(t){a=!!t}},startTime:{enumerable:!0,get:function(){return s},set:function(t){if("number"!=typeof t)throw new TypeError("Start time must be set to a number.");s=t,this.hasBeenReset=!0}},endTime:{enumerable:!0,get:function(){return c},set:function(t){if("number"!=typeof t)throw new TypeError("End time must be set to a number.");c=t,this.hasBeenReset=!0}},text:{enumerable:!0,get:function(){return l},set:function(t){l=""+t,this.hasBeenReset=!0}},region:{enumerable:!0,get:function(){return u},set:function(t){u=t,this.hasBeenReset=!0}},vertical:{enumerable:!0,get:function(){return h},set:function(t){var n=function(t){return"string"==typeof t&&!!e[t.toLowerCase()]&&t.toLowerCase()}(t);if(!1===n)throw new SyntaxError("Vertical: an invalid or illegal direction string was specified.");h=n,this.hasBeenReset=!0}},snapToLines:{enumerable:!0,get:function(){return f},set:function(t){f=!!t,this.hasBeenReset=!0}},line:{enumerable:!0,get:function(){return g},set:function(t){if("number"!=typeof t&&"auto"!==t)throw new SyntaxError("Line: an invalid number or illegal string was specified.");g=t,this.hasBeenReset=!0}},lineAlign:{enumerable:!0,get:function(){return p},set:function(t){var e=r(t);e?(p=e,this.hasBeenReset=!0):console.warn("lineAlign: an invalid or illegal string was specified.")}},position:{enumerable:!0,get:function(){return d},set:function(t){if(t<0||t>100)throw new Error("Position must be between 0 and 100.");d=t,this.hasBeenReset=!0}},positionAlign:{enumerable:!0,get:function(){return m},set:function(t){var e=r(t);e?(m=e,this.hasBeenReset=!0):console.warn("positionAlign: an invalid or illegal string was specified.")}},size:{enumerable:!0,get:function(){return v},set:function(t){if(t<0||t>100)throw new Error("Size must be between 0 and 100.");v=t,this.hasBeenReset=!0}},align:{enumerable:!0,get:function(){return b},set:function(t){var e=r(t);if(!e)throw new SyntaxError("align: an invalid or illegal alignment string was specified.");b=e,this.hasBeenReset=!0}}}),this.displayState=void 0}i.prototype.getCueAsHTML=function(){return WebVTT.convertCueToDOMTree(window,this.text)},t.exports=i},66311:(t,e,n)=>{var r=n(49697),i=Object.create||function(){function t(){}return function(e){if(1!==arguments.length)throw new Error("Object.create shim only accepts one parameter.");return t.prototype=e,new t}}();function o(t,e){this.name="ParsingError",this.code=t.code,this.message=e||t.message}function a(t){function e(t,e,n,r){return 3600*(0|t)+60*(0|e)+(0|n)+(0|r)/1e3}var n=t.match(/^(\d+):(\d{1,2})(:\d{1,2})?\.(\d{3})/);return n?n[3]?e(n[1],n[2],n[3].replace(":",""),n[4]):n[1]>59?e(n[1],n[2],0,n[4]):e(0,n[1],n[2],n[4]):null}function s(){this.values=i(null)}function c(t,e,n,r){var i=r?t.split(r):[t];for(var o in i)if("string"==typeof i[o]){var a=i[o].split(n);2===a.length&&e(a[0].trim(),a[1].trim())}}function l(t,e,n){var r=t;function i(){var e=a(t);if(null===e)throw new o(o.Errors.BadTimeStamp,"Malformed timestamp: "+r);return t=t.replace(/^[^\sa-zA-Z-]+/,""),e}function l(){t=t.replace(/^\s+/,"")}if(l(),e.startTime=i(),l(),"--\x3e"!==t.substr(0,3))throw new o(o.Errors.BadTimeStamp,"Malformed time stamp (time stamps must be separated by '--\x3e'): "+r);t=t.substr(3),l(),e.endTime=i(),l(),function(t,e){var r=new s;c(t,(function(t,e){switch(t){case"region":for(var i=n.length-1;i>=0;i--)if(n[i].id===e){r.set(t,n[i].region);break}break;case"vertical":r.alt(t,e,["rl","lr"]);break;case"line":var o=e.split(","),a=o[0];r.integer(t,a),r.percent(t,a)&&r.set("snapToLines",!1),r.alt(t,a,["auto"]),2===o.length&&r.alt("lineAlign",o[1],["start","center","end"]);break;case"position":o=e.split(","),r.percent(t,o[0]),2===o.length&&r.alt("positionAlign",o[1],["start","center","end"]);break;case"size":r.percent(t,e);break;case"align":r.alt(t,e,["start","center","end","left","right"])}}),/:/,/\s/),e.region=r.get("region",null),e.vertical=r.get("vertical","");try{e.line=r.get("line","auto")}catch(t){}e.lineAlign=r.get("lineAlign","start"),e.snapToLines=r.get("snapToLines",!0),e.size=r.get("size",100);try{e.align=r.get("align","center")}catch(t){e.align=r.get("align","middle")}try{e.position=r.get("position","auto")}catch(t){e.position=r.get("position",{start:0,left:0,center:50,middle:50,end:100,right:100},e.align)}e.positionAlign=r.get("positionAlign",{start:"start",left:"start",center:"center",middle:"center",end:"end",right:"end"},e.align)}(t,e)}o.prototype=i(Error.prototype),o.prototype.constructor=o,o.Errors={BadSignature:{code:0,message:"Malformed WebVTT signature."},BadTimeStamp:{code:1,message:"Malformed time stamp."}},s.prototype={set:function(t,e){this.get(t)||""===e||(this.values[t]=e)},get:function(t,e,n){return n?this.has(t)?this.values[t]:e[n]:this.has(t)?this.values[t]:e},has:function(t){return t in this.values},alt:function(t,e,n){for(var r=0;r<n.length;++r)if(e===n[r]){this.set(t,e);break}},integer:function(t,e){/^-?\d+$/.test(e)&&this.set(t,parseInt(e,10))},percent:function(t,e){return!!(e.match(/^([\d]{1,3})(\.[\d]*)?%$/)&&(e=parseFloat(e))>=0&&e<=100)&&(this.set(t,e),!0)}};var u=r.createElement&&r.createElement("textarea"),h={c:"span",i:"i",b:"b",u:"u",ruby:"ruby",rt:"rt",v:"span",lang:"span"},f={white:"rgba(255,255,255,1)",lime:"rgba(0,255,0,1)",cyan:"rgba(0,255,255,1)",red:"rgba(255,0,0,1)",yellow:"rgba(255,255,0,1)",magenta:"rgba(255,0,255,1)",blue:"rgba(0,0,255,1)",black:"rgba(0,0,0,1)"},g={v:"title",lang:"lang"},p={rt:"ruby"};function d(t,e){function n(){if(!e)return null;var t,n=e.match(/^([^<]*)(<[^>]*>?)?/);return t=n[1]?n[1]:n[2],e=e.substr(t.length),t}function r(t,e){return!p[e.localName]||p[e.localName]===t.localName}function i(e,n){var r=h[e];if(!r)return null;var i=t.document.createElement(r),o=g[e];return o&&n&&(i[o]=n.trim()),i}for(var o,s,c=t.document.createElement("div"),l=c,d=[];null!==(o=n());)if("<"!==o[0])l.appendChild(t.document.createTextNode((s=o,u.innerHTML=s,s=u.textContent,u.textContent="",s)));else{if("/"===o[1]){d.length&&d[d.length-1]===o.substr(2).replace(">","")&&(d.pop(),l=l.parentNode);continue}var m,v=a(o.substr(1,o.length-2));if(v){m=t.document.createProcessingInstruction("timestamp",v),l.appendChild(m);continue}var b=o.match(/^<([^.\s/0-9>]+)(\.[^\s\\>]+)?([^>\\]+)?(\\?)>?$/);if(!b)continue;if(!(m=i(b[1],b[3])))continue;if(!r(l,m))continue;if(b[2]){var w=b[2].split(".");w.forEach((function(t){var e=/^bg_/.test(t),n=e?t.slice(3):t;if(f.hasOwnProperty(n)){var r=e?"background-color":"color",i=f[n];m.style[r]=i}})),m.className=w.join(" ")}d.push(b[1]),l.appendChild(m),l=m}return c}var m=[[1470,1470],[1472,1472],[1475,1475],[1478,1478],[1488,1514],[1520,1524],[1544,1544],[1547,1547],[1549,1549],[1563,1563],[1566,1610],[1645,1647],[1649,1749],[1765,1766],[1774,1775],[1786,1805],[1807,1808],[1810,1839],[1869,1957],[1969,1969],[1984,2026],[2036,2037],[2042,2042],[2048,2069],[2074,2074],[2084,2084],[2088,2088],[2096,2110],[2112,2136],[2142,2142],[2208,2208],[2210,2220],[8207,8207],[64285,64285],[64287,64296],[64298,64310],[64312,64316],[64318,64318],[64320,64321],[64323,64324],[64326,64449],[64467,64829],[64848,64911],[64914,64967],[65008,65020],[65136,65140],[65142,65276],[67584,67589],[67592,67592],[67594,67637],[67639,67640],[67644,67644],[67647,67669],[67671,67679],[67840,67867],[67872,67897],[67903,67903],[67968,68023],[68030,68031],[68096,68096],[68112,68115],[68117,68119],[68121,68147],[68160,68167],[68176,68184],[68192,68223],[68352,68405],[68416,68437],[68440,68466],[68472,68479],[68608,68680],[126464,126467],[126469,126495],[126497,126498],[126500,126500],[126503,126503],[126505,126514],[126516,126519],[126521,126521],[126523,126523],[126530,126530],[126535,126535],[126537,126537],[126539,126539],[126541,126543],[126545,126546],[126548,126548],[126551,126551],[126553,126553],[126555,126555],[126557,126557],[126559,126559],[126561,126562],[126564,126564],[126567,126570],[126572,126578],[126580,126583],[126585,126588],[126590,126590],[126592,126601],[126603,126619],[126625,126627],[126629,126633],[126635,126651],[1114109,1114109]];function v(t){for(var e=0;e<m.length;e++){var n=m[e];if(t>=n[0]&&t<=n[1])return!0}return!1}function b(t){var e=[],n="";if(!t||!t.childNodes)return"ltr";function r(t,e){for(var n=e.childNodes.length-1;n>=0;n--)t.push(e.childNodes[n])}function i(t){if(!t||!t.length)return null;var e=t.pop(),n=e.textContent||e.innerText;if(n){var o=n.match(/^.*(\n|\r)/);return o?(t.length=0,o[0]):n}return"ruby"===e.tagName?i(t):e.childNodes?(r(t,e),i(t)):void 0}for(r(e,t);n=i(e);)for(var o=0;o<n.length;o++)if(v(n.charCodeAt(o)))return"rtl";return"ltr"}function w(){}function y(t,e,n){w.call(this),this.cue=e,this.cueDiv=d(t,e.text);var r={color:"rgba(255, 255, 255, 1)",backgroundColor:"rgba(0, 0, 0, 0.8)",position:"relative",left:0,right:0,top:0,bottom:0,display:"inline",writingMode:""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext"};this.applyStyles(r,this.cueDiv),this.div=t.document.createElement("div"),r={direction:b(this.cueDiv),writingMode:""===e.vertical?"horizontal-tb":"lr"===e.vertical?"vertical-lr":"vertical-rl",unicodeBidi:"plaintext",textAlign:"middle"===e.align?"center":e.align,font:n.font,whiteSpace:"pre-line",position:"absolute"},this.applyStyles(r),this.div.appendChild(this.cueDiv);var i=0;switch(e.positionAlign){case"start":case"line-left":i=e.position;break;case"center":i=e.position-e.size/2;break;case"end":case"line-right":i=e.position-e.size}""===e.vertical?this.applyStyles({left:this.formatStyle(i,"%"),width:this.formatStyle(e.size,"%")}):this.applyStyles({top:this.formatStyle(i,"%"),height:this.formatStyle(e.size,"%")}),this.move=function(t){this.applyStyles({top:this.formatStyle(t.top,"px"),bottom:this.formatStyle(t.bottom,"px"),left:this.formatStyle(t.left,"px"),right:this.formatStyle(t.right,"px"),height:this.formatStyle(t.height,"px"),width:this.formatStyle(t.width,"px")})}}function T(t){var e,n,r,i;if(t.div){n=t.div.offsetHeight,r=t.div.offsetWidth,i=t.div.offsetTop;var o=(o=t.div.childNodes)&&(o=o[0])&&o.getClientRects&&o.getClientRects();t=t.div.getBoundingClientRect(),e=o?Math.max(o[0]&&o[0].height||0,t.height/o.length):0}this.left=t.left,this.right=t.right,this.top=t.top||i,this.height=t.height||n,this.bottom=t.bottom||i+(t.height||n),this.width=t.width||r,this.lineHeight=void 0!==e?e:t.lineHeight}function x(t,e,n,r){var i=new T(e),o=e.cue,a=function(t){if("number"==typeof t.line&&(t.snapToLines||t.line>=0&&t.line<=100))return t.line;if(!t.track||!t.track.textTrackList||!t.track.textTrackList.mediaElement)return-1;for(var e=t.track,n=e.textTrackList,r=0,i=0;i<n.length&&n[i]!==e;i++)"showing"===n[i].mode&&r++;return-1*++r}(o),s=[];if(o.snapToLines){var c;switch(o.vertical){case"":s=["+y","-y"],c="height";break;case"rl":s=["+x","-x"],c="width";break;case"lr":s=["-x","+x"],c="width"}var l=i.lineHeight,u=l*Math.round(a),h=n[c]+l,f=s[0];Math.abs(u)>h&&(u=u<0?-1:1,u*=Math.ceil(h/l)*l),a<0&&(u+=""===o.vertical?n.height:n.width,s=s.reverse()),i.move(f,u)}else{var g=i.lineHeight/n.height*100;switch(o.lineAlign){case"center":a-=g/2;break;case"end":a-=g}switch(o.vertical){case"":e.applyStyles({top:e.formatStyle(a,"%")});break;case"rl":e.applyStyles({left:e.formatStyle(a,"%")});break;case"lr":e.applyStyles({right:e.formatStyle(a,"%")})}s=["+y","-x","+x","-y"],i=new T(e)}var p=function(t,e){for(var i,o=new T(t),a=1,s=0;s<e.length;s++){for(;t.overlapsOppositeAxis(n,e[s])||t.within(n)&&t.overlapsAny(r);)t.move(e[s]);if(t.within(n))return t;var c=t.intersectPercentage(n);a>c&&(i=new T(t),a=c),t=new T(o)}return i||o}(i,s);e.move(p.toCSSCompatValues(n))}function E(){}w.prototype.applyStyles=function(t,e){for(var n in e=e||this.div,t)t.hasOwnProperty(n)&&(e.style[n]=t[n])},w.prototype.formatStyle=function(t,e){return 0===t?0:t+e},y.prototype=i(w.prototype),y.prototype.constructor=y,T.prototype.move=function(t,e){switch(e=void 0!==e?e:this.lineHeight,t){case"+x":this.left+=e,this.right+=e;break;case"-x":this.left-=e,this.right-=e;break;case"+y":this.top+=e,this.bottom+=e;break;case"-y":this.top-=e,this.bottom-=e}},T.prototype.overlaps=function(t){return this.left<t.right&&this.right>t.left&&this.top<t.bottom&&this.bottom>t.top},T.prototype.overlapsAny=function(t){for(var e=0;e<t.length;e++)if(this.overlaps(t[e]))return!0;return!1},T.prototype.within=function(t){return this.top>=t.top&&this.bottom<=t.bottom&&this.left>=t.left&&this.right<=t.right},T.prototype.overlapsOppositeAxis=function(t,e){switch(e){case"+x":return this.left<t.left;case"-x":return this.right>t.right;case"+y":return this.top<t.top;case"-y":return this.bottom>t.bottom}},T.prototype.intersectPercentage=function(t){return Math.max(0,Math.min(this.right,t.right)-Math.max(this.left,t.left))*Math.max(0,Math.min(this.bottom,t.bottom)-Math.max(this.top,t.top))/(this.height*this.width)},T.prototype.toCSSCompatValues=function(t){return{top:this.top-t.top,bottom:t.bottom-this.bottom,left:this.left-t.left,right:t.right-this.right,height:this.height,width:this.width}},T.getSimpleBoxPosition=function(t){var e=t.div?t.div.offsetHeight:t.tagName?t.offsetHeight:0,n=t.div?t.div.offsetWidth:t.tagName?t.offsetWidth:0,r=t.div?t.div.offsetTop:t.tagName?t.offsetTop:0;return{left:(t=t.div?t.div.getBoundingClientRect():t.tagName?t.getBoundingClientRect():t).left,right:t.right,top:t.top||r,height:t.height||e,bottom:t.bottom||r+(t.height||e),width:t.width||n}},E.StringDecoder=function(){return{decode:function(t){if(!t)return"";if("string"!=typeof t)throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(t))}}},E.convertCueToDOMTree=function(t,e){return t&&e?d(t,e):null},E.processCues=function(t,e,n){if(!t||!e||!n)return null;for(;n.firstChild;)n.removeChild(n.firstChild);var r=t.document.createElement("div");if(r.style.position="absolute",r.style.left="0",r.style.right="0",r.style.top="0",r.style.bottom="0",r.style.margin="1.5%",n.appendChild(r),function(t){for(var e=0;e<t.length;e++)if(t[e].hasBeenReset||!t[e].displayState)return!0;return!1}(e)){var i=[],o=T.getSimpleBoxPosition(r),a={font:Math.round(.05*o.height*100)/100+"px sans-serif"};!function(){for(var n,s,c=0;c<e.length;c++)s=e[c],n=new y(t,s,a),r.appendChild(n.div),x(0,n,o,i),s.displayState=n.div,i.push(T.getSimpleBoxPosition(n))}()}else for(var s=0;s<e.length;s++)r.appendChild(e[s].displayState)},E.Parser=function(t,e,n){n||(n=e,e={}),e||(e={}),this.window=t,this.vttjs=e,this.state="INITIAL",this.buffer="",this.decoder=n||new TextDecoder("utf8"),this.regionList=[]},E.Parser.prototype={reportOrThrowError:function(t){if(!(t instanceof o))throw t;this.onparsingerror&&this.onparsingerror(t)},parse:function(t){var e,n=this;function r(){for(var t=n.buffer,e=0;e<t.length&&"\r"!==t[e]&&"\n"!==t[e];)++e;var r=t.substr(0,e);return"\r"===t[e]&&++e,"\n"===t[e]&&++e,n.buffer=t.substr(e),r}t&&(n.buffer+=n.decoder.decode(t,{stream:!0}));try{var i;if("INITIAL"===n.state){if(!/\r\n|\n/.test(n.buffer))return this;var u=(i=r()).match(/^WEBVTT([ \t].*)?$/);if(!u||!u[0])throw new o(o.Errors.BadSignature);n.state="HEADER"}for(var h=!1;n.buffer;){if(!/\r\n|\n/.test(n.buffer))return this;switch(h?h=!1:i=r(),n.state){case"HEADER":/:/.test(i)?(e=i).match(/X-TIMESTAMP-MAP/)?c(e,(function(t,e){"X-TIMESTAMP-MAP"===t&&function(t){var e=new s;c(t,(function(t,n){switch(t){case"MPEGT":e.integer(t+"S",n);break;case"LOCA":e.set(t+"L",a(n))}}),/[^\d]:/,/,/),n.ontimestampmap&&n.ontimestampmap({MPEGTS:e.get("MPEGTS"),LOCAL:e.get("LOCAL")})}(e)}),/=/):c(e,(function(t,e){"Region"===t&&function(t){var e=new s;if(c(t,(function(t,n){switch(t){case"id":e.set(t,n);break;case"width":e.percent(t,n);break;case"lines":e.integer(t,n);break;case"regionanchor":case"viewportanchor":var r=n.split(",");if(2!==r.length)break;var i=new s;if(i.percent("x",r[0]),i.percent("y",r[1]),!i.has("x")||!i.has("y"))break;e.set(t+"X",i.get("x")),e.set(t+"Y",i.get("y"));break;case"scroll":e.alt(t,n,["up"])}}),/=/,/\s/),e.has("id")){var r=new(n.vttjs.VTTRegion||n.window.VTTRegion);r.width=e.get("width",100),r.lines=e.get("lines",3),r.regionAnchorX=e.get("regionanchorX",0),r.regionAnchorY=e.get("regionanchorY",100),r.viewportAnchorX=e.get("viewportanchorX",0),r.viewportAnchorY=e.get("viewportanchorY",100),r.scroll=e.get("scroll",""),n.onregion&&n.onregion(r),n.regionList.push({id:e.get("id"),region:r})}}(e)}),/:/):i||(n.state="ID");continue;case"NOTE":i||(n.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(i)){n.state="NOTE";break}if(!i)continue;n.cue=new(n.vttjs.VTTCue||n.window.VTTCue)(0,0,"");try{n.cue.align="center"}catch(t){n.cue.align="middle"}if(n.state="CUE",-1===i.indexOf("--\x3e")){n.cue.id=i;continue}case"CUE":try{l(i,n.cue,n.regionList)}catch(t){n.reportOrThrowError(t),n.cue=null,n.state="BADCUE";continue}n.state="CUETEXT";continue;case"CUETEXT":var f=-1!==i.indexOf("--\x3e");if(!i||f&&(h=!0)){n.oncue&&n.oncue(n.cue),n.cue=null,n.state="ID";continue}n.cue.text&&(n.cue.text+="\n"),n.cue.text+=i.replace(/\u2028/g,"\n").replace(/u2029/g,"\n");continue;case"BADCUE":i||(n.state="ID");continue}}}catch(t){n.reportOrThrowError(t),"CUETEXT"===n.state&&n.cue&&n.oncue&&n.oncue(n.cue),n.cue=null,n.state="INITIAL"===n.state?"BADWEBVTT":"BADCUE"}return this},flush:function(){var t=this;try{if(t.buffer+=t.decoder.decode(),(t.cue||"HEADER"===t.state)&&(t.buffer+="\n\n",t.parse()),"INITIAL"===t.state)throw new o(o.Errors.BadSignature)}catch(e){t.reportOrThrowError(e)}return t.onflush&&t.onflush(),this}},t.exports=E},94784:(t,e,n)=>{var r=n(89840),i=t.exports={WebVTT:n(66311),VTTCue:n(50184),VTTRegion:n(35891)};r.vttjs=i,r.WebVTT=i.WebVTT;var o=i.VTTCue,a=i.VTTRegion,s=r.VTTCue,c=r.VTTRegion;i.shim=function(){r.VTTCue=o,r.VTTRegion=a},i.restore=function(){r.VTTCue=s,r.VTTRegion=c},r.VTTCue||i.shim()}}]);
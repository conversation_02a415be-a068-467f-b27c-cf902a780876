"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4444],{1039:(e,t,n)=>{n.d(t,{$:()=>r});var o=n(32234);function r(e,t){t.silentMultipleInit||o.Vy.error(`${e} is already initialized.`)}},7328:(e,t,n)=>{n.d(t,{Rr:()=>T,A2:()=>E,VJ:()=>R,Q6:()=>x,JK:()=>C,Wb:()=>$,a5:()=>w});var o=n(32234),r=n(63502),s=n(35019),i=n(48899),a=n(30956),c=n(92555),u=n(29336),d=n(42182),l=n(12452),p=n(59248),f=n(84601),g=n(47978),m=n(78476),v=n(90970),y=n(68255);const S={log:"log",configuration:"configuration",usage:"usage"},b=["https://www.datadoghq-browser-agent.com","https://www.datad0g-browser-agent.com","https://d3uc069fcn7uxw.cloudfront.net","https://d20xtzwzcl0ceb.cloudfront.net","http://localhost","<anonymous>"],h=[a.R8];let k=(0,y.O)(),_=e=>{k.add((()=>_(e)))};function w(e,t){let n;const o=new c.c,r=new Set,s=!h.includes(t.site)&&(0,p.ic)(t.telemetrySampleRate),a={[S.log]:s,[S.configuration]:s&&(0,p.ic)(t.telemetryConfigurationSampleRate),[S.usage]:s&&(0,p.ic)(t.telemetryUsageSampleRate)},m={is_local_file:"file:"===window.location.protocol,is_worker:"WorkerGlobalScope"in self};return _=s=>{const c=(0,f.s)(s);if(a[s.type]&&r.size<t.maxTelemetryEventsPerPage&&!r.has(c)){const t=function(e,t,o){return(0,g.kg)({type:"telemetry",date:(0,u.nx)(),service:e,version:"6.5.1",source:"browser",_dd:{format_version:2},telemetry:(0,g.kg)(t,{runtime_env:o,connectivity:(0,v.q)(),sdk_setup:"npm"}),experimental_features:Array.from((0,i.q7)())},void 0!==n?n():{})}(e,s,m);o.notify(t),(0,l.b)("telemetry",t),r.add(c)}},(0,d.Bd)(R),{setContextProvider:e=>{n=e},observable:o,enabled:s}}function C(){k.drain()}function $(e){return e.site===a.Bb}function E(e,t){(0,d.oO)(o.bP.debug,e,t),_({type:S.log,message:e,status:"debug",...t})}function R(e,t){_({type:S.log,status:"error",...O(e),...t})}function T(e){_({type:S.configuration,configuration:e})}function x(e){_({type:S.usage,usage:e})}function O(e){if((0,r.bJ)(e)){const t=(0,m.T)(e);return{error:{kind:t.name,stack:(0,s.Yn)(A(t))},message:t.message}}return{error:{stack:r.e6},message:`Uncaught ${(0,f.s)(e)}`}}function A(e){return e.stack=e.stack.filter((e=>!e.url||b.some((t=>e.url.startsWith(t))))),e}},8822:(e,t,n)=>{n.d(t,{T:()=>c,w:()=>u});var o=n(92832),r=n(32234);const s=200;function i(e,t){const n=s-e.length-1;return(t.length>n||function(e){return!!function(){try{return new RegExp("[\\p{Ll}]","u"),!0}catch(e){return!1}}()&&new RegExp("[^\\p{Ll}\\p{Lo}0-9_:./-]","u").test(e)}(t))&&r.Vy.warn(`${e} value doesn't meet tag requirements and will be sanitized. ${r.xG} ${r.fH}/getting_started/tagging/#defining-tags`),`${e}:${t.replace(/,/g,"_")}`}var a=n(30956);function c(e){const t=e.site||a.NW,n=function(e){const{env:t,service:n,version:o,datacenter:r}=e,s=[];return t&&s.push(i("env",t)),n&&s.push(i("service",n)),o&&s.push(i("version",o)),r&&s.push(i("datacenter",r)),s}(e),r=function(e,t){return{logsEndpointBuilder:(0,o.n)(e,"logs",t),rumEndpointBuilder:(0,o.n)(e,"rum",t),sessionReplayEndpointBuilder:(0,o.n)(e,"replay",t)}}(e,n),s=function(e,t){if(!e.replica)return;const n={...e,site:a.NW,clientToken:e.replica.clientToken},r={logsEndpointBuilder:(0,o.n)(n,"logs",t),rumEndpointBuilder:(0,o.n)(n,"rum",t)};return{applicationId:e.replica.applicationId,...r}}(e,n);return{replica:s,site:t,...r}}function u(e){return a.Ih.every((t=>e.includes(t)))}},10100:(e,t,n)=>{n.d(t,{G:()=>l});var o=n(23054),r=n(92555),s=n(29336),i=n(75248),a=n(78218),c=n(80886);let u;const d=new WeakMap;function l(e){return u||(u=function(e){return new r.c((t=>{const{stop:n}=(0,o.H)(XMLHttpRequest.prototype,"open",p),{stop:r}=(0,o.H)(XMLHttpRequest.prototype,"send",(n=>{!function({target:e,handlingStack:t},n,r){const i=d.get(e);if(!i)return;const u=i;u.state="start",u.startClocks=(0,s.M8)(),u.isAborted=!1,u.xhr=e,u.handlingStack=t;let l=!1;const{stop:p}=(0,o.H)(e,"onreadystatechange",(()=>{e.readyState===XMLHttpRequest.DONE&&f()})),f=()=>{if(g(),p(),l)return;l=!0;const t=i;t.state="complete",t.duration=(0,s.vk)(u.startClocks.timeStamp,(0,s.nx)()),t.status=e.status,r.notify((0,a.yG)(t))},{stop:g}=(0,c.q)(n,e,"loadend",f);r.notify(u)}(n,e,t)}),{computeHandlingStack:!0}),{stop:i}=(0,o.H)(XMLHttpRequest.prototype,"abort",f);return()=>{n(),r(),i()}}))}(e)),u}function p({target:e,parameters:[t,n]}){d.set(e,{state:"open",method:String(t).toUpperCase(),url:(0,i.l2)(String(n))})}function f({target:e}){const t=d.get(e);t&&(t.isAborted=!0)}},13230:(e,t,n)=>{n.d(t,{Ck:()=>c});var o=n(80886),r=n(47978),s=n(78218);const i="_dd_c",a=[];function c(e,t,n,c){const u=function(e,t){return`${i}_${e}_${t}`}(n,c);a.push((0,o.q)(e,window,"storage",(({key:e})=>{u===e&&t.setContext(l())}))),t.changeObservable.subscribe((function(){localStorage.setItem(u,JSON.stringify(t.getContext()))}));const d=(0,r.kg)(l(),t.getContext());function l(){const e=localStorage.getItem(u);return e?JSON.parse(e):{}}(0,s.RI)(d)||t.setContext(d)}},16105:(e,t,n)=>{n.d(t,{V:()=>d,u:()=>l});var o=n(35019),r=n(42182),s=n(92555),i=n(80886),a=n(1356),c=n(68393),u=n(29336);const d={intervention:"intervention",deprecation:"deprecation",cspViolation:"csp_violation"};function l(e,t){const n=[];t.includes(d.cspViolation)&&n.push(function(e){return new s.c((t=>{const{stop:n}=(0,i.q)(e,document,"securitypolicyviolation",(e=>{t.notify(function(e){const t=`'${e.blockedURI}' blocked by '${e.effectiveDirective}' directive`;return p({type:e.effectiveDirective,message:`${d.cspViolation}: ${t}`,originalError:e,csp:{disposition:e.disposition},stack:f(e.effectiveDirective,e.originalPolicy?`${t} of the policy "${(0,a._R)(e.originalPolicy,100,"...")}"`:"no policy",e.sourceFile,e.lineNumber,e.columnNumber)})}(e))}));return n}))}(e));const o=t.filter((e=>e!==d.cspViolation));return o.length&&n.push(function(e){return new s.c((t=>{if(!window.ReportingObserver)return;const n=(0,r.dm)(((e,n)=>e.forEach((e=>t.notify(function(e){const{type:t,body:n}=e;return p({type:n.id,message:`${t}: ${n.message}`,originalError:e,stack:f(n.id,n.message,n.sourceFile,n.lineNumber,n.columnNumber)})}(e)))))),o=new window.ReportingObserver(n,{types:e,buffered:!0});return o.observe(),()=>{o.disconnect()}}))}(o)),(0,s.F)(...n)}function p(e){return{startClocks:(0,u.M8)(),source:c.g.REPORT,handling:"unhandled",...e}}function f(e,t,n,r,s){return n?(0,o.Yn)({name:e,message:t,stack:[{func:"?",url:n,line:null!=r?r:void 0,column:null!=s?s:void 0}]}):void 0}},21655:(e,t,n)=>{n.d(t,{l:()=>g});var o=n(63502),r=n(92555),s=n(32234),i=n(42182),a=n(42995),c=n(84601),u=n(68393),d=n(78476),l=n(35019),p=n(29336);let f={};function g(e){const t=e.map((e=>(f[e]||(f[e]=function(e){return new r.c((t=>{const n=s.JZ[e];return s.JZ[e]=(...r)=>{n.apply(console,r);const f=(0,l.uC)("console error");(0,i.um)((()=>{t.notify(function(e,t,n){const r=e.map((e=>function(e){return"string"==typeof e?(0,a.a)(e):(0,o.bJ)(e)?(0,l.NR)((0,d.T)(e)):(0,c.s)((0,a.a)(e),void 0,2)}(e))).join(" ");let i;if(t===s.bP.error){const t=e.find(o.bJ);i={stack:t?(0,l.Yn)((0,d.T)(t)):void 0,fingerprint:(0,o.Nt)(t),causes:t?(0,o.Dr)(t,"console"):void 0,startClocks:(0,p.M8)(),message:r,source:u.g.CONSOLE,handling:"handled",handlingStack:n,context:(0,o.Qb)(t)}}return{api:t,message:r,error:i,handlingStack:n}}(r,e,f))}))},()=>{s.JZ[e]=n}}))}(e)),f[e])));return(0,r.F)(...t)}},30956:(e,t,n)=>{n.d(t,{$A:()=>c,Bb:()=>o,Ih:()=>u,NW:()=>s,R8:()=>a,TC:()=>r,dV:()=>i});const o="datad0g.com",r="dd0g-gov.com",s="datadoghq.com",i="datadoghq.eu",a="ddog-gov.com",c="pci.browser-intake-datadoghq.com",u=["ddsource","ddtags"]},39377:(e,t,n)=>{n.d(t,{Kp:()=>c,hL:()=>a,y5:()=>i});var o=n(92555),r=n(19642),s=n(80886);const i={HIDDEN:"visibility_hidden",UNLOADING:"before_unload",PAGEHIDE:"page_hide",FROZEN:"page_frozen"};function a(e){return new o.c((t=>{const{stop:n}=(0,s.l)(e,window,["visibilitychange","freeze"],(e=>{"visibilitychange"===e.type&&"hidden"===document.visibilityState?t.notify({reason:i.HIDDEN}):"freeze"===e.type&&t.notify({reason:i.FROZEN})}),{capture:!0}),o=(0,s.q)(e,window,"beforeunload",(()=>{t.notify({reason:i.UNLOADING})})).stop;return()=>{n(),o()}}))}function c(e){return(0,r.KQ)(i).includes(e)}},42661:(e,t,n)=>{n.d(t,{mj:()=>c});var o=n(23054),r=n(29336),s=n(78476),i=n(63502),a=n(68393);function c(e){const t=(t,n)=>{const o=(0,i.As)({stackTrace:t,originalError:n,startClocks:(0,r.M8)(),nonErrorPrefix:"Uncaught",source:a.g.SOURCE,handling:"unhandled"});e.notify(o)},{stop:n}=(c=t,(0,o.H)(window,"onerror",(({parameters:[e,t,n,o,r]})=>{let a;a=(0,i.bJ)(r)?(0,s.T)(r):(0,s.h)(e,t,n,o),c(a,null!=r?r:e)})));var c;const{stop:u}=function(e){return(0,o.H)(window,"onunhandledrejection",(({parameters:[t]})=>{const n=t.reason||"Empty reason",o=(0,s.T)(n);e(o,n)}))}(t);return{stop:()=>{n(),u()}}}},54991:(e,t,n)=>{n.d(t,{X6:()=>u,aj:()=>c,ao:()=>a});var o=n(84409);const r="datadog-synthetics-public-id",s="datadog-synthetics-result-id",i="datadog-synthetics-injects-rum";function a(){return Boolean(window._DATADOG_SYNTHETICS_INJECTS_RUM||(0,o.B9)(i))}function c(){const e=window._DATADOG_SYNTHETICS_PUBLIC_ID||(0,o.B9)(r);return"string"==typeof e?e:void 0}function u(){const e=window._DATADOG_SYNTHETICS_RESULT_ID||(0,o.B9)(s);return"string"==typeof e?e:void 0}},62650:(e,t,n)=>{n.d(t,{i:()=>u});var o=n(23054),r=n(42182),s=n(92555),i=n(29336),a=n(75248);let c;function u(){return c||(c=new s.c((e=>{if(!window.fetch)return;const{stop:t}=(0,o.H)(window,"fetch",(t=>function({parameters:e,onPostCall:t,handlingStack:n},o){const[s,c]=e;let u=c&&c.method;void 0===u&&s instanceof Request&&(u=s.method);const d=void 0!==u?String(u).toUpperCase():"GET",l=s instanceof Request?s.url:(0,a.l2)(String(s)),p={state:"start",init:c,input:s,method:d,startClocks:(0,i.M8)(),url:l,handlingStack:n};o.notify(p),e[0]=p.input,e[1]=p.init,t((e=>function(e,t,n){const o=n;function s(t){o.state="resolve",Object.assign(o,t),e.notify(o)}t.then((0,r.dm)((e=>{s({response:e,responseType:e.type,status:e.status,isAborted:!1})})),(0,r.dm)((e=>{var t,n;s({status:0,isAborted:(null===(n=null===(t=o.init)||void 0===t?void 0:t.signal)||void 0===n?void 0:n.aborted)||e instanceof DOMException&&e.code===DOMException.ABORT_ERR,error:e})})))}(o,e,p)))}(t,e)),{computeHandlingStack:!0});return t}))),c}},63502:(e,t,n)=>{n.d(t,{As:()=>c,Dr:()=>p,Nt:()=>u,Qb:()=>d,bJ:()=>l,e6:()=>a});var o=n(42995),r=n(84601),s=n(78476),i=n(35019);const a="No stack, consider using an instance of Error";function c({stackTrace:e,originalError:t,handlingStack:n,componentStack:s,startClocks:c,nonErrorPrefix:f,source:g,handling:m}){const v=l(t),y=function(e,t,n,s){return(null==e?void 0:e.message)&&(null==e?void 0:e.name)?e.message:t?"Empty message":`${n} ${(0,r.s)((0,o.a)(s))}`}(e,v,f,t),S=function(e,t){return void 0!==t&&(!!e||t.stack.length>0&&(t.stack.length>1||void 0!==t.stack[0].url))}(v,e)?(0,i.Yn)(e):a,b=v?p(t,g):void 0;return{startClocks:c,source:g,handling:m,handlingStack:n,componentStack:s,originalError:t,type:e?e.name:void 0,message:y,stack:S,causes:b,fingerprint:u(t),context:d(t)}}function u(e){return l(e)&&"dd_fingerprint"in e?String(e.dd_fingerprint):void 0}function d(e){if(null!==e&&"object"==typeof e&&"dd_context"in e)return e.dd_context}function l(e){return e instanceof Error||"[object Error]"===Object.prototype.toString.call(e)}function p(e,t){let n=e;const o=[];for(;l(null==n?void 0:n.cause)&&o.length<10;){const e=(0,s.T)(n.cause);o.push({message:n.cause.message,source:t,type:null==e?void 0:e.name,stack:e&&(0,i.Yn)(e)}),n=n.cause}return o.length?o:void 0}},66869:(e,t,n)=>{n.d(t,{nd:()=>V,oC:()=>j});var o=n(93001),r=n(92555),s=n(29336),i=n(52999),a=n(1356),c=n(32234),u=n(14451),d=n(84409);const l="_dd_s";var p=n(78218),f=n(19642),g=n(72765);const m=/^([a-zA-Z]+)=([a-z0-9-]+)$/,v="&",y="1";function S(e,t){const n={isExpired:y};return t.trackAnonymousUser&&((null==e?void 0:e.anonymousId)?n.anonymousId=null==e?void 0:e.anonymousId:n.anonymousId=(0,a.lk)()),n}function b(e){return(0,p.RI)(e)}function h(e){return!b(e)}function k(e){return void 0!==e.isExpired||!((void 0===(t=e).created||(0,s.x3)()-Number(t.created)<g.AQ)&&(void 0===t.expire||(0,s.x3)()<Number(t.expire)));var t}function _(e){e.expire=String((0,s.x3)()+g.HB)}function w(e){return(0,f.WP)(e).map((([e,t])=>"anonymousId"===e?`aid=${t}`:`${e}=${t}`)).join(v)}function C(e){const t={};return function(e){return!!e&&(-1!==e.indexOf(v)||m.test(e))}(e)&&e.split(v).forEach((e=>{const n=m.exec(e);if(null!==n){const[,e,o]=n;"aid"===e?t.anonymousId=o:t[e]=o}})),t}const $="_dd",E="_dd_r",R="_dd_l",T="rum",x="logs";function O(e){const t=function(e){const t={};return t.secure=!!e.useSecureSessionCookie||!!e.usePartitionedCrossSiteSessionCookie,t.crossSite=!!e.usePartitionedCrossSiteSessionCookie,t.partitioned=!!e.usePartitionedCrossSiteSessionCookie,e.trackSessionAcrossSubdomains&&(t.domain=(0,d.DQ)()),t}(e);return(0,d.z$)(t)?{type:g.Q_.COOKIE,cookieOptions:t}:void 0}function A(){return C((0,d.Ri)(l))}const I="_dd_test_";function D(){try{const e=(0,a.lk)(),t=`${I}${e}`;localStorage.setItem(t,e);const n=localStorage.getItem(t);return localStorage.removeItem(t),e===n?{type:g.Q_.LOCAL_STORAGE}:void 0}catch(e){return}}function N(e){localStorage.setItem(l,w(e))}function L(){return C(localStorage.getItem(l))}const P=10,U=100,G=[];let q;function M(e,t,n=0){var o;const{isLockEnabled:r,persistSession:s,expireSession:i}=t,c=e=>s({...e,lock:d}),u=()=>{const e=t.retrieveSession(),n=e.lock;return e.lock&&delete e.lock,{session:e,lock:n}};if(q||(q=e),e!==q)return void G.push(e);if(r&&n>=U)return void H(t);let d,l=u();if(r){if(l.lock)return void B(e,t,n);if(d=(0,a.lk)(),c(l.session),l=u(),l.lock!==d)return void B(e,t,n)}let p=e.process(l.session);if(r&&(l=u(),l.lock!==d))B(e,t,n);else{if(p&&(k(p)?i(p):(_(p),r?c(p):s(p))),r&&(!p||!k(p))){if(l=u(),l.lock!==d)return void B(e,t,n);s(l.session),p=l.session}null===(o=e.after)||void 0===o||o.call(e,p||l.session),H(t)}}function B(e,t,n){(0,o.wg)((()=>{M(e,t,n+1)}),P)}function H(e){q=void 0;const t=G.shift();t&&M(t,e)}const W=s.OY;function V(e){switch(e.sessionPersistence){case g.Q_.COOKIE:return O(e);case g.Q_.LOCAL_STORAGE:return D();case void 0:{let t=O(e);return!t&&e.allowFallbackToLocalStorage&&(t=D()),t}default:c.Vy.error(`Invalid session persistence '${String(e.sessionPersistence)}'`)}}function j(e,t,n,c){const p=new r.c,f=new r.c,m=new r.c,v=e.type===g.Q_.COOKIE?function(e,t){const n={isLockEnabled:(0,u.F2)(),persistSession:(o=t,e=>{(0,d.TV)(l,w(e),g.HB,o)}),retrieveSession:A,expireSession:n=>function(e,t,n){const o=S(t,n);(0,d.TV)(l,w(o),n.trackAnonymousUser?g._P:g.AQ,e)}(t,n,e)};var o;return function(e){if(!(0,d.B9)(l)){const t=(0,d.B9)($),n=(0,d.B9)(E),o=(0,d.B9)(R),r={};t&&(r.id=t),o&&/^[01]$/.test(o)&&(r[x]=o),n&&/^[012]$/.test(n)&&(r[T]=n),h(r)&&(_(r),e.persistSession(r))}}(n),n}(t,e.cookieOptions):function(e){return{isLockEnabled:!1,persistSession:N,retrieveSession:L,expireSession:t=>function(e,t){N(S(e,t))}(t,e)}}(t),{expireSession:y}=v,C=(0,o.yb)((function(){M({process:e=>k(e)?S(e,t):void 0,after:P},v)}),W);let O;U();const{throttled:I,cancel:D}=(0,i.n)((()=>{M({process:e=>{if(b(e))return;const t=P(e);return function(e){if(b(e))return!1;const{trackingType:t,isTracked:o}=c(e[n]);e[n]=t,delete e.isExpired,o&&!e.id&&(e.id=(0,a.lk)(),e.created=String((0,s.x3)()))}(t),t},after:e=>{h(e)&&!G()&&function(e){O=e,p.notify()}(e),O=e}},v)}),W);function P(e){return k(e)&&(e=S(e,t)),G()&&(function(e){return O.id!==e.id||O[n]!==e[n]}(e)?(O=S(O,t),f.notify()):(m.notify({previousState:O,newState:e}),O=e)),e}function U(){M({process:e=>{if(b(e))return S(e,t)},after:e=>{O=e}},v)}function G(){return void 0!==O[n]}return{expandOrRenewSession:I,expandSession:function(){M({process:e=>G()?P(e):void 0},v)},getSession:()=>O,renewObservable:p,expireObservable:f,sessionStateUpdateObservable:m,restartSession:U,expire:()=>{D(),y(O),P(S(O,t))},stop:()=>{(0,o.vG)(C)},updateSessionState:function(e){M({process:t=>({...t,...e}),after:P},v)}}}},68393:(e,t,n)=>{n.d(t,{g:()=>o});const o={AGENT:"agent",CONSOLE:"console",CUSTOM:"custom",LOGGER:"logger",NETWORK:"network",SOURCE:"source",REPORT:"report"}},69986:(e,t,n)=>{n.d(t,{Sz:()=>v,WA:()=>p,bX:()=>m,hO:()=>y,uT:()=>f});var o=n(36271),r=n(32234),s=n(29336),i=n(59248),a=n(36289),c=n(78218),u=n(66869),d=n(98324),l=n(8822);const p={ALLOW:"allow",MASK:"mask",MASK_USER_INPUT:"mask-user-input"},f={ALL:"all",SAMPLED:"sampled"};function g(e,t){return null==e||"string"==typeof e||(r.Vy.error(`${t} must be defined as a string`),!1)}function m(e,t){return!(void 0!==e&&!(0,i.fp)(e)&&(r.Vy.error(`${t} Sample Rate should be a number between 0 and 100`),1))}function v(e){var t,n,i,p,f,v;if(e&&e.clientToken){if((!(y=e.site)||"string"!=typeof y||/(datadog|ddog|datad0g|dd0g)/.test(y)||(r.Vy.error(`Site should be a valid Datadog site. ${r.xG} ${r.fH}/getting_started/site/.`),0))&&m(e.sessionSampleRate,"Session")&&m(e.telemetrySampleRate,"Telemetry")&&m(e.telemetryConfigurationSampleRate,"Telemetry Configuration")&&m(e.telemetryUsageSampleRate,"Telemetry Usage")&&g(e.version,"Version")&&g(e.env,"Env")&&g(e.service,"Service")){var y;if(void 0===e.trackingConsent||(0,c.Rj)(d.w,e.trackingConsent))return{beforeSend:e.beforeSend&&(0,o.y)(e.beforeSend,"beforeSend threw an error:"),sessionStoreStrategyType:(0,u.nd)(e),sessionSampleRate:null!==(t=e.sessionSampleRate)&&void 0!==t?t:100,telemetrySampleRate:null!==(n=e.telemetrySampleRate)&&void 0!==n?n:20,telemetryConfigurationSampleRate:null!==(i=e.telemetryConfigurationSampleRate)&&void 0!==i?i:5,telemetryUsageSampleRate:null!==(p=e.telemetryUsageSampleRate)&&void 0!==p?p:5,service:e.service||void 0,silentMultipleInit:!!e.silentMultipleInit,allowUntrustedEvents:!!e.allowUntrustedEvents,trackingConsent:null!==(f=e.trackingConsent)&&void 0!==f?f:d.w.GRANTED,trackAnonymousUser:null===(v=e.trackAnonymousUser)||void 0===v||v,storeContextsAcrossPages:!!e.storeContextsAcrossPages,batchBytesLimit:16*a._m,eventRateLimiterThreshold:3e3,maxTelemetryEventsPerPage:15,flushTimeout:30*s.OY,batchMessagesLimit:50,messageBytesLimit:256*a._m,...(0,l.T)(e)};r.Vy.error('Tracking Consent should be either "granted" or "not-granted"')}}else r.Vy.error("Client Token is not configured, we will not send any data.")}function y(e){return{session_sample_rate:e.sessionSampleRate,telemetry_sample_rate:e.telemetrySampleRate,telemetry_configuration_sample_rate:e.telemetryConfigurationSampleRate,telemetry_usage_sample_rate:e.telemetryUsageSampleRate,use_before_send:!!e.beforeSend,use_partitioned_cross_site_session_cookie:e.usePartitionedCrossSiteSessionCookie,use_secure_session_cookie:e.useSecureSessionCookie,use_proxy:!!e.proxy,silent_multiple_init:e.silentMultipleInit,track_session_across_subdomains:e.trackSessionAcrossSubdomains,track_anonymous_user:e.trackAnonymousUser,session_persistence:e.sessionPersistence,allow_fallback_to_local_storage:!!e.allowFallbackToLocalStorage,store_contexts_across_pages:!!e.storeContextsAcrossPages,allow_untrusted_events:!!e.allowUntrustedEvents,tracking_consent:e.trackingConsent}}},71511:(e,t,n)=>{n.d(t,{H:()=>s,N:()=>i});var o=n(52999),r=n(80886);function s(e,t,n){if(document.readyState===t||"complete"===document.readyState)return n(),{stop:o.l};const s="complete"===t?"load":"DOMContentLoaded";return(0,r.q)(e,window,s,n,{once:!0})}function i(e,t){return new Promise((n=>{s(e,t,n)}))}},72765:(e,t,n)=>{n.d(t,{AQ:()=>r,HB:()=>s,Q_:()=>a,_P:()=>i});var o=n(29336);const r=4*o.MA,s=15*o.iW,i=o.$H,a={COOKIE:"cookie",LOCAL_STORAGE:"local-storage"}},74929:(e,t,n)=>{n.d(t,{qR:()=>l});var o=n(36289),r=n(52999),s=n(84601),i=n(32234),a=n(78218);const c=3*o._m,u=16*o._m,d=200;function l(e=2){const t=new Map;let n=!1;function r(r=0){if(n||0===e)return;const s=2===e?c:u;let a=r;t.forEach((e=>{a+=e.getBytesCount()})),a>s&&(function(e){i.Vy.warn(`Customer data exceeds the recommended ${e/o._m}KiB threshold. ${i.xG} ${i.Xs}/#customer-data-exceeds-the-recommended-threshold-warning`)}(s),n=!0)}return{createDetachedTracker:()=>{const e=p((()=>r(e.getBytesCount())));return e},getOrCreateTracker:e=>(t.has(e)||t.set(e,p(r)),t.get(e)),setCompressionStatus:t=>{0===e&&(e=t,r())},getCompressionStatus:()=>e,stop:()=>{t.forEach((e=>e.stop())),t.clear()}}}function p(e){let t=0;const{throttled:n,cancel:i}=(0,r.n)((n=>{t=(0,o.WW)((0,s.s)(n)),e()}),d),c=()=>{i(),t=0};return{updateCustomerData:e=>{(0,a.RI)(e)?c():n(e)},resetCustomerData:c,getBytesCount:()=>t,stop:()=>{i()}}}},78243:(e,t,n)=>{n.d(t,{Z:()=>a,m:()=>i});var o=n(36271),r=n(42182),s=n(32234);function i(e){const t={version:"6.5.1",onReady(e){e()},...e};return Object.defineProperty(t,"_setDebug",{get:()=>r.pM,enumerable:!1}),t}function a(e,t,n){const r=e[t];r&&!r.q&&r.version&&s.Vy.warn("SDK is loaded more than once. This is unsupported and might have unexpected behavior."),e[t]=n,r&&r.q&&r.q.forEach((e=>(0,o.y)(e,"onReady callback threw an error:")()))}},80886:(e,t,n)=>{n.d(t,{l:()=>i,q:()=>s});var o=n(42182),r=n(47197);function s(e,t,n,o,r){return i(e,t,[n],o,r)}function i(e,t,n,s,{once:i,capture:a,passive:c}={}){const u=(0,o.dm)((t=>{(t.isTrusted||t.__ddIsTrusted||e.allowUntrustedEvents)&&(i&&f(),s(t))})),d=c?{capture:a,passive:c}:a,l=window.EventTarget&&t instanceof EventTarget?window.EventTarget.prototype:t,p=(0,r.W)(l,"addEventListener");function f(){const e=(0,r.W)(l,"removeEventListener");n.forEach((n=>e.call(t,n,u,d)))}return n.forEach((e=>p.call(t,e,u,d))),{stop:f}}},84409:(e,t,n)=>{n.d(t,{B9:()=>d,DQ:()=>f,Ri:()=>a,TV:()=>i,z$:()=>p});var o=n(32234),r=n(29336),s=n(1356);function i(e,t,n=0,o){const r=new Date;r.setTime(r.getTime()+n);const s=`expires=${r.toUTCString()}`,i=o&&o.crossSite?"none":"strict",a=o&&o.domain?`;domain=${o.domain}`:"",c=o&&o.secure?";secure":"",u=o&&o.partitioned?";partitioned":"";document.cookie=`${e}=${t};${s};path=/;samesite=${i}${a}${c}${u}`}function a(e){return(0,s.rx)(document.cookie,e)}let c,u;function d(e){return c||(c=(0,s.it)(document.cookie)),c.get(e)}function l(e,t){i(e,"",0,t)}function p(e){if(void 0===document.cookie||null===document.cookie)return!1;try{const t=`dd_cookie_test_${(0,s.lk)()}`,n="test";i(t,n,r.iW,e);const o=a(t)===n;return l(t,e),o}catch(e){return o.Vy.error(e),!1}}function f(){if(void 0===u){const e=`dd_site_test_${(0,s.lk)()}`,t="test",n=window.location.hostname.split(".");let o=n.pop();for(;n.length&&!a(e);)o=`${n.pop()}.${o}`,i(e,t,r.OY,{domain:o});l(e,{domain:o}),u=o}return u}},87363:(e,t,n)=>{n.d(t,{ox:()=>f});var o=n(92555),r=n(44193),s=n(29336),i=n(80886),a=n(93001),c=n(72765),u=n(66869);const d=s.iW,l=c.AQ;let p=[];function f(e,t,n,c){const f=new o.c,g=new o.c,m=(0,u.oC)(e.sessionStoreStrategyType,e,t,n);p.push((()=>m.stop()));const v=(0,r.q)({expireDelay:l});function y(){return{id:m.getSession().id,trackingType:m.getSession()[t],isReplayForced:!!m.getSession().forcedReplay,anonymousId:m.getSession().anonymousId}}return p.push((()=>v.stop())),m.renewObservable.subscribe((()=>{v.add(y(),(0,s.$S)()),f.notify()})),m.expireObservable.subscribe((()=>{g.notify(),v.closeActive((0,s.$S)())})),m.expandOrRenewSession(),v.add(y(),(0,s.Oc)().relative),c.observable.subscribe((()=>{c.isGranted()?m.expandOrRenewSession():m.expire()})),function(e){const{stop:t}=(0,i.l)(e,window,["click","touchstart","keydown","scroll"],(()=>{c.isGranted()&&m.expandOrRenewSession()}),{capture:!0,passive:!0});p.push(t)}(e),function(e){const t=()=>{"visible"===document.visibilityState&&m.expandSession()},{stop:n}=(0,i.q)(e,document,"visibilitychange",t);p.push(n);const o=(0,a.yb)(t,d);p.push((()=>{(0,a.vG)(o)}))}(e),function(e){const{stop:t}=(0,i.q)(e,window,"resume",(()=>m.restartSession()),{capture:!0});p.push(t)}(e),{findSession:(e,t)=>v.find(e,t),renewObservable:f,expireObservable:g,sessionStateUpdateObservable:m.sessionStateUpdateObservable,expire:m.expire,updateSessionState:m.updateSessionState}}},90842:(e,t,n)=>{n.d(t,{D:()=>u});var o=n(47978),r=n(42995),s=n(92555),i=n(32234),a=n(31583);function c(e,t,n){const o={...e};for(const[r,{required:s,type:a}]of Object.entries(t))"string"===a&&r in o&&(o[r]=String(o[r])),s&&!(r in e)&&i.Vy.warn(`The property ${r} of ${n} is required; context will not be sent to the intake.`);return o}function u(e="",{customerDataTracker:t,propertiesConfig:n={}}={}){let u={};const d=new s.c,l={getContext:()=>(0,o.Go)(u),setContext:o=>{!function(e){const t="object"===(0,a.P)(e);return t||i.Vy.error("Unsupported context:",e),t}(o)?l.clearContext():(u=(0,r.a)(c(o,n,e)),null==t||t.updateCustomerData(u)),d.notify()},setContextProperty:(o,s)=>{u=(0,r.a)(c({...u,[o]:s},n,e)),null==t||t.updateCustomerData(u),d.notify()},removeContextProperty:o=>{delete u[o],null==t||t.updateCustomerData(u),c(u,n,e),d.notify()},clearContext:()=>{u={},null==t||t.resetCustomerData(),d.notify()},changeObservable:d};return l}},90970:(e,t,n)=>{function o(){var e;const t=window.navigator;return{status:t.onLine?"connected":"not_connected",interfaces:t.connection&&t.connection.type?[t.connection.type]:void 0,effective_type:null===(e=t.connection)||void 0===e?void 0:e.effectiveType}}n.d(t,{q:()=>o})},92832:(e,t,n)=>{n.d(t,{G:()=>c,n:()=>a});var o=n(29336),r=n(75248),s=n(1356),i=n(30956);function a(e,t,n){const i=function(e,t){const n=`/api/v2/${t}`,o=e.proxy;if("string"==typeof o){const e=(0,r.l2)(o);return t=>`${e}?ddforward=${encodeURIComponent(`${n}?${t}`)}`}if("function"==typeof o)return e=>o({path:n,parameters:e});const s=c(t,e);return e=>`https://${s}${n}?${e}`}(e,t);return{build(r,a){const c=function({clientToken:e,internalAnalyticsSubdomain:t},n,r,i,{retry:a,encoding:c}){const u=["sdk_version:6.5.1",`api:${i}`].concat(r);a&&u.push(`retry_count:${a.count}`,`retry_after:${a.lastFailureStatus}`);const d=["ddsource=browser",`ddtags=${encodeURIComponent(u.join(","))}`,`dd-api-key=${e}`,`dd-evp-origin-version=${encodeURIComponent("6.5.1")}`,"dd-evp-origin=browser",`dd-request-id=${(0,s.lk)()}`];return c&&d.push(`dd-evp-encoding=${c}`),"rum"===n&&d.push(`batch_time=${(0,o.nx)()}`),t&&d.reverse(),d.join("&")}(e,t,n,r,a);return i(c)},urlPrefix:i(""),trackType:t}}function c(e,t){const{site:n=i.NW,internalAnalyticsSubdomain:o}=t;if("logs"===e&&t.usePciIntake&&n===i.NW)return i.$A;if(o&&n===i.NW)return`${o}.${i.NW}`;if(n===i.TC)return`http-intake.logs.${n}`;const r=n.split("."),s=r.pop();return`browser-intake-${r.join("-")}.${s}`}},95844:(e,t,n)=>{n.d(t,{$:()=>i});var o=n(93001),r=n(29336),s=n(68393);function i(e,t,n){let i=0,a=!1;return{isLimitReached(){if(0===i&&(0,o.wg)((()=>{i=0}),r.iW),i+=1,i<=t||a)return a=!1,!1;if(i===t+1){a=!0;try{n({message:`Reached max number of ${e}s by minute: ${t}`,source:s.g.AGENT,startClocks:(0,r.M8)()})}finally{a=!1}}return!0}}}},98324:(e,t,n)=>{n.d(t,{D:()=>s,w:()=>r});var o=n(92555);const r={GRANTED:"granted",NOT_GRANTED:"not-granted"};function s(e){const t=new o.c;return{tryToInit(t){e||(e=t)},update(n){e=n,t.notify()},isGranted:()=>e===r.GRANTED,observable:t}}}}]);
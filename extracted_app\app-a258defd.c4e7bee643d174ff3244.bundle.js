"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6212],{2260:(t,e,r)=>{r.d(e,{u:()=>o});var n=r(96890),i=r.n(n);const s=[48,32,16];function o(t){return i().read(t).then((t=>{const e=t.bitmap,r=e.width;if(t._originalMime!==i().MIME_PNG||r!==e.height)throw new Error("Please give me an png image of 256x256 pixels.");256!==r&&t.resize(256,256,i().RESIZE_BICUBIC);const n=s.map((e=>t.clone().resize(e,e,i().RESIZE_BICUBIC)));return Promise.all(n.concat(t))})).then(a)}function a(t){const e=function(t){const e=Buffer.alloc(6);return e.writeUInt16LE(0,0),e.writeUInt16LE(1,2),e.writeUInt16LE(t,4),e}(t.length),r=[e],n=[];let i=e.length,s=e.length+16*t.length;return t.forEach((t=>{const e=function(t,e){const r=Buffer.alloc(16),n=t.bitmap,i=n.data.length+40,s=n.width>=256?0:n.width,o=s;return r.writeUInt8(s,0),r.writeUInt8(o,1),r.writeUInt8(0,2),r.writeUInt8(0,3),r.writeUInt16LE(1,4),r.writeUInt16LE(32,6),r.writeUInt32LE(i,8),r.writeUInt32LE(e,12),r}(t,s),o=function(t){const e=Buffer.alloc(40),r=t.bitmap.width,n=2*r;return e.writeUInt32LE(40,0),e.writeInt32LE(r,4),e.writeInt32LE(n,8),e.writeUInt16LE(1,12),e.writeUInt16LE(32,14),e.writeUInt32LE(0,16),e.writeUInt32LE(0,20),e.writeInt32LE(0,24),e.writeInt32LE(0,28),e.writeUInt32LE(0,32),e.writeUInt32LE(0,36),e}(t),a=function(t){const e=t.bitmap,r=e.data.length,n=e.width,i=n,s=function(t){return t%32==0?t/8:4*(Math.floor(t/32)+1)}(n)*i,o=Buffer.alloc(r+s);for(let e=0;e<i;e++)for(let r=0;r<n;r++){const s=t.getPixelColor(r,e),a=s>>8&255|(s>>16&255)<<8|(s>>24&255)<<16|(255&s)<<24,u=4*((i-e-1)*n+r);o.writeInt32LE(a,u)}for(let e=0;e<i;e++)for(let s=0;s<n;s++){const a=(255&t.getPixelColor(s,e))>0?0:1,u=(i-e-1)*n+s,c=n%32==0?Math.floor(n/32):Math.floor(n/32)+1,f=Math.floor(u/n),l=Math.floor(u%n),h=1&a,d=r+f*c*4+Math.floor(l/8),g=o.readUInt8(d)|h<<7-l%8;o.writeUInt8(g,d)}return o}(t);r.push(e),n.push(o,a),i+=e.length+o.length+a.length,s+=o.length+a.length})),Buffer.concat(r.concat(n),i)}},18088:(t,e,r)=>{r.d(e,{y:()=>n});const n=t=>({action:"⚔️",adventure:"🗺️",casual:"🌟",fighting:"🥊",fps:"🎯",horror:"👻",indie:"🎨",open_world:"🌎","open-world":"🌎",platformer:"🦊",puzzle:"🧩",racing:"🏎️",rpg:"🐉",shooter:"🔫",simulation:"🎮",sports:"⚽",strategy:"🎲",survival:"🏕️"}[t.slug]??"")},25007:(t,e,r)=>{r.d(e,{d:()=>n});class n{constructor(t,e=0){this.buffer=t,this.position=e}readByte(){return this.readNumber("readUInt8",1)}readInt32(){return this.readNumber("readInt32LE",4)}readUInt32(){return this.readNumber("readUInt32LE",4)}readUInt64(){return this.readNumber("readBigUInt64LE",8)}readInt64(){return this.readNumber("readBigInt64LE",8)}readFloat(){return this.readNumber("readFloatLE",4)}readNumber(t,e){const r=this.buffer[t](this.position);return this.position+=e,r}readNullTerminatedString(t){const e=this.buffer.indexOf(0,this.position),r=this.buffer.toString(t,this.position,e);return this.position=e+1,r}}},26700:(t,e,r)=>{r.d(e,{h:()=>o,B:()=>s});var n=r(79896),i=r(16928);async function s(t,e){const r=i.join(t,(e??"").replace(/[\\/:"*?<>|]+/g,"").replace(/  +/g," "));return await n.promises.mkdir(r,{recursive:!0}),r}async function o(t){try{return await n.promises.access(t,n.constants.F_OK),!0}catch(t){return!1}}},29844:(t,e,r)=>{r.d(e,{LC:()=>b,P8:()=>d,fj:()=>p,oP:()=>m,pD:()=>g});const n=46,i=58,s=47,o=92,a=65,u=97,c=90,f=122;function l(t){return t===s||t===o}function h(t){return t>=a&&t<=c||t>=u&&t<=f}function d(t,e){let r=0,n=-1,s=!0;if(t.length>=2&&h(t.charCodeAt(0))&&t.charCodeAt(1)===i&&(r=2),void 0!==e&&e.length>0&&e.length<=t.length){if(e===t)return"";let i=e.length-1,o=-1;for(let a=t.length-1;a>=r;--a){const u=t.charCodeAt(a);if(l(u)){if(!s){r=a+1;break}}else-1===o&&(s=!1,o=a+1),i>=0&&(u===e.charCodeAt(i)?-1==--i&&(n=a):(i=-1,n=o))}r===n?n=o:-1===n&&(n=t.length)}else{for(let e=t.length-1;e>=r;--e)if(l(t.charCodeAt(e))){if(!s){r=e+1;break}}else-1===n&&(s=!1,n=e+1);if(-1===n)return""}return t.slice(r,n)}function g(t){const e=t.length;if(0===e)return".";let r=-1,n=0;const s=t.charCodeAt(0);if(1===e)return l(s)?t:".";if(l(s)){if(r=n=1,l(t.charCodeAt(1))){let i=2,s=i;for(;i<e&&!l(t.charCodeAt(i));)i++;if(i<e&&i!==s){for(s=i;i<e&&l(t.charCodeAt(i));)i++;if(i<e&&i!==s){for(s=i;i<e&&!l(t.charCodeAt(i));)i++;if(i===e)return t;i!==s&&(r=n=i+1)}}}}else h(s)&&t.charCodeAt(1)===i&&(r=e>2&&l(t.charCodeAt(2))?3:2,n=r);let o=-1,a=!0;for(let r=e-1;r>=n;--r)if(l(t.charCodeAt(r))){if(!a){o=r;break}}else a=!1;if(-1===o){if(-1===r)return".";o=r}return t.slice(0,o)}function b(t){let e=0,r=-1,s=0,o=-1,a=!0,u=0;t.length>=2&&t.charCodeAt(1)===i&&h(t.charCodeAt(0))&&(e=s=2);for(let i=t.length-1;i>=e;--i){const e=t.charCodeAt(i);if(l(e)){if(!a){s=i+1;break}}else-1===o&&(a=!1,o=i+1),e===n?-1===r?r=i:1!==u&&(u=1):-1!==r&&(u=-1)}return-1===r||-1===o||0===u||1===u&&r===o-1&&r===s+1?"":t.slice(r,o)}function m(t){const e=t.length;if(0===e)return!1;const r=t.charCodeAt(0);return l(r)||e>2&&h(r)&&t.charCodeAt(1)===i&&l(t.charCodeAt(2))}function p(...t){if(0===t.length)return".";let e="",r="";for(const n of t)n.length>0&&(e?e+=`\\${n}`:e=r=n);if(!e)return".";let o=!0,a=0;if(l(r.charCodeAt(0))){++a;const t=r.length;t>1&&l(r.charCodeAt(1))&&(++a,t>2&&(l(r.charCodeAt(2))?++a:o=!1))}if(o){for(;a<e.length&&l(e.charCodeAt(a));)a++;a>=2&&(e=`\\${e.slice(a)}`)}return function(t){const e=t.length;if(0===e)return".";const r=t.charCodeAt(0);if(1===e)return r===s?"\\":t;let o=0,a="",u=!1;if(l(r))if(u=!0,l(t.charCodeAt(1))){let r=2,n=r;for(;r<e&&!l(t.charCodeAt(r));)r++;if(r<e&&r!==n){const i=t.slice(n,r);for(n=r;r<e&&l(t.charCodeAt(r));)r++;if(r<e&&r!==n){for(n=r;r<e&&!l(t.charCodeAt(r));)r++;if(r===e)return`\\\\${i}\\${t.slice(n)}\\`;r!==n&&(a=`\\\\${i}\\${t.slice(n,r)}`,o=r)}}}else o=1;else h(r)&&t.charCodeAt(1)===i&&(a=t.slice(0,2),o=2,e>2&&l(t.charCodeAt(2))&&(u=!0,o=3));let c=o<e?function(t,e){let r="",i=0,o=-1,a=0,u=0;for(let c=0;c<=t.length;++c){if(c<t.length)u=t.charCodeAt(c);else{if(l(u))break;u=s}if(l(u)){if(o===c-1||1===a);else if(2===a){if(r.length<2||2!==i||r.charCodeAt(r.length-1)!==n||r.charCodeAt(r.length-2)!==n){if(r.length>2){const t=r.lastIndexOf("\\");-1===t?(r="",i=0):(r=r.slice(0,t),i=r.length-1-r.lastIndexOf("\\")),o=c,a=0;continue}if(0!==r.length){r="",i=0,o=c,a=0;continue}}e&&(r+=r.length>0?"\\..":"..",i=2)}else r.length>0?r+=`\\${t.slice(o+1,c)}`:r=t.slice(o+1,c),i=c-o-1;o=c,a=0}else u===n&&-1!==a?++a:a=-1}return r}(t.slice(o),!u):"";return 0!==c.length||u||(c="."),c.length>0&&l(t.charCodeAt(e-1))&&(c+="\\"),a?u?`${a}\\${c}`:`${a}${c}`:u?`\\${c}`:c}(e)}},32426:(t,e,r)=>{r.d(e,{o:()=>a});var n=r(16148),i=r(5836),s=r(48613),o=r(43938);const a=(0,n.h)((0,i.R)(window,"blur").pipe((0,s.u)(!1)),(0,i.R)(window,"focus").pipe((0,s.u)(!0)),(0,i.R)(window,"visibilitychange").pipe((0,s.u)("visible"===document.visibilityState))).pipe((0,o.Z)("visible"===document.visibilityState&&document.hasFocus()))},38777:(t,e,r)=>{r.d(e,{$U:()=>i,HL:()=>n.HL,Ix:()=>n.Ix,SO:()=>n.SO,Vd:()=>n.Vd,Wn:()=>n.Wn,_M:()=>n._M,_T:()=>n._T,lE:()=>n.lE,nm:()=>n.nm,yB:()=>n.yB});var n=r(92465);function i(t,e,r){return t.on(e,r),(0,n.nm)((()=>t.off(e,r)))}},41224:(t,e,r)=>{function n(t,e){if(t){const r=e.releaseChannels?.find((e=>e.id===t));return!!r?.config.debug}return!1}r.d(e,{Z:()=>n})},50643:(t,e,r)=>{r.d(e,{c:()=>n});class n{#t;#e;#r;#n;#i;#s;#o;constructor(t){this.#i=[],this.#s=[],this.#i.push({type:"text",text:t})}setScenario(t){return this.#t=t,this}setDuration(t){return this.#e=t,this}setActivationType(t){return this.#r=t,this}setLaunchString(t){return this.#n=t,this}addText(t,e){return this.#i.push({type:"text",text:t,placement:e}),this}addImage(t,e,r){return this.#i.push({type:"image",src:t,placement:e??void 0,hints:{crop:r}}),this}addAction(t){return this.#s.push(t),this}setAudio(t){return this.#o=t,this}toXml(){const t=document.implementation.createDocument("","",null),e=t.createElement("toast");this.#t&&e.setAttribute("scenario",this.#t),this.#e&&e.setAttribute("duration",this.#e),this.#r&&e.setAttribute("activationType",this.#r),this.#n&&e.setAttribute("launch",this.#n);const r=t.createElement("visual"),n=t.createElement("binding");n.setAttribute("template","ToastGeneric");for(const e of this.#i){const r=t.createElement(e.type);"text"===e.type&&(r.textContent=e.text,e.placement&&r.setAttribute("placement",e.placement),e.hints&&e.hints.maxLines&&r.setAttribute("hint-maxLines",e.hints.maxLines.toString())),"image"===e.type&&(r.setAttribute("src",e.src),e.placement&&r.setAttribute("placement",e.placement),e.hints&&e.hints.crop&&r.setAttribute("hint-crop",e.hints.crop)),n.appendChild(r)}if(r.appendChild(n),e.appendChild(r),this.#s.length>0){const r=t.createElement("actions");for(const e of this.#s){const n=t.createElement("action");n.setAttribute("content",e.content),n.setAttribute("arguments",e.arguments.toString()),e.activationType&&n.setAttribute("activationType",e.activationType),e.placement&&n.setAttribute("placement",e.placement),e.imageUri&&n.setAttribute("imageUri",e.imageUri),e.hints&&(e.hints.inputId&&n.setAttribute("hint-intputId",e.hints.inputId),e.hints.buttonStyle&&n.setAttribute("hint-buttonStyle",e.hints.buttonStyle),e.hints.toolTip&&n.setAttribute("hint-toolTip",e.hints.toolTip)),r.appendChild(n)}e.appendChild(r)}if(this.#o){const r=t.createElement("audio");this.#o.src&&r.setAttribute("src",this.#o.src),this.#o.loop&&r.setAttribute("loop",this.#o.loop?"true":"false"),this.#o.silent&&r.setAttribute("silent",this.#o.silent?"true":"false"),e.appendChild(r)}return t.appendChild(e),(new XMLSerializer).serializeToString(t.documentElement)}}},54700:(t,e,r)=>{r.d(e,{Q:()=>i});var n=r(35392);async function i(t){const e=await n.promises.open(t,"r");try{const t=Buffer.alloc(14);return(await e.read(t,0,t.length,0)).bytesRead===t.length&&67324752===t.readUInt32LE(0)?function(t,e){const r=t.toString(2).padStart(16,"0"),n=[parseInt(r.substring(0,7),2)+1980,parseInt(r.substring(7,11),2),parseInt(r.substring(11,16),2)].join("-"),i=e.toString(2).padStart(16,"0"),s=[parseInt(i.substring(0,5),2),parseInt(i.substring(5,11),2),2*parseInt(i.substring(11,16),2)].join(":");return new Date(`${n} ${s} GMT+0`)}(t.readUInt16LE(12),t.readUInt16LE(10)):null}finally{await e.close()}}},62679:(t,e,r)=>{r.d(e,{I6:()=>c,og:()=>u});var n=r(79896),i=r(16928),s=r(96610),o=(r("services/bugsnag/index"),r(26700));const a=(0,s.getLogger)("thumbnail-generator"),u=".thumbnails";async function c(t,e){const r=await(0,o.B)(t,u),s=i.normalize(e).split(i.sep),c=`${s.length>=2?s[s.length-2]:""}_${s[s.length-1]}`,f=i.join(r,`${c}_thumbnail.webp`);return new Promise(((t,r)=>{try{const s=document.createElement("video");s.crossOrigin="anonymous",s.preload="auto",s.muted=!0;const o=()=>{s.src&&(URL.revokeObjectURL(s.src),s.src="",s.load()),document.body.contains(s)&&document.body.removeChild(s),s.onloadedmetadata=null,s.onseeked=null,s.onerror=null};s.onloadedmetadata=()=>{s.currentTime=Math.max(1,.1*s.duration)},s.onseeked=()=>{try{const u=document.createElement("canvas");u.width=200,u.height=113;const c=u.getContext("2d");if(!c)throw o(),new Error("Failed to get canvas context");c.drawImage(s,0,0,u.width,u.height);const l=c.getImageData(0,0,u.width,u.height).data;let h=0;for(let t=0;t<l.length;t+=16){const e=l[t],r=l[t+1],n=l[t+2];e<20&&r<20&&n<20&&h++}if(h>l.length/16*.9&&s.currentTime<s.duration-5)return void(s.currentTime=Math.min(s.duration-1,s.currentTime+5));const d=u.toDataURL("image/webp",.9).replace(/^data:image\/webp;base64,/,""),g=Buffer.from(d,"base64");n.promises.writeFile(f,g).then((()=>{o();const r=i.basename(e);a.info(`Generated thumbnail for ${r} at ${f}`),t()})).catch((t=>{o(),r(t)}))}catch(t){o(),r(t)}},s.onerror=()=>{o(),r(new Error(`Thumbnail generation video element failed to load: ${s.error?.code??"Unknown error code."} ${s.error?.message??""}`))},n.readFile(e,((t,e)=>{if(t)return o(),void r(t);const n=new Blob([e],{type:"video/mp4"}),i=URL.createObjectURL(n);s.src=i,setTimeout((()=>{!s.duration&&document.body.contains(s)&&(o(),r(new Error("Timed out while loading video")))}),1e4)}))}catch(t){r(t)}}))}},64415:(t,e,r)=>{r.d(e,{$:()=>s,p:()=>o});var n=r(47120);function i(t,e,r){return t.item[r].toLowerCase().startsWith(e)?1:0}function s(t,e,r,s){if(!e||"string"!=typeof e)return t.slice(0);const o=e.toLowerCase();return new n.A(t,{includeScore:!0,includeMatches:!0,threshold:.18,minMatchCharLength:1,keys:r,shouldSort:!0,ignoreFieldNorm:!0,ignoreLocation:!0}).search(e).sort(((t,e)=>s?t.item[s].localeCompare(e.item[s]):0)).sort(((t,e)=>s?i(e,o,s)-i(t,o,s):0)).map((t=>t.item))}function o(t,e,r,n={}){if(!e||"string"!=typeof e)return t.slice(0);const i=e.toLowerCase().split(/\s+/).filter((t=>t.length>0));return t.filter((t=>i.every((e=>r.some((r=>String(t[r]||"").toLowerCase().includes(e))))))).sort(((t,e)=>{if(n?.prioritizeStartMatches)for(const n of i)for(const i of r){const r=String(t[i]||"").toLowerCase(),s=String(e[i]||"").toLowerCase(),o=r.startsWith(n),a=s.startsWith(n);return o&&!a?-1:!o&&a?1:0}if(n?.sortKey){const r=String(t[n.sortKey]||""),i=String(e[n.sortKey]||"");return r.localeCompare(i)}return 0}))}},64980:(t,e,r)=>{r.d(e,{u:()=>s});const n=new Map;function i(t){let e=n.get(t);if(!e){const r=[];for(let e=0;e<t.length;e++){const n=t.charAt(e);n>="A"&&n<="Z"?r.push("_",n.toLowerCase()):r.push(n)}e=r.join(""),n.set(t,e)}return e}function s(t){return"object"==typeof t&&null!==t?Array.isArray(t)?t.map(s):Object.fromEntries(Object.entries(t).map((([t,e])=>[i(t),s(e)]))):t}},75767:(t,e,r)=>{r.d(e,{x:()=>o});var n=r(79896),i=r(16928),s=r(73841);class o{#a;#u;#c;constructor(t,e){this.#a=t,this.#u=e}async getTemplates(t){if(this.#c??=this.#f(this.#u),t.toString()===this.#u.toString())return await this.#c;const[e,r]=await Promise.all([this.#c,this.#f(t)]);return e.forEach(((t,e)=>{r.has(e)||r.set(e,t)})),r}async#f(t){const e=await n.promises.readFile(i.normalize(`${this.#a}/${t.toString()}.json`),"utf8");return(0,s.T)(JSON.parse(e))}}},77372:(t,e,r)=>{r.d(e,{E3:()=>o,u5:()=>n});var n,i,s=r(35392);class o{#l;constructor(t){this.#l=t}static async load(t){const e=await s.promises.open(t,"r");try{const t=Buffer.alloc(2048),r=await e.read(t,0,t.length,0),n=new o(r.buffer);if(2048===r.bytesRead&&23117===n.dosHeader.magic)return n}finally{await e.close()}throw new Error("Not a PE image.")}get dosHeader(){return new a(this.#l)}get ntHeaders(){return new u(this.#l.slice(this.dosHeader.lfanew))}}class a{#l;constructor(t){this.#l=t}get magic(){return this.#l.readUInt16LE(0)}get lfanew(){return this.#l.readUInt32LE(60)}get stub(){return this.#l.slice(64,this.lfanew-64)}}class u{#l;constructor(t){this.#l=t}get signature(){return this.#l.readUInt32LE(0)}get fileHeader(){return new c(this.#l.slice(4,24))}get optionalHeader(){return new f(this.#l.slice(24,24+this.fileHeader.sizeOfOptionalHeader))}get sectionHeaders(){return new l(this.#l.slice(24+this.fileHeader.sizeOfOptionalHeader),this.fileHeader.numberOfSections)}async getResourceDirectory(t){const e=this.optionalHeader.directories[2].rva;if(0===e)return null;const r=this.sectionHeaders.all().find((t=>t.virtualAddress===e));if(!r)return null;const n=await s.promises.open(t,"r");try{const t=Buffer.alloc(r.sizeOfRawData),e=await n.read(t,0,t.length,r.pointerToRawData);return e.bytesRead!==r.sizeOfRawData?null:new h(e.buffer,r.virtualAddress)}finally{await n.close()}}}!function(t){t[t.I386=332]="I386",t[t.IA64=512]="IA64",t[t.AMD64=34404]="AMD64"}(n||(n={})),function(t){t[t.RELOCS_STRIPPED=1]="RELOCS_STRIPPED",t[t.EXECUTABLE_IMAGE=2]="EXECUTABLE_IMAGE",t[t.LINE_NUMS_STRIPPED=4]="LINE_NUMS_STRIPPED",t[t.IMAGE_FILE_LOCAL_SYMS_STRIPPED=8]="IMAGE_FILE_LOCAL_SYMS_STRIPPED",t[t.AGGRESIVE_WS_TRIM=16]="AGGRESIVE_WS_TRIM",t[t.LARGE_ADDRESS_AWARE=32]="LARGE_ADDRESS_AWARE",t[t.BYTES_REVERSED_LO=128]="BYTES_REVERSED_LO",t[t.X32BIT_MACHINE=256]="X32BIT_MACHINE",t[t.DEBUG_STRIPPED=512]="DEBUG_STRIPPED",t[t.REMOVABLE_RUN_FROM_SWAP=1024]="REMOVABLE_RUN_FROM_SWAP",t[t.NET_RUN_FROM_SWAP=2048]="NET_RUN_FROM_SWAP",t[t.SYSTEM=4096]="SYSTEM",t[t.DLL=8192]="DLL",t[t.UP_SYSTEM_ONLY=16384]="UP_SYSTEM_ONLY",t[t.BYTES_REVERSED_HI=32768]="BYTES_REVERSED_HI"}(i||(i={}));class c{#l;constructor(t){this.#l=t}get machine(){return this.#l.readUInt16LE(0)}get numberOfSections(){return this.#l.readUInt16LE(2)}get timestamp(){return this.#l.readUInt32LE(4)}get pointerToSymbolTable(){return this.#l.readUInt32LE(8)}get numberOfSymbols(){return this.#l.readUInt32LE(12)}get sizeOfOptionalHeader(){return this.#l.readUInt16LE(16)}get characteristics(){return this.#l.readUInt16LE(18)}}class f{#l;constructor(t){this.#l=t}get magic(){return this.#l.readUInt16LE(0)}get majorLinkerVersion(){return this.#l.readUInt8(2)}get minorLinkerVersion(){return this.#l.readUInt8(3)}get linkerVersion(){return`${this.majorLinkerVersion}.${this.minorLinkerVersion}`}get directories(){const t=[];for(let e=0;e<16;e++)t[e]=this.directoryInfo(e);return t}directoryInfo(t){const e=this.#h(t);return{rva:this.#l.readUInt32LE(e),size:this.#l.readUInt32LE(e+4)}}#h(t){return(267===this.magic?96:112)+8*t}}class l{#l;#d;constructor(t,e){this.#l=t,this.#d=e}find(t){for(let e=0;e<this.#d;e++){const r=40*e;let n=this.#l.toString("ascii",r,r+7);if(n=n.substring(0,n.indexOf("\0")),n===t)return this.#g(e)}return null}all(){const t=[];for(let e=0;e<this.#d;e++)t.push(this.#g(e));return t}#g(t){const e=40*t,r=this.#l.toString("ascii",e,e+7);return{name:r.substring(0,r.indexOf("\0")),virtualSize:this.#l.readUInt32LE(e+8),virtualAddress:this.#l.readUInt32LE(e+12),sizeOfRawData:this.#l.readUInt32LE(e+16),pointerToRawData:this.#l.readUInt32LE(e+20),pointerToRelocations:this.#l.readUInt32LE(e+24),pointerToLineNumbers:this.#l.readUInt32LE(e+28),numberOfRelocations:this.#l.readUInt16LE(e+32),numberOfLineNumbers:this.#l.readUInt16LE(e+34),characteristics:this.#l.readUInt32LE(e+36)}}}class h{#l;#b;#m;constructor(t,e,r=0){this.#l=t,this.#b=e,this.#m=r}get characteristics(){return this.#l.readUInt32LE(this.#m+0)}get timestamp(){return this.#l.readUInt32LE(this.#m+4)}get resourceMajorVersion(){return this.#l.readUInt16LE(this.#m+8)}get resourceMinorVersion(){return this.#l.readUInt16LE(this.#m+10)}get resourceVersionString(){return`${this.resourceMajorVersion}.${this.resourceMinorVersion}`}get numberOfNamedEntries(){return this.#l.readUInt16LE(this.#m+12)}get numberOfIdEntries(){return this.#l.readUInt16LE(this.#m+14)}get numberOfEntries(){return this.numberOfNamedEntries+this.numberOfIdEntries}getVersionInfo(){const t=this.entries().find((t=>16===t.id&&t.isSubdirectory));if(!t)return null;let e=t.subdirectory?.entries();if(!e||0===e.length||!e[0].isSubdirectory||!e[0].subdirectory)return null;if(e=e[0].subdirectory.entries(),0===e.length||!e[0].isData)return null;const r=e[0].data;if(!r)return null;const n=r.rva-this.#b,i=this.#l.slice(n,n+r.size);return new g(i)}entries(){const t=this.numberOfEntries,e=[];for(let r=0;r<t;r++)e.push(new d(this.#l,this.#b,this.#m+16+8*r));return e}}class d{#l;#b;#m;constructor(t,e,r){this.#l=t,this.#b=e,this.#m=r}get name(){let t=this.#l.readInt32LE(this.#m);if(t>=0)return null;t&=2147483647;const e=this.#l.readUInt16LE(t);return this.#l.toString("utf16le",t+2,t+2+e)}get id(){const t=this.#l.readInt32LE(this.#m);return t>=0?t:null}get hasName(){return null===this.id}get location(){return 2147483647&this.#l.readUInt32LE(this.#m+4)}get isSubdirectory(){return this.#l.readInt32LE(this.#m+4)<0}get isData(){return!this.isSubdirectory}get subdirectory(){return this.isSubdirectory?new h(this.#l,this.#b,this.location):null}get data(){if(!this.isData)return null;const t=this.location;return{rva:this.#l.readUInt32LE(t),size:this.#l.readUInt32LE(t+4),codePage:this.#l.readUInt32LE(t+8),reserved:this.#l.readUInt32LE(t+12)}}}class g{#l;#m;constructor(t){this.#l=t;let e=38;const r=e+32;for(;e<=r&&4277077181!==t.readUInt32LE(e);)e++;if(e>r)throw new Error("Invalid VS_VERSION_INFO structure.");this.#m=e}#p(t){return[this.#l.readUInt16LE(t+2),this.#l.readUInt16LE(t),this.#l.readUInt16LE(t+6),this.#l.readUInt16LE(t+4)].join(".")}get fileVersion(){return this.#p(this.#m+8)}get productVersion(){return this.#p(this.#m+16)}}},86824:(t,e,r)=>{r.d(e,{H:()=>i,V:()=>s});var n=r(76982);function i(t,e){return s(60*t,60*e)}function s(t,e){return n.randomInt(1e3*t,1e3*e)}},86977:(t,e,r)=>{r.d(e,{w:()=>u});var n=r(77372);const i=4,s=7,o=2556;async function a(t){try{const e=await n.E3.load(t),r=await e.ntHeaders.getResourceDirectory(t);if(!r)throw new Error("Resource directory not found.");const i=r.getVersionInfo();if(!i)throw new Error("Version info not found.");return i.fileVersion}catch{return null}}async function u(t){const e=`${t}\\Microsoft.NET\\Framework\\v4.0.30319\\mscorlib.dll`,r=`${t}\\Microsoft.NET\\Framework64\\v4.0.30319\\mscorlib.dll`,n=await a(r)||await a(e);if(null===n)return!1;const u=n.split("."),c=parseInt(u[0],10);if(c>i)return!0;if(c<i)return!1;const f=parseInt(u[1],10);return f>s||!(f<s)&&parseInt(u[2],10)>=o}},90211:(t,e,r)=>{r.d(e,{R:()=>i});const n=/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/;class i{constructor(t,e,r,n){this.major=t,this.minor=e,this.build=r,this.revision=n}static parse(t){const e=n.exec(t);return e?new this(Number(e[1]),Number(e[2]),Number(e[3]),Number(e[4])):null}compare(t){return this.major-t.major||this.minor-t.minor||this.build-t.build||this.revision-t.revision}eq(t){return 0===this.compare(t)}gt(t){return this.compare(t)>0}gte(t){return this.compare(t)>=0}lt(t){return this.compare(t)<0}lte(t){return this.compare(t)<=0}}}}]);
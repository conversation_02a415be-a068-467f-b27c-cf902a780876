"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5353],{2690:(e,t,s)=>{s.d(t,{J:()=>n});var a=s(15215),i=s("aurelia-framework"),r=s(38777);let n=class{#e;attached(){this.#e=(new r.Vd).pushEventListener(document,"click",this.#t.bind(this)).pushEventListener(document,"auxclick",this.#t.bind(this))}detached(){this.#e?.dispose(),this.#e=null}#t(e){const t=this.#s(e.target);if(!t)return;if("auxclick"===e.type||e.ctrlKey||e.metaKey)return void e.preventDefault();const s=t.getAttribute("href");s&&"#"!==s?s.startsWith("#/")||(e.preventDefault(),window.open(s,"_blank")):e.preventDefault()}#s(e){for(;e;){if("A"===e.tagName)return e;e=e.parentElement}return null}};n=(0,a.Cg)([(0,i.autoinject)()],n)},16128:(e,t,s)=>{s.d(t,{X:()=>m});var a=s(15215),i=s(69278),r=s("aurelia-event-aggregator"),n=s("aurelia-framework"),l=s(96610),o=s(19072),d=s(3972),c=s(54995);const h=(0,l.getLogger)("overlay"),g=["S-1-15-2-957691514-1856554628-2223506514-2735362290-2978057538-3373136399-3720596370","S-1-15-2-3188398512-4273922640-615417156-2416968318-1371955550-2718685005-1518716088"];let m=class{#a;#i;#r;#n;#l;#o;#d;constructor(e,t){this.#r=new r.EventAggregator,this.#n=!1,this.#l=!1,this.#d=[],this.#a=e,this.#i=t}get connected(){return this.connections>0}get connections(){return this.#d.length}get open(){return!!this.#o}onClientConnected(e){return this.#r.subscribe("connect",e)}onClientDisconnected(e){return this.#r.subscribe("disconnect",e)}onMessageReceived(e){return this.#r.subscribe("message",e)}subscriptionChanged(){this.subscription?this.#c():this.#h()}#g(){return!(this.#n||this.#l||this.#o||!this.subscription||"win32"!==this.#i.info.osPlatform)}attached(){setTimeout((()=>this.#c()),2e3)}detached(){this.#n=!0,this.#h()}#m(){return"\\\\.\\pipe\\WeMod\\Overlay"}#p(){setTimeout((()=>this.#c()),1e4)}async#c(){if(this.#g()){this.#l=!0;try{const e=await this.#u(this.#m());e.on("close",(()=>this.#f(e))),this.#o=e,h.debug("Awaiting connection")}catch(e){h.error("Failed to start server",e),this.#p()}finally{this.#l=!1}}}#f(e){e===this.#o&&(this.#d=[],this.#o=null,this.#g()&&this.#p())}#u(e){const t=(0,i.createServer)();return new Promise(((s,a)=>{let i=!1;t.on("error",(e=>{t.close((()=>{i||(i=!0,a(e))}))})),t.on("listening",(async()=>{try{await this.#a.grantFilePermissions(e,g,d.TB.Read|d.TB.Write)}catch(e){return i=!0,void a(e)}t.on("connection",(e=>this.#w(t,e))),i=!0,s(t)})),t.listen(e)}))}#w(e,t){e===this.#o?(h.debug("Client connected"),this.#d.push(t),t.on("end",(()=>{h.debug("Client disconnected");const e=this.#d.indexOf(t);e>=0&&(this.#d.splice(e,1),this.#r.publish("disconnect"))})),t.on("data",(e=>this.#v(e))),this.#r.publish("connect")):t.destroy()}broadcast(e){if(this.#o&&this.connections>0){const t=function(e){const t=JSON.stringify(e),s=Buffer.byteLength(t),a=Buffer.alloc(s+4);return a.writeUInt32LE(s,0),a.write(t,4),a}(e);this.#d.forEach((e=>{try{e.write(t,(e=>{e&&!["EPIPE","ERR_STREAM_DESTROYED"].includes(e.code)&&h.error("Error writing to socket",e)}))}catch{}}))}}#v(e){let t=0;for(;t+4<e.length;){const s=e.readInt32LE(t);t+=4;const a=e.toString("utf8",t,t+s);t+=s;try{this.#r.publish("message",JSON.parse(a))}catch(e){h.error("Error while handling client message",e)}}}#h(){if(this.#o){const e=this.#d;this.#d=[],this.#o.close((()=>{for(const t of e)try{t.destroy()}catch{}})),this.#o=null,e.length>0&&this.#r.publish("disconnect")}}};m=(0,a.Cg)([(0,c.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,c.$t)((e=>e.account?.subscription))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[d.Mz,o.s])],m)},23655:(e,t,s)=>{s.d(t,{g:()=>c});var a=s(15215),i=s("aurelia-framework"),r=s("game-help/cyberpunk-mission-help"),n=s("game-help/elden-ring-boss-guide"),l=s(20057),o=s(54995);const d={[r.TITLE_SLUG]:[{component:"game-help/cyberpunk-mission-help",proOnly:!0,supportedLocales:["en-US"],featureFlagId:"cyberpunk_2077_mission_help"}],[n.TITLE_SLUG]:[{component:"game-help/elden-ring-boss-guide",proOnly:!1,supportedLocales:["en-US"],featureFlagId:"elden_ring_boss_guide"}]};let c=class{#y;#b;constructor(e){this.#b=e}attached(){this.#y=this.#b.onLocaleChanged((()=>this.#I())),this.#I()}detached(){this.#y?.dispose()}#I(){this.locale=this.#b.getEffectiveLocale()}get availableComponents(){return Object.entries(d).reduce(((e,[t,s])=>{const a=s.filter((({proOnly:e,supportedLocales:t,featureFlagId:s})=>!(e&&!this.account.subscription||t&&!t.includes(this.locale.toString())||!(!s||this.features&&this.features[s]))));return a.length>0&&(e[t]=a),e}),{})}};(0,a.Cg)([(0,i.computedFrom)("locale","account.subscription","features"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],c.prototype,"availableComponents",null),c=(0,a.Cg)([(0,i.autoinject)(),(0,o.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,o.$t)((e=>e.account)),features:(0,o.$t)((e=>e.catalog?.features))}}),(0,a.Sn)("design:paramtypes",[l.F2])],c)},39835:(e,t,s)=>{s.d(t,{I:()=>b});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),l=s(89045),o=s(59239),d=s(88120),c=s(45660),h=s(38110),g=s(83802),m=s("dialogs/maps-nps-dialog"),p=s(21795),u=s("shared/dialogs/basic-dialog"),f=s(54995),w=s(48881),v=s(62914),y=s(71006);let b=class{#A;#S;#C;#_;#T;#r;#G;#k;#P;constructor(e,t,s,a,i,r,n){this.#A=new Map,this.#S=new Map,this.#C=e,this.#_=t,this.#T=s,this.#r=a,this.#G=i,this.#k=r,this.#P=n}attached(){}detached(){for(const[e,t]of this.#A.entries())e.dispose(),t.dispose();this.#A.clear();for(const e of this.#S.values())e.dispose();this.#S.clear()}#E(e){const t=e?.userHasSelectedLocation??!1,s=(0,l.A)(Date.now(),new Date(this.lastMapsNPSDialog))>30;!t||this.lastMapsNPSDialog&&!s||(this.#_.open({trigger:"maps-feedback",mapId:e?.mapId}),this.#C.dispatch(w.vk,"lastMapsNPSDialog")),e.dispose(),this.#A.delete(e.webviewWindow)}async openMap(e,t,s=void 0,a=void 0,i=void 0,r=!1){i&&this.#P.event("game_map_opened_from_source",{source:i});const n=this.#T.openWindow(`game-map:${e}`,"game-map",{mapId:t,debug:r?"1":void 0,locationId:s,versionId:a},{mapId:t,titleColor:(0,h.UU)("--theme--background-accent")?.trim()},{},void 0);let l=this.#A.get(n);l||(l=new I(t,this.#C,this.#r,this.#G,this.#k,n),this.#A.set(n,l),this.#S.set(n,n.onClose((()=>{l&&this.#E(l)})))),this.hasUsedMaps||this.#C.dispatch(w.NX,"hasUsedMaps",!0)}};b=(0,a.Cg)([(0,r.autoinject)(),(0,f.m6)({setup:"attached",teardown:"detached",selectors:{lastMapsNPSDialog:(0,f.$t)((e=>e.timestamps?.lastMapsNPSDialog)),hasUsedMaps:(0,f.$t)((e=>e.flags?.hasUsedMaps))}}),(0,a.Sn)("design:paramtypes",[n.il,m.MapsNpsDialogService,y.T,i.EventAggregator,u.BasicDialogService,g.jR,v.j0])],b);class I{#L;#U;#O;#N;#D;#M;#C;#r;#G;#k;constructor(e,t,s,a,i,r){this.userHasSelectedLocation=!1,this.#U=null,this.#O=null,this.mapId=e,this.#C=t,this.#r=s,this.#G=a,this.#k=i,this.webviewWindow=r,this.#N=this.#C.state.pipe((0,o.E)("account","subscription"),(0,d.F)()).subscribe((()=>this.#W())),this.#D=this.webviewWindow.onRpcReady((e=>this.#H(e))),this.#M=this.webviewWindow.onClose((()=>this.#R()))}dispose(){this.userHasSelectedLocation=!1,this.#U&&(this.#U.dispose(),this.#U=null),this.#O&&(this.#O.dispose(),this.#O=null),this.#N.unsubscribe(),this.#D?.dispose(),this.#M?.dispose()}async switchMap(e,t){return!1!==await this.webviewWindow.executeIgnoreUnhandled("switch_game_map",{mapId:e,locationId:t})}async#j(){const e=this.#k.trainer?.getMetadata(g.vO);return!1!==await this.webviewWindow.executeIgnoreUnhandled("set_current_trainer",e?{titleId:e.info.titleId,flags:e.info.blueprint.flags}:null)}async#W(){this.#L&&await this.webviewWindow.executeIgnoreUnhandled("refresh_account",null)}#R(){this.dispose()}#H(e){this.#L=!0,e.setHandler("get_map_settings",(async e=>{if("string"==typeof e){const t=await this.#C.state.pipe((0,c.$)(),(0,o.E)("mapSettings",e)).toPromise();return t?JSON.parse(t):null}})),e.setHandler("set_map_settings",(async e=>{"object"==typeof e&&null!==e&&"string"==typeof e.mapId&&"object"==typeof e.settings&&null!==e.settings&&await this.#C.dispatch(w.hL,e.mapId,JSON.stringify(e.settings))})),e.setHandler("get_title_map_settings",(async e=>{if("string"==typeof e){const t=await this.#C.state.pipe((0,c.$)(),(0,o.E)("titleMapSettings",e)).toPromise();return t?JSON.parse(t):null}})),e.setHandler("set_title_map_settings",(async e=>{"object"==typeof e&&null!==e&&"string"==typeof e.titleId&&"object"==typeof e.settings&&null!==e.settings&&await this.#C.dispatch(w.sR,e.titleId,JSON.stringify(e.settings))})),e.setHandler("map_event",(e=>("render_success"===e&&(this.#U??=this.#k.onTrainerActivated((()=>this.#j())),this.#O??=this.#k.onTrainerEnded((()=>this.#j())),this.#j(),this.webviewWindow.show()),"render_error"===e&&(this.#G.ok("game_maps.map_render_error_message"),this.webviewWindow.handleLoadError("render_error")),!0))),e.setHandler("event",(e=>{if("game_map_select_location"===e.name&&(this.userHasSelectedLocation=!0),"string"==typeof e.name&&"object"==typeof e.data&&null!==e.data){const t=e.data.map_id;if("string"==typeof t)return delete e.data.map_id,this.#r.publish(new p.WC(e.name,{mapId:t,...e.data},e.dispatch)),!0}return!1})),e.setHandler("get_player_coordinates",(async()=>!!this.#k.trainer&&await this.#k.trainer.getPlayerCoordinates())),e.setHandler("set_player_coordinates",(async e=>!!this.#k.trainer&&await this.#k.trainer.setPlayerCoordinates(e)))}}},41032:(e,t,s)=>{s.d(t,{O:()=>l});var a=s(15215),i=s("aurelia-framework"),r=s(68663),n=s(83802);let l=class{#F;#$;#V;constructor(e,t){this.#F=e,this.#$=t}attached(){this.#V=this.#F.onTrainerActivated((e=>this.#x(e)))}async#x(e){const t=e.getMetadata(n.vO);try{await this.#$.recordGamePresence(t.info.gameId)}catch{}}detached(){this.#V?.dispose(),this.#V=null}};l=(0,a.Cg)([(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[n.jR,r.x])],l)},43050:(e,t,s)=>{s.d(t,{AD:()=>C,D6:()=>y,DE:()=>I,DH:()=>H,IW:()=>v,Jo:()=>W,Rp:()=>L,Rq:()=>S,TQ:()=>A,VQ:()=>G,Vr:()=>f,Y2:()=>q,ZT:()=>F,_4:()=>T,b$:()=>D,dL:()=>P,dZ:()=>E,f1:()=>_,gu:()=>w,iA:()=>O,nP:()=>$,no:()=>N,pK:()=>U,qA:()=>k,qE:()=>b,uP:()=>M});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),l=s(32534),o=s(52871),d=s(88120),c=s(24008),h=s(70236),g=s(38777);const m=7,p=7,u=["catalog","gameHistory","installedGameVersions"],f={id:"all-games",getGames:e=>Object.values(e.catalog.games).filter((e=>(0,h.Lt)(e.flags,c.rT.Available))).sort(((e,t)=>(t.trainer?.createdAt??"").localeCompare(e.trainer?.createdAt??""))),mergeSimilarGames:!0},w={id:"all-games-alpha",getGames:e=>f.getGames(e).sort(((t,s)=>{const a=e.catalog.titles[t.titleId],i=e.catalog.titles[s.titleId];return a.name.localeCompare(i.name)})),mergeSimilarGames:!0},v={id:"my-games",getGames:e=>{const t=e.gameHistory,s=Object.values(e.catalog.games).filter((t=>e.installedGameVersions.hasOwnProperty(t.id))).filter((e=>(0,h.Lt)(e.flags,c.rT.Available))).sort(((e,t)=>(e.trainer?.rank??0)-(t.trainer?.rank??0))),a=s.filter((t=>e.installedGameVersions[t.id]?.some((e=>e.version&&t.trainer?.supportedVersions.includes(e.version)))??!1)),i=s.filter((e=>"string"==typeof t[e.id]?.lastPlayedAt)),r=Object.values(e.correlatedUnavailableTitles).flatMap((e=>e.games)).filter((t=>t?.correlationIds?.some((t=>!!e.installedApps[t])))).map((t=>V(t.id,e))).filter((e=>!!e)),n=r.filter((e=>!!e&&"string"==typeof t[e.id]?.lastPlayedAt)),l=[...i,...n].sort(((e,s)=>(t[s?.id??""].lastPlayedAt??"").localeCompare(t[e?.id??""].lastPlayedAt??""))),o=a.filter((e=>!l.includes(e)));l.push(...o);const d=s.filter((e=>!l.includes(e)&&!a.includes(e)));l.push(...d);const g=r.filter((e=>!(!e||n.find((t=>t?.id===e.id)))));return l.push(...g),l.filter((e=>!!e))},mergeSimilarGames:!0},y={id:"unsupported-games",getGames:e=>{const t=e.gameHistory,s=Object.values(e.catalog.games).filter((t=>e.installedGameVersions.hasOwnProperty(t.id))).filter((e=>!(0,h.Lt)(e.flags,c.rT.Available))),a=[...Object.values(e.correlatedUnavailableTitles).flatMap((e=>e.games)).filter((t=>t?.correlationIds?.some((t=>!!e.installedApps[t])))).map((t=>V(t.id,e)))];return a.push(...s.filter((e=>!a.includes(e)))),a.filter((e=>!!e)).sort(((e,s)=>(t[s?.id??""]?.lastPlayedAt??"").localeCompare(t[e?.id??""]?.lastPlayedAt??"")))},mergeSimilarGames:!0},b={id:"supported-games",getGames:e=>{const t=e.gameHistory,s=Object.values(e.catalog.games).filter((t=>e.installedGameVersions.hasOwnProperty(t.id))).filter((e=>(0,h.Lt)(e.flags,c.rT.Available))).sort(((e,t)=>(e.trainer?.rank??0)-(t.trainer?.rank??0))),a=s.filter((t=>e.installedGameVersions[t.id]?.some((e=>e.version&&t.trainer?.supportedVersions.includes(e.version)))??!1)),i=[...s,...a].sort(((e,s)=>(t[s.id]?.lastPlayedAt??"").localeCompare(t[e.id]?.lastPlayedAt??""))),r=a.filter((e=>!i.includes(e)));return i.push(...r),i.sort(((e,s)=>(t[s.id]?.lastPlayedAt??"").localeCompare(t[e.id]?.lastPlayedAt??"")))},mergeSimilarGames:!0},I={id:"my-games-dashboard",getGames:e=>v.getGames(e).filter((t=>e.catalog.games[t.id]||"string"==typeof e.gameHistory[t.id]?.lastPlayedAt)),mergeSimilarGames:!0},A={id:"my-games-collection",getGames:e=>{let t=v.getGames(e);const s=t.filter((e=>(0,h.Lt)(e.flags,c.rT.Available))),a=U.getGames(e).filter((e=>!t.find((t=>t.titleId===e.titleId))));if(t=[...t,...a],!s.length&&t.length){const s=f.getGames(e).filter((e=>e.tags.includes("free"))).filter((t=>!e.installedGameVersions.hasOwnProperty(t.id))).slice(0,10);t=t.concat(s)}return t.sort(((t,s)=>{const a=e.catalog.titles[t.titleId]||e.correlatedUnavailableTitles[t.titleId],i=e.catalog.titles[s.titleId]||e.correlatedUnavailableTitles[s.titleId];return a.name.localeCompare(i.name)}))},mergeSimilarGames:!0,watchKeys:["favoriteTitles"]},S={id:"my-games-activity-feed",getGames:e=>[...Object.values(e.catalog.games).filter((t=>e.installedGameVersions.hasOwnProperty(t.id))),...U.getGames(e),...e.followedGames.map((t=>e.catalog.games[t.gameId])).filter((e=>!!e))].filter((e=>(0,h.Lt)(e.flags,c.rT.Available))).sort(((e,t)=>(t.trainer?.updatedAt??"").localeCompare(e.trainer?.updatedAt??""))),mergeSimilarGames:!0,watchKeys:["favoriteTitles","account"]},C={id:"my-games-follow",getGames:e=>S.getGames(e),mergeSimilarGames:!1,watchKeys:["favoriteTitles","account"]},_={id:"my-games-remote",getGames:e=>{const t=Object.values(e.catalog.games).filter((t=>e.installedGameVersions.hasOwnProperty(t.id))).filter((e=>(0,h.Lt)(e.flags,c.rT.Available))).sort(((t,s)=>e.catalog.titles[t.titleId].name.localeCompare(e.catalog.titles[s.titleId].name))),s=e.gameHistory,a=t.filter((e=>"string"==typeof s[e.id]?.lastPlayedAt)).sort(((e,t)=>(s[t?.id??""]?.lastPlayedAt??"").localeCompare(s[e?.id??""].lastPlayedAt??""))),i=t.filter((e=>!a.includes(e)));return a.push(...i),a}},T={id:"recently-played",getGames:e=>{const t=e.gameHistory;return[...Object.keys(t).filter((e=>"string"==typeof t[e].lastPlayedAt)).filter((t=>e.catalog.games.hasOwnProperty(t))).map((t=>e.catalog.games[t])),...Object.keys(t).filter((e=>"string"==typeof t[e].lastPlayedAt)).filter((t=>!e.catalog.games[t])).map((t=>V(t,e))).filter((e=>!!e))].sort(((e,s)=>(t[s?.id??""]?.lastPlayedAt??"").localeCompare(t[e?.id??""]?.lastPlayedAt??"")))}},G={id:"recently-viewed",getGames:e=>{const t=e.gameHistory;return[...Object.keys(t).filter((e=>"string"==typeof t[e].lastViewedAt)).filter((t=>e.catalog.games.hasOwnProperty(t))).map((t=>e.catalog.games[t])),...Object.keys(t).filter((e=>"string"==typeof t[e].lastViewedAt)).filter((t=>!e.catalog.games[t])).map((t=>V(t,e))).filter((e=>!!e))].filter((e=>!!e)).sort(((e,s)=>(t[s?.id??""]?.lastViewedAt??"").localeCompare(t[e?.id??""]?.lastViewedAt??"")))},mergeSimilarGames:!0},k={id:"recently-played-available",getGames:e=>T.getGames(e).filter((e=>(0,h.Lt)(e.flags,c.rT.Available)))},P={id:"most-popular",getGames:e=>Object.values(e.catalog.games).filter((e=>(0,h.Lt)(e.flags,c.rT.Active))).sort(((e,t)=>(e.trainer?.rank??0)-(t.trainer?.rank??0))),mergeSimilarGames:!0},E={id:"new-releases",getGames:e=>Object.values(e.catalog.games).filter((e=>(0,h.Lt)(e.flags,c.rT.Active))).sort(((e,t)=>{const s=(e.trainer?.createdAt??"").localeCompare(e.trainer?.updatedAt??"")>=0?e.trainer?.createdAt:e.trainer?.updatedAt;return(((t.trainer?.createdAt??"").localeCompare(t.trainer?.updatedAt??"")>=0?t.trainer?.createdAt:t.trainer?.updatedAt)??"").localeCompare(s??"")})),mergeSimilarGames:!0},L={id:"queue",getGames:e=>e.catalog.queue.map((t=>e.catalog.games[t]))},U={id:"favorites",getGames:e=>{const t=e.gameHistory;return[...Object.keys(e.favoriteTitles).filter((t=>e.catalog.titles[t]&&e.catalog.titles[t].gameIds)).flatMap((t=>e.catalog.titles[t].gameIds)).map((t=>e.catalog.games[t])).filter((e=>(0,h.Lt)(e.flags,c.rT.Available))),...Object.keys(e.favoriteTitles).filter((t=>e.correlatedUnavailableTitles[t])).flatMap((t=>e.correlatedUnavailableTitles[t].games)).map((t=>V(t.id,e)))].sort(((t,s)=>{const a=e.catalog.titles[t?.titleId??""]||e.correlatedUnavailableTitles[t?.titleId??""],i=e.catalog.titles[s?.titleId??""]||e.correlatedUnavailableTitles[s?.titleId??""];return a?.name.localeCompare(i?.name)})).sort(((e,s)=>(t[s?.id??""]?.lastPlayedAt??"").localeCompare(t[e?.id??""]?.lastPlayedAt??""))).filter((e=>!!e))},watchKeys:["favoriteTitles"],mergeSimilarGames:!0},O={id:"games-with-maps",getGames:e=>Array.from(new Set(e.catalog.maps.map((e=>e.titleId)))).map((t=>e.catalog.titles[t])).filter((e=>!!e)).flatMap((e=>e.gameIds)).map((t=>e.catalog.games[t])).sort(((t,s)=>{const a=e.catalog.titles[t.titleId],i=e.catalog.titles[s.titleId];return a.name.localeCompare(i.name)})),mergeSimilarGames:!0,watchKeys:["favoriteTitles"]},N={id:"favorite-games-with-maps",getGames:e=>O.getGames(e).filter((t=>!!e.favoriteTitles[t.titleId])),mergeSimilarGames:!0,watchKeys:["favoriteTitles"]},D={id:"games-with-map-teleport",getGames:e=>O.getGames(e).filter((t=>e.catalog.maps.filter((e=>e.titleId===t.titleId)).some((e=>(0,h.Lt)(e.flags,c.nC.HasGameCoordinates))))),mergeSimilarGames:!0,watchKeys:["favoriteTitles"]},M={id:"games-with-map-live-location",getGames:e=>O.getGames(e).filter((t=>{const s=e.catalog.maps.filter((e=>e.titleId===t.titleId)),a=s?.filter((e=>o.g.includes(e.id)));return a?.length>0})),mergeSimilarGames:!0,watchKeys:["favoriteTitles"]},W={id:"games-with-precision-mods",getGames:e=>f.getGames(e).filter((e=>(0,h.Lt)(e.flags,c.rT.PrecisionModsSupported))),mergeSimilarGames:!0,watchKeys:["favoriteTitles"]},H={id:"games-with-overlay-support",getGames:e=>f.getGames(e).filter((e=>(0,h.Lt)(e.flags,c.rT.OverlaySupported))),mergeSimilarGames:!0,watchKeys:["favoriteTitles"]};function R(e,t){const s=e.localeCompare(t);return s>0&&"steam"===e?-1:s<0&&"steam"===t||s<0&&"standalone"===e?1:s>0&&"standalone"===t?-1:s}function j(e,t){const s=e.catalog.games[t],a=e.catalog.titles[s.titleId],i=e.gameHistory[s.id],r=s.trainer?.createdAt?(0,l.A)(new Date(s.trainer.createdAt),Date.now())<=m:null,n=s.trainer?.updatedAt?(0,l.A)(new Date(s.trainer.updatedAt),Date.now())<=p:null;return{titleId:a.id,titleThumbnail:a.thumbnail,titleName:a.name,titleTerms:[...a.terms],gameId:s.id,platformIds:[s.platformId],correlationIds:[...s.correlationIds],gameEdition:s.edition??null,creator:s.creatorId?e.catalog.creators[s.creatorId].username:null,creatorAvatar:s.creatorId?e.catalog.creators[s.creatorId].avatar:null,players:s.trainer?.players??null,secondsPlayed:e.gameHistory[s.id]?.playDuration||0,createdAt:s.trainer?.createdAt??null,updatedAt:s.trainer?.updatedAt??null,lastPlayedAt:(i&&i.lastPlayedAt)??null,isNew:r??!1,isUpdated:(n&&!r)??!1,isAvailable:(0,h.Lt)(s.flags,c.rT.Available),isInstalled:e.installedGameVersions.hasOwnProperty(s.id),isChoice:s.tags.includes("choice"),isFree:s.tags.includes("free"),isFavorite:!!e.favoriteTitles[s.titleId],genres:a.genreIds.map((t=>e.catalog.genres[t]?.slug.replaceAll("-","_"))),rank:s.trainer?.rank??0}}function F(e,t){const s=x(Object.values(e.catalog.games).filter((e=>e.titleId===t?.id&&(0,h.Lt)(e.flags,c.rT.Active))).map((t=>j(e,t.id))));return s.length?s[0]:null}function $(e,t){return{titleId:e.id,titleThumbnail:e.thumbnail,titleName:e.name,titleTerms:[...e.terms],gameId:1===e.games.length?e.games[0].id:null,platformIds:e.games.map((e=>e.platformId)),correlationIds:e.games.flatMap((e=>e.correlationIds)),gameEdition:null,creator:null,creatorAvatar:null,players:null,secondsPlayed:0,createdAt:null,updatedAt:null,lastPlayedAt:null,isNew:!1,isUpdated:!1,isAvailable:!1,isInstalled:t,isChoice:!1,isFree:!1,isFavorite:!1,genres:[],rank:0}}function V(e,t){const s=Object.values(t.correlatedUnavailableTitles).find((t=>t.games?.some((t=>t.id===e))));if(s){const t=s.games.find((t=>t.id===e));return t?{id:t.id,titleId:s.id,platformId:t.platformId,correlationIds:t.correlationIds,purchaseUris:[],tags:[],flags:t.flags}:null}return null}function x(e){const t=[],s=[],a=e.reduce(((e,t)=>(e[t.titleId]||(e[t.titleId]=[]),e[t.titleId].push(t),e)),{});return e.forEach((e=>{if(s.includes(e))return;const i=a[e.titleId];if(i.length>1){s.push(...i),i.push(e);const a=[],r=[];for(const e of i)a.includes(e.platformIds[0])||a.push(e.platformIds[0]),e.gameId&&!r.includes(e.gameId)&&r.push(e.gameId);const n=[...i].sort(((e,t)=>(t.createdAt??"").localeCompare(e.createdAt??"")))[0].createdAt,o=[...i].sort(((e,t)=>(t.updatedAt??"").localeCompare(e.updatedAt??"")))[0].updatedAt,d=n?(0,l.A)(new Date(n),Date.now())<=m:null,c=o?(0,l.A)(new Date(o),Date.now())<=p:null,h={...e,gameEdition:null,gameId:1===r.length?r[0]:null,platformIds:a.sort(R),players:i.reduce(((e,t)=>e+(t.players??0)),0),secondsPlayed:i.reduce(((e,t)=>e+t.secondsPlayed),0),createdAt:n,updatedAt:o,lastPlayedAt:[...i].sort(((e,t)=>(t.lastPlayedAt??"").localeCompare(e.lastPlayedAt??"")))[0].lastPlayedAt,isInstalled:i.some((e=>e.isInstalled)),isChoice:i.some((e=>e.isChoice)),isNew:d??!1,isUpdated:(c&&!d)??!1};t.push(h)}else t.push(e)})),t}class B{#r;#C;#B;constructor(e,t){this.games=[],this.#r=new i.EventAggregator,this.config=t,this.#C=e}initialize(){this.#B=this.#C.state.pipe((0,d.F)(((e,t)=>u.every((s=>e[s]===t[s]))))).subscribe((e=>{this.state=e,this.updateGames()})),this.#B=this.#C.state.pipe((0,d.F)(((e,t)=>(this.config.watchKeys??[]).every((s=>e[s]===t[s]))))).subscribe((e=>{this.state=e,this.updateGames(!0)}))}updateGames(e){if(!this.state)return;const t=this.config.getGames(this.state);!e&&this.games&&t.length===this.games.length&&JSON.stringify(t)===JSON.stringify(this.games)||(this.games=t,this.#r.publish("updated"))}dispose(){this.#B?.unsubscribe(),this.#B=null}onUpdated(e){return this.#r.subscribe("updated",e)}}class K{#K;#q;constructor(e,t){this.items=[],this.#K=e,this.filter=t}dispose(){this.#q.dispose()}initialize(){this.#q=this.#K.onUpdated((()=>this.updateItems())),this.updateItems()}updateItems(){const e=this.#K.state;if(!e)return;const t=this.#K.games;let s=this.#z(t).map((t=>e.catalog.games[t.id]?j(e,t.id):e.correlatedUnavailableTitles[t.titleId]?{...$(e.correlatedUnavailableTitles[t.titleId],!0),platformIds:[t.platformId]}:void 0)).filter((e=>!!e));s=this.#K.config.mergeSimilarGames?x(s):s,this.totalItems=s.length,this.filter?.maxItems&&(s=s.slice(0,this.filter.maxItems)),this.filter?.sort&&s.sort(((e,t)=>this.filter?.sort?.(e,t)??0)),this.items=s}#z(e){return this.filter&&(this.filter.tags&&(e=e.filter((e=>e.tags.some((e=>this.filter.tags?.includes(e)))))),this.filter.genres?.length&&(e=e.filter((e=>{const t=this.#K.state?.catalog.titles[e.titleId];return t&&t.genreIds.some((e=>this.filter.genres?.includes(e)))})))),e}}(0,a.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.toView}),(0,a.Sn)("design:type",Number)],K.prototype,"totalItems",void 0);let q=class{#C;#J;constructor(e){this.#J=new Map,this.#C=e}attached(){}detached(){}async initialize(){const e=[I,A,S,P,f,T,E,L,U];for(const t of e)await(0,g.Wn)(),this.#X(t)}#X(e){const t=new B(this.#C,e);return t.initialize(),this.#J.set(e,t),t}#Y(e){return this.#J.get(e)||this.#X(e)}getFilteredFeed(e,t){const s=this.#Y(e),a=new K(s,t);return a.initialize(),a}};q=(0,a.Cg)([(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[n.il])],q)},50654:(e,t,s)=>{s.d(t,{O:()=>E});var a=s(15215),i=s(16928),r=s("aurelia-event-aggregator"),n=s("aurelia-framework"),l=s(20770),o=s(35392),d=s(68663),c=s(83802),h=s(21795),g=s(19072),m=s("services/bugsnag/index"),p=s("shared/api/index"),u=s("shared/api/value-converters"),f=s(92465),w=s(20057),v=s(54995),y=s(49442),b=s(14046),I=s("shared/utility/resources/value-converters/platform"),A=s(88849),S=s(38110),C=s(48881),_=s(50643),T=s(29844),G=s(86824),k=s(67064),P=s(62914);let E=class{#Q;#Z;#ee;#$;#C;#te;#r;#i;#b;#se;#ae;#k;#P;constructor(e,t,s,a,i,r,n,l,o,d){this.followedGames=[],this.#$=e,this.#C=t,this.#te=s,this.#r=a,this.#i=i,this.#b=r,this.#se=n,this.#ae=l,this.#k=o,this.#P=d}attached(){this.refresh(),this.#Q=(0,f.SO)((()=>this.refresh()),(0,G.H)(20,25)),this.#Z=(0,f.SO)((()=>this.#ie()),6e4)}detached(){this.#Q.dispose(),this.#Z.dispose()}async followGames(e,t){if(0===e.length)return!0;try{let s;for(let a=0;a<e.length;a+=500){const i=e.slice(a,a+500);s=await this.#$.followGames(i,t)}return s&&await this.#re(s),e.forEach((e=>this.#r.publish(new h.HE(e,t)))),!0}catch(e){return this.#te.toast({content:"follow_games.failed_to_set_notification_preferences",type:"alert"}),e instanceof p.ResponseError||(0,m.report)(e),!1}}async unfollowGames(e){if(0===e.length)return!0;try{return await this.#re(await this.#$.unfollowGames(e)),e.forEach((e=>this.#r.publish(new h.t$(e)))),!0}catch{return this.#te.toast({content:"follow_games.failed_to_set_notification_preferences",type:"alert"}),!1}}async unfollowAllGames(){try{return await this.#$.unfollowAllGames(),await this.#re([]),!0}catch{return this.#te.toast({content:"follow_games.failed_to_set_notification_preferences",type:"alert"}),!1}}async refresh(){try{const e=await this.#$.getFollowedGames();await this.#re(e)}catch{}}async#re(e){const t=e.map((e=>this.#ne(e)));JSON.stringify(t)!==JSON.stringify(this.followedGames)&&await this.#C.dispatch(C.jd,t),this.#ie()}#ne(e){const t=this.catalog.games[e.gameId];return{...e,createdAt:t?.trainer?.createdAt||null,updatedAt:t?.trainer?.updatedAt||null}}catalogChanged(){this.#re(this.followedGames)}async#ie(){const e=await this.#i.getSystemIdleTime()>=30;if(e)return void(this.#ee=Date.now());const t=!this.#ee||(0,b.bu)(Date.now(),this.#ee)>=6e4,s=!this.#k.trainer&&!e&&t,a=[],i=new Date(Date.now()-864e5);this.followedGames.forEach((e=>{const t=e.gameId,r=this.gameHistory[t]?.lastNotification;if(!r)return void a.push(t);const n=new Date(e.updatedAt??""),l=new Date(r);(0,b.dS)(n,l)&&(0,b.dS)(i,l)&&s&&this.showNotification(e)})),a.length&&this.#C.dispatch(C.d2,a)}async showNotification(e){if(await this.#C.dispatch(C.Op,e.gameId),!this.allowDesktopNotifications||!this.#i.isWindows10OrGreater)return;const t=this.#se.toView(e.gamePlatformId),s=e.updatedAt===e.createdAt?"release":"update",a=`wemod:titles/${e.titleId}?gameId=${e.gameId}&trigger=notification:${s}/${e.gameId}`,i=new _.c(e.titleName).addText(this.#b.getValue(`follow_games.$game_for_$platform_${s}_message`,{game:e.titleName,platform:t})).setActivationType("protocol").setLaunchString(a);e.titleThumbnail&&await this.#le(i,e.titleThumbnail,s),i.addAction({activationType:"protocol",content:this.#b.getValue("follow_games.play_now"),arguments:a}),await this.#i.showToast(i.toXml())&&this.#P.event("notification_show",{type:s,gameId:e.gameId},P.Io)}async#le(e,t,s){try{const a=this.#ae.toView(t,{size:460}),r=(0,T.LC)(new URL(a).pathname),n=await this.#oe(a),l=await this.#de(n,s),d=l?Buffer.from(await l.arrayBuffer()):null,c=i.join(this.#i.info.paths.temp,(0,A.DU)(16)+r);d&&(await o.promises.writeFile(c,d),e.addImage(c),setTimeout((()=>{o.promises.unlink(c).catch(y.Y)}),5e3))}catch{}}async#de(e,t){const s=this.#b.getValue(`follow_games.${t}_notification_image_watermark`),a=document.createElement("canvas"),i=a.getContext("2d");if(a.width=460,a.height=215,i){i.drawImage(e,0,0,460,215),i.font="bold 30px/40px Inter",i.textBaseline="middle";const t=i.measureText(s);i.beginPath(),i.rect(9,151,t.width+32,54),i.fillStyle=(0,S.oU)("accent"),i.fill(),i.fillStyle="#000",i.fillText(s,25,178)}return await new Promise((e=>a.toBlob(e)))}#oe(e){return new Promise((t=>{const s=new Image;s.crossOrigin="anonymous",s.onload=()=>{t(s)},s.src=e}))}};E=(0,a.Cg)([(0,v.m6)({setup:"attached",teardown:"detached",selectors:{gameHistory:(0,v.$t)((e=>e.gameHistory)),catalog:(0,v.$t)((e=>e.catalog)),allowDesktopNotifications:(0,v.$t)((e=>e.settings?.allowDesktopNotifications)),followedGames:(0,v.$t)((e=>e.followedGames))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[d.x,l.il,k.l,r.EventAggregator,g.s,w.F2,I.PlatformNameValueConverter,u.CdnValueConverter,c.jR,P.j0])],E)},70763:(e,t,s)=>{s.d(t,{i:()=>m});var a=s(15215),i=s("aurelia-framework"),r=s(20770),n=s(21447),l=s(60692),o=s(68539),d=s("cheats/resources/elements/first-play-upgrade-prompt-dialog"),c=s(54995),h=s(14046),g=s(48881);let m=class{#ce;#C;#he;constructor(e,t,s){this.#ce=e,this.#C=t,this.#he=s}attached(){}detached(){}async open(){if(await this.shouldShow()){const e=await this.#ce.open();if(this.#C.dispatch(g.vk,"lastFirstPlayUpgradeCheck"),!e.wasCancelled)return!1}return this.#C.dispatch(g.vk,"lastFirstPlayUpgradeCheck"),!0}async shouldShow(){const e=!!this.#he.assignments.get(l.n.MakeMobileAppFree),t=this.hasUsedInteractiveControls||this.hasUsedHotkeys;if(this.account.subscription||!t||e)return!1;const s=this.lastFirstPlayUpgradeCheck?new Date(this.lastFirstPlayUpgradeCheck):void 0,a=this.lastFirstPlayUpgradePrompt?new Date(this.lastFirstPlayUpgradePrompt):void 0;if(s&&(0,n.A)(s)||a&&(0,n.A)(a))return!1;const i=new Date,r=new Date(this.account.joinedAt);return(0,h.c_)(i,r)<=14?!(a&&s&&(0,h.c_)(s,a)<1||a&&(0,h.c_)(i,a)<2):!(a&&(0,h.c_)(i,a)<30)}};m=(0,a.Cg)([(0,c.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,c.$t)((e=>e.account)),lastFirstPlayUpgradePrompt:(0,c.$t)((e=>e.timestamps?.lastFirstPlayUpgradePrompt)),lastFirstPlayUpgradeCheck:(0,c.$t)((e=>e.timestamps?.lastFirstPlayUpgradeCheck)),hasUsedInteractiveControls:(0,c.$t)((e=>e.flags?.hasUsedInteractiveControls)),hasUsedHotkeys:(0,c.$t)((e=>e.flags?.hasUsedHotkeys))}}),(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[d.FirstPlayUpgradePromptDialogService,r.il,o.z])],m)},75115:(e,t,s)=>{s.d(t,{N:()=>g});var a=s(15215),i=s("aurelia-framework"),r=s(68663),n=s(16953),l=s(19072),o=s(54995),d=s(38777),c=s(67064),h=s(62914);let g=class{#i;#$;#te;#P;#ge;#e;constructor(e,t,s,a){this.#i=e,this.#$=t,this.#te=s,this.#P=a}attached(e){this.#ge=e,this.#e=(new d.Vd).push(this.#i.addNewWindowInterceptor(this.#me.bind(this))).push(this.#i.setNewWindowErrorHandler(this.#pe.bind(this)))}detached(){this.#e?.dispose(),this.#e=null}#pe(e){this.#te.toast({content:"url_handler.failed_to_open_$url",i18nParams:{url:e.length>30?e.substring(0,30)+"...":e},type:"alert",onTop:!0,actions:[{label:"url_handler.copy_url",onclick:()=>this.#i.copyText(e)}]})}async#me(e,t){if("ad"===t){const t=new URL(e);if(t.hostname.match(/(?:\.|^)(?:doubleclick\.net|googleadservices\.com)$/)){const s=t.searchParams.get("adurl");s?.startsWith(n.A.websiteUrl+"/")&&(e=s)}this.#P.event("ad_url_open",{url:e})}else e=this.#ue(e);if(e.startsWith(n.A.websiteUrl+"/")){const s=this.#fe(new URL(e),t);if(!0===s||"string"==typeof s)return s;if("ad"!==t&&e.endsWith("#auth")){e=e.substring(0,e.length-5);try{const t=await this.#$.requestWebAuthCode();return`${e}${e.includes("?")?"&":"?"}auth=${t}`}catch{}}}}#ue(e){return e.startsWith("website://")?e.replace("website://",n.A.websiteUrl+"/"):e}#fe(e,t){if("/cheats"===e.pathname)return this.#ge.navigateToRoute("titles"),!0;if("/queue"===e.pathname)return this.#ge.navigateToRoute("queue"),!0;if("/gift"===e.pathname)return this.#i.activate(`wemod://gift${e.search}`,t),!0;if("/pro"===e.pathname)return this.#i.activate(`wemod://pro${e.search}`,t),!0;const s=e.pathname.split("/");if(3===s.length&&"cheats"===s[1]){const e=s[2].endsWith("-trainers")?s[2].slice(0,-9):s[2],t=Object.values(this.catalog.titles).find((t=>t.slug===e));if(t)return this.#ge.navigateToRoute("title",{titleId:t.id,gameId:"",trainerId:""}),!0}return 3===s.length&&"surveys"===s[1]&&(e.searchParams.set("user_id",this.token.userId),e.toString())}};g=(0,a.Cg)([(0,i.autoinject)(),(0,o.m6)({setup:"attached",teardown:"detached",selectors:{catalog:(0,o.$t)((e=>e.catalog)),token:(0,o.$t)((e=>e.token))}}),(0,a.Sn)("design:paramtypes",[l.s,r.x,c.l,h.j0])],g)},79810:(e,t,s)=>{s.d(t,{r:()=>g});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),l=s(21795),o=s(19072),d=s("shared/utility/resources/value-converters/platform"),c=s(48881),h=s(67064);let g=class{#we;#i;#r;#te;#se;#C;constructor(e,t,s,a,i){this.#we=!1,this.#i=e,this.#r=t,this.#te=s,this.#se=a,this.#C=i}attached(){}detached(){}async installGame(e,t){if(!this.#we){const s=e?.purchaseUris??[];this.#we=!0;let a=!1;try{const i=this.#ve(e);for(const r of s)if(await this.#i.launchExternal(r)){this.#r.publish(new l.U0(e.id,r,i,t)),a=!0;break}}finally{a?setTimeout((()=>this.#we=!1),1e3):(this.#we=!1,s.length&&this.#te.toast({type:"alert",content:"game_installer.were_having_trouble_opening_$platform",i18nParams:{platform:this.#se.toView(e.platformId)}})),this.#C.dispatch(c.NX,"hasClickedInstallGame",!0)}}}#ve(e){return!!e&&e.tags.includes("free")}};g=(0,a.Cg)([(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[o.s,i.EventAggregator,h.l,d.PlatformNameValueConverter,n.il])],g)},82174:(e,t,s)=>{s.d(t,{u:()=>c});var a=s(15215),i=s("aurelia-framework"),r=s(68663),n=s(41882),l=s(54995),o=s(38777),d=s(86824);let c=class{#$;#ye;constructor(e){this.#$=e}attached(){this.#be()}detached(){}#be(){this.#ye=(0,o.Ix)((()=>this.report()),(0,d.H)(15,20))}#Ie(){this.#ye&&(this.#ye.dispose(),this.#ye=null)}async report(){if(this.#Ie(),!this.enabled)return void this.#be();let e=!1;const t={};if(Object.keys(this.installedVersions).forEach((s=>{const a=Array.from(new Set(this.installedVersions[s].filter((e=>this.#Ae(e))).map((e=>e.version)).filter((e=>!!e))));a.length>0&&(t[s]=a,e=!0)})),e)try{await this.#$.reportInstalledGameVersions(t)}catch{}this.#be()}#Ae(e){if(null===e.version)return!1;const t=this.installedApps[e.correlationId];if(t){if("uwp"===t.platform&&t.location.endsWith("\\Content"))return!1;if(t.platform===n.u)return!1}return!0}};c=(0,a.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{enabled:(0,l.$t)((e=>e.settings?.analytics)),installedVersions:(0,l.$t)((e=>e.installedGameVersions)),installedApps:(0,l.$t)((e=>e.installedApps))}}),(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[r.x])],c)},92380:(e,t,s)=>{s.d(t,{$:()=>d});var a=s(15215),i=s("aurelia-framework"),r=s(38110),n=s(71006);const l={guide:"elden-ring"},o={minWidth:400,minHeight:700,preferredWidth:540,preferredHeight:750};let d=class{#Se;#Ce;#T;constructor(e){this.#T=e}attached(){}detached(){this.#Ce?.dispose(),this.#Se?.dispose()}async open(e){this.#Se=this.#T.openWindow("elden-ring-boss-guide","game-guides",l,{},{...o,titleColor:(0,r.UU)("--theme--background-accent")?.trim()}),this.#Ce?.dispose(),this.#Ce=this.#Se.onClose((()=>{this.#Se=null})),await this.#Se.load(),e&&this.setBossName(e)}setBossName(e){this.#Se?.executeIgnoreUnhandled("guide_data_update",{guide:"elden-ring",data:{lastActiveBoss:e}})}};d=(0,a.Cg)([(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[n.T])],d)},98300:(e,t,s)=>{s.d(t,{Hy:()=>L,sg:()=>E});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(68663),l=s(72208),o=s(7892),d=s(83802),c=s(19072),h=s(64931),g=s(11087),m=s(3972),p=s(24008),u=s(64706),f=s(20057),w=s(54995),v=s(49442),y=s(70236),b=s(38777),I=s(90211),A=s(62914),S=s(78563),C=s(16128),_=s(10704),T=s(45953);const G="WeMod.WeModOverlay_t7g4ya3tqt6sw",k="9P2C17TK96BB",P=new I.R(5,721,5282,0),E="1809";let L=class{#o;#k;#i;#a;#_e;#b;#Te;#Ge;#ke;#Pe;#P;#Ee;#Le;#$;#r;#e;#Ue;#Oe;constructor(e,t,s,a,i,r,n,l,o,d,c,h,g,m){this.status="disconnected",this.#o=e,this.#k=t,this.#i=s,this.#a=a,this.#_e=i,this.#b=r,this.#Te=n,this.#Ge=l,this.#ke=o,this.#Pe=d,this.#P=c,this.#Ee=h,this.#Le=g,this.#$=m}attached(){this.#Ne(),this.#e=new b.Vd([this.#r=new b._M,this.#k.onNewTrainer((e=>this.#De(e))),this.#o.onClientConnected((()=>this.#Me())),this.#o.onClientDisconnected((()=>this.#We())),this.#o.onMessageReceived((e=>this.#He(e))),this.#Ge.onCheatStatesChanged((e=>{if(this.#Ue){const t=this.#Ue.getMetadata(d.vO).info;e.gameId===t.gameId&&this.#Re()}})),this.#b.onLocaleChanged((()=>this.#je())),this.#Pe.onDisplayTrainerChanged(this.#Fe.bind(this)),this.#Le.subscribe(S.v,(e=>this.#$e(e.titleId,e.historyItem)))])}detached(){this.#e.dispose()}themeChanged(){this.#je()}accessTokenChanged(){this.#je()}onConnectionStatusChanged(e){return this.#r.subscribe("status",e)}#Me(){this.#Ve("connected")}#We(){0===this.#o.connections&&this.#Ve("disconnected")}#Ve(e){e!==this.status&&(this.status=e,this.#r.publish("status",e))}#He(e){switch(e.type){case"broadcast_request":switch(e.request){case"config":this.#je();break;case"trainer_state":this.#Re();break;case"assistant_info":this.#xe();break;case"auth_code":this.#Be()}break;case"trainer_value":e.stateId===this.#Oe&&this.#Ue?.isActive()?this.#Ue.setValue(e.name,e.value,4,e.cheatId):this.#Re();break;case"cheat_instructions_read":this.#ke.markRead("overlay",this.#Ue?.getMetadata(d.vO)?.info.gameId??"",e.cheatId,e.instructions);break;case"event":this.#P.event(e.name,e.data,e.dispatch??A.Io);break;case"assistant_history_item":{const t=this.#Ee.getAssistant(e.titleId);t&&t.history.add(u.gG.deserialize(e.historyItem));break}case"open_uri":window.open(e.uri,"_blank")}}#Ne(){this.#Oe=Date.now()}#De(e){this.#Ue=e,e.onActivated((()=>this.#Re())),e.onValueSet((e=>{this.#o.connected&&this.#Ke({type:"trainer_value",stateId:this.#Oe,name:e.name,value:e.value})})),e.onEnded((()=>{this.#Ue=null,this.#Ne(),this.#Re()})),this.#Ne(),this.#Re()}#Re(){if(this.#o.connected)if(this.#Ue){const e=this.#Ue.getMetadata(d.vO).info;this.#Ke({type:"trainer_state",stateId:this.#Oe,blueprint:{cheats:e.blueprint.cheats,notes:e.blueprint.notes},strings:this.#qe(e.gameId),values:this.#Ue.isActive()?Object.fromEntries(this.#Ue.values.entries()):null,cheatStates:this.#Ge.cheatStates})}else this.#Ke({type:"trainer_state",stateId:this.#Oe,blueprint:null,strings:null,values:null,cheatStates:null})}#qe(e){return this.gameTranslations[e]?.[this.#b.getEffectiveLocale().toString()]??null}#je(){this.#o.connected&&this.#Ke({type:"config",theme:this.theme,locale:this.#b.getEffectiveLocale().toString()})}#xe(){if(!this.#o.connected)return;const e=this.#Pe.displayTrainer,t=e?this.catalog.titles[e.titleId]:null,s=e?.titleId??"";this.#Ke({type:"assistant_info",titleId:s,titleName:t?.name??"",hasAssistant:(0,y.Lt)(t?.flags??0,p.D1.HasAssistant),history:this.assistantHistory[s]||[]})}async#Be(){if(!this.#o.connected)return;const e=await this.#$.requestOverlayAuthCode().catch((()=>null));null!==e&&this.#Ke({type:"auth_code",code:e})}#$e(e,t){this.#o.connected&&"overlay"!==t.client&&this.#Ke({type:"assistant_history_item",titleId:e,historyItem:t.serialize()})}#Ke(e){this.#o.broadcast(e)}isSystemSupported(){if("win32"!==this.#i.info.osPlatform||"x64"!==this.#i.info.osArch)return!1;const e=this.#i.info.osVersion.split(".");return e.length>=3&&"10"===e[0]&&"0"===e[1]&&parseInt(e[2],10)>=17763}async isAppInstalled(){return null!==await this.#a.getInstalledPackageId(G)}async isGameBarEnabled(){return!!await this.#_e.queryValue("HKEY_CURRENT_USER\\System\\GameConfigStore\\GameDVR_Enabled")&&0!==await this.#_e.queryValue("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR\\AppCaptureEnabled")}async openInStore(e){this.#P.event("overlay_install_click",{location:e},A.Io);const t=await this.#a.getInstalledPackageId("Microsoft.XboxGamingOverlay_8wekyb3d8bbwe"),s=t&&I.R.parse(h.L.parse(t).version)?.gte(P)?`ms-gamebar://launchforeground/activate/StoreWidget?productId=${k}`:`ms-windows-store://pdp/?ProductId=${k}`;await this.#i.launchExternal(s)||window.open(`https://www.microsoft.com/store/apps/${k}`,"_blank")}async openGameBarSettings(){await this.#i.launchExternal("ms-settings:gaming-gamebar")||window.open("https://support.xbox.com/help/games-apps/game-setup-and-play/troubleshoot-game-bar-windows","_blank")}open(){return this.#i.launchExternal(`ms-gamebar://launchforeground/activate/${G}_App_WeMod.Overlay.Trainer`)}async refreshFeatureStatus(){let e;return e=this.isSystemSupported()?await this.isAppInstalled()?await this.isGameBarEnabled()?"installed":"game-bar-disabled":"not-installed":"unsupported",(this.featureStatus&&"installed"===e||"installed"===this.featureStatus)&&this.#Te.refreshApps().catch(v.Y),this.featureStatus=e,e}#Fe(){this.#xe()}};L=(0,a.Cg)([(0,w.m6)({setup:"attached",teardown:"detached",selectors:{theme:(0,w.$t)((e=>e.settings?.theme)),gameTranslations:(0,w.$t)((e=>e.gameTranslations)),catalog:(0,w.$t)((e=>e.catalog)),assistantHistory:(0,w.$t)((e=>e.assistantHistory))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[C.X,d.jR,c.s,m.Mz,g.J,f.F2,_.r,o.p,l.u,T.m,A.j0,S.s,i.EventAggregator,n.x])],L)}}]);
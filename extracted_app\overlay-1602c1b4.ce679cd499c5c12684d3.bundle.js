"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[723],{96555:(e,i,t)=>{t.d(i,{o:()=>a});class a{constructor(e,i){this.platform=e,this.sku=i}static parse(e){const i=e.indexOf(":");return new a(e.substring(0,i),e.substring(i+1))}toString(){return`${this.platform}:${this.sku}`}}},"shared/cheats/resources/elements/mod-row.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>d});var a=t(14385),n=t.n(a),s=new URL(t(44013),t.b),c=new URL(t(87332),t.b),o=n()(s);const d='<template> <require from="./mods-list.scss"></require> <require from="./mod-timer"></require> <require from="./number-input"></require> <require from="./save-cheats-icon"></require> <require from="./selection-input"></require> <require from="./trainer-hotkey"></require> <require from="./pin-mod-icon"></require> <require from="../value-converters/blueprint-translation"></require> <require from="../value-converters/group-cheats"></require> <require from="../../../resources/custom-attributes/context-menu"></require> <require from="../../../resources/custom-attributes/swipeable"></require> <require from="../../../resources/elements/incremental-input"></require> <require from="../../../resources/elements/pro-badge"></require> <require from="../../../resources/elements/range-input"></require> <require from="../../../resources/elements/scalar-input"></require> <require from="../../../resources/elements/toggle.html"></require> <require from="../../../resources/elements/tooltip"></require> <div context-menu="unique-id.bind: \'context-menu-\' + cheat.uuid; disabled.bind: isRemote;" swipeable="disabled.bind: !isRemote || cheat.parent;" class="cheat ${$last ? \'last\' : \'\'} ${$parent.$last ? \'last-child-mod\': \'\'} ${inputsDisabled[cheat.uuid] && playButtonLabel ? \'mod-disabled\' : \'\'} ${isModDisabled(cheat.uuid) ? \'mod-broken\' : \'\'} ${cheat.instructions && !readCheats.has(cheat.uuid) ? \'is-unread\' : \'\'} ${showHotkeys ? \'hotkeys\' : \'\'} ${showInputs ? \'input\' : \'\'} ${isModTimerEnabled(cheat.type) ? \'mod-timer-enabled\' : \'\'}"> <div class="name-input-container ${cheat.type !== \'button\' && cheat.type !== \'toggle\' && !showHotkeys && showInputs ? \'cheat--compact-mobile\' : \'\' }"> <div class="mod-disabled-hint ${playButtonLabelClass}"> <i class="mod-disabled-hint-icon"></i> <div class="mod-info"> <div class="mod-name-wrapper ${cheat.description || cheat.instructions ? \'has-tooltip\' : \'\'}" mouseleave.trigger="markCheatInstructionsRead(cheat)"> <span class="mod-name hint-text">${cheat.name | blueprintTranslation:translations}</span> <span class="mod-tooltip" if.bind="cheat.description || cheat.instructions"> <i class="icon mod-disabled-info-icon"></i> <tooltip class="info" direction="top-left" if.bind="!isRemote && !isOverlay"> <div slot="content"> <div class="info-content ${(cheat.description.length || 0) + (cheat.instructions.length || 0) > 250 ? \'wide\' : \'\'}"> <p class="message" if.bind="cheat.description"> ${cheat.description | blueprintTranslation:translations} </p> <hr if.bind="cheat.description && cheat.instructions"> <template if.bind="cheat.instructions"> <h5 class="header"> <span class="info-icon"> <i class="icon"></i> </span> <span>${\'trainer_cheats_list.cheat_instructions\' | i18n}</span> </h5> <p class="message"> ${cheat.instructions | blueprintTranslation:translations} </p> </template> </div> </div> </tooltip> </span> <tooltip class="info" direction="top-center" if.bind="(isRemote || isOverlay) && (cheat.description || cheat.instructions)"> <div slot="content"> <div class="info-content ${(cheat.description.length || 0) + (cheat.instructions.length || 0) > 250 ? \'wide\' : \'\'}"> <p class="message" if.bind="cheat.description"> ${cheat.description | blueprintTranslation:translations} </p> <hr if.bind="cheat.description && cheat.instructions"> <template if.bind="cheat.instructions"> <h5 class="header"> <span class="info-icon"> <i class="icon"></i> </span> <span>${\'trainer_cheats_list.cheat_instructions\' | i18n}</span> </h5> <p class="message">${cheat.instructions | blueprintTranslation:translations}</p> </template> </div> </div> </tooltip> </div> <span class="hint-text"> ${\'trainer_cheats_list.press_$x_to_use_mod\' | i18n:{ x: playButtonLabel }} </span> </div> </div> <div if.bind="isModDisabled(cheat.uuid)" class="mod-broken-hint"> <i class="mod-disabled-hint-icon"></i> <div class="mod-info"> <div class="mod-name-wrapper"> <span class="mod-name hint-text">${cheat.name | blueprintTranslation:translations}</span> </div> <span class="broken-text hint-text">${\'trainer_cheats_list.broken_mod_hint\' | i18n}</span> </div> </div> <div class="cheat-name"> <div class="cheat-name-inner ${cheat.description || cheat.instructions ? \'has-tooltip\' : \'\'}" mouseleave.trigger="markCheatInstructionsRead(cheat, $event)"> <save-cheats-icon if.bind="trainerSupportsSaveCheats && !isModDisabled(cheat.uuid)" can-use-save-cheats.bind="canUseSaveCheats" cheat.bind="cheat" enabled.bind="isSaveCheatsEnabled || cheatStates[cheat.uuid].saveCheats.enabled" dialog-disabled.bind="dialogDisabled"></save-cheats-icon> <i if.bind="isModDisabled(cheat.uuid)" class="broken-icon"></i> <span class="label-wrapper"> <span class="label">${cheat.name | blueprintTranslation:translations}</span> <span class="info-icon" if.bind="cheat.description || cheat.instructions"> <i class="icon"></i> <tooltip class="info" direction="top-left" if.bind="!isRemote && !isOverlay"> <div slot="content"> <div class="info-content ${(cheat.description.length || 0) + (cheat.instructions.length || 0) > 250 ? \'wide\' : \'\'}"> <p class="message" if.bind="cheat.description"> ${cheat.description | blueprintTranslation:translations} </p> <hr if.bind="cheat.description && cheat.instructions"> <template if.bind="cheat.instructions"> <h5 class="header"> <span class="info-icon"> <i class="icon"></i> </span> <span>${\'trainer_cheats_list.cheat_instructions\' | i18n}</span> </h5> <p class="message"> ${cheat.instructions | blueprintTranslation:translations} </p> </template> </div> </div> </tooltip> </span> <tooltip class="info" direction="top-center" if.bind="(isRemote || isOverlay) && (cheat.description || cheat.instructions)"> <div slot="content"> <div class="info-content ${(cheat.description.length || 0) + (cheat.instructions.length || 0) > 250 ? \'wide\' : \'\'}"> <p class="message" if.bind="cheat.description"> ${cheat.description | blueprintTranslation:translations} </p> <hr if.bind="cheat.description && cheat.instructions"> <template if.bind="cheat.instructions"> <h5 class="header"> <span class="info-icon"> <i class="icon"></i> </span> <span>${\'trainer_cheats_list.cheat_instructions\' | i18n}</span> </h5> <p class="message">${cheat.instructions | blueprintTranslation:translations}</p> </template> </div> </div> </tooltip> </span> </div> </div> <div class="input" if.bind="showInputs"> <template if.bind="!(isBetaMod(cheat) && betaModsEnabled && !canUseBetaMods)"> <span class="pro-upgrade" if.bind="!enabled && !isPro && canUseInAppControls" show.bind="[\'loading\', \'play\', \'install\', \'install_for_free\'].includes(trainerPlayButtonState)"> <span class="loading"> <template if.bind="trainerPlayButtonState === \'loading\'">${\'loading.loading_message\' | i18n}</template> <template else>${\'trainer_cheats_list.press_$x_above_to_get_started\' | i18n:{x: playButtonLabel}}</template> <i><inline-svg src="'+o+'"></inline-svg></i> </span> </span> <button class="pro-upgrade" pro-cta="trigger: cheat_control" if.bind="enabled && !canUseInAppControls"> <div> <span innerhtml.bind="\'trainer_cheats_list.only_for_pro_members\' | i18n | markdown"></span> <span> ${\'trainer_cheats_list.upgrade_now\' | i18n} <i><inline-svg src="'+o+'"></inline-svg></i> </span> </div> </button> </template> <div class="input-inner"> <button class="beta-upgrade" pro-cta="trigger: beta_mod" if.bind="isBetaMod(cheat) && betaModsEnabled && !canUseBetaMods"> <div> <span innerhtml.bind="\'trainer_cheats_list.only_pro_members_can_use_beta_mods\' | i18n | markdown"></span> <span> ${\'trainer_cheats_list.upgrade_now\' | i18n} <i><inline-svg src="'+o+'"></inline-svg></i> </span> </div> </button> <div if.bind="showInputs" class="input-shrinkwrap ${inputsDisabled[cheat.uuid] || isModDisabled(cheat.uuid) ? \'disabled\' : \'\'} ${canUseInAppControls && !isPro ? \'force-enable\' : \'\'}"> <range-input if.bind="cheat.type === \'slider\'" use-overlay.bind="useRangeInputOverlay" value.to-view="variables[cheat.target] & debounce" change.call="handleNumericChange(cheat, value)" step.bind="cheat.args.step" min.bind="cheat.args.min" max.bind="cheat.args.max" disabled.bind="(cheat.parent && !isPro) || inputsDisabled[cheat.uuid] || (isRemote && !enabled && !cheatStates[cheat.uuid].saveCheats.enabled)"></range-input> <number-input if.bind="cheat.type === \'number\'" value.to-view="variables[cheat.target]" change.call="handleNumericChange(cheat, value)" min.bind="cheat.args.min" max.bind="cheat.args.max" step.bind="cheat.args.step" button.bind="cheat.args.button ? true : false" disabled.bind="(cheat.parent && !isPro) || inputsDisabled[cheat.uuid] || (isRemote && !enabled && !cheatStates[cheat.uuid].saveCheats.enabled)"></number-input> <toggle if.bind="cheat.type === \'toggle\'" value.to-view="!!variables[cheat.target]" change.call="handleToggleChange(cheat, value)" disabled.bind="(cheat.parent && !isPro) || inputsDisabled[cheat.uuid] || (isRemote && !enabled && !cheatStates[cheat.uuid].saveCheats.enabled)"> </toggle> <button if.bind="cheat.type === \'button\'" click.delegate="handleButtonClick(cheat)" class="button-input" disabled.bind="(cheat.parent && !isPro) || inputsDisabled[cheat.uuid] || (isRemote && !enabled && !cheatStates[cheat.uuid].saveCheats.enabled)"> ${\'trainer_cheats_list.apply\' | i18n} </button> <selection-input if.bind="cheat.type === \'selection\'" value.to-view="variables[cheat.target]" change.call="handleSelectionChange(cheat, value)" options.bind="cheat.args.options" disabled.bind="(cheat.parent && !isPro) || inputsDisabled[cheat.uuid]" button.bind="cheat.args.button ? \'trainer_cheats_list.button_\' + cheat.args.button : null"></selection-input> <scalar-input if.bind="cheat.type === \'scalar\'" options.bind="cheat.args.options" value.to-view="variables[cheat.target]" postfix.bind="cheat.args.postfix" default.bind="cheat.args.default" disabled.bind="!isPro || inputsDisabled[cheat.uuid]" change.call="handleScalarChange(cheat, value)"></scalar-input> <incremental-input if.bind="cheat.type === \'incremental\'" options.bind="cheat.args.options" change.call="handleIncrementalChange(cheat, value)" disabled.bind="!isPro || inputsDisabled[cheat.uuid]"></incremental-input> </div> </div> </div> </div> <div class="mod-row-controls"> <div if.bind="childMods[cheat.uuid]" class="precision-mods"> <div class="precision-mods-button-container" data-tooltip-trigger-for="pro-precision-mod-tooltip"> <button click.delegate="handlePrecisionModClick($event, cheat.uuid, category.category)" class="precision-mods-button ${!isRemote && !isOverlay && (!hasViewedPrecisionModsSection || (!isPro && precisionModsSectionsOpen[getPrecisionModSectionName(cheat.uuid, category.category)])) ? \'shimmer\' : \'\'}"> <i class="precision-mods-button-icon precision"></i> <span class="precision-mods-button-text">${\'trainer_cheats_list.pro\' | i18n}</span> </button> <div if.bind="!isRemote && !isOverlay && !precisionModsSectionsViewed[cheat.uuid]" class="precision-mods-button-indicator"></div> </div> </div> </div> <div class="hotkeys" if.bind="showHotkeys"> <div class="hotkeys-inner"> <template if.bind="isBetaMod(cheat) && betaModsEnabled"> <i class="lock-icon"><inline-svg src="'+n()(c)+'"></inline-svg></i> </template> <template else> <trainer-hotkey if.bind="!(!isPro && !!cheat.parent)" repeat.for="hotkey of cheat.hotkeys" cheat-blueprints.bind="trainer.blueprint.cheats" cheat-type.bind="cheat.type" cheat-uuid.bind="cheat.uuid" default-keys.bind="hotkey" disabled.bind="hotkeyEditDisabled || (!isPro && !!cheat.parent)" game-id.bind="trainer.gameId" game-preferences.bind="gamePreferences" hotkey-index.bind="$index" set-custom-hotkey.call="setCustomHotkey()" on-hotkey-press.call="onHotkeyPress()" synced-keys.bind="cheatStates[cheat.uuid].hotkeys[$index]" ea.bind="ea"></trainer-hotkey> </template> </div> </div> <pin-mod-icon class.bind="pinnedModsList.includes(cheat.uuid) ? \'active\' : \'\'" if.bind="!pinDisabled" tooltip-id.bind="`pin-mod-promo-tooltip-${cheat.uuid}-${category.category}`" active.bind="pinnedModsList.includes(cheat.uuid)" can-use-pinned-mods.bind="canUsePinnedMods" dialog-disabled.bind="dialogDisabled" has-clicked-mod-pin.bind="hasClickedModPin" is-auto-pin.bind="isAutoPin(cheat.uuid)" handle-pin-click.call="handlePinClick(cheat)"></pin-mod-icon> <div class="swipe-menu" if.bind="isRemote && !cheat.parent"> <div class="swipe-menu-btn pin-btn" click.delegate="handlePinClick(cheat)"> <i class="swipe-menu-btn-icon pin">keep</i> <span if.bind="pinnedModsList.includes(cheat.uuid)" class="label"> ${\'trainer_cheats_list.unpin\' | i18n} </span> <span else class="label">${\'trainer_cheats_list.pin\' | i18n}</span> </div> </div> </div> <wm-context-menu id.bind="\'context-menu-\' + cheat.uuid" if.bind="!isRemote"> <ul> <li click.delegate="handlePinClick(cheat)" class="context-menu-item-pin ${isOverlay && !pinnedModsList.includes(cheat.uuid) && !canUsePinnedMods ? \'disabled\' : \'\'}" pro-cta="trigger: pin_mod_context_menu; disabled.bind: canUsePinnedMods || dialogDisabled || (!dialogDisabled && isAutoPin(cheat.uuid) && pinnedModsList.includes(cheat.uuid)); feature: pin_mods;"> <span if.bind="pinnedModsList.includes(cheat.uuid)"> ${\'trainer_cheats_list.unpin\' | i18n} </span> <span else>${\'trainer_cheats_list.pin\' | i18n}</span> <pro-badge if.bind="!canUsePinnedMods" class="small"></pro-badge> </li> </ul> </wm-context-menu> </template> '}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1620,8826],{17275:(e,a,i)=>{i.d(a,{C:()=>l});var o=i(15215),t=i("aurelia-dialog"),s=i("aurelia-event-aggregator"),n=i("aurelia-framework");let l=class{#e;#a;constructor(e,a){this.#e=e,this.#a=a}async open(e,a,i){this.#a.publish("dialog:open:start",this.viewModelClass);const o=this.#e.open({viewModel:this.viewModelClass,host:document.querySelector("#dialogs")??void 0,model:e,lock:a,keyboard:!a,ignoreTransitions:i});return o.then((()=>this.#a.publish("dialog:open:complete",this.viewModelClass))),await o.whenClosed()}};l=(0,o.Cg)([(0,n.autoinject)(),(0,o.Sn)("design:paramtypes",[t.DialogService,s.EventAggregator])],l)},92465:(e,a,i)=>{i.d(a,{_T:()=>b,HL:()=>p,Vd:()=>u,_M:()=>h,yB:()=>c,nm:()=>n,Wn:()=>l,SO:()=>d,Ix:()=>r,lE:()=>s});var o=i("aurelia-event-aggregator"),t=i(49442);const s=Object.freeze(n(t.Y));function n(e){return{dispose:()=>{e&&(e(),e=void 0)}}}function l(e){return new Promise((a=>setTimeout(a,e)))}function r(e,a){const i=setTimeout(e,a);return n((()=>clearTimeout(i)))}function d(e,a){const i=setInterval(e,a);return n((()=>clearInterval(i)))}function c(e,a,i,o){return e.addEventListener(a,i,o),n((()=>e.removeEventListener(a,i,o)))}const g=e=>!(null==e);class u{#i;constructor(e=[]){this.#i=e.filter(g)}get disposed(){return null===this.#i}push(e){return null!==e&&this.#i?.push(e),this}dispose(){if(null!==this.#i){const e=this.#i;this.#i=null;for(let a=e.length-1;a>=0;a--)e[a].dispose()}}pushFunction(e){return this.push(n(e))}pushEventListener(e,a,i,o){return this.push(c(e,a,i,o))}}class h extends o.EventAggregator{constructor(){super(...arguments),this.#o=new u}#o;subscribe(e,a){if(!this.#o)return s;const i=super.subscribe(e,a);return this.#o.push(i),i}subscribeOnce(e,a){if(!this.#o)return s;const i=super.subscribeOnce(e,a);return this.#o.push(i),i}dispose(){this.#o&&(this.#o.dispose(),this.#o=null)}}class b extends Error{constructor(e="The operation was canceled."){super(e),Object.setPrototypeOf(this,b.prototype)}}class p{constructor(){this.#a=new h,this.#t=!1}#a;#t;get canceled(){return this.#t}cancel(){this.canceled||(this.#t=!0,this.#a?.publish("_"))}onCancel(e){return this.#a?.subscribeOnce("_",e)??null}dispose(){this.#a&&(this.#a.dispose(),this.#a=null)}}},99626:(e,a,i)=>{function o(e){return"zh-CN"===e?e:e.split("-")[0]}i.d(a,{o:()=>o})},"shared/dialogs/basic-dialog":(e,a,i)=>{i.r(a),i.d(a,{BasicDialog:()=>l,BasicDialogService:()=>c,DialogResult:()=>r,DialogTextKey:()=>d});var o=i(15215),t=i("aurelia-dialog"),s=i("aurelia-framework"),n=i(20057);let l=class{constructor(e){this.controller=e}async activate(e){this.config=e,this.controller.settings.lock=!e.cancelable}close(e){this.controller.ok(e.label.toString())}};var r,d;l=(0,o.Cg)([(0,s.autoinject)(),(0,o.Sn)("design:paramtypes",[t.DialogController])],l),function(e){e[e.OK=0]="OK",e[e.Yes=1]="Yes",e[e.No=2]="No",e[e.Cancel=3]="Cancel",e[e.Help=4]="Help"}(r||(r={})),function(e){e.OK="basic_dialog.ok",e.Yes="basic_dialog.yes",e.Confirm="basic_dialog.confirm",e.No="basic_dialog.no",e.Cancel="basic_dialog.cancel",e.Help="basic_dialog.help"}(d||(d={}));let c=class{#s;constructor(e){this.#s=e}show(e){return this.open(e).whenClosed((e=>e.wasCancelled?null:e.output))}open(e){return this.#s.open({viewModel:"shared/dialogs/basic-dialog",host:document.querySelector("#dialogs")??void 0,startingZIndex:1001,model:{...e,options:e.options.map((e=>"string"==typeof e||e instanceof n.LW?{label:e}:e)),align:e.align||"center",cancelable:e.cancelable??!0}})}async ok(e,a){return await this.show({message:e,messageParams:a,options:[{label:"basic_dialog.ok"}]}),r.OK}async yesNo(e,a,i,o,t,s,n){const l=o??d.Yes,c=t??d.No;return await this.show({size:"narrow",message:e,messageParams:a,options:i?[{label:l},{label:c,style:"primary"}]:[{label:c},{label:l,style:"primary"}],dontShowAgainFlag:s,dontShowAgainValue:n})===l?r.Yes:r.No}async help(e,a,i=[]){let o=[{label:"basic_dialog.ok",style:"primary"}];a&&o.push("basic_dialog.help"),o=o.concat(i);const t=await this.show({message:e,options:o});if("basic_dialog.help"===t)return window.open(a,"_blank"),r.Help;if("basic_dialog.ok"===t)return r.OK;for(const e of i)if("string"==typeof e){if(e===t)return e}else if(e instanceof n.LW){if(e.toString()===t)return t}else if(e.label===t)return t;return r.Cancel}};c=(0,o.Cg)([(0,s.autoinject)(),(0,o.Sn)("design:paramtypes",[t.DialogService])],c)},"shared/dialogs/basic-dialog.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>o});const o='<template> <require from="../resources/elements/close-button"></require> <require from="./basic-dialog.scss"></require> <ux-dialog class="basic-dialog ${config.className} align-${config.align} size-${config.size || \'full\'}"> <ux-dialog-header> <close-button if.bind="!controller.settings.lock" click.trigger="controller.cancel()" tabindex="0"></close-button> <header if.bind="config.headerLabel"> <span>${config.headerLabel | i18n}</span> </header> </ux-dialog-header> <ux-dialog-body> <div><p innerhtml.bind="config.message | i18n:config.messageParams | markdown"></p></div> </ux-dialog-body> <ux-dialog-footer> <button repeat.for="option of config.options" class="${config.options.length === 1 || option.style === \'primary\' ? \'\' : \'secondary\'}" click.delegate="close(option)"> <img if.bind="option.icon" src.bind="option.icon"> <span>${option.label | i18n}</span> </button> <label class="dont-show-again ${config.dontShowAgainValue ? \'checked\' : \'\'}" if.bind="config.dontShowAgainFlag" click.delegate="config.dontShowAgainValue = !config.dontShowAgainValue"> <span class="checkbox"></span> <span class="label">${\'basic_dialog.dont_remind_again\' | i18n}</span> </label> </ux-dialog-footer> </ux-dialog> </template> '},"shared/dialogs/basic-dialog.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>u});var o=i(31601),t=i.n(o),s=i(76314),n=i.n(s),l=i(4417),r=i.n(l),d=new URL(i(81206),i.b),c=n()(t()),g=r()(d);c.push([e.id,`ux-dialog.basic-dialog{width:auto;min-width:300px;max-width:550px}ux-dialog.basic-dialog.size-narrow{max-width:300px}ux-dialog.basic-dialog ux-dialog-body{padding-bottom:20px}ux-dialog.basic-dialog ux-dialog-body a{color:var(--theme--highlight);transition:filter .15s;cursor:pointer}ux-dialog.basic-dialog ux-dialog-body a:hover{filter:brightness(1.2)}ux-dialog.basic-dialog ux-dialog-body img{margin-top:8px;max-width:100%;border-radius:5px}ux-dialog.basic-dialog ux-dialog-footer{padding:0}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;display:flex;padding-top:20px;justify-content:center}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again,ux-dialog.basic-dialog ux-dialog-footer .dont-show-again *{cursor:pointer}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again>*:first-child{margin-right:9px}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;width:15px;height:15px;background:rgba(0,0,0,0);border-color:rgba(255,255,255,.25)}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox,ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox *{cursor:pointer}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox:checked:before{opacity:1}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${g});mask:url(${g})}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox:before{left:1px;top:0;width:15px;height:11px;transform:scale(1)}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color)}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .label,ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .label *{cursor:pointer}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again.checked .checkbox:before{opacity:1}`,""]);const u=c},"shared/featurebase/featurebase-feedback-dialog":(e,a,i)=>{i.r(a),i.d(a,{FeaturebaseFeedbackDialog:()=>c,FeaturebaseFeedbackDialogService:()=>g});var o=i(15215),t=i("aurelia-dialog"),s=i("aurelia-framework"),n=i(99626),l=i(17275),r=i(92465),d=i(20057);let c=class{#n;#l;constructor(e,a){this.controller=e,this.locale="en",this.loading=!0,this.#l=a}activate(e){e?.boardId&&(this.boardId=e.boardId),e?.jwtToken&&(this.jwtToken=e.jwtToken),e?.metadata&&(this.metadata=encodeURIComponent(JSON.stringify(e.metadata)))}attached(){this.#n=(0,r.yB)(window,"message",this.handleMessage.bind(this)),this.#r()}detached(){this.#n?.dispose()}handleMessage(e){if("FeaturebaseWidget"===e.data?.target)switch(e.data?.data?.action){case"menuHeightChangedFeedback":this.iframeEl.style.height=`${e.data.data.data}px`,this.iframeEl.style.minHeight=`${e.data.data.data}px`;break;case"initializeOrg":this.loading=!1;break;case"feedbackSubmitted":this.controller.close(!0)}}#r(){this.locale=(0,n.o)(this.#l.getEffectiveLocale().toString())??"en"}};c=(0,o.Cg)([(0,s.autoinject)(),(0,o.Sn)("design:paramtypes",[t.DialogController,d.F2])],c);let g=class extends l.C{constructor(){super(...arguments),this.viewModelClass="shared/featurebase/featurebase-feedback-dialog"}};g=(0,o.Cg)([(0,s.autoinject)()],g)},"shared/featurebase/featurebase-feedback-dialog.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>o});const o='<template> <require from="./featurebase-feedback-dialog.scss"></require> <require from="../resources/elements/close-button"></require> <require from="../resources/elements/loading-indicator"></require> <ux-dialog class="featurebase-feedback-dialog ${loading ? \'loading\' : \'\'}"> <ux-dialog-header> <close-button click.delegate="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <loading-indicator if.bind="loading"></loading-indicator> <iframe if.bind="boardId" src="https://hub.wemod.com/${locale}/widget/feedbackWidgetSSR?theme=dark&defaultBoard=${boardId}&metaData=${metadata}&jwtToken=${jwtToken}" ref="iframeEl"></iframe> </ux-dialog-body> </ux-dialog> </template> '},"shared/featurebase/featurebase-feedback-dialog.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>l});var o=i(31601),t=i.n(o),s=i(76314),n=i.n(s)()(t());n.push([e.id,".featurebase-feedback-dialog{width:448px;padding:0;overflow:hidden;background:#1a1e29 !important}.featurebase-feedback-dialog ux-dialog-body{display:flex}.featurebase-feedback-dialog close-button{right:20px;top:20px;z-index:1}.featurebase-feedback-dialog loading-indicator{display:flex;width:100%;height:300px;align-items:center;justify-content:center}.featurebase-feedback-dialog iframe{width:100%;height:361px;border:0;transition:height .15s ease-in-out,opacity .15s}.featurebase-feedback-dialog.loading iframe{height:300px;opacity:0;position:absolute;left:0;top:0}.featurebase-feedback-dialog:not(.loading) loading-indicator{display:none}.featurebase-feedback-dialog:not(.loading) iframe{height:initial;opacity:1;position:relative;left:auto;top:auto}",""]);const l=n}}]);
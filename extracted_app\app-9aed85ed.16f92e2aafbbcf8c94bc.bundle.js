"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7126],{"settings/settings-menu":(t,e,i)=>{i.r(e),i.d(e,{SettingsMenu:()=>f});var n=i(15215),s=i("aurelia-framework"),o=i(18776),a=i(62914),r=i(40127),d=i(10191),l=i(67064),g=i(16953),c=i(19072),p=i(20057),u=i(54995),m=i(70236),b=i(41224);const h=[{id:"general",title:"settings_dialog.general",settings:[{name:"settings_dialog.close_to_tray",key:"closeToTray",default:!0,type:"toggle",description:"settings_dialog.close_to_tray_description"},{name:"settings_dialog.language",key:"language",component:"./resources/elements/language-selector",description:"settings_dialog.language_description"},{name:"settings_dialog.release_channel",description:"settings_dialog.release_channel_description",component:"./resources/elements/release-channel-selector"},{name:"settings_dialog.anonymous_reporting",key:"analytics",default:!0,type:"toggle",description:"settings_dialog.anonymous_reporting_description"}],component:"./resources/elements/general-settings"},{id:"customization",title:"settings_dialog.customization",settings:[{name:"settings_dialog.disable_hotkeys",key:"disableHotkeys",default:!1,type:"toggle",description:"settings_dialog.disable_hotkeys_description",proOnly:!0},{name:"settings_dialog.save_cheats",key:"enableSaveCheatsByDefault",default:!0,type:"toggle",description:"settings_dialog.save_cheats_description",proOnly:!0},{name:"settings_dialog.enable_overlay",key:"enableOverlay",default:!0,type:"toggle",description:"settings_dialog.enable_overlay_description",beta:!0,track:!0,overlayOnly:!0},{name:"settings_dialog.overlay_hotkey",default:!0,component:"./resources/elements/overlay-hotkey-selector",description:"settings_dialog.overlay_hotkey_description",beta:!0,overlayOnly:!0,track:!0},{name:"settings_dialog.disable_assistant",key:"disableAssistant",default:!1,type:"toggle",description:"settings_dialog.disable_assistant_description",proOnly:!0},{name:"settings_dialog.disable_auto_pins",key:"disableAutoPins",default:!1,type:"toggle",description:"settings_dialog.disable_auto_pins_description",freeOnly:!0},{name:"settings_dialog.cheat_sounds",key:"cheatSounds",default:!0,type:"toggle",description:"settings_dialog.cheat_sounds_description",track:!0},{name:"settings_dialog.cheat_volume",key:"cheatSoundVolume",default:!0,component:"./resources/elements/cheat-volume-slider",description:"settings_dialog.cheat_volume_description",track:!0},{name:"settings_dialog.sound_pack",description:"settings_dialog.sound_pack_description",component:"./resources/elements/sound-pack"},{name:"settings_dialog.theme",key:"theme",default:"default",component:"./resources/elements/theme-selector",column:!0,description:"settings_dialog.theme_description"}]},{id:"capture",title:"settings_dialog.capture",settings:[{name:"settings_dialog.enable_capture",key:"enableCapture",default:!1,type:"toggle",description:t=>t.host.isWindows11OrGreater?"settings_dialog.enable_capture_description":"settings_dialog.enable_capture_description_windows_10",track:!0,beta:!0},{name:"settings_dialog.capture_quality_preset",key:"captureVideoPresetName",description:"settings_dialog.capture_quality_preset_description",type:"radio",default:r.Hi,radioOptions:Object.entries(r.og).map((([t,e])=>({labelKey:"settings_dialog.capture_quality_preset_$resolution@$fps",labelParams:{resolution:e.resolutionLabel,fps:e.fps},value:t}))),track:!0},{name:"settings_dialog.capture_highlight_length",key:"captureBufferSeconds",description:"settings_dialog.capture_highlight_length_description",type:"radio",default:r.ye,radioOptions:[{labelKey:"settings_dialog.capture_highlight_length_$seconds_seconds",labelParams:{seconds:15},value:15},{labelKey:"settings_dialog.capture_highlight_length_$seconds_seconds",labelParams:{seconds:30},value:30},{labelKey:"settings_dialog.capture_highlight_length_$seconds_seconds",labelParams:{seconds:60},value:60}],track:!0},{name:"settings_dialog.capture_audio_device",key:"captureAudioOutputDeviceId",description:"settings_dialog.capture_audio_device_description",type:"radio",default:"default",radioOptions:[{labelKey:"settings_dialog.capture_audio_enabled",labelParams:{},value:"default"},{labelKey:"settings_dialog.capture_audio_disabled",labelParams:{},value:"disabled"}],track:!0}],component:"./resources/elements/capture-settings"},{id:"accessibility",title:"settings_dialog.accessibility",settings:[{name:"settings_dialog.use_windows_high_contrast_mode",key:"useWindowsContrastMode",default:!1,type:"toggle",description:"settings_dialog.use_windows_high_contrast_mode_description"}]},{id:"account",title:"settings_dialog.my_account",collapsed:!0,children:[{id:"profile",title:"settings_dialog.profile",breadcrumb:"settings_dialog.my_account",component:"./resources/elements/profile-settings"},{id:"billing",title:"settings_dialog.billing",breadcrumb:"settings_dialog.my_account",component:"./resources/elements/billing-settings"},{id:"password",title:"settings_dialog.password",breadcrumb:"settings_dialog.my_account",component:"./resources/elements/change-password"}]},{id:"creator",title:p.F2.literal("Creator"),creatorOnly:!0,settings:[{name:p.F2.literal("Use external console"),key:"trainerLibExternalConsole",default:!1,type:"toggle",description:p.F2.literal("Use the external console for trainer logging.")}]},{id:"trainer-testing",title:p.F2.literal("Trainer Testing"),trainerTesterOnly:!0,settings:[{name:p.F2.literal("Alpha features"),key:"trainerLibAlpha",default:!1,type:"toggle",description:p.F2.literal("Enable alpha features in trainers.")},{name:p.F2.literal("Simulate alpha features"),key:"trainerLibAlphaSimulate",default:!1,type:"toggle",description:p.F2.literal("Enable simulation mode for alpha features.")}]},{id:"notifications",title:"settings_dialog.notifications",component:"./resources/elements/notifications-settings",settings:[{name:"settings_dialog.desktop_notifications",key:"allowDesktopNotifications",default:!0,windows10Only:!0,type:"toggle",description:"settings_dialog.desktop_notifications_description"}]},{id:"debug",title:p.F2.literal("Debug Settings"),component:"./resources/elements/debug-settings",requireDebug:!0,settings:[{name:p.F2.literal("Override country"),key:"overrideCountry",default:null,type:"text",maxLength:2,description:p.F2.literal("Override the country for checkout / pricing.")},{name:p.F2.literal("Prevent catalog refresh"),key:"preventCatalogRefresh",default:!1,type:"toggle",description:p.F2.literal("Prevent the catalog from automatically refreshing. Useful for testing things that require modifying the catalog, like followed game notifications.")},{name:p.F2.literal("Override screenshot window title"),key:"overrideScreenshotWindowTitle",default:null,type:"text",description:p.F2.literal("Overrides the name of the window used to grab screenshots")},{name:p.F2.literal("Time machine"),description:p.F2.literal("Change the reported system time to the specified value. Your system timezone will be used. Webviews will not be affected. Changes are lost on reload."),component:"./resources/elements/time-machine"}]},{id:"ads-debug",title:p.F2.literal("Debug Ads"),adDeveloperOnly:!0,settings:[{name:p.F2.literal("Override ad provider"),key:"overrideAdProvider",default:null,type:"text",description:p.F2.literal("Force the use of the specified ad provider")}]},{id:"components-debug",component:"./resources/elements/debug-components",title:p.F2.literal("Debug Components"),requireDebug:!0,settings:[]}];let f=class{#t;#e;#i;#n;#s;constructor(t,e,i,n,s){this.map=h,this.selectedGroup=h[0],this.host=t,this.#e=e,this.#i=i,this.#n=n,this.#s=s,this.appVersion=`v${t.info.version}`}activate(t,e,i){this.#t=i?.previousInstruction,t?.group&&this.selectGroup(t.group)}bind(){this.devMode=g.A.debug,this.debugMode=(0,b.Z)(this.host.info.releaseChannel,this.account)}get isCreator(){return(0,m.Lt)(this.account.flags,64)}get isTrainerTester(){return(0,m.Lt)(this.account.flags,16384)}get isAdDeveloper(){return(0,m.Lt)(this.account.flags,8192)}copyAppVersion(){this.host.copyText(this.appVersion),this.#e.toast({content:"settings_dialog.app_version_copied_toast",type:"ok",onTop:!0})}openChangelog(){const t=new URL("https://wemod.featurebase.app/api/v1/auth/access/jwt");t.searchParams.set("jwt",this.account.featurebaseJwt??""),t.searchParams.set("return_to","https://hub.wemod.com/changelog"),this.#i.event("changelog_link_click",{trigger:"settings_menu"}),window.open(t.toString(),"_blank")}selectGroup(t){this.map.forEach((e=>{if(e.children&&(e.collapsed=!0),t&&(t===e.id&&(this.selectedGroup=e,this.#s.navigateToRoute("settings",{group:t},{replace:!0,trigger:!1})),e.children)){const i=e.id+"/";e.children.forEach((n=>{t===i+n.id&&(this.selectedGroup=n,e.collapsed=!1,this.#s.navigateToRoute("settings",{group:n.id},{replace:!0,trigger:!1}))}))}}))}selectGroupObj(t){let e=null;this.map.forEach((i=>{const n=!!i.children?.includes(t);i.children&&!n&&(i.collapsed=!0),n&&(e=i)})),(t.settings||t.component)&&(this.selectedGroup=t),t.children&&(t.collapsed=!t.collapsed,this.selectedGroup=t.children[0]),null!==e?this.#s.navigateToRoute("settings",{group:e.id+"/"+t.id},{replace:!0,trigger:!1}):this.#s.navigateToRoute("settings",{group:t.id},{replace:!0,trigger:!1})}get breadcrumbs(){const t=[];return this.selectedGroup.breadcrumb?t.push([this.selectedGroup.breadcrumb]):t.push(["settings_dialog.settings"]),t}get filteredSelectedGroupSettings(){return this.selectedGroup?.settings?this.selectedGroup.settings.filter((t=>!(t.freeOnly&&this.subscription||t.proOnly&&!this.subscription||t.debugOnly&&!this.devMode&&!this.debugMode||t.windows10Only&&!this.host.isWindows10OrGreater||t.overlayOnly&&!this.#n.canUseOverlay))):[]}selectedGroupChanged(){const t=this.selectedGroup.title instanceof p.LW?this.selectedGroup.title.value:this.selectedGroup.title;this.#i?.screenView({name:t,class:"Settings"})}close(){this.#t?.config?.name?this.#s.navigateToRoute(this.#t.config.name,this.#t.params):this.#s.navigateToRoute("dashboard")}getSettingDescriptionKey(t){return"function"==typeof t.description?t.description(this):t.description}};(0,n.Cg)([s.observable,(0,n.Sn)("design:type",Object)],f.prototype,"selectedGroup",void 0),(0,n.Cg)([(0,s.computedFrom)("account.flags"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],f.prototype,"isCreator",null),(0,n.Cg)([(0,s.computedFrom)("account.flags"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],f.prototype,"isTrainerTester",null),(0,n.Cg)([(0,s.computedFrom)("account.flags"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],f.prototype,"isAdDeveloper",null),(0,n.Cg)([(0,s.computedFrom)("selectedGroup"),(0,n.Sn)("design:type",Array),(0,n.Sn)("design:paramtypes",[])],f.prototype,"breadcrumbs",null),(0,n.Cg)([(0,s.computedFrom)("selectedGroup"),(0,n.Sn)("design:type",Array),(0,n.Sn)("design:paramtypes",[])],f.prototype,"filteredSelectedGroupSettings",null),f=(0,n.Cg)([(0,u.m6)({selectors:{account:(0,u.$t)((t=>t.account)),catalog:(0,u.$t)((t=>t.catalog)),subscription:(0,u.$t)((t=>t.account?.subscription))}}),(0,s.autoinject)(),(0,n.Sn)("design:paramtypes",[c.s,l.l,a.j0,d.s,o.Ix])],f)},"settings/settings-menu.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});var n=i(14385),s=i.n(n),o=new URL(i(76703),i.b);const a='<template> <require from="./settings-menu.scss"></require> <require from="../resources/elements/breadcrumbs"></require> <require from="../resources/elements/beta-tag.html"></require> <require from="../resources/custom-attributes/detach-el"></require> <require from="../shared/resources/elements/close-button"></require> <div class="settings-menu" detach-el> <div class="app-content"> <div class="sidebar"> <div class="sidebar-background"> <div class="sidebar-scrollable"> <div class="sidebar-wrapper"> <div class="sidebar-item" repeat.for="group of map" if.bind="devMode || (\n                                         (!group.requireDebug || debugMode) &&\n                                         (!group.creatorOnly || isCreator) &&\n                                         (!group.trainerTesterOnly || isTrainerTester) &&\n                                         (!group.adDeveloperOnly || isAdDeveloper))"> <div class="sidebar-item-label ${selectedGroup == group || group.collapsed === false ? \'selected\' : \'\'} ${group.children ? \'\' : \'no-children\'}" click.delegate="selectGroupObj(group)" tabindex="0"> <i class="caret"><inline-svg src="'+s()(o)+'"></inline-svg></i> <span>${group.title | i18n}</span> </div> <div class="sidebar-item-content ${group.collapsed ? \'collapsed\' : \'\'}" if.bind="group.children"> <ul class="sidebar-item-children"> <li repeat.for="child of group.children" class="sidebar-item-child ${selectedGroup == child ? \'current\' : \'\'}" click.delegate="selectGroupObj(child)" tabindex="0"> <div class="info"> <div class="label">${child.title | i18n}</div> </div> </li> </ul> </div> </div> </div> <div class="actions"> <button click.delegate="openChangelog()">${\'settings_dialog.changelog\' | i18n}</button> <a click.delegate="copyAppVersion()" href="#">${appVersion}</a> </div> </div> </div> </div> <div class="app-view"> <div class="view-background"> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" overflow-fade="vertical"> <breadcrumbs items.bind="breadcrumbs"></breadcrumbs> <header if.bind="selectedGroup.title">${selectedGroup.title | i18n}</header> <section class="settings-content"> <div class="settings"> <template if.bind="selectedGroup.settings"> <template repeat.for="setting of filteredSelectedGroupSettings"> <div class="setting"> <div class="setting-name"> <beta-tag if.bind="setting.beta"></beta-tag> <span>${setting.name | i18n}</span> </div> <div class="setting-info ${setting.column ? \'column-layout\' : \'\'}"> <div class="setting-description" innerhtml.bind="getSettingDescriptionKey(setting) | i18n | markdown"></div> <div class="setting-input"> <compose if.bind="!setting.component" view-model="./resources/elements/setting" model.bind="setting" setting.bind="setting" view="./resources/elements/${setting.type}.html"> </compose> <compose if.bind="setting.component" view-model="${setting.component}"></compose> </div> </div> </div> </template> </template> <div class="group-component" if.bind="selectedGroup.component"> <compose view-model="${selectedGroup.component}"></compose> </div> </div> </section> </div> </div> </div> </div> </div> <close-button click.trigger="close()" tabindex="0"></close-button> </div> </template> '},"settings/settings-menu.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>p});var n=i(31601),s=i.n(n),o=i(76314),a=i.n(o),r=i(4417),d=i.n(r),l=new URL(i(83959),i.b),g=a()(s()),c=d()(l);g.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,.settings-menu .settings .setting .setting-info .setting-description em:before{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.settings-menu{background:rgba(0,0,0,0);position:fixed;left:0;top:0;height:100vh;width:100vw;z-index:9999}.settings-menu close-button{position:fixed;right:20px;top:40px}.settings-menu .sidebar-item-children>*:first-child{display:none}.settings-menu .app-content{background-color:var(--theme--background);color:#fff}.settings-menu .app-content .sidebar .sidebar-background{padding-top:36px}.settings-menu .app-view{padding-left:298px !important}.settings-menu .app-view .view-background .view-scrollable{padding-top:36px !important}.settings-menu .app-view .view-background .view-scrollable:before{content:"";display:block !important;position:absolute;left:0;top:0;width:100%;height:36px;background:rgba(var(--theme--background--rgb), 0.95);z-index:1}.settings-menu header{font-weight:800;font-size:35px;line-height:40px;font-weight:800;color:#fff;display:inline-flex;align-items:center;margin-bottom:33px}.settings-menu header>*+*{margin-left:15px;line-height:0}.settings-menu .settings-content{flex:1}.settings-menu .settings{display:flex;flex:1;flex-direction:column;min-height:100%;max-width:710px}.settings-menu .settings>compose{display:flex;flex:1;flex-direction:column}.settings-menu .settings>*+*{border-top:1px solid rgba(255,255,255,.1)}.settings-menu .settings .setting,.settings-menu .settings .group-component{padding:20px 0}.settings-menu .settings .setting:first-child,.settings-menu .settings .group-component:first-child{padding-top:0}.settings-menu .settings .setting .setting-name{font-size:14px;line-height:21px;line-height:19px;font-weight:700;color:#fff;margin-bottom:9px;display:inline-flex;align-items:center;gap:6px}.settings-menu .settings .setting .setting-info{display:flex}.settings-menu .settings .setting .setting-info.column-layout{flex-direction:column}.settings-menu .settings .setting .setting-info.column-layout .setting-description{padding-right:0;padding-bottom:15px}.settings-menu .settings .setting .setting-info .setting-description{font-size:14px;line-height:21px;line-height:19px;font-weight:500;flex:1 0;color:rgba(255,255,255,.6);padding-right:20px}.settings-menu .settings .setting .setting-info .setting-description em{color:var(--color--accent-yellow)}.settings-menu .settings .setting .setting-info .setting-description em:before{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;content:"warning";font-variation-settings:"FILL" 1,"wght" 400 !important;display:inline-block;vertical-align:middle;margin-right:3px;font-size:14px;font-weight:800}.settings-menu .settings .setting .setting-info .setting-input{flex:0 1 auto}.settings-menu .settings .setting .setting-info .setting-input compose{display:flex}.settings-menu .settings .setting .setting-info .setting-input .standard-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;margin-top:-3px}.settings-menu .settings .setting .setting-info .setting-input .standard-button,.settings-menu .settings .setting .setting-info .setting-input .standard-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .settings .setting .setting-info .setting-input .standard-button{border:1px solid #fff}}.settings-menu .settings .setting .setting-info .setting-input .standard-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.settings-menu .settings .setting .setting-info .setting-input .standard-button>*:first-child{padding-left:0}.settings-menu .settings .setting .setting-info .setting-input .standard-button>*:last-child{padding-right:0}.settings-menu .settings .setting .setting-info .setting-input .standard-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .settings .setting .setting-info .setting-input .standard-button svg *{fill:CanvasText}}.settings-menu .settings .setting .setting-info .setting-input .standard-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .settings .setting .setting-info .setting-input .standard-button svg{opacity:1}}.settings-menu .settings .setting .setting-info .setting-input .standard-button img{height:50%}.settings-menu .settings .setting .setting-info .setting-input .standard-button:disabled{opacity:.3}.settings-menu .settings .setting .setting-info .setting-input .standard-button:disabled,.settings-menu .settings .setting .setting-info .setting-input .standard-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.settings-menu .settings .setting .setting-info .setting-input .standard-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.settings-menu .settings .setting .setting-info .setting-input .standard-button:not(:disabled):hover svg{opacity:1}}.settings-menu .settings .setting .setting-info .setting-input .standard-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.settings-menu .settings .setting .setting-info .setting-input .standard-button.alert{box-shadow:inset 0 0 0 1px var(--color--alert);--cta__icon--color: var(--color--alert)}@media(hover: hover){.settings-menu .settings .setting .setting-info .setting-input .standard-button.alert:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--alert);background-color:rgba(0,0,0,0)}}.settings-menu .settings .setting .setting-info .setting-input .standard-button.alert:not(:disabled):active{background-color:var(--color--alert)}.settings-menu .settings .setting .setting-info .setting-input selection-input .wrapper:after{top:1px}.settings-menu .settings-form{display:flex;flex-direction:column;display:flex;flex-direction:column;align-items:flex-start;width:100%;max-width:334px}.settings-menu .settings-form>*{margin-top:10px;margin-bottom:10px}.settings-menu .settings-form>*:first-child{margin-top:0}.settings-menu .settings-form>*:last-child{margin-bottom:0}.settings-menu .settings-form .form-row{display:flex;flex-direction:column-reverse;width:100%}.settings-menu .settings-form .form-row label{font-size:12px;line-height:18px;font-weight:500;--input__label--color: rgba(255, 255, 255, 0.4);color:var(--input__label--color);display:block;margin:0 0 5px 9px}.settings-menu .settings-form .form-row input{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%}.settings-menu .settings-form .form-row input::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.settings-menu .settings-form .form-row input::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.settings-menu .settings-form .form-row input:disabled{opacity:.5}.settings-menu .settings-form .form-row input:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.settings-menu .settings-form .form-row input:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.settings-menu .settings-form .form-row input.with-icon{padding-right:30px}.settings-menu .settings-form .form-row .input-wrapper{position:relative;width:100%}.settings-menu .settings-form .form-row .input-wrapper i{--input__icon--color: rgba(255, 255, 255, 0.4);position:absolute;right:0;top:0;height:100%;width:30px;display:inline-flex;align-items:center;justify-content:center;pointer-events:none}.settings-menu .settings-form .form-row .input-wrapper i svg *{fill:var(--input__icon--color)}.settings-menu .settings-form .form-row .input-wrapper i.ok{--input__icon--color: var(--color--accent)}.settings-menu .settings-form .error{font-size:13px;line-height:20px;font-weight:500;color:var(--color--alert);margin-left:14px;margin-top:3px}.settings-menu .settings-form button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:800;font-size:21px;line-height:30px;font-weight:800;--cta--padding: 18px;--cta--height: 39px;--cta--hover--border-width: 2px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}.settings-menu .settings-form button,.settings-menu .settings-form button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .settings-form button{border:1px solid #fff}}.settings-menu .settings-form button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.settings-menu .settings-form button>*:first-child{padding-left:0}.settings-menu .settings-form button>*:last-child{padding-right:0}.settings-menu .settings-form button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .settings-form button svg *{fill:CanvasText}}.settings-menu .settings-form button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .settings-form button svg{opacity:1}}.settings-menu .settings-form button img{height:50%}.settings-menu .settings-form button:disabled{opacity:.3}.settings-menu .settings-form button:disabled,.settings-menu .settings-form button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.settings-menu .settings-form button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.settings-menu .settings-form button:not(:disabled):hover svg{opacity:1}}.settings-menu .settings-form button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.settings-menu .settings-form button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.settings-menu .settings-form button:not(:disabled):active{background-color:var(--theme--highlight)}.settings-menu .settings-table{table-layout:fixed;padding:0;margin:22px 0 0 0;max-width:710px}.settings-menu .settings-table thead td{padding-bottom:20px;vertical-align:bottom}.settings-menu .settings-table thead:not(:first-of-type) tr td{padding-top:20px}.settings-menu .settings-table tbody td{padding-bottom:14px;vertical-align:middle}.settings-menu .settings-table tbody:not(:last-of-type) tr:last-child td{padding-bottom:24px;border-bottom:1px solid rgba(255,255,255,.1)}.settings-menu .settings-table td{padding-right:38px}.settings-menu .settings-table td:last-child{padding-right:0}.settings-menu .settings-table .settings-table-title{font-weight:300;width:100%;font-size:25px;color:rgba(255,255,255,.6);line-height:26px}.settings-menu .settings-table .settings-table-column-title{font-size:12px;color:rgba(255,255,255,.4);letter-spacing:1px;line-height:16px;text-transform:uppercase}.settings-menu .settings-table .setting-info .setting-name{font-weight:bold;font-size:16px;color:#fff;line-height:22px;padding:0;margin:0 0 6px 0}.settings-menu .settings-table .setting-info .setting-description{font-size:15px;color:rgba(255,255,255,.4);line-height:21px;padding:0;margin:0}.settings-menu .actions{position:absolute;left:25px;bottom:30px}.settings-menu .actions>*{margin:10px 10px 0 0}.settings-menu .actions button,.settings-menu .actions a.button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}.settings-menu .actions button,.settings-menu .actions button *,.settings-menu .actions a.button,.settings-menu .actions a.button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .actions button,body:not(.override-contrast-mode) .settings-menu .actions a.button{border:1px solid #fff}}.settings-menu .actions button>*,.settings-menu .actions a.button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.settings-menu .actions button>*:first-child,.settings-menu .actions a.button>*:first-child{padding-left:0}.settings-menu .actions button>*:last-child,.settings-menu .actions a.button>*:last-child{padding-right:0}.settings-menu .actions button svg,.settings-menu .actions a.button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .actions button svg *,body:not(.override-contrast-mode) .settings-menu .actions a.button svg *{fill:CanvasText}}.settings-menu .actions button svg *,.settings-menu .actions a.button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .actions button svg,body:not(.override-contrast-mode) .settings-menu .actions a.button svg{opacity:1}}.settings-menu .actions button img,.settings-menu .actions a.button img{height:50%}.settings-menu .actions button:disabled,.settings-menu .actions a.button:disabled{opacity:.3}.settings-menu .actions button:disabled,.settings-menu .actions button:disabled *,.settings-menu .actions a.button:disabled,.settings-menu .actions a.button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.settings-menu .actions button:not(:disabled):hover,.settings-menu .actions a.button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.settings-menu .actions button:not(:disabled):hover svg,.settings-menu .actions a.button:not(:disabled):hover svg{opacity:1}}.settings-menu .actions button:not(:disabled):active,.settings-menu .actions a.button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.settings-menu .actions a{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;box-shadow:none}.settings-menu .actions a,.settings-menu .actions a *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .actions a{border:1px solid #fff}}.settings-menu .actions a>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.settings-menu .actions a>*:first-child{padding-left:0}.settings-menu .actions a>*:last-child{padding-right:0}.settings-menu .actions a svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .actions a svg *{fill:CanvasText}}.settings-menu .actions a svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .actions a svg{opacity:1}}.settings-menu .actions a img{height:50%}.settings-menu .actions a:disabled{opacity:.3}.settings-menu .actions a:disabled,.settings-menu .actions a:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.settings-menu .actions a:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.settings-menu .actions a:not(:disabled):hover svg{opacity:1}}.settings-menu .actions a:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(forced-colors: active){body:not(.override-contrast-mode) .settings-menu .actions a{border:0 !important}}.settings-menu .sidebar{display:block;width:298px;height:100%;position:absolute;left:0;bottom:0;top:0;z-index:1}.settings-menu .sidebar-background{--overflow-fade--background: var(--theme--secondary-background);background:var(--theme--secondary-background);display:block;height:100%;padding-top:3px}.settings-menu .sidebar-background .top{display:block;overflow:hidden;-webkit-app-region:drag}.settings-menu .sidebar-background .top>*{transform:translate(0, -3px)}.settings-menu .sidebar-scrollable-wrapper{height:calc(100% - 36px)}.settings-menu .sidebar-scrollable{height:100%;overflow-y:overlay;overflow-x:hidden;position:relative;z-index:0;width:298px}.settings-menu .sidebar-scrollable::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.settings-menu .sidebar-scrollable::-webkit-scrollbar-thumb:window-inactive,.settings-menu .sidebar-scrollable::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.settings-menu .sidebar-scrollable::-webkit-scrollbar-thumb:window-inactive:hover,.settings-menu .sidebar-scrollable::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.settings-menu .sidebar-scrollable::-webkit-scrollbar{background:rgba(0,0,0,0) !important;display:none}.settings-menu .sidebar-scrollable:hover::-webkit-scrollbar{display:block}.settings-menu .sidebar-wrapper{padding:0 0 15px 0}.settings-menu .sidebar-item-label{font-size:14px;line-height:21px;line-height:19px;font-weight:700;color:rgba(255,255,255,.5);transition:color .15s;margin:0;padding:8px 22px 9px 20px;display:flex;align-items:center}.settings-menu .sidebar-item-label>span{overflow:hidden;flex:1 1 auto}.settings-menu .sidebar-item-label em{font-size:12px;line-height:18px;font-weight:500;font-style:normal;color:rgba(255,255,255,.3)}.settings-menu .sidebar-item-label em:after{content:"";display:block;clear:both}.settings-menu .sidebar-item-label,.settings-menu .sidebar-item-label *{cursor:pointer}.settings-menu .sidebar-item-label.no-children .caret{visibility:hidden}.settings-menu .sidebar-item-label.disabled{pointer-events:none}.settings-menu .sidebar-item-label .caret{width:9px;margin-right:8.5px;vertical-align:middle;transition:transform .15s;transform:rotate(-90deg);opacity:.5}.settings-menu .sidebar-item-label:not(.no-children):hover .caret,.settings-menu .sidebar-item-label:not(.no-children).selected .caret{opacity:1}.settings-menu .sidebar-item-label:not(.disabled):hover,.settings-menu .sidebar-item-label:not(.disabled).selected{opacity:1;color:#fff}.settings-menu .sidebar-item-label.selected .caret{transform:rotate(0deg)}.settings-menu .sidebar-item-content{max-height:auto;overflow:hidden;padding:0 20px}.settings-menu .sidebar-item-content.collapsed{max-height:0}.settings-menu .sidebar-item-content .sidebar-item-children{display:flex;flex-direction:column;margin:0;padding:0;display:flex;flex-direction:column}.settings-menu .sidebar-item-content .sidebar-item-children>*{margin-top:3.5px;margin-bottom:3.5px}.settings-menu .sidebar-item-content .sidebar-item-children>*:first-child{margin-top:0}.settings-menu .sidebar-item-content .sidebar-item-children>*:last-child{margin-bottom:0}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child{--game__row--color: rgba(255, 255, 255, 0.7);--game__meta--color: rgba(255, 255, 255, 0.4);--game__thumbnail--border-color: transparent;display:flex;flex-direction:row;position:relative;display:block;width:100%;text-decoration:none;align-items:center;padding-left:30px}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child,.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child *{cursor:pointer}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child i{display:inline-block;width:15px;height:15px;display:inline-flex;justify-content:center;align-items:center}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child i:after{content:"";display:block;clear:both}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child i svg{float:left}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child i svg{max-width:15px;max-height:15px}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child i svg *{color:#fff}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child i svg *{fill:rgba(255,255,255,.4);transition:fill .15s}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child>*:nth-child(1){margin-right:20px;flex:0 0 auto}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child>*:nth-child(2){display:flex;flex-direction:column;flex:1 1 auto;position:relative;padding:5px 0;overflow:hidden}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child>*:nth-child(2){overflow:visible}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child>*{padding-top:0 !important;padding-bottom:0 !important}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child .info{font-size:14px;line-height:21px;line-height:19px;font-weight:500;flex:0 0 auto;display:flex;align-items:center;color:var(--game__row--color);transition:color .15s;overflow:hidden;overflow:visible}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child .info>*+*{margin-left:8px}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child .info>*>*+*{margin-left:10px}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child .info>i:last-of-type:not(:first-of-type){margin-left:18px}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child .info b{font-weight:700}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child .info>*{flex:0 1 auto;display:flex;align-items:center;min-width:0}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child:after{border-right:3px solid var(--theme--highlight);content:"";display:block;height:100%;position:absolute;left:-20px;top:0;opacity:0;pointer-events:none;z-index:-1}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child.current *,.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child:hover *{color:#fff}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child.current:after,.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child:hover:after{opacity:1}.settings-menu .sidebar-item-content .sidebar-item-children .sidebar-item-child:hover favorite-button{visibility:visible}`,""]);const p=g}}]);
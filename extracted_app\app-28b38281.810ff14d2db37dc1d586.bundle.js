"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1553],{37294:(e,t,a)=>{a.d(t,{D:()=>m,q:()=>o});var o,n=a(48379),r=a(38777);!function(e){e.Invalid="invalid",e.Unsure="unsure",e.Valid="valid"}(o||(o={}));const c=new Map([["gmail","com"],["outlook","com"],["hotmail","com"],["live","com"],["yahoo","com"]]),s=new Map([["gemail","gmail"],["gmial","gmail"],["gmai","gmail"]]),i=new Map([["c","com"],["ccom","com"],["cim","com"],["cob","com"],["coim","com"],["colm","com"],["com2","com"],["coma","com"],["comcom","com"],["come","com"],["comf","com"],["comi","com"],["comk","com"],["coml","com"],["comm","com"],["coms","com"],["con","com"],["coom","com"],["coom","com"],["cpm","com"],["dcom","com"],["ney","net"],["nt","net"],["ocm","com"],["r","ru"],["vcom","com"],["vom","com"],["xom","com"],["yk","uk"]]);class m{async validate(e){if("string"!=typeof e)return{status:o.Invalid};const t=e.split("@");if(2!==t.length)return{status:o.Invalid};const a=t[0].replace(/\s/g,""),m=t[1].replace(/\s/g,"").replace(/[。｡︒]/g,".").toLocaleLowerCase();if(0===a.length||m.length<3||a.length+m.length>255)return{status:o.Invalid};if(!m.includes("."))return{status:o.Invalid};const l=`${a}@${m}`,u=m.split("."),p=u.pop(),d=u.join(".");let g=i.get(p??"");const f=s.get(d);return g||f?(f&&!g&&(g=c.get(d)),{status:o.Unsure,recommendation:`${a}@${f||d}.${g||p}`,normalized:l}):/^[\p{L}\p{N}_+.-]+$/gu.test(a)&&/^[\p{L}\p{N}.-]+$/gu.test(m)?{status:await Promise.race([this.#e(n.tl(m)),(0,r.Wn)(3e3).then((()=>!1))])?o.Valid:o.Unsure,normalized:l}:{status:o.Unsure,normalized:l}}async#e(e){const t=new URL("https://*******/dns-query");t.searchParams.set("type","MX"),t.searchParams.set("name",e);try{const e=await(await fetch(t,{headers:{Accept:"application/dns-json"}})).json();return 0===e.Status&&!!e.Answer?.some((e=>!e.data.endsWith(" .")&&!e.data.endsWith(" localhost.")))}catch{return!1}}}},51436:(e,t,a)=>{a.d(t,{$:()=>c});var o=a(65993),n=a.n(o),r=a("services/bugsnag/index");class c{run(e,t){return(0,r.isConfigured)()&&n().resetEventCount?.(),t()}}},"services/bugsnag/index":(e,t,a)=>{a.r(t),a.d(t,{configure:()=>E,isConfigured:()=>w,report:()=>I,setEnabled:()=>M,setMetadata:()=>C});var o=a(65993),n=a.n(o),r=a(96610),c=a(20770),s=a(45660),i=a(59239),m=a(19072),l=a(17724),u=a(70236),p=a(88849);const d=(0,r.getLogger)("bugsnag");let g,f=null,h=!0;const y=new Set(["Breadcrumb not attached due to onBreadcrumb callback","Event not sent due to onError callback"]),v=[{type:"request",metadata:{status:200,request:/\.svg$/}},{type:"request",metadata:{request:/^GET data:/}},{type:"request",metadata:{status:304,request:/\/v3\/catalog$/}},{type:"log",metadata:{"[1]":"EVENT_UPDATE_STATE_CHANGED","[2]":/^(checking|not-available)$/}},{type:"log",metadata:{"[1]":/^(-> )?ACTION_CHECK_FOR_UPDATE$/}}];function b(e){return(...t)=>{y.has(t[0])||d[e](...t)}}function E(e,t){t.apiKey&&(f=[{pattern:R("file:///"),replacement:""},{pattern:R(encodeURIComponent("file:///")),replacement:""},{pattern:R(t.appPath),replacement:"~"},{pattern:R(encodeURIComponent(t.appPath)),replacement:"~"},{pattern:R(encodeURI(t.appPath)),replacement:"~"},{pattern:R(t.appPath.replaceAll("\\","/")),replacement:"~"},{pattern:R(JSON.stringify(t.appPath).slice(1,-1)),replacement:"~"},{pattern:R(encodeURIComponent(t.appPath.replaceAll("\\","/"))),replacement:"~"},{pattern:R(encodeURI(t.appPath.replaceAll("\\","/"))),replacement:"~"}],g=e.container,n().start({apiKey:t.apiKey,appVersion:t.appVersion,releaseStage:t.releaseStage,autoTrackSessions:!1,projectRoot:"~",metadata:{app:{path:t.appPath},device:{locale:t.locale,arch:t.osArch}},logger:{debug:b("debug"),info:b("info"),warn:b("warn"),error:b("error")},onBreadcrumb:[k,U,q],onError:[k,P,$,T,j,x,S]}))}function M(e){h=e}function w(){return null!==f}function C(e,t,a){w()&&n().addMetadata(e,t,a)}function k(){return h}function P(e){return"object"!=typeof e.originalError||!0!==e.originalError.doNotReport}function $(e){return!(0,l.hb)(e.originalError)}function I(e,t,a){w()?(a&&(e.customMetadata=a),n().notify(e,t)):console.error(e)}function R(e){return new RegExp(e.replace(/[-/\\^$*+?.()|[\]{}]/g,"\\$&"),"ig")}function A(e){return"string"==typeof e&&f?.forEach((t=>e=e.replace(t.pattern,t.replacement))),e}function U(e){return"object"!=typeof e.metadata||null===e.metadata||v.every((t=>t.type!==e.type||Object.keys(t.metadata).some((a=>{const o=t.metadata[a],n=e.metadata[a];return void 0===n||(o instanceof RegExp?!["string","number"].includes(typeof n)||!o.test(n.toString()):n!==o)}))))}function q(e){e.message=A(e.message);const t=e.metadata;"object"==typeof t&&null!==t&&Object.keys(t).forEach((e=>t[e]=A(t[e])))}function j(e){e.context=A(e.context??""),e.request.url=A(e.request.url??""),e.errors.forEach((e=>{e.errorMessage=A(e.errorMessage),e.stacktrace.forEach((e=>e.file=A(e.file)))}))}async function x(e){const t=(0,m.S)(),a=t.info,o=await t.getMemoryInfo();if(e.addMetadata("device",{hostname:a.osHostname,cpu:a.deviceCpuModel,cpuCount:a.deviceCpuCount,memoryTotal:(0,p.z3)(o.total),memoryFree:(0,p.z3)(o.free)}),!n().getMetadata("device","antivirus")){const a=(await t.getInstalledAvProducts()??["<error>"]).sort();n().addMetadata("device","antivirus",a),e.addMetadata("device","antivirus",a)}return!0}function S(e){const t=e.originalError;t?.customMetadata&&e.addMetadata("Custom",t.customMetadata)}async function T(){const e=await async function(){const e=g.get(c.il);return await(e?.state.pipe((0,s.$)(),(0,i.E)("account")).toPromise())}();return!e||!!e.subscription===(0,u.Lt)(e.flags,512)}}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3577],{803:e=>{var t=/\r\n?|\n/g,n=/\0/g;e.exports=function(e){var r;r=(r=e.src.replace(t,"\n")).replace(n,"�"),e.src=r}},986:(e,t,n)=>{var r=n(49963).arrayReplaceAt;function s(e){return/^<\/a\s*>/i.test(e)}e.exports=function(e){var t,n,o,i,a,h,p,c,l,u,f,d,k,m,C,g,b,v,A=e.tokens;if(e.md.options.linkify)for(n=0,o=A.length;n<o;n++)if("inline"===A[n].type&&e.md.linkify.pretest(A[n].content))for(k=0,t=(i=A[n].children).length-1;t>=0;t--)if("link_close"!==(h=i[t]).type){if("html_inline"===h.type&&(v=h.content,/^<a[>\s]/i.test(v)&&k>0&&k--,s(h.content)&&k++),!(k>0)&&"text"===h.type&&e.md.linkify.test(h.content)){for(l=h.content,b=e.md.linkify.match(l),p=[],d=h.level,f=0,c=0;c<b.length;c++)m=b[c].url,C=e.md.normalizeLink(m),e.md.validateLink(C)&&(g=b[c].text,g=b[c].schema?"mailto:"!==b[c].schema||/^mailto:/i.test(g)?e.md.normalizeLinkText(g):e.md.normalizeLinkText("mailto:"+g).replace(/^mailto:/,""):e.md.normalizeLinkText("http://"+g).replace(/^http:\/\//,""),(u=b[c].index)>f&&((a=new e.Token("text","",0)).content=l.slice(f,u),a.level=d,p.push(a)),(a=new e.Token("link_open","a",1)).attrs=[["href",C]],a.level=d++,a.markup="linkify",a.info="auto",p.push(a),(a=new e.Token("text","",0)).content=g,a.level=d,p.push(a),(a=new e.Token("link_close","a",-1)).level=--d,a.markup="linkify",a.info="auto",p.push(a),f=b[c].lastIndex);f<l.length&&((a=new e.Token("text","",0)).content=l.slice(f),a.level=d,p.push(a)),A[n].children=i=r(i,t,p)}}else for(t--;i[t].level!==h.level&&"link_open"!==i[t].type;)t--}},1839:(e,t,n)=>{var r=n(5099);function s(e,t,n){this.src=e,this.env=n,this.tokens=[],this.inlineMode=!1,this.md=t}s.prototype.Token=r,e.exports=s},3547:e=>{e.exports=function(e){var t,n,r,s=e.tokens;for(n=0,r=s.length;n<r;n++)"inline"===(t=s[n]).type&&e.md.inline.parse(t.content,e.md,e.env,t.children)}},5099:e=>{function t(e,t,n){this.type=e,this.tag=t,this.attrs=null,this.map=null,this.nesting=n,this.level=0,this.children=null,this.content="",this.markup="",this.info="",this.meta=null,this.block=!1,this.hidden=!1}t.prototype.attrIndex=function(e){var t,n,r;if(!this.attrs)return-1;for(n=0,r=(t=this.attrs).length;n<r;n++)if(t[n][0]===e)return n;return-1},t.prototype.attrPush=function(e){this.attrs?this.attrs.push(e):this.attrs=[e]},t.prototype.attrSet=function(e,t){var n=this.attrIndex(e),r=[e,t];n<0?this.attrPush(r):this.attrs[n]=r},t.prototype.attrGet=function(e){var t=this.attrIndex(e),n=null;return t>=0&&(n=this.attrs[t][1]),n},t.prototype.attrJoin=function(e,t){var n=this.attrIndex(e);n<0?this.attrPush([e,t]):this.attrs[n][1]=this.attrs[n][1]+" "+t},e.exports=t},7759:(e,t,n)=>{var r=n(5099),s=n(49963).isSpace;function o(e,t,n,r){var o,i,a,h,p,c,l,u;for(this.src=e,this.md=t,this.env=n,this.tokens=r,this.bMarks=[],this.eMarks=[],this.tShift=[],this.sCount=[],this.bsCount=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.ddIndent=-1,this.listIndent=-1,this.parentType="root",this.level=0,this.result="",u=!1,a=h=c=l=0,p=(i=this.src).length;h<p;h++){if(o=i.charCodeAt(h),!u){if(s(o)){c++,9===o?l+=4-l%4:l++;continue}u=!0}10!==o&&h!==p-1||(10!==o&&h++,this.bMarks.push(a),this.eMarks.push(h),this.tShift.push(c),this.sCount.push(l),this.bsCount.push(0),u=!1,c=0,l=0,a=h+1)}this.bMarks.push(i.length),this.eMarks.push(i.length),this.tShift.push(0),this.sCount.push(0),this.bsCount.push(0),this.lineMax=this.bMarks.length-1}o.prototype.push=function(e,t,n){var s=new r(e,t,n);return s.block=!0,n<0&&this.level--,s.level=this.level,n>0&&this.level++,this.tokens.push(s),s},o.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},o.prototype.skipEmptyLines=function(e){for(var t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},o.prototype.skipSpaces=function(e){for(var t,n=this.src.length;e<n&&(t=this.src.charCodeAt(e),s(t));e++);return e},o.prototype.skipSpacesBack=function(e,t){if(e<=t)return e;for(;e>t;)if(!s(this.src.charCodeAt(--e)))return e+1;return e},o.prototype.skipChars=function(e,t){for(var n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e},o.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e},o.prototype.getLines=function(e,t,n,r){var o,i,a,h,p,c,l,u=e;if(e>=t)return"";for(c=new Array(t-e),o=0;u<t;u++,o++){for(i=0,l=h=this.bMarks[u],p=u+1<t||r?this.eMarks[u]+1:this.eMarks[u];h<p&&i<n;){if(a=this.src.charCodeAt(h),s(a))9===a?i+=4-(i+this.bsCount[u])%4:i++;else{if(!(h-l<this.tShift[u]))break;i++}h++}c[o]=i>n?new Array(i-n+1).join(" ")+this.src.slice(h,p):this.src.slice(h,p)}return c.join("")},o.prototype.Token=r,e.exports=o},10203:e=>{var t=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,n=/\((c|tm|r|p)\)/i,r=/\((c|tm|r|p)\)/gi,s={c:"©",r:"®",p:"§",tm:"™"};function o(e,t){return s[t.toLowerCase()]}function i(e){var t,n,s=0;for(t=e.length-1;t>=0;t--)"text"!==(n=e[t]).type||s||(n.content=n.content.replace(r,o)),"link_open"===n.type&&"auto"===n.info&&s--,"link_close"===n.type&&"auto"===n.info&&s++}function a(e){var n,r,s=0;for(n=e.length-1;n>=0;n--)"text"!==(r=e[n]).type||s||t.test(r.content)&&(r.content=r.content.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---(?=[^-]|$)/gm,"$1—").replace(/(^|\s)--(?=\s|$)/gm,"$1–").replace(/(^|[^-\s])--(?=[^-\s]|$)/gm,"$1–")),"link_open"===r.type&&"auto"===r.info&&s--,"link_close"===r.type&&"auto"===r.info&&s++}e.exports=function(e){var r;if(e.md.options.typographer)for(r=e.tokens.length-1;r>=0;r--)"inline"===e.tokens[r].type&&(n.test(e.tokens[r].content)&&i(e.tokens[r].children),t.test(e.tokens[r].content)&&a(e.tokens[r].children))}},15711:e=>{e.exports=function(e,t,n){var r,s,o;if(e.sCount[t]-e.blkIndent<4)return!1;for(s=r=t+1;r<n;)if(e.isEmpty(r))r++;else{if(!(e.sCount[r]-e.blkIndent>=4))break;s=++r}return e.line=s,(o=e.push("code_block","code",0)).content=e.getLines(t,s,4+e.blkIndent,!0),o.map=[t,e.line],!0}},18103:(e,t,n)=>{var r=n(68359),s=n(49963).has,o=n(49963).isValidEntityCode,i=n(49963).fromCodePoint,a=/^&#((?:x[a-f0-9]{1,6}|[0-9]{1,7}));/i,h=/^&([a-z][a-z0-9]{1,31});/i;e.exports=function(e,t){var n,p,c=e.pos,l=e.posMax;if(38!==e.src.charCodeAt(c))return!1;if(c+1<l)if(35===e.src.charCodeAt(c+1)){if(p=e.src.slice(c).match(a))return t||(n="x"===p[1][0].toLowerCase()?parseInt(p[1].slice(1),16):parseInt(p[1],10),e.pending+=o(n)?i(n):i(65533)),e.pos+=p[0].length,!0}else if((p=e.src.slice(c).match(h))&&s(r,p[1]))return t||(e.pending+=r[p[1]]),e.pos+=p[0].length,!0;return t||(e.pending+="&"),e.pos++,!0}},21231:(e,t,n)=>{for(var r=n(49963).isSpace,s=[],o=0;o<256;o++)s.push(0);"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){s[e.charCodeAt(0)]=1})),e.exports=function(e,t){var n,o=e.pos,i=e.posMax;if(92!==e.src.charCodeAt(o))return!1;if(++o<i){if((n=e.src.charCodeAt(o))<256&&0!==s[n])return t||(e.pending+=e.src[o]),e.pos+=2,!0;if(10===n){for(t||e.push("hardbreak","br",0),o++;o<i&&(n=e.src.charCodeAt(o),r(n));)o++;return e.pos=o,!0}}return t||(e.pending+="\\"),e.pos++,!0}},22534:(e,t,n)=>{var r=n(49963).isSpace;e.exports=function(e,t){var n,s,o=e.pos;if(10!==e.src.charCodeAt(o))return!1;for(n=e.pending.length-1,s=e.posMax,t||(n>=0&&32===e.pending.charCodeAt(n)?n>=1&&32===e.pending.charCodeAt(n-1)?(e.pending=e.pending.replace(/ +$/,""),e.push("hardbreak","br",0)):(e.pending=e.pending.slice(0,-1),e.push("softbreak","br",0)):e.push("softbreak","br",0)),o++;o<s&&r(e.src.charCodeAt(o));)o++;return e.pos=o,!0}},23707:(e,t,n)=>{var r=n(49963).normalizeReference,s=n(49963).isSpace;e.exports=function(e,t){var n,o,i,a,h,p,c,l,u,f,d,k,m,C="",g=e.pos,b=e.posMax;if(33!==e.src.charCodeAt(e.pos))return!1;if(91!==e.src.charCodeAt(e.pos+1))return!1;if(p=e.pos+2,(h=e.md.helpers.parseLinkLabel(e,e.pos+1,!1))<0)return!1;if((c=h+1)<b&&40===e.src.charCodeAt(c)){for(c++;c<b&&(o=e.src.charCodeAt(c),s(o)||10===o);c++);if(c>=b)return!1;for(m=c,(u=e.md.helpers.parseLinkDestination(e.src,c,e.posMax)).ok&&(C=e.md.normalizeLink(u.str),e.md.validateLink(C)?c=u.pos:C=""),m=c;c<b&&(o=e.src.charCodeAt(c),s(o)||10===o);c++);if(u=e.md.helpers.parseLinkTitle(e.src,c,e.posMax),c<b&&m!==c&&u.ok)for(f=u.str,c=u.pos;c<b&&(o=e.src.charCodeAt(c),s(o)||10===o);c++);else f="";if(c>=b||41!==e.src.charCodeAt(c))return e.pos=g,!1;c++}else{if(void 0===e.env.references)return!1;if(c<b&&91===e.src.charCodeAt(c)?(m=c+1,(c=e.md.helpers.parseLinkLabel(e,c))>=0?a=e.src.slice(m,c++):c=h+1):c=h+1,a||(a=e.src.slice(p,h)),!(l=e.env.references[r(a)]))return e.pos=g,!1;C=l.href,f=l.title}return t||(i=e.src.slice(p,h),e.md.inline.parse(i,e.md,e.env,k=[]),(d=e.push("image","img",0)).attrs=n=[["src",C],["alt",""]],d.children=k,d.content=i,f&&n.push(["title",f])),e.pos=c,e.posMax=b,!0}},24752:(e,t,n)=>{var r=n(49963).isSpace;function s(e,t){var n=e.bMarks[t]+e.blkIndent,r=e.eMarks[t];return e.src.substr(n,r-n)}function o(e){var t,n=[],r=0,s=e.length,o=0,i=0,a=!1,h=0;for(t=e.charCodeAt(r);r<s;)96===t?a?(a=!1,h=r):o%2==0&&(a=!0,h=r):124!==t||o%2!=0||a||(n.push(e.substring(i,r)),i=r+1),92===t?o++:o=0,++r===s&&a&&(a=!1,r=h+1),t=e.charCodeAt(r);return n.push(e.substring(i)),n}e.exports=function(e,t,n,i){var a,h,p,c,l,u,f,d,k,m,C,g;if(t+2>n)return!1;if(l=t+1,e.sCount[l]<e.blkIndent)return!1;if(e.sCount[l]-e.blkIndent>=4)return!1;if((p=e.bMarks[l]+e.tShift[l])>=e.eMarks[l])return!1;if(124!==(a=e.src.charCodeAt(p++))&&45!==a&&58!==a)return!1;for(;p<e.eMarks[l];){if(124!==(a=e.src.charCodeAt(p))&&45!==a&&58!==a&&!r(a))return!1;p++}for(u=(h=s(e,t+1)).split("|"),k=[],c=0;c<u.length;c++){if(!(m=u[c].trim())){if(0===c||c===u.length-1)continue;return!1}if(!/^:?-+:?$/.test(m))return!1;58===m.charCodeAt(m.length-1)?k.push(58===m.charCodeAt(0)?"center":"right"):58===m.charCodeAt(0)?k.push("left"):k.push("")}if(-1===(h=s(e,t).trim()).indexOf("|"))return!1;if(e.sCount[t]-e.blkIndent>=4)return!1;if((f=(u=o(h.replace(/^\||\|$/g,""))).length)>k.length)return!1;if(i)return!0;for((d=e.push("table_open","table",1)).map=C=[t,0],(d=e.push("thead_open","thead",1)).map=[t,t+1],(d=e.push("tr_open","tr",1)).map=[t,t+1],c=0;c<u.length;c++)(d=e.push("th_open","th",1)).map=[t,t+1],k[c]&&(d.attrs=[["style","text-align:"+k[c]]]),(d=e.push("inline","",0)).content=u[c].trim(),d.map=[t,t+1],d.children=[],d=e.push("th_close","th",-1);for(d=e.push("tr_close","tr",-1),d=e.push("thead_close","thead",-1),(d=e.push("tbody_open","tbody",1)).map=g=[t+2,0],l=t+2;l<n&&!(e.sCount[l]<e.blkIndent)&&-1!==(h=s(e,l).trim()).indexOf("|")&&!(e.sCount[l]-e.blkIndent>=4);l++){for(u=o(h.replace(/^\||\|$/g,"")),d=e.push("tr_open","tr",1),c=0;c<f;c++)(d=e.push("td_open","td",1)).map=[l,l+1],k[c]&&(d.attrs=[["style","text-align:"+k[c]]]),(d=e.push("inline","",0)).map=[l,l+1],d.content=u[c]?u[c].trim():"",d.children=[],d=e.push("td_close","td",-1);d=e.push("tr_close","tr",-1)}return d=e.push("tbody_close","tbody",-1),d=e.push("table_close","table",-1),C[1]=g[1]=l,e.line=l,!0}},26757:e=>{e.exports=function(e,t){var n,r,s,o,i,a,h=e.pos;if(96!==e.src.charCodeAt(h))return!1;for(n=h,h++,r=e.posMax;h<r&&96===e.src.charCodeAt(h);)h++;for(s=e.src.slice(n,h),o=i=h;-1!==(o=e.src.indexOf("`",i));){for(i=o+1;i<r&&96===e.src.charCodeAt(i);)i++;if(i-o===s.length)return t||((a=e.push("code_inline","code",0)).markup=s,a.content=e.src.slice(h,o).replace(/\n/g," ").replace(/^ (.+) $/,"$1")),e.pos=i,!0}return t||(e.pending+=s),e.pos+=s.length,!0}},30961:(e,t,n)=>{var r=n(76557).l;e.exports=function(e,t){var n,s,o,i=e.pos;return!(!e.md.options.html||(o=e.posMax,60!==e.src.charCodeAt(i)||i+2>=o||33!==(n=e.src.charCodeAt(i+1))&&63!==n&&47!==n&&!function(e){var t=32|e;return t>=97&&t<=122}(n)||!(s=e.src.slice(i).match(r))||(t||(e.push("html_inline","",0).content=e.src.slice(i,i+s[0].length)),e.pos+=s[0].length,0)))}},36686:(e,t,n)=>{var r=n(49963).isSpace;function s(e,t){var n,s,o,i;return s=e.bMarks[t]+e.tShift[t],o=e.eMarks[t],42!==(n=e.src.charCodeAt(s++))&&45!==n&&43!==n||s<o&&(i=e.src.charCodeAt(s),!r(i))?-1:s}function o(e,t){var n,s=e.bMarks[t]+e.tShift[t],o=s,i=e.eMarks[t];if(o+1>=i)return-1;if((n=e.src.charCodeAt(o++))<48||n>57)return-1;for(;;){if(o>=i)return-1;if(!((n=e.src.charCodeAt(o++))>=48&&n<=57)){if(41===n||46===n)break;return-1}if(o-s>=10)return-1}return o<i&&(n=e.src.charCodeAt(o),!r(n))?-1:o}e.exports=function(e,t,n,r){var i,a,h,p,c,l,u,f,d,k,m,C,g,b,v,A,x,y,M,_,S,I,L,T,z,$,w,q,E=!1,j=!0;if(e.sCount[t]-e.blkIndent>=4)return!1;if(e.listIndent>=0&&e.sCount[t]-e.listIndent>=4&&e.sCount[t]<e.blkIndent)return!1;if(r&&"paragraph"===e.parentType&&e.tShift[t]>=e.blkIndent&&(E=!0),(L=o(e,t))>=0){if(u=!0,z=e.bMarks[t]+e.tShift[t],g=Number(e.src.substr(z,L-z-1)),E&&1!==g)return!1}else{if(!((L=s(e,t))>=0))return!1;u=!1}if(E&&e.skipSpaces(L)>=e.eMarks[t])return!1;if(C=e.src.charCodeAt(L-1),r)return!0;for(m=e.tokens.length,u?(q=e.push("ordered_list_open","ol",1),1!==g&&(q.attrs=[["start",g]])):q=e.push("bullet_list_open","ul",1),q.map=k=[t,0],q.markup=String.fromCharCode(C),v=t,T=!1,w=e.md.block.ruler.getRules("list"),y=e.parentType,e.parentType="list";v<n;){for(I=L,b=e.eMarks[v],l=A=e.sCount[v]+L-(e.bMarks[t]+e.tShift[t]);I<b;){if(9===(i=e.src.charCodeAt(I)))A+=4-(A+e.bsCount[v])%4;else{if(32!==i)break;A++}I++}if((c=(a=I)>=b?1:A-l)>4&&(c=1),p=l+c,(q=e.push("list_item_open","li",1)).markup=String.fromCharCode(C),q.map=f=[t,0],S=e.tight,_=e.tShift[t],M=e.sCount[t],x=e.listIndent,e.listIndent=e.blkIndent,e.blkIndent=p,e.tight=!0,e.tShift[t]=a-e.bMarks[t],e.sCount[t]=A,a>=b&&e.isEmpty(t+1)?e.line=Math.min(e.line+2,n):e.md.block.tokenize(e,t,n,!0),e.tight&&!T||(j=!1),T=e.line-t>1&&e.isEmpty(e.line-1),e.blkIndent=e.listIndent,e.listIndent=x,e.tShift[t]=_,e.sCount[t]=M,e.tight=S,(q=e.push("list_item_close","li",-1)).markup=String.fromCharCode(C),v=t=e.line,f[1]=v,a=e.bMarks[t],v>=n)break;if(e.sCount[v]<e.blkIndent)break;if(e.sCount[t]-e.blkIndent>=4)break;for($=!1,h=0,d=w.length;h<d;h++)if(w[h](e,v,n,!0)){$=!0;break}if($)break;if(u){if((L=o(e,v))<0)break}else if((L=s(e,v))<0)break;if(C!==e.src.charCodeAt(L-1))break}return(q=u?e.push("ordered_list_close","ol",-1):e.push("bullet_list_close","ul",-1)).markup=String.fromCharCode(C),k[1]=v,e.line=v,e.parentType=y,j&&function(e,t){var n,r,s=e.level+2;for(n=t+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===s&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].hidden=!0,e.tokens[n].hidden=!0,n+=2)}(e,m),!0}},39648:e=>{e.exports=function(e,t,n){var r,s,o,i,a,h,p,c,l,u,f=t+1,d=e.md.block.ruler.getRules("paragraph");if(e.sCount[t]-e.blkIndent>=4)return!1;for(u=e.parentType,e.parentType="paragraph";f<n&&!e.isEmpty(f);f++)if(!(e.sCount[f]-e.blkIndent>3)){if(e.sCount[f]>=e.blkIndent&&(h=e.bMarks[f]+e.tShift[f])<(p=e.eMarks[f])&&(45===(l=e.src.charCodeAt(h))||61===l)&&(h=e.skipChars(h,l),(h=e.skipSpaces(h))>=p)){c=61===l?1:2;break}if(!(e.sCount[f]<0)){for(s=!1,o=0,i=d.length;o<i;o++)if(d[o](e,f,n,!0)){s=!0;break}if(s)break}}return!!c&&(r=e.getLines(t,f,e.blkIndent,!1).trim(),e.line=f+1,(a=e.push("heading_open","h"+String(c),1)).markup=String.fromCharCode(l),a.map=[t,e.line],(a=e.push("inline","",0)).content=r,a.map=[t,e.line-1],a.children=[],(a=e.push("heading_close","h"+String(c),-1)).markup=String.fromCharCode(l),e.parentType=u,!0)}},50634:(e,t,n)=>{var r=n(49963).isSpace;e.exports=function(e,t,n,s){var o,i,a,h,p=e.bMarks[t]+e.tShift[t],c=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(35!==(o=e.src.charCodeAt(p))||p>=c)return!1;for(i=1,o=e.src.charCodeAt(++p);35===o&&p<c&&i<=6;)i++,o=e.src.charCodeAt(++p);return!(i>6||p<c&&!r(o)||(s||(c=e.skipSpacesBack(c,p),(a=e.skipCharsBack(c,35,p))>p&&r(e.src.charCodeAt(a-1))&&(c=a),e.line=t+1,(h=e.push("heading_open","h"+String(i),1)).markup="########".slice(0,i),h.map=[t,e.line],(h=e.push("inline","",0)).content=e.src.slice(p,c).trim(),h.map=[t,e.line],h.children=[],(h=e.push("heading_close","h"+String(i),-1)).markup="########".slice(0,i)),0))}},52373:e=>{e.exports=function(e,t,n,r){var s,o,i,a,h,p,c,l=!1,u=e.bMarks[t]+e.tShift[t],f=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(u+3>f)return!1;if(126!==(s=e.src.charCodeAt(u))&&96!==s)return!1;if(h=u,(o=(u=e.skipChars(u,s))-h)<3)return!1;if(c=e.src.slice(h,u),i=e.src.slice(u,f),96===s&&i.indexOf(String.fromCharCode(s))>=0)return!1;if(r)return!0;for(a=t;!(++a>=n||(u=h=e.bMarks[a]+e.tShift[a])<(f=e.eMarks[a])&&e.sCount[a]<e.blkIndent);)if(e.src.charCodeAt(u)===s&&!(e.sCount[a]-e.blkIndent>=4||(u=e.skipChars(u,s))-h<o||(u=e.skipSpaces(u))<f)){l=!0;break}return o=e.sCount[t],e.line=a+(l?1:0),(p=e.push("fence","code",0)).info=i,p.content=e.getLines(t+1,a,o,!0),p.markup=c,p.map=[t,e.line],!0}},56552:(e,t,n)=>{var r=n(49963).normalizeReference,s=n(49963).isSpace;e.exports=function(e,t){var n,o,i,a,h,p,c,l,u,f="",d=e.pos,k=e.posMax,m=e.pos,C=!0;if(91!==e.src.charCodeAt(e.pos))return!1;if(h=e.pos+1,(a=e.md.helpers.parseLinkLabel(e,e.pos,!0))<0)return!1;if((p=a+1)<k&&40===e.src.charCodeAt(p)){for(C=!1,p++;p<k&&(o=e.src.charCodeAt(p),s(o)||10===o);p++);if(p>=k)return!1;for(m=p,(c=e.md.helpers.parseLinkDestination(e.src,p,e.posMax)).ok&&(f=e.md.normalizeLink(c.str),e.md.validateLink(f)?p=c.pos:f=""),m=p;p<k&&(o=e.src.charCodeAt(p),s(o)||10===o);p++);if(c=e.md.helpers.parseLinkTitle(e.src,p,e.posMax),p<k&&m!==p&&c.ok)for(u=c.str,p=c.pos;p<k&&(o=e.src.charCodeAt(p),s(o)||10===o);p++);else u="";(p>=k||41!==e.src.charCodeAt(p))&&(C=!0),p++}if(C){if(void 0===e.env.references)return!1;if(p<k&&91===e.src.charCodeAt(p)?(m=p+1,(p=e.md.helpers.parseLinkLabel(e,p))>=0?i=e.src.slice(m,p++):p=a+1):p=a+1,i||(i=e.src.slice(h,a)),!(l=e.env.references[r(i)]))return e.pos=d,!1;f=l.href,u=l.title}return t||(e.pos=h,e.posMax=a,e.push("link_open","a",1).attrs=n=[["href",f]],u&&n.push(["title",u]),e.md.inline.tokenize(e),e.push("link_close","a",-1)),e.pos=p,e.posMax=k,!0}},60979:(e,t,n)=>{var r=n(5099),s=n(49963).isWhiteSpace,o=n(49963).isPunctChar,i=n(49963).isMdAsciiPunct;function a(e,t,n,r){this.src=e,this.env=n,this.md=t,this.tokens=r,this.tokens_meta=Array(r.length),this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache={},this.delimiters=[],this._prev_delimiters=[]}a.prototype.pushPending=function(){var e=new r("text","",0);return e.content=this.pending,e.level=this.pendingLevel,this.tokens.push(e),this.pending="",e},a.prototype.push=function(e,t,n){this.pending&&this.pushPending();var s=new r(e,t,n),o=null;return n<0&&(this.level--,this.delimiters=this._prev_delimiters.pop()),s.level=this.level,n>0&&(this.level++,this._prev_delimiters.push(this.delimiters),this.delimiters=[],o={delimiters:this.delimiters}),this.pendingLevel=this.level,this.tokens.push(s),this.tokens_meta.push(o),s},a.prototype.scanDelims=function(e,t){var n,r,a,h,p,c,l,u,f,d=e,k=!0,m=!0,C=this.posMax,g=this.src.charCodeAt(e);for(n=e>0?this.src.charCodeAt(e-1):32;d<C&&this.src.charCodeAt(d)===g;)d++;return a=d-e,r=d<C?this.src.charCodeAt(d):32,l=i(n)||o(String.fromCharCode(n)),f=i(r)||o(String.fromCharCode(r)),c=s(n),(u=s(r))?k=!1:f&&(c||l||(k=!1)),c?m=!1:l&&(u||f||(m=!1)),t?(h=k,p=m):(h=k&&(!m||l),p=m&&(!k||f)),{can_open:h,can_close:p,length:a}},a.prototype.Token=r,e.exports=a},65260:(e,t,n)=>{var r=n(49963).isWhiteSpace,s=n(49963).isPunctChar,o=n(49963).isMdAsciiPunct,i=/['"]/,a=/['"]/g;function h(e,t,n){return e.substr(0,t)+n+e.substr(t+1)}function p(e,t){var n,i,p,c,l,u,f,d,k,m,C,g,b,v,A,x,y,M,_,S,I;for(_=[],n=0;n<e.length;n++){for(i=e[n],f=e[n].level,y=_.length-1;y>=0&&!(_[y].level<=f);y--);if(_.length=y+1,"text"===i.type){l=0,u=(p=i.content).length;e:for(;l<u&&(a.lastIndex=l,c=a.exec(p));){if(A=x=!0,l=c.index+1,M="'"===c[0],k=32,c.index-1>=0)k=p.charCodeAt(c.index-1);else for(y=n-1;y>=0&&"softbreak"!==e[y].type&&"hardbreak"!==e[y].type;y--)if(e[y].content){k=e[y].content.charCodeAt(e[y].content.length-1);break}if(m=32,l<u)m=p.charCodeAt(l);else for(y=n+1;y<e.length&&"softbreak"!==e[y].type&&"hardbreak"!==e[y].type;y++)if(e[y].content){m=e[y].content.charCodeAt(0);break}if(C=o(k)||s(String.fromCharCode(k)),g=o(m)||s(String.fromCharCode(m)),b=r(k),(v=r(m))?A=!1:g&&(b||C||(A=!1)),b?x=!1:C&&(v||g||(x=!1)),34===m&&'"'===c[0]&&k>=48&&k<=57&&(x=A=!1),A&&x&&(A=C,x=g),A||x){if(x)for(y=_.length-1;y>=0&&(d=_[y],!(_[y].level<f));y--)if(d.single===M&&_[y].level===f){d=_[y],M?(S=t.md.options.quotes[2],I=t.md.options.quotes[3]):(S=t.md.options.quotes[0],I=t.md.options.quotes[1]),i.content=h(i.content,c.index,I),e[d.token].content=h(e[d.token].content,d.pos,S),l+=I.length-1,d.token===n&&(l+=S.length-1),u=(p=i.content).length,_.length=y;continue e}A?_.push({token:n,pos:c.index,single:M,level:f}):x&&M&&(i.content=h(i.content,c.index,"’"))}else M&&(i.content=h(i.content,c.index,"’"))}}}}e.exports=function(e){var t;if(e.md.options.typographer)for(t=e.tokens.length-1;t>=0;t--)"inline"===e.tokens[t].type&&i.test(e.tokens[t].content)&&p(e.tokens[t].children,e)}},65940:e=>{function t(e,t){var n,r,s,o,i,a,h,p,c={},l=t.length;for(n=0;n<l;n++)if((s=t[n]).length=s.length||0,s.close){for(c.hasOwnProperty(s.marker)||(c[s.marker]=[-1,-1,-1]),i=c[s.marker][s.length%3],a=-1,r=n-s.jump-1;r>i;r-=o.jump+1)if((o=t[r]).marker===s.marker&&(-1===a&&(a=r),o.open&&o.end<0&&(h=!1,(o.close||s.open)&&(o.length+s.length)%3==0&&(o.length%3==0&&s.length%3==0||(h=!0)),!h))){p=r>0&&!t[r-1].open?t[r-1].jump+1:0,s.jump=n-r+p,s.open=!1,o.end=n,o.jump=p,o.close=!1,a=-1;break}-1!==a&&(c[s.marker][(s.length||0)%3]=a)}}e.exports=function(e){var n,r=e.tokens_meta,s=e.tokens_meta.length;for(t(0,e.delimiters),n=0;n<s;n++)r[n]&&r[n].delimiters&&t(0,r[n].delimiters)}},73437:e=>{e.exports=function(e){var t;e.inlineMode?((t=new e.Token("inline","",0)).content=e.src,t.map=[0,1],t.children=[],e.tokens.push(t)):e.md.block.parse(e.src,e.md,e.env,e.tokens)}},77729:e=>{e.exports=function(e){var t,n,r=0,s=e.tokens,o=e.tokens.length;for(t=n=0;t<o;t++)s[t].nesting<0&&r--,s[t].level=r,s[t].nesting>0&&r++,"text"===s[t].type&&t+1<o&&"text"===s[t+1].type?s[t+1].content=s[t].content+s[t+1].content:(t!==n&&(s[n]=s[t]),n++);t!==n&&(s.length=n)}},81857:(e,t,n)=>{var r=n(71358),s=n(76557).p,o=[[/^<(script|pre|style)(?=(\s|>|$))/i,/<\/(script|pre|style)>/i,!0],[/^<!--/,/-->/,!0],[/^<\?/,/\?>/,!0],[/^<![A-Z]/,/>/,!0],[/^<!\[CDATA\[/,/\]\]>/,!0],[new RegExp("^</?("+r.join("|")+")(?=(\\s|/?>|$))","i"),/^$/,!0],[new RegExp(s.source+"\\s*$"),/^$/,!1]];e.exports=function(e,t,n,r){var s,i,a,h,p=e.bMarks[t]+e.tShift[t],c=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(!e.md.options.html)return!1;if(60!==e.src.charCodeAt(p))return!1;for(h=e.src.slice(p,c),s=0;s<o.length&&!o[s][0].test(h);s++);if(s===o.length)return!1;if(r)return o[s][2];if(i=t+1,!o[s][1].test(h))for(;i<n&&!(e.sCount[i]<e.blkIndent);i++)if(p=e.bMarks[i]+e.tShift[i],c=e.eMarks[i],h=e.src.slice(p,c),o[s][1].test(h)){0!==h.length&&i++;break}return e.line=i,(a=e.push("html_block","",0)).map=[t,i],a.content=e.getLines(t,i,e.blkIndent,!0),!0}},82015:e=>{function t(e){switch(e){case 10:case 33:case 35:case 36:case 37:case 38:case 42:case 43:case 45:case 58:case 60:case 61:case 62:case 64:case 91:case 92:case 93:case 94:case 95:case 96:case 123:case 125:case 126:return!0;default:return!1}}e.exports=function(e,n){for(var r=e.pos;r<e.posMax&&!t(e.src.charCodeAt(r));)r++;return r!==e.pos&&(n||(e.pending+=e.src.slice(e.pos,r)),e.pos=r,!0)}},82941:(e,t,n)=>{var r=n(49963).isSpace;e.exports=function(e,t,n,s){var o,i,a,h,p,c,l,u,f,d,k,m,C,g,b,v,A,x,y,M,_=e.lineMax,S=e.bMarks[t]+e.tShift[t],I=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(62!==e.src.charCodeAt(S++))return!1;if(s)return!0;for(h=f=e.sCount[t]+1,32===e.src.charCodeAt(S)?(S++,h++,f++,o=!1,v=!0):9===e.src.charCodeAt(S)?(v=!0,(e.bsCount[t]+f)%4==3?(S++,h++,f++,o=!1):o=!0):v=!1,d=[e.bMarks[t]],e.bMarks[t]=S;S<I&&(i=e.src.charCodeAt(S),r(i));)9===i?f+=4-(f+e.bsCount[t]+(o?1:0))%4:f++,S++;for(k=[e.bsCount[t]],e.bsCount[t]=e.sCount[t]+1+(v?1:0),c=S>=I,g=[e.sCount[t]],e.sCount[t]=f-h,b=[e.tShift[t]],e.tShift[t]=S-e.bMarks[t],x=e.md.block.ruler.getRules("blockquote"),C=e.parentType,e.parentType="blockquote",u=t+1;u<n&&(M=e.sCount[u]<e.blkIndent,!((S=e.bMarks[u]+e.tShift[u])>=(I=e.eMarks[u])));u++)if(62!==e.src.charCodeAt(S++)||M){if(c)break;for(A=!1,a=0,p=x.length;a<p;a++)if(x[a](e,u,n,!0)){A=!0;break}if(A){e.lineMax=u,0!==e.blkIndent&&(d.push(e.bMarks[u]),k.push(e.bsCount[u]),b.push(e.tShift[u]),g.push(e.sCount[u]),e.sCount[u]-=e.blkIndent);break}d.push(e.bMarks[u]),k.push(e.bsCount[u]),b.push(e.tShift[u]),g.push(e.sCount[u]),e.sCount[u]=-1}else{for(h=f=e.sCount[u]+1,32===e.src.charCodeAt(S)?(S++,h++,f++,o=!1,v=!0):9===e.src.charCodeAt(S)?(v=!0,(e.bsCount[u]+f)%4==3?(S++,h++,f++,o=!1):o=!0):v=!1,d.push(e.bMarks[u]),e.bMarks[u]=S;S<I&&(i=e.src.charCodeAt(S),r(i));)9===i?f+=4-(f+e.bsCount[u]+(o?1:0))%4:f++,S++;c=S>=I,k.push(e.bsCount[u]),e.bsCount[u]=e.sCount[u]+1+(v?1:0),g.push(e.sCount[u]),e.sCount[u]=f-h,b.push(e.tShift[u]),e.tShift[u]=S-e.bMarks[u]}for(m=e.blkIndent,e.blkIndent=0,(y=e.push("blockquote_open","blockquote",1)).markup=">",y.map=l=[t,0],e.md.block.tokenize(e,t,u),(y=e.push("blockquote_close","blockquote",-1)).markup=">",e.lineMax=_,e.parentType=C,l[1]=e.line,a=0;a<b.length;a++)e.bMarks[a+t]=d[a],e.tShift[a+t]=b[a],e.sCount[a+t]=g[a],e.bsCount[a+t]=k[a];return e.blkIndent=m,!0}},86897:(e,t,n)=>{var r=n(49963).normalizeReference,s=n(49963).isSpace;e.exports=function(e,t,n,o){var i,a,h,p,c,l,u,f,d,k,m,C,g,b,v,A,x=0,y=e.bMarks[t]+e.tShift[t],M=e.eMarks[t],_=t+1;if(e.sCount[t]-e.blkIndent>=4)return!1;if(91!==e.src.charCodeAt(y))return!1;for(;++y<M;)if(93===e.src.charCodeAt(y)&&92!==e.src.charCodeAt(y-1)){if(y+1===M)return!1;if(58!==e.src.charCodeAt(y+1))return!1;break}for(p=e.lineMax,v=e.md.block.ruler.getRules("reference"),k=e.parentType,e.parentType="reference";_<p&&!e.isEmpty(_);_++)if(!(e.sCount[_]-e.blkIndent>3||e.sCount[_]<0)){for(b=!1,l=0,u=v.length;l<u;l++)if(v[l](e,_,p,!0)){b=!0;break}if(b)break}for(M=(g=e.getLines(t,_,e.blkIndent,!1).trim()).length,y=1;y<M;y++){if(91===(i=g.charCodeAt(y)))return!1;if(93===i){d=y;break}(10===i||92===i&&++y<M&&10===g.charCodeAt(y))&&x++}if(d<0||58!==g.charCodeAt(d+1))return!1;for(y=d+2;y<M;y++)if(10===(i=g.charCodeAt(y)))x++;else if(!s(i))break;if(!(m=e.md.helpers.parseLinkDestination(g,y,M)).ok)return!1;if(c=e.md.normalizeLink(m.str),!e.md.validateLink(c))return!1;for(a=y=m.pos,h=x+=m.lines,C=y;y<M;y++)if(10===(i=g.charCodeAt(y)))x++;else if(!s(i))break;for(m=e.md.helpers.parseLinkTitle(g,y,M),y<M&&C!==y&&m.ok?(A=m.str,y=m.pos,x+=m.lines):(A="",y=a,x=h);y<M&&(i=g.charCodeAt(y),s(i));)y++;if(y<M&&10!==g.charCodeAt(y)&&A)for(A="",y=a,x=h;y<M&&(i=g.charCodeAt(y),s(i));)y++;return!(y<M&&10!==g.charCodeAt(y)||!(f=r(g.slice(1,d)))||(o||(void 0===e.env.references&&(e.env.references={}),void 0===e.env.references[f]&&(e.env.references[f]={title:A,href:c}),e.parentType=k,e.line=t+x+1),0))}},86955:e=>{var t=/^<([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/,n=/^<([a-zA-Z][a-zA-Z0-9+.\-]{1,31}):([^<>\x00-\x20]*)>/;e.exports=function(e,r){var s,o,i,a,h,p,c=e.pos;return!(60!==e.src.charCodeAt(c)||(s=e.src.slice(c)).indexOf(">")<0||(n.test(s)?(a=(o=s.match(n))[0].slice(1,-1),h=e.md.normalizeLink(a),!e.md.validateLink(h)||(r||((p=e.push("link_open","a",1)).attrs=[["href",h]],p.markup="autolink",p.info="auto",(p=e.push("text","",0)).content=e.md.normalizeLinkText(a),(p=e.push("link_close","a",-1)).markup="autolink",p.info="auto"),e.pos+=o[0].length,0)):!t.test(s)||(a=(i=s.match(t))[0].slice(1,-1),h=e.md.normalizeLink("mailto:"+a),!e.md.validateLink(h)||(r||((p=e.push("link_open","a",1)).attrs=[["href",h]],p.markup="autolink",p.info="auto",(p=e.push("text","",0)).content=e.md.normalizeLinkText(a),(p=e.push("link_close","a",-1)).markup="autolink",p.info="auto"),e.pos+=i[0].length,0))))}},87046:e=>{e.exports=function(e,t){var n,r,s,o,i,a,h=t+1,p=e.md.block.ruler.getRules("paragraph"),c=e.lineMax;for(a=e.parentType,e.parentType="paragraph";h<c&&!e.isEmpty(h);h++)if(!(e.sCount[h]-e.blkIndent>3||e.sCount[h]<0)){for(r=!1,s=0,o=p.length;s<o;s++)if(p[s](e,h,c,!0)){r=!0;break}if(r)break}return n=e.getLines(t,h,e.blkIndent,!1).trim(),e.line=h,(i=e.push("paragraph_open","p",1)).map=[t,e.line],(i=e.push("inline","",0)).content=n,i.map=[t,e.line],i.children=[],i=e.push("paragraph_close","p",-1),e.parentType=a,!0}},88e3:(e,t,n)=>{var r=n(49963).isSpace;e.exports=function(e,t,n,s){var o,i,a,h,p=e.bMarks[t]+e.tShift[t],c=e.eMarks[t];if(e.sCount[t]-e.blkIndent>=4)return!1;if(42!==(o=e.src.charCodeAt(p++))&&45!==o&&95!==o)return!1;for(i=1;p<c;){if((a=e.src.charCodeAt(p++))!==o&&!r(a))return!1;a===o&&i++}return!(i<3||(s||(e.line=t+1,(h=e.push("hr","hr",0)).map=[t,e.line],h.markup=Array(i+1).join(String.fromCharCode(o))),0))}},93898:e=>{function t(e,t){var n,r,s,o,i,a;for(n=t.length-1;n>=0;n--)95!==(r=t[n]).marker&&42!==r.marker||-1!==r.end&&(s=t[r.end],a=n>0&&t[n-1].end===r.end+1&&t[n-1].token===r.token-1&&t[r.end+1].token===s.token+1&&t[n-1].marker===r.marker,i=String.fromCharCode(r.marker),(o=e.tokens[r.token]).type=a?"strong_open":"em_open",o.tag=a?"strong":"em",o.nesting=1,o.markup=a?i+i:i,o.content="",(o=e.tokens[s.token]).type=a?"strong_close":"em_close",o.tag=a?"strong":"em",o.nesting=-1,o.markup=a?i+i:i,o.content="",a&&(e.tokens[t[n-1].token].content="",e.tokens[t[r.end+1].token].content="",n--))}e.exports.q=function(e,t){var n,r,s=e.pos,o=e.src.charCodeAt(s);if(t)return!1;if(95!==o&&42!==o)return!1;for(r=e.scanDelims(e.pos,42===o),n=0;n<r.length;n++)e.push("text","",0).content=String.fromCharCode(o),e.delimiters.push({marker:o,length:r.length,jump:n,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close});return e.pos+=r.length,!0},e.exports.g=function(e){var n,r=e.tokens_meta,s=e.tokens_meta.length;for(t(e,e.delimiters),n=0;n<s;n++)r[n]&&r[n].delimiters&&t(e,r[n].delimiters)}},97141:e=>{function t(e,t){var n,r,s,o,i,a=[],h=t.length;for(n=0;n<h;n++)126===(s=t[n]).marker&&-1!==s.end&&(o=t[s.end],(i=e.tokens[s.token]).type="s_open",i.tag="s",i.nesting=1,i.markup="~~",i.content="",(i=e.tokens[o.token]).type="s_close",i.tag="s",i.nesting=-1,i.markup="~~",i.content="","text"===e.tokens[o.token-1].type&&"~"===e.tokens[o.token-1].content&&a.push(o.token-1));for(;a.length;){for(r=(n=a.pop())+1;r<e.tokens.length&&"s_close"===e.tokens[r].type;)r++;n!==--r&&(i=e.tokens[r],e.tokens[r]=e.tokens[n],e.tokens[n]=i)}}e.exports.q=function(e,t){var n,r,s,o,i=e.pos,a=e.src.charCodeAt(i);if(t)return!1;if(126!==a)return!1;if(s=(r=e.scanDelims(e.pos,!0)).length,o=String.fromCharCode(a),s<2)return!1;for(s%2&&(e.push("text","",0).content=o,s--),n=0;n<s;n+=2)e.push("text","",0).content=o+o,e.delimiters.push({marker:a,length:0,jump:n,token:e.tokens.length-1,end:-1,open:r.can_open,close:r.can_close});return e.pos+=r.length,!0},e.exports.g=function(e){var n,r=e.tokens_meta,s=e.tokens_meta.length;for(t(e,e.delimiters),n=0;n<s;n++)r[n]&&r[n].delimiters&&t(e,r[n].delimiters)}}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5660],{82559:(e,t,o)=>{o.d(t,{D:()=>i});const i={incomplete:"incomplete",claimable:"claimable",claimed:"claimed"}},"gamification/objectives":(e,t,o)=>{o.r(t),o.d(t,{Objectives:()=>f});var i,a=o(15215),n=o(68663),r=o("aurelia-framework"),c=o(38951),s=o(10147),l=o(29610),b=o(72596),v=o(54995),p=o(32426),d=o(82559);let f=i=class{#e;#t;constructor(e){this.objectiveSets=[],this.Objectives=i,this.#t=e}async bind(){this.objectiveSets=await this.#t.getCurrentObjectiveSets(),this.#e=(0,c.Y)(1e4).pipe((0,s.E)(p.o),(0,l.p)((([e,t])=>t)),(0,l.p)((()=>this.objectiveSets.length>0)),(0,b.n)((()=>this.#t.getCurrentObjectiveSets()))).subscribe((e=>this.objectiveSets=e))}get streak(){return this.account?.activeStreak??0}unbind(){this.#e?.unsubscribe()}async accountChanged(){this.objectiveSets=await this.#t.getCurrentObjectiveSets()}static sortAndFilterObjectives(e){return e.filter((e=>e.state!==d.D.claimed)).sort(((e,t)=>e.state===t.state?0:e.state===d.D.claimable?-1:1))}};(0,a.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.fromView}),(0,a.Sn)("design:type",Object)],f.prototype,"title",void 0),(0,a.Cg)([(0,r.computedFrom)("account"),(0,a.Sn)("design:type",Number),(0,a.Sn)("design:paramtypes",[])],f.prototype,"streak",null),f=i=(0,a.Cg)([(0,r.autoinject)(),(0,v.m6)({selectors:{account:(0,v.$t)((e=>e.account))}}),(0,a.Sn)("design:paramtypes",[n.x])],f)},"gamification/objectives.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>r});var i=o(14385),a=o.n(i),n=new URL(o(89247),o.b);const r='<template> <require from="./objectives.scss"></require> <require from="./resources/elements/objective"></require> <div class="objectives-container ${objectiveSets.length === 0 ? \'objectives-container--empty\' : \'\'}"> <div class="header"> <div class="profile-container navigable"> <img if.bind="account.profileImage" fallback-src="'+a()(n)+'" src.bind="account.profileImage | cdn: {size: 48}"> <div class="stack"> <span class="welcome">${\'objectives.welcome_to_wemod\' | i18n}</span> <span class="username">${account.username}</span> </div> <a href="wemod://profile/profile"></a> </div> <div class="stack navigable boosts"> <span class="profile-metric-label">${\'objectives.boosts\' | i18n}</span> <span class="boost-count ${account.boosts === 0 ? \'boost-count--none\' : \'\'}">${account.boosts}</span> <a href="wemod://queue/queue"></a> </div> <div class="stack"> <span class="profile-metric-label">${\'objectives.streak\' | i18n}</span> <span class="streak"> <span>${(streak === 1 ? \'objectives.$x_day\' : \'objectives.$x_days\') | i18n:{x: streak}}</span> </span> </div> </div> <div if.bind="objectiveSets.length > 0" class="items-container" repeat.for="objectiveSet of objectiveSets"> <div class="title-container"> <div class="title"> <span class="objective-icon"></span> <span>${objectiveSet.name}</span> </div> <span class="expiration"> ${objectiveSet.resetsIn} </span> </div> <div class="items-overflow"> <objective repeat.for="objective of Objectives.sortAndFilterObjectives(objectiveSet.objectives)" objective.bind="objective"></objective> </div> </div> </div> </template> '},"gamification/objectives.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>p});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),c=o(4417),s=o.n(c),l=new URL(o(83959),o.b),b=r()(a()),v=s()(l);b.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${v}) format("woff2")}.material-symbols-outlined,objectives .objectives-container .title .objective-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}objectives{position:relative;display:block}objectives .objectives-container{display:flex;flex-direction:column;padding:20px 0px 12px;gap:20px;background:linear-gradient(180deg, rgba(221, 240, 12, 0) 0%, rgba(221, 240, 12, 0.1) 100%),rgba(19,20,23,.9);border-radius:16px}objectives .objectives-container--empty{padding:20px 0px}objectives .objectives-container .items-container{padding:0px 12px}objectives .objectives-container .items-overflow{max-height:128px;overflow-y:auto;overflow-x:hidden}objectives .objectives-container .items-overflow::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}objectives .objectives-container .items-overflow::-webkit-scrollbar-thumb:window-inactive,objectives .objectives-container .items-overflow::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}objectives .objectives-container .items-overflow::-webkit-scrollbar-thumb:window-inactive:hover,objectives .objectives-container .items-overflow::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}objectives .objectives-container .items-overflow::-webkit-scrollbar{background:rgba(0,0,0,0)}objectives .objectives-container .header{display:flex;flex-direction:row;padding:0px 20px}objectives .objectives-container .header .navigable{transition:opacity .2s;position:relative}objectives .objectives-container .header .navigable:hover{opacity:.6}objectives .objectives-container .header .navigable *{cursor:pointer}objectives .objectives-container .header .navigable a{position:absolute;width:100%;height:100%}objectives .objectives-container .header img{border-radius:48px;border:.5px solid var(--white-white-15-transparent, rgba(255, 255, 255, 0.15));height:36px;width:36px}objectives .objectives-container .header .profile-container{display:flex;flex-direction:row;gap:12px;flex-grow:1;overflow:hidden;white-space:nowrap}objectives .objectives-container .header .username{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700}.theme-default objectives .objectives-container .header .username{color:#fff}.theme-purple-pro objectives .objectives-container .header .username{color:#fff}.theme-green-pro objectives .objectives-container .header .username{color:#fff}.theme-orange-pro objectives .objectives-container .header .username{color:#fff}.theme-pro objectives .objectives-container .header .username{color:#fff}objectives .objectives-container .header .welcome{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;text-wrap:auto}.theme-default objectives .objectives-container .header .welcome{color:rgba(255,255,255,.8)}.theme-purple-pro objectives .objectives-container .header .welcome{color:rgba(255,255,255,.8)}.theme-green-pro objectives .objectives-container .header .welcome{color:rgba(255,255,255,.8)}.theme-orange-pro objectives .objectives-container .header .welcome{color:rgba(255,255,255,.8)}.theme-pro objectives .objectives-container .header .welcome{color:rgba(255,255,255,.8)}objectives .objectives-container .header .profile-metric-label{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;color:#ddf00c;text-align:end}objectives .objectives-container .header .boost-count,objectives .objectives-container .header .streak{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700;text-align:end}.theme-default objectives .objectives-container .header .boost-count,.theme-default objectives .objectives-container .header .streak{color:#fff}.theme-purple-pro objectives .objectives-container .header .boost-count,.theme-purple-pro objectives .objectives-container .header .streak{color:#fff}.theme-green-pro objectives .objectives-container .header .boost-count,.theme-green-pro objectives .objectives-container .header .streak{color:#fff}.theme-orange-pro objectives .objectives-container .header .boost-count,.theme-orange-pro objectives .objectives-container .header .streak{color:#fff}.theme-pro objectives .objectives-container .header .boost-count,.theme-pro objectives .objectives-container .header .streak{color:#fff}objectives .objectives-container .header .boost-count--none,objectives .objectives-container .header .streak--none{color:rgba(255,255,255,.5)}objectives .objectives-container .header .stack{display:flex;flex-direction:column}objectives .objectives-container .header .boosts{margin-right:12px}objectives .objectives-container .title{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;color:#ddf00c;display:flex;align-items:center;gap:10px;padding:4px 0}objectives .objectives-container .title-container{display:flex;flex-direction:row;justify-content:space-between;align-items:center;padding:0px 10px}objectives .objectives-container .title-container .expiration{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default objectives .objectives-container .title-container .expiration{color:rgba(255,255,255,.6)}.theme-purple-pro objectives .objectives-container .title-container .expiration{color:rgba(255,255,255,.6)}.theme-green-pro objectives .objectives-container .title-container .expiration{color:rgba(255,255,255,.6)}.theme-orange-pro objectives .objectives-container .title-container .expiration{color:rgba(255,255,255,.6)}.theme-pro objectives .objectives-container .title-container .expiration{color:rgba(255,255,255,.6)}objectives .objectives-container .title .objective-icon{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px}objectives .objectives-container .title .objective-icon:before{font-family:inherit;content:"editor_choice"}`,""]);const p=b},"gamification/resources/elements/action-badge":(e,t,o)=>{o.r(t),o.d(t,{ActionBadge:()=>n});var i=o(15215),a=o("aurelia-framework");class n{constructor(){this.sparkleCount=0,this.sparkles=[]}claimableChanged(){this.sparkleCount=this.claimable?Math.round(1*Math.random())+3:0,setTimeout((()=>{if(this.sparkleCount>0){const e=n.getShuffledEdgeList();this.sparkles.filter((e=>!!e)).forEach((t=>n.randomizeSparkle(t,e)))}}))}static getShuffledEdgeList(){return["top","right","bottom","left"].sort((()=>Math.random()-.5))}static randomizeSparkle(e,t){const o=Math.round(20*Math.random())+12;e.style.width=`${o}px`,e.style.height=`${o}px`;const i=t.pop()??"top",a=-4*Math.random()-o/2+"px",n=`calc(${Math.round(100*Math.random())}% - ${o/2}px)`;"top"===i||"bottom"===i?(e.style[i]=a,e.style.left=n):"right"!==i&&"left"!==i||(e.style.top=n,e.style[i]=a);const r=6*Math.random()+2.75+"s";e.style.animation=`shimmer ${r} infinite linear, ${Math.random()>.5?"pulse":"pulse-reverse"} ${r} infinite linear`}}(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",Boolean)],n.prototype,"claimable",void 0),(0,i.Cg)([a.bindable,(0,i.Sn)("design:type",String)],n.prototype,"action",void 0),(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Array)],n.prototype,"sparkles",void 0)},"gamification/resources/elements/action-badge.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>r});var i=o(14385),a=o.n(i),n=new URL(o(97198),o.b);const r='<template> <require from="./action-badge.scss"></require> <div if.bind="claimable" class="badge badge--claimable"> <span class="icon"></span> <span class="label"> ${\'objectives.claim_boost\' | i18n} </span> <img repeat.for="i of sparkleCount" class="sparkle" src="'+a()(n)+'" ref="sparkles[i]"> </div> <div else class="badge"> <span class="icon"></span> <span class="label"> ${action} </span> </div> </template> '},"gamification/resources/elements/action-badge.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>p});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),c=o(4417),s=o.n(c),l=new URL(o(83959),o.b),b=r()(a()),v=s()(l);b.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${v}) format("woff2")}.material-symbols-outlined,action-badge .badge:not(.badge--claimable) .icon,action-badge .badge--claimable .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}action-badge .tooltip-content{width:200px;text-align:center}action-badge .badge{display:flex;align-items:center;gap:4px;color:#ddf00c;padding:2px 6px;background:#454b14;border-radius:6px;position:relative}action-badge .badge .label{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px}action-badge .badge:not(.badge--claimable) .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;left:0;padding:2px 0 2px 6px;background:#454b14;border-radius:6px 0 0 6px;position:absolute;font-size:16px;opacity:0}action-badge .badge:not(.badge--claimable) .icon:before{font-family:inherit;content:"lock"}action-badge .badge:not(.badge--claimable):hover{border-radius:0 6px 6px 0}action-badge .badge:not(.badge--claimable):hover .icon{animation:lock-slide .15s forwards}action-badge .badge--claimable{color:#3b5209;background:#ddf00c;transition:background-color .2s}action-badge .badge--claimable:hover{background:#9bac04}action-badge .badge--claimable .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px}action-badge .badge--claimable .icon:before{font-family:inherit;content:"double_arrow"}@keyframes lock-slide{0%{transform:translateX(0%);opacity:0}100%{transform:translateX(-100%);opacity:1}}action-badge .sparkle{position:absolute;opacity:0}@keyframes shimmer{0%{opacity:0}10%{opacity:0}50%{opacity:1}90%{opacity:0}100%{opacity:0}}@keyframes pulse{0%{transform:scale(0.4) rotate(0deg)}50%{transform:scale(1) rotate(180deg)}100%{transform:scale(0.4) rotate(360deg)}}@keyframes pulse-reverse{0%{transform:scale(0.25) rotate(0deg)}50%{transform:scale(1) rotate(-180deg)}100%{transform:scale(0.25) rotate(-360deg)}}`,""]);const p=b},"gamification/resources/elements/objective":(e,t,o)=>{o.r(t),o.d(t,{Objective:()=>v});var i=o(15215),a=o(68663),n=o(10699),r=o(62914),c=o("aurelia-event-aggregator"),s=o("aurelia-framework"),l=o(21795),b=o(82559);let v=class{#o;#t;#i;#a;#n;constructor(e,t,o,i){this.#n=!1,this.#o=e,this.#t=t,this.#i=o,this.#a=i}claim(){this.#n||(this.#n=!0,this.#t.claimObjectiveReward(this.objective.id).then((()=>{this.#a.event("objective_reward_claimed",{objectiveId:String(this.objective.id)},r.Io),this.#i.refreshAccount()})).finally((()=>{this.#n=!1})))}followUrl(){this.objective.url&&(this.#o.publish(new l.SA(String(this.objective.id),this.objective.state!==b.D.incomplete)),null!==this.objective.url&&window.open(this.objective.url,"_blank"))}};(0,i.Cg)([s.bindable,(0,i.Sn)("design:type",Object)],v.prototype,"objective",void 0),v=(0,i.Cg)([(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[c.EventAggregator,a.x,n.G,r.j0])],v)},"gamification/resources/elements/objective.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>i});const i='<template> <require from="./objective.scss"></require> <require from="./action-badge"></require> <require from="shared/resources/elements/tooltip"></require> <div class="objective ${objective.state === \'claimable\' ? \'objective--complete\' : \'\'} ${objective.url ? \'objective--navigable\' : \'\'}" click.delegate="objective.state === \'claimable\' ? claim() : followUrl()"> <span class="objective-completion-icon"> </span> <span class="objective-label">${objective.name} </span> <action-badge claimable.bind="objective.state === \'claimable\'" action.bind="objective.action"> </action-badge> </div> </template> '},"gamification/resources/elements/objective.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>p});var i=o(31601),a=o.n(i),n=o(76314),r=o.n(n),c=o(4417),s=o.n(c),l=new URL(o(83959),o.b),b=r()(a()),v=s()(l);b.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${v}) format("woff2")}.material-symbols-outlined,objective .objective strong::after,objective .objective-completion-icon,objective .objective--complete .objective-completion-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}objective .objective{display:flex;align-items:center;gap:10px;padding:8px;border-radius:8px;transition:background-color .2s;position:relative}.theme-default objective .objective-label{color:rgba(255,255,255,.8)}.theme-purple-pro objective .objective-label{color:rgba(255,255,255,.8)}.theme-green-pro objective .objective-label{color:rgba(255,255,255,.8)}.theme-orange-pro objective .objective-label{color:rgba(255,255,255,.8)}.theme-pro objective .objective-label{color:rgba(255,255,255,.8)}objective .objective strong{color:#ddf00c;display:inline-flex;align-items:center;gap:4px}objective .objective strong::after{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;content:"arrow_outward";color:#ddf00c}objective .objective-label,objective .objective strong{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;flex-grow:1;transition:color .2s}objective .objective-completion-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:#ddf00c}objective .objective-completion-icon:before{font-family:inherit;content:"radio_button_unchecked"}objective .objective--complete .objective-label{text-decoration:line-through}.theme-default objective .objective--complete .objective-label{color:rgba(255,255,255,.6)}.theme-purple-pro objective .objective--complete .objective-label{color:rgba(255,255,255,.6)}.theme-green-pro objective .objective--complete .objective-label{color:rgba(255,255,255,.6)}.theme-orange-pro objective .objective--complete .objective-label{color:rgba(255,255,255,.6)}.theme-pro objective .objective--complete .objective-label{color:rgba(255,255,255,.6)}objective .objective--complete strong{text-decoration:line-through}objective .objective--complete strong::after{display:none}objective .objective--complete .objective-completion-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:#ddf00c}objective .objective--complete .objective-completion-icon:before{font-family:inherit;content:"check_circle"}objective .objective--navigable:hover,objective .objective--complete:hover{background:rgba(255,255,255,.05)}objective .objective--navigable:hover,objective .objective--navigable:hover *,objective .objective--complete:hover,objective .objective--complete:hover *{cursor:pointer}.theme-default objective .objective--navigable:hover .objective-label,.theme-default objective .objective--complete:hover .objective-label{color:#fff}.theme-purple-pro objective .objective--navigable:hover .objective-label,.theme-purple-pro objective .objective--complete:hover .objective-label{color:#fff}.theme-green-pro objective .objective--navigable:hover .objective-label,.theme-green-pro objective .objective--complete:hover .objective-label{color:#fff}.theme-orange-pro objective .objective--navigable:hover .objective-label,.theme-orange-pro objective .objective--complete:hover .objective-label{color:#fff}.theme-pro objective .objective--navigable:hover .objective-label,.theme-pro objective .objective--complete:hover .objective-label{color:#fff}objective action-badge{min-width:max-content}`,""]);const p=b}}]);
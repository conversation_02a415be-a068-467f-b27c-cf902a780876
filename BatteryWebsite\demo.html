<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PowerCore - 项目演示</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            line-height: 1.6;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        .header {
            text-align: center;
            padding: 60px 0;
            background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
        }
        
        .header h1 {
            font-family: 'Orbitron', monospace;
            font-size: 3.5rem;
            font-weight: 900;
            margin-bottom: 20px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .header p {
            font-size: 1.3rem;
            color: #cccccc;
            margin-bottom: 40px;
        }
        
        .demo-btn {
            display: inline-block;
            padding: 15px 40px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 600;
            font-size: 18px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
        }
        
        .demo-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 30px rgba(0, 212, 255, 0.5);
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 80px 0;
        }
        
        .feature-card {
            background: rgba(45, 45, 68, 0.8);
            backdrop-filter: blur(20px);
            border-radius: 20px;
            padding: 40px 30px;
            border: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
            transition: 0.5s;
        }
        
        .feature-card:hover::before {
            left: 100%;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            border-color: #00d4ff;
            box-shadow: 0 20px 40px rgba(0, 212, 255, 0.3);
        }
        
        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            color: white;
            margin-bottom: 25px;
            position: relative;
            z-index: 1;
        }
        
        .feature-card h3 {
            font-family: 'Orbitron', monospace;
            font-size: 1.5rem;
            margin-bottom: 15px;
            color: #00d4ff;
            position: relative;
            z-index: 1;
        }
        
        .feature-card p {
            color: #cccccc;
            line-height: 1.8;
            position: relative;
            z-index: 1;
        }
        
        .tech-showcase {
            background: rgba(26, 26, 26, 0.8);
            border-radius: 20px;
            padding: 60px 40px;
            margin: 80px 0;
            border: 1px solid rgba(0, 212, 255, 0.2);
        }
        
        .tech-title {
            text-align: center;
            font-family: 'Orbitron', monospace;
            font-size: 2.5rem;
            margin-bottom: 40px;
            color: #00d4ff;
        }
        
        .tech-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 30px;
        }
        
        .tech-item {
            text-align: center;
            padding: 30px 20px;
            background: rgba(0, 212, 255, 0.1);
            border-radius: 15px;
            border: 1px solid rgba(0, 212, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .tech-item:hover {
            transform: scale(1.05);
            background: rgba(0, 212, 255, 0.2);
        }
        
        .tech-item i {
            font-size: 48px;
            color: #00d4ff;
            margin-bottom: 20px;
        }
        
        .tech-item h4 {
            font-family: 'Orbitron', monospace;
            margin-bottom: 15px;
            color: #ffffff;
        }
        
        .tech-item p {
            color: #cccccc;
            font-size: 14px;
        }
        
        .preview-section {
            margin: 80px 0;
            text-align: center;
        }
        
        .preview-title {
            font-family: 'Orbitron', monospace;
            font-size: 2.5rem;
            margin-bottom: 30px;
            color: #00d4ff;
        }
        
        .preview-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 40px;
        }
        
        .preview-card {
            background: rgba(45, 45, 68, 0.8);
            border-radius: 15px;
            overflow: hidden;
            border: 1px solid rgba(0, 212, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .preview-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 212, 255, 0.3);
        }
        
        .preview-image {
            height: 200px;
            background: linear-gradient(135deg, #00d4ff, #0099cc);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 64px;
            color: white;
        }
        
        .preview-content {
            padding: 25px;
        }
        
        .preview-content h4 {
            font-family: 'Orbitron', monospace;
            margin-bottom: 10px;
            color: #00d4ff;
        }
        
        .preview-content p {
            color: #cccccc;
            font-size: 14px;
        }
        
        .stats-section {
            background: rgba(26, 26, 26, 0.8);
            border-radius: 20px;
            padding: 60px 40px;
            margin: 80px 0;
            text-align: center;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 40px;
            margin-top: 40px;
        }
        
        .stat-item {
            padding: 30px 20px;
        }
        
        .stat-number {
            font-family: 'Orbitron', monospace;
            font-size: 3rem;
            font-weight: 700;
            color: #00d4ff;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #cccccc;
            font-size: 1.1rem;
        }
        
        .footer {
            text-align: center;
            padding: 60px 0;
            border-top: 1px solid rgba(0, 212, 255, 0.2);
            margin-top: 80px;
        }
        
        .footer p {
            color: #888888;
            margin-bottom: 20px;
        }
        
        .social-links {
            display: flex;
            justify-content: center;
            gap: 20px;
        }
        
        .social-links a {
            width: 50px;
            height: 50px;
            background: rgba(0, 212, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #00d4ff;
            font-size: 20px;
            transition: all 0.3s ease;
            border: 1px solid rgba(0, 212, 255, 0.3);
        }
        
        .social-links a:hover {
            background: #00d4ff;
            color: white;
            transform: translateY(-3px);
        }
        
        @media (max-width: 768px) {
            .header h1 {
                font-size: 2.5rem;
            }
            
            .features-grid,
            .tech-grid,
            .preview-grid {
                grid-template-columns: 1fr;
            }
            
            .tech-showcase,
            .stats-section {
                padding: 40px 20px;
            }
        }
        
        /* 动画效果 */
        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }
        
        .feature-icon {
            animation: float 3s ease-in-out infinite;
        }
        
        .feature-card:nth-child(2) .feature-icon {
            animation-delay: 0.5s;
        }
        
        .feature-card:nth-child(3) .feature-icon {
            animation-delay: 1s;
        }
        
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        
        .tech-item i {
            animation: pulse 2s ease-in-out infinite;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><i class="fas fa-bolt"></i> PowerCore</h1>
            <p>未来电池技术的高端展示网站</p>
            <a href="index.html" class="demo-btn">
                <i class="fas fa-rocket"></i> 查看完整网站
            </a>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-palette"></i>
                </div>
                <h3>现代化设计</h3>
                <p>采用最新的设计趋势，深色主题配合渐变效果，营造科技感十足的视觉体验。使用CSS Grid和Flexbox实现完美的响应式布局。</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-cube"></i>
                </div>
                <h3>3D可视化</h3>
                <p>创新的3D电池模型展示，支持鼠标拖拽旋转交互。电池剖面图展示内部结构，动态离子流动动画演示工作原理。</p>
            </div>
            
            <div class="feature-card">
                <div class="feature-icon">
                    <i class="fas fa-magic"></i>
                </div>
                <h3>炫酷动画</h3>
                <p>基于AOS和GSAP的高性能动画效果，包括滚动触发动画、粒子系统、数字计数动画等，提供流畅的用户体验。</p>
            </div>
        </div>
        
        <div class="tech-showcase">
            <h2 class="tech-title">技术特色</h2>
            <div class="tech-grid">
                <div class="tech-item">
                    <i class="fab fa-html5"></i>
                    <h4>HTML5</h4>
                    <p>语义化标签，无障碍访问</p>
                </div>
                <div class="tech-item">
                    <i class="fab fa-css3-alt"></i>
                    <h4>CSS3</h4>
                    <p>Grid布局，动画，3D变换</p>
                </div>
                <div class="tech-item">
                    <i class="fab fa-js-square"></i>
                    <h4>JavaScript</h4>
                    <p>ES6+，模块化，异步编程</p>
                </div>
                <div class="tech-item">
                    <i class="fas fa-mobile-alt"></i>
                    <h4>响应式</h4>
                    <p>完美适配各种设备</p>
                </div>
            </div>
        </div>
        
        <div class="preview-section">
            <h2 class="preview-title">页面预览</h2>
            <div class="preview-grid">
                <div class="preview-card">
                    <div class="preview-image">
                        <i class="fas fa-home"></i>
                    </div>
                    <div class="preview-content">
                        <h4>英雄区域</h4>
                        <p>3D电池模型，动态背景，行动号召</p>
                    </div>
                </div>
                
                <div class="preview-card">
                    <div class="preview-image">
                        <i class="fas fa-battery-full"></i>
                    </div>
                    <div class="preview-content">
                        <h4>产品展示</h4>
                        <p>产品网格，悬停效果，规格展示</p>
                    </div>
                </div>
                
                <div class="preview-card">
                    <div class="preview-image">
                        <i class="fas fa-cogs"></i>
                    </div>
                    <div class="preview-content">
                        <h4>技术展示</h4>
                        <p>电池剖面，交互切换，动画演示</p>
                    </div>
                </div>
                
                <div class="preview-card">
                    <div class="preview-image">
                        <i class="fas fa-flask"></i>
                    </div>
                    <div class="preview-content">
                        <h4>创新实验室</h4>
                        <p>数字动画，研究展示，统计数据</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="stats-section">
            <h2 class="tech-title">项目数据</h2>
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">1000+</div>
                    <div class="stat-label">代码行数</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">15+</div>
                    <div class="stat-label">动画效果</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">100%</div>
                    <div class="stat-label">响应式</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">5</div>
                    <div class="stat-label">主要页面</div>
                </div>
            </div>
        </div>
        
        <div class="footer">
            <p>&copy; 2024 PowerCore. 项目演示 - 仅供学习使用</p>
            <div class="social-links">
                <a href="#"><i class="fab fa-github"></i></a>
                <a href="#"><i class="fab fa-codepen"></i></a>
                <a href="#"><i class="fab fa-dribbble"></i></a>
                <a href="#"><i class="fab fa-behance"></i></a>
            </div>
        </div>
    </div>
</body>
</html>

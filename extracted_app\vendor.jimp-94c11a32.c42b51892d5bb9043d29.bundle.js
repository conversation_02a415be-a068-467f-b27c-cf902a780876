"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2808],{7234:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{gaussian:function(t,i){if("number"!=typeof t)return a.throwError.call(this,"r must be a number",i);if(t<1)return a.throwError.call(this,"r must be greater than 0",i);for(var e=Math.ceil(2.57*t),r=2*e+1,n=t*t*2,h=n*Math.PI,s=[],o=0;o<r;o++){s[o]=[];for(var l=0;l<r;l++){var u=Math.pow(l-e,2)+Math.pow(o-e,2);s[o][l]=Math.exp(-u/n)/h}}for(var d=0;d<this.bitmap.height;d++)for(var f=0;f<this.bitmap.width;f++)for(var p=0,c=0,b=0,m=0,g=0,v=0;v<r;v++){for(var y=0;y<r;y++){var w=Math.min(this.bitmap.width-1,Math.max(0,y+f-e)),_=Math.min(this.bitmap.height-1,Math.max(0,v+d-e)),M=s[v][y],B=_*this.bitmap.width+w<<2;p+=this.bitmap.data[B]*M,c+=this.bitmap.data[B+1]*M,b+=this.bitmap.data[B+2]*M,m+=this.bitmap.data[B+3]*M,g+=M}var P=d*this.bitmap.width+f<<2;this.bitmap.data[P]=Math.round(p/g),this.bitmap.data[P+1]=Math.round(c/g),this.bitmap.data[P+2]=Math.round(b/g),this.bitmap.data[P+3]=Math.round(m/g)}return(0,a.isNodePattern)(i)&&i.call(this,null,this),this}}},t.exports=i.default},20174:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{threshold:function(t,i){var e=this,r=t.max,n=t.replace,h=void 0===n?255:n,s=t.autoGreyscale,o=void 0===s||s;return"number"!=typeof r?a.throwError.call(this,"max must be a number",i):"number"!=typeof h?a.throwError.call(this,"replace must be a number",i):"boolean"!=typeof o?a.throwError.call(this,"autoGreyscale must be a boolean",i):(r=this.constructor.limit255(r),h=this.constructor.limit255(h),o&&this.greyscale(),this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,i,a){var n=e.bitmap.data[a]<r?e.bitmap.data[a]:h;e.bitmap.data[a]=n,e.bitmap.data[a+1]=n,e.bitmap.data[a+2]=n})),(0,a.isNodePattern)(i)&&i.call(this,null,this),this)}}},t.exports=i.default},30058:(t,i,e)=>{var a=e(2613);Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var r=a(e(73738)),n=e(65414);i.default=function(){return{displace:function(t,i,e){if("object"!==(0,r.default)(t)||t.constructor!==this.constructor)return n.throwError.call(this,"The source must be a Jimp image",e);if("number"!=typeof i)return n.throwError.call(this,"factor must be a number",e);var a=this.cloneQuiet();return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(e,r,n){var h=t.bitmap.data[n]/256*i;h=Math.round(h);var s=this.getPixelIndex(e+h,r);this.bitmap.data[s]=a.bitmap.data[n],this.bitmap.data[s+1]=a.bitmap.data[n+1],this.bitmap.data[s+2]=a.bitmap.data[n+2]})),(0,n.isNodePattern)(e)&&e.call(this,null,this),this}}},t.exports=i.default},31050:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{fisheye:function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{r:2.5},e=arguments.length>1?arguments[1]:void 0;"function"==typeof i&&(e=i,i={r:2.5});var r=this.cloneQuiet(),n=r.bitmap,h=n.width,s=n.height;return r.scanQuiet(0,0,h,s,(function(e,a){var n=e/h,o=a/s,l=Math.sqrt(Math.pow(n-.5,2)+Math.pow(o-.5,2)),u=2*Math.pow(l,i.r),d=(n-.5)/l,f=(o-.5)/l,p=Math.round((u*d+.5)*h),c=Math.round((u*f+.5)*s),b=r.getPixelColor(p,c);t.setPixelColor(b,e,a)})),this.setPixelColor(r.getPixelColor(h/2,s/2),h/2,s/2),(0,a.isNodePattern)(e)&&e.call(this,null,this),this}}},t.exports=i.default},39219:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{shadow:function(){var t=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;"function"==typeof i&&(e=i,i={});var r=i,n=r.opacity,h=void 0===n?.7:n,s=r.size,o=void 0===s?1.1:s,l=r.x,u=void 0===l?-25:l,d=r.y,f=void 0===d?25:d,p=r.blur,c=void 0===p?5:p,b=this.clone(),m=this.clone();return m.scan(0,0,m.bitmap.width,m.bitmap.height,(function(i,e,a){m.bitmap.data[a]=0,m.bitmap.data[a+1]=0,m.bitmap.data[a+2]=0,m.bitmap.data[a+3]=m.constructor.limit255(m.bitmap.data[a+3]*h),t.bitmap.data[a]=0,t.bitmap.data[a+1]=0,t.bitmap.data[a+2]=0,t.bitmap.data[a+3]=0})),m.resize(m.bitmap.width*o,m.bitmap.height*o).blur(c),this.composite(m,u,f),this.composite(b,0,0),(0,a.isNodePattern)(e)&&e.call(this,null,this),this}}},t.exports=i.default},42273:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{scale:function(t,i,e){if("number"!=typeof t)return a.throwError.call(this,"f must be a number",e);if(t<0)return a.throwError.call(this,"f must be a positive number",e);"function"==typeof i&&void 0===e&&(e=i,i=null);var r=this.bitmap.width*t,n=this.bitmap.height*t;return this.resize(r,n,i),(0,a.isNodePattern)(e)&&e.call(this,null,this),this},scaleToFit:function(t,i,e,r){if("number"!=typeof t||"number"!=typeof i)return a.throwError.call(this,"w and h must be numbers",r);"function"==typeof e&&void 0===r&&(r=e,e=null);var n=t/i>this.bitmap.width/this.bitmap.height?i/this.bitmap.height:t/this.bitmap.width;return this.scale(n,e),(0,a.isNodePattern)(r)&&r.call(this,null,this),this}}},t.exports=i.default},50669:(t,i,e)=>{var a=e(2613);Object.defineProperty(i,"__esModule",{value:!0}),i.default=function(t){return t("crop",(function(t,i,e,a,r){if("number"!=typeof t||"number"!=typeof i)return n.throwError.call(this,"x and y must be numbers",r);if("number"!=typeof e||"number"!=typeof a)return n.throwError.call(this,"w and h must be numbers",r);if(t=Math.round(t),i=Math.round(i),e=Math.round(e),a=Math.round(a),0===t&&e===this.bitmap.width){var h=e*i+t<<2,s=h+(a*e<<2);this.bitmap.data=this.bitmap.data.slice(h,s)}else{var o=Buffer.allocUnsafe(e*a*4),l=0;this.scanQuiet(t,i,e,a,(function(t,i,e){var a=this.bitmap.data.readUInt32BE(e,!0);o.writeUInt32BE(a,l,!0),l+=4})),this.bitmap.data=o}return this.bitmap.width=e,this.bitmap.height=a,(0,n.isNodePattern)(r)&&r.call(this,null,this),this})),{class:{autocrop:function(){for(var t,i=this.bitmap.width,e=this.bitmap.height,a=0,h=2e-4,s=!0,o=!1,l={north:!1,south:!1,east:!1,west:!1},u=arguments.length,d=new Array(u),f=0;f<u;f++)d[f]=arguments[f];for(var p=0,c=d.length;p<c;p++)if("number"==typeof d[p]&&(h=d[p]),"boolean"==typeof d[p]&&(s=d[p]),"function"==typeof d[p]&&(t=d[p]),"object"===(0,r.default)(d[p])){var b=d[p];void 0!==b.tolerance&&(h=b.tolerance),void 0!==b.cropOnlyFrames&&(s=b.cropOnlyFrames),void 0!==b.cropSymmetric&&(o=b.cropSymmetric),void 0!==b.leaveBorder&&(a=b.leaveBorder),void 0!==b.ignoreSides&&(l=b.ignoreSides)}var m=this.getPixelColor(0,0),g=this.constructor.intToRGBA(m),v=0,y=0,w=0,_=0;if(m=this.getPixelColor(0,0),!l.north)t:for(var M=0;M<e-1;M++){for(var B=0;B<i;B++){var P=this.getPixelColor(B,M),E=this.constructor.intToRGBA(P);if(this.constructor.colorDiff(g,E)>h)break t}v++}if(m=this.getPixelColor(i,0),!l.east)t:for(var C=0;C<i-1;C++){for(var x=0+v;x<e;x++){var N=this.getPixelColor(C,x),I=this.constructor.intToRGBA(N);if(this.constructor.colorDiff(g,I)>h)break t}y++}if(m=this.getPixelColor(0,e),!l.south)t:for(var T=e-1;T>=v+1;T--){for(var W=i-y-1;W>=0;W--){var O=this.getPixelColor(W,T),A=this.constructor.intToRGBA(O);if(this.constructor.colorDiff(g,A)>h)break t}w++}if(m=this.getPixelColor(i,e),!l.west)t:for(var R=i-1;R>=0+y+1;R--){for(var z=e-1;z>=0+v;z--){var S=this.getPixelColor(R,z),H=this.constructor.intToRGBA(S);if(this.constructor.colorDiff(g,H)>h)break t}_++}if(_-=a,y-=a,v-=a,w-=a,o){var G=Math.min(y,_),L=Math.min(v,w);_=G,y=G,v=L,w=L}var k=i-((_=_>=0?_:0)+(y=y>=0?y:0)),j=e-((w=w>=0?w:0)+(v=v>=0?v:0));return(s?0!==y&&0!==v&&0!==_&&0!==w:0!==y||0!==v||0!==_||0!==w)&&this.crop(y,v,k,j),(0,n.isNodePattern)(t)&&t.call(this,null,this),this}}}};var r=a(e(73738)),n=e(65414);t.exports=i.default},53919:(t,i,e)=>{var a=e(2613);Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var r=e(65414),n=a(e(66453)),h=a(e(75415));i.default=function(){return{constants:{RESIZE_NEAREST_NEIGHBOR:"nearestNeighbor",RESIZE_BILINEAR:"bilinearInterpolation",RESIZE_BICUBIC:"bicubicInterpolation",RESIZE_HERMITE:"hermiteInterpolation",RESIZE_BEZIER:"bezierInterpolation"},class:{resize:function(t,i,e,a){if("number"!=typeof t||"number"!=typeof i)return r.throwError.call(this,"w and h must be numbers",a);if("function"==typeof e&&void 0===a&&(a=e,e=null),t===this.constructor.AUTO&&i===this.constructor.AUTO)return r.throwError.call(this,"w and h cannot both be set to auto",a);if(t===this.constructor.AUTO&&(t=this.bitmap.width*(i/this.bitmap.height)),i===this.constructor.AUTO&&(i=this.bitmap.height*(t/this.bitmap.width)),t<0||i<0)return r.throwError.call(this,"w and h must be positive numbers",a);if(t=Math.round(t),i=Math.round(i),"function"==typeof h.default[e]){var s={data:Buffer.alloc(t*i*4),width:t,height:i};h.default[e](this.bitmap,s),this.bitmap=s}else{var o=this;new n.default(this.bitmap.width,this.bitmap.height,t,i,!0,!0,(function(e){o.bitmap.data=Buffer.from(e),o.bitmap.width=t,o.bitmap.height=i})).resize(this.bitmap.data)}return(0,r.isNodePattern)(a)&&a.call(this,null,this),this}}}},t.exports=i.default},65242:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{cover:function(t,i,e,r,n){if("number"!=typeof t||"number"!=typeof i)return a.throwError.call(this,"w and h must be numbers",n);e&&"function"==typeof e&&void 0===n?(n=e,e=null,r=null):"function"==typeof r&&void 0===n&&(n=r,r=null);var h=7&(e=e||this.constructor.HORIZONTAL_ALIGN_CENTER|this.constructor.VERTICAL_ALIGN_MIDDLE),s=e>>3;if((0===h||h&h-1)&&(0===s||s&s-1))return a.throwError.call(this,"only use one flag per alignment direction",n);var o=h>>1,l=s>>1,u=t/i>this.bitmap.width/this.bitmap.height?t/this.bitmap.width:i/this.bitmap.height;return this.scale(u,r),this.crop((this.bitmap.width-t)/2*o,(this.bitmap.height-i)/2*l,t,i),(0,a.isNodePattern)(n)&&n.call(this,null,this),this}}},t.exports=i.default},66453:t=>{function i(t,i,e,a,r,n,h){this.widthOriginal=Math.abs(Math.floor(t)||0),this.heightOriginal=Math.abs(Math.floor(i)||0),this.targetWidth=Math.abs(Math.floor(e)||0),this.targetHeight=Math.abs(Math.floor(a)||0),this.colorChannels=r?4:3,this.interpolationPass=Boolean(n),this.resizeCallback="function"==typeof h?h:function(){},this.targetWidthMultipliedByChannels=this.targetWidth*this.colorChannels,this.originalWidthMultipliedByChannels=this.widthOriginal*this.colorChannels,this.originalHeightMultipliedByChannels=this.heightOriginal*this.colorChannels,this.widthPassResultSize=this.targetWidthMultipliedByChannels*this.heightOriginal,this.finalResultSize=this.targetWidthMultipliedByChannels*this.targetHeight,this.initialize()}i.prototype.initialize=function(){if(!(this.widthOriginal>0&&this.heightOriginal>0&&this.targetWidth>0&&this.targetHeight>0))throw new Error("Invalid settings specified for the resizer.");this.configurePasses()},i.prototype.configurePasses=function(){this.widthOriginal===this.targetWidth?this.resizeWidth=this.bypassResizer:(this.ratioWeightWidthPass=this.widthOriginal/this.targetWidth,this.ratioWeightWidthPass<1&&this.interpolationPass?(this.initializeFirstPassBuffers(!0),this.resizeWidth=4===this.colorChannels?this.resizeWidthInterpolatedRGBA:this.resizeWidthInterpolatedRGB):(this.initializeFirstPassBuffers(!1),this.resizeWidth=4===this.colorChannels?this.resizeWidthRGBA:this.resizeWidthRGB)),this.heightOriginal===this.targetHeight?this.resizeHeight=this.bypassResizer:(this.ratioWeightHeightPass=this.heightOriginal/this.targetHeight,this.ratioWeightHeightPass<1&&this.interpolationPass?(this.initializeSecondPassBuffers(!0),this.resizeHeight=this.resizeHeightInterpolated):(this.initializeSecondPassBuffers(!1),this.resizeHeight=4===this.colorChannels?this.resizeHeightRGBA:this.resizeHeightRGB))},i.prototype._resizeWidthInterpolatedRGBChannels=function(t,i){var e,a,r=i?4:3,n=this.ratioWeightWidthPass,h=this.widthBuffer,s=0,o=0,l=0,u=0,d=0;for(e=0;s<1/3;e+=r,s+=n)for(o=e,l=0;o<this.widthPassResultSize;l+=this.originalWidthMultipliedByChannels,o+=this.targetWidthMultipliedByChannels)h[o]=t[l],h[o+1]=t[l+1],h[o+2]=t[l+2],i&&(h[o+3]=t[l+3]);for(s-=1/3,a=this.widthOriginal-1;s<a;e+=r,s+=n)for(u=1-(d=s%1),o=e,l=Math.floor(s)*r;o<this.widthPassResultSize;l+=this.originalWidthMultipliedByChannels,o+=this.targetWidthMultipliedByChannels)h[o+0]=t[l+0]*u+t[l+r+0]*d,h[o+1]=t[l+1]*u+t[l+r+1]*d,h[o+2]=t[l+2]*u+t[l+r+2]*d,i&&(h[o+3]=t[l+3]*u+t[l+r+3]*d);for(a=this.originalWidthMultipliedByChannels-r;e<this.targetWidthMultipliedByChannels;e+=r)for(o=e,l=a;o<this.widthPassResultSize;l+=this.originalWidthMultipliedByChannels,o+=this.targetWidthMultipliedByChannels)h[o]=t[l],h[o+1]=t[l+1],h[o+2]=t[l+2],i&&(h[o+3]=t[l+3]);return h},i.prototype._resizeWidthRGBChannels=function(t,i){var e=i?4:3,a=this.ratioWeightWidthPass,r=1/a,n=this.originalWidthMultipliedByChannels-e+1,h=this.targetWidthMultipliedByChannels-e+1,s=this.outputWidthWorkBench,o=this.widthBuffer,l=this.outputWidthWorkBenchOpaquePixelsCount,u=0,d=0,f=0,p=0,c=0,b=0,m=0,g=1,v=0,y=0,w=0,_=0;do{for(c=0;c<this.originalHeightMultipliedByChannels;)s[c++]=0,s[c++]=0,s[c++]=0,i&&(s[c++]=0,l[c/e-1]=0);u=a;do{for(d=1+f-p,g=Math.min(u,d),c=0,b=f;c<this.originalHeightMultipliedByChannels;b+=n)v=t[b],y=t[++b],w=t[++b],_=i?t[++b]:255,s[c++]+=(_?v:0)*g,s[c++]+=(_?y:0)*g,s[c++]+=(_?w:0)*g,i&&(s[c++]+=_*g,l[c/e-1]+=_?g:0);if(!(u>=d)){p+=u;break}p=f+=e,u-=d}while(u>0&&f<this.originalWidthMultipliedByChannels);for(c=0,b=m;c<this.originalHeightMultipliedByChannels;b+=h)u=i?l[c/e]:1,g=i?u?1/u:0:r,o[b]=s[c++]*g,o[++b]=s[c++]*g,o[++b]=s[c++]*g,i&&(o[++b]=s[c++]*r);m+=e}while(m<this.targetWidthMultipliedByChannels);return o},i.prototype._resizeHeightRGBChannels=function(t,i){var e=this.ratioWeightHeightPass,a=1/e,r=this.outputHeightWorkBench,n=this.heightBuffer,h=this.outputHeightWorkBenchOpaquePixelsCount,s=0,o=0,l=0,u=0,d=0,f=0,p=0,c=1,b=0,m=0,g=0,v=0;do{for(d=0;d<this.targetWidthMultipliedByChannels;)r[d++]=0,r[d++]=0,r[d++]=0,i&&(r[d++]=0,h[d/4-1]=0);s=e;do{for(o=1+l-u,c=Math.min(s,o),p=l,d=0;d<this.targetWidthMultipliedByChannels;)b=t[p++],m=t[p++],g=t[p++],v=i?t[p++]:255,r[d++]+=(v?b:0)*c,r[d++]+=(v?m:0)*c,r[d++]+=(v?g:0)*c,i&&(r[d++]+=v*c,h[d/4-1]+=v?c:0);if(!(s>=o)){u+=s;break}u=l=p,s-=o}while(s>0&&l<this.widthPassResultSize);for(d=0;d<this.targetWidthMultipliedByChannels;)s=i?h[d/4]:1,c=i?s?1/s:0:a,n[f++]=Math.round(r[d++]*c),n[f++]=Math.round(r[d++]*c),n[f++]=Math.round(r[d++]*c),i&&(n[f++]=Math.round(r[d++]*a))}while(f<this.finalResultSize);return n},i.prototype.resizeWidthInterpolatedRGB=function(t){return this._resizeWidthInterpolatedRGBChannels(t,!1)},i.prototype.resizeWidthInterpolatedRGBA=function(t){return this._resizeWidthInterpolatedRGBChannels(t,!0)},i.prototype.resizeWidthRGB=function(t){return this._resizeWidthRGBChannels(t,!1)},i.prototype.resizeWidthRGBA=function(t){return this._resizeWidthRGBChannels(t,!0)},i.prototype.resizeHeightInterpolated=function(t){for(var i,e=this.ratioWeightHeightPass,a=this.heightBuffer,r=0,n=0,h=0,s=0,o=0,l=0,u=0;r<1/3;r+=e)for(h=0;h<this.targetWidthMultipliedByChannels;)a[n++]=Math.round(t[h++]);for(r-=1/3,i=this.heightOriginal-1;r<i;r+=e)for(l=1-(u=r%1),o=(s=Math.floor(r)*this.targetWidthMultipliedByChannels)+this.targetWidthMultipliedByChannels,h=0;h<this.targetWidthMultipliedByChannels;++h)a[n++]=Math.round(t[s++]*l+t[o++]*u);for(;n<this.finalResultSize;)for(h=0,s=i*this.targetWidthMultipliedByChannels;h<this.targetWidthMultipliedByChannels;++h)a[n++]=Math.round(t[s++]);return a},i.prototype.resizeHeightRGB=function(t){return this._resizeHeightRGBChannels(t,!1)},i.prototype.resizeHeightRGBA=function(t){return this._resizeHeightRGBChannels(t,!0)},i.prototype.resize=function(t){this.resizeCallback(this.resizeHeight(this.resizeWidth(t)))},i.prototype.bypassResizer=function(t){return t},i.prototype.initializeFirstPassBuffers=function(t){this.widthBuffer=this.generateFloatBuffer(this.widthPassResultSize),t||(this.outputWidthWorkBench=this.generateFloatBuffer(this.originalHeightMultipliedByChannels),this.colorChannels>3&&(this.outputWidthWorkBenchOpaquePixelsCount=this.generateFloat64Buffer(this.heightOriginal)))},i.prototype.initializeSecondPassBuffers=function(t){this.heightBuffer=this.generateUint8Buffer(this.finalResultSize),t||(this.outputHeightWorkBench=this.generateFloatBuffer(this.targetWidthMultipliedByChannels),this.colorChannels>3&&(this.outputHeightWorkBenchOpaquePixelsCount=this.generateFloat64Buffer(this.targetWidth)))},i.prototype.generateFloatBuffer=function(t){try{return new Float32Array(t)}catch(t){return[]}},i.prototype.generateFloat64Buffer=function(t){try{return new Float64Array(t)}catch(t){return[]}},i.prototype.generateUint8Buffer=function(t){try{return new Uint8Array(t)}catch(t){return[]}},t.exports=i},75415:t=>{t.exports={nearestNeighbor:function(t,i){for(var e=t.width,a=t.height,r=i.width,n=i.height,h=t.data,s=i.data,o=0;o<n;o++)for(var l=0;l<r;l++){var u=4*(o*r+l),d=4*(Math.floor(o*a/n)*e+Math.floor(l*e/r));s[u++]=h[d++],s[u++]=h[d++],s[u++]=h[d++],s[u++]=h[d++]}},bilinearInterpolation:function(t,i){for(var e=t.width,a=t.height,r=i.width,n=i.height,h=t.data,s=i.data,o=function(t,i,e,a,r){return i===a?e:Math.round((t-i)*r+(a-t)*e)},l=function(t,i,a,r,n,l,u,d){var f=4*(u*e+r)+i,p=4*(u*e+n)+i,c=o(a,r,h[f],n,h[p]);if(d===u)s[t+i]=c;else{p=4*(d*e+n)+i;var b=o(a,r,h[f=4*(d*e+r)+i],n,h[p]);s[t+i]=o(l,u,c,d,b)}},u=0;u<n;u++)for(var d=0;d<r;d++){var f=4*(u*r+d),p=d*e/r,c=Math.floor(p),b=Math.min(Math.ceil(p),e-1),m=u*a/n,g=Math.floor(m),v=Math.min(Math.ceil(m),a-1);l(f,0,p,c,b,m,g,v),l(f,1,p,c,b,m,g,v),l(f,2,p,c,b,m,g,v),l(f,3,p,c,b,m,g,v)}},_interpolate2D:function(t,i,e,a){for(var r=t.data,n=i.data,h=t.width,s=t.height,o=i.width,l=i.height,u=Math.max(1,Math.floor(h/o)),d=o*u,f=Math.max(1,Math.floor(s/l)),p=l*f,c=Buffer.alloc(d*s*4),b=0;b<s;b++)for(var m=0;m<d;m++)for(var g=m*(h-1)/d,v=Math.floor(g),y=g-v,w=4*(b*h+v),_=4*(b*d+m),M=0;M<4;M++){var B=w+M,P=v>0?r[B-4]:2*r[B]-r[B+4],E=r[B],C=r[B+4],x=v<h-2?r[B+8]:2*r[B+4]-r[B];c[_+M]=a(P,E,C,x,y)}for(var N=Buffer.alloc(d*p*4),I=0;I<p;I++)for(var T=0;T<d;T++)for(var W=I*(s-1)/p,O=Math.floor(W),A=W-O,R=4*(O*d+T),z=4*(I*d+T),S=0;S<4;S++){var H=R+S,G=O>0?c[H-4*d]:2*c[H]-c[H+4*d],L=c[H],k=c[H+4*d],j=O<s-2?c[H+8*d]:2*c[H+4*d]-c[H];N[z+S]=a(G,L,k,j,A)}var F=u*f;if(F>1)for(var U=0;U<l;U++)for(var D=0;D<o;D++){for(var Q=0,Z=0,K=0,V=0,q=0,J=0;J<f;J++)for(var X=U*f+J,Y=0;Y<u;Y++){var $=4*(X*d+(D*u+Y)),tt=N[$+3];tt&&(Q+=N[$],Z+=N[$+1],K+=N[$+2],q++),V+=tt}var it=4*(U*o+D);n[it]=q?Math.round(Q/q):0,n[it+1]=q?Math.round(Z/q):0,n[it+2]=q?Math.round(K/q):0,n[it+3]=Math.round(V/F)}else i.data=N},bicubicInterpolation:function(t,i,e){return this._interpolate2D(t,i,e,(function(t,i,e,a,r){var n=a-e-t+i,h=t-i-n,s=e-t,o=i;return Math.max(0,Math.min(255,n*(r*r*r)+h*(r*r)+s*r+o))}))},hermiteInterpolation:function(t,i,e){return this._interpolate2D(t,i,e,(function(t,i,e,a,r){var n=i,h=.5*(e-t),s=t-2.5*i+2*e-.5*a,o=.5*(a-t)+1.5*(i-e);return Math.max(0,Math.min(255,Math.round(((o*r+s)*r+h)*r+n)))}))},bezierInterpolation:function(t,i,e){return this._interpolate2D(t,i,e,(function(t,i,e,a,r){var n=1-r,h=i*n*n*n,s=3*(i+(e-t)/4)*n*n*r,o=3*(e-(a-i)/4)*n*r*r,l=e*r*r*r;return Math.max(0,Math.min(255,Math.round(h+s+o+l)))}))}}},75953:(t,i,e)=>{var a=e(2613);Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var r=a(e(41132)),n=e(99027),h=a(e(85330)),s=a(e(75148)),o=a(e(57931)),l=a(e(96358)),u=a(e(76391)),d=a(e(65242)),f=a(e(50669)),p=a(e(30058)),c=a(e(82567)),b=a(e(31050)),m=a(e(97178)),g=a(e(7234)),v=a(e(98235)),y=a(e(98115)),w=a(e(82786)),_=a(e(96238)),M=a(e(53919)),B=a(e(91210)),P=a(e(42273)),E=a(e(39219)),C=a(e(20174)),x=[h.default,s.default,o.default,l.default,u.default,d.default,f.default,p.default,c.default,b.default,m.default,g.default,v.default,y.default,w.default,_.default,M.default,B.default,P.default,E.default,C.default];i.default=function(t){var i=x.map((function(i){var e=i(t)||{};return e.class||e.constants||(e={class:e}),e}));return n.mergeDeep.apply(void 0,(0,r.default)(i))},t.exports=i.default},76391:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{contain:function(t,i,e,r,n){if("number"!=typeof t||"number"!=typeof i)return a.throwError.call(this,"w and h must be numbers",n);"string"==typeof e&&("function"==typeof r&&void 0===n&&(n=r),r=e,e=null),"function"==typeof e&&(void 0===n&&(n=e),r=null,e=null),"function"==typeof r&&void 0===n&&(n=r,r=null);var h=7&(e=e||this.constructor.HORIZONTAL_ALIGN_CENTER|this.constructor.VERTICAL_ALIGN_MIDDLE),s=e>>3;if((0===h||h&h-1)&&(0===s||s&s-1))return a.throwError.call(this,"only use one flag per alignment direction",n);var o=h>>1,l=s>>1,u=t/i>this.bitmap.width/this.bitmap.height?i/this.bitmap.height:t/this.bitmap.width,d=this.cloneQuiet().scale(u,r);return this.resize(t,i,r),this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,i,e){this.bitmap.data.writeUInt32BE(this._background,e)})),this.blit(d,(this.bitmap.width-d.bitmap.width)/2*o,(this.bitmap.height-d.bitmap.height)/2*l),(0,a.isNodePattern)(n)&&n.call(this,null,this),this}}},t.exports=i.default},82567:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);function r(t){var i=[1,9,3,11,13,5,15,7,4,12,2,10,16,8,14,6];return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,e,a){var r=i[((3&e)<<2)+t%4];this.bitmap.data[a]=Math.min(this.bitmap.data[a]+r,255),this.bitmap.data[a+1]=Math.min(this.bitmap.data[a+1]+r,255),this.bitmap.data[a+2]=Math.min(this.bitmap.data[a+2]+r,255)})),(0,a.isNodePattern)(t)&&t.call(this,null,this),this}i.default=function(){return{dither565:r,dither16:r}},t.exports=i.default},82786:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);function r(){var t={r:new Array(256).fill(0),g:new Array(256).fill(0),b:new Array(256).fill(0)};return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(i,e,a){t.r[this.bitmap.data[a+0]]++,t.g[this.bitmap.data[a+1]]++,t.b[this.bitmap.data[a+2]]++})),t}var n=function(t,i,e){return 255*(t-i)/(e-i)},h=function(t){return[t.findIndex((function(t){return t>0})),255-t.slice().reverse().findIndex((function(t){return t>0}))]};i.default=function(){return{normalize:function(t){var i=r.call(this),e={r:h(i.r),g:h(i.g),b:h(i.b)};return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,i,a){var r=this.bitmap.data[a+0],h=this.bitmap.data[a+1],s=this.bitmap.data[a+2];this.bitmap.data[a+0]=n(r,e.r[0],e.r[1]),this.bitmap.data[a+1]=n(h,e.g[0],e.g[1]),this.bitmap.data[a+2]=n(s,e.b[0],e.b[1])})),(0,a.isNodePattern)(t)&&t.call(this,null,this),this}}},t.exports=i.default},88952:(t,i,e)=>{var a=e(2613);Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var r=a(e(43693)),n=e(60114),h=e(65414),s="image/png";i.default=function(){return{mime:(0,r.default)({},s,["png"]),constants:{MIME_PNG:s,PNG_FILTER_AUTO:-1,PNG_FILTER_NONE:0,PNG_FILTER_SUB:1,PNG_FILTER_UP:2,PNG_FILTER_AVERAGE:3,PNG_FILTER_PATH:4},hasAlpha:(0,r.default)({},s,!0),decoders:(0,r.default)({},s,n.PNG.sync.read),encoders:(0,r.default)({},s,(function(t){var i=new n.PNG({width:t.bitmap.width,height:t.bitmap.height});return i.data=t.bitmap.data,n.PNG.sync.write(i,{width:t.bitmap.width,height:t.bitmap.height,deflateLevel:t._deflateLevel,deflateStrategy:t._deflateStrategy,filterType:t._filterType,colorType:"number"==typeof t._colorType?t._colorType:t._rgba?6:2,inputHasAlpha:t._rgba})})),class:{_deflateLevel:9,_deflateStrategy:3,_filterType:-1,_colorType:null,deflateLevel:function(t,i){return"number"!=typeof t?h.throwError.call(this,"l must be a number",i):t<0||t>9?h.throwError.call(this,"l must be a number 0 - 9",i):(this._deflateLevel=Math.round(t),(0,h.isNodePattern)(i)&&i.call(this,null,this),this)},deflateStrategy:function(t,i){return"number"!=typeof t?h.throwError.call(this,"s must be a number",i):t<0||t>3?h.throwError.call(this,"s must be a number 0 - 3",i):(this._deflateStrategy=Math.round(t),(0,h.isNodePattern)(i)&&i.call(this,null,this),this)},filterType:function(t,i){return"number"!=typeof t?h.throwError.call(this,"n must be a number",i):t<-1||t>4?h.throwError.call(this,"n must be -1 (auto) or a number 0 - 4",i):(this._filterType=Math.round(t),(0,h.isNodePattern)(i)&&i.call(this,null,this),this)},colorType:function(t,i){return"number"!=typeof t?h.throwError.call(this,"s must be a number",i):0!==t&&2!==t&&4!==t&&6!==t?h.throwError.call(this,"s must be a number 0, 2, 4, 6.",i):(this._colorType=Math.round(t),(0,h.isNodePattern)(i)&&i.call(this,null,this),this)}}}},t.exports=i.default},91210:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);function r(t,i){var e=(t%=360)*Math.PI/180,a=Math.cos(e),r=Math.sin(e),n=this.bitmap.width,h=this.bitmap.height;if(!0===i||"string"==typeof i){(n=Math.ceil(Math.abs(this.bitmap.width*a)+Math.abs(this.bitmap.height*r))+1)%2!=0&&n++,(h=Math.ceil(Math.abs(this.bitmap.width*r)+Math.abs(this.bitmap.height*a))+1)%2!=0&&h++;var s=this.cloneQuiet();this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,i,e){this.bitmap.data.writeUInt32BE(this._background,e)}));var o=Math.max(n,h,this.bitmap.width,this.bitmap.height);this.resize(o,o,i),this.blit(s,this.bitmap.width/2-s.bitmap.width/2,this.bitmap.height/2-s.bitmap.height/2)}var l=this.bitmap.width,u=this.bitmap.height,d=Buffer.alloc(this.bitmap.data.length);function f(t,i){return function(e,a){return{x:e+t,y:a+i}}}for(var p=f(-l/2,-u/2),c=f(l/2+.5,u/2+.5),b=1;b<=u;b++)for(var m=1;m<=l;m++){var g=p(m,b),v=c(a*g.x-r*g.y,a*g.y+r*g.x),y=l*(b-1)+m-1<<2;if(v.x>=0&&v.x<l&&v.y>=0&&v.y<u){var w=l*(0|v.y)+v.x<<2,_=this.bitmap.data.readUInt32BE(w);d.writeUInt32BE(_,y)}else d.writeUInt32BE(this._background,y)}if(this.bitmap.data=d,!0===i||"string"==typeof i){var M=l/2-n/2,B=u/2-h/2;this.crop(M,B,n,h)}}i.default=function(){return{rotate:function(t,i,e){return null==i&&(i=!0),"function"==typeof i&&void 0===e&&(e=i,i=!0),"number"!=typeof t?a.throwError.call(this,"deg must be a number",e):"boolean"!=typeof i&&"string"!=typeof i?a.throwError.call(this,"mode must be a boolean or a string",e):(r.call(this,t,i,e),(0,a.isNodePattern)(e)&&e.call(this,null,this),this)}}},t.exports=i.default},95212:(t,i)=>{function e(t,i){for(var e=0,a=0;a<i.length;a++)if(t.chars[i[a]]){var r=t.kernings[i[a]]&&t.kernings[i[a]][i[a+1]]?t.kernings[i[a]][i[a+1]]:0;e+=(t.chars[i[a]].xadvance||0)+r}return e}Object.defineProperty(i,"__esModule",{value:!0}),i.measureText=e,i.measureTextHeight=function(t,i,a){for(var r=i.split(" "),n="",h=t.common.lineHeight,s=0;s<r.length;s++){var o=n+r[s]+" ";e(t,o)>a&&s>0?(h+=t.common.lineHeight,n=r[s]+" "):n=o}return h}},96238:(t,i,e)=>{var a=e(2613);Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var r=a(e(73738)),n=a(e(41132)),h=a(e(16928)),s=a(e(92908)),o=e(65414),l=e(95212);function u(t,i,e,a,r){if(r.width>0&&r.height>0){var n=i.pages[r.page];t.blit(n,e+r.xoffset,a+r.yoffset,r.x,r.y,r.width,r.height)}return t}function d(t,i,e,a,r){for(var n=0;n<a.length;n++){var h;h=t.chars[a[n]]?a[n]:/\s/.test(a[n])?"":"?";var s=t.chars[h]||{},o=t.kernings[h];u(this,t,i,e,s||{}),i+=(o&&o[a[n+1]]?o[a[n+1]]:0)+(s.xadvance||r)}}var f=process.env.DIRNAME||"".concat(__dirname,"/../");i.default=function(){return{constants:{measureText:l.measureText,measureTextHeight:l.measureTextHeight,FONT_SANS_8_BLACK:h.default.join(f,"fonts/open-sans/open-sans-8-black/open-sans-8-black.fnt"),FONT_SANS_10_BLACK:h.default.join(f,"fonts/open-sans/open-sans-10-black/open-sans-10-black.fnt"),FONT_SANS_12_BLACK:h.default.join(f,"fonts/open-sans/open-sans-12-black/open-sans-12-black.fnt"),FONT_SANS_14_BLACK:h.default.join(f,"fonts/open-sans/open-sans-14-black/open-sans-14-black.fnt"),FONT_SANS_16_BLACK:h.default.join(f,"fonts/open-sans/open-sans-16-black/open-sans-16-black.fnt"),FONT_SANS_32_BLACK:h.default.join(f,"fonts/open-sans/open-sans-32-black/open-sans-32-black.fnt"),FONT_SANS_64_BLACK:h.default.join(f,"fonts/open-sans/open-sans-64-black/open-sans-64-black.fnt"),FONT_SANS_128_BLACK:h.default.join(f,"fonts/open-sans/open-sans-128-black/open-sans-128-black.fnt"),FONT_SANS_8_WHITE:h.default.join(f,"fonts/open-sans/open-sans-8-white/open-sans-8-white.fnt"),FONT_SANS_16_WHITE:h.default.join(f,"fonts/open-sans/open-sans-16-white/open-sans-16-white.fnt"),FONT_SANS_32_WHITE:h.default.join(f,"fonts/open-sans/open-sans-32-white/open-sans-32-white.fnt"),FONT_SANS_64_WHITE:h.default.join(f,"fonts/open-sans/open-sans-64-white/open-sans-64-white.fnt"),FONT_SANS_128_WHITE:h.default.join(f,"fonts/open-sans/open-sans-128-white/open-sans-128-white.fnt"),loadFont:function(t,i){var e=this;return"string"!=typeof t?o.throwError.call(this,"file must be a string",i):new Promise((function(a,r){i=i||function(t,i){t?r(t):a(i)},(0,s.default)(t,(function(a,r){var n={},s={};if(a)return o.throwError.call(e,a,i);for(var l=0;l<r.chars.length;l++)n[String.fromCharCode(r.chars[l].id)]=r.chars[l];for(var u=0;u<r.kernings.length;u++){var d=String.fromCharCode(r.kernings[u].first);s[d]=s[d]||{},s[d][String.fromCharCode(r.kernings[u].second)]=r.kernings[u].amount}(function(t,i,e){var a=e.map((function(e){return t.read(i+"/"+e)}));return Promise.all(a)})(e,h.default.dirname(t),r.pages).then((function(t){i(null,{chars:n,kernings:s,pages:t,common:r.common,info:r.info})}))}))}))}},class:{print:function(t,i,e,a,h,s,u){var f,p,c=this;if("function"==typeof h&&void 0===u&&(u=h,h=1/0),void 0===h&&(h=1/0),"function"==typeof s&&void 0===u&&(u=s,s=1/0),void 0===s&&(s=1/0),"object"!==(0,r.default)(t))return o.throwError.call(this,"font must be a Jimp loadFont",u);if("number"!=typeof i||"number"!=typeof e||"number"!=typeof h)return o.throwError.call(this,"x, y and maxWidth must be numbers",u);if("number"!=typeof h)return o.throwError.call(this,"maxWidth must be a number",u);if("number"!=typeof s)return o.throwError.call(this,"maxHeight must be a number",u);"object"===(0,r.default)(a)&&null!==a.text&&void 0!==a.text?(f=a.alignmentX||this.constructor.HORIZONTAL_ALIGN_LEFT,p=a.alignmentY||this.constructor.VERTICAL_ALIGN_TOP,a=a.text):(f=this.constructor.HORIZONTAL_ALIGN_LEFT,p=this.constructor.VERTICAL_ALIGN_TOP,a=a.toString()),s!==1/0&&p===this.constructor.VERTICAL_ALIGN_BOTTOM?e+=s-(0,l.measureTextHeight)(t,a,h):s!==1/0&&p===this.constructor.VERTICAL_ALIGN_MIDDLE&&(e+=s/2-(0,l.measureTextHeight)(t,a,h)/2);var b=Object.entries(t.chars)[0][1].xadvance,m=function(t,i,e){var a=i.split(" "),r=[],h=[],s=0;return a.forEach((function(i){var a=[].concat((0,n.default)(h),[i]).join(" "),o=(0,l.measureText)(t,a);o<=e?(o>s&&(s=o),h.push(i)):(r.push(h),h=[i])})),r.push(h),{lines:r,longestLine:s}}(t,a,h),g=m.lines,v=m.longestLine;return g.forEach((function(a){var r=a.join(" "),n=function(t,i,e,a,r){return r===t.HORIZONTAL_ALIGN_LEFT?0:r===t.HORIZONTAL_ALIGN_CENTER?(a-(0,l.measureText)(i,e))/2:a-(0,l.measureText)(i,e)}(c.constructor,t,r,h,f);d.call(c,t,i+n,e,r,b),e+=t.common.lineHeight})),(0,o.isNodePattern)(u)&&u.call(this,null,this,{x:i+v,y:e}),this}}}},t.exports=i.default},97178:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);function r(t,i,e){if("boolean"!=typeof t||"boolean"!=typeof i)return a.throwError.call(this,"horizontal and vertical must be Booleans",e);var r=Buffer.alloc(this.bitmap.data.length);return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(e,a,n){var h=t?this.bitmap.width-1-e:e,s=i?this.bitmap.height-1-a:a,o=this.bitmap.width*s+h<<2,l=this.bitmap.data.readUInt32BE(n);r.writeUInt32BE(l,o)})),this.bitmap.data=Buffer.from(r),(0,a.isNodePattern)(e)&&e.call(this,null,this),this}i.default=function(){return{flip:r,mirror:r}},t.exports=i.default},98115:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{mask:function(t){var i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,e=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,r=arguments.length>3?arguments[3]:void 0;if(!(t instanceof this.constructor))return a.throwError.call(this,"The source must be a Jimp image",r);if("number"!=typeof i||"number"!=typeof e)return a.throwError.call(this,"x and y must be numbers",r);i=Math.round(i),e=Math.round(e);var n=this.bitmap.width,h=this.bitmap.height,s=this;return t.scanQuiet(0,0,t.bitmap.width,t.bitmap.height,(function(t,a,r){var o=i+t,l=e+a;if(o>=0&&l>=0&&o<n&&l<h){var u=s.getPixelIndex(o,l),d=this.bitmap.data,f=(d[r+0]+d[r+1]+d[r+2])/3;s.bitmap.data[u+3]*=f/255}})),(0,a.isNodePattern)(r)&&r.call(this,null,this),this}}},t.exports=i.default},98235:(t,i,e)=>{Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=e(65414);i.default=function(){return{invert:function(t){return this.scanQuiet(0,0,this.bitmap.width,this.bitmap.height,(function(t,i,e){this.bitmap.data[e]=255-this.bitmap.data[e],this.bitmap.data[e+1]=255-this.bitmap.data[e+1],this.bitmap.data[e+2]=255-this.bitmap.data[e+2]})),(0,a.isNodePattern)(t)&&t.call(this,null,this),this}}},t.exports=i.default}}]);
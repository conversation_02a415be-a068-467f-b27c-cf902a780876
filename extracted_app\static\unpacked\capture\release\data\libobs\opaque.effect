#include "color.effect"

uniform float4x4 ViewProj;
uniform texture2d image;
uniform float multiplier;

sampler_state def_sampler {
	Filter   = Linear;
	AddressU = Clamp;
	AddressV = Clamp;
};

struct VertInOut {
	float4 pos : POSITION;
	float2 uv  : TEXCOORD0;
};

VertInOut VSDefault(VertInOut vert_in)
{
	VertInOut vert_out;
	vert_out.pos = mul(float4(vert_in.pos.xyz, 1.0), ViewProj);
	vert_out.uv  = vert_in.uv;
	return vert_out;
}

float4 PSDraw(VertInOut vert_in) : TARGET
{
	return float4(image.Sample(def_sampler, vert_in.uv).rgb, 1.0);
}

float4 PSDrawSrgbDecompress(VertInOut vert_in) : TARGET
{
	float3 rgb = image.Sample(def_sampler, vert_in.uv).rgb;
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, 1.0);
}

float4 PSDrawSrgbDecompressMultiply(VertInOut vert_in) : TARGET
{
	float3 rgb = image.Sample(def_sampler, vert_in.uv).rgb;
	rgb = srgb_nonlinear_to_linear(rgb);
	rgb *= multiplier;
	return float4(rgb, 1.0);
}

float4 PSDrawMultiply(VertInOut vert_in) : TARGET
{
	float3 rgb = image.Sample(def_sampler, vert_in.uv).rgb;
	rgb *= multiplier;
	return float4(rgb, 1.0);
}

float4 PSDrawTonemap(VertInOut vert_in) : TARGET
{
	float3 rgb = image.Sample(def_sampler, vert_in.uv).rgb;
	rgb = rec709_to_rec2020(rgb);
	rgb = reinhard(rgb);
	rgb = rec2020_to_rec709(rgb);
	return float4(rgb, 1.0);
}

float4 PSDrawMultiplyTonemap(VertInOut vert_in) : TARGET
{
	float3 rgb = image.Sample(def_sampler, vert_in.uv).rgb;
	rgb *= multiplier;
	rgb = rec709_to_rec2020(rgb);
	rgb = reinhard(rgb);
	rgb = rec2020_to_rec709(rgb);
	return float4(rgb, 1.0);
}

float4 PSDrawPQ(VertInOut vert_in) : TARGET
{
	float3 rgb = image.Sample(def_sampler, vert_in.uv).rgb;
	rgb = st2084_to_linear(rgb) * multiplier;
	rgb = rec2020_to_rec709(rgb);
	return float4(rgb, 1.0);
}

float4 PSDrawTonemapPQ(VertInOut vert_in) : TARGET
{
	float3 rgb = image.Sample(def_sampler, vert_in.uv).rgb;
	rgb = st2084_to_linear(rgb) * multiplier;
	rgb = reinhard(rgb);
	rgb = rec2020_to_rec709(rgb);
	return float4(rgb, 1.0);
}

technique Draw
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDraw(vert_in);
	}
}

technique DrawSrgbDecompress
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawSrgbDecompress(vert_in);
	}
}

technique DrawSrgbDecompressMultiply
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawSrgbDecompressMultiply(vert_in);
	}
}

technique DrawMultiply
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawMultiply(vert_in);
	}
}

technique DrawTonemap
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawTonemap(vert_in);
	}
}

technique DrawMultiplyTonemap
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawMultiplyTonemap(vert_in);
	}
}

technique DrawPQ
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawPQ(vert_in);
	}
}

technique DrawTonemapPQ
{
	pass
	{
		vertex_shader = VSDefault(vert_in);
		pixel_shader  = PSDrawTonemapPQ(vert_in);
	}
}

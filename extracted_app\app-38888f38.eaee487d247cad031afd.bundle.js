"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[211],{30770:(o,i,e)=>{e.d(i,{f:()=>d});var t=e(15215),n=e("aurelia-framework"),r=e(62914),a=e(78576),s=e("dialogs/fullscreen-webview-dialog"),l=e(38110);let d=class{#o;#i;#e;constructor(o,i,e){this.#o=o,this.#i=i,this.#e=e}async open(o){const i={frequency:o?.selectedPlan?o.selectedPlan?.recurring?.frequency:o?.frequency,nonInteraction:o?.nonInteraction,trigger:o?.trigger,discountCode:o?.discountCode};this.#i.event("pro_popup",{popupId:"pro",...i});const e=await this.#o.open({route:"checkout-flow",params:i,styles:{background:(0,l.IX)("secondary-background")}});e&&!e.wasCancelled&&this.#e.check(),this.#i.event("pro_popup_close",{})}};d=(0,t.Cg)([(0,n.autoinject)(),(0,t.Sn)("design:paramtypes",[s.FullscreenWebviewDialogService,r.j0,a.G])],d)},"dialogs/pro-onboarding-dialog":(o,i,e)=>{e.r(i),e.d(i,{ProOnboardingDialog:()=>m,ProOnboardingDialogService:()=>u});var t=e(15215),n=e("aurelia-dialog"),r=e("aurelia-framework"),a=e(78268),s=e(62914),l=e(98300),d=e(85805),g=e(19072),c=e(54995),p=e(70236),b=e(38777),h=e(23218);let m=class{#t;#n;#i;#r;#a;constructor(o,i,e,t,n,r){this.controller=o,this.remote=i,this.remoteHasConnected=!1,this.saveCheatsLinkClicked=!1,this.boostsLinkClicked=!1,this.#a=null,this.#n=e,this.#i=t,this.#r=n,this.host=r}async activate(o){this.config=o,this.#i.event("pro_onboarding_open",{trigger:this.config.trigger},s.Io)}attached(){this.remoteHasConnected=this.remote.status===d.t.Connected,this.#t=(new b.Vd).push(this.remote.onStatusChanged((o=>{o===d.t.Connected&&(this.remoteHasConnected=!0)}))),this.#s(),this.#a=setInterval((()=>this.#s()),5e3)}detached(){this.#t?.dispose(),this.#t=null,clearInterval(this.#a),this.#a=null}deactivate(o){const i=o.wasCancelled&&!o.output?"escape":o.output;this.#i.event("pro_onboarding_dismiss",{method:i,overlayStatus:this.overlayStatus,remoteHasConnected:this.remoteHasConnected,discordIsConnected:this.discordIsConnected,saveModsLinkClicked:this.saveCheatsLinkClicked,boostsLinkClicked:this.boostsLinkClicked,theme:this.theme},s.Io)}async#s(){this.overlayStatus=await this.#n.refreshFeatureStatus()}get discordIsConnected(){return(0,p.Lt)(this.account.flags,256)}handleDiscordConnectClick(){return this.#r.watchFlag(256,30),this.#i.event("discord_connect_click",{trigger:"pro_onboarding_dialog"},s.Io),!0}handleDiscordServerClick(){this.#i.event("discord_server_click",{trigger:"pro_onboarding_dialog"},s.Io)}get showDiscordStep(){return!this.host.info.locale.endsWith("CN")}};(0,t.Cg)([(0,r.computedFrom)("account.flags"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],m.prototype,"discordIsConnected",null),(0,t.Cg)([(0,r.computedFrom)("host.info.locale"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],m.prototype,"showDiscordStep",null),m=(0,t.Cg)([(0,r.autoinject)(),(0,c.m6)({selectors:{account:(0,c.$t)((o=>o.account)),theme:(0,c.$t)((o=>o.settings?.theme))}}),(0,t.Sn)("design:paramtypes",[n.DialogController,d.e,l.Hy,s.j0,a.s,g.s])],m);let u=class extends h.D{constructor(){super(...arguments),this.viewModelClass="dialogs/pro-onboarding-dialog"}};u=(0,t.Cg)([(0,r.autoinject)()],u)},"dialogs/pro-onboarding-dialog.html":(o,i,e)=>{e.r(i),e.d(i,{default:()=>b});var t=e(14385),n=e.n(t),r=new URL(e(2761),e.b),a=new URL(e(38032),e.b),s=new URL(e(33806),e.b),l=new URL(e(30904),e.b),d=new URL(e(60605),e.b),g=new URL(e(55318),e.b),c=new URL(e(26232),e.b),p=new URL(e(68118),e.b);const b='<template> <require from="./pro-onboarding-dialog.scss"></require> <require from="./resources/elements/remote-platforms.html"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/resources/elements/feature-number"></require> <require from="../resources/elements/remote-code"></require> <require from="../resources/elements/remote-qr-code"></require> <require from="../settings/resources/elements/theme-selector"></require> <ux-dialog class="pro-onboarding-dialog fullscreen-dialog"> <div class="scroll-wrapper" ref="scrollWrapperEl"> <div class="scroll-inner"> <header class="top"> <close-button click.delegate="controller.close(false, \'close\')" tabindex="0"></close-button> <img src="'+n()(r)+'"> <template if.bind="config.mode === \'post-pro-upgrade\'"> <h1> <span>${\'pro_onboarding_dialog.congrats\' | i18n}</span><br> <span innerhtml.bind="\'pro_onboarding_dialog.you_are_now_pro\' | i18n | markdown"></span> </h1> <h4 innerhtml.bind="\'pro_onboarding_dialog.thanks_for_joining\' | i18n | markdown"></h4> </template> <template else> <h1> <span innerhtml.bind="\'pro_onboarding_dialog.your_exclusive_pro_features\' | i18n | markdown"></span> </h1> <h4 innerhtml.bind="\'pro_onboarding_dialog.were_happy_to_have_you\' | i18n | markdown"></h4> </template> </header> <div class="step remote"> <feature-number>1</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.connect_wemod_remote\' | i18n | markdown"></h2> <template if.bind="remoteHasConnected"> <div class="success"> <span class="pulse"></span> <span class="label">${\'pro_onboarding_dialog.connected\' | i18n}</span> </div> </template> <template else> <div class="block app"> <remote-qr-code></remote-qr-code> <p class="stretch medium" innerhtml.bind="\'pro_onboarding_dialog.get_our_mobile_app\' | i18n | markdown"></p> <remote-platforms highlight="true"></remote-platforms> </div> <div class="block code"> <div> <p class="medium arrow" innerhtml.bind="\'pro_onboarding_dialog.then_enter_this_pin\' | i18n | markdown"></p> <remote-code></remote-code> </div> </div> </template> </div> <div class="step-image"> <img src="'+n()(a)+'"> </div> </div> <div class="step save-mods"> <feature-number>2</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.enable_save_mods\' | i18n | markdown"></h2> <p class="instructions" innerhtml.bind="\'pro_onboarding_dialog.save_mods_explanation\' | i18n | markdown"></p> <a class="button" href="https://wemod.gg/support-save-mods" target="_blank" click.delegate="saveCheatsLinkClicked = true">${\'pro_onboarding_dialog.how_it_works\' | i18n}</a> </div> <div class="step-image"> <div class="save-mods-image-wrapper"> <span class="toggle"> <img src="'+n()(s)+"\"> <span>${'pro_onboarding_dialog.save_mods' | i18n}</span> <img src=\""+n()(l)+'"> </span> <img class="graphic" src="'+n()(d)+'"> </div> </div> </div> <div class="step boosts"> <feature-number>3</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.influence_game_priority\' | i18n | markdown"></h2> <p class="instructions" innerhtml.bind="\'pro_onboarding_dialog.boosts_explanation\' | i18n | markdown"></p> <a class="button" href="https://wemod.gg/support-queue" target="_blank" click.delegate="boostsLinkClicked = true">${\'pro_onboarding_dialog.how_it_works\' | i18n}</a> </div> <div class="step-image"> <img src="'+n()(g)+'"> </div> </div> <div class="step themes"> <feature-number>4</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.choose_your_theme\' | i18n | markdown"></h2> <p class="instructions" innerhtml.bind="\'pro_onboarding_dialog.themes_explanation\' | i18n | markdown"></p> <theme-selector source="pro_onboarding_dialog"></theme-selector> </div> <div class="step-image"> <img src="'+n()(c)+'"> </div> </div> <div class="step discord" if.bind="showDiscordStep"> <feature-number>5</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.receive_special_role_in_discord\' | i18n | markdown"></h2> <p class="instructions" innerhtml.bind="\'pro_onboarding_dialog.first_your_account_must_be_linked_to_discord\' | i18n | markdown"></p> <div> <template if.bind="discordIsConnected"> <div class="success"> <span class="pulse"></span> <span class="label">${\'pro_onboarding_dialog.connected\' | i18n}</span> </div> </template> <template else> <a class="button accent" href="website://account/connections/discord#auth" target="_blank" click.delegate="handleDiscordConnectClick()">${\'pro_onboarding_dialog.connect_discord\' | i18n}</a> </template> </div> <a class="discord" href="website://discord" target="_blank" click.delegate="handleDiscordServerClick()">${\'pro_onboarding_dialog.join_server\' | i18n}</a> </div> <div class="step-image"> <img src="'+n()(p)+'"> </div> </div> <footer class="bottom"> <h2 class="accent" innerhtml.bind="`pro_onboarding_dialog.${config.mode === \'post-pro-upgrade\' ? \'plus_now_that_youre_pro\' : \'plus_with_your_pro_subscription\'}` | i18n | markdown"></h2> <p innerhtml.bind="\'pro_onboarding_dialog.you_support_creators_and_wemod_development\' | i18n | markdown"></p> <div class="buttons"> <button class="button secondary" if.bind="config.mode === \'post-pro-upgrade\'" click.delegate="controller.close(false, \'skip\')"> ${\'pro_onboarding_dialog.skip_for_now\' | i18n} </button> <button class="button highlight" click.delegate="controller.close(true, \'ok\')" innerhtml.bind="`pro_onboarding_dialog.${config.mode === \'post-pro-upgrade\' ? \'i_am_all_set_up_for_pro\' : \'i_understand_the_pro_features\' }` | i18n | markdown"></button> </div> </footer> </div> </div> </ux-dialog> </template> '},"dialogs/pro-onboarding-dialog.scss":(o,i,e)=>{e.r(i),e.d(i,{default:()=>y});var t=e(31601),n=e.n(t),r=e(76314),a=e.n(r),s=e(4417),l=e.n(s),d=new URL(e(96369),e.b),g=new URL(e(76048),e.b),c=new URL(e(81206),e.b),p=new URL(e(79948),e.b),b=new URL(e(58821),e.b),h=new URL(e(11394),e.b),m=a()(n()),u=l()(d),f=l()(g),x=l()(c),v=l()(p),w=l()(b),k=l()(h);m.push([o.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@keyframes -pulse{0%{transform:scale(0.3)}50%{transform:scale(0.5)}100%{transform:scale(0.3)}}@keyframes pulse-2{0%{transform:scale(1);opacity:0}70%{transform:scale(1.5);opacity:1}100%{transform:scale(2);opacity:0}}.billing-settings section{padding:20px;border-radius:10px;border:1px solid rgba(255,255,255,.04);transition:opacity .15s}.billing-settings section.filled{background:rgba(255,255,255,.04);border:none;padding-top:18px}.billing-settings section.filled h5{color:rgba(255,255,255,.6)}.billing-settings section.filled h5.plan-header{margin-bottom:16px;color:#fff}.billing-settings section+section{margin-top:20px}.billing-settings section.loading{opacity:.4}.billing-settings section.loading,.billing-settings section.loading *{pointer-events:none}.billing-settings section.layout{display:flex}.billing-settings section.layout>*:first-child{flex:1 1 auto}.billing-settings section.layout>*:last-child{flex:0 0 auto}.billing-settings .details{display:flex;align-items:center}.billing-settings .details .meta{display:flex;align-items:center;color:rgba(255,255,255,.5)}.billing-settings .details .meta>*+*{margin-left:7px}.billing-settings .details .meta,.billing-settings .details .meta strong,.billing-settings .details .meta em{font-size:13px;line-height:20px;font-weight:500}.billing-settings .details .meta em{font-style:normal;color:rgba(255,255,255,.8)}.billing-settings .details .card-type{width:34px;height:22px;border-radius:4px;background-position:center;background-repeat:no-repeat;background-image:url(${u});display:inline-block;flex:0 0 auto}.billing-settings .details .meta.payment{display:flex;flex-direction:column;align-items:flex-start;justify-content:center}.billing-settings .details .meta.payment>*+*{margin-left:0}.billing-settings .details .meta.payment.canceled{border-left:1px solid rgba(255,255,255,.1);padding-left:20px;flex-direction:row;align-items:center;gap:10px}.billing-settings .details .meta.warning,.billing-settings .details .meta.warning *{color:var(--color--accent-yellow)}.billing-settings .details .row-actions{display:flex;align-items:center}.billing-settings .details .row-actions .links{display:flex;align-items:center;margin-left:20px;padding-left:20px;border-left:1px solid rgba(255,255,255,.1)}.billing-settings .details .row-actions .links .remove{color:rgba(var(--color--alert--rgb), 0.8)}.billing-settings .details .row-actions .links .remove:hover{color:#fff}.billing-settings .link{font-size:13px;line-height:20px;font-weight:700;background:rgba(0,0,0,0);border:0;color:rgba(255,255,255,.25);padding:0}.billing-settings .link:hover{color:#fff}.billing-settings .resume-btn{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700;padding:20px 16px}.billing-settings .resume-btn,.billing-settings .resume-btn *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn{border:1px solid #fff}}.billing-settings .resume-btn>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .resume-btn>*:first-child{padding-left:0}.billing-settings .resume-btn>*:last-child{padding-right:0}.billing-settings .resume-btn svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn svg *{fill:CanvasText}}.billing-settings .resume-btn svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn svg{opacity:1}}.billing-settings .resume-btn img{height:50%}.billing-settings .resume-btn:disabled{opacity:.3}.billing-settings .resume-btn:disabled,.billing-settings .resume-btn:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .resume-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .resume-btn:not(:disabled):hover svg{opacity:1}}.billing-settings .resume-btn:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.billing-settings .resume-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.billing-settings .resume-btn:not(:disabled):active{background-color:var(--theme--highlight)}.billing-settings h5{font-weight:600;font-size:16px;line-height:25px;font-weight:700;color:#fff;margin:0 0 11px}.billing-settings h5 em{font-style:normal;color:var(--theme--highlight)}.billing-settings h5 strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;margin-right:4px;vertical-align:middle}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings h5 strong{border:1px solid #fff}}.billing-settings h5+.details{margin-top:2px}.billing-settings h5.warning{color:var(--color--accent-yellow) !important;display:inline-flex;align-items:center}.billing-settings h5.warning:before{display:inline-block;content:"";width:15px;height:15px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${f}) 6px 3px no-repeat}.billing-settings h5.warning:before{margin-right:8px}.billing-settings .canceled-details{display:flex;align-items:center;border-left:1px solid rgba(255,255,255,.1);padding-left:20px;margin-left:20px}.billing-settings .canceled-details .meta{display:flex;align-items:center;color:rgba(255,255,255,.5)}.billing-settings .canceled-details .meta>*+*{margin-left:7px}.billing-settings .canceled-details .meta,.billing-settings .canceled-details .meta strong,.billing-settings .canceled-details .meta em{font-size:13px;line-height:20px;font-weight:500}.billing-settings .canceled-details .meta em{font-style:normal;color:rgba(255,255,255,.8)}.billing-settings .canceled-details .card-type{width:34px;height:22px;border-radius:4px;background-position:center;background-repeat:no-repeat;background-image:url(${u});display:inline-block;flex:0 0 auto}.billing-settings .main-actions{margin:17px 0 0 0}.billing-settings .main-actions>*+*{margin-left:15px}.billing-settings .main-actions .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent);font-weight:800;--cta--padding: 18px;--cta--height: 40px;--cta--hover--border-width: 2px;font-size:18px}.billing-settings .main-actions .button,.billing-settings .main-actions .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button{border:1px solid #fff}}.billing-settings .main-actions .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .main-actions .button>*:first-child{padding-left:0}.billing-settings .main-actions .button>*:last-child{padding-right:0}.billing-settings .main-actions .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button svg *{fill:CanvasText}}.billing-settings .main-actions .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button svg{opacity:1}}.billing-settings .main-actions .button img{height:50%}.billing-settings .main-actions .button:disabled{opacity:.3}.billing-settings .main-actions .button:disabled,.billing-settings .main-actions .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .main-actions .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .main-actions .button:not(:disabled):hover svg{opacity:1}}.billing-settings .main-actions .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.billing-settings .main-actions .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}.billing-settings .main-actions .button:not(:disabled):active{background-color:var(--color--accent)}.billing-settings .main-actions .button:not(:disabled):active{--cta__icon--color: #000;color:#000}.billing-settings .main-actions .button.main{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.billing-settings .main-actions .button.main:hover{filter:brightness(1.1)}}.billing-settings .main-actions .button.secondary{background-color:rgba(255,255,255,.6);box-shadow:none;color:var(--theme--background)}.billing-settings .main-actions .button.secondary svg{opacity:1}.billing-settings .main-actions .button.secondary svg *{--cta__icon--color: var(--theme--background)}@media(hover: hover){.billing-settings .main-actions .button.secondary:not(:disabled):hover{background-color:var(--theme--highlight)}}.billing-settings .main-actions .button.accent{background-color:rgba(var(--color--accent--rgb), 0.08) !important;color:var(--color--accent) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.billing-settings .main-actions .button.accent:hover{filter:brightness(1.1)}}.billing-settings .main-actions .button.small{font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}.billing-settings .main-actions .divider{border-left:1px solid rgba(255,255,255,.1);height:28px}.billing-settings .main-actions .promo{font-size:13px;line-height:20px;color:var(--color--accent);display:inline-flex;align-items:center}.billing-settings .main-actions .promo i{margin-left:7px}.billing-settings .main-actions .promo i svg *{fill:var(--color--accent)}.billing-settings .alert{margin-top:8px}.billing-settings .info{font-size:14px;line-height:21px;font-weight:500;line-height:19px;color:rgba(255,255,255,.5)}.billing-settings .disclaimer{font-size:13px;line-height:20px;font-weight:500;color:rgba(255,255,255,.3);display:flex;align-items:center;margin:20px 0 0 0}.billing-settings .disclaimer .icon{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;--cta--height: 15px;--cta--hover--border-width: 1px;min-width:var(--cta--height);width:var(--cta--height);border-radius:50%;justify-content:center;align-items:center;position:relative;background:rgba(255,255,255,.1);box-shadow:none !important;pointer-events:none;margin-right:7px}.billing-settings .disclaimer .icon,.billing-settings .disclaimer .icon *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon{border:1px solid #fff}}.billing-settings .disclaimer .icon>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .disclaimer .icon>*:first-child{padding-left:0}.billing-settings .disclaimer .icon>*:last-child{padding-right:0}.billing-settings .disclaimer .icon svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon svg *{fill:CanvasText}}.billing-settings .disclaimer .icon svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon svg{opacity:1}}.billing-settings .disclaimer .icon img{height:50%}.billing-settings .disclaimer .icon:disabled{opacity:.3}.billing-settings .disclaimer .icon:disabled,.billing-settings .disclaimer .icon:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .disclaimer .icon:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .disclaimer .icon:not(:disabled):hover svg{opacity:1}}.billing-settings .disclaimer .icon:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.billing-settings .disclaimer .icon,.billing-settings .disclaimer .icon>*{padding:0 !important}.billing-settings .disclaimer .icon:active{background-color:rgba(0,0,0,0) !important;color:rgba(255,255,255,.8) !important}.billing-settings .disclaimer .icon svg{opacity:.5}.billing-settings .disclaimer a{color:rgba(255,255,255,.5)}.billing-settings .disclaimer a:hover{color:#fff}.billing-settings .graphic{margin:0 0 -20px 0;justify-self:flex-end}.billing-settings .subscribed{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;--checkbox--checked-color: var(--color--accent);border:1px solid rgba(255,255,255,.15);border-radius:100px;padding:8px 15px;transition:background-color .15s,border-color .15s;background-color:rgba(var(--color--accent--rgb), 0.08);cursor:initial;border-color:rgba(0,0,0,0);--checkbox__label--color: var(--checkbox--checked-color);margin-right:15px}.billing-settings .subscribed,.billing-settings .subscribed *{cursor:pointer}.billing-settings .subscribed,.billing-settings .subscribed *{cursor:pointer}.billing-settings .subscribed>*:first-child{margin-right:9px}.billing-settings .subscribed:hover>*{--checkbox__label--color: #fff}.billing-settings .subscribed,.billing-settings .subscribed *:not(info-tooltip){cursor:default !important}.billing-settings .subscribed .checkbox{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;width:15px;height:15px;background:rgba(0,0,0,0);border-color:rgba(255,255,255,.25);border-color:rgba(0,0,0,0)}.billing-settings .subscribed .checkbox,.billing-settings .subscribed .checkbox *{cursor:pointer}.billing-settings .subscribed .checkbox:checked:before{opacity:1}.billing-settings .subscribed .checkbox:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${x});mask:url(${x})}.billing-settings .subscribed .checkbox:before{left:1px;top:0;width:15px;height:11px;transform:scale(1)}.billing-settings .subscribed .checkbox:before{opacity:1}.billing-settings .subscribed>.icon{margin-right:9px}.billing-settings .subscribed>.icon svg *{fill:var(--color--accent)}.billing-settings .subscribed .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color);transition:color .15s;color:var(--color--accent) !important}.billing-settings .subscribed .label,.billing-settings .subscribed .label *{cursor:pointer}.billing-settings .subscribed info-tooltip{margin-left:10px}.billing-settings .subscribed info-tooltip,.billing-settings .subscribed info-tooltip *{cursor:pointer}.billing-settings .subscribed.warning{background:rgba(var(--color--accent-yellow--rgb), 0.08)}.billing-settings .subscribed.warning .label{color:var(--color--accent-yellow) !important}.billing-settings .subscribed.warning .checkbox{display:none}.billing-settings .subscribed.warning:before{display:inline-block;content:"";width:19px;height:19px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${v}) center no-repeat;margin-right:11px}.billing-settings .subscribed.canceled{background:rgba(var(--theme--highlight--rgb), 0.08)}.billing-settings .subscribed.canceled .checkbox:before{background:var(--theme--highlight) !important}.billing-settings .subscribed.canceled .label{color:var(--theme--highlight) !important}.billing-settings .wemod-tag{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:normal;text-transform:capitalize;white-space:nowrap;padding:1px 6px}.billing-settings hr{border:0;border-top:1px solid rgba(255,255,255,.1);margin:20px 0 16px}.pro-onboarding-dialog .scroll-wrapper{width:100vw;height:100vh;overflow-x:hidden;overflow-y:auto}.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.pro-onboarding-dialog .scroll-inner{position:relative;padding:0 80px}.pro-onboarding-dialog .top{text-align:center;padding:65px 25px 8px}.pro-onboarding-dialog .top img{margin-bottom:8px}.pro-onboarding-dialog .top h1{font-weight:700;font-size:52px;line-height:55px;margin:0 0 25px;color:#fff}.pro-onboarding-dialog .top h1 strong{color:var(--color--accent)}.pro-onboarding-dialog .top h4{font-size:16px;line-height:24px;font-weight:500;margin:0;color:rgba(255,255,255,.6)}.pro-onboarding-dialog .top h4 strong{font-weight:600;color:#fff}.pro-onboarding-dialog .step{background:linear-gradient(180deg, transparent, var(--theme--background));border-radius:20px;position:relative;padding:27px 34px;max-width:1210px;margin:0 auto 40px;display:flex}.pro-onboarding-dialog .step feature-number{position:absolute;left:-14px;top:-6px}.pro-onboarding-dialog .step .step-content{flex:1;padding-right:20px}.pro-onboarding-dialog .step .step-content h2{font-size:24px;line-height:32px;font-weight:700;color:#fff;margin:0 0 13px}.pro-onboarding-dialog .step .step-content h2 strong{font-weight:700;color:var(--theme--highlight)}.pro-onboarding-dialog .step .step-image{flex:1;margin-bottom:-27px}.pro-onboarding-dialog .step.remote .step-image{display:flex;align-items:flex-end;justify-content:center}.pro-onboarding-dialog .step.remote .step-image img{width:100%;max-width:380px}.pro-onboarding-dialog .step.remote .block{padding:20px;border-radius:10px;background:var(--theme--secondary-background);max-width:535px}.pro-onboarding-dialog .step.remote .block+.block{margin-top:20px}.pro-onboarding-dialog .step.remote .app{display:flex;align-items:flex-start}.pro-onboarding-dialog .step.remote .app>*{flex:0 0 auto}.pro-onboarding-dialog .step.remote .app>*+*{margin-left:18px}.pro-onboarding-dialog .step.remote .app>*.stretch{flex:1 1 auto}.pro-onboarding-dialog .step.remote .code{text-align:center}.pro-onboarding-dialog .step.remote .code p{margin:0 0 14px}.pro-onboarding-dialog .step.remote remote-qr-code{width:80px;height:80px}.pro-onboarding-dialog .step.remote remote-platforms{width:150px}@media(max-width: 1080px){.pro-onboarding-dialog .step.remote .app{flex-wrap:wrap}.pro-onboarding-dialog .step.remote .app remote-qr-code{order:2}.pro-onboarding-dialog .step.remote .app remote-platforms{order:3;width:calc(100% - 100px)}.pro-onboarding-dialog .step.remote .app p{order:1;margin:0 0 20px}}.pro-onboarding-dialog .step.save-mods .instructions{max-width:504px}.pro-onboarding-dialog .step.save-mods .button{margin-top:24px}.pro-onboarding-dialog .step.save-mods .step-image{display:flex;justify-content:flex-end;align-items:flex-start;margin:-27px -34px -27px 0}.pro-onboarding-dialog .step.save-mods .step-image .save-mods-image-wrapper{position:relative;height:333px;overflow:hidden}.pro-onboarding-dialog .step.save-mods .step-image .save-mods-image-wrapper .toggle{display:inline-flex;align-items:center;position:absolute;right:201px;top:30px;height:47px;border:1px solid rgba(255,255,255,.15);padding:11px 20px 11px 11px;border-radius:50px;font-size:15px;color:rgba(255,255,255,.6)}.pro-onboarding-dialog .step.save-mods .step-image .save-mods-image-wrapper .toggle>*+*{margin-left:10px}.pro-onboarding-dialog .step.save-mods .step-image .graphic{width:491px}.pro-onboarding-dialog .step.boosts .button{margin-top:24px}.pro-onboarding-dialog .step.boosts .step-content{max-width:524px}.pro-onboarding-dialog .step.boosts .step-image{display:flex;justify-content:flex-end;align-items:flex-end;margin-right:-34px;margin-top:-27px}.pro-onboarding-dialog .step.boosts .step-image img{width:100%;max-width:520px}.pro-onboarding-dialog .step.themes .step-content{padding-right:100px}.pro-onboarding-dialog .step.themes .step-image{display:flex;justify-content:flex-end;align-items:flex-end;margin-right:-34px;margin-top:-27px;border-top-left-radius:20px;border-bottom-right-radius:20px;overflow:hidden}.pro-onboarding-dialog .step.themes .step-image img{width:100%;max-width:526px}.pro-onboarding-dialog .step.themes theme-selector{display:block;margin-top:30px}.pro-onboarding-dialog .step.discord .instructions{max-width:504px;margin-bottom:24px}.pro-onboarding-dialog .step.discord .step-image{display:flex;align-items:center;justify-content:center;margin-bottom:0}.pro-onboarding-dialog .step.discord .step-image img{width:100%;max-width:250px}.pro-onboarding-dialog .step.discord a.discord{font-size:13px;line-height:20px;font-weight:500;padding:4px 10px 4px 29px;color:rgba(255,255,255,.8);background:var(--theme--secondary-background) url(${w}) 9px center no-repeat;border-radius:4px;display:inline-block;margin-top:20px}.pro-onboarding-dialog .step.discord a.discord:hover{color:#fff}.pro-onboarding-dialog .bottom{width:100%;max-width:795px;margin:0 auto 70px;background:linear-gradient(247.39deg, rgba(255, 255, 255, 0.15) 0%, rgba(var(--color--brand-blue--rgb), 0.06) 70.59%);border-radius:20px;padding:27px 34px;text-align:center}.pro-onboarding-dialog .bottom .buttons{display:flex;align-items:center;justify-content:center;margin:9px 0 0 -15px;flex-wrap:wrap}.pro-onboarding-dialog .bottom .buttons .button{margin:15px 0 0 15px}.pro-onboarding-dialog h2{font-size:24px;line-height:32px;font-weight:700;color:#fff;margin:0 0 13px}.pro-onboarding-dialog h2 strong{font-weight:700;color:var(--theme--highlight)}.pro-onboarding-dialog h2.accent strong{color:var(--color--accent)}.pro-onboarding-dialog p{font-size:16px;line-height:24px;font-weight:600;color:rgba(255,255,255,.5)}.pro-onboarding-dialog p strong{font-weight:600;color:#fff}.pro-onboarding-dialog p em{font-weight:600;color:var(--theme--highlight)}.pro-onboarding-dialog p.medium,.pro-onboarding-dialog p.medium *{font-weight:500}.pro-onboarding-dialog p.arrow:after{content:"";display:inline-block;position:relative;left:5px;top:10px;width:15px;height:15px;background:url(${k}) top left no-repeat}.pro-onboarding-dialog p.warning{display:flex;align-items:center;margin:22px 0 0 0;padding:15px 0 0 0;border-top:1px solid rgba(255,255,255,.1);max-width:535px}.pro-onboarding-dialog p.warning:before{display:inline-block;content:"";width:15px;height:15px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${f}) 6px 3px no-repeat}.pro-onboarding-dialog p.warning>span{font-size:13px;line-height:20px;color:rgba(255,255,255,.4);display:inline-block;max-width:246px;margin-left:12px}.pro-onboarding-dialog p.warning>span em{color:#fff}.pro-onboarding-dialog .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px}.pro-onboarding-dialog .button,.pro-onboarding-dialog .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-onboarding-dialog .button{border:1px solid #fff}}.pro-onboarding-dialog .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.pro-onboarding-dialog .button>*:first-child{padding-left:0}.pro-onboarding-dialog .button>*:last-child{padding-right:0}.pro-onboarding-dialog .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-onboarding-dialog .button svg *{fill:CanvasText}}.pro-onboarding-dialog .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-onboarding-dialog .button svg{opacity:1}}.pro-onboarding-dialog .button img{height:50%}.pro-onboarding-dialog .button:disabled{opacity:.3}.pro-onboarding-dialog .button:disabled,.pro-onboarding-dialog .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.pro-onboarding-dialog .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.pro-onboarding-dialog .button:not(:disabled):hover svg{opacity:1}}.pro-onboarding-dialog .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.pro-onboarding-dialog .button.accent{box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent)}@media(hover: hover){.pro-onboarding-dialog .button.accent:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}.pro-onboarding-dialog .button.accent:not(:disabled):active{background-color:var(--color--accent)}.pro-onboarding-dialog .button.accent:not(:disabled):active{--cta__icon--color: #000;color:#000}.pro-onboarding-dialog .button.highlight{box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}@media(hover: hover){.pro-onboarding-dialog .button.highlight:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.pro-onboarding-dialog .button.highlight:not(:disabled):active{background-color:var(--theme--highlight)}.pro-onboarding-dialog .button.secondary{background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}@media(hover: hover){.pro-onboarding-dialog .button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}.pro-onboarding-dialog .success{display:flex;align-items:center;margin-top:24px}.pro-onboarding-dialog .success .label{font-weight:800;font-size:21px;line-height:30px;font-weight:700;break-inside:avoid;white-space:nowrap;color:#fff;margin:0}.pro-onboarding-dialog .success .pulse{width:16px;height:16px;position:relative;margin-right:10px;display:inline-block}.pro-onboarding-dialog .success .pulse:after{background:rgba(var(--theme--highlight--rgb), 0.75);content:"";position:absolute;left:0;top:0;width:16px;height:16px;display:inline-block;border-radius:50%;animation:pulse 1s infinite ease-in-out}.pro-onboarding-dialog .success .pulse:before{background:rgba(var(--theme--highlight--rgb), 0.24);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:16px;border-radius:50%;animation:pulse-2 1s infinite}`,""]);const y=m},"dialogs/pro-showcase-columns-dialog":(o,i,e)=>{e.r(i),e.d(i,{ProShowcaseColumnsDialog:()=>s,ProShowcaseColumnsDialogService:()=>l});var t=e(15215),n=e("aurelia-dialog"),r=e("aurelia-framework"),a=e(17275);let s=class{constructor(o){this.controller=o}async activate(o){o?.defaultFeature&&(this.defaultFeature=o?.defaultFeature)}handleClose(o){o?this.controller.ok():this.controller.cancel()}};s=(0,t.Cg)([(0,r.autoinject)(),(0,t.Sn)("design:paramtypes",[n.DialogController])],s);let l=class extends a.C{constructor(){super(...arguments),this.viewModelClass="dialogs/pro-showcase-columns-dialog"}};l=(0,t.Cg)([(0,r.autoinject)()],l)},"dialogs/pro-showcase-columns-dialog.html":(o,i,e)=>{e.r(i),e.d(i,{default:()=>t});const t='<template> <require from="./pro-showcase-columns-dialog.scss"></require> <require from="shared/resources/elements/close-button"></require> <require from="pro-promos/pro-showcase-columns/pro-showcase-columns"></require> <ux-dialog class="pro-showcase-columns-dialog"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <pro-showcase-columns default-feature.bind="defaultFeature" on-close.call="handleClose(result)"></pro-showcase-columns> </ux-dialog-body> </ux-dialog> </template> '},"dialogs/pro-showcase-columns-dialog.scss":(o,i,e)=>{e.r(i),e.d(i,{default:()=>s});var t=e(31601),n=e.n(t),r=e(76314),a=e.n(r)()(n());a.push([o.id,".pro-showcase-columns-dialog{width:900px;max-width:100%;max-height:80vh;padding:0}.pro-showcase-columns-dialog pro-showcase-columns{border-radius:20px;overflow:hidden;position:relative;z-index:0}.pro-showcase-columns-dialog close-button{z-index:1}",""]);const s=a},"dialogs/pro-showcase-dialog":(o,i,e)=>{e.r(i),e.d(i,{ProShowcaseDialog:()=>s,ProShowcaseDialogService:()=>l});var t=e(15215),n=e("aurelia-dialog"),r=e("aurelia-framework"),a=e(17275);let s=class{constructor(o){this.controller=o}activate(o){this.defaultFeature=o?.defaultFeature}handleProCtaClick(){this.controller.ok(this.defaultFeature)}handleCloseClick(){this.controller.cancel(this.defaultFeature)}};s=(0,t.Cg)([(0,r.autoinject)(),(0,t.Sn)("design:paramtypes",[n.DialogController])],s);let l=class extends a.C{constructor(){super(...arguments),this.viewModelClass="dialogs/pro-showcase-dialog"}};l=(0,t.Cg)([(0,r.autoinject)()],l)},"dialogs/pro-showcase-dialog.html":(o,i,e)=>{e.r(i),e.d(i,{default:()=>t});const t='<template> <require from="pro-promos/pro-showcase/pro-showcase"></require> <require from="./pro-showcase-dialog.scss"></require> <require from="shared/resources/elements/close-button"></require> <ux-dialog class="pro-showcase-dialog"> <close-button click.trigger="handleCloseClick()" tabindex="0"></close-button> <pro-showcase current-feature.two-way="defaultFeature" on-pro-cta-click.call="handleProCtaClick()"> </pro-showcase> </ux-dialog> </template> '},"dialogs/pro-showcase-dialog.scss":(o,i,e)=>{e.r(i),e.d(i,{default:()=>s});var t=e(31601),n=e.n(t),r=e(76314),a=e.n(r)()(n());a.push([o.id,".pro-showcase-dialog{width:900px;max-width:100%;padding:0}.pro-showcase-dialog close-button{z-index:1}.pro-showcase-dialog pro-showcase{overflow:hidden;border-radius:20px;display:flex}",""]);const s=a}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4200],{23218:(e,o,a)=>{a.d(o,{D:()=>r});var i=a(15215),t=a("aurelia-dialog"),l=a("aurelia-event-aggregator"),n=a("aurelia-framework");let r=class{#e;#o;constructor(e,o){this.dialog=e,this.allowMultiple=!1,this.#e=!1,this.#o=o}async open(e,o){if(!this.#e||this.allowMultiple){this.#e=!0,this.#o.publish("dialog:open:start",this.viewModelClass);try{const a=this.dialog.open({viewModel:this.viewModelClass,host:document.querySelector("#fullscreen-dialogs")??void 0,startingZIndex:1e3,model:e,lock:o,keyboard:!o});return a.then((()=>this.#o.publish("dialog:open:complete",this.viewModelClass))),await a.whenClosed()}finally{this.#e=!1}}}};r=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[t.DialogService,l.EventAggregator])],r)},52871:(e,o,a)=>{a.d(o,{g:()=>c});var i=a(15215),t=a("aurelia-framework"),l=a(20770),n=a(45660),r=a(59239),s=a("dialogs/feature-announcement-dialog");const c=["2","4","35","36","37","38","45","46","48","139","258","78","90","147","208","230","257","275","56","13","51","148","175","31"];let d=class{#a;#i;constructor(e,o){this.#a=e,this.#i=o}async openIfTitleSupportsLiveLocation(e){const o=await this.#i.state.pipe((0,n.$)(),(0,r.E)("catalog","maps")).toPromise(),a=o?.filter((o=>o.titleId===e)),i=a?.filter((e=>c.includes(e.id)));return i?.length>0&&await this.open()}async open(){return await this.#a.open({featureName:"liveLocation",headerKey:"live_location_announcement_dialog.introducing_the_all_new",featureTitleKey:"live_location_announcement_dialog.live_location_tracking",messageKey:"live_location_announcement_dialog.see_your_position_in_real_time",messageParams:{},videoUrl:"https://media.wemod.com/videos/feature-announcements/live-location.webm",loopVideo:!0}),!0}};d=(0,i.Cg)([(0,t.autoinject)(),(0,i.Sn)("design:paramtypes",[s.FeatureAnnouncementDialogService,l.il])],d)},71341:(e,o,a)=>{a.d(o,{U:()=>r});var i=a(15215),t=a("aurelia-framework"),l=a(78576),n=a("dialogs/fullscreen-webview-dialog");let r=class{#t;#l;constructor(e,o){this.#t=e,this.#l=o}async open(e){const o={discountCode:e?.discountCode,frequency:e?.frequency?e.frequency:e?.selectedPlan?e?.selectedPlan?.recurring?.frequency:void 0,trigger:e?.trigger},a=await this.#t.open({route:"checkout",params:o,styles:{background:"var(--theme--background)"}});a&&!a.wasCancelled&&this.#l.check()}};r=(0,i.Cg)([(0,t.autoinject)(),(0,i.Sn)("design:paramtypes",[n.FullscreenWebviewDialogService,l.G])],r)},"dialogs/choose-plan-promo-dialog":(e,o,a)=>{a.r(o),a.d(o,{ChoosePlanPromoDialog:()=>r,ChoosePlanPromoDialogService:()=>s});var i=a(15215),t=a("aurelia-dialog"),l=a("aurelia-framework"),n=a(17275);let r=class{constructor(e){this.controller=e}handleProCtaClick(){this.controller.close(!0)}};r=(0,i.Cg)([(0,l.autoinject)(),(0,i.Sn)("design:paramtypes",[t.DialogController])],r);let s=class extends n.C{constructor(){super(...arguments),this.viewModelClass="dialogs/choose-plan-promo-dialog"}};s=(0,i.Cg)([(0,l.autoinject)()],s)},"dialogs/choose-plan-promo-dialog.html":(e,o,a)=>{a.r(o),a.d(o,{default:()=>i});const i='<template> <require from="pro-promos/choose-plan-promo/choose-plan-promo"></require> <require from="./choose-plan-promo-dialog.scss"></require> <require from="shared/resources/elements/close-button"></require> <ux-dialog class="choose-plan-promo-dialog"> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <choose-plan-promo on-pro-cta-click.call="handleProCtaClick()"> </choose-plan-promo> </ux-dialog> </template> '},"dialogs/choose-plan-promo-dialog.scss":(e,o,a)=>{a.r(o),a.d(o,{default:()=>r});var i=a(31601),t=a.n(i),l=a(76314),n=a.n(l)()(t());n.push([e.id,".choose-plan-promo-dialog{width:900px;max-width:100%;padding:0}.choose-plan-promo-dialog close-button{z-index:1}.choose-plan-promo-dialog pro-showcase{overflow:hidden;border-radius:20px;display:flex}",""]);const r=n},"dialogs/email-dialog":(e,o,a)=>{a.r(o),a.d(o,{EmailDialog:()=>c,EmailDialogService:()=>d});var i=a(15215),t=a("aurelia-dialog"),l=a("aurelia-framework"),n=a(54995),r=a(70236),s=a(23218);let c=class{constructor(e){this.controller=e,this.saving=!1}attached(){this.accountChanged()}accountChanged(){this.emailSet=(0,r.Lt)(this.account.flags,2),this.emailSet&&this.controller.close(!0)}async save(){this.saving=!0,!this.emailSet&&this.accountEmail&&await this.accountEmail.submit(),this.saving=!1}get canSave(){return(this.emailSet||["valid","unsure"].includes(this.accountEmailStatus))&&!this.saving}};(0,i.Cg)([(0,l.computedFrom)("emailSet","accountEmailStatus","saving"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],c.prototype,"canSave",null),c=(0,i.Cg)([(0,n.m6)({selectors:{account:(0,n.$t)((e=>e.account))}}),(0,l.autoinject)(),(0,i.Sn)("design:paramtypes",[t.DialogController])],c);let d=class extends s.D{constructor(){super(...arguments),this.viewModelClass="dialogs/email-dialog"}};d=(0,i.Cg)([(0,l.autoinject)()],d)},"dialogs/email-dialog.html":(e,o,a)=>{a.r(o),a.d(o,{default:()=>i});const i='<template> <require from="./email-dialog.scss"></require> <require from="./resources/elements/support-wemod-footer"></require> <require from="../queue/resources/elements/creators-list"></require> <require from="../resources/elements/account-email"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="email-dialog fullscreen-dialog"> <div class="scroll-wrapper"> <div class="scroll-inner"> <header> <h1>${\'email_dialog.thank_you_for_your_payment\' | i18n}</h1> <close-button if.bind="!controller.settings.lock" click.trigger="controller.cancel()" tabindex="0"></close-button> </header> <div class="layout"> <div class="content"> <h3>${\'email_dialog.account_email\' | i18n}</h3> <p>${\'email_dialog.please_enter_a_valid_email_address\' | i18n}</p> <div class="text-input required"> <label>${\'email_dialog.your_email_address\' | i18n}</label> <account-email view-model.ref="accountEmail" status.bind="accountEmailStatus" location="email_dialog"></account-email> </div> <button disabled.bind="!canSave" click.delegate="save()"> ${\'email_dialog.continue_with_pro\' | i18n} </button> <div class="note">${\'email_dialog.note_you_can_update\' | i18n}</div> </div> <div class="creators"> <h4>${\'email_dialog.the_creators\' | i18n}</h4> <p>${\'email_dialog.your_subscription_helps\' | i18n}</p> <creators-list></creators-list> </div> </div> <support-wemod-footer></support-wemod-footer> </div> </div> </ux-dialog> </template> '},"dialogs/email-dialog.scss":(e,o,a)=>{a.r(o),a.d(o,{default:()=>u});var i=a(31601),t=a.n(i),l=a(76314),n=a.n(l),r=a(4417),s=a.n(r),c=new URL(a(83959),a.b),d=n()(t()),g=s()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.email-dialog{background:var(--theme--background) !important}.email-dialog .scroll-wrapper{width:100vw;height:100vh;overflow-x:hidden;overflow-y:auto;padding:128px 100px 60px}.email-dialog .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.email-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.email-dialog .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.email-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.email-dialog .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.email-dialog .scroll-inner{display:flex;flex-direction:column;min-height:100%}.email-dialog header{margin:0 0 50px}.email-dialog header h1{font-weight:700;font-size:52px;line-height:40px;color:#fff}.email-dialog .layout{display:flex;flex:1 1 auto;align-items:flex-start}.email-dialog .layout>.content{background:linear-gradient(180deg, var(--theme--secondary-background) 0%, transparent 100%);border-radius:20px;padding:30px;flex:1 1 auto;margin:0 96px 0 0}.email-dialog .layout>.content h3{font-weight:800;font-size:24px;line-height:32px;color:#fff;margin:0 0 8px}.email-dialog .layout>.content p{font-size:14px;line-height:21px;color:rgba(255,255,255,.5);margin:0 0 30px}.email-dialog .layout>.content .text-input{margin-bottom:24px;max-width:334px}.email-dialog .layout>.content .text-input label{font-size:12px;line-height:18px;font-weight:500;--input__label--color: rgba(255, 255, 255, 0.4);color:var(--input__label--color);display:block;margin:0 0 5px 9px}.email-dialog .layout>.content .text-input.required label:after{display:inline;content:" *";color:#fff}.email-dialog .layout>.content button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important;min-width:290px;margin:0 0 30px}.email-dialog .layout>.content button,.email-dialog .layout>.content button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .email-dialog .layout>.content button{border:1px solid #fff}}.email-dialog .layout>.content button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.email-dialog .layout>.content button>*:first-child{padding-left:0}.email-dialog .layout>.content button>*:last-child{padding-right:0}.email-dialog .layout>.content button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .email-dialog .layout>.content button svg *{fill:CanvasText}}.email-dialog .layout>.content button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .email-dialog .layout>.content button svg{opacity:1}}.email-dialog .layout>.content button img{height:50%}.email-dialog .layout>.content button:disabled{opacity:.3}.email-dialog .layout>.content button:disabled,.email-dialog .layout>.content button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.email-dialog .layout>.content button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.email-dialog .layout>.content button:not(:disabled):hover svg{opacity:1}}.email-dialog .layout>.content button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.email-dialog .layout>.content button:hover{filter:brightness(1.1)}}.email-dialog .layout>.content .note{font-size:11px;line-height:17px;letter-spacing:1px;font-weight:600;color:rgba(255,255,255,.4)}.email-dialog .layout>.creators{flex:0 1 408px;padding:40px 50px;border:1px solid #424242;border-radius:20px}.email-dialog .layout>.creators h4{font-weight:800;font-size:21px;line-height:30px;font-weight:700;color:#fff;margin:0 0 5px}.email-dialog .layout>.creators p{font-size:14px;line-height:21px;line-height:19px;color:rgba(255,255,255,.5);margin:0 0 20px}.email-dialog support-wemod-footer{margin-top:40px}`,""]);const u=d},"dialogs/feature-announcement-dialog":(e,o,a)=>{a.r(o),a.d(o,{FeatureAnnouncementDialog:()=>d,FeatureAnnouncementDialogService:()=>g});var i=a(15215),t=a("aurelia-dialog"),l=a("aurelia-framework"),n=a(20770),r=a(62914),s=a(17275),c=a(48881);let d=class{#n;#i;constructor(e,o,a){this.controller=e,this.#n=o,this.#i=a}activate(e){this.config=e,!1===e?.cancelable&&(this.controller.settings.lock=!0,this.controller.settings.overlayDismiss=!1,this.controller.settings.keyboard=!1),this.#n.event("feature_announcement_dialog_open",{featureName:e.featureName},r.Io),this.#i.dispatch(c.NX,`${e.featureName}AnnouncementShown`,!0)}handleActionClick(e){e.onClick&&e.onClick(),this.controller.cancel()}};d=(0,i.Cg)([(0,l.autoinject)(),(0,i.Sn)("design:paramtypes",[t.DialogController,r.j0,n.il])],d);let g=class extends s.C{constructor(){super(...arguments),this.viewModelClass="dialogs/feature-announcement-dialog"}};g=(0,i.Cg)([(0,l.autoinject)()],g)},"dialogs/feature-announcement-dialog.html":(e,o,a)=>{a.r(o),a.d(o,{default:()=>i});const i='<template> <require from="./feature-announcement-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../resources/elements/beta-tag.html"></require> <ux-dialog class="feature-announcement-dialog scrollable"> <div class="dialog-scroll-wrapper"> <ux-dialog-header if.bind="config.cancelable !== false"> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body style.bind="{\'--feature-announcement-dialog-highlight-color\': config.highlightColor}"> <header> <div if.bind="config.headerKey">${config.headerKey | i18n}</div> <h1> <span class="icon" if.bind="config.icon">${config.icon}</span> ${config.featureTitleKey | i18n} <beta-tag if.bind="config.beta"></beta-tag> </h1> </header> <p innerhtml.bind="config.messageKey | i18n:config.messageParams | markdown"></p> <div class="actions" if.bind="config.actions"> <wm-button repeat.for="action of config.actions" click.delegate="handleActionClick(action)" color.bind="action.isSecondary ? \'inverse\' : \'primary\'" variant.bind="action.isSecondary ? \'unfilled\' : \'filled\'" size="l"> ${action.labelKey | i18n} </wm-button> </div> <video src.bind="config.videoUrl" autoplay muted loop.bind="config.loopVideo"></video> </ux-dialog-body> </div> </ux-dialog> </template> '},"dialogs/feature-announcement-dialog.scss":(e,o,a)=>{a.r(o),a.d(o,{default:()=>u});var i=a(31601),t=a.n(i),l=a(76314),n=a.n(l),r=a(4417),s=a.n(r),c=new URL(a(83959),a.b),d=n()(t()),g=s()(c);d.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,.feature-announcement-dialog .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.feature-announcement-dialog{--feature-announcement-dialog-highlight-color: var(--theme--highlight);width:900px;padding:0;text-align:center;background:var(--theme--background) !important}.feature-announcement-dialog header{margin:56px 0 0;color:#fff}.feature-announcement-dialog header div{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px}.feature-announcement-dialog header h1{font-weight:900;font-size:52px;line-height:65px;font-style:italic;letter-spacing:-3px;display:block;margin:-5px 0}.feature-announcement-dialog p{margin:5px 0 36px;padding:0;display:inline-block;font-size:18px;line-height:30px;max-width:550px}.theme-default .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.theme-purple-pro .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.theme-green-pro .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.theme-orange-pro .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.theme-pro .feature-announcement-dialog p{color:rgba(255,255,255,.6)}.feature-announcement-dialog p strong{font-weight:700}.theme-default .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.theme-purple-pro .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.theme-green-pro .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.theme-orange-pro .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.theme-pro .feature-announcement-dialog p strong{color:rgba(255,255,255,.8)}.feature-announcement-dialog video{display:block;width:100%;border-bottom-left-radius:20px;border-bottom-right-radius:20px}.feature-announcement-dialog .actions{display:flex;justify-content:center;flex-direction:column;align-items:center;gap:8px;margin-bottom:16px}.feature-announcement-dialog .actions wm-button{width:auto;min-width:180px}.feature-announcement-dialog .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:48px;color:var(--feature-announcement-dialog-highlight-color);vertical-align:middle;margin-top:-8px}.feature-announcement-dialog beta-tag{--beta-tag-bg-color: var(--feature-announcement-dialog-highlight-color);--beta-tag-color: #fff;vertical-align:text-top}`,""]);const u=d},"dialogs/fullscreen-webview-dialog":(e,o,a)=>{a.r(o),a.d(o,{FullscreenWebviewDialog:()=>r,FullscreenWebviewDialogService:()=>s});var i=a(15215),t=a("aurelia-framework"),l=a(23218),n=a("dialogs/webview-dialog");class r extends n.WebviewDialog{}let s=class extends l.D{constructor(){super(...arguments),this.viewModelClass="dialogs/fullscreen-webview-dialog",this.allowMultiple=!0}};s=(0,i.Cg)([(0,t.autoinject)()],s)},"dialogs/fullscreen-webview-dialog.html":(e,o,a)=>{a.r(o),a.d(o,{default:()=>i});const i='<template> <require from="./fullscreen-webview-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/resources/elements/loading-indicator"></require> <ux-dialog class="fullscreen-webview-dialog fullscreen-dialog ${!loading ? \'loaded\' : \'\'}" style.bind="config.styles"> <close-button click.delegate="controller.close(false, \'fullscreen_webview_close_button\')" tabindex="0"></close-button> <div class="scroll-wrapper"> <div class="scroll-inner"> <div class="loading-indicator" if.bind="loading"> <loading-indicator></loading-indicator> </div> <iframe ref="iframeEl" show.bind="!loading"></iframe> </div> </div> </ux-dialog> </template> '},"dialogs/fullscreen-webview-dialog.scss":(e,o,a)=>{a.r(o),a.d(o,{default:()=>r});var i=a(31601),t=a.n(i),l=a(76314),n=a.n(l)()(t());n.push([e.id,".fullscreen-webview-dialog .scroll-wrapper{width:100vw;height:100vh;overflow-x:hidden;overflow-y:auto}.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.fullscreen-webview-dialog .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.fullscreen-webview-dialog .scroll-inner{display:flex;flex-direction:column;min-height:100vh}.fullscreen-webview-dialog iframe{width:100%;height:100%;border:0;min-height:100vh}.fullscreen-webview-dialog .loading-indicator{display:flex;width:100%;height:100vh;align-items:center;justify-content:center}.fullscreen-webview-dialog close-button{transition:visibility 0s .3s;visibility:visible}.fullscreen-webview-dialog.loaded close-button{visibility:hidden;pointer-events:none}",""]);const r=n},"dialogs/payment-processing-dialog":(e,o,a)=>{a.r(o),a.d(o,{PaymentProcessingDialog:()=>n,PaymentProcessingDialogService:()=>r});var i=a(15215),t=a("aurelia-dialog"),l=a("aurelia-framework");class n{}let r=class{#r;constructor(e){this.#r=e}async show(){this.#s()||await this.#r.open({viewModel:"dialogs/payment-processing-dialog",host:document.querySelector("#dialogs")||document.body,startingZIndex:1002,lock:!0,keyboard:!1})}async hide(){await(this.#s()?.close(!0))}#s(){for(const e of this.#r.controllers)if(e.controller.viewModel instanceof n)return e;return null}};r=(0,i.Cg)([(0,l.autoinject)(),(0,i.Sn)("design:paramtypes",[t.DialogService])],r)},"dialogs/payment-processing-dialog.html":(e,o,a)=>{a.r(o),a.d(o,{default:()=>i});const i='<template> <require from="../shared/resources/elements/payment-processing"></require> <payment-processing loading.bind="true" modal.bind="true"></payment-processing> </template> '}}]);
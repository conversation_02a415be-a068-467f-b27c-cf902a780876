"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5474],{44301:(e,a,i)=>{i.d(a,{Q:()=>o});const o={feedbackProvided:"FeedbackProvided",modActivated:"ModActivated",appWindowFocused:"AppWindowFocused"}},"api/index":(e,a,i)=>{i.r(a),i.d(a,{configure:()=>n});var o=i("shared/api/index"),t=i(84551),r=i(68663);function n(e,a){e.container.registerSingleton(r.x,(function(){return new r.x(e.container.get(o.WeModApiClient),e.container.get(t.Y),a.catalogUrl)}))}},"app/app":(e,a,i)=>{i.r(a),i.d(a,{App:()=>f});var o=i(15215),t=i(7530),r=i("aurelia-event-aggregator"),n=i("aurelia-framework"),s=i(20770),l=i(60692),p=i(45660),d=i(59239),c=i(68539),b=i(20057),v=i(49442),u=i(27378),w=i(48881),g=i(12511),h=i(38777),k=i(76861),m=i(68492);let f=class{#e;#a;#i;#o;#t;#r;#n;constructor(e,a,i,o,t){this.isSidebarCollapsed=!1,this.isSidebarForcedCollapsed=!1,this.#e=e,this.#a=a,this.#i=i,this.#o=o,this.#n=t}configureRouter(e,a){e.options.root="/",e.map(m.A),this.#t=a}async activate(){this.#r=(new h.Vd).push(this.#i.onLocaleChanged((()=>this.#s()))).pushEventListener(window,"blur",(()=>this.#l(!1))).pushEventListener(window,"focus",(()=>this.#l(!0))),this.#s(),await this.#a.activate()}deactivate(){return this.#r.dispose(),this.#a.deactivate()}attached(){this.#a.attached(this.#t),this.#e.state.pipe((0,p.$)(),(0,d.E)("flags","firstRun")).subscribe((e=>{e&&this.#e.dispatch(w.NX,"firstRun",!1)})),this.forceCollapsedSubscription=(0,u.c)("(max-width: 1200px)").subscribe(this.handleForceCollapsedChanged.bind(this)),this.#e.dispatch(w.Kc,{isTitleSidebarCollapsed:g.u.settings.isTitleSidebarCollapsed}),this.#o.trigger(l.n.NewProSelectPlanPagesRound2).catch(v.Y),this.#n.publish("app-ready")}handleForceCollapsedChanged(e){this.isSidebarForcedCollapsed=e}detached(){this.#a.detached(),this.forceCollapsedSubscription?.unsubscribe()}handleCollapseClick(){this.isSidebarCollapsed=!this.isSidebarCollapsed}#l(e){document.body.classList.toggle("disable-looping-animation",!e)}#s(){const e=(this.#i.getEffectiveLocale()||this.#i.getSystemLocale()).language;document.documentElement.lang=e.toString()}};(0,o.Cg)([(0,n.bindable)({defaultBindingMode:t.BG.twoWay}),(0,o.Sn)("design:type",Boolean)],f.prototype,"isSidebarCollapsed",void 0),(0,o.Cg)([(0,n.bindable)({defaultBindingMode:t.BG.twoWay}),(0,o.Sn)("design:type",Boolean)],f.prototype,"isSidebarForcedCollapsed",void 0),f=(0,o.Cg)([(0,n.autoinject)(),(0,o.Sn)("design:paramtypes",[s.il,k.P,b.F2,c.z,r.EventAggregator])],f)},"app/app.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>o});const o='<template> <require from="./app.scss"></require> <require from="./resources/elements/app-header"></require> <require from="./resources/elements/app-sidebar"></require> <require from="./resources/elements/nav-header-actions"></require> <require from="./resources/elements/promotion-banner"></require> <require from="./toasts"></require> <require from="../ads/cmp-popup"></require> <div id="fullscreen-dialogs"></div> <div id="dialogs"></div> <div class="app-layout"> <div class="content-layout"> <app-sidebar is-sidebar-collapsed.bind="isSidebarCollapsed" is-sidebar-forced-collapsed.bind="isSidebarForcedCollapsed" inert-when-dialog-open></app-sidebar> <div class="main-content"> <div class="window-header ${isSidebarCollapsed || isSidebarForcedCollapsed ? \'sidebar-collapsed\' : \'\'}"> <nav-header-actions is-sidebar-forced-collapsed.bind="isSidebarForcedCollapsed" handle-collapse-click.call="handleCollapseClick()" inert-when-dialog-open></nav-header-actions> <app-header></app-header> </div> <div class="app-content inert-when-dialog-open"> <router-view></router-view> </div> </div> </div> <footer class="app-footer"> <promotion-banner class="inert-when-dialog-open"></promotion-banner> </footer> </div> <toasts is-sidebar-collapsed.bind="isSidebarCollapsed" is-sidebar-forced-collapsed.bind="isSidebarForcedCollapsed"></toasts> <cmp-popup></cmp-popup> </template> '},"app/app.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>s});var o=i(31601),t=i.n(o),r=i(76314),n=i.n(r)()(t());n.push([e.id,'#app{z-index:0;position:relative}.app-layout{width:100vw;height:100vh;position:relative;display:flex;flex-direction:column;background:var(--theme--background)}.content-layout{flex:1 1 auto;display:flex;flex-direction:row;overflow:hidden}.content-layout .main-content{background:var(--theme--background);display:flex;flex-direction:column;width:100%}.app-footer{flex:0 0 auto}body.disable-view-transition .app-content>router-view .view-background,body.disable-view-transition .app-content>.app-view .view-background{animation:none !important}body.disable-looping-animation *:not(.allow-looping-animation),body.disable-looping-animation *:not(.allow-looping-animation):before,body.disable-looping-animation *:not(.allow-looping-animation):after{animation-iteration-count:1 !important}.app-content{--safe-area-padding: var(--toasts-safe-area);height:100%;z-index:0;position:relative}.app-content>router-view,.app-content>.app-view{display:block;height:100%}.app-content>router-view .view-background,.app-content>.app-view .view-background{--overflow-fade--background: var(--theme--background);display:block;height:100%;position:relative;z-index:0}.app-content>router-view .view-background.au-enter-active,.app-content>.app-view .view-background.au-enter-active{animation:view-transition .15s}.app-content>router-view .view-background.au-leave-active,.app-content>.app-view .view-background.au-leave-active{animation:view-transition .15s reverse forwards}.app-content>router-view .view-background>.overflow-fade__wrapper:before,.app-content>.app-view .view-background>.overflow-fade__wrapper:before{top:var(--constant--appHeaderHeight)}.app-content>router-view .view-background .view-scrollable,.app-content>.app-view .view-background .view-scrollable{display:flex;flex-direction:column;height:100%;width:100%;overflow-y:overlay;overflow-x:hidden;padding:var(--constant--appHeaderHeight) 24px 72px;contain:size}.app-content>router-view .view-background .view-scrollable::-webkit-scrollbar,.app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.app-content>router-view .view-background .view-scrollable::-webkit-scrollbar-thumb:window-inactive,.app-content>router-view .view-background .view-scrollable::-webkit-scrollbar-thumb,.app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar-thumb:window-inactive,.app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.app-content>router-view .view-background .view-scrollable::-webkit-scrollbar-thumb:window-inactive:hover,.app-content>router-view .view-background .view-scrollable::-webkit-scrollbar-thumb:hover,.app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar-thumb:window-inactive:hover,.app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.app-content>router-view .view-background .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement,.app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement{height:var(--constant--appHeaderHeight)}.app-content>router-view .view-background .view-scrollable::-webkit-scrollbar-button:single-button:vertical:increment,.app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar-button:single-button:vertical:increment{height:0px}.app-content>router-view .view-background .view-scrollable::-webkit-scrollbar,.app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent var(--constant--appHeaderHeight), rgba(255, 255, 255, 0.1) var(--constant--appHeaderHeight), rgba(255, 255, 255, 0.1) calc(100% - 0px), transparent calc(100% - 0px))}.app-content>router-view .view-background .view-scrollable:before,.app-content>.app-view .view-background .view-scrollable:before{content:"";position:absolute;left:0;top:0;width:100%;height:var(--constant--appHeaderHeight);background:rgba(var(--theme--background--rgb), 0.95);z-index:1}.window-header{display:flex;width:100%;position:absolute;top:0;left:0;height:36px;-webkit-app-region:drag}.window-header nav-header-actions{padding-top:8px;position:absolute;z-index:999;-webkit-app-region:no-drag;left:112px;transition:all .3s ease-in-out}.window-header.sidebar-collapsed nav-header-actions{left:88px}@keyframes view-transition{from{opacity:0;transform:translate(10px, 0)}to{opacity:1;transform:translate(0, 0)}}',""]);const s=n}}]);
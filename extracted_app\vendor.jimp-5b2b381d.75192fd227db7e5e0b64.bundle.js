"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8573],{5482:t=>{function e(t,e){this.size=this.size||t,this.smallerSize=this.smallerSize||e,function(t){for(var e=1;e<t;e++)a[e]=1;a[0]=1/Math.sqrt(2)}(this.size)}e.prototype.size=32,e.prototype.smallerSize=8,e.prototype.distance=function(t,e){for(var a=0,r=0;r<t.length;r++)t[r]!==e[r]&&a++;return a/t.length},e.prototype.getHash=function(t){(t=t.clone().resize(this.size,this.size)).grayscale();for(var e=[],r=0;r<t.bitmap.width;r++){e[r]=[];for(var n=0;n<t.bitmap.height;n++)e[r][n]=(i=t.getPixelColor(r,n),o=void 0,o={},o.r=Math.floor(i/Math.pow(256,3)),o.g=Math.floor((i-o.r*Math.pow(256,3))/Math.pow(256,2)),o.b=Math.floor((i-o.r*Math.pow(256,3)-o.g*Math.pow(256,2))/Math.pow(256,1)),o.a=Math.floor((i-o.r*Math.pow(256,3)-o.g*Math.pow(256,2)-o.b*Math.pow(256,1))/Math.pow(256,0)),o).b}for(var i,o,u=function(t,e){for(var r=e,n=[],i=0;i<r;i++){n[i]=[];for(var o=0;o<r;o++){for(var u=0,l=0;l<r;l++)for(var f=0;f<r;f++)u+=Math.cos((2*l+1)/(2*r)*i*Math.PI)*Math.cos((2*f+1)/(2*r)*o*Math.PI)*t[l][f];u*=a[i]*a[o]/4,n[i][o]=u}}return n}(e,this.size),l=0,f=0;f<this.smallerSize;f++)for(var s=0;s<this.smallerSize;s++)l+=u[f][s];for(var h=l/(this.smallerSize*this.smallerSize),c="",d=0;d<this.smallerSize;d++)for(var p=0;p<this.smallerSize;p++)c+=u[d][p]>h?"1":"0";return c};var a=[];t.exports=e},10048:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.getExtension=e.getType=e.addType=void 0;var a={};e.addType=function(t,e){a[t]=e},e.getType=function(t){var e,r=t.split("/").slice(-1);return(e=r[r.length-1].split(".").pop(),Object.entries(a).find((function(t){return t[1].includes(e)}))||[])[0]},e.getExtension=function(t){return(a[t.toLowerCase()]||[])[0]}},23832:(t,e,a)=>{var r=a(6305),n=a(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.addConstants=S,e.addJimpMethods=H,e.jimpEvMethod=U,e.jimpEvChange=z,Object.defineProperty(e,"addType",{enumerable:!0,get:function(){return B.addType}}),e.default=void 0;for(var i=n(a(59646)),o=n(a(85715)),u=n(a(17383)),l=n(a(34579)),f=n(a(28452)),s=n(a(63072)),h=n(a(12475)),c=n(a(29511)),d=n(a(43693)),p=n(a(73738)),b=n(a(79896)),m=n(a(16928)),g=n(a(24434)),v=a(65414),y=n(a(31493)),E=n(a(43480)),w=n(a(68160)),_=n(a(6535)),M=n(a(5482)),O=n(a(52789)),N=n(a(69004)),I=n(a(97494)),B=r(a(10048)),L=a(71779),P=r(a(59373)),x="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$_",A=[NaN,NaN],T=2;T<65;T++){var D=(0,y.default)(y.default.BIN,x.slice(0,T))(new Array(65).join("1"));A.push(D.length)}function R(){}function j(t,e){(0,O.default)(t,(function(a,r,n){if(a)return e(a);if("headers"in r&&"location"in r.headers)return t.url=r.headers.location,j(t,e);if("object"===(0,p.default)(n)&&Buffer.isBuffer(n))return e(null,n);var i="Could not load Buffer from <"+t.url+"> (HTTP: "+r.statusCode+")";return new Error(i)}))}var C,k={data:null,width:null,height:null},G=function(t){function e(){for(var t,a=arguments.length,r=new Array(a),n=0;n<a;n++)r[n]=arguments[n];(0,u.default)(this,e),t=(0,f.default)(this,(0,s.default)(e).call(this)),(0,d.default)((0,h.default)(t),"bitmap",k),(0,d.default)((0,h.default)(t),"_background",0),(0,d.default)((0,h.default)(t),"_originalMime",e.MIME_PNG),(0,d.default)((0,h.default)(t),"_exif",null),(0,d.default)((0,h.default)(t),"_rgba",!0),(0,d.default)((0,h.default)(t),"writeAsync",(function(e){return(0,I.default)(t.write,(0,h.default)(t),e)})),(0,d.default)((0,h.default)(t),"getBase64Async",(function(e){return(0,I.default)(t.getBase64,(0,h.default)(t),e)})),(0,d.default)((0,h.default)(t),"getBuffer",L.getBuffer),(0,d.default)((0,h.default)(t),"getBufferAsync",L.getBufferAsync),(0,d.default)((0,h.default)(t),"getPixelColour",t.getPixelColor),(0,d.default)((0,h.default)(t),"setPixelColour",t.setPixelColor);var i,o,l=(0,h.default)(t),c=R;function m(){for(var t=arguments.length,e=new Array(t),a=0;a<t;a++)e[a]=arguments[a];var r=e[0];(r||{}).methodName="constructor",setTimeout((function(){var t;r&&c===R?l.emitError("constructor",r):r||l.emitMulti("constructor","initialized"),(t=c).call.apply(t,[l].concat(e))}),1)}if(i=r[0],Object.prototype.toString.call(i).toLowerCase().indexOf("arraybuffer")>-1&&(r[0]=function(t){for(var e=Buffer.alloc(t.byteLength),a=new Uint8Array(t),r=0;r<e.length;++r)e[r]=a[r];return e}(r[0])),"number"==typeof r[0]&&"number"==typeof r[1]||parseInt(r[0],10)&&parseInt(r[1],10)){var g=parseInt(r[0],10),y=parseInt(r[1],10);if(c=r[2],"number"==typeof r[2]&&(t._background=r[2],c=r[3]),"string"==typeof r[2]&&(t._background=e.cssColorToHex(r[2]),c=r[3]),void 0===c&&(c=R),"function"!=typeof c)return(0,f.default)(t,v.throwError.call((0,h.default)(t),"cb must be a function",m));t.bitmap={data:Buffer.alloc(g*y*4),width:g,height:y};for(var E=0;E<t.bitmap.data.length;E+=4)t.bitmap.data.writeUInt32BE(t._background,E);m(null,(0,h.default)(t))}else if("object"===(0,p.default)(r[0])&&r[0].url){if("function"!=typeof(c=r[1]||R))return(0,f.default)(t,v.throwError.call((0,h.default)(t),"cb must be a function",m));j(r[0],(function(e,a){if(e)return v.throwError.call((0,h.default)(t),e,m);t.parseBitmap(a,r[0].url,m)}))}else if(r[0]instanceof e){var w=r[0];if(void 0===(c=r[1])&&(c=R),"function"!=typeof c)return(0,f.default)(t,v.throwError.call((0,h.default)(t),"cb must be a function",m));t.bitmap={data:Buffer.from(w.bitmap.data),width:w.bitmap.width,height:w.bitmap.height},t._quality=w._quality,t._deflateLevel=w._deflateLevel,t._deflateStrategy=w._deflateStrategy,t._filterType=w._filterType,t._rgba=w._rgba,t._background=w._background,t._originalMime=w._originalMime,m(null,(0,h.default)(t))}else if((o=r[0])&&"object"===(0,p.default)(o)&&"number"==typeof o.width&&"number"==typeof o.height&&(Buffer.isBuffer(o.data)||o.data instanceof Uint8Array||"function"==typeof Uint8ClampedArray&&o.data instanceof Uint8ClampedArray)&&(o.data.length===o.width*o.height*4||o.data.length===o.width*o.height*3)){var _=r[0];c=r[1]||R;var M=_.width*_.height*4===_.data.length?Buffer.from(_.data):function(t){if(t.length%3!=0)throw new Error("Buffer length is incorrect");for(var e=Buffer.allocUnsafe(t.length/3*4),a=0,r=0;r<t.length;r++)e[a]=t[r],(r+1)%3==0&&(e[++a]=255),a++;return e}(_.data);t.bitmap={data:M,width:_.width,height:_.height},m(null,(0,h.default)(t))}else if("string"==typeof r[0]){var O=r[0];if(void 0===(c=r[1])&&(c=R),"function"!=typeof c)return(0,f.default)(t,v.throwError.call((0,h.default)(t),"cb must be a function",m));!function(t,e){b.default&&"function"==typeof b.default.readFile&&!t.match(/^(http|ftp)s?:\/\/./)?b.default.readFile(t,e):j({url:t},e)}(O,(function(e,a){if(e)return v.throwError.call((0,h.default)(t),e,m);t.parseBitmap(a,O,m)}))}else if("object"===(0,p.default)(r[0])&&Buffer.isBuffer(r[0])){var N=r[0];if("function"!=typeof(c=r[1]))return(0,f.default)(t,v.throwError.call((0,h.default)(t),"cb must be a function",m));t.parseBitmap(N,null,m)}else{"function"!=typeof(c=r[r.length-1])&&"function"!=typeof(c=r[r.length-2])&&(c=R);var B=e.__extraConstructors.find((function(t){return t.test.apply(t,r)}));if(!B)return(0,f.default)(t,v.throwError.call((0,h.default)(t),"No matching constructor overloading was found. Please see the docs for how to call the Jimp constructor.",m));new Promise((function(e,a){var n;(n=B.run).call.apply(n,[(0,h.default)(t),e,a].concat(r))})).then((function(){return m(null,(0,h.default)(t))})).catch(m)}return t}return(0,c.default)(e,t),(0,l.default)(e,[{key:"parseBitmap",value:function(t,e,a){L.parseBitmap.call(this,t,null,a)}},{key:"rgba",value:function(t,e){return"boolean"!=typeof t?v.throwError.call(this,"bool must be a boolean, true for RGBA or false for RGB",e):(this._rgba=t,(0,v.isNodePattern)(e)&&e.call(this,null,this),this)}},{key:"emitMulti",value:function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};a=Object.assign(a,{methodName:t,eventName:e}),this.emit("any",a),t&&this.emit(t,a),this.emit(e,a)}},{key:"emitError",value:function(t,e){this.emitMulti(t,"error",e)}},{key:"getHeight",value:function(){return this.bitmap.height}},{key:"getWidth",value:function(){return this.bitmap.width}},{key:"inspect",value:function(){return"<Jimp "+(this.bitmap===k?"pending...":this.bitmap.width+"x"+this.bitmap.height)+">"}},{key:"toString",value:function(){return"[object Jimp]"}},{key:"getMIME",value:function(){return this._originalMime||e.MIME_PNG}},{key:"getExtension",value:function(){var t=this.getMIME();return B.getExtension(t)}},{key:"write",value:function(t,e){var a=this;if(!b.default||!b.default.createWriteStream)throw new Error("Cant access the filesystem. You can use the getBase64 method.");if("string"!=typeof t)return v.throwError.call(this,"path must be a string",e);if(void 0===e&&(e=R),"function"!=typeof e)return v.throwError.call(this,"cb must be a function",e);var r=B.getType(t)||this.getMIME(),n=m.default.parse(t);return n.dir&&E.default.sync(n.dir),this.getBuffer(r,(function(r,n){if(r)return v.throwError.call(a,r,e);var i=b.default.createWriteStream(t);i.on("open",(function(){i.write(n),i.end()})).on("error",(function(t){return v.throwError.call(a,t,e)})),i.on("finish",(function(){e.call(a,null,a)}))})),this}},{key:"getBase64",value:function(t,a){return t===e.AUTO&&(t=this.getMIME()),"string"!=typeof t?v.throwError.call(this,"mime must be a string",a):"function"!=typeof a?v.throwError.call(this,"cb must be a function",a):(this.getBuffer(t,(function(e,r){if(e)return v.throwError.call(this,e,a);var n="data:"+t+";base64,"+r.toString("base64");a.call(this,null,n)})),this)}},{key:"hash",value:function(t,e){if("function"==typeof(t=t||64)&&(e=t,t=64),"number"!=typeof t)return v.throwError.call(this,"base must be a number",e);if(t<2||t>64)return v.throwError.call(this,"base must be a number between 2 and 64",e);var a=this.pHash();for(a=(0,y.default)(y.default.BIN,x.slice(0,t))(a);a.length<A[t];)a="0"+a;return(0,v.isNodePattern)(e)&&e.call(this,null,a),a}},{key:"pHash",value:function(){return(new M.default).getHash(this)}},{key:"distanceFromHash",value:function(t){var e=new M.default,a=e.getHash(this);return e.distance(a,t)}},{key:"getPixelIndex",value:function(t,a,r,n){var i,o;if("function"==typeof r&&void 0===n&&(n=r,r=null),r||(r=e.EDGE_EXTEND),"number"!=typeof t||"number"!=typeof a)return v.throwError.call(this,"x and y must be numbers",n);i=t=Math.round(t),o=a=Math.round(a),r===e.EDGE_EXTEND&&(t<0&&(i=0),t>=this.bitmap.width&&(i=this.bitmap.width-1),a<0&&(o=0),a>=this.bitmap.height&&(o=this.bitmap.height-1)),r===e.EDGE_WRAP&&(t<0&&(i=this.bitmap.width+t),t>=this.bitmap.width&&(i=t%this.bitmap.width),a<0&&(i=this.bitmap.height+a),a>=this.bitmap.height&&(o=a%this.bitmap.height));var u=this.bitmap.width*o+i<<2;return(i<0||i>=this.bitmap.width)&&(u=-1),(o<0||o>=this.bitmap.height)&&(u=-1),(0,v.isNodePattern)(n)&&n.call(this,null,u),u}},{key:"getPixelColor",value:function(t,e,a){if("number"!=typeof t||"number"!=typeof e)return v.throwError.call(this,"x and y must be numbers",a);t=Math.round(t),e=Math.round(e);var r=this.getPixelIndex(t,e),n=this.bitmap.data.readUInt32BE(r);return(0,v.isNodePattern)(a)&&a.call(this,null,n),n}},{key:"setPixelColor",value:function(t,e,a,r){if("number"!=typeof t||"number"!=typeof e||"number"!=typeof a)return v.throwError.call(this,"hex, x and y must be numbers",r);e=Math.round(e),a=Math.round(a);var n=this.getPixelIndex(e,a);return this.bitmap.data.writeUInt32BE(t,n),(0,v.isNodePattern)(r)&&r.call(this,null,this),this}},{key:"hasAlpha",value:function(){for(var t=0;t<this.bitmap.height;t++)for(var e=0;e<this.bitmap.width;e++){var a=this.bitmap.width*t+e<<2;if(255!==this.bitmap.data[a+3])return!0}return!1}},{key:"scanIterator",value:function(t,e,a,r){return"number"!=typeof t||"number"!=typeof e?v.throwError.call(this,"x and y must be numbers"):"number"!=typeof a||"number"!=typeof r?v.throwError.call(this,"w and h must be numbers"):(0,v.scanIterator)(this,t,e,a,r)}}]),e}(g.default);function S(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:G;Object.entries(t).forEach((function(t){var a=(0,o.default)(t,2),r=a[0],n=a[1];e[r]=n}))}function H(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:G;Object.entries(t).forEach((function(t){var a=(0,o.default)(t,2),r=a[0],n=a[1];e.prototype[r]=n}))}function U(t,e,a){var r="before-"+e,n=e.replace(/e$/,"")+"ed";G.prototype[t]=function(){for(var e,i=arguments.length,o=new Array(i),u=0;u<i;u++)o[u]=arguments[u];var l,f=o[a.length-1],s=this;"function"==typeof f?(e=function(){for(var e=arguments.length,a=new Array(e),r=0;r<e;r++)a[r]=arguments[r];var i=a[0],o=a[1];i?s.emitError(t,i):s.emitMulti(t,n,(0,d.default)({},t,o)),f.apply(this,a)},o[o.length-1]=e):e=!1,this.emitMulti(t,r);try{l=a.apply(this,o),e||this.emitMulti(t,n,(0,d.default)({},t,l))}catch(e){e.methodName=t,this.emitError(t,e)}return l},G.prototype[t+"Quiet"]=a}function z(t,e){U(t,"change",e)}S(P),H({composite:N.default}),G.__extraConstructors=[],G.appendConstructorOption=function(t,e,a){G.__extraConstructors.push({name:t,test:e,run:a})},G.read=function(){for(var t=arguments.length,e=new Array(t),a=0;a<t;a++)e[a]=arguments[a];return new Promise((function(t,a){(0,i.default)(G,e.concat([function(e,r){e?a(e):t(r)}]))}))},G.create=G.read,G.rgbaToInt=function(t,e,a,r,n){if("number"!=typeof t||"number"!=typeof e||"number"!=typeof a||"number"!=typeof r)return v.throwError.call(this,"r, g, b and a must be numbers",n);if(t<0||t>255)return v.throwError.call(this,"r must be between 0 and 255",n);if((e<0||e>255)&&v.throwError.call(this,"g must be between 0 and 255",n),a<0||a>255)return v.throwError.call(this,"b must be between 0 and 255",n);if(r<0||r>255)return v.throwError.call(this,"a must be between 0 and 255",n);t=Math.round(t),a=Math.round(a),e=Math.round(e),r=Math.round(r);var i=t*Math.pow(256,3)+e*Math.pow(256,2)+a*Math.pow(256,1)+r*Math.pow(256,0);return(0,v.isNodePattern)(n)&&n.call(this,null,i),i},G.intToRGBA=function(t,e){if("number"!=typeof t)return v.throwError.call(this,"i must be a number",e);var a={};return a.r=Math.floor(t/Math.pow(256,3)),a.g=Math.floor((t-a.r*Math.pow(256,3))/Math.pow(256,2)),a.b=Math.floor((t-a.r*Math.pow(256,3)-a.g*Math.pow(256,2))/Math.pow(256,1)),a.a=Math.floor((t-a.r*Math.pow(256,3)-a.g*Math.pow(256,2)-a.b*Math.pow(256,1))/Math.pow(256,0)),(0,v.isNodePattern)(e)&&e.call(this,null,a),a},G.cssColorToHex=function(t){return"number"==typeof(t=t||0)?Number(t):parseInt((0,_.default)(t).toHex8(),16)},G.limit255=function(t){return t=Math.max(t,0),Math.min(t,255)},G.diff=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:.1;if(!(t instanceof G&&e instanceof G))return v.throwError.call(this,"img1 and img2 must be an Jimp images");var r=t.bitmap,n=e.bitmap;if(r.width===n.width&&r.height===n.height||(r.width*r.height>n.width*n.height?t=t.cloneQuiet().resize(n.width,n.height):e=e.cloneQuiet().resize(r.width,r.height)),"number"!=typeof a||a<0||a>1)return v.throwError.call(this,"threshold must be a number between 0 and 1");var i=new G(r.width,r.height,4294967295);return{percent:(0,w.default)(r.data,n.data,i.bitmap.data,i.bitmap.width,i.bitmap.height,{threshold:a})/(i.bitmap.width*i.bitmap.height),image:i}},G.distance=function(t,e){var a=new M.default,r=a.getHash(t),n=a.getHash(e);return a.distance(r,n)},G.compareHashes=function(t,e){return(new M.default).distance(t,e)},G.colorDiff=function(t,e){var a=function(t){return Math.pow(t,2)},r=Math.max;return 0===t.a||t.a||(t.a=255),0===e.a||e.a||(e.a=255),(r(a(t.r-e.r),a(t.r-e.r-t.a+e.a))+r(a(t.g-e.g),a(t.g-e.g-t.a+e.a))+r(a(t.b-e.b),a(t.b-e.b-t.a+e.a)))/195075},U("clone","clone",(function(t){var e=new G(this);return(0,v.isNodePattern)(t)&&t.call(e,null,e),e})),z("background",(function(t,e){return"number"!=typeof t?v.throwError.call(this,"hex must be a hexadecimal rgba value",e):(this._background=t,(0,v.isNodePattern)(e)&&e.call(this,null,this),this)})),z("scan",(function(t,e,a,r,n,i){if("number"!=typeof t||"number"!=typeof e)return v.throwError.call(this,"x and y must be numbers",i);if("number"!=typeof a||"number"!=typeof r)return v.throwError.call(this,"w and h must be numbers",i);if("function"!=typeof n)return v.throwError.call(this,"f must be a function",i);var o=(0,v.scan)(this,t,e,a,r,n);return(0,v.isNodePattern)(i)&&i.call(this,null,o),o})),"BROWSER"===process.env.ENVIRONMENT&&("undefined"!=typeof window&&"object"===("undefined"==typeof window?"undefined":(0,p.default)(window))&&(C=window),"undefined"!=typeof self&&"object"===("undefined"==typeof self?"undefined":(0,p.default)(self))&&(C=self),C.Jimp=G,C.Buffer=Buffer);var V=G;e.default=V},45199:(t,e,a)=>{var r=a(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a(43693)),i=r(a(15609)),o=a(65414),u="image/jpeg";e.default=function(){return{mime:(0,n.default)({},u,["jpeg","jpg","jpe"]),constants:{MIME_JPEG:u},decoders:(0,n.default)({},u,i.default.decode),encoders:(0,n.default)({},u,(function(t){return i.default.encode(t.bitmap,t._quality).data})),class:{_quality:100,quality:function(t,e){return"number"!=typeof t?o.throwError.call(this,"n must be a number",e):t<0||t>100?o.throwError.call(this,"n must be a number 0 - 100",e):(this._quality=Math.round(t),(0,o.isNodePattern)(e)&&e.call(this,null,this),this)}}}},t.exports=e.default},52789:(t,e,a)=>{var r=a(2613),n=r(a(43693)),i=r(a(94634));function o(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}process.browser||"BROWSER"===process.env.ENVIRONMENT||void 0!==process.versions.electron&&"renderer"===process.type&&"function"==typeof XMLHttpRequest?t.exports=function(t,e){var a=new XMLHttpRequest;a.open("GET",t.url,!0),a.responseType="arraybuffer",a.addEventListener("load",(function(){if(a.status<400)try{var r=Buffer.from(this.response);e(null,a,r)}catch(a){return e(new Error("Response is not a buffer for url "+t.url+". Error: "+a.message))}else e(new Error("HTTP Status "+a.status+" for url "+t.url))})),a.addEventListener("error",(function(t){e(t)})),a.send()}:t.exports=function(t,e){var r=(0,i.default)({},t);a(65802)(function(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?o(a,!0).forEach((function(e){(0,n.default)(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):o(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}({compression:!0},r),(function(t,a){null===t?e(null,a,a.body):e(t)}))}},59373:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.EDGE_CROP=e.EDGE_WRAP=e.EDGE_EXTEND=e.BLEND_EXCLUSION=e.BLEND_DIFFERENCE=e.BLEND_HARDLIGHT=e.BLEND_LIGHTEN=e.BLEND_DARKEN=e.BLEND_OVERLAY=e.BLEND_SCREEN=e.BLEND_ADD=e.BLEND_MULTIPLY=e.BLEND_DESTINATION_OVER=e.BLEND_SOURCE_OVER=e.VERTICAL_ALIGN_BOTTOM=e.VERTICAL_ALIGN_MIDDLE=e.VERTICAL_ALIGN_TOP=e.HORIZONTAL_ALIGN_RIGHT=e.HORIZONTAL_ALIGN_CENTER=e.HORIZONTAL_ALIGN_LEFT=e.AUTO=void 0,e.AUTO=-1,e.HORIZONTAL_ALIGN_LEFT=1,e.HORIZONTAL_ALIGN_CENTER=2,e.HORIZONTAL_ALIGN_RIGHT=4,e.VERTICAL_ALIGN_TOP=8,e.VERTICAL_ALIGN_MIDDLE=16,e.VERTICAL_ALIGN_BOTTOM=32,e.BLEND_SOURCE_OVER="srcOver",e.BLEND_DESTINATION_OVER="dstOver",e.BLEND_MULTIPLY="multiply",e.BLEND_ADD="add",e.BLEND_SCREEN="screen",e.BLEND_OVERLAY="overlay",e.BLEND_DARKEN="darken",e.BLEND_LIGHTEN="lighten",e.BLEND_HARDLIGHT="hardLight",e.BLEND_DIFFERENCE="difference",e.BLEND_EXCLUSION="exclusion",e.EDGE_EXTEND=1,e.EDGE_WRAP=2,e.EDGE_CROP=3},62687:(t,e,a)=>{var r=a(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a(43693)),i=r(a(34399)),o=a(31224),u="image/gif";e.default=function(){return{mime:(0,n.default)({},u,["gif"]),constants:{MIME_GIF:u},decoders:(0,n.default)({},u,(function(t){var e=new i.default.GifReader(t),a=Buffer.alloc(e.width*e.height*4);return e.decodeAndBlitFrameRGBA(0,a),{data:a,width:e.width,height:e.height}})),encoders:(0,n.default)({},u,(function(t){var e=new o.BitmapImage(t.bitmap);o.GifUtil.quantizeDekker(e,256);var a=new o.GifFrame(e);return(new o.GifCodec).encodeGif([a],{}).then((function(t){return t.buffer}))}))}},t.exports=e.default},68898:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.srcOver=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a;return{r:(t.r*t.a+e.r*e.a*(1-t.a))/r,g:(t.g*t.a+e.g*e.a*(1-t.a))/r,b:(t.b*t.a+e.b*e.a*(1-t.a))/r,a:r}},e.dstOver=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a;return{r:(e.r*e.a+t.r*t.a*(1-e.a))/r,g:(e.g*e.a+t.g*t.a*(1-e.a))/r,b:(e.b*e.a+t.b*t.a*(1-e.a))/r,a:r}},e.multiply=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a,u=e.r*e.a,l=e.g*e.a,f=e.b*e.a;return{r:(n*u+n*(1-e.a)+u*(1-t.a))/r,g:(i*l+i*(1-e.a)+l*(1-t.a))/r,b:(o*f+o*(1-e.a)+f*(1-t.a))/r,a:r}},e.add=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a;return{r:(n+e.r*e.a)/r,g:(i+e.g*e.a)/r,b:(o+e.b*e.a)/r,a:r}},e.screen=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a,u=e.r*e.a,l=e.g*e.a,f=e.b*e.a;return{r:(n*e.a+u*t.a-n*u+n*(1-e.a)+u*(1-t.a))/r,g:(i*e.a+l*t.a-i*l+i*(1-e.a)+l*(1-t.a))/r,b:(o*e.a+f*t.a-o*f+o*(1-e.a)+f*(1-t.a))/r,a:r}},e.overlay=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a,u=e.r*e.a,l=e.g*e.a,f=e.b*e.a;return{r:(2*u<=e.a?2*n*u+n*(1-e.a)+u*(1-t.a):n*(1+e.a)+u*(1+t.a)-2*u*n-e.a*t.a)/r,g:(2*l<=e.a?2*i*l+i*(1-e.a)+l*(1-t.a):i*(1+e.a)+l*(1+t.a)-2*l*i-e.a*t.a)/r,b:(2*f<=e.a?2*o*f+o*(1-e.a)+f*(1-t.a):o*(1+e.a)+f*(1+t.a)-2*f*o-e.a*t.a)/r,a:r}},e.darken=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a,u=e.r*e.a,l=e.g*e.a,f=e.b*e.a;return{r:(Math.min(n*e.a,u*t.a)+n*(1-e.a)+u*(1-t.a))/r,g:(Math.min(i*e.a,l*t.a)+i*(1-e.a)+l*(1-t.a))/r,b:(Math.min(o*e.a,f*t.a)+o*(1-e.a)+f*(1-t.a))/r,a:r}},e.lighten=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a,u=e.r*e.a,l=e.g*e.a,f=e.b*e.a;return{r:(Math.max(n*e.a,u*t.a)+n*(1-e.a)+u*(1-t.a))/r,g:(Math.max(i*e.a,l*t.a)+i*(1-e.a)+l*(1-t.a))/r,b:(Math.max(o*e.a,f*t.a)+o*(1-e.a)+f*(1-t.a))/r,a:r}},e.hardLight=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a,u=e.r*e.a,l=e.g*e.a,f=e.b*e.a;return{r:(2*n<=t.a?2*n*u+n*(1-e.a)+u*(1-t.a):n*(1+e.a)+u*(1+t.a)-2*u*n-e.a*t.a)/r,g:(2*i<=t.a?2*i*l+i*(1-e.a)+l*(1-t.a):i*(1+e.a)+l*(1+t.a)-2*l*i-e.a*t.a)/r,b:(2*o<=t.a?2*o*f+o*(1-e.a)+f*(1-t.a):o*(1+e.a)+f*(1+t.a)-2*f*o-e.a*t.a)/r,a:r}},e.difference=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a,u=e.r*e.a,l=e.g*e.a,f=e.b*e.a;return{r:(n+u-2*Math.min(n*e.a,u*t.a))/r,g:(i+l-2*Math.min(i*e.a,l*t.a))/r,b:(o+f-2*Math.min(o*e.a,f*t.a))/r,a:r}},e.exclusion=function(t,e){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;t.a*=a;var r=e.a+t.a-e.a*t.a,n=t.r*t.a,i=t.g*t.a,o=t.b*t.a,u=e.r*e.a,l=e.g*e.a,f=e.b*e.a;return{r:(n*e.a+u*t.a-2*n*u+n*(1-e.a)+u*(1-t.a))/r,g:(i*e.a+l*t.a-2*i*l+i*(1-e.a)+l*(1-t.a))/r,b:(o*e.a+f*t.a-2*o*f+o*(1-e.a)+f*(1-t.a))/r,a:r}}},69004:(t,e,a)=>{var r=a(6305);Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e,a){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=arguments.length>4?arguments[4]:void 0;if("function"==typeof r&&(u=r,r={}),!(t instanceof this.constructor))return n.throwError.call(this,"The source must be a Jimp image",u);if("number"!=typeof e||"number"!=typeof a)return n.throwError.call(this,"x and y must be numbers",u);var l=r,f=l.mode,s=l.opacitySource,h=l.opacityDest;f||(f=i.BLEND_SOURCE_OVER),("number"!=typeof s||s<0||s>1)&&(s=1),("number"!=typeof h||h<0||h>1)&&(h=1);var c=o[f];e=Math.round(e),a=Math.round(a);var d=this;return 1!==h&&d.opacity(h),t.scanQuiet(0,0,t.bitmap.width,t.bitmap.height,(function(t,r,n){var o=d.getPixelIndex(e+t,a+r,i.EDGE_CROP),u=c({r:this.bitmap.data[n+0]/255,g:this.bitmap.data[n+1]/255,b:this.bitmap.data[n+2]/255,a:this.bitmap.data[n+3]/255},{r:d.bitmap.data[o+0]/255,g:d.bitmap.data[o+1]/255,b:d.bitmap.data[o+2]/255,a:d.bitmap.data[o+3]/255},s);d.bitmap.data[o+0]=this.constructor.limit255(255*u.r),d.bitmap.data[o+1]=this.constructor.limit255(255*u.g),d.bitmap.data[o+2]=this.constructor.limit255(255*u.b),d.bitmap.data[o+3]=this.constructor.limit255(255*u.a)})),(0,n.isNodePattern)(u)&&u.call(this,null,this),this};var n=a(65414),i=r(a(59373)),o=r(a(68898));t.exports=e.default},71779:(t,e,a)=>{var r=a(6305),n=a(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.parseBitmap=function(t,e,a){var r,n;return o.default.async((function(i){for(;;)switch(i.prev=i.next){case 0:return i.next=2,o.default.awrap(d(t,e));case 2:if("string"==typeof(r=i.sent)){i.next=5;break}return i.abrupt("return",a(new Error("Could not find MIME for Buffer <"+e+">")));case 5:if(this._originalMime=r.toLowerCase(),i.prev=6,n=this.getMIME(),!this.constructor.decoders[n]){i.next=12;break}this.bitmap=this.constructor.decoders[n](t),i.next=13;break;case 12:return i.abrupt("return",f.throwError.call(this,"Unsupported MIME type: "+n,a));case 13:i.next=18;break;case 15:return i.prev=15,i.t0=i.catch(6),i.abrupt("return",a.call(this,i.t0,this));case 18:try{this._exif=l.default.create(t).parse(),b(this)}catch(t){}return a.call(this,null,this),i.abrupt("return",this);case 21:case"end":return i.stop()}}),null,this,[[6,15]])},e.getBuffer=m,e.getBufferAsync=function(t){return(0,c.default)(m,this,t)};var i=n(a(85715)),o=n(a(54756)),u=n(a(53846)),l=n(a(87833)),f=a(65414),s=r(a(59373)),h=r(a(10048)),c=n(a(97494));function d(t,e){var a;return o.default.async((function(r){for(;;)switch(r.prev=r.next){case 0:return r.next=2,o.default.awrap(u.default.fromBuffer(t));case 2:if(!(a=r.sent)){r.next=5;break}return r.abrupt("return",a.mime);case 5:if(!e){r.next=7;break}return r.abrupt("return",h.getType(e));case 7:return r.abrupt("return",null);case 8:case"end":return r.stop()}}))}function p(t){return t._exif&&t._exif.tags&&t._exif.tags.Orientation||1}function b(t){if(!(p(t)<2)){var e=function(t){var e=t.getWidth(),a=t.getHeight();switch(p(t)){case 1:default:return null;case 2:return function(t,a){return[e-t-1,a]};case 3:return function(t,r){return[e-t-1,a-r-1]};case 4:return function(t,e){return[t,a-e-1]};case 5:return function(t,e){return[e,t]};case 6:return function(t,e){return[e,a-t-1]};case 7:return function(t,r){return[e-r-1,a-t-1]};case 8:return function(t,a){return[e-a-1,t]}}}(t),a=p(t)>4;!function(t,e,a,r){for(var n=t.bitmap.data,o=t.bitmap.width,u=Buffer.alloc(n.length),l=0;l<e;l++)for(var f=0;f<a;f++){var s=r(l,f),h=(0,i.default)(s,2),c=h[0],d=e*f+l<<2,p=o*h[1]+c<<2,b=n.readUInt32BE(p);u.writeUInt32BE(b,d)}t.bitmap.data=u,t.bitmap.width=e,t.bitmap.height=a}(t,a?t.bitmap.height:t.bitmap.width,a?t.bitmap.width:t.bitmap.height,e)}}function m(t,e){if(t===s.AUTO&&(t=this.getMIME()),"string"!=typeof t)return f.throwError.call(this,"mime must be a string",e);if("function"!=typeof e)return f.throwError.call(this,"cb must be a function",e);var a,r;if(t=t.toLowerCase(),this._rgba&&this.constructor.hasAlpha[t]?this.bitmap.data=Buffer.from(this.bitmap.data):this.bitmap.data=(a=this.constructor,r=this,new a(r.bitmap.width,r.bitmap.height,r._background).composite(r,0,0).bitmap).data,this.constructor.encoders[t]){var n=this.constructor.encoders[t](this);e.call(this,null,n)}else e.call(this,"Unsupported MIME type: "+t);return this}},75284:(t,e,a)=>{var r=a(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a(43693)),i=r(a(84252)),o=a(65414),u="image/bmp",l="image/x-ms-bmp",f=function(t){return e=i.default.decode(t),(0,o.scan)({bitmap:e},0,0,e.width,e.height,(function(t,a,r){var n=this.bitmap.data[r+0],i=this.bitmap.data[r+1],o=this.bitmap.data[r+2],u=this.bitmap.data[r+3];this.bitmap.data[r+0]=u,this.bitmap.data[r+1]=o,this.bitmap.data[r+2]=i,this.bitmap.data[r+3]=e.is_with_alpha?n:255})).bitmap;var e},s=function(t){return i.default.encode(function(t){return(0,o.scan)(t,0,0,t.bitmap.width,t.bitmap.height,(function(t,e,a){var r=this.bitmap.data[a+0],n=this.bitmap.data[a+1],i=this.bitmap.data[a+2],o=this.bitmap.data[a+3];this.bitmap.data[a+0]=o,this.bitmap.data[a+1]=i,this.bitmap.data[a+2]=n,this.bitmap.data[a+3]=r})).bitmap}(t)).data};e.default=function(){var t,e;return{mime:(0,n.default)({},u,["bmp"]),constants:{MIME_BMP:u,MIME_X_MS_BMP:l},decoders:(t={},(0,n.default)(t,u,f),(0,n.default)(t,l,f),t),encoders:(e={},(0,n.default)(e,u,s),(0,n.default)(e,l,s),e)}},t.exports=e.default},88348:(t,e,a)=>{var r=a(6305),n=a(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l.default,a={hasAlpha:{},encoders:{},decoders:{},class:{},constants:{}};function r(t){Object.entries(t).forEach((function(t){var e=(0,u.default)(t,2),r=e[0],n=e[1];a[r]=s({},a[r],{},n)}))}return t.types&&(t.types.forEach((function(t){var e=t();Array.isArray(e.mime)?l.addType.apply(void 0,(0,i.default)(e.mime)):Object.entries(e.mime).forEach((function(t){return l.addType.apply(void 0,(0,i.default)(t))})),delete e.mime,r(e)})),e.decoders=s({},e.decoders,{},a.decoders),e.encoders=s({},e.encoders,{},a.encoders),e.hasAlpha=s({},e.hasAlpha,{},a.hasAlpha)),t.plugins&&t.plugins.forEach((function(t){var e=t(l.jimpEvChange)||{};e.class||e.constants?r(e):r({class:e})})),(0,l.addJimpMethods)(a.class,e),(0,l.addConstants)(a.constants,e),l.default};var i=n(a(41132)),o=n(a(43693)),u=n(a(85715)),l=r(a(23832));function f(t,e){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),a.push.apply(a,r)}return a}function s(t){for(var e=1;e<arguments.length;e++){var a=null!=arguments[e]?arguments[e]:{};e%2?f(a,!0).forEach((function(e){(0,o.default)(t,e,a[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):f(a).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(a,e))}))}return t}t.exports=e.default},96890:(t,e,a)=>{var r=a(2613);Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var n=r(a(88348)),i=r(a(72314)),o=r(a(75953)),u=(0,n.default)({types:[i.default],plugins:[o.default]});e.default=u,t.exports=e.default},97494:(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;e.default=function(t,e){for(var a=arguments.length,r=new Array(a>2?a-2:0),n=2;n<a;n++)r[n-2]=arguments[n];return new Promise((function(a,n){r.push((function(t,e){t&&n(t),a(e)})),t.bind(e).apply(void 0,r)}))},t.exports=e.default}}]);
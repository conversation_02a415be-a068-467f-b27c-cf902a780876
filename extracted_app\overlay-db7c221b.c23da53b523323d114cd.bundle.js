"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2888,5975],{"shared/cheats/resources/elements/mod-timer":(e,t,i)=>{i.r(t),i.d(t,{ModTimer:()=>o});var s=i(15215),a=i("aurelia-event-aggregator"),r=i("aurelia-framework"),n=i(20057);let o=class{#e;constructor(e,t){this.ea=e,this.contentOnly=!1,this.cheat=null,this.latency=0,this.disabled=!1,this.activeIntervals={},this.loopPhaseStates={},this.PRESET_TIMERS=[1,5,10,30],this.TIMER_TABS=["off","on","loop"],this.selectedTimerTab=this.TIMER_TABS[0],this.timerDisplay=null,this.timerInterval=null,this.startTimer=0,this.endTimer=0,this.isOpen=!1,this.tooltip=null,this.#e=t}attached(){this.updateTimerInterval()}detached(){this.clearTimerInterval()}cheatStatesChanged(e,t){!this.cheat||e?.[this.cheat.uuid]?.timer?.timestamp?this.cheat&&e?.[this.cheat.uuid]?.timer?.timestamp!==t?.[this.cheat.uuid]?.timer?.timestamp&&(this.timerInterval?this.updateTimer():this.updateTimerInterval()):this.clearTimerInterval()}modTimersChanged(e,t){if(!this.trainer?.gameId||!this.cheat)return;const i=e?.[this.trainer.gameId]?.[this.cheat.uuid],s=t?.[this.trainer.gameId]?.[this.cheat.uuid];i?.timestamp?i?.timestamp!==s?.timestamp&&(this.timerInterval?this.updateTimer():this.updateTimerInterval()):this.clearTimerInterval()}disabledChanged(e){e&&this.clearTimerInterval()}timerIntervalChanged(e){e&&this.disabled&&this.clearTimerInterval()}get hasActiveTimer(){return!!(this.cheat&&(this.trainer?.gameId&&this.modTimers?.[this.trainer.gameId]?.[this.cheat.uuid]?.timestamp||this.cheatStates?.[this.cheat.uuid]?.timer?.timestamp))}get activeTimer(){let e={duration:0,start:0,end:0,type:""};if(this.cheat){const t=this.trainer?.gameId?this.modTimers?.[this.trainer.gameId]?.[this.cheat.uuid]:this.cheatStates?.[this.cheat.uuid]?.timer;if(t){const{duration:i,start:s,end:a,type:r}=t;e={duration:i??0,start:s??0,end:a??0,type:r??""}}}return e}get startTimerText(){const e=this.#e.getValue("trainer_cheats_list.minute");return this.startTimer?`${this.startTimer}${e}`:"..."}get endTimerText(){const e=this.#e.getValue("trainer_cheats_list.minute");return this.endTimer?`${this.endTimer}${e}`:"..."}updateTimerInterval(){if(this.timerInterval||this.clearTimerInterval(),this.hasActiveTimer)if(this.timerInterval)this.updateTimer();else{if(!this.cheat)return;const e=window.setInterval((()=>{this.updateTimer(),this.ea.publish("timer-tick")}),1e3);this.timerInterval=e,this.eaParent?.publish("timer-update",{cheatId:this.cheat.uuid,activeInterval:e})}}clearTimerInterval(){this.cheat&&(this.timerInterval&&(window.clearInterval(this.timerInterval),this.timerInterval=null,this.eaParent?.publish("timer-update",{cheatId:this.cheat.uuid,activeInterval:this.timerInterval,clear:!0})),this.timerDisplay=null)}updateTimer(){if(!this.cheat)return;const e=Date.now(),t=(this.modTimers&&this.trainer?.gameId&&this.cheat?this.modTimers?.[this.trainer.gameId]?.[this.cheat.uuid]:this.cheatStates?.[this.cheat.uuid]?.timer)??null;if(!t?.timestamp)return void this.clearTimerInterval();const i=this.calculateRemainingTime(t,e);i<=0?this.handleTimerExpired(t):this.updateTimerDisplay(i)}calculateRemainingTime(e,t){if(!this.cheat)return 0;const i=t+(this.latency||0);if("loop"===e.type){const t=this.loopPhaseStates[this.cheat.uuid]?e.start:e.end;return(e.timestamp?new Date(e.timestamp).getTime():0)+60*(t??0)*1e3-i}return(e.timestamp?new Date(e.timestamp).getTime()+60*(e.duration??0)*1e3:0)-i}handleTimerExpired(e){if(this.clearTimerInterval(),this.cheat)if("loop"===e.type){const t=!(void 0===this.loopPhaseStates[this.cheat.uuid]||this.loopPhaseStates[this.cheat.uuid]);if(this.eaParent?.publish("timer-update",{cheatId:this.cheat.uuid,loopPhase:t}),"pinned"!==this.category){const i=(t?e.start:e.end)??0,s=(t?e.end:e.start)??0;this.setTimerDuration(0),this.onTimerExpired({cheat:this.cheat,loopSequence:t?2:1}),this.setTimerDuration(i,!1,i,s)}}else"pinned"!==this.category?(this.setTimerDuration(0),this.onTimerExpired({cheat:this.cheat})):this.clearTimerInterval()}updateTimerDisplay(e){const t=Math.round(e/1e3),i=Math.floor(t/60),s=t%60;this.timerDisplay=`${i}:${s.toString().padStart(2,"0")}`}handleTimerClick(e){e.preventDefault(),e.stopPropagation(),this.timerDisplay?this.cancelTimer():this.isOpen=!this.isOpen}cancelTimer(){if(!this.cheat)return;this.startTimer=0,this.endTimer=0;const e=(this.modTimers&&this.trainer?.gameId?this.modTimers?.[this.trainer.gameId]?.[this.cheat.uuid]:this.cheatStates?.[this.cheat.uuid]?.timer)??null;this.onTimerChange({data:{modId:this.cheat.uuid,modType:this.cheat.type,timestamp:new Date,type:e?.type||"on",cancel:!0,duration:0,durationLoop:"loop"===e?.type?0:void 0},firstCall:!0}),this.clearTimerInterval(),this.tooltip?.hide()}setTimerDuration(e,t,i,s){if(!this.cheat)return;let a,r=e;if(i&&s){t&&this.eaParent?.publish("timer-update",{cheatId:this.cheat.uuid,loopPhase:!0});const e=this.loopPhaseStates[this.cheat.uuid];r=e?i:s,a=e?s:i}const n=(this.modTimers&&this.trainer?.gameId?this.modTimers?.[this.trainer.gameId]?.[this.cheat.uuid]:this.cheatStates?.[this.cheat.uuid]?.timer)??null,o=t?this.selectedTimerTab:n?.type||(i&&s?"loop":this.selectedTimerTab),d={modId:this.cheat.uuid,modType:this.cheat.type,timestamp:new Date,type:o,cancel:!1,duration:r,durationLoop:a};try{this.onTimerChange({data:d,firstCall:t})}catch{}this.tooltip?.hide()}};(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Boolean)],o.prototype,"contentOnly",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Object)],o.prototype,"trainer",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Object)],o.prototype,"cheat",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",String)],o.prototype,"category",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Object)],o.prototype,"modTimerMessagesDismissed",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Function)],o.prototype,"onTimerChange",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Function)],o.prototype,"onTimerExpired",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Function)],o.prototype,"handleDismissMessage",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Object)],o.prototype,"modTimers",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Object)],o.prototype,"cheatStates",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Number)],o.prototype,"latency",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Boolean)],o.prototype,"isPro",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Boolean)],o.prototype,"isOverlay",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Boolean)],o.prototype,"disabled",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",a.EventAggregator)],o.prototype,"eaParent",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Object)],o.prototype,"activeIntervals",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Object)],o.prototype,"loopPhaseStates",void 0),(0,s.Cg)([r.observable,(0,s.Sn)("design:type",Object)],o.prototype,"timerInterval",void 0),(0,s.Cg)([(0,r.computedFrom)("cheatStates","modTimers","trainer.gameId","activeIntervals"),(0,s.Sn)("design:type",Boolean),(0,s.Sn)("design:paramtypes",[])],o.prototype,"hasActiveTimer",null),(0,s.Cg)([(0,r.computedFrom)("hasActiveTimer"),(0,s.Sn)("design:type",Object),(0,s.Sn)("design:paramtypes",[])],o.prototype,"activeTimer",null),(0,s.Cg)([(0,r.computedFrom)("startTimer"),(0,s.Sn)("design:type",String),(0,s.Sn)("design:paramtypes",[])],o.prototype,"startTimerText",null),(0,s.Cg)([(0,r.computedFrom)("endTimer"),(0,s.Sn)("design:type",String),(0,s.Sn)("design:paramtypes",[])],o.prototype,"endTimerText",null),o=(0,s.Cg)([(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[a.EventAggregator,n.F2])],o)},"shared/cheats/resources/elements/mod-timer-content.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});const s='<template> <require from="../../../resources/elements/pro-badge"></require> <require from="../../../resources/elements/tabs"></require> <require from="../../../resources/elements/tab"></require> <div class="mod-timer-tooltip"> <div class="mod-timer-tooltip-controls"> <tabs full-width="true"> <tab repeat.for="tab of TIMER_TABS" active.bind="selectedTimerTab === tab" click.delegate="selectedTimerTab = tab" full-width="true"> <span if.bind="tab === \'loop\'" class="icon icon-loop">repeat</span> <span>${\'trainer_cheats_list.\' + tab | i18n}</span> </tab> </tabs> </div> <div class="mod-timer-tooltip-content-container"> <div if.bind="!isPro" class="pro-overlay" pro-cta="trigger: unlock_mod_timers_tooltip"> <span class="unlock-mod-timers-text">${\'trainer_cheats_list.unlock_mod_timers\' | i18n}<pro-badge class="small"></pro-badge></span> <span class="join-now-text">${\'trainer_cheats_list.join_now\' | i18n} <i class="icon icon-arrow-forward">arrow_forward</i> </span> </div> <div class="mod-timer-tooltip-message" if.bind="!modTimerMessagesDismissed[selectedTimerTab]"> ${\'trainer_cheats_list.mod_timer_message_\' + selectedTimerTab | i18n} <span class="dismiss-button" click.delegate="handleDismissMessage({ timerType: selectedTimerTab })">${\'trainer_cheats_list.dismiss\' | i18n}</span> </div> <div if.bind="selectedTimerTab !== \'loop\'" class="mod-timer-tooltip-presets"> <button repeat.for="presetTimer of PRESET_TIMERS" class="preset-timer-button ${activeTimer.duration === presetTimer && activeTimer.type === selectedTimerTab ? \'active\' : \'\'}" click.delegate="setTimerDuration(presetTimer, true)"> ${presetTimer}${\'trainer_cheats_list.minute\' | i18n} </button> </div> <template if.bind="selectedTimerTab === \'loop\'"> <div class="mod-timer-tooltip-loop-label">${\'trainer_cheats_list.on_after\' | i18n}</div> <div class="mod-timer-tooltip-presets"> <button repeat.for="presetTimer of PRESET_TIMERS" class="preset-timer-button ${activeTimer.start === presetTimer && activeTimer.type === selectedTimerTab ? \'active\' : \'\'}" click.delegate="startTimer = presetTimer"> ${presetTimer}${\'trainer_cheats_list.minute\' | i18n} </button> </div> <div class="mod-timer-tooltip-loop-label">${\'trainer_cheats_list.off_after\' | i18n}</div> <div class="mod-timer-tooltip-presets"> <button repeat.for="presetTimer of PRESET_TIMERS" class="preset-timer-button ${activeTimer.end === presetTimer && activeTimer.type === selectedTimerTab ? \'active\' : \'\'}" click.delegate="endTimer = presetTimer"> ${presetTimer}${\'trainer_cheats_list.minute\' | i18n} </button> </div> <hr> <div class="mod-timer-tooltip-loop-selected-container"> <i class="icon icon-loop">repeat</i> <div class="mod-timer-tooltip-loop-start-time ${startTimer ? \'active\' : \'\'}">${startTimerText}</div> <span class="mod-timer-tooltip-loop-selected-label">${\'trainer_cheats_list.on\' | i18n}</span> <div class="mod-timer-tooltip-loop-end-time ${endTimer ? \'active\' : \'\'}">${endTimerText}</div> <span class="mod-timer-tooltip-loop-selected-label">${\'trainer_cheats_list.off\' | i18n}</span> </div> <button class="mod-timer-tooltip-loop-start-button" disabled.bind="!startTimer || !endTimer" click.delegate="setTimerDuration(0, true, startTimer, endTimer)"> ${\'trainer_cheats_list.start_loop\' | i18n} </button> </template> </div> </div> </template> '},"shared/cheats/resources/elements/mod-timer.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});const s='<template> <div if.bind="contentOnly" class="mod-timer"> <compose view="./mod-timer-content.html"></compose> </div> <div else if.bind="!isRemote || (isRemote && timerDisplay)" class="mod-timer"> <div class="mod-timer-button-container ${disabled ? \'disabled\' : \'\'}" data-tooltip-trigger-for.bind="\'mod-timer-\' + cheat.uuid + \'-\' + category"> <button click.delegate="handleTimerClick($event)" disabled.bind="disabled" class="mod-timer-button ${isOpen ? \'active\' : \'\'} ${timerDisplay ? \'timer-active\' : \'\'}"> <span if.bind="timerDisplay"> <i if.bind="activeTimer.type === \'loop\'" class="icon icon-loop">repeat</i> <span class="timer-display">${timerDisplay}</span> <span class="timer-cancel">${\'trainer_cheats_list.cancel\' | i18n}</span> </span> <i else class="mod-timer-button-icon timer"></i> <span if.bind="!timerDisplay" class="mod-timer-button-text">${\'trainer_cheats_list.pro\' | i18n}</span> </button> </div> <wm-tooltip view-model.ref="tooltip" if.bind="!isRemote" tooltip-id.bind="\'mod-timer-\' + cheat.uuid + \'-\' + category" placement="top" trigger-method.bind="timerDisplay ? \'hover\' : \'click\'" style-variant="no-arrow" on-hide.call="isOpen = false"> <div class="mod-timer-tooltip-wrapper" slot="content"> <compose view="./mod-timer-content.html"></compose> </div> </wm-tooltip> </div> </template> '}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[96],{17724:(e,t,s)=>{s.d(t,{O4:()=>c,hD:()=>o,hb:()=>n,i7:()=>i,o3:()=>a});const r=new Set([502,503,504,520]);function n(e){return e instanceof a&&r.has(e.response.status)}class i{response(e){return e.status>=400?async function(e){throw await o.fromResponse(e)??new a(`API request failed with status ${e.status}.`,e)}(e):e}}class a extends Error{constructor(e,t){super(e),Object.setPrototypeOf(this,a.prototype),this.response=t}}class o extends a{static async fromResponse(e){const t=await e.json().catch((()=>null));return function(e){return null!==e&&"object"==typeof e&&"number"==typeof e.status&&"string"==typeof e.code&&"string"==typeof e.message&&["object","undefined"].includes(typeof e.data)}(t)?new o(e,t):null}constructor(e,t){super(t.message,e),Object.setPrototypeOf(this,o.prototype),this.status=t.status,this.code=t.code,this.data=t.data}get isMaintenanceError(){return 503===this.status}}var c;!function(e){e.BadRequest="bad-request",e.Unauthorized="unauthorized",e.AccessDenied="access-denied",e.NotFound="not-found",e.MethodNotAllowed="method-not-allowed",e.NotAcceptable="not-acceptable",e.Conflict="conflict",e.Gone="gone",e.UnsupportedMediaType="unsupported-media-type",e.UnprocessableEntity="unprocessable-entity",e.ResourceLocked="resource-locked",e.RateLimited="rate-limited",e.InternalError="internal-error",e.ServiceUnavailable="service-unavailable",e.ValidationError="validation-error",e.EmailVerificationRequired="email-verification-required",e.PaymentDeclined="payment-declined",e.UserAlreadySubscribed="user-already-subscribed",e.MaxGamesFollowed="max-games-followed"}(c||(c={}))},28035:(e,t,s)=>{s.d(t,{S:()=>i});var r=s(92126),n=s(83260);class i{#e;#t;constructor(e,t){this.#e=(new r.Qq).configure((t=>t.withBaseUrl(e))),this.#t=btoa(JSON.stringify(t))}get baseUrl(){return this.#e.baseUrl}addInterceptor(e){this.#e.interceptors.push(e)}get(e,t){return this.fetch({method:"GET",endpoint:e,query:t})}post(e,t){return this.fetch({method:"POST",endpoint:e,body:t})}put(e,t){return this.fetch({method:"PUT",endpoint:e,body:t})}patch(e,t){return this.fetch({method:"PATCH",endpoint:e,body:t})}delete(e){return this.fetch({method:"DELETE",endpoint:e})}async fetch(e){const t={Accept:"application/json","X-Super-Properties":this.#t},s={method:e.method,headers:t};void 0!==e.body&&(e.body instanceof FormData?s.body=e.body:(s.headers||(s.headers={}),s.headers["Content-Type"]="application/json",s.body=JSON.stringify(e.body)));let r=e.endpoint;"object"==typeof e.query&&null!==e.query&&(r+=`?${(0,n.Go)(e.query)}`);const i=this.#e.buildRequest(r,s);e.name&&e.collectMetrics&&(i.name=e.name);const a=await this.#e.fetch(i);return await this.#s(a)}async#s(e){return"application/json"===e.headers.get("Content-Type")?await e.json():await e.text()}}},41548:(e,t,s)=>{s.d(t,{k:()=>a});var r=s(15215),n=s("aurelia-framework"),i=s(84551);let a=class{#r;constructor(e){this.#r=e}request(e){return e.name?(e.start_time=Date.now(),e):e}response(e,t){return t?.name&&t.start_time&&this.#r.report({endpoint:t.name,method:t.method,responseTime:Date.now()-t.start_time}),e}};a=(0,r.Cg)([(0,n.autoinject)(),(0,r.Sn)("design:paramtypes",[i.Y])],a)},57503:(e,t,s)=>{s.d(t,{Z:()=>a});var r=s(15215),n=s("aurelia-framework"),i=s(60321);let a=class{#n;#i;#a;#o;#c;#h;#l;constructor(e){this.#i=!1,this.#a=null,this.#o=null,this.#n=e}get authorized(){return!!this.#o}setTokenRefreshedHandler(e){this.#c=e}setDeauthorizedHandler(e){this.#h=e}setAccessTokenResponse(e){"object"==typeof e&&(this.#o=e)}async forceRefreshAccessToken(){this.#i=!0,await this.#d()}async request(e){e.bodyUsed||e.cloned||!["POST","PUT"].includes(e.method)||(e.cloned=e.clone());const t=await this.#d();return null!==t&&e.headers.set("Authorization",`Bearer ${t.accessToken}`),e}async responseError(e,t,s){if("object"==typeof e&&null!==e&&401===e.status)if(this.#o){if(this.#o.refreshToken)return this.#i=!0,await s.fetch(t.cloned??t.clone())}else if(await this.#u(),this.#o)return await s.fetch(t.cloned??t.clone());throw e}async#d(){if(null===this.#o){if(!this.initialAccessTokenHandler)return null;if(await this.initialAccessTokenHandler(),null===this.#o)return null}if(this.#a)await this.#a;else if(this.#i||this.#p(this.#o)){this.#a=this.#f(this.#o);try{await this.#a}finally{this.#a=null}}return this.#o}#p(e){return 0!==e.expiresAt&&e.expiresAt<Date.now()/1e3+150}async#f(e){if(e.refreshToken)try{this.#o=await this.#n.requestAccessToken({grant_type:"refresh_token",refresh_token:e.refreshToken}),this.#i=!1,await this.#c(this.#o)}catch(e){throw[400,401,403].includes(e.status)&&await this.#u(),e}else await this.#u()}async#u(){return this.#i=!1,this.#o=null,await this.#h()}set initialAccessTokenHandler(e){this.#l=e}get initialAccessTokenHandler(){return this.#l}};a=(0,r.Cg)([(0,n.autoinject)(),(0,r.Sn)("design:paramtypes",[i.Q])],a)},60321:(e,t,s)=>{s.d(t,{Q:()=>i});var r=s(92126),n=s(83260);class i{#e;#y;constructor(e,t){this.#e=(new r.Qq).configure((t=>{t.withBaseUrl(e)})),this.#y=t}async revokeAccessToken(e){await this.#e.fetch("/auth/token",{method:"DELETE",headers:{Authorization:`Bearer ${e}`}})}async requestAccessToken(e){const t=await this.#e.fetch("/auth/token",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:(0,n.Go)({...e,client_id:this.#y})});if(t.status>=300)throw t;const s=await t.json();if("bearer"!==s.token_type.toLocaleLowerCase())throw new Error(`Expected bearer token type, got ${s.token_type}.`);return{accessToken:s.access_token,refreshToken:s.refresh_token,userId:s.user_id,expiresAt:Math.floor(Date.now()/1e3)+s.expires_in,clientParams:s.client_params}}}}}]);
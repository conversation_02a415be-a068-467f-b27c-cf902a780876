{"tray_notification": {"wemod_minimized": "WeMod est dans une fenêtre réduite", "wemod_is_running_in_the_tray": "WeMod est toujours actif dans la barre système. Faites un clic droit sur l’icône pour fermer l’application."}, "app_update_toast": {"update_failed": "Une erreur s’est produite lors de l’installation d’une mise à jour de l’application.", "retry": "<PERSON><PERSON><PERSON><PERSON>", "update_available": "Une mise à jour de WeMod est disponible.", "restart_now": "Redémarrer maintenant", "restart_later": "Redémarrer plus tard", "wemod_will_auto_restart": "WeMod redémarrera bientôt automatiquement pour appliquer une mise à jour.", "changelog": "Journal des modifications"}, "beta_toast": {"running_beta_version": "Vous utilisez une version bêta de WeMod ! Pensez à signaler les bugs sur le [forum](https://community.wemod.com).", "visit_forum": "Accéder au forum"}, "app_header": {"home": "Accueil", "explore": "Explorer", "upcoming": "À venir", "my_games": "<PERSON><PERSON> jeux", "maps": "<PERSON><PERSON>", "my_videos": "<PERSON>s vid<PERSON>"}, "app_sidebar_now_playing": {"now_playing": "Jeu en cours"}, "auth": {"welcome_back": "Vous êtes de retour !", "log_in": "Se connecter", "email_or_username": "Adresse e-mail ou nom d’utilisateur", "password": "Mot de passe", "forgot_password": "Mot de passe oublié ?", "create_an_account": "<PERSON><PERSON><PERSON> un compte", "invalid_info": "L’identifiant et le mot de passe sont incorrects.", "new_user": "Bienvenue sur WeMod", "already_have_an_account": "Vous avez déjà un compte ?", "log_in_now": "Se connecter", "create_account": "<PERSON><PERSON><PERSON> un compte", "email_address": "<PERSON><PERSON> votre adresse e-mail", "email_taken": "Cette adresse e-mail est déjà utilisée par un autre compte.", "invalid_email": "Adresse e-mail non valide", "there_was_a_problem": "Une erreur s’est produite lors de la création de votre compte", "invalid": "L’adresse e-mail que vous avez saisie n’est pas valide. Veuillez vérifier votre saisie, puis rées<PERSON>ez.", "misspelled_email_$email": "Votre adresse e-mail (**$email**) a peut-être été mal orthographiée. Veuillez vérifier votre saisie.", "looks_good": "Tout semble correct", "cancel": "Annuler", "review_terms": "En cliquant sur « Continuer », j’accepte les [conditions générales](website://terms) et la [politique de confidentialité](website://privacy).", "new_to_wemod": "Vous utilisez <PERSON> pour la première fois ?", "get_mods_for": "Essayez d’utiliser des mods pour :", "continue": "<PERSON><PERSON><PERSON>", "back": "Retour", "unlock_$x_mods_for": "Déverrouillez $x mods pour :", "unlock_one_mod_for": "Déverrouillez un mod pour :", "unlock_mods_for": "Déverrouillez des mods pour :"}, "url_handler": {"failed_to_open_$url": "Impossible d’ouvrir le lien suivant : $url", "copy_url": "Copier l’URL"}, "uri_handler": {"unsupported_message": "Le lien que vous avez suivi n’est pas pris en charge par cette version de WeMod. Veuillez mettre à jour votre application, puis réessayez."}, "game": {"discussion": "Discussion", "required_reading": "À lire avant de jouer", "your_mods_may_not_work": "Si vous ne suivez pas ces instructions, vos mods risquent de ne pas fonctionner.", "some_mods_may_not_work_update": "Quelques mods sont en cours de mise à jour et pourraient ne pas fonctionner comme prévu. La plupart des mods fonctionnent normalement.", "learn_more": "En savoir plus", "read_notes": "Lire les notes", "got_it": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "play": "<PERSON><PERSON>", "cheats_might_not_work_dialog": "Quelques mods sont en cours de mise à jour et pourraient ne pas fonctionner comme prévu. La plupart des mods fonctionnent normalement.", "cant_connect_to_wemod": "Connexion à WeMod impossible.", "retry": "<PERSON><PERSON><PERSON><PERSON>", "exit_confirm_dialog": "Les mods sont encore en cours d’exécution. Voulez-vous vraiment quitter ?", "these_mods_have_been_retired": "Ces mods ont été abandonnés en raison de leur incompatibilité avec les nouvelles versions du jeu. Il est possible qu’ils fonctionnent encore avec des versions antérieures, mais nous ne prenons plus ce jeu en charge.", "incompatible_and_retired": "Incompatibles et abandonnés"}, "trainer_meta": {"$players_members_play_this": "**$players** membres y jouent", "$players_players": "$players joueurs", "played_by": "<PERSON><PERSON> par", "created_by": "C<PERSON><PERSON> par", "have_a_question": "Vous avez une question ?", "gameplay_video": "Vidéo de <PERSON>", "last_updated_$date": "Dernière mise à jour : **$date**", "overview_of_$game_mods": "Vue d’ensemble des mods disponibles pour $game", "video": "Vidéo", "version": "Version :", "game_version_copied": "Version du jeu copiée dans le presse-papiers."}, "update_pending": {"update_pending": "Certains mods sont en cours de mise à jour", "requires_testing": "**Tests** nécessaires", "$game_for_$platform_is_$position_in_the_queue": "**$game** pour $platform est actuellement en position **n° $position** dans la file d’attente des jeux à venir.\nLe moment de la mise à jour dépendra de sa position dans la file d’attente, mais aussi de la quantité de travail nécessaire pour créer et tester les mods pour ce jeu et pour ceux avant lui dans la file.", "$game_for_$platform_is_$position_in_the_queue_modal": "**$game** pour $platform est actuellement en position **n° $position** dans la file d’attente des jeux à venir. Les mods sont susceptibles de ne pas fonctionner comme prévu jusqu’à leur mise à jour.\n**Remarque :** le moment de la mise à jour dépendra de sa position dans la file d’attente, mais aussi de la quantité de travail nécessaire pour créer et tester les mods pour ce jeu et pour ceux avant lui dans la file.", "$game_was_updated_on_$platform_and_needs_to_be_tested": "**$game** a été mis à jour pour $platform et a besoin d’être testé. Une mise à jour des mods peut être nécessaire.", "cant_wait_boost_it": "Vous avez hâte d’essayer des mods pour ce jeu ? Boostez-le !", "okay": "<PERSON><PERSON><PERSON>"}, "titles": {"explore": "Explorer", "recently_played": "<PERSON><PERSON>", "most_popular": "Les plus populaires", "browse_by_genre": "Parcourir par genre", "all": "Tous", "new_and_updated_games": "Nouveautés et mises à jour récentes", "trending_free_games_to_install": "Jeux gratuits à la une", "see_all": "<PERSON><PERSON> afficher", "view_all": "<PERSON><PERSON> afficher", "upcoming_games_and_updates": "Jeux et mises à jour à venir", "see_upcoming_games": "Voir les jeux à venir", "featured_games": "Jeux à la une", "no_results": "Aucun résultat", "no_results_advice": "Essayez de modifier votre recherche.", "back": "Retour à l’exploration", "search": "<PERSON><PERSON><PERSON>", "$genre_games": "$genre"}, "dashboard": {"announcements": "Annonces", "objectives": "Objectifs", "most_popular": "Les plus populaires", "see_all": "<PERSON><PERSON> afficher", "my_games": "<PERSON><PERSON> jeux", "free_games": "Jeux gratuits", "my_games_coaching_tip_header": "Nous avons trouvé vos jeux !", "my_games_coaching_tip_message": "Bonne nouvelle ! Des mods sont déjà disponibles pour les jeux installés répertoriés ci-dessous. Sélectionnez un titre pour voir tous les mods disponibles.", "free_games_coaching_tip_header": "Jeux gratuits, mods gratuits", "free_games_coaching_tip_message": "Essayez l’un de ces jeux gratuits populaires pour essayer nos milliers de mods.", "see_upcoming_games": "Voir les jeux à venir"}, "app_rating_dialog": {"how_are_you_liking_wemod_so_far": "Que pensez-vous de We<PERSON>od jusqu’à présent ?", "thanks_for_the_feedback": "Merci pour votre retour !", "what_can_we_do": "Que pouvons-nous améliorer pour obtenir 5 étoiles ?", "glad_youre_enjoying_the_app": "Super, nous sommes heureux que vous aimiez l’application !", "review_on_$x": "Pouvez-vous nous laisser un commentaire positif sur $service ?", "submit": "Envoyer", "sure": "Bien sûr !", "no_thanks": "Non merci"}, "failed_payment_dialog": {"your_pro_payment_is_due": "Il est temps de payer votre abonnement *Pro*", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_monthly": "Cliquez ci-dessous pour ajouter ou mettre à jour votre moyen de paiement pour votre abonnement de $x/mois.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_daily": "Cliquez ci-dessous pour ajouter ou mettre à jour votre moyen de paiement pour votre abonnement de $x/jour.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_weekly": "Cliquez ci-dessous pour ajouter ou mettre à jour votre moyen de paiement pour votre abonnement de $x/semaine.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_quarterly": "Cliquez ci-dessous pour ajouter ou mettre à jour votre moyen de paiement pour votre abonnement de $x/trimestre.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_yearly": "Cliquez ci-dessous pour ajouter ou mettre à jour votre moyen de paiement pour votre abonnement de $x/an.", "add_payment": "Ajouter un moyen de paiement", "without_payment_youll_lose_these_benefits": "En l’absence de paiement, vous perdrez votre abonnement Pro et **ces avantages**"}, "reactivate_pro": {"youre_a_pro_again": "Vous êtes de nouveau un véritable Pro. Merci pour votre soutien !"}, "features": {"interactive_controls": "*Contrôles* interactifs", "interactive_controls_description": "Activez les mods avec *des contrôles simples*.", "interactive_controls_info": "Si vous avez un deuxième écran, les contrôles interactifs simplifient l’activation des mods.", "remote_app": "*Application* Remote", "remote_app_description": "Oubliez les raccourcis.\nUtilisez *votre téléphone* pour contrôler les mods.", "remote_app_info": "Gardez un œil sur les mods qui sont activés et contrôlez-les facilement et avec précision. L’application Remote se connecte sans problème à la version de bureau de WeMod à l’aide d’un code d’authentification.", "save_cheats": "*Enregistrer* les mods", "save_cheats_description": "Plus besoin de configurer les mods à chaque fois que vous lancez un jeu. Paramétrez-les une seule fois, puis ne vous en inquiétez plus.", "save_cheats_info": "Revenez facilement en jeu en appliquant automatiquement les derniers paramètres de vos mods.", "overlay": "*Overlay* en jeu", "overlay_description": "Contrôlez vos mods *sans quitter votre partie*.", "overlay_info": "L’overlay vous permet d’afficher les options en les superposant à votre écran de jeu."}, "payment_method": {"credit_card": "Carte bancaire", "paypal": "PayPal", "amazon_pay": "Amazon Pay", "pay": "Pay", "alipay": "Alipay", "direct_debit": "Prélèvement bancaire", "kr_market": "<PERSON><PERSON>", "kakao_pay": "Kakao Pay", "kr_card": "<PERSON><PERSON>", "naver_pay": "Naver Pay", "ending_in_digits": "Se terminant par $digits", "expiration_$month_$year": "Date d’expiration : $month/$year"}, "settings_dialog": {"view_updates": "Voir les mises à jour", "app_version_copied_toast": "Version de l’application copiée dans le presse-papiers.", "settings": "Paramètres", "close_to_tray": "Rédu<PERSON> dans la barre système", "close_to_tray_description": "Lorsque vous fermez <PERSON>, l’application est réduite dans la barre système.", "language": "<PERSON><PERSON>", "language_description": "Permet de changer la langue de l’application.", "anonymous_reporting": "Collecte de données anonyme", "anonymous_reporting_description": "Enregistre certaines données affichées à l’écran et d’autres informations de manière anonyme pour aider à améliorer WeMod.", "release_channel": "Version", "release_channel_description": "Passez à la version Bêta pour tester les nouvelles fonctionnalités avant qu’elles ne soient rendues publiques.", "cheat_sounds": "Sons des mods", "cheat_sounds_description": "L’application lance un signal sonore lorsqu’un mod est activé.", "cheat_volume": "Volume des mods", "cheat_volume_description": "<PERSON><PERSON> de régler le volume pour les sons des mods.", "sound_pack": "Pack de sons", "sound_pack_description": "Choisissez un pack de sons pour l’activation des mods.", "theme": "Thème", "theme_description": "Modifiez l’apparence de l’application.", "general": "Général", "customization": "Personnalisation", "my_account": "Mon compte", "profile": "Profil", "billing": "Facturation", "password": "Mot de passe", "disable_hotkeys": "Désactiver les raccourcis", "disable_hotkeys_description": "Désactive la possibilité de contrôler les mods avec des raccourcis clavier.", "save_cheats": "Enregistrer les mods", "save_cheats_description": "Plus besoin de configurer les mods à chaque fois que vous lancez un jeu. Paramétrez-les une seule fois, puis ne vous en inquiétez plus.", "accessibility": "Accessibilité", "use_windows_high_contrast_mode": "Utiliser les thèmes contrastés de Windows", "use_windows_high_contrast_mode_description": "Autorise Windows à changer les couleurs de l’application pour améliorer le contraste.", "notifications": "Notifications", "followed_games": "<PERSON>ux suivis", "desktop_notifications": "Notifications de bureau", "desktop_notifications_description": "Active les notifications de bureau. Vous en recevrez dès lors qu’un jeu que vous suivez est mis à jour ou dès qu’il est pris en charge.", "disable_assistant": "Désactiver le Guide de jeu par IA", "disable_assistant_description": "Masquer le Guide de jeu par IA sur la page du jeu", "disable_auto_pins": "Désactiver les mods épinglés automatiquement", "disable_auto_pins_description": "Désactiver les mods épinglés automatiquement sur la page de jeu", "reduce_motion": "Rédu<PERSON> le mouvement", "reduce_motion_description": "Réduire les animations et les effets de la souris", "enable_overlay": "Activer le nouvel overlay WeMod", "enable_overlay_description": "Activez le nouvel overlay WeMod en jeu pour accéder aux mods et à d’autres fonctionnalités sans quitter votre partie. Remplace l’overlay Game Bar.", "overlay_hotkey": "Raccourci de l’overlay", "overlay_hotkey_description": "Sélectionnez le raccourci utilisé pour activer ou désactiver le nouvel overlay en jeu de WeMod.", "changelog": "Journal des modifications", "capture": "Vid<PERSON><PERSON>", "capture_quality_preset": "Qualité de la vidéo", "capture_quality_preset_description": "Sélectionnez la résolution et la fréquence d'images des vidéos de jeu enregistrées", "enable_capture": "<PERSON>r les vidéos de jeu", "enable_capture_description_windows_10": "Activez les vidéos de jeu pour enregistrer les moments forts de votre jeu.\n_Windows 10 peut afficher une bordure jaune lors de la capture de vidéos.\nEnvisagez de passer à Windows 11._", "enable_capture_description": "Activez les vidéos de jeu pour enregistrer les meilleurs moments de votre partie", "capture_highlight_length": "<PERSON><PERSON><PERSON> du meilleur moment", "capture_highlight_length_description": "Sélectionnez la durée des meilleurs moments de jeu", "capture_highlight_length_$seconds_seconds": "Dernières **$seconds** secondes", "capture_quality_preset_$resolution@$fps": "**$resolution** @$fpsfps", "capture_audio_device": "Audio", "capture_audio_device_description": "Capturez l'audio depuis le périphérique audio par défaut", "capture_audio_enabled": "Activé", "capture_audio_disabled": "Désactivé"}, "language_selector": {"automatic_$lang": "Automatique ($lang)"}, "remote_tooltip": {"click_to_use_the_wemod_remote": "Cliquez ici pour utiliser WeMod Remote", "connect_to_wemod_remote": "Connexion à WeMod Remote", "enter_this_pin_on_your_device_to_connect": "Saisissez ce code sur votre appareil pour vous connecter et contrôler les mods depuis votre téléphone.", "connected": "Connecté", "disconnect": "Déconnecter", "get_the_app": "Télécharger l’application mobile", "scan_the_qr_code_or_visit_the_site": "Scannez le code QR ou consultez [wemod.com/remote]($url) sur votre appareil mobile.", "reconnect": "Reconnecter", "force_disconnected_message": "Il semble que vous utilisiez WeMod sur un autre PC. Nous avons donc déconnecté ce PC de Remote.", "disconnect_remote_app": "Déconnecter l'application Remote"}, "user": {"go_pro": "Passer Pro", "support_wemod": "Soutenir WeMod"}, "sidebar_user": {"go_pro": "Passer _Pro_", "go_pro_collapsed": "Passer _Pro_", "support_wemod": "Soutenir _WeMod_"}, "custom_installation_selector": {"not_an_exe_toast": "Ceci n’est pas un fichier EXE", "select_your_game_exe": "Sélectionner le fichier EXE du jeu", "pick_this_game_exe": "Sélectionner le fichier EXE de ce jeu", "exe_files": "Fichiers EXE", "custom_game_exe": "Fichier .exe d’un jeu personnalisé", "add_game_exe": "A<PERSON><PERSON> le fichier .exe du jeu", "or_drag_the_file_here": "ou faites glisser le fichier ici", "custom_exe_info": "Si WeMod n’a pas automatiquement trouvé votre jeu, vous devrez ajouter manuellement le raccourci du jeu ou le fichier exécutable (.exe)."}, "feedback_dialog": {"how_was_playing_$game_with_wemod": "Avez-vous aimé jouer à $game avec WeMod ?", "say_thanks_to_the_developer": "Remerciez le développeur", "type_here": "Saisir un commentaire", "submit": "Envoyer", "allow_this_to_be_posted_publicly": "Autoriser la publication du commentaire", "did_you_read_the_notes": "<PERSON><PERSON>-vous lu les notes avant de lancer le jeu ?", "report_problem": "Signaler un problème", "cancel": "Annuler", "which_best_describes_experience": "Quelle option décrit le mieux votre expérience ?", "my_game_crashed": "Mon jeu a planté", "cheats_broken_or_confusing": "Mods non fonctionnels ou comportement étrange", "other_issue": "Autre problème", "where_were_you_in_the_game_when_it_crashed": "<PERSON><PERSON>-vous dans le jeu au moment du plantage ?", "main_menu_or_before": "<PERSON>u principal ou avant", "cutscene_or_loading_screen": "Cinématique ou écran de chargement", "in_game_playing": "En jeu", "in_game_menu_inventory": "En jeu, dans un menu ou l’inventaire", "other": "<PERSON><PERSON>", "any_extra_details_that_can_help_us": "Avez-vous des informations supplémentaires à nous transmettre qui pourraient nous aider ?", "please_describe_the_problem": "Veuillez décrire le problème que vous rencontrez avec ces mods.", "extra_details_example": "Exemple : « DLC de l’édition Gold installée, mais le jeu plante après l’écran de chargement du premier niveau lorsque la santé illimitée est activée. »", "cheat_log_will_be_sent": "La liste des mods utilisés sera jointe à votre signalement.", "submit_feedback": "Envoyer", "which_cheats_experienced_trouble": "Quels sont les mods qui ne fonctionnaient pas ?", "all_of_the_cheats": "Tous les mods", "additional_info_example": "Informations supplémentaires facultatives (par exemple « ne fonctionnait pas dans le combat de boss au niveau 2 »)", "please_describe_the_issue_in_detail": "Pouvez-vous décrire le problème en détail ?", "other_details_example": "Exemple : « rien ne semble fonctionner en mode Réaliste… »", "didnt_work_no_effect": "<PERSON>ien n’a fonctionn<PERSON>, aucun effet", "game_crash": "Le jeu a planté", "not_as_expected": "Le mod n’a pas fonctionné comme prévu", "optional_additional_info": "Informations supplémentaires facultatives (par exemple « ne fonctionnait pas dans le combat de boss au niveau 2 »)", "select_a_cheat": "Sélectionner un mod", "select_a_reason": "Sélectionner un motif", "select_a_location": "Sélectionner un emplacement", "allow_posting_publicly": "Autoriser la publication", "send": "Envoyer"}, "trainer_cheats_list": {"press_$x_above_to_get_started": "Cliquez sur « $x » ci-dessus pour commencer", "press_$x_to_use_mod": "Appuyez sur « $x » pour utiliser ce mod", "only_for_pro_members": "Uniquement pour les membres **PRO**.", "upgrade_now": "Mettre à niveau", "apply": "Appliquer", "button_set": "Définir", "button_add": "Ajouter", "category_cheats": "mods", "category_enemies": "ennemis", "category_game": "jeu", "category_inventory": "inventaire", "category_physics": "moteur physique", "category_player": "joueur", "category_stats": "caractéristiques", "category_teleport": "téléportation", "category_vehicles": "véhicules", "category_weapons": "armes", "category_challenge": "Mods de défi", "category_pinned": "<PERSON><PERSON><PERSON>", "cheat_instructions": "Instructions du mod", "pro": "Pro", "only_pro_members_can_use_beta_mods": "Seuls les membres **PRO** peuvent utiliser les mods en version Bêta.", "beta": "<PERSON><PERSON><PERSON>", "disabled_play_tooltip": "Certains mods nécessitent que le jeu soit en cours d’exécution, que ce soit pour des raisons de compatibilité ou de stabilité.", "disabled_add_or_install_tooltip": "<PERSON><PERSON><PERSON><PERSON> ou installez votre jeu pour en profiter avec des mods.", "broken_mod_hint": "Ce mod est en cours de mise à jour et est susceptible de ne pas fonctionner comme prévu.", "auto_pin_setting_toast": "Vous pouvez réinitialiser vos mods épinglés automatiquement dans les paramètres.", "pin": "<PERSON><PERSON><PERSON>", "unpin": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "off": "DÉSACTIVÉ", "on": "ACTIVÉ", "loop": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "start_loop": "<PERSON><PERSON><PERSON><PERSON> la boucle", "on_after": "ACTIVÉ après", "off_after": "DÉSACTIVÉ après", "mod_timer_message_on": "Le mod sélectionné s’activera après le délai indiqué.", "mod_timer_message_off": "Le mod sélectionné se désactivera après le délai indiqué.", "mod_timer_message_loop": "Le mod sélectionné alternera entre ACTIVÉ et DÉSACTIVÉ en fonction des délais choisis.", "minute": "min.", "dismiss": "<PERSON><PERSON><PERSON>", "unlock_mod_timers": "Déverrouiller les minuteries pour les mods", "join_now": "Rejoindre"}, "trainer_hotkey": {"not_set": "Non défini", "numpad_$x": "Pavé num. $x", "key_control": "Ctrl", "key_alt": "Alt", "key_insert": "Ins", "key_delete": "Suppr", "key_page_up": "PgPréc", "key_page_down": "PgSuiv", "key_end": "Fin", "key_home": "Accueil", "key_left": "G<PERSON><PERSON>", "key_up": "<PERSON><PERSON>", "key_right": "<PERSON><PERSON><PERSON>", "key_down": "Bas", "key_scroll_lock": "<PERSON><PERSON><PERSON><PERSON>.", "key_print_screen": "Impr. <PERSON><PERSON>ran", "key_pause": "Pause", "action_apply": "Appliquer", "action_toggle": "Activer/Désactiver", "action_decrease": "<PERSON><PERSON><PERSON><PERSON>", "action_increase": "Augmenter", "action_previous": "Précédent", "action_next": "Suivant", "hotkey_tooltip": "<PERSON>euillez appuyer sur la combinaison de touches."}, "trainer_launcher": {"newer_version_required": "Vous avez besoin d’une version plus ré<PERSON><PERSON> de WeMod pour utiliser ces mods.", "game_already_running": "Ces mods nécessitent que le jeu soit lancé par WeMod. Veuillez fermer votre jeu avant de cliquer sur Jouer.", "game_not_running": "Le jeu doit être lancé en dehors de WeMod avant de cliquer sur Jouer.", "elevation_denied": "WeMod ne peut pas accéder à votre jeu, car il est exécuté en tant qu’administrateur.", "cannot_download_cheats": "Nous rencontrons des difficultés pour télécharger les mods. Veuillez réessayer plus tard.", "cannot_find_dll": "Fichier DLL du trainer introuvable. Vérifiez que ce fichier a été généré.", "cheats_missing": "Les mods ne sont pas installés sur votre ordinateur. Veuillez vérifier que WeMod est autorisé par votre antivirus avant de continuer.", "close": "<PERSON><PERSON><PERSON>", "fix": "<PERSON><PERSON><PERSON><PERSON>", "auto_fix_av_failed": "Nous n’avons pas pu résoudre le problème automatiquement. Veuillez consulter votre antivirus pour en savoir plus.", "ok": "OK", "files_missing": "Les fichiers dont nous avons besoin pour activer les mods sont manquants sur votre ordinateur. Cela est généralement dû à un logiciel antivirus qui les considère par erreur comme étant malveillants.\n\n<PERSON>ur continuer, vous devrez peut-être désactiver temporairement votre antivirus après avoir réinstallé WeMod.", "reinstall_now": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trouble_starting_or_finding": "Nous avons des difficultés à lancer votre jeu ou à le trouver s’il est déjà ouvert.", "retry": "<PERSON><PERSON><PERSON><PERSON>", "x64_expected": "Ces mods ont été conçus pour la version 64 bits du jeu, or vous utilisez la version 32 bits.", "x86_expected": "Ces mods ont été conçus pour la version 32 bits du jeu, or vous utilisez la version 64 bits.", "trouble_loading_cheats": "Nous avons des difficultés à charger les mods dans votre jeu. <PERSON><PERSON><PERSON>z essayer de redémarrer le jeu, ou cliquez sur *Aide* pour en savoir plus.", "report_problem": "Signaler un problème", "preparation_failed": "Nous avons du mal à préparer le chargement des mods. Vérifiez que WeMod est autorisé dans votre pare-feu ou antivirus.", "trouble_running_cheats": "Nous avons du mal à exécuter les mods. Vérifiez que WeMod est autorisé dans votre pare-feu ou antivirus.", "activation_problem": "Une erreur s’est produite lors du chargement des mods. Il est possible que la version du jeu que vous utilisez ne soit pas compatible, ou que vous deviez réessayer à un autre moment du jeu.", "activation_prevented": "Une erreur s’est produite lors du chargement des mods. Si vous n’avez pas fermé le jeu, il est possible que la version que vous utilisez ne soit pas compatible ou que vous deviez autoriser WeMod dans votre pare-feu ou antivirus.", "launch_outside_wemod": "Lancer en de<PERSON><PERSON>"}, "trainer_play_button": {"loading_mods": "Chargement des mods…", "playing": "<PERSON><PERSON>", "play": "<PERSON><PERSON>", "install_for_free": "Installer gratuitement", "install": "Installer", "play_coaching_tip_header": "C’est parti !", "play_coaching_tip_message": "Vous avez hâte d’essayer vos nouveaux mods ? Lancez le jeu directement à l’aide de WeMod.", "install_coaching_tip_install_game_header": "Installer ou rechercher un jeu", "install_coaching_tip_message": "Cliquez sur « Ajouter le jeu » pour lancer le téléchargement sur la plateforme de votre choix\nDéjà installé ? Utilisez le menu « Ajouter le jeu » pour trouver et ajouter le fichier exécutable.", "stop_playing": "<PERSON><PERSON><PERSON><PERSON>", "add_game": "<PERSON><PERSON><PERSON> le jeu", "start": "Commencer"}, "active_trainer_toast": {"cheats_are_running": "Les mods s’exécutent en arrière-plan.", "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "end": "Fin"}, "game_feed": {"new": "nouveau", "new_abbreviation": "n", "updated": "mis à jour", "updated_abbreviation": "m", "last_played_$date": "Dernière utilisation : $date", "released_on_$date": "Date de sortie : $date", "community_choice_award_winner": "Gagnant du prix de la communauté", "no_cheats_available": "Aucun mod disponible"}, "activity": {"was_updated_by_$creator": "a été mis à jour par $creator", "$creator_created_cheats_for": "$creator a créé des mods pour", "all": "Tous", "my_games": "<PERSON><PERSON> jeux", "get_notifications_for_games_you_care_about": "<PERSON><PERSON><PERSON> des **notifications** pour les jeux qui vous intéressent.", "follow_all": "Tout suivre", "your_notifications_are_set": "Vos **notifications** sont définies. Pour les g<PERSON>rer, accédez aux *paramètres*."}, "leave_feedback_dialog": {"what_are_your_overall_thoughts": "Que pensez-vous de WeMod ? Que pouvons-nous faire pour améliorer notre plateforme ?", "this_is_not_for_games_or_trainers": "Donnez-nous votre avis général, et non votre ressenti sur des jeux ou des mods spécifiques.", "there_was_a_problem_toast": "Une erreur s’est produite lors de l’envoi de vos commentaires.", "submit": "Envoyer", "minimum_$x_characters": "$x caractères minimum"}, "objectives": {"title": "Objectifs", "pro": "Pro", "objective_set_email": "Ajouter une adresse e-mail pour les notifications", "objective_set_username": "Définir votre nom d’utilisateur", "objective_set_password": "Choisir un mot de passe", "objective_set_profile_image": "Changer votre image de profil", "objective_leave_feedback": "Donner votre avis", "objective_join_discord": "Nous rejoindre sur **Discord**", "objective_join_community": "Rejoindre la **communauté**", "objective_like_on_facebook": "<PERSON>mer notre page **Facebook**", "objective_follow_on_twitter": "Nous suivre sur **X**", "objective_subscribe_on_youtube": "S’abonner à notre chaîne **YouTube**", "objective_try_pro": "Essayer WeMod Pro gratuitement", "objective_connect_remote": "Connecter votre appareil mobile", "objective_install_overlay": "Installer l’overlay en jeu", "objective_use_interactive_controls": "Utiliser les contrôles des mods dans l’application", "objective_boost_a_game": "Booster un jeu dans la file d’attente", "objective_play_a_game": "Jouer pour la première fois avec WeMod", "objective_use_hotkeys": "Activer des mods en utilisant les raccourcis", "get_started": "Commencer", "complete_your_profile": "Compléter votre profil", "get_started_with_pro": "Premiers pas avec l’abonnement Pro", "1_boost": "+1 boost", "$x_boosts": "+$x boosts", "boosts": "Bo<PERSON><PERSON>", "claim_boost": "Récupérer le boost", "welcome_to_wemod": "Bienvenue dans WeMod !", "streak": "Série", "$x_day": "$x jour", "$x_days": "$x jours"}, "billing_settings": {"subscribed": "Abonnement actif", "$x_days_left_in_trial": "$x jours d’essai restants", "next_billing": "Prochain prélèvement", "until": "Jusq<PERSON>’au", "resume": "Reprendre", "remove": "<PERSON><PERSON><PERSON><PERSON>", "view_plan_details": "Voir les détails de l’abonnement", "payment_method": "Moyen de paiement", "change_payment_method": "Changer de moyen de paiement", "add_payment_method": "Ajouter un moyen de paiement", "pro_subscription_end_on_$date_dialog": "Votre abonnement Pro se terminera le $date, à moins que de nouvelles informations de facturation ne soient ajoutées.", "no_payment_method": "Aucun moyen de paiement", "upgrade_to_pro": "Passer à la version Pro", "get_the_best_experience_with_pro": "Bénéficiez d’une meilleure expérience de jeu avec l’abonnement PRO", "get_pro_as_a_gift": "Obtenez l’abonnement PRO en cadeau", "upgrade": "Mettre à niveau", "give_the_gift_of_pro": "*Offrez* un abonnement Pro", "share_an_elevated_modding_experience": "Partagez une expérience de modding hors du commun avec vos amis.", "gift_pro_to_a_friend": "Offrir l’abonnement Pro à un ami", "gifting_disclaimer": "Besoin d’aide avec un cadeau ? Envoyez un e-mail à [<EMAIL>](mailto:<EMAIL>).", "pro_plan": "Abonnement **Pro**", "active_gift": "Cadeau actif", "active_gift_yearly_info": "$sender vous a offert un an d’abonnement Pro.", "active_gift_monthly_info": "$sender vous a offert un mois d’abonnement Pro.", "ends": "Se termine le", "payment_overdue": "Paiement en retard", "save_$x_or_more": "Économisez au moins $x %", "switch_to_yearly_payment": "Passer au paiement annuel", "update_billing_address": "Veuillez mettre à jour l’adresse de facturation", "applicable_taxes": "taxes applicables", "last_amount_billed_$x_on_$date": "Dernier montant facturé : $x le $date", "expires": "Date d’expiration :"}, "change_password": {"current_password": "Mot de passe actuel", "new_password": "Nouveau mot de passe", "new_password_again": "Nouveau mot de passe (encore)", "create_password": "<PERSON><PERSON><PERSON> un mot de passe", "password_again": "<PERSON>t de passe (encore)", "save": "Enregistrer", "success_toast": "Vous avez modifié votre mot de passe !", "problem_toast": "Une erreur s’est produite lors de la modification de votre mot de passe.", "no_password_set": "Aucun mot de passe défini", "password_requirements_error": "Le mot de passe doit comporter au moins 8 caractères.", "password_confirm_error": "Le mot de passe et sa confirmation doivent correspondre.", "current_password_error": "Saisissez votre mot de passe actuel."}, "general_settings": {"save_log": "Enregistrer le journal", "save_messages": "Enregistre les messages qui aideront les développeurs de WeMod à corriger les erreurs.", "log_files": "Fichiers journal", "failed_to_save_toast": "Impossible d’enregistrer le fichier journal. Veuillez vérifier le chemin d’accès, puis réessayez.", "gdpr_consent": "Exploiter les données pour personnaliser WeMod", "gdpr_consent_description": "Cette option nous permet d’exploiter et de traiter des données analytiques sur la façon dont vous utilisez WeMod. Ainsi, nous pouvons améliorer les fonctionnalités existantes, personnaliser votre expérience et vous proposer des publicités pertinentes si vous utilisez notre plateforme gratuitement.", "gdpr_revoke_confirm_message": "Voulez-vous vraiment continuer ? Vous ne pourrez plus utiliser WeMod à moins que vous n’acceptiez nos [conditions générales](website://terms) et notre [politique de confidentialité](website://privacy) concernant le contenu personnalisé.", "ok": "OK", "cancel": "Annuler"}, "profile_settings": {"upload_new_pic": "Charger une photo", "username": "Nom d’utilisateur", "email_address": "<PERSON><PERSON> votre adresse e-mail", "current_password": "Mot de passe actuel", "save": "Enregistrer", "username_change_success_toast": "Vous avez modifié votre nom d’utilisateur !", "email_change_success_toast": "Vous avez modifié votre adresse e-mail !", "profile_image_change_success_toast": "Vous avez modifié votre image de profil !", "update_error_toast": "Une erreur s’est produite lors de la mise à jour de votre profil.", "logout": "Se déconnecter", "are_you_sure": "Voulez-vous vraiment vous déconnecter de votre compte ?", "sign_out": "Se déconnecter", "cancel": "Annuler", "hold_on": "Un instant !", "no_email_for_$username": "Votre compte ($username) n’est pas encore associé à une adresse e-mail ou à un mot de passe. Si vous vous déconnectez, vous ne pourrez pas vous reconnecter !", "update_account": "Mettre le compte à jour"}, "theme_selector": {"default": "<PERSON><PERSON> <PERSON><PERSON>", "green": "<PERSON>ert", "orange": "Orange", "black": "Sombre", "purple": "Violet"}, "release_channel_selector": {"app_upgrade": "Mettre l’application à niveau", "channel_may_include_unstable_features": "Ce canal de publication vous donne parfois accès à de nouvelles fonctionnalités, mais cette version n’est pas toujours stable. En cas de problème, prévenez-nous sur le forum ou sur notre serveur Discord. Pour changer de canal, vous devrez attendre la prochaine mise à jour.", "continue": "<PERSON><PERSON><PERSON>", "cancel": "Annuler", "app_downgrade": "Passer à une version antérieure de l’application", "youll_have_to_wait": "Pour passer sur ce canal, vous devrez attendre la prochaine version. Vous pouvez également réinstaller l’application."}, "failed_payment_toast": {"update_payment": "Mettre à jour le moyen de paiement", "fix": "<PERSON><PERSON><PERSON><PERSON>"}, "online_status": {"offline_toast": "Connexion à WeMod impossible. Tant que la connexion n’est pas rétablie, votre accès aux mods est limité.", "maintenance_toast": "Une maintenance de WeMod est en cours. Nous serons bientôt de retour !"}, "basic_dialog": {"ok": "OK", "yes": "O<PERSON>", "confirm": "Confirmer", "no": "Non", "cancel": "Annuler", "help": "Aide", "dont_remind_again": "Ne plus me le rappeler"}, "time": {"$x_years": "$xy", "$x_year": "$xy", "$x_mo": "$xm", "$x_days": "$xd", "$x_day": "$xd", "$x_hr": "$x h", "$x_min": "$x min", "$x_sec": "$x sec", "$x_years_ago": "Il y a $xy", "$x_year_ago": "Il y a $xy", "$x_mo_ago": "Il y a $xm", "$x_days_ago": "Il y a $xd", "$x_day_ago": "Il y a $xd", "$x_hr_ago": "Il y a $x h", "$x_min_ago": "Il y a $x min", "$x_sec_ago": "Il y a $x sec"}, "toggle": {"on": "Activé", "off": "Désactivé"}, "queue": {"upcoming": "À venir", "info_tooltip": "Voici la file d’attente des jeux à venir. Vous pouvez y voir sur quels jeux nous travaillons et booster ceux auxquels vous avez hâte de jouer. Le développement suit l’ordre de la file d’attente, bien que la date de mise à disposition des mods dépende de la complexité et des exigences de test de chaque jeu, mais aussi de ceux qui le précèdent dans la file d’attente.", "get_more": "Obtenir plus de boosts", "overview": "Vue d’ensemble", "needs_update": "Mise à jour en attente", "new_releases": "Nouveautés", "recently_completed": "Récemment terminé", "new": "nouveau", "update": "Mise à jour", "the_creators": "Les créateurs", "become_a_wemod_creator": "Devenez créateur WeMod", "join_our_creator_community": "Rejoignez notre communauté de créateurs et gagnez de l’argent en développant des mods et des trainers gratuits pour notre plateforme.", "apply": "Appliquer"}, "boost_button": {"no_boosts": "Aucun boost disponible ! En tant que membre Pro, vous en recevez chaque mois.", "no_boosts_free": "Abonnez-vous à WeMod Pro pour obtenir plus de boosts!", "an_error_occurred": "Une erreur s’est produite lors de l’activation du boost. Veuillez réessayer.", "you_boosted_$game": "Vous avez boosté **$game**. Nous vous informerons dès que les mods seront disponibles !", "dont_notify_me": "Désactiver les notifications", "notification_preferences_updated": "Préférences de notification mises à jour.", "failed_to_update_notification_preferences": "Impossible de mettre à jour les préférences de notification.", "boosts_available_after_trial_period": "Les boosts sont disponibles après la période d’essai."}, "boost_balance_button": {"boosts_available_after_trial_period": "Les boosts sont disponibles après la période d’essai."}, "unavailable_game": {"$game_for_$platform_is_$number_in_our_work_queue": "$game pour $platform est en position **n° $number** dans la file d’attente des jeux à venir.", "$game_for_$platform_is_assigned_to_$creator_and_is_$number_in_our_queue": "$game pour $platform est assigné à $creator et est en position **n° $number** dans la file d’attente des jeux à venir.", "the_time_until_its_released_will_depend": "La date de sortie des mods pour ce jeu dépendra de sa position dans la file d’attente, mais aussi de la quantité de travail nécessaire pour créer et tester les mods pour ce jeu et pour ceux avant lui dans la file.", "this_game_is_not_supported": "Ce jeu n’est **pas compatible** avec <PERSON>, soit pour des raisons techniques qui nous empêchent de le modder, soit parce qu’il s’agit d’un jeu multijoueur.", "upcoming": "À venir", "stats": "Caractéristiques", "were_a_small_team_x_games": "Nous sommes une petite équipe de créateurs qui gère une bibliothèque de plus de **$x** jeux. La date de sortie des mods pour un jeu dépend de sa position dans la file d’attente, mais aussi de la quantité de travail nécessaire pour créer et tester les mods. Vous souhaitez donner la priorité à un jeu ? Pensez à le booster dans la file d’attente.", "what_is_this_about": "De quoi s’agit-il ?", "wemod_is_a_free_app_x_games": "WeMod est une application gratuite avec une bibliothèque de plus de **$x jeux**. Nous sommes une petite équipe, ce qui explique que les ajouts et les mises à jour prennent du temps.", "the_queue_is_our_way_of_being_transparent_with_our_users": "La file d’attente des jeux à venir est notre façon de mettre la **transparence** au cœur de notre relation avec la communauté. Nous donnons la priorité aux jeux en tête de cette liste. Pour déterminer la place de chaque jeu, l’algorithme prend en compte le nombre de joueurs actifs, le temps écoulé depuis la dernière mise à jour, les boosts et l’intérêt des utilisateurs.", "view_upcoming_games": "Voir les jeux à venir", "this_week": "<PERSON><PERSON> se<PERSON>", "this_month": "Ce mois-ci", "$x_games_added": "**$x** jeux a<PERSON>s", "$x_games_updated": "**$x** jeux mis à jour", "get_boosts": "Obtenir des boosts", "boosts": "Bo<PERSON><PERSON>"}, "trainer_notify": {"no_cheats_yet": "Nous n’avons pas encore de mods pour ce jeu. Faites-nous savoir s’il vous intéresse et nous vous enverrons un e-mail une fois que des mods seront disponibles.", "do_you_want_us_to_notify_you_when_its_ready": "Vou<PERSON>z-vous recevoir une notification lorsque les mods seront prêts ?", "do_you_want_us_to_notify_you_when_we_update_it": "Vou<PERSON>z-vous recevoir une notification pour les mises à jour ?", "do_you_want_us_to_notify_you_if_we_update_it": "Vou<PERSON>z-vous recevoir une notification pour savoir si une mise à jour est prévue ?", "notify_me_when_mods_updated": "Recevoir une notification lorsque les mods sont mis à jour", "email_address": "<PERSON><PERSON> votre adresse e-mail", "notify_me": "Activer les notifications", "email_error": "Une erreur s’est produite avec l’adresse e-mail fournie", "well_send_you_an_email": "Nous vous enverrons un e-mail !", "well_send_you_an_email_when_available": "Nous vous enverrons un e-mail lorsque des mods seront disponibles !", "unfollow": "Ne plus suivre", "request_mods": "Demander des mods"}, "loading": {"tagline": "Vos jeux, vos règles", "loading_message": "Chargement…", "checking_for_updates_message": "Vérification des mises à jour…", "installing_update_message": "Installation de la mise à jour…", "install_later": "Installer plus tard", "download_directly": "Télécharger directement", "offline_message": "Votre ordinateur semble être hors ligne. Une connexion Internet est nécessaire pour lancer l’application pour la première fois.", "maintenance_message": "Une maintenance de WeMod est en cours. Nous serons bientôt de retour !", "support_initialization_error_message": "Une erreur s’est produite lors du chargement de WeMod. Si le problème persiste, réinstallez l’application ou autorisez-la dans votre antivirus.", "reinstall": "Réinstaller l’application", "more_info": "En savoir plus", "dotnet_error_message": "<PERSON><PERSON> <PERSON> installer la bonne version du framework .NET", "download": "Télécharger", "retry": "<PERSON><PERSON><PERSON><PERSON>", "review_terms": "En cliquant sur « Créer un compte », j’accepte les [conditions générales](website://terms) et la [politique de confidentialité](website://privacy).", "accept_and_continue": "Accepter et continuer", "consent_error_message": "Une erreur s’est produite. Veuillez réessayer."}, "status_tag": {"new_game": "Nouveau jeu", "updated_game": "Jeu mis à jour"}, "pro_popup": {"upgrade_to_pro": "Passer à la version Pro", "remote_app": "*Application* Remote", "use_the_remote_on_your_phone": "Accédez à l’application WeMod sur votre smartphone ou tablette pour configurer les mods à partir d’un autre écran.", "save_cheats": "**Enregistrer** les mods", "save_cheats_info": "Revenez facilement en jeu en appliquant automatiquement les derniers paramètres de vos mods.", "cheat_overlay": "**Overlay** en jeu", "an_in_game_menu": "Configurez facilement vos mods sans quitter votre partie."}, "remote_upgrade_dialog": {"the_remote_makes_it_easy": "Une véritable télécommande", "simply_connect_the_desktop_app_to_your_mobile_device": "Connectez simplement l’application de bureau à votre appareil mobile à l’aide d’un code et le tour est joué ! Vous pouvez maintenant voir exactement quels mods sont activés, mais aussi bénéficier d’un contrôle plus précis grâce aux curseurs et aux menus déroulants.", "upgrade_now": "Mettre à niveau"}, "poll_dialog": {"submit": "Envoyer", "i_changed_my_mind": "<PERSON>’ai changé mon avis", "cancel_my_subscription": "Résilier mon abonnement"}, "overlay": {"start_game_message": "Pour contrôler vos mods à l’aide de cet overlay, lancez votre jeu depuis l’application WeMod.", "loading_cheats_message": "Chargement des mods…", "notes": "Notes", "view_notes": "Voir les notes", "cheat_instructions": "Instructions du mod", "mods": "Mods", "maps": "<PERSON><PERSON>", "game_guide": "Guide de jeu"}, "overlay_tooltip": {"click_to_use_the_overlay": "Cliquez ici pour utiliser l’ancien overlay", "unsupported": "Non pris en charge", "your_pc_does_not_meet_the_requirements": "Votre PC ne répond pas aux exigences minimales pour utiliser l’ancien overlay.", "requires_version": "Requiert Windows 10 version $version ou ultérieure", "not_installed": "Installer l’ancien overlay", "install_the_overlay_from_the_microsoft_store": "Pour les jeux qui ne prennent pas encore en charge notre nouvel overlay, installez l’ancienne version via le Microsoft Store.", "install_the_overlay": "Installer l’ancien overlay", "enable_game_bar": "Activer la <PERSON> Bar", "the_overlay_requires_game_bar": "Pour que l’ancien overlay fonctionne, la Xbox Game Bar doit être activée sur votre PC.", "open_game_bar_settings": "<PERSON><PERSON><PERSON><PERSON>r les paramètres de la Game Bar", "overlay": "Ancien overlay", "the_overlay_is_installed": "Accédez à vos mods en utilisant le raccourci ***Win* *G*** après avoir lancé un jeu à l’aide de WeMod."}, "pro_onboarding_dialog": {"congrats": "Félicitations !\nVous rejoignez l’équipe *Pro*", "you_are_now_pro": "Vous rejoignez l<PERSON>équipe **Pro**", "thanks_for_joining": "Merci d’avoir rejoint la communauté ! Maintenant, **terminons la configuration**.", "overlay_requirements": "La *Xbox Game Bar* doit être activée sur votre PC et vous devez exécuter Windows 10 version $version ou ultérieure.", "install_overlay": "Installer l’overlay", "open_game_bar_settings": "<PERSON><PERSON><PERSON><PERSON>r les paramètres de la Game Bar", "get_in_game_overlay": "Installer l’**overlay** en jeu", "bring_cheats_as_close_to_the_game_as_possible": "Rendez vos mods accessibles en jeu avec l’overlay de WeMod. Une fois installé à partir de Microsoft Store, vous pouvez accéder à vos options sans quitter votre partie en utilisant le raccourci ***Win* *G***.", "connect_wemod_remote": "Connexion à **WeMod Remote**", "get_our_mobile_app": "Téléchargez notre **application mobile**, puis scannez le code QR ou consultez *wemod.com/remote* sur votre appareil mobile.", "then_enter_this_pin": "Ensuite, **saisissez ce code** sur votre smartphone pour établir la connexion.", "receive_special_role_in_discord": "<PERSON><PERSON><PERSON> un rôle spécial dans **Discord**", "plus_now_that_youre_pro": "Bonus ! Maintenant que vous êtes **Pro**,", "plus_with_your_pro_subscription": "Bonus ! Avec votre abonnement **Pro**,", "you_support_creators_and_wemod_development": "vous **soutenez les créateurs et le développement de WeMod**. Vous avez donc la possibilité de **proposer des mods** pour les jeux populaires et **vous avez la priorité lorsque vous contactez le support**.", "skip_for_now": "Ignorer pour le moment", "i_am_all_set_up_for_pro": "Tout est prêt pour que je passe Pro", "connect_discord": "Connexion à Discord", "join_server": "Rejoindre le serveur", "connected": "Connecté", "installed": "Installé", "first_your_account_must_be_linked_to_discord": "Tout d’abord, connectez-vous à Discord en utilisant l’option ci-dessous. Une fois votre compte connecté et notre serveur rejoint, le bot **mettra à jour votre rôle dans les 24 heures**.", "your_exclusive_pro_features": "Vos fonctionnalités **Pro** exclusives", "were_happy_to_have_you": "Nous sommes très heureux que vous soyez passé Pro. Il est maintenant temps de découvrir comment accéder à toutes les **fonctionnalités exclusives, y compris les nouveautés et les avantages les plus intéressants**.", "enable_save_mods": "Gardez vos mods en mémoire avec l’**enregistrement des mods**", "save_mods_explanation": "Vos mods sont paramétrés exactement comme vous le souhaitez ? Enregistrez-les et la prochaine fois que vous lancerez votre jeu, la **même configuration sera appliquée**.", "how_it_works": "Fonctionnement", "access_in_app_controls": "Accéder aux **contrôles interactifs**", "interactive_controls_explanation": "Vous pouvez également activer les mods à l’aide des contrôles interactifs sur la page du jeu. Il s’agit **de curseurs, de menus déroulants, de valeurs à saisir manuellement et de boutons**.", "influence_game_priority": "Influencez la priorité des jeux avec les **boosts supplémentaires**", "boosts_explanation": "Les boosts servent à influencer la **priorité des jeux dans la file d’attente**. Plus un jeu reçoit de boosts, plus il est probable que les créateurs commencent à travailler dessus rapidement.", "choose_your_theme": "Choisissez la couleur de votre **thème**", "themes_explanation": "Vous pouvez modifier **l’apparence de votre application** à tout moment dans les paramètres.", "i_understand_the_pro_features": "J’ai compris les fonctionnalités de l’abonnement Pro", "disabled_game_bar_instructions": "Activez la **Xbox Game Bar** sur votre PC pour utiliser l’overlay de WeMod.", "enable_xbox_game_bar": "Activer la Xbox Game Bar", "save_mods": "Enregistrer les mods"}, "remote_platforms": {"available_on_mobile": "Disponible dans l’*App Store* et *Google Play*"}, "cancel_win_back_dialog": {"is_pro_not_for_you": "L’abonnement **Pro** n’est pas fait pour vous ?", "wemod_pro_subscribers_enjoy_benefits": "Les abonnés WeMod Pro bénéficient d’un accès plus simple à leurs mods, peuvent gérer leurs paramètres sans quitter leur partie et influencent l’ordre de priorité des jeux que nous prenons en charge.", "by_canceling_pro_you_lost_these_benefits": "En annulant votre abonnement Pro, vous perdez **ces avantages**", "need_a_new_game_to_play": "Besoin de découvrir de nouveaux horizons ?", "wemod_offers_support_for_trending_free_games": "WeMod est compatible avec une sélection de jeux tendance gratuits. Et si vous les essayiez ?", "need_help_with_pro": "Besoin d’aide avec votre abonnement Pro ?", "visit_our_faq_or_email_us": "Consultez notre [foire aux questions](https://wemod.gg/pro-features) dédiée ou envoyez-nous un e-mail : [<EMAIL>](mailto:<EMAIL>).", "continue_with_pro": "Conserver mon abonnement PRO", "i_still_wish_to_cancel": "Je souhaite toujours résilier mon abonnement", "see_all": "<PERSON><PERSON> afficher", "keep_supporting_the_creators": "Continuez à soutenir les créateurs", "your_subscription_supports_the_creators": "Votre abonnement soutient les créateurs qui développent des mods pour vos jeux préférés.", "$x_cheats_created": "*$x* mods créés"}, "simple_pro_dialog": {"forget_hotkeys_with_pro": "Oubliez les raccourcis en passant *Pro*", "did_you_know": "Saviez-vous qu’avec *WeMod Pro*, vous pouvez contrôler vos mods en jeu grâce à l’overlay ? *🤯* De plus, vous pouvez utiliser l’application Remote exclusive pour modifier vos options.\n\nMoins de raccourcis clavier à retenir, *une meilleure expérience de jeu*.", "start_free_trial": "Commencer l’essai gratuit", "learn_more": "En savoir plus"}, "secure_account_dialog": {"password": "Mot de passe", "password_again": "<PERSON>t de passe (encore)", "save": "Enregistrer", "set_password_success_toast": "Vous avez défini votre mot de passe !", "set_password_problem_toast": "Une erreur s’est produite lors de la définition de votre mot de passe.", "set_email_success_toast": "Adresse e-mail ajoutée ! Gardez un œil sur votre boîte de réception : vous recevrez bientôt un message pour vérifier votre adresse.", "email_address": "<PERSON><PERSON> votre adresse e-mail", "email_only_message": "Saisissez votre adresse e-mail pour ne jamais perdre accès à votre compte Pro.", "no_email_or_password_message": "Saisissez une adresse e-mail et un mot de passe pour ne jamais perdre accès à votre compte.", "no_password_message": "Saisissez un mot de passe pour ne jamais perdre accès à votre compte.", "no_email_message": "Saisissez votre adresse e-mail pour ne jamais perdre accès à votre compte.", "create_your_login_credentials": "Créez vos informations de connexion", "password_requirements_error": "Le mot de passe doit comporter au moins 8 caractères.", "password_confirm_error": "Le mot de passe et sa confirmation doivent correspondre."}, "pro_general": {"join_now": "Rejoindre"}, "save_cheats_tooltip": {"save_cheats": "*Enregistrer* les mods", "save_cheats_info": "Revenez facilement en jeu en appliquant automatiquement les derniers paramètres de vos mods."}, "save_cheats_tooltip_graphic": {"jump_height": "Sauter plus haut", "extra_ammo": "Munitions supplémentaires", "super_fast": "Vitesse de d<PERSON>placement", "unlimited_gold": "Or illimité", "fly_mode": "Lévitation"}, "save_cheats_toggle": {"save_cheats": "Enregistrer les mods", "save_cheats_info": "Revenez facilement en jeu en appliquant automatiquement les derniers paramètres de vos mods.", "no_eligible_cheats": "Pour ce jeu, aucun mod n’est éligible pour la fonctionnalité d’enregistrement. Cela peut se produire lorsque les mods ont besoin que quelque chose se passe en jeu avant de fonctionner. Appliquer les mods au lancement entraînerait une erreur.", "coaching_tip_header": "Démarrage rapide avec Enregistrer les mods", "coaching_tip_message": "Enregistrez facilement vos mods préférés. Plus besoin de les configurer à chaque fois !"}, "pinned_mods_tooltip": {"unlock_pinned_mods": "Déverrouiller les mods épinglés"}, "remote_app_tooltip": {"remote_app": "Application Remote", "no_more_alt_tabbing": "Les Alt + Tab à répétition, c’est terminé ! <PERSON><PERSON><PERSON> tous vos mods et accédez aux cartes quand vous le souhaitez."}, "save_mods_tooltip": {"save_mods": "Enregistrer les mods", "save_mods_info": "Revenez facilement en jeu en appliquant automatiquement les derniers paramètres de vos mods"}, "precision_mods_tooltip": {"precision_controls": "Contrôles de précision", "precision_mods_info": "Allez plus loin avec vos mods grâce à des contrôles précis et une personnalisation complète.", "unlock_precision_controls": "Déverrouiller les contrôles de précision", "join_now": "Rejoindre"}, "faux_mods_ui": {"save_mods": "Enregistrer les mods", "mod_unlimited_health": "<PERSON><PERSON> illimitée", "mod_unlimited_stamina": "Endurance illimitée", "mod_game_speed": "Vitesse du jeu", "mod_game_speed_value": "500", "mod_off": "Désactivé", "mod_on": "Activé"}, "pro_badge": {"pro": "Pro"}, "save_cheats_disable_confirm_dialog": {"turn_off_save_cheats": "Désactiver la fonctionnalité Enregistrer les mods ?", "are_you_sure_message": "Voulez-vous vraiment *désactiver* Enregistrer les mods pour ce jeu ?\n\nVotre configuration actuelle ne sera pas enregistrée.", "cancel": "Annuler", "turn_off": "Désactiver", "dont_show_again": "Ne plus afficher"}, "save_cheats_icon": {"save_cheats_is_active": "La fonctionnalité Enregistrer les mods est activée", "save_cheats_is_inactive": "La fonctionnalité Enregistrer les mods est désactivée", "unlock_save_mods": "Déverrouiller l’enregistrement des mods", "join_now": "Rejoindre"}, "plan_details_dialog": {"pro": "Pro", "plan_details": "Détails de mon abonnement", "billed_monthly": "Abonnement mensuel", "billed_yearly": "Abonnement annuel", "$x_days_left_in_trial": "$x jours d’essai restants", "next_billing": "Prochain prélèvement", "until": "Jusq<PERSON>’au", "your_pro_plan_includes": "Avantages inclus dans votre abonnement PRO :", "in_app_controls": "*Contrôles* en jeu", "remote_mobile_app": "*Application mobile* Remote", "in_game_overlay": "Overlay en jeu", "save_cheats": "Enregistrer les mods", "game_boosting": "Boosts pour la file d’attente des jeux", "exclusive_themes": "Thèmes exclusifs", "discord_role": "Rôle spécial dans Discord", "priority_support": "Support prioritaire", "learn_more": "En savoir plus", "need_help": "Besoin d’aide ?", "help_message": "Consultez notre [foire aux questions](https://wemod.gg/faq) ou envoyez-nous un e-mail : [<EMAIL>](mailto:<EMAIL>).", "cancel_subscription": "Résilier l’abonnement", "suggest_cheat_ideas": "Proposer des idées de mods", "payment_overdue": "Paiement en retard", "switch_to_yearly": "Passer à un abonnement annuel", "save_$x_or_more": "Économisez au moins $x %"}, "breadcrumbs": {"back_to_dashboard": "Retour à l’accueil", "back_to_titles": "Retour aux jeux", "back_to_free_games": "Retour aux jeux gratuits", "back_to_my_games": "Retour à mes jeux", "back_to_queue": "Retour aux jeux à venir", "back_to_games": "Retour à l’exploration", "back_to_favorites": "Retour aux favoris", "back_to_game_pass": "Retour au Game Pass", "back_to_most_popular": "Retour aux plus populaires", "back_to_community_choice": "Retour au choix de la communauté", "back_to_recently_played": "Retour aux jeux récemment lancés", "back_to_all_supported_games": "Retour à la liste des jeux compatibles"}, "desktop_shortcut": {"play_$title": "Jouer à $title"}, "genres": {"action": "Action", "adventure": "Aventure", "casual": "Détente", "fps": "FPS", "horror": "<PERSON><PERSON><PERSON>", "indie": "Indépendant", "open_world": "<PERSON><PERSON> ouvert", "platformer": "Plateformes", "puzzle": "Puzzle", "rpg": "RPG", "racing": "Course", "shooter": "Tir", "simulation": "Simulation", "sports": "Sport", "strategy": "Stratégie", "survival": "<PERSON><PERSON>", "fighting": "Combat"}, "game_collection": {"free_games": "Jeux gratuits", "my_games": "<PERSON><PERSON> jeux", "most_popular": "Les plus populaires", "popular": "Populaire", "recently_played": "<PERSON><PERSON>", "game_pass": "PC Game Pass", "favorites": "<PERSON><PERSON><PERSON>", "playable": "<PERSON><PERSON><PERSON><PERSON> à jouer", "unsupported": "Non pris en charge", "unsupported_and_not_installed": "Non pris en charge", "installable": "Installer", "launch_without_mods": "Lancer sans mods", "community_choice": "Choix de la communauté", "free_games_to_install": "Commencez à utiliser des mods avec un jeu gratuit compatible", "you_have_no_installed_games": "Vous n’avez aucun jeu <PERSON>.", "install_a_free_game_to_get_started": "Installez un jeu gratuit pour faire vos premiers pas avec WeMod.", "you_have_no_favorites": "Vous n’avez pas encore de jeux favoris.", "add_games_to_your_favorites_by_selecting_the_star_icon": "Ajoutez-en à votre sélection en utilisant l’icône en forme d’*étoile*.", "search_your_game_library": "Rechercher dans votre bibliothèque de jeux", "search_games": "Rechercher des jeux", "search_your_favorites": "Rechercher dans vos favoris", "go": "<PERSON><PERSON><PERSON>", "no_results": "Aucun résultat", "no_results_advice": "Essayez de modifier votre recherche.", "all": "Tous", "favorites_not_installed": "Jeux favoris non installés", "add_games_to_your_favorites": "Ajoutez des jeux à vos favoris", "all_supported_games": "Tous les jeux compatibles", "new_and_updated_games": "Nouveautés et mises à jour récentes", "sort_by": "Trier par", "a_z": "A-Z", "maps": "<PERSON><PERSON>", "teleport": "Téléportation", "live_location": "Localisation en direct", "games_with_maps": "Jeux avec cartes", "precision_mods": "Contrôles de précision des mods", "overlay": "Overlay", "my_videos": "<PERSON>s vid<PERSON>", "recently_recorded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "game": "<PERSON><PERSON>"}, "ad_dialog": {"ad_revenue_uses": "Vos mods seront bientôt lancés. Les revenus publicitaires permettent de récompenser les créateurs et de proposer une expérience gratuite sur WeMod.", "get_an_ad_free_experience": "Supprimer les publicités"}, "suggest_cheats": {"have_an_idea_for_a_cheat": "Vous avez une idée de mod ?", "suggest_cheat": "Proposer un mod", "info_tooltip": "Voici la file d’attente des créateurs. Découvrez les jeux sur lesquels nos créateurs travaillent et boostez ceux qui vous font envie. Les créateurs de mods donnent la priorité aux jeux qui sont en tête de la file. Le temps nécessaire pour qu’un jeu soit prêt dépend de la quantité de travail nécessaire pour créer et tester les mods pour ce jeu et pour ceux qui le précèdent dans la file d’attente.", "suggest_a_cheat": "Proposer un mod", "start_typing": "<PERSON><PERSON> du texte…", "have_an_idea": "Vous avez une idée ?", "thank_you": "Merci ! Vos propositions ont été prises en compte.", "disclaimer": "N’oubliez pas qu’en vous abonnant à WeMod Pro, vous acceptez nos [conditions générales](website://terms). Vous pouvez résilier à tout moment.", "error": "Malheureusement, une erreur s’est produite lors de l’envoi de vos idées de mods. Veuillez réessayer plus tard."}, "beta_tag": {"beta": "<PERSON><PERSON><PERSON>"}, "overlay_education_dialog": {"in_game_overlay": "Overlay en jeu", "overlay_requirements": "La *Xbox Game Bar* doit être activée sur votre PC et vous devez exécuter Windows 10 version $version ou ultérieure.", "install_message": "Saviez-vous qu’avec l’overlay de WeMod, vous pouvez utiliser vos mods sans quitter votre jeu ? Une fois l’overlay installé, utilisez simplement le raccourci ***Win* *G*** pour l’ouvrir et modifier vos options sans quitter votre partie.", "post_install_message": "Saviez-vous qu’avec l’overlay de WeMod, vous pouvez utiliser vos mods sans quitter votre jeu ? Lorsque vous êtes en jeu, utilisez simplement le raccourci ***Win* *G*** pour ouvrir l’interface et modifier vos options à la volée.", "open_game_bar_settings": "<PERSON><PERSON><PERSON><PERSON>r les paramètres de la Game Bar", "install_overlay": "Installer l’overlay", "try_it_now": "Essayer", "start_free_trial": "Commencer l’essai gratuit"}, "featured_game_feed": {"sponsored": "Sponso<PERSON><PERSON>"}, "game_search_input": {"go": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>"}, "pro_cta_label": {"start_free_trial": "Commencer l’essai gratuit", "upgrade_to_pro": "Passer à la version Pro"}, "welcome_mat_dialog": {"welcome_to_wemod": "Bienvenue sur **WeMod** !", "enjoy_all_of_wemod_with_pro": "Profitez de l’expérience **WeMod** complète en passant Pro !", "wemod_is_free_to_use": "C’est grâce à nos membres Pro que WeMod peut rester gratuit. Si vous le souhaitez, soutenez-nous et débloquez d’incroyables fonctionnalités exclusives !", "free_membership": "Abonnement gratuit…", "$x_cheats_for_$y_games": "$x mods pour $y jeux", "hotkey_cheat_controls": "Contrôles des mods avec des raccourcis (F1, etc.)", "auto_game_and_version_detection": "Détection automatique des jeux et des versions", "safe_and_virus_free_cheats": "Mods sûrs et sans virus", "discord_community_access": "Accès à la communauté Discord", "continue_with_free_membership": "Continuer à utiliser WeMod gratuitement", "upgrade_to_pro": "Passer à la version Pro", "everything_in_free_membership": "Tous les avantages de l’utilisation gratuite +", "interactive_cheat_controls": "Contrôles interactifs des mods", "save_cheats_between_plays": "Enregistrement des mods", "remote_mobile_app": "*Application mobile* Remote", "in_game_cheat_overlay": "Overlay en jeu pour accéder aux mods", "game_boosting": "Boosts pour la file d’attente", "exclusive_themes": "Thèmes exclusifs", "special_role_in_discord": "Rôle spécial dans Discord"}, "creators_list": {"$x_game": "**$x** jeu", "$x_games": "**$x** jeux"}, "email_dialog": {"thank_you_for_your_payment": "Merci pour votre paiement !", "the_creators": "Les créateurs", "your_subscription_helps": "Votre abonnement soutient les créateurs qui développent des mods pour vos jeux préférés.", "account_email": "Adresse e-mail du compte", "please_enter_a_valid_email_address": "Veuillez saisir une adresse e-mail valide pour accéder à votre compte Pro.", "your_email_address": "Votre adresse e-mail", "continue_with_pro": "Conserver mon abonnement PRO", "note_you_can_update": "Remarque : vous pouvez mettre à jour ces informations de connexion à tout moment dans les paramètres de votre compte."}, "support_wemod_footer": {"members_like_you_make_wemod_possible": "Ce sont les membres tels que vous qui rendent WeMod possible et mettent à jour notre bibliothèque de $x jeux"}, "payment_processing": {"we_are_processing_your_payment": "Votre paiement est en cours de traitement.", "thanks_for_your_patience": "Merci pour votre patience."}, "remote_education_dialog": {"wemod_remote": "WeMod *Remote*", "free_user_message": "Bonne nouvelle ! Nous venons d’améliorer WeMod Remote.\nAvec un abonnement Pro, vous pouvez utiliser cette application pour lancer des jeux sur votre PC, trouver des raccourcis clavier et contrôler les mods depuis votre téléphone.", "pro_user_message": "Bonne nouvelle pour les membres Pro ! Nous venons d’améliorer WeMod Remote.\nVous pouvez maintenant utiliser cette application pour lancer des jeux sur votre PC et rechercher des raccourcis clavier sans quitter votre partie.", "play_button_message": "Saviez-vous que vous pouvez utiliser votre téléphone pour contrôler les mods et sélectionner les jeux à lancer sur votre PC ?\nTéléchargez Pro Remote et contrôlez WeMod depuis votre canapé.", "connect_remote": "Connecter Remote", "download_remote": "Télécharger Remote", "download_for_phone": "Obtenez la **Télécommande WeMod** pour **Android** & **iOS** :", "feature_control_mods": "Contrôler les Mods", "feature_browse_maps": "Parcourir les cartes", "feautre_teleport": "Téléportation & plus encore !", "scan_qr": "<PERSON><PERSON><PERSON> le QR ou visitez [wemod.com/remote](https://wemod.com/remote) sur votre téléphone"}, "remote_education_graphic": {"controls": "<PERSON><PERSON><PERSON><PERSON>", "hotkeys": "<PERSON><PERSON><PERSON><PERSON>", "play": "<PERSON><PERSON>", "no_more_alt_tabbing": "Finies les manipulations alt-tab !"}, "discord_tooltip": {"join_our_discord": "<PERSON><PERSON><PERSON><PERSON> notre Discord", "connect_message": "Gardez le lien avec nous et d’autres membres de la communauté WeMod, et accédez à des cadeaux, des événements et plus encore.", "connect_discord": "Connexion à Discord"}, "launch_without_mods_button": {"launch_without_mods": "Lancer", "launch_failed": "Une erreur s’est produite lors du lancement de votre jeu."}, "app_header_search": {"search_games": "Rechercher des jeux", "new": "nouveau", "updated": "mis à jour", "no_cheats_available": "Aucun mod disponible", "no_results_message": "Aucun emplacement ne correspond à votre recherche.", "my_games": "<PERSON><PERSON> jeux", "favorites": "<PERSON><PERSON><PERSON>", "recently_played": "<PERSON><PERSON>", "favorites_empty_message": "Vos jeux favoris apparaîtront ici. Ajoutez-en à votre sélection en utilisant l’icône en forme d’étoile."}, "pro_banner": {"support_wemod_with_pro": "Soutenez WeMod en passant **Pro**", "features": "Contrôles dans l’application *+* Enregistrer les mods *+* overlay en jeu *+* d’autres avantages !"}, "post_pro_upgrade": {"yearly_gift_toast_message": "**Félicitations ! Vous avez reçu un cadeau de la part de $sender !** Profitez d’un an d’abonnement PRO", "monthly_gift_toast_message": "**Félicitations ! Vous avez reçu un cadeau de la part de $sender !** Profitez d’un mois d’abonnement PRO", "view_details": "Voir les détails"}, "notifications_settings": {"search_games": "Rechercher des jeux", "clear_search": "Effacer la recherche", "no_results_message": "Aucun emplacement ne correspond à votre recherche.", "no_followed_games_message": "Vous ne suivez aucun jeu.", "remove_from_followed": "Re<PERSON>rer des jeux suivis", "$game_removed_from_followed": "**$game** retiré des jeux suivis."}, "follow_games": {"failed_to_set_notification_preferences": "Impossible de définir les préférences de notification.", "$game_for_$platform_release_message": "Bonne nouvelle ! Nous venons de sortir de nouveaux mods pour $game sur $platform. Jetez-y un coup d’œil !", "$game_for_$platform_update_message": "C’est parti ! Nous venons de mettre à jour les mods pour $game sur $platform. Jetez-y un coup d’œil !", "play_now": "<PERSON><PERSON>", "release_notification_image_watermark": "Nouveaux mods !", "update_notification_image_watermark": "Mods mis à jour !"}, "map_banner": {"interactive_map": "Carte interactive", "interactive_maps": "Cartes interactives", "$name_map": "Carte de $name"}, "game_maps": {"map_render_error_message": "Une erreur s’est produite lors du chargement de votre carte. Veuillez réessayer !"}, "pro_onboarding_tooltip": {"youre_a_true_pro": "Vous êtes passé *Pro*", "say_hello_to_the_ultimate_gaming_experience": "Bénéficiez dès maintenant de l’expérience de jeu ultime avec des fonctionnalités exclusives telles qu’*Enregistrer les mods*, l’*overlay en jeu*, l’*application mobile Remote* et plus encore !", "explore_pro_features": "Explorer les fonctionnalités Pro", "click_to_see_the_benefits_of_pro": "Cliquez ici pour en savoir plus sur les avantages de l’abonnement Pro"}, "nps_dialog": {"how_likely_are_you_to_recommend_wemod_to_a_friend": "Recommanderiez-vous WeMod à vos amis ?", "not_likely": "<PERSON><PERSON><PERSON> peu <PERSON>", "extremely_likely": "Fort probable", "glad_youre_enjoying_the_app": "Super, nous sommes heureux que vous aimiez l’application !", "review_on_trustpilot": "Pouvez-vous nous laisser un commentaire positif sur Trustpilot ?", "submit": "Envoyer", "sure": "Bien sûr !", "no_thanks": "Non merci", "send": "Envoyer", "skip_feedback": "<PERSON><PERSON><PERSON>", "sorry_you_arent_satisfied": "Nous sommes désolés que vous ne soyez pas satisfait à 100 %", "what_can_we_do_better": "Dites-nous ce que nous pourrions améliorer :", "share_feedback": "N’hésitez pas à nous faire un retour d’expérience", "feedback_placeholder": "Saisir un commentaire"}, "maps_nps_dialog": {"how_satisfied_are_you_with_the_map": "Dans quelle mesure êtes-vous satisfait de la carte interactive ?", "not_satisfied_at_all": "Pas du tout satisfait", "extremely_satisfied": "Extrêmement satisfait", "thank_you_for_your_feedback": "Merci pour votre retour !", "let_us_know_how_to_improve": "Dites-nous ce que nous pourrions faire pour améliorer la carte", "feedback_placeholder": "Saisir un commentaire", "skip_feedback": "<PERSON><PERSON><PERSON>", "send": "Envoyer"}, "post_assistant_nps_dialog": {"how_satisfied_are_you": "Dans quelle mesure êtes-vous satisfait du Guide de jeu par IA ?", "not_satisfied_at_all": "Pas du tout satisfait", "extremely_satisfied": "Extrêmement satisfait", "thank_you_for_feedback": "Merci pour votre retour !", "how_can_we_improve": "Dites-nous ce que nous pourrions faire pour améliorer le Guide de jeu par IA", "feedback_placeholder": "Saisir un commentaire", "skip_feedback": "<PERSON><PERSON><PERSON>", "send": "Envoyer"}, "game_guide_nps_dialog": {"thank_you_for_your_feedback": "Merci pour votre retour !", "let_us_know_how_to_improve": "Dites-nous ce que nous pourrions faire pour améliorer le guide.", "feedback_placeholder": "Saisir un commentaire", "skip_feedback": "<PERSON><PERSON><PERSON>", "send": "Envoyer"}, "time_limit_reached_post_game_dialog": {"daily_time_limit_exceeded": "Limite de temps quotidienne *dépassée*.\nPassez à ~~Pro~~ pour un accès **illimité** aux mods.", "daily_time_limit_per_game_exceeded": "Limite de temps quotidienne pour ce jeu *dépassée*.\nPassez à ~~Pro~~ pour un accès **illimité** aux mods.", "free_can_use_wemod_for_1_hour_each_day": "Les utilisateurs gratuits peuvent utiliser WeMod gratuitement pendant *1 heure* par jour.", "free_can_use_wemod_for_1_hour_per_game_each_day": "Les utilisateurs gratuits peuvent utiliser WeMod gratuitement pendant *1 heure par jeu* chaque jour.", "free_can_use_wemod_for_2_hours_each_day": "Les utilisateurs gratuits peuvent utiliser WeMod gratuitement pendant *2 heures* par jour.", "free_can_use_wemod_for_2_hours_per_game_each_day": "Les utilisateurs gratuits peuvent utiliser WeMod gratuitement pendant *2 heures par jeu* chaque jour.", "free_can_use_wemod_for_3_hours_each_day": "Les utilisateurs gratuits peuvent utiliser WeMod gratuitement pendant *3 heures* par jour.", "free_can_use_wemod_for_3_hours_per_game_each_day": "Les utilisateurs gratuits peuvent utiliser WeMod gratuitement pendant *3 heures par jeu* chaque jour.", "free_can_use_wemod_for_4_hours_each_day": "Les utilisateurs gratuits peuvent utiliser WeMod gratuitement pendant *4 heures* par jour.", "free_can_use_wemod_for_4_hours_per_game_each_day": "Les utilisateurs gratuits peuvent utiliser WeMod gratuitement pendant *4 heures par jeu* chaque jour.", "free": "<PERSON><PERSON><PERSON>", "pro": "Pro", "play_another_game_today": "Essayez un autre jeu sans attendre", "ill_wait_until_tomorrow": "J’attendrai demain", "time_limit_resets_in": "La limite de temps se réinitialise dans :", "1_hour": "**1** heure", "1_minute": "**1** minute", "$hours_hours": "**$hours** heures", "$minutes_minutes": "**$minutes** minutes"}, "time_limit_reached_pre_game_dialog": {"upgrade_to_pro_to_keep_playing": "Passez *Pro* pour continuer à utiliser les mods !", "you_have_exceeded_your_1_hour_limit_for_today": "Vous avez dépassé votre limite quotidienne d’*une heure*", "you_have_exceeded_your_1_hour_limit_for_this_game_for_today": "Vous avez dépassé votre limite quotidienne d’*une heure* pour ce jeu", "you_have_exceeded_your_2_hour_limit_for_today": "Vous avez dépassé votre limite quotidienne de *deux heures*", "you_have_exceeded_your_2_hour_limit_for_this_game_for_today": "Vous avez dépassé votre limite quotidienne de *deux heures* pour ce jeu", "you_have_exceeded_your_3_hour_limit_for_today": "Vous avez dépassé votre limite quotidienne de *trois heures*", "you_have_exceeded_your_3_hour_limit_for_this_game_for_today": "Vous avez dépassé votre limite quotidienne de *trois heures* pour ce jeu", "you_have_exceeded_your_4_hour_limit_for_today": "Vous avez dépassé votre limite quotidienne de *quatre heures*", "you_have_exceeded_your_4_hour_limit_for_this_game_for_today": "Vous avez dépassé votre limite quotidienne de *quatre heures* pour ce jeu", "pro_users_have_unlimited_mod_access": "Les utilisateurs Pro ont un accès illimité aux mods et peuvent utiliser l’*application mobile* Remote et *Enregistrer les mods*.", "pro_users_have_unlimited_mod_access_mobile_free": "Les utilisateurs Pro bénéficient d’un accès illimité aux mods, en plus d’une expérience *sans publicité* et de la possibilité d’*enregistrer les mods*.", "ill_play_another_game": "Je vais essayer un autre jeu", "ill_play_tomorrow": "Je jouerai demain"}, "time_limit_reached_alternate_graphic": {"unlimited": "Illimité", "save_mods": "Enregistrer les mods", "ad_free": "Sans publicité"}, "time_limit_reset_dialog": {"mod_access_restored": "Accès aux mods **r<PERSON><PERSON><PERSON><PERSON>** !\nPassez *Pro* pour utiliser les mods sans limite de temps", "you_have_1_hour_of_free_mod_access_today": "Vous avez *une heure* d’accès gratuit aux mods aujourd’hui", "you_have_1_hour_of_free_mod_access_per_game_today": "Vous avez *une heure* d’accès gratuit aux mods *pour chaque jeu* aujourd’hui", "you_have_2_hours_of_free_mod_access_today": "Vous avez *deux heures* d’accès gratuit aux mods aujourd’hui", "you_have_2_hours_of_free_mod_access_per_game_today": "Vous avez *deux heures* d’accès gratuit aux mods *pour chaque jeu* aujourd’hui", "you_have_3_hours_of_free_mod_access_today": "Vous avez *trois heures* d’accès gratuit aux mods aujourd’hui", "you_have_3_hours_of_free_mod_access_per_game_today": "Vous avez *trois heures* d’accès gratuit aux mods *pour chaque jeu* aujourd’hui", "you_have_4_hours_of_free_mod_access_today": "Vous avez *quatre heures* d’accès gratuit aux mods aujourd’hui", "you_have_4_hours_of_free_mod_access_per_game_today": "Vous avez *quatre heures* d’accès gratuit aux mods *pour chaque jeu* aujourd’hui", "pro_users_have_unlimited_mod_access": "Les utilisateurs Pro ont un accès illimité aux mods et peuvent utiliser l’*application mobile* Remote et *Enregistrer les mods*."}, "time_limit_enforcer": {"times_up": "Temps écoulé !", "upgrade_to_pro_to_play_beyond_your_daily_1_hour_limit": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne de 1 heure.", "upgrade_to_pro_to_play_beyond_your_daily_2_hour_limit": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne de 2 heures.", "upgrade_to_pro_to_play_beyond_your_daily_3_hour_limit": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne de 3 heures.", "upgrade_to_pro_to_play_beyond_your_daily_4_hour_limit": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne de 4 heures.", "upgrade_to_pro": "Passer à la version Pro", "upgrade_to_pro_to_play_beyond_your_daily_1_hour_limit_per_game": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne de 1 heure par jeu.", "upgrade_to_pro_to_play_beyond_your_daily_2_hour_limit_per_game": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne de 2 heures par jeu.", "upgrade_to_pro_to_play_beyond_your_daily_3_hour_limit_per_game": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne de 3 heures par jeu.", "upgrade_to_pro_to_play_beyond_your_daily_4_hour_limit_per_game": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne de 4 heures par jeu.", "upgrade_to_pro_to_use_mods_beyond_your_daily_limit": "Passez Pro pour utiliser des mods au-delà de votre limite quotidienne.", "five_minutes_left": "5 minutes restantes"}, "first_play_upgrade_prompt_dialog": {"level_up_with_pro": "Passez au niveau supérieur avec l’abonnement *Pro*", "and_get_all_of_this": "Et bénéficiez de tous ces avantages :", "jump_into_playing": "Replongez dans vos jeux sans attendre", "fast_access_with_overlay": "Accès rapide aux mods avec l’overlay", "level_up_with_pro_to_get": "Passez au niveau supérieur avec l’abonnement *Pro* pour bénéficier des avantages suivants :", "in_game_overlay": "Overlay en jeu", "save_mods": "Enregistrer les mods", "no_ads": "Expérience sans publicité", "stay_free_and_start_game": "Continuez à utiliser la version gratuite et lancez un jeu", "and_much_more": "… et bien plus encore !", "desktop_mod_controls": "Contrôles des mods sur l’application de bureau", "your_game": "<PERSON><PERSON><PERSON> jeu", "mobile_app": "Application mobile", "overlay": "*Overlay* en jeu", "advanced_mod_controls": "Contrôles avancés des mods"}, "assistant_button": {"label": "Pourquoi cette information est-elle nécessaire pour la période d’essai gratuite ?"}, "assistant_popout": {"unlock_the_power_of_ai": "✨ Profitez de la puissance de l’IA avec votre Guide de jeu personnel.", "ask_me_about_$game": "Il suffit de lui poser une question, et vous obtiendrez des conseils et des astuces utiles pour $game !"}, "assistant": {"general_error_message": "Malheureusement, le Guide de jeu par IA rencontre des difficultés pour le moment. Cette fonctionnalité n’en est qu’à ses débuts. Nous vous remercions pour votre patience pendant que nous travaillons à son amélioration ! Si le problème persiste, n’hésitez pas à nous contacter à l’adresse **<EMAIL>**.", "rate_limit_error_message": "😴 Malheureusement, le Guide de jeu par IA prend actuellement une pause bien méritée. Merci de l’avoir essayé ! Il sera bientôt de retour pour vous aider. En attendant, bon jeu !", "offline_error_message": "Il semble que vous n’ayez pas accès à Internet. Veuillez vérifier votre connexion, puis réessayez.", "game_guide_may_make_mistakes": "Le guide de jeu peut commettre des erreurs. Vérifiez toujours ses réponses."}, "assistant_chat": {"type_in_a_question": "<PERSON><PERSON> une question…", "ask_anything": "Posez votre question…", "share_your_feedback_positive": "Indiquez ce que vous avez aimé à propos de cette réponse", "share_your_feedback_negative": "Indiquez ce que vous n’avez pas aimé à propos de cette réponse", "thanks_for_your_feedback_positive": "Merci pour votre retour ! Ravi de rendre service.", "thanks_for_your_feedback_negative": "Merci ! Vos commentaires m’aident à m’améliorer.", "welcome_message_1": "Bonjour, heureux de vous revoir ! Comment puis-je vous aider avec $game aujourd’hui ?", "welcome_message_2": "Voulez-vous mieux exploiter vos compétences dans $game ? Dites-moi ce dont vous avez besoin et laissez la magie opérer !", "welcome_message_3": "Heureux de vous revoir ! Je suis là pour vous aider à devenir un véritable pro sur $game. Que voulez-vous faire aujourd’hui ?", "welcome_message_4": "Voulez-vous améliorer vos compétences sur $game ? Voyons cela ensemble !", "hello_$user": "Bonjour, $user."}, "assistant_box": {"game_guide": "Guide de jeu", "beta": "<PERSON><PERSON><PERSON>"}, "sources_button": {"sources": "Sources", "all_under_$license": "*Contenu sous [licence $license]($url)."}, "feedback_buttons": {"positive_feedback": "Laisser un avis positif", "negative_feedback": "Laisser un avis négatif"}, "assistant_history_list": {"alpha_message": "**Alpha** : le Guide de jeu par IA n’en est encore qu’à ses débuts. Certaines réponses sont susceptibles d’être inexactes. En l’utilisant, vous acceptez nos [conditions générales](website://terms) et notre [politique de confidentialité](website://privacy).", "thinking": "Laissez-moi réfléchir…", "mission_helper": "Assistant de mission"}, "assistant_icon": {"alpha": "Alpha"}, "assistant_manager": {"overlay_prompt_message": "Overlay de WeMod en jeu : vous pouvez également me poser des questions dans l’overlay pour ne jamais quitter votre partie.\n\n1. [Installez ou mettez à jour l’overlay](wemod://overlay-install?location=assistant_message).\n2. Appuyez sur Windows + G pour l’activer.\n\n[Obtenir de l’aide](https://wemod.gg/support-assistant-overlay)"}, "assistant_education_dialog": {"ai_game_guide": "Guide de jeu par IA", "unlock_the_power_of_ai_with_your_own_personal_game_guide": "✨ Profitez de la puissance de l’IA avec votre guide de jeu personnel. Il suffit de lui poser une question, et vous obtiendrez des conseils et des astuces utiles pour certains jeux.\nMais ce n’est pas tout ! [Mettez à jour l’overlay](wemod://overlay-install?location=assistant_education_dialog) pour accéder à votre assistant personnel sans quitter votre partie.", "ask_a_question": "Poser une question", "type_in_a_question": "<PERSON><PERSON> une question…", "chat_question_1": "À quel système appartient Je<PERSON> ?", "chat_answer_1": "<PERSON><PERSON><PERSON> appartient au système Alpha du Centaure.", "chat_question_2": "Quelle est la valeur du Beowulf ?", "chat_answer_2": "La valeur du fusil Beowulf dans Starfield est de 4 820 crédits. Il dispose de 8 emplacements de modules.", "chat_question_3": "Quelle arme est la plus précise, <PERSON><PERSON><PERSON><PERSON> ou Cocher ?", "chat_answer_3": "Le niveau de précision du fusil à pompe Brèche est de 55,3 %, alors que celle du Cocher est de 38,4 %."}, "time_limit_pre_game_dialog": {"wemod_is_free_for_$x_hours_each_day": "L’utilisation de WeMod est gratuite pour **$x heures** par jour", "upgrade_to_pro_for_unlimited_modding": "Passez **Pro** pour utiliser les mods *sans limites*.", "skip_for_now": "Ignorer pour le moment"}, "time_limit_graphic": {"unlimited": "Illimité"}, "time_limit_countdown": {"free_mod_access_time_remaining": "Temps d’accès gratuit aux mods restant", "daily_free_mod_access_exceeded": "Accès gratuit aux mods *épuisé* pour aujourd’hui", "free_wemod_users_are_limited_to_$hours_hours_per_day": "Les utilisateurs gratuits de WeMod sont limités à $hours heures par jour.", "upgrade_now": "Mettre à niveau", "until_free_mod_access_restored": "Avant le **rétablissement** de l’accès gratuit aux mods", "free_mod_access_reset_tooltip": "L’accès gratuit aux mods est rétabli à minuit, heure locale"}, "time_remaining_post_game_dialog": {"time_remaining": "Temps restant :", "1_hour": "**1** heure", "1_minute": "**1** minute", "$hours_hours": "**$hours** heures", "$minutes_minutes": "**$minutes** minutes", "upgrade_to_pro_for_unlimited_modding": "Passez **Pro** pour utiliser les mods *sans limites*."}, "favorite_button": {"mark_as_favorite": "Ajouter ce jeu aux favoris."}, "follow_button": {"notify_when_mods_update": "Vous recevrez une notification lorsque des mods sont ajoutés ou mis à jour pour ce jeu."}, "maps_education_dialog": {"upgrade_to_pro_and_try_interactive_maps": "Passez **Pro** pour essayer les cartes interactives", "find_everything_youre_looking_for": "Trouvez *tout* ce que vous cherchez et plus encore…", "ill_try_later": "J’essaierai plus tard", "check_out_our_new_interactive_maps": "Découvrez nos *nouvelles* cartes interactives", "open_maps": "<PERSON><PERSON><PERSON><PERSON><PERSON> les cartes"}, "teleport_education_dialog": {"upgrade_to_pro_and_try_teleport_maps": "Met<PERSON>z à niveau vers **Pro** pour _téléporter_ dans les cartes", "instantly_go_where_you_want": "*Instantanément* allez où vous voulez d'un clic", "ill_try_later": "J’essaierai plus tard", "teleport_with_interactive_maps": "_Téléportez-vous_ avec la carte interactive", "open_maps": "Ouv<PERSON>r la carte"}, "maps_graphic": {"gun": "Arme", "vehicle": "Véhicule", "item": "Objet", "enemy_camp": "Camp d’ennemi"}, "title_settings_menu": {"select_game_source": "Sélectionnez la source du jeu", "game_source": "Source du jeu", "display_options": "Options d’affichage", "create_desktop_shortcut": "<PERSON><PERSON><PERSON> un raccourci sur le bureau", "added_to_your_desktop": "Ajouté à votre bureau", "mods_version": "Version des mods", "reset_auto_pins": "Réinitialiser les mods épinglés automatiquement"}, "game_selector": {"installed": "Installé", "added": "<PERSON><PERSON><PERSON>", "view_on_$platform": "Voir sur $platform", "select_game_source": "Sélectionner la source du jeu"}, "game_installer": {"were_having_trouble_opening_$platform": "Nous avons des difficultés à ouvrir $platform. Vérifiez que la plateforme est bien installée ou ajoutez un jeu manuellement."}, "add_game_menu": {"install": "Installer", "free": "<PERSON><PERSON><PERSON>", "get_on_$platform": "Obtenir sur $platform", "add_game_manually": "Ajouter un jeu manuellement", "not_detected_message": "Si WeMod n’a pas automatiquement trouvé votre jeu, vous devrez ajouter manuellement le raccourci du jeu ou le fichier exécutable (.exe).", "installed": "Installé", "added": "<PERSON><PERSON><PERSON>"}, "display_options_menu": {"show_hotkeys": "Aff<PERSON>r les raccourcis", "show_hotkeys_description": "Les raccourcis restent actifs lorsqu’ils sont cachés."}, "mod_onboarding": {"get_started_in_$x_steps": "Faire vos premiers pas en $x étapes", "step_1": "Étape 1", "step_2": "Étape 2", "step_3": "Étape 3", "game_not_installed_step_1_instructions": "C<PERSON>z sur *Ajouter le jeu* pour installer votre jeu.", "game_not_installed_step_1_instructions_free": "Cliquez sur *Installer gratuitement* pour installer votre jeu.", "game_not_installed_step_2_instructions": "<PERSON><PERSON><PERSON> sur **<PERSON><PERSON>** pour lancer votre jeu.", "game_not_installed_step_3_instructions": "Activez les **mods** que vous souhaitez utiliser.", "game_installed_step_1_instructions": "<PERSON><PERSON><PERSON> sur **<PERSON><PERSON>** pour lancer votre jeu.", "game_installed_step_2_instructions": "Activez les **mods** que vous souhaitez utiliser.", "modding_tip": "*Astuce :* utilisez *Alt + Tab* pour revenir à WeMod pendant le jeu, ou utilisez les raccourcis clavier."}, "mod_onboarding_button_graphic": {"install": "Installer"}, "mod_onboarding_mods_graphic": {"player": "<PERSON><PERSON><PERSON>", "unlimited_health": "<PERSON><PERSON> illimitée", "unlimited_stamina": "Endurance illimitée"}, "title_settings_button": {"mod_settings": "Paramètres des mods"}, "version_history_menu": {"unreleased": "Non publié", "$x_mod": "$x mod", "$x_mods": "$x mods", "latest": "Le plus récent"}, "cyberpunk_mission_help": {"mission_helper": "Assistant de mission", "live": "ACTIF", "detecting_mission": "Détection de la mission…", "get_help": "<PERSON><PERSON><PERSON><PERSON> <PERSON>aide", "offline": "<PERSON><PERSON> ligne", "launch_message": "Votre assistant personnel qui vous aidera à accomplir votre mission actuelle. Lancez le jeu pour commencer.", "mission_help_prompt": "Je suis sur la mission « $mission ». J’aurais besoin d’un guide précis pour la mener à bien."}, "elden_ring_boss_guide": {"elden_ring_boss_guide": "Guide du boss", "all_the_info_you_need": "Toutes les informations dont vous avez besoin pour vaincre les boss d’Elden Ring.", "view_guide": "Voir le guide", "step_by_step_$boss_guide": "Guide étape par étape pour vaincre le **$boss**.", "includes_video_guide": "Guide vidéo inclus"}, "onboarding": {"select_a_game": "Sélectionner un jeu", "loading_games": "Nous cherchons vos jeux", "empty_search_title": "<PERSON><PERSON><PERSON> jeu trouvé", "empty_search_message": "Essayez de chercher un autre jeu ou découvrez les titres populaires."}, "onboarding_all_games_card": {"browse_all": " <PERSON><PERSON><PERSON><PERSON> plus de 3 000 jeux sur WeMod ->"}, "onboarding_game_tutorial": {"press_spacebar_to_continue": "Appuyez sur la **barre d’espace** pour continuer", "welcome_to_game_page": "Bienvenue sur votre page de jeu", "play_mod_enjoy": "<PERSON><PERSON><PERSON>, m<PERSON><PERSON>, profitez. C'est aussi simple que ça !", "add_your_game": "A<PERSON>ter votre jeu", "use_add_game_button": "Utilisez le bouton **Ajouter le jeu** pour ajouter le fichier exécutable de votre jeu ou pour l'installer.", "use_play_game_button": "Utilisez le bouton **<PERSON><PERSON>** pour lancer vos jeux.", "launch_your_game": "Lancer votre jeu", "customize_with_mods": "Personnalisation avec les mods", "toggle_and_adjust_mods": "Activez et ajustez les **mods** en utilisant les différents contrôles.\n\n*Astuce :* utilisez *Alt + Tab* pour revenir à WeMod pendant que vous êtes en jeu.", "use_hotkeys_for_convenience": "Utiliser les raccourcis pour simplifier votre expérience", "customizable_keyboard_shortcuts": "Les **r<PERSON><PERSON><PERSON><PERSON> clav<PERSON>** personnalisables vous permettent d’activer vos mods encore plus rapidement lorsque vous êtes en jeu.", "skip": "<PERSON><PERSON><PERSON>", "next_arrow": "->", "back_arrow": "<-", "done": "<PERSON><PERSON><PERSON><PERSON>", "play": "<PERSON><PERSON>", "save_mods": "Enregistrer les mods", "pro": "PRO", "unlimited_health": "<PERSON><PERSON> illimitée", "player": "<PERSON><PERSON><PERSON>", "off": "Désactivé", "on": "Activé", "unlimited_stamina": "Endurance illimitée", "inventory": "Inventaire", "unlimited_ammo": "Munitions illimitées", "unlimited_items": "Objets illimités", "speed": "Vitesse", "ctrl_f1": "Ctrl + F1", "ctrl_shift_f1": "Ctrl + Maj + F1", "f4": "F4", "f3": "F3", "f2": "F2", "f1": "F1"}, "sidebar_user_menu": {"help": "Aide", "share_feedback": "Partager des commentaires"}, "sidebar_game_lists": {"favorites": "<PERSON><PERSON><PERSON>", "supported": "Compatible avec mods", "other": "Autres jeux", "view_all_$number_games": "Voir les $number jeux ->", "new": "Nouveau"}, "sidebar_game_menu": {"add_to_favorites": "Ajouter aux favoris", "remove_from_favorites": "Retirer des favoris", "launch_without_mods": "Lancer sans mods", "follow": "Suivre", "unfollow": "Ne plus suivre", "play": "<PERSON><PERSON>"}, "app_sidebar_search_button": {"search": "<PERSON><PERSON><PERSON>"}, "app_search": {"search": "<PERSON><PERSON><PERSON>", "use_keys_to_navigate": "Utiliser *↑* *↓* pour naviguer", "no_cheats_available": "Aucun mod disponible", "no_results_message": "Aucun jeu ne correspond à votre recherche. Veuillez réessayer.", "my_games": "<PERSON><PERSON> jeux", "recently_played_games": "<PERSON>ux lancés récemment", "recent_games": "<PERSON><PERSON> récents"}, "remote_button": {"connect_phone": "Connecter le téléphone", "reconnect_phone": "Reconnecter le téléphone", "connected": "Connecté", "promo_tooltip": "Contrôlez vos mods et parcourez les cartes depuis votre téléphone."}, "overlay_button": {"experiencing_overlay_issues": "Vous rencontrez des problèmes avec l’overlay ?", "improve_overlay_feedback": "Nous travaillons actuellement à améliorer l’overlay. Vous rencontrez un problème ? Votre retour nous aidera à l'améliorer.", "install_overlay": "Installer l’overlay", "shortcut_key": "Win + G", "overlay": "Overlay", "overlay_feedback": "Retour d’expérience sur l’overlay", "overlay_unsupported": "Overlay non pris en charge", "report_an_issue": "Signaler un problème", "tooltip_message": "Appuyez sur **$hotkey** lorsque vous êtes en jeu pour accéder instantanément à vos mods.", "unsupported_tooltip_header": "Jeu non compatible avec l’overlay", "unsupported_tooltip_message": "$game n’est pas compatible avec l’overlay pour le moment. Vous pouvez toujours y jouer sans l’overlay. Nous travaillons à améliorer la compatibilité pour tous les jeux.", "disabled_tooltip_header": "Overlay désactivé", "disabled_tooltip_message": "Le nouvel overlay WeMod est désactivé. Veuillez l’activer dans la section Personnalisation du menu des paramètres."}, "overlay_header": {"back_to_game": "Retour au jeu"}, "overlay_window_menu": {"settings": "Paramètres", "clip_last_$seconds_s": "Clips (dernières $secondss)", "or_press_$hotkey": "Ou appuyez sur $hotkey"}, "overlay_launch_notification": {"press_$hotkey": "Appuyez sur $hotkey", "to_toggle_overlay": "pour activer ou désactiver l’overlay en jeu"}, "house_ad": {"save_mods_title": "**Enregistrer** les mods", "save_mods_description": "Conservez vos contrôles de mods **d’une partie à l’autre**.", "remote_app_title": "Application **Remote**", "remote_app_description": "Oubliez les raccourcis clavier.\nUtilisez **votre téléphone** pour contrôler les mods.", "game_boosting_description": "Utilisez des boosts pour\ninfluencer les\n**jeux sur lesquels nous travaillerons en priorité**.", "game_boosting_title": "**Boosts** pour la file d’attente", "exclusive_themes_title": "**Thèmes** exclusifs", "exclusive_themes_description": "Personnalisez votre application avec une **touche colorée**.", "remove_ads_with_wemod_pro": "Supprimez les publicités avec WeMod **Pro**"}, "overlay_nps_dialog": {"how_satisfied_are_you_with_the_overlay": "Dans quelle mesure êtes-vous satisfait de l’overlay ?", "not_satisfied_at_all": "Pas du tout satisfait", "extremely_satisfied": "Extrêmement satisfait", "thank_you_for_your_feedback": "Merci pour votre retour !", "let_us_know_how_to_improve": "Dites-nous ce que nous pourrions faire pour améliorer l’overlay", "feedback_placeholder": "Saisir un commentaire", "skip_feedback": "<PERSON><PERSON><PERSON>", "send": "Envoyer"}, "profile": {"edit_profile": "Modifier le profil", "member_since": "Membre depuis", "pro_since": "Pro depuis", "my_games": "<PERSON><PERSON> jeux", "boosts": "Bo<PERSON><PERSON>", "youre_a_pro": "Vous êtes *Pro*", "explore_pro_features": "Découvrir les avantages Pro", "your_pro_subscription": "Votre abonnement WeMod Pro vous donne accès à des fonctionnalités exclusives comme *Enregistrer les mods*, l’*Overlay en jeu*, l’*application mobile Remote* et plus encore.", "support_wemod": "Soutenez WeMod en *passant Pro*", "save_mods": "Enregistrer les mods", "save_mods_desc": "Enregistrez votre configuration de mods et lancez rapidement vos parties", "in_game_overlay": "Overlay en jeu", "in_game_overlay_desc": "Contrôlez vos mods en jeu avec l’overlay WeMod", "remote_mobile_app": "Application mobile Remote", "remote_mobile_app_desc": "Utilisez l’application WeMod pour configurer les mods", "and_a_lot_more": "Et bien plus", "and_a_lot_more_desc": "Comme le support prioritaire, les thèmes, les boosts de file d’attente, etc.", "followed_games": "<PERSON>ux suivis", "no_followed_games": "Aucun jeu suivi pour le moment", "no_followed_games_description": "Su<PERSON>z des jeux pour rester au courant des mises à jour de mods et des nouveautés !", "explore_games": "Explorer les jeux", "view_all": "<PERSON>ut afficher →"}, "achievements": {"achievements": "Su<PERSON>ès", "$value_week": "*$value* semaine", "$value_month": "*$value* mois", "$value_months": "*$value* mois", "$value_year": "*$value* an", "$value_years": "*$value* ans", "one_week_club": "Club 1 semaine", "one_month_club": "Club 1 mois", "three_months_club": "Club 3 mois", "six_months_club": "Club 6 mois", "one_year_club": "Club 1 an", "two_years_club": "Club 2 ans", "three_years_club": "Club 3 ans", "four_years_club": "Club 4 ans", "five_years_club": "Club 5 ans", "six_years_club": "Club 6 ans", "seven_years_club": "Club 7 ans", "eight_years_club": "Club 8 ans", "nine_years_club": "Club 9 ans", "ten_years_club": "Club 10 ans", "joined_wemod": "Rejoindre We<PERSON>od", "map_explorer": "Explorateur de carte"}, "overlay_announcement_dialog": {"introducing_the_all_new": "Présentation du tout nouveau", "wemod_overlay": "<PERSON><PERSON><PERSON>", "a_faster_more_responsive_way_to_enhance_your_gameplay": "Une façon plus rapide et plus réactive d’améliorer votre gameplay, sans avoir recours à la Xbox Game Bar. Utilisez le raccourci **$hotkey** dans les jeux pris en charge pour accéder à vos mods."}, "map_mods_list": {"category_maps": "<PERSON><PERSON>", "key_location_teleport": "Téléportation au lieu clé", "open_in_map": "Ouv<PERSON>r dans la carte"}, "ad_popup": {"remove_ads_with_wemod_pro": "Supprimez les publicités avec WeMod **Pro**"}, "map_feed_item": {"$count_maps": "$count cartes"}, "new_badge": {"new": "Nouveau"}, "overlay_settings_menu": {"restore_windows": "Réinitialiser la position des fenêtres", "enable_notifications": "Activer les notifications", "notification_position": "Position des notifications", "top": "<PERSON><PERSON>", "bottom": "Bas", "center": "Centre"}, "overlay_mod_notification": {"set_$mod_$value": "**Définir $mod** : $value", "$mod_enabled": "Activé : **$mod**", "$mod_disabled": "Désactivé : **$mod**", "unread_instructions": "Non lu"}, "rewards": {"rewards": "Récompenses", "wemod_pro": "WeMod **Pro**", "pc_game_pass": "PC Game Pass", "congrats_pro_member": "Félicitations, vous êtes membre Pro !", "you_get_one_month": "Nous vous offrons **1 mois** de **PC Game Pass**. <PERSON>tte récompense est disponible du $start au $end.", "more_details": "Aff<PERSON>r les détails", "hide_details": "Masquer les détails", "claim_now": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view_code": "Voir le code", "claim_gamepass_header": "Félicitations", "claim_gamepass_description": "Utilisez le code ci-dessous sur le site Xbox dédié pour profiter du PC Game Pass pendant 1 mois.", "redemption_code": "Code à utiliser", "copy": "<PERSON><PERSON><PERSON>", "copied": "Copié !", "redeem": "Utiliser", "a_gift_for_you": "Un cadeau pour vous !", "congrats_pro_subscriber": "Félicitations, vous avez un abonnement Pro ! À cet effet, nous vous offrons 1 mois de PC Game Pass.", "claim_now_sidebar": "Récupérer →", "generic_claim_error": "Une erreur s’est produite lors de la récupération de votre récompense. Veuillez réessayer plus tard ou contacter l’équipe d’assistance.", "gamepass_offer_details": "Valable uniquement pour les nouveaux membres Xbox Game Pass. Cette offre est disponible pour les utilisateurs de WeMod Pro (hors période d’essai) aux États-Unis, au Royaume-Uni, au Canada et en Australie. Une fois le code récupéré, il doit être utilisé avant le $redemptionDate sur [$redeemName]($redeemUrl). L’offre sera active du $start au $end. Un moyen de paiement valide est requis. En l’absence de résiliation, à la fin de l’offre, le tarif standard en vigueur sera débité pour la poursuite de l’abonnement. Offre limitée à 1 par personne/compte. Le Contrat de Services Microsoft, les conditions d’utilisation du Game Pass et les exigences techniques s’appliquent. Ces éléments sont disponibles à l’adresse suivante : [$termsName]($termsUrl)."}, "pro_showcase": {"ready_to_level_up_go_pro": "Vous souhaitez passer au niveau supérieur ?  *Passez Pro !*", "remote": "Application Remote", "remote_description": "Les Alt + Tab à répétition, c’est terminé ! G<PERSON><PERSON> tous vos mods et accédez aux cartes sans quitter votre partie, et consultez le guide du jeu d’un simple geste avec l’application mobile WeMod Remote.", "remote_subtitle": "Utilisez l’application WeMod pour configurer les mods", "save_mods": "Enregistrer les mods", "save_mods_description": "Enregistrez la configuration de vos mods et foncez dans votre partie ! Tous les mods que vous avez paramétrés peuvent être sauvegardés et appliqués à chaque lancement du jeu.", "save_mods_subtitle": "Enregistrez votre configuration de mods et lancez rapidement vos parties", "save_mods_mods": "Mods", "pin_mods": "<PERSON><PERSON>", "pin_mods_description": "Épinglez vos mods préférés pour y accéder rapidement. Ils apparaîtront toujours en haut de la liste pour simplifier votre expérience !", "pin_mods_subtitle": "Épinglez vos mods préférés pour y accéder rapidement", "boosts": "Boosts pour les jeux", "boosts_description": "Votez pour vos jeux préférés ! Vos votes influencent directement les mods sur lesquels nous travaillons en priorité : nous nous concentrons sur les jeux qui sont les plus importants pour vous.", "boosts_subtitle": "Votez pour vos jeux préférés et nous créerons des mods adaptés", "game_guide": "Guide de jeu", "game_guide_description": "Trouvez instantanément des réponses à vos questions sur un jeu, découvrez les stratégies optimales et apprenez des astuces de pro pour améliorer votre expérience !", "game_guide_subtitle": "Conseils et astuces alimentés par l’IA pour votre jeu", "pinned": "<PERSON><PERSON><PERSON>", "demo_mod_1": "<PERSON><PERSON> illimitée", "demo_mod_2": "Endurance illimitée", "demo_mod_3": "Invincibilité/Intouchable", "demo_mod_4": "Vitesse du jeu", "popular": "Populaire", "game_guide_illustration_response": "Pour réinitialiser les attributs dans Cyberpunk 2077, rendez-vous dans le menu du personnage et utilisez le bouton de réinitialisation des avantages. Vous pourrez alors redistribuer vos points d’attribut pour changer votre style de jeu. Vous pouvez égalemment réinitialiser ces points en échange d’eurodollars.", "game_guide_illustration_question": "Comment réinitialiser les attributs ?", "game_guide_illustration_pending_question": "Quelle est la meilleure stratégie pour réussir la mission finale ?", "game_guide_illustration_example_avatar_alt": "Exemple d’avatar utilisateur", "game_guide_illustration_assistant_avatar_alt": "Avatar de l’assistant du guide de jeu", "game_guide_assistant_icon_alt": "Assistant du guide de jeu", "precision_mod_controls": "Contrôles de précision des mods", "precision_mod_controls_description": "Allez plus loin avec vos mods grâce à des contrôles précis et une personnalisation complète.", "mod_timers_controls": "Minuteries de mod : le timing parfait", "mod_timers_controls_description": "Programmez vos mods pour qu’ils s’activent automatiquement quand vous en avez le plus besoin.", "pro": "PRO", "refill": "Recharger", "one_and_a_half_times": "1,5×", "two_times": "2×", "half_times": "0,5×", "off": "Désactivé", "on": "Activé", "unlimited_health": "<PERSON><PERSON> illimitée", "refill_health": "Re<PERSON>rge de sant<PERSON>", "set_max_health": "Définir la santé maximale", "regeneration_rate": "Taux de régénération", "regeneration_delay": "<PERSON><PERSON><PERSON> régén<PERSON>", "new_in_wemod_pro": "Nouveau dans *WeMod PRO !*", "check_it_out": "C’est parti", "try_now": "Essayer", "minute": "min.", "start_timer": "<PERSON><PERSON><PERSON><PERSON> le minuteur", "loop": "<PERSON><PERSON><PERSON>", "refill_stamina": "Recharger l’endurance"}, "featurebase_feedback_dialog": {"feedback_submitted": "Merci pour votre retour ! ❤️"}, "pro_showcase_columns": {"basic": "Basique", "unlimited_modding": "Modding illimité", "$games_games_with_$mods_mods": "Plus de $games jeux, plus de $mods mods", "native_overlay": "Overlay natif en jeu", "interactive_controls": "Commandes interactives", "$count_maps": "Plus de $count cartes", "one_click_teleport": "Téléportation en un clic grâce aux cartes", "continue_with_free": "Continuer avec la version gratuite", "pro": "Pro", "everything_in_free_plus": "Tous les avantages de la version gratuite +", "save_mods": "Enregistrer les mods", "pin_mods": "<PERSON><PERSON><PERSON> les mods", "mobile_remote_app": "Application mobile Remote", "game_guides": "Guides de jeu", "custom_themes": "Thèmes personnalisés", "no_ads": "Expérience sans publicité", "boost_games": "Booster les jeux", "priority_support_and_more": "Support prioritaire et plus encore", "save_mods_title_case": "Enregistrer les mods", "save_mods_description": "Revenez facilement en jeu en appliquant automatiquement les derniers paramètres de vos mods.", "pin_mods_title_case": "<PERSON><PERSON>", "pin_mods_description": "Épinglez vos mods préférés pour y accéder rapidement lorsque vous les utilisez fréquemment.", "remote_title_case": "Application Remote", "remote_description": "<PERSON><PERSON>rez tous vos mods d’un simple geste avec l’application mobile WeMod Remote.", "game_guide_title_case": "Guide de jeu", "game_guide_description": "Profitez de la puissance de l’IA avec votre Guide de jeu personnel.", "themes_title_case": "Thèmes personnalisés", "themes_description": "Personnalisez le thème de votre application WeMod et des contrôles des mods.", "no_ads_title_case": "Sans publicité", "no_ads_description": "Profitez d’une application de bureau WeMod entièrement sans publicité, en plus de l’application mobile et de l’overlay natif.", "boosts_title_case": "Booster les jeux", "boosts_description": "Votez pour jeux préférés et nous leur développerons des mods en priorité.", "popular": "Populaire"}, "hover_me": {"hover_me": "Survolez-moi"}, "choose_plan_promo": {"tagline": "Vous souhaitez passer au niveau supérieur ?", "heading": "Tirez le meilleur parti de WeMod", "description": "Aidez-nous à créer des mods pour vos jeux préférés et débloquez d’incroyables fonctionnalités exclusives !", "free": "<PERSON><PERSON><PERSON>", "pro": "Pro", "everything_in_free": "Tous les avantages de la version gratuite ", "free_mod_count": "Plus de 35 000 mods", "free_interactive_controls": "Contrôles interactifs", "free_overlay": "Overlay natif en jeu", "free_maps": "Plus de 150 cartes pour plus de 50 jeux", "pro_unlimited_modding": "Modding illimité sur plus de 3 000 jeux", "pro_no_ads": "Expérience sans publicité", "pro_pin_mods": "Épinglage des mods", "pro_save_mods": "Enregistrement des mods", "pro_remote_app": "Application mobile Remote", "pro_custom_themes": "Thèmes personnalisés et plus encore", "continue_with_free": "Continuer avec la version gratuite", "choose_your_plan": "Choisir votre abonnement"}, "overlay_maps_window": {"$name_map": "Carte : $name"}, "pro_showcase_slideshow": {"games_title": "Plus de 3 000 jeux", "games_description": "Plus besoin de s'inquiéter de la compatibilité avec la version du jeu ou de retélécharger des trainers. Grâce à notre communauté, nous excellons dans un domaine crucial : nos mods fonctionnent, tout simplement.", "auto_detect_title": "Détection automatique des jeux", "auto_detect_description": "WeMod analyse vos bibliothèques pour détecter automatiquement les jeux installés à partir de lanceurs populaires comme Steam, Epic et plus encore !", "overlay_title": "Overlay en jeu", "overlay_description": "Les Alt + Tab à répétition, c’est terminé ! L’overlay de WeMod est le moyen le plus simple d’activer des mods sans jamais quitter le jeu et sans avoir recours à des raccourcis clavier.", "remote_title": "Application mobile Remote", "remote_description": "<PERSON><PERSON><PERSON> tous vos mods d’un simple geste avec l’application mobile Remote de WeMod !", "play_title": "Jouez à votre façon", "play_description": "Des milliers de mods, de cartes et de trainers gratuits pour que vous puissiez profiter de vos jeux solo à votre façon.", "maps_title": "Cartes interactives", "maps_description": "Trouvez des coffres, des lieux, des secrets et plus encore avec les cartes interactives de WeMod !"}, "support_assistant": {"report_an_issue": "Signaler un problème", "beta": "<PERSON><PERSON><PERSON>", "title": "Assistant <PERSON><PERSON><PERSON>", "wemod_logo": "Logo WeMod", "loading": "Chargement…", "subtitle": "Support", "assistant_says": "L'assistant dit :", "your_selection": "V<PERSON> avez sélectionné :", "welcome": "Salut, $username ! Je suis là pour vous aider avec tous les problèmes WeMod que vous rencontrez. Comment puis-je vous aider aujourd'hui ?", "what_else_can_i_help_you_with": "Avec quoi d'autre puis-je vous aider ?", "mods_not_working": "Les mods ne fonctionnent pas", "overlay_not_working": "La superposition ne fonctionne pas", "hotkeys_not_working": "Les raccourcis clavier ne fonctionnent pas", "game_is_crashing": "Le jeu a planté", "i_have_general_feedback": "J'ai des commentaires généraux", "i_have_an_account_issue": "J'ai un problème de compte", "game_general_setup_instructions": "Résolvons les problèmes de vos mods pour $gameTitle. Tout d'abord, vérifions si vous avez suivi ces étapes générales de configuration :", "game_general_setup_instructions_1": "Assurez-vous d'exécuter WeMod en tant qu'administrateur", "game_general_setup_instructions_2": "Désactivez tout logiciel antivirus ou ajoutez WeMod aux exceptions", "game_general_setup_instructions_3": "Assurez-vous que <PERSON> est mis à jour à la dernière version", "game_general_setup_instructions_4": "Redémarrez votre ordinateur si vous ne l'avez pas fait récemment", "game_general_setup_instructions_confirmation": "Avez-vous complété ces étapes générales de configuration ?", "recommend_game_general_setup_steps": "Je recommande de suivre ces étapes générales d'abord, car elles résolvent de nombreux problèmes courants. Souhaitez-vous les essayer et revenir si le problème persiste ?", "yes_ive_followed_all_these_steps": "<PERSON><PERSON>, j'ai suivi toutes ces étapes", "yes_ive_followed_these_instructions": "<PERSON><PERSON>, j'ai suivi ces instructions", "no_i_havent_tried_all_of_them": "Non, je ne les ai pas toutes essayées", "no_i_havent_tried_all_of_these": "Non, je n'ai pas essayé toutes celles-ci", "ill_try_these_steps": "Je vais essayer ces étapes", "great_try_those_general_setup_steps": "Super ! Essayez ces étapes générales de configuration et faites-moi savoir si elles vous aident. Si vous rencontrez encore des problèmes par la suite, n'hésitez pas à revenir à cette discussion.", "now_lets_check_if_youve_followed_game_instructions": "Maintenant, vérifions si vous avez suivi les instructions de configuration spécifiques pour $gameTitle :", "have_you_followed_these_game_instructions": "Avez-vous suivi ces instructions spécifiques au jeu ?", "i_recommend_following_these_game_instructions": "Je recommande de suivre ces instructions spécifiques au jeu d'abord. Elles sont conçues pour résoudre les problèmes courants avec $gameTitle. Souhaitez-vous les essayer et revenir si le problème persiste ?", "great_try_following_those_game_instructions": "Super ! Essayez de suivre ces instructions spécifiques au jeu et faites-moi savoir si elles vous aident. Si vous rencontrez encore des problèmes par la suite, n'hésitez pas à revenir à cette discussion.", "which_mods_arent_working_correctly": "Quels mods ne fonctionnent pas correctement ?", "all_mods": "Tous les mods", "specific_mods": "Mods spécifiques", "which_specific_mods_arent_working_correctly": "Quels mods spécifiques ne fonctionnent pas correctement ?", "please_select_mods": "Veuillez sélectionner les mods", "submit": "Envoyer", "have_you_followed_these_mod_specific_notes": "Avez-vous suivi ces notes spécifiques aux mods ?", "yes_ive_followed_all_these_notes": "<PERSON><PERSON>, j'ai suivi toutes ces notes", "instructions": "Instructions", "mod_basic_troubleshooting": "Essayons quelques étapes de dépannage de base :", "mod_basic_troubleshooting_1": "Vérifiez que vous utilisez une version authentique et officiellement achetée du logiciel (WeMod ne fonctionne pas correctement avec des versions non officielles)", "mod_basic_troubleshooting_2": "Vérifiez que vous n'utilisez pas d'autres mods ou mods Nexus qui pourraient entrer en conflit", "mod_basic_troubleshooting_3": "Assurez-vous que votre antivirus ne bloque pas WeMod", "mod_basic_troubleshooting_4": "Vérifiez s'il y a une mise à jour du jeu qui aurait pu casser la compatibilité", "mod_basic_troubleshooting_confirmation": "L'une de ces étapes a-t-elle aidé à résoudre votre problème ?", "im_glad_to_header_that": "Je suis content de l'entendre ! Y a-t-il autre chose avec lequel vous avez besoin d'aide ?", "i_recommend_following_these_mod_specific_notes": "Je recommande de suivre d'abord ces notes spécifiques aux mods. Elles sont conçues pour résoudre les problèmes courants avec ces mods particuliers. Souhaitez-vous les essayer et revenir si le problème persiste ?", "great_try_following_those_mod_specific_notes": "Super ! Essayez de suivre ces notes spécifiques aux mods et faites-moi savoir si elles aident. Si vous avez encore des problèmes par la suite, n'hésitez pas à revenir à cette discussion.", "advanced_troubleshooting": "Essayons quelques étapes de dépannage avancées :", "advanced_troubleshooting_1": "Essayez de lancer à la fois le jeu et WeMod en tant qu'administrateur", "advanced_troubleshooting_2": "Vérifiez que vous jouez à la dernière version du jeu", "advanced_troubleshooting_confirmation": "L'une de ces étapes a-t-elle aidé à résoudre votre problème ?", "where_did_you_purchase_game": "Où avez-vous acheté $gameTitle ?", "steam": "Steam", "epic_games": "Epic Games Store", "gog": "GOG", "xbox_microsoft_store": "Xbox/Microsoft Store", "other": "<PERSON><PERSON>", "game_platform_notes": "Pour les jeux $gamePlatform, vous pouvez essayer :", "game_platform_notes_steam_1": "Ouvrez votre **Bibliothèque**.", "game_platform_notes_steam_2": "Faites un clic droit sur le jeu et choisissez **Propriétés**.", "game_platform_notes_steam_3": "Sélectionnez **Fichiers Installés**.", "game_platform_notes_steam_4": "Cliquez sur **Vérifier l'intégrité des fichiers du jeu**.", "game_platform_notes_epic_1": "<PERSON><PERSON>vrez l'onglet **Bibliothèque**.", "game_platform_notes_epic_2": "Cliquez sur le menu ⋯ du jeu.", "game_platform_notes_epic_3": "Choisissez **<PERSON><PERSON><PERSON>**.", "game_platform_notes_epic_4": "Cliquez sur **Vérifier**.", "game_platform_notes_gog_1": "Sélectionnez le jeu.", "game_platform_notes_gog_2": "Cliquez sur le bouton ⋯ à côté de **<PERSON><PERSON>**.", "game_platform_notes_gog_3": "Allez dans **G<PERSON><PERSON> l'installation**.", "game_platform_notes_gog_4": "Choisissez **Vérifier/<PERSON><PERSON><PERSON><PERSON>**.", "game_platform_notes_xbox_1": "Dans l'application **Xbox**, ouvrez **Ma bibliothèque**.", "game_platform_notes_xbox_2": "Cliquez sur le menu ⋯ du jeu.", "game_platform_notes_xbox_3": "Sélectionnez **<PERSON><PERSON><PERSON>**.", "game_platform_notes_xbox_4": "<PERSON><PERSON>vrez l<PERSON>onglet **Fichiers**.", "game_platform_notes_xbox_5": "Cliquez sur **Vérifier & Réparer**.", "game_platform_notes_confirmation": "Avez-vous suivi ces instructions ?", "we_recommend_following_game_platform_instructions": "Nous vous recommandons de suivre d'abord ces instructions pour $gamePlatform, car elles résolvent la plupart des problèmes !", "great_try_that_out": "Super ! Essayez cela et si vous rencontrez encore des problèmes après, n'hésitez pas à revenir à ce chat.", "please_head_over_to_our_online_feedback_hub": "Veuillez vous rendre sur notre plateforme de rétroaction en ligne pour soumettre vos commentaires généraux ou vos demandes de fonctionnalités !", "share_feedback": "Partager des commentaires →", "we_are_unable_to_assist_with_account": "Nous ne pouvons pas vous aider avec les problèmes liés au compte ou à la facturation via le bot virtuel. Pour obtenir de l'aide, veuillez consulter nos [articles d'assistance en libre-service](https://support.wemod.com). Si vous ne trouvez pas la réponse que vous cherchez, n'hésitez pas à nous contacter à [<EMAIL>](mailto:<EMAIL>). Notre équipe d'assistance se fera un plaisir de vous aider.", "ill_help_you_troubleshoot_hotkey_issues": "Je vais vous aider à résoudre les problèmes de raccourcis clavier. Utilisez-vous les touches du pavé numérique pour les raccourcis ?", "make_sure_numlock_is_turned_on": "Assurez-vous que le Verrouillage Num est activé. Les raccourcis clavier du pavé numérique ne fonctionneront pas si le Verrouillage Num est désactivé. Cela a-t-il résolu votre problème ?", "great_numlock_key_is_often_overlooked": "Super ! La touche Verrouillage Num est souvent négligée mais cruciale pour les raccourcis du pavé numérique. Y a-t-il autre chose avec lequel vous avez besoin d'aide ?", "overlay_basic_troubleshoot": "Je vais vous aider à résoudre les problèmes de superposition. Passons en revue ces vérifications rapides", "overlay_basic_troubleshoot_1": "Utilisez-vous une copie authentique du jeu achetée dans un magasin officiel ? Nous ne pouvons pas garantir que la superposition fonctionnera correctement avec les versions non officielles.", "overlay_basic_troubleshoot_2": "Utilisez-vous la dernière version de WeMod ?", "overlay_basic_troubleshoot_2_update_version": "Il semble que vous utilisiez une version obsolète de WeMod. Veuillez mettre à jour vers la dernière version et réessayer.", "overlay_basic_troubleshoot_confirmation": "L'une de ces actions a-t-elle aidé à résoudre votre problème de superposition ?", "lets_troubleshoot_game_crashes": "Passons en revue ces étapes de dépannage pour résoudre les crashes de votre jeu. Quand le crash se produit-il ?", "when_launching_the_game": "Au lancement du jeu alors que WeMod fonctionne", "when_activating_specific_mod": "Lors de l'activation d'un mod spécifique", "random_times_during_gameplay": "À des moments aléatoires pendant le jeu lorsque des mods sont actifs", "when_using_the_overlay": "Lors de l'utilisation de la superposition", "crash_troubleshooting": "Passons en revue ces étapes de dépannage pour résoudre les crashes :", "crash_troubleshooting_1": "Vérifiez si vous utilisez des mods tiers ou des mods Nexus (essay<PERSON> de les désactiver)", "crash_troubleshooting_2": "Assurez-vous d'utiliser une copie légitime du jeu d'un magasin officiel", "crash_troubleshooting_3": "WeMod est conçu uniquement pour le jeu solo - évitez de l'utiliser en multijoueur", "crash_troubleshooting_4": "Assurez-vous d'utiliser la dernière version de l'entraîneur pour votre jeu", "crash_troubleshooting_5": "Assurez-vous d'utiliser la dernière version de WeMod", "crash_troubleshooting_6": "Essayez de désactiver la superposition si votre jeu dispose de cette fonctionnalité", "verify_game_files": "Essayons de vérifier vos fichiers de jeu, ce qui résout souvent les problèmes de plantage :", "verify_game_files_1": "Pour Steam : <PERSON><PERSON> droit sur le jeu > Propriétés > Fichiers locaux > Vérifier l'intégrité", "verify_game_files_2": "Pour Epic : Bibliothèque > ⋮ sur le jeu > Vérifier", "verify_game_files_3": "Pour GOG : Bibliothèque > Plus > G<PERSON>rer l'installation > Vérifier/R<PERSON>parer", "verify_game_files_4": "Pour Xbox/Microsoft : Paramètres > Applications > Applications et fonctionnalités > [Jeu] > Options avancées > R<PERSON>parer", "verify_game_files_confirmation": "Veuillez exécuter le processus de vérification pour votre lanceur de jeu et faites-moi savoir si cela aide.", "crash_troubleshooting_confirmation": "Est-ce que l'une de ces étapes a aidé ?", "great_game_file_verification_often_fixes": "Génial ! La vérification des fichiers du jeu résout souvent les données de jeu corrompues qui provoquent des plantages. Y a-t-il autre chose avec lequel vous avez besoin d'aide ?", "advanced_crash_troubleshooting": "Essayons des solutions avancées :", "advanced_crash_troubleshooting_1": "Essayez de lancer à la fois le jeu et WeMod en tant qu'administrateur", "advanced_crash_troubleshooting_2": "Mettez à jour vos pilotes graphiques vers la dernière version", "advanced_crash_troubleshooting_3": "Désactivez les superpositions GPU ou les logiciels d'enregistrement (GeForce Experience, AMD Radeon Software)", "advanced_crash_troubleshooting_4": "Vérifiez le Visualiseur d'événements Windows pour les journaux de plantage", "advanced_crash_troubleshooting_5": "Essayez un démarrage propre (désactiver les services de démarrage non essentiels)", "advanced_crash_troubleshooting_confirmation": "Est-ce que l'une de ces solutions a aidé à résoudre les plantages ?", "still_crashing": "Toujours des plantages", "excellent_im_glad_we_were_able_to_resolve_crashing": "Excellent ! Je suis content que nous ayons pu résoudre le problème de plantage. Y a-t-il autre chose avec lequel vous avez besoin d'aide ?", "great_im_glad_that_helped": "Génial ! Je suis content que cela ait aidé. Y a-t-il autre chose avec lequel vous avez besoin d'assistance ?", "yes": "O<PERSON>", "no": "Non", "that_fixed_it": "<PERSON>la a résolu le problème !", "yes_its_fixed_now": "<PERSON><PERSON>, c'est résolu maintenant !", "no_still_having_issues": "Non, j'ai toujours des problèmes", "continue_anyway": "Continuer quand même", "i_have_another_issue": "J'ai un autre problème", "no_thats_all_thanks": "Non, c'est tout - merci !", "further_troubleshooting_needed": "Il semble que nous aurons besoin d'un dépannage supplémentaire pour résoudre ce problème. Notre équipe de support sur Discord est prête à aider ! Rendez-vous sur [notre serveur Discord →](https://www.wemod.com/discord) et commencez un nouveau fil. Copiez et collez le résumé du dépannage ci-dessous dans votre fil Discord pour une assistance plus rapide :", "copy": "<PERSON><PERSON><PERSON>", "sorry_something_went_wrong": "<PERSON><PERSON><PERSON><PERSON>, j'ai quelques difficultés en ce moment. 😅 Réessayez dans un instant, ou [contactez le support](https://support.wemod.com/).", "minimize": "<PERSON><PERSON><PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>"}, "live_location_announcement_dialog": {"introducing_the_all_new": "Présentation du tout nouveau", "live_location_tracking": "Suivi de localisation en direct", "see_your_position_in_real_time": "Suivez votre position exacte en temps réel lorsque vous explorez l’univers des jeux compatibles. Disponible pour les membres **PRO** dans l’overlay et l’application de bureau."}, "trainer_notes_dialog": {"almost_there": "C’est presque terminé !", "lets_get_you_set_up": "Configurons votre expérience", "back": "Retour", "est_time": "Temps estimé", "minute_abbreviation": " min.", "dismissable_note": "**Remarque :** bien que la plupart des jeux soient jouables en un clic, certains nécessitent d’être configurés.", "dismiss": "<PERSON><PERSON><PERSON>", "dont_show_again": "Ne plus afficher ceci", "dont_show_again_description": "V<PERSON> de<PERSON> terminer la lecture des instructions avant de jouer."}, "trainer_notes_dialog_small": {"setup_instructions": "Instructions de configuration", "got_it": "<PERSON><PERSON><PERSON>"}, "title_help_button": {"find_instructions_here": "Vous pouvez revenir aux instructions de configuration en cliquant sur le menu déroulant des paramètres."}, "help_menu": {"setup_information": "Informations de configuration", "help_menu": "<PERSON><PERSON> d'aide", "report_an_issue": "Signaler un problème", "general": "Général", "submit_feedback": "Envoyer", "feature_request": "De<PERSON>e de fonctionnalité", "suggest_a_mod": "Proposer un mod"}, "suggest_mod": {"title": "Proposer un mod", "description": "Vos suggestions et boosts aident à guider nos créateurs de mods pour construire les mods qui comptent le plus pour vous.", "most_popular_suggestions": "Suggestions les plus populaires", "related_suggestions": "Suggestions associées", "not_updated_recently": "*Important :* Ce jeu n'a pas été mis à jour depuis plus de 90 jours. L'examen des suggestions de mods par le créateur ne se produit que pendant les mises à jour du jeu et du mod. Souhaitez-vous continuer quand même ?", "not_updated_recently_ack": "<PERSON><PERSON>, <PERSON>r", "creator_review_note": "Le créateur examine les suggestions de mods uniquement lors des mises à jour du jeu et des mods.", "start_typing": "<PERSON><PERSON> du texte…", "boost": "Boost", "no_boosts_available": "Pas de Boosts Disponibles", "pro": "PRO", "no_freeform_note": "*Remarque :* En raison de limitations techniques, les suggestions personnalisées ne peuvent pas être acceptées pour le moment. Veuillez choisir parmi les options disponibles dans le menu déroulant.", "refund_note": "*Remarque :* Vos boosts vous seront entièrement remboursés si les créateurs déterminent que les suggestions de mod ne peuvent être mises en œuvre en raison de contraintes techniques ou de limitations du jeu.", "error_message": "Une erreur s’est produite lors de la suggestion. Veuillez réessayer plus tard.", "boost_error_message": "Une erreur s’est produite lors de l’activation du boost. Veuillez réessayer.", "boost_success_message": "Suggestion boostée avec succès !", "mod_suggestions": "Suggestions de Mod", "your_suggestions": "Vos Suggestions", "new": "nouveau", "needs_review": "Be<PERSON>in de Révision", "$x_new": "$x Nouveau(x)", "no_suggestions": "Pas de suggestions de mod approuvées pour le moment", "boosting_disabled_for_suggestions_under_review": "Le boost est désactivé pour les suggestions en cours de révision.", "successfully_boosted_$suggestion": "Vous avez boosté avec succès *$suggestion* !", "continue": "<PERSON><PERSON><PERSON>", "and_$x_more": "et $x de plus"}, "capture": {"one_$title_highlight_saved": "1 $title meilleur moment enregistré !", "$count_$title_highlights_saved": "$count $title meilleurs moments enregistrés !", "view_captures": "<PERSON><PERSON><PERSON><PERSON>r le dossier des vidéos ↗"}, "capture_settings": {"capture_location": "Dossier vidéo", "capture_location_description": "Le dossier où vos meilleurs moments sont enregistrés", "view_captures": "<PERSON><PERSON><PERSON><PERSON>r le dossier des vidéos ↗"}, "instant_highlight_announcement_dialog": {"introducing_the_all_new": "Présentation du tout nouveau", "instant_highlights": "Moments Forts Instantanés", "never_miss_a_gaming_highlight_again": "Ne manquez plus jamais un moment fort de jeu ! Sauvegardez instantanément vos 15 à 60 dernières secondes de jeu en appuyant simplement sur une touche de raccourci.\nUtil<PERSON>z le WeMod Overlay ou appuyez sur **$hotkey** pour sauvegarder votre clip.", "lets_go": "C'est parti !", "not_now": "Peut-être plus tard", "okay": "<PERSON><PERSON><PERSON>"}, "overlay_instant_highlights_announcement_notification": {"new_instant_hightlights": "Nouveau ! *Moment Fort Instantané*", "press_$hotkey_to_clip_the_last_$secondss": "Appuyez sur *$hotkey* pour capturer les dernières $secondss"}, "overlay_highlight_save_success_notification": {"highlight_saved": "Moment fort enregistré"}, "my_videos": {"title": "<PERSON>s vid<PERSON>", "favorites": "<PERSON><PERSON><PERSON>", "all": "Tous", "game": "<PERSON><PERSON>", "recently_recorded": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sort_by": "Trier par", "no_results": "<PERSON><PERSON>ne vidéo trouvée", "no_results_advice": "Capturez un moment fort instantané pendant le jeu pour commencer", "no_results_advice_search": "Essayez de chercher un autre jeu, ou découvrez les titres populaires", "no_results_advice_favorites": "Ajoutez quelques vidéos à vos favoris pour commencer", "group_name_today": "Today", "group_name_yesterday": "<PERSON>er", "group_name_last_week": "Last Week", "group_name_past": "Past", "search_placeholder": "Search videos", "search_button": "<PERSON><PERSON><PERSON>", "recorded": "Recorded", "file_already_exists": "A file with this name already exists.", "file_rename_failed": "Failed to rename file. Please try again.", "open_file_location": "Open file location ↗", "introducing_the_all_new": "Présentation du tout nouveau", "instant_highlights": "Moments Forts Instantanés", "never_miss_a_gaming_highlight_again": "Ne manquez plus jamais un moment fort de jeu ! Enregistrez instantanément vos 15 à 60 dernières secondes de jeu avec une simple pression sur une touche de raccourci. Utilisez la superposition WeMod ou appuyez sur **$hotkey** pour sauvegarder votre clip.", "dismiss": "<PERSON><PERSON><PERSON>"}, "edit_input": {"save": "Enregistrer", "cancel": "Annuler"}}
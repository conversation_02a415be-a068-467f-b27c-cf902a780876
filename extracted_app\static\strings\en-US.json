{"tray_notification": {"wemod_minimized": "WeMod Minimized", "wemod_is_running_in_the_tray": "<PERSON><PERSON><PERSON> is running in the tray. Right-click the icon to exit."}, "app_update_toast": {"update_failed": "An error occurred while installing an app update.", "retry": "Retry", "update_available": "Update for WeMod available.", "restart_now": "Restart Now", "restart_later": "<PERSON><PERSON>", "wemod_will_auto_restart": "WeMod will auto-restart soon to apply an update.", "changelog": "Changelog"}, "beta_toast": {"running_beta_version": "You’re running a beta version of WeMod! Please report bugs on the [forum](https://community.wemod.com).", "visit_forum": "Visit Forum"}, "app_header": {"home": "Home", "explore": "Explore", "upcoming": "Upcoming", "my_games": "My games", "maps": "Maps", "my_videos": "My videos"}, "app_sidebar_now_playing": {"now_playing": "Now Playing"}, "auth": {"welcome_back": "Welcome back!", "log_in": "Log in", "email_or_username": "Email or username", "password": "Password", "forgot_password": "Forgot password?", "create_an_account": "Create an account", "invalid_info": "Invalid email/username and password.", "new_user": "Welcome to WeMod", "already_have_an_account": "Already have an account?", "log_in_now": "Log in now", "create_account": "Create account", "email_address": "Enter your email", "email_taken": "Email already used by another account", "invalid_email": "Invalid email", "there_was_a_problem": "There was a problem creating your account", "invalid": "The email you entered is invalid. Please check it and try again.", "misspelled_email_$email": "Your email (**$email**) may have been misspelled. Please double check that it’s correct.", "looks_good": "Looks Good", "cancel": "Cancel", "review_terms": "By clicking ‘Continue’, I agree to WeMod’s [Terms and Conditions](website://terms) and [Privacy Policy](website://privacy).", "new_to_wemod": "New to We<PERSON><PERSON>?", "get_mods_for": "Get mods for:", "continue": "Continue", "back": "Back", "unlock_$x_mods_for": "Unlock $x mods for:", "unlock_one_mod_for": "Unlock one mod for:", "unlock_mods_for": "Unlock mods for:"}, "url_handler": {"failed_to_open_$url": "Failed to open $url", "copy_url": "Copy URL"}, "uri_handler": {"unsupported_message": "The link you followed is not supported by this version of WeMod. Please update your app and try again."}, "game": {"discussion": "Discussion", "required_reading": "Read before playing", "your_mods_may_not_work": "Your mods may not work if you don't follow these instructions.", "some_mods_may_not_work_update": "A few mods are being updated and may not work as expected. Most mods remain fully functional.", "learn_more": "Learn more", "read_notes": "Read notes", "got_it": "Got it", "close": "Close", "play": "Play", "cheats_might_not_work_dialog": "A few mods are being updated and may not work as expected. Most mods remain fully functional.", "cant_connect_to_wemod": "Can’t connect to WeMod.", "retry": "Retry", "exit_confirm_dialog": "The mods are still running. Are you sure you want to exit?", "these_mods_have_been_retired": "These mods have been retired due to their incompatibility with newer game versions. They might still work with older game versions, but we no longer support this game.", "incompatible_and_retired": "Incompatible and retired"}, "trainer_meta": {"$players_members_play_this": "**$players** members play this", "$players_players": "$players players", "played_by": "Played by", "created_by": "Created by", "have_a_question": "Have a question?", "gameplay_video": "Gameplay video", "last_updated_$date": "Last updated **$date**", "overview_of_$game_mods": "Overview of $game mods", "video": "Video", "version": "Version:", "game_version_copied": "Game version copied to clipboard."}, "update_pending": {"update_pending": "Some mods are being updated", "requires_testing": "Requires **testing**", "$game_for_$platform_is_$position_in_the_queue": "**$game** for $platform is currently **#$position** in our upcoming games queue.\nTime to update will depend on its order in the queue and the effort involved in creating and testing mods for this game and games ahead of it.", "$game_for_$platform_is_$position_in_the_queue_modal": "**$game** for $platform is currently **#$position** in our upcoming games queue. The mods may not work as expected until they are updated. \n **Note:** The time to update will depend on its order in the queue and the effort involved in creating and testing mods for this game and games ahead of it.", "$game_was_updated_on_$platform_and_needs_to_be_tested": "**$game** was updated on $platform and needs to be tested. These mods may require an update.", "cant_wait_boost_it": "Can’t wait? Boost it", "okay": "Okay"}, "titles": {"explore": "Explore", "recently_played": "Recently played", "most_popular": "Most popular", "browse_by_genre": "Browse by genre", "all": "All", "new_and_updated_games": "New and recently updated", "trending_free_games_to_install": "Trending free games to install", "see_all": "See all", "view_all": "View all", "upcoming_games_and_updates": "Upcoming games and updates", "see_upcoming_games": "See upcoming games", "featured_games": "Featured games", "no_results": "No results", "no_results_advice": "Try to modify your search", "back": "Back to explore", "search": "Search", "$genre_games": "$genre Games"}, "dashboard": {"announcements": "Announcements", "objectives": "Objectives", "most_popular": "Most popular", "see_all": "View all", "my_games": "My games", "free_games": "Free games to install", "my_games_coaching_tip_header": "We found your games!", "my_games_coaching_tip_message": "Great news! There are mods available for the installed games listed below. Select a title to see all available mods.", "free_games_coaching_tip_header": "Free games, free mods", "free_games_coaching_tip_message": "Try one of these popular free games to get started using WeMod’s thousands of mods.", "see_upcoming_games": "See upcoming games"}, "app_rating_dialog": {"how_are_you_liking_wemod_so_far": "How are you liking WeMod so far?", "thanks_for_the_feedback": "Thanks for the feedback!", "what_can_we_do": "What can we improve on to make that 5 stars?", "glad_youre_enjoying_the_app": "Awesome, glad you’re enjoying the app!", "review_on_$x": "Would you mind putting in a good word for us on $service?", "submit": "Submit", "sure": "Sure!", "no_thanks": "No Thanks"}, "failed_payment_dialog": {"your_pro_payment_is_due": "Your *Pro* payment is due", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_monthly": "Click below to add or update payment for your subscription of $x/mo.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_daily": "Click below to add or update payment for your subscription of $x/day.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_weekly": "Click below to add or update payment for your subscription of $x/week.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_quarterly": "Click below to add or update payment for your subscription of $x/3 mo.", "click_below_to_add_or_update_payment_for_your_subscription_of_$x_yearly": "Click below to add or update payment for your subscription of $x/year.", "add_payment": "Add payment", "without_payment_youll_lose_these_benefits": "Without payment, you lose Pro and **these benefits**"}, "reactivate_pro": {"youre_a_pro_again": "You’re a Pro again. Thanks for the support!"}, "features": {"interactive_controls": "Interactive *controls*", "interactive_controls_description": "Activate mods with *easy controls*.", "interactive_controls_info": "If you have a second monitor, the interactive controls make enabling mods even easier.", "remote_app": "Remote *app*", "remote_app_description": "Forget hotkeys.\nUse *your phone* to control mods.", "remote_app_info": "Easily keep track of which mods are enabled and get precise control over them. The Remote mobile app connects seamlessly to the WeMod desktop app using an authentication code.", "save_cheats": "*Save* mods", "save_cheats_description": "No more configuring mods every time you play. Set ’em and forget ’em.", "save_cheats_info": "Easily jump back into games by auto-applying your previous mod settings.", "overlay": "In-game *overlay*", "overlay_description": "Bring your mod controls in-game for *faster gameplay*.", "overlay_info": "The overlay brings mods as close to the game as possible by appearing directly on top of it."}, "payment_method": {"credit_card": "Credit card", "paypal": "PayPal", "amazon_pay": "Amazon Pay", "pay": "Pay", "alipay": "Alipay", "direct_debit": "Direct Debit", "kr_market": "South Korea Market", "kakao_pay": "Kakao Pay", "kr_card": "Korean Card", "naver_pay": "Naver Pay", "ending_in_digits": "Ending in $digits", "expiration_$month_$year": "Expiration date $month/$year"}, "settings_dialog": {"view_updates": "View updates", "app_version_copied_toast": "App version copied to clipboard.", "settings": "Settings", "close_to_tray": "Close to tray", "close_to_tray_description": "Have WeMod minimize to the system tray when you close the app.", "language": "Language", "language_description": "Change the app’s language.", "anonymous_reporting": "Anonymous reporting", "anonymous_reporting_description": "Report screen views and other anonymous info to help improve WeMod.", "release_channel": "Release channel", "release_channel_description": "Try out new features before they’re unleashed to the world.", "cheat_sounds": "Mod sounds", "cheat_sounds_description": "Play sounds when activating a mod.", "cheat_volume": "Mod volume", "cheat_volume_description": "Change the volume of mod sounds.", "sound_pack": "Sound pack", "sound_pack_description": "Choose a sound pack for activating mods.", "theme": "Theme", "theme_description": "Change the look and feel of the app.", "general": "General", "customization": "Customization", "my_account": "My account", "profile": "Profile", "billing": "Billing", "password": "Password", "disable_hotkeys": "Disable hotkeys", "disable_hotkeys_description": "Turn off the ability to control mods with hotkeys.", "save_cheats": "Save mods", "save_cheats_description": "Automatically remember mod settings for all games.", "accessibility": "Accessibility", "use_windows_high_contrast_mode": "Use Windows contrast theme", "use_windows_high_contrast_mode_description": "Allow Windows to change the colors of the app to improve contrast.", "notifications": "Notifications", "followed_games": "Followed games", "desktop_notifications": "Desktop notifications", "desktop_notifications_description": "Enable desktop notifications when we update or add support for a game you’re following.", "disable_assistant": "Disable AI Game Guide", "disable_assistant_description": "Hide the AI Game Guide on the Game Page", "disable_auto_pins": "Disable Auto-Pinned Mods", "disable_auto_pins_description": "Disable the automatically pinned mods on the Game Page", "reduce_motion": "Reduce motion", "reduce_motion_description": "Reduce animations and hover effects", "enable_overlay": "Enable New WeMod Overlay", "enable_overlay_description": "Enable the New WeMod In-Game Overlay to access mods and other features while playing. Replaces the Game Bar overlay", "overlay_hotkey": "Overlay hotkey", "overlay_hotkey_description": "Select the hotkey used to toggle the New WeMod In-Game Overlay", "changelog": "Changelog", "capture": "Videos", "capture_quality_preset": "Video quality", "capture_quality_preset_description": "Select the resolution and frame rate of recorded game videos", "enable_capture": "Enable game videos", "enable_capture_description_windows_10": "Enable game videos to record highlights of your gameplay.\n_Windows 10 may show a yellow border when capturing videos.\nConsider upgrading to Windows 11._", "enable_capture_description": "Enable game videos to record highlights of your gameplay", "capture_highlight_length": "Highlight length", "capture_highlight_length_description": "Select the length of game highlights", "capture_highlight_length_$seconds_seconds": "Last **$seconds** seconds", "capture_quality_preset_$resolution@$fps": "**$resolution** @$fpsfps", "capture_audio_device": "Audio", "capture_audio_device_description": "Capture audio from the default audio device", "capture_audio_enabled": "Enabled", "capture_audio_disabled": "Disabled"}, "language_selector": {"automatic_$lang": "Automatic ($lang)"}, "remote_tooltip": {"click_to_use_the_wemod_remote": "<PERSON><PERSON> to use the WeMod Remote", "connect_to_wemod_remote": "Connect to WeMod Remote", "enter_this_pin_on_your_device_to_connect": "Enter this pin on your device to connect and control mods from your phone.", "connected": "Connected", "disconnect": "Disconnect", "get_the_app": "Get the mobile app", "scan_the_qr_code_or_visit_the_site": "Scan the QR code or visit [wemod.com/remote]($url) on your mobile device.", "reconnect": "Reconnect", "force_disconnected_message": "It looks like you’re using WeMod on another PC, so we’ve disconnected this PC from the remote.", "disconnect_remote_app": "Disconnect Remote App"}, "user": {"go_pro": "Go Pro", "support_wemod": "Support WeMod"}, "sidebar_user": {"go_pro": "Go _Pro_", "go_pro_collapsed": "Go _Pro_", "support_wemod": "Support _WeMod_"}, "custom_installation_selector": {"not_an_exe_toast": "Not an EXE file", "select_your_game_exe": "Select Your Game EXE", "pick_this_game_exe": "Pick This Game EXE", "exe_files": "EXE Files", "custom_game_exe": "Custom game .exe", "add_game_exe": "Add game .exe", "or_drag_the_file_here": "or drag the file here", "custom_exe_info": "If WeMod hasn’t automatically found your game, you’ll have to manually add the game's shortcut or executable (.exe) file."}, "feedback_dialog": {"how_was_playing_$game_with_wemod": "How was playing $game with WeMod?", "say_thanks_to_the_developer": "Say thanks to the developer", "type_here": "Type here", "submit": "Submit", "allow_this_to_be_posted_publicly": "Allow this to be posted publicly", "did_you_read_the_notes": "Did you read the notes before starting the game?", "report_problem": "Report Problem", "cancel": "Cancel", "which_best_describes_experience": "Which option best describes your experience?", "my_game_crashed": "My Game Crashed", "cheats_broken_or_confusing": "Mods Broken or Confusing", "other_issue": "Other Issue", "where_were_you_in_the_game_when_it_crashed": "Where were you in the game when it crashed?", "main_menu_or_before": "Main menu or before", "cutscene_or_loading_screen": "Cutscene or loading screen", "in_game_playing": "In-game; playing", "in_game_menu_inventory": "In-game; menu / inventory", "other": "Other", "any_extra_details_that_can_help_us": "Any extra details that can help us?", "please_describe_the_problem": "Please describe the problem you’re experiencing with these mods.", "extra_details_example": "e.g. gold edition dlc installed; game crashes after first level loading screen when unlimited health is enabled", "cheat_log_will_be_sent": "A log of which mods were used will be sent with your feedback.", "submit_feedback": "Submit <PERSON>", "which_cheats_experienced_trouble": "Which mods were malfunctioning?", "all_of_the_cheats": "All of the mods", "additional_info_example": "Optional additional info (e.g. didn’t work on level 2 boss fight)", "please_describe_the_issue_in_detail": "Please describe the issue in detail:", "other_details_example": "e.g. nothing seems to work in realism mode...", "didnt_work_no_effect": "Didn’t work; no effect", "game_crash": "Game crash", "not_as_expected": "Not as expected", "optional_additional_info": "Optional additional info (e.g. didn’t work on level 2 boss fight)", "select_a_cheat": "Select a Mod", "select_a_reason": "Select a Reason", "select_a_location": "Select a Location", "allow_posting_publicly": "Allow this to post publicly", "send": "Send"}, "trainer_cheats_list": {"press_$x_above_to_get_started": "Press “$x” above to get started", "press_$x_to_use_mod": "Press “$x” to use this mod", "only_for_pro_members": "Only for **PRO** members.", "upgrade_now": "Upgrade now", "apply": "Apply", "button_set": "Set", "button_add": "Add", "category_cheats": "mods", "category_enemies": "enemies", "category_game": "game", "category_inventory": "inventory", "category_physics": "physics", "category_player": "player", "category_stats": "stats", "category_teleport": "teleport", "category_vehicles": "vehicles", "category_weapons": "weapons", "category_challenge": "Challenge Mods", "category_pinned": "pinned", "cheat_instructions": "Mod instructions", "pro": "Pro", "only_pro_members_can_use_beta_mods": "Only **PRO** members can use beta mods.", "beta": "Beta", "disabled_play_tooltip": "Some mods require the game to be running due to mod compatibility or stability reasons.", "disabled_add_or_install_tooltip": "Add or Install your game to use mods with it.", "broken_mod_hint": "This mod is temporarily disabled while we update it", "auto_pin_setting_toast": "You can reset your auto pins in Mod Settings", "pin": "<PERSON>n", "unpin": "Unpin", "off": "OFF", "on": "ON", "loop": "Loop", "cancel": "Cancel", "start_loop": "Start Loop", "on_after": "ON after", "off_after": "OFF after", "mod_timer_message_on": "The selected mod will turn ON after selected duration.", "mod_timer_message_off": "The selected mod will turn OFF after selected duration.", "mod_timer_message_loop": "The selected mod will loop between ON and OFF for the selected durations.", "minute": "m", "dismiss": "<PERSON><PERSON><PERSON>", "unlock_mod_timers": "Unlock Mod Timers", "join_now": "Join Now"}, "trainer_hotkey": {"not_set": "Not Set", "numpad_$x": "Numpad $x", "key_control": "Ctrl", "key_alt": "Alt", "key_insert": "Ins", "key_delete": "Del", "key_page_up": "PgUp", "key_page_down": "PgDn", "key_end": "End", "key_home": "Home", "key_left": "Left", "key_up": "Up", "key_right": "Right", "key_down": "Down", "key_scroll_lock": "ScrLk", "key_print_screen": "PrtSc", "key_pause": "Pause", "action_apply": "Apply", "action_toggle": "Toggle", "action_decrease": "Decrease", "action_increase": "Increase", "action_previous": "Previous", "action_next": "Next", "hotkey_tooltip": "Please press the key combination now"}, "trainer_launcher": {"newer_version_required": "You need a newer version of WeMod to use these mods.", "game_already_running": "These mods require the game to be launched by WeMod. Please close your game before pressing Play.", "game_not_running": "The game must be launched outside of WeMod before pressing Play.", "elevation_denied": "WeMod can’t access your game because it’s running as an Administrator.", "cannot_download_cheats": "We’re having trouble downloading the mods. Please try again later.", "cannot_find_dll": "Trainer DLL not found. Make sure it’s built!", "cheats_missing": "The mods are missing from your computer. Please make sure WeMod is allowed by your anti-virus software before continuing.", "close": "Close", "fix": "Fix", "auto_fix_av_failed": "We couldn’t fix the issue automatically. Please check your anti-virus software for more details.", "ok": "OK", "files_missing": "Files that WeMod requires to enable mods are missing from your computer. This is typically due to anti-virus software incorrectly detecting them as malicious files.\n\nTo continue, you may need to temporarily disable your anti-virus after reinstalling WeMod.", "reinstall_now": "Reinstall Now", "trouble_starting_or_finding": "We’re having trouble starting or finding your opened game.", "retry": "Retry", "x64_expected": "These mods were made for the 64-bit version of the game. You’re running the 32-bit version.", "x86_expected": "These mods were made for the 32-bit version of the game. You’re running the 64-bit version.", "trouble_loading_cheats": "We’re having trouble loading the mods into your game. Try restarting the game, or pressing *Help* for more info.", "report_problem": "Report Problem", "preparation_failed": "We’re having trouble preparing to load the mods. You may have to allow WeMod through your firewall or anti-virus software.", "trouble_running_cheats": "We’re having trouble running the mods. You may have to allow WeMod through your firewall or anti-virus software.", "activation_problem": "There was a problem loading the mods. Your game version might not be compatible or you may have to try again at a different point in the game.", "activation_prevented": "There was a problem loading the mods. If you didn’t close the game, your game version might not be compatible or you may have to allow WeMod through your firewall or anti-virus software.", "launch_outside_wemod": "Launch outside WeMod"}, "trainer_play_button": {"loading_mods": "Loading mods...", "playing": "Playing", "play": "Play", "install_for_free": "Install for free", "install": "Install", "play_coaching_tip_header": "Let’s go!", "play_coaching_tip_message": "Ready to try out your new mods? Launch the game directly from WeMod.", "install_coaching_tip_install_game_header": "Install or Find Game", "install_coaching_tip_message": "Click “Add game” to download on your platform of choice\nAlready have it installed? Use the “Add game” menu to find and add your game file.", "stop_playing": "Stop playing", "add_game": "Add game", "start": "Start"}, "active_trainer_toast": {"cheats_are_running": "Mods are running in the background.", "open": "Open", "end": "End"}, "game_feed": {"new": "new", "new_abbreviation": "n", "updated": "updated", "updated_abbreviation": "u", "last_played_$date": "Last played $date", "released_on_$date": "Released on $date", "community_choice_award_winner": "Community Choice Award Winner", "no_cheats_available": "No mods available"}, "activity": {"was_updated_by_$creator": "was updated by $creator", "$creator_created_cheats_for": "$creator created mods for", "all": "All", "my_games": "My games", "get_notifications_for_games_you_care_about": "Get **notifications** for games you care about.", "follow_all": "Follow all", "your_notifications_are_set": "Your **notifications** are set, manage them in *settings*"}, "leave_feedback_dialog": {"what_are_your_overall_thoughts": "What are your overall thoughts of WeMod? What can we improve on?", "this_is_not_for_games_or_trainers": "This is not for specific games or mods!", "there_was_a_problem_toast": "There was a problem posting your feedback.", "submit": "Submit", "minimum_$x_characters": "Minimum $x characters"}, "objectives": {"title": "Objectives", "pro": "Pro", "objective_set_email": "Add an email for notifications", "objective_set_username": "Set your username", "objective_set_password": "Set a password", "objective_set_profile_image": "Change your profile image", "objective_leave_feedback": "Leave us feedback", "objective_join_discord": "Join us on **Discord**", "objective_join_community": "Join **the community**", "objective_like_on_facebook": "Like us on **Facebook**", "objective_follow_on_twitter": "Follow us on **X**", "objective_subscribe_on_youtube": "Subscribe to our **YouTube**", "objective_try_pro": "Try WeMod Pro for free", "objective_connect_remote": "Connect the mobile remote", "objective_install_overlay": "Install the in-game overlay", "objective_use_interactive_controls": "Use in-app mod controls", "objective_boost_a_game": "Boost a game in the upcoming queue", "objective_play_a_game": "Play first game with WeMod", "objective_use_hotkeys": "Activate mods using hotkeys", "get_started": "Get started", "complete_your_profile": "Complete your profile", "get_started_with_pro": "Get started with Pro", "1_boost": "+1 Boost", "$x_boosts": "+$x Boosts", "boosts": "Bo<PERSON><PERSON>", "claim_boost": "<PERSON><PERSON><PERSON>", "welcome_to_wemod": "Welcome to WeMod!", "streak": "Streak", "$x_day": "$x day", "$x_days": "$x days"}, "billing_settings": {"subscribed": "Subscribed", "$x_days_left_in_trial": "$x days left in trial", "next_billing": "Next billing", "until": "Until", "resume": "Resume", "remove": "Remove", "view_plan_details": "View plan details", "payment_method": "Payment method", "change_payment_method": "Change payment method", "add_payment_method": "Add payment method", "pro_subscription_end_on_$date_dialog": "Your Pro subscription will end on $date unless new billing information is added.", "no_payment_method": "No payment method", "upgrade_to_pro": "Upgrade to **Pro**", "get_the_best_experience_with_pro": "Get the best overall gaming experience with PRO", "get_pro_as_a_gift": "Get PRO as a gift", "upgrade": "Upgrade", "give_the_gift_of_pro": "Give the *Gift* of Pro", "share_an_elevated_modding_experience": "Share an elevated modding experience with your friends.", "gift_pro_to_a_friend": "Gift Pro to a friend", "gifting_disclaimer": "Need help with a gift you sent? Email [<EMAIL>](mailto:<EMAIL>).", "pro_plan": "**Pro** plan", "active_gift": "Active gift", "active_gift_yearly_info": "You received a free year of Pro as a gift from $sender.", "active_gift_monthly_info": "You received a free month of Pro as a gift from $sender.", "ends": "Ends", "payment_overdue": "Payment overdue", "save_$x_or_more": "Save $x% or more", "switch_to_yearly_payment": "Switch to yearly payment", "update_billing_address": "Please update billing address", "applicable_taxes": "applicable taxes", "last_amount_billed_$x_on_$date": "Last amount billed: $x on $date", "expires": "Expires"}, "change_password": {"current_password": "Current password", "new_password": "New password", "new_password_again": "New password, again", "create_password": "Create a password", "password_again": "Password, again", "save": "Save", "success_toast": "You successfully changed your password!", "problem_toast": "There was a problem changing your password.", "no_password_set": "No password set", "password_requirements_error": "Password must have at least 8 characters", "password_confirm_error": "Password and confirmation must match", "current_password_error": "Enter your current password"}, "general_settings": {"save_log": "Save log", "save_messages": "Save messages that’ll help the WeMod developers fix things.", "log_files": "Log Files", "failed_to_save_toast": "Failed to save log file. Please check the path and try again.", "gdpr_consent": "Use data to personalize WeMod for you", "gdpr_consent_description": "This allows us to use and process analytical information regarding how you use WeMod. It allows us to improve existing features, customize your experience, and deliver ads relevant to you as a free user.", "gdpr_revoke_confirm_message": "Are you sure? You’ll no longer be able use WeMod unless you accept our [Terms and Conditions](website://terms) and [Privacy Policy](website://privacy) regarding personalized content.", "ok": "OK", "cancel": "Cancel"}, "profile_settings": {"upload_new_pic": "Upload new pic", "username": "Username", "email_address": "Email address", "current_password": "Current password", "save": "Save", "username_change_success_toast": "You successfully changed your username!", "email_change_success_toast": "You successfully changed your email!", "profile_image_change_success_toast": "You successfully changed your profile image!", "update_error_toast": "An error occurred while updating your profile.", "logout": "Logout", "are_you_sure": "Are you sure you want to sign out of your account?", "sign_out": "Sign Out", "cancel": "Cancel", "hold_on": "Whoa, hold on a sec.", "no_email_for_$username": "Your account ($username) doesn’t have an email address or password associated with it yet. If you logout, you won’t be able to sign back in again!", "update_account": "Update Account"}, "theme_selector": {"default": "<PERSON><PERSON><PERSON>", "green": "Green", "orange": "Orange", "black": "Dark", "purple": "Purple"}, "release_channel_selector": {"app_upgrade": "App Upgrade", "channel_may_include_unstable_features": "This release channel may include new, but unstable features. If you run into any issues, please post on the forum or our Discord channel. If you wish to downgrade to another channel in the future, you’ll have to wait for the next update.", "continue": "Continue", "cancel": "Cancel", "app_downgrade": "App Downgrade", "youll_have_to_wait": "You’ll have to wait until the next release or reinstall the app to switch to this channel."}, "failed_payment_toast": {"update_payment": "Update your payment information to keep your Pro subscription!", "fix": "Fix"}, "online_status": {"offline_toast": "Can’t connect to WeMod. You’ll have limited access to mods until you reconnect.", "maintenance_toast": "WeMod is currently undergoing maintenance. We’ll be back up and running shortly!"}, "basic_dialog": {"ok": "OK", "yes": "Yes", "confirm": "Confirm", "no": "No", "cancel": "Cancel", "help": "Help", "dont_remind_again": "Don't remind me again"}, "time": {"$x_years": "$xy", "$x_year": "$xy", "$x_mo": "$xm", "$x_days": "$xd", "$x_day": "$xd", "$x_hr": "$x hr", "$x_min": "$x min", "$x_sec": "$x sec", "$x_years_ago": "$xy ago", "$x_year_ago": "$xy ago", "$x_mo_ago": "$xm ago", "$x_days_ago": "$xd ago", "$x_day_ago": "$xd ago", "$x_hr_ago": "$x hr ago", "$x_min_ago": "$x min ago", "$x_sec_ago": "$x sec ago"}, "toggle": {"on": "On", "off": "Off"}, "queue": {"upcoming": "Upcoming", "info_tooltip": "This is our upcoming games queue. See which games are being worked on, and boost the ones you’re excited to play. Games are developed in queue order, though release timing depends on the complexity and testing requirements of each game and those ahead of it in the queue.", "get_more": "Get more", "overview": "Overview", "needs_update": "Needs update", "new_releases": "New releases", "recently_completed": "Recently completed", "new": "New", "update": "Update", "the_creators": "The creators", "become_a_wemod_creator": "Become a WeMod creator", "join_our_creator_community": "Join our exclusive creator community and make money developing free mods & trainers for the WeMod app.", "apply": "Apply"}, "boost_button": {"no_boosts": "You don’t have any boosts! As a Pro member, you’ll get more every month.", "no_boosts_free": "Subscribe to WeMod Pro to get more boosts!", "an_error_occurred": "An error occurred while boosting. Please try again.", "you_boosted_$game": "You boosted **$game**. We’ll notify you when mods are released!", "dont_notify_me": "Don’t Notify Me", "notification_preferences_updated": "Notification preferences updated.", "failed_to_update_notification_preferences": "Failed to update notification preferences.", "boosts_available_after_trial_period": "Boosts are available after the trial period."}, "boost_balance_button": {"boosts_available_after_trial_period": "Boosts available after trial period"}, "unavailable_game": {"$game_for_$platform_is_$number_in_our_work_queue": "$game for $platform is **#$number** in our upcoming games queue.", "$game_for_$platform_is_assigned_to_$creator_and_is_$number_in_our_queue": "$game for $platform is assigned to $creator and is **#$number** in our upcoming games queue.", "the_time_until_its_released_will_depend": "The time until it’s released will depend on the overall order of the queue and the effort involved in creating and testing mods for this game and games ahead of it.", "this_game_is_not_supported": "This game is **not supported** by WeMod. This is due either to technical aspects that make it impractical to mod or to the possible multiplayer nature of the game.", "upcoming": "Upcoming", "stats": "Stats", "were_a_small_team_x_games": "We’re a small team of creators maintaining a library of over **$x** games. Game release timing will always depend on priority in the queue and the overall effort involved in creating and testing mods for each game. Want us to start work on a game sooner? Boost the game priority in the queue. ", "what_is_this_about": "What is this about?", "wemod_is_a_free_app_x_games": "WeMod is a free app with over **$x games** in it’s library. Being a small team, some things take time to add or update.", "the_queue_is_our_way_of_being_transparent_with_our_users": "The upcoming games queue is our way of **being transparent** with the community. We prioritize games in our queue using an algorithm that takes into account number of active players, time since a game was last updated, boosts, and user interest.", "view_upcoming_games": "View upcoming games", "this_week": "This week", "this_month": "This month", "$x_games_added": "**$x** games added", "$x_games_updated": "**$x** games updated", "get_boosts": "Get Boosts", "boosts": "Bo<PERSON><PERSON>"}, "trainer_notify": {"no_cheats_yet": "We don’t have mods for this game yet. Let us know if you’re interested and we’ll email you once they’re added.", "do_you_want_us_to_notify_you_when_its_ready": "Do you want us to notify you when it’s ready?", "do_you_want_us_to_notify_you_when_we_update_it": "Do you want us to notify you when we update it?", "do_you_want_us_to_notify_you_if_we_update_it": "Do you want us to notify you if we update it?", "notify_me_when_mods_updated": "Get notified when mods are updated", "email_address": "Email address", "notify_me": "Notify me", "email_error": "There was a problem with the email address you provided", "well_send_you_an_email": "We’ll send you an email!", "well_send_you_an_email_when_available": "We’ll send you an email when mods are available!", "unfollow": "Unfollow", "request_mods": "Request Mods"}, "loading": {"tagline": "Your game, your rules", "loading_message": "Loading...", "checking_for_updates_message": "Checking for updates...", "installing_update_message": "Installing update...", "install_later": "Install later", "download_directly": "Download directly", "offline_message": "Your computer appears to be offline. An internet connection is required to start the app for the first time.", "maintenance_message": "WeMod is currently undergoing maintenance. We’ll be back up and running shortly!", "support_initialization_error_message": "An error occurred while loading WeMod. You may need to reinstall the app or allow it through your anti-virus software.", "reinstall": "Reinstall app", "more_info": "More info", "dotnet_error_message": "You need to have the correct version of the .NET framework installed", "download": "Download", "retry": "Retry", "review_terms": "To continue, please review our\n[Terms and Conditions](website://terms) and [Privacy Policy](website://privacy).", "accept_and_continue": "Accept and continue", "consent_error_message": "An error occurred. Please try again."}, "status_tag": {"new_game": "New game", "updated_game": "Updated game"}, "pro_popup": {"upgrade_to_pro": "Upgrade to **Pro**", "remote_app": "Remote **app**", "use_the_remote_on_your_phone": "Access the WeMod app on your phone or tablet to configure mods from a second screen.", "save_cheats": "**Save** mods", "save_cheats_info": "No more configuring mods every time you play. Set ’em and forget ’em.", "cheat_overlay": "In-game **overlay**", "an_in_game_menu": "Seamlessly fine-tune your mods without missing a moment."}, "remote_upgrade_dialog": {"the_remote_makes_it_easy": "The remote makes it easy", "simply_connect_the_desktop_app_to_your_mobile_device": "Simply connect the desktop app to your mobile device with a code and boom! Now you can see exactly what’s enabled, plus get more fine-grained control with sliders and drop-down menus.", "upgrade_now": "Upgrade now"}, "poll_dialog": {"submit": "Submit", "i_changed_my_mind": "I changed my mind", "cancel_my_subscription": "Cancel my subscription"}, "overlay": {"start_game_message": "To access your mods in this overlay, launch your game from the WeMod app.", "loading_cheats_message": "Loading mods...", "notes": "Notes", "view_notes": "View notes", "cheat_instructions": "Mod instructions", "mods": "Mods", "maps": "Maps", "game_guide": "Game Guide"}, "overlay_tooltip": {"click_to_use_the_overlay": "Click to use the legacy overlay", "unsupported": "Unsupported", "your_pc_does_not_meet_the_requirements": "Your PC does not meet the minimum requirements to use the legacy overlay.", "requires_version": "Requires Windows 10 version $version or newer", "not_installed": "Install legacy overlay", "install_the_overlay_from_the_microsoft_store": "For games that don't yet support our new overlay, install our legacy overlay from the Microsoft Store.", "install_the_overlay": "Install legacy overlay", "enable_game_bar": "Enable Game Bar", "the_overlay_requires_game_bar": "The legacy overlay requires the Xbox Game Bar to be enabled on your PC.", "open_game_bar_settings": "Open Game Bar settings", "overlay": "Legacy overlay", "the_overlay_is_installed": "Access your mods by pressing ***Win* *G*** after starting a game in WeMod."}, "pro_onboarding_dialog": {"congrats": "Congrats!", "you_are_now_pro": "You are now **Pro**", "thanks_for_joining": "Thanks for joining the community! Now let’s **complete your setup**.", "overlay_requirements": "Requires the *Xbox Game Bar* to be enabled on your PC and Windows 10 version $version or newer.", "install_overlay": "Install overlay", "open_game_bar_settings": "Open Game Bar settings", "get_in_game_overlay": "Install In-Game **Overlay**", "bring_cheats_as_close_to_the_game_as_possible": "Bring your mods as close to the game as possible with the WeMod overlay. Once installed from the Microsoft Store, you can access your mods in-game by pressing ***Win* *G***", "connect_wemod_remote": "Connect **WeMod remote**", "get_our_mobile_app": "Get our **mobile app**: scan the QR code or visit *wemod.com/remote* on your mobile device.", "then_enter_this_pin": "Then, **enter this pin** on your phone to connect", "receive_special_role_in_discord": "Receive special role in **Discord**", "plus_now_that_youre_pro": "Plus! Now that you’re **Pro**,", "plus_with_your_pro_subscription": "Plus! With your **Pro** subscription,", "you_support_creators_and_wemod_development": "you **support creators & WeMod development**, have the ability to **suggest mods** for popular games, and receive **priority support**.", "skip_for_now": "Skip for now", "i_am_all_set_up_for_pro": "I am all set up for Pro", "connect_discord": "Connect Discord", "join_server": "Join server", "connected": "Connected", "installed": "Installed", "first_your_account_must_be_linked_to_discord": "First, your account must be linked to Discord by connecting below. Once connected and you have joined our server, then the bot will **update your role within 24 hours**.", "your_exclusive_pro_features": "Your exclusive **Pro** features", "were_happy_to_have_you": "We’re so happy to have you in our Pro community. Learn how to access all the **latest and greatest Pro features**.", "enable_save_mods": "Remember your mods with **save mods**", "save_mods_explanation": "Got your mods perfectly dialed in just the way you like? Save them that way and we’ll launch your next game with your **previous mod configurations**.", "how_it_works": "How it works", "access_in_app_controls": "Access **Interactive Controls**", "interactive_controls_explanation": "The interactive controls on the game page are another way of activating mods. Interactive controls include **sliders, drop-down menus, manual input, and buttons**.", "influence_game_priority": "Influence game priority with **added boosts**", "boosts_explanation": "Boosts are used to influence the **priority of games in the upcoming games queue**. The more boosts a game has, the more likely it is to be worked on sooner.", "choose_your_theme": "Choose your color **theme**", "themes_explanation": "Select your **look and feel of the app**. You can also change this anytime in settings.", "i_understand_the_pro_features": "I understand the Pro features", "disabled_game_bar_instructions": "Enable the **Xbox Game Bar** on your PC to use the WeMod Overlay.", "enable_xbox_game_bar": "Enable Xbox Game Bar", "save_mods": "Save mods"}, "remote_platforms": {"available_on_mobile": "Available on *iOS* and *Google Play*"}, "cancel_win_back_dialog": {"is_pro_not_for_you": "Is **Pro** not for you?", "wemod_pro_subscribers_enjoy_benefits": "WeMod Pro subscribers enjoy access to easier mod controls, faster gameplay, and influence over which games we support.", "by_canceling_pro_you_lost_these_benefits": "By canceling Pro you lose **these benefits**", "need_a_new_game_to_play": "Need a new game to play?", "wemod_offers_support_for_trending_free_games": "WeMod offers support for trending free games. Give one a try!", "need_help_with_pro": "Need help with Pro?", "visit_our_faq_or_email_us": "Visit our Pro [frequently asked questions](https://wemod.gg/pro-features) or email us at [<EMAIL>](mailto:<EMAIL>).", "continue_with_pro": "Continue with Pro", "i_still_wish_to_cancel": "I still wish to cancel", "see_all": "See all", "keep_supporting_the_creators": "Keep supporting the creators", "your_subscription_supports_the_creators": "Your subscription supports the creators who develop mods for your favorite games.", "$x_cheats_created": "*$x* mods created"}, "simple_pro_dialog": {"forget_hotkeys_with_pro": "Forget hotkeys with *Pro*", "did_you_know": "Did you know that with *WeMod Pro*, you can bring mod controls in-game with the WeMod overlay? *🤯* Plus, modify mods with the exclusive WeMod remote app and in-app controls.\n\nLess hotkey hassle, *more gaming*.", "start_free_trial": "Start free trial", "learn_more": "Learn more"}, "secure_account_dialog": {"password": "Password", "password_again": "Password, again", "save": "Save", "set_password_success_toast": "You successfully set your password!", "set_password_problem_toast": "There was a problem setting your password.", "set_email_success_toast": "You successfully set your email! Keep an eye on your inbox for a message from us to verify your email address.", "email_address": "Email address", "email_only_message": "Enter your email so you always have access to your Pro account.", "no_email_or_password_message": "Enter an email & password so you always have access to your account.", "no_password_message": "Enter a password so you always have access to your account.", "no_email_message": "Enter your email so you always have access to your account.", "create_your_login_credentials": "Create your login credentials", "password_requirements_error": "Password must have at least 8 characters", "password_confirm_error": "Password and confirmation must match"}, "pro_general": {"join_now": "Join Now"}, "save_cheats_tooltip": {"save_cheats": "*Save* mods", "save_cheats_info": "Save your mods so that they’re enabled every time you return to play."}, "save_cheats_tooltip_graphic": {"jump_height": "Jump Height", "extra_ammo": "Extra Ammo", "super_fast": "Super Fast", "unlimited_gold": "Unlimited Gold", "fly_mode": "Fly Mode"}, "save_cheats_toggle": {"save_cheats": "Save mods", "save_cheats_info": "Save your mods so that they’re enabled every time you return to play.", "no_eligible_cheats": "There are no mods eligible to be saved for this game. This can happen when every mod needs something to occur in-game before they work. Applying them on launch would result in an error.", "coaching_tip_header": "Quick Start with Save Mods", "coaching_tip_message": "Save your mods so that they’re enabled every time you return to play. Give it a try!"}, "pinned_mods_tooltip": {"unlock_pinned_mods": "<PERSON>lock Pinned Mods"}, "remote_app_tooltip": {"remote_app": "Remote App", "no_more_alt_tabbing": "No more alt-tabbing! Manage your mods and access maps on-the-go."}, "save_mods_tooltip": {"save_mods": "Save Mods", "save_mods_info": "Save your mods so that they’re enabled every time you return to play"}, "precision_mods_tooltip": {"precision_controls": "Precision Controls", "precision_mods_info": "Take your mods further with precise controls and complete customization.", "unlock_precision_controls": "Unlock Precision Controls", "join_now": "Join Now"}, "faux_mods_ui": {"save_mods": "Save Mods", "mod_unlimited_health": "Unlimited health", "mod_unlimited_stamina": "Unlimited stamina", "mod_game_speed": "Game speed", "mod_game_speed_value": "500", "mod_off": "Off", "mod_on": "On"}, "pro_badge": {"pro": "Pro"}, "save_cheats_disable_confirm_dialog": {"turn_off_save_cheats": "Turn off Save Mods?", "are_you_sure_message": "Are you sure you want to *turn off* Save Mods for this game?\n\nYour current mod configurations will not be saved.", "cancel": "Cancel", "turn_off": "Turn off", "dont_show_again": "Don’t show again"}, "save_cheats_icon": {"save_cheats_is_active": "Save mods is active", "save_cheats_is_inactive": "Save mods is inactive", "unlock_save_mods": "Unlock Save Mods", "join_now": "Join Now"}, "plan_details_dialog": {"pro": "Pro", "plan_details": "Plan details", "billed_monthly": "Billed monthly", "billed_yearly": "Billed yearly", "$x_days_left_in_trial": "$x days left in trial", "next_billing": "Next billing", "until": "Until", "your_pro_plan_includes": "Your PRO plan includes...", "in_app_controls": "In-app controls", "remote_mobile_app": "Remote mobile app", "in_game_overlay": "In-game overlay", "save_cheats": "Save mods", "game_boosting": "Additional game boosting", "exclusive_themes": "Exclusive themes", "discord_role": "Special role in Discord", "priority_support": "Priority support", "learn_more": "Learn more", "need_help": "Need help?", "help_message": "Visit our [frequently asked questions](https://wemod.gg/faq) or email us at [<EMAIL>](mailto:<EMAIL>).", "cancel_subscription": "Cancel subscription", "suggest_cheat_ideas": "Suggest mod ideas", "payment_overdue": "Payment overdue", "switch_to_yearly": "Switch to yearly", "save_$x_or_more": "Save $x% or more"}, "breadcrumbs": {"back_to_dashboard": "Back home", "back_to_titles": "Back to games", "back_to_free_games": "Back to free games", "back_to_my_games": "Back to my games", "back_to_queue": "Back to upcoming", "back_to_games": "Back to explore", "back_to_favorites": "Back to favorites", "back_to_game_pass": "Back to Game Pass", "back_to_most_popular": "Back to most popular", "back_to_community_choice": "Back to community choice", "back_to_recently_played": "Back to recently played", "back_to_all_supported_games": "Back to all supported games"}, "desktop_shortcut": {"play_$title": "Play $title"}, "genres": {"action": "Action", "adventure": "Adventure", "casual": "Casual", "fps": "FPS", "horror": "Horror", "indie": "Indie", "open_world": "Open World", "platformer": "Platformer", "puzzle": "Puzzle", "rpg": "RPG", "racing": "Racing", "shooter": "Shooter", "simulation": "Simulation", "sports": "Sports", "strategy": "Strategy", "survival": "Survival", "fighting": "Fighting"}, "game_collection": {"free_games": "Free games", "my_games": "My games", "most_popular": "Most popular", "popular": "Popular", "recently_played": "Recently played", "game_pass": "PC Game Pass", "favorites": "Favorites", "playable": "Ready to play", "unsupported": "Unsupported", "unsupported_and_not_installed": "Unsupported", "installable": "Install now", "launch_without_mods": "Launch without mods", "community_choice": "Community choice", "free_games_to_install": "Get started using mods with a free supported game", "you_have_no_installed_games": "You have no installed games.", "install_a_free_game_to_get_started": "Install a free game to get started using WeMod.", "you_have_no_favorites": "You don’t have any favorites yet.", "add_games_to_your_favorites_by_selecting_the_star_icon": "Add games to your Favorites by selecting the *star* icon.", "search_your_game_library": "Search your game library", "search_games": "Search games", "search_your_favorites": "Search your favorites", "go": "Go", "no_results": "No results", "no_results_advice": "Try to modify your search", "all": "All", "favorites_not_installed": "Favorites not installed", "add_games_to_your_favorites": "Add games to your favorites", "all_supported_games": "All supported games", "new_and_updated_games": "New and recently updated", "sort_by": "Sort by", "a_z": "A-Z", "maps": "Maps", "teleport": "Teleport", "live_location": "Live Location", "games_with_maps": "Games with Maps", "precision_mods": "Precision Mods", "overlay": "Overlay", "my_videos": "My videos", "recently_recorded": "Recently Recorded", "game": "Game"}, "ad_dialog": {"ad_revenue_uses": "Your mods will launch shortly. Ad revenue is used to reward the creators and help us provide a free WeMod experience.", "get_an_ad_free_experience": "Get an ad-free experience"}, "suggest_cheats": {"have_an_idea_for_a_cheat": "Have an idea for a mod?", "suggest_cheat": "Suggest mod", "info_tooltip": "Have mod ideas? Let us know! Remember that submitting ideas does not guarantee they will be added. This can be due to technical limitations, type of idea, or other factors.", "suggest_a_cheat": "Suggest a mod", "start_typing": "Start typing...", "have_an_idea": "Have an idea?", "thank_you": "Thank you! Your ideas were submitted.", "disclaimer": "Remember that submitting ideas does not guarantee they will be added, but it does help the creators!", "error": "We’re sorry, there was an error submitting your mod ideas. Please try again later."}, "beta_tag": {"beta": "Beta"}, "overlay_education_dialog": {"in_game_overlay": "In-game *overlay*", "overlay_requirements": "Requires Windows 10 version $version or newer", "install_message": "Did you know you can use mods in-game with the WeMod Overlay? Once installed, just press ***Win* *G*** to open it and control mods while you play.", "post_install_message": "Did you know you can use mods in-game with the WeMod Overlay? While playing, just press ***Win* *G*** to open it and control your mods.", "open_game_bar_settings": "Open Game Bar settings", "install_overlay": "Install overlay", "try_it_now": "Try it now", "start_free_trial": "Start free trial"}, "featured_game_feed": {"sponsored": "Sponsored"}, "game_search_input": {"go": "Go", "search": "Search"}, "pro_cta_label": {"start_free_trial": "Start free trial", "upgrade_to_pro": "Upgrade to Pro"}, "welcome_mat_dialog": {"welcome_to_wemod": "Welcome to **WeMod!**", "enjoy_all_of_wemod_with_pro": "Enjoy all of **WeMod** with <PERSON>!", "wemod_is_free_to_use": "WeMod is free-to-use because of our Pro members. Stay free or support us and unlock great features!", "free_membership": "Free membership...", "$x_cheats_for_$y_games": "$x mods for $y games", "hotkey_cheat_controls": "Hotkey (F1, etc) mod controls", "auto_game_and_version_detection": "Auto-game & version detection", "safe_and_virus_free_cheats": "Safe & virus free mods", "discord_community_access": "Discord community access", "continue_with_free_membership": "Continue with free membership", "upgrade_to_pro": "Upgrade to **Pro**", "everything_in_free_membership": "Everything in free membership +", "interactive_cheat_controls": "Interactive mod controls", "save_cheats_between_plays": "Save mods between plays", "remote_mobile_app": "Remote mobile app", "in_game_cheat_overlay": "In-game mod overlay", "game_boosting": "Game boosting", "exclusive_themes": "Exclusive themes", "special_role_in_discord": "Special role in Discord"}, "creators_list": {"$x_game": "**$x** game", "$x_games": "**$x** games"}, "email_dialog": {"thank_you_for_your_payment": "Thank you for your payment!", "the_creators": "The creators", "your_subscription_helps": "Your subscription helps support the creators who develop mods for your favorite games.", "account_email": "Account email", "please_enter_a_valid_email_address": "Please enter a valid email address to access your Pro account.", "your_email_address": "Your email address", "continue_with_pro": "Continue with Pro", "note_you_can_update": "Note: You can update these credentials at any time in your account settings."}, "support_wemod_footer": {"members_like_you_make_wemod_possible": "Members like you make WeMod possible and keep our library of $x games up to date"}, "payment_processing": {"we_are_processing_your_payment": "We are processing your payment.", "thanks_for_your_patience": "Thanks for your patience."}, "remote_education_dialog": {"wemod_remote": "WeMod *Remote*", "free_user_message": "Good news! We just made the WeMod Remote even better.\nWith Pro, you can use the remote to start playing games on your PC, look up hotkey info, and control mods from your phone.", "pro_user_message": "Good news, Pro members! We just made the WeMod Remote even better.\nYou can now use the remote to start playing games on your PC and look up hotkey shortcuts while you play.", "play_button_message": "Did you know you can use your phone to control mods and select which games to play on your PC?\nDownload the Pro Remote and control WeMod from your couch.", "connect_remote": "Connect remote", "download_remote": "Download remote", "download_for_phone": "Get the **WeMod Remote** for **Android** & **iOS**:", "feature_control_mods": "Control Mods", "feature_browse_maps": "Browse Maps", "feautre_teleport": "Teleport & more!", "scan_qr": "<PERSON>an QR or visit [wemod.com/remote](https://wemod.com/remote) on your phone"}, "remote_education_graphic": {"controls": "Controls", "hotkeys": "Hotkeys", "play": "Play", "no_more_alt_tabbing": "No more alt-tabbing!"}, "discord_tooltip": {"join_our_discord": "Join our Discord", "connect_message": "Connect with us and other WeMod community members with access to giveaways, events, and more.", "connect_discord": "Connect Discord"}, "launch_without_mods_button": {"launch_without_mods": "Launch", "launch_failed": "There was a problem launching your game."}, "app_header_search": {"search_games": "Search games", "new": "N", "updated": "U", "no_cheats_available": "No mods available", "no_results_message": "There are no games that match your search. Please search again.", "my_games": "My games", "favorites": "Favorites", "recently_played": "Recently played", "favorites_empty_message": "Your favorites will show up here. Add games to your Favorites by selecting the star icon."}, "pro_banner": {"support_wemod_with_pro": "Support WeMod with **Pro**", "features": "In-app controls *+* Save mods *+* In-game overlay *+* more!"}, "post_pro_upgrade": {"yearly_gift_toast_message": "**Congrats! You received a gift from $sender!** &nbsp;Enjoy 1 year of PRO", "monthly_gift_toast_message": "**Congrats! You received a gift from $sender!** &nbsp;Enjoy 1 month of PRO", "view_details": "View details"}, "notifications_settings": {"search_games": "Search", "clear_search": "Clear search", "no_results_message": "There are no games that match your search.", "no_followed_games_message": "You aren’t following any games.", "remove_from_followed": "Remove from followed", "$game_removed_from_followed": "**$game** removed from followed."}, "follow_games": {"failed_to_set_notification_preferences": "Failed to set notification preferences.", "$game_for_$platform_release_message": "Good news! We just released new mods for $game for $platform. Check it out!", "$game_for_$platform_update_message": "Let’s go! We just released updated mods for $game for $platform. Check it out!", "play_now": "Play now", "release_notification_image_watermark": "New mods!", "update_notification_image_watermark": "Updated mods!"}, "map_banner": {"interactive_map": "Interactive map", "interactive_maps": "Interactive maps", "$name_map": "$name Map"}, "game_maps": {"map_render_error_message": "There was a problem loading your map. Please try again!"}, "pro_onboarding_tooltip": {"youre_a_true_pro": "You’re a true *Pro*", "say_hello_to_the_ultimate_gaming_experience": "Say hello to the ultimate gaming experience with exclusive features like *save mods*, the *in-game overlay*, *mobile remote*, and more!", "explore_pro_features": "Explore Pro features", "click_to_see_the_benefits_of_pro": "Click to learn about the benefits of Pro"}, "nps_dialog": {"how_likely_are_you_to_recommend_wemod_to_a_friend": "How likely are you to recommend <PERSON><PERSON><PERSON> to a friend?", "not_likely": "Not likely at all", "extremely_likely": "Extremely likely", "glad_youre_enjoying_the_app": "Awesome, glad you're enjoying the app!", "review_on_trustpilot": "Would you mind putting in a good word for us on Trustpilot?", "submit": "Submit", "sure": "Sure!", "no_thanks": "No thanks", "send": "Send", "skip_feedback": "Skip feedback", "sorry_you_arent_satisfied": "We're sorry you aren't fully satisfied", "what_can_we_do_better": "Please let us know what we could be doing better:", "share_feedback": "Please share any feedback on your experience with WeMod", "feedback_placeholder": "Type here"}, "maps_nps_dialog": {"how_satisfied_are_you_with_the_map": "How satisfied are you with the interactive map?", "not_satisfied_at_all": "Not satisfied at all", "extremely_satisfied": "Extremely satisfied", "thank_you_for_your_feedback": "Thank you for your feedback!", "let_us_know_how_to_improve": "Please let us know what we could do to make the map better", "feedback_placeholder": "Type here", "skip_feedback": "Skip feedback", "send": "Send"}, "post_assistant_nps_dialog": {"how_satisfied_are_you": "How satisfied are you with the AI Game Guide?", "not_satisfied_at_all": "Not satisfied at all", "extremely_satisfied": "Extremely satisfied", "thank_you_for_feedback": "Thank you for your feedback!", "how_can_we_improve": "Please let us know what we could do to make the game guide better", "feedback_placeholder": "Type here", "skip_feedback": "Skip feedback", "send": "Send"}, "game_guide_nps_dialog": {"thank_you_for_your_feedback": "Thank you for your feedback!", "let_us_know_how_to_improve": "Please let us know what we could do to make the guide better.", "feedback_placeholder": "Type here", "skip_feedback": "Skip feedback", "send": "Send"}, "time_limit_reached_post_game_dialog": {"daily_time_limit_exceeded": "Daily time limit *exceeded*.\nUpgrade to ~~Pro~~ for **unlimited** modding.", "daily_time_limit_per_game_exceeded": "Daily time limit for this game *exceeded*.\nUpgrade to ~~Pro~~ for **unlimited** modding.", "free_can_use_wemod_for_1_hour_each_day": "Free users are able to use WeMod free for *1 hour* each day.", "free_can_use_wemod_for_1_hour_per_game_each_day": "Free users are able to use WeMod free for *1 hour per game* each day.", "free_can_use_wemod_for_2_hours_each_day": "Free users are able to use WeMod free for *2 hours* each day.", "free_can_use_wemod_for_2_hours_per_game_each_day": "Free users are able to use WeMod free for *2 hours per game* each day.", "free_can_use_wemod_for_3_hours_each_day": "Free users are able to use WeMod free for *3 hours* each day.", "free_can_use_wemod_for_3_hours_per_game_each_day": "Free users are able to use WeMod free for *3 hours per game* each day.", "free_can_use_wemod_for_4_hours_each_day": "Free users are able to use WeMod free for *4 hours* each day.", "free_can_use_wemod_for_4_hours_per_game_each_day": "Free users are able to use WeMod free for *4 hours per game* each day.", "free": "Free", "pro": "Pro", "play_another_game_today": "Play another game today", "ill_wait_until_tomorrow": "I’ll wait until tomorrow", "time_limit_resets_in": "Time limit resets in:", "1_hour": "**1** hour", "1_minute": "**1** minute", "$hours_hours": "**$hours** hours", "$minutes_minutes": "**$minutes** minutes"}, "time_limit_reached_pre_game_dialog": {"upgrade_to_pro_to_keep_playing": "Upgrade to *Pro* to keep modding!", "you_have_exceeded_your_1_hour_limit_for_today": "You have exceeded your *one hour* limit for today", "you_have_exceeded_your_1_hour_limit_for_this_game_for_today": "You have exceeded your *one hour* limit for this game for today", "you_have_exceeded_your_2_hour_limit_for_today": "You have exceeded your *two hour* limit for today", "you_have_exceeded_your_2_hour_limit_for_this_game_for_today": "You have exceeded your *two hour* limit for this game for today", "you_have_exceeded_your_3_hour_limit_for_today": "You have exceeded your *three hour* limit for today", "you_have_exceeded_your_3_hour_limit_for_this_game_for_today": "You have exceeded your *three hour* limit for this game for today", "you_have_exceeded_your_4_hour_limit_for_today": "You have exceeded your *four hour* limit for today", "you_have_exceeded_your_4_hour_limit_for_this_game_for_today": "You have exceeded your *four hour* limit for this game for today", "pro_users_have_unlimited_mod_access": "Pro Users have unlimited mod access in addition to the remote *mobile app* and *saving mods*.", "pro_users_have_unlimited_mod_access_mobile_free": "Pro Users have unlimited mod access in addition to an *ad free* experience and ability to *save mods*.", "ill_play_another_game": "I’ll play another game", "ill_play_tomorrow": "I’ll play tomorrow"}, "time_limit_reached_alternate_graphic": {"unlimited": "Unlimited", "save_mods": "Save mods", "ad_free": "Ad free"}, "time_limit_reset_dialog": {"mod_access_restored": "Mod access **restored**!\nUpgrade to *Pro* for unlimited modding", "you_have_1_hour_of_free_mod_access_today": "You have *one hour* of free mod access today", "you_have_1_hour_of_free_mod_access_per_game_today": "You have *one hour* of free mod access *per game* today", "you_have_2_hours_of_free_mod_access_today": "You have *two hours* of free mod access today", "you_have_2_hours_of_free_mod_access_per_game_today": "You have *two hours* of free mod access *per game* today", "you_have_3_hours_of_free_mod_access_today": "You have *three hours* of free mod access today", "you_have_3_hours_of_free_mod_access_per_game_today": "You have *three hours* of free mod access *per game* today", "you_have_4_hours_of_free_mod_access_today": "You have *four hours* of free mod access today", "you_have_4_hours_of_free_mod_access_per_game_today": "You have *four hours* of free mod access *per game* today", "pro_users_have_unlimited_mod_access": "Pro Users have unlimited mod access in addition to the remote *mobile app* and *saving mods*."}, "time_limit_enforcer": {"times_up": "Time’s up!", "upgrade_to_pro_to_play_beyond_your_daily_1_hour_limit": "Upgrade to Pro to use mods beyond your daily 1 hour limit.", "upgrade_to_pro_to_play_beyond_your_daily_2_hour_limit": "Upgrade to Pro to use mods beyond your daily 2 hour limit.", "upgrade_to_pro_to_play_beyond_your_daily_3_hour_limit": "Upgrade to Pro to use mods beyond your daily 3 hour limit.", "upgrade_to_pro_to_play_beyond_your_daily_4_hour_limit": "Upgrade to Pro to use mods beyond your daily 4 hour limit.", "upgrade_to_pro": "Upgrade to Pro", "upgrade_to_pro_to_play_beyond_your_daily_1_hour_limit_per_game": "Upgrade to Pro to use mods beyond your daily 1 hour limit per game.", "upgrade_to_pro_to_play_beyond_your_daily_2_hour_limit_per_game": "Upgrade to Pro to use mods beyond your daily 2 hour limit per game.", "upgrade_to_pro_to_play_beyond_your_daily_3_hour_limit_per_game": "Upgrade to Pro to use mods beyond your daily 3 hour limit per game.", "upgrade_to_pro_to_play_beyond_your_daily_4_hour_limit_per_game": "Upgrade to Pro to use mods beyond your daily 4 hour limit per game.", "upgrade_to_pro_to_use_mods_beyond_your_daily_limit": "Upgrade to Pro to use mods beyond your daily limit.", "five_minutes_left": "5 min left"}, "first_play_upgrade_prompt_dialog": {"level_up_with_pro": "Level up with *Pro*", "and_get_all_of_this": "And get all of this:", "jump_into_playing": "Jump right back in to playing", "fast_access_with_overlay": "Fast mod access with Overlay", "level_up_with_pro_to_get": "Level up with *Pro* to also get:", "in_game_overlay": "In-game overlay", "save_mods": "Save mods", "no_ads": "No ads", "stay_free_and_start_game": "Stay free and start game", "and_much_more": "... and much more!", "desktop_mod_controls": "Desktop mod controls", "your_game": "Your game", "mobile_app": "Mobile app", "overlay": "Overlay", "advanced_mod_controls": "Advanced Mod Controls"}, "assistant_button": {"label": "AI Game Guide button"}, "assistant_popout": {"unlock_the_power_of_ai": "✨ Unlock the power of AI with your own personal Game Guide.", "ask_me_about_$game": "Just ask and get helpful tips & strategies for $game!"}, "assistant": {"general_error_message": "We're sorry for the inconvenience. The AI Game Guide is currently having difficulty responding. Remember, the AI Game Guide is in early development and we appreciate your patience as we work on improvements! If this problem continues, please reach out to us at **<EMAIL>**.", "rate_limit_error_message": "😴 Unfortunately the AI Game Guide is taking a well-deserved rest right now. Thanks for giving it a try! Check back soon for more fun and assistance. Until then, happy gaming!", "offline_error_message": "It looks like you are offline. Please check your internet connection and try again.", "game_guide_may_make_mistakes": "Game guide may make mistakes, so double-check its responses."}, "assistant_chat": {"type_in_a_question": "Type in a question...", "ask_anything": "Ask anything...", "share_your_feedback_positive": "Share what you liked about that response", "share_your_feedback_negative": "Share what you didn’t like about that response", "thanks_for_your_feedback_positive": "Thanks for the feedback! Glad I could help.", "thanks_for_your_feedback_negative": "Thanks! Your feedback will help me improve.", "welcome_message_1": "Hey, welcome back! How can I assist you with $game today?", "welcome_message_2": "Ready to unleash your skills in $game? Tell me what you need, and let's make magic happen!", "welcome_message_3": "Welcome back! I'm your guide to mastering $game. What's on your gaming agenda today?", "welcome_message_4": "Ready to level up your skills in $game? Let's dive in together!", "hello_$user": "Hello, $user."}, "assistant_box": {"game_guide": "Game Guide", "beta": "Beta"}, "sources_button": {"sources": "Sources", "all_under_$license": "*All under [$license License]($url)"}, "feedback_buttons": {"positive_feedback": "Give positive feedback", "negative_feedback": "Give negative feedback"}, "assistant_history_list": {"alpha_message": "**Alpha** This AI Game Guide is still in early development. Not all answers will be accurate. By using, you agree to our [Terms & Conditions](website://terms) and [Privacy Policy](website://privacy).", "thinking": "Thinking...", "mission_helper": "Mission helper"}, "assistant_icon": {"alpha": "Alpha"}, "assistant_manager": {"overlay_prompt_message": "WeMod In-Game Overlay — you can also ask me questions in the overlay for an in-game experience.\n\n1. [Install or update the overlay](wemod://overlay-install?location=assistant_message)\n2. Press Windows + G to activate\n\n[Get Help](https://wemod.gg/support-assistant-overlay)"}, "assistant_education_dialog": {"ai_game_guide": "AI Game Guide", "unlock_the_power_of_ai_with_your_own_personal_game_guide": "✨ Unlock the power of AI with your own personal Game Guide. Just ask and get helpful tips & strategies in select games.\nPlus! [Update the overlay](wemod://overlay-install?location=assistant_education_dialog) and access convenient in-game assistance.", "ask_a_question": "Ask a question", "type_in_a_question": "Type in a question...", "chat_question_1": "What star system does <PERSON><PERSON><PERSON> belong to?", "chat_answer_1": "<PERSON><PERSON><PERSON> belongs to the star system called Alpha Centauri.", "chat_question_2": "What is the value of the Beowulf?", "chat_answer_2": "The value of the Beowulf rifle in Starfield is 4820 credits. It has 8 available mod slots.", "chat_question_3": "What has better accuracy the <PERSON><PERSON><PERSON> or the Coachman?", "chat_answer_3": "The Breach has the better accuracy rating at 55.3%, while the Coachman's accuracy rating is only 38.4%."}, "time_limit_pre_game_dialog": {"wemod_is_free_for_$x_hours_each_day": "WeMod is free for **$x hours** each day", "upgrade_to_pro_for_unlimited_modding": "Upgrade to **Pro** for *unlimited* modding.", "skip_for_now": "Skip for now"}, "time_limit_graphic": {"unlimited": "Unlimited"}, "time_limit_countdown": {"free_mod_access_time_remaining": "Free mod access time remaining", "daily_free_mod_access_exceeded": "Daily free mod access *exceeded*", "free_wemod_users_are_limited_to_$hours_hours_per_day": "Free WeMod users are limited to $hours hours of mod use per day.", "upgrade_now": "Upgrade now", "until_free_mod_access_restored": "Until free mod access **restored**", "free_mod_access_reset_tooltip": "Free mod access is restored at Midnight local time"}, "time_remaining_post_game_dialog": {"time_remaining": "Time remaining:", "1_hour": "**1** hour", "1_minute": "**1** minute", "$hours_hours": "**$hours** hours", "$minutes_minutes": "**$minutes** minutes", "upgrade_to_pro_for_unlimited_modding": "Upgrade to **Pro** for *unlimited* modding."}, "favorite_button": {"mark_as_favorite": "Mark this game as favorite."}, "follow_button": {"notify_when_mods_update": "Notify me when new mods are released or updated for this game."}, "maps_education_dialog": {"upgrade_to_pro_and_try_interactive_maps": "Upgrade to **Pro** and try interactive maps", "find_everything_youre_looking_for": "Find *everything* you’re looking for and more...", "ill_try_later": "I’ll try later", "check_out_our_new_interactive_maps": "Check out our *new* interactive maps", "open_maps": "Open Maps"}, "teleport_education_dialog": {"upgrade_to_pro_and_try_teleport_maps": "Upgrade to **Pro** to _teleport_ in maps", "instantly_go_where_you_want": "*Instantly* go where you want with the click of a button", "ill_try_later": "I’ll try later", "teleport_with_interactive_maps": "_Teleport_ with the interactive map", "open_maps": "Open Map"}, "maps_graphic": {"gun": "Gun", "vehicle": "Vehicle", "item": "<PERSON><PERSON>", "enemy_camp": "Enemy Camp"}, "title_settings_menu": {"select_game_source": "Select game source", "game_source": "Game source", "display_options": "Display options", "create_desktop_shortcut": "Create desktop shortcut", "added_to_your_desktop": "Added to your desktop", "mods_version": "Mods version", "reset_auto_pins": "Reset auto-pins"}, "game_selector": {"installed": "Installed", "added": "Added", "view_on_$platform": "View on $platform", "select_game_source": "Select game source"}, "game_installer": {"were_having_trouble_opening_$platform": "We’re having trouble opening $platform, make sure it's installed or add a custom game."}, "add_game_menu": {"install": "Install", "free": "Free", "get_on_$platform": "Get on $platform", "add_game_manually": "Add game manually", "not_detected_message": "If WeMod hasn’t automatically found your game, you’ll have to manually add the game's shortcut or executable (.exe) file.", "installed": "Installed", "added": "Added"}, "display_options_menu": {"show_hotkeys": "Show hotkeys", "show_hotkeys_description": "Hotkeys are still active when they are hidden"}, "mod_onboarding": {"get_started_in_$x_steps": "Get started in $x steps", "step_1": "Step 1", "step_2": "Step 2", "step_3": "Step 3", "game_not_installed_step_1_instructions": "Click *Add game* to install your game", "game_not_installed_step_1_instructions_free": "Click *Install for free* to install your game", "game_not_installed_step_2_instructions": "Click **Play** to launch your game", "game_not_installed_step_3_instructions": "Enable the **Mods** you want to use", "game_installed_step_1_instructions": "Click **Play** to launch your game", "game_installed_step_2_instructions": "Enable the **Mods** you want to use", "modding_tip": "*Mod Tip:* Use *Alt+Tab* to switch back to WeMod while playing, or use the keyboard shortcuts."}, "mod_onboarding_button_graphic": {"install": "Install"}, "mod_onboarding_mods_graphic": {"player": "Player", "unlimited_health": "Unlimited Health", "unlimited_stamina": "Unlimited Stamina"}, "title_settings_button": {"mod_settings": "Mod settings"}, "version_history_menu": {"unreleased": "Unreleased", "$x_mod": "$x mod", "$x_mods": "$x mods", "latest": "Latest"}, "cyberpunk_mission_help": {"mission_helper": "Mission Helper", "live": "LIVE", "detecting_mission": "Detecting mission...", "get_help": "Get Help", "offline": "Offline", "launch_message": "Your personal assistant to get you through your current mission. Launch the game to get started.", "mission_help_prompt": "I'm playing the mission \"$mission\", please give me a concise walkthrough for the mission."}, "elden_ring_boss_guide": {"elden_ring_boss_guide": "Boss Guide", "all_the_info_you_need": "Your comprehensive guide to taking down every Elden Ring boss.", "view_guide": "View guide", "step_by_step_$boss_guide": "Step-by-step guide on how to beat **$boss**.", "includes_video_guide": "Includes video guide"}, "onboarding": {"select_a_game": "Select a game", "loading_games": "Finding your games", "empty_search_title": "No games found", "empty_search_message": "Try searching for a different game, or check out what's popular."}, "onboarding_all_games_card": {"browse_all": " Browse all 3000+ games on WeMod"}, "onboarding_game_tutorial": {"press_spacebar_to_continue": "Press **spacebar** to continue", "welcome_to_game_page": "Welcome to your Game Page", "play_mod_enjoy": "Play, <PERSON><PERSON>, Enjoy. It's that simple!", "add_your_game": "Add Your Game", "use_add_game_button": "Use the **Add Game** button to add your game's exe or install it.", "use_play_game_button": "Use the **Play** button to launch your games.", "launch_your_game": "Launch Your Game", "customize_with_mods": "Customize With Mods", "toggle_and_adjust_mods": "Toggle & adjust the **Mods** by using the mod controls.\n\n*Mod Tip:* Use *Alt + Tab* to switch back to WeMod while playing the game.", "use_hotkeys_for_convenience": "Use hotkeys for convenience", "customizable_keyboard_shortcuts": "Customizable **keyboard shortcuts** for even faster mod use in-game.", "skip": "<PERSON><PERSON>", "next_arrow": "->", "back_arrow": "<-", "done": "Done", "play": "Play", "save_mods": "Save mods", "pro": "PRO", "unlimited_health": "Unlimited Health", "player": "Player", "off": "Off", "on": "On", "unlimited_stamina": "Unlimited Stamina", "inventory": "Inventory", "unlimited_ammo": "Unlimited Ammo", "unlimited_items": "Unlimited Items", "speed": "Speed", "ctrl_f1": "Ctrl F1", "ctrl_shift_f1": "Ctrl Shift F1", "f4": "F4", "f3": "F3", "f2": "F2", "f1": "F1"}, "sidebar_user_menu": {"help": "Help", "share_feedback": "Share feedback"}, "sidebar_game_lists": {"favorites": "Favorites", "supported": "Mod Supported", "other": "Other games", "view_all_$number_games": "View all $number games ->", "new": "New"}, "sidebar_game_menu": {"add_to_favorites": "Add to favorites", "remove_from_favorites": "Remove from favorites", "launch_without_mods": "Launch without mods", "follow": "Follow", "unfollow": "Unfollow", "play": "Play"}, "app_sidebar_search_button": {"search": "Search"}, "app_search": {"search": "Search", "use_keys_to_navigate": "Use *↑* *↓* to navigate", "no_cheats_available": "No mods available", "no_results_message": "There are no games that match your search. Please search again.", "my_games": "My games", "recently_played_games": "Recently played games", "recent_games": "Recent games"}, "remote_button": {"connect_phone": "Connect phone", "reconnect_phone": "Reconnect phone", "connected": "Connected", "promo_tooltip": "Control your mods and browse maps from your phone."}, "overlay_button": {"experiencing_overlay_issues": "Experiencing Overlay issues?", "improve_overlay_feedback": "We're working on improving the overlay. Found an issue? Your feedback helps us make it better.", "install_overlay": "Install overlay", "shortcut_key": "Win+G", "overlay": "Overlay", "overlay_feedback": "Overlay feedback", "overlay_unsupported": "Overlay unsupported", "report_an_issue": "Report an issue", "tooltip_message": "Press **$hotkey** while in-game to instantly access your mods.", "unsupported_tooltip_header": "Game doesn’t support overlay", "unsupported_tooltip_message": "$game doesn't support the overlay yet. You can still play without  the overlay, while we work on improving support across games.", "disabled_tooltip_header": "Overlay disabled", "disabled_tooltip_message": "The new WeMod overlay is disabled. Please enable it in the Customization section of the settings menu."}, "overlay_header": {"back_to_game": "Back to Game"}, "overlay_window_menu": {"settings": "Settings", "clip_last_$seconds_s": "Clip (last $secondss)", "or_press_$hotkey": "Or Press $hotkey"}, "overlay_launch_notification": {"press_$hotkey": "Press $hotkey", "to_toggle_overlay": "to toggle in-game overlay"}, "house_ad": {"save_mods_title": "**Save** mods", "save_mods_description": "Remember your mod controls **between plays**.", "remote_app_title": "**Remote** app", "remote_app_description": "Forget hotkeys.\nUse **your phone** to control mods.", "game_boosting_description": "Use boosts to\ninfluence which\n**games we add next**.", "game_boosting_title": "Game **boosting**", "exclusive_themes_title": "Exclusive **themes**", "exclusive_themes_description": "Spice up the app with some **extra color**.", "remove_ads_with_wemod_pro": "Remove ads with WeMod **Pro**"}, "overlay_nps_dialog": {"how_satisfied_are_you_with_the_overlay": "How satisfied are you with the overlay?", "not_satisfied_at_all": "Not satisfied at all", "extremely_satisfied": "Extremely satisfied", "thank_you_for_your_feedback": "Thank you for your feedback!", "let_us_know_how_to_improve": "Please let us know what we could do to make the overlay better", "feedback_placeholder": "Type here", "skip_feedback": "Skip feedback", "send": "Send"}, "profile": {"edit_profile": "Edit Profile", "member_since": "Member Since", "pro_since": "Pro Since", "my_games": "My Games", "boosts": "Bo<PERSON><PERSON>", "youre_a_pro": "You're a *Pro*", "explore_pro_features": "Explore Pro Features", "your_pro_subscription": "Your WeMod Pro subscription gives you access to exclusive features like *Save Mods*, *In-game Overlay*, *Mobile Remote* and more.", "support_wemod": "Support WeMod *Go Pro*", "save_mods": "Save Mods", "save_mods_desc": "Save your mods setup and jump back in", "in_game_overlay": "In-Game Overlay", "in_game_overlay_desc": "Mod in-game with the WeMod overlay", "remote_mobile_app": "Remote Mobile App", "remote_mobile_app_desc": "Use the WeMod app to configure mods", "and_a_lot_more": "And A Lot More", "and_a_lot_more_desc": "Like priority support, themes, game boosting etc.", "followed_games": "Followed Games", "no_followed_games": "No followed games yet", "no_followed_games_description": "Follow games to stay on top of mod updates and new releases!", "explore_games": "Explore Games", "view_all": "View All →"}, "achievements": {"achievements": "Achievements", "$value_week": "*$value* week", "$value_month": "*$value* month", "$value_months": "*$value* months", "$value_year": "*$value* year", "$value_years": "*$value* years", "one_week_club": "1 Week Club", "one_month_club": "1 Month Club", "three_months_club": "3 Months Club", "six_months_club": "6 Months Club", "one_year_club": "1 Year Club", "two_years_club": "2 Years Club", "three_years_club": "3 Years Club", "four_years_club": "4 Years Club", "five_years_club": "5 Years Club", "six_years_club": "6 Years Club", "seven_years_club": "7 Years Club", "eight_years_club": "8 Years Club", "nine_years_club": "9 Years Club", "ten_years_club": "10 Years Club", "joined_wemod": "Joined WeMod", "map_explorer": "Map Explorer"}, "overlay_announcement_dialog": {"introducing_the_all_new": "Introducing the all-new", "wemod_overlay": "<PERSON><PERSON><PERSON>", "a_faster_more_responsive_way_to_enhance_your_gameplay": "A faster, more responsive way to enhance your gameplay, no Xbox Game Bar needed. Use **$hotkey** on supported games to access your mods."}, "map_mods_list": {"category_maps": "Maps", "key_location_teleport": "Key Location Teleport", "open_in_map": "Open in Map"}, "ad_popup": {"remove_ads_with_wemod_pro": "Remove ads with WeMod **Pro**"}, "map_feed_item": {"$count_maps": "$count maps"}, "new_badge": {"new": "New"}, "overlay_settings_menu": {"restore_windows": "Restore windows", "enable_notifications": "Enable notifications", "notification_position": "Notification position", "top": "Top", "bottom": "Bottom", "center": "Center"}, "overlay_mod_notification": {"set_$mod_$value": "**Set $mod** $value", "$mod_enabled": "**$mod** enabled", "$mod_disabled": "**$mod** disabled", "unread_instructions": "Unread"}, "rewards": {"rewards": "Rewards", "wemod_pro": "wemod **pro**", "pc_game_pass": "PC Game Pass", "congrats_pro_member": "Congrats Pro Member!", "you_get_one_month": "You get **1 month** of **PC Game Pass**, on us! Reward is available from $start to $end.", "more_details": "More Details", "hide_details": "Hide Details", "claim_now": "Claim Now", "view_code": "View Code", "claim_gamepass_header": "Congratulations", "claim_gamepass_description": "Redeem the code below on Xbox's redemption site to get access to PC Game Pass for 1 month.", "redemption_code": "Redemption Code", "copy": "Copy", "copied": "Copied!", "redeem": "Redeem", "a_gift_for_you": "A gift for you!", "congrats_pro_subscriber": "Congrats Pro subscriber! You get 1 month of PC Game Pass, on us!", "claim_now_sidebar": "Claim <PERSON> →", "generic_claim_error": "There was an error claiming your reward. Please try again later, or contact support.", "gamepass_offer_details": "Valid for new Xbox Game Pass members only. This offer is available to non-trial WeMod Pro users from the United States, United Kingdom, Canada, and Australia. Once a code has been claimed, it must be redeemed by $redemptionDate at [$redeemName]($redeemUrl) The offer will be active from $start to $end. Valid payment method required. Unless you cancel, you will be charged the then-current regular membership rate when the promotional period ends. Limit: 1 per person/account. Subject to the Microsoft Services Agreement, Game Pass terms and system requirements at: [$termsName]($termsUrl)"}, "pro_showcase": {"ready_to_level_up_go_pro": "Ready to level up? *Go Pro!*", "remote": "Remote App", "remote_description": "No more alt-tabbing! Manage all your mods, access maps on the go and ask the game guide with just a tap on the WeMod Remote mobile app.", "remote_subtitle": "Use the WeMod app to configure mods", "save_mods": "Save Mods", "save_mods_description": "Save your mod setup and jump back in! Any mods you have configured can be saved and applied on launch every time you play.", "save_mods_subtitle": "Save your mod setup and jump back in", "save_mods_mods": "Mods", "pin_mods": "<PERSON>nned Mods", "pin_mods_description": "Pin your favorite mods for quick access. Your pinned mods will always appear at the top of the list, so you can activate them with ease!", "pin_mods_subtitle": "Pin your favorite mods for quick access", "boosts": "Boost Games", "boosts_description": "Vote for your favorite games – your boosts directly influence our mods roadmap, ensuring we create mods for the games that matter most!", "boosts_subtitle": "Vote for games to get mods next", "game_guide": "Game Guide", "game_guide_description": "Get instant answers to your gaming questions, discover optimal strategies, and learn pro tips to enhance your experience!", "game_guide_subtitle": "Tips & tricks for your game, powered by AI", "pinned": "Pinned", "demo_mod_1": "Unlimited Health", "demo_mod_2": "Unlimited Stamina", "demo_mod_3": "God Mode / Ignore Hits", "demo_mod_4": "Game Speed", "popular": "Popular", "game_guide_illustration_response": "To reset attributes in Cyberpunk 2077, you can go to the Character Menu and use the perk reset button. This allows you to redistribute your attribute points according to your preferred playstyle. Additionally, you can reset attribute points in exchange for eurodollars.", "game_guide_illustration_question": "How do I reset attributes?", "game_guide_illustration_pending_question": "What's the best way to beat the final mission?", "game_guide_illustration_example_avatar_alt": "Example user avatar", "game_guide_illustration_assistant_avatar_alt": "Game Guide assistant avatar", "game_guide_assistant_icon_alt": "Game Guide assistant", "precision_mod_controls": "Precision Mod Controls", "precision_mod_controls_description": "Take your mods further with precise controls and complete customization.", "mod_timers_controls": "Perfect Timing with <PERSON><PERSON>rs", "mod_timers_controls_description": "Schedule your mods to activate automatically when you need them most.", "pro": "PRO", "refill": "Refill", "one_and_a_half_times": "1.5x", "two_times": "2x", "half_times": "0.5x", "off": "Off", "on": "On", "unlimited_health": "Unlimited Health", "refill_health": "Refill Health", "set_max_health": "Set Max Health", "regeneration_rate": "Regeneration Rate", "regeneration_delay": "Regeneration Delay", "new_in_wemod_pro": "New in *WeMod PRO!*", "check_it_out": "Check it out", "try_now": "Try Now", "minute": "m", "start_timer": "Start Timer", "loop": "Loop", "refill_stamina": "Refill Stamina"}, "featurebase_feedback_dialog": {"feedback_submitted": "Thank you for your feedback! ❤️"}, "pro_showcase_columns": {"basic": "Basic", "unlimited_modding": "Unlimited modding", "$games_games_with_$mods_mods": "$games+ games with $mods+ mods", "native_overlay": "Native In-game overlay", "interactive_controls": "Interactive controls", "$count_maps": "$count+ maps", "one_click_teleport": "1-click teleport with maps", "continue_with_free": "Continue with Free", "pro": "Pro", "everything_in_free_plus": "Everything in Free+", "save_mods": "Save mods", "pin_mods": "Pin mods", "mobile_remote_app": "Mobile remote app", "game_guides": "Game Guides", "custom_themes": "Custom themes", "no_ads": "No ads", "boost_games": "Boost games", "priority_support_and_more": "Priority support and more", "save_mods_title_case": "Save Mods", "save_mods_description": "Save your mods so that they’re enabled every time you return to play", "pin_mods_title_case": "<PERSON>nned Mods", "pin_mods_description": "Pin your favourite mods with to quickly access frequently used mods.", "remote_title_case": "Remote App", "remote_description": "Manage all your mods with just a tap on the WeMod Remote mobile app.", "game_guide_title_case": "Game Guide", "game_guide_description": "Unlock the power of AI with your own personal Game Guide.", "themes_title_case": "Custom Themes", "themes_description": "Personalize your WeMod app and mod controls with custom themes.", "no_ads_title_case": "No Ads", "no_ads_description": "Experience a 100% ad-free WeMod desktop app, mobile app and native overlay.", "boosts_title_case": "Boost Games", "boosts_description": "Vote for which of your favourite games get mods next.", "popular": "Popular"}, "hover_me": {"hover_me": "Hover me"}, "choose_plan_promo": {"tagline": "Ready to level up?", "heading": "Get the most out of WeMod", "description": "Help us keep making mods for your favorite games and get access to cool exclusive features!", "free": "Free", "pro": "Pro", "everything_in_free": "Everything in Free", "free_mod_count": "35,000+ mods", "free_interactive_controls": "Interactive controls", "free_overlay": "Native in-game overlay", "free_maps": "150+ maps for 50+ games", "pro_unlimited_modding": "Unlimited modding on 3,000+ games", "pro_no_ads": "No ads", "pro_pin_mods": "Pin mods", "pro_save_mods": "Save mods", "pro_remote_app": "Mobile remote app", "pro_custom_themes": "Custom themes & more", "continue_with_free": "Continue With Free", "choose_your_plan": "Choose Your Plan"}, "overlay_maps_window": {"$name_map": "$name Map"}, "pro_showcase_slideshow": {"games_title": "Over 3,000 Games", "games_description": "No hassles with game versions and redownloading trainers. Regular community support means we excel in one crucial area: our mods simply work.", "auto_detect_title": "Automatic Game Detection", "auto_detect_description": "WeMod scans your game libraries to automatically detect installed games from popular launchers like Steam, Epic, and many more!", "overlay_title": "In-Game Overlay", "overlay_description": "No more alt-tabbing! The WeMod overlay is the easiest way to enable mods without ever leaving the game or remembering hotkeys.", "remote_title": "Remote Mobile App", "remote_description": "Manage all of your mods with just a tap on the WeMod Remote Mobile App!", "play_title": "Play Games Your Way", "play_description": "Thousands of free mods, maps, and trainers that let you enjoy single-player games on your own terms.", "maps_title": "Interactive Maps", "maps_description": "Find loot, locations, hidden secrets, and more with WeMod’s Interactive Maps!"}, "support_assistant": {"report_an_issue": "Report an issue", "beta": "Beta", "title": "<PERSON><PERSON><PERSON> Virtual Assistant", "wemod_logo": "WeMod <PERSON>", "loading": "Loading...", "subtitle": "Support", "assistant_says": "Assistant says:", "your_selection": "You selected:", "welcome": "Hi, $username! I’m here to help with any WeMod issues you’re experiencing. What can I help you with today?", "what_else_can_i_help_you_with": "What else can I help you with?", "mods_not_working": "Mods are not working", "overlay_not_working": "Overlay isn’t working", "hotkeys_not_working": "Hotkeys are not working", "game_is_crashing": "Game is crashing", "i_have_general_feedback": "I have general feedback", "i_have_an_account_issue": "I have an account issue", "game_general_setup_instructions": "Let’s troubleshoot your mod issues for $gameTitle. First, let’s check if you’ve followed these general setup steps:", "game_general_setup_instructions_1": "Make sure you’re running WeMod as administrator", "game_general_setup_instructions_2": "Disable any antivirus software or add WeMod to exceptions", "game_general_setup_instructions_3": "Ensure WeMod is updated to the latest version", "game_general_setup_instructions_4": "Restart your computer if you haven’t done so recently", "game_general_setup_instructions_confirmation": "Have you completed these general setup steps?", "recommend_game_general_setup_steps": "I recommend following these general steps first, as they resolve many common issues. Would you like to try them and come back if the issue persists?", "yes_ive_followed_all_these_steps": "Yes, I’ve followed all these steps", "yes_ive_followed_these_instructions": "Yes, I’ve followed these instructions", "no_i_havent_tried_all_of_them": "No, I haven’t tried all of them", "no_i_havent_tried_all_of_these": "No, I haven’t tried all of these", "ill_try_these_steps": "I’ll try these steps", "great_try_those_general_setup_steps": "Great! Try those general setup steps and let me know if they help. If you’re still having issues afterward, feel free to come back to this chat.", "now_lets_check_if_youve_followed_game_instructions": "Now let’s check if you’ve followed the specific setup instructions for $gameTitle:", "have_you_followed_these_game_instructions": "Have you followed these game-specific instructions?", "i_recommend_following_these_game_instructions": "I recommend following these game-specific instructions first. They’re designed to address common issues with $gameTitle. Would you like to try them and come back if the issue persists?", "great_try_following_those_game_instructions": "Great! Try following those game-specific instructions and let me know if they help. If you’re still having issues afterward, feel free to come back to this chat.", "which_mods_arent_working_correctly": "Which mods aren’t working correctly?", "all_mods": "All mods", "specific_mods": "Specific mods", "which_specific_mods_arent_working_correctly": "Which specific mods aren’t working correctly?", "please_select_mods": "Please select mods", "submit": "Submit", "have_you_followed_these_mod_specific_notes": "Have you followed these mod-specific notes?", "yes_ive_followed_all_these_notes": "Yes, I’ve followed all these notes", "instructions": "Instructions", "mod_basic_troubleshooting": "Let’s try some basic troubleshooting steps:", "mod_basic_troubleshooting_1": "Verify that you're using an authentic, officially purchased version of the software (WeMod doesn't function properly with unofficial versions)", "mod_basic_troubleshooting_2": "Verify you’re not using other mods or Nexus mods that might conflict", "mod_basic_troubleshooting_3": "Make sure your antivirus isn’t blocking WeMod", "mod_basic_troubleshooting_4": "Check if there’s a game update that might have broken compatibility", "mod_basic_troubleshooting_confirmation": "Did any of these steps help resolve your issue?", "im_glad_to_header_that": "I’m glad to hear that! Is there anything else you need help with?", "i_recommend_following_these_mod_specific_notes": "I recommend following these mod-specific notes first. They’re designed to address common issues with these particular mods. Would you like to try them and come back if the issue persists?", "great_try_following_those_mod_specific_notes": "Great! Try following those mod-specific notes and let me know if they help. If you’re still having issues afterward, feel free to come back to this chat.", "advanced_troubleshooting": "Let’s try some advanced troubleshooting steps:", "advanced_troubleshooting_1": "Try running both the game and <PERSON><PERSON><PERSON> as administrator", "advanced_troubleshooting_2": "Verify you’re playing the latest version of the game", "advanced_troubleshooting_confirmation": "Did any of these steps help resolve your issue?", "where_did_you_purchase_game": "Where did you purchase $gameTitle?", "steam": "Steam", "epic_games": "Epic Games Store", "gog": "GOG", "xbox_microsoft_store": "Xbox/Microsoft Store", "other": "Other", "game_platform_notes": "For $gamePlatform games, you can try:", "game_platform_notes_steam_1": "Open your **Library**.", "game_platform_notes_steam_2": "Right-click the game and choose **Properties**.", "game_platform_notes_steam_3": "Select **Installed Files**.", "game_platform_notes_steam_4": "Click **Verify integrity of game files**.", "game_platform_notes_epic_1": "Open the **Library** tab.", "game_platform_notes_epic_2": "Click the ⋯ menu on the game.", "game_platform_notes_epic_3": "Choose **Manage**.", "game_platform_notes_epic_4": "Click **Verify**.", "game_platform_notes_gog_1": "Select the game.", "game_platform_notes_gog_2": "Click the ⋯ button next to **Play**.", "game_platform_notes_gog_3": "Go to **Manage installation**.", "game_platform_notes_gog_4": "Choose **Verify/Repair**.", "game_platform_notes_xbox_1": "In the **Xbox app**, open **My Library**.", "game_platform_notes_xbox_2": "Click the ⋯ menu on the game.", "game_platform_notes_xbox_3": "Select **Manage**.", "game_platform_notes_xbox_4": "Open the **Files** tab.", "game_platform_notes_xbox_5": "Click **Verify & Repair**.", "game_platform_notes_confirmation": "Have you followed these instructions?", "we_recommend_following_game_platform_instructions": "We recommend following these instructions for $gamePlatform first, as they resolve most issues!", "great_try_that_out": "Great! Try that out and if you’re still having issues afterward, feel free to come back to this chat.", "please_head_over_to_our_online_feedback_hub": "Please head over to our online feedback hub to submit your general feedback or feature requests!", "share_feedback": "Share Feedback →", "we_are_unable_to_assist_with_account": "We are unable to assist you with account or billing-related issues through the virtual bot. For help, please refer to our [self-service support articles](https://support.wemod.com). If you cannot find the answer you're looking for, feel free to contact us at [<EMAIL>](mailto:<EMAIL>). Our support team will be happy to assist you.", "ill_help_you_troubleshoot_hotkey_issues": "I’ll help you troubleshoot the hotkey issues. Are you using the numpad keys for hotkeys?", "make_sure_numlock_is_turned_on": "Make sure <PERSON><PERSON><PERSON><PERSON> is turned on. The numpad hotkeys won’t work if <PERSON><PERSON><PERSON><PERSON> is off. Did this resolve your issue?", "great_numlock_key_is_often_overlooked": "Great! The NumLock key is often overlooked but crucial for numpad hotkeys. Is there anything else you need help with?", "overlay_basic_troubleshoot": "I’ll help you troubleshoot the overlay issues. Let’s go through these quick checks", "overlay_basic_troubleshoot_1": "Are you using an authentic copy of the game purchased from an official store? We can't guarantee the overlay will function correctly with unofficial versions.", "overlay_basic_troubleshoot_2": "Are you using the latest version of WeMod?", "overlay_basic_troubleshoot_2_update_version": "It looks like you’re using an outdated version of WeMod. Please update to the latest version and try again.", "overlay_basic_troubleshoot_confirmation": "Did either of these help resolve your overlay issue?", "lets_troubleshoot_game_crashes": " Let’s troubleshoot your game crashes. When does the crash occur?", "when_launching_the_game": "During game launch while <PERSON><PERSON><PERSON> is running", "when_activating_specific_mod": "When activating a specific mod", "random_times_during_gameplay": "Random times during gameplay when mods are active", "when_using_the_overlay": "When using the overlay", "crash_troubleshooting": "Let’s go through these troubleshooting steps to resolve the crashes:", "crash_troubleshooting_1": "Check if you’re using any third-party mods or Nexus mods (try disabling these)", "crash_troubleshooting_2": "Ensure you’re using a legitimate copy of the game from an official store", "crash_troubleshooting_3": "WeMod is designed for single-player only - avoid using it in multiplayer", "crash_troubleshooting_4": "Make sure you’re using the latest version of the trainer for your game", "crash_troubleshooting_5": "Ensure you’re using the latest version of WeMod", "crash_troubleshooting_6": "Try disabling the overlay if your game has this feature", "verify_game_files": "Let’s try verifying your game files, which often resolves crashing issues:", "verify_game_files_1": "For Steam: Right-click game > Properties > Local Files > Verify integrity", "verify_game_files_2": "For Epic: Library > ⋮ on game > Verify", "verify_game_files_3": "For GOG: Library > More > Manage Installation > Verify/Repair", "verify_game_files_4": "For Xbox/Microsoft: Settings > Apps > Apps & features > [Game] > Advanced options > Repair", "verify_game_files_confirmation": "Please run the verification process for your game launcher and let me know if it helps.", "crash_troubleshooting_confirmation": "Did any of these steps help?", "great_game_file_verification_often_fixes": "Great! Game file verification often fixes corrupted game data that causes crashes. Is there anything else you need help with?", "advanced_crash_troubleshooting": "Let’s try some advanced troubleshooting:", "advanced_crash_troubleshooting_1": "Try running both the game and <PERSON><PERSON><PERSON> as administrator", "advanced_crash_troubleshooting_2": "Update your graphics drivers to the latest version", "advanced_crash_troubleshooting_3": "Disable any GPU overlays or recording software (GeForce Experience, AMD Radeon Software)", "advanced_crash_troubleshooting_4": "Check Windows Event Viewer for crash logs", "advanced_crash_troubleshooting_5": "Try a clean boot (disable non-essential startup services)", "advanced_crash_troubleshooting_confirmation": "Did any of these help resolve the crashing?", "still_crashing": "Still crashing", "excellent_im_glad_we_were_able_to_resolve_crashing": "Excellent! I’m glad we were able to resolve the crashing issue. Is there anything else you need help with?", "great_im_glad_that_helped": "Great! I’m glad that helped. Is there anything else you need assistance with?", "yes": "Yes", "no": "No", "that_fixed_it": "That fixed it!", "yes_its_fixed_now": "Yes, it’s fixed now!", "no_still_having_issues": "No, still having issues", "continue_anyway": "Continue anyway", "i_have_another_issue": "I have another issue", "no_thats_all_thanks": "No, that’s all - thanks!", "further_troubleshooting_needed": "It looks like we'll need additional troubleshooting to solve this issue. Our support team on Discord is ready to help! Head over to [our Discord server →](https://www.wemod.com/discord) and start a new thread. Copy and paste the troubleshooting summary below into your Discord thread for faster assistance:", "copy": "Copy", "sorry_something_went_wrong": "Sorry, I'm having some trouble right now. 😅 Try again in a little bit, or [contact support](https://support.wemod.com/).", "minimize": "Minimize", "restore": "Rest<PERSON>", "close": "Close"}, "live_location_announcement_dialog": {"introducing_the_all_new": "Introducing the all-new", "live_location_tracking": "Live Location Tracking", "see_your_position_in_real_time": "See your exact position in real-time as you explore the game world. Available for **ALL** users in both the Overlay and the Desktop App for supported games."}, "trainer_notes_dialog": {"almost_there": "Almost there!", "lets_get_you_set_up": "Let's get you set up", "back": "Back", "est_time": "Est. Time", "minute_abbreviation": "min", "dismissable_note": "**Note:** While most games are one-click play, some games require a little extra setup.", "dismiss": "<PERSON><PERSON><PERSON>", "dont_show_again": "Don't show this to me again", "dont_show_again_description": "You must finish reading instructions to play"}, "trainer_notes_dialog_small": {"setup_instructions": "Setup Instructions", "got_it": "Got it"}, "title_help_button": {"find_instructions_here": "You can find the setup instructions again by clicking the settings dropdown."}, "help_menu": {"setup_information": "Setup information", "help_menu": "Help menu", "report_an_issue": "Report an issue", "general": "General", "submit_feedback": "Submit feedback", "feature_request": "Feature request", "suggest_a_mod": "Suggest a mod"}, "suggest_mod": {"title": "Suggest a Mod", "description": "Your suggestions and boosts help guide our mod creators to build the mods that matter most to you.", "most_popular_suggestions": "Most Popular Suggestions", "related_suggestions": "Related Suggestions", "not_updated_recently": "*Important:* This game hasn't been updated in over 90 days. Creator review of mod suggestions only occurs during game and mod updates. Do you wish to continue anyway?", "not_updated_recently_ack": "Yes, continue", "creator_review_note": "Creator review of mod suggestions only occurs during game and mod updates.", "start_typing": "Start typing...", "boost": "Boost", "no_boosts_available": "No Boosts Available", "pro": "PRO", "no_freeform_note": "*Note:* Due to technical limitations, custom suggestions cannot be accepted at this time. Please choose from the available options in the dropdown menu.", "refund_note": "*Note:* Your boosts will be fully refunded if the creators determine that mod suggestions cannot be implemented due to technical constraints or game limitations.", "error_message": "An error occured while making the suggestion. Please try again later.", "boost_error_message": "An error occured while boosting the suggestion. Please try again later.", "boost_success_message": "Suggestion boosted successfully!", "mod_suggestions": "Mod Suggestions", "your_suggestions": "Your Suggestions", "new": "New", "needs_review": "Needs Review", "$x_new": "$x New", "no_suggestions": "No approved mod suggestions yet", "boosting_disabled_for_suggestions_under_review": "Boosting is disabled for suggestions under review.", "successfully_boosted_$suggestion": "You've successfully boosted *$suggestion*!", "continue": "Continue", "and_$x_more": "and $x more"}, "capture": {"one_$title_highlight_saved": "1 $title highlight saved!", "$count_$title_highlights_saved": "$count $title highlights saved!", "view_captures": "Open videos folder ↗"}, "capture_settings": {"capture_location": "Video folder", "capture_location_description": "The folder where your highlights are saved", "view_captures": "Open videos folder ↗"}, "instant_highlight_announcement_dialog": {"introducing_the_all_new": "Introducing the all-new", "instant_highlights": "Instant Highlights", "never_miss_a_gaming_highlight_again": "Never miss a gaming highlight again! Instantly save your last 15-60 seconds of gameplay with a simple hotkey press.\nUse the WeMod Overlay or press **$hotkey** to save your clip.", "lets_go": "Let’s Go!", "not_now": "Maybe Later", "okay": "Okay"}, "overlay_instant_highlights_announcement_notification": {"new_instant_hightlights": "New! *Instant Highlight*", "press_$hotkey_to_clip_the_last_$secondss": "Press *$hotkey* to clip the last $secondss"}, "overlay_highlight_save_success_notification": {"highlight_saved": "Highlight saved"}, "my_videos": {"title": "My videos", "favorites": "Favorites", "all": "All", "game": "Game", "recently_recorded": "Recently Recorded", "sort_by": "Sort by", "no_results": "No videos found", "no_results_advice": "Clip an instant highlight during gameplay to get started", "no_results_advice_search": "Try searching for a different game or video", "no_results_advice_favorites": "Add some videos to your favorites to get started", "group_name_today": "Today", "group_name_yesterday": "Yesterday", "group_name_last_week": "Last Week", "group_name_past": "Past", "search_placeholder": "Search videos", "search_button": "Go", "recorded": "Recorded", "file_already_exists": "A file with this name already exists.", "file_rename_failed": "Failed to rename file. Please try again.", "open_file_location": "Open file location ↗", "introducing_the_all_new": "Introducing the all-new", "instant_highlights": "Instant Highlights", "never_miss_a_gaming_highlight_again": "Never miss a gaming highlight again! Instantly save your last 15-60 seconds of gameplay with a simple hotkey press. Use the WeMod Overlay or press **$hotkey** to save your clip.", "dismiss": "<PERSON><PERSON><PERSON>"}, "edit_input": {"save": "Save", "cancel": "Cancel"}}
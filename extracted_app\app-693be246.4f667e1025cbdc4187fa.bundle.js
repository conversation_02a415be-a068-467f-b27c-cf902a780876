"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7850],{"root/auth":(t,a,e)=>{e.r(a),e.d(a,{Auth:()=>g});var o=e(15215),i=e("aurelia-framework"),n=e(20770),r=e(62914),s=e(67064),l=e(16953),h=e(19072),c=e(60321),u=e(96555),d=e(20057),p=e(54995),f=e(48881);let g=class{#t;#a;#e;#o;#i;#n;#r;constructor(t,a,e,o,i,n){this.emailRequired=!l.A.debug,this.busy=!1,this.#t=t,this.#a=a,this.#e=e,this.#o=o,this.#i=i,this.#n=n}attached(){this.#r=this.#a.setNewWindowErrorHandler((t=>alert(t))),setTimeout((()=>this.existingEmailInput?.focus())),this.#i.event("authenticate_view",{mode:this.mode},r.Io),this.#s()}detached(){this.#r?.dispose(),this.#r=null}authenticate(){if(this.existingEmail?.length&&this.existingPassword?.length)return this.#l({grant_type:"password",username:this.existingEmail,password:this.existingPassword,gdpr_consent_given:this.gdprConsentGiven?"1":"0",country:this.#a.info.region??""},{method:"login",gdprConsentGiven:this.gdprConsentGiven??!1,emailCollected:this.existingEmail.includes("@"),usernameCollected:!this.existingEmail.includes("@"),passwordCollected:!0,nonInteraction:!1})}modeChanged(){"existing_user"===this.mode&&this.existingEmail&&setTimeout((()=>this.existingPasswordInput?.focus()))}onArrowClick(){this.#i.event("auth_slideshow_slide_click",{},r.Io)}async createNewAccountWithEmail(){!await this.emailInput.validateWithFeedback()&&this.emailRequired||await this.#l({grant_type:"installation_id",installation_id:this.installationId,email:this.newEmail,gdpr_consent_given:this.gdprConsentGiven?"1":"0",country:this.#a.info.region??"",language:this.#n.getEffectiveLocale().toString()},{method:"register",gdprConsentGiven:this.gdprConsentGiven??!1,emailCollected:!!this.newEmail,usernameCollected:!1,passwordCollected:!1,nonInteraction:!1})}async#l(t,a){if(!this.busy){this.busy=!0;try{await this.#e.dispatch(f.xQ,await this.#t.requestAccessToken(t)),this.onComplete({result:a})}catch(a){let e="auth.there_was_a_problem";if("password"===t.grant_type&&(e="auth.invalid_info"),"installation_id"===t.grant_type&&a instanceof Response){const t=await a.json();if("object"==typeof t&&null!==t&&"email"===t.hint)return e="auth.email_taken",this.mode="existing_user",void(this.existingEmail=this.newEmail)}this.#o.toast({type:"alert",content:e})}finally{this.busy=!1}}}#s(){if(this.#a.info.launchUri&&this.titles){const t=this.#a.info.launchUri.split(":")[1].split("?")[0].replace(/^\/+/,"").split("/");if(2===t.length&&"titles"===t[0]){const a=t[1],e=Object.values(this.games).find((t=>t.titleId===a));this.launchTitleName=this.titles?.[a]?.name,this.launchTitleTotalMods=e?.trainer?.cheatCount||0,e&&(this.launchTitleSteamAppId=this.#h(e))}}}#h(t){return t?.correlationIds.map(u.o.parse).find((t=>"steam"===t.platform))?.sku??null}};(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",String)],g.prototype,"mode",void 0),(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",Object)],g.prototype,"gdprConsentGiven",void 0),(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",Function)],g.prototype,"onComplete",void 0),g=(0,o.Cg)([(0,i.autoinject)(),(0,p.m6)({selectors:{installationId:(0,p.$t)((t=>t.installation?.id)),games:(0,p.$t)((t=>t.catalog?.games)),titles:(0,p.$t)((t=>t.catalog?.titles)),firstRun:(0,p.$t)((t=>t.flags?.firstRun))}}),(0,o.Sn)("design:paramtypes",[c.Q,h.s,n.il,s.l,r.j0,d.F2])],g)},"root/auth.html":(t,a,e)=>{e.r(a),e.d(a,{default:()=>s});var o=e(14385),i=e.n(o),n=new URL(e(18285),e.b),r=new URL(e(77898),e.b);const s='<template> <require from="./auth.scss"></require> <require from="shared/cheats/resources/custom-attributes/steam-capsule-bg"></require> <require from="../pro-promos/pro-showcase-slideshow/pro-showcase-slideshow"></require> <require from="../settings/resources/elements/language-selector"></require> <require from="../resources/elements/email-input"></require> <div class="header-label"> <div class="wemod-logo"><img src="'+i()(n)+'"></div> <span class="wemod-copy">wemod</span> </div> <div class="auth-container"> <button class="back-button" if.bind="mode === \'existing_user\'" click.trigger="mode = \'new_user\'"> <i class="icon-arrow-left"></i><span>${\'auth.back\' | i18n}</span> </button> <div class="form-container"> <div class="auth ${mode === \'existing_user\' ? \'auth-login\' : \'auth-new\'}"> <div class="logo ${launchTitleName ? \'with-launch-title\' : \'\'}"> <img src="'+i()(r)+'"> </div> <template if.bind="mode === \'existing_user\'"> <h1>${\'auth.welcome_back\' | i18n}</h1> <form submit.trigger="authenticate()"> <div class="text-input"> <div class="wrapper"> <input ref="existingEmailInput" value.bind="existingEmail" type="text" placeholder="${\'auth.email_or_username\' | i18n}"> </div> </div> <div class="text-input"> <div class="wrapper"> <input ref="existingPasswordInput" value.bind="existingPassword" type="password" placeholder="${\'auth.password\' | i18n}" maxlength="100"> </div> </div> <button class="primary" type="submit" disabled.bind="busy || !existingEmail || !existingPassword"> <span>${\'auth.log_in\' | i18n}</span><i class="icon-arrow-right"></i> </button> </form> <a href="website://forgot" target="_blank" class="forgot-link">${\'auth.forgot_password\' | i18n}</a> </template> <template if.bind="mode === \'new_user\'"> <form submit.trigger="createNewAccountWithEmail()"> <h1>${\'auth.new_user\' | i18n}</h1> <div class="launch-title-container" if.bind="launchTitleName && firstRun"> <div if.bind="launchTitleSteamAppId" class="game-image" steam-capsule-bg="steam-id.bind: launchTitleSteamAppId"></div> <span class="label-container"> <span if.bind="launchTitleTotalMods > 1" class="label">${\'auth.unlock_$x_mods_for\' | i18n:{x: launchTitleTotalMods}}</span> <span else class="label">${\'auth.unlock_mods_for\' | i18n}</span> <h1 class="title">${launchTitleName}</h1> </span> </div> <email-input value.bind="newEmail" status.bind="newEmailStatus" auto-focus.bind="true" placeholder.bind="\'auth.email_address\' | i18n" view-model.ref="emailInput" large.bind="true"></email-input> <button class="primary" type="submit" disabled.bind="busy || (emailRequired && newEmailStatus !== \'valid\' && newEmailStatus !== \'unsure\')"> <span>${\'auth.continue\' | i18n}</span><i class="icon-arrow-right"></i> </button> <div class="terms-message" if.bind="!gdprConsentGiven" innerhtml.bind="\'auth.review_terms\' | i18n | markdown"></div> <hr> <div class="form-footer"> <span class="footer-label">${\'auth.already_have_an_account\' | i18n}</span> <button click.delegate="mode = \'existing_user\'" class="secondary"> ${\'auth.log_in_now\' | i18n} </button> </div> </form> </template> </div> </div> <pro-showcase-slideshow on-arrow-click.call="onArrowClick()"></pro-showcase-slideshow> <language-selector secondary.bind="true"></language-selector> </div> </template> '},"root/auth.scss":(t,a,e)=>{e.r(a),e.d(a,{default:()=>d});var o=e(31601),i=e.n(o),n=e(76314),r=e.n(n),s=e(4417),l=e.n(s),h=new URL(e(83959),e.b),c=r()(i()),u=l()(h);c.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${u}) format("woff2")}.material-symbols-outlined,auth .back-button i,auth .form-container .auth button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}auth{display:flex;flex-direction:column;height:100vh;width:100%;margin-bottom:0}auth .header-label{position:absolute;color:#fff;top:8px;left:8px;height:24px;display:flex;flex-direction:row;align-items:center;gap:6px;padding:0 8px}auth .header-label .wemod-logo{display:flex;width:20px;height:12px;margin:0 auto}auth .header-label .wemod-copy{font-weight:600;font-size:14px;line-height:24px;display:flex;align-items:center;color:#fff;opacity:1;transition:.2s all ease-in-out;scale:1}auth::before{content:"";position:absolute;bottom:-50%;left:50%;transform:translateX(-50%);width:100%;height:100%;background:radial-gradient(ellipse at center, rgb(35, 39, 45) 0%, rgba(18, 18, 20, 0) 70%);pointer-events:none;z-index:0}auth .auth-container{display:flex;flex-direction:row;align-items:stretch;gap:24px;z-index:1;height:100%;width:100vw;padding:24px;flex:1;margin-top:48px}auth .back-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);position:absolute;top:48px;left:24px;height:48px;display:flex;flex-direction:row;align-items:center;justify-content:center;padding-top:10px;padding-bottom:10px}auth .back-button,auth .back-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) auth .back-button{border:1px solid #fff}}auth .back-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}auth .back-button>*:first-child{padding-left:0}auth .back-button>*:last-child{padding-right:0}auth .back-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) auth .back-button svg *{fill:CanvasText}}auth .back-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) auth .back-button svg{opacity:1}}auth .back-button img{height:50%}auth .back-button:disabled{opacity:.3}auth .back-button:disabled,auth .back-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){auth .back-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}auth .back-button:not(:disabled):hover svg{opacity:1}}auth .back-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){auth .back-button:not(:disabled):hover{background:rgba(255,255,255,.3)}}auth .back-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:18px;width:18px;height:18px}auth .back-button i:before{font-family:inherit;content:"arrow_back"}auth .form-container{flex:1;display:flex;justify-content:center}auth .form-container .auth{width:470px;padding:40px 48px 0;display:flex;flex-direction:column;align-items:center;justify-content:center;overflow:hidden}auth .form-container .auth .logo{margin:-60px auto -54px}auth .form-container .auth .logo img{width:200px;height:200px}auth .form-container .auth .label{font-weight:600;font-size:12px;color:rgba(255,255,255,.6);text-transform:uppercase;display:block;margin-bottom:5px}auth .form-container .auth h1{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;color:#fff}auth .form-container .auth h1.ellipsis{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0}auth .form-container .auth.auth-login h4{margin:0 0 15px}auth .form-container .auth h5{font-size:18px;line-height:30px;font-weight:500;color:rgba(255,255,255,.5);margin:0}auth .form-container .auth button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;display:flex;flex-direction:row;align-items:center;justify-content:center;height:48px !important}auth .form-container .auth button,auth .form-container .auth button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) auth .form-container .auth button{border:1px solid #fff}}auth .form-container .auth button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}auth .form-container .auth button>*:first-child{padding-left:0}auth .form-container .auth button>*:last-child{padding-right:0}auth .form-container .auth button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) auth .form-container .auth button svg *{fill:CanvasText}}auth .form-container .auth button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) auth .form-container .auth button svg{opacity:1}}auth .form-container .auth button img{height:50%}auth .form-container .auth button:disabled{opacity:.3}auth .form-container .auth button:disabled,auth .form-container .auth button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){auth .form-container .auth button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}auth .form-container .auth button:not(:disabled):hover svg{opacity:1}}auth .form-container .auth button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}auth .form-container .auth button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}auth .form-container .auth button i:before{font-family:inherit;content:"arrow_forward"}auth .form-container .auth button.primary{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;height:40px;box-shadow:none;line-height:24px;font-weight:700;color:#000;border-radius:56px;padding:10px 16px;background:#fff;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;letter-spacing:-0.5px;min-width:300px;width:100%}auth .form-container .auth button.primary,auth .form-container .auth button.primary *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) auth .form-container .auth button.primary{border:1px solid #fff}}auth .form-container .auth button.primary>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}auth .form-container .auth button.primary>*:first-child{padding-left:0}auth .form-container .auth button.primary>*:last-child{padding-right:0}auth .form-container .auth button.primary svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) auth .form-container .auth button.primary svg *{fill:CanvasText}}auth .form-container .auth button.primary svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) auth .form-container .auth button.primary svg{opacity:1}}auth .form-container .auth button.primary img{height:50%}auth .form-container .auth button.primary:disabled{opacity:.3}auth .form-container .auth button.primary:disabled,auth .form-container .auth button.primary:disabled *{cursor:default;pointer-events:none}@media(hover: hover){auth .form-container .auth button.primary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}auth .form-container .auth button.primary:not(:disabled):hover svg{opacity:1}}auth .form-container .auth button.primary:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}auth .form-container .auth button.primary:hover{background:rgba(255,255,255,.8) !important;color:rgba(0,0,0,.8) !important}auth .form-container .auth button.secondary{background-color:rgba(var(--color--brand-blue--rgb), 0.05);box-shadow:none !important;color:var(--color--brand-blue)}auth .form-container .auth button.secondary:hover{background:var(--color--brand-blue);color:#000}auth .form-container .auth form{width:100%}auth .form-container .auth hr{display:inline-block;border:0;border-top:1px solid rgba(255,255,255,.1);width:130px;margin:0 auto}auth .form-container .auth h5+button{margin-top:17px}auth .form-container .auth button+button,auth .form-container .auth email-input+button,auth .form-container .auth .text-input+button{margin-top:25px}auth .form-container .auth .text-input+.text-input{margin-top:15px}auth .form-container .auth .forgot-link+hr{margin-top:21px}auth .form-container .auth .forgot-link+hr+h5{margin-top:16px}auth .form-container .auth .terms-message+hr{margin-top:32px}auth .form-container .auth .terms-message+hr+h5{margin-top:23px}auth .form-container .auth email-input{width:100%}auth .form-container .auth .text-input{position:relative}auth .form-container .auth .text-input input{height:48px;width:100%;color:#fff;padding:12px;background-color:rgba(255,255,255,.05);border:1px solid rgba(255,255,255,.2);border-radius:12px;transition:background-color .15s,border-color .15s}auth .form-container .auth .text-input input,auth .form-container .auth .text-input input::placeholder{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px}auth .form-container .auth .text-input input::placeholder{color:rgba(255,255,255,.5)}auth .form-container .auth .text-input input:hover{border-color:rgba(255,255,255,.1)}auth .form-container .auth .text-input input:focus{background-color:rgba(255,255,255,0);border-color:rgba(255,255,255,.4);outline:none !important}auth .form-container .auth .forgot-link{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:rgba(255,255,255,.8);text-decoration:none;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;letter-spacing:-0.5px;margin-top:20px}auth .form-container .auth .forgot-link svg *{fill:rgba(255,255,255,.8)}@media(hover: hover){auth .form-container .auth .forgot-link:hover{color:#fff}auth .form-container .auth .forgot-link:hover svg *{fill:#fff}}auth .form-container .auth .terms-message{font-size:11px;line-height:16px;color:rgba(255,255,255,.3);max-width:270px;margin:17px auto 0}auth .form-container .auth .terms-message a{color:rgba(255,255,255,.5)}auth .form-container .auth .terms-message a:hover{color:#fff}auth .form-container .auth .launch-title-container{display:flex;flex-direction:row;align-items:center;align-self:flex-start;gap:16px;padding:16px;background-color:rgba(255,255,255,.05);border-radius:12px;margin-bottom:24px}auth .form-container .auth .launch-title-container .game-image{min-width:40px;height:60px;background-size:cover;background-position:center center;background-repeat:no-repeat;border-radius:8px;border:.5px solid rgba(255,255,255,.15);position:relative;display:inline-block !important}auth .form-container .auth .launch-title-container .game-image.is-fallback{width:0;min-width:0;visibility:hidden}auth .form-container .auth .launch-title-container .label-container{display:flex;flex-direction:column;align-items:flex-start;text-align:left}auth .form-container .auth .launch-title-container .label-container .label{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}auth .form-container .auth .launch-title-container .label-container .title{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:20px;line-height:24px;letter-spacing:-1px;margin:0}auth .form-container .form-footer{display:flex;flex-direction:column;align-items:center;justify-content:center;gap:16px;margin-top:16px}auth .form-container .form-footer .footer-label{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;color:rgba(255,255,255,.5)}auth .form-container .form-footer button{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;letter-spacing:-0.5px}auth language-selector{position:absolute;bottom:40px;left:24px;z-index:1000}@media(max-width: 1080px){auth .auth-container{gap:8px}}@media(max-height: 700px){auth .form-container .auth{padding:0 24px}auth .form-container .auth .terms-message+hr{margin-top:16px}auth .form-container .auth .logo.with-launch-title{display:none}}`,""]);const d=c}}]);
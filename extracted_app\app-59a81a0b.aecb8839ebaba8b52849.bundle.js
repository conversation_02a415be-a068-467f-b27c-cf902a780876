"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5784],{"app/resources/elements/app-header":(e,a,i)=>{i.r(a),i.d(a,{AppHeader:()=>d});var t=i(15215),r=i(45953),o=i("aurelia-event-aggregator"),n=i("aurelia-framework"),s=i(92465),p=i(54995);let d=class{#e;#a;#i;constructor(e,a){this.isGamePage=!1,this.isGameSupported=!1,this.isAppReady=!1,this.handleAppReady=()=>{this.isAppReady=!0,this.#e.push(this.#i.onDisplayTrainerChanged(this.handleDisplayTrainerChanged))},this.handleDisplayTrainerChanged=e=>{e?(this.titleName=this.catalog.titles[e.titleId]?.name,this.isGamePage=!0,this.trainerId=e.id,this.titleId=e.titleId,this.isGameSupported=!0):(this.titleName=void 0,this.isGamePage=!1,this.trainerId=void 0,this.titleId=void 0,this.isGameSupported=!1)},this.#a=e,this.#i=a}bind(){this.#e=new s.Vd([this.#a.subscribe("app-ready",this.handleAppReady)])}unbind(){this.#e?.dispose()}};(0,t.Cg)([n.bindable,(0,t.Sn)("design:type",Object)],d.prototype,"titleName",void 0),(0,t.Cg)([n.bindable,(0,t.Sn)("design:type",Object)],d.prototype,"titleId",void 0),(0,t.Cg)([n.bindable,(0,t.Sn)("design:type",Object)],d.prototype,"trainerId",void 0),(0,t.Cg)([n.bindable,(0,t.Sn)("design:type",Boolean)],d.prototype,"isGamePage",void 0),(0,t.Cg)([n.bindable,(0,t.Sn)("design:type",Boolean)],d.prototype,"isGameSupported",void 0),d=(0,t.Cg)([(0,n.autoinject)(),(0,p.m6)({selectors:{catalog:(0,p.$t)((e=>e.catalog))}}),(0,t.Sn)("design:paramtypes",[o.EventAggregator,r.m])],d)},"app/resources/elements/app-header.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>t});const t='<template> <require from="./app-header.scss"></require> <require from="./window-controls"></require> <require from="./help-menu"></require> <help-menu if.bind="isAppReady" placement="app-header" show-game-items.bind="isGamePage && isGameSupported" game-title.bind="titleName" title-id.bind="titleId" trainer-id.bind="trainerId"></help-menu> <window-controls></window-controls> </template> '},"app/resources/elements/app-header.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>b});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o),s=i(4417),p=i.n(s),d=new URL(i(83959),i.b),l=n()(r()),c=p()(d);l.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,.app-sidebar .nav a i.home-icon,.app-sidebar .nav a i.my-games-icon,.app-sidebar .nav a i.my-videos-icon,.app-sidebar .nav a i.explore-icon,.app-sidebar .nav a i.upcoming-icon,.app-sidebar .nav a i.maps-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.app-sidebar{display:flex;flex-direction:column;height:100%;width:220px;transition:.3s width ease-in-out;background:linear-gradient(to bottom, rgba(var(--theme--background-accent--rgb), 0.5) 0%, var(--theme--background-accent) 100%);position:relative;z-index:1}.app-sidebar sidebar-game-lists{overflow-y:hidden;height:-webkit-fill-available;padding-bottom:24px;scrollbar-gutter:stable}.app-sidebar sidebar-game-lists::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive:hover,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:decrement{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:increment{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent 8px, rgba(255, 255, 255, 0.1) 8px, rgba(255, 255, 255, 0.1) calc(100% - 8px), transparent calc(100% - 8px))}.app-sidebar sidebar-game-lists:hover{overflow-y:auto}.app-sidebar hr.sidebar-divider{display:flex;align-items:center;width:100%;height:0px;opacity:.5;border:none;border-top:1px solid rgba(255,255,255,.15);margin:8px 0}.app-sidebar hr.sidebar-divider.nav-divider{width:calc(100% - 16px);margin:8px auto}.app-sidebar.collapsed{width:72px}.app-sidebar.collapsed hr.sidebar-divider.nav-divider{width:100%}.app-sidebar.collapsed .sidebar-header{align-items:center;justify-content:center}.app-sidebar.collapsed .sidebar-header-branding .wemod-logo{margin:0 auto}.app-sidebar.collapsed .sidebar-header-branding .wemod-copy{visibility:hidden;position:absolute;left:40px;scale:.5;opacity:0}.app-sidebar.collapsed .nav{align-items:center;min-width:0;width:100%;margin:0 auto}.app-sidebar.collapsed .nav-text{visibility:hidden;position:absolute;transform:translateX(-10px);opacity:0}.app-sidebar.collapsed .nav a{padding:0}.app-sidebar.collapsed .nav a i{font-size:24px !important;margin:0 auto}.app-sidebar.collapsed .nav a new-badge{display:none}.app-sidebar .sidebar-header{position:relative;z-index:1;display:flex;flex-direction:row;align-items:start;gap:22px;min-height:40px;padding:8px}.app-sidebar .sidebar-header-branding{display:flex;flex-direction:row;align-items:center;gap:6px;padding:0 8px}.app-sidebar .sidebar-header-branding .wemod-logo{display:flex;width:20px;height:12px;margin:0 auto}.app-sidebar .sidebar-header-branding .wemod-copy{font-weight:600;font-size:14px;line-height:24px;display:flex;align-items:center;color:#fff;opacity:1;transition:.2s all ease-in-out;scale:1}.app-sidebar app-sidebar-search-button{margin:4px 0 8px}.app-sidebar .nav{position:relative;z-index:1;display:flex;flex-direction:column;gap:4px;min-width:72px;width:100%;padding:0 8px 0px 8px;border-radius:8px}.app-sidebar .nav-text{transition:opacity .5s ease-in-out,transform .2s ease-in-out}.app-sidebar .nav a{display:flex;flex-direction:row;align-items:center;padding:8px 8px 8px 12px;gap:10px;height:40px;mix-blend-mode:normal;border-radius:8px;font-weight:500;font-size:14px;line-height:24px;color:rgba(255,255,255,.8);border-radius:8px;width:100%;white-space:nowrap}.app-sidebar .nav a.current{background:rgba(255,255,255,.25);color:#fff}.app-sidebar .nav a.current i{color:rgba(255,255,255,.8) !important}.app-sidebar .nav a:hover{color:rgba(255,255,255,.9);background:rgba(255,255,255,.15)}.app-sidebar .nav a:hover i{color:rgba(255,255,255,.6) !important}.app-sidebar .nav a i{color:rgba(255,255,255,.6);font-size:20px}.app-sidebar .nav a .nav-text{flex:1}.app-sidebar .nav a i.home-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.home-icon:before{font-family:inherit;content:"home"}.app-sidebar .nav a i.my-games-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-games-icon:before{font-family:inherit;content:"browse"}.app-sidebar .nav a i.my-videos-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-videos-icon:before{font-family:inherit;content:"live_tv"}.app-sidebar .nav a i.explore-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.explore-icon:before{font-family:inherit;content:"feature_search"}.app-sidebar .nav a i.upcoming-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.upcoming-icon:before{font-family:inherit;content:"double_arrow"}.app-sidebar .nav a i.maps-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.maps-icon:before{font-family:inherit;content:"map"}app-header{height:36px;display:flex;align-items:center;justify-content:flex-end;margin-left:auto;padding:12px 5px 12px 10px;position:fixed;z-index:1001;right:0;top:0}app-header help-menu{-webkit-app-region:no-drag}app-header window-controls{flex-grow:0;-webkit-app-region:no-drag;position:relative}`,""]);const b=l},"app/resources/elements/app-search":(e,a,i)=>{i.r(a),i.d(a,{AppSearch:()=>x,HOTKEY:()=>h,HOTKEY_CODE:()=>m,OPEN_SEARCH_EVENT:()=>f});var t=i(15215),r=i("aurelia-dialog"),o=i("aurelia-event-aggregator"),n=i("aurelia-framework"),s=i("cheats/resources/value-converters/group-feed-items"),p=i(83802),d=i(96555),l=i(54995),c=i(38777),b=i(62914),u=i(43050),g=i(98120);const h=71,m="KeyG",f="open_search";let x=class{#t;#r;#o;#n;#s;#p;#d;#a;#l;#c;constructor(e,a,i,t,r,o,n,s){this.searchTerms="",this.#r=[],this.#n=e,this.#o=a,this.#s=i,this.#p=t,this.#d=r,this.#a=o,this.#l=n,this.#c=s}bind(){this.searchSession=this.#n.createSession((()=>this.#b()),"app_header"),this.myGames=this.#l.getFilteredFeed(u.DE,{maxItems:4}),this.recentlyPlayed=this.#l.getFilteredFeed(u._4,{maxItems:4}),this.recentlyViewed=this.#l.getFilteredFeed(u.VQ,{maxItems:4})}attached(){this.#t=(new c.Vd).pushEventListener(window,"keydown",this.#u.bind(this)).pushEventListener(document,"focusin",this.#g.bind(this)).push(this.searchSession).push(this.myGames).push(this.recentlyPlayed).push(this.recentlyViewed).push(this.#a.subscribe("router:navigation:complete",this.#h.bind(this))).push(this.#a.subscribe(f,(e=>this.open(e))))}detached(){this.#t.dispose()}searchTermsChanged(){this.searchSession?.reset(),this.#b()}resultsChanged(){this.#m()}#b(){if(!this.searchTerms||this.searchTerms.length<2)return void(this.results=null);const e=this.#n.search(this.searchSession,this.searchTerms).map((e=>"catalog-title"===e.resultType?(0,u.ZT)(this.state,e.result):"title-metadata"===e.resultType?(0,u.nP)(e.result,!!e.isInstalled):void 0)).filter((e=>!!e)),a=this.#d.toView(e,["playable","launch_without_mods","installable","unsupported_and_not_installed"]).map((e=>{const a=this.state.gameHistory;let i=[...e.items];if("playable"===e.group){const e=i.filter((e=>!!a[e.gameId??""]?.lastPlayedAt)).sort(((e,i)=>(a[i.gameId??""]?.lastPlayedAt??"").localeCompare(a[e.gameId??""]?.lastPlayedAt??""))),t=i.filter((a=>!e.includes(a)));i=[...e,...t]}return{group:e.group,items:i}}));this.results=a.flatMap((e=>e.items))}#u(e){if(!this.#t)return;const a=1===e.key.length&&(e.key>="a"&&e.key<="z"||e.key>="A"&&e.key<="Z"||e.key>="0"&&e.key<="9"),i=" "===e.key,t=e.altKey||e.ctrlKey||e.metaKey,r=document.activeElement===this.inputEl,o=["INPUT","TEXTAREA"].includes(document.activeElement?.tagName??"")&&!r,n=this.#s.hasOpenDialog,s=!!document.querySelector("trainer-hotkey.editing"),p=!!this.#p.trainer;e.ctrlKey&&e.code===m?this.open("hotkey"):o||n||s||("Escape"===e.key&&this.close(),!a&&!i||t||(r||p||(this.open("keyboard"),setTimeout((()=>this.searchTerms=e.key)),i&&e.preventDefault()),r&&0===this.searchTerms.length&&this.#m()),this.groupedFeedItems.some((e=>e.items.length>0))&&this.isOpen&&("ArrowDown"===e.key&&this.#f(1,e),"ArrowUp"===e.key&&this.#f(-1,e)),!t&&r&&!this.inputEl.value&&["Backspace","Delete"].includes(e.key)&&this.close())}clear(){this.searchTerms="",this.results=null,this.searchSession.reset(),this.inputEl.focus()}open(e){setTimeout((()=>this.clear())),this.isOpen||(this.#c?.event("search_open",{trigger:e},b.Io),this.isOpen=!0)}close(){this.isOpen=!1,this.clear(),this.inputEl.blur()}#m(){this.scrollEl&&(this.scrollEl.scrollTop=0)}#h(){this.close()}#x(e){return e.map((e=>{const a=!!this.myGames.items.find((a=>a.gameId===e.gameId)),i=this.titles[e.titleId],t=[];i?.gameIds?t.push(...i.gameIds.reduce(((e,a)=>[...e,...this.games[a].correlationIds]),[])):t.push(...e.correlationIds);const r=t.map(d.o.parse).find((e=>"steam"===e.platform))?.sku??"";return{...e,steamAppId:r,parentRoute:a?"collection/my-games":"",previousRoute:a?"my-games":""}}))}get groupedFeedItems(){let e;return this.results?e=[{items:this.#x(this.results)}]:(e=[],this.recentlyViewed.items.length&&e.push({title:"app_search.recent_games",items:this.#x(this.recentlyViewed.items)}),this.recentlyPlayed.items.length?e.push({title:"app_search.recently_played_games",items:this.#x(this.recentlyPlayed.items)}):this.myGames.items.length&&e.push({title:"app_search.my_games",items:this.#x(this.myGames.items)})),this.#r=[],e}#f(e,a){let i;this.#r.length||(this.#r=Array.from(this.#o.querySelectorAll(".focusable-result")));const t=this.#r.findIndex((e=>document.activeElement===e)),r=t>-1,o=this.#r.length-1;r?0===t&&-1===e||t===o&&1===e?(this.inputEl.focus(),a.preventDefault(),1===e&&this.#m()):r&&(i=this.#r[t+e]):i=1===e?this.#r[0]:this.#r[o],i&&(a.preventDefault(),i.focus())}#g(){this.isOpen&&!this.#o.contains(document.activeElement)&&this.close()}};(0,t.Cg)([n.observable,(0,t.Sn)("design:type",String)],x.prototype,"searchTerms",void 0),(0,t.Cg)([n.observable,(0,t.Sn)("design:type",Boolean)],x.prototype,"isOpen",void 0),(0,t.Cg)([n.observable,(0,t.Sn)("design:type",Object)],x.prototype,"results",void 0),(0,t.Cg)([(0,n.computedFrom)("results","myGames.items","recentlyPlayed.items","recentlyViewed.items"),(0,t.Sn)("design:type",Array),(0,t.Sn)("design:paramtypes",[])],x.prototype,"groupedFeedItems",null),x=(0,t.Cg)([(0,n.autoinject)(),(0,l.m6)({selectors:{state:e=>e.state,titles:(0,l.$t)((e=>e.catalog.titles)),games:(0,l.$t)((e=>e.catalog.games))}}),(0,t.Sn)("design:paramtypes",[g.p,Element,r.DialogService,p.jR,s.GroupFeedItemsValueConverter,o.EventAggregator,u.Y2,b.j0])],x)},"app/resources/elements/app-search.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>n});var t=i(14385),r=i.n(t),o=new URL(i(5261),i.b);const n='<template> <require from="./app-search.scss"></require> <require from="../../../cheats/resources/custom-attributes/steam-client-icon-bg"></require> <div class="app-search ${isOpen ? \'open\' : \'\'}"> <div class="app-search-overlay" click.delegate="close()"></div> <div class="app-search-wrapper"> <div class="app-search-input" click.delegate="inputEl.focus()"> <span class="icon"></span> <input ref="inputEl" value.bind="searchTerms & debounce:400" placeholder="${\'app_search.search\' | i18n}"> <span class="instructions" innerhtml.bind="\'app_search.use_keys_to_navigate\' | i18n | markdown"></span> </div> <div class="app-search-empty-message" if.bind="results && !results.length && !searchSession.pendingSearchTerms"> ${\'app_search.no_results_message\' | i18n} </div> <div class="app-search-results" overflow-fade="vertical" ref="scrollEl" if.bind="groupedFeedItems.length"> <div repeat.for="group of groupedFeedItems" if.bind="group.items.length" class="app-search-group ${group.className ? group.className : \'\'}"> <h4 class="app-search-group-title" if.bind="group.title">${group.title | i18n}</h4> <a repeat.for="item of group.items" class="app-search-result focusable-result" route-href="route.bind: \'title\'; params.bind: {titleId: item.titleId, gameId: item.gameId, parentRoute: item.parentRoute}" title-link="value.bind: \'app_header_search\'; title-id.bind: item.titleId; game-id.bind: item.gameId; search-result.bind: true;"> <div class="app-search-thumbnail" steam-client-icon-bg="steam-id.bind: item.steamAppId;"></div> <span class="app-search-unavailable-badge" if.bind="!item.isAvailable" title.bind="\'app_header_search.no_cheats_available\' | i18n"> <inline-svg src="'+r()(o)+'"></inline-svg> </span> <div class="app-search-result-name"> ${item.titleName}<template if.bind="item.gameEdition">&nbsp;(${item.gameEdition})</template> </div> </a> </div> </div> </div> </div> </template> '},"app/resources/elements/app-search.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>b});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o),s=i(4417),p=i.n(s),d=new URL(i(83959),i.b),l=n()(r()),c=p()(d);l.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,app-search .app-search-input .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}app-search .app-search{position:fixed;left:0;top:0;width:100%;height:100%;padding:24px;z-index:999;transition:opacity .15s,visibility 0s .15s;opacity:0;visibility:hidden;display:flex;justify-content:center}app-search .app-search.open{visibility:visible;opacity:1;transition-delay:0s}app-search .app-search-overlay{position:absolute;left:0;top:0;width:100%;height:100%;background:rgba(var(--theme--background--rgb), 0.5);z-index:-1}app-search .app-search-wrapper{background:linear-gradient(0deg, rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)),rgba(var(--theme--background--rgb), 0.95);box-shadow:0px 40px 60px rgba(0,0,0,.7);border:1px solid rgba(255,255,255,.05);border-radius:24px;display:flex;flex-direction:column;width:630px;height:fit-content;max-width:100%;overflow:hidden;max-height:min(492px,100%);margin-top:20vh}@media(max-height: 812px){app-search .app-search-wrapper{margin-top:0}}app-search .app-search-input{display:flex;align-items:center;gap:6px;padding:16px;flex:0 0 auto;position:relative;border-bottom:1px solid rgba(255,255,255,.05)}app-search .app-search-input,app-search .app-search-input *{cursor:text}app-search .app-search-input .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;padding-left:6px;color:rgba(255,255,255,.6);font-size:30px}app-search .app-search-input .icon:before{font-family:inherit;content:"search"}app-search .app-search-input input{flex:1 1 auto;padding:16px 0;color:#fff;background:rgba(0,0,0,0);border:none;outline:none !important}app-search .app-search-input input,app-search .app-search-input input::placeholder{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:20px;line-height:24px;letter-spacing:-1px;font-weight:500}app-search .app-search-input input::placeholder{color:rgba(255,255,255,.8)}app-search .app-search-input .instructions{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;flex:0 0 auto;color:rgba(255,255,255,.6)}app-search .app-search-input .instructions em{display:inline-block;padding:2px 4px;background:rgba(255,255,255,.04);border-radius:4px;font-style:normal}app-search .app-search-results{flex:1 1 auto;overflow:auto}app-search .app-search-results::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}app-search .app-search-results::-webkit-scrollbar-thumb:window-inactive,app-search .app-search-results::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}app-search .app-search-results::-webkit-scrollbar-thumb:window-inactive:hover,app-search .app-search-results::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}app-search .app-search-results::-webkit-scrollbar{width:25px;background:rgba(0,0,0,0)}app-search .app-search-results::-webkit-scrollbar-thumb:window-inactive,app-search .app-search-results::-webkit-scrollbar-thumb{border:10px solid rgba(0,0,0,0)}app-search .app-search-group{padding:12px 16px}app-search .app-search-group+.app-search-group{border-top:1px solid rgba(255,255,255,.05)}app-search .app-search-group-title{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;color:rgba(255,255,255,.6);padding:6px 8px;margin:0}app-search .app-search-result{display:flex;align-items:center;border-radius:8px;transition:background-color .15s;padding:6px 8px;gap:16px}app-search .app-search-result:hover,app-search .app-search-result:focus{background:rgba(255,255,255,.15);outline:none !important}app-search .app-search-thumbnail{width:20px;height:20px;border-radius:4px;overflow:hidden;position:relative;background-size:contain}app-search .app-search-thumbnail.is-fallback{display:flex !important;background:linear-gradient(180deg, #4743fc 0%, #dbe2e3 100%)}app-search .app-search-result-name{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:20px;line-height:24px;letter-spacing:-1px;font-weight:700;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;color:#fff;line-height:24px}app-search .app-search-empty-message{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:rgba(255,255,255,.7);padding:16px;border-top:1px solid rgba(255,255,255,.05)}app-search .app-search-unavailable-badge{width:16px;height:16px;border-radius:3px;background:#999;border:1px solid var(--theme--background);display:inline-flex;align-items:center;justify-content:center;flex:0 0 auto}app-search .app-search-unavailable-badge svg{width:12px}app-search .app-search-unavailable-badge svg *{fill:var(--theme--background)}`,""]);const b=l},"app/resources/elements/app-sidebar":(e,a,i)=>{i.r(a),i.d(a,{AppSidebar:()=>d});var t=i(15215),r=i(7530),o=i("aurelia-framework"),n=i(18776),s=i(44759),p=i(54995);let d=class{constructor(e,a){this.router=e,this.onlineStatus=a,this.isSidebarCollapsed=!1,this.isSidebarForcedCollapsed=!1}get shouldShowMyVideos(){return!!this.enableCapture}get isCollapsed(){return!(!this.isSidebarCollapsed&&!this.isSidebarForcedCollapsed)}get current(){return"dashboard"===this.router.currentInstruction.config.name?"dashboard":"collection"===this.router.currentInstruction.config.name&&"my-games"===this.router.currentInstruction.params.slug?"my-games":"my-videos"===this.router.currentInstruction.config.name?"my-videos":"collection"===this.router.currentInstruction.config.name&&"maps"===this.router.currentInstruction.params.slug?"maps":"queue"===this.router.currentInstruction.config.name||"queue"===this.router.currentInstruction.queryParams.parentRoute?"queue":["titles","collection"].includes(this.router.currentInstruction.config.name??"")?"titles":null}};(0,t.Cg)([o.bindable,(0,t.Sn)("design:type",Boolean)],d.prototype,"isSidebarCollapsed",void 0),(0,t.Cg)([o.bindable,(0,t.Sn)("design:type",Boolean)],d.prototype,"isSidebarForcedCollapsed",void 0),(0,t.Cg)([(0,r.Kj)("enableCapture"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],d.prototype,"shouldShowMyVideos",null),(0,t.Cg)([(0,r.Kj)("isSidebarCollapsed","isSidebarForcedCollapsed"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],d.prototype,"isCollapsed",null),(0,t.Cg)([(0,r.Kj)("router.currentInstruction"),(0,t.Sn)("design:type",Object),(0,t.Sn)("design:paramtypes",[])],d.prototype,"current",null),d=(0,t.Cg)([(0,o.autoinject)(),(0,p.m6)({selectors:{myVideosSeen:(0,p.$t)((e=>e.flags.myVideosSeen)),enableCapture:(0,p.$t)((e=>e.settings?.enableCapture))}}),(0,t.Sn)("design:paramtypes",[n.Ix,s.WA])],d)},"app/resources/elements/app-sidebar-more-menu":(e,a,i)=>{i.r(a),i.d(a,{AppSidebarMoreMenu:()=>g});var t=i(15215),r=i("aurelia-event-aggregator"),o=i("aurelia-framework"),n=i(18776),s=i(98119),p=i(69942),d=i(19072),l=i(54995),c=i(70236),b=i(38777),u=i(62914);let g=class{#e;#v;#c;#y;#w;constructor(e,a,i,t,r){this.isCollapsed=!1,this.badge="",this.#v=e,this.#c=a,this.host=i,this.#y=t,this.#w=r}attached(){this.#e=(new b.Vd).push(this.#v.subscribe("close-sidebar-user-menu",(()=>this.open=!1)))}detached(){this.#e.dispose(),this.closeDiscordTooltip()}closeDiscordTooltip(){this.discordTooltipOpen=!1}async openDiscord(){(0,c.Lt)(this.account.flags,256)?(this.#c.event("discord_server_click",{trigger:"header"},u.Io),await this.host.launchExternal("discord://discord.com/invite/wemod")||window.open("website://discord","_blank"),this.open=!1):this.discordTooltipOpen=!0}discordButtonClick(){this.discordTooltipOpen?this.closeDiscordTooltip():this.openDiscord()}handleRewardsButtonClick(){this.#v.publish("close-sidebar-user-menu"),this.#y.navigateToRoute("rewards")}openAccountProfile(){this.#v.publish("close-sidebar-user-menu"),this.#y.navigateToRoute("profile")}get showDiscordButton(){return!this.host.info.locale.endsWith("CN")}get hasAvailableRewards(){return(0,s.TQ)(this.account)||(0,s.uA)(this.account)}get hasClaimedReward(){return(0,s.uA)(this.account)}openFeedbackDialog(){this.featurebaseFeedbackFeatureFlag?.generalBoardId&&(this.open=!1,this.#w.open({boardId:this.featurebaseFeedbackFeatureFlag.generalBoardId}))}};(0,t.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.twoWay}),(0,t.Sn)("design:type",Boolean)],g.prototype,"open",void 0),(0,t.Cg)([o.bindable,(0,t.Sn)("design:type",Boolean)],g.prototype,"isCollapsed",void 0),(0,t.Cg)([o.bindable,(0,t.Sn)("design:type",String)],g.prototype,"badge",void 0),(0,t.Cg)([(0,o.computedFrom)("host.info.locale"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],g.prototype,"showDiscordButton",null),(0,t.Cg)([(0,o.computedFrom)("account"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],g.prototype,"hasAvailableRewards",null),(0,t.Cg)([(0,o.computedFrom)("account"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],g.prototype,"hasClaimedReward",null),g=(0,t.Cg)([(0,l.m6)({selectors:{account:(0,l.$t)((e=>e.account)),rewardsFeatureEnabled:(0,l.$t)((e=>e.catalog?.features?.partner_rewards)),featurebaseFeedbackFeatureFlag:(0,l.$t)((e=>e.catalog?.features?.featurebase_feedback))}}),(0,o.autoinject)(),(0,t.Sn)("design:paramtypes",[r.EventAggregator,u.j0,d.s,n.Ix,p.I])],g)},"app/resources/elements/app-sidebar-more-menu.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>n});var t=i(14385),r=i.n(t),o=new URL(i(86291),i.b);const n='<template class="au-animate"> <require from="../../../resources/elements/new-badge"></require> <require from="./app-sidebar-more-menu.scss"></require> <require from="./discord-tooltip"></require> <require from="./settings-button"></require> <require from="./app-sidebar-user-info"></require> <div class="overlay" click.delegate="open = false"></div> <div class="container"> <app-sidebar-user-info click.delegate="openAccountProfile()" badge.bind="badge"></app-sidebar-user-info> <hr class="sidebar-divider"> <nav> <div class="group"> <a class="button" href="https://wemod.gg/help" target="_blank" click.delegate="open = false" tabindex="0"> <i>help</i> <span class="label">${\'sidebar_user_menu.help\' | i18n}</span> <i>arrow_outward</i> </a> <div class="discord-button-container"> <button click.delegate="discordButtonClick()"> <span class="icon icon-discord"> <i class="wemod-icon wemod-icon--m"><inline-svg src="'+r()(o)+'"></inline-svg></i> </span> <span class="label">Discord</span> <i>arrow_outward</i> </button> <discord-tooltip open.bind="discordTooltipOpen"></discord-tooltip> </div> <div if.bind="rewardsFeatureEnabled && hasAvailableRewards" class="rewards-button-container"> <button click.delegate="handleRewardsButtonClick()"> <i class="icon rewards-icon"></i> <span class="label">${\'rewards.rewards\' | i18n}</span> <new-badge if.bind="!hasClaimedReward"></new-badge> </button> </div> <div if.bind="!!featurebaseFeedbackFeatureFlag.generalBoardId"> <button click.delegate="openFeedbackDialog()"> <i>chat_bubble</i> <span class="label">${\'sidebar_user_menu.share_feedback\' | i18n}</span> <i>arrow_forward</i> </button> </div> </div> <hr class="sidebar-divider"> <span class="group"> <settings-button></settings-button> </span> </nav> </div> </template> '},"app/resources/elements/app-sidebar-more-menu.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>b});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o),s=i(4417),p=i.n(s),d=new URL(i(83959),i.b),l=n()(r()),c=p()(d);l.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,app-sidebar-more-menu>.container .rewards-button-container .rewards-icon,app-sidebar-more-menu>.container>nav button i,app-sidebar-more-menu>.container>nav a.button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}app-sidebar-more-menu{position:absolute;left:0;z-index:1000;bottom:calc(100% + 6px) !important;margin-left:6px}app-sidebar-more-menu>.overlay{position:fixed;top:0;right:0;left:0;bottom:0;background:#0d0f12;opacity:.5;z-index:-1;-webkit-app-region:no-drag}app-sidebar-more-menu>.container{display:flex;flex-direction:column;justify-content:flex-start;padding:8px;position:relative;width:280px;background:rgba(128,128,128,.1);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:16px;font-weight:800;color:rgba(255,255,255,.8);letter-spacing:-0.5px;padding:0}app-sidebar-more-menu>.container .group{margin:8px}app-sidebar-more-menu>.container .disclosure-arrow{display:flex;align-items:center}app-sidebar-more-menu>.container .disclosure-arrow>.label{font-size:10px !important}app-sidebar-more-menu>.container .discord-button-container{position:relative;z-index:1}app-sidebar-more-menu>.container .discord-button-container .icon-discord path{fill:rgba(255,255,255,.6)}app-sidebar-more-menu>.container .rewards-button-container .rewards-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}app-sidebar-more-menu>.container .rewards-button-container .rewards-icon:before{font-family:inherit;content:"celebration"}app-sidebar-more-menu>.container .rewards-button-container new-badge{color:#000;background:#fff}app-sidebar-more-menu>.container>nav{display:flex;flex-direction:column;gap:1px}app-sidebar-more-menu>.container>nav button,app-sidebar-more-menu>.container>nav a.button{display:flex;align-items:center;gap:8px;padding:8px;background:rgba(0,0,0,0);border:none;transition:background-color .15s;border-radius:8px;text-align:left;width:100%}app-sidebar-more-menu>.container>nav button:hover,app-sidebar-more-menu>.container>nav a.button:hover{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}app-sidebar-more-menu>.container>nav button i,app-sidebar-more-menu>.container>nav a.button i{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;width:20px;height:20px;display:inline-flex;align-items:center;justify-content:center;color:var(--menu__item__icon--color, rgba(255, 255, 255, 0.6));transition:color .15s}app-sidebar-more-menu>.container>nav button .label,app-sidebar-more-menu>.container>nav a.button .label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.6));padding:2px 0;transition:color .15s;font-size:14px;line-height:20px;font-weight:700;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.8))}app-sidebar-more-menu>.container>nav button .badge,app-sidebar-more-menu>.container>nav a.button .badge{font-weight:700;flex:0 0 auto;font-size:10px;line-height:16px;text-transform:uppercase;color:var(--menu__item__badge--color, rgba(255, 255, 255, 0.8));transition:color .15s;background:var(--menu__item__badge--background-color, rgba(255, 255, 255, 0.1));padding:0 4px;border-radius:4px}app-sidebar-more-menu>.container>nav button .free-badge,app-sidebar-more-menu>.container>nav a.button .free-badge{font-weight:700;flex:0 0 auto;font-size:10px;line-height:16px;text-transform:uppercase;color:var(--menu__item__badge--color, rgba(255, 255, 255, 0.8));transition:color .15s;background:var(--menu__item__badge--background-color, rgba(255, 255, 255, 0.1));padding:0 4px;border-radius:4px;--menu__item__badge--color: var(--color--accent);--menu__item__badge--background-color: rgba(var(--color--accent--rgb), 0.1)}app-sidebar-more-menu>.container>nav button.active,app-sidebar-more-menu>.container>nav a.button.active{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}app-sidebar-more-menu>.container>nav button:hover .icon-discord path,app-sidebar-more-menu>.container>nav a.button:hover .icon-discord path{fill:#fff}app-sidebar-more-menu>.container hr{margin:12px -8px;padding:0;border:none;border-top:1px solid rgba(255,255,255,.15);opacity:.5 !important;margin:0 !important}app-sidebar-more-menu>.container>p{font-weight:500;font-size:14px;line-height:20px;color:rgba(255,255,255,.6);margin:0;padding:8px}app-sidebar-more-menu>.container app-sidebar-user-info{padding:8px;width:100%;position:relative;z-index:2}app-sidebar-more-menu>.container app-sidebar-user-info .user{cursor:pointer;width:100%;padding:8px}app-sidebar-more-menu>.container app-sidebar-user-info .user *{cursor:pointer}app-sidebar-more-menu>.container app-sidebar-user-info .user:hover{background-color:rgba(255,255,255,.15)}app-sidebar-more-menu>.container app-sidebar-user-info .user:hover .info .username{color:rgba(255,255,255,.8)}app-sidebar-more-menu>.container app-sidebar-user-info .avatar{display:flex;align-items:center;gap:8px}app-sidebar-more-menu>.container app-sidebar-user-info .avatar img{display:inline-block;border-radius:50%;overflow:hidden;width:40px;height:40px;outline:none}app-sidebar-more-menu>.container app-sidebar-user-info .info{display:grid}app-sidebar-more-menu>.container app-sidebar-user-info .info .username{color:inherit}app-sidebar-more-menu>.container app-sidebar-user-info .info .username .badge-container{justify-self:end;pointer-events:auto}app-sidebar-more-menu>.container app-sidebar-user-info .label{font-size:14px}`,""]);const b=l},"app/resources/elements/app-sidebar-search-button":(e,a,i)=>{i.r(a),i.d(a,{AppSidebarSearchButton:()=>s});var t=i(15215),r=i("aurelia-event-aggregator"),o=i("aurelia-framework"),n=i("app/resources/elements/app-search");let s=class{#a;constructor(e){this.searchOpen=!1,this.hotkey=n.HOTKEY,this.ctrlKey=17,this.#a=e}openSearch(){this.#a.publish(n.OPEN_SEARCH_EVENT,"click")}};(0,t.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,t.Sn)("design:type",Boolean)],s.prototype,"sidebarCollapsed",void 0),s=(0,t.Cg)([(0,o.autoinject)(),(0,t.Sn)("design:paramtypes",[r.EventAggregator])],s)},"app/resources/elements/app-sidebar-search-button.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>t});const t='<template> <require from="./app-sidebar-search-button.scss"></require> <require from="./app-search"></require> <require from="../../../shared/cheats/resources/value-converters/proper-hotkey"></require> <require from="../../../resources/custom-attributes/detach-el"></require> <button class="app-sidebar-search-button ${sidebarCollapsed ? \'collapsed\' : \'\'}" click.delegate="openSearch()"> <i class="app-sidebar-search-button-icon"></i> <span class="app-sidebar-search-button-label">${\'app_sidebar_search_button.search\' | i18n}</span> <span class="app-sidebar-search-button-hotkey">${ctrlKey | properHotkey}+${hotkey | properHotkey}</span> </button> <app-search detach-el></app-search> </template> '},"app/resources/elements/app-sidebar-search-button.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>b});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o),s=i(4417),p=i.n(s),d=new URL(i(83959),i.b),l=n()(r()),c=p()(d);l.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,app-sidebar-search-button .app-sidebar-search-button-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}app-sidebar-search-button{display:block;width:100%}app-sidebar-search-button .app-sidebar-search-button{display:flex;width:100%;text-align:left;align-items:center;border-radius:28px;background:rgba(255,255,255,.1);border:1px solid rgba(255,255,255,.1);box-shadow:0px 2px 2px rgba(0,0,0,.2);padding:12px;transition:background-color .15s}app-sidebar-search-button .app-sidebar-search-button,app-sidebar-search-button .app-sidebar-search-button *{cursor:pointer}app-sidebar-search-button .app-sidebar-search-button-label{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;flex:1 1 auto;color:rgba(255,255,255,.8);transition:color .15s,opacity .5s ease-in-out,max-width .5s ease-in-out,margin .2s ease-in-out;white-space:nowrap;overflow:hidden;margin-left:10px}app-sidebar-search-button .app-sidebar-search-button-hotkey{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;color:rgba(255,255,255,.4);margin-left:12px;display:none}app-sidebar-search-button .app-sidebar-search-button-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;color:rgba(255,255,255,.6);transition:color .15s}app-sidebar-search-button .app-sidebar-search-button-icon:before{font-family:inherit;content:"search"}app-sidebar-search-button .app-sidebar-search-button.collapsed{justify-content:center}app-sidebar-search-button .app-sidebar-search-button.collapsed .app-sidebar-search-button-label{transform:scale(0);opacity:0;max-width:0;margin-left:0}app-sidebar-search-button .app-sidebar-search-button.collapsed .app-sidebar-search-button-hotkey{display:none !important}app-sidebar-search-button .app-sidebar-search-button:hover{background:rgba(255,255,255,.15)}app-sidebar-search-button .app-sidebar-search-button:hover .app-sidebar-search-button-hotkey{display:inline-block}`,""]);const b=l},"app/resources/elements/app-sidebar-user":(e,a,i)=>{i.r(a),i.d(a,{AppSidebarUser:()=>c});var t=i(15215),r=i("aurelia-event-aggregator"),o=i("aurelia-framework"),n=i(18776),s=i(20770),p=i(54995),d=i(70236),l=i(48881);let c=class{#k;#y;#v;constructor(e,a,i){this.isCollapsed=!1,this.#k=e,this.#y=a,this.#v=i}get isCreator(){const e=(0,d.Lt)(this.account?.flags,64);return!e&&this.creatorMode&&this.#k.dispatch(l.Kc,{creatorMode:!1},"user_header",!0),e}get badge(){return this.isCreator?this?.creatorMode?"creator":"pro":this.account?.subscription?"pro":null}openProfilePage(){this.#v.publish("close-sidebar-user-menu"),this.#y.navigateToRoute("profile")}};(0,t.Cg)([o.bindable,(0,t.Sn)("design:type",Boolean)],c.prototype,"isCollapsed",void 0),(0,t.Cg)([(0,o.computedFrom)("account.flags"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],c.prototype,"isCreator",null),(0,t.Cg)([(0,o.computedFrom)("account.flags","creatorMode"),(0,t.Sn)("design:type",Object),(0,t.Sn)("design:paramtypes",[])],c.prototype,"badge",null),c=(0,t.Cg)([(0,p.m6)({selectors:{account:(0,p.$t)((e=>e.account)),creatorMode:(0,p.$t)((e=>e.settings.creatorMode))}}),(0,o.autoinject)(),(0,t.Sn)("design:paramtypes",[s.il,n.Ix,r.EventAggregator])],c)},"app/resources/elements/app-sidebar-user-info":(e,a,i)=>{i.r(a),i.d(a,{AppSidebarUserInfo:()=>p});var t=i(15215),r=i("aurelia-framework"),o=i(20770),n=i(54995),s=i(48881);let p=class{#k;constructor(e){this.isCollapsed=!1,this.dropdownOpen=!1,this.isCreator=!1,this.badge="",this.#k=e}toggleCreatorMode(e){this.isCreator&&(this.#k.dispatch(s.Kc,{creatorMode:!this.creatorMode},"user_header"),e?.stopPropagation())}};(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",Boolean)],p.prototype,"isCollapsed",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",Boolean)],p.prototype,"dropdownOpen",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",Boolean)],p.prototype,"isCreator",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",String)],p.prototype,"badge",void 0),p=(0,t.Cg)([(0,n.m6)({selectors:{account:(0,n.$t)((e=>e.account)),creatorMode:(0,n.$t)((e=>e.settings.creatorMode))}}),(0,r.autoinject)(),(0,t.Sn)("design:paramtypes",[o.il])],p)},"app/resources/elements/app-sidebar-user-info.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>n});var t=i(14385),r=i.n(t),o=new URL(i(89247),i.b);const n='<template class="${isCollapsed ? \'is-collapsed\' : \'\'} ${badge ? \'has-badge\' : \'\'}"> <require from="./app-sidebar-user-info.scss"></require> <require from="./sidebar-pro-badge"></require> <div class="user"> <div class="avatar" tabindex="0"> <img if.bind="account.profileImage" fallback-src="'+r()(o)+'" src.bind="account.profileImage | cdn:{size: 32}"> <i if.bind="!dropdownOpen" class="caret">keyboard_arrow_down</i> <i else class="caret">keyboard_arrow_up</i> </div> <div class="info"> <div class="username"> <span class="label" ref="usernameEl" tabindex="0">${account.username}</span> <span if.bind="badge" class="badge-container"> <span if.bind="isCreator" class="badge ${badge}" click.trigger="toggleCreatorMode($event)">${badge}</span> <sidebar-pro-badge else></sidebar-pro-badge> </span> </div> </div> </div> </template> '},"app/resources/elements/app-sidebar-user-info.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>b});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o),s=i(4417),p=i.n(s),d=new URL(i(83959),i.b),l=n()(r()),c=p()(d);l.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,app-sidebar-user-info.is-collapsed .user .avatar .caret,.app-sidebar .nav a i.home-icon,.app-sidebar .nav a i.my-games-icon,.app-sidebar .nav a i.my-videos-icon,.app-sidebar .nav a i.explore-icon,.app-sidebar .nav a i.upcoming-icon,.app-sidebar .nav a i.maps-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.app-sidebar{display:flex;flex-direction:column;height:100%;width:220px;transition:.3s width ease-in-out;background:linear-gradient(to bottom, rgba(var(--theme--background-accent--rgb), 0.5) 0%, var(--theme--background-accent) 100%);position:relative;z-index:1}.app-sidebar sidebar-game-lists{overflow-y:hidden;height:-webkit-fill-available;padding-bottom:24px;scrollbar-gutter:stable}.app-sidebar sidebar-game-lists::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive:hover,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:decrement{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:increment{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent 8px, rgba(255, 255, 255, 0.1) 8px, rgba(255, 255, 255, 0.1) calc(100% - 8px), transparent calc(100% - 8px))}.app-sidebar sidebar-game-lists:hover{overflow-y:auto}.app-sidebar hr.sidebar-divider{display:flex;align-items:center;width:100%;height:0px;opacity:.5;border:none;border-top:1px solid rgba(255,255,255,.15);margin:8px 0}.app-sidebar hr.sidebar-divider.nav-divider{width:calc(100% - 16px);margin:8px auto}.app-sidebar.collapsed{width:72px}.app-sidebar.collapsed hr.sidebar-divider.nav-divider{width:100%}.app-sidebar.collapsed .sidebar-header{align-items:center;justify-content:center}.app-sidebar.collapsed .sidebar-header-branding .wemod-logo{margin:0 auto}.app-sidebar.collapsed .sidebar-header-branding .wemod-copy{visibility:hidden;position:absolute;left:40px;scale:.5;opacity:0}.app-sidebar.collapsed .nav{align-items:center;min-width:0;width:100%;margin:0 auto}.app-sidebar.collapsed .nav-text{visibility:hidden;position:absolute;transform:translateX(-10px);opacity:0}.app-sidebar.collapsed .nav a{padding:0}.app-sidebar.collapsed .nav a i{font-size:24px !important;margin:0 auto}.app-sidebar.collapsed .nav a new-badge{display:none}.app-sidebar .sidebar-header{position:relative;z-index:1;display:flex;flex-direction:row;align-items:start;gap:22px;min-height:40px;padding:8px}.app-sidebar .sidebar-header-branding{display:flex;flex-direction:row;align-items:center;gap:6px;padding:0 8px}.app-sidebar .sidebar-header-branding .wemod-logo{display:flex;width:20px;height:12px;margin:0 auto}.app-sidebar .sidebar-header-branding .wemod-copy{font-weight:600;font-size:14px;line-height:24px;display:flex;align-items:center;color:#fff;opacity:1;transition:.2s all ease-in-out;scale:1}.app-sidebar app-sidebar-search-button{margin:4px 0 8px}.app-sidebar .nav{position:relative;z-index:1;display:flex;flex-direction:column;gap:4px;min-width:72px;width:100%;padding:0 8px 0px 8px;border-radius:8px}.app-sidebar .nav-text{transition:opacity .5s ease-in-out,transform .2s ease-in-out}.app-sidebar .nav a{display:flex;flex-direction:row;align-items:center;padding:8px 8px 8px 12px;gap:10px;height:40px;mix-blend-mode:normal;border-radius:8px;font-weight:500;font-size:14px;line-height:24px;color:rgba(255,255,255,.8);border-radius:8px;width:100%;white-space:nowrap}.app-sidebar .nav a.current{background:rgba(255,255,255,.25);color:#fff}.app-sidebar .nav a.current i{color:rgba(255,255,255,.8) !important}.app-sidebar .nav a:hover{color:rgba(255,255,255,.9);background:rgba(255,255,255,.15)}.app-sidebar .nav a:hover i{color:rgba(255,255,255,.6) !important}.app-sidebar .nav a i{color:rgba(255,255,255,.6);font-size:20px}.app-sidebar .nav a .nav-text{flex:1}.app-sidebar .nav a i.home-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.home-icon:before{font-family:inherit;content:"home"}.app-sidebar .nav a i.my-games-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-games-icon:before{font-family:inherit;content:"browse"}.app-sidebar .nav a i.my-videos-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-videos-icon:before{font-family:inherit;content:"live_tv"}.app-sidebar .nav a i.explore-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.explore-icon:before{font-family:inherit;content:"feature_search"}.app-sidebar .nav a i.upcoming-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.upcoming-icon:before{font-family:inherit;content:"double_arrow"}.app-sidebar .nav a i.maps-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.maps-icon:before{font-family:inherit;content:"map"}app-sidebar-user-info{display:flex;padding:4px 6px;gap:8px;border-radius:8px;align-items:center}app-sidebar-user-info .buttons{display:flex}app-sidebar-user-info .user{width:100%;display:flex;flex-direction:row;align-items:center;gap:8px;border-radius:8px}app-sidebar-user-info .user,app-sidebar-user-info .user *{text-decoration:none}app-sidebar-user-info .user .avatar{display:flex;align-items:center;gap:6px;flex:0}app-sidebar-user-info .user .avatar .caret{display:none}app-sidebar-user-info .user .avatar img{display:inline-block;border-radius:50%;overflow:hidden;width:20px;height:20px;outline:1px solid rgba(255,255,255,.5)}app-sidebar-user-info .user .info{display:none}app-sidebar-user-info.has-badge{flex:1 0 0}app-sidebar-user-info.has-badge .info{display:flex;flex:1 1 0;min-width:0;width:100%}app-sidebar-user-info.has-badge .info .username{width:100%;display:flex;align-items:center;font-size:12px;color:rgba(255,255,255,.6);min-width:0}app-sidebar-user-info.has-badge .info .username .label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;margin-right:8px;min-width:0;width:min-content}app-sidebar-user-info.has-badge .info .username .badge-container{margin-left:auto;margin-top:-1px;flex:0 0 auto;position:relative;justify-self:end}app-sidebar-user-info.has-badge .info .username .badge{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px}@media(forced-colors: active){body:not(.override-contrast-mode) app-sidebar-user-info.has-badge .info .username .badge{border:1px solid #fff}}app-sidebar-user-info.has-badge .info .username .badge,app-sidebar-user-info.has-badge .info .username .badge *{cursor:pointer}app-sidebar-user-info.has-badge .info .username .badge.pro{background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff}app-sidebar-user-info.is-collapsed{justify-content:center;padding:4px 6px 4px 10px;border-radius:12px;align-items:center}app-sidebar-user-info.is-collapsed .user .avatar img{width:28px;height:28px}app-sidebar-user-info.is-collapsed .user .avatar .caret{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:#fff;font-size:16px;display:flex;align-items:center;opacity:.5;transition:opacity .15s}app-sidebar-user-info.is-collapsed .user .info{display:none !important}`,""]);const b=l},"app/resources/elements/app-sidebar-user.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>t});const t='<template> <require from="./app-sidebar-user.scss"></require> <require from="./app-sidebar-more-menu"></require> <require from="./app-sidebar-user-info"></require> <require from="./go-pro-cta"></require> <require from="../../../resources/custom-attributes/close-if-press-escape"></require> <app-sidebar-more-menu is-collapsed.bind="isCollapsed" if.bind="dropdownOpen" close-if-press-escape="open.bind: dropdownOpen" open.bind="dropdownOpen" badge.bind="badge"></app-sidebar-more-menu> <div class="actions ${isCollapsed ? \'is-collapsed\' : \'\'}"> <go-pro-cta if.bind="!badge" is-collapsed.bind="isCollapsed"></go-pro-cta> <div class="primary-actions ${badge ? \'has-badge\' : \'\'}"> <app-sidebar-user-info click.delegate="isCollapsed ? dropdownOpen = true : openProfilePage()" is-collapsed.bind="isCollapsed" badge.bind="badge" is-creator.bind="isCreator" dropdown-open.bind="dropdownOpen"></app-sidebar-user-info> <div class="buttons"> <a class="button-container icon" click.delegate="dropdownOpen = true"> <i if.bind="dropdownOpen">keyboard_arrow_up</i> <i else>keyboard_arrow_down</i> </a> </div> </div> </div> </template> '},"app/resources/elements/app-sidebar-user.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>b});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o),s=i(4417),p=i.n(s),d=new URL(i(83959),i.b),l=n()(r()),c=p()(d);l.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,app-sidebar-user .buttons i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}app-sidebar-user{display:flex;flex-direction:row;margin-top:auto;position:relative}app-sidebar-user>*{display:flex;align-items:center}app-sidebar-user .app-sidebar-more-menu-container{position:absolute;left:0;z-index:1001 !important;bottom:calc(100% + 5px) !important}app-sidebar-user .actions{width:220px;transition:.3s all ease-in-out;border-top:1px solid rgba(255,255,255,.05);padding:12px 8px;flex:0 1 auto;display:flex;justify-content:space-between;align-items:center;min-width:0;bottom:0}app-sidebar-user .actions .primary-actions{display:flex;align-items:center;gap:6px}app-sidebar-user .actions .primary-actions.has-badge{width:100%;justify-content:space-between}app-sidebar-user .actions .primary-actions>app-sidebar-user-info{padding:4px}app-sidebar-user .actions .primary-actions>app-sidebar-user-info,app-sidebar-user .actions .primary-actions>app-sidebar-user-info *{cursor:pointer}app-sidebar-user .actions .primary-actions>app-sidebar-user-info:hover{background-color:rgba(255,255,255,.15)}app-sidebar-user .actions .primary-actions>app-sidebar-user-info:hover .info .username{color:rgba(255,255,255,.8)}app-sidebar-user .actions .primary-actions>app-sidebar-user-info .user .avatar,app-sidebar-user .actions .primary-actions>app-sidebar-user-info .user .avatar *{cursor:pointer}app-sidebar-user .actions .primary-actions>app-sidebar-user-info .user .info .label,app-sidebar-user .actions .primary-actions>app-sidebar-user-info .user .info .label *{cursor:pointer}app-sidebar-user .actions .primary-actions>app-sidebar-user-info .user .label{max-width:100px}app-sidebar-user .actions.is-collapsed{flex-direction:column;gap:6px;width:72px}app-sidebar-user .actions.is-collapsed .buttons{display:none}app-sidebar-user .actions .creator-mode-toggle{display:flex;justify-content:center;margin-right:9px}app-sidebar-user .buttons{display:flex}app-sidebar-user .buttons .button-container{border-radius:6px;padding:4px}app-sidebar-user .buttons .button-container:hover{background:rgba(255,255,255,.15)}app-sidebar-user .buttons .button-container:hover i{opacity:1}app-sidebar-user .buttons .button-container:hover i.settings{rotate:180deg}app-sidebar-user .buttons i{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:flex;justify-content:center;align-items:center;color:#fff;opacity:.4;width:20px;height:20px;transition:opacity .15s,rotate .25s}`,""]);const b=l},"app/resources/elements/app-sidebar.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>n});var t=i(14385),r=i.n(t),o=new URL(i(18285),i.b);const n='<template> <require from="resources/elements/new-badge"></require> <require from="./app-sidebar.scss"></require> <require from="./app-sidebar-search-button"></require> <require from="./app-sidebar-user"></require> <require from="./sidebar-now-playing"></require> <require from="./sidebar-game-lists"></require> <require from="./reward-card"></require> <div class="app-sidebar ${isCollapsed ? \'collapsed\' : \'\'}"> <div class="sidebar-header"> <div class="sidebar-header-branding"> <div class="wemod-logo"><img src="'+r()(o)+'"></div> <span class="wemod-copy">wemod</span> </div> </div> <nav class="nav"> <app-sidebar-search-button sidebar-collapsed.bind="isSidebarCollapsed || isSidebarForcedCollapsed"> </app-sidebar-search-button> <a route-href="route: dashboard" class="${current === \'dashboard\' ? \'current\' : \'\'}"> <i class="home-icon"></i><span class="nav-text">${\'app_header.home\' | i18n}</span> </a> <a route-href="route.bind: \'collection\'; params.bind: {slug: \'my-games\'}" class="${current === \'my-games\' ? \'current\' : \'\'}"> <i class="my-games-icon"></i><span class="nav-text">${\'app_header.my_games\' | i18n}</span> </a> <a if.bind="shouldShowMyVideos" route-href="route: my-videos" class="${current === \'my-videos\' ? \'current\' : \'\'}"> <i class="my-videos-icon"></i><span class="nav-text">${\'app_header.my_videos\' | i18n}</span> <new-badge if.bind="!myVideosSeen"></new-badge> </a> <a route-href="route.bind: \'collection\'; params.bind: {slug: \'maps\'}" class="${current === \'maps\' ? \'current\' : \'\'}"> <i class="maps-icon"></i> <span class="nav-text">${\'app_header.maps\' | i18n}</span> </a> <a route-href="route: titles" class="${current === \'titles\' ? \'current\' : \'\'}"> <i class="explore-icon"></i><span class="nav-text">${\'app_header.explore\' | i18n}</span> </a> <a if.bind="onlineStatus.status === \'online\'" route-href="route: queue" class="${current === \'queue\' ? \'current\' : \'\'}"> <i class="upcoming-icon"></i><span class="nav-text">${\'app_header.upcoming\' | i18n}</span> </a> </nav> <reward-card if.bind="!isCollapsed"></reward-card> <hr class="sidebar-divider nav-divider"> <sidebar-now-playing is-sidebar-collapsed.bind="isSidebarCollapsed || isSidebarForcedCollapsed"></sidebar-now-playing> <sidebar-game-lists is-sidebar-collapsed.bind="isSidebarCollapsed || isSidebarForcedCollapsed"></sidebar-game-lists> <app-sidebar-user is-collapsed.bind="isCollapsed"></app-sidebar-user> </div> </template> '},"app/resources/elements/app-sidebar.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>b});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o),s=i(4417),p=i.n(s),d=new URL(i(83959),i.b),l=n()(r()),c=p()(d);l.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,.app-sidebar .nav a i.home-icon,.app-sidebar .nav a i.my-games-icon,.app-sidebar .nav a i.my-videos-icon,.app-sidebar .nav a i.explore-icon,.app-sidebar .nav a i.upcoming-icon,.app-sidebar .nav a i.maps-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.app-sidebar{display:flex;flex-direction:column;height:100%;width:220px;transition:.3s width ease-in-out;background:linear-gradient(to bottom, rgba(var(--theme--background-accent--rgb), 0.5) 0%, var(--theme--background-accent) 100%);position:relative;z-index:1}.app-sidebar sidebar-game-lists{overflow-y:hidden;height:-webkit-fill-available;padding-bottom:24px;scrollbar-gutter:stable}.app-sidebar sidebar-game-lists::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive:hover,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:decrement{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:increment{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent 8px, rgba(255, 255, 255, 0.1) 8px, rgba(255, 255, 255, 0.1) calc(100% - 8px), transparent calc(100% - 8px))}.app-sidebar sidebar-game-lists:hover{overflow-y:auto}.app-sidebar hr.sidebar-divider{display:flex;align-items:center;width:100%;height:0px;opacity:.5;border:none;border-top:1px solid rgba(255,255,255,.15);margin:8px 0}.app-sidebar hr.sidebar-divider.nav-divider{width:calc(100% - 16px);margin:8px auto}.app-sidebar.collapsed{width:72px}.app-sidebar.collapsed hr.sidebar-divider.nav-divider{width:100%}.app-sidebar.collapsed .sidebar-header{align-items:center;justify-content:center}.app-sidebar.collapsed .sidebar-header-branding .wemod-logo{margin:0 auto}.app-sidebar.collapsed .sidebar-header-branding .wemod-copy{visibility:hidden;position:absolute;left:40px;scale:.5;opacity:0}.app-sidebar.collapsed .nav{align-items:center;min-width:0;width:100%;margin:0 auto}.app-sidebar.collapsed .nav-text{visibility:hidden;position:absolute;transform:translateX(-10px);opacity:0}.app-sidebar.collapsed .nav a{padding:0}.app-sidebar.collapsed .nav a i{font-size:24px !important;margin:0 auto}.app-sidebar.collapsed .nav a new-badge{display:none}.app-sidebar .sidebar-header{position:relative;z-index:1;display:flex;flex-direction:row;align-items:start;gap:22px;min-height:40px;padding:8px}.app-sidebar .sidebar-header-branding{display:flex;flex-direction:row;align-items:center;gap:6px;padding:0 8px}.app-sidebar .sidebar-header-branding .wemod-logo{display:flex;width:20px;height:12px;margin:0 auto}.app-sidebar .sidebar-header-branding .wemod-copy{font-weight:600;font-size:14px;line-height:24px;display:flex;align-items:center;color:#fff;opacity:1;transition:.2s all ease-in-out;scale:1}.app-sidebar app-sidebar-search-button{margin:4px 0 8px}.app-sidebar .nav{position:relative;z-index:1;display:flex;flex-direction:column;gap:4px;min-width:72px;width:100%;padding:0 8px 0px 8px;border-radius:8px}.app-sidebar .nav-text{transition:opacity .5s ease-in-out,transform .2s ease-in-out}.app-sidebar .nav a{display:flex;flex-direction:row;align-items:center;padding:8px 8px 8px 12px;gap:10px;height:40px;mix-blend-mode:normal;border-radius:8px;font-weight:500;font-size:14px;line-height:24px;color:rgba(255,255,255,.8);border-radius:8px;width:100%;white-space:nowrap}.app-sidebar .nav a.current{background:rgba(255,255,255,.25);color:#fff}.app-sidebar .nav a.current i{color:rgba(255,255,255,.8) !important}.app-sidebar .nav a:hover{color:rgba(255,255,255,.9);background:rgba(255,255,255,.15)}.app-sidebar .nav a:hover i{color:rgba(255,255,255,.6) !important}.app-sidebar .nav a i{color:rgba(255,255,255,.6);font-size:20px}.app-sidebar .nav a .nav-text{flex:1}.app-sidebar .nav a i.home-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.home-icon:before{font-family:inherit;content:"home"}.app-sidebar .nav a i.my-games-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-games-icon:before{font-family:inherit;content:"browse"}.app-sidebar .nav a i.my-videos-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-videos-icon:before{font-family:inherit;content:"live_tv"}.app-sidebar .nav a i.explore-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.explore-icon:before{font-family:inherit;content:"feature_search"}.app-sidebar .nav a i.upcoming-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.upcoming-icon:before{font-family:inherit;content:"double_arrow"}.app-sidebar .nav a i.maps-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.maps-icon:before{font-family:inherit;content:"map"}`,""]);const b=l},"app/resources/elements/discord-tooltip":(e,a,i)=>{i.r(a),i.d(a,{DiscordTooltip:()=>p});var t=i(15215),r=i("aurelia-event-aggregator"),o=i("aurelia-framework"),n=i(78268),s=i(62914);let p=class{#c;#_;#v;constructor(e,a,i){this.open=!1,this.#v=e,this.#c=a,this.#_=i}openChanged(){this.open&&this.#c.event("discord_tooltip_open",{},s.Io)}connect(){window.open("website://account/connections/discord#auth","_blank"),this.#v.publish("close-sidebar-user-menu"),this.#c.event("discord_connect_click",{trigger:"discord_tooltip"},s.Io),this.#_.watchFlag(256,30),this.open=!1}};(0,t.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.twoWay}),(0,t.Sn)("design:type",Boolean)],p.prototype,"open",void 0),p=(0,t.Cg)([(0,o.autoinject)(),(0,t.Sn)("design:paramtypes",[r.EventAggregator,s.j0,n.s])],p)},"app/resources/elements/discord-tooltip.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>n});var t=i(14385),r=i.n(t),o=new URL(i(81231),i.b);const n='<template> <require from="./discord-tooltip.scss"></require> <require from="../../../shared/resources/elements/tooltip"></require> <tooltip class="discord-tooltip custom-tooltip" open.bind="open"> <div slot="content"> <div class="layout"> <div class="content"> <h1>${\'discord_tooltip.join_our_discord\' | i18n}</h1> <p>${\'discord_tooltip.connect_message\' | i18n}</p> <button click.delegate="connect()">${\'discord_tooltip.connect_discord\' | i18n}</button> </div> <img class="graphic" src="'+r()(o)+'"> </div> </div> </tooltip> </template> '},"app/resources/elements/discord-tooltip.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>s});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o)()(r());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.discord-tooltip .tooltip{left:calc(100% + 12px);bottom:-6px;background:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.05);border-radius:10px}.discord-tooltip .tooltip .tooltip-content{padding:0;border:0 !important}.discord-tooltip .tooltip .tooltip-content>*+*{margin-left:0}.discord-tooltip .tooltip .tooltip-arrow{display:none}.discord-tooltip .layout{background:var(--theme--default--secondary-background);display:flex;width:550px}.discord-tooltip .content{flex:1 1 auto;padding:17px 24px}.discord-tooltip .graphic{flex:0 0 auto;width:204px;height:211px;border-top-right-radius:10px;border-bottom-right-radius:10px}.discord-tooltip h1{font-weight:800;font-size:21px;line-height:30px;font-weight:700;color:#fff;margin:0 0 15px}.discord-tooltip p{font-weight:700;font-size:15px;line-height:24px;font-weight:500;display:block;color:rgba(255,255,255,.75)}.discord-tooltip button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;margin-top:25px}.discord-tooltip button,.discord-tooltip button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .discord-tooltip button{border:1px solid #fff}}.discord-tooltip button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.discord-tooltip button>*:first-child{padding-left:0}.discord-tooltip button>*:last-child{padding-right:0}.discord-tooltip button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .discord-tooltip button svg *{fill:CanvasText}}.discord-tooltip button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .discord-tooltip button svg{opacity:1}}.discord-tooltip button img{height:50%}.discord-tooltip button:disabled{opacity:.3}.discord-tooltip button:disabled,.discord-tooltip button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.discord-tooltip button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.discord-tooltip button:not(:disabled):hover svg{opacity:1}}.discord-tooltip button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.discord-tooltip button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.discord-tooltip button:not(:disabled):active{background-color:var(--theme--highlight)}",""]);const s=n},"app/resources/elements/education-card":(e,a,i)=>{i.r(a),i.d(a,{EducationCard:()=>o});var t=i(15215),r=i("aurelia-framework");class o{constructor(){this.showBetaTag=!1}handleButtonClick(){this.onButtonClick?.()}}(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",String)],o.prototype,"subtitleKey",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",String)],o.prototype,"titleKey",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",String)],o.prototype,"descriptionKey",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",Object)],o.prototype,"descriptionParams",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",String)],o.prototype,"buttonTextKey",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",Object)],o.prototype,"graphicImageSrc",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",Object)],o.prototype,"backgroundImageSrc",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",Boolean)],o.prototype,"showBetaTag",void 0),(0,t.Cg)([r.bindable,(0,t.Sn)("design:type",Function)],o.prototype,"onButtonClick",void 0)},"app/resources/elements/education-card.html":(e,a,i)=>{i.r(a),i.d(a,{default:()=>t});const t='<template> <require from="./education-card.scss"></require> <require from="../../../resources/elements/beta-tag.html"></require> <div class="education-card" style.bind="backgroundImageSrc ? `background-image: url(${backgroundImageSrc})` : \'\'"> <div class="content"> <div class="subtitle" if.bind="subtitleKey">${subtitleKey | i18n}</div> <div class="title" if.bind="titleKey">${titleKey | i18n} <beta-tag if.bind="showBetaTag"></beta-tag></div> <p class="description" if.bind="descriptionKey" innerhtml.bind="descriptionKey | i18n: descriptionParams | markdown"></p> <button click.delegate="handleButtonClick()">${buttonTextKey | i18n}</button> </div> <img if.bind="graphicImageSrc" src.bind="graphicImageSrc"> </div> </template> '},"app/resources/elements/education-card.scss":(e,a,i)=>{i.r(a),i.d(a,{default:()=>s});var t=i(31601),r=i.n(t),o=i(76314),n=i.n(o)()(r());n.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.education-card{min-height:320px;width:100%;color:#fff;border-radius:20px;padding:0px 40px;display:flex;flex-direction:row;justify-content:space-between;background-size:cover;background-repeat:no-repeat;background-position:top center}.education-card .content{width:40%;display:flex;flex-direction:column;justify-content:center}.education-card .content .subtitle{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px}.education-card .content .title{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:36px;line-height:40px;letter-spacing:-2px;display:flex;align-items:center;gap:10px}.education-card .content .title beta-tag{--beta-tag-bg-color: #ee343f;--beta-tag-color: #fff}.education-card .content .description{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-primary);margin-top:8px}.education-card .content button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);width:min-content}.education-card .content button,.education-card .content button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .education-card .content button{border:1px solid #fff}}.education-card .content button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.education-card .content button>*:first-child{padding-left:0}.education-card .content button>*:last-child{padding-right:0}.education-card .content button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .education-card .content button svg *{fill:CanvasText}}.education-card .content button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .education-card .content button svg{opacity:1}}.education-card .content button img{height:50%}.education-card .content button:disabled{opacity:.3}.education-card .content button:disabled,.education-card .content button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.education-card .content button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.education-card .content button:not(:disabled):hover svg{opacity:1}}.education-card .content button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.education-card .content button:not(:disabled):hover{background:rgba(255,255,255,.3)}}.education-card img{height:280px;width:auto;align-self:flex-end}',""]);const s=n}}]);
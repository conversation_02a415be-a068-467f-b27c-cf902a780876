"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1110],{68663:(e,t,i)=>{function s(e){const t=e.headers.get("etag")||null;return t?t.replace(/^W\//,"").replace(/"/g,""):null}i.d(t,{x:()=>a});class a{#e;#t;#i;constructor(e,t,i){this.#t="",this.#e=e,this.#i=t,this.#t=i}registerInstallation(e){return this.#e.post("/v3/installation",{token:e})}requestRemoteAuthCode(){return this.#e.post("/v3/auth/remote_code")}async requestOverlayAuthCode(){return await this.#s("/v3/auth/overlay_code")}async requestWebAuthCode(){return await this.#s("/v3/auth/web_code")}async#s(e){return(await this.#e.post(e)).code}createWebview(e){return this.#e.post("/v3/webview",e)}getUserAccount(){return this.#e.fetch({endpoint:"/v3/account",method:"GET",name:"/v3/account",collectMetrics:!1})}async getUserAccountFlags(e){return(await this.#e.fetch({endpoint:`/v3/account/flags/${e}`,method:"GET",name:"/v3/account/flags/:mask",collectMetrics:!1})).flags}changeAccountEmail(e,t=null){return this.#e.post("/v3/account/email",{email:e,currentPassword:t})}changeAccountPassword(e,t=null){return this.#e.post("/v3/account/password",{password:e,currentPassword:t})}changeAccountUsername(e){return this.#e.post("/v3/account/username",{username:e,currentPassword:null})}changeAccountProfileImage(e){return this.#e.post("/v3/account/profile_image",{profileImage:e})}setAccountLanguage(e,t){return this.#e.post("/v3/account/language",{tag:e,auto:t})}resumeSubscription(){return this.#e.post("/v3/account/subscription",{state:"active"})}getLastInvoice(){return this.#e.get("/v3/account/invoice")}async getBillingPortalUrl(){const e=await this.#e.get("/v3/account/billing_portal");return e?e.url:null}async getCatalog(e){const t={method:"GET",cache:"no-store",headers:{Accept:"application/json"}};e&&t.headers&&(t.headers["If-None-Match"]=`"${e}"`);const i=Date.now(),a=await fetch(this.#t,t);return 200===a.status&&this.#i.report({endpoint:this.#t,method:"GET",responseTime:Date.now()-i}),{body:304!==a.status?await a.json():null,cacheKey:s(a)}}getUnavailableTitle(e){return this.#e.get(`/v3/unavailable_titles/${e}`)}searchUnavailableTitles(e,t){return this.#e.get("/v3/unavailable_titles",{q:e,limit:t.toString()})}getUnavailableTitlesByCorrelationIds(e){return this.#e.post("/v3/unavailable_titles",e)}getMostCompatibleTrainerForGame(e,t,i){return this.#e.fetch({endpoint:`/v3/games/${e}/trainer`,method:"GET",query:{gameVersions:i.join(","),locale:t,v:"3"},name:"/v3/games/:gameId/trainer",collectMetrics:!1})}getTrainerById(e,t){return this.#e.get(`/v3/trainers/${e}`,{locale:t,v:"3"})}getLocalTrainerById(e,t){return this.#e.get(`/v3/trainers/${e}/local`,{locale:t,v:"3"})}getLatestLocalTrainerForGame(e){return this.#e.get(`/v3/games/${e}/local_trainer`,{v:"3"})}getTrainerHistoryForGame(e){return this.#e.get(`/v3/games/${e}/trainers`)}reportInstalledGameVersions(e){return this.#e.post("/v3/installed_games",e)}submitTrainerFeedback(e){return this.#e.post("/v3/trainer_feedback",e)}claimRewardOffer(e){return this.#e.post("/v3/partner_rewards/claim",{rewardKey:e})}submitAppRating(e,t,i=!1){return this.#e.post("/v3/ratings"+(i?"?nps=true":""),{rating:e,feedback:t})}recordGamePresence(e){return this.#e.post("/v3/game_presence",{gameId:e})}boostGame(e,t){return this.#e.post(`/v3/games/${e}/boost`,{availableBoosts:t})}respondToPoll(e,t,i,s){return this.#e.post(`/v3/polls/${e}/cast`,{selections:t,customSelection:i,details:s})}getFollowedGames(){return this.#e.fetch({endpoint:"/v3/account/followed_games",method:"GET",name:"/v3/account/followed_games",collectMetrics:!1})}followGames(e,t){return this.#e.post("/v3/account/followed_games",{type:t,gameIds:e})}unfollowGames(e){return this.#e.post("/v3/account/followed_games",{type:0,gameIds:e})}unfollowAllGames(){return this.#e.delete("/v3/account/followed_games")}suggestCheats(e,t){return this.#e.post(`/v3/games/${e}/suggest`,{suggestions:t})}getPaymentMethods(){return this.#e.get("/v3/checkout/methods")}removePaymentMethod(e){return this.#e.delete(`/v3/checkout/methods/${e}`)}getConsentRequirements(){return this.#e.get("/v3/gdpr")}giveConsent(){return this.#e.post("/v3/account/gdpr",{consentGiven:!0})}revokeConsent(){return this.#e.post("/v3/account/gdpr",{consentGiven:!1})}getPromotion(){return this.#e.fetch({endpoint:"/v3/promotion",method:"GET",name:"/v3/promotion",collectMetrics:!1})}claimObjectiveReward(e){return this.#e.post("/v3/gamify/objective/claim",{id:e})}getCurrentObjectiveSets(){return this.#e.get("/v3/gamify/set/current")}reportUserEvent(e,...t){return this.#e.post("/v3/user/event",{event:e,...void 0===t[0]?{}:{data:t[0]}})}getUserGamePreferences(e){return this.#e.get(`/v3/user/game/${e}`)}updatePinnedMods(e,t){return this.#e.post(`/v3/user/game/${e}/pinned_mods`,{data:t})}getSuggestedMods(e){return this.#e.get(`/v3/games/${e}/mod_suggestions`)}createModSuggestion(e,t){return this.#e.post(`/v3/games/${e}/mod_suggestion`,{suggestion:t})}boostModSuggestion(e,t){return this.#e.post(`/v3/games/${e}/mod_suggestion/${t}/boost`)}}},"ads/ad-popup":(e,t,i)=>{i.r(t),i.d(t,{AdPopup:()=>g});var s=i(15215),a=i("aurelia-event-aggregator"),r=i("aurelia-framework"),o=i(62914),n=i(16953),c=i(19072),l=i(92465),d=i(20057),p=i(54995),h=i(70236);const u=`${n.A.websiteUrl}/webview/ad/dynamic`;let g=class{#a;#r;#o;#n;#c;constructor(e,t,i,s){this.src=null,this.#r=e,this.#o=t,this.#n=i,this.#c=s}attached(){this.#l(),this.#a=new l.Vd([this.#o.onLocaleChanged((()=>this.#d())),this.#r.whenVisible((()=>this.#n.event("gamepage_ad_player_show",{},o.Io))),this.#r.onRestoredFromTray((()=>this.#l())),this.#r.onClosedToTray((()=>this.#l())),this.#c.subscribe("reload-adviews",(()=>this.#l()))]),this.accountChanged()}detached(){this.#a?.dispose(),document.body.classList.remove("ad-popup-active")}accountChanged(){(0,h.Lt)(this.account?.flags,8192)?this.active=!0:this.account?.subscription?this.active=!1:this.active=!0}activeChanged(){document.body.classList.toggle("ad-popup-active",this.active)}#d(){this.#l()}async#l(){this.src=this.#r.visible?await this.#p():null}async#p(){let e=new URL(u);var t;return this.overrideAdProvider&&(e=new URL((t=this.overrideAdProvider,`${n.A.websiteUrl}/webview/ad/${t}`))),e.searchParams.set("host",this.#r.info.devMode?"dev_app":"app"),e.searchParams.set("locale",this.#o.getEffectiveLocale().toString()),e.searchParams.set("titleId",this.titleId),this.account.adViewData&&e.searchParams.set("config",this.account.adViewData),e.toString()}overrideAdProviderChanged(){this.#l()}};(0,s.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.fromView}),(0,s.Sn)("design:type",Boolean)],g.prototype,"active",void 0),(0,s.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.toView}),(0,s.Sn)("design:type",String)],g.prototype,"titleId",void 0),g=(0,s.Cg)([(0,p.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,p.$t)((e=>e.account)),overrideAdProvider:(0,p.$t)((e=>e.settings.overrideAdProvider))}}),(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[c.s,d.F2,o.j0,a.EventAggregator])],g)},"ads/ad-popup.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});const s='<template> <require from="./ad-popup.scss"></require> <require from="./ad-view"></require> <require from="../resources/elements/pro-cta-label"></require> <div class="wrapper" if.bind="active"> <ad-view if.bind="src" id="game_page_bottom_right" src.bind="src"></ad-view> <footer> <a pro-cta="trigger: game_page_ad_footer; feature: no_ads"> <span class="label" innerhtml.bind="\'ad_popup.remove_ads_with_wemod_pro\' | i18n | markdown"></span> <span class="cta"><pro-cta-label></pro-cta-label></span> </a> </footer> </div> </template> '},"ads/ad-popup.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var s=i(31601),a=i.n(s),r=i(76314),o=i.n(r)()(a());o.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}ad-popup .wrapper{display:flex;flex-direction:column;width:400px;position:fixed;right:30px;bottom:var(--promotion-banner-height, 0);margin-bottom:20px;background:rgba(255,255,255,.025);border-radius:8px;overflow:hidden}ad-popup .wrapper ad-view{overflow:hidden;width:100%;height:300px;border:0}@media(max-height: 800px){ad-popup .wrapper{margin-bottom:0}}ad-popup footer{display:flex;align-items:center;height:45px;border-top:1px solid rgba(255,255,255,.05)}ad-popup footer a{display:flex;align-items:center;width:100%;height:100%;padding:0 10px;gap:8px}ad-popup footer .label{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;flex:1;color:var(--theme--text-primary);line-height:16px}ad-popup footer .label strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;vertical-align:middle;margin:-1.5px 0 0 4px}@media(forced-colors: active){body:not(.override-contrast-mode) ad-popup footer .label strong{border:1px solid #fff}}ad-popup footer .cta{font-weight:900;border:1px solid rgba(255,255,255,.25);padding:6px 8px;font-size:10px;line-height:12px;font-style:italic;color:var(--theme--text-primary);transition:color .15s;border-radius:20px;text-transform:uppercase}ad-popup footer .cta:after{content:" ➔"}ad-popup footer:not(:hover) .label strong{background:rgba(255,255,255,.2)}ad-popup footer:hover .cta{color:var(--theme--text-highlight)}body{--ad-popup--safe-width: 0px;--ad-popup--safe-height: 0px}body.ad-popup-active{--ad-popup--safe-width: 430px;--ad-popup--safe-height: calc( 365px + var(--promotion-banner-height, 0px) )}body.ad-popup-active .view-background .overflow-fade__wrapper:after{width:calc(100% - var(--ad-popup--safe-width))}@media(max-height: 800px){body.ad-popup-active{--ad-popup--safe-height: calc(345px + var(--promotion-banner-height, 0px))}}',""]);const n=o},"ads/ad-view":(e,t,i)=>{i.r(t),i.d(t,{AdView:()=>p,AdViewMessageEvent:()=>d});var s=i(15215),a=i("aurelia-event-aggregator"),r=i("aurelia-framework"),o=i(19072),n=i(92465),c=i(54995),l=i(70236);class d{constructor(e,t,i){this.viewId=e,this.eventName=t,this.params=i}}let p=class{#a;#c;constructor(e,t){this.fullscreen=!1,this.#c=e,this.preloadPath=`${t.info.paths.assets}/preload/ad-view.js`,this.userAgent=navigator.userAgent.replace(/ Electron\/[0-9.]+/,"")}attached(){this.#a=(new n.Vd).pushEventListener(this.webviewEl,"console-message",(e=>{if(e.message.startsWith("wemod:")){const t=new URL(e.message),i=t.pathname,s=Object.fromEntries(t.searchParams.entries());this.#c.publish(new d(this.id,i,s))}})).pushEventListener(this.webviewEl,"dom-ready",(()=>{this.webviewEl.setAudioMuted(!0),(0,l.Lt)(this.accountFlags,8192)&&this.webviewEl.openDevTools()}))}detached(){this.#a?.dispose(),this.#a=null}};(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Boolean)],p.prototype,"fullscreen",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",String)],p.prototype,"id",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",String)],p.prototype,"src",void 0),p=(0,s.Cg)([(0,c.m6)({setup:"attached",teardown:"detached",selectors:{accountFlags:(0,c.$t)((e=>e.account?.flags))}}),(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[a.EventAggregator,o.s])],p)},"ads/ad-view.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});const s='<template class="${fullscreen ? \'fullscreen\' : \'\'}"> <require from="./ad-view.scss"></require> <webview ref="webviewEl" src.bind="src" partition="persist:ads" webpreferences="backgroundThrottling=no, nodeIntegration=no, sandbox=yes, disableDialogs=yes" preload.bind="preloadPath" useragent.bind="userAgent" allowpopups> </webview> </template> '},"ads/ad-view.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var s=i(31601),a=i.n(s),r=i(76314),o=i.n(r)()(a());o.push([e.id,"ad-view{display:block;width:100%;height:100%}ad-view webview{display:flex;width:100%;height:100%;border:none}",""]);const n=o},"ads/cmp-popup":(e,t,i)=>{i.r(t),i.d(t,{CmpPopup:()=>g});var s=i(15215),a=i("aurelia-event-aggregator"),r=i("aurelia-framework"),o=i(62914),n=i(16953),c=i(19072),l=i(92465),d=i(20057),p=i("ads/ad-view");const h="cmp",u=`${n.A.websiteUrl}/webview/ad-cmp`;let g=class{#r;#o;#n;#c;#h;#u;constructor(e,t,i,s){this.visible=!1,this.id=h,this.#h=null,this.#u=null,this.#r=e,this.#o=t,this.#n=i,this.#c=s}attached(){this.#h=this.#c.subscribe(p.AdViewMessageEvent,(e=>this.#g(e)))}detached(){this.#h?.dispose(),this.#u?.dispose()}#g(e){if(e.viewId===h&&"error"===e.eventName)return this.#n.event("cmp_script_error",{},o.Io),void this.#v();if("tcdata"!==e.eventName)return;if("true"!==e.params.gdprApplies)return void this.#v();const t=e.params.eventStatus;e.viewId===h?("cmpuishown"===t&&(this.#n.event("cmp_open",{},o.Io),this.#m(),this.#u?.dispose()),"tcloaded"===t&&this.#v(),"useractioncomplete"===t&&(this.#n.event("cmp_consent",{},o.Io),this.#v(),this.#c.publish("reload-adviews"))):"cmpuishown"===t&&this.#m()}#v(){this.#u?.dispose(),this.visible=!1}#m(){this.visible||(this.src=this.#w(),this.#u=(0,l.Ix)((()=>{this.#v(),this.#n.event("cmp_timeout_error",{},o.Io)}),2e4),this.visible=!0)}#w(){const e=this.#r.info.devMode?"dev_app":"app";return`${u}?host=${e}&locale=${this.#o.getEffectiveLocale().toString()}`}};g=(0,s.Cg)([(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[c.s,d.F2,o.j0,a.EventAggregator])],g)},"ads/cmp-popup.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});const s='<template> <require from="./cmp-popup.scss"></require> <require from="./ad-view"></require> <div class="wrapper" if.bind="visible"> <ad-view src.bind="src" id.bind="id"></ad-view> </div> </template> '},"ads/cmp-popup.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var s=i(31601),a=i.n(s),r=i(76314),o=i.n(r)()(a());o.push([e.id,"cmp-popup .wrapper{position:fixed;top:0;left:0;width:100vw;height:100vh;z-index:99999}",""]);const n=o}}]);
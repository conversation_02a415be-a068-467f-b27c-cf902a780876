"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4286],{"dialogs/selection-dialog":(o,t,e)=>{e.r(t),e.d(t,{SelectionDialog:()=>d,SelectionDialogService:()=>r});var i=e(15215),a=e("aurelia-dialog"),s=e("aurelia-framework"),l=e(17275),n=e(88849);let d=class{constructor(o){this.controller=o,this.customSelection="",this.details=""}activate(o){this.config=o;const t=!0===o.multiselect?{}:o.multiselect;if(this.minSelections=t&&t.minSelections||1,this.maxSelections=t?t.maxSelections||o.options.length:1,Array.isArray(o.defaultSelections)){const t=o.defaultSelections.map((o=>o.toString()));this.selections=o.options.filter((o=>t.includes(o.toString()))).slice(0,this.maxSelections)}else this.selections=[]}get canSubmit(){return!(this.selections.length<this.minSelections||this.selections.length>this.maxSelections)&&this.#o()&&this.#t()}#o(){if(!this.config.customSelection)return!0;if(!this.#e())return!0;const o=(0,n.jD)(this.customSelection)||"";return o.length>0&&o.length<=this.config.customSelection.maxLength}#t(){if(!this.config.detailsField)return!0;if(this.config.detailsField.lastOptionOnly&&!this.#e())return!0;const o=(0,n.jD)(this.details)||"";return o.length>=(this.config.detailsField.minLength||0)&&o.length<=this.config.detailsField.maxLength}get showDetailsField(){return!!this.config.detailsField&&(!this.config.detailsField.lastOptionOnly||this.#e())}get enableCustomSelectionField(){return!!this.config.customSelection&&this.#e()}submit(){this.controller.ok({selection:this.selections[0]?.toString(),selections:this.selections.map((o=>o.toString())),customSelection:this.enableCustomSelectionField?(0,n.jD)(this.customSelection):null,details:this.showDetailsField?(0,n.jD)(this.details):null})}toggleSelection(o){this.config.multiselect?this.selections.includes(o)?this.selections=this.selections.filter((t=>t!==o)):this.selections.length<this.maxSelections&&(this.selections.push(o),this.selections=this.selections.slice(0)):this.selections.includes(o)||(this.selections=[o])}#e(){const o=this.config.options[this.config.options.length-1];return this.selections.includes(o)}};(0,i.Cg)([(0,s.computedFrom)("selections","customSelection","details"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],d.prototype,"canSubmit",null),(0,i.Cg)([(0,s.computedFrom)("selections"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],d.prototype,"showDetailsField",null),(0,i.Cg)([(0,s.computedFrom)("selections"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],d.prototype,"enableCustomSelectionField",null),d=(0,i.Cg)([(0,s.autoinject)(),(0,i.Sn)("design:paramtypes",[a.DialogController])],d);let r=class extends l.C{constructor(){super(...arguments),this.viewModelClass="dialogs/selection-dialog"}};r=(0,i.Cg)([(0,s.autoinject)()],r)},"dialogs/selection-dialog.html":(o,t,e)=>{e.r(t),e.d(t,{default:()=>i});const i='<template> <require from="./selection-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="selection-dialog secondary-gradient-bg"> <ux-dialog-header> <h1 class="title">${config.title | i18n}</h1> <close-button click.delegate="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <div class="message" if.bind="config.message" innerhtml.bind="config.message | i18n | markdown"></div> <ul class="options"> <li repeat.for="option of config.options" class="option ${selections.includes(option) ? \'selected\' : \'\'} ${!selections.includes(option) && maxSelections > 1 && selections.length == maxSelections ? \'disabled\' : \'\'} ${$last && config.customSelection ? \'has-input\' : \'\'}" click.delegate="toggleSelection(option)" tabindex="0"> <span class="checkbox"></span> <span class="label"> ${option | i18n}${($last && config.customSelection) ? \':\' : \'\'} <input if.bind="$last && config.customSelection" placeholder.bind="config.customSelection.placeholder | i18n" maxlength.bind="config.customSelection.maxLength" focus.one-way="enableCustomSelectionField" value.bind="customSelection"> </span> </li> </ul> <textarea if.bind="showDetailsField" placeholder.bind="config.detailsField.placeholder | i18n" maxlength.bind="config.detailsField.maxLength" value.bind="details" rows="4"></textarea> </ux-dialog-body> <ux-dialog-footer> <div class="buttons"> <button class="primary" disabled.bind="!canSubmit" click.delegate="submit()"> ${config.submitLabel | i18n} </button> <a class="secondary" href="#" if.bind="config.cancelLabel" click.delegate="controller.cancel()">${config.cancelLabel | i18n}</a> </div> </ux-dialog-footer> </ux-dialog> </template> '},"dialogs/selection-dialog.scss":(o,t,e)=>{e.r(t),e.d(t,{default:()=>b});var i=e(31601),a=e.n(i),s=e(76314),l=e.n(s),n=e(4417),d=e.n(n),r=new URL(e(83959),e.b),g=new URL(e(81206),e.b),c=l()(a()),u=d()(r),p=d()(g);c.push([o.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${u}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.selection-dialog{width:670px;border:0;padding:43px 48px}.selection-dialog ux-dialog-header .title{font-size:30px;line-height:36px;color:#fff;padding-bottom:11px}.selection-dialog ux-dialog-body{margin:0;padding:0}.selection-dialog ux-dialog-body .message{font-size:13px;line-height:20px;font-weight:600;color:rgba(255,255,255,.5);margin:0 0 27px}.selection-dialog ux-dialog-body .options{list-style:none;margin:0;padding:0}.selection-dialog ux-dialog-body .options .option{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;--checkbox--checked-color: var(--color--accent);display:flex}.selection-dialog ux-dialog-body .options .option,.selection-dialog ux-dialog-body .options .option *{cursor:pointer}.selection-dialog ux-dialog-body .options .option,.selection-dialog ux-dialog-body .options .option *{cursor:pointer}.selection-dialog ux-dialog-body .options .option>*:first-child{margin-right:9px}.selection-dialog ux-dialog-body .options .option+.option{margin-top:16px}.selection-dialog ux-dialog-body .options .option.disabled{opacity:.5;pointer-events:none}.selection-dialog ux-dialog-body .options .option.selected .checkbox:before{opacity:1}.selection-dialog ux-dialog-body .options .option.selected .label{color:var(--color--accent)}.selection-dialog ux-dialog-body .options .option.has-input{margin-top:11px}.selection-dialog ux-dialog-body .options .option:hover:not(.selected) .label{color:#fff}.selection-dialog ux-dialog-body .options .option .checkbox{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none}.selection-dialog ux-dialog-body .options .option .checkbox,.selection-dialog ux-dialog-body .options .option .checkbox *{cursor:pointer}.selection-dialog ux-dialog-body .options .option .checkbox:checked:before{opacity:1}.selection-dialog ux-dialog-body .options .option .checkbox:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${p});mask:url(${p})}.selection-dialog ux-dialog-body .options .option .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color);display:flex;align-items:center}.selection-dialog ux-dialog-body .options .option .label,.selection-dialog ux-dialog-body .options .option .label *{cursor:pointer}.selection-dialog ux-dialog-body .options .option .label input{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;cursor:text;margin-left:8px}.selection-dialog ux-dialog-body .options .option .label input::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.selection-dialog ux-dialog-body .options .option .label input::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.selection-dialog ux-dialog-body .options .option .label input:disabled{opacity:.5}.selection-dialog ux-dialog-body .options .option .label input:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.selection-dialog ux-dialog-body .options .option .label input:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.selection-dialog ux-dialog-body textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin:20px 0;height:auto}.selection-dialog ux-dialog-body textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.selection-dialog ux-dialog-body textarea::-webkit-scrollbar-thumb:window-inactive,.selection-dialog ux-dialog-body textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.selection-dialog ux-dialog-body textarea::-webkit-scrollbar-thumb:window-inactive:hover,.selection-dialog ux-dialog-body textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.selection-dialog ux-dialog-body textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.selection-dialog ux-dialog-body textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.selection-dialog ux-dialog-body textarea:disabled{opacity:.5}.selection-dialog ux-dialog-body textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.selection-dialog ux-dialog-body textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.selection-dialog ux-dialog-footer{margin:25px 0 0;text-align:left;padding:0;border-bottom-left-radius:10px;border-bottom-right-radius:10px}.selection-dialog ux-dialog-footer .buttons{display:flex;align-items:center}.selection-dialog ux-dialog-footer .buttons>*+*{margin-left:20px}.selection-dialog ux-dialog-footer .buttons button.primary{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent);margin:0}.selection-dialog ux-dialog-footer .buttons button.primary,.selection-dialog ux-dialog-footer .buttons button.primary *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .selection-dialog ux-dialog-footer .buttons button.primary{border:1px solid #fff}}.selection-dialog ux-dialog-footer .buttons button.primary>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.selection-dialog ux-dialog-footer .buttons button.primary>*:first-child{padding-left:0}.selection-dialog ux-dialog-footer .buttons button.primary>*:last-child{padding-right:0}.selection-dialog ux-dialog-footer .buttons button.primary svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .selection-dialog ux-dialog-footer .buttons button.primary svg *{fill:CanvasText}}.selection-dialog ux-dialog-footer .buttons button.primary svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .selection-dialog ux-dialog-footer .buttons button.primary svg{opacity:1}}.selection-dialog ux-dialog-footer .buttons button.primary img{height:50%}.selection-dialog ux-dialog-footer .buttons button.primary:disabled{opacity:.3}.selection-dialog ux-dialog-footer .buttons button.primary:disabled,.selection-dialog ux-dialog-footer .buttons button.primary:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.selection-dialog ux-dialog-footer .buttons button.primary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.selection-dialog ux-dialog-footer .buttons button.primary:not(:disabled):hover svg{opacity:1}}.selection-dialog ux-dialog-footer .buttons button.primary:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.selection-dialog ux-dialog-footer .buttons button.primary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}.selection-dialog ux-dialog-footer .buttons button.primary:not(:disabled):active{background-color:var(--color--accent)}.selection-dialog ux-dialog-footer .buttons button.primary:not(:disabled):active{--cta__icon--color: #000;color:#000}.selection-dialog ux-dialog-footer .buttons a.secondary{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}.selection-dialog ux-dialog-footer .buttons a.secondary,.selection-dialog ux-dialog-footer .buttons a.secondary *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .selection-dialog ux-dialog-footer .buttons a.secondary{border:1px solid #fff}}.selection-dialog ux-dialog-footer .buttons a.secondary>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.selection-dialog ux-dialog-footer .buttons a.secondary>*:first-child{padding-left:0}.selection-dialog ux-dialog-footer .buttons a.secondary>*:last-child{padding-right:0}.selection-dialog ux-dialog-footer .buttons a.secondary svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .selection-dialog ux-dialog-footer .buttons a.secondary svg *{fill:CanvasText}}.selection-dialog ux-dialog-footer .buttons a.secondary svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .selection-dialog ux-dialog-footer .buttons a.secondary svg{opacity:1}}.selection-dialog ux-dialog-footer .buttons a.secondary img{height:50%}.selection-dialog ux-dialog-footer .buttons a.secondary:disabled{opacity:.3}.selection-dialog ux-dialog-footer .buttons a.secondary:disabled,.selection-dialog ux-dialog-footer .buttons a.secondary:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.selection-dialog ux-dialog-footer .buttons a.secondary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.selection-dialog ux-dialog-footer .buttons a.secondary:not(:disabled):hover svg{opacity:1}}.selection-dialog ux-dialog-footer .buttons a.secondary:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.selection-dialog ux-dialog-footer .buttons a.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}`,""]);const b=c},"dialogs/suggest-mod-dialog":(o,t,e)=>{e.r(t),e.d(t,{SuggestModDialog:()=>b,SuggestModDialogService:()=>h});var i=e(15215),a=e(68663),s=e(62914),l=e("aurelia-dialog"),n=e("aurelia-event-aggregator"),d=e("aurelia-framework"),r=e(20770),g=e("cheats/mod-suggestions/mod-suggestion-picker"),c=e(41572),u=e(9374),p=e(48881);let b=class{#i;#a;#s;constructor(o,t,e,i){this.controller=o,this.apiClient=t,this.store=e,this.analytics=i,this.popularOptions=[],this.searchOptions=[],this.selectedModSuggestion=null,this.submitting=!1,this.showErrorMessage=!1,this.successfulSuggestionModName=null,this.#i=e,this.#a=t,this.#s=i}activate(o){if(this.isGameRecentlyUpdated=o.isGameRecentlyUpdated,this.account=o.account,this.gameId=o.gameId,this.popularOptions=o.gameSuggestedMods.suggested.filter((o=>null!==o.rank)).sort(((o,t)=>o.rank-t.rank)).map((o=>({label:o.suggestion,value:o}))),this.searchOptions=Object.entries(o.gameSuggestedMods.suggestable??{}).map((([o,t])=>({label:t,value:o}))),this.popularOptions.length<3){const o=[...this.searchOptions];for(;this.popularOptions.length<3&&o.length;){const[t]=o.splice(Math.floor(Math.random()*o.length),1);this.popularOptions.some((o=>!(0,g.isSuggestedModNameOption)(o)&&o.value.suggestion===t.value))||this.popularOptions.push(t)}}}acknowledgeNotUpdatedRecently(){this.isGameRecentlyUpdated=!0}async submit(){if(!this.isSubmitDisabled&&(this.submitting=!0,null!==this.selectedModSuggestion&&(0,c.a)(this.account)&&this.account.boosts>0))try{const{remainingBoosts:o,suggested:t}=await((0,g.isSuggestedModNameOption)(this.selectedModSuggestion)?this.submitNewModSuggestion(this.gameId,this.selectedModSuggestion.value):this.boostExistingModSuggestion(this.gameId,this.selectedModSuggestion.value));(0,p.JD)(this.#i,p.QM,o),this.successfulSuggestionModName=t.suggestion}catch(o){this.showErrorMessage=!0}finally{this.submitting=!1}}boostExistingModSuggestion(o,t){return this.#s.event("suggest_mod_dialog_suggest_existing",{gameId:o,suggestionId:String(t.id),suggestion:t.suggestion}),this.#a.boostModSuggestion(o,t.id)}submitNewModSuggestion(o,t){return this.#s.event("suggest_mod_dialog_suggest_new",{gameId:o,suggestion:t}),this.#a.createModSuggestion(this.gameId,t)}continue(){this.controller.ok()}handleCloseClick(){this.controller.cancel()}get isSubmitDisabled(){return this.submitting||!this.selectedModSuggestion||this.account.boosts<=0}};(0,i.Cg)([(0,u._)((o=>[o.submitting,o.selectedModSuggestion,o.account.boosts])),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],b.prototype,"isSubmitDisabled",null),b=(0,i.Cg)([(0,d.autoinject)(),(0,i.Sn)("design:paramtypes",[l.DialogController,a.x,r.il,s.j0])],b);let h=class{#l;#n;#s;constructor(o,t,e){this.#n=o,this.#l=t,this.#s=e}async open(o){const t="dialogs/suggest-mod-dialog";this.#l.publish("dialog:open:start",t),this.#s.event("suggest_mod_dialog_open",{gameId:o.gameId,isGameRecentlyUpdated:o.isGameRecentlyUpdated});const e=this.#n.open({viewModel:t,model:o,host:document.body,position:(o,t)=>{o.classList.add("suggest-mod-dialog-ux-dialog-container")}});return e.then((()=>this.#l.publish("dialog:open:complete",t))),await e.whenClosed()}};h=(0,i.Cg)([(0,d.autoinject)(),(0,i.Sn)("design:paramtypes",[l.DialogService,n.EventAggregator,s.j0])],h)},"dialogs/suggest-mod-dialog.html":(o,t,e)=>{e.r(t),e.d(t,{default:()=>i});const i='<template> <require from="./suggest-mod-dialog.scss"></require> <require from="shared/resources/elements/close-button"></require> <require from="cheats/mod-suggestions/suggest-mod-dialog-title"></require> <require from="cheats/mod-suggestions/mod-suggestion-picker"></require> <ux-dialog class="suggest-mod-dialog"> <close-button click.trigger="handleCloseClick()" tabindex="0"></close-button> <suggest-mod-dialog-title></suggest-mod-dialog-title> <div class="suggest-mod-dialog-content"> <template if.bind="!isGameRecentlyUpdated"> <p innerhtml.bind="\'suggest_mod.not_updated_recently\' | i18n | markdown"></p> <wm-button class="suggest-mod-dialog-action-button" click.delegate="acknowledgeNotUpdatedRecently()">${\'suggest_mod.not_updated_recently_ack\' | i18n}</wm-button> </template> <template else> <template if.bind="successfulSuggestionModName"> <p innerhtml.bind="\'suggest_mod.successfully_boosted_$suggestion\' | i18n : { suggestion: successfulSuggestionModName } | markdown"></p> <wm-button class="suggest-mod-dialog-action-button" click.delegate="continue()">${\'suggest_mod.continue\' | i18n}</wm-button> </template> <template else> <p>${\'suggest_mod.description\' | i18n}</p> <mod-suggestion-picker suggested-options.bind="popularOptions" search-options.bind="searchOptions" selected-option.two-way="selectedModSuggestion"></mod-suggestion-picker> <template if.bind="showErrorMessage"> <p class="suggest-mod-dialog-error-message">${\'suggest_mod.error_message\' | i18n}</p> </template> <wm-button class="suggest-mod-dialog-action-button" click.delegate="submit()" disabled.bind="isSubmitDisabled"> ${(account.boosts == 0 ? \'suggest_mod.no_boosts_available\' : \'suggest_mod.boost\') | i18n} </wm-button> </template> </template> </div> </ux-dialog> </template> '},"dialogs/suggest-mod-dialog.scss":(o,t,e)=>{e.r(t),e.d(t,{default:()=>n});var i=e(31601),a=e.n(i),s=e(76314),l=e.n(s)()(a());l.push([o.id,'.suggest-mod-dialog{background:var(--theme--background) !important;color:var(--theme--text-primary);width:448px;padding:0 !important}.suggest-mod-dialog suggest-mod-dialog-title{padding:12px 16px}.suggest-mod-dialog-content{padding:12px 16px 24px;display:flex;flex-direction:column;gap:16px}.suggest-mod-dialog-action-button{margin:auto}.suggest-mod-dialog-error-message{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-highlight)}.suggest-mod-dialog-ux-dialog-container{position:fixed;top:50px}.suggest-mod-dialog-ux-dialog-container>div{align-items:baseline}.suggest-mod-dialog-ux-dialog-container>div>div{margin:0 auto}',""]);const n=l}}]);
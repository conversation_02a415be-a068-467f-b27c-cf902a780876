"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4971],{"dialogs/teleport-education-dialog":(e,t,i)=>{i.r(t),i.d(t,{TeleportEducationDialog:()=>m,TeleportEducationDialogService:()=>h});var a=i(15215),o=i("aurelia-dialog"),r=i("aurelia-framework"),n=i(62914),l=i(39835),s=i(24008),d=i(17275),g=i(54995),p=i(70236),c=i(30770);let m=class{#e;#t;#i;#a;#o;constructor(e,t,i,a){this.controller=e,this.#i=t,this.#a=i,this.#o=a}activate(e){this.#e=e.titleId}attached(){const e=this.maps.filter((e=>e.titleId===this.#e&&(0,p.Lt)(e.flags??0,s.nC.HasGameCoordinates)));e||this.controller.cancel(),this.#t=e,this.#i.event("teleport_education_dialog_open",{},n.Io)}openCheckout(){this.#i.event("teleport_education_dialog_pro_cta_click",{},n.Io),this.controller.close(!0),this.#a.open({trigger:"teleport_education_dialog",frequency:"yearly",nonInteraction:!1})}openGameMap(){this.#i.event("teleport_education_dialog_open_map_cta_click",{},n.Io),this.controller.close(!0),this.#o.openMap(this.#e.toString(),this.#t[0].id)}};m=(0,a.Cg)([(0,g.m6)({selectors:{maps:(0,g.$t)((e=>e.catalog?.maps)),subscription:(0,g.$t)((e=>e.account?.subscription))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[o.DialogController,n.j0,c.f,l.I])],m);let h=class extends d.C{constructor(){super(...arguments),this.viewModelClass="dialogs/teleport-education-dialog"}};h=(0,a.Cg)([(0,r.autoinject)()],h)},"dialogs/teleport-education-dialog.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var a=i(14385),o=i.n(a),r=new URL(i(55119),i.b);const n='<template> <require from="./teleport-education-dialog.scss"></require> <require from="../resources/elements/pro-cta-label"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="teleport-education-dialog"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <div class="layout"> <h1 if.bind="subscription" innerhtml.bind="\'teleport_education_dialog.teleport_with_interactive_maps\' | i18n | markdown"></h1> <h1 else innerhtml.bind="\'teleport_education_dialog.upgrade_to_pro_and_try_teleport_maps\' | i18n | markdown"></h1> <p innerhtml.bind="\'teleport_education_dialog.instantly_go_where_you_want\' | i18n | markdown"></p> <img class="teleport-graphic" src="'+o()(r)+'"> <div class="buttons"> <button if.bind="subscription" click.delegate="openGameMap()" class="primary"> ${\'teleport_education_dialog.open_maps\' | i18n} </button> <button else click.delegate="openCheckout()" class="primary"> <pro-cta-label></pro-cta-label> </button> <button class="secondary" click.delegate="controller.cancel()"> ${\'teleport_education_dialog.ill_try_later\' | i18n} </button> </div> </div> </ux-dialog-body> </ux-dialog> </template> '},"dialogs/teleport-education-dialog.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>l});var a=i(31601),o=i.n(a),r=i(76314),n=i.n(r)()(o());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.teleport-education-dialog{border:0;background:#000;background:linear-gradient(90deg, rgb(6, 68, 83) 0%, rgb(0, 0, 0) 20%, rgb(0, 0, 0) 50%, rgb(0, 0, 0) 80%, rgb(6, 68, 83) 100%) !important;width:800px;height:550px;padding:43px 48px;position:relative}.teleport-education-dialog .teleport-graphic{margin:0 auto}.teleport-education-dialog h1{font-size:30px;line-height:36px;color:#fff;text-align:center;margin:0 0 11px}.teleport-education-dialog h1 strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;vertical-align:middle}@media(forced-colors: active){body:not(.override-contrast-mode) .teleport-education-dialog h1 strong{border:1px solid #fff}}.teleport-education-dialog h1 em{font-style:normal;color:var(--color--accent)}.teleport-education-dialog p{font-size:18px;line-height:30px;text-align:center;color:rgba(255,255,255,.6)}.teleport-education-dialog p em{font-style:normal;color:#fff}.teleport-education-dialog .buttons{position:absolute;left:0;bottom:50px;width:100%;display:inline-flex;align-items:center;justify-content:center}.teleport-education-dialog .buttons button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;flex:0 0 auto}.teleport-education-dialog .buttons button,.teleport-education-dialog .buttons button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .teleport-education-dialog .buttons button{border:1px solid #fff}}.teleport-education-dialog .buttons button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.teleport-education-dialog .buttons button>*:first-child{padding-left:0}.teleport-education-dialog .buttons button>*:last-child{padding-right:0}.teleport-education-dialog .buttons button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .teleport-education-dialog .buttons button svg *{fill:CanvasText}}.teleport-education-dialog .buttons button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .teleport-education-dialog .buttons button svg{opacity:1}}.teleport-education-dialog .buttons button img{height:50%}.teleport-education-dialog .buttons button:disabled{opacity:.3}.teleport-education-dialog .buttons button:disabled,.teleport-education-dialog .buttons button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.teleport-education-dialog .buttons button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.teleport-education-dialog .buttons button:not(:disabled):hover svg{opacity:1}}.teleport-education-dialog .buttons button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.teleport-education-dialog .buttons button.primary{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.teleport-education-dialog .buttons button.primary:hover{filter:brightness(1.1)}}.teleport-education-dialog .buttons button+button{margin-left:10px}",""]);const l=n},"dialogs/time-limit-pre-game-dialog":(e,t,i)=>{i.r(t),i.d(t,{TimeLimitPreGameDialog:()=>g,TimeLimitPreGameDialogService:()=>p});var a=i(15215),o=i("aurelia-dialog"),r=i("aurelia-framework"),n=i(62914),l=i(17275),s=i(54995),d=i(30770);let g=class{#a;#i;constructor(e,t,i){this.controller=e,this.#a=t,this.#i=i}activate(e){this.limitHours=e.limitHours||1,this.#i.event("time_limit_pre_game_dialog_show",{limitHours:this.limitHours},n.Io)}openCheckout(){this.#a.open({trigger:"time_limit_pre_game_dialog",frequency:"yearly",nonInteraction:!1}),this.controller.close(!0)}subscriptionChanged(){this.subscription&&this.controller.cancel()}secondsPlayedTodayChanged(){0===this.secondsPlayedToday&&this.controller.cancel()}};g=(0,a.Cg)([(0,r.autoinject)(),(0,s.m6)({selectors:{subscription:(0,s.$t)((e=>e.account?.subscription)),secondsPlayedToday:(0,s.$t)((e=>e.counters?.secondsPlayedToday))}}),(0,a.Sn)("design:paramtypes",[o.DialogController,d.f,n.j0])],g);let p=class extends l.C{constructor(){super(...arguments),this.viewModelClass="dialogs/time-limit-pre-game-dialog"}};p=(0,a.Cg)([(0,r.autoinject)()],p)},"dialogs/time-limit-pre-game-dialog.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./time-limit-pre-game-dialog.scss"></require> <require from="./resources/elements/time-limit-graphic.html"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/resources/elements/pro-badge"></require> <require from="../resources/elements/pro-cta-label"></require> <ux-dialog class="time-limit-pre-game-dialog"> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <h1 innerhtml.bind="\'time_limit_pre_game_dialog.wemod_is_free_for_$x_hours_each_day\' | i18n:{x: limitHours} | markdown"></h1> <p innerhtml.bind="\'time_limit_pre_game_dialog.upgrade_to_pro_for_unlimited_modding\' | i18n | markdown"></p> <div class="graphic"> <time-limit-graphic></time-limit-graphic> </div> <div class="buttons"> <button class="primary" click.delegate="openCheckout()"><pro-cta-label></pro-cta-label></button> <button click.delegate="controller.cancel()">${\'time_limit_pre_game_dialog.skip_for_now\' | i18n}</button> </div> </ux-dialog> </template> '},"dialogs/time-limit-pre-game-dialog.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>l});var a=i(31601),o=i.n(a),r=i(76314),n=i.n(r)()(o());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.time-limit-pre-game-dialog{background:linear-gradient(0deg, var(--theme--secondary-background) 0%, #000 100%) !important;padding:43px 48px 50px;text-align:center;width:750px !important;padding:43px 48px 60px}.time-limit-pre-game-dialog h1{font-size:30px;line-height:36px;color:#fff;margin:0 0 23px}.time-limit-pre-game-dialog h1 em{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;font-style:normal;vertical-align:middle;margin-top:-6px}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-pre-game-dialog h1 em{border:1px solid #fff}}.time-limit-pre-game-dialog h1 strong{color:var(--color--accent)}.time-limit-pre-game-dialog .limit-message{font-size:12px;line-height:18px;background:rgba(var(--theme--highlight--rgb), 0.2);border-radius:2px;padding:2px 6px;color:#fff;margin-bottom:30px;display:inline-block}.time-limit-pre-game-dialog .limit-message em{font-style:none;color:var(--theme--highlight)}.time-limit-pre-game-dialog .limit-message.accent{background:rgba(var(--color--accent--rgb), 0.2)}.time-limit-pre-game-dialog .limit-message.accent em{color:var(--color--accent)}.time-limit-pre-game-dialog time-limit-reached-alternate-graphic{margin-bottom:27px}.time-limit-pre-game-dialog p{font-size:16px;line-height:24px;text-align:center;color:rgba(255,255,255,.5);margin:0 0 35px;padding:0;display:inline-block;max-width:410px}.time-limit-pre-game-dialog p em{font-style:normal;color:rgba(255,255,255,.8)}.time-limit-pre-game-dialog p strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;font-style:normal;vertical-align:middle;margin-top:-2px}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-pre-game-dialog p strong{border:1px solid #fff}}.time-limit-pre-game-dialog button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;display:block;margin:0 auto}.time-limit-pre-game-dialog button,.time-limit-pre-game-dialog button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-pre-game-dialog button{border:1px solid #fff}}.time-limit-pre-game-dialog button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.time-limit-pre-game-dialog button>*:first-child{padding-left:0}.time-limit-pre-game-dialog button>*:last-child{padding-right:0}.time-limit-pre-game-dialog button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-pre-game-dialog button svg *{fill:CanvasText}}.time-limit-pre-game-dialog button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-pre-game-dialog button svg{opacity:1}}.time-limit-pre-game-dialog button img{height:50%}.time-limit-pre-game-dialog button:disabled{opacity:.3}.time-limit-pre-game-dialog button:disabled,.time-limit-pre-game-dialog button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.time-limit-pre-game-dialog button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.time-limit-pre-game-dialog button:not(:disabled):hover svg{opacity:1}}.time-limit-pre-game-dialog button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.time-limit-pre-game-dialog button pro-cta-label{display:inline}.time-limit-pre-game-dialog button.primary{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.time-limit-pre-game-dialog button.primary:hover{filter:brightness(1.1)}}.time-limit-pre-game-dialog button.secondary{background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}@media(hover: hover){.time-limit-pre-game-dialog button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}.time-limit-pre-game-dialog button+button{margin-top:15px}.time-limit-pre-game-dialog h1{margin-bottom:11px}.time-limit-pre-game-dialog p{display:block;max-width:100%;margin-bottom:32px}.time-limit-pre-game-dialog .buttons{display:inline-flex;align-items:center}.time-limit-pre-game-dialog .buttons button+button{margin-left:10px;margin-top:0}.time-limit-pre-game-dialog .graphic{margin:0 auto -10px}.time-limit-pre-game-dialog .graphic svg{margin-left:40px}",""]);const l=n},"dialogs/time-limit-reached-post-game-dialog":(e,t,i)=>{i.r(t),i.d(t,{TimeLimitReachedPostGameDialog:()=>b,TimeLimitReachedPostGameDialogService:()=>u});var a=i(15215),o=i("aurelia-dialog"),r=i("aurelia-framework"),n=i(81512),l=i(62914),s=i(40930),d=i(60692),g=i(68539),p=i(54995),c=i(14046),m=i(71341),h=i(23218);let b=class{#r;#i;#n;constructor(e,t,i,a){this.controller=e,this.#r=t,this.#i=i,this.#n=a}activate(e){this.perGame=e.perGame,this.limitHours=e.limitHours||1,this.#i.event("time_limit_reached_post_game_dialog_show",{type:this.perGame?"per_game_daily":"daily",limitHours:this.limitHours},l.Io),this.showForegroundTimeLimit=3===this.#n.assignments.get(d.n.E39)}openCheckout(){this.#r.open({trigger:`time_limit_reached_${this.perGame?"per_game_":""}post_game_dialog`,frequency:"yearly"}),this.controller.close(!0)}subscriptionChanged(){this.subscription&&this.controller.cancel()}secondsPlayedTodayChanged(){0===this.secondsPlayedToday&&this.controller.cancel()}get resetTime(){const e=(0,n.A)().setHours(s.rf,s.px),t=new Date,i=new Date(e),a=(0,c.Ov)(i,t),o=new Date(1e3*a);return{hours:o.getUTCHours(),minutes:o.getUTCMinutes()}}};(0,a.Cg)([(0,r.computedFrom)("lastResetDailyPlayLimit"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],b.prototype,"resetTime",null),b=(0,a.Cg)([(0,r.autoinject)(),(0,p.m6)({selectors:{subscription:(0,p.$t)((e=>e.account?.subscription)),secondsPlayedToday:(0,p.$t)((e=>e.counters?.secondsPlayedToday)),lastResetDailyPlayLimit:(0,p.$t)((e=>e.timestamps?.lastResetDailyPlayLimit))}}),(0,a.Sn)("design:paramtypes",[o.DialogController,m.U,l.j0,g.z])],b);let u=class extends h.D{constructor(){super(...arguments),this.viewModelClass="dialogs/time-limit-reached-post-game-dialog"}};u=(0,a.Cg)([(0,r.autoinject)()],u)},"dialogs/time-limit-reached-post-game-dialog.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>d});var a=i(14385),o=i.n(a),r=new URL(i(71662),i.b),n=new URL(i(76581),i.b),l=new URL(i(96539),i.b),s=new URL(i(1196),i.b);const d='<template> <require from="./time-limit-reached-post-game-dialog.scss"></require> <require from="./resources/elements/time-limit-graphic.html"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../resources/elements/pro-cta-label"></require> <ux-dialog class="time-limit-reached-post-game-dialog fullscreen-dialog"> <close-button click.delegate="controller.cancel()" tabindex="0"></close-button> <div class="scroll-wrapper"> <div class="scroll-inner"> <div class="wrapper"> <h1 innerhtml.bind="`time_limit_reached_post_game_dialog.daily_time_limit${perGame ? \'_per_game\' : \'\'}_exceeded` | i18n | markdown"></h1> <p> </p><div innerhtml.bind="`time_limit_reached_post_game_dialog.free_can_use_wemod_for_${limitHours}_hour${limitHours > 1 ? \'s\' : \'\'}${perGame ? \'_per_game\' : \'\'}_each_day` | i18n | markdown"></div> <div if.bind="showForegroundTimeLimit"> <span>${\'time_limit_reached_post_game_dialog.time_limit_resets_in\' | i18n}</span> <template if.bind="resetTime.hours > 0"> <strong if.bind="resetTime.hours === 1" innerhtml.bind="\'time_limit_reached_post_game_dialog.1_hour\' | i18n | markdown"></strong> <strong else innerhtml.bind="\'time_limit_reached_post_game_dialog.$hours_hours\' | i18n:{hours: resetTime.hours} | markdown"></strong> </template> <template if.bind="resetTime.minutes > 0"> <strong if.bind="resetTime.minutes === 1" innerhtml.bind="\'time_limit_reached_post_game_dialog.1_minute\' | i18n | markdown"></strong> <strong else innerhtml.bind="\'time_limit_reached_post_game_dialog.$minutes_minutes\' | i18n:{minutes: resetTime.minutes} | markdown"></strong> </template> </div> <p></p> <span class="graphic ${showForegroundTimeLimit ? \'alternate\' : \'\'}"> <time-limit-graphic if.bind="showForegroundTimeLimit"></time-limit-graphic> <template else> <img if.bind="limitHours === 1" src="'+o()(r)+'"> <img if.bind="limitHours === 2" src="'+o()(n)+'"> <img if.bind="limitHours === 3" src="'+o()(l)+'"> <img if.bind="limitHours === 4" src="'+o()(s)+'"> <span class="free-badge">${\'time_limit_reached_post_game_dialog.free\' | i18n}</span> <span class="pro-badge">${\'time_limit_reached_post_game_dialog.pro\' | i18n}</span> </template> </span> <div class="buttons"> <button click.delegate="openCheckout()" class="primary"><pro-cta-label></pro-cta-label></button> <button click.delegate="controller.cancel()" class="secondary">${`time_limit_reached_post_game_dialog.${perGame ? \'play_another_game_today\' : \'ill_wait_until_tomorrow\'}` | i18n}</button> </div> </div> </div> </div> </ux-dialog> </template>'},"dialogs/time-limit-reached-post-game-dialog.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>l});var a=i(31601),o=i.n(a),r=i(76314),n=i.n(r)()(o());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.time-limit-reached-post-game-dialog .scroll-wrapper{width:100vw;height:100vh;overflow-x:hidden;overflow-y:auto;background:linear-gradient(0deg, var(--theme--secondary-background) 0%, #000 100%)}.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.time-limit-reached-post-game-dialog .scroll-inner{display:flex;flex-direction:column;min-height:100vh}.time-limit-reached-post-game-dialog .wrapper{padding:50px;display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:100vh}.time-limit-reached-post-game-dialog .wrapper h1{font-weight:700;text-align:center;font-size:52px;line-height:55px;color:#fff;margin-bottom:25px}.time-limit-reached-post-game-dialog .wrapper h1 em{font-style:normal;color:var(--theme--highlight)}.time-limit-reached-post-game-dialog .wrapper h1 s{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;text-decoration:none;line-height:42px;font-size:30px;letter-spacing:.9px;padding:0 9px 0 11px;border-radius:7.5px;vertical-align:middle;margin-top:-10px}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-post-game-dialog .wrapper h1 s{border:1px solid #fff}}.time-limit-reached-post-game-dialog .wrapper h1 strong{color:var(--color--accent)}.time-limit-reached-post-game-dialog .wrapper p{font-size:12px;line-height:18px;color:rgba(255,255,255,.6);margin:0 0 34px;padding:0}.time-limit-reached-post-game-dialog .wrapper p em{font-style:none;color:#fff}.time-limit-reached-post-game-dialog .wrapper button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px}.time-limit-reached-post-game-dialog .wrapper button,.time-limit-reached-post-game-dialog .wrapper button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-post-game-dialog .wrapper button{border:1px solid #fff}}.time-limit-reached-post-game-dialog .wrapper button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.time-limit-reached-post-game-dialog .wrapper button>*:first-child{padding-left:0}.time-limit-reached-post-game-dialog .wrapper button>*:last-child{padding-right:0}.time-limit-reached-post-game-dialog .wrapper button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-post-game-dialog .wrapper button svg *{fill:CanvasText}}.time-limit-reached-post-game-dialog .wrapper button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-post-game-dialog .wrapper button svg{opacity:1}}.time-limit-reached-post-game-dialog .wrapper button img{height:50%}.time-limit-reached-post-game-dialog .wrapper button:disabled{opacity:.3}.time-limit-reached-post-game-dialog .wrapper button:disabled,.time-limit-reached-post-game-dialog .wrapper button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.time-limit-reached-post-game-dialog .wrapper button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.time-limit-reached-post-game-dialog .wrapper button:not(:disabled):hover svg{opacity:1}}.time-limit-reached-post-game-dialog .wrapper button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.time-limit-reached-post-game-dialog .wrapper button.primary{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important;min-width:307px}@media(hover: hover){.time-limit-reached-post-game-dialog .wrapper button.primary:hover{filter:brightness(1.1)}}.time-limit-reached-post-game-dialog .wrapper .buttons{display:inline-flex;align-items:center;margin-top:20px}.time-limit-reached-post-game-dialog .wrapper .buttons button+button{margin-left:10px;margin-top:0}.time-limit-reached-post-game-dialog .wrapper .graphic{position:relative;width:480px;height:200px;margin-bottom:30px;pointer-events:none}.time-limit-reached-post-game-dialog .wrapper .graphic.alternate{height:300px}.time-limit-reached-post-game-dialog .wrapper .graphic img{position:absolute;left:0;top:-50px}.time-limit-reached-post-game-dialog .wrapper .graphic .free-badge{font-weight:700;font-size:16.1616px;line-height:27px;color:var(--theme--highlight);opacity:.6;position:absolute;left:100px;bottom:43px;transform:translateX(-50%)}.time-limit-reached-post-game-dialog .wrapper .graphic .pro-badge{font-weight:700;color:var(--color--accent);font-size:14px;line-height:26px;letter-spacing:.6px;text-transform:uppercase;background:rgba(var(--theme--secondary-background), 0.25);border:1px solid var(--color--accent);padding:0 5.5px;border-radius:4px;position:absolute;right:100px;bottom:43px;transform:translateX(50%)}",""]);const l=n},"dialogs/time-limit-reached-pre-game-dialog":(e,t,i)=>{i.r(t),i.d(t,{TimeLimitReachedPreGameDialog:()=>p,TimeLimitReachedPreGameDialogService:()=>c});var a=i(15215),o=i("aurelia-dialog"),r=i("aurelia-framework"),n=i(62914),l=i(60692),s=i(68539),d=i(17275),g=i(71341);let p=class{#r;#i;#n;constructor(e,t,i,a){this.controller=e,this.isInFreeMobileExperiment=!1,this.#r=t,this.#i=i,this.#n=a}attached(){this.isInFreeMobileExperiment=!!this.#n.assignments.get(l.n.MakeMobileAppFree)}activate(e){this.perGame=e.perGame,this.limitHours=e.limitHours||1,this.#i.event("time_limit_reached_pre_game_dialog_show",{type:this.perGame?"per_game_daily":"daily",limitHours:this.limitHours},n.Io)}openCheckout(){this.#r.open({trigger:`time_limit_reached_${this.perGame?"per_game_":""}pre_game_dialog`,frequency:"yearly"}),this.controller.close(!0)}};p=(0,a.Cg)([(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[o.DialogController,g.U,n.j0,s.z])],p);let c=class extends d.C{constructor(){super(...arguments),this.viewModelClass="dialogs/time-limit-reached-pre-game-dialog"}};c=(0,a.Cg)([(0,r.autoinject)()],c)},"dialogs/time-limit-reached-pre-game-dialog.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./time-limit-reached-pre-game-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../resources/elements/pro-cta-label"></require> <require from="./resources/elements/time-limit-reached-alternate-graphic"></require> <ux-dialog class="time-limit-reached-pre-game-dialog ${isInFreeMobileExperiment ? \'mobile-free\' : \'\'}"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <h1 innerhtml.bind="\'time_limit_reached_pre_game_dialog.upgrade_to_pro_to_keep_playing\' | i18n | markdown"></h1> </ux-dialog-header> <ux-dialog-body> <span class="limit-message" innerhtml.bind="`time_limit_reached_pre_game_dialog.you_have_exceeded_your_${limitHours}_hour_limit${perGame ? \'_for_this_game\' : \'\'}_for_today` | i18n | markdown"></span> <time-limit-reached-alternate-graphic is-in-free-mobile-experiment.bind="isInFreeMobileExperiment"></time-limit-reached-alternate-graphic> <p if.bind="!isInFreeMobileExperiment" innerhtml.bind="\'time_limit_reached_pre_game_dialog.pro_users_have_unlimited_mod_access\' | i18n | markdown"></p> <p else innerhtml.bind="\'time_limit_reached_pre_game_dialog.pro_users_have_unlimited_mod_access_mobile_free\' | i18n | markdown"></p> <button class="primary" click.delegate="openCheckout()"> <pro-cta-label></pro-cta-label> </button> <button class="secondary" click.delegate="controller.cancel()"> <template if.bind="perGame">${\'time_limit_reached_pre_game_dialog.ill_play_another_game\' | i18n}</template> <template else>${\'time_limit_reached_pre_game_dialog.ill_play_tomorrow\' | i18n}</template> </button> </ux-dialog-body> </ux-dialog> </template> '},"dialogs/time-limit-reached-pre-game-dialog.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>l});var a=i(31601),o=i.n(a),r=i(76314),n=i.n(r)()(o());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.time-limit-reached-pre-game-dialog{background:linear-gradient(0deg, var(--theme--secondary-background) 0%, #000 100%) !important;padding:43px 48px 50px;text-align:center;width:750px !important}.time-limit-reached-pre-game-dialog h1{font-size:30px;line-height:36px;color:#fff;margin:0 0 23px}.time-limit-reached-pre-game-dialog h1 em{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;font-style:normal;vertical-align:middle;margin-top:-6px}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-pre-game-dialog h1 em{border:1px solid #fff}}.time-limit-reached-pre-game-dialog h1 strong{color:var(--color--accent)}.time-limit-reached-pre-game-dialog .limit-message{font-size:12px;line-height:18px;background:rgba(var(--theme--highlight--rgb), 0.2);border-radius:2px;padding:2px 6px;color:#fff;margin-bottom:30px;display:inline-block}.time-limit-reached-pre-game-dialog .limit-message em{font-style:none;color:var(--theme--highlight)}.time-limit-reached-pre-game-dialog .limit-message.accent{background:rgba(var(--color--accent--rgb), 0.2)}.time-limit-reached-pre-game-dialog .limit-message.accent em{color:var(--color--accent)}.time-limit-reached-pre-game-dialog time-limit-reached-alternate-graphic{margin-bottom:27px}.time-limit-reached-pre-game-dialog p{font-size:16px;line-height:24px;text-align:center;color:rgba(255,255,255,.5);margin:0 0 35px;padding:0;display:inline-block;max-width:410px}.time-limit-reached-pre-game-dialog p em{font-style:normal;color:rgba(255,255,255,.8)}.time-limit-reached-pre-game-dialog p strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;font-style:normal;vertical-align:middle;margin-top:-2px}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-pre-game-dialog p strong{border:1px solid #fff}}.time-limit-reached-pre-game-dialog button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;display:block;margin:0 auto}.time-limit-reached-pre-game-dialog button,.time-limit-reached-pre-game-dialog button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-pre-game-dialog button{border:1px solid #fff}}.time-limit-reached-pre-game-dialog button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.time-limit-reached-pre-game-dialog button>*:first-child{padding-left:0}.time-limit-reached-pre-game-dialog button>*:last-child{padding-right:0}.time-limit-reached-pre-game-dialog button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-pre-game-dialog button svg *{fill:CanvasText}}.time-limit-reached-pre-game-dialog button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-pre-game-dialog button svg{opacity:1}}.time-limit-reached-pre-game-dialog button img{height:50%}.time-limit-reached-pre-game-dialog button:disabled{opacity:.3}.time-limit-reached-pre-game-dialog button:disabled,.time-limit-reached-pre-game-dialog button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.time-limit-reached-pre-game-dialog button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.time-limit-reached-pre-game-dialog button:not(:disabled):hover svg{opacity:1}}.time-limit-reached-pre-game-dialog button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.time-limit-reached-pre-game-dialog button pro-cta-label{display:inline}.time-limit-reached-pre-game-dialog button.primary{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.time-limit-reached-pre-game-dialog button.primary:hover{filter:brightness(1.1)}}.time-limit-reached-pre-game-dialog button.secondary{background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}@media(hover: hover){.time-limit-reached-pre-game-dialog button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}.time-limit-reached-pre-game-dialog button+button{margin-top:15px}.time-limit-reached-pre-game-dialog.mobile-free{padding-left:16px;padding-right:16px}.time-limit-reached-pre-game-dialog button.primary{min-width:300px}",""]);const l=n},"dialogs/time-remaining-post-game-dialog":(e,t,i)=>{i.r(t),i.d(t,{TimeRemainingPostGameDialog:()=>p,TimeRemainingPostGameDialogService:()=>c});var a=i(15215),o=i("aurelia-dialog"),r=i("aurelia-framework"),n=i(62914),l=i(96111),s=i(17275),d=i(54995),g=i(30770);let p=class{#a;#i;#l;constructor(e,t,i,a){this.controller=e,this.#a=t,this.#i=i,this.#l=a}activate(){this.#i.event("time_remaining_post_game_dialog_show",{},n.Io)}openCheckout(){this.#a.open({trigger:"time_remaining_post_game_dialog",frequency:"yearly",nonInteraction:!1}),this.controller.close(!0)}subscriptionChanged(){this.subscription&&this.controller.cancel()}secondsPlayedTodayChanged(){0===this.secondsPlayedToday&&this.controller.cancel()}get timeRemaining(){const e=this.#l.dailyPlayLimitSeconds-(this.secondsPlayedToday||0),t=Math.max(Math.ceil(e/60),0),i=new Date(60*t*1e3);return{hours:i.getUTCHours(),minutes:i.getUTCMinutes()}}};(0,a.Cg)([(0,r.computedFrom)("secondsPlayedToday"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],p.prototype,"timeRemaining",null),p=(0,a.Cg)([(0,r.autoinject)(),(0,d.m6)({selectors:{subscription:(0,d.$t)((e=>e.account?.subscription)),secondsPlayedToday:(0,d.$t)((e=>e.counters?.secondsPlayedToday))}}),(0,a.Sn)("design:paramtypes",[o.DialogController,g.f,n.j0,l.Y])],p);let c=class extends s.C{constructor(){super(...arguments),this.viewModelClass="dialogs/time-remaining-post-game-dialog"}};c=(0,a.Cg)([(0,r.autoinject)()],c)},"dialogs/time-remaining-post-game-dialog.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./time-remaining-post-game-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../resources/elements/pro-cta-label"></require> <ux-dialog class="time-remaining-post-game-dialog"> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <h1> ${\'time_remaining_post_game_dialog.time_remaining\' | i18n} <template if.bind="timeRemaining.hours > 0"> <span if.bind="timeRemaining.hours === 1" innerhtml.bind="\'time_remaining_post_game_dialog.1_hour\' | i18n | markdown"></span> <span else innerhtml.bind="\'time_remaining_post_game_dialog.$hours_hours\' | i18n:{hours: timeRemaining.hours} | markdown"></span> </template> <template if.bind="timeRemaining.minutes > 0"> <span if.bind="timeRemaining.minutes === 1" innerhtml.bind="\'time_remaining_post_game_dialog.1_minute\' | i18n | markdown"></span> <span else innerhtml.bind="\'time_remaining_post_game_dialog.$minutes_minutes\' | i18n:{minutes: timeRemaining.minutes} | markdown"></span> </template> </h1> <p innerhtml.bind="\'time_remaining_post_game_dialog.upgrade_to_pro_for_unlimited_modding\' | i18n | markdown"></p> <button click.delegate="openCheckout()"><pro-cta-label></pro-cta-label></button> </ux-dialog> </template> '},"dialogs/time-remaining-post-game-dialog.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>l});var a=i(31601),o=i.n(a),r=i(76314),n=i.n(r)()(o());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.time-remaining-post-game-dialog{background:linear-gradient(0deg, var(--theme--secondary-background) 0%, #000 100%) !important;padding:43px 48px 50px;text-align:center;width:500px !important;padding:43px 15px 50px}.time-remaining-post-game-dialog h1{font-size:30px;line-height:36px;color:#fff;margin:0 0 23px}.time-remaining-post-game-dialog h1 em{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;font-style:normal;vertical-align:middle;margin-top:-6px}@media(forced-colors: active){body:not(.override-contrast-mode) .time-remaining-post-game-dialog h1 em{border:1px solid #fff}}.time-remaining-post-game-dialog h1 strong{color:var(--color--accent)}.time-remaining-post-game-dialog .limit-message{font-size:12px;line-height:18px;background:rgba(var(--theme--highlight--rgb), 0.2);border-radius:2px;padding:2px 6px;color:#fff;margin-bottom:30px;display:inline-block}.time-remaining-post-game-dialog .limit-message em{font-style:none;color:var(--theme--highlight)}.time-remaining-post-game-dialog .limit-message.accent{background:rgba(var(--color--accent--rgb), 0.2)}.time-remaining-post-game-dialog .limit-message.accent em{color:var(--color--accent)}.time-remaining-post-game-dialog time-limit-reached-alternate-graphic{margin-bottom:27px}.time-remaining-post-game-dialog p{font-size:16px;line-height:24px;text-align:center;color:rgba(255,255,255,.5);margin:0 0 35px;padding:0;display:inline-block;max-width:410px}.time-remaining-post-game-dialog p em{font-style:normal;color:rgba(255,255,255,.8)}.time-remaining-post-game-dialog p strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;font-style:normal;vertical-align:middle;margin-top:-2px}@media(forced-colors: active){body:not(.override-contrast-mode) .time-remaining-post-game-dialog p strong{border:1px solid #fff}}.time-remaining-post-game-dialog button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;display:block;margin:0 auto}.time-remaining-post-game-dialog button,.time-remaining-post-game-dialog button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .time-remaining-post-game-dialog button{border:1px solid #fff}}.time-remaining-post-game-dialog button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.time-remaining-post-game-dialog button>*:first-child{padding-left:0}.time-remaining-post-game-dialog button>*:last-child{padding-right:0}.time-remaining-post-game-dialog button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .time-remaining-post-game-dialog button svg *{fill:CanvasText}}.time-remaining-post-game-dialog button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .time-remaining-post-game-dialog button svg{opacity:1}}.time-remaining-post-game-dialog button img{height:50%}.time-remaining-post-game-dialog button:disabled{opacity:.3}.time-remaining-post-game-dialog button:disabled,.time-remaining-post-game-dialog button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.time-remaining-post-game-dialog button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.time-remaining-post-game-dialog button:not(:disabled):hover svg{opacity:1}}.time-remaining-post-game-dialog button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.time-remaining-post-game-dialog button pro-cta-label{display:inline}.time-remaining-post-game-dialog button.primary{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.time-remaining-post-game-dialog button.primary:hover{filter:brightness(1.1)}}.time-remaining-post-game-dialog button.secondary{background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}@media(hover: hover){.time-remaining-post-game-dialog button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}.time-remaining-post-game-dialog button+button{margin-top:15px}.time-remaining-post-game-dialog h1{margin-bottom:24px}.time-remaining-post-game-dialog h1 strong{color:var(--theme--highlight)}.time-remaining-post-game-dialog h1 span{white-space:nowrap}.time-remaining-post-game-dialog p{display:block;max-width:100%;margin-bottom:30px}.time-remaining-post-game-dialog button{background:rgba(0,0,0,0) linear-gradient(190deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff}",""]);const l=n},"dialogs/trainer-notes-dialog":(e,t,i)=>{i.r(t),i.d(t,{TrainerNotesDialog:()=>m,TrainerNotesDialogService:()=>h});var a=i(15215),o=i("aurelia-dialog"),r=i("aurelia-framework"),n=i(20770),l=i(54995),s=i(88849),d=i(48335),g=i(62914),p=i(23218),c=i(48881);let m=class{#i;#s;constructor(e,t,i){this.controller=e,this.#i=t,this.#s=i,this.resizeHandler=(0,d.s)((()=>{this.#d()}),150)}attached(){setTimeout((()=>{this.#d()}),0),window.addEventListener("resize",this.resizeHandler),this.#i.event("trainer_instruction_open",{trainer_id:this.config.selectedTrainer.id,title_id:this.config.selectedTrainer.titleId},g.Io)}detached(){window.removeEventListener("resize",this.resizeHandler)}activate(e){this.config=e}get longNotes(){return this.config.trainerNotes.replace(/\s|\r|\n|\*/g,"").replace(/!\[.*?\]\(.*?\.(jpg|jpeg|png|svg).*?\)/gi,"").replace(/\[(.*?)\]\(.*?\)/g,"$1").length>150}toggleDontShowAgain(){this.dontShowToggled=!this.dontShowToggled}dismissNote(){this.#s.dispatch(c.NX,"hideTrainerInstructionsNote",!0),this.#i.event("trainer_instruction_message_dismiss_click",{title_id:this.config.selectedTrainer.titleId,trainer_id:this.config.selectedTrainer.id},g.Io)}handlePlayButtonClick(){this.isScrollable&&!this.hasScrolledToBottom?this.#i.event("trainer_instruction_disabled_cta_click",{trainer_id:this.config.selectedTrainer.id,title_id:this.config.selectedTrainer.titleId},g.Io):this.dontShowToggled&&(this.#s.dispatch(c.ah,this.config.selectedTrainer.id,(0,s.YZ)(this.config.selectedTrainer?.blueprint.notes??"")),this.#i.event("trainer_instruction_dismiss",{trainer_id:this.config.selectedTrainer.id,title_id:this.config.selectedTrainer.titleId},g.Io))}close(){this.controller.close(!1),this.#i.event("trainer_instruction_back_click",{trainer_id:this.config.selectedTrainer.id,title_id:this.config.selectedTrainer.titleId},g.Io)}handleScroll(){if(this.contentEl){const{scrollTop:e,scrollHeight:t,clientHeight:i}=this.contentEl;this.hasScrolledToBottom=t-e-i<5}}#d(){this.contentEl&&(this.isScrollable=this.contentEl.scrollHeight>this.contentEl.clientHeight,this.isScrollable?this.handleScroll():this.hasScrolledToBottom=!0)}};(0,a.Cg)([(0,r.computedFrom)("config.trainerNotes"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],m.prototype,"longNotes",null),m=(0,a.Cg)([(0,l.m6)({selectors:{hideTrainerInstructionsNote:(0,l.$t)((e=>e?.flags?.hideTrainerInstructionsNote))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[o.DialogController,g.j0,n.il])],m);let h=class extends p.D{constructor(){super(...arguments),this.viewModelClass="dialogs/trainer-notes-dialog"}};h=(0,a.Cg)([(0,r.autoinject)()],h)},"dialogs/trainer-notes-dialog-small":(e,t,i)=>{i.r(t),i.d(t,{TrainerNotesDialogSmall:()=>l,TrainerNotesDialogSmallService:()=>s});var a=i(15215),o=i("aurelia-dialog"),r=i("aurelia-framework"),n=i(17275);let l=class{constructor(e){this.controller=e}activate(e){this.config=e}};l=(0,a.Cg)([(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[o.DialogController])],l);let s=class extends n.C{constructor(){super(...arguments),this.viewModelClass="dialogs/trainer-notes-dialog-small"}};s=(0,a.Cg)([(0,r.autoinject)()],s)},"dialogs/trainer-notes-dialog-small.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./trainer-notes-dialog-small.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/cheats/resources/value-converters/blueprint-translation"></require> <ux-dialog class="trainer-notes-dialog-small"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> <span class="trainer-notes-dialog-small-header"> <i class="icon">assignment</i>${\'trainer_notes_dialog_small.setup_instructions\' | i18n} </span> </ux-dialog-header> <ux-dialog-body class="trainer-notes-dialog-small-body"> <div class="trainer-notes-dialog-small-body-content"> <div class="scroll-wrapper"> <p innerhtml.bind="config.trainerNotes | blueprintTranslation:config.selectedTrainer | markdown"></p> </div> </div> </ux-dialog-body> <button class="trainer-notes-dialog-small-button" click.trigger="controller.close(true)"> ${\'trainer_notes_dialog_small.got_it\' | i18n} </button> </ux-dialog> </template> '},"dialogs/trainer-notes-dialog-small.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>c});var a=i(31601),o=i.n(a),r=i(76314),n=i.n(r),l=i(4417),s=i.n(l),d=new URL(i(83959),i.b),g=n()(o()),p=s()(d);g.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined,.trainer-notes-dialog-small-header .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.trainer-notes-dialog-small{width:480px;padding:16px;background:rgba(var(--theme--background-accent));border:.5px solid rgba(255,255,255,.15);border-radius:16px;color:var(--theme--text-primary);backdrop-filter:blur(50px)}.trainer-notes-dialog-small-header{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;display:flex;align-items:center;gap:12px;padding-bottom:16px}.trainer-notes-dialog-small-header .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;font-size:16px;height:16px;width:16px}.trainer-notes-dialog-small-body{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:400;color:var(--theme--text-primary);padding:16px 0px}.trainer-notes-dialog-small-body-content{padding:16px 12px 16px 16px;border-radius:12px;background:rgba(255,255,255,.05)}.trainer-notes-dialog-small-body-content .scroll-wrapper{padding-right:4px}.trainer-notes-dialog-small-body-content .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.trainer-notes-dialog-small-body-content .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.trainer-notes-dialog-small-body-content .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.trainer-notes-dialog-small-body-content .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.trainer-notes-dialog-small-body-content .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.trainer-notes-dialog-small-body-content .scroll-wrapper p{margin:0}.trainer-notes-dialog-small-body-content p{margin:0}.trainer-notes-dialog-small-body-content a{font-weight:700;transition:filter .15s;cursor:pointer;text-decoration:underline;display:inline-block;color:var(--theme--text-highlight)}.trainer-notes-dialog-small-body-content a::after{content:"↗"}.trainer-notes-dialog-small-body-content a:hover{filter:brightness(1.2)}.trainer-notes-dialog-small-body-content img{margin-top:8px;width:100%;max-width:500px;max-height:500px;border-radius:5px}.trainer-notes-dialog-small-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;height:40px;box-shadow:none;line-height:24px;font-weight:700;color:#000;border-radius:56px;padding:10px 16px;background:#fff;display:flex;justify-self:center}.trainer-notes-dialog-small-button,.trainer-notes-dialog-small-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .trainer-notes-dialog-small-button{border:1px solid #fff}}.trainer-notes-dialog-small-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.trainer-notes-dialog-small-button>*:first-child{padding-left:0}.trainer-notes-dialog-small-button>*:last-child{padding-right:0}.trainer-notes-dialog-small-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .trainer-notes-dialog-small-button svg *{fill:CanvasText}}.trainer-notes-dialog-small-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .trainer-notes-dialog-small-button svg{opacity:1}}.trainer-notes-dialog-small-button img{height:50%}.trainer-notes-dialog-small-button:disabled{opacity:.3}.trainer-notes-dialog-small-button:disabled,.trainer-notes-dialog-small-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.trainer-notes-dialog-small-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.trainer-notes-dialog-small-button:not(:disabled):hover svg{opacity:1}}.trainer-notes-dialog-small-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.trainer-notes-dialog-small-button:hover{background:rgba(255,255,255,.8) !important;color:rgba(0,0,0,.8) !important}`,""]);const c=g},"dialogs/trainer-notes-dialog.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var a=i(14385),o=i.n(a),r=new URL(i(18285),i.b);const n='<template> <require from="./trainer-notes-dialog.scss"></require> <require from="shared/cheats/resources/value-converters/blueprint-translation"></require> <require from="shared/cheats/resources/custom-attributes/steam-capsule-bg"></require> <require from="../cheats/resources/custom-attributes/steam-hero-bg"></require> <require from="../cheats/resources/elements/trainer-play-button"></require> <ux-dialog class="trainer-notes-dialog fullscreen-dialog overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="header-label"> <div class="wemod-logo"><img src="'+o()(r)+'"></div> <span class="wemod-copy">wemod</span> </div> <div class="background" steam-hero-bg="steam-id.bind: config.steamId; title-thumbnail.bind: config.titleThumbnail"></div> <div class="trainer-notes-dialog-wrapper"> <div class="trainer-notes-dialog-wrapper-back" tabindex="0" click.delegate="close()"> <i class="icon-arrow-left">arrow_back</i> ${\'trainer_notes_dialog.back\' | i18n} </div> <div class="trainer-notes-dialog-wrapper-header"> <div class="trainer-notes-dialog-wrapper-header-left"> <div class="game-image" steam-capsule-bg="steam-id.bind: config.steamId"></div> <span class="trainer-notes-dialog-wrapper-header-left-text"> <span class="subtitle">${\'trainer_notes_dialog.almost_there\' | i18n}</span> <span class="title">${\'trainer_notes_dialog.lets_get_you_set_up\' | i18n}</span> </span> </div> <div class="trainer-notes-dialog-wrapper-header-right"> <span class="trainer-notes-dialog-wrapper-header-right-time"> ${\'trainer_notes_dialog.est_time\' | i18n} </span> <span class="trainer-notes-dialog-wrapper-header-right-time-value"> ${longNotes ? \'~\' : \'&lt;\'}1 ${\'trainer_notes_dialog.minute_abbreviation\' | i18n} </span> </div> </div> <div class="trainer-notes-dialog-wrapper-body"> <div if.bind="!hideTrainerInstructionsNote" class="trainer-notes-dialog-wrapper-body-note"> <span class="label" innerhtml.bind="\'trainer_notes_dialog.dismissable_note\' | i18n | markdown"></span> <span class="dismiss" tabindex="0" click.delegate="dismissNote()"> ${\'trainer_notes_dialog.dismiss\' | i18n} </span> </div> <div class="trainer-notes-dialog-wrapper-body-content"> <div class="scroll-wrapper" ref="contentEl" scroll.trigger="handleScroll()"> <p innerhtml.bind="config.trainerNotes | blueprintTranslation:config.selectedTrainer | markdown"></p> </div> </div> </div> <div class="trainer-notes-dialog-wrapper-footer"> <span class="trainer-notes-dialog-wrapper-footer-left" tabindex="0" click.delegate="toggleDontShowAgain()"> <div class="toggle-wrapper ${dontShowToggled ? \'enabled\' : \'\'}"> <span class="toggle"> <span class="handle"></span> </span> </div> <span class="label">${\'trainer_notes_dialog.dont_show_again\' | i18n}</span> </span> <span class="trainer-notes-dialog-wrapper-footer-right"> <span if.bind="isScrollable" class="trainer-notes-dialog-wrapper-footer-right-label"> ${\'trainer_notes_dialog.dont_show_again_description\' | i18n} </span> <trainer-play-button click.delegate="handlePlayButtonClick()" trainer-info.bind="config.selectedTrainer" model.bind="config.model" disabled.bind="isScrollable && !hasScrolledToBottom" auto-launch.bind="config.autoLaunch" auto-launch-without-mods.bind="config.autoLaunchWithoutMods" button-state="play" is-trainer-instructions-dialog="true" hide-dropdown="true" class="au-animate"></trainer-play-button> </span> </div> </div> </ux-dialog> </template> '},"dialogs/trainer-notes-dialog.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>h});var a=i(31601),o=i.n(a),r=i(76314),n=i.n(r),l=i(4417),s=i.n(l),d=new URL(i(83959),i.b),g=new URL(i(98593),i.b),p=n()(o()),c=s()(d),m=s()(g);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,.trainer-notes-dialog-wrapper-back i,.app-sidebar .nav a i.home-icon,.app-sidebar .nav a i.my-games-icon,.app-sidebar .nav a i.my-videos-icon,.app-sidebar .nav a i.explore-icon,.app-sidebar .nav a i.upcoming-icon,.app-sidebar .nav a i.maps-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.app-sidebar{display:flex;flex-direction:column;height:100%;width:220px;transition:.3s width ease-in-out;background:linear-gradient(to bottom, rgba(var(--theme--background-accent--rgb), 0.5) 0%, var(--theme--background-accent) 100%);position:relative;z-index:1}.app-sidebar sidebar-game-lists{overflow-y:hidden;height:-webkit-fill-available;padding-bottom:24px;scrollbar-gutter:stable}.app-sidebar sidebar-game-lists::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive:hover,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:decrement{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:increment{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent 8px, rgba(255, 255, 255, 0.1) 8px, rgba(255, 255, 255, 0.1) calc(100% - 8px), transparent calc(100% - 8px))}.app-sidebar sidebar-game-lists:hover{overflow-y:auto}.app-sidebar hr.sidebar-divider{display:flex;align-items:center;width:100%;height:0px;opacity:.5;border:none;border-top:1px solid rgba(255,255,255,.15);margin:8px 0}.app-sidebar hr.sidebar-divider.nav-divider{width:calc(100% - 16px);margin:8px auto}.app-sidebar.collapsed{width:72px}.app-sidebar.collapsed hr.sidebar-divider.nav-divider{width:100%}.app-sidebar.collapsed .sidebar-header{align-items:center;justify-content:center}.app-sidebar.collapsed .sidebar-header-branding .wemod-logo{margin:0 auto}.app-sidebar.collapsed .sidebar-header-branding .wemod-copy{visibility:hidden;position:absolute;left:40px;scale:.5;opacity:0}.app-sidebar.collapsed .nav{align-items:center;min-width:0;width:100%;margin:0 auto}.app-sidebar.collapsed .nav-text{visibility:hidden;position:absolute;transform:translateX(-10px);opacity:0}.app-sidebar.collapsed .nav a{padding:0}.app-sidebar.collapsed .nav a i{font-size:24px !important;margin:0 auto}.app-sidebar.collapsed .nav a new-badge{display:none}.app-sidebar .sidebar-header{position:relative;z-index:1;display:flex;flex-direction:row;align-items:start;gap:22px;min-height:40px;padding:8px}.app-sidebar .sidebar-header-branding{display:flex;flex-direction:row;align-items:center;gap:6px;padding:0 8px}.app-sidebar .sidebar-header-branding .wemod-logo{display:flex;width:20px;height:12px;margin:0 auto}.app-sidebar .sidebar-header-branding .wemod-copy{font-weight:600;font-size:14px;line-height:24px;display:flex;align-items:center;color:#fff;opacity:1;transition:.2s all ease-in-out;scale:1}.app-sidebar app-sidebar-search-button{margin:4px 0 8px}.app-sidebar .nav{position:relative;z-index:1;display:flex;flex-direction:column;gap:4px;min-width:72px;width:100%;padding:0 8px 0px 8px;border-radius:8px}.app-sidebar .nav-text{transition:opacity .5s ease-in-out,transform .2s ease-in-out}.app-sidebar .nav a{display:flex;flex-direction:row;align-items:center;padding:8px 8px 8px 12px;gap:10px;height:40px;mix-blend-mode:normal;border-radius:8px;font-weight:500;font-size:14px;line-height:24px;color:rgba(255,255,255,.8);border-radius:8px;width:100%;white-space:nowrap}.app-sidebar .nav a.current{background:rgba(255,255,255,.25);color:#fff}.app-sidebar .nav a.current i{color:rgba(255,255,255,.8) !important}.app-sidebar .nav a:hover{color:rgba(255,255,255,.9);background:rgba(255,255,255,.15)}.app-sidebar .nav a:hover i{color:rgba(255,255,255,.6) !important}.app-sidebar .nav a i{color:rgba(255,255,255,.6);font-size:20px}.app-sidebar .nav a .nav-text{flex:1}.app-sidebar .nav a i.home-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.home-icon:before{font-family:inherit;content:"home"}.app-sidebar .nav a i.my-games-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-games-icon:before{font-family:inherit;content:"browse"}.app-sidebar .nav a i.my-videos-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-videos-icon:before{font-family:inherit;content:"live_tv"}.app-sidebar .nav a i.explore-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.explore-icon:before{font-family:inherit;content:"feature_search"}.app-sidebar .nav a i.upcoming-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.upcoming-icon:before{font-family:inherit;content:"double_arrow"}.app-sidebar .nav a i.maps-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.maps-icon:before{font-family:inherit;content:"map"}#fullscreen-dialogs ux-dialog-overlay,ux-dialog.fullscreen-dialog{background:var(--theme--background) !important}ux-dialog{box-shadow:none}.trainer-notes-dialog{display:flex;justify-content:center;align-items:center;width:100vw;height:100vh;overflow:hidden}.trainer-notes-dialog .header-label{position:absolute;color:#fff;top:8px;left:8px;height:24px;display:flex;flex-direction:row;align-items:center;gap:6px;padding:0 8px}.trainer-notes-dialog .header-label .wemod-logo{display:flex;width:20px;height:12px;margin:0 auto}.trainer-notes-dialog .header-label .wemod-copy{font-weight:600;font-size:14px;line-height:24px;display:flex;align-items:center;color:#fff;opacity:1;transition:.2s all ease-in-out;scale:1}.trainer-notes-dialog .background{position:absolute;left:0;top:0;width:100%;height:100%;background-size:cover;background-repeat:no-repeat;background-image:url(${m});background-position:top center;pointer-events:none;opacity:.3;filter:blur(10px);transform:scale(1.02);overflow:hidden}.trainer-notes-dialog .background:before{content:"";display:block;width:100%;height:100%;position:absolute;left:0;top:0;background:-webkit-linear-gradient(top, rgba(var(--theme--background--rgb), 0.4) 0, var(--theme--background) 100%)}@media(max-width: 1200px){.trainer-notes-dialog .background{background-size:140% auto}}@media(forced-colors: active){body:not(.override-contrast-mode) .trainer-notes-dialog .background{background-image:none !important;background-color:#000 !important}}.trainer-notes-dialog .background{z-index:0}.trainer-notes-dialog-wrapper{display:flex;flex-direction:column;align-items:center;justify-content:center;row-gap:16px;width:100%;max-width:37.5vw;height:100%;padding:64px 0px;z-index:1}.trainer-notes-dialog-wrapper .game-image{min-width:53px;height:64px;background-size:cover;background-position:center center;background-repeat:no-repeat;border-radius:12px;position:relative;display:inline-block !important}.trainer-notes-dialog-wrapper .game-image.is-fallback{width:0;min-width:0;visibility:hidden}.trainer-notes-dialog-wrapper .game-image:after{content:"";position:absolute;border-radius:12px;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);mix-blend-mode:overlay;z-index:1}.trainer-notes-dialog-wrapper-back{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-highlight);display:flex;align-items:center;gap:4px;align-self:flex-start}.trainer-notes-dialog-wrapper-back,.trainer-notes-dialog-wrapper-back *{cursor:pointer}.trainer-notes-dialog-wrapper-back i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:14px}.trainer-notes-dialog-wrapper-header{display:flex;justify-content:space-between;align-items:flex-end;width:100%}.trainer-notes-dialog-wrapper-header-left{display:flex;align-items:center;gap:8px}.trainer-notes-dialog-wrapper-header-left-text{display:flex;flex-direction:column}.trainer-notes-dialog-wrapper-header-left-text .subtitle{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-primary)}.trainer-notes-dialog-wrapper-header-left-text .title{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;color:var(--theme--text-highlight)}.trainer-notes-dialog-wrapper-header-right{display:flex;flex-direction:column;align-items:flex-end}.trainer-notes-dialog-wrapper-header-right-time{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;color:var(--theme--text-secondary)}.trainer-notes-dialog-wrapper-header-right-time-value{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;color:var(--theme--text-highlight)}.trainer-notes-dialog-wrapper-body{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:400;display:flex;flex-direction:column;row-gap:16px;width:100%;color:var(--theme--text-primary)}.trainer-notes-dialog-wrapper-body-note,.trainer-notes-dialog-wrapper-body-content{background:rgba(255,255,255,.05);padding:16px;border-radius:16px;backdrop-filter:blur(32px);-webkit-backdrop-filter:blur(32px)}.trainer-notes-dialog-wrapper-body-note{display:flex;justify-content:space-between;align-items:center;gap:24px}.trainer-notes-dialog-wrapper-body-note .dismiss{font-weight:700;cursor:pointer;margin:-16px -16px;padding:16px 16px}.trainer-notes-dialog-wrapper-body-note .dismiss:hover{color:var(--theme--text-highlight)}.trainer-notes-dialog-wrapper-body-content{padding:4px 4px 4px 16px}.trainer-notes-dialog-wrapper-body-content,.trainer-notes-dialog-wrapper-body-content *{user-select:text;cursor:auto}.trainer-notes-dialog-wrapper-body-content .scroll-wrapper{padding-right:12px;max-height:45vh;overflow-y:auto}.trainer-notes-dialog-wrapper-body-content .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.trainer-notes-dialog-wrapper-body-content .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.trainer-notes-dialog-wrapper-body-content .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.trainer-notes-dialog-wrapper-body-content .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.trainer-notes-dialog-wrapper-body-content .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.trainer-notes-dialog-wrapper-body-content p{margin:0;padding-top:12px;padding-bottom:12px}.trainer-notes-dialog-wrapper-body-content a{font-weight:700;transition:filter .15s;cursor:pointer;text-decoration:underline;display:inline-block;color:var(--theme--text-highlight)}.trainer-notes-dialog-wrapper-body-content a::after{content:"↗"}.trainer-notes-dialog-wrapper-body-content a:hover{filter:brightness(1.2)}.trainer-notes-dialog-wrapper-body-content img{margin-top:8px;width:100%;max-width:500px;max-height:500px;border-radius:5px}.trainer-notes-dialog-wrapper-footer{display:flex;justify-content:space-between;align-items:center;width:100%}.trainer-notes-dialog-wrapper-footer-left{display:flex;align-items:center;gap:12px}.trainer-notes-dialog-wrapper-footer-left,.trainer-notes-dialog-wrapper-footer-left *{cursor:pointer}.trainer-notes-dialog-wrapper-footer-left .toggle-wrapper{position:relative;display:inline-flex;height:44px;border-radius:100px;align-items:center}@media(forced-colors: active){body:not(.override-contrast-mode) .trainer-notes-dialog-wrapper-footer-left .toggle-wrapper{border:1px solid #fff}}.trainer-notes-dialog-wrapper-footer-left .toggle-wrapper .toggle{position:relative;width:31px;height:20px;border-radius:12px;background:rgba(255,255,255,.15);flex:0 0 auto}@media(forced-colors: active){body:not(.override-contrast-mode) .trainer-notes-dialog-wrapper-footer-left .toggle-wrapper .toggle{border:1px solid #fff}}.trainer-notes-dialog-wrapper-footer-left .toggle-wrapper .toggle .handle{width:16px;height:16px;border-radius:50%;display:flex;align-items:center;justify-content:center;background:rgba(255,255,255,.5);position:absolute;left:2px;top:2px;transition:left .15s ease-in-out,background-color .15s}.trainer-notes-dialog-wrapper-footer-left .toggle-wrapper.enabled .toggle{background:linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),var(--theme--highlight)}.trainer-notes-dialog-wrapper-footer-left .toggle-wrapper.enabled .toggle .handle{left:calc(100% - 18px);background:#fff}.trainer-notes-dialog-wrapper-footer-right{display:flex;align-items:center;gap:8px}.trainer-notes-dialog-wrapper-footer-right-label{font-size:10px;line-height:12px;color:var(--theme--text-disabled);max-width:112px;text-align:right}`,""]);const h=p},"dialogs/webview-dialog":(e,t,i)=>{i.r(t),i.d(t,{WebviewDialog:()=>_,WebviewDialogService:()=>k,getWebviewSupportedSettings:()=>y});var a=i(15215),o=i("aurelia-dialog"),r=i("aurelia-event-aggregator"),n=i("aurelia-framework"),l=i(68663),s=i(10699),d=i(62914),g=i(27958),p=i(67064),c=i(21795),m=i(17275),h=i(92465),b=i(20057),u=i(92694),f=i(54995),x=i(49442),v=i(29944),w=i("dialogs/payment-processing-dialog");const y=e=>({theme:e.theme,useWindowsContrastMode:e.useWindowsContrastMode,reduceMotion:e.reduceMotion});let _=class{#i;#g;#p;#c;#m;#h;#b;#u;#f;#x;#v;#w;#y;constructor(e,t,i,a,o,r,n,l,s,d){this.controller=e,this.wasClosedByWebview=!1,this.loading=!0,this.#i=t,this.#g=i,this.#p=a,this.#c=o,this.#m=r,this.#h=n,this.#b=l,this.#u=s,this.#f=d}activate(e){this.config=e}deactivate(e){this.wasClosedByWebview||this.#w?.execute("external_close",{ok:!e.wasCancelled,output:e.output||"cancel"}).catch(x.Y),this.#i.event("webview_close",{wasClosedByWebview:this.wasClosedByWebview,wasLoading:this.loading,wasCanceled:e.wasCancelled,route:this.config.route},d.Io)}async attached(){let e;this.#x=Date.now(),this.#v=(0,h.Ix)((()=>this.#_("load-timeout",`Timed out trying to load "${this.iframeEl.src}"`)),3e4),this.#y=new h.Vd([this.#v]);try{e=await this.#m.createWebview({mode:"modal",locale:this.#c.getEffectiveLocale().baseName,route:this.config.route,params:this.config.params??{},settings:y(this.settings)})}catch(e){return void this.#_("create-webview-error",`Error creating webview with route "${this.config.route}"`)}this.iframeEl&&(this.iframeEl.src=e.url,this.iframeEl.contentWindow&&(this.#w=new v.Jx(this.iframeEl.contentWindow,e.origin),this.#w.setHandler("event",(e=>this.#k(e))),this.#w.setHandler("ad_conversion",(e=>this.#T(e))),this.#w.setHandler("error",(e=>this.#z(e))),this.#w.setHandler("router_event",(e=>this.#C(e))),this.#w.setHandler("resize",(e=>this.#q(e))),this.#w.setHandler("view_title",(e=>this.#S(e))),this.#w.setHandler("view_collection",(e=>this.#I(e))),this.#w.setHandler("close",(e=>this.#M(e))),this.#w.setHandler("open_uri",(e=>this.#$(e))),this.#w.setHandler("toast_message",(e=>this.#D(e))),this.#w.setHandler("set_payment_processing_state",(e=>this.#L(e))),this.#y.push(this.#w)))}detached(){this.#b.hide(),this.#y?.dispose(),this.#y=null,this.#v=null,this.#w=null}handleResize(e,t){this.iframeEl.width="auto"===t[0]?e[0].toString():`${t[0]}px`,this.iframeEl.height="auto"===t[1]?e[1].toString():`${t[1]}px`}#_(e,t){this.#i.event("webview_dialog_error",{type:e,message:t??""},d.Io),this.controller.cancel("error")}#k(e){return this.#i.event(e.name,e.data,e.dispatch??d.Io),!0}#T(e){return this.#i.adConversion(e.label,e.data),!0}#z(e){return this.#_(e.type,e.message),!0}#C(e){if(this.handleResize(e.size,e.preferredSize),"router:navigation:complete"===e.event){const e=Date.now()-this.#x;this.#v?.dispose(),this.loading=!1,this.#p.publish("webview_load",{mode:"modal",route:this.config.route,params:this.config.params,duration:e}),this.#f.collectImmediately({name:"webview_window_loadtime",value:e,tags:[{key:"client_version",value:"$clientVersion"},{key:"webview_route",value:this.config.route}]})}return"router:navigation:error"===e.event&&this.#_("router-navigation",`Error navigating to "${e.name}"`),!0}#q(e){return this.handleResize(e.size,e.preferredSize),!0}#S(e){return this.#g.router.navigateToRoute("title",{titleId:e.gameId,gameId:e.gameId,previousRoute:e.previousRoute,parentRoute:e.parentRoute}),this.#p.publish(new c.dY(e.location??"",e.titleId,e.gameId||null,null,!1)),!0}#I(e){return this.#g.router.navigateToRoute("collection",{slug:e.slug}),!0}#M(e){return this.wasClosedByWebview=!0,e.refreshAccount&&this.#u.refreshAccount(),this.controller.close(e.ok,e.output),!0}#$(e){return window.open(e.uri,"_blank"),!0}#D(e){return this.#h.toast({content:"string"==typeof e.content?b.F2.literal(e.content):e.content,type:e.type}),!0}async#L(e){return e.processing?await this.#b.show():await this.#b.hide(),!0}};_=(0,a.Cg)([(0,n.autoinject)(),(0,f.m6)({selectors:{settings:(0,f.$t)((e=>e.settings))}}),(0,a.Sn)("design:paramtypes",[o.DialogController,d.j0,g.L,r.EventAggregator,b.F2,l.x,p.l,w.PaymentProcessingDialogService,s.G,u.k])],_);let k=class extends m.C{constructor(){super(...arguments),this.viewModelClass="dialogs/webview-dialog"}};k=(0,a.Cg)([(0,n.autoinject)()],k)},"dialogs/webview-dialog.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./webview-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/resources/elements/loading-indicator"></require> <ux-dialog class="webview-dialog scrollable ${!loading ? \'loaded\' : \'\'}" style.bind="config.styles"> <close-button click.delegate="controller.close(false, \'close_button\')" tabindex="0"></close-button> <div class="loading-indicator" if.bind="loading"> <loading-indicator></loading-indicator> </div> <div class="dialog-scroll-wrapper" show.bind="!loading"> <iframe ref="iframeEl" sandbox="allow-same-origin allow-forms allow-scripts allow-popups allow-pointer-lock"></iframe> </div> </ux-dialog> </template> '},"dialogs/webview-dialog.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>l});var a=i(31601),o=i.n(a),r=i(76314),n=i.n(r)()(o());n.push([e.id,".webview-dialog{border:0;padding:0;width:auto}.webview-dialog .dialog-scroll-wrapper{display:flex;border-radius:20px}.webview-dialog iframe{border:0}.webview-dialog .loading-indicator{padding:60px 150px}.webview-dialog.loaded{animation:dialog-pop2 .2s ease-in-out !important}",""]);const l=n}}]);
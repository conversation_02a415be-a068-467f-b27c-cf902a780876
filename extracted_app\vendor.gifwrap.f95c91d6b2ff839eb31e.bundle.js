"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6125],{11658:(t,e)=>{class r{constructor(t,e,r){this.width=r.width,this.height=r.height,this.loops=r.loops,this.usesTransparency=r.usesTransparency,this.colorScope=r.colorScope,this.frames=e,this.buffer=t}}r.GlobalColorsPreferred=0,r.GlobalColorsOnly=1,r.LocalColorsOnly=2;class i extends Error{constructor(t){super(t),t instanceof Error&&(this.stack="Gif"+t.stack)}}e.Gif=r,e.GifError=i},16154:(t,e,r)=>{const i=r(34399),{Gif:n,GifError:o}=r(11658);function s(){const t=r(33606);return s=function(){return t},t}const{GifFrame:a}=r(39925);function h(t,e){const r=t.indexOf(e);return-1===r?null:r}function l(t,e){for(var r,i=0,n=t.length-1;i<=n;)if(t[r=Math.floor((i+n)/2)]>e)n=r-1;else{if(!(t[r]<e))return r;i=r+1}return null}function c(t){const e=t.colors;t.usesTransparency&&e.push(0);const r=e.length;let i=2;for(;r>i;)i<<=1;e.length=i,e.fill(0,r)}function f(t,e){let r=t.bitmap.width*t.bitmap.height;return r=Math.ceil(r*e/8),r+=Math.ceil(r/255),100+r+768}function u(t){let e=t.indexCount,r=0;for(--e;e;)++r,e>>=1;return r>0?r:1}function d(t,e,r,i,n){if(r.interlaced)throw new o("writing interlaced GIFs is not supported");const s=function(t,e,r){const i=r.colors,n=i.length<=8?h:l,s=e.bitmap.data,a=new Buffer(s.length/4);let c=i.length,f=0,u=0;for(;f<s.length;){if(0!==s[f+3]){const t=s.readUInt32BE(f,!0)>>8&16777215;a[u]=n(i,t)}else a[u]=c;f+=4,++u}if(r.usesTransparency){if(256===c)throw new o(`Frame ${t} already has 256 colorsand so can't use transparency`)}else c=null;return{buffer:a,transparentIndex:c}}(e,r,i),a={delay:r.delayCentisecs,disposal:r.disposalMethod,transparent:s.transparentIndex};n&&(c(i),a.palette=i.colors);try{let e,i=t.getOutputBuffer(),n=t.getOutputBufferPosition(),o=!0;for(;o;)if(e=t.addFrame(r.xOffset,r.yOffset,r.bitmap.width,r.bitmap.height,s.buffer,a),o=!1,e>=i.length-1){const e=new Buffer(1.5*i.length);i.copy(e),t.setOutputBuffer(e),t.setOutputBufferPosition(n),i=e,o=!0}return i}catch(t){throw new o(t)}}e.GifCodec=class{constructor(t={}){this._transparentRGB=null,"number"==typeof t.transparentRGB&&0!==t.transparentRGB&&(this._transparentRGBA=256*t.transparentRGB),this._testInitialBufferSize=0}decodeGif(t){try{let e;try{e=new i.GifReader(t)}catch(t){throw new o(t)}const r=e.numFrames(),s=[],a={width:e.width,height:e.height,loops:e.loopCount(),usesTransparency:!1};for(let t=0;t<r;++t){const r=this._decodeFrame(e,t,a.usesTransparency);s.push(r.frame),r.usesTransparency&&(a.usesTransparency=!0)}return Promise.resolve(new n(t,s,a))}catch(t){return Promise.reject(t)}}encodeGif(t,e={}){try{if(null===t||0===t.length)throw new o("there are no frames");const r=s().getMaxDimensions(t);return(e=Object.assign({},e)).width=r.maxWidth,e.height=r.maxHeight,e.loops=e.loops||0,e.colorScope=e.colorScope||n.GlobalColorsPreferred,Promise.resolve(this._encodeGif(t,e))}catch(t){return Promise.reject(t)}}_decodeFrame(t,e,r){let i,n;try{if(i=t.frameInfo(e),n=new Buffer(t.width*t.height*4),t.decodeAndBlitFrameRGBA(e,n),i.width!==t.width||i.height!==t.height){if(i.y&&(n=n.slice(i.y*t.width*4)),t.width>i.width)for(let e=0;e<i.height;++e)n.copy(n,e*i.width*4,4*(i.x+e*t.width),4*(i.x+e*t.width)+4*i.width);n=n.slice(0,i.width*i.height*4)}}catch(t){throw new o(t)}let s=!1;if(null===this._transparentRGBA){if(!r)for(let t=3;t<n.length;t+=4)0===n[t]&&(s=!0,t=n.length)}else for(let t=3;t<n.length;t+=4)0===n[t]&&(n.writeUInt32BE(this._transparentRGBA,t-3),s=!0);return{frame:new a(i.width,i.height,n,{xOffset:i.x,yOffset:i.y,disposalMethod:i.disposal,interlaced:i.interlaced,delayCentisecs:i.delay}),usesTransparency:s}}_encodeGif(t,e){let r;if(e.colorScope===n.LocalColorsOnly)r=s().getColorInfo(t,0);else if(r=s().getColorInfo(t,256),!r.colors){if(e.colorScope===n.GlobalColorsOnly)throw new o("Too many color indexes for global color table");e.colorScope=n.LocalColorsOnly}e.usesTransparency=r.usesTransparency;const a=r.palettes;return e.colorScope===n.LocalColorsOnly?function(t,e,r,s){const a={loop:e.loops};let h,l=new Buffer(2e3);try{h=new i.GifWriter(l,e.width,e.height,a)}catch(t){throw new o(t)}for(let e=0;e<t.length;++e)l=d(h,e,t[e],s[e],!0);return new n(l.slice(0,h.end()),t,e)}(t,e,0,a):function(t,e,r,s){const a={colors:s.colors.slice(),usesTransparency:s.usesTransparency};c(a);const h={palette:a.colors,loop:e.loops};let l,f=new Buffer(2e3);try{l=new i.GifWriter(f,e.width,e.height,h)}catch(t){throw new o(t)}for(let e=0;e<t.length;++e)f=d(l,e,t[e],s,!1);return new n(f.slice(0,l.end()),t,e)}(t,e,0,r)}_getSizeEstimateGlobal(t,e){if(this._testInitialBufferSize>0)return this._testInitialBufferSize;let r=968;const i=u(t);return e.forEach((t=>{r+=f(t,i)})),r}_getSizeEstimateLocal(t,e){if(this._testInitialBufferSize>0)return this._testInitialBufferSize;let r=200;for(let i=0;i<e.length;++i){const n=u(t[i]);r+=f(e[i],n)}return r}}},19208:t=>{class e{constructor(...t){if(0===t.length)throw new Error("constructor requires parameters");const r=t[0];if(null!==r&&"object"==typeof r)if(r instanceof e){const t=r.bitmap;this.bitmap={width:t.width,height:t.height,data:new Buffer(t.width*t.height*4)},t.data.copy(this.bitmap.data)}else{if(!(r.width&&r.height&&r.data))throw new Error("unrecognized constructor parameters");this.bitmap=r}else{if("number"!=typeof r||"number"!=typeof t[1])throw new Error("unrecognized constructor parameters");{const e=r,i=t[1],n=t[2];this.bitmap={width:e,height:i},Buffer.isBuffer(n)?this.bitmap.data=n:(this.bitmap.data=new Buffer(e*i*4),"number"==typeof n&&this.fillRGBA(n))}}}blit(t,e,r,i,n,o,s){if(i+o>this.bitmap.width)throw new Error("copy exceeds width of source bitmap");if(e+o>t.bitmap.width)throw new Error("copy exceeds width of target bitmap");if(n+s>this.bitmap.height)throw new Error("copy exceeds height of source bitmap");if(r+s>t.bitmap.height)throw new Erro("copy exceeds height of target bitmap");const a=this.bitmap.data,h=t.bitmap.data,l=4*this.bitmap.width,c=4*t.bitmap.width,f=4*o;let u=n*l+4*i,d=r*c+4*e;for(;--s>=0;)a.copy(h,d,u,u+f),u+=l,d+=c;return this}fillRGBA(t){const e=this.bitmap.data,r=4*this.bitmap.height;let i=0;for(;i<r;)e.writeUInt32BE(t,i),i+=4;for(;i<e.length;)e.copy(e,i,0,r),i+=r;return this}getRGBA(t,e){const r=4*(e*this.bitmap.width+t);return this.bitmap.data.readUInt32BE(r)}getRGBASet(){const t=new Set,e=this.bitmap.data;for(let r=0;r<e.length;r+=4)t.add(e.readUInt32BE(r,!0));return t}greyscale(){const t=this.bitmap.data;return this.scan(0,0,this.bitmap.width,this.bitmap.height,((e,r,i)=>{const n=Math.round(.299*t[i]+.587*t[i+1]+.114*t[i+2]);t[i]=n,t[i+1]=n,t[i+2]=n})),this}reframe(t,r,i,n,o){const s=t<0?0:t,a=r<0?0:r,h=i+s>this.bitmap.width?this.bitmap.width-s:i,l=n+a>this.bitmap.height?this.bitmap.height-a:n,c=t<0?-t:0,f=r<0?-r:0;let u;if(void 0===o){if(s!==t||a!=r||h!==i||l!==n)throw new GifError("fillRGBA required for this reframing");u=new e(i,n)}else u=new e(i,n,o);return this.blit(u,c,f,s,a,h,l),this.bitmap=u.bitmap,this}scale(t){if(1===t)return;if(!Number.isInteger(t)||t<1)throw new Error("the scale must be an integer >= 1");const e=this.bitmap.width,r=this.bitmap.height,i=e*t*4,n=this.bitmap.data,o=new Buffer(r*i*t);let s,a=0,h=0;for(let l=0;l<r;++l){s=h;for(let r=0;r<e;++r){const e=n.readUInt32BE(a,!0);for(let r=0;r<t;++r)o.writeUInt32BE(e,h),h+=4;a+=4}for(let e=1;e<t;++e)o.copy(o,h,s,h),h+=i,s+=i}return this.bitmap={width:e*t,height:r*t,data:o},this}scanAllCoords(t){const e=this.bitmap.width,r=this.bitmap.data.length;let i=0,n=0;for(let o=0;o<r;o+=4)t(i,n,o),++i===e&&(i=0,++n)}scanAllIndexes(t){const e=this.bitmap.data.length;for(let r=0;r<e;r+=4)t(r)}}t.exports=e},31224:(t,e,r)=>{const i=r(19208),{Gif:n,GifError:o}=r(11658),{GifCodec:s}=r(16154),{GifFrame:a}=r(39925),h=r(33606);t.exports={BitmapImage:i,Gif:n,GifCodec:s,GifFrame:a,GifUtil:h,GifError:o}},33606:(t,e,r)=>{const i=r(79896),n=r(65565),o=r(19208),{GifFrame:s}=r(39925),{GifError:a}=r(11658),{GifCodec:h}=r(16154),l=[".jpg",".jpeg",".png",".bmp"],c=new h;function f(t,e,r,i,o){const s=Array.isArray(t)?t:[t];if(o){if(["FloydSteinberg","FalseFloydSteinberg","Stucki","Atkinson","Jarvis","Burkes","Sierra","TwoSierra","SierraLite"].indexOf(o.ditherAlgorithm)<0)throw new Error(`Invalid ditherAlgorithm '${o.ditherAlgorithm}'`);void 0===o.serpentine&&(o.serpentine=!0),void 0===o.minimumColorDistanceToDither&&(o.minimumColorDistanceToDither=0),void 0===o.calculateErrorLikeGIMP&&(o.calculateErrorLikeGIMP=!1)}const a=new n.distance.Euclidean,h=new n.palette[e](a,r,i);let l;l=o?new n.image.ErrorDiffusionArray(a,n.image.ErrorDiffusionArrayKernel[o.ditherAlgorithm],o.serpentine,o.minimumColorDistanceToDither,o.calculateErrorLikeGIMP):new n.image.NearestColor(a);const c=[];s.forEach((t=>{const e=t.bitmap.data,r=new ArrayBuffer(e.length),i=new Uint32Array(r);for(let t=0,r=0;t<e.length;t+=4,++r)i[r]=e.readUInt32LE(t,!0);const o=n.utils.PointContainer.fromUint32Array(i,t.bitmap.width,t.bitmap.height);h.sample(o),c.push(o)}));const f=h.quantizeSync();for(let t=0;t<s.length;++t){const e=s[t].bitmap.data,r=l.quantizeSync(c[t],f).toUint32Array();for(let t=0,i=0;t<e.length;t+=4,++i)e.writeUInt32LE(r[i],t)}}e.cloneFrames=function(t){let e=[];return t.forEach((t=>{e.push(new s(t))})),e},e.getColorInfo=function(t,e){let r=!1;const i=[];for(let e=0;e<t.length;++e){let n=t[e].getPalette();if(n.usesTransparency&&(r=!0),n.indexCount>256)throw new a(`Frame ${e} uses more than 256 color indexes`);i.push(n)}if(0===e)return{usesTransparency:r,palettes:i};const n=new Set;i.forEach((t=>{t.colors.forEach((t=>{n.add(t)}))}));let o=n.size;if(r&&++o,e&&o>e)return{usesTransparency:r,palettes:i};const s=new Array(n.size),h=n.values();for(let t=0;t<s.length;++t)s[t]=h.next().value;return s.sort(((t,e)=>t-e)),{colors:s,indexCount:o,usesTransparency:r,palettes:i}},e.copyAsJimp=function(t,r){return e.shareAsJimp(t,new o(r))},e.getMaxDimensions=function(t){let e=0,r=0;return t.forEach((t=>{const i=t.xOffset+t.bitmap.width;i>e&&(e=i);const n=t.yOffset+t.bitmap.height;n>r&&(r=n)})),{maxWidth:e,maxHeight:r}},e.quantizeDekker=function(t,e,r){f(t,"NeuQuantFloat",e=e||256,0,r)},e.quantizeSorokin=function(t,e,r,i){let n;switch(e=e||256,r=r||"min-pop"){case"min-pop":n=2;break;case"top-pop":n=1;break;default:throw new Error(`Invalid quantizeSorokin histogram '${r}'`)}f(t,"RGBQuant",e,n,i)},e.quantizeWu=function(t,e,r,i){if(e=e||256,(r=r||5)<1||r>8)throw new Error("Invalid quantization quality");f(t,"WuQuant",e,r,i)},e.read=function(t,e){return e=e||c,Buffer.isBuffer(t)?e.decodeGif(t):(r=t,new Promise(((t,e)=>{i.readFile(r,((r,i)=>r?e(r):t(i)))}))).then((t=>e.decodeGif(t)));var r},e.shareAsJimp=function(t,e){const r=new t(e.bitmap.width,e.bitmap.height,0);return r.bitmap.data=e.bitmap.data,r},e.write=function(t,e,r,n){n=n||c;const o=t.match(/\.[a-zA-Z]+$/);if(null!==o&&l.includes(o[0].toLowerCase()))throw new Error(`GIF '${t}' has an unexpected suffix`);return n.encodeGif(e,r).then((e=>function(t,e){return new Promise(((r,n)=>{i.writeFile(t,e,(t=>t?n(t):r()))}))}(t,e.buffer).then((()=>e))))}},39925:(t,e,r)=>{const i=r(19208),{GifError:n}=r(11658);class o extends i{constructor(...t){if(super(...t),t[0]instanceof o){const e=t[0];this.xOffset=e.xOffset,this.yOffset=e.yOffset,this.disposalMethod=e.disposalMethod,this.delayCentisecs=e.delayCentisecs,this.interlaced=e.interlaced}else{const e=t[t.length-1];let r={};"object"!=typeof e||e instanceof i||(r=e),this.xOffset=r.xOffset||0,this.yOffset=r.yOffset||0,this.disposalMethod=void 0!==r.disposalMethod?r.disposalMethod:o.DisposeToBackgroundColor,this.delayCentisecs=r.delayCentisecs||8,this.interlaced=r.interlaced||!1}}getPalette(){const t=new Set,e=this.bitmap.data;let r=0,i=!1;for(;r<e.length;){if(0===e[r+3])i=!0;else{const i=e.readUInt32BE(r,!0)>>8&16777215;t.add(i)}r+=4}const n=new Array(t.size),o=t.values();for(r=0;r<n.length;++r)n[r]=o.next().value;n.sort(((t,e)=>t-e));let s=n.length;return i&&++s,{colors:n,usesTransparency:i,indexCount:s}}}o.DisposeToAnything=0,o.DisposeNothing=1,o.DisposeToBackgroundColor=2,o.DisposeToPrevious=3,e.GifFrame=o}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3814],{67064:(e,s,t)=>{t.d(s,{l:()=>r});var a=t(15215),o=t("aurelia-framework"),i=t(38777);let r=class{constructor(){this.messages=[]}toast(e){return this.messages.push(e),e.persist||this.removeAfterDelay(e),e}remove(e){const s=this.messages.findIndex((s=>s===e));-1!==s&&(this.messages.splice(s,1),e.onremove?.())}removeAfterDelay(e,s=!1){e.persist||(this.cancelRemoval(e),e.removeTimeout=(0,i.Ix)((()=>this.remove(e)),s?2500:5e3))}cancelRemoval(e){e.removeTimeout&&(e.removeTimeout?.dispose(),e.removeTimeout=null)}update(e,s){const t=this.messages.findIndex((s=>s===e));if(-1!==t){const e=[...this.messages];e[t]=s,this.messages=e}}};r=(0,a.Cg)([(0,o.singleton)()],r)},"app/toasts":(e,s,t)=>{t.r(s),t.d(s,{ToastSortValueConverter:()=>d,Toasts:()=>r});var a=t(15215),o=t("aurelia-framework"),i=t(67064);let r=class{#e;constructor(e){this.toaster=e,this.isSidebarCollapsed=!1,this.isSidebarForcedCollapsed=!1}attached(){this.#e=new ResizeObserver((()=>this.#s())),this.#e.observe(this.messagesEl),this.#s()}detached(){this.#e.disconnect()}handleClick(e){e.onclick&&e.onclick()}#s(){const e=this.messagesEl.getBoundingClientRect(),s=parseInt(window.getComputedStyle(this.messagesEl).marginBottom)+e.height;document.body.style.setProperty("--toasts-safe-area",`${s}px`)}};(0,a.Cg)([o.bindable,(0,a.Sn)("design:type",Boolean)],r.prototype,"isSidebarCollapsed",void 0),(0,a.Cg)([o.bindable,(0,a.Sn)("design:type",Boolean)],r.prototype,"isSidebarForcedCollapsed",void 0),r=(0,a.Cg)([(0,o.autoinject)(),(0,a.Sn)("design:paramtypes",[i.l])],r);const n=Object.freeze(["arrow-up","arrow-down"]);function g(e){return!!e.type&&n.includes(e.type)}function c(e,s){return(g(e)&&!g(s)?-1:!g(e)&&g(s)?1:0)||(e.lock&&!s.lock?1:!e.lock&&s.lock?-1:0)||(e.persist&&!s.persist?1:!e.persist&&s.persist?-1:0)}class d{toView(e){return e.slice(0).sort(c)}}},"app/toasts.html":(e,s,t)=>{t.r(s),t.d(s,{default:()=>d});var a=t(14385),o=t.n(a),i=new URL(t(16280),t.b),r=new URL(t(16984),t.b),n=new URL(t(86561),t.b),g=new URL(t(76386),t.b),c=new URL(t(34635),t.b);const d='<template class="${!toaster.messages.length ? \'empty\' : \'\'}"> <require from="./toasts.scss"></require> <require from="../resources/elements/alert-icon.html"></require> <div class="messages ${isSidebarCollapsed || isSidebarForcedCollapsed ? \'sidebar-collapsed\' : \'\'}" ref="messagesEl"> <div repeat.for="message of toaster.messages | toastSort" if.bind="!message.persist || (message.persist && !toaster.hidePersistent)" class="message-wrapper au-animate ${message.persist ? \'persist\' : \'\'} ${message.actions.length ? \'has-actions\' : \'\'} ${message.onTop ? \'on-top\' : \'\'} ${message.classNames ? message.classNames : \'\'}"> <div class="message-wrapper-inner"> <div class="message ${message.type} ${message.onclick ? \'clickable\' : \'\'} ${message.hasProBadge ? \'has-pro-badge\' : \'\'}" style.bind="{\'--toast-icon-color\': message.iconColor}" mouseenter.trigger="toaster.cancelRemoval(message)" mouseleave.trigger="toaster.removeAfterDelay(message, true)" click.delegate="handleClick(message)" tabindex.bind="message.onclick ? 0 : -1"> <i if.bind="message.icon" class="icon">${message.icon}</i> <i if.bind="message.type == \'ok\'" class="icon"><inline-svg src="'+o()(i)+'"></inline-svg></i> <i if.bind="message.type == \'boost\'" class="icon"><inline-svg src="'+o()(r)+'"></inline-svg></i> <i if.bind="[\'arrow-up\', \'arrow-down\'].includes(message.type)" class="icon"><inline-svg src="'+o()(n)+'"></inline-svg></i> <i if.bind="message.type === \'gift\'" class="icon"><inline-svg src="'+o()(g)+'"></inline-svg></i> <alert-icon if.bind="[\'alert\', \'warning\'].includes(message.type)" class="icon"></alert-icon> <strong if.bind="message.type == \'tada\'">🎉</strong> <div class="content"> <p innerhtml.bind="message.content | i18n:(message.i18nParams||{}) | markdown"></p> </div> <button if.bind="!message.lock" class="close-button" click.delegate="toaster.remove(message)"> <inline-svg src="'+o()(c)+'"></inline-svg> </button> <button repeat.for="action of message.actions" class="action ${action.primary ? \'primary\' : \'\'}" click.delegate="action.onclick()"> ${action.label | i18n} </button> </div> </div> </div> </div> </template> '},"app/toasts.scss":(e,s,t)=>{t.r(s),t.d(s,{default:()=>m});var a=t(31601),o=t.n(a),i=t(76314),r=t.n(i),n=t(4417),g=t.n(n),c=new URL(t(83959),t.b),d=r()(o()),l=g()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${l}) format("woff2")}.material-symbols-outlined,toasts .messages .message .icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}toasts{transition:opacity .15s,visibility 0s .15s}toasts .scroll-to-top{position:absolute;bottom:0;right:50px}toasts .scroll-to-top .content{display:none}toasts .messages{position:absolute;left:240px;bottom:0;z-index:1001;pointer-events:none;display:inline-flex;flex-direction:column;padding-right:20px;margin-bottom:20px;width:100%}toasts .messages.sidebar-collapsed{left:92px}toasts .messages .message-wrapper{display:inline-block}toasts .messages .message-wrapper+.message-wrapper{margin-top:20px}toasts .messages .message-wrapper-inner{pointer-events:all;display:inline-block}toasts .messages .message{box-shadow:0 0 5px rgba(var(--theme--background--rgb), 0.5);position:relative;border-radius:10px;border:1px solid rgba(255,255,255,.05);padding:11px 15px;text-align:left;display:inline-flex;align-items:center;--toast-icon-color: var(--color--accent);position:relative}.theme-default toasts .messages .message{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro toasts .messages .message{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro toasts .messages .message{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro toasts .messages .message{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro toasts .messages .message{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}toasts .messages .message>*+*{margin-left:9px}toasts .messages .message,toasts .messages .message a{font-size:14px;line-height:21px;line-height:19px;font-weight:500;color:rgba(255,255,255,.5)}toasts .messages .message strong,toasts .messages .message em{font-weight:700;color:#fff;font-style:normal}toasts .messages .message p{margin:0;padding:0}toasts .messages .message p+p{margin-top:10px}toasts .messages .message .action{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}toasts .messages .message .action,toasts .messages .message .action *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) toasts .messages .message .action{border:1px solid #fff}}toasts .messages .message .action>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}toasts .messages .message .action>*:first-child{padding-left:0}toasts .messages .message .action>*:last-child{padding-right:0}toasts .messages .message .action svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) toasts .messages .message .action svg *{fill:CanvasText}}toasts .messages .message .action svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) toasts .messages .message .action svg{opacity:1}}toasts .messages .message .action img{height:50%}toasts .messages .message .action:disabled{opacity:.3}toasts .messages .message .action:disabled,toasts .messages .message .action:disabled *{cursor:default;pointer-events:none}@media(hover: hover){toasts .messages .message .action:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}toasts .messages .message .action:not(:disabled):hover svg{opacity:1}}toasts .messages .message .action:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}toasts .messages .message .action:active{background-color:rgba(0,0,0,0) !important;color:rgba(255,255,255,.8) !important}toasts .messages .message .action.primary{background-color:rgba(255,255,255,.6) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){toasts .messages .message .action.primary:hover{filter:brightness(1.1)}}toasts .messages .message .close-button{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)) !important;display:inline-flex;width:26px;height:26px;box-shadow:none;padding:0;border-radius:50%;border:0;align-items:center;justify-content:center;transition:background-color .15s;position:absolute;right:-12.5px;top:-12.5px}@media(forced-colors: active){body:not(.override-contrast-mode) toasts .messages .message .close-button{border:1px solid #fff}}toasts .messages .message .close-button svg{opacity:1}@media(hover: hover){toasts .messages .message .close-button:hover{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.25)) !important}}toasts .messages .message .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--toast-icon-color)}toasts .messages .message .icon svg *{fill:var(--toast-icon-color)}toasts .messages .message.ok .icon svg *{fill:var(--color--accent)}toasts .messages .message.alert{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}toasts .messages .message.alert,toasts .messages .message.alert a{color:rgba(var(--color--alert--rgb), 0.9)}toasts .messages .message.alert .action{box-shadow:inset 0 0 0 1px var(--color--alert);--cta__icon--color: var(--color--alert)}@media(hover: hover){toasts .messages .message.alert .action:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--alert);background-color:rgba(0,0,0,0)}}toasts .messages .message.alert .action:not(:disabled):active{background-color:var(--color--alert)}toasts .messages .message.alert .close-button{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.2), rgba(var(--color--alert--rgb), 0.2)) !important}@media(hover: hover){toasts .messages .message.alert .close-button:hover{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.3), rgba(var(--color--alert--rgb), 0.3)) !important}}.theme-default toasts .messages .message.warning{background:#191713;border-color:rgba(255,170,43,.05)}.theme-purple-pro toasts .messages .message.warning{background:#1a1616;border-color:rgba(255,170,43,.05)}.theme-green-pro toasts .messages .message.warning{background:#171915;border-color:rgba(255,170,43,.05)}.theme-orange-pro toasts .messages .message.warning{background:#1b1713;border-color:rgba(255,170,43,.05)}.theme-pro toasts .messages .message.warning{background:#16120c;border-color:rgba(255,170,43,.05)}toasts .messages .message.warning .content{color:#fff}toasts .messages .message.warning .content strong{color:var(--color--accent-yellow)}toasts .messages .message.warning alert-icon{background:rgba(var(--color--accent-yellow--rgb), 0.3)}toasts .messages .message.warning alert-icon i svg *{fill:var(--color--accent-yellow)}.theme-default toasts .messages .message.gift{background:#0e2e38;border-color:#106f89}.theme-purple-pro toasts .messages .message.gift{background:#0f2d3a;border-color:#116f8a}.theme-green-pro toasts .messages .message.gift{background:#0c2f3a;border-color:#0f708a}.theme-orange-pro toasts .messages .message.gift{background:#102e38;border-color:#116f89}.theme-pro toasts .messages .message.gift{background:#0b2a31;border-color:#0f6d85}toasts .messages .message.gift .content strong{font-weight:500;color:var(--color--brand-blue)}toasts .messages .message.gift .icon svg *{fill:var(--color--brand-green)}toasts .messages .message.gift .action{box-shadow:inset 0 0 0 1px var(--color--brand-blue);--cta__icon--color: var(--color--brand-blue)}@media(hover: hover){toasts .messages .message.gift .action:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--brand-blue);background-color:rgba(0,0,0,0)}}toasts .messages .message.gift .action:not(:disabled):active{background-color:var(--color--brand-blue)}toasts .messages .message.clickable,toasts .messages .message.clickable *{cursor:pointer}toasts .messages .message.clickable.arrow-up:hover .icon svg *,toasts .messages .message.clickable.arrow-down:hover .icon svg *{fill:#fff}toasts .messages .message.arrow-up .icon svg *,toasts .messages .message.arrow-down .icon svg *{fill:rgba(255,255,255,.6);transition:fill .15s}toasts .messages .message.arrow-up .icon{transform:rotate(180deg)}toasts .messages .message.has-pro-badge .content s{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;vertical-align:middle;text-decoration:none}@media(forced-colors: active){body:not(.override-contrast-mode) toasts .messages .message.has-pro-badge .content s{border:1px solid #fff}}toasts:not(.empty)~.app-layout .app-content>router-view .view-background .view-scrollable{padding-bottom:calc(var(--toasts-safe-area) + 20px)}.ux-dialog-open~toasts .messages .message-wrapper.persist:not(.on-top),.ux-dialog-open~toasts .messages .message-wrapper.has-actions:not(.on-top){display:none !important}.ad-popup-active toasts .messages{max-width:calc(100vw - var(--ad-popup--safe-width) - 20px)}`,""]);const m=d},"assistant/assistant-box":(e,s,t)=>{t.r(s),t.d(s,{AssistantBox:()=>b});var a=t(15215),o=t("aurelia-event-aggregator"),i=t("aurelia-framework"),r=t(20770),n=t(78563),g=t(31051),c=t(44759),d=t(92465),l=t(54995),m=t(14046),p=t(48881);let b=class{#t;#a;#o;#i;#r;#n;constructor(e,s,t,a,o){this.onlineStatus=o,this.isAssistantOpen=!1,this.showIcon=!1,this.hasAskedQuestionThisSession=!1,this.#o=e,this.#i=s,this.#r=t,this.#n=a}attached(){this.#g(),this.assistant.history.onItemFinalized((e=>{"question"===e.item.type&&(this.hasAskedQuestionThisSession=!0)}))}detached(){this.#c(),this.#t?.dispose(),this.#a?.dispose(),document.body.classList.toggle("assistant-open",!1)}async#c(){this.hasAskedQuestionThisSession&&await this.#n.showDialog({trigger:"post_assistant_chat",titleId:this.titleInfo.id??""})}#g(){this.titleInfo.id&&(this.assistant=this.#o.getAssistant(this.titleInfo.id),this.#t=new d.Vd([this.#r.subscribe("close-assistant",(()=>this.toggleAssistant(!1))),this.#r.subscribe("open-assistant",(()=>this.toggleAssistant(!0))),this.#r.subscribe("ask-mission-help",(e=>{this.#a?.dispose(),this.isAssistantOpen?e&&this.assistant.ask(e,"mission-help",!1):(this.toggleAssistant(!0),this.#a=this.assistant.onReady((()=>{e&&this.assistant.ask(e,"mission-help",!1),this.#a?.dispose()})))}))]),setTimeout((()=>{this.showIcon=!0,this.#d()}),500))}toggleAssistant(e){this.isAssistantOpen=e,e||(this.#c(),this.hasAskedQuestionThisSession=!1),e&&!this.popoutHidden&&this.closePopout()}closePopout(e){e?.stopPropagation(),this.#i.dispatch(p.NX,"assistantPopoutHidden",!0),this.#i.dispatch(p.vk,"lastAssistantPopout"),this.#i.dispatch(p.Ew,"timesAssistantPopoutSeen",1)}#d(){let e=!1;if(!this.#o.questionsAsked.length){if(this.lastAssistantPopout){const s=Date.now(),t=new Date(this.lastAssistantPopout),a=(0,m.c_)(s,t);1===this.timesAssistantPopoutSeen&&a>=3&&(e=!0),2===this.timesAssistantPopoutSeen&&a>=7&&(e=!0),3===this.timesAssistantPopoutSeen&&a>=30&&(e=!0)}e&&this.#i.dispatch(p.NX,"assistantPopoutHidden",!1)}}};(0,a.Cg)([i.bindable,(0,a.Sn)("design:type",Object)],b.prototype,"titleInfo",void 0),(0,a.Cg)([i.bindable,(0,a.Sn)("design:type",Boolean)],b.prototype,"isAssistantOpen",void 0),b=(0,a.Cg)([(0,l.m6)({selectors:{popoutHidden:(0,l.$t)((e=>e.flags?.assistantPopoutHidden)),lastAssistantPopout:(0,l.$t)((e=>e.timestamps?.lastAssistantPopout)),timesAssistantPopoutSeen:(0,l.$t)((e=>e.counters?.timesAssistantPopoutSeen)),disableAssistant:(0,l.$t)((e=>e.settings?.disableAssistant))}}),(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[n.s,r.il,o.EventAggregator,g.o,c.WA])],b)},"assistant/assistant-box.html":(e,s,t)=>{t.r(s),t.d(s,{default:()=>a});const a='<template class="${disableAssistant ? \'disabled\' : \'\'} ${isAssistantOpen ? \'assistant-open\' : \'\'}"> <require from="./assistant-box.scss"></require> <require from="../shared/assistant/assistant-chat"></require> <div class="header" click.delegate="!isAssistantOpen ? null : toggleAssistant(false)"> <i class="game-icon"></i> <span>${\'assistant_box.game_guide\' | i18n}</span> <span class="beta-badge">${\'assistant_box.beta\' | i18n}</span> <i if.bind="isAssistantOpen" class="minimize-icon" tabindex="0"></i> </div> <div class="assistant-interface"> <assistant-chat if.bind="isAssistantOpen" assistant.bind="assistant" title-id.bind="titleInfo.id" client="desktop" close.call="toggleAssistant(false)"> </assistant-chat> <div else class="input-prompt-wrapper" click.delegate="toggleAssistant(!isAssistantOpen)"> <button class="input-placeholder" disabled.bind="disableAssistant"> ${\'assistant_chat.ask_anything\' | i18n} </button> <button class="submit-button" disabled.bind="disableAssistant">↑</button> </div> </div> </template> '},"assistant/assistant-box.scss":(e,s,t)=>{t.r(s),t.d(s,{default:()=>m});var a=t(31601),o=t.n(a),i=t(76314),r=t.n(i),n=t(4417),g=t.n(n),c=new URL(t(83959),t.b),d=r()(o()),l=g()(c);d.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}ux-dialog.basic-dialog.trainer-notes-dialog ux-dialog-body img{max-width:70%}section.game .game-layout{min-height:100%}section.game .game-info,section.game .creator-tools{display:flex;flex-direction:column;gap:12px}section.game .game-info section,section.game .creator-tools section{display:flex;padding:16px 24px;background:rgba(255,255,255,.03);border-radius:16px;gap:12px;align-items:center}section.game .game-info button,section.game .creator-tools button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}section.game .game-info button,section.game .game-info button *,section.game .creator-tools button,section.game .creator-tools button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .game-info button,body:not(.override-contrast-mode) section.game .creator-tools button{border:1px solid #fff}}section.game .game-info button>*,section.game .creator-tools button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}section.game .game-info button>*:first-child,section.game .creator-tools button>*:first-child{padding-left:0}section.game .game-info button>*:last-child,section.game .creator-tools button>*:last-child{padding-right:0}section.game .game-info button svg,section.game .creator-tools button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .game-info button svg *,body:not(.override-contrast-mode) section.game .creator-tools button svg *{fill:CanvasText}}section.game .game-info button svg *,section.game .creator-tools button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .game-info button svg,body:not(.override-contrast-mode) section.game .creator-tools button svg{opacity:1}}section.game .game-info button img,section.game .creator-tools button img{height:50%}section.game .game-info button:disabled,section.game .creator-tools button:disabled{opacity:.3}section.game .game-info button:disabled,section.game .game-info button:disabled *,section.game .creator-tools button:disabled,section.game .creator-tools button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){section.game .game-info button:not(:disabled):hover,section.game .creator-tools button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}section.game .game-info button:not(:disabled):hover svg,section.game .creator-tools button:not(:disabled):hover svg{opacity:1}}section.game .game-info button:not(:disabled):active,section.game .creator-tools button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){section.game .game-info button:not(:disabled):hover,section.game .creator-tools button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}section.game .game-info button:not(:disabled):active,section.game .creator-tools button:not(:disabled):active{background-color:var(--theme--highlight)}section.game .creator-tools section{margin-bottom:12px}section.game .error-message-wrapper{display:flex;flex:1;align-items:center;justify-content:center;text-align:center}section.game .error-message-wrapper .error-message{margin:100px 0}section.game .error-message-wrapper .message{font-size:14px;line-height:21px;font-weight:700;color:#fff;margin-bottom:10px}section.game .error-message-wrapper .message alert-icon{margin-right:10px}section.game .error-message-wrapper button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--color--alert);--cta__icon--color: var(--color--alert)}section.game .error-message-wrapper button,section.game .error-message-wrapper button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .error-message-wrapper button{border:1px solid #fff}}section.game .error-message-wrapper button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}section.game .error-message-wrapper button>*:first-child{padding-left:0}section.game .error-message-wrapper button>*:last-child{padding-right:0}section.game .error-message-wrapper button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .error-message-wrapper button svg *{fill:CanvasText}}section.game .error-message-wrapper button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) section.game .error-message-wrapper button svg{opacity:1}}section.game .error-message-wrapper button img{height:50%}section.game .error-message-wrapper button:disabled{opacity:.3}section.game .error-message-wrapper button:disabled,section.game .error-message-wrapper button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){section.game .error-message-wrapper button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}section.game .error-message-wrapper button:not(:disabled):hover svg{opacity:1}}section.game .error-message-wrapper button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){section.game .error-message-wrapper button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--alert);background-color:rgba(0,0,0,0)}}section.game .error-message-wrapper button:not(:disabled):active{background-color:var(--color--alert)}section.game .info-sections{display:none}section.game .info-sections:has(section){display:flex;flex-direction:column;gap:10px;overflow:hidden;margin:0px 0px 20px 0px}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${l}) format("woff2")}.material-symbols-outlined,assistant-box .header .game-icon,assistant-box .header .minimize-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}assistant-box{display:flex;flex-direction:column;padding:0px;margin:0px;align-items:flex-start;gap:0px;width:100%;height:112px;transition:height .3s}assistant-box.assistant-open{height:100vh;gap:1px}assistant-box.assistant-open .assistant-interface{padding:0px}assistant-box.assistant-open .header:hover{background:linear-gradient(0deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.03) 100%),rgba(var(--theme--background-accent--rgb), 0.9)}assistant-box.assistant-open .header:hover,assistant-box.assistant-open .header:hover *{cursor:pointer}assistant-box.assistant-open .header:hover .minimize-icon{color:rgba(255,255,255,.5)}assistant-box .header{display:flex;padding:12px 16px;align-items:center;gap:12px;min-height:48px;color:rgba(255,255,255,.8);font-size:14px;font-weight:700;background:rgba(19,20,23,.9);width:100%;border-top-left-radius:16px;border-top-right-radius:16px}assistant-box .header .game-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:rgba(255,255,255,.8);font-size:14px}assistant-box .header .game-icon:before{font-family:inherit;content:"stadia_controller"}assistant-box .header .minimize-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:rgba(255,255,255,.3);font-size:16px;margin-left:auto}assistant-box .header .minimize-icon:before{font-family:inherit;content:"unfold_less"}assistant-box .header .beta-badge{display:flex;gap:2px;color:#fff;font-weight:700;line-height:16px;letter-spacing:.5px;font-size:10px;padding:0px 4px;align-items:center;border-radius:4px;background:linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),#6046ff;text-transform:uppercase}assistant-box .assistant-interface{display:flex;width:100%;height:100%;background:rgba(19,20,23,.9);border-bottom-left-radius:16px;border-bottom-right-radius:16px;padding:0px 16px 16px 16px}assistant-box .assistant-interface .input-prompt-wrapper{min-height:0px}assistant-box .input-prompt-wrapper{display:flex;flex:1 0 0;align-self:end;justify-content:space-between;height:48px;padding:8px 8px 8px 16px;align-items:center;gap:8px;border-radius:28px;background:rgba(255,255,255,.05);width:100%}assistant-box .input-prompt-wrapper:hover{background:rgba(255,255,255,.1)}assistant-box .input-prompt-wrapper,assistant-box .input-prompt-wrapper *{cursor:pointer}assistant-box .input-prompt-wrapper .input-placeholder{appearance:none;-webkit-appearance:none;background:rgba(0,0,0,0);border:none;box-shadow:none;outline:none;color:rgba(255,255,255,.3);font-size:14px;font-style:normal;font-weight:500}assistant-box .input-prompt-wrapper .submit-button{appearance:none;-webkit-appearance:none;border:none;box-shadow:none;outline:none;display:flex;width:32px;height:32px;padding:4px;justify-content:center;align-items:center;gap:10px;border-radius:28px;background:rgba(255,255,255,.15);color:rgba(255,255,255,.6);text-align:center;font-size:20px;font-weight:500;line-height:20px}assistant-box assistant-chat{height:100%;width:100%;padding:0px;margin:0px;border-bottom-right-radius:16px;border-bottom-left-radius:16px}assistant-box assistant-chat button.close-button{display:none}assistant-box.disabled{visibility:hidden}`,""]);const m=d}}]);
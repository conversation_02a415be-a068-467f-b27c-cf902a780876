"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2991],{7892:(e,t,s)=>{s.d(t,{p:()=>g});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),r=s(83802),o=s(20057),h=s(54995),d=s(38777),l=s(72208),c=s(56705),u=s(45953);let g=class{#e;#t;#s;#i;#a;#n;#r;#o;constructor(e,t,s,i,a,n){this.#e=e,this.#t=t,this.#s=s,this.#i=i,this.#a=a,this.#n=n}attached(){this.#o=(new d.Vd).push(this.#n.onVisibleTrainerChanged((e=>{this.#r=e,this.#h()}))).push(this.#e.onNewTrainer((e=>{const t=e.getMetadata(r.vO);this.#r=t.info,this.#h()}))).push(this.#s.onLocaleChanged((()=>this.#h())))}detached(){this.#o?.dispose(),this.#o=null}cheatBlueprintInstructionsReadChanged(){this.#h()}gamePreferencesChanged(){this.#h()}pinnedModsChanged(){this.#h("pinnedMods")}modTimersChanged(){this.#h()}#h(e){const t={};if(!this.#r)return;const s=(0,c.o)(this.#r.blueprint),i=s&&this.#a.enabledForGame(this.#r.gameId),a=this.gamePreferences[this.#r.gameId]?.saveCheats,n=this.gamePreferences[this.#r.gameId]?.customHotkeys;this.#r.blueprint.cheats.forEach((e=>{const r=s&&this.#a.cheatSupportsActivateOnLoad(e),o=this.modTimers?.[this.#r.gameId]?.[e.uuid];t[e.uuid]={pinned:this.pinnedMods?.[this.#r.gameId]?.some((t=>t?.uuid===e?.uuid))||!1,pinnedOrder:this.pinnedMods?.[this.#r.gameId]?.findIndex((t=>t?.uuid===e?.uuid))??-1,instructionsRead:this.#i.areInstructionsRead(e.uuid,e.instructions??""),saveCheats:{enabled:r&&i,supported:r,value:r&&i&&a?a.trainerState[e.target]:void 0},hotkeys:e.hotkeys.map(((t,s)=>n?n[e.uuid]?.[s]??t:t)),timer:o?{timestamp:o.timestamp,duration:o.duration,type:o.type,start:o.start,end:o.end}:void 0}})),this.cheatStates=t,this.#t.publish("cheat-states",{gameId:this.#r.gameId,states:t,dep:e})}onCheatStatesChanged(e){return this.#t.subscribe("cheat-states",e)}};g=(0,i.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{cheatBlueprintInstructionsRead:(0,h.$t)((e=>e.cheatBlueprintInstructionsRead)),gamePreferences:(0,h.$t)((e=>e.gamePreferences)),pinnedMods:(0,h.$t)((e=>e.pinnedMods)),modTimers:(0,h.$t)((e=>e.modTimers))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[r.jR,a.EventAggregator,o.F2,l.u,c.Q,u.m])],g)},13625:(e,t,s)=>{s.d(t,{E:()=>g});var i=s(15215),a=s("aurelia-framework"),n=s(96610),r=s(20489),o=s(24297),h=s(83802);const d=(0,n.getLogger)("trainer"),l={[r.FX.AcquiringBinary]:"Acquiring trainer binary...",[r.FX.EnsuringBinaryAccess]:"Ensuring trainer binary access...",[r.FX.CheckingInternalBinaries]:"Validating internal libraries...",[r.FX.FindingProcess]:"Finding or launching process...",[r.FX.ValidatingProcess]:"Validating target process...",[r.FX.CreatingTrainerHost]:"Creating trainer host...",[r.FX.Injecting]:"Injecting dependencies into process...",[r.FX.InitializingIpc]:"Initializing IPC channels...",[r.FX.Executing]:"Executing trainer...",[r.FX.Connecting]:"Waiting for connection...",[r.FX.Activating]:"Activating trainer...",[r.FX.Active]:"Ready for user input.",[r.FX.Ended]:"Trainer ended."},c={[r.KC.ElevationDenied]:"Trainer elevation denied.",[r.KC.Canceled]:"Trainer launch canceled.",[r.KC.TimedOut]:"Trainer launch timed out.",[r.KC.Incompatible]:"Trainer was built for a newer version of the app.",[r.KC.GameAlreadyRunning]:"Game was already running, but an internal launch is required.",[r.KC.GameNotRunning]:"Game was not running, but an external launch is required.",[r.KC.Error]:"Failed to launch trainer."},u={0:"trainer",2:"GUI control",1:"hotkey",3:"remote",4:"overlay",6:"overlay_native",5:"save mods",7:"mod timer"};let g=class{#e;#d;constructor(e){this.#e=e}attached(){this.#d=this.#e.onNewTrainer((e=>this.#l(e)))}detached(){this.#d?.dispose(),this.#d=null}async#l(e){e.onStateChanged((e=>{const t=l[e];t&&d.info(t)})),e.onEnded((()=>{const t=c[e.launchResult??""];t&&d.warn(t)})),e.onValueSet((e=>{d.info(`${e.name} set to ${e.value} by ${u[e.source]}`)})),e.onLogMessage((e=>{switch(e.level){case o.$b.Info:d.info(e.message);break;case o.$b.Warning:d.warn(e.message);break;case o.$b.Error:d.error(e.message);break;default:d.debug(e.message)}}))}};g=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[h.jR])],g)},45953:(e,t,s)=>{s.d(t,{m:()=>h});var i=s(15215),a=s("aurelia-framework"),n=s(27958),r=s(83802),o=s(38777);let h=class{#t;#c;constructor(e,t){this.#t=new o._M,e.onNewTrainer(this.#u.bind(this)),e.onTrainerEnded(this.#g.bind(this)),this.#c=t}attached(){}detached(){this.#t.dispose()}setVisibleTrainer(e){this.visibleTrainer=e,this.#t.publish("visible",this.visibleTrainer),this.runningTrainer||(this.displayTrainer=e,this.#t.publish("display",this.displayTrainer))}#u(e){const t=e.getMetadata(r.vO).info;this.runningTrainer={info:t,instance:e},this.#t.publish("running",this.runningTrainer),this.displayTrainer=t,this.#t.publish("display",this.displayTrainer)}#g(){this.runningTrainer=null,this.displayTrainer=this.visibleTrainer,this.#t.publish("display",this.displayTrainer),this.#t.publish("running",null)}onDisplayTrainerChanged(e){return this.#t.subscribe("display",e)}onVisibleTrainerChanged(e){return this.#t.subscribe("visible",e)}onRunningTrainerChanged(e){return this.#t.subscribe("running",e)}async cancelVisibleTrainer(){await(0,o.Wn)(),"title"===this.#c.router.currentInstruction.config.name&&this.#c.router.currentInstruction.queryParams.trainerId?(await(0,o.Wn)(500),this.visibleTrainer?.id||this.setVisibleTrainer(null)):this.setVisibleTrainer(null)}};h=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.jR,n.L])],h)},49857:(e,t,s)=>{s.d(t,{b:()=>p});var i=s(15215),a=s("aurelia-framework"),n=s(20770),r=s(59239),o=s(88120),h=s(83802),d=s(3972),l=s(70236),c=s(48881),u=s(38777),g=s(43570);let p=class{#e;#p;#v;#m;#d;#b;constructor(e,t,s,i){this.#e=e,this.#p=t,this.#v=s,this.#m=i}attached(){this.#d=this.#e.onTrainerActivated(this.#y.bind(this))}detached(){this.#d?.dispose(),this.#d=null}#y(e){const t=e.getMetadata(h.vO);let s,i,a;const n=this.#m.state.pipe((0,r.E)("gamePreferences",t.info.gameId,"customHotkeys"),(0,o.F)()).subscribe((e=>s=e??{})),d=this.#m.state.pipe((0,r.E)("flags","hasUsedHotkeys"),(0,o.F)()).subscribe((e=>i=e)),l=this.#m.state.pipe((0,r.E)("settings","disableHotkeys"),(0,o.F)()).subscribe((e=>a=e)),g=this.#m.state.pipe((0,r.E)("account","subscription"),(0,o.F)()).subscribe((e=>{!e&&a&&this.#m.dispatch(c.Kc,{disableHotkeys:!1},"trainer_hotkeys",!0),this.#b=!!e})),p=(new u.Vd).pushFunction((()=>n.unsubscribe())).pushFunction((()=>d.unsubscribe())).pushFunction((()=>l.unsubscribe())).pushFunction((()=>g.unsubscribe())).push(this.#v.onHotkeyPress((n=>{e.isActive()&&!a&&this.#C(e,t.info.blueprint,s,n)&&!i&&this.#m.dispatch(c.NX,"hasUsedHotkeys",!0)})));e.onEnded((()=>p.dispose()))}#C(e,t,s,i){const a=this.#T(t,s,i),n=e.getMetadata(h.vO);if(0===a.length)return!1;const r=new Set;for(const t of a){const s=t.cheat.uuid;let i=null;const a=(e,t)=>"toggle"===t&&void 0===e||void 0!==e;if((!t.cheat.parent||this.#b)&&!(n?.info?.brokenCheatUuids??[]).includes(s)&&a(t.cheat.args,t.cheat.type)){switch(t.cheat.type){case"button":i=this.#f(e,t.cheat.target,t.cheat.args,s);break;case"toggle":i=this.#S(e,t.cheat.target,s);break;case"number":case"slider":i=this.#I(e,t.cheat.target,t.cheat.args,t.hotkeyIndex,s);break;case"scalar":i=this.#k(e,t.cheat.target,t.cheat.args,t.hotkeyIndex,s);break;case"selection":i=this.#w(e,t.cheat.target,t.cheat.args,t.hotkeyIndex,s)}null===i||r.has(i)||(r.add(i),this.#p.play(i))}}return!0}#T(e,t,s){let i=[],a=0;for(const n of e.cheats)if(!(0,l.Lt)(n.flags,2)||this.#b)for(let e=0;e<n.hotkeys.length;e++){const r=(t[n.uuid]||{})[e];if(null===r)continue;const o=r||n.hotkeys[e];0!==o.length&&0!==o[0]&&o.every((e=>s.includes(e)))&&(o.length>a?(i=[{cheat:n,hotkeyIndex:e}],a=o.length):o.length===a&&i.push({cheat:n,hotkeyIndex:e}))}return i}#f(e,t,s,i){return e.setValue(t,s.value,1,i),g.A.Execute}#S(e,t,s){return e.getValue(t)?(e.setValue(t,!1,1,s),g.A.Disable):(e.setValue(t,!0,1,s),g.A.Enable)}#k(e,t,s,i,a){const n=s.default?Number(s.default):s.options[Math.floor(s.options.length/2)],r=s.options.indexOf(e.getValue(t)??n);let o=null,h=e.getValue(t)??n;return r+1>=s.options.length&&1===i||r-1<0&&0===i?(o=g.A.Limit,o):(0===i?(h=s.options[r-1],o=g.A.Decrease):1===i&&(h=s.options[r+1],o=g.A.Increase),e.setValue(t,h,1,a),o)}#I(e,t,s,i,a){const n=e.getValue(t)??Math.min(s.max,Math.max(0,s.min));let r=n;0===i?r-=s.step:1===i&&(r+=s.step);const o=s.step.toString(),h=o.indexOf(".");-1!==h&&(r=parseFloat(r.toFixed(o.length-h-1))),r=Math.max(r,s.min),r=Math.min(r,s.max);let d=null;return r===n?d=g.A.Limit:(r>n&&(d=g.A.Increase),r<n&&(d=g.A.Decrease),e.setValue(t,r,1,a)),d}#w(e,t,s,i,a){let n=null,r=e.getValue(t)??-1;if(0===i)--r<0&&(r=s.options.length-1),n=g.A.Decrease;else{if(1!==i)return null;++r>s.options.length-1&&(r=0),n=g.A.Increase}return e.setValue(t,r,1,a),n}};p=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[h.jR,g.L,d.Mz,n.il])],p)},50260:(e,t,s)=>{s.d(t,{m:()=>h});var i=s(15215),a=s("aurelia-framework"),n=s(54995),r=s(85805),o=s(45953);let h=class{#n;#V;#d;constructor(e,t){this.#n=e,this.#V=t}attached(){this.#d=this.#n.onDisplayTrainerChanged(this.#P.bind(this))}#P(e){e?this.#V.setCurrentTrainer({trainerId:e.id,gameId:e.gameId,supportedVersions:e.supportedVersions},this.#n.runningTrainer?.instance):this.#V.setCurrentTrainer(null)}detached(){this.#d?.dispose(),this.#d=null,this.#V.setCurrentTrainer(null)}};h=(0,i.Cg)([(0,a.autoinject)(),(0,n.m6)({setup:"attached",teardown:"detached",selectors:{catalog:(0,n.$t)((e=>e.catalog))}}),(0,i.Sn)("design:paramtypes",[o.m,r.e])],h)},60796:(e,t,s)=>{s.d(t,{G:()=>l,Q:()=>c});var i=s(15215),a=s("aurelia-framework"),n=s(20770),r=s(34884),o=s(83802),h=s(48881),d=s(85805);class l{constructor(e,t){this.startedAt=e,this.secondsPlayed=t}}let c=class{#e;#m;#V;#d;constructor(e,t,s){this.#e=e,this.#m=t,this.#V=s}attached(){this.#d=this.#e.onTrainerActivated((e=>this.#y(e)))}detached(){this.#d?.dispose(),this.#d=null}#y(e){const t=e.getMetadata(o.vO),s=new Date;let i=!1;e.onValueSet((e=>{3===e.source&&(i=!0)})),e.onEnded((()=>{const a=(0,r.A)(s,Date.now());e.addMetadata(new l(s,a)),this.#m.dispatch(h.ui,t.info.gameId,s.toISOString(),a),i&&this.#V.sendGamePlayed(t.info.gameId,s.toISOString(),a)}))}};c=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[o.jR,n.il,d.e])],c)},84847:(e,t,s)=>{s.d(t,{R:()=>l});var i=s(15215),a=s("aurelia-framework"),n=s(20770),r=s(83802),o=s(54995),h=s(48881);const d=[2,4,6,3];let l=class{#e;#m;#A;constructor(e,t){this.#e=e,this.#m=t}attached(){this.#A=this.#e.onTrainerActivated((e=>{this.hasUsedInteractiveControls||e.onValueSet((e=>{d.includes(e.source)&&this.#F(),[4,6].includes(e.source)&&this.#M()}))}))}detached(){this.#A&&(this.#A.dispose(),this.#A=null)}#F(){this.hasUsedInteractiveControls||this.#m.dispatch(h.NX,"hasUsedInteractiveControls",!0)}#M(){this.hasUsedOverlay||this.#m.dispatch(h.NX,"hasUsedOverlay",!0)}};l=(0,i.Cg)([(0,o.m6)({setup:"attached",teardown:"detached",selectors:{hasUsedInteractiveControls:(0,o.$t)((e=>e.flags?.hasUsedInteractiveControls)),hasUsedOverlay:(0,o.$t)((e=>e.flags?.hasUsedOverlay))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.jR,n.il])],l)},91736:(e,t,s)=>{s.d(t,{S:()=>d});var i=s(15215),a=s("aurelia-framework"),n=s(20489),r=s(83802),o=s(14240),h=s(56669);let d=class{#e;#d;constructor(e){this.#e=e}attached(){this.#d=this.#e.onNewTrainer((e=>this.#u(e)))}detached(){this.#d?.dispose(),this.#d=null}#u(e){const t=e.getMetadata(r.vO);if("trainerlib"!==t.info.loader)return;const s=new o.y(t);s.event(h.Gp.PlayPressed),e.addMetadata(s),e.onStateChanged((t=>{t===n.FX.Injecting&&s.event(h.Gp.GameFound,{process_name:e.process?.imagePath.substring(e.process.imagePath.lastIndexOf("\\")+1)??""})})),e.onActivated((async()=>{s.event(h.Gp.TrainerInitialized)})),e.onLogMessage((e=>{s.event(h.Gp.LogMessage,{level:e.level,message:e.message})})),e.onValueSet((e=>{s.event(h.Gp.ValueChanged,{source:{1:"app",2:"app",0:"trainer",3:"remote",4:"overlay",6:"overlay_native",5:"save_cheats",7:"mod_timer"}[e.source],name:e.name,old_value:e.oldValue,new_value:e.value})})),e.onEnded((()=>{s.event(h.Gp.TrainerEnd)}))}};d=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[r.jR])],d)}}]);
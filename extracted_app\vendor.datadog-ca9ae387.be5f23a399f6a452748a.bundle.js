"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1147],{16059:(e,t,n)=>{n.d(t,{sA:()=>q});var s=n(7328),o=n(42182),i=n(80886),u=n(93001),a=n(29336),r=n(36289),d=n(51666),c=n(68393);const l=80*r._m,f=32,b=3*r.iH,g=a.iW,y=a.OY;function m(e,t,n,s,o){0===t.transportStatus&&0===t.queuedPayloads.size()&&t.bandwidthMonitor.canHandle(e)?h(e,t,n,{onSuccess:()=>v(0,t,n,s,o),onFailure:()=>{t.queuedPayloads.enqueue(e),p(t,n,s,o)}}):t.queuedPayloads.enqueue(e)}function p(e,t,n,s){2===e.transportStatus&&(0,u.wg)((()=>{h(e.queuedPayloads.first(),e,t,{onSuccess:()=>{e.queuedPayloads.dequeue(),e.currentBackoffTime=y,v(1,e,t,n,s)},onFailure:()=>{e.currentBackoffTime=Math.min(g,2*e.currentBackoffTime),p(e,t,n,s)}})}),e.currentBackoffTime)}function h(e,t,n,{onSuccess:s,onFailure:o}){t.bandwidthMonitor.add(e),n(e,(n=>{t.bandwidthMonitor.remove(e),function(e){return"opaque"!==e.type&&(0===e.status&&!navigator.onLine||408===e.status||429===e.status||(0,d.G)(e.status))}(n)?(t.transportStatus=t.bandwidthMonitor.ongoingRequestCount>0?1:2,e.retry={count:e.retry?e.retry.count+1:1,lastFailureStatus:n.status},o()):(t.transportStatus=0,s())}))}function v(e,t,n,s,o){0===e&&t.queuedPayloads.isFull()&&!t.queueFullReported&&(o({message:`Reached max ${s} events size queued for upload: ${b/r.iH}MiB`,source:c.g.AGENT,startClocks:(0,a.M8)()}),t.queueFullReported=!0);const i=t.queuedPayloads;for(t.queuedPayloads=C();i.size()>0;)m(i.dequeue(),t,n,s,o)}function C(){const e=[];return{bytesCount:0,enqueue(t){this.isFull()||(e.push(t),this.bytesCount+=t.bytesCount)},first:()=>e[0],dequeue(){const t=e.shift();return t&&(this.bytesCount-=t.bytesCount),t},size:()=>e.length,isFull(){return this.bytesCount>=b}}}function q(e,t,n){const i={transportStatus:0,currentBackoffTime:y,bandwidthMonitor:{ongoingRequestCount:0,ongoingByteCount:0,canHandle(e){return 0===this.ongoingRequestCount||this.ongoingByteCount+e.bytesCount<=l&&this.ongoingRequestCount<f},add(e){this.ongoingRequestCount+=1,this.ongoingByteCount+=e.bytesCount},remove(e){this.ongoingRequestCount-=1,this.ongoingByteCount-=e.bytesCount}},queuedPayloads:C(),queueFullReported:!1},u=(n,s)=>function(e,t,n,s){if(function(){try{return window.Request&&"keepalive"in new Request("http://a")}catch(e){return!1}}()&&n.bytesCount<t){const t=e.build("fetch",n);fetch(t,{method:"POST",body:n.data,keepalive:!0,mode:"cors"}).then((0,o.dm)((e=>null==s?void 0:s({status:e.status,type:e.type}))),(0,o.dm)((()=>{B(e.build("xhr",n),n.data,s)})))}else B(e.build("xhr",n),n.data,s)}(e,t,n,s);return{send:t=>{m(t,i,u,e.trackType,n)},sendOnExit:n=>{!function(e,t,n){if(!!navigator.sendBeacon&&n.bytesCount<t)try{const t=e.build("beacon",n);if(navigator.sendBeacon(t,n.data))return}catch(e){!function(e){w||(w=!0,(0,s.VJ)(e))}(e)}B(e.build("xhr",n),n.data)}(e,t,n)}}}let w=!1;function B(e,t,n){const s=new XMLHttpRequest;s.open("POST",e,!0),t instanceof Blob&&s.setRequestHeader("Content-Type",t.type),(0,i.q)({allowUntrustedEvents:!0},s,"loadend",(()=>{null==n||n({status:s.status})}),{once:!0}),s.send(t)}},55407:(e,t,n)=>{n.d(t,{Ww:()=>i,Y9:()=>o,d0:()=>u});var s=n(54564);function o(){const e=(0,s.V)().DatadogEventBridge;if(e)return{getCapabilities(){var t;return JSON.parse((null===(t=e.getCapabilities)||void 0===t?void 0:t.call(e))||"[]")},getPrivacyLevel(){var t;return null===(t=e.getPrivacyLevel)||void 0===t?void 0:t.call(e)},getAllowedWebViewHosts:()=>JSON.parse(e.getAllowedWebViewHosts()),send(t,n,s){const o=s?{id:s}:void 0;e.send(JSON.stringify({eventType:t,event:n,view:o}))}}}function i(e){const t=o();return!!t&&t.getCapabilities().includes(e)}function u(e){var t;void 0===e&&(e=null===(t=(0,s.V)().location)||void 0===t?void 0:t.hostname);const n=o();return!!n&&n.getAllowedWebViewHosts().some((t=>e===t||e.endsWith(`.${t}`)))}},85090:(e,t,n)=>{n.d(t,{Z:()=>g});var s=n(32234),o=n(19642),i=n(39377),u=n(84601),a=n(36289);function r({encoder:e,request:t,flushController:n,messageBytesLimit:r}){let c={};const l=n.flushObservable.subscribe((n=>function(n){const s=(0,o.KQ)(c).join("\n");c={};const u=(0,i.Kp)(n.reason),r=u?t.sendOnExit:t.send;if(u&&e.isAsync){const t=e.finishSync();t.outputBytesCount&&r(d(t));const n=[t.pendingData,s].filter(Boolean).join("\n");n&&r({data:n,bytesCount:(0,a.WW)(n)})}else s&&e.write(e.isEmpty?s:`\n${s}`),e.finish((e=>{r(d(e))}))}(n)));function f(t,o){const i=(0,u.s)(t),a=e.estimateEncodedBytesCount(i);a>=r?s.Vy.warn(`Discarded a message whose size was bigger than the maximum allowed size ${r}KB. ${s.xG} ${s.Xs}/#technical-limitations`):(function(e){return void 0!==e&&void 0!==c[e]}(o)&&function(t){const s=c[t];delete c[t];const o=e.estimateEncodedBytesCount(s);n.notifyAfterRemoveMessage(o)}(o),function(t,s,o){n.notifyBeforeAddMessage(s),void 0!==o?(c[o]=t,n.notifyAfterAddMessage()):e.write(e.isEmpty?t:`\n${t}`,(e=>{n.notifyAfterAddMessage(e-s)}))}(i,a,o))}return{flushController:n,add:f,upsert:f,stop:l.unsubscribe}}function d(e){let t;return t="string"==typeof e.output?e.output:new Blob([e.output],{type:"text/plain"}),{data:t,bytesCount:e.outputBytesCount,encoding:e.encoding}}var c=n(16059),l=n(92555),f=n(93001);function b({messagesLimit:e,bytesLimit:t,durationLimit:n,pageExitObservable:s,sessionExpireObservable:o}){const i=s.subscribe((e=>b(e.reason))),u=o.subscribe((()=>b("session_expire"))),a=new l.c((()=>()=>{i.unsubscribe(),u.unsubscribe()}));let r,d=0,c=0;function b(e){if(0===c)return;const t=c,n=d;c=0,d=0,g(),a.notify({reason:e,messagesCount:t,bytesCount:n})}function g(){(0,f.DJ)(r),r=void 0}return{flushObservable:a,get messagesCount(){return c},notifyBeforeAddMessage(e){d+e>=t&&b("bytes_limit"),c+=1,d+=e,void 0===r&&(r=(0,f.wg)((()=>{b("duration_limit")}),n))},notifyAfterAddMessage(n=0){d+=n,c>=e?b("messages_limit"):d>=t&&b("bytes_limit")},notifyAfterRemoveMessage(e){d-=e,c-=1,0===c&&g()}}}function g(e,t,n,s,o,i,u=r){const a=l(e,t),d=n&&l(e,n);function l(e,{endpoint:t,encoder:n}){return u({encoder:n,request:(0,c.sA)(t,e.batchBytesLimit,s),flushController:b({messagesLimit:e.batchMessagesLimit,bytesLimit:e.batchBytesLimit,durationLimit:e.flushTimeout,pageExitObservable:o,sessionExpireObservable:i}),messageBytesLimit:e.messageBytesLimit})}return{flushObservable:a.flushController.flushObservable,add(e,t=!0){a.add(e),d&&t&&d.add(n.transformMessage?n.transformMessage(e):e)},upsert:(e,t)=>{a.upsert(e,t),d&&d.upsert(n.transformMessage?n.transformMessage(e):e,t)},stop:()=>{a.stop(),d&&d.stop()}}}}}]);
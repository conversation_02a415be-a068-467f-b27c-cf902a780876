"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[843,8036],{19072:(t,e,a)=>{a.d(e,{S:()=>p,s:()=>h});var n=a("aurelia-event-aggregator"),s=a(96610),i=a(84157),r=a(16953),o=a(49442),c=a(38777);const l=(0,s.getLogger)("host");let u;function p(){return u}class h{constructor(){this.#t=new n.EventAggregator,this.#e=[],this.#a=[]}#t;#n;#e;#a;#s;#i;#r;#o;#c;static async create(){return u||(u=new h,await u.#l()),u}async#l(){const t=await this.#u("ACTION_INITIALIZE_APP");if(this.info=t.app,this.updateState=t.updateState,this.isInTraySinceStartup=t.startedInTray,this.#p(t.window),t.envOverride&&(0,r.h)(t.envOverride),this.#s=r.A.releaseFeed,this.#i=`${r.A.storageNamespace}:squirrelChannel`,this.info.updaterAvailable){const t=localStorage.getItem(this.#i)||this.info.releaseChannel;t&&this.setUpdateChannel(t)}this.#h("EVENT_APP_ACTIVATED"),this.#h("EVENT_CLOSED_TO_TRAY"),this.#h("EVENT_WEBVIEW_WINDOW_LOAD_FINISH"),this.#h("EVENT_WEBVIEW_WINDOW_LOAD_ERROR"),this.#h("EVENT_WEBVIEW_WINDOW_CLOSED"),this.#h("EVENT_OVERLAY_WINDOW_CLOSED"),this.#h("EVENT_OVERLAY_HOTKEY_PRESSED"),this.#h("EVENT_OVERLAY_GRAPHICS_INITIALIZED"),this.#h("EVENT_OVERLAY_GRAPHICS_HOOKED"),this.#h("EVENT_CAPTURE_REPLAY_BUFFER_SAVED"),this.#d("EVENT_APP_QUIT_REQUESTED",(()=>this.#g())),this.#d("EVENT_RESTORED_FROM_TRAY",(()=>this.#f())),this.#d("EVENT_NEW_WINDOW_REQUESTED",(t=>t?this.#y(t):null)),this.#d("EVENT_WINDOW_STATE_CHANGED",(t=>t?this.#p(t):null)),this.#d("EVENT_UPDATE_STATE_CHANGED",(t=>t?this.#m(t):null)),this.#d("EVENT_WINDOW_MESSAGE",(t=>t?this.#w(t):null)),document.addEventListener("keydown",(t=>{t.ctrlKey&&t.shiftKey&&("D"!==t.key&&"d"!==t.key||this.#u("ACTION_OPEN_DEV_TOOLS"),"W"!==t.key&&"w"!==t.key||this.#u("ACTION_DEFAULT_WINDOW"))}))}#h(t){this.#d(t,(e=>this.#t.publish(t,e)))}#d(t,e){i.ipcRenderer.removeAllListeners(t),i.ipcRenderer.addListener(t,((a,n)=>{l.debug(t,n),e(n,a.ports)}))}#p(t){this.minimized=t.minimized,this.maximized=t.maximized,this.visible=t.visible}addNewWindowInterceptor(t){return this.#A(this.#e,t)}setNewWindowErrorHandler(t){return this.#n=t,(0,c.nm)((()=>{this.#n===t&&(this.#n=null)}))}async#y(t){let e=t.uri;for(const a of this.#e){const n=await a(e,t.source);if(!0===n)return;"string"==typeof n&&(e=n)}if(!await this.launchExternal(e)){if(!this.#n)throw new Error(`Failed to launch URL "${e}"`);this.#n(e)}}#w(t){window.postMessage(t.message,t.targetOrigin,t.transfer||void 0)}addAppQuitInterceptor(t){return this.#A(this.#a,t)}async#I(){for(const t of this.#a)if(!0===await t())return!1;return!0}async#g(){await this.#I()&&await this.quit()}#A(t,e){return t.push(e),{dispose:()=>{const a=t.indexOf(e);-1!==a&&t.splice(a,1)}}}async#u(t,...e){l.debug(t,...e);try{const a=await i.ipcRenderer.invoke(t,...e);return void 0!==a&&l.debug(`-> ${t}`,a),a}catch(e){throw l.error(`-> ${t}`,e),e}}getWindowsFolder(){return this.info.env.SYSTEMROOT}getNativeSystemFolder(){return this.info.wow64?`${this.getWindowsFolder()}\\Sysnative`:`${this.getWindowsFolder()}\\System32`}getTempFolder(){const t=this.info.env;let e=t.TEMP||t.TEMP||`${t.SYSTEMROOT}\\Temp`;return e.length>1&&e.endsWith("\\")&&!e.endsWith(":\\")&&(e=e.slice(0,-1)),e}expandEnvironmentStrings(t){return t.replace(/%([^%]+)%/g,((t,e)=>this.info.env[e.toUpperCase()]||`%${e}%`))}getMemoryInfo(){return this.#u("ACTION_GET_MEMORY_INFO")}getInstalledAvProducts(){return this.#u("ACTION_GET_INSTALLED_AV_PRODUCTS")}setInstallationInfo(t,e){return this.#u("ACTION_SET_INSTALLATION_INFO",t,e)}getCreatorConfiguration(){return this.#u("ACTION_GET_CREATOR_CONFIGURATION")}readShortcutLink(t){return this.#u("ACTION_READ_SHORTCUT_LINK",t)}showOpenFileDialog(t){return this.#u("ACTION_SHOW_OPEN_FILE_DIALOG",t)}showSaveFileDialog(t){return this.#u("ACTION_SHOW_SAVE_FILE_DIALOG",t)}launchExternal(t){return this.#u("ACTION_OPEN_EXTERNAL",t)}openFilePath(t){return this.#u("ACTION_OPEN_FILE_PATH",t)}showFileLocation(t){return this.#u("ACTION_SHOW_FILE_LOCATION",t)}setCloseBehavior(t){return this.#u("ACTION_SET_CLOSE_BEHAVIOR",t)}close(){return this.#u("ACTION_CLOSE_WINDOW")}quit(){return this.#u("ACTION_QUIT_APP")}async show(){return this.#u("ACTION_SHOW_WINDOW")}focus(){return this.#u("ACTION_FOCUS_WINDOW")}minimize(){return this.#u("ACTION_MINIMIZE_WINDOW")}maximize(){return this.#u("ACTION_MAXIMIZE_WINDOW")}unmaximize(){return this.#u("ACTION_UNMAXIMIZE_WINDOW")}async reload(){return await this.#u("ACTION_RELOAD_WINDOW"),new Promise(o.Y)}async showToast(t){return this.#u("ACTION_SHOW_TOAST",t)}activate(t,e){this.#t.publish("EVENT_APP_ACTIVATED",{uri:t,source:e})}onActivated(t){return this.#t.subscribe("EVENT_APP_ACTIVATED",t)}onClosedToTray(t){return this.#t.subscribe("EVENT_CLOSED_TO_TRAY",t)}onRestoredFromTray(t){return this.#t.subscribe("EVENT_RESTORED_FROM_TRAY",t)}whenVisible(t){return this.visible?(t(),c.lE):this.#t.subscribeOnce("EVENT_RESTORED_FROM_TRAY",t)}#f(){this.isInTraySinceStartup=!1,this.#t.publish("EVENT_RESTORED_FROM_TRAY")}onWebviewWindowLoadFinish(t){return this.#t.subscribe("EVENT_WEBVIEW_WINDOW_LOAD_FINISH",t)}onWebviewWindowLoadError(t){return this.#t.subscribe("EVENT_WEBVIEW_WINDOW_LOAD_ERROR",t)}onWebviewWindowClosed(t){return this.#t.subscribe("EVENT_WEBVIEW_WINDOW_CLOSED",t)}showWebviewWindow(t){return this.#u("ACTION_SHOW_WEBVIEW_WINDOW",t)}onUpdateStateChanged(t){return this.#t.subscribe("EVENT_UPDATE_STATE_CHANGED",t)}async captureScreenshot(t){return await this.#u("ACTION_CAPTURE_SCREENSHOT",t)}#m(t){"applying"===t&&(this.#o=Date.now(),this.#c=null),"applied"!==t&&"apply-error"!==t||(this.#c=Date.now()),this.updateState=t,this.#t.publish("EVENT_UPDATE_STATE_CHANGED",t)}fakeApplyUpdate(){return this.#u("ACTION_FAKE_APPLY_UPDATE")}fakeApplyUpdateError(){return this.#u("ACTION_FAKE_APPLY_UPDATE_ERROR")}checkForUpdate(){return this.#r?this.#u("ACTION_CHECK_FOR_UPDATE",this.#r):Promise.resolve("not-checked")}applyUpdate(){return this.#r?this.#u("ACTION_APPLY_UPDATE",this.#r):Promise.resolve("not-checked")}async restartForUpdate(t,e=!1){return!(!e&&!await this.#I())&&await this.#u("ACTION_RESTART_APP_FOR_UPDATE",t)}async setUpdateChannel(t){if("string"!=typeof t)return!1;if(t===this.updateChannel)return!0;this.updateChannel=t;const e=new URL(this.#s.replace("{channel}",t));return e.searchParams.set("osVersion",this.info.osVersion),this.#r=e.toString(),localStorage.setItem(this.#i,t),!0}get manualUpdateUrl(){if(!this.updateChannel)return null;const t=new URL(this.#r);return t.pathname+="/releases/latest",t.toString()}copyText(t){return this.#u("ACTION_COPY_TEXT",t)}createDesktopShortcut(t,e,a){return this.#u("ACTION_CREATE_DESKTOP_SHORTCUT",{uri:t,label:e,icon:a})}async getSystemIdleTime(){return await this.#u("ACTION_GET_SYSTEM_IDLE_TIME")}async postAnalyticsEvent(t,e={}){return await this.#u("ACTION_POST_ANALYTICS_EVENT",{name:t,params:e})}#E(){return"win32"!==this.info.osPlatform?null:this.info.osVersion.split(".").map((t=>parseInt(t,10)))}get isWindows10OrGreater(){const t=this.#E();return!!t&&t.length>=1&&t[0]>=10}get isWindows11OrGreater(){const t=this.#E();return!!t&&!(t.length<3)&&(10===t[0]?t[2]>=22e3:t[0]>10)}get isMacOS(){return"darwin"===this.info.osPlatform}get applyUpdateDuration(){return this.#o?((this.#c??Date.now())-this.#o)/1e3:null}async flashWindow(){await this.#u("ACTION_FLASH_WINDOW")}async getDisplayCount(){return await this.#u("ACTION_GET_DISPLAY_COUNT")}async createOverlayWindow(t,e){const a=new MessageChannel;return await this.#u("ACTION_CREATE_OVERLAY_WINDOW",{processId:t,hotkey:e}),i.ipcRenderer.postMessage("ACTION_SET_OVERLAY_PORT",null,[a.port2]),a.port1}async destroyOverlayWindow(){return await this.#u("ACTION_DESTROY_OVERLAY_WINDOW")}async updateOverlayHotkey(t){return this.#u("ACTION_UPDATE_OVERLAY_HOTKEY",t)}onOverlayWindowClosed(t){return this.#t.subscribe("EVENT_OVERLAY_WINDOW_CLOSED",t)}onOverlayHotkeyPressed(t){return this.#t.subscribe("EVENT_OVERLAY_HOTKEY_PRESSED",t)}onOverlayGraphicsInitialized(t){return this.#t.subscribe("EVENT_OVERLAY_GRAPHICS_INITIALIZED",t)}onOverlayGraphicsHooked(t){return this.#t.subscribe("EVENT_OVERLAY_GRAPHICS_HOOKED",t)}onCaptureReplayBufferSaved(t){return this.#t.subscribe("EVENT_CAPTURE_REPLAY_BUFFER_SAVED",t)}async startCapture(t,e){return await this.#u("ACTION_START_CAPTURE",{config:t,processId:e})}async stopCapture(){return await this.#u("ACTION_STOP_CAPTURE")}async startReplayBuffer(){return await this.#u("ACTION_START_CAPTURE_REPLAY_BUFFER")}async saveReplayBuffer(){return await this.#u("ACTION_SAVE_CAPTURE_REPLAY_BUFFER")}async stopReplayBuffer(){return await this.#u("ACTION_STOP_CAPTURE_REPLAY_BUFFER")}}},41882:(t,e,a)=>{a.d(e,{u:()=>n});const n="custom"},62614:(t,e,a)=>{a.d(e,{X8:()=>u,cR:()=>l,d_:()=>p});var n=a(96610),s=a(35392),i=a(19072),r=a(88849);const o={[n.logLevel.debug]:"DEBUG",[n.logLevel.info]:"INFO",[n.logLevel.warn]:"WARN",[n.logLevel.error]:"ERROR"},c=[];async function l(){const t=(0,i.S)(),e=t.info,a=await t.getMemoryInfo();return{appVersion:e.version,locale:e.locale,osVersion:e.osVersion,osArch:e.osArch,cpuModel:e.deviceCpuModel,cpuCount:e.deviceCpuCount,freeMemory:a.free,totalMemory:a.total,antivirusProducts:await t.getInstalledAvProducts()??["Error"]}}async function u(t){const e=await l(),a=[`App Version: ${e.appVersion}`,`OS: ${e.osVersion}`,`Arch: ${e.osArch}`,`CPU: ${e.cpuModel??"Unknown"}`,`CPU Count: ${e.cpuCount}`,`Free Memory: ${(0,r.z3)(e.freeMemory)} / ${(0,r.z3)(e.totalMemory)}`,`Antivirus: ${e.antivirusProducts.join(", ")||"None"}`,`Locale: ${e.locale}`,""];for(const t of c)a.push(`${o[t.level]} [${t.logger.id}] ${t.message}${t.args?"\r\n"+t.args:""}`);await s.promises.writeFile(t,a.join("\r\n"))}class p{#_(t,e,...a){e===n.logLevel.debug&&"native"===t.id&&"string"==typeof a[0]&&a[0].startsWith("query-registry")||c.push({logger:t,level:e,message:a[0],args:a.slice(1).map(((t,e)=>{return`\t${e} ${a=t,void 0===a?"undefined":JSON.stringify(a)}`;var a})).join("\r\n")})}debug(t,...e){this.#_(t,n.logLevel.debug,...e)}info(t,...e){this.#_(t,n.logLevel.info,...e)}warn(t,...e){this.#_(t,n.logLevel.warn,...e)}error(t,...e){this.#_(t,n.logLevel.error,...e)}}},64931:(t,e,a)=>{a.d(e,{L:()=>n});class n{constructor(t,e,a,n,s){this.name=t,this.version=e,this.arch=a,this.locale=n,this.publisherId=s,""===n&&(n=null)}toString(){return`${this.name}_${this.version}_${this.arch}_${this.locale||""}_${this.publisherId}`}get pfn(){return`${this.name}_${this.publisherId}`}static parse(t){const e=t.split("_");if(5!==e.length)throw new Error(`Invalid package full name '${t}'.`);return new n(...e)}}},80252:(t,e,a)=>{a.d(e,{D:()=>u,k:()=>l});var n=a(15215),s=a(79896),i=a("aurelia-framework"),r=a(20770),o=a(45660),c=a(59239);class l{constructor(t,e){this.gameId=t,this.path=e}static parse(t){const e=t.indexOf("_");if(-1===e)throw new Error(`Invalid custom platform SKU '${t}'.`);return new l(t.substring(0,e),t.substring(e+1))}toString(){return`${this.gameId}_${this.path}`}}let u=class{#L;constructor(t){this.#L=t}async findApps(t){return(await this.getApps()).filter((e=>t.includes(e.sku)))}async getApps(){const t=Object.values(await this.#C()).filter((t=>"custom"===t.platform)),e=[];return await Promise.all(t.map((async t=>{await s.promises.stat(t.location).then((t=>t.isFile())).catch((()=>!1))&&e.push(t)}))),e}getLaunchConfiguration(t,e){const a=l.parse(t).path;return Promise.resolve({command:`"${a}" ${e||""}`.trim(),cwd:a.substring(0,a.lastIndexOf("\\"))})}#C(){return this.#L.state.pipe((0,o.$)(),(0,c.E)("installedApps")).toPromise()}async getIcon(t){return null}};u=(0,n.Cg)([(0,i.autoinject)(),(0,n.Sn)("design:paramtypes",[r.il])],u)},89356:(t,e,a)=>{a.d(e,{Q:()=>h});var n=a(15215),s=a(16928),i=a("aurelia-framework"),r=a(35392),o=a(3972),c=a(29844),l=a(77372),u=a(54700);let p=class{#T;constructor(t){this.#T=t}async read(t){if(null===t||!await r.promises.stat(t).then((t=>t.isFile())).catch((()=>!1)))return null;switch((0,c.LC)(t).toLocaleLowerCase()){case".exe":case".dll":return await this.#v(t);case".p7x":return await this.#O(t);case".zip":case".jar":return await this.#S(t);default:return null}}async readProtectedFileTimes(t){try{const e=await this.#T.statFile(t);return null===e?null:{createdAt:e.creationTime,modifiedAt:e.lastWriteTime}}catch{return null}}async#v(t){try{return(await l.E3.load(t)).ntHeaders.fileHeader.timestamp}catch{return null}}async#O(t){try{return(await this.#T.getFileSignatureInfo(t)).timestamp}catch{return null}}async#S(t){const e=await(0,u.Q)(t);return e?Math.floor(e.getTime()/1e3):null}};p=(0,n.Cg)([(0,i.autoinject)(),(0,n.Sn)("design:paramtypes",[o.Mz])],p);let h=class{#R;constructor(t){this.#R=t}async getGameVersion(t,e,a,n){const i=s.join(t,e),o=await r.promises.stat(i).catch((()=>null));if(null===o)return"uwp"===a?e.toLocaleLowerCase().endsWith(".p7x")?await this.#N(s.join(t,"AppxManifest.xml"),n):await this.#N(i,n):null;const c=Math.floor(o.mtimeMs/1e3),l=Math.floor(o.birthtimeMs/1e3);if(n&&n.modifiedAt===c&&n.createdAt===l)return n;let u=await this.#R.read(i);return null!==u||"uwp"!==a||e.toLocaleLowerCase().endsWith(".p7x")||(u=c),null!==u?{version:u,modifiedAt:c,createdAt:l}:null}async#N(t,e){const a=await this.#R.readProtectedFileTimes(t);if(!a)return null;const{modifiedAt:n,createdAt:s}=a;return n?e?.modifiedAt!==n?{version:n,modifiedAt:n,createdAt:s}:e:null}};h=(0,n.Cg)([(0,i.autoinject)(),(0,n.Sn)("design:paramtypes",[p])],h)},90231:(t,e,a)=>{a.d(e,{D:()=>y,X:()=>f});var n=a(15215),s=a(35317),i=a("aurelia-framework"),r=a(20770),o=a(45660),c=a(59239),l=a(19072),u=a(83974),p=a("services/bugsnag/index"),h=a(96555),d=a(38777);function g(t){return t&&(t=t.replaceAll("/","\\")).endsWith("\\")&&(t=t.substring(0,t.length-1)),t}class f extends Error{constructor(t){super(`App launched failed with code ${t}.`),this.errorCode=t,Object.setPrototypeOf(this,f.prototype)}}let y=class{#b;#L;#k;constructor(t,e){this.#b=t,this.#L=e}setPlatforms(t){this.#k=t}setIconCacheDirectory(t){this.iconCacheDirectory=t}async findApps(t){const e=new Map,a=new Map;await this.#D(t,a,e);const n=new Map;for(const s of t){const t=e.get(s);if(t){const e=t.redirect?this.#P(t.location??"",t.redirect,a):t.location;e&&n.set(s,{platform:t.platform,sku:t.sku,location:e,alternateLocations:t.alternateLocations})}}return n}async#D(t,e,a){const n=new Set,s=this.#W(t);for(const[t,i]of s){const s=e.get(t)??new Map,r=i.filter((t=>!s.has(t)));if(r.length>0){const i=await(this.#k[t]?.findApps(r,await this.#F(t)))??[];for(const e of r){const n=new h.o(t,e).toString(),r=i.find((t=>t.sku===e)),o=r?{correlationId:n,platform:t,...r}:null;s.set(e,o),a.set(n,o)}e.set(t,s);for(const t of i)t.redirect&&n.add(new h.o(t.redirect.platform,t.redirect.sku).toString())}}n.size>0&&await this.#D(Array.from(n),e,a)}#W(t){const e=new Map;for(const a of t){const{platform:t,sku:n}=h.o.parse(a),s=e.get(t);void 0===s?e.set(t,[n]):s.push(n)}return e}async getInstalledApps(){const t=new Map,e=await this.#V();await Promise.all(Object.keys(this.#k).map((async a=>{try{const n=await this.#k[a].getApps(e[a]);t.set(a,new Map(n.map((t=>(t.location=g(t.location??""),t.alternateLocations&&(t.alternateLocations=t.alternateLocations.map(g)),[t.sku,t])))))}catch(t){t instanceof u.M||(0,p.report)(new Error(`${t?.constructor?.name??"Unknown error"} occured while reading ${a} apps. Message: ${t?.message}`))}})));const a=[];for(const[e,n]of t)for(const[s,i]of n){const n=i.redirect?this.#P(i.location??"",i.redirect,t):i.location;n&&a.push({platform:e,sku:s,location:n,alternateLocations:i.alternateLocations})}return a}#P(t,e,a,n=new Set){const s=a.get(e.platform);if(!s)return t;const i=s.get(e.sku);return i?i.redirect?n.has(i.redirect)?i.location??null:(n.add(i.redirect),this.#P(i.location??"",i.redirect,a,n)):i.location??null:t}async launchApp(t,e,a,n,i){"string"==typeof a&&(a=a.replaceAll("&","^&"));const r=await this.#$(t,e,a,n);if(null===r)throw new Error("App not found.");await new Promise(((t,e)=>{const a=this.#b.isMacOS?"/bin/bash":`${this.#b.getNativeSystemFolder()}\\cmd.exe`,n=(0,s.spawn)(r.command,{detached:!0,windowsHide:!0,stdio:"ignore",env:this.#b.info.env,cwd:r.cwd,shell:a}),o=new d.Vd([(0,d.$U)(n,"error",(t=>{o.dispose(),e(t)})),(0,d.$U)(n,"exit",(a=>{o.dispose(),"number"!=typeof a||0!==a&&100010!==a&&!r.successExitCodes?.includes(a)?e(new f(a)):t()})),i.onCancel((()=>{o.dispose(),e(new d._T)}))])}))}async#$(t,e,a,n){const s=await this.#F(t);return await(this.#k[t]?.getLaunchConfiguration(e,a,s,n))??null}async#V(){return await this.#L.state.pipe((0,o.$)(),(0,c.E)("catalog","platforms")).toPromise()}async#F(t){return await this.#L.state.pipe((0,o.$)(),(0,c.E)("catalog","platforms",t)).toPromise()??{}}async getIcon(t,e){return await(this.#k[t]?.getIcon(e,await this.#F(t)))??null}};y=(0,n.Cg)([(0,i.autoinject)(),(0,n.Sn)("design:paramtypes",[l.s,r.il])],y)},"installations/index":(t,e,a)=>{a.r(e),a.d(e,{configure:()=>nt,platforms:()=>at});var n=a(20770),s=a(19072),i=a(90231),r=a(80252),o=a(15215),c=a("aurelia-framework"),l=a(11087),u=a(35392);class p{#U;constructor(t){this.#U=t}static async getDefault(t,e){const a=await t.queryValue("HKEY_LOCAL_MACHINE\\SOFTWARE\\Epic Games\\EpicGamesLauncher\\AppDataPath",l.S.Registry32);if("string"==typeof a&&a.length>0&&await u.promises.stat(a).then((t=>t.isDirectory())).catch((()=>!1)))return new p(a.endsWith("\\")?a.substring(0,a.length-1):a);const n=e.expandEnvironmentStrings("%ProgramData%\\Epic\\EpicGamesLauncher\\Data");return await u.promises.stat(n).then((t=>t.isDirectory())).catch((()=>!1))?new p(n):null}async getInstalledApplications(){return(await this.#M()).filter((t=>!0!==t.bIsIncompleteInstall&&"string"==typeof t.InstallLocation&&"string"==typeof t.CatalogNamespace&&"string"==typeof t.CatalogItemId&&"string"==typeof t.AppName&&Array.isArray(t.AppCategories)))}async#M(){const t=`${this.#U}\\Manifests`,e=await u.promises.readdir(t,{withFileTypes:!0}).catch((()=>[])),a=[];for(const n of e)if(n.isFile()&&n.name.endsWith(".item"))try{a.push(JSON.parse(await u.promises.readFile(`${t}\\${n.name}`,"utf8")))}catch{}return a}}let h=class{#x;#b;#G;constructor(t,e){this.#x=t,this.#b=e}async#H(){return this.#G||(this.#G=await p.getDefault(this.#x,this.#b)),this.#G}async findApps(t){return(await this.getApps()).filter((e=>t.includes(e.sku)))}async getApps(){const t=await this.#H();if(!t)return[];const e=[],a=await t.getInstalledApplications();for(const t of a.filter((t=>t.AppCategories.includes("games"))))e.push({sku:`${t.CatalogNamespace}:${t.CatalogItemId}:${t.AppName}`,location:t.InstallLocation,redirect:this.#j(t)});return e}#j(t){if("string"!=typeof t.LaunchExecutable||"string"!=typeof t.LaunchCommand)return null;if("uplaylaunch.exe"!==t.LaunchExecutable.toLocaleLowerCase())return null;const e=t.LaunchCommand.match(/-uplayid=([0-9]+)/i)?.[1]??null;return e?{platform:"uplay",sku:e}:null}async getLaunchConfiguration(t,e){return{command:`start "" "com.epicgames.launcher://apps/${encodeURIComponent(t)}?action=launch&silent=true"`}}async getIcon(t){return null}};var d;h=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[l.J,s.s])],h);const g="HKEY_LOCAL_MACHINE\\SOFTWARE\\GOG.com\\Games";let f=d=class{#x;constructor(t,e){this.location=t,this.#x=e}static async getDefault(t,e){const a=await t.queryValue("HKEY_LOCAL_MACHINE\\SOFTWARE\\GOG.com\\GalaxyClient\\paths\\client",l.S.Registry32);if("string"==typeof a&&a.length>0&&await d.isValidInstallDir(a))return new d(a,t);const n=e.expandEnvironmentStrings("%PROGRAMFILES(X86)%\\GOG Galaxy");return await d.isValidInstallDir(n)?new d(n,t):null}static async isValidInstallDir(t){try{return(await u.promises.stat(`${t}\\GalaxyClient.exe`)).isFile()}catch{return!1}}async getGames(){const t=await this.#x.querySubkeySubkeyValues(g,l.S.Registry32);if(!t)return[];const e=[];for(const a of Object.values(t)){const t=this.#Y(a);t&&e.push(t)}return e}async findGame(t){try{const e=await this.#x.querySubkeyValues(`${g}\\${t}`,l.S.Registry32);return this.#Y(e)}catch{return null}}#Y(t){try{const e={};for(const a of Object.keys(t))e[a.toUpperCase()]=t[a];if("string"!=typeof(t=e).PATH||0===t.PATH.length)return null;const a=parseInt(t.GAMEID,10);return isNaN(a)||a<=0?null:{id:a,buildId:t.BUILDID,name:t.GAMENAME,launchCommand:t.LAUNCHCOMMAND,launchParam:t.LAUNCHPARAM,workingDir:t.WORKINGDIR,path:t.PATH}}catch{return null}}};f=d=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[String,l.J])],f);let y=class{#x;#b;#B;constructor(t,e){this.#x=t,this.#b=e}async#K(){return this.#B||(this.#B=await f.getDefault(this.#x,this.#b)),this.#B}async findApps(t){const e=await this.#K();return e?(await Promise.all(t.map((t=>e.findGame(parseInt(t,10)))))).filter((t=>!!t)).map((t=>this.#q(t))):[]}async getApps(){const t=await this.#K();return t?(await t.getGames()).map((t=>this.#q(t))):[]}#q(t){return{sku:t.id.toString(),location:t.path.toLocaleLowerCase()}}async getLaunchConfiguration(t,e){const a=await this.#K();if(!a)return null;const n=await a.findGame(Number(t));return n?{command:`"${a.location}\\GalaxyClient.exe" /command=runGame /gameId=${t} /path="${n.path}"`,cwd:a.location,successExitCodes:[4294967295]}:null}async getIcon(t){return null}};y=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[l.J,s.s])],y);var m=a(41882),w=a(76982);const A=Buffer.alloc(32);async function I(t){if("string"!=typeof t)return null;const e=`${t}\\Launcher.exe`;return await u.promises.stat(e).then((t=>t.isFile())).catch((()=>!1))?e:null}class E{#z;#U;constructor(t,e){this.#z=t,this.#U=e}static async getDefault(t,e){const a=await t.queryValue("HKEY_LOCAL_MACHINE\\SOFTWARE\\Rockstar Games\\Launcher\\InstallFolder",l.S.Registry32).catch((()=>null)),n=await I(a)??await I(e.expandEnvironmentStrings("%PROGRAMW6432%\\Rockstar Games\\Launcher"))??await I(e.expandEnvironmentStrings("%PROGRAMFILES%\\Rockstar Games\\Launcher"));return null===n?null:new E(n,e.expandEnvironmentStrings("%PROGRAMDATA%\\Rockstar Games\\Launcher"))}async getTitles(){const t=await async function(t){try{const e=await u.promises.readFile(t),a=w.createDecipheriv("aes-256-cbc",A,e.slice(0,16)),n=Buffer.concat([a.update(e.slice(16)),a.final()]);return JSON.parse(n.toString("utf-8"))}catch{return null}}(`${this.#U}\\titles.dat`);return"object"==typeof t&&null!==t&&Array.isArray(t.tl)?t.tl.filter((t=>null!==t&&"object"==typeof t)).map((t=>({id:t.ti,location:t.il,version:t.iv,flags:t.if}))).filter((t=>0!==t.flags&&"launcher"!==t.id&&"string"==typeof t.location&&0!==t.location.length&&"string"==typeof t.id&&"string"==typeof t.version&&"number"==typeof t.flags)):[]}getLaunchCommand(t){return`"${this.#z}" -minmodeApp=${t}`}}let _=class{#x;#b;#Q;constructor(t,e){this.#x=t,this.#b=e}async#J(){return this.#Q||(this.#Q=await E.getDefault(this.#x,this.#b))}async findApps(t){return(await this.getApps()).filter((e=>t.includes(e.sku)))}async getApps(){const t=await this.#J();return null===t?[]:(await t.getTitles()).map((t=>({sku:t.id,location:t.location.toLocaleLowerCase()})))}async getLaunchConfiguration(t){const e=await this.#J();return{command:e?.getLaunchCommand(t)??""}}async getIcon(t){return null}};_=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[l.J,s.s])],_);let L=class{#b;#x;constructor(t,e){this.#b=t,this.#x=e}async getLaunchConfiguration(t,e,a){const n=a.apps.find((e=>e.id===t));if(!n)return null;const s=await this.#X(n);return s?{command:`"${s}\\${n.launcher}" ${n.args||""} ${e||""}`.trim(),cwd:n.cwd?`${s}\\${n.cwd}`:s}:null}async findApps(t,e){const a=[];for(const n of t){const t=e.apps.find((t=>t.id===n));if(void 0!==t){const e=await this.#X(t);null!==e&&a.push({sku:t.id,location:e.toLocaleLowerCase()})}}return a}async getApps(t){return(await Promise.all(t.apps.map((async t=>({sku:t.id,location:(await this.#X(t)||"").toLocaleLowerCase()}))))).filter((t=>!!t.location))}async#X(t){for(const e of t.locators){const a=await this.#Z(e);if(a&&await u.promises.stat(`${a}\\${t.launcher}`).then((t=>t.isFile())).catch((()=>!1)))return a}return null}async#Z(t){let e;switch(t.type){case"directory":e=t.value;break;case"registry_value":e=await this.#tt(t.value);break;case"uninstall_key":e=await this.#tt(`HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\${t.value}\\InstallLocation`)}return"string"!=typeof e||0===e.length?null:(e=e.split(",")[0].replaceAll('"',""),e=this.#b.expandEnvironmentStrings(e).replaceAll("/","\\"),e)}async#tt(t){let e;try{e=await this.#x.queryValue(t,l.S.Registry64)}catch{}if(!e)try{e=await this.#x.queryValue(t,l.S.Registry32)}catch{}return"string"==typeof e?e:null}async getIcon(t){return null}};L=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[s.s,l.J])],L);var C=a(96610),T=a(25007);const v=[[/"\t/g,'":\t'],[/\t/g,""],[/\n/g,""],[/"\{/g,'":{'],[/\}"/g,'},"'],[/("*)""/g,'$1","']];function O(t){return v.forEach((e=>t=t.replace(e[0],e[1]))),JSON.parse(`{${t}}`)}function S(t,e=null){return R(new T.d(t),e)}function R(t,e=null){let a=t.readByte();const n={};for(;8!==a;)n[e?e[t.readUInt32()]:t.readNullTerminatedString("ascii")]=N(a,t,e),a=t.readByte();return n}function N(t,e,a=null){switch(t){case 0:return R(e,a);case 1:return e.readNullTerminatedString("utf8");case 2:case 6:case 4:return e.readInt32();case 3:return e.readFloat();case 7:return e.readUInt64();case 10:return e.readInt64();default:throw new Error(`Unknown node type ${t} in VDF`)}}class b{#et;#at;constructor(t,e){this.#et=t,this.#at=e}static async open(t){const e=await u.promises.open(t,"r");try{const t=Buffer.alloc(16);if((await e.read(t,0,16)).bytesRead<8)throw new Error("Invalid Steam appinfo file.");const a={version:t.readUint32BE(),universe:t.readUint32LE(4),appsOffset:8,strings:null};if(![675567111,692344327].includes(a.version))throw new Error(`Unsupported Steam appinfo file version ${a.version}.`);if(692344327===a.version){const n=Number(t.readBigInt64LE(8));if(isNaN(n))throw new Error("Steam appinfo file too large to process.");const s=new T.d(Buffer.alloc((await e.stat()).size-n));await e.read(s.buffer,0,s.buffer.length,n);const i=new Array(s.readUInt32());for(let t=0;t<i.length;t++)i[t]=s.readNullTerminatedString("utf8");a.appsOffset=16,a.strings=i}return new b(a,e)}catch(t){throw await e.close(),t}}async*enumerateApps(t){let e=t.length;const a=new Set(t);let n=this.#et.appsOffset;const s=Buffer.alloc(8);let i=Buffer.alloc(0);for(;e>0;){await this.#at.read(s,0,8,n),n+=8;const t=s.readUInt32LE();if(0===t)break;const r=s.readUInt32LE(4);a.has(t)?(e--,r>i.length&&(i=Buffer.alloc(2*r)),await this.#at.read(i,0,r-60,n+60),n+=r,yield S(i,this.#et.strings)):n+=r}}close(){return this.#at.close()}}var k=a(16928),D=a.n(k),P=a(70236),W=a(29844);const F=/^appmanifest_\d+\.acf$/i,V=(0,C.getLogger)("steam");let $=!1;class U{#nt;constructor(t,e=null){this.location=e,this.#nt=t}get isPrimary(){return this.location===this.#nt.directory}get appDirectory(){return D().join(this.location??"","steamapps")}async getChildLibraries(){if(!this.isPrimary)return[];const t=D().join(this.appDirectory,"libraryfolders.vdf"),e=[];try{const a=O(await u.promises.readFile(t,"utf8")),n=a.LibraryFolders??a.libraryfolders;if("object"==typeof n)for(let t=1;n.hasOwnProperty(t.toString());t++){const a=n[t.toString(10)],s="string"==typeof a?a:a.path;"string"==typeof s&&await u.promises.stat(s).then((t=>t.isDirectory())).catch((()=>!1))&&e.push(new U(this.#nt,s))}}catch{V.error("Invalid VDF",t)}return e}async refreshApps(){const t=await u.promises.readdir(this.appDirectory,{withFileTypes:!0}).catch((()=>null));if(null===t)return[];const e=new Set,a=[];for(const n of t)if(n.isFile()&&F.test(n.name)){const t=await this.#st(n.name);null!==t&&(e.add(t.appId),a.push(t))}const n=await this.getChildLibraries();for(const t of n){const n=await t.refreshApps();for(const t of n)e.has(t.appId)||(e.add(t.appId),a.push(t))}return!$&&this.isPrimary&&(V.debug("Libraries discovered.",[this.location].concat(n.map((t=>t.location)))),$=!0),a}async findApp(t){const e=`appmanifest_${t}.acf`;let a=await this.#st(e,!1);if(null!==a)return a;for(const t of await this.getChildLibraries())if(a=await t.#st(e,!1),null!==a)return a;return null}async#st(t,e=!0){t=D().join(this.appDirectory,t);try{const e=O(await u.promises.readFile(t,"utf8"));if(null===e)return null;const a=Object.keys(e);if(0===a.length)return null;if(a.forEach((t=>e[t.toLowerCase()]=e[t])),"appstate"in e){const t=e.appstate;if(Object.keys(t).forEach((e=>t[e.toLowerCase()]=t[e])),"appid"in t&&"stateflags"in t&&"buildid"in t&&"installdir"in t){const e=parseInt(t.stateflags,10);if((0,P.Lt)(e,4))return(0,W.oP)(t.installdir)||(t.installdir=(0,W.fj)(this.appDirectory,"common",t.installdir)),{appId:parseInt(t.appid,10),stateFlags:parseInt(t.stateflags,10),buildId:parseInt(t.buildid,10),location:t.installdir}}}}catch{e&&V.warn("Invalid ACF",t)}return null}}const M=(0,C.getLogger)("steam");class x{constructor(t){this.directory=t}get exePath(){return this.directory+"\\Steam.exe"}defaultLibrary(){return new U(this,this.directory)}static async getDefault(t,e){const a=await t.queryValue("HKEY_LOCAL_MACHINE\\SOFTWARE\\Valve\\Steam\\InstallPath",l.S.Registry32);if("string"==typeof a&&a.length>0&&await u.promises.stat(a).then((t=>t.isDirectory())).catch((()=>!1)))return new x(a);let n=e.expandEnvironmentStrings("%PROGRAMFILES(X86)%\\Steam");return e.isMacOS&&(n=e.expandEnvironmentStrings(`${e.info.env.HOME}/Library/Application Support/Steam`)),await u.promises.stat(n).then((t=>t.isDirectory())).catch((()=>!1))?new x(n):(a?M.warn("Invalid installation path.",a):M.debug("Steam not installed."),null)}async openAppInfo(){try{return await b.open(this.directory+"\\appcache\\appinfo.vdf")}catch{return null}}}const G=["battleye","anticheat","anti cheat","easyanticheat","easy anti cheat","eac","be"],H=`(${["no","without","disable"].join("|")})\\s+(${G.join("|")})`,j=`(${G.join("|")})\\s+(${["disable"].join("|")})`,Y=new RegExp(`(?:${H})|(?:${j})`,"i");let B=class{#x;#b;#nt;constructor(t,e){this.#x=t,this.#b=e}async#K(){return this.#nt||(this.#nt=await x.getDefault(this.#x,this.#b)),this.#nt}async getLibrary(){const t=await this.#K();return t&&t.defaultLibrary()}async findApps(t){const e=await this.#K();if(!e)return[];const a=[];for(const n of t){const t=await e.defaultLibrary().findApp(Number(n));null!==t&&a.push(this.#it(t))}return a}async getApps(){const t=await this.#K();return t?(await t.defaultLibrary().refreshApps()).map((t=>this.#it(t))):[]}#it(t){return{sku:t.appId.toString(),location:t.location.toLocaleLowerCase()}}async#rt(t,e){const a=await this.#K();if(!a)return null;const n=await a.openAppInfo();if(!n)return null;try{const a=parseInt(t,10),s=(await n.enumerateApps([a]).next())?.value??null;return s?.appinfo&&"common"in s.appinfo?e(s.appinfo):null}finally{n.close()}}async#ot(t){try{return await this.#rt(t,(t=>{const e=t?.config?.launch??{},a=Object.values(e).find((t=>t?.description&&function(t){const e=t.toLowerCase().replace(/[^a-z0-9\s]/gi,"");return Y.test(e)}(t.description)));return a?{launchType:a.type}:null}))}catch(t){return null}}#ct(t,e,a){return!a||a.startsWith("-")||a.startsWith("/")?`"${t.exePath}" -applaunch ${e} ${a}`:`"${t.exePath}" "steam://launch/${e}/${a??""}"`}async getLaunchConfiguration(t,e){const a=await this.#K();if(this.#b.isMacOS)return{command:`open steam://run/${t}`};if(!a)throw new Error("Steam installation not found");const n=await this.#ot(t);if(n){const e=n?.launchType;return{command:this.#ct(a,t,e??"dialog")}}return{command:this.#ct(a,t,e)}}async getIcon(t){const e=await this.#K();return e?await this.#rt(t,(t=>{const a=t?.common.clienticon;return a?`${e.directory}\\steam\\games\\${a}.ico`:null})):null}};B=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[l.J,s.s])],B);const K="HKEY_LOCAL_MACHINE\\SOFTWARE\\Ubisoft\\Launcher\\Installs";class q{#lt;#x;constructor(t,e){t.endsWith("\\")&&(t=t.substring(0,t.length-1)),this.#lt=t,this.#x=e}static async getDefault(t){const e=await t.queryValue("HKEY_LOCAL_MACHINE\\SOFTWARE\\Ubisoft\\Launcher\\InstallDir",l.S.Registry32);return"string"==typeof e?new q(e,t):null}async getAllGames(){const t=await this.#x.querySubkeySubkeyValues(K,l.S.Registry32);if(!t)return[];const e=[];for(const[a,n]of Object.entries(t)){const t=await this.#ut(a,n);t&&e.push(t)}return e}async findGame(t){const e=await this.#x.querySubkeyValues(`${K}\\${t}`,l.S.Registry32);return e?await this.#ut(t,e):null}async#ut(t,e){try{const a=parseInt(t,10);if(!isNaN(a)&&"object"==typeof e&&"string"==typeof e.InstallDir){const t=e.InstallDir.replaceAll("/","\\");if((await u.promises.stat(t+"uplay_install.state")).isFile())return{id:a,location:t}}}catch{}return null}getLaunchCommand(t,e){let a=`"${this.#lt}\\UbisoftGameLauncher.exe" -upc_uplay_id ${t}`;return"string"==typeof e&&e.length>0&&(a+=" -upc_arguments "+btoa(e)),a}}let z=class{#x;#pt;constructor(t){this.#x=t}async#H(){return this.#pt||(this.#pt=await q.getDefault(this.#x))}async findApps(t){const e=await this.#H();if(null===e)return[];const a=[];for(const n of t){const t=await e.findGame(n);t&&a.push(this.#q(t))}return a}async getApps(){const t=await this.#H();return null===t?[]:(await t.getAllGames()).map((t=>this.#q(t)))}#q(t){return{sku:t.id.toString(),location:t.location.toLocaleLowerCase()}}async getLaunchConfiguration(t,e){const a=await this.#H();return{command:a?.getLaunchCommand(parseInt(t,10),e)??""}}async getIcon(t){return null}};z=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[l.J])],z);var Q=a(3972),J=a(64931);class X{constructor(t,e){this.fullName=t,this.packageRoot=e}async getLaunchCommand(t){return t??=await this.#ht(),`start shell:AppsFolder\\${this.fullName.pfn}!${t}`}async#ht(){try{const t=await u.promises.readFile(this.packageRoot+"\\AppxManifest.xml","utf8"),e=t.indexOf('Id="',t.indexOf("<Application "));return t.substring(e+4,t.indexOf('"',e+4))}catch{return"App"}}}let Z=class{#x;#dt;constructor(t,e){this.#x=t,this.#dt=e}async getAllApps(){const t=await this.#x.querySubkeySubkeyValues("HKEY_CLASSES_ROOT\\Local Settings\\Software\\Microsoft\\Windows\\CurrentVersion\\AppModel\\Repository\\Packages");if(!t)return[];const e=await this.#gt(),a=new Set(await this.#ft()),n=Object.values(t).map((t=>this.#st(t,e))).filter((t=>null!==t));for(const t of n)if(t&&a.has(t.fullName.toString())){const e=await this.#dt.resolvePath(t.packageRoot);e&&e!==t.packageRoot&&(t.resolvedPackageRoot=e)}return n}#st(t,e){if("string"==typeof t.PackageID&&"string"==typeof t.PackageRootFolder){const a=e.get(t.PackageID);try{const e=J.L.parse(t.PackageID),n=new X(e,t.PackageRootFolder);return a&&(n.installedLocation=a.InstalledLocation||void 0,n.mutableLink=a.MutableLink||void 0,n.mutableLocation=a.MutableLocation||void 0),n}catch{}}return null}async#ft(){const t=await this.#x.querySubkeyValues("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\GamingServices\\PackageRepository\\Package",l.S.Registry64);return t?Object.keys(t):[]}async#gt(){const t=await this.#x.querySubkeySubkeyValues("HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\AppModel\\StateRepository\\Cache\\Package\\Data",l.S.Registry64);return new Map(Object.values(t??{}).map((t=>[t.PackageFullName,t])))}};Z=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[l.J,Q.Mz])],Z);const tt=[400,350,300,250,225,200,180,175,150,140,125,100,80];let et=class{#yt;constructor(t){this.#yt=t}async findApps(t,e){return(await this.getApps(e)).filter((e=>t.includes(e.sku)))}async getApps(t){return Array.from((await this.#mt(t)).values()).map((t=>{let e=t.packageRoot;const a=[];return t.resolvedPackageRoot&&(a.push(t.packageRoot),e=t.resolvedPackageRoot),t.installedLocation&&t.installedLocation!==t.packageRoot&&t.installedLocation!==e&&a.push(t.installedLocation),t.mutableLink&&t.mutableLink!==e&&!a.includes(t.mutableLink)&&a.push(t.mutableLink),t.mutableLocation&&t.mutableLocation!==e&&!a.includes(t.mutableLocation)&&a.push(t.mutableLocation),{sku:t.fullName.pfn,location:e,alternateLocations:a.length?a:void 0}}))}async getLaunchConfiguration(t,e,a,n){const s=(await this.#mt(a)).get(t);return s?{command:await s.getLaunchCommand(n?a.containers[s.fullName.pfn]:void 0)}:null}async#mt(t){const e=t.ignore.map((t=>new RegExp(t,"i"))),a=new Map;return(await this.#yt.getAllApps()).filter((t=>e.every((e=>!e.test(t.fullName.pfn))))).forEach((t=>{"x64"!==t.fullName.arch&&a.has(t.fullName.pfn)||a.set(t.fullName.pfn,t)})),a}async getIcon(t,e){const a=await this.findApps([t],e);if(a){const t=a[0],e=(await u.promises.readFile((0,W.fj)(t.location??"","AppxManifest.xml"))).toString(),n=[/VisualElements.+\Square150x150Logo="([^"]+)"/,/VisualElements.+\Square44x44Logo="([^"]+)"/,/VisualElements.+\sLogo="([^"]+)"/];let s="";for(const t of n){const a=t.exec(e);if(a){s=a[1];break}}if(!s)return null;const i=(0,W.LC)(s);if(!await u.promises.stat((0,W.fj)(t.location??"",s)).catch((()=>!1)))for(const e of tt){const a=s.replace(i,`.scale-${e}${i}`);await u.promises.stat((0,W.fj)(t.location??"",a)).catch((()=>!1))&&(s=a)}return await u.promises.stat((0,W.fj)(t.location??"",s)).catch((()=>!1))?(0,W.fj)(t.location??"",s):null}return null}};et=(0,o.Cg)([(0,c.autoinject)(),(0,o.Sn)("design:paramtypes",[Z])],et);const at={gog:y,steam:B,uplay:z,uwp:et,epic:h,rockstar:_,standalone:L,[m.u]:r.D};function nt(t,e){t.container.registerSingleton(i.D,(function(){const a=new i.D(t.container.get(s.s),t.container.get(n.il));return a.setPlatforms(function(t){const e={};return Object.keys(at).forEach((a=>e[a]=t.container.get(at[a]))),e}(t)),a.setIconCacheDirectory(e.iconCacheDirectory),a}))}},main:(t,e,a)=>{a.r(e),a.d(e,{configure:()=>y});var n=a(16928),s=(a("aurelia-framework"),a(96610)),i=a(20770),r=a(45660),o=a(59239),c=a(16953),l=a(60692),u=a(19072),p=a(62614),h=a("services/bugsnag/index"),d=a(44242),g=a(41772),f=a(75767);async function y(t){const e=await async function(){const t=await u.s.create(),e=["1073807364","3221226091","3762504530","4294967295"];return t.onUpdateStateChanged((async a=>{if("available"===a)try{await t.applyUpdate()}catch(t){t instanceof Error&&e.some((e=>t.message.includes(e)))||(0,h.report)(t)}})),t.checkForUpdate().catch((()=>{})),document.addEventListener("keydown",(async e=>{if(e.ctrlKey&&e.shiftKey&&"L"===e.key){const e=await t.showSaveFileDialog({defaultPath:`WeMod-${Date.now()}.log`,filters:[{extensions:["log"],name:"Log Files"}]});!e.canceled&&e.filePath&&await(0,p.X8)(e.filePath).catch((()=>{}))}})),t}();t.container.registerInstance(u.s,e),console.info(`WeMod v${e.info.version}`),t.use.standardConfiguration().plugin("aurelia-dialog",(t=>{t.useDefaults(),t.settings.lock=!1,t.settings.keyboard=!0,t.settings.startingZIndex=1001,t.settings.restoreFocus=t=>{if(["#dialogs","#fullscreen-dialogs"].forEach((t=>{const e=document.querySelector(t);e&&!e.hasChildNodes()&&e.classList.remove("ux-dialog-open")})),t)try{t.focus()}catch{}}})).plugin("aurelia-animator-css").feature("services/bugsnag/index",{appVersion:e.info.version,appPath:e.info.paths.app,releaseStage:"stable"===e.info.releaseChannel?void 0:e.info.releaseChannel,locale:e.info.locale,osArch:e.info.osArch,...c.A.services.bugsnag}).feature("shared/utility/index",{debug:c.A.debug}).feature("shared/i18n/index",{defaultLocale:"en-US",systemLocale:e.info.locale,supportedLocales:g.d,templateRepository:new f.x(n.normalize(`${e.info.paths.app}/static/strings`),new Intl.Locale("en-US")),initialLocale:()=>t.container.get(i.il).state.pipe((0,r.$)(),(0,o.E)("settings","language")).toPromise()}).feature("shared/markdown/index").feature("shared/pusher/index",c.A.services.pusher).feature("shared/metrics/index",{tags:[{key:"client",value:"desktop"}]}).feature("shared/api/index",{baseUrl:c.A.services.api.baseUrl,clientId:c.A.services.api.clientId,cdnUrl:c.A.services.cdn.baseUrl,superProperties:{client:"desktop",clientVersion:e.info.version,os:"win32"===e.info.osPlatform?"windows":e.info.osPlatform,osVersion:e.info.osVersion,osArch:e.info.osArch,systemLocale:e.info.locale,systemRegion:e.info.region},triggerableExperiments:l.K}).feature("shared/components/index").feature("api/index",{catalogUrl:c.A.services.api.catalogUrl}).feature("store/index",{storageKey:`${c.A.storageNamespace}:globalStore`,debug:c.A.debug}).feature("native/support/index",{exe:n.normalize(`${e.info.paths.assets}/auxiliary/WeModAuxiliaryService.exe`)}).feature("cheats/storage/index",{cacheDirectory:n.normalize(`${e.info.paths.storage}/trainers`)}).feature("cheats/trainer/index",{binaryDir:n.normalize(`${e.info.paths.assets}/trainerlib`)}).feature("installations/index",{iconCacheDirectory:n.normalize(`${e.info.paths.storage}/icons`)}).feature("resources/index"),t.use.preTask((async()=>{s.addAppender(new d.g(c.A.debug?[]:["aurelia","templating","host"])),s.addAppender(new p.d_),s.setLevel(s.logLevel.debug)})),c.A.testing&&t.use.plugin("aurelia-testing"),await t.start(),await t.setRoot("root/root",document.getElementById("root")??void 0)}c.A.debug||(process.noDeprecation=!0),(()=>{const t=global.URL.prototype.toString;global.URL.prototype.toString=function(){const e=t.apply(this,arguments);return"string"==typeof e&&e.startsWith("file://")?e.replaceAll("'","%27").replaceAll(")","%29"):e}})()}}]);
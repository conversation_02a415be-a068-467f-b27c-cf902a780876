"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1620],{17275:(o,e,i)=>{i.d(e,{C:()=>l});var a=i(15215),s=i("aurelia-dialog"),t=i("aurelia-event-aggregator"),n=i("aurelia-framework");let l=class{#o;#e;constructor(o,e){this.#o=o,this.#e=e}async open(o,e,i){this.#e.publish("dialog:open:start",this.viewModelClass);const a=this.#o.open({viewModel:this.viewModelClass,host:document.querySelector("#dialogs")??void 0,model:o,lock:e,keyboard:!e,ignoreTransitions:i});return a.then((()=>this.#e.publish("dialog:open:complete",this.viewModelClass))),await a.whenClosed()}};l=(0,a.Cg)([(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[s.DialogService,t.EventAggregator])],l)},92465:(o,e,i)=>{i.d(e,{_T:()=>b,HL:()=>p,Vd:()=>u,_M:()=>h,yB:()=>d,nm:()=>n,Wn:()=>l,SO:()=>c,Ix:()=>r,lE:()=>t});var a=i("aurelia-event-aggregator"),s=i(49442);const t=Object.freeze(n(s.Y));function n(o){return{dispose:()=>{o&&(o(),o=void 0)}}}function l(o){return new Promise((e=>setTimeout(e,o)))}function r(o,e){const i=setTimeout(o,e);return n((()=>clearTimeout(i)))}function c(o,e){const i=setInterval(o,e);return n((()=>clearInterval(i)))}function d(o,e,i,a){return o.addEventListener(e,i,a),n((()=>o.removeEventListener(e,i,a)))}const g=o=>!(null==o);class u{#i;constructor(o=[]){this.#i=o.filter(g)}get disposed(){return null===this.#i}push(o){return null!==o&&this.#i?.push(o),this}dispose(){if(null!==this.#i){const o=this.#i;this.#i=null;for(let e=o.length-1;e>=0;e--)o[e].dispose()}}pushFunction(o){return this.push(n(o))}pushEventListener(o,e,i,a){return this.push(d(o,e,i,a))}}class h extends a.EventAggregator{constructor(){super(...arguments),this.#a=new u}#a;subscribe(o,e){if(!this.#a)return t;const i=super.subscribe(o,e);return this.#a.push(i),i}subscribeOnce(o,e){if(!this.#a)return t;const i=super.subscribeOnce(o,e);return this.#a.push(i),i}dispose(){this.#a&&(this.#a.dispose(),this.#a=null)}}class b extends Error{constructor(o="The operation was canceled."){super(o),Object.setPrototypeOf(this,b.prototype)}}class p{constructor(){this.#e=new h,this.#s=!1}#e;#s;get canceled(){return this.#s}cancel(){this.canceled||(this.#s=!0,this.#e?.publish("_"))}onCancel(o){return this.#e?.subscribeOnce("_",o)??null}dispose(){this.#e&&(this.#e.dispose(),this.#e=null)}}},"shared/dialogs/basic-dialog":(o,e,i)=>{i.r(e),i.d(e,{BasicDialog:()=>l,BasicDialogService:()=>d,DialogResult:()=>r,DialogTextKey:()=>c});var a=i(15215),s=i("aurelia-dialog"),t=i("aurelia-framework"),n=i(20057);let l=class{constructor(o){this.controller=o}async activate(o){this.config=o,this.controller.settings.lock=!o.cancelable}close(o){this.controller.ok(o.label.toString())}};var r,c;l=(0,a.Cg)([(0,t.autoinject)(),(0,a.Sn)("design:paramtypes",[s.DialogController])],l),function(o){o[o.OK=0]="OK",o[o.Yes=1]="Yes",o[o.No=2]="No",o[o.Cancel=3]="Cancel",o[o.Help=4]="Help"}(r||(r={})),function(o){o.OK="basic_dialog.ok",o.Yes="basic_dialog.yes",o.Confirm="basic_dialog.confirm",o.No="basic_dialog.no",o.Cancel="basic_dialog.cancel",o.Help="basic_dialog.help"}(c||(c={}));let d=class{#t;constructor(o){this.#t=o}show(o){return this.open(o).whenClosed((o=>o.wasCancelled?null:o.output))}open(o){return this.#t.open({viewModel:"shared/dialogs/basic-dialog",host:document.querySelector("#dialogs")??void 0,startingZIndex:1001,model:{...o,options:o.options.map((o=>"string"==typeof o||o instanceof n.LW?{label:o}:o)),align:o.align||"center",cancelable:o.cancelable??!0}})}async ok(o,e){return await this.show({message:o,messageParams:e,options:[{label:"basic_dialog.ok"}]}),r.OK}async yesNo(o,e,i,a,s,t,n){const l=a??c.Yes,d=s??c.No;return await this.show({size:"narrow",message:o,messageParams:e,options:i?[{label:l},{label:d,style:"primary"}]:[{label:d},{label:l,style:"primary"}],dontShowAgainFlag:t,dontShowAgainValue:n})===l?r.Yes:r.No}async help(o,e,i=[]){let a=[{label:"basic_dialog.ok",style:"primary"}];e&&a.push("basic_dialog.help"),a=a.concat(i);const s=await this.show({message:o,options:a});if("basic_dialog.help"===s)return window.open(e,"_blank"),r.Help;if("basic_dialog.ok"===s)return r.OK;for(const o of i)if("string"==typeof o){if(o===s)return o}else if(o instanceof n.LW){if(o.toString()===s)return s}else if(o.label===s)return s;return r.Cancel}};d=(0,a.Cg)([(0,t.autoinject)(),(0,a.Sn)("design:paramtypes",[s.DialogService])],d)},"shared/dialogs/basic-dialog.html":(o,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/close-button"></require> <require from="./basic-dialog.scss"></require> <ux-dialog class="basic-dialog ${config.className} align-${config.align} size-${config.size || \'full\'}"> <ux-dialog-header> <close-button if.bind="!controller.settings.lock" click.trigger="controller.cancel()" tabindex="0"></close-button> <header if.bind="config.headerLabel"> <span>${config.headerLabel | i18n}</span> </header> </ux-dialog-header> <ux-dialog-body> <div><p innerhtml.bind="config.message | i18n:config.messageParams | markdown"></p></div> </ux-dialog-body> <ux-dialog-footer> <button repeat.for="option of config.options" class="${config.options.length === 1 || option.style === \'primary\' ? \'\' : \'secondary\'}" click.delegate="close(option)"> <img if.bind="option.icon" src.bind="option.icon"> <span>${option.label | i18n}</span> </button> <label class="dont-show-again ${config.dontShowAgainValue ? \'checked\' : \'\'}" if.bind="config.dontShowAgainFlag" click.delegate="config.dontShowAgainValue = !config.dontShowAgainValue"> <span class="checkbox"></span> <span class="label">${\'basic_dialog.dont_remind_again\' | i18n}</span> </label> </ux-dialog-footer> </ux-dialog> </template> '},"shared/dialogs/basic-dialog.scss":(o,e,i)=>{i.r(e),i.d(e,{default:()=>u});var a=i(31601),s=i.n(a),t=i(76314),n=i.n(t),l=i(4417),r=i.n(l),c=new URL(i(81206),i.b),d=n()(s()),g=r()(c);d.push([o.id,`ux-dialog.basic-dialog{width:auto;min-width:300px;max-width:550px}ux-dialog.basic-dialog.size-narrow{max-width:300px}ux-dialog.basic-dialog ux-dialog-body{padding-bottom:20px}ux-dialog.basic-dialog ux-dialog-body a{color:var(--theme--highlight);transition:filter .15s;cursor:pointer}ux-dialog.basic-dialog ux-dialog-body a:hover{filter:brightness(1.2)}ux-dialog.basic-dialog ux-dialog-body img{margin-top:8px;max-width:100%;border-radius:5px}ux-dialog.basic-dialog ux-dialog-footer{padding:0}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;display:flex;padding-top:20px;justify-content:center}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again,ux-dialog.basic-dialog ux-dialog-footer .dont-show-again *{cursor:pointer}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again>*:first-child{margin-right:9px}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;width:15px;height:15px;background:rgba(0,0,0,0);border-color:rgba(255,255,255,.25)}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox,ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox *{cursor:pointer}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox:checked:before{opacity:1}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${g});mask:url(${g})}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .checkbox:before{left:1px;top:0;width:15px;height:11px;transform:scale(1)}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color)}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .label,ux-dialog.basic-dialog ux-dialog-footer .dont-show-again .label *{cursor:pointer}ux-dialog.basic-dialog ux-dialog-footer .dont-show-again.checked .checkbox:before{opacity:1}`,""]);const u=d}}]);
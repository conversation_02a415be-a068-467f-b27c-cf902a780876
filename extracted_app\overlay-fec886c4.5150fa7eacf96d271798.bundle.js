"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4143],{2690:(e,t,s)=>{s.d(t,{J:()=>n});var a=s(15215),i=s("aurelia-framework"),r=s(38777);let n=class{#e;attached(){this.#e=(new r.Vd).pushEventListener(document,"click",this.#t.bind(this)).pushEventListener(document,"auxclick",this.#t.bind(this))}detached(){this.#e?.dispose(),this.#e=null}#t(e){const t=this.#s(e.target);if(!t)return;if("auxclick"===e.type||e.ctrlKey||e.metaKey)return void e.preventDefault();const s=t.getAttribute("href");s&&"#"!==s?s.startsWith("#/")||(e.preventDefault(),window.open(s,"_blank")):e.preventDefault()}#s(e){for(;e;){if("A"===e.tagName)return e;e=e.parentElement}return null}};n=(0,a.Cg)([(0,i.autoinject)()],n)},10704:(e,t,s)=>{s.d(t,{r:()=>b});var a=s(15215),i=s(16928),r=s("aurelia-framework"),n=s(96610),o=s(20770),l=s(19072),c=s(90231),d=s(89356),h=s(41882),u=s("services/bugsnag/index"),g=s(54995),m=s(48881),p=s(38777),f=s(86824),v=s(82174);const y=(0,n.getLogger)("installations");let b=class{#a;#i;#r;#n;#o;#l;#c;#d;#h;#u;#g;#m;constructor(e,t,s,a,i){this.#d=!1,this.#u=new Map,this.#g=new Map,this.#m=!1,this.#a=e,this.#i=t,this.#r=s,this.#n=a,this.#o=i}initialize(){return this.#p(!0)}attached(){this.#a.onClosedToTray((()=>{this.#h&&y.debug("Closed to system tray. Pausing watcher"),this.#m=!0,this.#f()})),this.#a.onRestoredFromTray((()=>{this.#m=!1,this.#v(!0)}))}detached(){this.#y(),this.#u.clear(),this.#f()}#y(){this.#c&&(this.#c.dispose(),this.#c=null)}#f(){this.#h&&(this.#h.dispose(),this.#h=null)}#v(e=!1){this.#u.size>0&&!this.#h&&!this.#m&&(e?(y.debug("Restored from system tray. Resuming watcher"),this.#h=p.lE,this.#b()):this.#h=(0,p.Ix)((()=>this.#b()),7e3))}async#p(e=!1){if(this.#c||e){this.#y();try{await this.refreshApps(),await this.#w()}finally{this.#d?(this.#d=!1,this.#p(!0)):this.#c=(0,p.Ix)((()=>this.#p()),(0,f.H)(2,3))}}}watchGame(e){const t=this.#u.get(e)??0;return 0===t&&(y.debug(`Watching game ${e}`),this.#g.set(e,0)),this.#u.set(e,t+1),this.#v(),this.#b(),(0,p.nm)((()=>this.#I(e)))}#I(e){const t=this.#u.get(e)??0;1===t?(y.debug(`Unwatching game ${e}`),this.#g.delete(e),this.#u.delete(e)):this.#u.set(e,t-1),0===this.#u.size&&this.#f()}async#b(){try{const e=Array.from(this.#u.keys()),t=Array.from(new Set(Array.prototype.concat(...e.map((e=>this.catalog.games[e]?.correlationIds??[]))))),s=Object.fromEntries((await this.#i.findApps(t)).entries());for(const t of e){const e=this.catalog.games[t];if(e&&await this.#A(e,s)){y.debug(`Versions changed for game ${t}`);const s=(this.#g.get(t)??0)+1;if(this.#g.set(t,s),!(s>=12)){this.#c?await this.#p():this.#d=!0;break}12===s&&(0,u.report)(new Error(`Watch trigger limit exceeded for ${e.platformId} game ${t}`))}}}catch(e){y.error("Failed to refresh watched games",Array.from(this.#u.keys()),e)}finally{this.#f(),this.#v()}}async refreshApps(){this.#l?await this.#l:(this.#l=this.#S(),await this.#l.finally((()=>this.#l=null)))}async#S(){const e=await this.#i.getInstalledApps(),t={},s=Object.keys(this.installedApps);let a=e.reduce(((e,s)=>{const a=`${s.platform}:${s.sku}`;if(t[a]=s,e)return!0;{const e=this.installedApps[a];if(e){if(s.location!==e.location)return!0;{const t=s.alternateLocations,a=e.alternateLocations;return Array.isArray(t)!==Array.isArray(a)||!!Array.isArray(t)&&(t.length!==a?.length||t.some((e=>!a?.includes(e))))}}return!0}}),e.length!==s.length);a||(a=s.some((e=>!t[e]))),a&&await this.#r.dispatch(m.e1,t),await this.#r.dispatch(m.vk,"appsRefreshedAt")}async#w(){const e=Object.values(this.catalog.games),t=Object.keys(this.installedVersions).length,s=new Set;let a={},i=0,r=!1;for(let t,n=0;(t=e.slice(n,n+15)).length>0;n+=15)await Promise.all(t.map((async e=>{const t=await this.#A(e,this.installedApps,s);null!==t&&(a[e.id]=t,i++)}))),(i>=5||i>0&&n+15>=e.length)&&(await this.#r.dispatch(m.K8,a),a={},i=0,r=!0);(r||s.size!==t)&&await this.#r.dispatch(m.EP,s),r&&await this.#o.report()}async#A(e,t,s=null){const a=[],r=this.installedVersions[e.id]||[];let n=!1;for(const s of e.correlationIds){const i=r.find((e=>e.correlationId===s))||null,o=await this.#T(e,t[s],i);n||=o!==i,null!==o&&a.push({gameId:e.id,correlationId:s,version:o.version,modifiedAt:o.modifiedAt,createdAt:o.createdAt})}for(const t of r){const s=this.installedApps[t.correlationId];if(s?.platform===h.u){let r=null;try{r=await this.#n.getGameVersion(i.dirname(s.location),i.basename(s.location),s.platform,t)}catch{}n||=r!==t,a.push({gameId:e.id,correlationId:t.correlationId,version:r?.version??null,modifiedAt:r?.modifiedAt??null,createdAt:r?.createdAt??null})}else e.correlationIds.includes(t.correlationId)||(n=!0)}return a.length>0&&s?.add(e.id),n?a:null}async#T(e,t,s){if(!t||!e.versionPath)return null;try{return await this.#n.getGameVersion(t.location,e.versionPath,t.platform,s??void 0)}catch{return null}}};b=(0,a.Cg)([(0,g.m6)({setup:"initialize",teardown:"detached",selectors:{catalog:(0,g.$t)((e=>e.catalog)),installedApps:(0,g.$t)((e=>e.installedApps)),installedVersions:(0,g.$t)((e=>e.installedGameVersions))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[l.s,c.D,o.il,d.Q,v.u])],b)},16128:(e,t,s)=>{s.d(t,{X:()=>g});var a=s(15215),i=s(69278),r=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s(96610),l=s(19072),c=s(3972),d=s(54995);const h=(0,o.getLogger)("overlay"),u=["S-1-15-2-957691514-1856554628-2223506514-2735362290-2978057538-3373136399-3720596370","S-1-15-2-3188398512-4273922640-615417156-2416968318-1371955550-2718685005-1518716088"];let g=class{#C;#a;#k;#G;#P;#_;#E;constructor(e,t){this.#k=new r.EventAggregator,this.#G=!1,this.#P=!1,this.#E=[],this.#C=e,this.#a=t}get connected(){return this.connections>0}get connections(){return this.#E.length}get open(){return!!this.#_}onClientConnected(e){return this.#k.subscribe("connect",e)}onClientDisconnected(e){return this.#k.subscribe("disconnect",e)}onMessageReceived(e){return this.#k.subscribe("message",e)}subscriptionChanged(){this.subscription?this.#R():this.#V()}#O(){return!(this.#G||this.#P||this.#_||!this.subscription||"win32"!==this.#a.info.osPlatform)}attached(){setTimeout((()=>this.#R()),2e3)}detached(){this.#G=!0,this.#V()}#j(){return"\\\\.\\pipe\\WeMod\\Overlay"}#D(){setTimeout((()=>this.#R()),1e4)}async#R(){if(this.#O()){this.#P=!0;try{const e=await this.#L(this.#j());e.on("close",(()=>this.#$(e))),this.#_=e,h.debug("Awaiting connection")}catch(e){h.error("Failed to start server",e),this.#D()}finally{this.#P=!1}}}#$(e){e===this.#_&&(this.#E=[],this.#_=null,this.#O()&&this.#D())}#L(e){const t=(0,i.createServer)();return new Promise(((s,a)=>{let i=!1;t.on("error",(e=>{t.close((()=>{i||(i=!0,a(e))}))})),t.on("listening",(async()=>{try{await this.#C.grantFilePermissions(e,u,c.TB.Read|c.TB.Write)}catch(e){return i=!0,void a(e)}t.on("connection",(e=>this.#M(t,e))),i=!0,s(t)})),t.listen(e)}))}#M(e,t){e===this.#_?(h.debug("Client connected"),this.#E.push(t),t.on("end",(()=>{h.debug("Client disconnected");const e=this.#E.indexOf(t);e>=0&&(this.#E.splice(e,1),this.#k.publish("disconnect"))})),t.on("data",(e=>this.#W(e))),this.#k.publish("connect")):t.destroy()}broadcast(e){if(this.#_&&this.connections>0){const t=function(e){const t=JSON.stringify(e),s=Buffer.byteLength(t),a=Buffer.alloc(s+4);return a.writeUInt32LE(s,0),a.write(t,4),a}(e);this.#E.forEach((e=>{try{e.write(t,(e=>{e&&!["EPIPE","ERR_STREAM_DESTROYED"].includes(e.code)&&h.error("Error writing to socket",e)}))}catch{}}))}}#W(e){let t=0;for(;t+4<e.length;){const s=e.readInt32LE(t);t+=4;const a=e.toString("utf8",t,t+s);t+=s;try{this.#k.publish("message",JSON.parse(a))}catch(e){h.error("Error while handling client message",e)}}}#V(){if(this.#_){const e=this.#E;this.#E=[],this.#_.close((()=>{for(const t of e)try{t.destroy()}catch{}})),this.#_=null,e.length>0&&this.#k.publish("disconnect")}}};g=(0,a.Cg)([(0,d.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,d.$t)((e=>e.account?.subscription))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[c.Mz,l.s])],g)},28747:(e,t,s)=>{s.d(t,{a:()=>m});var a=s(15215),i=s("aurelia-framework"),r=s(16953),n=s(19072),o=s("services/bugsnag/index"),l=s(54995),c=s(64980);const d="https://www.google-analytics.com",h=globalThis,u=Object.freeze({crv:"P-256",ext:!0,key_ops:["verify"],kty:"EC",x:"TvK_46GZKD04AzNmtLO2AZNE95q50zddzR6TAtPHAq0",y:"uSqGv7tok8iUolBG2vx7aJJmqst3Dn90q5CSvqpebt0"});let g=null,m=class{#a;#N;#U;constructor(e){this.#U={},this.#x=function(){h.dataLayer?.push(arguments),h.dataLayer?.length&&h.dataLayer?.splice(15,1)},this.#a=e,h.dataLayer=[]}enabledChanged(e){"boolean"==typeof e&&(0,o.setEnabled)(this.enabled)}userIdChanged(e){"string"==typeof e&&this.#B({user_id:e})}async activate(){this.#a.isInTraySinceStartup&&this.#a.whenVisible((()=>{this.#N&&this.event("screen_view",{})})),this.#x("js",new Date),this.#x("consent","default",{ad_storage:"granted",ad_user_data:"granted",ad_personalization:"granted",analytics_storage:"granted"}),this.#x("set",{client_id:this.installation.id,send_page_view:!1,page_title:"",page_location:"",page_referrer:""}),this.#B({transport_url:d,user_id:this.userId??null,app_version:this.#a.info.version}),r.A.services.ga.adsTagId&&this.#x("config",r.A.services.ga.adsTagId,{groups:"ads"}),this.enabledChanged(this.enabled),this.userIdChanged(this.userId);const e=navigator.sendBeacon.bind(navigator);navigator.sendBeacon=(t,s)=>t.toString().startsWith(`${d}/g/collect?v=2`)&&"string"==typeof s?s.replaceAll("\r","").split("\n").every((s=>e(`${t}&${s}`))):e(t,s);const t=new p(r.A.appOrigin);h.gtagDocument=new v(t),this.installation.cookies?._gcl_aw&&t.setCookieValue("_gcl_aw",this.installation.cookies._gcl_aw),await this.#H()}async#H(){try{const e=await async function(){const e=await fetch("https://ga.wemod.com/bundle.js?ids="+[r.A.services.ga.measurementId,r.A.services.ga.adsTagId].filter((e=>!!e)).join(","));if(!e.ok)throw new Error(`GA bundle request failed with status ${e.status}.`);const t=e.headers.get("X-Signature");if(!t)throw new Error("Expected X-Signature header on GA bundle response.");const s=await e.arrayBuffer();if(g??=await crypto.subtle.importKey("jwk",u,{name:"ECDSA",namedCurve:"P-256"},!1,["verify"]),!await crypto.subtle.verify({name:"ECDSA",hash:"SHA-256"},g,Buffer.from(t,"base64"),s))throw new Error("Invalid signature on GA bunldle response.");return new TextDecoder("utf-8").decode(s)}(),t=document.createElement("script");t.text=e,document.head.append(t)}catch{setTimeout((()=>this.#H()),6e4)}}#x;#B(e){this.#U=Object.assign(this.#U,e),this.#x("config",r.A.services.ga.measurementId,this.#U)}event(e,t){this.enabled&&this.#x("event",e,(0,c.u)(t))}adEvent(e,t={}){this.enabled&&r.A.services.ga.adsTagId&&this.#x("event","conversion",(0,c.u)({...t,send_to:`${r.A.services.ga.adsTagId}/${e}`}))}user(e,t){this.#x("set","user_properties",{[e]:t})}screenView(e){this.#N&&e.name===this.#N.name&&e.class===this.#N.class||(this.#B({firebase_screen:e.name,firebase_screen_class:e.class}),this.#a.isInTraySinceStartup||this.event("screen_view",{firebase_previous_screen:this.#N?.name??null,firebase_previous_class:this.#N?.class??null,...e.params||null}),this.#N={name:e.name,class:e.class})}};m=(0,a.Cg)([(0,i.autoinject)(),(0,l.m6)({setup:"activate",teardown:"deactivate",selectors:{installation:(0,l.$t)((e=>e.installation)),userId:(0,l.$t)((e=>e.account?.uuid)),enabled:(0,l.$t)((e=>e.settings?.analytics))}}),(0,a.Sn)("design:paramtypes",[n.s])],m);class p{#F;constructor(e){this.#F=e}getCurrentUrl(){return this.#F+document.location.hash.substring(5)}getCookies(){return Object.entries(this.#z()).map((e=>e.join("="))).join("; ")}setCookie(e){const[t,s]=e.split(";",2)[0].trim().split("=",2);this.setCookieValue(t,s)}setCookieValue(e,t){const s=this.#z();s&&(s[e]=t||""),localStorage.setItem("gaCookie",JSON.stringify(s))}#z(){try{return JSON.parse(localStorage.getItem("gaCookie")||"{}")}catch{return{}}}}class f{constructor(e){this.helper=e;const t=this.url;this.origin=t.origin,this.protocol=t.protocol,this.host=t.hostname,this.hostname=t.hostname,this.port=t.port}get url(){return new URL(this.href)}get href(){return this.helper.getCurrentUrl()}get pathname(){return this.url.pathname}get search(){return this.url.search}get[Symbol.toStringTag](){return this.href}toString(){return this.href}}class v{constructor(e){this.helper=e,this.location=new f(e)}get cookie(){return this.helper.getCookies()}set cookie(e){this.helper.setCookie(e)}}},43050:(e,t,s)=>{s.d(t,{Y2:()=>P,f1:()=>y});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),o=s(32534),l=(s(52871),s(88120)),c=s(24008),d=s(70236),h=s(38777);const u=["catalog","gameHistory","installedGameVersions"],g={id:"all-games",getGames:e=>Object.values(e.catalog.games).filter((e=>(0,d.Lt)(e.flags,c.rT.Available))).sort(((e,t)=>(t.trainer?.createdAt??"").localeCompare(e.trainer?.createdAt??""))),mergeSimilarGames:!0},m=e=>{const t=e.gameHistory,s=Object.values(e.catalog.games).filter((t=>e.installedGameVersions.hasOwnProperty(t.id))).filter((e=>(0,d.Lt)(e.flags,c.rT.Available))).sort(((e,t)=>(e.trainer?.rank??0)-(t.trainer?.rank??0))),a=s.filter((t=>e.installedGameVersions[t.id]?.some((e=>e.version&&t.trainer?.supportedVersions.includes(e.version)))??!1)),i=s.filter((e=>"string"==typeof t[e.id]?.lastPlayedAt)),r=Object.values(e.correlatedUnavailableTitles).flatMap((e=>e.games)).filter((t=>t?.correlationIds?.some((t=>!!e.installedApps[t])))).map((t=>C(t.id,e))).filter((e=>!!e)),n=r.filter((e=>!!e&&"string"==typeof t[e.id]?.lastPlayedAt)),o=[...i,...n].sort(((e,s)=>(t[s?.id??""].lastPlayedAt??"").localeCompare(t[e?.id??""].lastPlayedAt??""))),l=a.filter((e=>!o.includes(e)));o.push(...l);const h=s.filter((e=>!o.includes(e)&&!a.includes(e)));o.push(...h);const u=r.filter((e=>!(!e||n.find((t=>t?.id===e.id)))));return o.push(...u),o.filter((e=>!!e))},p={id:"my-games-dashboard",getGames:e=>m(e).filter((t=>e.catalog.games[t.id]||"string"==typeof e.gameHistory[t.id]?.lastPlayedAt)),mergeSimilarGames:!0},f={id:"my-games-collection",getGames:e=>{let t=m(e);const s=t.filter((e=>(0,d.Lt)(e.flags,c.rT.Available))),a=S.getGames(e).filter((e=>!t.find((t=>t.titleId===e.titleId))));if(t=[...t,...a],!s.length&&t.length){const s=g.getGames(e).filter((e=>e.tags.includes("free"))).filter((t=>!e.installedGameVersions.hasOwnProperty(t.id))).slice(0,10);t=t.concat(s)}return t.sort(((t,s)=>{const a=e.catalog.titles[t.titleId]||e.correlatedUnavailableTitles[t.titleId],i=e.catalog.titles[s.titleId]||e.correlatedUnavailableTitles[s.titleId];return a.name.localeCompare(i.name)}))},mergeSimilarGames:!0,watchKeys:["favoriteTitles"]},v={id:"my-games-activity-feed",getGames:e=>[...Object.values(e.catalog.games).filter((t=>e.installedGameVersions.hasOwnProperty(t.id))),...S.getGames(e),...e.followedGames.map((t=>e.catalog.games[t.gameId])).filter((e=>!!e))].filter((e=>(0,d.Lt)(e.flags,c.rT.Available))).sort(((e,t)=>(t.trainer?.updatedAt??"").localeCompare(e.trainer?.updatedAt??""))),mergeSimilarGames:!0,watchKeys:["favoriteTitles","account"]},y={id:"my-games-remote",getGames:e=>{const t=Object.values(e.catalog.games).filter((t=>e.installedGameVersions.hasOwnProperty(t.id))).filter((e=>(0,d.Lt)(e.flags,c.rT.Available))).sort(((t,s)=>e.catalog.titles[t.titleId].name.localeCompare(e.catalog.titles[s.titleId].name))),s=e.gameHistory,a=t.filter((e=>"string"==typeof s[e.id]?.lastPlayedAt)).sort(((e,t)=>(s[t?.id??""]?.lastPlayedAt??"").localeCompare(s[e?.id??""].lastPlayedAt??""))),i=t.filter((e=>!a.includes(e)));return a.push(...i),a}},b={id:"recently-played",getGames:e=>{const t=e.gameHistory;return[...Object.keys(t).filter((e=>"string"==typeof t[e].lastPlayedAt)).filter((t=>e.catalog.games.hasOwnProperty(t))).map((t=>e.catalog.games[t])),...Object.keys(t).filter((e=>"string"==typeof t[e].lastPlayedAt)).filter((t=>!e.catalog.games[t])).map((t=>C(t,e))).filter((e=>!!e))].sort(((e,s)=>(t[s?.id??""]?.lastPlayedAt??"").localeCompare(t[e?.id??""]?.lastPlayedAt??"")))}},w={id:"most-popular",getGames:e=>Object.values(e.catalog.games).filter((e=>(0,d.Lt)(e.flags,c.rT.Active))).sort(((e,t)=>(e.trainer?.rank??0)-(t.trainer?.rank??0))),mergeSimilarGames:!0},I={id:"new-releases",getGames:e=>Object.values(e.catalog.games).filter((e=>(0,d.Lt)(e.flags,c.rT.Active))).sort(((e,t)=>{const s=(e.trainer?.createdAt??"").localeCompare(e.trainer?.updatedAt??"")>=0?e.trainer?.createdAt:e.trainer?.updatedAt;return(((t.trainer?.createdAt??"").localeCompare(t.trainer?.updatedAt??"")>=0?t.trainer?.createdAt:t.trainer?.updatedAt)??"").localeCompare(s??"")})),mergeSimilarGames:!0},A={id:"queue",getGames:e=>e.catalog.queue.map((t=>e.catalog.games[t]))},S={id:"favorites",getGames:e=>{const t=e.gameHistory;return[...Object.keys(e.favoriteTitles).filter((t=>e.catalog.titles[t]&&e.catalog.titles[t].gameIds)).flatMap((t=>e.catalog.titles[t].gameIds)).map((t=>e.catalog.games[t])).filter((e=>(0,d.Lt)(e.flags,c.rT.Available))),...Object.keys(e.favoriteTitles).filter((t=>e.correlatedUnavailableTitles[t])).flatMap((t=>e.correlatedUnavailableTitles[t].games)).map((t=>C(t.id,e)))].sort(((t,s)=>{const a=e.catalog.titles[t?.titleId??""]||e.correlatedUnavailableTitles[t?.titleId??""],i=e.catalog.titles[s?.titleId??""]||e.correlatedUnavailableTitles[s?.titleId??""];return a?.name.localeCompare(i?.name)})).sort(((e,s)=>(t[s?.id??""]?.lastPlayedAt??"").localeCompare(t[e?.id??""]?.lastPlayedAt??""))).filter((e=>!!e))},watchKeys:["favoriteTitles"],mergeSimilarGames:!0};function T(e,t){const s=e.localeCompare(t);return s>0&&"steam"===e?-1:s<0&&"steam"===t||s<0&&"standalone"===e?1:s>0&&"standalone"===t?-1:s}function C(e,t){const s=Object.values(t.correlatedUnavailableTitles).find((t=>t.games?.some((t=>t.id===e))));if(s){const t=s.games.find((t=>t.id===e));return t?{id:t.id,titleId:s.id,platformId:t.platformId,correlationIds:t.correlationIds,purchaseUris:[],tags:[],flags:t.flags}:null}return null}class k{#k;#r;#q;constructor(e,t){this.games=[],this.#k=new i.EventAggregator,this.config=t,this.#r=e}initialize(){this.#q=this.#r.state.pipe((0,l.F)(((e,t)=>u.every((s=>e[s]===t[s]))))).subscribe((e=>{this.state=e,this.updateGames()})),this.#q=this.#r.state.pipe((0,l.F)(((e,t)=>(this.config.watchKeys??[]).every((s=>e[s]===t[s]))))).subscribe((e=>{this.state=e,this.updateGames(!0)}))}updateGames(e){if(!this.state)return;const t=this.config.getGames(this.state);!e&&this.games&&t.length===this.games.length&&JSON.stringify(t)===JSON.stringify(this.games)||(this.games=t,this.#k.publish("updated"))}dispose(){this.#q?.unsubscribe(),this.#q=null}onUpdated(e){return this.#k.subscribe("updated",e)}}class G{#K;#J;constructor(e,t){this.items=[],this.#K=e,this.filter=t}dispose(){this.#J.dispose()}initialize(){this.#J=this.#K.onUpdated((()=>this.updateItems())),this.updateItems()}updateItems(){const e=this.#K.state;if(!e)return;const t=this.#K.games;let s=this.#Y(t).map((t=>{return e.catalog.games[t.id]?function(e,t){const s=e.catalog.games[t],a=e.catalog.titles[s.titleId],i=e.gameHistory[s.id],r=s.trainer?.createdAt?(0,o.A)(new Date(s.trainer.createdAt),Date.now())<=7:null,n=s.trainer?.updatedAt?(0,o.A)(new Date(s.trainer.updatedAt),Date.now())<=7:null;return{titleId:a.id,titleThumbnail:a.thumbnail,titleName:a.name,titleTerms:[...a.terms],gameId:s.id,platformIds:[s.platformId],correlationIds:[...s.correlationIds],gameEdition:s.edition??null,creator:s.creatorId?e.catalog.creators[s.creatorId].username:null,creatorAvatar:s.creatorId?e.catalog.creators[s.creatorId].avatar:null,players:s.trainer?.players??null,secondsPlayed:e.gameHistory[s.id]?.playDuration||0,createdAt:s.trainer?.createdAt??null,updatedAt:s.trainer?.updatedAt??null,lastPlayedAt:(i&&i.lastPlayedAt)??null,isNew:r??!1,isUpdated:(n&&!r)??!1,isAvailable:(0,d.Lt)(s.flags,c.rT.Available),isInstalled:e.installedGameVersions.hasOwnProperty(s.id),isChoice:s.tags.includes("choice"),isFree:s.tags.includes("free"),isFavorite:!!e.favoriteTitles[s.titleId],genres:a.genreIds.map((t=>e.catalog.genres[t]?.slug.replaceAll("-","_"))),rank:s.trainer?.rank??0}}(e,t.id):e.correlatedUnavailableTitles[t.titleId]?{...(s=e.correlatedUnavailableTitles[t.titleId],{titleId:s.id,titleThumbnail:s.thumbnail,titleName:s.name,titleTerms:[...s.terms],gameId:1===s.games.length?s.games[0].id:null,platformIds:s.games.map((e=>e.platformId)),correlationIds:s.games.flatMap((e=>e.correlationIds)),gameEdition:null,creator:null,creatorAvatar:null,players:null,secondsPlayed:0,createdAt:null,updatedAt:null,lastPlayedAt:null,isNew:!1,isUpdated:!1,isAvailable:!1,isInstalled:true,isChoice:!1,isFree:!1,isFavorite:!1,genres:[],rank:0}),platformIds:[t.platformId]}:void 0;var s})).filter((e=>!!e));s=this.#K.config.mergeSimilarGames?function(e){const t=[],s=[],a=e.reduce(((e,t)=>(e[t.titleId]||(e[t.titleId]=[]),e[t.titleId].push(t),e)),{});return e.forEach((e=>{if(s.includes(e))return;const i=a[e.titleId];if(i.length>1){s.push(...i),i.push(e);const a=[],r=[];for(const e of i)a.includes(e.platformIds[0])||a.push(e.platformIds[0]),e.gameId&&!r.includes(e.gameId)&&r.push(e.gameId);const n=[...i].sort(((e,t)=>(t.createdAt??"").localeCompare(e.createdAt??"")))[0].createdAt,l=[...i].sort(((e,t)=>(t.updatedAt??"").localeCompare(e.updatedAt??"")))[0].updatedAt,c=n?(0,o.A)(new Date(n),Date.now())<=7:null,d=l?(0,o.A)(new Date(l),Date.now())<=7:null,h={...e,gameEdition:null,gameId:1===r.length?r[0]:null,platformIds:a.sort(T),players:i.reduce(((e,t)=>e+(t.players??0)),0),secondsPlayed:i.reduce(((e,t)=>e+t.secondsPlayed),0),createdAt:n,updatedAt:l,lastPlayedAt:[...i].sort(((e,t)=>(t.lastPlayedAt??"").localeCompare(e.lastPlayedAt??"")))[0].lastPlayedAt,isInstalled:i.some((e=>e.isInstalled)),isChoice:i.some((e=>e.isChoice)),isNew:c??!1,isUpdated:(d&&!c)??!1};t.push(h)}else t.push(e)})),t}(s):s,this.totalItems=s.length,this.filter?.maxItems&&(s=s.slice(0,this.filter.maxItems)),this.filter?.sort&&s.sort(((e,t)=>this.filter?.sort?.(e,t)??0)),this.items=s}#Y(e){return this.filter&&(this.filter.tags&&(e=e.filter((e=>e.tags.some((e=>this.filter.tags?.includes(e)))))),this.filter.genres?.length&&(e=e.filter((e=>{const t=this.#K.state?.catalog.titles[e.titleId];return t&&t.genreIds.some((e=>this.filter.genres?.includes(e)))})))),e}}(0,a.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.toView}),(0,a.Sn)("design:type",Number)],G.prototype,"totalItems",void 0);let P=class{#r;#X;constructor(e){this.#X=new Map,this.#r=e}attached(){}detached(){}async initialize(){const e=[p,f,v,w,g,b,I,A,S];for(const t of e)await(0,h.Wn)(),this.#Z(t)}#Z(e){const t=new k(this.#r,e);return t.initialize(),this.#X.set(e,t),t}#Q(e){return this.#X.get(e)||this.#Z(e)}getFilteredFeed(e,t){const s=this.#Q(e),a=new G(s,t);return a.initialize(),a}};P=(0,a.Cg)([(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[n.il])],P)},61116:(e,t,s)=>{s.d(t,{c:()=>n});var a=s(15215),i=s("aurelia-framework"),r=s(54995);let n=class{attached(){}detached(){}get creators(){if(!this.catalog)return[];const e={};return Object.values(this.catalog.games).filter((e=>e.trainer)).forEach((t=>{t.trainer?.contributorIds.forEach((t=>{e[t]||(e[t]=0),e[t]++}))})),Object.values(this.catalog.creators).filter((e=>e.active)).map((t=>({...t,gameCount:e[t.id]??0}))).sort(((e,t)=>t.gameCount-e.gameCount))}};(0,a.Cg)([(0,i.computedFrom)("catalog"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],n.prototype,"creators",null),n=(0,a.Cg)([(0,r.m6)({setup:"attached",teardown:"detached",selectors:{catalog:(0,r.$t)((e=>e.catalog))}}),(0,i.autoinject)()],n)},72208:(e,t,s)=>{s.d(t,{u:()=>u});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(20770),o=s(83802),l=s(21795),c=s(54995),d=s(88849),h=s(48881);let u=class{#ee;#r;#k;#te;constructor(e,t,s){this.#ee=e,this.#r=t,this.#k=s}attached(){this.#te=this.#ee.onTrainerActivated((e=>{e.onValueSetError((e=>{e.cheatId&&this.markUnread(e.cheatId)}))}))}detached(){this.#te?.dispose(),this.#te=null}markRead(e,t,s,a,i){if(this.#k.publish(new l.mN(t,s,a&&i?"both":a?"instructions":"description",e)),!a)return;const r=(0,d.YZ)(a);this.cheatBlueprintInstructionsRead[s]!==r&&this.#r.dispatch(h.W,s,r)}markUnread(e){this.#r.dispatch(h.W,e,null)}areInstructionsRead(e,t){return!t||this.cheatBlueprintInstructionsRead?.[e]===(0,d.YZ)(t)}};u=(0,a.Cg)([(0,c.m6)({setup:"attached",teardown:"detached",selectors:{cheatBlueprintInstructionsRead:(0,c.$t)((e=>e.cheatBlueprintInstructionsRead))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[o.jR,n.il,i.EventAggregator])],u)},82174:(e,t,s)=>{s.d(t,{u:()=>d});var a=s(15215),i=s("aurelia-framework"),r=s(68663),n=s(41882),o=s(54995),l=s(38777),c=s(86824);let d=class{#se;#ae;constructor(e){this.#se=e}attached(){this.#ie()}detached(){}#ie(){this.#ae=(0,l.Ix)((()=>this.report()),(0,c.H)(15,20))}#re(){this.#ae&&(this.#ae.dispose(),this.#ae=null)}async report(){if(this.#re(),!this.enabled)return void this.#ie();let e=!1;const t={};if(Object.keys(this.installedVersions).forEach((s=>{const a=Array.from(new Set(this.installedVersions[s].filter((e=>this.#ne(e))).map((e=>e.version)).filter((e=>!!e))));a.length>0&&(t[s]=a,e=!0)})),e)try{await this.#se.reportInstalledGameVersions(t)}catch{}this.#ie()}#ne(e){if(null===e.version)return!1;const t=this.installedApps[e.correlationId];if(t){if("uwp"===t.platform&&t.location.endsWith("\\Content"))return!1;if(t.platform===n.u)return!1}return!0}};d=(0,a.Cg)([(0,o.m6)({setup:"attached",teardown:"detached",selectors:{enabled:(0,o.$t)((e=>e.settings?.analytics)),installedVersions:(0,o.$t)((e=>e.installedGameVersions)),installedApps:(0,o.$t)((e=>e.installedApps))}}),(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[r.x])],d)},98300:(e,t,s)=>{s.d(t,{Hy:()=>E});var a=s(15215),i=s("aurelia-event-aggregator"),r=s("aurelia-framework"),n=s(68663),o=s(72208),l=s(7892),c=s(83802),d=s(19072),h=s(64931),u=s(11087),g=s(3972),m=s(24008),p=s(64706),f=s(20057),v=s(54995),y=s(49442),b=s(70236),w=s(38777),I=s(90211),A=s(62914),S=s(78563),T=s(16128),C=s(10704),k=s(45953);const G="WeMod.WeModOverlay_t7g4ya3tqt6sw",P="9P2C17TK96BB",_=new I.R(5,721,5282,0);let E=class{#_;#oe;#a;#C;#le;#ce;#de;#he;#ue;#ge;#me;#pe;#fe;#se;#k;#e;#ve;#ye;constructor(e,t,s,a,i,r,n,o,l,c,d,h,u,g){this.status="disconnected",this.#_=e,this.#oe=t,this.#a=s,this.#C=a,this.#le=i,this.#ce=r,this.#de=n,this.#he=o,this.#ue=l,this.#ge=c,this.#me=d,this.#pe=h,this.#fe=u,this.#se=g}attached(){this.#be(),this.#e=new w.Vd([this.#k=new w._M,this.#oe.onNewTrainer((e=>this.#we(e))),this.#_.onClientConnected((()=>this.#Ie())),this.#_.onClientDisconnected((()=>this.#Ae())),this.#_.onMessageReceived((e=>this.#Se(e))),this.#he.onCheatStatesChanged((e=>{if(this.#ve){const t=this.#ve.getMetadata(c.vO).info;e.gameId===t.gameId&&this.#Te()}})),this.#ce.onLocaleChanged((()=>this.#Ce())),this.#ge.onDisplayTrainerChanged(this.#ke.bind(this)),this.#fe.subscribe(S.v,(e=>this.#Ge(e.titleId,e.historyItem)))])}detached(){this.#e.dispose()}themeChanged(){this.#Ce()}accessTokenChanged(){this.#Ce()}onConnectionStatusChanged(e){return this.#k.subscribe("status",e)}#Ie(){this.#Pe("connected")}#Ae(){0===this.#_.connections&&this.#Pe("disconnected")}#Pe(e){e!==this.status&&(this.status=e,this.#k.publish("status",e))}#Se(e){switch(e.type){case"broadcast_request":switch(e.request){case"config":this.#Ce();break;case"trainer_state":this.#Te();break;case"assistant_info":this.#_e();break;case"auth_code":this.#Ee()}break;case"trainer_value":e.stateId===this.#ye&&this.#ve?.isActive()?this.#ve.setValue(e.name,e.value,4,e.cheatId):this.#Te();break;case"cheat_instructions_read":this.#ue.markRead("overlay",this.#ve?.getMetadata(c.vO)?.info.gameId??"",e.cheatId,e.instructions);break;case"event":this.#me.event(e.name,e.data,e.dispatch??A.Io);break;case"assistant_history_item":{const t=this.#pe.getAssistant(e.titleId);t&&t.history.add(p.gG.deserialize(e.historyItem));break}case"open_uri":window.open(e.uri,"_blank")}}#be(){this.#ye=Date.now()}#we(e){this.#ve=e,e.onActivated((()=>this.#Te())),e.onValueSet((e=>{this.#_.connected&&this.#Re({type:"trainer_value",stateId:this.#ye,name:e.name,value:e.value})})),e.onEnded((()=>{this.#ve=null,this.#be(),this.#Te()})),this.#be(),this.#Te()}#Te(){if(this.#_.connected)if(this.#ve){const e=this.#ve.getMetadata(c.vO).info;this.#Re({type:"trainer_state",stateId:this.#ye,blueprint:{cheats:e.blueprint.cheats,notes:e.blueprint.notes},strings:this.#Ve(e.gameId),values:this.#ve.isActive()?Object.fromEntries(this.#ve.values.entries()):null,cheatStates:this.#he.cheatStates})}else this.#Re({type:"trainer_state",stateId:this.#ye,blueprint:null,strings:null,values:null,cheatStates:null})}#Ve(e){return this.gameTranslations[e]?.[this.#ce.getEffectiveLocale().toString()]??null}#Ce(){this.#_.connected&&this.#Re({type:"config",theme:this.theme,locale:this.#ce.getEffectiveLocale().toString()})}#_e(){if(!this.#_.connected)return;const e=this.#ge.displayTrainer,t=e?this.catalog.titles[e.titleId]:null,s=e?.titleId??"";this.#Re({type:"assistant_info",titleId:s,titleName:t?.name??"",hasAssistant:(0,b.Lt)(t?.flags??0,m.D1.HasAssistant),history:this.assistantHistory[s]||[]})}async#Ee(){if(!this.#_.connected)return;const e=await this.#se.requestOverlayAuthCode().catch((()=>null));null!==e&&this.#Re({type:"auth_code",code:e})}#Ge(e,t){this.#_.connected&&"overlay"!==t.client&&this.#Re({type:"assistant_history_item",titleId:e,historyItem:t.serialize()})}#Re(e){this.#_.broadcast(e)}isSystemSupported(){if("win32"!==this.#a.info.osPlatform||"x64"!==this.#a.info.osArch)return!1;const e=this.#a.info.osVersion.split(".");return e.length>=3&&"10"===e[0]&&"0"===e[1]&&parseInt(e[2],10)>=17763}async isAppInstalled(){return null!==await this.#C.getInstalledPackageId(G)}async isGameBarEnabled(){return!!await this.#le.queryValue("HKEY_CURRENT_USER\\System\\GameConfigStore\\GameDVR_Enabled")&&0!==await this.#le.queryValue("HKEY_CURRENT_USER\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\GameDVR\\AppCaptureEnabled")}async openInStore(e){this.#me.event("overlay_install_click",{location:e},A.Io);const t=await this.#C.getInstalledPackageId("Microsoft.XboxGamingOverlay_8wekyb3d8bbwe"),s=t&&I.R.parse(h.L.parse(t).version)?.gte(_)?`ms-gamebar://launchforeground/activate/StoreWidget?productId=${P}`:`ms-windows-store://pdp/?ProductId=${P}`;await this.#a.launchExternal(s)||window.open(`https://www.microsoft.com/store/apps/${P}`,"_blank")}async openGameBarSettings(){await this.#a.launchExternal("ms-settings:gaming-gamebar")||window.open("https://support.xbox.com/help/games-apps/game-setup-and-play/troubleshoot-game-bar-windows","_blank")}open(){return this.#a.launchExternal(`ms-gamebar://launchforeground/activate/${G}_App_WeMod.Overlay.Trainer`)}async refreshFeatureStatus(){let e;return e=this.isSystemSupported()?await this.isAppInstalled()?await this.isGameBarEnabled()?"installed":"game-bar-disabled":"not-installed":"unsupported",(this.featureStatus&&"installed"===e||"installed"===this.featureStatus)&&this.#de.refreshApps().catch(y.Y),this.featureStatus=e,e}#ke(){this.#_e()}};E=(0,a.Cg)([(0,v.m6)({setup:"attached",teardown:"detached",selectors:{theme:(0,v.$t)((e=>e.settings?.theme)),gameTranslations:(0,v.$t)((e=>e.gameTranslations)),catalog:(0,v.$t)((e=>e.catalog)),assistantHistory:(0,v.$t)((e=>e.assistantHistory))}}),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[T.X,c.jR,d.s,g.Mz,u.J,f.F2,C.r,l.p,o.u,k.m,A.j0,S.s,i.EventAggregator,n.x])],E)}}]);
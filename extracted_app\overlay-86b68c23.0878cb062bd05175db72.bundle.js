"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1754],{49740:(t,e,i)=>{i.d(e,{v:()=>d});var o=i(15215),n=i("aurelia-framework"),s=i(92465),a=i(54995),r=i(6745);let d=class{#t;#e;constructor(t){this.notifications=[],this.#t=new s.Vd,this.#e=t}attached(){this.#t.push((0,s.Ix)((()=>this.#i()),5e3)),this.#t.push((0,s.Ix)((()=>this.#o()),1e4)),this.#t.push(this.#e.onOverlayNotificationRequested((t=>this.addOrUpdateNotification(t)))),this.#t.push(this.#e.onTrainerValueSet((t=>this.#n(t))))}detached(){this.#t.dispose(),this.notifications.forEach((t=>this.#s(t)))}addOrUpdateNotification(t){if(!t.force&&!this.overlaySettings.enableNotifications)return;let e=t.id?this.#a(t.id):void 0;e?Object.assign(e,t):(e={id:`notification_${this.notifications.length+1}`,...t},this.notifications.push(e)),e.removeTimeout?.dispose(),e.removeTimeout=(0,s.Ix)((()=>{e&&this.#s(e)}),4e3),this.notifications.length>4&&this.#s(this.notifications[0])}#s(t){const e=t.id?this.#a(t.id):void 0;if(e){const t=this.notifications.indexOf(e);this.notifications.splice(t,1),e.removeTimeout?.dispose()}}#a(t){return this.notifications.find((e=>e.id===t))}#i(){this.addOrUpdateNotification({force:!0,id:"overlay_launch",type:"launch",data:null})}#n(t){if(![1,3,7].includes(t.source))return;const e=this.#e.trainerInfo.blueprint.cheats.find((e=>e.uuid===t.cheatId));e&&this.addOrUpdateNotification({id:`mod_${e.uuid}`,type:"mod_value_set",data:{mod:e,value:t.value}})}#o(){this.settings.enableCapture&&this.addOrUpdateNotification({id:"instant_highlight_announcement",type:"instant_highlight_announcement",data:null})}};(0,o.Cg)([n.observable,(0,o.Sn)("design:type",Array)],d.prototype,"notifications",void 0),d=(0,o.Cg)([(0,n.autoinject)(),(0,a.m6)({setup:"attached",teardown:"detached",selectors:{windowSettings:(0,a.$t)((t=>t.overlayWindowSettings)),overlaySettings:(0,a.$t)((t=>t.overlaySettings)),settings:(0,a.$t)((t=>t.settings))}}),(0,o.Sn)("design:paramtypes",[r.xr])],d)},66811:(t,e,i)=>{i.d(e,{o:()=>p});var o=i(15215),n=i("aurelia-framework"),s=i(20770),a=i(62914),r=i(24008),d=i(92465),l=i(54995),h=i(70236),w=i(6745),c=i(79522),g=i(42518);let p=class{#r;#d;#l;constructor(t,e){this.windows=[],this.customWindowTitles={},this.#d=!1,this.#l=t,this.host=e}attached(){this.initializeWindows(),this.#r=(0,d.yB)(window,"resize",(()=>this.#h()))}detached(){this.#r?.dispose()}async setWindowSettings(t,e){await this.#l.dispatch(c.C_,this.host.trainerInfo.game.id,t.id,{...this.gameWindowSettings?.[t.id],...e})}async initializeWindows(){this.windows.length=0,this.windows.push(g.KU),this.host.trainerInfo.maps.length>0&&this.windows.push(g.so),this.windows.push({...g.U3,enabled:this.isGameGuideEnabled})}async open(t,e){await this.setWindowSettings(t,{open:!0}),this.host.event("overlay_window_open",{id:t.id,trigger:e},a.Io)}async close(t,e){this.host.event("overlay_window_close",{id:t.id,trigger:e},a.Io),await this.setWindowSettings(t,{open:!1})}toggle(t,e){this.gameWindowSettings?.[t.id].open?this.close(t,e):this.open(t,e)}async resetSettings(){this.host.event("overlay_window_reset",{},a.Io),await this.#l.dispatch(c.Yh,this.host.trainerInfo.game.id)}async moveToTop(t,e){await this.#l.dispatch(c.ZS,this.host.trainerInfo.game.id,t.id,e)}get gameWindowSettings(){if(this.windowSettings)return this.windowSettings[this.host.trainerInfo.game.id]}#h(){}get isWindowTransforming(){return this.#d}startTransformingWindow(){this.#d=!0}stopTransformingWindow(){this.#d=!1}setCustomWindowTitle(t,e){this.customWindowTitles[t]=e}get isGameGuideEnabled(){const t=!!this.account?.subscription,e=(0,h.Lt)(this.host.trainerInfo.title.flags,r.D1.HasAssistant);return!this.disableAssistant&&t&&e}accountChanged(){this.#w()}disableAssistantChanged(){this.#w()}#w(){const t=this.windows.find((t=>t.id===g.U3.id));this.isGameGuideEnabled||this.close(g.U3,"auto"),t&&(t.enabled=this.isGameGuideEnabled)}};(0,o.Cg)([(0,n.computedFrom)("windowSettings"),(0,o.Sn)("design:type",Object),(0,o.Sn)("design:paramtypes",[])],p.prototype,"gameWindowSettings",null),(0,o.Cg)([(0,n.computedFrom)("account.subscription","disableAssistant","host.trainerInfo.title.flags"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],p.prototype,"isGameGuideEnabled",null),p=(0,o.Cg)([(0,n.autoinject)(),(0,l.m6)({setup:"attached",teardown:"detached",selectors:{windowSettings:(0,l.$t)((t=>t.overlayWindowSettings)),account:(0,l.$t)((t=>t.account)),disableAssistant:(0,l.$t)((t=>t.settings.disableAssistant))}}),(0,o.Sn)("design:paramtypes",[s.il,w.xr])],p)},"overlay/resources/elements/overlay-window":(t,e,i)=>{i.r(e),i.d(e,{EVENT_APPLY_WINDOW_GEOMETRY:()=>c,EVENT_PERSIST_WINDOW_GEOMETRY:()=>w,OverlayWindow:()=>g});var o=i(15215),n=i("aurelia-event-aggregator"),s=i("aurelia-framework"),a=i(62914),r=i(92465),d=i(6745),l=i(66811),h=i(42518);const w="persist-window-geometry",c="apply-window-geometry";let g=class{#c;#g;#p;#m;#t;#u;constructor(t,e,i,o){this.windowManager=t,this.dragging=!1,this.resizing=!1,this.#c=e,this.#u=i,this.host=o}attached(){this.#y(),this.#f(),this.#v(),this.#t=new r.Vd([(0,r.yB)(window,"resize",(()=>this.#y())),(0,r.yB)(this.#c,"mousedown",this.moveToTop.bind(this)),(0,r.yB)(window,"mousemove",this.#b.bind(this)),(0,r.yB)(window,"mousemove",this.#x.bind(this)),(0,r.yB)(window,"mouseup",this.#S.bind(this)),this.#u.subscribe(w,(()=>this.#z())),this.#u.subscribe(c,(()=>this.#f()))])}detached(){this.#t?.dispose()}toggledPinned(){this.windowManager.setWindowSettings(this.config,{pinned:!this.settings?.pinned})}handleDragStart(t){if(t.target===this.opacitySliderEl||this.opacitySliderEl?.contains(t.target))return t.preventDefault(),void t.stopPropagation();this.dragging=!0,this.#W(t)}async#b(t){if(!this.dragging)return;const e=t.clientX-this.#g,i=t.clientY-this.#p;if(0!==e||0!==i){this.#M(),await new Promise((t=>requestAnimationFrame(t)));const t=p(this.#m.x+e,0,window.innerWidth-this.#m.width),o=p(this.#m.y+i,0,window.innerHeight-this.#m.height);this.#c.style.left=`${t}px`,this.#c.style.top=`${o}px`}}handleResizeStart(t){this.resizing=!0,this.#W(t)}async#x(t){if(!this.resizing)return;const e=t.clientX-this.#g,i=t.clientY-this.#p;if(0!==e||0!==i){this.#M(),await new Promise((t=>requestAnimationFrame(t)));const t=p(this.#m.width+e,h.LP[this.config.size].width,window.innerWidth-this.#m.x),o=p(this.#m.height+i,h.LP[this.config.size].height,window.innerHeight-this.#m.y);this.#c.style.width=`${t}px`,this.#c.style.height=`${o}px`}}#W(t){this.#m=this.#c.getBoundingClientRect(),this.#g=t.clientX,this.#p=t.clientY,this.transformMoved=!1,this.windowManager.startTransformingWindow()}#M(){this.transformMoved||this.#u.publish(w),this.transformMoved=!0}async#S(){this.transformMoved&&(await this.#z(),this.moveToTop(),this.resizing&&this.host.event("overlay_window_resize",{id:this.config.id,width:Math.floor(this.customGeometry?.width??0),height:Math.floor(this.customGeometry?.height??0)},a.Io),this.dragging&&this.host.event("overlay_window_drag",{id:this.config.id,x:Math.floor(this.customGeometry?.x??0),y:Math.floor(this.customGeometry?.y??0)},a.Io)),this.dragging=!1,this.resizing=!1,this.transformMoved=!1,this.windowManager.stopTransformingWindow()}#y(){this.resolution=`${window.innerWidth}x${window.innerHeight}`}get settings(){if(this.windowManager.gameWindowSettings)return this.windowManager.gameWindowSettings[this.config.id]}get customGeometry(){if(!this.settings)return;const t=this.settings.resolutionGeometry[this.resolution];return t||this.#G(),t}resolutionChanged(){this.#f(),this.#S()}#f(){this.customGeometry?(this.#c.style.left=`${this.customGeometry.x}px`,this.#c.style.top=`${this.customGeometry.y}px`,this.#c.style.width=`${this.customGeometry.width}px`,this.#c.style.height=`${this.customGeometry.height}px`,this.#c.style.zIndex=this.customGeometry.zIndex.toString()):this.#G()}#G(){this.#c.style.left="",this.#c.style.top="",this.#c.style.width="",this.#c.style.height="",this.#c.style.zIndex=""}async#z(){const t=this.#c.getBoundingClientRect(),e={...this.settings?.resolutionGeometry};e[this.resolution]={x:t.x,y:t.y,width:t.width,height:t.height,zIndex:e[this.resolution]?.zIndex??0},await this.windowManager.setWindowSettings(this.config,{resolutionGeometry:e}),this.#f()}async moveToTop(){this.customGeometry&&(await this.windowManager.moveToTop(this.config,this.resolution),this.#u.publish(c))}handleOpacityChange(t){this.host.event("overlay_window_opacity_change",{id:this.config.id,opacity:t},a.Io),this.windowManager.setWindowSettings(this.config,{opacity:t})}get isPro(){return!!this.host.account?.subscription}async#v(){const t=this.windowManager.gameWindowSettings&&Object.values(this.windowManager.gameWindowSettings).some((t=>!!t.resolutionGeometry[this.resolution])),e=this.settings?.resolutionGeometry[this.resolution];t&&!e&&(await this.#z(),this.moveToTop())}};function p(t,e,i){return t<e?e:t>i?i:t}(0,o.Cg)([(0,s.bindable)({defaultBindingMode:s.bindingMode.toView}),(0,o.Sn)("design:type",Boolean)],g.prototype,"pinned",void 0),(0,o.Cg)([(0,s.bindable)({defaultBindingMode:s.bindingMode.toView}),(0,o.Sn)("design:type",Object)],g.prototype,"config",void 0),(0,o.Cg)([s.observable,(0,o.Sn)("design:type",String)],g.prototype,"resolution",void 0),(0,o.Cg)([(0,s.computedFrom)("config","windowManager.gameWindowSettings"),(0,o.Sn)("design:type",Object),(0,o.Sn)("design:paramtypes",[])],g.prototype,"settings",null),(0,o.Cg)([(0,s.computedFrom)("resolution","windowManager.gameWindowSettings"),(0,o.Sn)("design:type",Object),(0,o.Sn)("design:paramtypes",[])],g.prototype,"customGeometry",null),(0,o.Cg)([(0,s.computedFrom)("host.account.subscription"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],g.prototype,"isPro",null),g=(0,o.Cg)([(0,s.autoinject)(),(0,o.Sn)("design:paramtypes",[l.o,Element,n.EventAggregator,d.xr])],g)},"overlay/resources/elements/overlay-window.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});var o=i(14385),n=i.n(o),s=new URL(i(18285),i.b);const a='<template class="size-${config.size} ${pinned ? \'overlay-pinned\' : \'\'} ${settings.pinned ? \'pinned\' : \'\'} ${customGeometry ? \'custom-geometry\' : \'\'}"> <require from="./overlay-window.scss"></require> <require from="../../../shared/resources/elements/range-input"></require> <header> <div class="header-background" mousedown.delegate="handleDragStart($event)"></div> <span class="icon"> <template if.bind="config.icon">${config.icon}</template> <img else class="logo" src="'+n()(s)+'"> </span> <span class="title"> <template if.bind="windowManager.customWindowTitles[config.id]">${windowManager.customWindowTitles[config.id]}</template> <template else>${config.titleKey | i18n}</template> </span> <nav class="actions" inert.bind="(dragging && transformMoved) || (pinned && settings.pinned)"> <slot name="actions"></slot> <button class="action opacity" if.bind="isPro && config.supportsOpacity"> <div class="opacity-slider" ref="opacitySliderEl"> <range-input use-overlay.bind="true" min.bind="0" max.bind="100" step.bind="1" value.bind="settings.opacity" change.call="handleOpacityChange(value)"></range-input> </div> </button> <button class="action pin ${settings.pinned ? \'pinned\' : \'\'}" click.delegate="toggledPinned()" mousedown.delegate="handleDragStart($event)"> ${settings.pinned ? \'keep_off\' : \'keep\'} </button> <button class="action" click.delegate="windowManager.close(config, \'close_button\')" mousedown.delegate="handleDragStart($event)"> close </button> </nav> </header> <div class="background"> <div class="fill" css="opacity: ${isPro ? settings.opacity / 100 : 100}"></div> </div> <div class="content" inert.bind="windowManager.isWindowTransforming"> <slot name="content"></slot> </div> <span class="resize-handle" mousedown.delegate="handleResizeStart($event)"> <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"> <line x1="16.667" y1="4.66654" x2="4.66703" y2="16.6665" stroke="white" stroke-opacity="0.8"/> <line x1="16.8887" y1="8.96976" x2="8.96911" y2="16.8894" stroke="white" stroke-opacity="0.8"/> <line x1="17.1924" y1="13.1919" x2="13.1924" y2="17.1919" stroke="white" stroke-opacity="0.8"/> </svg> </span> </template> '},"overlay/resources/elements/overlay-window.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>c});var o=i(31601),n=i.n(o),s=i(76314),a=i.n(s),r=i(4417),d=i.n(r),l=new URL(i(83959),i.b),h=a()(n()),w=d()(l);h.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${w}) format("woff2")}.material-symbols-outlined,overlay-window>header .icon,overlay-window>header .actions .action,overlay-window>header .actions .action.opacity{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}overlay-window{pointer-events:none;position:relative;z-index:0;display:flex;flex-direction:column;border-radius:16px;overflow:hidden}overlay-window>header,overlay-window>.content,overlay-window>.background,overlay-window>.resize-handle{pointer-events:auto}overlay-window>header{padding:12px;transition:all .15s cubic-bezier(0.27, 1, 0.36, 1);display:flex;align-items:center;gap:10px;position:relative;z-index:1;filter:drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.33));flex:0 0 auto}overlay-window>header .header-background{position:absolute;left:0;top:0;width:100%;height:100%;z-index:0}overlay-window>header .icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;flex:0 0 auto;width:16px;height:16px;display:flex;align-items:center;justify-content:center;color:rgba(255,255,255,.8);pointer-events:none}overlay-window>header .icon img{opacity:.8;width:13px}overlay-window>header .title{font-weight:800;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:#fff;font-size:16px;letter-spacing:.67px;pointer-events:none}overlay-window>header .actions{flex:0 1 auto;position:relative;z-index:2}overlay-window>header .actions,overlay-window>header .actions>*{display:inline-flex;align-items:center}overlay-window>header .actions .action{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;border:none;outline:0;padding:0;background:rgba(0,0,0,0);display:inline-flex;flex:0 0 auto;width:28px;height:28px;align-items:center;justify-content:center;color:rgba(255,255,255,.6);border-radius:6px;transition:color .15s,background-color .15s,opacity .5s,max-width .15s cubic-bezier(0.27, 1, 0.36, 1)}overlay-window>header .actions .action:hover{color:rgba(255,255,255,.8);background-color:rgba(255,255,255,.15)}overlay-window>header .actions .action.toggle-off{opacity:.5}overlay-window>header .actions .action.opacity{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;position:relative;overflow:hidden;display:flex;align-items:center;flex:1 1 auto;width:100%;max-width:28px}overlay-window>header .actions .action.opacity:before{font-family:inherit;content:"opacity"}overlay-window>header .actions .action.opacity:before{width:28px;flex:0 0 auto}overlay-window>header .actions .action.opacity .opacity-slider{flex:1 1 auto;opacity:0;transition:opacity .15s cubic-bezier(0.27, 1, 0.36, 1);padding-right:2px}overlay-window>header .actions .action.opacity .opacity-slider range-input{--range-input--height: 24px}overlay-window>header .actions .action.opacity .opacity-slider range-input input[type=number]{display:none}overlay-window>header .actions .action.opacity:hover{max-width:138px}overlay-window>header .actions .action.opacity:hover .opacity-slider{opacity:1}overlay-window>header .actions .action.pin.pinned{background-color:#fff;color:var(--theme--background)}overlay-window>.background{position:absolute;top:0;right:0;bottom:0;left:0;outline:.5px solid rgba(255,255,255,.25);box-shadow:0px 2px 8px rgba(0,0,0,.33);border-radius:16px;transition:top .15s cubic-bezier(0.27, 1, 0.36, 1);z-index:-1}overlay-window>.background .fill{display:block;width:100%;height:100%;background:var(--theme--background);border-radius:16px}overlay-window>.resize-handle{width:24px;height:24px;position:absolute;right:0;bottom:0;padding:0 4px 4px 0;cursor:se-resize;z-index:2}overlay-window>.resize-handle svg{pointer-events:none}overlay-window>.content{filter:drop-shadow(0px 2px 8px rgba(0, 0, 0, 0.33));flex:1 1 auto;position:relative;z-index:0}overlay-window.overlay-pinned>.resize-handle{display:none}overlay-window.overlay-pinned.pinned>header{transform:translateY(52px);opacity:0;pointer-events:none}overlay-window.overlay-pinned.pinned>.background{top:52px}overlay-window.overlay-pinned.pinned>.content{border-radius:16px;overflow:hidden}overlay-window.custom-geometry{position:fixed}`,""]);const c=h},"overlay/resources/elements/overlay-windows":(t,e,i)=>{i.r(e),i.d(e,{OverlayWindows:()=>a});var o=i(15215),n=i("aurelia-framework"),s=i(66811);let a=class{constructor(t){this.windowManager=t}};(0,o.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,o.Sn)("design:type",Boolean)],a.prototype,"pinned",void 0),a=(0,o.Cg)([(0,n.autoinject)(),(0,o.Sn)("design:paramtypes",[s.o])],a)},"overlay/resources/elements/overlay-windows.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>o});const o='<template> <require from="./overlay-windows.scss"></require> <require from="./overlay-window"></require> <require from="./maps-window"></require> <require from="./mods-window"></require> <require from="./mods-window-actions"></require> <require from="./assistant-window"></require> <overlay-window repeat.for="config of windowManager.windows" if.bind="config.enabled && windowManager.gameWindowSettings[config.id].open === true" config.bind="config" pinned.bind="pinned"> <template if.bind="config.id === \'mods\'"> <mods-window slot="content"></mods-window> <mods-window-actions slot="actions"></mods-window-actions> </template> <template if.bind="config.id === \'maps\'"> <maps-window slot="content"></maps-window> </template> <template if.bind="config.id === \'gameGuide\'"> <assistant-window slot="content"></assistant-window> </template> </overlay-window> </template> '},"overlay/resources/elements/overlay-windows.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>r});var o=i(31601),n=i.n(o),s=i(76314),a=i.n(s)()(n());a.push([t.id,"overlay-windows{display:flex;align-items:center;justify-content:center;gap:2vw;padding:2vw;pointer-events:none}overlay-windows overlay-window{height:50vh}overlay-windows overlay-window.size-1{width:20vw;min-width:320px}overlay-windows overlay-window.size-2{width:40vw;min-width:601px}@media(max-width: 1440px){overlay-windows overlay-window.size-1{width:30vw}overlay-windows overlay-window.size-2{width:50vw}}",""]);const r=a}}]);
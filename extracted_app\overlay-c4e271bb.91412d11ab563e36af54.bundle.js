"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3789,7606],{48881:(e,t,n)=>{n.d(t,{Gi:()=>K,EP:()=>G,Vz:()=>F,Ew:()=>pe,W:()=>he,ah:()=>Me,WS:()=>i,Lc:()=>ce,TU:()=>De,NX:()=>E,e1:()=>T,K8:()=>b,$Z:()=>Z,Yt:()=>te,oz:()=>se,Kc:()=>_,vk:()=>u,iC:()=>B,Ui:()=>Ue,Xk:()=>Fe});var r=n(64706);const i="ACTION_PERSIST_ASSISTANT_HISTORY",a="ACTION_CLEAR_ASSISTANT_HISTORY",o={[i]:function(e,t,n){const i=[...e.assistantHistory[t]||[],...n];return i.length>r.$H&&i.splice(0,i.length-r.$H),{...e,assistantHistory:{...e.assistantHistory,[t]:[...i]}}},[a]:function(e){return{...e,assistantHistory:{}}}},s="ACTION_COACHING_TIP_SHOWN",l="ACTION_RESET_COACHING_TIP_HISTORY",c="ACTION_TOGGLE_SIDEBAR_LIST_COLLAPSED",T="ACTION_SET_INSTALLED_APPS",_="ACTION_SET_SETTINGS",E="ACTION_SET_FLAG",u="ACTION_SET_TIMESTAMP",A={[s]:function(e,t,n){return{...e,coachingTipHistory:{...e.coachingTipHistory,[t]:{...e.coachingTipHistory[t]||{},tipShownAt:n}}}},[l]:function(e){return{...e,coachingTipHistory:{}}},[c]:function(e,t){return{...e,sidebarCollapsedLists:{...e.sidebarCollapsedLists,[t]:!e.sidebarCollapsedLists?.[t]}}},[T]:function(e,t){return{...e,installedApps:t}},[_]:function(e,t,n,r){return{...e,settings:{...e.settings,...t}}},[E]:function(e,t,n){return e.flags[t]===n?e:{...e,flags:{...e.flags,[t]:!!n}}},[u]:function(e,t,n){return void 0===n&&(n=(new Date).toISOString()),{...e,timestamps:{...e.timestamps,[t]:n}}}},I="ACTION_CACHE_TRAINER",O="ACTION_CACHE_TRAINER_RESPONSE",S={[I]:function(e,t){return{...e,trainers:{...e.trainers,[t.gameId]:[...(e.trainers[t.gameId]||[]).filter((e=>e.id!==t.id)),t]}}},[O]:function(e,t){const{trainer:n,i18n:r}=t;return{...e,trainers:{...e.trainers,[n.gameId]:[...(e.trainers[n.gameId]||[]).filter((e=>e.id!==n.id)),n]},gameTranslations:r?{...e.gameTranslations,[n.gameId]:{...e.gameTranslations[n.gameId]||{},[r.locale]:r.strings}}:e.gameTranslations}}},d="ACTION_TOGGLE_FAVORITE_VIDEO",N={[d]:function(e,t){const n=[...e.favoriteVideos||[]];return n.includes(t)?{...e,favoriteVideos:n.filter((e=>e!==t))}:{...e,favoriteVideos:[...n,t]}}};var f=n(24008),C=n(70236);const m="ACTION_SET_CATALOG",g="ACTION_CLEAR_INVALID_CATALOG",R="ACTION_CLEAR_ACQUISITION_DATA",L={[m]:function(e,t,n=null){if(null===t||!(0,f.Cz)(t))throw new Error("Invalid catalog object.");const r=Object.keys(e.correlatedUnavailableTitles).filter((e=>t.titles[e]&&t.titles[e].gameIds.some((e=>(0,C.Lt)(t.games[e].flags,f.rT.Available)))));if(0===r.length)return{...e,catalog:t,catalogCacheKey:n};const i={...e.correlatedUnavailableTitles};return r.forEach((e=>delete i[e])),{...e,catalog:t,catalogCacheKey:n,correlatedUnavailableTitles:i}},[g]:function(e){return(0,f.Cz)(e.catalog)?e:{...e,catalog:null,catalogCacheKey:null}},[R]:function(e){return(0,f.Cz)(e.catalog)?e:{...e,installation:{...e.installation,acquisition:null}}}};var y=n(80252),p=n(41882);function D(e,t){return e.gamePreferences[t]?{...e.gamePreferences[t]}:{customHotkeys:{},saveCheats:{trainerState:{},enabled:void 0},overlaySeen:!1,overlayDisabled:!1}}const M="ACTION_GAME_PLAYED",H="ACTION_GAME_VIEWED",h="ACTION_POSITIVE_TRAINER_FEEDBACK_SUBMITTED",v="ACTION_GAME_NOTIFICATION_SHOWN",P="ACTION_GAME_NOTIFICATIONS_RESET",b="ACTION_SET_INSTALLED_GAME_VERSIONS",G="ACTION_CLEAR_UNINSTALLED_GAME_VERSIONS",U="ACTION_ADD_CUSTOM_INSTALLATION",V="ACTION_REMOVE_CUSTOM_INSTALLATION";function w(e,t){const n={...e.installedApps};Object.keys(n).forEach((e=>{const r=n[e];if(r.platform===p.u)try{y.k.parse(r.sku).gameId===t&&delete n[e]}catch{}}));const r={...e.installedGameVersions};return r.hasOwnProperty(t)&&(r[t]=r[t].filter((e=>!e.correlationId.startsWith(`${p.u}:`)))),{...e,installedApps:n,installedGameVersions:r}}const k="ACTION_SET_CUSTOM_HOTKEY",B="ACTION_SET_TRAINER_STATE",F="ACTION_ENABLE_GAME_SAVE_CHEATS",K="ACTION_CLEAR_SAVE_CHEATS",W="ACTION_SET_GAME_COLLECTION_PREFERENCES",$="ACTION_SET_FOLLOWED_GAMES",j={[M]:function(e,t,n,r){const i=e.gameHistory[t]?.playDuration||0;return{...e,gameHistory:{...e.gameHistory,[t]:{...e.gameHistory[t]||{},lastPlayedAt:n,playDuration:i+r}}}},[H]:function(e,t){return{...e,gameHistory:{...e.gameHistory,[t]:{...e.gameHistory[t]||{},lastViewedAt:(new Date).toISOString()}}}},[h]:function(e,t){return{...e,gameHistory:{...e.gameHistory,[t]:{...e.gameHistory[t]||{},lastPositiveFeedbackAt:(new Date).toISOString()}}}},[v]:function(e,t,n){return{...e,gameHistory:{...e.gameHistory,[t]:{...e.gameHistory[t]||{},lastNotification:n||(new Date).toISOString()}}}},[P]:function(e,t){const n={};return t.forEach((t=>{n[t]={...e.gameHistory[t]||{},lastNotification:(new Date).toISOString()}})),{...e,gameHistory:{...e.gameHistory,...n}}},[b]:function(e,t){return{...e,installedGameVersions:{...e.installedGameVersions,...t}}},[G]:function(e,t){const n=Object.entries(e.installedGameVersions),r=n.filter((([e])=>t.has(e)));return n.length===r.length?e:{...e,installedGameVersions:Object.fromEntries(r)}},[U]:function(e,t,n,r){const i=w(e,t),a=new y.k(t,n.toLocaleLowerCase()),o=`${p.u}:${a.toString()}`,s=[...i.installedGameVersions[t]||[]];return s.some((e=>e.correlationId===o))||s.push({gameId:t,correlationId:o,version:r?.version??null,modifiedAt:r?.modifiedAt??null,createdAt:r?.createdAt??null}),{...i,installedApps:{...i.installedApps,[o]:{platform:p.u,sku:a.toString(),location:n}},installedGameVersions:{...i.installedGameVersions,[t]:s}}},[V]:w,[k]:function(e,t,n,r,i){const a=D(e,t),o={...a.customHotkeys||{}},s={...o,[n]:{...o[n]||{}}};return void 0===i?(delete s[n][r],0===Object.keys(s[n]).length&&delete s[n]):s[n][r]=i,{...e,gamePreferences:{...e.gamePreferences,[t]:{...a,customHotkeys:s}}}},[B]:function(e,t,n,r){const i=D(e,t),a={...i.saveCheats||{trainerState:{},enabled:void 0}},o={...a.trainerState,[n]:r};return{...e,gamePreferences:{...e.gamePreferences,[t]:{...i,saveCheats:{...a,trainerState:o}}}}},[F]:function(e,t,n,r){const i=D(e,t);n&&r&&(r=Object.fromEntries(Object.entries(r).filter((([e,t])=>void 0!==t))));const a={trainerState:n?r??i.saveCheats?.trainerState??{}:{},enabled:n};return{...e,gamePreferences:{...e.gamePreferences,[t]:{...i,saveCheats:{...a,enabled:n}}}}},[K]:function(e,t){const n={...e.gamePreferences};return Object.keys(n).forEach((r=>{n[r]={...D(e,r),saveCheats:{enabled:!t&&n[r].saveCheats?.enabled,trainerState:{}}}})),{...e,gamePreferences:n}},[W]:function(e,t,n){const r=e.gameCollectionPreferences[t]??{};return{...e,gameCollectionPreferences:{...e.gameCollectionPreferences,[t]:{...r,...n,selectedFilterOptions:{...r?.selectedFilterOptions,...n.selectedFilterOptions}}}}},[$]:function(e,t){return{...e,followedGames:t}}},Y="ACTION_SET_INSTALLATION_TOKEN",q="ACTION_SET_INSTALLATION_PARAMS",z={[Y]:function(e,t){if(e.installation.token)throw new Error("Installation token already set.");if(e.installation.id)throw new Error("Installation ID already set.");return{...e,installation:{...e.installation,token:t}}},[q]:function(e,t){if(e.installation.id)throw new Error("Installation ID already set.");return{...e,installation:{...e.installation,id:t.installationId,token:void 0,acquisition:t.acquisition??null,cookies:t.cookies??null}}}},J="ACTION_SET_MAP_SETTINGS",Q="ACTION_SET_TITLE_MAP_SETTINGS",X="ACTION_CLEAR_MAP_SETTINGS",x={[J]:function(e,t,n){return{...e,mapSettings:{...e.mapSettings,[t]:n}}},[Q]:function(e,t,n){return{...e,titleMapSettings:{...e.titleMapSettings,[t]:n}}},[X]:function(e){return{...e,mapSettings:{},titleMapSettings:{}}}},Z="ACTION_SET_MOD_TIMER",ee="ACTION_CLEAR_MOD_TIMER",te="ACTION_SET_MOD_TIMER_MESSAGES_DISMISSED",ne="ACTION_CLEAR_MOD_TIMER_MESSAGES_DISMISSED",re={[Z]:function(e,t){const{gameId:n,modId:r}=t,i=e?.modTimers?.[n]?{...e.modTimers[n]}:{};return 0===t.duration||t.cancel||0===t?.start?delete i[r]:i[r]={timestamp:t.timestamp,duration:t.duration,type:t.type,start:t?.start,end:t?.end},{...e,modTimers:{...e?.modTimers||{},[n]:i}}},[ee]:function(e){return{...e,modTimers:{}}},[te]:function(e,t){return{...e,modTimerMessagesDismissed:{...e.modTimerMessagesDismissed,[t]:!0}}},[ne]:function(e){return{...e,modTimerMessagesDismissed:{}}}},ie="ACTION_SET_OVERLAY_SEEN",ae="ACTION_SET_OVERLAY_DISABLED",oe={[ie]:function(e,t){const n=D(e,t);return{...e,gamePreferences:{...e.gamePreferences,[t]:{...n,overlaySeen:!0}}}},[ae]:function(e,t,n){const r=D(e,t);return{...e,gamePreferences:{...e.gamePreferences,[t]:{...r,overlayDisabled:n}}}}},se="ACTION_SET_PINNED_MOD",le={[se]:function(e,t,n){return{...e,pinnedMods:{...e.pinnedMods||{},[t]:n}}}},ce="ACTION_PROMOTION_SHOW",Te="ACTION_PROMOTION_CLICKED",_e="ACTION_PROMOTION_BANNER_DISMISSED",Ee="ACTION_CLEAR_PROMOTION_HISTORY",ue={[ce]:function(e,t,n,r){return{...e,promotionHistory:{...e.promotionHistory,[t]:{...e.promotionHistory[t]||{},[`${n}ShownAt`]:r}}}},[Te]:function(e,t,n,r){return{...e,promotionHistory:{...e.promotionHistory,[t]:{...e.promotionHistory[t]||{},[`${n}ClickedAt`]:r}}}},[_e]:function(e,t,n){return{...e,promotionHistory:{...e.promotionHistory,[t]:{...e.promotionHistory[t]||{},bannerDismissedAt:n}}}},[Ee]:function(e){return{...e,promotionHistory:{}}}},Ae="ACTION_SET_ACKNOWLEDGED_REWARD",Ie={[Ae]:function(e,t,n){return{...e,acknowledgedRewards:{...e.acknowledgedRewards,[t]:n??!0}}}};var Oe=n(12511);const Se="ACTION_RESET_STATE",de={[Se]:function(e){return{...Oe.u,token:e.token,installation:{...e.installation,id:e.installation.id,acquisition:e.installation.acquisition},stateVersion:e.stateVersion}}};var Ne=n("services/bugsnag/index");const fe="ACTION_TOGGLE_FAVORITE_TITLE",Ce="ACTION_TITLE_VIEWED",me="ACTION_SET_TITLE_PREFERENCES",ge="ACTION_REGISTER_CORRELATED_UNAVAILABLE_TITLES",Re="ACTION_REMOVE_CORRELATED_UNAVAILABLE_TITLES",Le="ACTION_REMOVE_UNINSTALLED_CORRELATED_UNAVAILABLE_TITLES";function ye(e,t,n){let r=!1;return Object.keys(t).filter((e=>!n.has(e))).forEach((e=>{r=!0,delete t[e]})),Object.values(e).forEach((t=>{void 0===t.games?(0,Ne.report)(new Error(`Undefined games property for title type ${typeof t}, OBJ: ${JSON.stringify(t)}`)):t.games.flatMap((e=>e.correlationIds)).every((e=>!n.has(e)))&&(r=!0,delete e[t.id])})),r}const pe="ACTION_INCREMENT_COUNTER",De="ACTION_SET_COUNTER",Me="ACTION_MARK_TRAINER_NOTES_READ",He="ACTION_CLEAR_READ_TRAINER_NOTES",he="ACTION_MARK_CHEAT_BLUEPRINT_INSTRUCTIONS_READ",ve="ACTION_MARK_ALL_CHEAT_BLUEPRINT_INSTRUCTIONS_UNREAD",Pe="ACTION_TRAINER_FEEDBACK_REQUESTED",be="ACTION_MARK_PRECISION_MODS_SECTIONS_VIEWED",Ge="ACTION_SET_ACCESS_TOKEN",Ue="ACTION_SET_USER_ACCOUNT",Ve="ACTION_MOD_SUGGESTION_LIST_SET_USER_BOOSTS",we="ACTION_SUGGEST_MOD_DIALOG_SET_USER_BOOSTS";function ke(e,t){return e.account?{...e,account:{...e.account,boosts:t}}:{...e}}const Be="ACTION_DESTROY_USER",Fe={...o,...A,...S,...N,...L,...j,...z,...x,...re,...oe,...le,...ue,...Ie,...de,[fe]:function(e,t){const n={...e.favoriteTitles};return n.hasOwnProperty(t)?delete n[t]:n[t]=!0,{...e,favoriteTitles:n}},[Ce]:function(e,t){return{...e,titleHistory:{...e.titleHistory,[t]:{lastViewedAt:(new Date).toISOString()}}}},[me]:function(e,t,n){return{...e,titlePreferences:{...e.titlePreferences,[t]:n}}},[ge]:function(e,t,n,r){const i={...e.correlatedUnavailableTitles},a={...e.correlatedUnavailableTitleRefreshes};ye(i,a,r),t.forEach((e=>i[e.id]=e));const o=Math.ceil(Date.now()/1e3);return n.forEach((e=>a[e]=o)),{...e,correlatedUnavailableTitles:i,correlatedUnavailableTitleRefreshes:a}},[Re]:function(e,t){if(t.every((t=>!e.correlatedUnavailableTitles.hasOwnProperty(t))))return e;const n={...e.correlatedUnavailableTitles};return t.forEach((e=>delete n[e])),{...e,correlatedUnavailableTitles:n}},[Le]:function(e,t){const n={...e.correlatedUnavailableTitles},r={...e.correlatedUnavailableTitleRefreshes};return ye(n,r,t)?{...e,correlatedUnavailableTitles:n,correlatedUnavailableTitleRefreshes:r}:e},[pe]:function(e,t,n){return 0===n?e:{...e,counters:{...e.counters,[t]:(e.counters[t]||0)+n}}},[De]:function(e,t,n){return e.counters[t]===n?e:{...e,counters:{...e.counters,[t]:n}}},[Me]:function(e,t,n){return{...e,trainerNotesRead:{...e.trainerNotesRead,[t]:n}}},[He]:function(e){return{...e,trainerNotesRead:{}}},[he]:function(e,t,n){const r={...e.cheatBlueprintInstructionsRead};return n?r[t]=n:delete r[t],{...e,cheatBlueprintInstructionsRead:r}},[ve]:function(e){return{...e,cheatBlueprintInstructionsRead:{}}},[Pe]:function(e,t,n,r,i){const a=`${t}:${n||""}:${r??""}`,o=(e.trainerFeedbackRequests||{})[a]||[],s=Array.from(new Set((new Array).concat(o,i)));return{...e,trainerFeedbackRequests:{...e.trainerFeedbackRequests||{},[a]:s}}},[be]:function(e,t){const n={...e.precisionModsSectionsViewed};return n[t]=!0,{...e,precisionModsSectionsViewed:n}},[Ge]:function(e,t){return{...e,token:t}},[Ue]:function(e,t){return e.account&&JSON.stringify(t)===JSON.stringify(e.account)?e:{...e,account:t}},[Ve]:function(e,t){return ke(e,t)},[we]:function(e,t){return ke(e,t)},[Be]:function(e){return{...e,token:null,account:null,flags:{...e.flags,signOutOnStartup:!1},assistantHistory:{}}}}}}]);
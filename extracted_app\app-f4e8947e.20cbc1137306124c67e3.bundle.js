"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5583],{"onboarding/resources/elements/all-games-card":(e,t,i)=>{i.r(t),i.d(t,{AllGamesCard:()=>r});var a=i("shared/utility/resources/value-converters/platform");class r{constructor(){this.icons=[...a.platformIcons.values()]}}},"onboarding/resources/elements/all-games-card.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});var a=i(14385),r=i.n(a),l=new URL(i(80223),i.b);const o='<template> <require from="./all-games-card.scss"></require> <div class="card-container"> <img class="games-bg" src="'+r()(l)+'"> <div class="browse-container"> <div class="browse-text"> <h1 class="browse-header">${\'onboarding_all_games_card.browse_all\' | i18n}</h1> <h1 class="browse-arrow">-></h1> </div> <div class="platform-icons"> <img repeat.for="icon of icons" src.bind="icon"> </div> </div> </div> </template> '},"onboarding/resources/elements/all-games-card.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var a=i(31601),r=i.n(a),l=i(76314),o=i.n(l)()(r());o.push([e.id,'all-games-card .card-container{width:100%;height:184px;overflow:hidden;position:relative;border-radius:16px;transition:transform .3s ease-in-out}all-games-card .card-container:hover{transform:scale(1.01)}all-games-card .card-container:after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.3);border-radius:16px;z-index:1;mix-blend-mode:overlay;cursor:pointer}all-games-card .card-container .games-bg{z-index:0;position:absolute;animation:scroll 30s alternate infinite;width:100%}@keyframes scroll{0%{transform:translateY(0)}100%{transform:translateY(-50%)}}all-games-card .card-container .browse-container{display:flex;flex-direction:column;gap:12px;align-items:start;justify-content:center;width:500px;padding:12px 0px 12px 24px;height:100%;position:relative;z-index:1;background:linear-gradient(90deg, #5e5754 50%, rgba(79, 76, 71, 0.9) 60%, rgba(67, 64, 63, 0.9) 75%, rgba(255, 255, 255, 0) 100%)}all-games-card .card-container .browse-container .browse-text{display:flex}all-games-card .card-container .browse-container .browse-text .browse-header{width:260px;letter-spacing:-2px}all-games-card .card-container .browse-container .browse-text .browse-arrow{align-self:end}all-games-card .card-container .browse-container .browse-text h1{display:flex;color:#fff;text-shadow:0px 0px 48px rgba(255,255,255,.5);font-size:32px;font-weight:800;max-width:350px;width:290px;margin:0}all-games-card .card-container .browse-container .platform-icons{display:flex;gap:8px}all-games-card .card-container .browse-container .platform-icons img{display:flex;width:16px;height:16px;opacity:.5}',""]);const n=o},"onboarding/resources/elements/fake-add-game":(e,t,i)=>{i.r(t),i.d(t,{FakeAddGame:()=>a});class a{}},"onboarding/resources/elements/fake-add-game.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <svg width="169" height="44" viewBox="0 0 169 44" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect x="0.25" y="0.25" width="168.5" height="43.5" rx="21.75" stroke="#ACFF35" stroke-width="0.5"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="24" font-weight="900" letter-spacing="-1px"> <tspan x="16" y="30.7273">Add Game</tspan> </text> <mask id="mask0_4041_15802" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="137" y="12" width="20" height="20"> <rect x="137" y="12" width="20" height="20" fill="#D9D9D9"/> </mask> <g mask="url(#mask0_4041_15802)"> <path d="M146.999 25.1989L141.871 20.0707L143.041 18.9009L146.999 22.8592L150.958 18.9009L152.127 20.0707L146.999 25.1989Z" fill="white" fill-opacity="0.8"/> </g> </svg> </template> '},"onboarding/resources/elements/fake-game-controls":(e,t,i)=>{i.r(t),i.d(t,{FakeGameControls:()=>a});class a{}},"onboarding/resources/elements/fake-game-controls.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./fake-game-controls.scss"></require> <svg width="928" height="386" viewBox="0 0 928 386" fill="none" xmlns="http://www.w3.org/2000/svg"> <g filter="url(#filter0_b_1498_229817)"> <g clip-path="url(#clip0_1498_229817)"> <rect width="928" height="40" fill="white" fill-opacity="0.01"/> <mask id="mask0_1498_229817" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="12" y="12" width="16" height="16"> <rect x="12" y="12" width="16" height="16" fill="#D9D9D9"/> </mask> <g mask="url(#mask0_1498_229817)"> <path d="M19.9999 19.5951C19.3138 19.5951 18.7367 19.3611 18.2686 18.8931C17.8006 18.425 17.5665 17.8479 17.5665 17.1618C17.5665 16.4757 17.8006 15.8986 18.2686 15.4306C18.7367 14.9625 19.3138 14.7285 19.9999 14.7285C20.686 14.7285 21.263 14.9625 21.7311 15.4306C22.1991 15.8986 22.4332 16.4757 22.4332 17.1618C22.4332 17.8479 22.1991 18.425 21.7311 18.8931C21.263 19.3611 20.686 19.5951 19.9999 19.5951ZM15.0332 25.3554V23.6233C15.0332 23.3413 15.1063 23.0814 15.2524 22.8437C15.3985 22.606 15.601 22.4052 15.8601 22.2413C16.4858 21.8738 17.1528 21.5926 17.8612 21.3977C18.5695 21.2029 19.2819 21.1054 19.9983 21.1054C20.7147 21.1054 21.4276 21.2029 22.137 21.3977C22.8464 21.5926 23.5139 21.8738 24.1396 22.2413C24.3987 22.3941 24.6013 22.5922 24.7474 22.8354C24.8935 23.0787 24.9665 23.3413 24.9665 23.6233V25.3554H15.0332Z" fill="white" fill-opacity="0.6"/> </g> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="bold" letter-spacing="0px"> <tspan x="40" y="25.0909">${\'onboarding_game_tutorial.player\' | i18n}</tspan> </text> <rect width="928" height="96" transform="translate(0 41)" fill="white" fill-opacity="0.01"/> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="40" y="70.0909">${\'onboarding_game_tutorial.unlimited_health\' | i18n}</tspan> </text> <g opacity="0.5"> <rect x="536" y="51" width="100" height="28" rx="14" fill="white" fill-opacity="0.05"/> <rect x="538" y="53" width="48" height="24" rx="12" fill="white" fill-opacity="0.2"/> <text fill="white" fill-opacity="0.75" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="552.959" y="69.3636">${\'onboarding_game_tutorial.off\' | i18n}</tspan> </text> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="601.803" y="69.3636">${\'onboarding_game_tutorial.on\' | i18n}</tspan> </text> </g> <rect x="656" y="52" width="27" height="26" rx="8" fill="white" fill-opacity="0.04"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="662.09" y="70.0909">${\'onboarding_game_tutorial.f1\' | i18n}</tspan> </text> <path fill-rule="evenodd" clip-rule="evenodd" d="M21.2222 112L22 105L15 114H18.7778L18 121L25 112L21.2222 112Z" fill="#00C7F2"/> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="40" y="118.091">${\'onboarding_game_tutorial.unlimited_stamina\' | i18n}</tspan> </text> <rect x="536" y="99" width="100" height="28" rx="14" fill="white" fill-opacity="0.05"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="552.959" y="117.364">${\'onboarding_game_tutorial.off\' | i18n}</tspan> </text> <rect x="586" y="101" width="48" height="24" rx="12" fill="#00C7F2"/> <rect x="586" y="101" width="48" height="24" rx="12" fill="black" fill-opacity="0.2"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="601.5" y="117.364">${\'onboarding_game_tutorial.on\' | i18n}</tspan> </text> <rect x="656" y="100" width="29" height="26" rx="8" fill="white" fill-opacity="0.04"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="662.105" y="118.091">${\'onboarding_game_tutorial.f2\' | i18n}</tspan> </text> </g> </g> <g filter="url(#filter1_b_1498_229817)"> <g clip-path="url(#clip1_1498_229817)"> <rect width="928" height="40" transform="translate(0 153)" fill="white" fill-opacity="0.01"/> <mask id="mask1_1498_229817" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="12" y="165" width="16" height="16"> <rect x="12" y="165" width="16" height="16" fill="#D9D9D9"/> </mask> <g mask="url(#mask1_1498_229817)"> <path d="M14.2002 173.4V168.701C14.2002 168.281 14.3482 167.926 14.6442 167.636C14.9401 167.345 15.2939 167.2 15.7053 167.2H19.4002V173.4H14.2002ZM20.6002 167.2H24.295C24.7143 167.2 25.07 167.346 25.362 167.638C25.6541 167.93 25.8002 168.287 25.8002 168.707V171.4H20.6002V167.2ZM20.6002 178.8V172.6H25.8002V177.295C25.8002 177.707 25.6541 178.06 25.362 178.356C25.07 178.652 24.7143 178.8 24.295 178.8H20.6002ZM14.2002 174.6H19.4002V178.8H15.7053C15.2939 178.8 14.9401 178.652 14.6442 178.356C14.3482 178.06 14.2002 177.706 14.2002 177.293V174.6Z" fill="white" fill-opacity="0.6"/> </g> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="bold" letter-spacing="0px"> <tspan x="40" y="178.091">${\'onboarding_game_tutorial.inventory\' | i18n}</tspan> </text> <rect width="928" height="192" transform="translate(0 194)" fill="white" fill-opacity="0.01"/> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="40" y="223.091">${\'onboarding_game_tutorial.unlimited_ammo\' | i18n}</tspan> </text> <g opacity="0.5"> <rect x="536" y="204" width="100" height="28" rx="14" fill="white" fill-opacity="0.05"/> <rect x="538" y="206" width="48" height="24" rx="12" fill="white" fill-opacity="0.2"/> <text fill="white" fill-opacity="0.75" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="552.959" y="222.364">${\'onboarding_game_tutorial.off\' | i18n}</tspan> </text> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="601.803" y="222.364">${\'onboarding_game_tutorial.on\' | i18n}</tspan> </text> </g> <rect x="656" y="205" width="30" height="26" rx="8" fill="white" fill-opacity="0.04"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="662.387" y="223.091">${\'onboarding_game_tutorial.f3\' | i18n}</tspan> </text> <path fill-rule="evenodd" clip-rule="evenodd" d="M21.2222 265L22 258L15 267H18.7778L18 274L25 265L21.2222 265Z" fill="#00C7F2"/> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="40" y="271.091">${\'onboarding_game_tutorial.unlimited_items\' | i18n}</tspan> </text> <rect x="536" y="252" width="100" height="28" rx="14" fill="white" fill-opacity="0.05"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="552.959" y="270.364">${\'onboarding_game_tutorial.off\' | i18n}</tspan> </text> <rect x="586" y="254" width="48" height="24" rx="12" fill="#00C7F2"/> <rect x="586" y="254" width="48" height="24" rx="12" fill="black" fill-opacity="0.2"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="601.5" y="270.364">${\'onboarding_game_tutorial.on\' | i18n}</tspan> </text> <rect x="656" y="253" width="30" height="26" rx="8" fill="white" fill-opacity="0.04"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="662.318" y="271.091">${\'onboarding_game_tutorial.f4\' | i18n}</tspan> </text> <path fill-rule="evenodd" clip-rule="evenodd" d="M21.2222 313L22 306L15 315H18.7778L18 322L25 313L21.2222 313Z" fill="#00C7F2"/> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="40" y="319.091">${\'onboarding_game_tutorial.speed\' | i18n}</tspan> </text> <rect x="436" y="300" width="80" height="28" rx="8" fill="white" fill-opacity="0.05"/> <text fill="white" fill-opacity="0.8" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="442.033" y="318.364">500</tspan> </text> <g clip-path="url(#clip2_1498_229817)"> <rect x="522" y="300" width="80" height="28" rx="8" fill="white" fill-opacity="0.05"/> <rect x="522" y="300" width="38" height="28" fill="#00C7F2"/> <rect x="522" y="300" width="38" height="28" fill="black" fill-opacity="0.2"/> <g filter="url(#filter2_ddd_1498_229817)"> <rect x="552" y="300" width="16" height="28" rx="8" fill="white"/> </g> <mask id="mask2_1498_229817" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="552" y="306" width="16" height="16"> <rect x="552" y="322" width="16" height="16" transform="rotate(-90 552 322)" fill="#D9D9D9"/> </mask> <g mask="url(#mask2_1498_229817)"> <path d="M561.866 319.167H560.616V308.833H561.866V319.167ZM559.366 319.167H558.116V308.833H559.366V319.167Z" fill="#CCCCCC"/> </g> </g> <rect x="608" y="300" width="28" height="28" rx="8" fill="#00C7F2"/> <rect x="608" y="300" width="28" height="28" rx="8" fill="black" fill-opacity="0.2"/> <mask id="mask3_1498_229817" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="612" y="304" width="20" height="20"> <rect x="612" y="304" width="20" height="20" fill="#D9D9D9"/> </mask> <g mask="url(#mask3_1498_229817)"> <path d="M620.229 318.232L616.513 314.495L617.633 313.375L620.229 315.95L626.409 309.792L627.529 310.933L620.229 318.232Z" fill="white" fill-opacity="0.9"/> </g> <rect x="656" y="301" width="72" height="26" rx="8" fill="white" fill-opacity="0.04"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="662.024" y="319.091">&#x2191; ${\'onboarding_game_tutorial.ctrl_f1\' | i18n}</tspan> </text> <rect x="736" y="301" width="104" height="26" rx="8" fill="white" fill-opacity="0.04"/> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="742.268" y="319.091">&#x2193; ${\'onboarding_game_tutorial.ctrl_shift_f1\' | i18n}</tspan> </text> </g> </g> <defs> <filter id="filter0_b_1498_229817" x="-50" y="-50" width="1028" height="237" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feglood flood-opacity="0" result="BackgroundImageFix"/> <fegaussianblur in="BackgroundImageFix" stdDeviation="25"/> <fecomposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1498_229817"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1498_229817" result="shape"/> </filter> <filter id="filter1_b_1498_229817" x="-50" y="103" width="1028" height="333" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feglood flood-opacity="0" result="BackgroundImageFix"/> <fegaussianblur in="BackgroundImageFix" stdDeviation="25"/> <fecomposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1498_229817"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1498_229817" result="shape"/> </filter> <filter id="filter2_ddd_1498_229817" x="549" y="298" width="22" height="34" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feglood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset dy="1"/> <fegaussianblur stdDeviation="1.5"/> <fecolormatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.2 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_1498_229817"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset dy="2"/> <fegaussianblur stdDeviation="0.5"/> <fecolormatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/> <feblend mode="normal" in2="effect1_dropShadow_1498_229817" result="effect2_dropShadow_1498_229817"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset dy="1"/> <fegaussianblur stdDeviation="0.5"/> <fecolormatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.14 0"/> <feblend mode="normal" in2="effect2_dropShadow_1498_229817" result="effect3_dropShadow_1498_229817"/> <feblend mode="normal" in="SourceGraphic" in2="effect3_dropShadow_1498_229817" result="shape"/> </filter> <clippath id="clip0_1498_229817"> <rect width="928" height="137" rx="16" fill="white"/> </clippath> <clippath id="clip1_1498_229817"> <rect y="153" width="928" height="233" rx="16" fill="white"/> </clippath> <clippath id="clip2_1498_229817"> <rect x="522" y="300" width="80" height="28" rx="8" fill="white"/> </clippath> </defs> </svg> </template> '},"onboarding/resources/elements/fake-game-controls.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var a=i(31601),r=i.n(a),l=i(76314),o=i.n(l)()(r());o.push([e.id,"fake-game-controls text{fill-opacity:1}",""]);const n=o},"onboarding/resources/elements/fake-play-game":(e,t,i)=>{i.r(t),i.d(t,{FakePlayGame:()=>a});class a{}},"onboarding/resources/elements/fake-play-game.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <svg width="129" height="44" viewBox="0 0 129 44" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="129" height="44" rx="22" fill="url(#paint0_linear_4053_12408)"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M35 15.667C35 14.2002 33.8002 13 32.3334 13H21.667C20.2002 13 19 14.2002 19 15.667V20.0059L22.4042 22.2753C22.8471 22.0155 23.3398 21.8728 23.8374 21.8473L27.0357 17C27.1394 16.2774 27.4692 15.581 28.0251 15.0251C29.392 13.6583 31.608 13.6583 32.9749 15.0251C34.3417 16.392 34.3417 18.6081 32.9749 19.9749C32.419 20.5308 31.7226 20.8606 31 20.9643V20.9643L27.1085 24.4485C27.2815 25.4287 26.9896 26.4751 26.2323 27.2323C24.9994 28.4652 23.0006 28.4652 21.7677 27.2323C21.0043 26.4689 20.7138 25.4118 20.8958 24.4247L19 23.1608V26.3331C19 27.8008 20.2002 29 21.667 29H31.05V26.8826C31.05 24.9303 33.1878 23.7313 34.8537 24.7494L35 24.8388V15.667ZM24.8418 25.4778C24.5889 25.8572 24.1726 26.0624 23.7484 26.0624C23.4983 26.0624 23.2454 25.991 23.0216 25.8419L21.8973 25.0924C21.9458 26.2118 22.8683 27.1045 23.9996 27.1045C25.162 27.1045 26.1043 26.1623 26.1043 24.9998C26.1043 23.8373 25.162 22.8951 23.9997 22.8951C23.8077 22.8951 23.622 22.9213 23.4453 22.9695L24.4777 23.6577C25.0809 24.0597 25.2439 24.8746 24.8418 25.4778ZM33 17.5C33 16.1193 31.8807 15 30.5 15C29.1193 15 28 16.1193 28 17.5C28 18.8807 29.1193 20 30.5 20C31.8807 20 33 18.8807 33 17.5ZM30.5 16C29.6716 16 29 16.6716 29 17.5C29 18.3284 29.6716 19 30.5 19C31.3284 19 32 18.3284 32 17.5C32 16.6716 31.3284 16 30.5 16ZM34.4518 32.7374C33.6189 33.2464 32.55 32.647 32.55 31.6708V27.3284C32.55 26.3523 33.6189 25.7528 34.4518 26.2618L38.0047 28.433C38.8022 28.9204 38.8022 30.0788 38.0047 30.5662L34.4518 32.7374Z" fill="white"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="24" font-weight="900" letter-spacing="-1px"> <tspan x="44" y="30.7273">${\'onboarding_game_tutorial.play\' | i18n}</tspan> </text> <mask id="mask0_4053_12408" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="97" y="12" width="20" height="20"> <rect x="97" y="12" width="20" height="20" fill="#D9D9D9"/> </mask> <g mask="url(#mask0_4053_12408)"> <path d="M106.999 25.1984L101.871 20.0702L103.041 18.9004L106.999 22.8587L110.958 18.9004L112.127 20.0702L106.999 25.1984Z" fill="white" fill-opacity="0.8"/> </g> <defs> <lineargradient id="paint0_linear_4053_12408" x1="129" y1="0" x2="102.113" y2="78.8291" gradientUnits="userSpaceOnUse"> <stop stop-color="#0BF2F6"/> <stop offset="1" stop-color="#9200FF"/> </lineargradient> </defs> </svg> </template> '},"onboarding/resources/elements/fake-save-mods":(e,t,i)=>{i.r(t),i.d(t,{FakeSaveMods:()=>a});class a{}},"onboarding/resources/elements/fake-save-mods.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <svg width="177" height="44" viewBox="0 0 177 44" fill="none" xmlns="http://www.w3.org/2000/svg"> <g filter="url(#filter0_b_4041_15785)"> <rect width="177" height="44" rx="22" fill="white" fill-opacity="0.1"/> <g clip-path="url(#clip0_4041_15785)"> <rect x="12" y="11" width="36" height="22" rx="11" fill="white" fill-opacity="0.1"/> <g filter="url(#filter1_dd_4041_15785)"> <circle cx="23" cy="22" r="9" fill="white" fill-opacity="0.8"/> </g> <path fill-rule="evenodd" clip-rule="evenodd" d="M23.9167 21.25L24.5 16L19.25 22.75H22.0833L21.5 28L26.75 21.25L23.9167 21.25Z" fill="#808080"/> </g> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="bold" letter-spacing="-0.5px"> <tspan x="56" y="27.0909">${\'onboarding_game_tutorial.save_mods\' | i18n}</tspan> </text> <rect x="131" y="14" width="30" height="16" rx="4" fill="url(#paint0_linear_4041_15785)"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="10" font-weight="bold" letter-spacing="0.5px"> <tspan x="135" y="25.6364">${\'onboarding_game_tutorial.pro\' | i18n}</tspan> </text> </g> <defs> <filter id="filter0_b_4041_15785" x="-35" y="-35" width="247" height="114" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fegaussianblur in="BackgroundImageFix" stdDeviation="17.5"/> <fecomposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_4041_15785"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_4041_15785" result="shape"/> </filter> <filter id="filter1_dd_4041_15785" x="11" y="11" width="24" height="24" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset dy="1"/> <fegaussianblur stdDeviation="1"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.06 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_4041_15785"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset dy="1"/> <fegaussianblur stdDeviation="1.5"/> <fecolormatrix type="matrix" values="0 0 0 0 0.0627451 0 0 0 0 0.0941176 0 0 0 0 0.156863 0 0 0 0.1 0"/> <feblend mode="normal" in2="effect1_dropShadow_4041_15785" result="effect2_dropShadow_4041_15785"/> <feblend mode="normal" in="SourceGraphic" in2="effect2_dropShadow_4041_15785" result="shape"/> </filter> <lineargradient id="paint0_linear_4041_15785" x1="161" y1="14" x2="147.713" y2="38.9135" gradientUnits="userSpaceOnUse"> <stop stop-color="#0BF2F6"/> <stop offset="1" stop-color="#9200FF"/> </lineargradient> <clippath id="clip0_4041_15785"> <rect x="12" y="11" width="36" height="22" rx="11" fill="white"/> </clippath> </defs> </svg> </template> '},"onboarding/resources/elements/fake-title-actions":(e,t,i)=>{i.r(t),i.d(t,{FakeTitleActions:()=>a});class a{}},"onboarding/resources/elements/fake-title-actions.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <svg width="112" height="32" viewBox="0 0 112 32" fill="none" xmlns="http://www.w3.org/2000/svg"> <g filter="url(#filter0_b_1498_217957)"> <rect width="32" height="32" rx="16" fill="white" fill-opacity="0.1"/> <mask id="mask0_1498_217957" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="6" y="6" width="20" height="20"> <rect x="6" y="6" width="20" height="20" fill="#D9D9D9"/> </mask> <g mask="url(#mask0_1498_217957)"> <path d="M12.4167 11.33L14.7324 8.31073C14.8972 8.08907 15.093 7.92628 15.3197 7.82238C15.5464 7.71846 15.7855 7.6665 16.037 7.6665C16.2846 7.6665 16.5174 7.72003 16.7353 7.82709C16.9532 7.93413 17.1445 8.09534 17.3093 8.31073L19.6118 11.3246L23.1538 12.4934C23.5107 12.6409 23.7812 12.8566 23.9655 13.1406C24.1498 13.4246 24.242 13.7285 24.242 14.0523C24.242 14.2058 24.2164 14.3652 24.1654 14.5305C24.1144 14.6959 24.0395 14.8523 23.9407 14.9998L21.6763 18.1136L21.7596 21.5222C21.7457 21.9749 21.5768 22.3691 21.2528 22.7047C20.9288 23.0403 20.5362 23.2081 20.0748 23.2081C20.0239 23.2081 19.8996 23.19 19.7019 23.1536L16 22.0863L12.342 23.152C12.2711 23.1809 12.209 23.194 12.156 23.1913C12.1029 23.1886 12.0488 23.1873 11.9938 23.1873C11.523 23.1873 11.1131 23.0264 10.764 22.7047C10.415 22.383 10.2474 21.9819 10.2613 21.5014L10.3446 18.1344L8.05131 14.979C7.95159 14.8286 7.87645 14.6791 7.8259 14.5307C7.7753 14.3822 7.75 14.2266 7.75 14.0638C7.75 13.7208 7.85069 13.4026 8.05208 13.1094C8.25347 12.8162 8.53472 12.6082 8.89583 12.4854L12.4167 11.33ZM13.3558 12.7194L9.71315 13.9469C9.63302 13.9736 9.57826 14.0284 9.54887 14.1112C9.5195 14.194 9.53152 14.2701 9.58494 14.3396L12.0171 17.6594L11.8654 21.2562C11.86 21.347 11.8921 21.4192 11.9615 21.4726C12.031 21.526 12.1085 21.5394 12.1939 21.5126L16.0104 20.4149L19.8477 21.471C19.9332 21.4977 20.0107 21.4843 20.0801 21.4309C20.1496 21.3775 20.1816 21.3054 20.1763 21.2145L20.0224 17.6168L22.4359 14.2979C22.4893 14.2285 22.5013 14.1523 22.472 14.0695C22.4426 13.9867 22.3878 13.932 22.3077 13.9053L18.6234 12.7194L16.2003 9.59117C16.1523 9.52173 16.0855 9.487 16 9.487C15.9145 9.487 15.8478 9.52173 15.7997 9.59117L13.3558 12.7194Z" fill="white" fill-opacity="0.8"/> </g> </g> <g filter="url(#filter1_b_1498_217957)"> <rect x="40" width="32" height="32" rx="16" fill="white" fill-opacity="0.1"/> <mask id="mask1_1498_217957" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="46" y="6" width="20" height="20"> <rect x="46" y="6" width="20" height="20" fill="#D9D9D9"/> </mask> <g mask="url(#mask1_1498_217957)"> <path d="M49.75 22.0706V20.4457H50.5897V15.0194C50.5897 13.732 50.9947 12.5918 51.8045 11.5987C52.6143 10.6056 53.6517 9.98199 54.9167 9.72773V8.85433C54.9167 8.5534 55.0221 8.29762 55.2331 8.08698C55.444 7.87632 55.7001 7.771 56.0015 7.771C56.3028 7.771 56.5585 7.87632 56.7684 8.08698C56.9783 8.29762 57.0833 8.5534 57.0833 8.85433V9.72773C58.3483 9.98199 59.3891 10.6008 60.2059 11.5842C61.0227 12.5675 61.431 13.7126 61.431 15.0194V20.4457H62.2708V22.0706H49.75ZM55.9934 24.6604C55.5544 24.6604 55.1808 24.5047 54.8726 24.1934C54.5644 23.8821 54.4102 23.5078 54.4102 23.0706H57.5897C57.5897 23.514 57.4334 23.8898 57.1208 24.198C56.8082 24.5063 56.4324 24.6604 55.9934 24.6604ZM52.2147 20.4457H59.8061V15.0194C59.8061 13.9574 59.4367 13.061 58.6979 12.3303C57.959 11.5995 57.0592 11.2341 55.9983 11.2341C54.9375 11.2341 54.0416 11.5995 53.3109 12.3303C52.5801 13.061 52.2147 13.9574 52.2147 15.0194V20.4457Z" fill="white" fill-opacity="0.8"/> </g> </g> <g filter="url(#filter2_b_1498_217957)"> <rect x="80" width="32" height="32" rx="16" fill="white" fill-opacity="0.1"/> <mask id="mask2_1498_217957" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="86" y="6" width="20" height="20"> <rect x="86" y="6" width="20" height="20" fill="#D9D9D9"/> </mask> <g mask="url(#mask2_1498_217957)"> <path d="M90.3529 22.1666V17.8269H88.832V16.2019H93.5403V17.8269H91.9778V22.1666H90.3529ZM90.3529 14.3814V9.8125H91.9778V14.3814H90.3529ZM93.667 14.4022V12.7773H95.1878V9.8125H96.8128V12.7773H98.3544V14.4022H93.667ZM95.1878 22.1666V16.2228H96.8128V22.1666H95.1878ZM100.044 22.1666V19.2019H98.4811V17.5769H103.189V19.2019H101.669V22.1666H100.044ZM100.044 15.7564V9.8125H101.669V15.7564H100.044Z" fill="white" fill-opacity="0.8"/> </g> </g> <defs> <filter id="filter0_b_1498_217957" x="-35" y="-35" width="102" height="102" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fegaussianblur in="BackgroundImageFix" stdDeviation="17.5"/> <fecomposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1498_217957"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1498_217957" result="shape"/> </filter> <filter id="filter1_b_1498_217957" x="5" y="-35" width="102" height="102" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fegaussianblur in="BackgroundImageFix" stdDeviation="17.5"/> <fecomposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1498_217957"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1498_217957" result="shape"/> </filter> <filter id="filter2_b_1498_217957" x="45" y="-35" width="102" height="102" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fegaussianblur in="BackgroundImageFix" stdDeviation="17.5"/> <fecomposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1498_217957"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_1498_217957" result="shape"/> </filter> </defs> </svg> </template> '},"onboarding/resources/elements/loading":(e,t,i)=>{i.r(t),i.d(t,{Loading:()=>a});class a{}},"onboarding/resources/elements/loading.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});var a=i(14385),r=i.n(a),l=new URL(i(18285),i.b);const o='<template> <require from="./loading.scss"></require> <require from="../../../app/resources/elements/app-header"></require> <app-header></app-header> <div class="loading-container"> <div class="wemod-logo"><img src="'+r()(l)+'"></div> <div class="loading-icon"> <svg class="top-left" width="7" height="7" viewBox="0 0 7 7" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M0.25 3.5L3.625 0.125L7 3.5L3.625 6.875L0.25 3.5Z" fill="white"/> </svg> <svg class="middle-right" width="5" height="5" viewBox="0 0 5 5" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M2.75 4.5C3.99264 4.5 5 3.49264 5 2.25C5 1.00736 3.99264 0 2.75 0C1.50736 0 0.5 1.00736 0.5 2.25C0.5 3.49264 1.50736 4.5 2.75 4.5Z" fill="white"/> </svg> <svg class="bottom-right" width="10" height="9" viewBox="0 0 10 9" fill="none" xmlns="http://www.w3.org/2000/svg"> <path opacity="0.5" d="M9.75 4.5L5.25 0L0.75 4.5L5.25 9L9.75 4.5Z" fill="white"/> </svg> <svg class="top-right" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"> <path opacity="0.5" d="M6.75 15.75L5.625 10.125L0 9L0 6.75L5.625 5.625L6.75 0H9L10.125 5.625L15.75 6.75V9L10.125 10.125L9 15.75H6.75Z" fill="white"/> </svg> <svg class="bottom-left" width="25" height="25" viewBox="0 0 25 25" fill="none" xmlns="http://www.w3.org/2000/svg"> <path opacity="0.5" d="M0 13.75V11.5L9 9.25L11.25 0.25H13.5L15.75 9.25L24.75 11.5V13.75L15.75 16L13.5 25H11.25L9 16L0 13.75Z" fill="white"/> </svg> </div> <h1>${\'onboarding.loading_games\' | i18n}</h1> </div> </template> '},"onboarding/resources/elements/loading.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var a=i(31601),r=i.n(a),l=i(76314),o=i.n(l)()(r());o.push([e.id,"loading{-webkit-app-region:drag;transition:opacity .3s;animation:onboarding-loading-opacity 1s ease-in}loading .wemod-logo{position:absolute;top:40px}loading .wemod-logo img{height:30px}loading .loading-container{height:100vh;width:100vw;display:flex;flex-direction:column;align-items:center;justify-items:center;justify-content:center;background:var(--theme--default--background)}loading .loading-container h1{display:flex;color:#fff;text-align:center;text-shadow:0px 0px 48px rgba(255,255,255,.5);font-size:32px;font-weight:800;line-height:100%;letter-spacing:-2px;animation:onboarding-loading-opacity 1s infinite alternate}loading .loading-container .loading-icon{position:relative;width:36px;height:36px}loading .loading-container .loading-icon svg{position:absolute}loading .loading-container .loading-icon svg.bottom-left{bottom:0;left:0;animation:onboarding-loading-spin 3s infinite ease-in-out reverse}loading .loading-container .loading-icon svg.bottom-right{bottom:0;right:0}loading .loading-container .loading-icon svg.top-left{top:0;left:0;animation:onboarding-loading-glow 2s infinite}loading .loading-container .loading-icon svg.top-right{top:0;right:0;animation:onboarding-loading-spin 2s infinite ease-in-out}loading .loading-container .loading-icon svg.middle-right{top:50%;right:0;animation:onboarding-loading-glow 3s infinite}loading app-header{position:absolute}loading app-header .logo{display:none}@keyframes onboarding-loading-spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}50%{transform:rotate(0deg)}}@keyframes onboarding-loading-glow{0%{scale:1;filter:drop-shadow(0 0 0 rgba(255, 255, 255, 0.5))}50%{scale:2;filter:drop-shadow(0 0 8px rgba(255, 255, 255, 0.5))}100%{scale:1;filter:drop-shadow(0 0 0 rgba(255, 255, 255, 0.5))}}@keyframes onboarding-loading-opacity{from{opacity:.5}to{opacity:1}}",""]);const n=o},"onboarding/resources/elements/title-card":(e,t,i)=>{i.r(t),i.d(t,{TitleCard:()=>l});var a=i(15215),r=i("aurelia-framework");class l{constructor(){this.format="portrait"}}(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],l.prototype,"titleInfo",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",String)],l.prototype,"format",void 0)},"onboarding/resources/elements/title-card.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template class="${format}"> <require from="./title-card.scss"></require> <require from="shared/cheats/resources/custom-attributes/steam-capsule-bg"></require> <require from="../../../resources/elements/title-thumbnail"></require> <div class="card-wrapper"> <div class="blurred-image"> <div if.bind="format == \'portrait\'" class="image" steam-capsule-bg="steam-id.bind: titleInfo.steamAppId"></div> <title-thumbnail else class="image" src.bind="titleInfo.thumbnail" width="260"></title-thumbnail> <div class="particles"></div> <div class="glow-top"></div> <div class="glow-bottom"></div> </div> <div class="glow"></div> <div if.bind="format == \'portrait\'" class="image" steam-capsule-bg="steam-id.bind: titleInfo.steamAppId"></div> <div else class="image"> <title-thumbnail class="image inner" src.bind="titleInfo.thumbnail" width="260"></title-thumbnail> </div> <div class="title-name"> <h1>${titleInfo.name}</h1> </div> </div> </template> '},"onboarding/resources/elements/title-card.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>w});var a=i(31601),r=i.n(a),l=i(76314),o=i.n(l),n=i(4417),s=i.n(n),d=new URL(i(45632),i.b),p=new URL(i(48526),i.b),c=new URL(i(94683),i.b),f=new URL(i(37963),i.b),g=o()(r()),h=s()(d),m=s()(p),u=s()(c),x=s()(f);g.push([e.id,`title-card{--duration: 0.75s;--easing: cubic-bezier(0.27, 1, 0.36, 1);display:block;width:210px;position:relative;z-index:0;-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0)}title-card.landscape{--ratio: calc(1 / 2.28 * 100%)}title-card.portrait{--ratio: calc(1.504 / 1 * 100%)}title-card .card-wrapper{opacity:.8;transition:var(--duration) var(--easing);display:block;padding-top:var(--ratio)}title-card .title-name{display:none;position:absolute;flex-direction:column;justify-content:center;align-items:center;height:100%;width:100%;top:0;transition:var(--duration) var(--easing)}title-card .title-name:hover,title-card .title-name:focus{transform:translateY(-10px)}title-card .title-name h1{color:#fff;text-align:center;font-size:20px;font-style:italic;font-weight:900;line-height:100%;letter-spacing:-0.75px;width:200px;max-width:200px;overflow:hidden;text-overflow:ellipsis}title-card .image.is-fallback+.title-name{display:flex}title-card .image{position:absolute;left:0;top:0;width:100%;background-color:#000;background-size:cover;background-repeat:no-repeat;background-position:center;pointer-events:none;border-radius:16px;overflow:hidden;transition:var(--duration) var(--easing)}title-card .image.is-fallback{background:radial-gradient(177.18% 151.73% at 100% 0%, #1fbaf8 0%, #2a9bf9 9.99%, #3874fb 21.4%, #4743fb 38.79%, #2a1257 68.95%, #1c1625 79.41%, #0f1014 100%) !important;display:block !important}title-card .image:after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);border-radius:16px;z-index:1;mix-blend-mode:overlay;cursor:pointer}title-card .image:before{content:"";position:absolute;top:-50%;width:200%;left:-50%;height:200%;background:linear-gradient(137.95deg, rgba(255, 255, 255, 0) 37.19%, #fff 44.41%, #fff 49.805%, rgba(255, 255, 255, 0) 67.215%);transform:translate(-20%, -20%);opacity:0;z-index:1;transition:var(--duration) var(--easing)}title-card div.image{padding-top:var(--ratio)}title-card .blurred-image{position:absolute;left:0;top:0;width:100%;opacity:0;pointer-events:none;transition:var(--duration) var(--easing)}title-card .blurred-image .image{filter:blur(40px);position:relative}title-card .blurred-image .glow-top,title-card .blurred-image .glow-bottom{content:"";position:absolute;mix-blend-mode:overlay;opacity:.5}title-card .blurred-image .glow-top{width:100%;background-image:url(${h});background-repeat:no-repeat;padding-top:90%;left:0;top:0;transform:translateY(-40%)}title-card .blurred-image .glow-bottom{width:155%;padding-top:81.76%;background-image:url(${m});background-repeat:no-repeat;left:-27.5%;bottom:0;transform:translateY(30%)}title-card .particles{width:100%;height:200px;position:absolute;left:0;top:0;transform:translateY(-50%);background-image:url(${u});background-size:100% auto;background-position:center center;background-repeat:repeat;mix-blend-mode:overlay;mask-image:radial-gradient(ellipse 100% 30%, rgba(255, 255, 255, 0.5) 0%, transparent 100%);animation:card-particles 5s linear infinite;opacity:0;transition:var(--duration) var(--easing);pointer-events:none}title-card .particles:before{content:"";position:absolute;left:0;top:50%;width:100%;padding-top:24.76%;transform:translateY(-50%);background-image:url(${x});background-size:cover;background-repeat:no-repeat}@keyframes card-particles{0%{background-position-y:0}100%{background-position-y:100%}}title-card .glow{position:absolute;left:0;top:0;width:100%;height:100%;opacity:0;transition:var(--duration) var(--easing);pointer-events:none}title-card:hover,title-card:hover *,title-card:focus,title-card:focus *{cursor:pointer}title-card:hover .card-wrapper,title-card:focus .card-wrapper{opacity:1}title-card:hover .image:not(.inner),title-card:focus .image:not(.inner){transform:translateY(-10px)}title-card:hover .image:not(.inner):before,title-card:focus .image:not(.inner):before{opacity:.3;transform:translate(0, 0)}title-card:hover .blurred-image,title-card:focus .blurred-image{opacity:1}title-card:hover .particles,title-card:focus .particles{opacity:1}title-card:hover .glow,title-card:focus .glow{opacity:1}`,""]);const w=g}}]);
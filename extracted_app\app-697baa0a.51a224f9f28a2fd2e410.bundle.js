"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3098],{39402:(e,t,s)=>{s.d(t,{X:()=>n});var a=s(15215),o=s("aurelia-framework"),r=s(56669),i=s(77502),l=s(40502);class n{constructor(e){this.currentStep=i.g.Start,this.selectedMods=[],this.selectedAllMods=!1,this.gamePlatform="",this.transcript=[],this.trainerInfo=e.config.trainerInfo,this.titleInfo=e.config.titleInfo,this.debugInfo=e.config.debugInfo,this.gameTranslations=e.config.gameTranslations}get metadata(){return{titleName:this.titleInfo?.name,gameInstructions:this.trainerInfo?.blueprint.notes,modsList:this.trainerInfo?.blueprint.cheats,gameTranslations:this.gameTranslations,selectedMods:this.selectedMods,gamePlatform:this.gamePlatform,transcript:this.transcript}}goToNextStep(e){let t=i.g.FlowError;switch(this.currentStep){case i.g.Start:t=i.g.GameGeneralSetup;break;case i.g.GameGeneralSetup:"yes"===e?(this.transcript.push({label:"Completed general game setup",value:"yes"}),this.trainerInfo?.blueprint.notes?t=i.g.GameInstructionsReview:(this.transcript.push({label:"Reviewed game instructions",value:"N/A"}),t=i.g.WhichModsNotWorking)):"no"===e&&(t=i.g.GameGeneralSetupFollowup);break;case i.g.GameGeneralSetupFollowup:"yes"===e?t=i.g.AgreedToGameGeneralSetup:"no"===e&&(this.transcript.push({label:"Completed general game setup",value:"no"}),this.trainerInfo?.blueprint.notes?t=i.g.GameInstructionsReview:(this.transcript.push({label:"Reviewed game instructions",value:"N/A"}),t=i.g.WhichModsNotWorking));break;case i.g.GameInstructionsReview:"yes"===e?(this.transcript.push({label:"Reviewed game instructions",value:"yes"}),t=i.g.WhichModsNotWorking):"no"===e&&(t=i.g.GameInstructionsFollowup);break;case i.g.GameInstructionsFollowup:"yes"===e?t=i.g.AgreedToGameInstructions:"no"===e&&(this.transcript.push({label:"Reviewed game instructions",value:"no"}),t=i.g.WhichModsNotWorking);break;case i.g.WhichModsNotWorking:"all"===e?(this.transcript.push({label:"Mods affected",value:"all"}),this.selectedAllMods=!0,t=i.g.ModBasicTroubleshoot):"specific"===e&&(t=i.g.SelectMods);break;case i.g.SelectMods:if(!Array.isArray(e)||!e.length)break;this.transcript.push({label:"Mods affected",value:e.map((e=>e.name)).join(", ")}),this.selectedMods=e,this.selectedMods.some((e=>e.instructions||e.description))?t=i.g.SelectedModsInstructions:(this.transcript.push({label:"Reviewed specific mod instructions",value:"N/A"}),t=i.g.ModBasicTroubleshoot);break;case i.g.SelectedModsInstructions:"yes"===e?(this.transcript.push({label:"Reviewed specific mod instructions",value:"yes"}),t=i.g.ModBasicTroubleshoot):"no"===e&&(t=i.g.SelectedModsInstructionsFollowup);break;case i.g.SelectedModsInstructionsFollowup:"yes"===e?t=i.g.AgreedToModsInstructions:"no"===e&&(this.transcript.push({label:"Reviewed specific mod instructions",value:"no"}),t=i.g.ModBasicTroubleshoot);break;case i.g.ModBasicTroubleshoot:"yes"===e?t=i.g.FlowResolved:"no"===e&&(this.transcript.push({label:"Completed basic mod troubleshooting",value:"yes"}),t=i.g.ModAdvancedTroubleshoot);break;case i.g.ModAdvancedTroubleshoot:if("yes"===e)t=i.g.FlowResolved;else if("no"===e){this.transcript.push({label:"Completed advanced mod troubleshooting",value:"yes"});const{step:e,platform:s}=(0,l.RY)(this.debugInfo?.gamePlatform);t=e,this.gamePlatform=s}break;case i.g.GamePlatformSelection:this.transcript.push({label:"Game platform",value:e}),this.gamePlatform=e,t="other"===e?i.g.SubmitIssueToDiscord:i.g.GamePlatformNotes;break;case i.g.GamePlatformNotes:"yes"===e?(this.transcript.push({label:"Reviewed game platform notes",value:"yes"}),t=i.g.SubmitIssueToDiscord):"no"===e&&(t=i.g.GamePlatformNotesFollowup);break;case i.g.GamePlatformNotesFollowup:"yes"===e?t=i.g.AgreedToGamePlatformNotes:"no"===e&&(this.transcript.push({label:"Reviewed game platform notes",value:"no"}),t=i.g.SubmitIssueToDiscord)}return this.currentStep=t,t===i.g.SubmitIssueToDiscord&&this.#e(),{step:t,metadata:this.metadata}}#e(){const e=this.selectedAllMods?"all":this.selectedMods.map((e=>({uuid:e.uuid,type:r.h1.Other,notes:null})));(0,l.$g)({envInfo:this.debugInfo.envInfo,trainerId:this.trainerInfo?.id||"",preferredInstallation:this.debugInfo.preferredInstallation,selectedMods:e,flowName:"mod-troubleshoot"})}}(0,a.Cg)([(0,o.computedFrom)("titleInfo.name","trainerInfo.blueprint.notes","trainerInfo.blueprint.cheats","gameTranslations","selectedMods","gamePlatform","transcript"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],n.prototype,"metadata",null)},40502:(e,t,s)=>{s.d(t,{$g:()=>u,RY:()=>c});var a=s(68663),o=s(27884),r=s(56669),i=s(96555),l=s(49442),n=s(77502);const m=["steam","epic","gog","xbox"];function c(e){return e&&m.includes(e)?{step:n.g.GamePlatformNotes,platform:e}:{step:n.g.GamePlatformSelection,platform:""}}async function u({envInfo:e,trainerId:t,preferredInstallation:s,selectedMods:n,flowName:m}){const c={crashLocation:null,allCheatsBroken:"all"===n||null===n&&null,cheats:Array.isArray(n)?n:[],notes:`support assistant chat report: ${m}`},u={trainerId:t,correlationId:s?.app?new i.o(s.app.platform,s.app.sku).toString():null,gameVersion:s?.version||null,type:r.M3.Failure,report:c,events:null,app:{version:e.appVersion},system:{version:e.osVersion,arch:e.osArch,locale:e.locale,freeMemory:e.freeMemory,antivirus:e.antivirusProducts}},g=o.mc.instance.get(a.x);await g.submitTrainerFeedback(u).catch(l.Y)}},42806:(e,t,s)=>{s.d(t,{B:()=>l});var a=s(15215),o=s("aurelia-framework"),r=s(77502),i=s(40502);class l{constructor(e){this.currentStep=r.g.Start,this.gamePlatform="",this.transcript=[],this.titleInfo=e.config.titleInfo,this.debugInfo=e.config.debugInfo}get metadata(){return{titleName:this.titleInfo?.name,gamePlatform:this.gamePlatform,transcript:this.transcript}}goToNextStep(e){let t=r.g.FlowError;switch(this.currentStep){case r.g.Start:t=r.g.OverlayBasicTroubleshoot;break;case r.g.OverlayBasicTroubleshoot:if("yes"===e)t=r.g.FlowResolved;else if("no"===e){this.transcript.push({label:"Completed basic overlay troubleshooting",value:"yes"});const{step:e,platform:s}=(0,i.RY)(this.debugInfo?.gamePlatform);t=e,this.gamePlatform=s}break;case r.g.GamePlatformSelection:this.transcript.push({label:"Game platform",value:e}),this.gamePlatform=e,t="other"===e?r.g.SubmitIssueToDiscord:r.g.GamePlatformNotes;break;case r.g.GamePlatformNotes:"yes"===e?(this.transcript.push({label:"Reviewed game platform notes",value:"yes"}),t=r.g.SubmitIssueToDiscord):"no"===e&&(t=r.g.GamePlatformNotesFollowup);break;case r.g.GamePlatformNotesFollowup:"yes"===e?t=r.g.AgreedToGamePlatformNotes:"no"===e&&(this.transcript.push({label:"Reviewed game platform notes",value:"no"}),t=r.g.SubmitIssueToDiscord)}return this.currentStep=t,{step:t,metadata:this.metadata}}}(0,a.Cg)([(0,o.computedFrom)("titleInfo.name","gamePlatform","transcript"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],l.prototype,"metadata",null)},83854:(e,t,s)=>{s.d(t,{c:()=>l});var a=s(15215),o=s("aurelia-framework"),r=s(77502),i=s(40502);class l{constructor(e){this.currentStep=r.g.Start,this.gamePlatform="",this.transcript=[],this.titleInfo=e.config.titleInfo,this.debugInfo=e.config.debugInfo,this.canUseOverlay=e.config.canUseOverlay}get metadata(){return{titleName:this.titleInfo?.name,canUseOverlay:this.canUseOverlay,gamePlatform:this.gamePlatform,transcript:this.transcript}}goToNextStep(e){let t=r.g.FlowError;switch(this.currentStep){case r.g.Start:t=r.g.GameCrashTiming;break;case r.g.GameCrashTiming:this.transcript.push({label:"Game crashes when",value:e.replace(/-/g," ")}),t=r.g.GameCrashBasicTroubleshoot;break;case r.g.GameCrashBasicTroubleshoot:"yes"===e?t=r.g.FlowResolved:"no"===e&&(this.transcript.push({label:"Completed basic troubleshooting",value:"yes"}),t=r.g.VerifyGameFiles);break;case r.g.VerifyGameFiles:"yes"===e?t=r.g.VerifyGameFilesResolved:"no"===e&&(this.transcript.push({label:"Completed game file verification",value:"yes"}),t=r.g.GameCrashAdvancedTroubleshoot);break;case r.g.GameCrashAdvancedTroubleshoot:if("yes"===e)t=r.g.FlowResolved;else if("no"===e){this.transcript.push({label:"Completed advanced troubleshooting",value:"yes"});const{step:e,platform:s}=(0,i.RY)(this.debugInfo?.gamePlatform);t=e,this.gamePlatform=s}break;case r.g.GamePlatformSelection:this.transcript.push({label:"Game platform",value:e}),this.gamePlatform=e,t="other"===e?r.g.SubmitIssueToDiscord:r.g.GamePlatformNotes;break;case r.g.GamePlatformNotes:"yes"===e?(this.transcript.push({label:"Reviewed game platform notes",value:"yes"}),t=r.g.SubmitIssueToDiscord):"no"===e&&(t=r.g.GamePlatformNotesFollowup);break;case r.g.GamePlatformNotesFollowup:"yes"===e?t=r.g.AgreedToGamePlatformNotes:"no"===e&&(this.transcript.push({label:"Reviewed game platform notes",value:"no"}),t=r.g.SubmitIssueToDiscord)}return this.currentStep=t,t===r.g.SubmitIssueToDiscord&&this.#e(),{step:t,metadata:this.metadata}}#e(){(0,i.$g)({envInfo:this.debugInfo.envInfo,trainerId:this.trainerId||"",preferredInstallation:this.debugInfo.preferredInstallation,selectedMods:null,flowName:"game-crash-troubleshoot"})}}(0,a.Cg)([(0,o.computedFrom)("titleInfo.name","gamePlatform","canUseOverlay","transcript"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],l.prototype,"metadata",null)},92004:(e,t,s)=>{s.d(t,{D:()=>l});var a=s(15215),o=s("aurelia-framework"),r=s(77502),i=s(40502);class l{constructor(e){this.currentStep=r.g.Start,this.gamePlatform="",this.transcript=[],this.titleInfo=e.config.titleInfo,this.debugInfo=e.config.debugInfo}get metadata(){return{titleName:this.titleInfo?.name,gamePlatform:this.gamePlatform,transcript:this.transcript}}goToNextStep(e){let t=r.g.FlowError;switch(this.currentStep){case r.g.Start:t=r.g.HotkeyNumpadCheck;break;case r.g.HotkeyNumpadCheck:if("yes"===e)this.transcript.push({label:"Using numpad for hotkeys",value:"yes"}),t=r.g.HotkeyNumpadCheckFollowup;else if("no"===e){this.transcript.push({label:"Using numpad for hotkeys",value:"no"});const{step:e,platform:s}=(0,i.RY)(this.debugInfo?.gamePlatform);t=e,this.gamePlatform=s}break;case r.g.HotkeyNumpadCheckFollowup:if("yes"===e)t=r.g.FlowResolved;else if("no"===e){this.transcript.push({label:"Verified numlock is on",value:"yes"});const{step:e,platform:s}=(0,i.RY)(this.debugInfo?.gamePlatform);t=e,this.gamePlatform=s}break;case r.g.GamePlatformSelection:this.transcript.push({label:"Game platform",value:e}),this.gamePlatform=e,t="other"===e?r.g.SubmitIssueToDiscord:r.g.GamePlatformNotes;break;case r.g.GamePlatformNotes:"yes"===e?(this.transcript.push({label:"Reviewed game platform notes",value:"yes"}),t=r.g.SubmitIssueToDiscord):"no"===e&&(t=r.g.GamePlatformNotesFollowup);break;case r.g.GamePlatformNotesFollowup:"yes"===e?t=r.g.AgreedToGamePlatformNotes:"no"===e&&(this.transcript.push({label:"Reviewed game platform notes",value:"no"}),t=r.g.SubmitIssueToDiscord)}return this.currentStep=t,{step:t,metadata:this.metadata}}}(0,a.Cg)([(0,o.computedFrom)("titleInfo.name","gamePlatform","transcript"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],l.prototype,"metadata",null)}}]);
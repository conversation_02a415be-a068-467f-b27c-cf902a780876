"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1536],{"resources/custom-attributes/attach-src":(e,t,i)=>{i.r(t),i.d(t,{AttachSrcCustomAttribute:()=>r});var a=i(15215),n=i("aurelia-framework");let r=class{#e;constructor(e){this.#e=e}attached(){this.#e.src=this.value}};r=(0,a.Cg)([(0,n.inject)(Element),(0,n.noView)(),(0,a.Sn)("design:paramtypes",[Object])],r)},"resources/custom-attributes/close-if-press-escape":(e,t,i)=>{i.r(t),i.d(t,{CloseIfPressEscapeCustomAttribute:()=>r});var a=i(15215),n=i("aurelia-framework");let r=class{constructor(){this.handleKeydown=this.handleKeydown.bind(this)}bind(){document.addEventListener("keydown",this.handleKeydown)}unbind(){document.removeEventListener("keydown",this.handleKeydown)}handleKeydown(e){"Escape"===e.key&&(this.open=!1)}};(0,a.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.twoWay}),(0,a.Sn)("design:type",Boolean)],r.prototype,"open",void 0),r=(0,a.Cg)([(0,n.noView)(),(0,a.Sn)("design:paramtypes",[])],r)},"resources/custom-attributes/detach-el":(e,t,i)=>{i.r(t),i.d(t,{DetachElCustomAttribute:()=>r});var a=i(15215),n=i("aurelia-framework");let r=class{#e;#t;#i;constructor(e){this.#e=e,this.#t=e.parentElement}attached(){const e=this.#e.getBoundingClientRect(),t=document.querySelector("router-view")??document.body;this.#i=document.createElement("DIV"),t.appendChild(this.#i),this.#i.style.position="fixed",this.#i.style.left=`${e.left}px`,this.#i.style.top=`${e.top}px`,this.#i.style.zIndex="999",this.#i.style.width=`${e.width}px`,this.#i.style.height=`${e.height}px`,this.#i.appendChild(this.#e)}detached(){this.#i?.parentElement?.removeChild(this.#i),this.#i=null,this.#t?.appendChild(this.#e)}};r=(0,a.Cg)([(0,n.inject)(Element),(0,n.noView)(),(0,a.Sn)("design:paramtypes",[Object])],r)},"resources/custom-attributes/pro-cta":(e,t,i)=>{i.r(t),i.d(t,{ProCtaCustomAttribute:()=>d});var a=i(15215),n=i(733),r=i("aurelia-event-aggregator"),s=i("aurelia-framework"),o=i(21795),l=i(38777);let d=class{#a;#e;#n;#r;#s;constructor(e,t,i){this.feature="save_mods",this.#e=e,this.#n=t,this.#r=i}attached(){this.#a="string"==typeof this.#e.getAttribute("tabIndex"),this.disabledChanged()}bind(){this.#s=(0,l.yB)(this.#e,"click",(async()=>{this.trigger&&!this.disabled&&(this.#n.publish(new o.kK(this.trigger)),await this.#r.openDialog(this.trigger,this.feature),this.callback?.())}))}unbind(){this.#s&&(this.#s?.dispose(),this.#s=null)}disabledChanged(){this.#a||this.#e.setAttribute("tabIndex",this.disabled?"-1":"0")}};(0,a.Cg)([(0,s.bindable)({primaryProperty:!0}),(0,a.Sn)("design:type",String)],d.prototype,"trigger",void 0),(0,a.Cg)([(0,s.bindable)(),(0,a.Sn)("design:type",Boolean)],d.prototype,"disabled",void 0),(0,a.Cg)([(0,s.bindable)(),(0,a.Sn)("design:type",Function)],d.prototype,"callback",void 0),(0,a.Cg)([(0,s.bindable)(),(0,a.Sn)("design:type",String)],d.prototype,"feature",void 0),d=(0,a.Cg)([(0,s.inject)(Element,r.EventAggregator,n.V),(0,s.noView)(),(0,a.Sn)("design:paramtypes",[Element,r.EventAggregator,n.V])],d)},"resources/custom-attributes/title-link":(e,t,i)=>{i.r(t),i.d(t,{TitleLinkCustomAttribute:()=>l});var a=i(15215),n=i("aurelia-event-aggregator"),r=i("aurelia-framework"),s=i(21795),o=i(38777);let l=class{#e;#n;#s;constructor(e,t){this.#e=e,this.#n=t}bind(){this.#s=(0,o.yB)(this.#e,"click",(async()=>{this.value&&this.titleId&&this.#n.publish(new s.dY(this.value,this.titleId,this.gameId||null,this.trainerId||null,this.searchResult||!1))}))}unbind(){this.#s?.dispose(),this.#s=null}};(0,a.Cg)([(0,r.bindable)({primaryProperty:!0}),(0,a.Sn)("design:type",String)],l.prototype,"value",void 0),(0,a.Cg)([(0,r.bindable)(),(0,a.Sn)("design:type",String)],l.prototype,"titleId",void 0),(0,a.Cg)([(0,r.bindable)(),(0,a.Sn)("design:type",String)],l.prototype,"gameId",void 0),(0,a.Cg)([(0,r.bindable)(),(0,a.Sn)("design:type",String)],l.prototype,"trainerId",void 0),(0,a.Cg)([(0,r.bindable)(),(0,a.Sn)("design:type",Boolean)],l.prototype,"searchResult",void 0),l=(0,a.Cg)([(0,r.inject)(Element,n.EventAggregator),(0,r.noView)(),(0,a.Sn)("design:paramtypes",[Element,n.EventAggregator])],l)},"resources/elements/account-email":(e,t,i)=>{i.r(t),i.d(t,{AccountEmail:()=>g});var a=i(15215),n=i("aurelia-framework"),r=i(20770),s=i(68663),o=i(62914),l=i(67064),d=i(20057),c=i(54995),u=i(70236),p=i(48881);let g=class{#o;#l;#d;#c;#u;constructor(e,t,i,a){this.focused=!1,this.#o=!1,this.#l=e,this.#d=t,this.#c=i,this.#u=a}bind(){this.accountChanged()}accountChanged(){this.value=this.account.email}#p(){return"string"==typeof this.value?this.value.replace(/[。｡︒]/g,"."):null}async submit(){if(this.#o)throw new Error("Email change is already in progress.");this.#o=!0;try{return await this.#g()}catch(e){const t=e.data&&e.data.entries?e.data.entries[0].message:e.toString();this.#c.toast({content:d.F2.literal(t),type:"alert"})}finally{this.#o=!1}}async#g(){const e=this.#p(),t=await this.emailInput.validateWithFeedback(),i=!!this.account.email;return!(!t||!e||(await this.#l.dispatch(p.Ui,await this.#d.changeAccountEmail(e)),this.#u.event("email_collect",{location:this.location,firstEmail:i},o.Io),0))}get existingEmail(){return(0,u.Lt)(this.account?.flags,2)&&this.account?.email?this.account?.email:null}};(0,a.Cg)([n.observable,(0,a.Sn)("design:type",Object)],g.prototype,"value",void 0),(0,a.Cg)([n.bindable,(0,a.Sn)("design:type",String)],g.prototype,"placeholder",void 0),(0,a.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.twoWay}),(0,a.Sn)("design:type",String)],g.prototype,"status",void 0),(0,a.Cg)([n.bindable,(0,a.Sn)("design:type",String)],g.prototype,"location",void 0),(0,a.Cg)([(0,n.computedFrom)("account.flags","account.email","value"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],g.prototype,"existingEmail",null),g=(0,a.Cg)([(0,c.m6)({selectors:{account:(0,c.$t)((e=>e.account))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[r.il,s.x,l.l,o.j0])],g)},"resources/elements/account-email.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./account-email.scss"></require> <require from="./email-input"></require> <email-input value.bind="value" placeholder.bind="placeholder" status.bind="status" existing-email.bind="existingEmail" view-model.ref="emailInput"></email-input> </template> '},"resources/elements/account-email.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});var a=i(31601),n=i.n(a),r=i(76314),s=i.n(r)()(n());s.push([e.id,"account-email email-input{display:block}",""]);const o=s},"resources/elements/beta-tag.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a="<template> <require from=\"./beta-tag.scss\"></require> ${'beta_tag.beta' | i18n} </template> "},"resources/elements/beta-tag.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});var a=i(31601),n=i.n(a),r=i(76314),s=i.n(r)()(n());s.push([e.id,"beta-tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;--beta-tag-bg-color: var(--theme--highlight);--beta-tag-color: #111;background-color:var(--beta-tag-bg-color);color:var(--beta-tag-color);min-width:initial}",""]);const o=s},"resources/elements/email-input":(e,t,i)=>{i.r(t),i.d(t,{EmailInput:()=>l});var a=i(15215),n=i("aurelia-framework"),r=i(16953),s=i(37294),o=i("shared/dialogs/basic-dialog");let l=class{#h;#m;#b;constructor(e,t){this.status="none",this.autoFocus=!1,this.large=!1,this.focused=!1,this.#b=!r.A.debug,this.#h=e,this.#m=t}attached(){this.autoFocus&&this.inputEl?.focus()}bind(){this.value=this.existingEmail||""}valueChanged(){this.#v()}existingEmailChanged(){this.value=this.existingEmail,this.#v()}#p(){return"string"==typeof this.value?this.value.replace(/[。｡︒]/g,"."):null}async#v(){const e=this.#p();if(null===e||0===e.length)return this.status="invalid",{status:s.q.Invalid};if(this.existingEmail===this.value)return this.status="none",null;this.status="validating";const t=await this.#h.validate(e);return e!==this.#p()?null:(this.status=t.status,t)}async validateWithFeedback(){const e=await this.#v();if(null===e||null===e.status)return!1;if(e.status===s.q.Valid)return!0;if(e.status===s.q.Invalid)return!this.#b||(await this.#m.ok("auth.invalid"),!1);if(e.status===s.q.Unsure)return"auth.looks_good"===await this.#m.show({cancelable:!0,message:"auth.misspelled_email_$email",messageParams:{email:this.#p()},options:[{label:"auth.cancel"},{label:"auth.looks_good",style:"primary"}]});throw new Error(`Unknown email validation status ${e.status}.`)}handleBlur(){this.value=this.inputEl.value,this.focused=!1}handleFocus(){this.focused=!0}};(0,a.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.twoWay}),(0,a.Sn)("design:type",String)],l.prototype,"value",void 0),(0,a.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,a.Sn)("design:type",String)],l.prototype,"placeholder",void 0),(0,a.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.fromView}),(0,a.Sn)("design:type",String)],l.prototype,"status",void 0),(0,a.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,a.Sn)("design:type",Boolean)],l.prototype,"autoFocus",void 0),(0,a.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,a.Sn)("design:type",String)],l.prototype,"existingEmail",void 0),(0,a.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,a.Sn)("design:type",Boolean)],l.prototype,"large",void 0),l=(0,a.Cg)([(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[s.D,o.BasicDialogService])],l)},"resources/elements/email-input.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});var a=i(14385),n=i.n(a),r=new URL(i(16280),i.b);const s='<template class="${focused ? \'focused\' : \'\'} ${value ? \'\' : \'empty\'} ${large ? \'large\' : \'\'} ${status === \'valid\' ? \'valid\' : \'\'}"> <require from="./email-input.scss"></require> <input type="text" ref="inputEl" value.bind="value & debounce:400" spellcheck="false" placeholder.bind="placeholder || \'\'" focus.trigger="handleFocus()" blur.trigger="handleBlur()"> <i if.bind="status === \'valid\'" class="ok"><inline-svg src="'+n()(r)+'"></inline-svg></i> </template> '},"resources/elements/email-input.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>p});var a=i(31601),n=i.n(a),r=i(76314),s=i.n(r),o=i(4417),l=i.n(o),d=new URL(i(83959),i.b),c=s()(n()),u=l()(d);c.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${u}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}email-input{position:relative;width:100%;display:inline-block;width:auto}email-input input{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%}email-input input::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}email-input input::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}email-input input:disabled{opacity:.5}email-input input:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}email-input input:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}email-input.large input{height:48px;width:100%;color:#fff;padding:12px;background-color:rgba(255,255,255,.05);border:1px solid rgba(255,255,255,.2);border-radius:12px;transition:background-color .15s,border-color .15s}email-input.large input,email-input.large input::placeholder{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px}email-input.large input::placeholder{color:rgba(255,255,255,.5)}email-input.large input:hover{border-color:rgba(255,255,255,.1)}email-input.large input:focus{background-color:rgba(255,255,255,0);border-color:rgba(255,255,255,.4);outline:none !important}email-input.valid input{padding-right:30px}email-input i{--input__icon--color: rgba(255, 255, 255, 0.4);position:absolute;right:0;top:0;height:100%;width:30px;display:inline-flex;align-items:center;justify-content:center;pointer-events:none}email-input i svg *{fill:var(--input__icon--color)}email-input i.ok{--input__icon--color: var(--color--accent)}`,""]);const p=c},"resources/elements/pro-cta-label":(e,t,i)=>{i.r(t),i.d(t,{ProCtaLabel:()=>l});var a=i(15215),n=i("aurelia-framework"),r=i(811),s=i(54995),o=i(70236);let l=class{constructor(e){this.promotions=e,this.trialAvailable=!0}get eligibleForFreeTrial(){return this.trialAvailable&&!(0,o.Lt)(this.account?.flags,2048)&&!(0,o.Lt)(this.promotions.promotion?.flags??0,1)}};(0,a.Cg)([n.bindable,(0,a.Sn)("design:type",Boolean)],l.prototype,"trialAvailable",void 0),(0,a.Cg)([n.bindable,(0,a.Sn)("design:type",String)],l.prototype,"trialKey",void 0),(0,a.Cg)([n.bindable,(0,a.Sn)("design:type",String)],l.prototype,"noTrialKey",void 0),(0,a.Cg)([(0,n.computedFrom)("account.flags","trialAvailable","promotions.promotion"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],l.prototype,"eligibleForFreeTrial",null),l=(0,a.Cg)([(0,s.m6)({selectors:{account:(0,s.$t)((e=>e.account))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[r.n])],l)},"resources/elements/pro-cta-label.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a="<template> ${eligibleForFreeTrial ? trialKey || 'pro_cta_label.start_free_trial' : noTrialKey || 'pro_cta_label.upgrade_to_pro' | i18n} </template> "},"resources/elements/progress-bar.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template bindable="progress, indeterminate"> <require from="./progress-bar.scss"></require> <div class="value" if.bind="!indeterminate" css.bind="{width: (progress * 100) +\'%\'}"></div> <div class="indeterminate-value" if.bind="indeterminate"></div> </template> '},"resources/elements/progress-bar.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});var a=i(31601),n=i.n(a),r=i(76314),s=i.n(r)()(n());s.push([e.id,"progress-bar{display:block;width:100%;height:7px;border-radius:100px;background:rgba(255,255,255,.2);position:relative;overflow:hidden}progress-bar .value{height:100%;border-radius:100px}progress-bar .indeterminate-value{position:absolute;height:100%;border-radius:2px;top:0;left:0;animation:indeterminate 2.1s cubic-bezier(0.65, 0.815, 0.735, 0.395) infinite}progress-bar .value,progress-bar .indeterminate-value{background:var(--theme--highlight)}@keyframes indeterminate{0%{left:-35%;right:100%}60%{left:100%;right:-90%}100%{left:100%;right:-90%}}",""]);const o=s},"resources/elements/remote-code":(e,t,i)=>{i.r(t),i.d(t,{RemoteCode:()=>l});var a=i(15215),n=i("aurelia-framework"),r=i(68663),s=i(85805),o=i(38777);let l=class{#d;#f;#y;constructor(e,t){this.remote=e,this.visible=!0,this.#f=null,this.#y=null,this.#d=t}attached(){this.visibleChanged(),this.#y=this.remote.onStatusChanged((e=>{e!==s.t.Disconnected&&(this.code=null)}))}detached(){null!==this.#y&&(this.#y.dispose(),this.#y=null)}get isConnected(){return this.remote.status===s.t.Connected}async visibleChanged(){this.visible&&(this.isConnected||this.code||await this.#w())}async#x(){this.isConnected||(this.code&&(this.timeElapsed=this.code.expiresIn),this.codeExpired=!0,await(0,o.Wn)(1e3),this.isConnected||(await this.#w(),this.codeExpired=!1))}async#w(){try{this.code=await this.#d.requestRemoteAuthCode()}catch{return void(this.code=null)}this.timeElapsed=0,clearInterval(this.#f),this.#f=setInterval((()=>{this.timeElapsed+=.01,(!this.code||this.timeElapsed>=this.code.expiresIn)&&(clearInterval(this.#f),this.visible?this.#x():this.code=null)}),10)}};(0,a.Cg)([(0,n.bindable)(),(0,a.Sn)("design:type",Boolean)],l.prototype,"visible",void 0),(0,a.Cg)([(0,n.computedFrom)("remote.status"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],l.prototype,"isConnected",null),l=(0,a.Cg)([(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[s.e,r.x])],l)},"resources/elements/remote-code.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./remote-code.scss"></require> <require from="./progress-bar.html"></require> <div class="code ${codeExpired ? \'expired\' : \'\'}"> <span repeat.for="digit of code.code.split(\'\')" class="digit">${digit}</span> </div> <progress-bar progress.bind="1 - timeElapsed / code.expiresIn"></progress-bar> </template> '},"resources/elements/remote-code.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});var a=i(31601),n=i.n(a),r=i(76314),s=i.n(r)()(n());s.push([e.id,"remote-code{display:flex;flex-direction:column;align-items:center;width:fit-content}remote-code .code{margin:0;height:40px;padding:8px;display:flex;align-items:center;justify-content:center;gap:8px;background-color:rgba(255,255,255,.05);border-radius:8px 8px 0 0;min-width:141px}remote-code .code .digit{font-weight:500;text-align:center;color:#fff;transition:color .25s;font-size:24px;letter-spacing:-1px}remote-code .code .digit:nth-child(1){margin-left:7px;transition-delay:0s}remote-code .code .digit:nth-child(2){transition-delay:.1s}remote-code .code .digit:nth-child(3){transition-delay:.2s}remote-code .code .digit:nth-child(4){transition-delay:.3s}remote-code .code .digit:nth-child(5){margin-right:7px;transition-delay:.4s}remote-code .code.expired .digit{color:rgba(0,0,0,0)}remote-code progress-bar{height:4px;background-color:rgba(255,255,255,.15);border-top-left-radius:0;border-top-right-radius:0;overflow:hidden}remote-code progress-bar .value{background:var(--theme--highlight) !important;border-radius:0}",""]);const o=s},"resources/elements/remote-qr-code":(e,t,i)=>{i.r(t),i.d(t,{RemoteQrCode:()=>o});var a=i(15215),n=i("aurelia-framework"),r=i(87583),s=i(16953);class o{constructor(){this.options=void 0}attached(){this.#C()}#C(){this.canvasElement&&r.mo(this.canvasElement,`${s.A.websiteUrl}/remote`,this.options)}}(0,a.Cg)([(0,n.bindable)(),(0,a.Sn)("design:type",Object)],o.prototype,"options",void 0)},"resources/elements/remote-qr-code.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./remote-qr-code.scss"></require> <canvas ref="canvasElement"></canvas> </template> '},"resources/elements/remote-qr-code.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>o});var a=i(31601),n=i.n(a),r=i(76314),s=i.n(r)()(n());s.push([e.id,"remote-qr-code canvas{width:100% !important;height:100% !important;border-radius:5px}",""]);const o=s},"resources/index":(e,t,i)=>{function a(e){e.globalResources(["./value-converters/game","./value-converters/flags","./custom-attributes/attach-src","./custom-attributes/pro-cta","./custom-attributes/title-link"])}i.r(t),i.d(t,{configure:()=>a}),i("aurelia-framework")}}]);
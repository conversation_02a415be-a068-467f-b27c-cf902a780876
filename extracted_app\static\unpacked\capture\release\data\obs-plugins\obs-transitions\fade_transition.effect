uniform float4x4 ViewProj;
uniform texture2d tex_a;
uniform texture2d tex_b;
uniform float fade_val;

sampler_state textureSampler {
	Filter    = Linear;
	AddressU  = Clamp;
	AddressV  = Clamp;
};

struct VertData {
	float2 uv  : TEXCOORD0;
	float4 pos : POSITION;
};

struct FragData {
	float2 uv  : TEXCOORD0;
};

VertData VSDefault(VertData v_in)
{
	VertData vert_out;
	vert_out.uv  = v_in.uv;
	vert_out.pos = mul(float4(v_in.pos.xyz, 1.0), ViewProj);
	return vert_out;
}

float srgb_nonlinear_to_linear_channel(float u)
{
	return (u <= 0.04045) ? (u / 12.92) : pow(mad(u, 1. / 1.055, .055 / 1.055), 2.4);
}

float3 srgb_nonlinear_to_linear(float3 v)
{
	return float3(srgb_nonlinear_to_linear_channel(v.r), srgb_nonlinear_to_linear_channel(v.g), srgb_nonlinear_to_linear_channel(v.b));
}

float4 Fade(FragData f_in)
{
	float4 a_val = tex_a.<PERSON>ple(textureSampler, f_in.uv);
	float4 b_val = tex_b.Sample(textureSampler, f_in.uv);
	float4 rgba = lerp(a_val, b_val, fade_val);
	return rgba;
}

float4 PSFade(FragData f_in) : TARGET
{
	float4 rgba = Fade(f_in);
	rgba.rgb = srgb_nonlinear_to_linear(rgba.rgb);
	return rgba;
}

float4 PSFadeLinear(FragData f_in) : TARGET
{
	float4 rgba = Fade(f_in);
	return rgba;
}

float4 FadeSingle(FragData f_in)
{
	float4 a_val = tex_a.Sample(textureSampler, f_in.uv);
	float4 rgba = a_val * fade_val;
	return rgba;
}

float4 PSFadeSingle(FragData f_in) : TARGET
{
	float4 rgba = FadeSingle(f_in);
	return rgba;
}

technique Fade
{
	pass
	{
		vertex_shader = VSDefault(v_in);
		pixel_shader = PSFade(f_in);
	}
}

technique FadeLinear
{
	pass
	{
		vertex_shader = VSDefault(v_in);
		pixel_shader = PSFadeLinear(f_in);
	}
}

technique FadeSingle
{
	pass
	{
		vertex_shader = VSDefault(v_in);
		pixel_shader = PSFadeSingle(f_in);
	}
}

"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6773],{6745:(e,t,s)=>{s.d(t,{xr:()=>y});var a=s(15215),r=s("aurelia-event-aggregator"),i=s("aurelia-framework"),n=s(84157);const o="set_settings",c="get_trainer_info",u="set_trainer_value",l="get_mod_timer_messages_dismissed",d="set_settings",h="toggle_pinned",p="trainer_value_set",g="cheat_states_set",b="set_account",m="mod_timer_messages_dismissed",v="overlay_hotkey_pressed",_="add_overlay_notification";class y{#e;#t;constructor(e){this.#t=new r.EventAggregator,this.#e=e}async initialize(){[this.info,this.trainerInfo,this.modTimerMessagesDismissed]=await Promise.all([await(this.#e?.execute("get_app_info",null)),await(this.#e?.execute(c,null)),await(this.#e?.execute(l,null))]),this.#e.setHandler(o,(async e=>this.#t.publish(d,e))),this.#e.setHandler("toggle_pinned",(async e=>this.#t.publish(h,e))),this.#e.setHandler(u,(async e=>this.#t.publish(p,e))),this.#e.setHandler("set_cheat_states",(async e=>{this.cheatStates=e,this.#t.publish(g,e)})),this.#e.setHandler(o,(async e=>this.#t.publish(d,e))),this.#e.setHandler("set_account",(async e=>this.handleAccountChange(e))),this.#e.setHandler("set_mod_timer_messages_dismissed",(async e=>{this.setModTimerMessagesDismissed(e)})),this.#e.setHandler("on_overlay_hotkey_pressed",(async()=>this.#t.publish(v))),this.#e.setHandler("add_overlay_notification",(async e=>this.#t.publish(_,e)))}handleAccountChange(e){this.account=e,this.#t.publish(b,e)}requestOverlaySettings(){this.#e.execute("request_settings",null)}requestAccount(){this.#e.execute("request_account",null)}event(e,t,s){const a={name:e,params:t,dispatch:s};this.#e.execute("analytics_event",a)}setTrainerValue(e){this.#e.execute(u,e)}markCheatInstructionsRead(e){this.#e.execute("mark_cheat_instructions_read",e)}onSettingsChanged(e){return this.#t.subscribe(d,e)}onTogglePinned(e){return this.#t.subscribe(h,e)}onAccountChanged(e){return this.#t.subscribe(b,e)}onTrainerValueSet(e){return this.#t.subscribe(p,e)}onCheatStatesSet(e){return this.#t.subscribe(g,e)}onModTimerMessagesDismissed(e){return this.#t.subscribe(m,e)}onOverlayHotkeyPressed(e){return this.#t.subscribe(v,e)}onOverlayNotificationRequested(e){return this.#t.subscribe(_,e)}async setGlobalBlockInput(e){await n.ipcRenderer.invoke("ACTION_OVERLAY_BLOCK_INPUT",e)}async getAuthCode(){return await this.#e.execute("get_auth_code",null)||null}async refreshTrainerInfo(){this.trainerInfo=await(this.#e?.execute(c,null)),this.cheatStates=this.trainerInfo?.cheatStates}async getPlayerCoordinates(){return await this.#e.execute("get_player_coordinates",null)}async setPlayerCoordinates(e){return await this.#e.execute("set_player_coordinates",e)}pinMod(e,t,s,a){this.#e.execute("set_pinned_mod",{gameId:e,trainerId:t,mod:s,pin:a})}async dismissModTimerMessage(e){await this.#e.execute("dismiss_mod_timer_message",e),this.modTimerMessagesDismissed=await this.#e.execute(l,null),this.#t.publish(m,this.modTimerMessagesDismissed)}async getModTimerMessagesDismissed(){return await this.#e.execute(l,null)}setModTimerMessagesDismissed(e){this.modTimerMessagesDismissed=e,this.#t.publish(m,e)}setModTimer(e,t){this.#e.execute("set_mod_timer",{config:e,firstCall:t})}async saveCaptureHighlight(){await this.#e.execute("save_capture_highlight",null)}}(0,a.Cg)([i.observable,(0,a.Sn)("design:type",Object)],y.prototype,"account",void 0)},"overlay/debug/overlay-debug":(e,t,s)=>{s.r(t),s.d(t,{OverlayDebug:()=>n});var a=s(15215),r=s("aurelia-framework");const i=[{component:"./resources/elements/maps-debug",label:"Maps"}];class n{constructor(){this.open=!1,this.currentTab=i[0],this.tabs=i}close(){this.open=!1}}(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],n.prototype,"open",void 0)},"overlay/debug/overlay-debug.html":(e,t,s)=>{s.r(t),s.d(t,{default:()=>a});const a='<template> <require from="./overlay-debug.scss"></require> <require from="../../shared/resources/elements/close-button"></require> <require from="../../shared/resources/elements/tabs"></require> <require from="../../shared/resources/elements/tab"></require> <main> <close-button click.delegate="close()"></close-button> <tabs show.bind="tabs.length > 1"> <tab repeat.for="tab of tabs" click.delegate="currentTab = tab" class="${currentTab === tab ? \'active\' : \'\'}">${currentTab.label}</tab> </tabs> <article> <compose view-model.bind="currentTab.component"></compose> </article> </main> </template> '},"overlay/debug/overlay-debug.scss":(e,t,s)=>{s.r(t),s.d(t,{default:()=>o});var a=s(31601),r=s.n(a),i=s(76314),n=s.n(i)()(r());n.push([e.id,"overlay-debug{position:fixed;padding:20px;left:0;top:0;width:100%;height:100%;z-index:0}overlay-debug>main{position:relative;width:100%;height:100%;background:rgba(var(--theme--background--rgb), 0.95);border-radius:10px;padding:20px;display:flex;flex-direction:column;gap:20px}overlay-debug>main>tabs{flex:0 0 auto}overlay-debug>main>article{flex:1 1 auto}overlay-debug close-button{position:absolute;right:-13px;top:-13px;z-index:1}overlay-debug .debug-buttons-row{display:flex;flex-wrap:wrap;gap:10px}overlay-debug .debug-buttons-row+.debug-button-row{margin-top:10px}",""]);const o=n},"overlay/debug/resources/elements/maps-debug":(e,t,s)=>{s.r(t),s.d(t,{MapsDebug:()=>o});var a=s(15215),r=s("aurelia-framework"),i=s(20770),n=s(79522);let o=class{#s;constructor(e){this.#s=e}clearMapSettings(){this.#s.dispatch(n.y0)}};o=(0,a.Cg)([(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[i.il])],o)},"overlay/debug/resources/elements/maps-debug.html":(e,t,s)=>{s.r(t),s.d(t,{default:()=>a});const a='<template> <div class="debug-buttons-row"> <wm-button click.delegate="clearMapSettings()">Clear Map Settings</wm-button> </div> </template> '}}]);
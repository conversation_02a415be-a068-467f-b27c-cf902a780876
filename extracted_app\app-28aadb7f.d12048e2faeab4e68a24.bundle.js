"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[96,7488],{17724:(e,t,s)=>{s.d(t,{O4:()=>c,hD:()=>o,hb:()=>r,i7:()=>i,o3:()=>a});const n=new Set([502,503,504,520]);function r(e){return e instanceof a&&n.has(e.response.status)}class i{response(e){return e.status>=400?async function(e){throw await o.fromResponse(e)??new a(`API request failed with status ${e.status}.`,e)}(e):e}}class a extends Error{constructor(e,t){super(e),Object.setPrototypeOf(this,a.prototype),this.response=t}}class o extends a{static async fromResponse(e){const t=await e.json().catch((()=>null));return function(e){return null!==e&&"object"==typeof e&&"number"==typeof e.status&&"string"==typeof e.code&&"string"==typeof e.message&&["object","undefined"].includes(typeof e.data)}(t)?new o(e,t):null}constructor(e,t){super(t.message,e),Object.setPrototypeOf(this,o.prototype),this.status=t.status,this.code=t.code,this.data=t.data}get isMaintenanceError(){return 503===this.status}}var c;!function(e){e.BadRequest="bad-request",e.Unauthorized="unauthorized",e.AccessDenied="access-denied",e.NotFound="not-found",e.MethodNotAllowed="method-not-allowed",e.NotAcceptable="not-acceptable",e.Conflict="conflict",e.Gone="gone",e.UnsupportedMediaType="unsupported-media-type",e.UnprocessableEntity="unprocessable-entity",e.ResourceLocked="resource-locked",e.RateLimited="rate-limited",e.InternalError="internal-error",e.ServiceUnavailable="service-unavailable",e.ValidationError="validation-error",e.EmailVerificationRequired="email-verification-required",e.PaymentDeclined="payment-declined",e.UserAlreadySubscribed="user-already-subscribed",e.MaxGamesFollowed="max-games-followed"}(c||(c={}))},24008:(e,t,s)=>{s.d(t,{Cz:()=>i,D1:()=>a,nC:()=>c,rT:()=>o});const n=e=>"object"==typeof e&&null!==e,r=[["titles",n],["games",n],["creators",n],["genres",n],["platforms",n],["maps",Array.isArray],["queue",Array.isArray],["stats",n],["featured",Array.isArray],["announcements",Array.isArray],["polls",Array.isArray],["releaseChannels",Array.isArray],["featuredGames",Array.isArray]];function i(e){return null===e||n(e)&&r.every((t=>t[1](e[t[0]])))}var a,o,c;!function(e){e[e.HasAssistant=1]="HasAssistant"}(a||(a={})),function(e){e[e.ReleaseQueued=1]="ReleaseQueued",e[e.UpdateQueued=2]="UpdateQueued",e[e.Active=4]="Active",e[e.Retired=8]="Retired",e[e.Unsupported=16]="Unsupported",e[e.Outdated=32]="Outdated",e[e.Free=64]="Free",e[e.AllowCheatSuggestions=128]="AllowCheatSuggestions",e[e.Queued=3]="Queued",e[e.Available=12]="Available",e[e.OverlaySupported=256]="OverlaySupported",e[e.PrecisionModsSupported=512]="PrecisionModsSupported"}(o||(o={})),function(e){e[e.HasGameCoordinates=1]="HasGameCoordinates"}(c||(c={}))},28035:(e,t,s)=>{s.d(t,{S:()=>i});var n=s(92126),r=s(83260);class i{#e;#t;constructor(e,t){this.#e=(new n.Qq).configure((t=>t.withBaseUrl(e))),this.#t=btoa(JSON.stringify(t))}get baseUrl(){return this.#e.baseUrl}addInterceptor(e){this.#e.interceptors.push(e)}get(e,t){return this.fetch({method:"GET",endpoint:e,query:t})}post(e,t){return this.fetch({method:"POST",endpoint:e,body:t})}put(e,t){return this.fetch({method:"PUT",endpoint:e,body:t})}patch(e,t){return this.fetch({method:"PATCH",endpoint:e,body:t})}delete(e){return this.fetch({method:"DELETE",endpoint:e})}async fetch(e){const t={Accept:"application/json","X-Super-Properties":this.#t},s={method:e.method,headers:t};void 0!==e.body&&(e.body instanceof FormData?s.body=e.body:(s.headers||(s.headers={}),s.headers["Content-Type"]="application/json",s.body=JSON.stringify(e.body)));let n=e.endpoint;"object"==typeof e.query&&null!==e.query&&(n+=`?${(0,r.Go)(e.query)}`);const i=this.#e.buildRequest(n,s);e.name&&e.collectMetrics&&(i.name=e.name);const a=await this.#e.fetch(i);return await this.#s(a)}async#s(e){return"application/json"===e.headers.get("Content-Type")?await e.json():await e.text()}}},29702:(e,t,s)=>{s.d(t,{K:()=>i});var n=s(15215),r=s("aurelia-framework");let i=class{#n;constructor(e){this.#n=e}collectOne(e){this.collectMany([e])}collectMany(e){navigator.sendBeacon&&navigator.sendBeacon(`${this.#n}/v3/metrics`,JSON.stringify(e))}};i=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[String])],i)},41548:(e,t,s)=>{s.d(t,{k:()=>a});var n=s(15215),r=s("aurelia-framework"),i=s(84551);let a=class{#r;constructor(e){this.#r=e}request(e){return e.name?(e.start_time=Date.now(),e):e}response(e,t){return t?.name&&t.start_time&&this.#r.report({endpoint:t.name,method:t.method,responseTime:Date.now()-t.start_time}),e}};a=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.Y])],a)},41572:(e,t,s)=>{s.d(t,{a:()=>n});const n=e=>!!e?.subscription},44759:(e,t,s)=>{s.d(t,{Rk:()=>u,WA:()=>l});var n=s(15215),r=s(7530),i=s("aurelia-framework"),a=s(92465),o=s(28035),c=s(17724);class h extends Error{constructor(e,t){super(e),this.innerError=t,Object.setPrototypeOf(this,h.prototype)}}function u(e){return e instanceof h}let l=class{#i;#a;#o;#c;constructor(e){this.#a=new a._M,this.#i=e,this.#o=(new a.Vd).pushEventListener(window,"online",(()=>this.#h())).pushEventListener(window,"offline",(()=>this.#u())),e.addInterceptor({responseError:e=>{if(e instanceof TypeError)throw this.#u(),new h("Request failed due to a network error",e);throw e instanceof c.hD&&e.isMaintenanceError&&this.#l(),e}}),this.status=navigator.onLine?"online":"offline","online"!==this.status&&this.#h()}onStatusChanged(e){return this.#a.subscribe("_",e)}dispose(){this.#a.dispose(),this.#o.dispose()}#u(){"offline"!==this.status&&(this.#d("offline"),this.#h())}#l(){"maintenance"!==this.status&&(this.#d("maintenance"),this.#h())}async#h(){if("online"!==this.status){this.#c&&(this.#c.dispose(),this.#c=null);try{await this.#i.get("/v3/ping"),this.#d("online")}catch{this.#c=(0,a.Ix)((()=>this.#h()),5e3)}}}#d(e){e!==this.status&&(this.status=e,(0,r.N9)("online-changed"),this.#a.publish("_",e))}};l=(0,n.Cg)([(0,i.autoinject)(),(0,n.Sn)("design:paramtypes",[o.S])],l)},51808:(e,t,s)=>{s.d(t,{V:()=>n});const n={approved:1,isNew:2}},56669:(e,t,s)=>{var n,r,i,a;s.d(t,{BW:()=>r,Gp:()=>n,M3:()=>a,h1:()=>i}),function(e){e[e.PlayPressed=0]="PlayPressed",e[e.GameLaunched=1]="GameLaunched",e[e.GameFound=2]="GameFound",e[e.TrainerExecuted=3]="TrainerExecuted",e[e.TrainerInitialized=4]="TrainerInitialized",e[e.ValueChanged=5]="ValueChanged",e[e.LogMessage=6]="LogMessage",e[e.TrainerEnd=7]="TrainerEnd",e[e.TrainerInjecting=8]="TrainerInjecting",e[e.OpeningConnection=9]="OpeningConnection"}(n||(n={})),function(e){e[e.Other=0]="Other",e[e.MainMenu=1]="MainMenu",e[e.Loading=2]="Loading",e[e.InGamePlaying=3]="InGamePlaying",e[e.InGameMenu=4]="InGameMenu"}(r||(r={})),function(e){e[e.Other=0]="Other",e[e.GameCrash=1]="GameCrash",e[e.NoEffect=2]="NoEffect",e[e.NotAsExpected=3]="NotAsExpected"}(i||(i={})),function(e){e[e.Success=0]="Success",e[e.Failure=1]="Failure"}(a||(a={}))},57503:(e,t,s)=>{s.d(t,{Z:()=>a});var n=s(15215),r=s("aurelia-framework"),i=s(60321);let a=class{#p;#f;#g;#m;#y;#w;#v;constructor(e){this.#f=!1,this.#g=null,this.#m=null,this.#p=e}get authorized(){return!!this.#m}setTokenRefreshedHandler(e){this.#y=e}setDeauthorizedHandler(e){this.#w=e}setAccessTokenResponse(e){"object"==typeof e&&(this.#m=e)}async forceRefreshAccessToken(){this.#f=!0,await this.#b()}async request(e){e.bodyUsed||e.cloned||!["POST","PUT"].includes(e.method)||(e.cloned=e.clone());const t=await this.#b();return null!==t&&e.headers.set("Authorization",`Bearer ${t.accessToken}`),e}async responseError(e,t,s){if("object"==typeof e&&null!==e&&401===e.status)if(this.#m){if(this.#m.refreshToken)return this.#f=!0,await s.fetch(t.cloned??t.clone())}else if(await this.#T(),this.#m)return await s.fetch(t.cloned??t.clone());throw e}async#b(){if(null===this.#m){if(!this.initialAccessTokenHandler)return null;if(await this.initialAccessTokenHandler(),null===this.#m)return null}if(this.#g)await this.#g;else if(this.#f||this.#A(this.#m)){this.#g=this.#k(this.#m);try{await this.#g}finally{this.#g=null}}return this.#m}#A(e){return 0!==e.expiresAt&&e.expiresAt<Date.now()/1e3+150}async#k(e){if(e.refreshToken)try{this.#m=await this.#p.requestAccessToken({grant_type:"refresh_token",refresh_token:e.refreshToken}),this.#f=!1,await this.#y(this.#m)}catch(e){throw[400,401,403].includes(e.status)&&await this.#T(),e}else await this.#T()}async#T(){return this.#f=!1,this.#m=null,await this.#w()}set initialAccessTokenHandler(e){this.#v=e}get initialAccessTokenHandler(){return this.#v}};a=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.Q])],a)},60321:(e,t,s)=>{s.d(t,{Q:()=>i});var n=s(92126),r=s(83260);class i{#e;#R;constructor(e,t){this.#e=(new n.Qq).configure((t=>{t.withBaseUrl(e)})),this.#R=t}async revokeAccessToken(e){await this.#e.fetch("/auth/token",{method:"DELETE",headers:{Authorization:`Bearer ${e}`}})}async requestAccessToken(e){const t=await this.#e.fetch("/auth/token",{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:(0,r.Go)({...e,client_id:this.#R})});if(t.status>=300)throw t;const s=await t.json();if("bearer"!==s.token_type.toLocaleLowerCase())throw new Error(`Expected bearer token type, got ${s.token_type}.`);return{accessToken:s.access_token,refreshToken:s.refresh_token,userId:s.user_id,expiresAt:Math.floor(Date.now()/1e3)+s.expires_in,clientParams:s.client_params}}}},68539:(e,t,s)=>{s.d(t,{z:()=>r});var n=s("aurelia-event-aggregator");class r{#a;#E;#i;#S;#C;constructor(e,t={},s=void 0){this.#a=new n.EventAggregator,this.#E=new Set,this.#S=new Map,this.assignments=new Map,this.#i=e,this.assignments=new Map(Object.entries(t)),this.#C=s}async trigger(e){const t=this.assignments.get(e);if(void 0!==t)return t;if(this.#C&&!this.#C.includes(e))return null;const s=await this.#i.post("/v3/experiment",{key:e});return this.#O({[e]:s.variant}),s.variant}setOverride(e,t){this.#S.set(e,t),this.#O({[e]:t})}clearOverride(e){this.#S.delete(e)}queueTrigger(e){this.#E.add(e)}onVariantChanged(e){return this.#a.subscribe("variant",e)}createInterceptor(){return{request:e=>(this.#E.size>0&&(e.headers.set("X-Trigger-Experiments",btoa(JSON.stringify(Array.from(this.#E)))),this.#E.clear()),e),response:(e,t)=>{const s=e.headers.get("X-Experiments");if(s){let e=null;try{const t=JSON.parse(atob(s));"object"==typeof t&&null!==t&&(e=t)}catch{}e&&this.#O(e)}return e}}}setAllExperiments(e){e={...e,...Object.fromEntries(this.#S)};const t=new Set(Object.keys(e));for(const e of Array.from(this.assignments.keys()))t.has(e)||(this.assignments.delete(e),this.#a.publish("variant",{existingKey:e,variant:null}));this.#O(e)}#O(e){e={...e,...Object.fromEntries(this.#S)};for(const[t,s]of Object.entries(e))(this.assignments.get(t)??null)!==s&&(this.assignments.set(t,s),this.#a.publish("variant",{key:t,variant:s}))}}},"shared/api/index":(e,t,s)=>{s.r(t),s.d(t,{ApiError:()=>r.hD,ApiErrorInterceptor:()=>r.i7,AuthorizationInterceptor:()=>o.Z,ErrorCode:()=>r.O4,ResponseError:()=>r.o3,WeModApiClient:()=>n.S,WeModAuthClient:()=>a.Q,configure:()=>l,isTemporaryServerError:()=>r.hb}),s("aurelia-framework");var n=s(28035),r=s(17724),i=s(41548),a=s(60321),o=s(57503),c=s(68539),h=s(29702),u=s("shared/api/value-converters");function l(e,t){const s=new a.Q(t.baseUrl,t.clientId);e.container.registerInstance(a.Q,s);const l=new n.S(t.baseUrl,t.superProperties);e.container.registerInstance(n.S,l);const d=new h.K(t.baseUrl);e.container.registerInstance(h.K,d);const p=e.container.get(r.i7);l.addInterceptor(p);const f=new o.Z(s);e.container.registerInstance(o.Z,f),t.initialAccessTokenResponse&&f.setAccessTokenResponse(t.initialAccessTokenResponse),l.addInterceptor(f);const g=e.container.get(i.k);l.addInterceptor(g);const m=new c.z(l,t.activeExperiments,t.triggerableExperiments);e.container.registerInstance(c.z,m),l.addInterceptor(m.createInterceptor()),e.container.registerInstance(u.CdnValueConverter,new u.CdnValueConverter(t.cdnUrl)),e.globalResources(["./value-converters"])}}}]);
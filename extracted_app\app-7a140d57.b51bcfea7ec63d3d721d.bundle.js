"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6958],{10110:(e,t,s)=>{s.d(t,{x:()=>d});var i=s(15215),n=s("aurelia-framework"),r=s(20770),a=s(45660),o=s(59239),c=s(68663),h=s(20057),l=s(48881),u=s(83802);let d=class{#e;#t;#s;#i;constructor(e,t,s,i){this.#e=e,this.#t=t,this.#s=s,this.#i=i}async fetch(e,t){const s={gameId:e,trainerId:t,locale:this.#s.getEffectiveLocale().baseName,creatorMode:await this.#t.state.pipe((0,a.$)(),(0,o.E)("settings","creatorMode")).toPromise()??!1};return await this.#n(s)||await this.#r(s)||this.#a(s)||await this.#o(s)}async#n(e){if(!e.trainerId)return null;const t=this.#a(e);if(e.trainerId===t?.id)return t;try{return await this.#c(e)}catch{return e.creatorMode?null:await this.#h(e)}}async#c(e){const t=e.creatorMode?await this.#e.getLocalTrainerById(e.trainerId??"",e.locale):await this.#e.getTrainerById(e.trainerId??"",e.locale);return await this.#t.dispatch(l.I_,t),t.trainer}async#h(e){const t=await this.#t.state.pipe((0,a.$)(),(0,o.E)("trainers",e.gameId)).toPromise();return t?.find((t=>t.id===e.trainerId))||null}async#r(e){if(!e.creatorMode)return null;try{const t=(await this.#e.getLatestLocalTrainerForGame(e.gameId)).trainer;if(t)return t}catch{}return null}#a(e){const t=this.#i.trainer?.getMetadata(u.vO);return t?.info.gameId===e.gameId?t.info:null}async#o(e){if(e.creatorMode)return null;const t=await this.#l(e.gameId);try{return await this.#u(e.gameId,e.locale,t)}catch{return await this.#d(e.gameId,t)}}async#u(e,t,s){const i=await this.#e.getMostCompatibleTrainerForGame(e,t,s);return await this.#t.dispatch(l.I_,i),i.trainer}async#d(e,t){const s=await this.#t.state.pipe((0,a.$)(),(0,o.E)("trainers",e)).toPromise();if(!s?.length)return null;const i=s.slice().sort(((e,t)=>t.releasedAt.localeCompare(e.releasedAt)));return i.find((e=>t.some((t=>e.supportedVersions.includes(t)))))||i[0]||null}async#l(e){const t=await this.#t.state.pipe((0,a.$)(),(0,o.E)("installedGameVersions")).toPromise();return t[e]?Array.from(new Set(t[e].map((e=>e.version)).filter((e=>null!==e)))):[]}};d=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[c.x,r.il,h.F2,u.jR])],d)},14240:(e,t,s)=>{s.d(t,{y:()=>a});var i=s(62614),n=s(56669),r=s(96555);class a{#p;constructor(e){this.metadata=e,this.events=[],this.#p=Date.now()}event(e,t){this.events.push({type:e,time:Date.now()-this.#p,data:t})}hasEvent(e){return-1!==this.events.findIndex((t=>t.type===e))}getUsedCheatTargets(){return Array.from(new Set(this.events.filter((e=>e.type===n.Gp.ValueChanged&&"trainer"!==e.data.source)).map((e=>e.data.name))))}async compile(e,t){const s=await(0,i.cR)();return{trainerId:this.metadata.info.id,correlationId:new r.o(this.metadata.app.platform,this.metadata.app.sku).toString(),gameVersion:this.metadata.gameVersion,type:e,report:t,events:e===n.M3.Failure?this.events:null,app:{version:s.appVersion},system:{version:s.osVersion,arch:s.osArch,locale:s.locale,freeMemory:s.freeMemory,antivirus:s.antivirusProducts}}}}},20489:(e,t,s)=>{s.d(t,{yo:()=>v,KC:()=>y,FX:()=>w});var i=s(15215),n=s("aurelia-framework"),r=s(10351),a=s(49442),o=s(70236),c=s(38777),h=s(77372),l=s(53737),u=s(97813),d=s(52399),p=s(24297),g=s(3972);let m=class{#g;constructor(e){this.#g=e}run(e){return this.#g.createRemoteThread(e.hostProcessId,e.call.module,e.call.export,this.#m(e),e.call.proxy??void 0)}#m(e){const t=Buffer.alloc(1552+32*e.variables.length);let s=0;return t.write(e.logPipe,0,512,"utf16le"),s+=512,t.write(e.messagePipe,s,256,"utf16le"),s+=256,t.writeUInt32LE(e.flags,s),s+=4,s+=252,t.write(e.module,s,512,"utf16le"),s+=512,t.writeUInt32LE(e.targetProcessId,s),s+=4,t.writeUInt32LE(e.gameVersion||0,s),s+=4,t.writeUInt32LE(e.variables.length,s),s+=4,e.variables.forEach((e=>{t.write(e,s,32,"ascii"),s+=32})),t}};var w,y;m=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[g.Mz])],m),function(e){e[e.AwaitingLaunch=0]="AwaitingLaunch",e[e.AcquiringBinary=1]="AcquiringBinary",e[e.EnsuringBinaryAccess=2]="EnsuringBinaryAccess",e[e.CheckingInternalBinaries=3]="CheckingInternalBinaries",e[e.FindingProcess=4]="FindingProcess",e[e.ValidatingProcess=5]="ValidatingProcess",e[e.CreatingTrainerHost=6]="CreatingTrainerHost",e[e.Injecting=7]="Injecting",e[e.InitializingIpc=8]="InitializingIpc",e[e.Executing=9]="Executing",e[e.Connecting=10]="Connecting",e[e.Activating=11]="Activating",e[e.Active=12]="Active",e[e.Ended=13]="Ended"}(w||(w={})),function(e){e[e.Success=0]="Success",e[e.Canceled=1]="Canceled",e[e.TimedOut=2]="TimedOut",e[e.ElevationDenied=3]="ElevationDenied",e[e.Incompatible=4]="Incompatible",e[e.GameAlreadyRunning=5]="GameAlreadyRunning",e[e.GameNotRunning=6]="GameNotRunning",e[e.Error=7]="Error"}(y||(y={}));class f extends Error{constructor(){super("Supplied trainer is incompatible with this app version."),Object.setPrototypeOf(this,f.prototype)}}let v=class{#w;#y;#f;#v;constructor(e,t,s,i){this.#w=e,this.#y=t,this.#f=s,this.#v=i}make(e){return new b(e,this.#w,this.#y,this.#f,this.#v)}};v=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[u.S,d.K,p.f,m])],v);class b{#b;#P;#L;#w;#y;#f;#v;#I;#E;#A;#T;#x;#C;#V;#F;constructor(e,t,s,i,n){this.log=[],this.timings=new Map,this.dllPath=null,this.process=null,this.finalState=null,this.launchResult=null,this.#P=new Map,this.#E=w.AwaitingLaunch,this.#A=new Map,this.#T=new c._M,this.#C=null,this.#V=null,this.#F=!1,this.#L=e,this.#w=t,this.#y=s,this.#f=i,this.#v=n,this.#b=new Map,this.values=this.#b}get launchDuration(){return this.#I?this.wasEverActive()?this.timings.get(w.Active)??0:Date.now()-this.#I:0}get activeDuration(){return this.wasEverActive()?(this.totalDuration??0)-this.launchDuration:0}get totalDuration(){return this.#I?this.hasEnded()?this.timings.get(w.Ended)??null:Date.now()-this.#I:null}isLoading(){return!this.isActive()&&!this.hasEnded()}isActive(){return this.#E===w.Active}wasEverActive(){return this.#F}hasEnded(){return this.#E===w.Ended}isEnding(){return!!this.finalState}addMetadata(e){this.#A.set(Object.getPrototypeOf(e).constructor,e)}getMetadata(e){return this.#A.get(e)}onStateChanged(e){return this.#T.subscribe("state",e)}onActivated(e){return this.#T.subscribeOnce("activated",e)}onEnded(e){return this.#T.subscribeOnce("ended",e)}onValueSet(e){return this.#T.subscribe("value",e)}onValueSetError(e){return this.#T.subscribe("value-error",e)}onLogMessage(e){return this.#T.subscribe("log",e)}getValue(e){return this.#b.get(e)}#M(e,t){if(function(e){return e.startsWith("wm_")}(e))this.#P.set(e,t);else{const s=this.#b.get(e);t!==s&&(this.#b.set(e,t),this.#T?.publish("value",{name:e,value:t,oldValue:s,source:0}))}}async getPlayerCoordinates(){return 1===await this.#S("wm_coord_get",-1)&&1===this.#P.get("wm_coord_get")&&{x:this.#P.get("wm_coord_x"),y:this.#P.get("wm_coord_y"),z:this.#P.get("wm_coord_z")}}async setPlayerCoordinates(e){return!!function(e){return null!==e&&"object"==typeof e&&"number"==typeof e.x&&isFinite(e.x)&&"number"==typeof e.y&&isFinite(e.y)&&"number"==typeof e.z&&isFinite(e.z)}(e)&&(!!(await Promise.all([this.#S("wm_coord_x",e.x??0),this.#S("wm_coord_y",e.y??0),this.#S("wm_coord_z",e.z??0)])).every((e=>0===e))&&(1===await this.#S("wm_coord_set",-1)&&1===this.#P.get("wm_coord_set")))}async setValue(e,t,s,i){t=function(e){return!0===e&&(e=1),!1===e&&(e=0),e}(t);const n=this.#b.get(e);if(!this.#b.has(e))throw new Error(`Attempted to set unknown trainer variable '${e}'`);if(this.#b.set(e,t),this.#T?.publish("value",{name:e,value:t,oldValue:n,source:s,cheatId:i}),!this.#x)return!0;try{const s=await this.#x.setValue(e,t);return(1!==s||n||1!==t||0!==this.#b.get(e))&&2!==s||(this.#T?.publish("value-error",{name:e,cheatId:i}),!1)}catch(e){if(this.isEnding())return!0;throw e}}async#S(e,t){if(this.#P.set(e,t),!this.#x)return!1;try{return await this.#x.setValue(e,t)}catch(e){if(this.isEnding())return!1;throw e}}async launch(e){try{return this.#I=Date.now(),await this.#D(e),!0}catch(e){return this.launchError=e,this.launchResult=function(e){return e instanceof c._T?y.Canceled:e instanceof l.MU?y.TimedOut:e instanceof r.Vn?y.ElevationDenied:e instanceof f?y.Incompatible:e instanceof l.cK?y.GameAlreadyRunning:e instanceof l.Ps?y.GameNotRunning:y.Error}(e),this.dispose(),!1}}dispose(){if(this.#E!==w.Ended){if(this.finalState=this.#E,this.#B(w.Ended),this.#T.publish("ended"),this.#T.dispose(),this.#x?.dispose(),this.#C){const e=this.#C;this.#C=null,setTimeout((()=>e.dispose()),1e4)}this.process=null}}async#D(e){if(e.canceled)throw new c._T;this.#B(w.AcquiringBinary);const t=await this.#k(e);this.dllPath=t,this.#B(w.EnsuringBinaryAccess);const s=await this.#$(t);this.#B(w.CheckingInternalBinaries),await this.#_(s),this.#B(w.FindingProcess),this.process=await this.#L.getProcess(e),this.#B(w.ValidatingProcess),this.#R(s,this.process),(0,o.Lt)(this.#L.flags??0,16)?(this.#B(w.CreatingTrainerHost),this.#C=await this.#j(s),this.#V=this.#C.processId??null):this.#V=this.process.id,this.#B(w.Injecting);const i=await this.#U(t);this.#B(w.InitializingIpc);try{this.#x=await this.#O()}catch(e){throw i.suspended&&this.#y.resumeProcess(this.process.id).catch(a.Y),e}this.#B(w.Executing);try{await new Promise((async(s,n)=>{const r=(0,c.Ix)((()=>{a?.dispose(),n(new l.MU("Trainer loading timed out."))}),this.#L.connectTimeout??0),a=e.onCancel((()=>{r.dispose(),n(new c._T("Trainer launch canceled."))}));if(this.#x.onConnect((()=>{r.dispose(),this.#E===w.Executing&&this.#B(w.Connecting),this.#B(w.Activating)})),this.#x.onReady((async()=>{if(a?.dispose(),i.suspended&&(0,o.Lt)(this.#L.flags??0,2))try{if(this.process?.id&&!await this.#y.resumeProcess(this.process.id))throw new Error("Failed to resume process after setup.")}catch(e){return void n(e)}this.launchResult=y.Success,this.#F=!0,this.#B(w.Active),this.#T.publish("activated"),s()})),this.#x.onLogMessage((e=>this.#T?.publish("log",e))),this.#x.onValue((e=>this.#M(e.name,e.value))),this.#x.onDisconnect((()=>{this.isLoading()?(r.dispose(),a?.dispose(),n(new Error("Trainer disconnected."))):this.dispose()})),this.#L.trainerArgs.variables.forEach((e=>this.#b.set(e,void 0))),!this.#V||!this.process?.id)return void n();let h=0;1===this.#L.alphaFeatures&&(h|=1),2===this.#L.alphaFeatures&&(h|=1,h|=2);try{await this.#v.run({hostProcessId:this.#V,targetProcessId:this.process.id,module:t,gameVersion:this.#L.trainerArgs.gameVersion,flags:h,variables:this.#L.trainerArgs.variables,messagePipe:this.#x.messagePipe,logPipe:this.#x.logPipe,call:i}),this.#E===w.Executing&&this.#B(w.Connecting)}catch(e){r.dispose(),a?.dispose(),n(e)}}))}catch(e){throw i.suspended&&this.#y.resumeProcess(this.process.id).catch(a.Y),e}}async#k(e){const t=await this.#L.getTrainerBinary(e);if(!1===t)throw new f;return t}async#$(e){return(await h.E3.load(e)).ntHeaders.fileHeader.machine===h.u5.AMD64?"x64":"ia32"}async#_(e){if((await this.#y.getMissingFiles(e)).length>0)throw new Error("Internal trainer DLLs are missing.")}#j(e){return this.#w.launch(e)}#R(e,t){if("x64"===e&&!t.x64||"ia32"===e&&t.x64)throw new Error("Trainer was not built for this process architecture.")}#O(){return this.#f.open({logPipe:this.#L.useExternalLogger?"\\\\.\\pipe\\WeMod_TrainerLib_Debug":void 0})}#U(e){return this.#y.inject({hostProcessId:this.#V??0,targetProcessId:this.process?.id??0,arch:this.process?.x64?"x64":"ia32",trainerDll:e,flags:this.#L.flags})}#B(e){if(e<=this.#E)throw new Error(`Cannot transition from state ${this.#E} to ${e}.`);this.#E=e,this.timings.set(e,Date.now()-this.#I),this.#T.publish("state",e)}get state(){return this.#E}}},24297:(e,t,s)=>{s.d(t,{$b:()=>u,f:()=>l});var i=s(15215),n=s(69278),r=s("aurelia-framework"),a=s(96610),o=s(3972),c=s(38777);const h=(0,a.getLogger)("trainer-ipc");let l=class{#g;constructor(e){this.#g=e}async open(e){const t=`\\\\.\\pipe\\WeMod\\Trainer_${Date.now()}_`,s=new c.Vd,[i,n,r]=await Promise.all([this.#H(`${t}In`,s),this.#H(`${t}Out`,s),e.logPipe?Promise.resolve(null):this.#H(`${t}Log`,s)]).catch((e=>{throw s.dispose(),e}));return await(0,c.Wn)(500),new d(i,n,r??e.logPipe)}#H(e,t){return new Promise(((s,i)=>{const r=(0,n.createServer)();t.push((0,c.nm)((()=>{a.dispose(),r.close()})));const a=new c.Vd([(0,c.$U)(r,"error",(e=>{a.dispose(),i(e)})),(0,c.$U)(r,"listening",(async()=>{a.dispose();try{await this.#g.grantFilePermissions(e,[o.Ro.everyone,o.Ro.appContainer],o.TB.Read|o.TB.Write),s(r)}catch(e){i(e)}}))]);r.listen(e)}))}};var u;l=(0,i.Cg)([(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[o.Mz])],l),function(e){e[e.Verbose=0]="Verbose",e[e.Info=1]="Info",e[e.Warning=2]="Warning",e[e.Error=3]="Error"}(u||(u={}));class d{#W;#z;#G;#K;#N;constructor(e,t,s){if(this.#W=new c._M,this.#z=new c.Vd,this.#K=new Map,this.#N=1,this.messagePipe=t.address(),this.#z.push((0,c.nm)((()=>{e.close(),t.close(),"string"!=typeof s&&s.close()}))),"string"==typeof s)this.logPipe=s;else{this.logPipe=s.address();const e=(0,c.$U)(s,"connection",(t=>{e.dispose(),this.#z.push((0,c.nm)((()=>t.destroy()))),this.#z.push((0,c.$U)(t,"data",this.#q.bind(this)))}));this.#z.push(e)}const i=(0,c.$U)(t,"connection",(t=>{i.dispose(),this.#G=t,this.#z.push((0,c.nm)((()=>{this.#G=null,t.destroy()}))),this.#z.push((0,c.$U)(t,"close",(()=>this.dispose()))),this.#z.push((0,c.$U)(t,"error",(e=>{"EPIPE"===e.code&&this.dispose()}))),t.write(e.address(),"utf16le",(e=>e&&this.dispose()))}));this.#z.push(i);const n=(0,c.$U)(e,"connection",(e=>{n.dispose(),this.#z.push((0,c.nm)((()=>e.destroy()))),this.#z.push((0,c.$U)(e,"data",this.#Y.bind(this))),this.#W.publish("connect")}));this.#z.push(i)}onConnect(e){return this.#W.subscribeOnce("connect",e)}onReady(e){return this.#W.subscribeOnce("ready",e)}onDisconnect(e){return this.#W.subscribeOnce("disconnect",e)}onLogMessage(e){return this.#W.subscribe("log",e)}onValue(e){return this.#W.subscribe("value",e)}dispose(){if(!this.#z)return;const e=this.#z;e?.dispose(),this.#K.clear(),this.#W.publish("disconnect"),this.#W?.dispose()}setValue(e,t){const s=this.#N++,i=new Promise(((e,t)=>this.#K.set(s,[e,t]))),n=Buffer.alloc(56);return n.writeUInt32LE(2,0),n.writeUInt32LE(s,8),n.write(e||"",16,32,"ascii"),n.writeDoubleLE(t,48),this.#G?.write(n,(e=>{if(e){const t=this.#K.get(s);t&&(this.#K.delete(s),t[1](e))}})),i}#q(e){if(e.length<16||!this.#z)return;let t=0;for(;t+4<e.length;){const s=e.readUInt32LE(t);t+=8;const i=e.readUInt32LE(t);t+=8;const n=e.toString("utf16le",t,t+i);t+=i;const r={time:Date.now(),level:s,message:n};this.#W.publish("log",r)}}#Y(e){if(!this.#z)return;let t=0;for(;t+8<=e.length;){const s=e.readUInt32LE(t);switch(t+=8,s){case 0:break;case 1:this.#W.publish("ready");break;case 2:{t+=8;let s=e.toString("ascii",t,t+32);s=s.substring(0,s.indexOf("\0")),t+=32;const i=e.readDoubleLE(t);t+=8,this.#W.publish("value",{name:s,value:i})}break;case 3:{const s=e.readUInt32LE(t);t+=8;const i=e.readUInt32LE(t);t+=8;const n=this.#K.get(s);n&&(this.#K.delete(s),n[0](i))}break;default:return void h.error(`Unknown message type ${s}.`)}}}}},52399:(e,t,s)=>{s.d(t,{K:()=>c});var i=s(35392),n=s(3972),r=s(49442),a=s(70236),o=s(29844);class c{#g;#X;constructor(e,t){this.#g=t,this.#X={x64:{trainerLib:`${e}\\TrainerLib_x64.dll`,trainerLibDecoy:`${e}\\stub\\TrainerLib_x64.dll`,ceLib:`${e}\\CELib_x64.dll`},ia32:{trainerLib:`${e}\\TrainerLib_x86.dll`,trainerLibDecoy:`${e}\\stub\\TrainerLib_x86.dll`,ceLib:`${e}\\CELib_x86.dll`}}}async getMissingFiles(e){const t=Object.values(this.#X[e]),s=await Promise.all(t.map((e=>i.promises.stat(e).then((e=>e.isFile())).catch((()=>!1)))));return t.filter(((e,t)=>!s[t]))}async inject(e){const t=this.#X[e.arch];await this.#Z([e.trainerDll,t.trainerLib,t.ceLib]);const s=(0,a.Lt)(e.flags,8)?await this.#J(e.trainerDll,t.trainerLibDecoy):null;if(s&&await this.#Z([s,t.trainerLibDecoy]),await this.#g.waitForInputIdle(e.targetProcessId,1e4),(0,a.Lt)(e.flags,1)&&await this.#g.suspendProcess(e.targetProcessId)<0)throw new Error("Failed to suspend process.");try{s&&await Promise.all([this.#g.injectDll(e.hostProcessId,t.trainerLibDecoy,0,5e3),this.#g.injectDll(e.hostProcessId,s,0,5e3)]),await this.#g.injectDll(e.hostProcessId,t.trainerLib,(0,a.Lt)(e.flags,4)?7340032:0,5e3)}catch(t){throw(0,a.Lt)(e.flags,1)&&await this.#g.resumeProcess(e.targetProcessId).catch(r.Y),t}return{module:t.trainerLib,export:"Run",proxy:(0,a.Lt)(e.flags,4)?"ExitProcess":null,suspended:(0,a.Lt)(e.flags,1)}}async resumeProcess(e){return await this.#g.resumeProcess(e)>=0}async#Z(e){try{await Promise.all(e.map((e=>this.#g.grantFilePermissions(e,[n.Ro.everyone,n.Ro.appContainer],n.TB.Read|n.TB.Execute))))}catch{}}async#J(e,t){const s=`${(0,o.pD)(e)}\\stub`,n=`${s}\\${(0,o.P8)(e)}`;return await i.promises.stat(n).then((e=>e.isFile())).catch((()=>!1))||(await i.promises.stat(s).then((e=>e.isDirectory())).catch((()=>!1))||await i.promises.mkdir(s),await i.promises.copyFile(t,n)),n}}},53737:(e,t,s)=>{s.d(t,{MU:()=>u,Ps:()=>p,ag:()=>g,cK:()=>d});var i,n=s(15215),r=s("aurelia-framework"),a=s(90231),o=s(41882),c=s(3972),h=s(49442),l=s(38777);!function(e){e[e.Found=0]="Found",e[e.Launched=1]="Launched",e[e.Error=2]="Error",e[e.Timeout=3]="Timeout",e[e.Canceled=4]="Canceled"}(i||(i={}));class u extends Error{constructor(e="The operation timed out."){super(e),Object.setPrototypeOf(this,u.prototype)}}class d extends Error{constructor(e="The game is already running and cannot be launched."){super(e),Object.setPrototypeOf(this,d.prototype)}}class p extends Error{constructor(e="Failed to find an existing game process."){super(e),Object.setPrototypeOf(this,p.prototype)}}let g=class{#g;#Q;constructor(e,t){this.#g=e,this.#Q=t}launchAndForget(e,t=1e4){return new Promise(((s,i)=>{this.#Q.launchApp(e.platform,e.sku,void 0,!1,new l.HL).then(s).catch(i),setTimeout(s,t)}))}async launch(e,t={},s={},i){const n=await this.#g.resolvePath(e.location),r=this.#ee(e,s,n),a=this.#te(s.reducer),o=s.minimumAge??8e3,c=s.timeout??45e3,h=await this.#se(r,a,s.cliArgs??null,o,!0);if(h){if(t&&t.required)throw new d;return{process:h}}if(!1===t)throw new p;return await this.#ie(this.#ne(e,n,t),(()=>this.#se(r,a,s.cliArgs??null,o,!1)),c,i)}#ne(e,t,s){return async i=>{let n;!1!==s&&(s.bypass&&e.platform!==o.u&&this.#g.runLauncherBypass({directory:t,...s.bypass}),s.proxy&&e.platform!==o.u&&(n=await this.#re(e,t,s)));try{return await this.#Q.launchApp(e.platform,e.sku,s?s.cliArgs:void 0,!0,i),n}catch(e){throw n?.dispose(),e}}}async#re(e,t,s){if(e.platform!==o.u&&s.proxy){const e=`${t}\\${s.proxy.launcher}`;if(await this.#g.mountLauncherProxy({...s.proxy,launcher:e}))return(0,l.nm)((()=>this.#g.unmountLauncherProxy(e).catch(h.Y)))}return l.lE}#ee(e,t,s){if(t.image){if(t.image.includes("*")){const e=new RegExp("\\\\"+t.image.split("*").map((e=>e.replace(/[\^$\\.*+?()[\]{}|]/g,"\\$&"))).join("[^\\\\]*")+"$","i");return t=>e.test(t.imagePath)}{const e=`\\${t.image}`.toLocaleLowerCase();return t=>t.imagePath.toLocaleLowerCase().endsWith(e)}}if(e.platform===o.u){const e=s.toLocaleLowerCase();return t=>t.imagePath.toLocaleLowerCase()===e}if(e.alternateLocations?.length){const t=e.alternateLocations.map((e=>`${e}\\`.toLocaleLowerCase()));return t.push(`${s}\\`.toLocaleLowerCase()),e=>{const s=e.imagePath.toLocaleLowerCase();return t.some((e=>s.startsWith(e)))}}{const e=`${s}\\`.toLocaleLowerCase();return t=>t.imagePath.toLocaleLowerCase().startsWith(e)}}#te(e){switch(e){case 1:return(e,t)=>e?t.elapsedTime>e.elapsedTime?t:e:t;case 2:return(e,t)=>e?t.elapsedTime<e.elapsedTime?t:e:t;default:return(e,t)=>e?t.workingSetSize>e.workingSetSize?t:e:t}}async#se(e,t,s,i,n){const r=(await this.#g.getRunningProcesses(s)).filter(e).reduce(t,null);return null===r?null:r.elapsedTime>=i?r:n?(await(0,l.Wn)(i-r.elapsedTime),await this.#se(e,t,s,i,!0)):null}async#ie(e,t,s,i){return await new Promise((async(n,r)=>{let a;const o=new l.Vd([(0,l.Ix)((()=>{a?.dispose(),o.dispose(),r(new u("App launch timed out."))}),s),(0,l.SO)((async()=>{const e=await t();e&&!o.disposed&&(o.dispose(),n({process:e,cleanup:a}))}),2500),(0,l.nm)((()=>c.cancel()))]);i&&o.push(i.onCancel((()=>{a?.dispose(),o.dispose(),r(new l._T("App launch canceled."))})));const c=new l.HL;try{a=await e(c)}catch(e){o.disposed||(o.dispose(),r(e))}}))}};g=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[c.Mz,a.D])],g)},83802:(e,t,s)=>{s.d(t,{ZS:()=>m,vO:()=>g,jR:()=>w});var i=s(15215),n=s("aurelia-framework"),r=s(20770),a=s(45660),o=s(59239),c=s(38777),h=s(53737),l=s(20489),u=s(80525),d=s(70893);let p=class{#ae;#oe;constructor(e,t){this.#ae=e,this.#oe=t}async fetch(e,t,s){switch(e){case"trainerlib":return await this.#ce(t,s);case"trainerlib-local":return await this.#he(t);default:return!1}}#ce(e,t){return this.#ae.fetchBinary({trainerId:e.trainerId,url:e.binaryUrl,hash:e.binaryHash},t)}async#he(e){const t=await this.#oe.getTrainerBinaryPath(e.repoPath);if(!t)throw new Error("Local binary not found.");return t}};p=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[d.L,u.W])],p);class g{constructor(e,t,s=null,i){this.info=e,this.app=t,this.gameVersion=s,this.trigger=i}}class m extends Error{constructor(e){super(e),Object.setPrototypeOf(this,m.prototype)}}let w=class{#le;#ue;#de;#t;#W;#pe;constructor(e,t,s,i){this.trainer=null,this.#W=new c._M,this.#le=e,this.#ue=t,this.#de=s,this.#t=i}async endTrainer(){if(this.trainer){const e=new Promise((e=>this.trainer?.onEnded((()=>e()))));this.#pe?this.#pe.cancel():this.trainer.dispose(),await e}}onNewTrainer(e){return this.#W.subscribe("trainer",e)}onTrainerActivated(e){return this.#W.subscribe("activated",e)}onTrainerEnded(e){return this.#W.subscribe("ended",e)}async launch(e){if(this.trainer)throw new m("Attempted to start a trainer when one is already running.");const t=e.info,s=t.blueprint,i=await this.#ge();let n=0;i.trainerLibAlpha&&(n=1,i.trainerLibAlphaSimulate&&(n=2));const r=this.#le.make({getProcess:async t=>{const i=await this.#ue.launch(e.app,s.config?.launch,s.config?.target,t),n=()=>i.cleanup?.dispose();return r.isEnding()?n():(r.onActivated(n),r.onEnded(n)),i.process},getTrainerBinary:e=>this.#de.fetch(t.loader,t.loaderArgs,e),connectTimeout:15e3,trainerArgs:{gameVersion:e.gameVersion,variables:Array.from(new Set(s.cheats.map((e=>e.target))))},useExternalLogger:i.trainerLibExternalConsole??!1,alphaFeatures:n,flags:t.blueprint.config?.activate?.flags});return r.addMetadata(e),r.onActivated((()=>{this.#pe=null,this.#W.publish("activated",r)})),r.onEnded((()=>{this.trainer=null,this.#W.publish("ended",r)})),this.trainer=r,this.#pe=new c.HL,this.#W.publish("trainer",r),await r.launch(this.#pe),r}#ge(){return this.#t.state.pipe((0,a.$)(),(0,o.E)("settings")).toPromise()}dispose(){this.trainer?.dispose(),this.trainer=null,this.#W?.dispose()}};w=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[l.yo,h.ag,p,r.il])],w)},97813:(e,t,s)=>{s.d(t,{S:()=>n});var i=s(35317);class n{#X;constructor(e){this.#X={x64:`${e}\\TrainerHost_x64.exe`,ia32:`${e}\\TrainerHost_x86.exe`}}async launch(e){return new r((0,i.spawn)(this.#X[e],{detached:!1}))}}class r{#me;constructor(e){this.#me=e}get processId(){return this.#me?.pid}dispose(){if(this.#me){try{this.#me.kill()}catch{}this.#me=null}}}},"cheats/trainer/index":(e,t,s)=>{s.r(t),s.d(t,{configure:()=>a});var i=s(3972),n=s(97813),r=s(52399);function a(e,t){e.container.registerSingleton(r.K,(function(){return new r.K(t.binaryDir,e.container.get(i.Mz))})),e.container.registerSingleton(n.S,(function(){return new n.S(t.binaryDir)}))}}}]);
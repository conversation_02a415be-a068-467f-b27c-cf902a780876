"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4898],{"app/resources/elements/settings-button":(e,i,t)=>{t.r(i),t.d(i,{SettingsButton:()=>l});var a=t(15215),s=t("aurelia-dialog"),r=t("aurelia-event-aggregator"),o=t("aurelia-framework"),n=t(18776);let l=class{#e;#i;#t;constructor(e,i,t){this.#e=e,this.#i=i,this.#t=t}openSettings(){this.#i.closeAll(),this.#e.publish("close-sidebar-user-menu"),this.#t.navigateToRoute("settings")}};l=(0,a.Cg)([(0,o.autoinject)(),(0,a.Sn)("design:paramtypes",[r.EventAggregator,s.DialogService,n.Ix])],l)},"app/resources/elements/settings-button.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template click.delegate="openSettings()" tabindex="0"> <require from="./settings-button.scss"></require> <a class="button icon"> <i class="settings">settings</i> <span class="label">${\'settings_dialog.settings\' | i18n}</span> </a> </template> '},"app/resources/elements/settings-button.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>p});var a=t(31601),s=t.n(a),r=t(76314),o=t.n(r),n=t(4417),l=t.n(n),d=new URL(t(83959),t.b),m=o()(s()),g=l()(d);m.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,settings-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}settings-button i{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:flex;justify-content:center;align-items:center;color:#fff;width:20px;height:20px;transition:opacity .15s,rotate .25s}`,""]);const p=m},"app/resources/elements/sidebar-game-lists":(e,i,t)=>{t.r(i),t.d(i,{SidebarGameLists:()=>c});var a=t(15215),s=t(7530),r=t("aurelia-framework"),o=t(18776),n=t(20770),l=t(96555),d=t("shared/i18n/resources/value-converters"),m=t(54995),g=t(43050),p=t(48881);const b=[{labelKey:"sidebar_game_lists.favorites",listType:"favorites",feedFilter:{},feedConfig:g.pK},{labelKey:"sidebar_game_lists.supported",listType:"supported",feedFilter:{},feedConfig:g.qE},{labelKey:"sidebar_game_lists.other",listType:"other",feedFilter:{},feedConfig:g.D6}];let c=class{#a;#s;constructor(e,i,t,a){this.i18nNumber=e,this.router=i,this.isSidebarCollapsed=!1,this.filteredGameFeeds=[],this.maxListItems={favorites:10,supported:10,other:5},this.#a=t,this.#s=a}attached(){this.setUpGameFeeds()}get currentTitleId(){return this.router.currentInstruction?.params?.titleId??""}detached(){this.favoriteGamesFeed?.dispose(),this.supportedGamesFeed?.dispose(),this.otherGamesFeed?.dispose()}setUpGameFeeds(){this.favoriteGamesFeed=this.#a.getFilteredFeed(g.pK,{}),this.supportedGamesFeed=this.#a.getFilteredFeed(g.qE,{}),this.otherGamesFeed=this.#a.getFilteredFeed(g.D6,{}),b.forEach((e=>{switch(e.listType){case"favorites":this.filteredGameFeeds.push({...e,feed:this.favoriteGamesFeed});break;case"supported":this.filteredGameFeeds.push({...e,feed:this.supportedGamesFeed});break;case"other":this.filteredGameFeeds.push({...e,feed:this.otherGamesFeed})}}))}getI18nNumber(e,i){return this.i18nNumber.toView(e,{minimumSignificantDigits:1,maximumFractionDigits:0,signDisplay:i?"always":"never"})}get gameLists(){return this.filteredGameFeeds.map((e=>({listType:e.listType,labelKey:e.labelKey,games:e.feed.items.map((e=>{const i=this.titles[e.titleId]??this.correlatedUnavailableTitles[e.titleId],t=[];let a=e.gameId;if(i){const e=e=>!!(e?.games??[]).length;e(i)?(a=a??i.games[0]?.id??null,t.push(...i.games.reduce(((e,i)=>[...e,...i.correlationIds]),[]))):(a=a??i.gameIds[0]??null,t.push(...i.gameIds.reduce(((e,i)=>[...e,...this.games[i].correlationIds]),[])))}else a=null;const s=t.map(l.o.parse).find((e=>"steam"===e.platform))?.sku??"";return{...e,gameId:a,steamAppId:s}})).filter((i=>null!==i.gameId&&("favorites"===e.listType||!this.favoriteGamesFeed.items.some((e=>e.titleId===i.titleId)))))})))}handleListCollapse(e){this.#s.dispatch(p.O1,e)}handleViewMoreGames(e){switch(e){case"favorites":this.router.navigateToRoute("collection",{slug:"my-games",selectedFeed:"favorites"});break;case"supported":case"other":this.router.navigateToRoute("collection",{slug:"my-games"})}}};(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],c.prototype,"isSidebarCollapsed",void 0),(0,a.Cg)([(0,s.Kj)("router.currentInstruction.params"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],c.prototype,"currentTitleId",null),(0,a.Cg)([(0,s.Kj)("favoriteGamesFeed.items","supportedGamesFeed.items","otherGamesFeed.items","correlatedUnavailableTitles"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],c.prototype,"gameLists",null),c=(0,a.Cg)([(0,r.autoinject)(),(0,m.m6)({selectors:{titles:(0,m.$t)((e=>e.catalog.titles)),games:(0,m.$t)((e=>e.catalog.games)),correlatedUnavailableTitles:(0,m.$t)((e=>e.correlatedUnavailableTitles)),sidebarCollapsedLists:(0,m.$t)((e=>e.sidebarCollapsedLists))}}),(0,a.Sn)("design:paramtypes",[d.I18nNumberValueConverter,o.Ix,g.Y2,n.il])],c)},"app/resources/elements/sidebar-game-lists.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template> <require from="./sidebar-game-lists.scss"></require> <require from="./sidebar-game-row"></require> <div class="sidebar-game-lists ${isSidebarCollapsed ? \'sidebar-collapsed\' : \'\'}" repeat.for="gameList of gameLists" if.bind="gameList.games.length"> <div class="sidebar-game-lists-header" click.delegate="handleListCollapse(gameList.listType)" tabindex="0"> <div class="sidebar-game-lists-header-icon ${gameList.listType} ${sidebarCollapsedLists[gameList.listType] ? \'is-collapsed\' : \'\'}"></div> <div class="sidebar-game-lists-header-text">${gameList.labelKey | i18n} ${gameList.games.length}</div> </div> <div class="sidebar-game-lists-list ${sidebarCollapsedLists[gameList.listType] ? \'is-collapsed\' : \'\'}"> <sidebar-game-row repeat.for="title of gameList.games.slice(0, maxListItems[gameList.listType])" title.bind="title" is-sidebar-collapsed.bind="isSidebarCollapsed" class.bind="currentTitleId === title.titleId ? \'current-game\' : \'\'"></sidebar-game-row> <div class="view-more-games-container" if.bind="gameList.games.length > maxListItems[gameList.listType]"> <button click.delegate="handleViewMoreGames(gameList.listType)" class="view-more-games" tabindex="0"> <span if.bind="isSidebarCollapsed">${getI18nNumber(gameList.games.length - maxListItems[gameList.listType], true)}</span> <span else> ${\'sidebar_game_lists.view_all_$number_games\' | i18n: { number: getI18nNumber(gameList.games.length)}}</span> </button> </div> </div> <hr if.bind="!$last" class="sidebar-divider"> </div> </template> '},"app/resources/elements/sidebar-game-lists.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>k});var a=t(31601),s=t.n(a),r=t(76314),o=t.n(r),n=t(4417),l=t.n(n),d=new URL(t(83959),t.b),m=new URL(t(9976),t.b),g=new URL(t(23369),t.b),p=new URL(t(50105),t.b),b=new URL(t(69019),t.b),c=new URL(t(11519),t.b),u=o()(s()),h=l()(d),f=l()(m),w=l()(g),y=l()(p),x=l()(b),v=l()(c);u.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${h}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}sidebar-game-lists .sidebar-game-lists{display:flex;flex-direction:column;justify-content:center;padding:0px 8px 0px 8px;white-space:nowrap}sidebar-game-lists .sidebar-game-lists-list{display:flex;flex-direction:column;gap:2px;padding-top:8px;transition:.3s all ease-in-out;max-height:100vh;opacity:1}sidebar-game-lists .sidebar-game-lists-list.is-collapsed{position:absolute;visibility:hidden;max-height:0;opacity:0}sidebar-game-lists .sidebar-game-lists-list.is-collapsed sidebar-game-row{display:none}sidebar-game-lists .sidebar-game-lists-list.is-collapsed .view-more-games-container{display:none}sidebar-game-lists .sidebar-game-lists-list sidebar-game-row.current-game{background:rgba(255,255,255,.15);border-radius:8px}sidebar-game-lists .sidebar-game-lists-header{display:flex;flex-direction:row;align-items:center;gap:10px;padding:8px 8px 8px 12px;font-weight:700;font-size:11px;letter-spacing:.5px;text-transform:uppercase;height:32px;color:rgba(255,255,255,.6)}sidebar-game-lists .sidebar-game-lists-header:hover{background:rgba(255,255,255,.15);border-radius:8px}sidebar-game-lists .sidebar-game-lists-header:hover,sidebar-game-lists .sidebar-game-lists-header:hover *{cursor:pointer}sidebar-game-lists .sidebar-game-lists-header:hover .sidebar-game-lists-header-icon{background:url(${f}) no-repeat center}sidebar-game-lists .sidebar-game-lists-header:hover .sidebar-game-lists-header-icon.is-collapsed{background:url(${w}) no-repeat center}sidebar-game-lists .sidebar-game-lists-header-icon{display:flex;flex:0 0 auto;align-items:center;justify-content:center;width:20px;height:20px;color:#fff;opacity:.6}sidebar-game-lists .sidebar-game-lists-header-icon.favorites{background:url(${y}) no-repeat center}sidebar-game-lists .sidebar-game-lists-header-icon.supported{background:url(${x}) no-repeat center}sidebar-game-lists .sidebar-game-lists-header-icon.other{background:url(${v}) no-repeat center}sidebar-game-lists .sidebar-game-lists-header-text{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;transition:opacity .5s ease-in-out,transform .2s ease-in-out;text-transform:uppercase}sidebar-game-lists .sidebar-game-lists .view-more-games-container{display:flex;align-items:center;justify-content:start;padding:4px 8px 0px 12px}sidebar-game-lists .sidebar-game-lists .view-more-games-container .view-more-games{appearance:none;background:none;border:none;display:flex;justify-content:center;align-items:center;height:24px;font-weight:500;font-size:14px;line-height:24px;color:rgba(255,255,255,.6);padding:0}sidebar-game-lists .sidebar-game-lists .view-more-games-container .view-more-games:hover{color:rgba(255,255,255,.8)}sidebar-game-lists .sidebar-game-lists .view-more-games-container .view-more-games:hover,sidebar-game-lists .sidebar-game-lists .view-more-games-container .view-more-games:hover *{cursor:pointer}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed{transition:.2s all ease-in-out;padding:0px 0px 0px 8px}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .sidebar-game-lists-list{gap:8px}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .sidebar-game-lists-list sidebar-game-row.current-game{margin:0 auto;border:4px solid rgba(255,255,255,.15);box-sizing:content-box;border-radius:16px}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .sidebar-game-lists-header{justify-content:center;padding:8px;width:50%;margin:auto}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .sidebar-game-lists-header-icon{height:24px;width:24px;color:var(--theme--highlight);opacity:1}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .sidebar-game-lists-header-text{visibility:hidden;position:absolute;opacity:0;transform:translateX(-10px);min-width:72px}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .view-more-games-container{height:44px;max-width:44px;margin:0 auto;padding:0}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .view-more-games-container .view-more-games{background:rgba(255,255,255,.1);min-width:44px;height:44px;border-radius:12px;margin:0 auto}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .view-more-games-container .view-more-games:hover{background:rgba(255,255,255,.15);box-shadow:0 0 0 4px rgba(255,255,255,.05)}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .view-more-games-container{height:44px}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .view-more-games-container .view-more-games{background:rgba(255,255,255,.1);min-width:44px;height:44px;border-radius:12px;margin:0 auto}sidebar-game-lists .sidebar-game-lists.sidebar-collapsed .view-more-games-container .view-more-games:hover{background:rgba(255,255,255,.15);box-shadow:0 0 0 4px rgba(255,255,255,.05)}`,""]);const k=u},"app/resources/elements/sidebar-game-more-menu":(e,i,t)=>{t.r(i),t.d(i,{SidebarGameMoreMenu:()=>p});var a=t(15215),s=t(7530),r=t("aurelia-framework"),o=t(18776),n=t(20770),l=t(50654),d=t(92465),m=t(54995),g=t(48881);let p=class{#t;#s;#r;#o;#n;constructor(e,i,t){this.isSidebarCollapsed=!1,this.favorite=!1,this.busy=!1,this.#t=e,this.#s=i,this.followedGames=t}attached(){this.#r=new IntersectionObserver((()=>this.#l())),this.#r.observe(this.moreMenuEl),this.#o=new ResizeObserver((()=>this.#l())),this.#o.observe(document.documentElement),this.#l(),this.#n=(0,d.Ix)((()=>{this.isMoreMenuOpen=!1}),3e3)}detached(){this.#r?.disconnect(),this.#o?.disconnect(),this.#n?.dispose(),this.isMoreMenuOpen=!1}handleMouseInteraction(){this.#n?.dispose(),this.#n=(0,d.Ix)((()=>{this.isMoreMenuOpen=!1}),3e3)}get following(){return!!this.title.gameId&&!!this.followedGames.followedGames.find((e=>e.gameId===this.title.gameId))}openTitlePage(){this.title&&this.#t.navigateToRoute("title",{titleId:this.title.titleId})}#l(){if(!this.moreMenuEl||!this.gameRowEl)return;let e=this.moreMenuEl.getBoundingClientRect();const i=this.gameRowEl.getBoundingClientRect();this.moreMenuEl.style.bottom="",this.moreMenuEl.style.top=`${i.top}px`,e=this.moreMenuEl.getBoundingClientRect(),e.bottom>=window.innerHeight&&(this.moreMenuEl.style.bottom="8px",this.moreMenuEl.style.top="")}favoritesChanged(){this.favorites&&(this.favorite=this.favorites.hasOwnProperty(this.title.titleId))}get favoriteCopyKey(){return this.favorite?"sidebar_game_menu.remove_from_favorites":"sidebar_game_menu.add_to_favorites"}handleFavoriteClick(){this.#s.dispatch(g.W6,this.title.titleId)}get followCopyKey(){return this.following?"sidebar_game_menu.unfollow":"sidebar_game_menu.follow"}async handleFollowClick(){if(!this.busy){this.busy=!0;try{this.title.gameId&&(this.following?await this.followedGames.unfollowGames([this.title.gameId]):await this.followedGames.followGames([this.title.gameId],2))}finally{this.busy=!1}}}handlePlayGame(){this.#t.navigateToRoute("title",{titleId:this.title.titleId,trainerId:"",autoLaunch:!0})}handleLaunchWithoutMods(){this.#t.navigateToRoute("title",{titleId:this.title.titleId,trainerId:"",autoLaunchhWithoutMods:!0})}};(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],p.prototype,"title",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],p.prototype,"isSidebarCollapsed",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",HTMLElement)],p.prototype,"gameRowEl",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],p.prototype,"isMoreMenuOpen",void 0),(0,a.Cg)([s.sH,(0,a.Sn)("design:type",HTMLElement)],p.prototype,"moreMenuEl",void 0),(0,a.Cg)([s.sH,(0,a.Sn)("design:type",Object)],p.prototype,"favorites",void 0),(0,a.Cg)([(0,s.Kj)("title.gameId","followedGames.followedGames"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],p.prototype,"following",null),(0,a.Cg)([(0,s.Kj)("favorite"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],p.prototype,"favoriteCopyKey",null),(0,a.Cg)([(0,s.Kj)("following"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],p.prototype,"followCopyKey",null),p=(0,a.Cg)([(0,r.autoinject)(),(0,m.m6)({selectors:{favorites:(0,m.$t)((e=>e.favoriteTitles))}}),(0,a.Sn)("design:paramtypes",[o.Ix,n.il,l.O])],p)},"app/resources/elements/sidebar-game-more-menu.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template> <require from="./sidebar-game-more-menu.scss"></require> <require from="../../../cheats/resources/custom-attributes/steam-hero-bg"></require> <require from="../../../cheats/resources/custom-attributes/steam-client-icon-bg"></require> <div mouseover.delegate="handleMouseInteraction()" ref="moreMenuEl" class="sidebar-game-more-menu ${isSidebarCollapsed ? \'sidebar-collapsed\' : \'\'}"> <div class="game-header"> <div class="bg" steam-hero-bg="steam-id.bind: title.steamAppId;"></div> <div class="game-info"> <div class="game-thumbnail" steam-client-icon-bg="steam-id.bind: title.steamAppId;"></div> <div class="game-title">${title.titleName}</div> </div> <button click.delegate="handlePlayGame()" class="play-button">${\'sidebar_game_menu.play\' | i18n}</button> </div> <hr> <div class="menu-actions"> <button click.delegate="handleLaunchWithoutMods()"> <i class="play-icon"></i> <span>${\'sidebar_game_menu.launch_without_mods\' | i18n}</span> </button> <button click.delegate="handleFavoriteClick()"> <i class="favorite-icon ${favorite ? \'is-favorite\' : \'\'}"></i> <span>${favoriteCopyKey | i18n}</span> </button> <button click.delegate="handleFollowClick()"> <i class="follow-icon ${following ? \'is-following\' : \'\'}"></i> <span>${followCopyKey | i18n}</span> </button> </div> </div> </template> '},"app/resources/elements/sidebar-game-more-menu.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>p});var a=t(31601),s=t.n(a),r=t(76314),o=t.n(r),n=t(4417),l=t.n(n),d=new URL(t(83959),t.b),m=o()(s()),g=l()(d);m.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i,sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.play-icon,sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.favorite-icon,sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.follow-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}sidebar-game-more-menu .sidebar-game-more-menu{display:flex;flex-direction:column;justify-content:flex-start;padding:8px;position:relative;width:280px;background:rgba(128,128,128,.1);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:16px;position:absolute;z-index:1;left:228px}sidebar-game-more-menu .sidebar-game-more-menu.sidebar-collapsed{left:80px}sidebar-game-more-menu .sidebar-game-more-menu .game-header{position:relative;display:flex;background-color:var(--theme--background);border-radius:8px;overflow:hidden}sidebar-game-more-menu .sidebar-game-more-menu .game-header .bg{height:80px;width:100%;background-size:cover;background-repeat:no-repeat;-webkit-mask-image:linear-gradient(0deg, transparent 0%, #000 100%)}sidebar-game-more-menu .sidebar-game-more-menu .game-header.is-fallback{background:radial-gradient(177.18% 151.73% at 100% 0%, #1fbaf8 0%, #2a9bf9 9.99%, #3874fb 21.4%, #4743fb 38.79%, #2a1257 68.95%, #1c1625 79.41%, #0f1014 100%) !important}sidebar-game-more-menu .sidebar-game-more-menu .game-header .play-button{position:absolute;appearance:none;border:none;right:8px;bottom:8px;z-index:1;display:flex;flex-direction:row;justify-content:center;align-items:center;padding:6px 14px;gap:2px;height:36px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);border-radius:84px;font-weight:900;font-size:20px;line-height:24px;letter-spacing:-0.8px;color:#fff}sidebar-game-more-menu .sidebar-game-more-menu .game-header .game-info{display:flex;position:absolute;gap:8px;bottom:8px;left:8px}sidebar-game-more-menu .sidebar-game-more-menu .game-header .game-info .game-title{color:#fff;font-size:14px;font-weight:800;line-height:100%;letter-spacing:-0.5px;padding:3px 0;max-width:100px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}sidebar-game-more-menu .sidebar-game-more-menu .game-header .game-info .game-thumbnail{display:flex;height:20px;width:20px;border-radius:4px;background-size:contain}sidebar-game-more-menu .sidebar-game-more-menu .game-header .game-info .game-thumbnail.is-fallback{display:flex !important;background:linear-gradient(180deg, #4743fc 0%, #dbe2e3 100%)}sidebar-game-more-menu .sidebar-game-more-menu hr{margin:12px -8px;padding:0;border:none;border-top:1px solid rgba(255,255,255,.15)}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions{display:flex;flex-direction:column;gap:1px}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions button{display:flex;align-items:center;gap:8px;padding:8px;background:rgba(0,0,0,0);border:none;transition:background-color .15s;border-radius:8px;text-align:left}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions button:hover{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;width:20px;height:20px;display:inline-flex;align-items:center;justify-content:center;color:var(--menu__item__icon--color, rgba(255, 255, 255, 0.6));transition:color .15s}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.play-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.play-icon:before{font-family:inherit;content:"play_arrow"}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.favorite-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.favorite-icon:before{font-family:inherit;content:"kid_star"}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.favorite-icon.is-favorite{font-variation-settings:"FILL" 1,"wght" 400 !important}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.follow-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.follow-icon:before{font-family:inherit;content:"notifications"}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions i.follow-icon.is-following{font-variation-settings:"FILL" 1,"wght" 400 !important}sidebar-game-more-menu .sidebar-game-more-menu .menu-actions span{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.6));padding:2px 0;transition:color .15s;font-size:14px;line-height:20px;font-weight:700;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.8))}`,""]);const p=m},"app/resources/elements/sidebar-game-row":(e,i,t)=>{t.r(i),t.d(i,{SidebarGameRow:()=>d});var a=t(15215),s=t(7530),r=t("aurelia-framework"),o=t(18776),n=t(54995),l=t(14046);let d=class{#t;constructor(e){this.isSidebarCollapsed=!1,this.isMoreMenuOpen=!1,this.#t=e}get isTitleNew(){const e=this.installedGameVersions[this.title.gameId??""]?.[0]?.createdAt??null;return!!(e&&(0,l.c_)(Date.now(),new Date(1e3*e))<=3)}openTitlePage(){this.title&&this.#t.navigateToRoute("title",{titleId:this.title.titleId})}handleMenuClick(e){e.stopPropagation(),this.isMoreMenuOpen=!this.isMoreMenuOpen}};(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],d.prototype,"title",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],d.prototype,"isSidebarCollapsed",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],d.prototype,"isMoreMenuOpen",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",HTMLElement)],d.prototype,"gameRowEl",void 0),(0,a.Cg)([(0,s.Kj)("installedGameVersions"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],d.prototype,"isTitleNew",null),d=(0,a.Cg)([(0,r.autoinject)(),(0,n.m6)({selectors:{installedGameVersions:(0,n.$t)((e=>e.installedGameVersions))}}),(0,a.Sn)("design:paramtypes",[o.Ix])],d)},"app/resources/elements/sidebar-game-row.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template> <require from="shared/resources/custom-attributes/close-if-click-outside"></require> <require from="cheats/resources/custom-attributes/steam-client-icon-bg"></require> <require from="resources/elements/new-badge"></require> <require from="./sidebar-game-row.scss"></require> <require from="./sidebar-game-more-menu"></require> <div if.bind="title" click.delegate="openTitlePage()" class="sidebar-game-row ${isSidebarCollapsed ? \'sidebar-collapsed\' : \'\'}" ref="gameRowEl" tabindex="0"> <div class="sidebar-game-row-image" title="${isSidebarCollapsed ? title.titleName : \'\'}" steam-client-icon-bg="steam-id.bind: title.steamAppId;"> <div class="sidebar-game-row-image-new-dot" if.bind="isTitleNew"></div> </div> <span title.bind="title.titleName.length > 13 ? title.titleName : \'\'" class="sidebar-game-row-title"> ${title.titleName} </span> <new-badge if.bind="isTitleNew"></new-badge> <div click.delegate="handleMenuClick($event)" class="sidebar-game-row-more"><i class="more-icon"></i></div> </div> <sidebar-game-more-menu title.bind="title" is-sidebar-collapsed.bind="isSidebarCollapsed" if.bind="isMoreMenuOpen" is-more-menu-open.two-way="isMoreMenuOpen" close-if-click-outside.two-way="isMoreMenuOpen" game-row-el.bind="gameRowEl"></sidebar-game-more-menu> </template> '},"app/resources/elements/sidebar-game-row.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>p});var a=t(31601),s=t.n(a),r=t(76314),o=t.n(r),n=t(4417),l=t.n(n),d=new URL(t(83959),t.b),m=o()(s()),g=l()(d);m.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,sidebar-game-row .sidebar-game-row-more .more-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}sidebar-game-row{--sidebar-game-row-title--color: rgba(#fff, 0.8)}sidebar-game-row .sidebar-game-row{display:flex;flex-direction:row;align-items:center;width:100%;gap:10px;padding:4px 8px 4px 12px;font-weight:500;font-size:14px;line-height:24px;color:rgba(255,255,255,.8)}sidebar-game-row .sidebar-game-row:hover{background:rgba(255,255,255,.15);border-radius:8px}sidebar-game-row .sidebar-game-row:hover,sidebar-game-row .sidebar-game-row:hover *{cursor:pointer}sidebar-game-row .sidebar-game-row:hover .sidebar-game-row-more{display:flex}sidebar-game-row .sidebar-game-row:hover new-badge{display:none}sidebar-game-row .sidebar-game-row-image{display:flex;min-width:20px;width:20px;height:20px;border-radius:4px;transition:.2s all ease-in-out;position:relative;background-size:contain}sidebar-game-row .sidebar-game-row-image.is-fallback{display:flex !important;background:linear-gradient(180deg, #4743fc 0%, #dbe2e3 100%)}sidebar-game-row .sidebar-game-row-image-new-dot{position:absolute;display:flex;right:-4px;top:-4px;width:10px;height:10px;background-color:#fff;border-radius:100%;border:2px solid rgba(var(--theme--background--rgb), 0.9)}sidebar-game-row .sidebar-game-row-title{color:var(--sidebar-game-row-title--color);transition:opacity .5s ease-in-out,transform .2s ease-in-out;max-width:220px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}sidebar-game-row .sidebar-game-row-more{margin-left:auto;display:none;padding:4px;width:24px;height:24px}sidebar-game-row .sidebar-game-row-more:hover{background:rgba(255,255,255,.1);border-radius:6px}sidebar-game-row .sidebar-game-row-more .more-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:flex;color:rgba(255,255,255,.6);font-size:16px}sidebar-game-row .sidebar-game-row-more .more-icon:before{font-family:inherit;content:"more_vert"}sidebar-game-row .sidebar-game-row.sidebar-collapsed{height:44px;max-width:44px;margin:0 auto;padding:0}sidebar-game-row .sidebar-game-row.sidebar-collapsed:hover{background:none}sidebar-game-row .sidebar-game-row.sidebar-collapsed:hover .sidebar-game-row-more{display:none}sidebar-game-row .sidebar-game-row.sidebar-collapsed .sidebar-game-row-title{visibility:hidden;position:absolute;transform:translateX(-10px);opacity:0;width:0}sidebar-game-row .sidebar-game-row.sidebar-collapsed .sidebar-game-row-more{display:none}sidebar-game-row .sidebar-game-row.sidebar-collapsed new-badge{display:none}sidebar-game-row .sidebar-game-row.sidebar-collapsed .sidebar-game-row-image{min-width:44px;height:44px;border-radius:12px;margin:0 auto}sidebar-game-row .sidebar-game-row.sidebar-collapsed .sidebar-game-row-image-new-dot{width:15px;height:15px;border:3px solid rgba(var(--theme--background--rgb), 0.9)}sidebar-game-row .sidebar-game-row.sidebar-collapsed .sidebar-game-row-image:hover{box-shadow:0 0 0 5px rgba(255,255,255,.2)}`,""]);const p=m},"app/resources/elements/sidebar-now-playing":(e,i,t)=>{t.r(i),t.d(i,{SidebarNowPlaying:()=>p});var a=t(15215),s=t(7530),r=t("aurelia-event-aggregator"),o=t("aurelia-framework"),n=t(43050),l=t(83802),d=t(96555),m=t(92465),g=t(54995);let p=class{#d;#m;#g;constructor(e,i){this.isSidebarCollapsed=!1,this.trainerInfo=null,this.trainer=null,this.#d=e,this.#m=i}attached(){const e=this.#p.bind(this);this.#g=new m.Vd([this.#d.onNewTrainer(e),this.#d.onTrainerEnded(e),this.#m.subscribe("router:navigation:success",e)])}#p(){if(!this.#d.trainer)return this.trainerInfo=null,void(this.trainer=null);this.trainer=this.#d.trainer,this.trainerInfo=this.#d.trainer.getMetadata(l.vO).info}get currentTitle(){if(!this.trainerInfo?.titleId)return null;const e=this.titles[this.trainerInfo.titleId],i=this.games[this.trainerInfo.gameId],t=i?.correlationIds?.map(d.o.parse)?.find((e=>"steam"===e.platform))?.sku??null;if(e){const i=(0,n.ZT)(this.state,e);if(i)return{...i,gameId:this.trainerInfo.gameId,steamAppId:t}}return null}detached(){this.#g?.dispose(),this.#g=null}};(0,a.Cg)([o.bindable,(0,a.Sn)("design:type",Boolean)],p.prototype,"isSidebarCollapsed",void 0),(0,a.Cg)([(0,s.Kj)("trainerInfo","titles"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],p.prototype,"currentTitle",null),p=(0,a.Cg)([(0,o.autoinject)(),(0,g.m6)({selectors:{state:e=>e.state,titles:(0,g.$t)((e=>e.catalog.titles)),games:(0,g.$t)((e=>e.catalog.games))}}),(0,a.Sn)("design:paramtypes",[l.jR,r.EventAggregator])],p)},"app/resources/elements/sidebar-now-playing.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template> <require from="./sidebar-now-playing.scss"></require> <require from="./sidebar-game-row"></require> <div class="sidebar-now-playing ${isSidebarCollapsed ? \'sidebar-collapsed\' : \'\'}" if.bind="currentTitle"> <div class="sidebar-now-playing-header"> <svg class="sidebar-now-playing-header-icon" width="11" height="12" viewBox="0 0 11 12" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M0.3125 9.59434C0.3125 11.2424 2.11711 12.2545 3.52339 11.3951L9.52177 7.72945C10.8684 6.90653 10.8684 4.9508 9.52177 4.12789L3.52339 0.462211C2.11711 -0.397184 0.3125 0.614905 0.3125 2.26299V9.59434Z" fill="currentColor" fill-opacity="1"/> </svg> <div class="sidebar-now-playing-header-text">${\'app_sidebar_now_playing.now_playing\' | i18n}</div> </div> <sidebar-game-row title.bind="currentTitle" is-sidebar-collapsed.bind="isSidebarCollapsed"></sidebar-game-row> <hr class="sidebar-divider"> </div> </template> '},"app/resources/elements/sidebar-now-playing.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>p});var a=t(31601),s=t.n(a),r=t(76314),o=t.n(r),n=t(4417),l=t.n(n),d=new URL(t(83959),t.b),m=o()(s()),g=l()(d);m.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}sidebar-now-playing .sidebar-now-playing{display:flex;flex-direction:column;padding:8px;transition:.2s all ease-in-out}sidebar-now-playing .sidebar-now-playing-header{display:flex;flex-direction:row;align-items:center;gap:10px;padding:0px 8px 8px 12px;font-weight:700;font-size:11px;letter-spacing:.5px;text-transform:uppercase;color:rgba(255,255,255,.6);height:32px}sidebar-now-playing .sidebar-now-playing-header-icon{display:flex;align-items:center;justify-content:center;width:20px;max-height:20px;color:#fff;opacity:.6}sidebar-now-playing .sidebar-now-playing-header-text{opacity:1;scale:1;transition:opacity .8s ease-in-out,scale .2s ease-in-out}sidebar-now-playing .sidebar-now-playing sidebar-game-row{--sidebar-game-row-title--color: var(--theme--highlight)}sidebar-now-playing .sidebar-now-playing.sidebar-collapsed{align-items:center}sidebar-now-playing .sidebar-now-playing.sidebar-collapsed .sidebar-now-playing-header-icon{max-height:24px;color:var(--theme--highlight);opacity:1;padding:0;margin:0 auto}sidebar-now-playing .sidebar-now-playing.sidebar-collapsed .sidebar-now-playing-header-text{visibility:hidden;position:absolute;scale:.5;opacity:0;min-width:72px}sidebar-now-playing .sidebar-now-playing.sidebar-collapsed sidebar-game-row .sidebar-game-row-image{border:1px solid var(--theme--highlight)}`,""]);const p=m},"app/resources/elements/sidebar-pro-badge":(e,i,t)=>{t.r(i),t.d(i,{SidebarProBadge:()=>a});class a{constructor(){this.tooltipOpen=!1}detached(){this.tooltipOpen=!1}handleOpenTooltip(e){e&&e.stopPropagation(),this.tooltipOpen=!0}}},"app/resources/elements/sidebar-pro-badge.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template class="${tooltipOpen ? \'tooltip-open\' : \'\'}"> <require from="./sidebar-pro-badge.scss"></require> <require from="./pro-onboarding-tooltip"></require> <require from="../../../shared/resources/elements/pro-badge"></require> <pro-badge class="small" click.delegate="handleOpenTooltip($event)"></pro-badge> <pro-onboarding-tooltip open.bind="tooltipOpen"></pro-onboarding-tooltip> </template> '},"app/resources/elements/sidebar-pro-badge.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>n});var a=t(31601),s=t.n(a),r=t(76314),o=t.n(r)()(s());o.push([e.id,"sidebar-pro-badge{position:relative}sidebar-pro-badge>.pro-onboarding-tooltip{position:absolute;bottom:0;right:0}sidebar-pro-badge pro-badge{display:inline-block}sidebar-pro-badge pro-badge,sidebar-pro-badge pro-badge *{cursor:pointer}",""]);const n=o},"app/resources/elements/title-sidebar-collapse-button":(e,i,t)=>{t.r(i),t.d(i,{TitleSidebarCollapseButton:()=>d});var a=t(15215),s=t(62914),r=t("aurelia-framework"),o=t(20770),n=t(54995),l=t(48881);let d=class{#s;#b;constructor(e,i){this.#s=e,this.#b=i}handleToggleTitleSidebar(){this.#b.event("title_sidebar_collapse",{isCollapsed:this.isTitleSidebarCollapsed},s.Io),this.#s.dispatch(l.Kc,{isTitleSidebarCollapsed:!this.isTitleSidebarCollapsed})}};d=(0,a.Cg)([(0,r.autoinject)(),(0,n.m6)({selectors:{isTitleSidebarCollapsed:(0,n.$t)((e=>e.settings.isTitleSidebarCollapsed))}}),(0,a.Sn)("design:paramtypes",[o.il,s.j0])],d)},"app/resources/elements/title-sidebar-collapse-button.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template> <require from="./title-sidebar-collapse-button.scss"></require> <button click.delegate="handleToggleTitleSidebar()" class="collapse-button"> <i class="collapse-icon"></i> </button> </template> '},"app/resources/elements/title-sidebar-collapse-button.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>p});var a=t(31601),s=t.n(a),r=t(76314),o=t.n(r),n=t(4417),l=t.n(n),d=new URL(t(83959),t.b),m=o()(s()),g=l()(d);m.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,title-sidebar-collapse-button button.collapse-button .collapse-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}title-sidebar-collapse-button button.collapse-button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;min-width:44px;height:44px}title-sidebar-collapse-button button.collapse-button,title-sidebar-collapse-button button.collapse-button *{cursor:pointer}title-sidebar-collapse-button button.collapse-button:hover{background:rgba(255,255,255,.25)}title-sidebar-collapse-button button.collapse-button:hover .collapse-icon{font-variation-settings:"FILL" 1,"wght" 400 !important}title-sidebar-collapse-button button.collapse-button .collapse-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:#fff;display:flex}title-sidebar-collapse-button button.collapse-button .collapse-icon:before{font-family:inherit;content:"dock_to_left"}`,""]);const p=m},"app/resources/elements/window-controls":(e,i,t)=>{t.r(i),t.d(i,{WindowControls:()=>o});var a=t(15215),s=t("aurelia-framework"),r=t(19072);let o=class{constructor(e){this.host=e}};o=(0,a.Cg)([(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[r.s])],o)},"app/resources/elements/window-controls.html":(e,i,t)=>{t.r(i),t.d(i,{default:()=>a});const a='<template class="window-controls"> <require from="./window-controls.scss"></require> <ul> <li if.bind="!host.minimized" click.trigger="host.minimize()" tabindex="0"> <span class="minimize"></span> </li> <li if.bind="!host.maximized" click.trigger="host.maximize()" tabindex="0"> <span class="maximize"></span> </li> <li if.bind="host.maximized" click.trigger="host.unmaximize()" tabindex="0"> <span class="restore"></span> </li> <li class="close" click.trigger="host.close()" tabindex="0"> <span class="close"></span> </li> </ul> </template> '},"app/resources/elements/window-controls.scss":(e,i,t)=>{t.r(i),t.d(i,{default:()=>w});var a=t(31601),s=t.n(a),r=t(76314),o=t.n(r),n=t(4417),l=t.n(n),d=new URL(t(19733),t.b),m=new URL(t(6335),t.b),g=new URL(t(64381),t.b),p=new URL(t(60247),t.b),b=o()(s()),c=l()(d),u=l()(m),h=l()(g),f=l()(p);b.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}window-controls{transition:opacity 2s ease-in-out;opacity:1}window-controls *{cursor:pointer}window-controls ul{display:flex;list-style:none;margin:0;padding:0}window-controls li{padding:0;width:52px;height:36px;box-sizing:content-box;display:grid;place-content:center;transition:background-color .3s ease-in-out}window-controls li:focus-visible,window-controls li:hover{background-color:rgba(255,255,255,.3)}window-controls li:focus-visible:has(.close),window-controls li:hover:has(.close){background-color:var(--color--alert)}window-controls li span{background-color:#fff;width:12px;height:12px}window-controls li .minimize{height:1px;-webkit-mask-box-image:url(${c})}window-controls li .maximize{width:10px;height:10px;-webkit-mask-box-image:url(${u})}window-controls li .restore{-webkit-mask-box-image:url(${h})}window-controls li .close{-webkit-mask-box-image:url(${f})}@media(forced-colors: active){body:not(.override-contrast-mode) window-controls li{opacity:1;forced-color-adjust:none;background:CanvasText}}`,""]);const w=b}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1278],{74896:(e,t,n)=>{n.d(t,{Tt:()=>S,Ze:()=>v,i6:()=>te,qg:()=>ee});var i=n(27098),r=n(89840),a=n.n(r),s=n(29191),o=n(27860),u=n(28978);const c=e=>!!e&&"object"==typeof e,l=(...e)=>e.reduce(((e,t)=>("object"!=typeof t||Object.keys(t).forEach((n=>{Array.isArray(e[n])&&Array.isArray(t[n])?e[n]=e[n].concat(t[n]):c(e[n])&&c(t[n])?e[n]=l(e[n],t[n]):e[n]=t[n]})),e)),{}),d=e=>Object.keys(e).map((t=>e[t])),m=e=>e.reduce(((e,t)=>e.concat(t)),[]),p=e=>{if(!e.length)return[];const t=[];for(let n=0;n<e.length;n++)t.push(e[n]);return t};const g=({baseUrl:e="",source:t="",range:n="",indexRange:r=""})=>{const s={uri:t,resolvedUri:(0,i.A)(e||"",t)};if(n||r){const e=(n||r).split("-");let t,i=a().BigInt?a().BigInt(e[0]):parseInt(e[0],10),o=a().BigInt?a().BigInt(e[1]):parseInt(e[1],10);i<Number.MAX_SAFE_INTEGER&&"bigint"==typeof i&&(i=Number(i)),o<Number.MAX_SAFE_INTEGER&&"bigint"==typeof o&&(o=Number(o)),t="bigint"==typeof o||"bigint"==typeof i?a().BigInt(o)-a().BigInt(i)+a().BigInt(1):o-i+1,"bigint"==typeof t&&t<Number.MAX_SAFE_INTEGER&&(t=Number(t)),s.byterange={length:t,offset:i}}return s},b=e=>(e&&"number"!=typeof e&&(e=parseInt(e,10)),isNaN(e)?null:e),f={static(e){const{duration:t,timescale:n=1,sourceDuration:i,periodDuration:r}=e,a=b(e.endNumber),s=t/n;return"number"==typeof a?{start:0,end:a}:"number"==typeof r?{start:0,end:r/s}:{start:0,end:i/s}},dynamic(e){const{NOW:t,clientOffset:n,availabilityStartTime:i,timescale:r=1,duration:a,periodStart:s=0,minimumUpdatePeriod:o=0,timeShiftBufferDepth:u=1/0}=e,c=b(e.endNumber),l=(t+n)/1e3,d=i+s,m=l+o-d,p=Math.ceil(m*r/a),g=Math.floor((l-d-u)*r/a),f=Math.floor((l-d)*r/a);return{start:Math.max(0,g),end:"number"==typeof c?c:Math.min(p,f)}}},h=e=>{const{type:t,duration:n,timescale:i=1,periodDuration:r,sourceDuration:a}=e,{start:s,end:o}=f[t](e),u=((e,t)=>{const n=[];for(let i=e;i<t;i++)n.push(i);return n})(s,o).map((e=>t=>{const{duration:n,timescale:i=1,periodStart:r,startNumber:a=1}=e;return{number:a+t,duration:n/i,timeline:r,time:t*n}})(e));if("static"===t){const e=u.length-1,t="number"==typeof r?r:a;u[e].duration=t-n/i*e}return u},y=e=>{const{baseUrl:t,initialization:n={},sourceDuration:i,indexRange:r="",periodStart:a,presentationTime:s,number:o=0,duration:u}=e;if(!t)throw new Error("NO_BASE_URL");const c=g({baseUrl:t,source:n.sourceURL,range:n.range}),l=g({baseUrl:t,source:t,indexRange:r});if(l.map=c,u){const t=h(e);t.length&&(l.duration=t[0].duration,l.timeline=t[0].timeline)}else i&&(l.duration=i,l.timeline=a);return l.presentationTime=s||a,l.number=o,[l]},S=(e,t,n)=>{const i=e.sidx.map?e.sidx.map:null,r=e.sidx.duration,s=e.timeline||0,o=e.sidx.byterange,u=o.offset+o.length,c=t.timescale,l=t.references.filter((e=>1!==e.referenceType)),d=[],m=e.endList?"static":"dynamic",p=e.sidx.timeline;let g,b=p,f=e.mediaSequence||0;g="bigint"==typeof t.firstOffset?a().BigInt(u)+t.firstOffset:u+t.firstOffset;for(let e=0;e<l.length;e++){const o=t.references[e],u=o.referencedSize,l=o.subsegmentDuration;let h;h="bigint"==typeof g?g+a().BigInt(u)-a().BigInt(1):g+u-1;const S=y({baseUrl:n,timescale:c,timeline:s,periodStart:p,presentationTime:b,number:f,duration:l,sourceDuration:r,indexRange:`${g}-${h}`,type:m})[0];i&&(S.map=i),d.push(S),g+="bigint"==typeof g?a().BigInt(u):u,b+=l/c,f++}return e.segments=d,e},I=["AUDIO","SUBTITLES"],U=1/60,D=e=>{return(t=e,n=({timeline:e})=>e,d(t.reduce(((e,t)=>(t.forEach((t=>{e[n(t)]=t})),e)),{}))).sort(((e,t)=>e.timeline>t.timeline?1:-1));var t,n},E=e=>{let t=[];return(0,s._)(e,I,((e,n,i,r)=>{t=t.concat(e.playlists||[])})),t},T=({playlist:e,mediaSequence:t})=>{e.mediaSequence=t,e.segments.forEach(((t,n)=>{t.number=e.mediaSequence+n}))},v=e=>e&&e.uri+"-"+(e=>{let t;return t="bigint"==typeof e.offset||"bigint"==typeof e.length?a().BigInt(e.offset)+a().BigInt(e.length)-a().BigInt(1):e.offset+e.length-1,`${e.offset}-${t}`})(e.byterange),N=e=>{const t=e.reduce((function(e,t){return e[t.attributes.baseUrl]||(e[t.attributes.baseUrl]=[]),e[t.attributes.baseUrl].push(t),e}),{});let n=[];return Object.values(t).forEach((e=>{const t=d(e.reduce(((e,t)=>{const n=t.attributes.id+(t.attributes.lang||"");return e[n]?(t.segments&&(t.segments[0]&&(t.segments[0].discontinuity=!0),e[n].segments.push(...t.segments)),t.attributes.contentProtection&&(e[n].attributes.contentProtection=t.attributes.contentProtection)):(e[n]=t,e[n].attributes.timelineStarts=[]),e[n].attributes.timelineStarts.push({start:t.attributes.periodStart,timeline:t.attributes.periodStart}),e}),{}));n=n.concat(t)})),n.map((e=>{var t;return e.discontinuityStarts=(t=e.segments||[],t.reduce(((e,t,n)=>(t.discontinuity&&e.push(n),e)),[])),e}))},R=(e,t)=>{const n=v(e.sidx),i=n&&t[n]&&t[n].sidx;return i&&S(e,i,e.sidx.resolvedUri),e},O=(e,t={})=>{if(!Object.keys(t).length)return e;for(const n in e)e[n]=R(e[n],t);return e},A=({attributes:e,segments:t,sidx:n,discontinuityStarts:i})=>{const r={attributes:{NAME:e.id,AUDIO:"audio",SUBTITLES:"subs",RESOLUTION:{width:e.width,height:e.height},CODECS:e.codecs,BANDWIDTH:e.bandwidth,"PROGRAM-ID":1},uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,discontinuityStarts:i,timelineStarts:e.timelineStarts,segments:t};return e.frameRate&&(r.attributes["FRAME-RATE"]=e.frameRate),e.contentProtection&&(r.contentProtection=e.contentProtection),e.serviceLocation&&(r.attributes.serviceLocation=e.serviceLocation),n&&(r.sidx=n),r},L=({attributes:e})=>"video/mp4"===e.mimeType||"video/webm"===e.mimeType||"video"===e.contentType,P=({attributes:e})=>"audio/mp4"===e.mimeType||"audio/webm"===e.mimeType||"audio"===e.contentType,M=({attributes:e})=>"text/vtt"===e.mimeType||"text"===e.contentType,w=e=>e?Object.keys(e).reduce(((t,n)=>{const i=e[n];return t.concat(i.playlists)}),[]):[],x=({dashPlaylists:e,locations:t,contentSteering:n,sidxMapping:i={},previousManifest:r,eventStream:a})=>{if(!e.length)return{};const{sourceDuration:s,type:o,suggestedPresentationDelay:u,minimumUpdatePeriod:c}=e[0].attributes,l=N(e.filter(L)).map(A),d=N(e.filter(P)),m=N(e.filter(M)),p=e.map((e=>e.attributes.captionServices)).filter(Boolean),g={allowCache:!0,discontinuityStarts:[],segments:[],endList:!0,mediaGroups:{AUDIO:{},VIDEO:{},"CLOSED-CAPTIONS":{},SUBTITLES:{}},uri:"",duration:s,playlists:O(l,i)};c>=0&&(g.minimumUpdatePeriod=1e3*c),t&&(g.locations=t),n&&(g.contentSteering=n),"dynamic"===o&&(g.suggestedPresentationDelay=u),a&&a.length>0&&(g.eventStream=a);const b=0===g.playlists.length,f=d.length?((e,t={},n=!1)=>{let i;const r=e.reduce(((e,r)=>{const a=r.attributes.role&&r.attributes.role.value||"",s=r.attributes.lang||"";let o=r.attributes.label||"main";if(s&&!r.attributes.label){const e=a?` (${a})`:"";o=`${r.attributes.lang}${e}`}e[o]||(e[o]={language:s,autoselect:!0,default:"main"===a,playlists:[],uri:""});const u=R((({attributes:e,segments:t,sidx:n,mediaSequence:i,discontinuitySequence:r,discontinuityStarts:a},s)=>{const o={attributes:{NAME:e.id,BANDWIDTH:e.bandwidth,CODECS:e.codecs,"PROGRAM-ID":1},uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,discontinuitySequence:r,discontinuityStarts:a,timelineStarts:e.timelineStarts,mediaSequence:i,segments:t};return e.contentProtection&&(o.contentProtection=e.contentProtection),e.serviceLocation&&(o.attributes.serviceLocation=e.serviceLocation),n&&(o.sidx=n),s&&(o.attributes.AUDIO="audio",o.attributes.SUBTITLES="subs"),o})(r,n),t);return e[o].playlists.push(u),void 0===i&&"main"===a&&(i=r,i.default=!0),e}),{});return i||(r[Object.keys(r)[0]].default=!0),r})(d,i,b):null,h=m.length?((e,t={})=>e.reduce(((e,n)=>{const i=n.attributes.label||n.attributes.lang||"text",r=n.attributes.lang||"und";return e[i]||(e[i]={language:r,default:!1,autoselect:!1,playlists:[],uri:""}),e[i].playlists.push(R((({attributes:e,segments:t,mediaSequence:n,discontinuityStarts:i,discontinuitySequence:r})=>{void 0===t&&(t=[{uri:e.baseUrl,timeline:e.periodStart,resolvedUri:e.baseUrl||"",duration:e.sourceDuration,number:0}],e.duration=e.sourceDuration);const a={NAME:e.id,BANDWIDTH:e.bandwidth,"PROGRAM-ID":1};e.codecs&&(a.CODECS=e.codecs);const s={attributes:a,uri:"",endList:"static"===e.type,timeline:e.periodStart,resolvedUri:e.baseUrl||"",targetDuration:e.duration,timelineStarts:e.timelineStarts,discontinuityStarts:i,discontinuitySequence:r,mediaSequence:n,segments:t};return e.serviceLocation&&(s.attributes.serviceLocation=e.serviceLocation),s})(n),t)),e}),{}))(m,i):null,y=l.concat(w(f),w(h)),S=y.map((({timelineStarts:e})=>e));var I,v;return g.timelineStarts=D(S),I=y,v=g.timelineStarts,I.forEach((e=>{e.mediaSequence=0,e.discontinuitySequence=v.findIndex((function({timeline:t}){return t===e.timeline})),e.segments&&e.segments.forEach(((e,t)=>{e.number=t}))})),f&&(g.mediaGroups.AUDIO.audio=f),h&&(g.mediaGroups.SUBTITLES.subs=h),p.length&&(g.mediaGroups["CLOSED-CAPTIONS"].cc=p.reduce(((e,t)=>t?(t.forEach((t=>{const{channel:n,language:i}=t;e[i]={autoselect:!1,default:!1,instreamId:n,language:i},t.hasOwnProperty("aspectRatio")&&(e[i].aspectRatio=t.aspectRatio),t.hasOwnProperty("easyReader")&&(e[i].easyReader=t.easyReader),t.hasOwnProperty("3D")&&(e[i]["3D"]=t["3D"])})),e):e),{})),r?(({oldManifest:e,newManifest:t})=>{const n=e.playlists.concat(E(e)),i=t.playlists.concat(E(t));return t.timelineStarts=D([e.timelineStarts,t.timelineStarts]),(({oldPlaylists:e,newPlaylists:t,timelineStarts:n})=>{t.forEach((t=>{t.discontinuitySequence=n.findIndex((function({timeline:e}){return e===t.timeline}));const i=((e,t)=>{for(let n=0;n<e.length;n++)if(e[n].attributes.NAME===t)return e[n];return null})(e,t.attributes.NAME);if(!i)return;if(t.sidx)return;const r=t.segments[0],a=i.segments.findIndex((function(e){return Math.abs(e.presentationTime-r.presentationTime)<U}));if(-1===a)return T({playlist:t,mediaSequence:i.mediaSequence+i.segments.length}),t.segments[0].discontinuity=!0,t.discontinuityStarts.unshift(0),void((!i.segments.length&&t.timeline>i.timeline||i.segments.length&&t.timeline>i.segments[i.segments.length-1].timeline)&&t.discontinuitySequence--);i.segments[a].discontinuity&&!r.discontinuity&&(r.discontinuity=!0,t.discontinuityStarts.unshift(0),t.discontinuitySequence--),T({playlist:t,mediaSequence:i.segments[a].number})}))})({oldPlaylists:n,newPlaylists:i,timelineStarts:t.timelineStarts}),t})({oldManifest:r,newManifest:g}):g},B=(e,t,n)=>{const{NOW:i,clientOffset:r,availabilityStartTime:a,timescale:s=1,periodStart:o=0,minimumUpdatePeriod:u=0}=e,c=(i+r)/1e3+u-(a+o);return Math.ceil((c*s-t)/n)},C=(e,t)=>{const{type:n,minimumUpdatePeriod:i=0,media:r="",sourceDuration:a,timescale:s=1,startNumber:o=1,periodStart:u}=e,c=[];let l=-1;for(let d=0;d<t.length;d++){const m=t[d],p=m.d,g=m.r||0,b=m.t||0;let f;if(l<0&&(l=b),b&&b>l&&(l=b),g<0){const o=d+1;f=o===t.length?"dynamic"===n&&i>0&&r.indexOf("$Number$")>0?B(e,l,p):(a*s-l)/p:(t[o].t-l)/p}else f=g+1;const h=o+c.length+f;let y=o+c.length;for(;y<h;)c.push({number:y,duration:p/s,time:l,timeline:u}),l+=p,y++}return c},q=/\$([A-z]*)(?:(%0)([0-9]+)d)?\$/g,_=(e,t)=>e.replace(q,(e=>(t,n,i,r)=>{if("$$"===t)return"$";if(void 0===e[n])return t;const a=""+e[n];return"RepresentationID"===n?a:(r=i?parseInt(r,10):1,a.length>=r?a:`${new Array(r-a.length+1).join("0")}${a}`)})(t)),$=(e,t)=>{const n={RepresentationID:e.id,Bandwidth:e.bandwidth||0},{initialization:r={sourceURL:"",range:""}}=e,a=g({baseUrl:e.baseUrl,source:_(r.sourceURL,n),range:r.range}),s=((e,t)=>e.duration||t?e.duration?h(e):C(e,t):[{number:e.startNumber||1,duration:e.sourceDuration,time:0,timeline:e.periodStart}])(e,t);return s.map((t=>{n.Number=t.number,n.Time=t.time;const r=_(e.media||"",n),s=e.timescale||1,o=e.presentationTimeOffset||0,u=e.periodStart+(t.time-o)/s;return{uri:r,timeline:t.timeline,duration:t.duration,resolvedUri:(0,i.A)(e.baseUrl||"",r),map:a,number:t.number,presentationTime:u}}))},F=(e,t)=>{const{duration:n,segmentUrls:i=[],periodStart:r}=e;if(!n&&!t||n&&t)throw new Error("SEGMENT_TIME_UNSPECIFIED");const a=i.map((t=>((e,t)=>{const{baseUrl:n,initialization:i={}}=e,r=g({baseUrl:n,source:i.sourceURL,range:i.range}),a=g({baseUrl:n,source:t.media,range:t.mediaRange});return a.map=r,a})(e,t)));let s;return n&&(s=h(e)),t&&(s=C(e,t)),s.map(((t,n)=>{if(a[n]){const i=a[n],s=e.timescale||1,o=e.presentationTimeOffset||0;return i.timeline=t.timeline,i.duration=t.duration,i.number=t.number,i.presentationTime=r+(t.time-o)/s,i}})).filter((e=>e))},k=({attributes:e,segmentInfo:t})=>{let n,i;t.template?(i=$,n=l(e,t.template)):t.base?(i=y,n=l(e,t.base)):t.list&&(i=F,n=l(e,t.list));const r={attributes:e};if(!i)return r;const a=i(n,t.segmentTimeline);if(n.duration){const{duration:e,timescale:t=1}=n;n.duration=e/t}else a.length?n.duration=a.reduce(((e,t)=>Math.max(e,Math.ceil(t.duration))),0):n.duration=0;return r.attributes=n,r.segments=a,t.base&&n.indexRange&&(r.sidx=a[0],r.segments=[]),r},G=(e,t)=>p(e.childNodes).filter((({tagName:e})=>e===t)),z=e=>e.textContent.trim(),j=e=>{const t=/P(?:(\d*)Y)?(?:(\d*)M)?(?:(\d*)D)?(?:T(?:(\d*)H)?(?:(\d*)M)?(?:([\d.]*)S)?)?/.exec(e);if(!t)return 0;const[n,i,r,a,s,o]=t.slice(1);return 31536e3*parseFloat(n||0)+2592e3*parseFloat(i||0)+86400*parseFloat(r||0)+3600*parseFloat(a||0)+60*parseFloat(s||0)+parseFloat(o||0)},H={mediaPresentationDuration:e=>j(e),availabilityStartTime(e){return/^\d+-\d+-\d+T\d+:\d+:\d+(\.\d+)?$/.test(t=e)&&(t+="Z"),Date.parse(t)/1e3;var t},minimumUpdatePeriod:e=>j(e),suggestedPresentationDelay:e=>j(e),type:e=>e,timeShiftBufferDepth:e=>j(e),start:e=>j(e),width:e=>parseInt(e,10),height:e=>parseInt(e,10),bandwidth:e=>parseInt(e,10),frameRate:e=>(e=>parseFloat(e.split("/").reduce(((e,t)=>e/t))))(e),startNumber:e=>parseInt(e,10),timescale:e=>parseInt(e,10),presentationTimeOffset:e=>parseInt(e,10),duration(e){const t=parseInt(e,10);return isNaN(t)?j(e):t},d:e=>parseInt(e,10),t:e=>parseInt(e,10),r:e=>parseInt(e,10),presentationTime:e=>parseInt(e,10),DEFAULT:e=>e},W=e=>e&&e.attributes?p(e.attributes).reduce(((e,t)=>{const n=H[t.name]||H.DEFAULT;return e[t.name]=n(t.value),e}),{}):{},V={"urn:uuid:1077efec-c0b2-4d02-ace3-3c1e52e2fb4b":"org.w3.clearkey","urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed":"com.widevine.alpha","urn:uuid:9a04f079-9840-4286-ab92-e65be0885f95":"com.microsoft.playready","urn:uuid:f239e769-efa3-4850-9c16-a903c6932efb":"com.adobe.primetime","urn:mpeg:dash:mp4protection:2011":"mp4protection"},X=(e,t)=>t.length?m(e.map((function(e){return t.map((function(t){const n=z(t),r=(0,i.A)(e.baseUrl,n),a=l(W(t),{baseUrl:r});return r!==n&&!a.serviceLocation&&e.serviceLocation&&(a.serviceLocation=e.serviceLocation),a}))}))):e,Y=e=>{const t=G(e,"SegmentTemplate")[0],n=G(e,"SegmentList")[0],i=n&&G(n,"SegmentURL").map((e=>l({tag:"SegmentURL"},W(e)))),r=G(e,"SegmentBase")[0],a=n||t,s=a&&G(a,"SegmentTimeline")[0],o=n||r||t,u=o&&G(o,"Initialization")[0],c=t&&W(t);c&&u?c.initialization=u&&W(u):c&&c.initialization&&(c.initialization={sourceURL:c.initialization});const d={template:c,segmentTimeline:s&&G(s,"S").map((e=>W(e))),list:n&&l(W(n),{segmentUrls:i,initialization:W(u)}),base:r&&l(W(r),{initialization:W(u)})};return Object.keys(d).forEach((e=>{d[e]||delete d[e]})),d},Z=e=>m(G(e.node,"EventStream").map((t=>{const n=W(t),i=n.schemeIdUri;return G(t,"Event").map((t=>{const r=W(t),a=r.presentationTime||0,s=n.timescale||1,o=r.duration||0,u=a/s+e.attributes.start;return{schemeIdUri:i,value:n.value,id:r.id,start:u,end:u+o/s,messageData:z(t)||r.messageData,contentEncoding:n.contentEncoding,presentationTimeOffset:n.presentationTimeOffset||0}}))}))),J=(e,t)=>(n,i)=>{const r=X(t,G(n.node,"BaseURL")),a=l(e,{periodStart:n.attributes.start});"number"==typeof n.attributes.duration&&(a.periodDuration=n.attributes.duration);const s=G(n.node,"AdaptationSet"),u=Y(n.node);return m(s.map(((e,t,n)=>i=>{const r=W(i),a=X(t,G(i,"BaseURL")),s=G(i,"Role")[0],u={role:W(s)};let c=l(e,r,u);const d=G(i,"Accessibility")[0],p="urn:scte:dash:cc:cea-608:2015"===(g=W(d)).schemeIdUri?("string"!=typeof g.value?[]:g.value.split(";")).map((e=>{let t,n;return n=e,/^CC\d=/.test(e)?[t,n]=e.split("="):/^CC\d$/.test(e)&&(t=e),{channel:t,language:n}})):"urn:scte:dash:cc:cea-708:2015"===g.schemeIdUri?("string"!=typeof g.value?[]:g.value.split(";")).map((e=>{const t={channel:void 0,language:void 0,aspectRatio:1,easyReader:0,"3D":0};if(/=/.test(e)){const[n,i=""]=e.split("=");t.channel=n,t.language=e,i.split(",").forEach((e=>{const[n,i]=e.split(":");"lang"===n?t.language=i:"er"===n?t.easyReader=Number(i):"war"===n?t.aspectRatio=Number(i):"3D"===n&&(t["3D"]=Number(i))}))}else t.language=e;return t.channel&&(t.channel="SERVICE"+t.channel),t})):void 0;var g;p&&(c=l(c,{captionServices:p}));const b=G(i,"Label")[0];if(b&&b.childNodes.length){const e=b.childNodes[0].nodeValue.trim();c=l(c,{label:e})}const f=G(i,"ContentProtection").reduce(((e,t)=>{const n=W(t);n.schemeIdUri&&(n.schemeIdUri=n.schemeIdUri.toLowerCase());const i=V[n.schemeIdUri];if(i){e[i]={attributes:n};const r=G(t,"cenc:pssh")[0];if(r){const t=z(r);e[i].pssh=t&&(0,o.A)(t)}}return e}),{});Object.keys(f).length&&(c=l(c,{contentProtection:f}));const h=Y(i),y=G(i,"Representation"),S=l(n,h);return m(y.map(((e,t,n)=>i=>{const r=G(i,"BaseURL"),a=X(t,r),s=l(e,W(i)),o=Y(i);return a.map((e=>({segmentInfo:l(n,o),attributes:l(s,e)})))})(c,a,S)))})(a,r,u)))},K=(e,t)=>{if(e.length>1&&t({type:"warn",message:"The MPD manifest should contain no more than one ContentSteering tag"}),!e.length)return null;const n=l({serverURL:z(e[0])},W(e[0]));return n.queryBeforeStart="true"===n.queryBeforeStart,n},Q=e=>{if(""===e)throw new Error("DASH_EMPTY_MANIFEST");const t=new u.DOMParser;let n,i;try{n=t.parseFromString(e,"application/xml"),i=n&&"MPD"===n.documentElement.tagName?n.documentElement:null}catch(e){}if(!i||i&&i.getElementsByTagName("parsererror").length>0)throw new Error("DASH_INVALID_XML");return i},ee=(e,t={})=>{const n=((e,t={})=>{const{manifestUri:n="",NOW:i=Date.now(),clientOffset:r=0,eventHandler:a=function(){}}=t,s=G(e,"Period");if(!s.length)throw new Error("INVALID_NUMBER_OF_PERIOD");const o=G(e,"Location"),u=W(e),c=X([{baseUrl:n}],G(e,"BaseURL")),l=G(e,"ContentSteering");u.type=u.type||"static",u.sourceDuration=u.mediaPresentationDuration||0,u.NOW=i,u.clientOffset=r,o.length&&(u.locations=o.map(z));const d=[];return s.forEach(((e,t)=>{const n=W(e),i=d[t-1];n.start=(({attributes:e,priorPeriodAttributes:t,mpdType:n})=>"number"==typeof e.start?e.start:t&&"number"==typeof t.start&&"number"==typeof t.duration?t.start+t.duration:t||"static"!==n?null:0)({attributes:n,priorPeriodAttributes:i?i.attributes:null,mpdType:u.type}),d.push({node:e,attributes:n})})),{locations:u.locations,contentSteeringInfo:K(l,a),representationInfo:m(d.map(J(u,c))),eventStream:m(d.map(Z))}})(Q(e),t),i=n.representationInfo.map(k);return x({dashPlaylists:i,locations:n.locations,contentSteering:n.contentSteeringInfo,sidxMapping:t.sidxMapping,previousManifest:t.previousManifest,eventStream:n.eventStream})},te=e=>(e=>{const t=G(e,"UTCTiming")[0];if(!t)return null;const n=W(t);switch(n.schemeIdUri){case"urn:mpeg:dash:utc:http-head:2014":case"urn:mpeg:dash:utc:http-head:2012":n.method="HEAD";break;case"urn:mpeg:dash:utc:http-xsdate:2014":case"urn:mpeg:dash:utc:http-iso:2014":case"urn:mpeg:dash:utc:http-xsdate:2012":case"urn:mpeg:dash:utc:http-iso:2012":n.method="GET";break;case"urn:mpeg:dash:utc:direct:2014":case"urn:mpeg:dash:utc:direct:2012":n.method="DIRECT",n.value=Date.parse(n.value);break;default:throw new Error("UNSUPPORTED_UTC_TIMING_SCHEME")}return n})(Q(e))}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7183],{811:(e,t,s)=>{s.d(t,{n:()=>I});var i=s(15215),a=s(16928),n=s("aurelia-dialog"),o=s("aurelia-framework"),r=s(20770),c=s(51977),l=s(69735),h=s(35392),d=s(68663),u=s("dialogs/webview-dialog"),p=s(16953),m=s(19072),g=s(20057),y=s(54995),b=s(49442),v=s(48881),f=s(38777),S=s(50643),C=s(86824),w=s(62914);let I=class{#e;#t;#s;#i;#a;#n;#o;#r;#c;#l;#h;#d;constructor(e,t,s,i,a,n,o){this.promotion=null,this.#n=e,this.#o=t,this.#r=s,this.#c=i,this.#l=a,this.#h=n,this.#d=o}attached(){this.#i=this.#d.onLocaleChanged((()=>this.refresh())),this.refresh()}detached(){this.promotion=null,this.#a?.dispose(),this.#e?.dispose(),this.#t?.dispose(),this.#s?.dispose(),this.#i?.dispose()}async refresh(e){this.#t?.dispose();try{if(!e)try{e=await this.#c.getPromotion()}catch{return}JSON.stringify(e)!==JSON.stringify(this.promotion)&&this.#u(e)}finally{this.#t=(0,f.Ix)((()=>this.refresh()),(0,C.H)(60,70))}}#u(e){this.promotion=e,this.#e?.dispose(),this.#a?.dispose(),e&&(this.#p(),this.#a=this.#h.whenVisible((()=>this.#l.event("promotion_app_banner_show",{promotionId:e.id},w.Io)))),this.#m()}audienceChanged(){this.refresh()}subscriptionChanged(e,t){JSON.stringify(t??null)!==JSON.stringify(e??null)&&this.refresh()}async showDialog(e=null){if("string"==typeof e&&this.#o.hasOpenDialog)return!1;const t=this.promotion?.components?.dialog;if(!t)return!1;if(e){if(!t.triggers.includes(e))return!1;const s=this.promotionHistory[this.promotion?.id||""]?.dialogShownAt,i=this.promotionHistory[this.promotion?.id||""]?.notificationClickedAt;if(s||i)return!1}return this.#n.dispatch(v.Lc,this.promotion?.id,"dialog",(new Date).toISOString()),await this.#r.open({route:t.route,params:t.params}),!0}#p(){const e=this.promotion?.components?.notification;e&&(this.#e=(0,f.Ix)((async()=>{if(document.hasFocus()&&!p.A.debug)return;if(!this.promotion)return;const t=this.promotion.id,s=this.promotionHistory[t],i=s?.notificationShownAt;if(i)return;const a=new S.c(e.title).addText(e.message).setActivationType("protocol").setLaunchString(this.#g(t,e.url));if(e.image){const s=await this.#y(t,e.image);s&&a.addImage(s,"appLogoOverride")}e.actions.forEach((e=>a.addAction({activationType:e.type,content:e.label,arguments:"protocol"===e.type?this.#g(t,e.arguments):e.arguments}))),await this.#h.showToast(a.toXml())&&(await this.#n.dispatch(v.Lc,t,"notification",(new Date).toISOString()),this.#l.event("promotion_notification_show",{promotionId:this.promotion.id,windowState:this.#h.visible?this.#h.minimized?"minimized":"visible":"tray"},w.Io))}),1e3*e.delay))}#g(e,t){const s=new URL(t);return"wemod:"===s.protocol&&(s.searchParams.set("trigger",`promotion:${e}/notification`),t=s.toString()),t}async#y(e,t){const s=t.match(/^data:image\/(png|jpeg|gif|webp);base64,([a-zA-Z0-9+/]+={0,2})$/);if(s){const t=a.join(this.#h.info.paths.temp,"WeMod");await h.promises.mkdir(t).catch(b.Y);const i=a.join(t,`promo-notification-${e}.${s[1]}`);return await h.promises.writeFile(i,s[2],"base64"),i}return!1}#m(){if(this.#s?.dispose(),!this.promotion||!this.promotion.endsAt)return;const e=new Date(this.promotion.endsAt);if((0,c.A)(e,Date.now()))this.#u(null);else{const t=(0,l.A)(Date.now(),e);this.#s=(0,f.Ix)((()=>this.#m()),t)}}};I=(0,i.Cg)([(0,o.autoinject)(),(0,y.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,y.$t)((e=>e.account?.subscription)),promotionHistory:(0,y.$t)((e=>e.promotionHistory))}}),(0,i.Sn)("design:paramtypes",[r.il,n.DialogService,u.WebviewDialogService,d.x,w.j0,m.s,g.F2])],I)},29879:(e,t,s)=>{s.d(t,{m:()=>h});var i=s(15215),a=s(79896),n=s(70857),o=s(16928),r=s("aurelia-framework"),c=s(84157),l=s(19072);let h=class{#b;#v;#h;constructor(e){this.#b=new Set,this.#v=0,this.#h=e}attached(){}detached(){}async captureScreenshot(e){if(!this.#b.has(e)){this.#b.add(e),await this.#h.captureScreenshot(e);try{const t=this.#f(e);let s;try{s=await a.promises.readFile(t),this.#v+=1,this.#v>=100&&c.webFrame.clearCache()}catch{}return this.#b.delete(e),s}catch{}this.#b.delete(e)}}async cleanupScreenshot(e){const t=this.#f(e);try{await a.promises.unlink(t),c.webFrame.clearCache()}catch{}}#f(e){return o.join(n.tmpdir(),`${e}.png`)}};h=(0,i.Cg)([(0,r.autoinject)(),(0,i.Sn)("design:paramtypes",[l.s])],h)},33700:(e,t,s)=>{s.d(t,{q:()=>l});var i=s(15215),a=s("aurelia-framework"),n=s(24008),o=s(20057),r=s(54995),c=s(70236);let l=class{#d;constructor(e){this.#d=e}attached(){}detached(){}get count(){return this.games?Object.values(this.games).filter((e=>(0,c.Lt)(e.flags,n.rT.Available))).length:0}get formattedCount(){return this.#d.formatNumber(this.count)}};(0,i.Cg)([(0,a.computedFrom)("games"),(0,i.Sn)("design:type",Number),(0,i.Sn)("design:paramtypes",[])],l.prototype,"count",null),(0,i.Cg)([(0,a.computedFrom)("count"),(0,i.Sn)("design:type",String),(0,i.Sn)("design:paramtypes",[])],l.prototype,"formattedCount",null),l=(0,i.Cg)([(0,r.m6)({setup:"attached",teardown:"detached",selectors:{games:(0,r.$t)((e=>e.catalog?.games))}}),(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[o.F2])],l)},43570:(e,t,s)=>{s.d(t,{A:()=>i,L:()=>p});var i,a=s(15215),n=s(16928),o=s("aurelia-framework"),r=s(20770),c=s(83802),l=s(54995),h=s(48881),d=s(38777);!function(e){e.Execute="execute",e.Enable="enable",e.Disable="disable",e.Increase="increase",e.Decrease="decrease",e.Limit="limit",e.Error="error"}(i||(i={}));const u=["Percussive","Boopie","Melodic","Retro"];let p=class{#n;#S;#C;#w;constructor(e,t){this.availablePacks=u,this.#C=null,this.#n=e,this.#S=t}attached(){this.selectedPack||this.setSoundPack(u[0],"sound_player",!0),this.#w=this.#S.onNewTrainer((e=>{this.preload(),e.onValueSetError((()=>this.play(i.Error)))}))}detached(){this.#w?.dispose(),this.#w=null}selectedPackChanged(){this.#C=null}async setSoundPack(e,t,s){this.availablePacks.includes(e)&&(this.#C=null,await this.#n.dispatch(h.Kc,{cheatSoundPack:e},t,s))}async setVolume(e,t){e>=0&&e<=100&&(await this.#n.dispatch(h.Kc,{cheatSoundVolume:e},t),this.#C?.setVolume(e))}async preload(){if(null!==this.#C)return!0;const e=this.selectedPack,t=await m.load(e,Number(this.volume));return e===this.selectedPack&&(this.#C=t,!0)}async play(e){this.enabled&&(null!==this.#C||await this.preload())&&await(this.#C?.play(e).catch((()=>null)))}async playAtFixedVolume(e,t){(null!==this.#C||await this.preload())&&this.#C?.playAtFixedVolume(e,t)}};p=(0,a.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{enabled:(0,l.$t)((e=>e.settings?.cheatSounds)),selectedPack:(0,l.$t)((e=>e.settings?.cheatSoundPack)),volume:(0,l.$t)((e=>e.settings?.cheatSoundVolume))}}),(0,o.autoinject)(),(0,a.Sn)("design:paramtypes",[r.il,c.jR])],p);class m{#I;constructor(e){this.#I=e}play(e){const t=this.#I[e];return t.currentTime=0,t.play()}playAtFixedVolume(e,t){const s=this.#I[e],i=s.volume;return s.currentTime=0,s.volume=t/100,s.addEventListener("ended",(()=>{s.volume=i}),{once:!0}),s.play()}setVolume(e){"number"==typeof e&&Object.values(this.#I).forEach((t=>m.#T(t,e)))}static async load(e,t){const s={};return await Promise.all(Object.values(i).map((async i=>{s[i]=await this.#P(function(e,t){return n.normalize(`./static/audio/cheats/${e}/${t}.mp3`)}(e,i),t)}))),new m(s)}static#P(e,t){return new Promise(((s,i)=>{const a=new Audio(e),n=(new d.Vd).pushEventListener(a,"error",(e=>{n.dispose(),i(e)})).pushEventListener(a,"canplaythrough",(()=>{n.dispose(),"number"==typeof t&&m.#T(a,t),s(a)}))}))}static#T(e,t){e.volume=t/100}}},43861:(e,t,s)=>{s.d(t,{X:()=>i,Z:()=>p});var i,a=s(15215),n=s("aurelia-framework"),o=s(20770),r=s(68663),c=s("dialogs/selection-dialog"),l=s(20057),h=s(54995),d=s(48881),u=s(68368);!function(e){e[e.NotShown=0]="NotShown",e[e.Canceled=1]="Canceled",e[e.Completed=2]="Completed"}(i||(i={}));let p=class{#A;#n;#c;#L;constructor(e,t,s,i){this.#A=e,this.#n=t,this.#c=s,this.#L=i}attached(){}detached(){}async openPostTrainer(){return this.#_()?await this.openByUsage("generic"):{status:i.NotShown}}#_(){const e=Object.values(this.gameHistory).filter((e=>!!e.lastPlayedAt));return!(e.length<3)&&!(e.map((e=>e.playDuration||0)).reduce(((e,t)=>e+t),0)<1800)}async openByUsage(e,t=!1){const s=this.polls.filter((t=>t.usage===e)).filter((e=>t||this.#L.isApplicable(e))).filter((e=>t||!this.account.answeredPolls.includes(e.id)));if(0===s.length)return{status:i.NotShown};const a=s[Math.floor(Math.random()*s.length)];return await this.open(a)}async open(e,t=!1){let s="",a="";"subscription_cancelation"===e.usage&&(s="poll_dialog.cancel_my_subscription",a="poll_dialog.i_changed_my_mind"),"generic"===e.usage&&(s="poll_dialog.submit");const n={title:l.F2.literal(e.title),message:e.message?l.F2.literal(e.message):void 0,options:e.options.map(l.F2.literal),multiselect:e.multiselect,customSelection:e.customSelection?{...e.customSelection,placeholder:e.customSelection.placeholder?l.F2.literal(e.customSelection.placeholder):void 0}:void 0,detailsField:e.detailsField?{...e.detailsField,placeholder:e.detailsField.placeholder?l.F2.literal(e.detailsField.placeholder):""}:void 0,submitLabel:s,cancelLabel:a},o=await this.#A.open(n);if(o.wasCancelled)return{status:i.Canceled};const r=o.output;if(e.id&&!t)try{const t=await this.#c.respondToPoll(e.id,r.selections,r.customSelection,r.details);await this.#n.dispatch(d.Ui,t)}catch{}return{status:i.Completed,selections:r.selections,customSelection:r.customSelection,additionalInfo:r.details}}};p=(0,a.Cg)([(0,h.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,h.$t)((e=>e.account)),polls:(0,h.$t)((e=>e.catalog?.polls)),gameHistory:(0,h.$t)((e=>e.gameHistory))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[c.SelectionDialogService,o.il,r.x,u.u])],p)},56705:(e,t,s)=>{s.d(t,{Q:()=>p,o:()=>m});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s(20770),r=s(83802),c=s(21795),l=s(54995),h=s(70236),d=s(48881),u=s(38777);let p=class{#S;#n;#D;#k;#E;constructor(e,t,s){this.#E=new u._M,this.#S=e,this.#n=t,this.#D=s}attached(){this.#k=(new u.Vd).push(this.#S.onTrainerActivated((e=>this.#V(e)))),this.#F()}detached(){this.#k?.dispose(),this.#k=null}#V(e){const t=e.getMetadata(r.vO).info,s=m(t.blueprint),i=this.enabledForGame(t.gameId);if(!s)return;const a=this.gamePreferences[t.gameId]?.saveCheats?.trainerState;i&&a&&t.blueprint.cheats.forEach((async s=>{if(!(t?.brokenCheatUuids??[]).includes(s.uuid)&&this.cheatSupportsActivateOnLoad(s)){const t=a[s.target];void 0!==t&&await e.setValue(s.target,t,5)}})),e.onValueSet((e=>{this.saveValue(t.gameId,e.name,e.value)}))}async saveValue(e,t,s){this.#E.publish("value-saved",{gameId:e,target:t,value:s}),await this.#n.dispatch(d.iC,e,t,s)}onValueSaved(e){return this.#E.subscribe("value-saved",e)}async enable(e,t,s){s??=this.#M(e),await this.#n.dispatch(d.Vz,e,!0,s),this.#D.publish(new c.Ij(t,e,!0))}#M(e){const t=this.#S.trainer;return t&&t.getMetadata(r.vO).info.gameId===e?Object.fromEntries(t.values.entries()):null}async disable(e,t){await this.#n.dispatch(d.Vz,e,!1),this.#D.publish(new c.Ij(t,e,!1))}enableSaveCheatsByDefaultChanged(e,t){!1===e&&!0===t&&this.#n.dispatch(d.Gi,!0)}cheatSupportsActivateOnLoad(e){return(0,h.Lt)(e?.flags,1)}#R(e){return this.gamePreferences&&this.gamePreferences[e]?.saveCheats?.enabled}enabledForGame(e){return!!this.canUse&&(this.#R(e)??this.enableSaveCheatsByDefault)}enabledByDefault(){return this.enableSaveCheatsByDefault}#F(){this.canUse||(this.gamePreferences&&Object.values(this.gamePreferences).some((e=>{if("boolean"==typeof e.saveCheats?.enabled)return!0;const t=e.saveCheats?.trainerState;return t&&Object.keys(t).length>0}))||this.enableSaveCheatsByDefault)&&this.#n.dispatch(d.Gi,!1)}accountChanged(){this.#F()}get canUse(){return this.account&&!!this.account.subscription}};function m(e){return(0,h.Lt)(e?.flags,1)}(0,i.Cg)([(0,n.computedFrom)("account.subscription"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],p.prototype,"canUse",null),p=(0,i.Cg)([(0,l.m6)({setup:"attached",teardown:"detached",selectors:{gamePreferences:(0,l.$t)((e=>e.gamePreferences)),enableSaveCheatsByDefault:(0,l.$t)((e=>e.settings?.enableSaveCheatsByDefault)),account:(0,l.$t)((e=>e.account))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[r.jR,o.il,a.EventAggregator])],p)},68225:(e,t,s)=>{s.d(t,{c:()=>u});var i=s(15215),a=s(95604),n=s("aurelia-event-aggregator"),o=s("aurelia-framework"),r=s(18776),c=s(16953),l=s(19072),h=s(92465),d=s(54995);let u=class{#k;#E;#h;#O;constructor(e,t,s){this.#E=e,this.#h=t,this.#O=s}attached(){this.#$()}detached(){this.#k?.dispose()}#$(){const{applicationId:e,clientToken:t}=c.A.services.datadog;e&&t&&"stable"===this.#h.info.releaseChannel&&(a.L.init({applicationId:e,clientToken:t,site:"datadoghq.com",service:"desktop-app",env:"stable"===this.#h.info.releaseChannel?"production":"debug",version:this.#h.info.version,sessionSampleRate:this.reportRate,sessionReplaySampleRate:0,defaultPrivacyLevel:"mask-user-input",trackViewsManually:!0,sessionPersistence:"local-storage"}),a.L.setGlobalContext({appVersion:this.#h.info.version,appPath:this.#h.info.paths.app,releaseStage:this.#h.info.releaseChannel,locale:this.#h.info.locale,region:this.#h.info.region,osArch:this.#h.info.osArch,osVersion:this.#h.info.osVersion,startedInTray:this.#h.isInTraySinceStartup}),a.L.startView(this.#O.currentInstruction.fragment),this.#k=new h.Vd([this.#E.subscribe("router:navigation:processing",(({instruction:e})=>{a.L.startView(`${e.fragment}`)})),this.#E.subscribe("dialog:open:start",(e=>{a.L.startView(`dialog/${e}`)}))]))}};u=(0,i.Cg)([(0,d.m6)({setup:"attached",teardown:"detached",selectors:{reportRate:(0,d.$t)((e=>e.catalog?.features?.desktop_dd_rum?.reportRate??0))}}),(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[n.EventAggregator,l.s,r.Ix])],u)},78576:(e,t,s)=>{s.d(t,{G:()=>f});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s(20770),r=s(51977),c=s(73592),l=s(67064),h=s("dialogs/email-dialog"),d=s("dialogs/pro-onboarding-dialog"),u=s(16953),p=s(21795),m=s(54995),g=s(70236),y=s(48881),b=s(38777),v=s(78268);let f=class{#k;#E;#G;#x;#j;#N;#H;#U;#n;constructor(e,t,s,i,n,o,r){this.#E=new a.EventAggregator,this.#G=e,this.#x=t,this.#j=s,this.#N=i,this.#H=n,this.#U=o,this.#n=r}attached(){this.#k=new b.Vd([this.#x.subscribe(p.Yb,(()=>this.check())),this.#x.subscribe(p.jP,(()=>this.check()))]).pushEventListener(window,"focus",(()=>this.check())),!this.account.subscription||this.proOnStartup&&!this.#B()||this.trigger()}detached(){this.#k.dispose()}async trigger(){this.#W(),this.account.subscription?.gift||(0,g.Lt)(this.account.flags,2)||await this.#N.open(void 0,!u.A.debug),await this.#j.open({trigger:"post_pro_upgrade",mode:"post-pro-upgrade"}),this.#z()}#W(){this.settings.reduceMotion||this.#G.play({path:"static/animations/fireworks.json"})}check(){this.account.subscription||this.#H.watchFlag(512,1,!0)}openTooltip(){this.#x.publish("open-pro-onboarding-tooltip")}onSuccess(e){return this.#E.subscribe("success",e)}accountChanged(e,t){const s=!t.subscription&&e.subscription,i="active"!==t.subscription?.state&&"active"===e.subscription?.state;(s||i)&&(this.trigger(),this.#E.publish("success"))}#z(){if(this.account.subscription?.gift){const e=this.account.subscription.period;["monthly","yearly"].includes(e)&&this.#U.toast({content:`post_pro_upgrade.${e}_gift_toast_message`,i18nParams:{sender:this.account.subscription.gift.senderName},type:"gift",persist:!0,actions:[{onclick:()=>window.open("wemod://settings/account/billing","_blank"),label:"post_pro_upgrade.view_details"}]})}}#B(){if(this.account?.subscription?.gift){const e=this.account.subscription.startedAt;if(!this.lastGiftOnboardingShown||(0,r.A)(new Date(this.lastGiftOnboardingShown),new Date(e)))return this.#n.dispatch(y.vk,"lastGiftOnboardingShown"),!0}return!1}};f=(0,i.Cg)([(0,n.autoinject)(),(0,m.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,m.$t)((e=>e.account)),settings:(0,m.$t)((e=>e.settings)),proOnStartup:(0,m.$t)((e=>e.flags?.proOnStartup)),lastGiftOnboardingShown:(0,m.$t)((e=>e.timestamps?.lastGiftOnboardingShown))}}),(0,i.Sn)("design:paramtypes",[c.b,a.EventAggregator,d.ProOnboardingDialogService,h.EmailDialogService,v.s,l.l,o.il])],f)},85805:(e,t,s)=>{s.d(t,{e:()=>_,t:()=>i});var i,a=s(15215),n=s(7530),o=s("aurelia-framework"),r=s(20770),c=s(83802),l=s(60692),h=s(48100),d=s(88120),u=s(68539),p=s(62914),m=s(20489),g=s(19072),y=s(20057),b=s("shared/pusher/index"),v=s(54995),f=s(48881),S=s(38777),C=s(27958),w=s(72208),I=s(43050),T=s(96276),P=s(56705),A=s(7892);!function(e){e[e.Disconnected=0]="Disconnected",e[e.Connecting=1]="Connecting",e[e.WaitingForClient=2]="WaitingForClient",e[e.Connected=3]="Connected",e[e.ForceDisconnected=4]="ForceDisconnected"}(i||(i={}));const L=e=>({theme:e.theme,analytics:e.analytics,disableAssistant:e.disableAssistant,reduceMotion:e.reduceMotion});let _=class{#K;#Y;#X;#J;#n;#q;#Z;#l;#d;#h;#Q;#E;#k;#ee;#te;#se;#ie;#ae;#ne;#oe;#re;#ce;#le;#he;#de;constructor(e,t,s,a,n,o,r,c,l,h,d,u){this.status=i.Disconnected,this.isInFreeMobileExperiment=!1,this.#E=new S._M,this.#k=new S.Vd,this.#ae=null,this.#re=[],this.#ce={},this.#le=0,this.#K=e,this.#Y=s,this.#oe=a,this.#X=n,this.#J=o,this.#n=t,this.#q=r,this.#Z=c,this.#l=l,this.#d=h,this.#h=d,this.#Q=u}activate(){this.isInFreeMobileExperiment=!!this.#Q.assignments.get(l.n.MakeMobileAppFree),this.#h.isInTraySinceStartup&&this.#k.push(this.#h.whenVisible((()=>this.#ue(this.remoteChannel)))),this.#k.push(this.#E).push(this.#oe.onCheatStatesChanged((e=>{e.gameId===this.#se&&this.#pe()}))).push(this.#d.onLocaleChanged((()=>this.#me())));const e=this.#n.state.pipe((0,h.T)((e=>e.settings)),(0,d.F)()).subscribe((e=>this.#ge(e))),t=this.#n.state.pipe((0,h.T)((e=>e.account?.remoteChannel)),(0,d.F)()).subscribe((e=>this.#ue(e??""))),s=this.#n.state.pipe((0,h.T)((e=>e.account?.subscription?.remoteChannel)),(0,d.F)()).subscribe((e=>this.#ue(e??""))),i=this.#n.state.pipe((0,h.T)((e=>e.trainerNotesRead)),(0,d.F)()).subscribe((e=>this.#ye(e))),a=this.#J.onInstallationsChanged((()=>this.#be()));this.#k.push((0,S.nm)((()=>{e.unsubscribe(),a.dispose(),i.unsubscribe(),t.unsubscribe(),s.unsubscribe()}))),this.#me()}deactivate(){this.#k.dispose()}onStatusChanged(e){return this.#E.subscribe("status",e)}get remoteChannel(){return this.isInFreeMobileExperiment?this.remoteChannelFreeMobileApp??"":this.remoteChannelSubscriber??""}#ge(e){this.#he=e.theme,this.#ee&&(this.#ee.send("client-theme-id",this.#he),this.#ee.send("client-settings",L(e)))}#me(){this.#de=this.#d.getEffectiveLocale().toString(),this.#ee&&this.#ee.send("client-language",this.#de)}#ye(e){this.#ce=e,this.#be()}#ve(){this.#ne&&(this.#ne.dispose(),this.#ne=null),this.#te=Date.now().toString(),this.#ie=null,this.#re=[],this.#ae=null}#be(){if(this.status===i.Connected){let e,t=!1,s=this.#ae?.getMetadata(c.vO)?.gameVersion??null,i=!1;const a=this.#ce[this.#ie??""]||null;this.#se&&(e=this.#J.getPreferredInstallationInfo(this.#se),e.app&&(t=!0,s??=e.version??null,i="number"==typeof e.version&&!this.#re.includes(e.version))),this.#ee?.send("client-state",{instanceId:this.#te,trainerId:this.#ie,trainerLoading:this.#ae?.isLoading(),gameInstalled:t,gameVersion:s,needsCompatibilityWarning:i,values:this.#fe(),themeId:this.#he,settings:L(this.settings),language:this.#de,accountUuid:this.account.uuid,notesReadHash:a})}}#fe(){return this.#ae?.isActive()?Object.fromEntries(this.#ae.values.entries()):null}async#ue(e){if(!e)return;if(this.#h.isInTraySinceStartup)return;if(this.#ee&&this.#ee.name===e)return;this.#ee?.dispose(),this.#ee=null;const t=++this.#le;e?(this.#Se(i.Connecting),this.#ve(),await this.#Ce(e,t),this.#we()):this.#Se(i.Disconnected)}async#Ce(e,t){try{const s=await this.#K.joinPresence(e);t!==this.#le?s.close():(s.onMemberAdded((e=>{"infinity"===e.info.type?this.#Ie():this.#Te()})),s.onMemberRemoved((()=>this.#Te())),s.listen("client-state",(()=>this.#be())),s.listen("client-cheat-states",(e=>this.#pe(e))),s.listen("client-value-changed",(e=>this.#Pe(e))),s.listen("client-saved-value-changed",(e=>this.#Ae(e))),s.listen("client-cheat-insructions-read",(e=>this.#Le(e))),s.listen("client-launch-trainer",(e=>this.#_e(e))),s.listen("client-trainer-notes-read",(e=>this.#De(e))),s.listen("client-playable-games",(()=>this.#we())),s.listen("client-navigate-to-route",(e=>this.#ke(e))),s.listen("client-pin-mod",(e=>this.#Ee(e))),s.listen("client-set-mod-timer",(e=>this.#Ve(e))),s.listen("client-dismiss-mod-timer-message",(e=>this.#Fe(e))),s.listen("client-mod-timer-messages-dismissed",(()=>this.#Me())),this.#ee=s,this.#Te())}catch{setTimeout((()=>{t===this.#le&&this.#Ce(e,t)}),5e3)}}disconnect(){this.#ee&&(this.#ee.send("client-disconnect"),this.#Te())}#Te(){const e=this.#ee?.members.some((e=>"remote"===e.info.type))?i.Connected:i.WaitingForClient;this.#Se(e)}#Se(e){const t=this.status;this.status=e,e!==t&&(e===i.Connected&&this.#be(),this.#E.publish("status",this.status))}#Pe(e){this.#ae&&e?.instanceId===this.#te&&(this.#ae.isActive()?this.#ae.setValue(e.name,e.value,3,e.cheatId):this.#be())}#Ae(e){e?.instanceId===this.#te&&this.#se&&this.#X.saveValue(this.#se,e.name,e.value)}setCurrentTrainer(e,t=null){const s=e?.trainerId||null,i=(s?e?.gameId:null)||null,a=(s?e?.supportedVersions:null)||[];if(s===this.#ie&&t===this.#ae)return;if(this.#ve(),this.#ne&&(this.#ne.dispose(),this.#ne=null),this.#ie=s,this.#se=i,this.#re=[...a],!s)return this.#ae=null,void this.#be();if(this.#ae=t,null===t)return void this.#be();const n=new S.Vd;t.isActive()?this.#Re(t,n):n.push(t.onActivated((()=>this.#Re(t,n)))),n.push(t.onEnded((()=>{this.#be()}))),n.push(t.onStateChanged((()=>this.#Oe()))),this.#ne=n}sendGamePlayed(e,t,s){this.#ee?.send("client-game-played",{gameId:e,playedAt:t,duration:s})}#pe(e){this.status===i.Connected&&(this.#ee?.send("client-cheat-states",this.#oe.cheatStates),this.#$e(e?.remoteTime))}#Re(e,t){t.push(e.onValueSet((e=>{this.status===i.Connected&&3!==e.source&&this.#ee?.send("client-value-changed",{instanceId:this.#te,name:e.name,value:e.value,cheatId:e.cheatId})}))),this.#be()}#Le(e){e&&this.#se&&this.#Y.markRead("remote",this.#se,e.cheatId,e.instructions)}#_e(e){this.#E.publish("launch-trainer",e?.trainerId)}#De(e){e&&this.#n.dispatch(f.ah,e.trainerId,e.notesHash)}onLaunchTrainer(e){return this.#E.subscribe("launch-trainer",e)}#Oe(){this.#ae?.state===m.FX.AcquiringBinary&&this.#be()}#ke(e){e&&this.#q.router.navigateToRoute(e.routeName,e.params,{replace:!0})}#Ee(e){if(e){const t=e.pin?"pin_mod_click":"unpin_mod_click",s=e.pin?[...this.pinnedMods?.[e.gameId]||[],e.mod]:this.pinnedMods?.[e.gameId]?.filter((t=>t.uuid!==e.mod.uuid))||[];this.#n.dispatch(f.oz,e.gameId,s),this.#l.event(t,{source:"remote",gameId:e.gameId,trainerId:e.trainerId,modId:e.mod.uuid},p.Io)}}#Ve(e){const t=e?.config;if(t){const s=t.cancel?"mod_timer_cancel_click":"mod_timer_start_click",i=this.#ae?.getMetadata(c.vO);this.#$e(e?.remoteTime),this.#n.dispatch(f.$Z,t),e?.firstCall&&this.#l.event(s,{duration:"loop"===t.type?String(t.start):String(t.duration),durationLoop:String(t.end),titleId:i?.info.titleId,modId:t.modId,modType:t.modType,type:t.type,source:"remote"},p.Io)}}#$e(e){if(e){const t=Date.now(),s=t-e;this.#ee?.send("client-time-sync",{latency:s,localTime:t})}}#Fe(e){e&&this.#n.dispatch(f.Yt,e.timerType)}#Me(){this.status===i.Connected&&this.#ee?.send("client-mod-timer-messages-dismissed",this.modTimerMessagesDismissed)}#we(){this.status===i.Connected&&this.#ee?.send("client-playable-games",this.#Ge().slice(0,1e3).join(","))}#Ge(){const e=this.#Z.getFilteredFeed(I.f1,{}),t=e.items.map((e=>e.gameId)).filter((e=>!!e));return e.dispose(),t}installedGameVersionsChanged(){this.#we()}catalogChanged(){this.#we()}gameHistoryChanged(){this.#we()}modTimerMessagesDismissedChanged(){this.#Me()}reconnect(){this.remoteChannel&&this.#ue(this.remoteChannel)}#Ie(){this.#l.event("remote_force_disconnect",{},p.Io),this.#K.disconnect(),this.#Se(i.ForceDisconnected),this.#ve()}};(0,a.Cg)([(0,n.Kj)("isInFreeMobileExperiment","remoteChannelSubscriber","remoteChannelFreeMobileApp"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],_.prototype,"remoteChannel",null),_=(0,a.Cg)([(0,o.autoinject)(),(0,v.m6)({setup:"activate",teardown:"deactivate",selectors:{account:(0,v.$t)((e=>e.account)),installedGameVersions:(0,v.$t)((e=>e.installedGameVersions)),gameHistory:(0,v.$t)((e=>e.gameHistory)),catalog:(0,v.$t)((e=>e.catalog)),remoteChannelSubscriber:(0,v.$t)((e=>e.account?.subscription?.remoteChannel)),remoteChannelFreeMobileApp:(0,v.$t)((e=>e.account?.remoteChannel)),settings:(0,v.$t)((e=>e.settings)),pinnedMods:(0,v.$t)((e=>e.pinnedMods)),modTimerMessagesDismissed:(0,v.$t)((e=>e.modTimerMessagesDismissed))}}),(0,a.Sn)("design:paramtypes",[b.Pusher,r.il,w.u,A.p,P.Q,T.T,C.L,I.Y2,p.j0,y.F2,g.s,u.z])],_)},96276:(e,t,s)=>{s.d(t,{T:()=>c});var i=s(15215),a=s("aurelia-event-aggregator"),n=s("aurelia-framework"),o=s(41882),r=s(54995);let c=class{constructor(){this.#E=new a.EventAggregator}#E;activate(){}deactivate(){}getPreferredInstallationInfo(e){const t={app:null,version:null};if(!e)return t;const s=this.installedGameVersions[e];if(!s?.length)return t;for(const e of s){const s=this.installedApps[e.correlationId];if(s&&(t.app=s,t.version=e.version,s.platform===o.u))break}return t}installedAppsChanged(){this.#E.publish("installations-changed")}installedGameVersionsChanged(){this.#E.publish("installations-changed")}onInstallationsChanged(e){return this.#E.subscribe("installations-changed",e)}};c=(0,i.Cg)([(0,n.autoinject)(),(0,r.m6)({setup:"activate",teardown:"deactivate",selectors:{installedGameVersions:(0,r.$t)((e=>e.installedGameVersions)),installedApps:(0,r.$t)((e=>e.installedApps))}})],c)},97170:(e,t,s)=>{s.d(t,{V:()=>I});var i=s(15215),a=s(16928),n=s("aurelia-framework"),o=s(20770),r=s(55648),c=s(81810),l=s(83802),h=s("dialogs/time-limit-reached-post-game-dialog"),d=s(19072),u=s(20057),p=s(54995),m=s(49442),g=s(14046),y=s(48881),b=s(38777),v=s(50643),f=s(62914),S=s(43570),C=s(40930);const w="secondsPlayedToday:";let I=class{#xe;#je;#n;#S;#Ne;#h;#l;#He;#d;constructor(e,t,s,i,a,n,o){this.dailyPlayLimitSeconds=C.xH,this.dailyPlayLimitHours=C.xH/60/60,this.#n=e,this.#S=t,this.#Ne=s,this.#h=i,this.#l=a,this.#He=n,this.#d=o}attached(){this.#xe=this.#S.onTrainerActivated((e=>this.#V(e))),this.#je=(0,b.SO)((()=>this.#Ue()),C.EW),this.#Be().then((()=>this.#Ue()))}detached(){this.#xe?.dispose(),this.#xe=null,this.#je?.dispose(),this.#je=null}async#V(e){if(this.subscription)return;if(!this.isEnabled||e.isEnding())return;const t=e.getMetadata(l.vO).info.gameId;let s=this.dailyPlayLimitSeconds-this.secondsPlayedToday(t);if(s>0){let i=new Date;const a=setInterval((async()=>{if(!this.isEnabled)return;const e=await this.#We(t,i);i=new Date,s-=e,s<=0&&(this.#S.trainer?.addMetadata(new C.XO(!0)),this.#S.endTrainer())}),C.FE);e.onEnded((()=>{this.isEnabled&&this.#We(t,i),clearInterval(a)}))}}async#We(e,t){const s=new Date,i=Math.round((0,g.bu)(s,t)/1e3);return i>0?(await this.#n.dispatch(y.Ew,this.#ze(e),i),i):0}get isEnabled(){return!1}isOverDailyLimit(e){return this.secondsPlayedToday(e)>=this.dailyPlayLimitSeconds}isOverAnyDailyLimit(){return this.getCounterEntries().some((([e,t])=>t>=this.dailyPlayLimitSeconds))}#Ke(){return this.getCounterEntries().some((([e,t])=>t>0))}countersChanged(e,t){this.getCounterEntries().forEach((([s])=>{const i=this.#Ye(s);this.#Xe(i,e[s],t[s])}))}#Xe(e,t,s){const i=(s||0)<this.dailyPlayLimitSeconds,a=t<this.dailyPlayLimitSeconds;i&&!a&&this.#l.event("time_limit_exceeded",{type:"per_game_daily",limitHours:this.dailyPlayLimitHours},f.Io),!i&&a&&this.#l.event("time_limit_reset",{type:"per_game_daily",limitHours:this.dailyPlayLimitHours},f.Io)}async#Je(){const e=a.join(this.#h.info.paths.assets,"time-limit-toast-icon-1-hour.png"),t="wemod://pro?trigger=time_limit_exceeded_per_game_notification",s=new v.c(this.#d.getValue("time_limit_enforcer.times_up")).addText(this.#d.getValue("time_limit_enforcer.upgrade_to_pro_to_play_beyond_your_daily_1_hour_limit_per_game")).setActivationType("protocol").setLaunchString(t).addImage(e,"appLogoOverride").setScenario("alarm").setAudio({silent:!0}).addAction({content:"Upgrade to Pro",arguments:t,activationType:"protocol"}).toXml();this.#He.playAtFixedVolume(S.A.Error,75),await this.#h.showToast(s)&&this.#l.event("time_limit_exceeded_notification_show",{type:"per_game_daily",limitHours:this.dailyPlayLimitHours},f.Io)}async triggerPostTrainerEvents(e){const t=this.isEnabled&&this.isOverDailyLimit(e);return t&&(this.#h.flashWindow().catch(m.Y),this.#Je().catch(m.Y),await this.#Ne.open({perGame:!0,limitHours:this.dailyPlayLimitHours})),t}async#Be(){this.lastResetDailyPlayLimit||await this.resetAllTimeLimits()}async#Ue(){if(!this.isEnabled)return;const e=new Date,t=(0,r.A)().setHours(C.rf,C.px),s=(0,g.dS)(e,t),i=(0,c.A)(e,new Date(this.lastResetDailyPlayLimit));s&&this.#Ke()&&!i&&this.resetAllTimeLimits()}async resetAllTimeLimits(){this.getCounterEntries().forEach((([e])=>this.#n.dispatch(y.TU,e,0))),await this.#n.dispatch(y.vk,"lastResetDailyPlayLimit")}exceedAllTimeLimits(){this.getCounterEntries().forEach((([e])=>this.#n.dispatch(y.TU,e,this.dailyPlayLimitSeconds)))}secondsPlayedToday(e){return this.counters[this.#ze(e)]||0}getCounterEntries(){return Object.entries(this.counters).filter((([e])=>e.startsWith(w)))}#ze(e){return`${w}${e}`}#Ye(e){return e.replace(w,"")}};(0,i.Cg)([(0,n.computedFrom)("subscription"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],I.prototype,"isEnabled",null),I=(0,i.Cg)([(0,p.m6)({setup:"attached",teardown:"detached",selectors:{subscription:(0,p.$t)((e=>e.account?.subscription)),counters:(0,p.$t)((e=>e.counters)),lastResetDailyPlayLimit:(0,p.$t)((e=>e.timestamps?.lastResetDailyPlayLimit))}}),(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[o.il,l.jR,h.TimeLimitReachedPostGameDialogService,d.s,f.j0,S.L,u.F2])],I)}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3710],{98119:(e,a,r)=>{r.d(a,{OX:()=>o,TQ:()=>t,oH:()=>s,uA:()=>i});const t=e=>!!e?.rewardOffers?.some((e=>s(e))),i=e=>!!e?.rewardOffers?.some((e=>!!e.redemptionCode)),s=e=>{const a=new Date(e.startsAt).getTime(),r=new Date(e.endsAt).getTime(),t=Date.now();return e.active&&t>=a&&t<=r},o=e=>!e.redemptionCode&&!!e.rewardKey},"resources/value-converters/flags":(e,a,r)=>{r.r(a),r.d(a,{FlagIconSvgValueConverter:()=>b,flags:()=>v});var t=r(34480),i=r(87680),s=r(43627),o=r(73773),d=r(35552),n=r(22460),l=r(77091),c=r(1081),m=r(82419),p=r(86666),g=r(21431),w=r(10384),f=r(89384),h=r(73474),u=r(41772);const v=new Map([["en-US",t],["pt-BR",i],["zh-CN",s],["de-DE",o],["es-ES",d],["fr-FR",n],["hi-IN",c],["id-ID",l],["it-IT",m],["ja-JP",p],["ko-KR",g],["pl-PL",w],["th-TH",f],["tr-TR",h]]);class b{toView(e){return u.d.includes(e)&&v.get(e)||""}}},"resources/value-converters/game":(e,a,r)=>{r.r(a),r.d(a,{GameFlagsValueConverter:()=>s});var t=r(24008),i=r(70236);class s{toView(e,...a){return(0,i.br)("number"==typeof e?e:e.flags,a.map((e=>t.rT[e])))}}},"resources/value-converters/number":(e,a,r)=>{r.r(a),r.d(a,{NumberValueConverter:()=>t});class t{fromView(e){return Number(e)}}},"rewards/resources/elements/game-pass-reward":(e,a,r)=>{r.r(a),r.d(a,{GAME_PASS_REWARD_NAME:()=>h,GamePassReward:()=>u});var t=r(15215),i=r(68663),s=r(10699),o=r(62914),d=r(67064),n=r("aurelia-framework"),l=r(20770),c=r("dialogs/reward-claim-code-dialog"),m=r(19072),p=r(98119),g=r(48881),w=r(20057),f=r(54995);const h="gamepass_jan_2025";let u=class{#e;#a;#r;#t;#i;#s;#o;#d;constructor(e,a,r,t,i,s,o,d){this.detailsOpen=!1,this.redeemUrl="https://redeem.microsoft.com",this.redeemName="redeem.microsoft.com",this.termsUrl="https://xbox.com/subscriptionterms",this.termsName="xbox.com/subscriptionterms",this.#a=e,this.#e=a,this.#r=r,this.#t=t,this.#i=i,this.#s=s,this.#o=o,this.#d=d}handleDetailsClick(){this.detailsOpen=!this.detailsOpen}async handleRedeemCode(e){this.#s.copyText(e);const a=`${this.redeemUrl}?mstoken=${e}`;window.open(a,"_blank")}get gamePassRewardOffer(){return this.account?.rewardOffers?.find((e=>e.rewardKey===h&&(e.redemptionCode||(0,p.oH)(e))))}get hasGamePassReward(){return!!this.gamePassRewardOffer}get hasClaimedGamePassReward(){return!!this.gamePassRewardOffer?.redemptionCode}get datesActive(){return this.gamePassRewardOffer?{start:this.#r.formatDateTime(this.gamePassRewardOffer.startsAt,{dateStyle:"medium"}),end:this.#r.formatDateTime(this.gamePassRewardOffer.endsAt,{dateStyle:"medium"}),claimBy:this.#r.formatDateTime(this.gamePassRewardOffer.claimBy??this.gamePassRewardOffer.endsAt,{dateStyle:"medium"})}:{start:"",end:"",claimBy:""}}showRedemptionDialog(e){this.#e.open({reward:h,headerCopy:this.#r.getValue("rewards.claim_gamepass_header"),descriptionCopy:this.#r.getValue("rewards.claim_gamepass_description"),code:e,redeemCallback:this.handleRedeemCode.bind(this)})}async claimCodeClicked(){if(!this.gamePassRewardOffer)return;this.#a.event("reward_gamepass_code_claim_click",{},o.Io);const e=this.gamePassRewardOffer?.redemptionCode;if(e)this.showRedemptionDialog(e);else try{const e=await this.#t.claimRewardOffer(this.gamePassRewardOffer.rewardKey);if(!e?.redemptionCode)return void this.handleResponseError();this.#o.dispatch(g.T9,h),this.showRedemptionDialog(e.redemptionCode),await this.#d.refreshAccount()}catch{return void this.handleResponseError()}}handleResponseError(){this.#a.event("reward_gamepass_code_claim_error",{},o.Io),this.#i.toast({content:this.#r.getValue("rewards.generic_claim_error"),type:"alert"})}};(0,t.Cg)([(0,n.computedFrom)("account"),(0,t.Sn)("design:type",Object),(0,t.Sn)("design:paramtypes",[])],u.prototype,"gamePassRewardOffer",null),(0,t.Cg)([(0,n.computedFrom)("gamePassRewardOffer"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],u.prototype,"hasGamePassReward",null),(0,t.Cg)([(0,n.computedFrom)("gamePassRewardOffer"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],u.prototype,"hasClaimedGamePassReward",null),(0,t.Cg)([(0,n.computedFrom)("gamePassRewardOffer"),(0,t.Sn)("design:type",Object),(0,t.Sn)("design:paramtypes",[])],u.prototype,"datesActive",null),u=(0,t.Cg)([(0,f.m6)({selectors:{account:(0,f.$t)((e=>e.account))}}),(0,n.autoinject)(),(0,t.Sn)("design:paramtypes",[o.j0,c.RewardClaimCodeDialogService,w.F2,i.x,d.l,m.s,l.il,s.G])],u)},"rewards/resources/elements/game-pass-reward.html":(e,a,r)=>{r.r(a),r.d(a,{default:()=>d});var t=r(14385),i=r.n(t),s=new URL(r(18285),r.b),o=new URL(r(14030),r.b);const d='<template> <require from="./game-pass-reward.scss"></require> <div class="game-pass-reward" if.bind="hasGamePassReward"> <div class="reward-header"> <div class="wemod"> <div class="wemod-logo"> <inline-svg src="'+i()(s)+'"></inline-svg> </div> <span innerhtml.bind="\'rewards.wemod_pro\' | i18n | markdown"></span> </div> <div class="divider"></div> <div class="game-pass"> <img class="xbox-logo" alt="${\'rewards.pc_game_pass\' | i18n}" src="'+i()(o)+'"> </div> </div> <div class="reward-info"> <span class="sub-heading">${\'rewards.congrats_pro_member\' | i18n}</span> <div class="content"> <span innerhtml.bind="\'rewards.you_get_one_month\' | i18n : {start: datesActive.start, end: datesActive.end} | markdown"></span> <a click.delegate="handleDetailsClick()">${detailsOpen ? \'rewards.hide_details\' : \'rewards.more_details\' | i18n}</a> </div> <div class="details-section ${detailsOpen ? \'open\' : \'\'}" innerhtml.bind="\'rewards.gamepass_offer_details\' | i18n : {redeemUrl, redeemName, termsUrl, termsName, start: datesActive.start, end: datesActive.end, redemptionDate: datesActive.claimBy} | markdown:false"></div> <button class="claim-button" click.delegate="claimCodeClicked()"> ${hasClaimedGamePassReward ? \'rewards.view_code\' : \'rewards.claim_now\' | i18n} </button> </div> <div class="background"> <svg width="100%" height="100%" viewBox="0 0 752 208" fill="none" xmlns="http://www.w3.org/2000/svg"> <g opacity="0.3"> <path fill-rule="evenodd" clip-rule="evenodd" d="M865.615 -27.6617L882.165 -176.609L733.22 14.8922H813.603L797.053 163.84L945.998 -27.6617L865.615 -27.6617Z" fill="white"/> </g> <g opacity="0.1"> <path fill-rule="evenodd" clip-rule="evenodd" d="M240.614 16.4893L248.889 -57.9844L174.416 37.7666H214.608L206.333 112.24L280.805 16.4893L240.614 16.4893Z" fill="white"/> </g> <g opacity="0.1"> <path fill-rule="evenodd" clip-rule="evenodd" d="M475.809 3.16708L479.946 -34.0703L442.71 13.8046H462.806L458.669 51.042L495.905 3.16708L475.809 3.16708Z" fill="white"/> </g> <g opacity="0.1"> <path fill-rule="evenodd" clip-rule="evenodd" d="M27.4946 129.704L48.1817 -56.4805L-138 182.897H-37.5206L-58.2078 369.081L127.974 129.704L27.4946 129.704Z" fill="white"/> </g> <g opacity="0.1"> <path fill-rule="evenodd" clip-rule="evenodd" d="M607.413 120.01L617.757 26.918L524.666 146.607H574.906L564.562 239.699L657.653 120.01L607.413 120.01Z" fill="white"/> </g> </svg> </div> </div> </template> '},"rewards/resources/elements/game-pass-reward.scss":(e,a,r)=>{r.r(a),r.d(a,{default:()=>d});var t=r(31601),i=r.n(t),s=r(76314),o=r.n(s)()(i());o.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.game-pass-reward{position:relative;overflow:hidden;display:flex;flex-direction:column;gap:16px;width:100%;padding:40px 0px;border-radius:16px;justify-content:center;align-items:center;color:#fff;text-align:center;background:radial-gradient(100% 697.62% at 100% 0%, rgba(201, 203, 206, 0.5) 0%, rgba(146, 148, 151, 0.5) 22%, rgba(98, 100, 103, 0.5) 40.5%, rgba(68, 70, 73, 0.5) 63%, rgba(51, 53, 56, 0.5) 83.89%, rgba(30, 32, 35, 0.5) 100%) rgba(255,255,255,.025)}.game-pass-reward .background{position:absolute;top:0;left:0;width:100%;height:100%;z-index:-1}.game-pass-reward .reward-header{display:flex;gap:16px;align-items:center}.game-pass-reward .reward-header .wemod{color:#fff;font-size:28px;font-style:normal;font-weight:600}.game-pass-reward .reward-header .wemod strong{text-transform:uppercase;font-style:italic;font-weight:900;line-height:100%;letter-spacing:-1.527px}.game-pass-reward .reward-header .wemod svg{display:flex;height:24px;width:100%}.game-pass-reward .reward-header .wemod svg *{fill-opacity:1}.game-pass-reward .reward-header .xbox-logo{height:28px}.game-pass-reward .reward-header .wemod,.game-pass-reward .reward-header .game-pass{display:flex;align-items:center;gap:12px}.game-pass-reward .reward-header .divider{width:1px;height:24px;background-color:rgba(255,255,255,.5)}.game-pass-reward .reward-info{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;display:flex;flex-direction:column;align-items:center;gap:8px;width:464px}.game-pass-reward .reward-info .sub-heading{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px}.game-pass-reward .reward-info .content{text-align:center}.game-pass-reward .reward-info .content a{color:var(--theme--highlight)}.game-pass-reward .reward-info .content a:hover{text-decoration:underline}.game-pass-reward .reward-info .details-section{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px;text-align:left;max-height:0;overflow:hidden;opacity:0;transform:translateY(-10px);transition:all .3s cubic-bezier(0.4, 0, 0.2, 1)}.game-pass-reward .reward-info .details-section a{color:var(--theme--highlight)}.game-pass-reward .reward-info .details-section a:hover{text-decoration:underline}.game-pass-reward .reward-info .details-section.open{max-height:500px;opacity:1;transform:translateY(0)}.game-pass-reward .reward-info .claim-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);background:#fff;color:#000;margin-top:8px;width:fit-content}.game-pass-reward .reward-info .claim-button,.game-pass-reward .reward-info .claim-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .game-pass-reward .reward-info .claim-button{border:1px solid #fff}}.game-pass-reward .reward-info .claim-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.game-pass-reward .reward-info .claim-button>*:first-child{padding-left:0}.game-pass-reward .reward-info .claim-button>*:last-child{padding-right:0}.game-pass-reward .reward-info .claim-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .game-pass-reward .reward-info .claim-button svg *{fill:CanvasText}}.game-pass-reward .reward-info .claim-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .game-pass-reward .reward-info .claim-button svg{opacity:1}}.game-pass-reward .reward-info .claim-button img{height:50%}.game-pass-reward .reward-info .claim-button:disabled{opacity:.3}.game-pass-reward .reward-info .claim-button:disabled,.game-pass-reward .reward-info .claim-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.game-pass-reward .reward-info .claim-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.game-pass-reward .reward-info .claim-button:not(:disabled):hover svg{opacity:1}}.game-pass-reward .reward-info .claim-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.game-pass-reward .reward-info .claim-button:not(:disabled):hover{background:rgba(255,255,255,.3)}}',""]);const d=o},"rewards/rewards":(e,a,r)=>{r.r(a),r.d(a,{Rewards:()=>o});var t=r(15215),i=r("aurelia-framework"),s=r(62914);let o=class{#a;constructor(e){this.#a=e}attached(){this.#a.screenView({name:"Rewards",class:"Rewards"})}};o=(0,t.Cg)([(0,i.autoinject)(),(0,t.Sn)("design:paramtypes",[s.j0])],o)},"rewards/rewards.html":(e,a,r)=>{r.r(a),r.d(a,{default:()=>t});const t='<template> <require from="./rewards.scss"></require> <require from="./resources/elements/game-pass-reward"></require> <div class="rewards-page"> <h1 class="title">Rewards</h1> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" overflow-fade="vertical"> <div class="rewards"> <game-pass-reward></game-pass-reward> </div> </div> </div> </div> </template> '},"rewards/rewards.scss":(e,a,r)=>{r.r(a),r.d(a,{default:()=>d});var t=r(31601),i=r.n(t),s=r(76314),o=r.n(s)()(i());o.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.rewards-page{display:flex;flex-direction:column;width:100%;padding:24px}.rewards-page .title{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;padding-top:4px}.theme-default .rewards-page .title{color:#fff}.theme-purple-pro .rewards-page .title{color:#fff}.theme-green-pro .rewards-page .title{color:#fff}.theme-orange-pro .rewards-page .title{color:#fff}.theme-pro .rewards-page .title{color:#fff}.rewards-page .rewards{display:flex;flex-direction:column;gap:12px;width:100%;max-width:1920px}',""]);const d=o}}]);
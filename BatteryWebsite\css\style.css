/* 全局样式和重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* 主色调 */
    --primary-color: #00d4ff;
    --secondary-color: #0099cc;
    --accent-color: #ff6b35;
    --success-color: #00ff88;

    /* 背景色 */
    --bg-primary: #0a0a0a;
    --bg-secondary: #1a1a1a;
    --bg-tertiary: #2a2a2a;

    /* 文字色 */
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-muted: #888888;

    /* 渐变 */
    --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    --gradient-dark: linear-gradient(135deg, #1a1a1a 0%, #0a0a0a 100%);

    /* 阴影 */
    --shadow-light: 0 4px 20px rgba(0, 212, 255, 0.3);
    --shadow-dark: 0 8px 40px rgba(0, 0, 0, 0.5);

    /* 字体 */
    --font-primary: 'Orbitron', monospace;
    --font-secondary: 'Roboto', sans-serif;

    /* 动画 */
    --transition-fast: 0.3s ease;
    --transition-medium: 0.5s ease;
    --transition-slow: 0.8s ease;
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-secondary);
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    overflow-x: hidden;
}

/* 容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 加载动画 */
.loader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.5s ease, visibility 0.5s ease;
}

.loader.hidden {
    opacity: 0;
    visibility: hidden;
}

.battery-loader {
    text-align: center;
}

.battery-body {
    width: 80px;
    height: 40px;
    border: 3px solid var(--primary-color);
    border-radius: 8px;
    position: relative;
    margin: 0 auto 20px;
    overflow: hidden;
}

.battery-level {
    height: 100%;
    background: var(--gradient-primary);
    width: 0%;
    animation: batteryCharge 2s ease-in-out infinite;
    border-radius: 4px;
}

.battery-tip {
    position: absolute;
    right: -8px;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 20px;
    background: var(--primary-color);
    border-radius: 0 3px 3px 0;
}

.charging-text {
    font-family: var(--font-primary);
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes batteryCharge {
    0% { width: 0%; }
    50% { width: 100%; }
    100% { width: 0%; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* 导航栏 */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(20px);
    z-index: 1000;
    transition: var(--transition-fast);
    border-bottom: 1px solid rgba(0, 212, 255, 0.2);
}

.navbar.scrolled {
    background: rgba(10, 10, 10, 0.98);
    box-shadow: var(--shadow-dark);
}

.nav-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 20px;
    max-width: 1200px;
    margin: 0 auto;
}

.nav-logo {
    display: flex;
    align-items: center;
    font-family: var(--font-primary);
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-logo i {
    margin-right: 10px;
    font-size: 28px;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 30px;
}

.nav-link {
    color: var(--text-secondary);
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--gradient-primary);
    transition: var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--primary-color);
    transition: var(--transition-fast);
}

/* 英雄区域 */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.energy-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
    animation: energyFlow 10s ease-in-out infinite;
}

.circuit-pattern {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image:
        linear-gradient(90deg, rgba(0, 212, 255, 0.1) 1px, transparent 1px),
        linear-gradient(rgba(0, 212, 255, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
    animation: circuitMove 20s linear infinite;
}

@keyframes energyFlow {
    0%, 100% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.1) rotate(180deg); }
}

@keyframes circuitMove {
    0% { transform: translate(0, 0); }
    100% { transform: translate(50px, 50px); }
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: center;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-title {
    font-family: var(--font-primary);
    font-size: 4rem;
    font-weight: 900;
    line-height: 1.1;
    margin-bottom: 30px;
}

.title-line {
    display: block;
}

.title-line.highlight {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    margin-bottom: 40px;
    line-height: 1.8;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 50px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition-fast);
    display: inline-flex;
    align-items: center;
    gap: 10px;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(0, 212, 255, 0.5);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-color);
    border: 2px solid var(--primary-color);
}

.btn-secondary:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
}

/* 3D电池可视化 */
.hero-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.battery-3d {
    perspective: 1000px;
}

.battery-container {
    width: 300px;
    height: 400px;
    position: relative;
    transform-style: preserve-3d;
    animation: batteryRotate 10s ease-in-out infinite;
}

.battery-shell {
    width: 100%;
    height: 100%;
    background: linear-gradient(145deg, #2a2a2a, #1a1a1a);
    border-radius: 20px;
    position: relative;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        inset 0 2px 10px rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(0, 212, 255, 0.3);
}

.battery-core {
    position: absolute;
    top: 20px;
    left: 20px;
    right: 20px;
    bottom: 20px;
    background: var(--gradient-primary);
    border-radius: 15px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
}

.energy-flow {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        45deg,
        transparent 30%,
        rgba(255, 255, 255, 0.3) 50%,
        transparent 70%
    );
    animation: energyPulse 2s ease-in-out infinite;
}

.power-indicator {
    font-family: var(--font-primary);
    font-size: 24px;
    font-weight: 700;
    color: white;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.8);
}

.charging-effect {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 30px;
    background: var(--gradient-secondary);
    border-radius: 5px 5px 0 0;
    box-shadow: 0 0 20px rgba(255, 107, 53, 0.8);
}

@keyframes batteryRotate {
    0%, 100% { transform: rotateY(0deg) rotateX(0deg); }
    25% { transform: rotateY(90deg) rotateX(10deg); }
    50% { transform: rotateY(180deg) rotateX(0deg); }
    75% { transform: rotateY(270deg) rotateX(-10deg); }
}

@keyframes energyPulse {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

/* 滚动指示器 */
.scroll-indicator {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    animation: bounce 2s infinite;
}

.scroll-arrow {
    width: 40px;
    height: 40px;
    border: 2px solid var(--primary-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--primary-color);
    cursor: pointer;
    transition: var(--transition-fast);
}

.scroll-arrow:hover {
    background: var(--primary-color);
    color: white;
    transform: scale(1.1);
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

/* 通用区域样式 */
section {
    padding: 100px 0;
    position: relative;
}

.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-title {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 20px;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.8;
}

/* 产品展示区域 */
.products {
    background: var(--bg-secondary);
    position: relative;
}

.products::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 50% 50%, rgba(0, 212, 255, 0.05) 0%, transparent 70%);
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 40px;
    position: relative;
    z-index: 1;
}

.product-card {
    background: var(--bg-tertiary);
    border-radius: 20px;
    overflow: hidden;
    transition: var(--transition-medium);
    border: 1px solid rgba(0, 212, 255, 0.2);
    position: relative;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-light);
    border-color: var(--primary-color);
}

.product-image {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.product-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-medium);
}

.product-card:hover .product-image img {
    transform: scale(1.1);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.8), rgba(0, 153, 204, 0.8));
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-fast);
}

.product-card:hover .product-overlay {
    opacity: 1;
}

.product-specs {
    display: flex;
    flex-direction: column;
    gap: 10px;
    text-align: center;
}

.spec {
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    backdrop-filter: blur(10px);
}

.product-info {
    padding: 30px;
}

.product-info h3 {
    font-family: var(--font-primary);
    font-size: 1.5rem;
    margin-bottom: 15px;
    color: var(--primary-color);
}

.product-info p {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.6;
}

.product-features {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.feature {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    color: var(--text-muted);
}

.feature i {
    color: var(--success-color);
}

/* 技术展示区域 */
.technology {
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

.tech-showcase {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: center;
}

.tech-visual {
    display: flex;
    justify-content: center;
    align-items: center;
}

.battery-cutaway {
    width: 300px;
    height: 400px;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: var(--shadow-dark);
    border: 2px solid var(--primary-color);
}

.layer {
    position: absolute;
    width: 100%;
    height: 33.33%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    transition: var(--transition-fast);
    cursor: pointer;
}

.layer::before {
    content: attr(data-layer);
    position: absolute;
    left: 20px;
    font-size: 14px;
    opacity: 0.8;
}

.cathode {
    top: 0;
    background: linear-gradient(135deg, #ff6b35, #f7931e);
}

.electrolyte {
    top: 33.33%;
    background: linear-gradient(135deg, #00d4ff, #0099cc);
}

.anode {
    top: 66.66%;
    background: linear-gradient(135deg, #00ff88, #00cc6a);
}

.layer-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: radial-gradient(circle, rgba(255, 255, 255, 0.3) 2px, transparent 2px);
    background-size: 20px 20px;
    animation: particleMove 3s linear infinite;
}

.ion-flow {
    position: absolute;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent 0%,
        rgba(255, 255, 255, 0.5) 50%,
        transparent 100%
    );
    animation: ionFlow 2s ease-in-out infinite;
}

.current-flow {
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    width: 40px;
    height: 200px;
    background: linear-gradient(
        0deg,
        transparent 0%,
        var(--primary-color) 50%,
        transparent 100%
    );
    animation: currentPulse 1.5s ease-in-out infinite;
}

@keyframes particleMove {
    0% { transform: translateX(0); }
    100% { transform: translateX(20px); }
}

@keyframes ionFlow {
    0%, 100% { transform: translateX(-100%); }
    50% { transform: translateX(100%); }
}

@keyframes currentPulse {
    0%, 100% { opacity: 0.3; }
    50% { opacity: 1; }
}

.tech-content {
    padding-left: 40px;
}

.tech-features {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.tech-feature {
    display: flex;
    gap: 20px;
    padding: 30px;
    background: var(--bg-secondary);
    border-radius: 15px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: var(--transition-fast);
    cursor: pointer;
}

.tech-feature:hover,
.tech-feature.active {
    border-color: var(--primary-color);
    box-shadow: var(--shadow-light);
    transform: translateX(10px);
}

.feature-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.feature-content h3 {
    font-family: var(--font-primary);
    font-size: 1.3rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.feature-content p {
    color: var(--text-secondary);
    margin-bottom: 15px;
    line-height: 1.6;
}

.feature-content ul {
    list-style: none;
    padding: 0;
}

.feature-content li {
    color: var(--text-muted);
    margin-bottom: 8px;
    position: relative;
    padding-left: 20px;
}

.feature-content li::before {
    content: '⚡';
    position: absolute;
    left: 0;
    color: var(--success-color);
}

/* 创新实验室区域 */
.innovation {
    background: var(--bg-secondary);
    position: relative;
}

.innovation::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at 80% 20%, rgba(255, 107, 53, 0.1) 0%, transparent 50%);
}

.innovation-content {
    position: relative;
    z-index: 1;
}

.lab-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 80px;
}

.stat-item {
    text-align: center;
    padding: 40px 20px;
    background: var(--bg-tertiary);
    border-radius: 15px;
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: var(--transition-fast);
}

.stat-item:hover {
    border-color: var(--primary-color);
    transform: translateY(-5px);
    box-shadow: var(--shadow-light);
}

.stat-number {
    font-family: var(--font-primary);
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 10px;
    display: block;
}

.stat-label {
    color: var(--text-secondary);
    font-size: 1.1rem;
    font-weight: 500;
}

.research-areas {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 40px;
}

.research-card {
    background: var(--bg-tertiary);
    padding: 40px 30px;
    border-radius: 15px;
    text-align: center;
    border: 1px solid rgba(0, 212, 255, 0.2);
    transition: var(--transition-medium);
    position: relative;
    overflow: hidden;
}

.research-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: var(--transition-medium);
}

.research-card:hover::before {
    left: 100%;
}

.research-card:hover {
    border-color: var(--primary-color);
    transform: translateY(-10px);
    box-shadow: var(--shadow-light);
}

.research-icon {
    width: 80px;
    height: 80px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 32px;
    color: white;
    margin: 0 auto 25px;
    position: relative;
    z-index: 1;
}

.research-card h3 {
    font-family: var(--font-primary);
    font-size: 1.4rem;
    margin-bottom: 15px;
    color: var(--primary-color);
    position: relative;
    z-index: 1;
}

.research-card p {
    color: var(--text-secondary);
    line-height: 1.6;
    position: relative;
    z-index: 1;
}

/* 联系我们区域 */
.contact {
    background: var(--bg-primary);
    position: relative;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 40px;
}

.contact-item {
    display: flex;
    gap: 20px;
    align-items: flex-start;
}

.contact-icon {
    width: 60px;
    height: 60px;
    background: var(--gradient-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    flex-shrink: 0;
}

.contact-details h3 {
    font-family: var(--font-primary);
    font-size: 1.2rem;
    margin-bottom: 10px;
    color: var(--primary-color);
}

.contact-details p {
    color: var(--text-secondary);
    line-height: 1.6;
}

.contact-form {
    background: var(--bg-secondary);
    padding: 40px;
    border-radius: 20px;
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.form-group {
    position: relative;
    margin-bottom: 30px;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 15px 20px;
    background: var(--bg-tertiary);
    border: 2px solid rgba(0, 212, 255, 0.2);
    border-radius: 10px;
    color: var(--text-primary);
    font-size: 16px;
    transition: var(--transition-fast);
    resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
}

.form-group label {
    position: absolute;
    top: 15px;
    left: 20px;
    color: var(--text-muted);
    transition: var(--transition-fast);
    pointer-events: none;
}

.form-group input:focus + label,
.form-group textarea:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group textarea:not(:placeholder-shown) + label {
    top: -10px;
    left: 15px;
    font-size: 12px;
    color: var(--primary-color);
    background: var(--bg-secondary);
    padding: 0 5px;
}

/* 页脚 */
.footer {
    background: var(--bg-secondary);
    border-top: 1px solid rgba(0, 212, 255, 0.2);
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-section h3 {
    font-family: var(--font-primary);
    color: var(--primary-color);
    margin-bottom: 20px;
}

.footer-logo {
    display: flex;
    align-items: center;
    font-family: var(--font-primary);
    font-size: 24px;
    font-weight: 700;
    color: var(--primary-color);
    margin-bottom: 20px;
}

.footer-logo i {
    margin-right: 10px;
    font-size: 28px;
}

.footer-section p {
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    gap: 15px;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: var(--bg-tertiary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary);
    transition: var(--transition-fast);
    border: 1px solid rgba(0, 212, 255, 0.2);
}

.social-links a:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: var(--shadow-light);
}

.footer-section ul {
    list-style: none;
}

.footer-section li {
    margin-bottom: 10px;
}

.footer-section a {
    color: var(--text-secondary);
    text-decoration: none;
    transition: var(--transition-fast);
}

.footer-section a:hover {
    color: var(--primary-color);
}

.footer-bottom {
    text-align: center;
    padding-top: 40px;
    border-top: 1px solid rgba(0, 212, 255, 0.2);
    color: var(--text-muted);
}

.footer-bottom a {
    color: var(--primary-color);
    text-decoration: none;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: var(--gradient-primary);
    border: none;
    border-radius: 50%;
    color: white;
    font-size: 20px;
    cursor: pointer;
    transition: var(--transition-fast);
    opacity: 0;
    visibility: hidden;
    z-index: 1000;
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-light);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .hero-content {
        grid-template-columns: 1fr;
        gap: 40px;
        text-align: center;
    }

    .hero-title {
        font-size: 3rem;
    }

    .tech-showcase {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .tech-content {
        padding-left: 0;
    }

    .contact-content {
        grid-template-columns: 1fr;
        gap: 40px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 70px);
        background: rgba(10, 10, 10, 0.98);
        backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 50px;
        transition: var(--transition-fast);
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-buttons {
        justify-content: center;
    }

    .section-title {
        font-size: 2.5rem;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .battery-container {
        width: 250px;
        height: 300px;
    }

    .battery-cutaway {
        width: 250px;
        height: 300px;
    }

    .lab-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .research-areas {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-title {
        font-size: 2rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .btn {
        padding: 12px 24px;
        font-size: 14px;
    }

    .section-title {
        font-size: 2rem;
    }

    .battery-container {
        width: 200px;
        height: 250px;
    }

    .battery-cutaway {
        width: 200px;
        height: 250px;
    }

    .lab-stats {
        grid-template-columns: 1fr;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .contact-form {
        padding: 30px 20px;
    }
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--gradient-primary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

/* 选择文本样式 */
::selection {
    background: var(--primary-color);
    color: white;
}

::-moz-selection {
    background: var(--primary-color);
    color: white;
}

/* 焦点样式 */
*:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* 动画类 */
.fade-in {
    animation: fadeIn 1s ease-in-out;
}

.slide-up {
    animation: slideUp 0.8s ease-out;
}

.scale-in {
    animation: scaleIn 0.6s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 性能优化 */
.hero-background,
.energy-particles,
.circuit-pattern {
    will-change: transform;
}

.battery-container,
.battery-cutaway {
    will-change: transform;
}

/* 打印样式 */
@media print {
    .navbar,
    .back-to-top,
    .hero-background {
        display: none;
    }

    body {
        background: white;
        color: black;
    }

    .section-title,
    .hero-title {
        color: black;
    }
}
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7602],{16953:(e,o,t)=>{t.d(o,{A:()=>l,h:()=>i});const a={...t(57651)};function i(e){const o={...e};delete o.services,Object.assign(a,o),e.services&&Object.assign(a.services,e.services)}const l=a},"dialogs/welcome-mat-dialog":(e,o,t)=>{t.r(o),t.d(o,{WelcomeMatDialog:()=>s,WelcomeMatDialogService:()=>u});var a=t(15215),i=t("aurelia-dialog"),l=t("aurelia-framework"),d=t(62914),r=t(24008),n=t(17275),c=t("shared/i18n/resources/value-converters"),g=t(54995),m=t(70236);let s=class{#e;constructor(e,o,t){this.i18nNumber=e,this.controller=o,this.#e=t}get cheatCount(){return this.catalog?Object.values(this.catalog.games).filter((e=>(0,m.Lt)(e.flags,r.rT.Active))).reduce(((e,o)=>e+(o.trainer?.cheatCount??0)),0):0}get gameCount(){return this.catalog?Object.values(this.catalog.games).filter((e=>(0,m.Lt)(e.flags,r.rT.Active))).length:0}get playedGameCount(){return Object.values(this.gameHistory).filter((e=>!!e.lastPlayedAt)).length}activate(){this.#e.event("welcome_mat_open",{},d.Io)}deactivate(e){let o;o=e.wasCancelled&&!e.output?"escape":e.output,this.#e.event("welcome_mat_dismiss",{method:o},d.Io)}};(0,a.Cg)([(0,l.computedFrom)("catalog"),(0,a.Sn)("design:type",Number),(0,a.Sn)("design:paramtypes",[])],s.prototype,"cheatCount",null),(0,a.Cg)([(0,l.computedFrom)("catalog"),(0,a.Sn)("design:type",Number),(0,a.Sn)("design:paramtypes",[])],s.prototype,"gameCount",null),(0,a.Cg)([(0,l.computedFrom)("gameHistory"),(0,a.Sn)("design:type",Number),(0,a.Sn)("design:paramtypes",[])],s.prototype,"playedGameCount",null),s=(0,a.Cg)([(0,g.m6)({setup:"activate",teardown:"deactivate",selectors:{catalog:(0,g.$t)((e=>e.catalog)),gameHistory:(0,g.$t)((e=>e.gameHistory))}}),(0,l.autoinject)(),(0,a.Sn)("design:paramtypes",[c.I18nNumberValueConverter,i.DialogController,d.j0])],s);let u=class extends n.C{constructor(){super(...arguments),this.viewModelClass="dialogs/welcome-mat-dialog"}};u=(0,a.Cg)([(0,l.autoinject)()],u)},"dialogs/welcome-mat-dialog.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>a});const a="<template> <require from=\"./welcome-mat-dialog.scss\"></require> <require from=\"../shared/resources/elements/close-button\"></require> <require from=\"../resources/elements/pro-cta-label\"></require> <ux-dialog class=\"welcome-mat-dialog module scrollable secondary-gradient-bg\"> <close-button click.delegate=\"controller.close(true, 'close_button')\"></close-button> <div class=\"dialog-scroll-wrapper\"> <ux-dialog-header> <h1 if.bind=\"!playedGameCount\" innerhtml.bind=\"'welcome_mat_dialog.welcome_to_wemod' | i18n | markdown\"></h1> <h1 else innerhtml.bind=\"'welcome_mat_dialog.enjoy_all_of_wemod_with_pro' | i18n | markdown\"></h1> <p>${'welcome_mat_dialog.wemod_is_free_to_use' | i18n}</p> </ux-dialog-header> <ux-dialog-body> <div class=\"free col\"> <h5>${'welcome_mat_dialog.free_membership' | i18n}</h5> <ul> <li> ${'welcome_mat_dialog.$x_cheats_for_$y_games' | i18n:{x: i18nNumber.toView(cheatCount), y: i18nNumber.toView(gameCount)}} </li> <li>${'welcome_mat_dialog.hotkey_cheat_controls' | i18n}</li> <li>${'welcome_mat_dialog.auto_game_and_version_detection' | i18n}</li> <li>${'welcome_mat_dialog.safe_and_virus_free_cheats' | i18n}</li> <li>${'welcome_mat_dialog.discord_community_access' | i18n}</li> </ul> <button click.delegate=\"controller.close(true, 'continue_button')\"> ${'welcome_mat_dialog.continue_with_free_membership' | i18n} </button> </div> <div class=\"pro col\"> <h4 innerhtml.bind=\"'welcome_mat_dialog.upgrade_to_pro' | i18n | markdown\"></h4> <p>${'welcome_mat_dialog.everything_in_free_membership' | i18n}</p> <ul> <li>${'welcome_mat_dialog.interactive_cheat_controls' | i18n}</li> <li>${'welcome_mat_dialog.save_cheats_between_plays' | i18n}</li> <li>${'welcome_mat_dialog.remote_mobile_app' | i18n}</li> <li>${'welcome_mat_dialog.in_game_cheat_overlay' | i18n}</li> <li>${'welcome_mat_dialog.game_boosting' | i18n}</li> <li>${'welcome_mat_dialog.exclusive_themes' | i18n}</li> <li>${'welcome_mat_dialog.special_role_in_discord' | i18n}</li> </ul> <button pro-cta=\"trigger: welcome_mat\" click.delegate=\"controller.close(true, 'pro_cta')\"> <pro-cta-label></pro-cta-label> </button> </div> </ux-dialog-body> </div> </ux-dialog> </template> "},"dialogs/welcome-mat-dialog.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});var a=t(31601),i=t.n(a),l=t(76314),d=t.n(l),r=t(4417),n=t.n(r),c=new URL(t(16280),t.b),g=d()(i()),m=n()(c);g.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.welcome-mat-dialog{font-size:14px;line-height:21px;line-height:19px;width:700px;padding:0}.welcome-mat-dialog .dialog-scroll-wrapper{padding:30px 60px 60px}.welcome-mat-dialog ux-dialog-header{display:block;margin-bottom:28px;text-align:center}.welcome-mat-dialog ux-dialog-header h1{font-weight:800;font-size:35px;line-height:40px;font-weight:800;color:#fff;margin:0 0 13px}.welcome-mat-dialog ux-dialog-header h1 strong{font-weight:800;font-size:35px;line-height:40px;color:var(--theme--highlight)}.welcome-mat-dialog ux-dialog-header p{font-size:14px;line-height:21px;line-height:19px;display:block;color:rgba(255,255,255,.7);max-width:513px}.welcome-mat-dialog ux-dialog-body{font-size:14px;line-height:21px;display:flex}.welcome-mat-dialog ux-dialog-body>*{flex:1}.welcome-mat-dialog ux-dialog-body button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0}.welcome-mat-dialog ux-dialog-body button,.welcome-mat-dialog ux-dialog-body button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .welcome-mat-dialog ux-dialog-body button{border:1px solid #fff}}.welcome-mat-dialog ux-dialog-body button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.welcome-mat-dialog ux-dialog-body button>*:first-child{padding-left:0}.welcome-mat-dialog ux-dialog-body button>*:last-child{padding-right:0}.welcome-mat-dialog ux-dialog-body button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .welcome-mat-dialog ux-dialog-body button svg *{fill:CanvasText}}.welcome-mat-dialog ux-dialog-body button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .welcome-mat-dialog ux-dialog-body button svg{opacity:1}}.welcome-mat-dialog ux-dialog-body button img{height:50%}.welcome-mat-dialog ux-dialog-body button:disabled{opacity:.3}.welcome-mat-dialog ux-dialog-body button:disabled,.welcome-mat-dialog ux-dialog-body button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.welcome-mat-dialog ux-dialog-body button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.welcome-mat-dialog ux-dialog-body button:not(:disabled):hover svg{opacity:1}}.welcome-mat-dialog ux-dialog-body button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.welcome-mat-dialog ux-dialog-body ul{list-style:none;margin:0 0 30px;padding:0}.welcome-mat-dialog ux-dialog-body ul li:before{content:"";display:inline-block;width:15px;height:11px;vertical-align:middle;-webkit-mask-box-image:url(${m});margin-right:15px}.welcome-mat-dialog ux-dialog-body ul li+li{margin-top:11px}.welcome-mat-dialog ux-dialog-body .col{display:flex;flex-direction:column;align-items:flex-start}.welcome-mat-dialog ux-dialog-body .col button{display:flex;width:100%}.welcome-mat-dialog ux-dialog-body .free{padding:26px 0;margin-right:30px}.welcome-mat-dialog ux-dialog-body .free h5{font-weight:600;font-size:16px;line-height:25px;color:rgba(255,255,255,.6);margin:0 0 20px}.welcome-mat-dialog ux-dialog-body .free ul li:before{background:rgba(255,255,255,.6)}.welcome-mat-dialog ux-dialog-body .free button{font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px}.welcome-mat-dialog ux-dialog-body .pro{padding:17px 30px 23px;background:var(--theme--secondary-background);border-radius:20px}.welcome-mat-dialog ux-dialog-body .pro h4{font-weight:800;font-size:21px;line-height:30px;font-weight:700;display:inline-flex;align-items:center;color:#fff;margin:0 0 14px}.welcome-mat-dialog ux-dialog-body .pro h4 strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;margin-left:7px}@media(forced-colors: active){body:not(.override-contrast-mode) .welcome-mat-dialog ux-dialog-body .pro h4 strong{border:1px solid #fff}}.welcome-mat-dialog ux-dialog-body .pro p{color:rgba(255,255,255,.4);margin:0 0 13px}.welcome-mat-dialog ux-dialog-body .pro button{font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.welcome-mat-dialog ux-dialog-body .pro button:hover{filter:brightness(1.1)}}.welcome-mat-dialog ux-dialog-body .pro ul li{color:#fff}.welcome-mat-dialog ux-dialog-body .pro ul li:before{background:var(--theme--highlight)}`,""]);const s=g}}]);
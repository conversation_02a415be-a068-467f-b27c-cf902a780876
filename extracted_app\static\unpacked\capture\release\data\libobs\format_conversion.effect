/******************************************************************************
    Copyright (C) 2023 by <PERSON><PERSON> <<EMAIL>>

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 2 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <http://www.gnu.org/licenses/>.
******************************************************************************/

#include "color.effect"

uniform float     width;
uniform float     height;
uniform float     width_i;
uniform float     height_i;
uniform float     width_d2;
uniform float     height_d2;
uniform float     width_x2_i;
uniform float     height_x2_i;
uniform float     maximum_over_sdr_white_nits;
uniform float     sdr_white_nits_over_maximum;
uniform float     hlg_exponent;
uniform float     hdr_lw;
uniform float     hdr_lmax;

uniform float4    color_vec0;
uniform float4    color_vec1;
uniform float4    color_vec2;
uniform float3    color_range_min = {0.0, 0.0, 0.0};
uniform float3    color_range_max = {1.0, 1.0, 1.0};

uniform texture2d image;
uniform texture2d image1;
uniform texture2d image2;
uniform texture2d image3;

sampler_state def_sampler {
	Filter   = Linear;
	AddressU = Clamp;
	AddressV = Clamp;
};

struct FragPos {
	float4 pos : POSITION;
};

struct VertTexPos {
	float2 uv  : TEXCOORD0;
	float4 pos : POSITION;
};

struct VertTexTexPos {
	float4 uvuv  : TEXCOORD0;
	float4 pos : POSITION;
};

struct VertTexPosWide {
	float3 uuv : TEXCOORD0;
	float4 pos : POSITION;
};

struct VertTexPosWideWide {
	float4 uuvv : TEXCOORD0;
	float4 pos : POSITION;
};

struct FragTex {
	float2 uv : TEXCOORD0;
};

struct FragTexTex {
	float4 uvuv : TEXCOORD0;
};

struct FragTexWide {
	float3 uuv : TEXCOORD0;
};

struct FragTexWideWide {
	float4 uuvv : TEXCOORD0;
};

FragPos VSPos(uint id : VERTEXID)
{
	float idHigh = float(id >> 1);
	float idLow = float(id & uint(1));

	float x = idHigh * 4.0 - 1.0;
	float y = idLow * 4.0 - 1.0;

	FragPos vert_out;
	vert_out.pos = float4(x, y, 0.0, 1.0);
	return vert_out;
}

VertTexPosWide VSTexPos_Left(uint id : VERTEXID)
{
	float idHigh = float(id >> 1);
	float idLow = float(id & uint(1));

	float x = idHigh * 4.0 - 1.0;
	float y = idLow * 4.0 - 1.0;

	float u_right = idHigh * 2.0;
	float u_left = u_right - width_i;
	float v = obs_glsl_compile ? (idLow * 2.0) : (1.0 - idLow * 2.0);

	VertTexPosWide vert_out;
	vert_out.uuv = float3(u_left, u_right, v);
	vert_out.pos = float4(x, y, 0.0, 1.0);
	return vert_out;
}

VertTexPosWideWide VSTexPos_TopLeft(uint id : VERTEXID)
{
	float idHigh = float(id >> 1);
	float idLow = float(id & uint(1));

	float x = idHigh * 4.0 - 1.0;
	float y = idLow * 4.0 - 1.0;

	float u_right = idHigh * 2.0;
	float u_left = u_right - width_i;
	float v_bottom;
	float v_top;
	if (obs_glsl_compile) {
		v_bottom = idLow * 2.0;
		v_top = v_bottom + height_i;
	} else {
		v_bottom = 1.0 - idLow * 2.0;
		v_top = v_bottom - height_i;
	}

	VertTexPosWideWide vert_out;
	vert_out.uuvv = float4(u_left, u_right, v_top, v_bottom);
	vert_out.pos = float4(x, y, 0.0, 1.0);
	return vert_out;
}

VertTexTexPos VSPacked422Left_Reverse(uint id : VERTEXID)
{
	float idHigh = float(id >> 1);
	float idLow = float(id & uint(1));

	float x = idHigh * 4. - 1.;
	float y = idLow * 4. - 1.;

	float u = idHigh * 2.;
	float v = idLow * 2.;
	v = obs_glsl_compile ? v : (1. - v);

	VertTexTexPos vert_out;
	vert_out.uvuv = float4(width_d2 * u, height * v, u + width_x2_i, v);
	vert_out.pos = float4(x, y, 0., 1.);
	return vert_out;
}

VertTexPos VS420Left_Reverse(uint id : VERTEXID)
{
	float idHigh = float(id >> 1);
	float idLow = float(id & uint(1));

	float x = idHigh * 4. - 1.;
	float y = idLow * 4. - 1.;

	float u = idHigh * 2. + width_x2_i;
	float v = idLow * 2.;
	v = obs_glsl_compile ? v : (1. - v);

	VertTexPos vert_out;
	vert_out.uv = float2(u, v);
	vert_out.pos = float4(x, y, 0., 1.);
	return vert_out;
}

VertTexPos VS420TopLeft_Reverse(uint id : VERTEXID)
{
	float idHigh = float(id >> 1);
	float idLow = float(id & uint(1));

	float x = idHigh * 4. - 1.;
	float y = idLow * 4. - 1.;

	float u = idHigh * 2. + width_x2_i;
	float v = idLow * 2. - height_x2_i;
	v = obs_glsl_compile ? v : (1. - v);

	VertTexPos vert_out;
	vert_out.uv = float2(u, v);
	vert_out.pos = float4(x, y, 0., 1.);
	return vert_out;
}

VertTexPos VS422Left_Reverse(uint id : VERTEXID)
{
	float idHigh = float(id >> 1);
	float idLow = float(id & uint(1));

	float x = idHigh * 4.0 - 1.0;
	float y = idLow * 4.0 - 1.0;

	float u = idHigh * 2.0 + width_x2_i;
	float v = obs_glsl_compile ? (idLow * 2.0) : (1.0 - idLow * 2.0);

	VertTexPos vert_out;
	vert_out.uv = float2(u, v);
	vert_out.pos = float4(x, y, 0.0, 1.0);
	return vert_out;
}

float PS_Y(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y;
}

float PS_P010_PQ_Y_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb * sdr_white_nits_over_maximum;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	y = floor(saturate(y) * 1023. + 0.5) * (64. / 65535.);
	return y;
}

float PS_P010_HLG_Y_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb * sdr_white_nits_over_maximum;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	y = floor(saturate(y) * 1023. + 0.5) * (64. / 65535.);
	return y;
}

float PS_P010_SRGB_Y(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	rgb = srgb_linear_to_nonlinear(rgb);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	y = floor(saturate(y) * 1023. + 0.5) * (64. / 65535.);
	return y;
}

float PS_P216_PQ_Y_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb * sdr_white_nits_over_maximum;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y;
}

float PS_P216_HLG_Y_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb * sdr_white_nits_over_maximum;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y;
}

float PS_P216_SRGB_Y(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	rgb = srgb_linear_to_nonlinear(rgb);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y;
}

float PS_P416_PQ_Y_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb * sdr_white_nits_over_maximum;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y;
}

float PS_P416_HLG_Y_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb * sdr_white_nits_over_maximum;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y;
}

float PS_P416_SRGB_Y(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	rgb = srgb_linear_to_nonlinear(rgb);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y;
}

float PS_I010_PQ_Y_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb * sdr_white_nits_over_maximum;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y * (1023. / 65535.);
}

float PS_I010_HLG_Y_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb * sdr_white_nits_over_maximum;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y * (1023. / 65535.);
}

float PS_I010_SRGB_Y(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	rgb = srgb_linear_to_nonlinear(rgb);
	float y = dot(color_vec0.xyz, rgb) + color_vec0.w;
	return y * (1023. / 65535.);
}

float2 PS_UV_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * 0.5;
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	return float2(u, v);
}

float2 PS_P010_PQ_UV_709_2020_WideWide(FragTexWideWide frag_in) : TARGET
{
	float3 rgb_topleft = image.Sample(def_sampler, frag_in.uuvv.xz).rgb;
	float3 rgb_topright = image.Sample(def_sampler, frag_in.uuvv.yz).rgb;
	float3 rgb_bottomleft = image.Sample(def_sampler, frag_in.uuvv.xw).rgb;
	float3 rgb_bottomright = image.Sample(def_sampler, frag_in.uuvv.yw).rgb;
	float3 rgb = (rgb_topleft + rgb_topright + rgb_bottomleft + rgb_bottomright) * (0.25 * sdr_white_nits_over_maximum);
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	uv = floor(saturate(uv) * 1023. + 0.5) * (64. / 65535.);
	return uv;
}

float2 PS_P010_HLG_UV_709_2020_WideWide(FragTexWideWide frag_in) : TARGET
{
	float3 rgb_topleft = image.Sample(def_sampler, frag_in.uuvv.xz).rgb;
	float3 rgb_topright = image.Sample(def_sampler, frag_in.uuvv.yz).rgb;
	float3 rgb_bottomleft = image.Sample(def_sampler, frag_in.uuvv.xw).rgb;
	float3 rgb_bottomright = image.Sample(def_sampler, frag_in.uuvv.yw).rgb;
	float3 rgb = (rgb_topleft + rgb_topright + rgb_bottomleft + rgb_bottomright) * (0.25 * sdr_white_nits_over_maximum);
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	uv = floor(saturate(uv) * 1023. + 0.5) * (64. / 65535.);
	return uv;
}

float2 PS_P010_SRGB_UV_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * 0.5;
	rgb = srgb_linear_to_nonlinear(rgb);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	uv = floor(saturate(uv) * 1023. + 0.5) * (64. / 65535.);
	return uv;
}

float2 PS_P216_PQ_UV_709_2020_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * (0.5 * sdr_white_nits_over_maximum);
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	return uv;
}

float2 PS_P216_HLG_UV_709_2020_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * (0.5 * sdr_white_nits_over_maximum);
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	return uv;
}

float2 PS_P216_SRGB_UV_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * 0.5;
	rgb = srgb_linear_to_nonlinear(rgb);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	return uv;
}

float2 PS_P416_PQ_UV_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	return uv;
}

float2 PS_P416_HLG_UV_709_2020(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	return uv;
}

float2 PS_P416_SRGB_UV(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	rgb = srgb_linear_to_nonlinear(rgb);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	float2 uv = float2(u, v);
	return uv;
}

float PS_U(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	return u;
}

float PS_V(FragPos frag_in) : TARGET
{
	float3 rgb = image.Load(int3(frag_in.pos.xy, 0)).rgb;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	return v;
}

float PS_U_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * 0.5;
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	return u;
}

float PS_V_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * 0.5;
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	return v;
}

float PS_I010_PQ_U_709_2020_WideWide(FragTexWideWide frag_in) : TARGET
{
	float3 rgb_topleft = image.Sample(def_sampler, frag_in.uuvv.xz).rgb;
	float3 rgb_topright = image.Sample(def_sampler, frag_in.uuvv.yz).rgb;
	float3 rgb_bottomleft = image.Sample(def_sampler, frag_in.uuvv.xw).rgb;
	float3 rgb_bottomright = image.Sample(def_sampler, frag_in.uuvv.yw).rgb;
	float3 rgb = (rgb_topleft + rgb_topright + rgb_bottomleft + rgb_bottomright) * (0.25 * sdr_white_nits_over_maximum);
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	return u * (1023. / 65535.);
}

float PS_I010_HLG_U_709_2020_WideWide(FragTexWideWide frag_in) : TARGET
{
	float3 rgb_topleft = image.Sample(def_sampler, frag_in.uuvv.xz).rgb;
	float3 rgb_topright = image.Sample(def_sampler, frag_in.uuvv.yz).rgb;
	float3 rgb_bottomleft = image.Sample(def_sampler, frag_in.uuvv.xw).rgb;
	float3 rgb_bottomright = image.Sample(def_sampler, frag_in.uuvv.yw).rgb;
	float3 rgb = (rgb_topleft + rgb_topright + rgb_bottomleft + rgb_bottomright) * (0.25 * sdr_white_nits_over_maximum);
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	return u * (1023. / 65535.);
}

float PS_I010_SRGB_U_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * 0.5;
	rgb = srgb_linear_to_nonlinear(rgb);
	float u = dot(color_vec1.xyz, rgb) + color_vec1.w;
	return u * (1023. / 65535.);
}

float PS_I010_PQ_V_709_2020_WideWide(FragTexWideWide frag_in) : TARGET
{
	float3 rgb_topleft = image.Sample(def_sampler, frag_in.uuvv.xz).rgb;
	float3 rgb_topright = image.Sample(def_sampler, frag_in.uuvv.yz).rgb;
	float3 rgb_bottomleft = image.Sample(def_sampler, frag_in.uuvv.xw).rgb;
	float3 rgb_bottomright = image.Sample(def_sampler, frag_in.uuvv.yw).rgb;
	float3 rgb = (rgb_topleft + rgb_topright + rgb_bottomleft + rgb_bottomright) * (0.25 * sdr_white_nits_over_maximum);
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_st2084(rgb);
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	return v * (1023. / 65535.);
}

float PS_I010_HLG_V_709_2020_WideWide(FragTexWideWide frag_in) : TARGET
{
	float3 rgb_topleft = image.Sample(def_sampler, frag_in.uuvv.xz).rgb;
	float3 rgb_topright = image.Sample(def_sampler, frag_in.uuvv.yz).rgb;
	float3 rgb_bottomleft = image.Sample(def_sampler, frag_in.uuvv.xw).rgb;
	float3 rgb_bottomright = image.Sample(def_sampler, frag_in.uuvv.yw).rgb;
	float3 rgb = (rgb_topleft + rgb_topright + rgb_bottomleft + rgb_bottomright) * (0.25 * sdr_white_nits_over_maximum);
	rgb = rec709_to_rec2020(rgb);
	rgb = linear_to_hlg(rgb, hdr_lw);
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	return v * (1023. / 65535.);
}

float PS_I010_SRGB_V_Wide(FragTexWide frag_in) : TARGET
{
	float3 rgb_left = image.Sample(def_sampler, frag_in.uuv.xz).rgb;
	float3 rgb_right = image.Sample(def_sampler, frag_in.uuv.yz).rgb;
	float3 rgb = (rgb_left + rgb_right) * 0.5;
	rgb = srgb_linear_to_nonlinear(rgb);
	float v = dot(color_vec2.xyz, rgb) + color_vec2.w;
	return v * (1023. / 65535.);
}

float3 YUV_to_RGB(float3 yuv)
{
	yuv = clamp(yuv, color_range_min, color_range_max);
	float r = dot(color_vec0.xyz, yuv) + color_vec0.w;
	float g = dot(color_vec1.xyz, yuv) + color_vec1.w;
	float b = dot(color_vec2.xyz, yuv) + color_vec2.w;
	return float3(r, g, b);
}

float3 PSUYVY_Reverse(FragTexTex frag_in) : TARGET
{
	float2 y01 = image.Load(int3(frag_in.uvuv.xy, 0)).yw;
	float2 cbcr = image.Sample(def_sampler, frag_in.uvuv.zw, 0).zx;
	float leftover = frac(frag_in.uvuv.x);
	float y = (leftover < 0.5) ? y01.x : y01.y;
	float3 yuv = float3(y, cbcr);
	float3 rgb = YUV_to_RGB(yuv);
	return rgb;
}

float3 PSYUY2_Reverse(FragTexTex frag_in) : TARGET
{
	float2 y01 = image.Load(int3(frag_in.uvuv.xy, 0)).zx;
	float2 cbcr = image.Sample(def_sampler, frag_in.uvuv.zw, 0).yw;
	float leftover = frac(frag_in.uvuv.x);
	float y = (leftover < 0.5) ? y01.x : y01.y;
	float3 yuv = float3(y, cbcr);
	float3 rgb = YUV_to_RGB(yuv);
	return rgb;
}

float4 PSYUY2_PQ_Reverse(FragTexTex frag_in) : TARGET
{
	float2 y01 = image.Load(int3(frag_in.uvuv.xy, 0)).zx;
	float2 cbcr = image.Sample(def_sampler, frag_in.uvuv.zw, 0).yw;
	float leftover = frac(frag_in.uvuv.x);
	float y = (leftover < 0.5) ? y01.x : y01.y;
	float3 yuv = float3(y, cbcr);
	float3 pq = YUV_to_RGB(yuv);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSYUY2_HLG_Reverse(FragTexTex frag_in) : TARGET
{
	float2 y01 = image.Load(int3(frag_in.uvuv.xy, 0)).zx;
	float2 cbcr = image.Sample(def_sampler, frag_in.uvuv.zw, 0).yw;
	float leftover = frac(frag_in.uvuv.x);
	float y = (leftover < 0.5) ? y01.x : y01.y;
	float3 yuv = float3(y, cbcr);
	float3 hlg = YUV_to_RGB(yuv);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float3 PSYVYU_Reverse(FragTexTex frag_in) : TARGET
{
	float2 y01 = image.Load(int3(frag_in.uvuv.xy, 0)).zx;
	float2 cbcr = image.Sample(def_sampler, frag_in.uvuv.zw, 0).wy;
	float leftover = frac(frag_in.uvuv.x);
	float y = (leftover < 0.5) ? y01.x : y01.y;
	float3 yuv = float3(y, cbcr);
	float3 rgb = YUV_to_RGB(yuv);
	return rgb;
}

float3 PSPlanar420_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	float3 rgb = YUV_to_RGB(yuv);
	return rgb;
}

float4 PSPlanar420_PQ_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	float3 pq = YUV_to_RGB(yuv);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSPlanar420_HLG_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	float3 hlg = YUV_to_RGB(yuv);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSPlanar420A_Reverse(VertTexPos frag_in) : TARGET
{
	int3 xy0_luma = int3(frag_in.pos.xy, 0);
	float y = image.Load(xy0_luma).x;
	float alpha = image3.Load(xy0_luma).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	float4 rgba = float4(YUV_to_RGB(yuv), alpha);
	return rgba;
}

float3 PSPlanar422_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	float3 rgb = YUV_to_RGB(yuv);
	return rgb;
}

float4 PSPlanar422_10LE_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	yuv *= 65535. / 1023.;
	float3 rgb = YUV_to_RGB(yuv);
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, 1.);
}

float4 PSPlanar422_10LE_PQ_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	yuv *= 65535. / 1023.;
	float3 pq = YUV_to_RGB(yuv);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSPlanar422_10LE_HLG_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	yuv *= 65535. / 1023.;
	float3 hlg = YUV_to_RGB(yuv);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSPlanar422A_Reverse(VertTexPos frag_in) : TARGET
{
	int3 xy0_luma = int3(frag_in.pos.xy, 0);
	float y = image.Load(xy0_luma).x;
	float alpha = image3.Load(xy0_luma).x;
	float cb = image1.Sample(def_sampler, frag_in.uv).x;
	float cr = image2.Sample(def_sampler, frag_in.uv).x;
	float3 yuv = float3(y, cb, cr);
	float4 rgba = float4(YUV_to_RGB(yuv), alpha);
	return rgba;
}

float3 PSPlanar444_Reverse(FragPos frag_in) : TARGET
{
	int3 xy0 = int3(frag_in.pos.xy, 0);
	float y = image.Load(xy0).x;
	float cb = image1.Load(xy0).x;
	float cr = image2.Load(xy0).x;
	float3 yuv = float3(y, cb, cr);
	float3 rgb = YUV_to_RGB(yuv);
	return rgb;
}

float4 PSPlanar444_12LE_Reverse(FragPos frag_in) : TARGET
{
	int3 xy0 = int3(frag_in.pos.xy, 0);
	float y = image.Load(xy0).x;
	float cb = image1.Load(xy0).x;
	float cr = image2.Load(xy0).x;
	float3 yuv = float3(y, cb, cr);
	yuv *= 65535. / 4095.;
	float3 rgb = YUV_to_RGB(yuv);
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, 1.);
}

float4 PSPlanar444_12LE_PQ_Reverse(FragPos frag_in) : TARGET
{
	int3 xy0 = int3(frag_in.pos.xy, 0);
	float y = image.Load(xy0).x;
	float cb = image1.Load(xy0).x;
	float cr = image2.Load(xy0).x;
	float3 yuv = float3(y, cb, cr);
	yuv *= 65535. / 4095;
	float3 pq = YUV_to_RGB(yuv);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSPlanar444_12LE_HLG_Reverse(FragPos frag_in) : TARGET
{
	int3 xy0 = int3(frag_in.pos.xy, 0);
	float y = image.Load(xy0).x;
	float cb = image1.Load(xy0).x;
	float cr = image2.Load(xy0).x;
	float3 yuv = float3(y, cb, cr);
	yuv *= 65535. / 4095;
	float3 hlg = YUV_to_RGB(yuv);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSPlanar444A_Reverse(FragPos frag_in) : TARGET
{
	int3 xy0 = int3(frag_in.pos.xy, 0);
	float y = image.Load(xy0).x;
	float cb = image1.Load(xy0).x;
	float cr = image2.Load(xy0).x;
	float alpha = image3.Load(xy0).x;
	float3 yuv = float3(y, cb, cr);
	float4 rgba = float4(YUV_to_RGB(yuv), alpha);
	return rgba;
}

float4 PSPlanar444A_12LE_Reverse(FragPos frag_in) : TARGET
{
	int3 xy0 = int3(frag_in.pos.xy, 0);
	float y = image.Load(xy0).x;
	float cb = image1.Load(xy0).x;
	float cr = image2.Load(xy0).x;
	float alpha = image3.Load(xy0).x * 16.;
	float3 yuv = float3(y, cb, cr);
	yuv *= 65535. / 4095.;
	float3 rgb = YUV_to_RGB(yuv);
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, alpha);
}

float4 PSAYUV_Reverse(FragPos frag_in) : TARGET
{
	float4 yuva = image.Load(int3(frag_in.pos.xy, 0));
	float4 rgba = float4(YUV_to_RGB(yuva.xyz), yuva.a);
	return rgba;
}

float3 PSNV12_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float2 cbcr = image1.Sample(def_sampler, frag_in.uv).xy;
	float3 yuv = float3(y, cbcr);
	float3 rgb = YUV_to_RGB(yuv);
	return rgb;
}

float4 PSNV12_PQ_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float2 cbcr = image1.Sample(def_sampler, frag_in.uv).xy;
	float3 yuv = float3(y, cbcr);
	float3 pq = YUV_to_RGB(yuv);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSNV12_HLG_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float2 cbcr = image1.Sample(def_sampler, frag_in.uv).xy;
	float3 yuv = float3(y, cbcr);
	float3 hlg = YUV_to_RGB(yuv);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSI010_SRGB_Reverse(VertTexPos frag_in) : TARGET
{
	float ratio = 65535. / 1023.;
	float y = image.Load(int3(frag_in.pos.xy, 0)).x * ratio;
	float cb = image1.Sample(def_sampler, frag_in.uv).x * ratio;
	float cr = image2.Sample(def_sampler, frag_in.uv).x * ratio;
	float3 yuv = float3(y, cb, cr);
	float3 rgb = YUV_to_RGB(yuv);
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, 1.);
}

float4 PSI010_PQ_2020_709_Reverse(VertTexPos frag_in) : TARGET
{
	float ratio = 65535. / 1023.;
	float y = image.Load(int3(frag_in.pos.xy, 0)).x * ratio;
	float cb = image1.Sample(def_sampler, frag_in.uv).x * ratio;
	float cr = image2.Sample(def_sampler, frag_in.uv).x * ratio;
	float3 yuv = float3(y, cb, cr);
	float3 pq = YUV_to_RGB(yuv);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSI010_HLG_2020_709_Reverse(VertTexPos frag_in) : TARGET
{
	float ratio = 65535. / 1023.;
	float y = image.Load(int3(frag_in.pos.xy, 0)).x * ratio;
	float cb = image1.Sample(def_sampler, frag_in.uv).x * ratio;
	float cr = image2.Sample(def_sampler, frag_in.uv).x * ratio;
	float3 yuv = float3(y, cb, cr);
	float3 hlg = YUV_to_RGB(yuv);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSP010_SRGB_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float2 cbcr = image1.Sample(def_sampler, frag_in.uv).xy;
	float3 yuv_65535 = floor(float3(y, cbcr) * 65535. + 0.5);
	float3 yuv_1023 = floor(yuv_65535 * 0.015625);
	float3 yuv = yuv_1023 / 1023.;
	float3 rgb = YUV_to_RGB(yuv);
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, 1.);
}

float4 PSP010_PQ_2020_709_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float2 cbcr = image1.Sample(def_sampler, frag_in.uv).xy;
	float3 yuv_65535 = floor(float3(y, cbcr) * 65535. + 0.5);
	float3 yuv_1023 = floor(yuv_65535 * 0.015625);
	float3 yuv = yuv_1023 / 1023.;
	float3 pq = YUV_to_RGB(yuv);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSP010_HLG_2020_709_Reverse(VertTexPos frag_in) : TARGET
{
	float y = image.Load(int3(frag_in.pos.xy, 0)).x;
	float2 cbcr = image1.Sample(def_sampler, frag_in.uv).xy;
	float3 yuv_65535 = floor(float3(y, cbcr) * 65535. + 0.5);
	float3 yuv_1023 = floor(yuv_65535 * 0.015625);
	float3 yuv = yuv_1023 / 1023.;
	float3 hlg = YUV_to_RGB(yuv);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float3 compute_v210_reverse(float2 pos)
{
	uint x = uint(pos.x);
	uint packed_x = x % 6u;
	uint base_x = x / 6u * 4u;
	float y, cb, cr;
	if (packed_x == 0u)
	{
		float3 word0_rgb = image.Load(int3(base_x, pos.y, 0)).rgb;
		y = word0_rgb.y;
		cb = word0_rgb.x;
		cr = word0_rgb.z;
	}
	else if (packed_x == 1u)
	{
		float2 word0_rb = image.Load(int3(base_x, pos.y, 0)).rb;
		float2 word1_rg = image.Load(int3(base_x + 1u, pos.y, 0)).rg;
		y = word1_rg.x;
		cb = (word0_rb.x + word1_rg.y) * 0.5;
		cr = (word0_rb.y + image.Load(int3(base_x + 2u, pos.y, 0)).r) * 0.5;
	}
	else if (packed_x == 2u)
	{
		float2 word1_gb = image.Load(int3(base_x + 1u, pos.y, 0)).gb;
		y = word1_gb.y;
		cb = word1_gb.x;
		cr = image.Load(int3(base_x + 2u, pos.y, 0)).r;
	}
	else if (packed_x == 3u)
	{
		float2 word2_rb = image.Load(int3(base_x + 2u, pos.y, 0)).rb;
		y = image.Load(int3(base_x + 2u, pos.y, 0)).g;
		cb = (image.Load(int3(base_x + 1u, pos.y, 0)).g + word2_rb.y) * 0.5;
		cr = (word2_rb.x + image.Load(int3(base_x + 3u, pos.y, 0)).g) * 0.5;
	}
	else if (packed_x == 4u)
	{
		float2 word3_rg = image.Load(int3(base_x + 3u, pos.y, 0)).rg;
		y = word3_rg.x;
		cb = image.Load(int3(base_x + 2u, pos.y, 0)).b;
		cr = word3_rg.y;
	}
	else
	{
		float2 word3_gb = image.Load(int3(base_x + 3u, pos.y, 0)).gb;
		y = word3_gb.y;
		cb = image.Load(int3(base_x + 2u, pos.y, 0)).b;
		cr = word3_gb.x;
		uint base_x_4 = base_x + 4u;
		if ((pos.x + 1.) < width)
		{
			float2 word4_gb = image.Load(int3(base_x + 4u, pos.y, 0)).rb;
			cb = (cb + word4_gb.x) * 0.5;
			cr = (cr + word4_gb.y) * 0.5;
		}
	}
	float3 yuv_65535 = floor(float3(y, cb, cr) * 65535. + 0.5);
	float3 yuv_1023 = floor(yuv_65535 * 0.015625);
	float3 yuv = yuv_1023 / 1023.;
	float3 rgb = YUV_to_RGB(yuv);
	return rgb;
}

float4 PSV210_SRGB_Reverse(FragPos frag_in) : TARGET
{
	float3 rgb = compute_v210_reverse(frag_in.pos.xy);
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, 1.);
}

float4 PSV210_PQ_2020_709_Reverse(FragPos frag_in) : TARGET
{
	float3 pq = compute_v210_reverse(frag_in.pos.xy);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSV210_HLG_2020_709_Reverse(FragPos frag_in) : TARGET
{
	float3 hlg = compute_v210_reverse(frag_in.pos.xy);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float3 PSY800_Limited(FragPos frag_in) : TARGET
{
	float limited = image.Load(int3(frag_in.pos.xy, 0)).x;
	float full = (255.0 / 219.0) * limited - (16.0 / 219.0);
	return float3(full, full, full);
}

float3 PSY800_Full(FragPos frag_in) : TARGET
{
	float3 full = image.Load(int3(frag_in.pos.xy, 0)).xxx;
	return full;
}

float4 PSRGB_Limited(FragPos frag_in) : TARGET
{
	float4 rgba = image.Load(int3(frag_in.pos.xy, 0));
	rgba.rgb = (255.0 / 219.0) * rgba.rgb - (16.0 / 219.0);
	return rgba;
}

float3 PSBGR3_Limited(FragPos frag_in) : TARGET
{
	float x = frag_in.pos.x * 3.0;
	float y = frag_in.pos.y;
	float b = image.Load(int3(x - 1.0, y, 0)).x;
	float g = image.Load(int3(x, y, 0)).x;
	float r = image.Load(int3(x + 1.0, y, 0)).x;
	float3 rgb = float3(r, g, b);
	rgb = (255.0 / 219.0) * rgb - (16.0 / 219.0);
	return rgb;
}

float3 PSBGR3_Full(FragPos frag_in) : TARGET
{
	float x = frag_in.pos.x * 3.0;
	float y = frag_in.pos.y;
	float b = image.Load(int3(x - 1.0, y, 0)).x;
	float g = image.Load(int3(x, y, 0)).x;
	float r = image.Load(int3(x + 1.0, y, 0)).x;
	float3 rgb = float3(r, g, b);
	return rgb;
}

float3 compute_r10l_reverse(float2 pos, bool limited)
{
	float4 xyzw = image.Load(int3(pos, 0)).bgra;
	uint4 xyzw255 = uint4(mad(xyzw, 255., 0.5));
	uint r = ((xyzw255.z & 0xC0u) >> 6) | (xyzw255.w << 2);
	uint g = ((xyzw255.y & 0xFu) >> 4) | ((xyzw255.z & 0x3Fu) << 4);
	uint b = (xyzw255.x >> 2) | ((xyzw255.y & 0xFu) << 6);
	float3 rgb = float3(uint3(r, g, b));
	if (limited)
	{
		rgb = mad(rgb, 1. / 876., -16. / 219.);
	}
	else
	{
		rgb /= 1023.;
	}

	return rgb;
}

float4 PSR10L_SRGB_Full_Reverse(FragPos frag_in) : TARGET
{
	float3 rgb = compute_r10l_reverse(frag_in.pos.xy, false);
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, 1.);
}

float4 PSR10L_PQ_2020_709_Full_Reverse(FragPos frag_in) : TARGET
{
	float3 pq = compute_r10l_reverse(frag_in.pos.xy, false);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSR10L_HLG_2020_709_Full_Reverse(FragPos frag_in) : TARGET
{
	float3 hlg = compute_r10l_reverse(frag_in.pos.xy, false);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSR10L_SRGB_Limited_Reverse(FragPos frag_in) : TARGET
{
	float3 rgb = compute_r10l_reverse(frag_in.pos.xy, true);
	rgb = srgb_nonlinear_to_linear(rgb);
	return float4(rgb, 1.);
}

float4 PSR10L_PQ_2020_709_Limited_Reverse(FragPos frag_in) : TARGET
{
	float3 pq = compute_r10l_reverse(frag_in.pos.xy, true);
	float3 hdr2020 = st2084_to_linear_eetf(pq, hdr_lw, hdr_lmax) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

float4 PSR10L_HLG_2020_709_Limited_Reverse(FragPos frag_in) : TARGET
{
	float3 hlg = compute_r10l_reverse(frag_in.pos.xy, true);
	float3 hdr2020 = hlg_to_linear(hlg, hlg_exponent) * maximum_over_sdr_white_nits;
	float3 rgb = rec2020_to_rec709(hdr2020);
	return float4(rgb, 1.);
}

technique Planar_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_Y(frag_in);
	}
}

technique Planar_U
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_U(frag_in);
	}
}

technique Planar_V
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_V(frag_in);
	}
}

technique Planar_U_Left
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_U_Wide(frag_in);
	}
}

technique Planar_V_Left
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_V_Wide(frag_in);
	}
}

technique NV12_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_Y(frag_in);
	}
}

technique NV12_UV
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_UV_Wide(frag_in);
	}
}

technique I010_PQ_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_I010_PQ_Y_709_2020(frag_in);
	}
}

technique I010_HLG_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_I010_HLG_Y_709_2020(frag_in);
	}
}

technique I010_SRGB_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_I010_SRGB_Y(frag_in);
	}
}

technique I010_PQ_U
{
	pass
	{
		vertex_shader = VSTexPos_TopLeft(id);
		pixel_shader  = PS_I010_PQ_U_709_2020_WideWide(frag_in);
	}
}

technique I010_HLG_U
{
	pass
	{
		vertex_shader = VSTexPos_TopLeft(id);
		pixel_shader  = PS_I010_HLG_U_709_2020_WideWide(frag_in);
	}
}

technique I010_SRGB_U
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_I010_SRGB_U_Wide(frag_in);
	}
}

technique I010_PQ_V
{
	pass
	{
		vertex_shader = VSTexPos_TopLeft(id);
		pixel_shader  = PS_I010_PQ_V_709_2020_WideWide(frag_in);
	}
}

technique I010_HLG_V
{
	pass
	{
		vertex_shader = VSTexPos_TopLeft(id);
		pixel_shader  = PS_I010_HLG_V_709_2020_WideWide(frag_in);
	}
}

technique I010_SRGB_V
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_I010_SRGB_V_Wide(frag_in);
	}
}

technique P010_PQ_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P010_PQ_Y_709_2020(frag_in);
	}
}

technique P010_HLG_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P010_HLG_Y_709_2020(frag_in);
	}
}

technique P010_SRGB_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P010_SRGB_Y(frag_in);
	}
}

technique P010_PQ_UV
{
	pass
	{
		vertex_shader = VSTexPos_TopLeft(id);
		pixel_shader  = PS_P010_PQ_UV_709_2020_WideWide(frag_in);
	}
}

technique P010_HLG_UV
{
	pass
	{
		vertex_shader = VSTexPos_TopLeft(id);
		pixel_shader  = PS_P010_HLG_UV_709_2020_WideWide(frag_in);
	}
}

technique P010_SRGB_UV
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_P010_SRGB_UV_Wide(frag_in);
	}
}

technique P216_PQ_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P216_PQ_Y_709_2020(frag_in);
	}
}

technique P216_HLG_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P216_HLG_Y_709_2020(frag_in);
	}
}

technique P216_SRGB_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P216_SRGB_Y(frag_in);
	}
}

technique P216_PQ_UV
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_P216_PQ_UV_709_2020_Wide(frag_in);
	}
}

technique P216_HLG_UV
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_P216_HLG_UV_709_2020_Wide(frag_in);
	}
}

technique P216_SRGB_UV
{
	pass
	{
		vertex_shader = VSTexPos_Left(id);
		pixel_shader  = PS_P216_SRGB_UV_Wide(frag_in);
	}
}

technique P416_PQ_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P416_PQ_Y_709_2020(frag_in);
	}
}

technique P416_HLG_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P416_HLG_Y_709_2020(frag_in);
	}
}

technique P416_SRGB_Y
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P416_SRGB_Y(frag_in);
	}
}

technique P416_PQ_UV
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P416_PQ_UV_709_2020(frag_in);
	}
}

technique P416_HLG_UV
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P416_HLG_UV_709_2020(frag_in);
	}
}

technique P416_SRGB_UV
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PS_P416_SRGB_UV(frag_in);
	}
}

technique UYVY_Reverse
{
	pass
	{
		vertex_shader = VSPacked422Left_Reverse(id);
		pixel_shader  = PSUYVY_Reverse(frag_in);
	}
}

technique YUY2_Reverse
{
	pass
	{
		vertex_shader = VSPacked422Left_Reverse(id);
		pixel_shader  = PSYUY2_Reverse(frag_in);
	}
}

technique YUY2_PQ_Reverse
{
	pass
	{
		vertex_shader = VSPacked422Left_Reverse(id);
		pixel_shader  = PSYUY2_PQ_Reverse(frag_in);
	}
}

technique YUY2_HLG_Reverse
{
	pass
	{
		vertex_shader = VSPacked422Left_Reverse(id);
		pixel_shader  = PSYUY2_HLG_Reverse(frag_in);
	}
}

technique YVYU_Reverse
{
	pass
	{
		vertex_shader = VSPacked422Left_Reverse(id);
		pixel_shader  = PSYVYU_Reverse(frag_in);
	}
}

technique I420_Reverse
{
	pass
	{
		vertex_shader = VS420Left_Reverse(id);
		pixel_shader  = PSPlanar420_Reverse(frag_in);
	}
}

technique I420_PQ_Reverse
{
	pass
	{
		vertex_shader = VS420TopLeft_Reverse(id);
		pixel_shader  = PSPlanar420_PQ_Reverse(frag_in);
	}
}

technique I420_HLG_Reverse
{
	pass
	{
		vertex_shader = VS420TopLeft_Reverse(id);
		pixel_shader  = PSPlanar420_HLG_Reverse(frag_in);
	}
}

technique I40A_Reverse
{
	pass
	{
		vertex_shader = VS420Left_Reverse(id);
		pixel_shader  = PSPlanar420A_Reverse(frag_in);
	}
}

technique I422_Reverse
{
	pass
	{
		vertex_shader = VS422Left_Reverse(id);
		pixel_shader  = PSPlanar422_Reverse(frag_in);
	}
}

technique I210_Reverse
{
	pass
	{
		vertex_shader = VS422Left_Reverse(id);
		pixel_shader  = PSPlanar422_10LE_Reverse(frag_in);
	}
}

technique I210_PQ_Reverse
{
	pass
	{
		vertex_shader = VS422Left_Reverse(id);
		pixel_shader  = PSPlanar422_10LE_PQ_Reverse(frag_in);
	}
}

technique I210_HLG_Reverse
{
	pass
	{
		vertex_shader = VS422Left_Reverse(id);
		pixel_shader  = PSPlanar422_10LE_HLG_Reverse(frag_in);
	}
}

technique I42A_Reverse
{
	pass
	{
		vertex_shader = VS422Left_Reverse(id);
		pixel_shader  = PSPlanar422A_Reverse(frag_in);
	}
}

technique I444_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSPlanar444_Reverse(frag_in);
	}
}

technique I412_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSPlanar444_12LE_Reverse(frag_in);
	}
}

technique I412_PQ_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSPlanar444_12LE_PQ_Reverse(frag_in);
	}
}

technique I412_HLG_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSPlanar444_12LE_HLG_Reverse(frag_in);
	}
}

technique YUVA_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSPlanar444A_Reverse(frag_in);
	}
}

technique YA2L_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSPlanar444A_12LE_Reverse(frag_in);
	}
}

technique AYUV_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSAYUV_Reverse(frag_in);
	}
}

technique NV12_Reverse
{
	pass
	{
		vertex_shader = VS420Left_Reverse(id);
		pixel_shader  = PSNV12_Reverse(frag_in);
	}
}

technique NV12_PQ_Reverse
{
	pass
	{
		vertex_shader = VS420TopLeft_Reverse(id);
		pixel_shader  = PSNV12_PQ_Reverse(frag_in);
	}
}

technique NV12_HLG_Reverse
{
	pass
	{
		vertex_shader = VS420TopLeft_Reverse(id);
		pixel_shader  = PSNV12_HLG_Reverse(frag_in);
	}
}

technique I010_SRGB_Reverse
{
	pass
	{
		vertex_shader = VS420Left_Reverse(id);
		pixel_shader  = PSI010_SRGB_Reverse(frag_in);
	}
}

technique I010_PQ_2020_709_Reverse
{
	pass
	{
		vertex_shader = VS420TopLeft_Reverse(id);
		pixel_shader  = PSI010_PQ_2020_709_Reverse(frag_in);
	}
}

technique I010_HLG_2020_709_Reverse
{
	pass
	{
		vertex_shader = VS420TopLeft_Reverse(id);
		pixel_shader  = PSI010_HLG_2020_709_Reverse(frag_in);
	}
}

technique P010_SRGB_Reverse
{
	pass
	{
		vertex_shader = VS420Left_Reverse(id);
		pixel_shader  = PSP010_SRGB_Reverse(frag_in);
	}
}

technique P010_PQ_2020_709_Reverse
{
	pass
	{
		vertex_shader = VS420TopLeft_Reverse(id);
		pixel_shader  = PSP010_PQ_2020_709_Reverse(frag_in);
	}
}

technique P010_HLG_2020_709_Reverse
{
	pass
	{
		vertex_shader = VS420TopLeft_Reverse(id);
		pixel_shader  = PSP010_HLG_2020_709_Reverse(frag_in);
	}
}

technique V210_SRGB_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSV210_SRGB_Reverse(frag_in);
	}
}

technique V210_PQ_2020_709_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSV210_PQ_2020_709_Reverse(frag_in);
	}
}

technique V210_HLG_2020_709_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSV210_HLG_2020_709_Reverse(frag_in);
	}
}

technique Y800_Limited
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSY800_Limited(frag_in);
	}
}

technique Y800_Full
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSY800_Full(frag_in);
	}
}

technique RGB_Limited
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSRGB_Limited(frag_in);
	}
}

technique BGR3_Limited
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSBGR3_Limited(frag_in);
	}
}

technique BGR3_Full
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSBGR3_Full(frag_in);
	}
}

technique R10L_SRGB_Full_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSR10L_SRGB_Full_Reverse(frag_in);
	}
}

technique R10L_PQ_2020_709_Full_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSR10L_PQ_2020_709_Full_Reverse(frag_in);
	}
}

technique R10L_HLG_2020_709_Full_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSR10L_HLG_2020_709_Full_Reverse(frag_in);
	}
}

technique R10L_SRGB_Limited_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSR10L_SRGB_Limited_Reverse(frag_in);
	}
}

technique R10L_PQ_2020_709_Limited_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSR10L_PQ_2020_709_Limited_Reverse(frag_in);
	}
}

technique R10L_HLG_2020_709_Limited_Reverse
{
	pass
	{
		vertex_shader = VSPos(id);
		pixel_shader  = PSR10L_HLG_2020_709_Limited_Reverse(frag_in);
	}
}

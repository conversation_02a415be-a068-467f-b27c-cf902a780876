"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[9201],{35030:(t,e,a)=>{a.d(e,{U:()=>I});var s=a(15215),l=a("aurelia-framework"),i=a(20770),r=a(68663),n=a(41882),h=a(17724),d=a(24008),o=a(96555),c=a(54995),m=a(49442),u=a(70236),p=a(48881),g=a(38777);const f={steam:1,uwp:2,epic:3,origin:4,uplay:5,rockstar:6,gog:7};function b(t,e){return t.available&&!e.available?-1:e.available&&!t.available?1:t.gameInstalled&&!e.gameInstalled?-1:e.gameInstalled&&!t.gameInstalled?1:t.appInstalled&&!e.appInstalled?-1:e.appInstalled&&!t.appInstalled?1:(f[t.platformId]??100)-(f[e.platformId]??100)}let I=class{#t;#e;#a;#s;#l;#i;constructor(t,e){this.#s={},this.#l=t,this.#i=e}attached(){this.#t=new g.Vd,this.#a={},this.#e={}}detached(){this.#t?.dispose(),this.#t=null}catalogChanged(){this.#a&&this.#r()}installedAppsChanged(){this.#a&&this.#n()}installedGameVersionsChanged(){this.#a&&this.#n()}async watch(t){return this.#s[t]=(this.#s[t]||0)+1,await this.#h(t),this.#a[t]}unwatch(t){const e=Math.max((this.#s[t]||0)-1,0);this.#s[t]=e,e||(delete this.#e[t],delete this.#a[t])}async#r(){Promise.all(Object.keys(this.#e).map((t=>this.#h(t).catch(m.Y))))}#n(){Object.keys(this.#a).forEach((t=>this.#d(t)))}async#h(t){if(this.catalog.titles.hasOwnProperty(t)){const e=this.catalog.titles[t],a={id:e.id,name:e.name,terms:e.terms,thumbnail:e.thumbnail,games:e.gameIds.map((t=>this.catalog.games[t])).map((t=>({id:t.id,platformId:t.platformId,edition:t.edition,correlationIds:t.correlationIds,flags:t.flags}))),flags:e.flags};this.#e[t]=Object.assign(this.#e[t]||{},a)}else{if(this.#e[t])return;try{const e=await this.#l.getUnavailableTitle(t);if(!this.#s[t])return;this.#e[t]=Object.assign(this.#e[t]||{},e)}catch(e){throw e instanceof h.hD&&404===e.status&&this.#i.dispatch(p.zm,[t]),e}}this.#d(t)}#d(t){const e=this.#e[t]??{id:t,name:"",thumbnail:"",games:[]},a={...e,steamAppId:e?.games.flatMap((t=>t.correlationIds)).map(o.o.parse).find((t=>"steam"===t.platform))?.sku??null,games:e?.games.map((t=>{const e=this.installedGameVersions[t.id],a=this.catalog.games[t.id];return{id:t.id,platformId:t.platformId,correlationIds:[...t.correlationIds],edition:t.edition??null,available:(0,u.Lt)(t.flags,d.rT.Available),appInstalled:t.correlationIds.some((t=>!!this.installedApps[t])),gameInstalled:e?.length>0,customGameInstalled:!!e&&!!e.map((t=>this.installedApps[t.correlationId])).find((t=>t?.platform===n.u)),flags:t.flags,tags:a?.tags??[]}})).sort(b)};this.#a[t]=Object.assign(this.#a[t]||{},a)}};I=(0,s.Cg)([(0,c.m6)({setup:"attached",teardown:"detached",selectors:{catalog:(0,c.$t)((t=>t.catalog)),installedApps:(0,c.$t)((t=>t.installedApps)),installedGameVersions:(0,c.$t)((t=>t.installedGameVersions))}}),(0,l.autoinject)(),(0,s.Sn)("design:paramtypes",[r.x,i.il])],I)},98120:(t,e,a)=>{a.d(e,{p:()=>b,r:()=>f});var s=a(15215),l=a("aurelia-event-aggregator"),i=a("aurelia-framework"),r=a(68663),n=a(21795),h=a(17724),d=a(24008),o=a(44759),c=a(54995),m=a(85975),u=a(49442),p=a(70236),g=a(64415);class f{#o;#c;constructor(t,e){this.searchResults=[],this.#c=t,this.location=e}initialize(){this.#o=this.#c.onStatusChanged((()=>this.triggerResultsRefresh()))}reset(){this.pendingSearchTerms=null,this.searchResults=[]}dispose(){this.#o?.dispose(),this.#o=null}triggerResultsRefresh(){this.resultsShouldRefresh&&this.resultsShouldRefresh()}}let b=class{#c;#l;#m;constructor(t,e,a){this.#c=t,this.#l=e,this.#m=a}activate(){}deactivate(){}createSession(t,e){const a=new f(this.#c,e);return a.resultsShouldRefresh=t,a.initialize(),a}search(t,e){const a="online"===this.#c.status;let s=this.#u(Object.values(this.state.catalog.titles),e);if(e&&e!==t.pendingSearchTerms&&this.#m.publish(new n.Pq(t.location,e)),a||(s=s.filter((t=>t.gameIds.some((t=>!!this.cachedTrainers[t])))),t.reset()),a&&e){const a=e.trim();this.#p(t,a)?this.#g(t,a).catch(u.Y):t.pendingSearchTerms=a}const l=new Set;let i=s.filter((t=>t.gameIds.some((t=>(0,p.Lt)(this.state.catalog.games[t]?.flags,d.rT.Available))))).map((t=>({resultType:"catalog-title",result:t,isInstalled:null})));return a&&i.push(...this.#f(t)),i=i.filter((t=>!l.has(t.result.id)&&(l.add(t.result.id),!0))),i}#p(t,e){return!(e.length<=2||e===t.pendingSearchTerms)&&(e.length<25||e.split("").some((t=>t!==e[0])))}#f(t){const e=this.#b(t);return[...t.searchResults.filter((t=>-1===e.findIndex((e=>e.result.id===t.result.id)))),...e]}async#g(t,e){let a;t.pendingSearchTerms=e;try{a=await this.#l.searchUnavailableTitles(e,15)}catch(t){if(!(0,h.hb)(t)&&!(0,o.Rk)(t))throw t;a=[]}e===t.pendingSearchTerms&&(t.searchResults=a.map((t=>({resultType:"title-metadata",result:t,isInstalled:!1}))),t.triggerResultsRefresh(),t.pendingSearchTerms=null)}#b(t){return this.#u(Object.values(this.state.correlatedUnavailableTitles).filter((t=>t.games.some((t=>!(0,p.Lt)(t.flags,d.rT.Unsupported))))),t.pendingSearchTerms??"").map((t=>({resultType:"title-metadata",result:t,isInstalled:!0})))}#u(t,e){return(0,g.$)(t,e,["name","terms"],"name")}};b=(0,s.Cg)([(0,c.m6)({setup:"activate",teardown:"deactivate",selectors:{state:t=>t.state.pipe((0,m.r)("catalog","favoriteTitles","installedGameVersions","gameHistory","correlatedUnavailableTitles")),cachedTrainers:(0,c.$t)((t=>t.trainers))}}),(0,i.autoinject)(),(0,s.Sn)("design:paramtypes",[o.WA,r.x,l.EventAggregator])],b)}}]);
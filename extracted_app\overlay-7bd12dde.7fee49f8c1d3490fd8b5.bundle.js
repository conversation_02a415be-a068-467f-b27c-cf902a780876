"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7067],{15182:(e,o,s)=>{s.d(o,{Xz:()=>i,pF:()=>a});const r=["save_mods","pin_mods","remote","game_guide","boosts"],t=[...r,"themes","no_ads","support"],i=e=>r.includes(e),a=e=>t.includes(e)},"pro-promos/choose-plan-promo/choose-plan-promo":(e,o,s)=>{s.r(o),s.d(o,{ChoosePlanPromo:()=>i});var r=s(15215),t=s("aurelia-framework");class i{}(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Function)],i.prototype,"onProCtaClick",void 0)},"pro-promos/choose-plan-promo/choose-plan-promo.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r='<template> <require from="./choose-plan-promo.scss"></require> <require from="pro-promos/resources/elements/pro-promo-copy"></require> <require from="./resources/elements/plan-selector"></require> <div class="choose-plan-promo-container"> <pro-promo-copy tagline="${\'choose_plan_promo.tagline\' | i18n}" description="${\'choose_plan_promo.description\' | i18n}" heading="${\'choose_plan_promo.heading\' | i18n}"> </pro-promo-copy> <plan-selector on-pro-cta-click.call="onProCtaClick()"> </plan-selector> </div> </template> '},"pro-promos/choose-plan-promo/choose-plan-promo.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>m});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i),n=s(4417),l=s.n(n),p=new URL(s(62590),s.b),c=a()(t()),u=l()(p);c.push([e.id,`choose-plan-promo .choose-plan-promo-container{display:flex;flex-direction:row;align-items:center;min-height:584px;padding:32px;gap:48px;background-image:url(${u});border-radius:20px;overflow:hidden}`,""]);const m=c},"pro-promos/choose-plan-promo/resources/elements/plan-feature":(e,o,s)=>{s.r(o),s.d(o,{PlanFeature:()=>r});class r{}},"pro-promos/choose-plan-promo/resources/elements/plan-feature.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r='<template> <require from="./plan-feature.scss"></require> <span class="plan-feature-icon"> <slot name="icon"></slot> </span> <span class="plan-feature-description"> <slot></slot> </span> </template> '},"pro-promos/choose-plan-promo/resources/elements/plan-feature.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,'plan-feature{gap:8px}plan-feature .plan-feature-description{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px}plan-feature .plan-feature-icon{display:inline-flex}',""]);const n=a},"pro-promos/choose-plan-promo/resources/elements/plan-selector":(e,o,s)=>{s.r(o),s.d(o,{PlanSelector:()=>p});var r=s(15215),t=s(96111),i=s("aurelia-framework"),a=s(20770),n=s(71341),l=s(68539);let p=class{#e;constructor(e,o,s,r){this.currentPlan="pro",this.#e=e,this.timeLimitEnforcer=r}get hasTimeLimit(){return this.timeLimitEnforcer.isEnabled}get hasInteractiveControls(){return this.timeLimitEnforcer.canUseInAppControls}handleOptionClick(e){this.currentPlan=e}handleProCtaClick(){this.onProCtaClick?.(),"pro"===this.currentPlan&&this.#e.open({trigger:"choose_plan_promo"})}};(0,r.Cg)([i.bindable,(0,r.Sn)("design:type",Function)],p.prototype,"onProCtaClick",void 0),(0,r.Cg)([(0,i.computedFrom)("timeLimitEnforcer.isEnabled"),(0,r.Sn)("design:type",Boolean),(0,r.Sn)("design:paramtypes",[])],p.prototype,"hasTimeLimit",null),(0,r.Cg)([(0,i.computedFrom)("timeLimitEnforcer.canUseInAppControls"),(0,r.Sn)("design:type",Boolean),(0,r.Sn)("design:paramtypes",[])],p.prototype,"hasInteractiveControls",null),p=(0,r.Cg)([(0,i.autoinject)(),(0,r.Sn)("design:paramtypes",[n.U,a.il,l.z,t.Y])],p)},"pro-promos/choose-plan-promo/resources/elements/plan-selector.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(14385),t=s.n(r),i=new URL(s(62152),s.b),a=new URL(s(77103),s.b);const n='<template> <require from="./plan-selector.scss"></require> <require from="./plan-feature"></require> <require from="resources/elements/pro-cta-label"></require> <div class="plan-selector-container" role="radiogroup" aria-labelledby="plan-selector-group-label"> <h2 id="plan-selector-group-label" class="plan-selector-heading"> ${\'choose_plan_promo.choose_your_plan\' | i18n} </h2> <div class="plan-selector-option ${currentPlan === \'free\' ? \'plan-selector-option--selected\' : \'\'}" click.delegate="handleOptionClick(\'free\')" tabindex="0"> <label for="free"> <div class="plan-selector-option-heading"> ${\'choose_plan_promo.free\' | i18n} <input type="radio" value="free" model.bind="currentPlan" name="plan-group" tabindex="-1"> </div> <div class="plan-selector-feature-grid"> <plan-feature if.bind="!hasTimeLimit"> <span class="plan-selector-feature-icon" slot="icon">all_inclusive</span> ${\'choose_plan_promo.pro_unlimited_modding\' | i18n} </plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon"><inline-svg src="'+t()(i)+'"></inline-svg></span> ${\'choose_plan_promo.free_mod_count\' | i18n} </plan-feature> <plan-feature if.bind="hasInteractiveControls"> <span class="plan-selector-feature-icon" slot="icon">page_info</span> ${\'choose_plan_promo.free_interactive_controls\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">layers</span> ${\'choose_plan_promo.free_overlay\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">map</span> ${\'choose_plan_promo.free_maps\' | i18n}</plan-feature> </div> </label> </div> <div class="plan-selector-option plan-selector-option--pro ${currentPlan === \'pro\' ? \'plan-selector-option--selected\' : \'\'}" click.delegate="handleOptionClick(\'pro\')" tabindex="0"> <label for="pro"> <div class="plan-selector-option-heading plan-selector-option-heading"> ${\'choose_plan_promo.pro\' | i18n} <input type="radio" value="pro" model.bind="currentPlan" name="plan-group" tabindex="-1"> </div> <div class="plan-selector-option-pro-badge"> <span class="plan-selector-option-pro-badge-icon"></span> ${\'choose_plan_promo.everything_in_free\' | i18n} </div> <div class="plan-selector-feature-grid"> <plan-feature if.bind="hasTimeLimit"> <span class="plan-selector-feature-icon" slot="icon">all_inclusive</span> ${\'choose_plan_promo.pro_unlimited_modding\' | i18n} </plan-feature> <plan-feature if.bind="!hasInteractiveControls"> <span class="plan-selector-feature-icon" slot="icon">page_info</span> ${\'choose_plan_promo.free_interactive_controls\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">keep</span> ${\'choose_plan_promo.pro_pin_mods\' | i18n} </plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon"><inline-svg src="'+t()(a)+'"></inline-svg></span>${\'choose_plan_promo.pro_save_mods\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">smartphone</span> ${\'choose_plan_promo.pro_remote_app\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">colors</span> ${\'choose_plan_promo.pro_custom_themes\' | i18n}</plan-feature> <plan-feature> <span class="plan-selector-feature-icon" slot="icon">ad_off</span> ${\'choose_plan_promo.pro_no_ads\' | i18n} </plan-feature> </div> </label> </div> <wm-button color.bind="currentPlan === \'free\' ? \'inverse\' : \'primary\'" click.delegate="handleProCtaClick()" full-width.bind="true"> <pro-cta-label if.bind="currentPlan === \'pro\'"></pro-cta-label> <span else>${\'choose_plan_promo.continue_with_free\' | i18n}</span> </wm-button> </div> </template> '},"pro-promos/choose-plan-promo/resources/elements/plan-selector.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>m});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i),n=s(4417),l=s.n(n),p=new URL(s(83959),s.b),c=a()(t()),u=l()(p);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${u}) format("woff2")}.material-symbols-outlined,plan-selector .plan-selector-feature-icon,plan-selector .plan-selector-option-pro-badge-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}plan-selector .plan-selector-container{display:flex;flex-direction:column;align-items:center;gap:12px}plan-selector .plan-selector-container wm-button{width:100%}plan-selector .plan-selector-heading{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;margin:0;color:var(--theme--text-primary)}plan-selector .plan-selector-feature-grid{display:grid;grid-template-columns:repeat(2, 1fr);row-gap:16px;column-gap:8px}plan-selector .plan-selector-feature-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--text-highlight);height:20px;width:20px}plan-selector .plan-selector-option{cursor:pointer}plan-selector .plan-selector-option *{cursor:pointer}plan-selector .plan-selector-option{width:100%;padding:20px;display:flex;border:1px solid rgba(255,255,255,.15);background-color:rgba(255,255,255,.05);border-radius:16px;backdrop-filter:blur(25px);color:var(--theme--text-primary)}plan-selector .plan-selector-option-pro-badge{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-primary);display:flex;align-items:center;justify-content:center;gap:8px;width:100%;padding:8px 0;border-radius:8px;background-color:rgba(255,255,255,.05)}plan-selector .plan-selector-option-pro-badge-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--text-highlight)}plan-selector .plan-selector-option-pro-badge-icon:before{font-family:inherit;content:"new_releases"}plan-selector .plan-selector-option--pro{background:radial-gradient(141.43% 141.42% at 100% 0%, rgba(31, 186, 248, 0.49) 0%, rgba(42, 155, 249, 0.48) 9.99%, rgba(56, 116, 251, 0.47) 21.4%, rgba(71, 67, 252, 0.4) 38.79%, rgba(97, 0, 255, 0.15) 58.28%, rgba(50, 0, 87, 0.05) 79.41%, rgba(50, 0, 87, 0) 100%),rgba(255,255,255,.03)}plan-selector .plan-selector-option--pro .plan-selector-option-heading{font-feature-settings:"liga" off,"clig" off;font-size:36px;font-style:italic;font-weight:900;line-height:100%;letter-spacing:-1.5px}plan-selector .plan-selector-option label{width:100%;display:flex;flex-direction:column;gap:16px}plan-selector .plan-selector-option--selected{border:1px solid #fff;background-color:rgba(255,255,255,.15);color:var(--theme--text-highlight)}plan-selector .plan-selector-option--selected.plan-selector-option--pro{background:radial-gradient(141.43% 141.42% at 100% 0%, rgba(31, 186, 248, 0.97) 0%, rgba(42, 155, 249, 0.95) 9.99%, rgba(56, 116, 251, 0.93) 21.4%, rgba(71, 67, 252, 0.8) 38.79%, rgba(97, 0, 255, 0.3) 58.28%, rgba(50, 0, 87, 0.1) 79.41%, rgba(50, 0, 87, 0) 100%),rgba(255,255,255,.03)}plan-selector .plan-selector-option--selected .plan-selector-option-heading::after{border:5px solid var(--theme--text-highlight);width:6px;height:6px}plan-selector .plan-selector-option-heading{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:24px;line-height:28px;letter-spacing:-1px;display:flex;align-items:center;justify-content:space-between;color:var(--theme--text-highlight)}plan-selector .plan-selector-option-heading input{appearance:none;position:absolute;opacity:0}plan-selector .plan-selector-option-heading::after{content:"";display:block;width:14px;height:14px;border-radius:50%;border:1px solid var(--theme--text-primary)}plan-selector .plan-selector label:has(input:checked){color:red}plan-selector plan-feature{display:flex;align-items:center}`,""]);const m=c},"pro-promos/pro-showcase-columns/pro-showcase-columns":(e,o,s)=>{s.r(o),s.d(o,{ProShowcaseColumns:()=>d});var r=s(15215),t=s(96111),i=s("aurelia-framework"),a=s(20770),n=s(71341),l=s(68539),p=s(92465),c=s("shared/i18n/resources/value-converters"),u=s(54995);const m=["save_mods","pin_mods","remote","game_guide","themes","no_ads","boosts"];let d=class{#o;#s;#r;constructor(e,o,s,r,t){this.currentFeature="save_mods",this.hasHoveredFeature=!1,this.#s=e,this.#r=o,this.timeLimitEnforcer=t}attached(){this.#t()}detached(){this.#o?.dispose()}get hasTimeLimit(){return this.timeLimitEnforcer.isEnabled}get hasInteractiveControls(){return this.timeLimitEnforcer.canUseInAppControls}bind(){this.defaultFeature&&(this.currentFeature=this.defaultFeature)}get localizedGamesCount(){return this.#s.toView(3e3)}get localizedModsCount(){return this.#s.toView(35e3)}get localizedMapsCount(){return this.#s.toView(this.catalog.maps.length)}handleContinueClick(){this.onClose?.({result:!1})}handleUpgradeClick(){this.#r.open({trigger:"pro_showcase_columns"}),this.onClose?.({result:!0})}setCurrentFeature(e){this.currentFeature=e,this.hasHoveredFeature=!0}#t(){this.#o=(0,p.SO)((()=>{if(this.hasHoveredFeature)return;const e=(m.indexOf(this.currentFeature)+1)%m.length;this.currentFeature=m[e]}),2e3)}};(0,r.Cg)([i.bindable,(0,r.Sn)("design:type",String)],d.prototype,"defaultFeature",void 0),(0,r.Cg)([i.bindable,(0,r.Sn)("design:type",Function)],d.prototype,"onClose",void 0),(0,r.Cg)([(0,i.computedFrom)("timeLimitEnforcer.isEnabled"),(0,r.Sn)("design:type",Boolean),(0,r.Sn)("design:paramtypes",[])],d.prototype,"hasTimeLimit",null),(0,r.Cg)([(0,i.computedFrom)("timeLimitEnforcer.canUseInAppControls"),(0,r.Sn)("design:type",Boolean),(0,r.Sn)("design:paramtypes",[])],d.prototype,"hasInteractiveControls",null),(0,r.Cg)([(0,i.computedFrom)("language"),(0,r.Sn)("design:type",String),(0,r.Sn)("design:paramtypes",[])],d.prototype,"localizedGamesCount",null),(0,r.Cg)([(0,i.computedFrom)("language"),(0,r.Sn)("design:type",String),(0,r.Sn)("design:paramtypes",[])],d.prototype,"localizedModsCount",null),(0,r.Cg)([(0,i.computedFrom)("language","catalog"),(0,r.Sn)("design:type",String),(0,r.Sn)("design:paramtypes",[])],d.prototype,"localizedMapsCount",null),d=(0,r.Cg)([(0,u.m6)({selectors:{catalog:(0,u.$t)((e=>e.catalog)),language:(0,u.$t)((e=>e.settings.language))}}),(0,i.autoinject)(),(0,r.Sn)("design:paramtypes",[c.I18nNumberValueConverter,n.U,a.il,l.z,t.Y])],d)},"pro-promos/pro-showcase-columns/pro-showcase-columns.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>g});var r=s(14385),t=s.n(r),i=new URL(s(62152),s.b),a=new URL(s(77103),s.b),n=new URL(s(63803),s.b),l=new URL(s(59184),s.b),p=new URL(s(43159),s.b),c=new URL(s(70150),s.b),u=new URL(s(20363),s.b),m=new URL(s(48021),s.b),d=new URL(s(72583),s.b),h=new URL(s(49388),s.b);const g='<template> <require from="./pro-showcase-columns.scss"></require> <require from="./resources/elements/pro-showcase-columns-list-item"></require> <require from="./resources/elements/pro-showcase-columns-feature"></require> <require from="./resources/elements/hover-me"></require> <require from="resources/elements/pro-cta-label"></require> <require from="pro-promos/resources/elements/faux-mods-ui.html"></require> <require from="shared/pro-promos/pin-mods-illustration"></require> <require from="shared/pro-promos/game-guide-illustration"></require> <section> <header>${\'pro_showcase_columns.basic\' | i18n}</header> <ul> <li if.bind="!hasTimeLimit"> <pro-showcase-columns-list-item icon="all_inclusive" label="${\'pro_showcase_columns.unlimited_modding\' | i18n}"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item label="${\'pro_showcase_columns.$games_games_with_$mods_mods\' | i18n:{games: localizedGamesCount, mods:\n                localizedModsCount}}"> <span slot="customIcon"> <span><inline-svg src="'+t()(i)+'"></inline-svg></span> </span> </pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="layers" label="${\'pro_showcase_columns.native_overlay\' | i18n}"></pro-showcase-columns-list-item> </li> <li if.bind="hasInteractiveControls"> <pro-showcase-columns-list-item icon="page_info" label="${\'pro_showcase_columns.interactive_controls\' | i18n}"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="map" label="${\'pro_showcase_columns.$count_maps\' | i18n:{count: localizedMapsCount}}"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="sprint" label="${\'pro_showcase_columns.one_click_teleport\' | i18n}"> </pro-showcase-columns-list-item> </li> </ul> <footer> <wm-button style="--wm-button-overlay-opacity:0.15" color="inverse" click.delegate="handleContinueClick()">${\'pro_showcase_columns.continue_with_free\' | i18n}</wm-button> </footer> <hover-me if.bind="!hasHoveredFeature"></hover-me> </section> <section> <header> <em>${\'pro_showcase_columns.pro\' | i18n}</em> </header> <ul> <li> <pro-showcase-columns-list-item icon="new_releases" label="${\'pro_showcase_columns.everything_in_free_plus\' | i18n}"></pro-showcase-columns-list-item> </li> <li if.bind="!hasInteractiveControls"> <pro-showcase-columns-list-item icon="page_info" label="${\'pro_showcase_columns.interactive_controls\' | i18n}"></pro-showcase-columns-list-item> </li> <li else if.bind="hasTimeLimit"> <pro-showcase-columns-list-item icon="all_inclusive" label="${\'pro_showcase_columns.unlimited_modding\' | i18n}"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item can-hover.bind="true" is-popular.bind="true" label="${\'pro_showcase_columns.save_mods\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'save_mods\')" is-current.bind="currentFeature === \'save_mods\'"> <i slot="customIcon"> <span><inline-svg src="'+t()(a)+'"></inline-svg></span> </i> </pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="keep" can-hover.bind="true" label="${\'pro_showcase_columns.pin_mods\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'pin_mods\')" is-current.bind="currentFeature === \'pin_mods\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="smartphone" can-hover.bind="true" label="${\'pro_showcase_columns.mobile_remote_app\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'remote\')" is-current.bind="currentFeature === \'remote\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item can-hover.bind="true" label="${\'pro_showcase_columns.game_guides\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'game_guide\')" is-current.bind="currentFeature === \'game_guide\'"> <i slot="customIcon"> <img src="'+t()(n)+'"> </i> </pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="colors" can-hover.bind="true" label="${\'pro_showcase_columns.custom_themes\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'themes\')" is-current.bind="currentFeature === \'themes\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="ad_off" can-hover.bind="true" label="${\'pro_showcase_columns.no_ads\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'no_ads\')" is-current.bind="currentFeature === \'no_ads\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="double_arrow" can-hover.bind="true" label="${\'pro_showcase_columns.boost_games\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'boosts\')" is-current.bind="currentFeature === \'boosts\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="support" label="${\'pro_showcase_columns.priority_support_and_more\' | i18n}"></pro-showcase-columns-list-item> </li> </ul> <footer> <wm-button click.delegate="handleUpgradeClick()"><pro-cta-label></pro-cta-label></wm-button> </footer> </section> <section> <pro-showcase-columns-feature if.bind="currentFeature === \'save_mods\'" is-popular.bind="true" title="${\'pro_showcase_columns.save_mods_title_case\' | i18n}" description="${\'pro_showcase_columns.save_mods_description\' | i18n}" image-src="'+t()(l)+'"> <faux-mods-ui></faux-mods-ui> </pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'pin_mods\'" title="${\'pro_showcase_columns.pin_mods_title_case\' | i18n}" description="${\'pro_showcase_columns.pin_mods_description\' | i18n}" image-src="'+t()(p)+'" video-src="https://media.wemod.com/videos/upgrade-promo/pro-showcase-columns/pin-mods.webm" video-type="video/webm"> <pin-mods-illustration></pin-mods-illustration> </pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'remote\'" title="${\'pro_showcase_columns.remote_title_case\' | i18n}" description="${\'pro_showcase_columns.remote_description\' | i18n}" image-src="'+t()(c)+'" video-src="https://media.wemod.com/videos/upgrade-promo/pro-showcase-columns/remote.webm" video-type="video/webm"></pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'game_guide\'" title="${\'pro_showcase_columns.game_guide_title_case\' | i18n}" description="${\'pro_showcase_columns.game_guide_description\' | i18n}" image-src="'+t()(u)+'" video-src="https://media.wemod.com/videos/upgrade-promo/pro-showcase-columns/game-guide.webm" video-type="video/webm"> <game-guide-illustration></game-guide-illustration> </pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'themes\'" title="${\'pro_showcase_columns.themes_title_case\' | i18n}" description="${\'pro_showcase_columns.themes_description\' | i18n}" image-src="'+t()(m)+'"></pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'no_ads\'" title="${\'pro_showcase_columns.no_ads_title_case\' | i18n}" description="${\'pro_showcase_columns.no_ads_description\' | i18n}" image-src="'+t()(d)+'"></pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'boosts\'" title="${\'pro_showcase_columns.boosts_title_case\' | i18n}" description="${\'pro_showcase_columns.boosts_description\' | i18n}" image-src="'+t()(h)+'" video-src="https://media.wemod.com/videos/upgrade-promo/pro-showcase-columns/boosts.webm" video-type="video/webm"></pro-showcase-columns-feature> </section> </template> '},"pro-promos/pro-showcase-columns/pro-showcase-columns.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,"pro-showcase-columns{--pro-showcase-accent: #fa1280;background:var(--theme--background) linear-gradient(rgba(255, 255, 255, 0.025), rgba(255, 255, 255, 0.025)) !important;display:flex;width:900px;min-height:612px;height:100%}pro-showcase-columns>section{width:320px;padding:20px;display:flex;flex-direction:column;position:relative}pro-showcase-columns>section:first-child{width:260px;background:rgba(255,255,255,.025)}pro-showcase-columns>section:nth-child(2){--pro-showcase-columns-list-item--icon-color: #fff}pro-showcase-columns>section:last-child{padding:0}pro-showcase-columns>section>header{font-weight:400;font-size:36px;line-height:36px;letter-spacing:-1px;color:#fff;margin-bottom:12px}pro-showcase-columns>section>header em{font-weight:900;font-style:italic;letter-spacing:-1.5px;color:#fff}pro-showcase-columns>section>ul{list-style:none;padding:0;margin:0;flex:1 0 auto}pro-showcase-columns>section>footer .wm-button{display:block;width:100%;justify-content:center}pro-showcase-columns faux-mods-ui{display:block;margin:50px auto 0;width:280px;transform:scale(1.2)}pro-showcase-columns game-guide-illustration{transform:scale(0.9);display:block;margin-bottom:-50px}pro-showcase-columns pin-mods-illustration{display:block;transform:scale(0.8) translate(-54px, 120px)}pro-showcase-columns hover-me{position:absolute;right:25px;bottom:200px}",""]);const n=a},"pro-promos/pro-showcase-columns/resources/elements/hover-me":(e,o,s)=>{s.r(o),s.d(o,{HoverMe:()=>r});class r{}},"pro-promos/pro-showcase-columns/resources/elements/hover-me.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r="<template> <require from=\"./hover-me.scss\"></require> ${'hover_me.hover_me' | i18n} </template> "},"pro-promos/pro-showcase-columns/resources/elements/hover-me.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,'hover-me{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;display:inline-block;color:var(--theme--background);text-transform:uppercase;padding:4px 6px;position:relative;z-index:0;border-radius:6px;background:#fff;animation:hover-me-bounce 1s ease-in-out infinite}hover-me:before{content:"";position:absolute;right:-5px;top:2.5px;border-radius:6px;background:#fff;transform:rotate(45deg);width:20px;height:20px;z-index:-1}@keyframes hover-me-bounce{0%{transform:translateX(0)}20%{transform:translateX(-6px)}38%{transform:translateX(2px)}40%{transform:translateX(0)}}',""]);const n=a},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-feature":(e,o,s)=>{s.r(o),s.d(o,{ProShowcaseColumnsFeature:()=>i});var r=s(15215),t=s("aurelia-framework");class i{constructor(){this.isPopular=!1}}(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Boolean)],i.prototype,"isPopular",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],i.prototype,"title",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],i.prototype,"description",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Object)],i.prototype,"imageSrc",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Object)],i.prototype,"videoSrc",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Object)],i.prototype,"videoType",void 0)},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-feature.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r='<template class="pro-showcase-columns-feature"> <require from="./pro-showcase-columns-feature.scss"></require> <require from="pro-promos/resources/elements/pro-promo-badge"></require> <div class="pro-showcase-columns-feature__other"> <slot></slot> </div> <div class="pro-showcase-columns-feature__content"> <pro-promo-badge if.bind="isPopular" size="m">${\'pro_showcase_columns.popular\' | i18n}</pro-promo-badge> <h1>${title}</h1> <p>${description}</p> </div> <div class="pro-showcase-columns-feature__background"> <wm-background-video if.bind="videoSrc" slot="bg" poster.bind="imageSrc" src.bind="videoSrc" type.bind="videoType"></wm-background-video> <img if.bind="!videoSrc" src.bind="imageSrc"> </div> </template> '},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-feature.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,'.pro-showcase-columns-feature{height:100%;display:flex;flex-direction:column;justify-content:flex-end;position:relative;z-index:0}.pro-showcase-columns-feature__background{position:absolute;z-index:-1;top:0;left:0;width:100%;height:100%}.pro-showcase-columns-feature__background img{position:absolute;left:0;top:0;width:100%;height:100%;object-fit:cover}.pro-showcase-columns-feature__background wm-background-video{position:absolute;left:0;top:0;width:100%;height:100%}.pro-showcase-columns-feature__other{flex:1 1 auto;overflow:hidden}.pro-showcase-columns-feature__content{flex:0 0 auto;display:flex;flex-direction:column;align-items:center;text-align:center;position:relative;z-index:0;padding:30px 30px 70px;overflow:hidden}.pro-showcase-columns-feature__content h1{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;color:rgba(255,255,255,.9);margin:0 0 4px}.pro-showcase-columns-feature__content p{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:28px;color:rgba(255,255,255,.9);margin:0;padding:0}',""]);const n=a},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-list-item":(e,o,s)=>{s.r(o),s.d(o,{ProShowcaseColumnsListItem:()=>i});var r=s(15215),t=s("aurelia-framework");class i{constructor(){this.isPopular=!1,this.canHover=!1}}(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Boolean)],i.prototype,"isPopular",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Boolean)],i.prototype,"canHover",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],i.prototype,"label",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],i.prototype,"icon",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Boolean)],i.prototype,"isCurrent",void 0)},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-list-item.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r='<template class="pro-showcase-columns-list-item ${canHover ? \'pro-showcase-columns-list-item--can-hover\' : \'\'} ${isCurrent ? \'pro-showcase-columns-list-item--is-current\' : \'\'}"> <require from="./pro-showcase-columns-list-item.scss"></require> <require from="pro-promos/resources/elements/pro-promo-badge"></require> <i> <template if.bind="icon">${icon}</template> <slot name="customIcon"></slot> </i> <span>${label}</span> <pro-promo-badge if.bind="isPopular" size="s">${\'pro_showcase_columns.popular\' | i18n}</pro-promo-badge> </template> '},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-list-item.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>m});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i),n=s(4417),l=s.n(n),p=new URL(s(83959),s.b),c=a()(t()),u=l()(p);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${u}) format("woff2")}.material-symbols-outlined,.pro-showcase-columns-list-item>i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.pro-showcase-columns-list-item{display:flex;align-items:center;color:var(--theme--text-primary);padding:12px 0;position:relative;z-index:0}.pro-showcase-columns-list-item>i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;flex:0 0 20px;display:inline-flex;width:20px;align-items:center;margin-right:9px;overflow:hidden;color:var(--pro-showcase-columns-list-item--icon-color, rgba(255, 255, 255, 0.8))}.pro-showcase-columns-list-item>i img{width:20px;height:20px}.pro-showcase-columns-list-item>span{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;flex:0 1 auto}.pro-showcase-columns-list-item>pro-promo-badge{flex:0 0 auto;margin-left:8px}.pro-showcase-columns-list-item:before{content:"";position:absolute;left:-10px;top:0;bottom:0;right:-10px;border-radius:8px;background-color:#fff;z-index:-1;opacity:0;transition:opacity .3s}.pro-showcase-columns-list-item--can-hover:hover{color:var(--theme--background)}.pro-showcase-columns-list-item--can-hover:hover>i{color:var(--theme--background)}.pro-showcase-columns-list-item--can-hover:hover:before{opacity:1}.pro-showcase-columns-list-item--is-current{color:var(--theme--background)}.pro-showcase-columns-list-item--is-current>i{color:var(--theme--background)}.pro-showcase-columns-list-item--is-current:before{opacity:1}`,""]);const m=c},"pro-promos/pro-showcase/pro-showcase":(e,o,s)=>{s.r(o),s.d(o,{ProShowcase:()=>i});var r=s(15215),t=s("aurelia-framework");class i{constructor(){this.currentFeature="save_mods"}}(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],i.prototype,"currentFeature",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Function)],i.prototype,"onProCtaClick",void 0)},"pro-promos/pro-showcase/pro-showcase.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>m});var r=s(14385),t=s.n(r),i=new URL(s(20909),s.b),a=new URL(s(89041),s.b),n=new URL(s(99845),s.b),l=new URL(s(95926),s.b),p=new URL(s(9278),s.b),c=new URL(s(77103),s.b),u=new URL(s(63803),s.b);const m='<template> <require from="./pro-showcase.scss"></require> <require from="./resources/elements/pro-showcase-feature"></require> <require from="./resources/elements/pro-showcase-option"></require> <require from="shared/pro-promos/save-mods-illustration"></require> <require from="shared/pro-promos/pin-mods-illustration"></require> <require from="shared/pro-promos/game-guide-illustration"></require> <div class="pro-showcase"> <div class="pro-showcase-feature-display"> <pro-showcase-feature if.bind="currentFeature === \'save_mods\'" feature="${\'pro_showcase.save_mods\' | i18n}" description="${\'pro_showcase.save_mods_description\' | i18n}" is-popular.bind="true" on-pro-cta-click.call="onProCtaClick()"> <wm-background-video slot="bg" poster="'+t()(i)+'" src="https://media.wemod.com/videos/upgrade-promo/pro-showcase/save-mods.webm" type="video/webm"> </wm-background-video> <save-mods-illustration></save-mods-illustration> </pro-showcase-feature> <pro-showcase-feature if.bind="currentFeature === \'pin_mods\'" feature="${\'pro_showcase.pin_mods\' | i18n}" description="${\'pro_showcase.pin_mods_description\' | i18n}" on-pro-cta-click.call="onProCtaClick()"> <wm-background-video slot="bg" poster="'+t()(a)+'" src="https://media.wemod.com/videos/upgrade-promo/pro-showcase/pin-mods.webm" type="video/webm"> </wm-background-video> <pin-mods-illustration></pin-mods-illustration> </pro-showcase-feature> <pro-showcase-feature if.bind="currentFeature === \'remote\'" feature="${\'pro_showcase.remote\' | i18n}" description="${\'pro_showcase.remote_description\' | i18n}" on-pro-cta-click.call="onProCtaClick()"> <img slot="bg" src="'+t()(n)+'"> </pro-showcase-feature> <pro-showcase-feature if.bind="currentFeature === \'boosts\'" feature="${\'pro_showcase.boosts\' | i18n}" description="${\'pro_showcase.boosts_description\' | i18n}" on-pro-cta-click.call="onProCtaClick()"> <wm-background-video slot="bg" poster="'+t()(l)+'" src="https://media.wemod.com/videos/upgrade-promo/pro-showcase/boost.webm" type="video/webm"> </wm-background-video> </pro-showcase-feature> <pro-showcase-feature if.bind="currentFeature === \'game_guide\'" feature="${\'pro_showcase.game_guide\' | i18n}" description="${\'pro_showcase.game_guide_description\' | i18n}" on-pro-cta-click.call="onProCtaClick()"> <wm-background-video slot="bg" poster="'+t()(p)+'" src="https://media.wemod.com/videos/upgrade-promo/pro-showcase/game-guide.webm" type="video/webm"> </wm-background-video> <game-guide-illustration></game-guide-illustration> </pro-showcase-feature> </div> <div class="pro-showcase-option-list"> <pro-showcase-option mouseover.delegate="currentFeature = \'remote\'" selected.bind="currentFeature === \'remote\'"> <content slot="title"> ${\'pro_showcase.remote\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.remote_subtitle\' | i18n}</content> <content slot="icon"> <span class="pro-showcase-option-font-icon"> devices </span> </content> </pro-showcase-option> <pro-showcase-option mouseover.delegate="currentFeature = \'save_mods\'" selected.bind="currentFeature === \'save_mods\'" is-popular.bind="true"> <content slot="title"> ${\'pro_showcase.save_mods\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.save_mods_subtitle\' | i18n}</content> <content slot="icon"> <span class="save-cheats-icon"> <inline-svg src="'+t()(c)+'"></inline-svg> </span> </content> </pro-showcase-option> <pro-showcase-option mouseover.delegate="currentFeature = \'pin_mods\'" selected.bind="currentFeature === \'pin_mods\'"> <content slot="title"> ${\'pro_showcase.pin_mods\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.pin_mods_subtitle\' | i18n}</content> <content slot="icon"><span class="pro-showcase-option-font-icon--filled"> keep </span></content> </pro-showcase-option> <pro-showcase-option mouseover.delegate="currentFeature = \'boosts\'" selected.bind="currentFeature === \'boosts\'"> <content slot="title"> ${\'pro_showcase.boosts\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.boosts_subtitle\' | i18n}</content> <content slot="icon"><span class="pro-showcase-option-font-icon"> double_arrow </span></content> </pro-showcase-option> <pro-showcase-option mouseover.delegate="currentFeature = \'game_guide\'" selected.bind="currentFeature === \'game_guide\'"> <content slot="title"> ${\'pro_showcase.game_guide\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.game_guide_subtitle\' | i18n}</content> <content slot="icon"> <img src="'+t()(u)+'" alt="${\'pro_showcase.game_guide_assistant_icon_alt\' | i18n}"> </content> </pro-showcase-option> </div> </div> </template> '},"pro-promos/pro-showcase/pro-showcase.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>m});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i),n=s(4417),l=s.n(n),p=new URL(s(83959),s.b),c=a()(t()),u=l()(p);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${u}) format("woff2")}.material-symbols-outlined,pro-showcase .pro-showcase-option-font-icon,pro-showcase .pro-showcase-option-font-icon--filled{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}pro-showcase{--pro-showcase-accent: #fa1280}pro-showcase .pro-showcase{display:flex;flex-direction:column}pro-showcase .pro-showcase-feature-display{height:466px}pro-showcase .pro-showcase-option-list{display:flex;gap:4px;padding:20px;background-color:var(--theme--background);border-top:1px solid rgba(255,255,255,.15)}pro-showcase .pro-showcase-option-list pro-showcase-option{flex:1 1 0}pro-showcase .pro-showcase-option-list svg,pro-showcase .pro-showcase-option-list img{width:20px;height:20px}pro-showcase .pro-showcase-option-list img{border-radius:100px;overflow:hidden}pro-showcase .pro-showcase-option-font-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}pro-showcase .pro-showcase-option-font-icon--filled{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}`,""]);const m=c},"pro-promos/pro-showcase/resources/elements/pro-showcase-feature":(e,o,s)=>{s.r(o),s.d(o,{ProShowcaseFeature:()=>n});var r=s(15215),t=s("aurelia-framework"),i=s(71341),a=s(54995);let n=class{#e;constructor(e){this.#e=e}handleProCtaClick(){this.onProCtaClick?.(),this.subscription||this.#e.open({trigger:"pro_showcase"})}};(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],n.prototype,"feature",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],n.prototype,"description",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Boolean)],n.prototype,"isPopular",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Function)],n.prototype,"onProCtaClick",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],n.prototype,"proUserCtaKey",void 0),n=(0,r.Cg)([(0,t.autoinject)(),(0,a.m6)({selectors:{subscription:(0,a.$t)((e=>e.account?.subscription))}}),(0,r.Sn)("design:paramtypes",[i.U])],n)},"pro-promos/pro-showcase/resources/elements/pro-showcase-feature.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r='<template> <require from="./pro-showcase-feature.scss"></require> <require from="pro-promos/resources/elements/pro-promo-badge"></require> <require from="pro-promos/resources/elements/pro-promo-copy"></require> <require from="resources/elements/pro-cta-label"></require> <div class="pro-showcase-feature"> <div class="pro-showcase-feature-bg"> <slot name="bg"></slot> </div> <div class="pro-showcase-feature-fg"> <div class="pro-showcase-feature-column"> <pro-promo-copy tagline.bind="subscription ? \'pro_showcase.new_in_wemod_pro\' : \'pro_showcase.ready_to_level_up_go_pro\' | i18n | markdown" description.bind="description" heading.bind="feature" is-popular.bind="isPopular"> </pro-promo-copy> <wm-button class="free-trial-button" size="m" trailing-icon="arrow_forward" click.delegate="handleProCtaClick()"> <pro-cta-label if.bind="!subscription"></pro-cta-label> <span else>${(proUserCtaKey || \'pro_showcase.check_it_out\') | i18n}</span> </wm-button> </div> <div class="pro-showcase-feature-illustration"> <slot></slot> </div> </div> </div> </template> '},"pro-promos/pro-showcase/resources/elements/pro-showcase-feature.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,'pro-showcase-feature .pro-showcase-feature{display:flex;align-items:center;position:relative;width:100%;height:100%}pro-showcase-feature .pro-showcase-feature-fg{position:absolute;display:flex;align-items:center;justify-content:space-between;padding:0 70px 0 36px;width:100%}pro-showcase-feature .pro-showcase-feature-bg{width:100%;height:100%}pro-showcase-feature .pro-showcase-feature-bg>*{display:flex;width:100%;height:100%}pro-showcase-feature .pro-showcase-feature-column{width:100%;height:100%;gap:4;width:320px;max-width:320px}pro-showcase-feature .pro-showcase-feature-column-header{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:40px;line-height:48px;letter-spacing:-2.5px;font-weight:900;color:var(--theme--text-highlight);margin:0;font-style:italic}pro-showcase-feature .pro-showcase-feature-column-header-container{display:flex;gap:10px}pro-showcase-feature .pro-showcase-feature-column-header-container .popular-badge{margin-top:18px}pro-showcase-feature .pro-showcase-feature-column-tagline{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;color:var(--theme--text-highlight)}pro-showcase-feature .pro-showcase-feature-column-tagline em{font-weight:500;font-style:italic}pro-showcase-feature .pro-showcase-feature-column-description{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:28px;color:var(--theme--text-primary)}pro-showcase-feature .pro-showcase-feature-column .free-trial-button{display:block;margin-top:20px;width:fit-content}',""]);const n=a},"pro-promos/pro-showcase/resources/elements/pro-showcase-option":(e,o,s)=>{s.r(o),s.d(o,{ProShowcaseOption:()=>i});var r=s(15215),t=s("aurelia-framework");class i{}(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Boolean)],i.prototype,"selected",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Boolean)],i.prototype,"isPopular",void 0)},"pro-promos/pro-showcase/resources/elements/pro-showcase-option.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r='<template> <require from="./pro-showcase-option.scss"></require> <div class="pro-showcase-option ${selected ? \'pro-showcase-option--selected\' : \'\'}"> <span class="pro-showcase-option-icon"><slot name="icon"></slot></span> <span class="pro-showcase-option-title-container"><h2 class="pro-showcase-option-title"><slot name="title"></slot></h2> <span if.bind="isPopular" class="pro-showcase-option-popular-badge"> ${\'pro_showcase.popular\' | i18n} </span> </span> <span class="pro-showcase-option-subtitle"><slot name="subtitle"></slot></span> </div> </template> '},"pro-promos/pro-showcase/resources/elements/pro-showcase-option.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,'pro-showcase-option .pro-showcase-option{display:flex;flex-direction:column;color:var(--theme--text-primary);border-radius:16px;padding:12px;transition:background-color .2s}pro-showcase-option .pro-showcase-option--selected{background-color:rgba(255,255,255,.05)}pro-showcase-option .pro-showcase-option--selected .pro-showcase-option-icon{color:var(--theme--text-highlight)}pro-showcase-option .pro-showcase-option--selected .pro-showcase-option-title{color:var(--theme--text-highlight)}pro-showcase-option .pro-showcase-option-icon{margin-bottom:6px}pro-showcase-option .pro-showcase-option-popular-badge{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;color:var(--theme--text-highlight);font-size:8px;line-height:12px;height:min-content;padding:1px 3px;border-radius:4px;background-color:var(--pro-showcase-accent)}pro-showcase-option .pro-showcase-option-title{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:800;margin:0;font-style:italic}pro-showcase-option .pro-showcase-option-title-container{display:inline-flex;align-items:center;gap:6px}pro-showcase-option .pro-showcase-option-subtitle{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}',""]);const n=a},"pro-promos/resources/elements/faux-mods-ui.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(14385),t=s.n(r),i=new URL(s(51091),s.b),a=t()(i);const n='<template> <require from="./faux-mods-ui.scss"></require> <div class="faux-mods-ui"> <div class="faux-mods-ui-save-toggle"> <span class="faux-mods-ui-save-toggle-button"><i class="faux-mods-ui-save-toggle-button-icon"><inline-svg src="'+a+'"></inline-svg></i></span> ${\'faux_mods_ui.save_mods\' | i18n | markdown} </div> <ul class="faux-mods-ui-mods-list"> <li> <i class="faux-mods-ui-mod-icon"><inline-svg src="'+a+'"></inline-svg></i> <span class="faux-mods-ui-mod-copy">${\'faux_mods_ui.mod_unlimited_health\' | i18n | markdown}</span> <span class="faux-mods-ui-mod-toggle-button">${\'faux_mods_ui.mod_off\' | i18n | markdown} <span>${\'faux_mods_ui.mod_on\' | i18n | markdown}</span></span> </li> <li> <i class="faux-mods-ui-mod-icon"><inline-svg src="'+a+'"></inline-svg></i> <span class="faux-mods-ui-mod-copy">${\'faux_mods_ui.mod_unlimited_stamina\' | i18n | markdown}</span> <span class="faux-mods-ui-mod-toggle-button">${\'faux_mods_ui.mod_off\' | i18n | markdown} <span>${\'faux_mods_ui.mod_on\' | i18n | markdown}</span></span> </li> <li> <i class="faux-mods-ui-mod-icon"><inline-svg src="'+a+'"></inline-svg></i> <span class="faux-mods-ui-mod-copy">${\'faux_mods_ui.mod_game_speed\' | i18n | markdown}</span> <span class="faux-mods-ui-mod-slider"> <span class="value">${\'faux_mods_ui.mod_game_speed_value\' | i18n | markdown}</span> <span class="outer-bar"><span class="inner-bar"></span></span> </span> </li> </ul> </div> </template> '},"pro-promos/resources/elements/faux-mods-ui.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>m});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i),n=s(4417),l=s.n(n),p=new URL(s(11494),s.b),c=a()(t()),u=l()(p);c.push([e.id,`.faux-mods-ui{display:flex;align-items:center;flex-direction:column;width:100%;height:100%;padding:14px 28px;overflow:hidden;color:#fff}.faux-mods-ui-save-toggle{display:flex;align-items:center;gap:8px;border-radius:96px;background:rgba(255,255,255,.3);backdrop-filter:blur(12px);padding:10px 16px 10px 12px;font-size:13.5px;font-weight:700;margin:0 0 10px}.faux-mods-ui-save-toggle-button{display:block;position:relative;border-radius:12px;width:35px;height:21px;background:#fa1280}.faux-mods-ui-save-toggle-button-icon{position:absolute;top:2px;right:2px;display:flex;align-items:center;justify-content:center;width:17px;height:17px;border-radius:17px;background:#fff}.faux-mods-ui-save-toggle-button-icon svg{height:11px}.faux-mods-ui-save-toggle-button-icon svg *{fill:#fa1280}.faux-mods-ui-mods-list{display:block;border-radius:10px;background:rgba(255,255,255,.3);backdrop-filter:blur(12px);margin:0;font-size:10px;font-weight:500;width:100%;padding:0}.faux-mods-ui-mods-list li{display:flex;gap:9px;align-items:center;padding:7.5px 9px;margin:0}.faux-mods-ui-mod-icon svg{height:11px}.faux-mods-ui-mod-icon svg *{fill:#fa1280}.faux-mods-ui-mod-copy{flex-grow:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.faux-mods-ui-mod-toggle-button{padding:2px 2px 2px 8px;border-radius:21px;background:var(--white-20, rgba(255, 255, 255, 0.2));font-size:9px;line-height:200%;word-wrap:none;flex-shrink:0}.faux-mods-ui-mod-toggle-button span{display:inline-block;background:#fa1280;border-radius:10px;padding:0 6px;margin:0 0 0 4px}.faux-mods-ui-mod-slider{display:flex;align-items:center}.faux-mods-ui-mod-slider .value{background:var(--white-20, rgba(255, 255, 255, 0.2));border-radius:6px;font-size:9px;line-height:18px;padding:2px 6px;margin:0 4px 0}.faux-mods-ui-mod-slider .outer-bar{background:var(--white-20, rgba(255, 255, 255, 0.2));border-radius:6px;width:50px;height:22px;overflow:hidden}.faux-mods-ui-mod-slider .inner-bar{display:block;position:relative;height:100%;width:55%;background:#fa1280}.faux-mods-ui-mod-slider .inner-bar::after{display:block;position:absolute;content:"";top:0;right:0;transform:translateX(50%);width:12px;height:100%;border-radius:6px;background:#fff;box-shadow:0px 1px 1px rgba(0,0,0,.14),0px 2px 1px rgba(0,0,0,.12),0px 1px 3px rgba(0,0,0,.2);background-image:url(${u});background-position:center;background-repeat:no-repeat}`,""]);const m=c},"pro-promos/resources/elements/pro-promo-badge":(e,o,s)=>{s.r(o),s.d(o,{ProPromoBadge:()=>i});var r=s(15215),t=s(30960);class i{constructor(){this.size="m"}}(0,r.Cg)([t._t,(0,r.Sn)("design:type",String)],i.prototype,"size",void 0)},"pro-promos/resources/elements/pro-promo-badge.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r='<template> <require from="./pro-promo-badge.scss"></require> <span class="pro-promo-badge pro-promo-badge--${size}"> <slot></slot> </span> </template> '},"pro-promos/resources/elements/pro-promo-badge.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,'pro-promo-badge{display:inline-flex}pro-promo-badge .pro-promo-badge{height:min-content;color:var(--theme--text-highlight);background-color:var(--pro-showcase-accent)}pro-promo-badge .pro-promo-badge--s{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;font-size:8px;line-height:12px;padding:1px 3px;border-radius:4px}pro-promo-badge .pro-promo-badge--m{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;padding:2px 6px;border-radius:6px}',""]);const n=a},"pro-promos/resources/elements/pro-promo-copy":(e,o,s)=>{s.r(o),s.d(o,{ProPromoCopy:()=>i});var r=s(15215),t=s("aurelia-framework");class i{}(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],i.prototype,"heading",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],i.prototype,"description",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",Boolean)],i.prototype,"isPopular",void 0),(0,r.Cg)([t.bindable,(0,r.Sn)("design:type",String)],i.prototype,"tagline",void 0)},"pro-promos/resources/elements/pro-promo-copy.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>r});const r='<template> <require from="./pro-promo-copy.scss"></require> <div class="pro-promo-copy"> <span class="pro-promo-copy-tagline" innerhtml.bind="tagline"> </span> <div class="pro-promo-copy-header-container"> <h1 class="pro-promo-copy-header">${heading}</h1> <pro-promo-badge if.bind="isPopular" class="popular-badge"> ${\'pro_showcase.popular\' | i18n} </pro-promo-badge> </div> <span class="pro-promo-copy-description"> ${description}</span> </div> </template> '},"pro-promos/resources/elements/pro-promo-copy.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,'pro-promo-copy .pro-promo-copy{display:flex;flex-direction:column;width:100%;height:100%;gap:4;width:320px;max-width:320px}pro-promo-copy .pro-promo-copy-header{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:40px;line-height:48px;letter-spacing:-2.5px;font-weight:900;color:var(--theme--text-highlight);margin:0;font-style:italic}pro-promo-copy .pro-promo-copy-header-container{display:flex;gap:10px}pro-promo-copy .pro-promo-copy-header-container .popular-badge{margin-top:18px}pro-promo-copy .pro-promo-copy-tagline{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;color:var(--theme--text-highlight)}pro-promo-copy .pro-promo-copy-tagline em{font-weight:500;font-style:italic}pro-promo-copy .pro-promo-copy-description{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:28px;color:var(--theme--text-primary)}pro-promo-copy .pro-promo-copy-content-wrapper{display:block;margin-top:20px}',""]);const n=a},"queue/resources/elements/creators-list":(e,o,s)=>{s.r(o),s.d(o,{CreatorsList:()=>n});var r=s(15215),t=s("aurelia-framework"),i=s(61116),a=s("shared/i18n/resources/value-converters");let n=class{constructor(e,o){this.creators=e,this.i18nNumber=o}};n=(0,r.Cg)([(0,t.autoinject)(),(0,r.Sn)("design:paramtypes",[i.c,a.I18nNumberValueConverter])],n)},"queue/resources/elements/creators-list.html":(e,o,s)=>{s.r(o),s.d(o,{default:()=>a});var r=s(14385),t=s.n(r),i=new URL(s(89247),s.b);const a='<template> <require from="./creators-list.scss"></require> <div class="creators"> <div class="creator" repeat.for="creator of creators.creators"> <img src.bind="creator.avatar | cdn:{size: 48}" fallback-src="'+t()(i)+'"> <div class="meta"> <div class="username">${creator.username}</div> <div class="cheats" innerhtml.bind="(creator.gameCount === 1 ? \'creators_list.$x_game\' : \'creators_list.$x_games\') | i18n:{x: i18nNumber.toView(creator.gameCount)} | markdown"></div> </div> </div> </div> </template> '},"queue/resources/elements/creators-list.scss":(e,o,s)=>{s.r(o),s.d(o,{default:()=>n});var r=s(31601),t=s.n(r),i=s(76314),a=s.n(i)()(t());a.push([e.id,'creators-list .creator{display:flex;align-items:flex-start}creators-list .creator+.creator{margin-top:12px}creators-list .creator img{flex:0 0 auto;width:40px;height:40px;margin:0 14px 0 0;border-radius:50%;overflow:hidden}creators-list .creator .username{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px}.theme-default creators-list .creator .username{color:#fff}.theme-purple-pro creators-list .creator .username{color:#fff}.theme-green-pro creators-list .creator .username{color:#fff}.theme-orange-pro creators-list .creator .username{color:#fff}.theme-pro creators-list .creator .username{color:#fff}creators-list .creator .cheats{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default creators-list .creator .cheats{color:rgba(255,255,255,.4)}.theme-purple-pro creators-list .creator .cheats{color:rgba(255,255,255,.4)}.theme-green-pro creators-list .creator .cheats{color:rgba(255,255,255,.4)}.theme-orange-pro creators-list .creator .cheats{color:rgba(255,255,255,.4)}.theme-pro creators-list .creator .cheats{color:rgba(255,255,255,.4)}creators-list .creator .cheats strong{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}.theme-purple-pro creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}.theme-green-pro creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}.theme-orange-pro creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}.theme-pro creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}',""]);const n=a}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1516],{"queue/queue":(e,t,i)=>{i.r(t),i.d(t,{Queue:()=>m});var o=i(15215),a=i("aurelia-framework"),u=i(18776),r=i(62914),n=i(24008),s=i(44759),c=i(54995),l=i(70236);let m=class{#e;#t;#i;#o;constructor(e,t,i){this.#e=e,this.#t=t,this.#i=i}bind(){this.changeView(this.view)}catalogChanged(){this.#a()}activate(e){this.#o=this.#t.onStatusChanged((e=>this.#u(e))),this.#u(this.#t.status),this.view=e.view??"all",this.#r(e.gameId)}deactivate(){this.#o?.dispose(),this.#o=null}#a(){switch(this.view){case"recent":this.queueItems=Object.values(this.catalog.games).filter((e=>(0,l.Lt)(e.flags,n.rT.Available))).sort(((e,t)=>(t.trainer?.updatedAt??"").localeCompare(e.trainer?.updatedAt??""))).slice(0,5).map((e=>this.#n(e.id)));break;default:{const e={all:n.rT.Queued,updates:n.rT.UpdateQueued,releases:n.rT.ReleaseQueued}[this.view];this.queueItems=this.catalog.queue.map(((e,t)=>this.#n(e,t+1))).filter((t=>(0,l.Lt)(t.game.flags,e)));break}}}changeView(e){this.view=e,this.#a(),this.#e.screenView({name:{all:"Overview",updates:"Needs update",releases:"New releases",recent:"Recently completed"}[e],class:"Queue"})}#u(e){"online"!==e&&this.#i.navigateToRoute("dashboard")}#n(e,t=null){const i=this.catalog.games[e];return{game:i,title:this.catalog.titles[i.titleId],creator:i.creatorId?this.catalog.creators[i.creatorId]:void 0,position:t}}#r(e){setTimeout((()=>{const t=document.getElementById(`queue-game-${e}`);t&&t.scrollIntoView({behavior:"smooth",block:"center"})}))}};m=(0,o.Cg)([(0,c.m6)({selectors:{catalog:(0,c.$t)((e=>e.catalog)),availableBoosts:(0,c.$t)((e=>e.account?.boosts))}}),(0,a.autoinject)(),(0,o.Sn)("design:paramtypes",[r.j0,s.WA,u.Ix])],m)},"queue/queue.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>s});var o=i(14385),a=i.n(o),u=new URL(i(89247),i.b),r=new URL(i(57149),i.b),n=new URL(i(11858),i.b);const s='<template> <require from="./queue.scss"></require> <require from="./resources/elements/creators-list"></require> <require from="../resources/elements/boost-balance-button"></require> <require from="../resources/elements/boost-button"></require> <require from="../resources/elements/title-thumbnail"></require> <require from="../shared/resources/elements/info-tooltip"></require> <require from="../shared/resources/elements/tabs"></require> <require from="../shared/resources/elements/tab"></require> <section class="queue view-background au-animate"> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" overflow-fade="vertical"> <div class="title-actions-wrapper"> <h1> <span class="title">${\'queue.upcoming\' | i18n}</span> <info-tooltip direction="top-left">${\'queue.info_tooltip\' | i18n}</info-tooltip> </h1> <div class="actions"> <boost-balance-button></boost-balance-button> </div> </div> <tabs> <tab active.bind="view === \'all\'" click.delegate="changeView(\'all\')"> <span>${\'queue.overview\' | i18n}</span> </tab> <tab active.bind="view === \'updates\'" click.delegate="changeView(\'updates\')"> <span>${\'queue.needs_update\' | i18n}</span> </tab> <tab active.bind="view === \'releases\'" click.delegate="changeView(\'releases\')"> <span>${\'queue.new_releases\' | i18n}</span> </tab> <tab active.bind="view === \'recent\'" click.delegate="changeView(\'recent\')"> <span>${\'queue.recently_completed\' | i18n}</span> </tab> </tabs> <div class="layout"> <section class="left"> <div class="queue-items"> <div class="queue-item ${item.game.id === gameInfo.id ? \'active\' : \'\'}" repeat.for="item of queueItems" id="queue-game-${item.game.id}"> <div class="position" if.bind="item.position">${item.position}</div> <a class="game" route-href="route.bind: \'title\'; params.bind: {titleId: item.title.id, gameId: item.game.id, previousRoute: \'queue\', parentRoute: \'queue\'}" title-link="value.bind: \'queue\'; title-id.bind: item.title.id; game-id.bind: item.game.id;"> <title-thumbnail class="thumbnail" src.bind="item.title.thumbnail" width="140"></title-thumbnail> <div class="meta"> <div class="name"> <i title.bind="item.game.platformId | platformName"> <inline-svg src.bind="item.game.platformId | platformIconSvg"></inline-svg> </i> <label>${item.title.name}</label> </div> <div class="spacer"></div> <div class="creator"> <span if.bind="item.creator"> <img class="avatar" src.bind="item.creator.avatar | cdn:{size: 16}" fallback-src="'+a()(u)+'"> <label class="username">${item.creator.username}</label> </span> <span if.bind="item.game | gameFlags:\'ReleaseQueued\'" class="wemod-tag wemod-tag--new">${\'queue.new\' | i18n}</span> <span if.bind="item.game | gameFlags:\'UpdateQueued\'" class="wemod-tag wemod-tag--updated">${\'queue.update\' | i18n}</span> <span class="time" if.bind="view === \'recent\'"> <i><inline-svg src="'+a()(r)+'"></inline-svg></i> <label>${item.game.trainer.updatedAt | i18nElaspedTime:true}</label> </span> </div> </div> </a> <boost-button game-id.bind="item.game.id" icon="'+a()(n)+'" show-count.bind="true" if.bind="view !== \'recent\'"></boost-button> </div> </div> </section> <section class="right"> <h2>${\'queue.the_creators\' | i18n}</h2> <div class="creators-list-wrapper"> <creators-list></creators-list> </div> <div class="become-a-creator"> <h2>${\'queue.become_a_wemod_creator\' | i18n}</h2> <p>${\'queue.join_our_creator_community\' | i18n}</p> <a href="website://create#auth" target="_blank">${\'queue.apply\' | i18n}</a> </div> </section> </div> </div> </div> </section> </template> '},"queue/queue.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var o=i(31601),a=i.n(o),u=i(76314),r=i.n(u)()(a());r.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}section.queue .view-scrollable{gap:24px;padding-top:24px}section.queue .title-actions-wrapper{display:flex;align-items:flex-start;justify-content:space-between;padding-top:14px}section.queue h1{display:flex;align-items:center;margin:0;gap:10px}section.queue h1 .title{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px}.theme-default section.queue h1 .title{color:#fff}.theme-purple-pro section.queue h1 .title{color:#fff}.theme-green-pro section.queue h1 .title{color:#fff}.theme-orange-pro section.queue h1 .title{color:#fff}.theme-pro section.queue h1 .title{color:#fff}section.queue h1 .actions{flex:1;display:flex;justify-content:flex-end}section.queue h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px}.theme-default section.queue h2{color:#fff}.theme-purple-pro section.queue h2{color:#fff}.theme-green-pro section.queue h2{color:#fff}.theme-orange-pro section.queue h2{color:#fff}.theme-pro section.queue h2{color:#fff}section.queue .layout{display:flex;gap:24px}section.queue .layout .left{flex:1 1 60%;overflow:hidden}section.queue .layout .right{display:flex;flex-direction:column;flex:0 1 440px;overflow:hidden;gap:16px}section.queue .layout .right h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;margin:0}section.queue .queue-items{background-color:var(--theme--background-accent);display:flex;flex-direction:column;border-radius:16px}section.queue .queue-items .queue-item{display:flex;position:relative;padding:12px 16px}section.queue .queue-items .queue-item:nth-child(1n+0) img{animation-delay:0.1s}section.queue .queue-items .queue-item:nth-child(2n+0) img{animation-delay:0.2s}section.queue .queue-items .queue-item:nth-child(3n+0) img{animation-delay:0.3s}section.queue .queue-items .queue-item:nth-child(4n+0) img{animation-delay:0.4s}section.queue .queue-items .queue-item:nth-child(5n+0) img{animation-delay:0.5s}section.queue .queue-items .queue-item:nth-child(6n+0) img{animation-delay:0.6s}section.queue .queue-items .queue-item:nth-child(7n+0) img{animation-delay:0.7s}section.queue .queue-items .queue-item:nth-child(8n+0) img{animation-delay:0.8s}section.queue .queue-items .queue-item:nth-child(9n+0) img{animation-delay:0.9s}section.queue .queue-items .queue-item+.queue-item{border-top:1px solid var(--theme--background)}section.queue .queue-items .queue-item .position{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;min-width:32px;padding-right:8px;box-sizing:content-box;display:flex;align-items:center;justify-content:center}.theme-default section.queue .queue-items .queue-item .position{color:rgba(255,255,255,.4)}.theme-purple-pro section.queue .queue-items .queue-item .position{color:rgba(255,255,255,.4)}.theme-green-pro section.queue .queue-items .queue-item .position{color:rgba(255,255,255,.4)}.theme-orange-pro section.queue .queue-items .queue-item .position{color:rgba(255,255,255,.4)}.theme-pro section.queue .queue-items .queue-item .position{color:rgba(255,255,255,.4)}section.queue .queue-items .queue-item .game{display:flex;align-items:center;gap:12px;flex-grow:1;overflow:hidden}section.queue .queue-items .queue-item .game:hover .thumbnail{border-color:var(--theme--highlight)}section.queue .queue-items .queue-item .game .thumbnail{border-radius:8px;border:2px solid rgba(0,0,0,0);transition:border-color .15s;width:104px;overflow:hidden}section.queue .queue-items .queue-item .game .meta{display:flex;justify-content:center;flex-direction:column;gap:2px;overflow:hidden;padding-right:16px}section.queue .queue-items .queue-item .game .name,section.queue .queue-items .queue-item .game .creator,section.queue .queue-items .queue-item .game .creator span{display:flex;align-items:center;gap:6px}section.queue .queue-items .queue-item .game .avatar{width:16px;height:16px;border-radius:50%;overflow:hidden}section.queue .queue-items .queue-item .game .name label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px}.theme-default section.queue .queue-items .queue-item .game .name label{color:rgba(255,255,255,.6)}.theme-purple-pro section.queue .queue-items .queue-item .game .name label{color:rgba(255,255,255,.6)}.theme-green-pro section.queue .queue-items .queue-item .game .name label{color:rgba(255,255,255,.6)}.theme-orange-pro section.queue .queue-items .queue-item .game .name label{color:rgba(255,255,255,.6)}.theme-pro section.queue .queue-items .queue-item .game .name label{color:rgba(255,255,255,.6)}section.queue .queue-items .queue-item .game .creator .username{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default section.queue .queue-items .queue-item .game .creator .username{color:rgba(255,255,255,.4)}.theme-purple-pro section.queue .queue-items .queue-item .game .creator .username{color:rgba(255,255,255,.4)}.theme-green-pro section.queue .queue-items .queue-item .game .creator .username{color:rgba(255,255,255,.4)}.theme-orange-pro section.queue .queue-items .queue-item .game .creator .username{color:rgba(255,255,255,.4)}.theme-pro section.queue .queue-items .queue-item .game .creator .username{color:rgba(255,255,255,.4)}section.queue .queue-items .queue-item .game .time{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default section.queue .queue-items .queue-item .game .time{color:rgba(255,255,255,.6)}.theme-purple-pro section.queue .queue-items .queue-item .game .time{color:rgba(255,255,255,.6)}.theme-green-pro section.queue .queue-items .queue-item .game .time{color:rgba(255,255,255,.6)}.theme-orange-pro section.queue .queue-items .queue-item .game .time{color:rgba(255,255,255,.6)}.theme-pro section.queue .queue-items .queue-item .game .time{color:rgba(255,255,255,.6)}section.queue .queue-items .queue-item .game .wemod-tag,section.queue .queue-items .queue-item .game .time{text-transform:uppercase}section.queue .queue-items .queue-item boost-button{display:flex;align-items:center;margin-left:auto}section.queue .creators-list-wrapper{padding:16px;border-radius:16px;background:var(--theme--background-accent);overflow:hidden}@media(forced-colors: active){body:not(.override-contrast-mode) section.queue .creators-list-wrapper{border:1px solid #fff}}section.queue .become-a-creator{padding:16px;border-radius:16px;background:var(--theme--background-accent);overflow:hidden;display:flex;flex-direction:column;gap:12px}@media(forced-colors: active){body:not(.override-contrast-mode) section.queue .become-a-creator{border:1px solid #fff}}section.queue .become-a-creator h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:20px;line-height:24px;letter-spacing:-1px;margin:0}.theme-default section.queue .become-a-creator h2{color:#fff}.theme-purple-pro section.queue .become-a-creator h2{color:#fff}.theme-green-pro section.queue .become-a-creator h2{color:#fff}.theme-orange-pro section.queue .become-a-creator h2{color:#fff}.theme-pro section.queue .become-a-creator h2{color:#fff}section.queue .become-a-creator p{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;margin:0}.theme-default section.queue .become-a-creator p{color:rgba(255,255,255,.6)}.theme-purple-pro section.queue .become-a-creator p{color:rgba(255,255,255,.6)}.theme-green-pro section.queue .become-a-creator p{color:rgba(255,255,255,.6)}.theme-orange-pro section.queue .become-a-creator p{color:rgba(255,255,255,.6)}.theme-pro section.queue .become-a-creator p{color:rgba(255,255,255,.6)}section.queue .become-a-creator a{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;height:40px;box-shadow:none;line-height:24px;font-weight:700;color:#000;border-radius:56px;padding:10px 16px;background:#fff;width:min-content;padding:6px 16px}section.queue .become-a-creator a,section.queue .become-a-creator a *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) section.queue .become-a-creator a{border:1px solid #fff}}section.queue .become-a-creator a>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}section.queue .become-a-creator a>*:first-child{padding-left:0}section.queue .become-a-creator a>*:last-child{padding-right:0}section.queue .become-a-creator a svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) section.queue .become-a-creator a svg *{fill:CanvasText}}section.queue .become-a-creator a svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) section.queue .become-a-creator a svg{opacity:1}}section.queue .become-a-creator a img{height:50%}section.queue .become-a-creator a:disabled{opacity:.3}section.queue .become-a-creator a:disabled,section.queue .become-a-creator a:disabled *{cursor:default;pointer-events:none}@media(hover: hover){section.queue .become-a-creator a:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}section.queue .become-a-creator a:not(:disabled):hover svg{opacity:1}}section.queue .become-a-creator a:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}section.queue .become-a-creator a:hover{background:rgba(255,255,255,.8) !important;color:rgba(0,0,0,.8) !important}',""]);const n=r}}]);
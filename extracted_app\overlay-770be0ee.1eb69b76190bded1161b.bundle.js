"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3815],{22920:(e,t,s)=>{s.d(t,{h:()=>c});var i=s(15215),n=s("aurelia-event-aggregator"),a=s("aurelia-framework"),r=s("shared/api/index"),o=s(20057),l=s(84551),d=s(59255);let c=class{#e;#t;#s;#i;constructor(e,t,s,i){this.#e=e,this.#t=t,this.#s=s,this.#i=i}createAssistant(e,t){return new d.F(e,t,this.#e,this.#t,this.#s,this.#i)}};c=(0,i.Cg)([(0,a.autoinject)(),(0,i.Sn)("design:paramtypes",[o.F2,r.WeMod<PERSON>piClient,l.Y,n.EventAggregator])],c)},24008:(e,t,s)=>{s.d(t,{Cz:()=>a,D1:()=>r,nC:()=>l,rT:()=>o});const i=e=>"object"==typeof e&&null!==e,n=[["titles",i],["games",i],["creators",i],["genres",i],["platforms",i],["maps",Array.isArray],["queue",Array.isArray],["stats",i],["featured",Array.isArray],["announcements",Array.isArray],["polls",Array.isArray],["releaseChannels",Array.isArray],["featuredGames",Array.isArray]];function a(e){return null===e||i(e)&&n.every((t=>t[1](e[t[0]])))}var r,o,l;!function(e){e[e.HasAssistant=1]="HasAssistant"}(r||(r={})),function(e){e[e.ReleaseQueued=1]="ReleaseQueued",e[e.UpdateQueued=2]="UpdateQueued",e[e.Active=4]="Active",e[e.Retired=8]="Retired",e[e.Unsupported=16]="Unsupported",e[e.Outdated=32]="Outdated",e[e.Free=64]="Free",e[e.AllowCheatSuggestions=128]="AllowCheatSuggestions",e[e.Queued=3]="Queued",e[e.Available=12]="Available",e[e.OverlaySupported=256]="OverlaySupported",e[e.PrecisionModsSupported=512]="PrecisionModsSupported"}(o||(o={})),function(e){e[e.HasGameCoordinates=1]="HasGameCoordinates"}(l||(l={}))},29702:(e,t,s)=>{s.d(t,{K:()=>a});var i=s(15215),n=s("aurelia-framework");let a=class{#n;constructor(e){this.#n=e}collectOne(e){this.collectMany([e])}collectMany(e){navigator.sendBeacon&&navigator.sendBeacon(`${this.#n}/v3/metrics`,JSON.stringify(e))}};a=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[String])],a)},59255:(e,t,s)=>{s.d(t,{F:()=>h,d:()=>c});var i=s(15215),n=s("aurelia-event-aggregator"),a=s("aurelia-framework"),r=s(15563),o=s.n(r),l=s(14046),d=s(64706);class c{constructor(e,t,s,i){this.name=e,this.client=t,this.titleId=s,this.params=i}}class h{#a;#r;#e;#t;#o;#s;#i;constructor(e,t,s,i,a,r){this.#o=new n.EventAggregator,this.config=Object.freeze(e),this.history=t,this.#e=s,this.#t=i,this.#s=a,this.#i=r}dispose(){this.#l(),this.history.dispose()}async#d(e=!1){this.#l();const t=await this.#t.post("/v3/assistant/session",{titleId:this.config.titleId});if(this.isActive){if(!e)if(this.history.items.length){const e=this.history.items[this.history.items.length-1];if((0,l.Ov)(Date.now(),e?.timestamp??Date.now())>=43200){const e=Math.floor(4*Math.random())+1,t=this.#e.getValue(`assistant_chat.welcome_message_${e}`,{game:this.config.titleName}),s=new d.gG("welcome","",[],null,null,!1,!0,this.config.client,null);this.history.add(s),this.#c(s,t)}}else{const e=new d.gG("welcome",t.welcomeMessage,[],null,null,!0,!0,this.config.client,null);this.history.add(e)}this.#a=setTimeout((()=>this.#h()),1e3*(t.serviceTokenTtl-300)),this.#r=setTimeout((()=>this.#g()),1e3*t.serviceTokenTtl),this.session=t,this.#o.publish("session",this.session)}}async#h(){await this.#d(!0)}#g(){this.session=null}#l(){clearTimeout(this.#a),clearTimeout(this.#r)}async#u(e,t){const s=Date.now(),i=await fetch(`${this.config.baseUrl}/${e}`,{method:"POST",headers:{Accept:"application/x-ndjson","Content-Type":"application/json",Authorization:`Bearer ${this.session?.serviceToken}`,"Accept-Language":this.#e.getEffectiveLocale().toString()},body:JSON.stringify(t)});return this.#s.report({endpoint:`assistant:/${e}`,method:"post",responseTime:Date.now()-s}),i}async ask(e,t=null,s=!0,i){const n=new d.gG("answer","",[],null,null,!1,!0,this.config.client,t);let a=!0;s&&this.history.add(new d.gG("question",e,[],null,null,!0,!0,this.config.client,null)),this.history.add(n);try{this.isThinking=!0;const t={assistantId:this.session?.assistantId,question:e,...i&&{config:i}},s=await this.#u("ask",t);if(s.ok){const e=s.body?.getReader();if(!e)throw new Error("Response invalid.");const t=o()(e);let i;for(;!i||!i.done;)if(i=await t.next(),i.value&&("attribution"===i.value.type&&n.sources.push(...i.value.sources),"token"===i.value.type&&(n.text+=i.value.token),"metadata"===i.value.type&&(n.requestId=i.value.requestId),"error"===i.value.type)){this.#p(n),a=!1;break}}else{let e=!1;401===s.status&&await this.#h(),429===s.status&&(this.#b(n),e=!0),e||this.#p(n),a=!1}}catch{navigator.onLine?this.#p(n):this.#m(n),a=!1}finally{this.isThinking=!1}return n.persist&&n.finalize(),{ok:a,answer:n}}async submitFeedback(e,t,s){const i={requestId:e,message:t,type:"positive"===s?"up":"down"};await this.#u("feedback",i);const n=new d.gG("answer","",[],null,null,!1,!1,this.config.client,null);this.history.add(n),this.#c(n,this.#e.getValue(`assistant_chat.thanks_for_your_feedback_${s}`)),this.analyticsEvent("assistant_feedback",{type:s})}#v(e,t){e.type="error",e.text="",e.persist=!1,this.#c(e,t)}#b(e){const t=this.#e.getValue("assistant.rate_limit_error_message");this.#v(e,t)}#p(e){const t=this.#e.getValue("assistant.general_error_message");this.#v(e,t)}#m(e){const t=this.#e.getValue("assistant.offline_error_message");this.#v(e,t)}#c(e,t){this.isResponding=!0;const s=t.match(/\s+/),i=s?s[0]:"",n=i?t.split(i)[0]:t,a=t.slice(n.length).trim();if(e.text+=n+i,a.length){const t=100*Math.random()+50;setTimeout((()=>this.#c(e,a)),t)}else this.isResponding=!1,e.finalize()}onSessionChanged(e){return this.#o.subscribe("session",e)}onReady(e){return this.#o.subscribe("ready",e)}get isReady(){const e=!!this.session&&!!this.isActive;return e&&this.#o.publish("ready"),e}setActive(e){this.isActive=e,e&&!this.session&&this.#d(),e||clearTimeout(this.#a)}analyticsEvent(e,t){this.#i.publish(new c(e,this.config.client,this.config.titleId,t??{}))}}(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Object)],h.prototype,"session",void 0),(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Boolean)],h.prototype,"isResponding",void 0),(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Boolean)],h.prototype,"isThinking",void 0),(0,i.Cg)([a.observable,(0,i.Sn)("design:type",Boolean)],h.prototype,"isActive",void 0),(0,i.Cg)([(0,a.computedFrom)("session","isActive"),(0,i.Sn)("design:type",Boolean),(0,i.Sn)("design:paramtypes",[])],h.prototype,"isReady",null)},64706:(e,t,s)=>{s.d(t,{$H:()=>n,fz:()=>r,gG:()=>a});var i=s("aurelia-event-aggregator");const n=200;class a{#y;constructor(e,t="",s=[],i=null,n,a,r,o,l){this.type=e,this.text=t,this.sources=s,this.requestId=i,this.timestamp=n||Date.now(),this.#y=a,this.persist=r,this.client=o,this.style=l??null}static deserialize(e){return new a(e.type,e.text,e.sources,e.requestId,e.timestamp,!0,!0,e.client,e.style??null)}get finalized(){return this.#y}finalize(){this.#y=!0,this.onFinalized&&this.onFinalized(this)}serialize(){return{type:this.type,text:this.text,sources:this.sources,timestamp:this.timestamp,client:this.client,requestId:this.requestId,style:this.style}}}class r{#f;#o;constructor(e,t=[]){this.items=[],this.#o=new i.EventAggregator,this.#f=e,this.handleItemFinalized=this.handleItemFinalized.bind(this),t.forEach((e=>this.add(e)))}add(e){this.items.push(e),this.items.length>n&&this.items.splice(0,this.items.length-n),this.handleItemAdded(e),e.finalized?this.handleItemFinalized(e):e.onFinalized=this.handleItemFinalized}handleItemFinalized(e){this.#o.publish("item-finalized",{titleId:this.#f,item:e})}onItemFinalized(e){return this.#o.subscribe("item-finalized",e)}handleItemAdded(e){this.#o.publish("item-added",{titleId:this.#f,item:e})}onItemAdded(e){return this.#o.subscribe("item-added",e)}clear(){this.items=[]}dispose(){}}},68539:(e,t,s)=>{s.d(t,{z:()=>n});var i=s("aurelia-event-aggregator");class n{#o;#w;#t;#k;#A;constructor(e,t={},s=void 0){this.#o=new i.EventAggregator,this.#w=new Set,this.#k=new Map,this.assignments=new Map,this.#t=e,this.assignments=new Map(Object.entries(t)),this.#A=s}async trigger(e){const t=this.assignments.get(e);if(void 0!==t)return t;if(this.#A&&!this.#A.includes(e))return null;const s=await this.#t.post("/v3/experiment",{key:e});return this.#x({[e]:s.variant}),s.variant}setOverride(e,t){this.#k.set(e,t),this.#x({[e]:t})}clearOverride(e){this.#k.delete(e)}queueTrigger(e){this.#w.add(e)}onVariantChanged(e){return this.#o.subscribe("variant",e)}createInterceptor(){return{request:e=>(this.#w.size>0&&(e.headers.set("X-Trigger-Experiments",btoa(JSON.stringify(Array.from(this.#w)))),this.#w.clear()),e),response:(e,t)=>{const s=e.headers.get("X-Experiments");if(s){let e=null;try{const t=JSON.parse(atob(s));"object"==typeof t&&null!==t&&(e=t)}catch{}e&&this.#x(e)}return e}}}setAllExperiments(e){e={...e,...Object.fromEntries(this.#k)};const t=new Set(Object.keys(e));for(const e of Array.from(this.assignments.keys()))t.has(e)||(this.assignments.delete(e),this.#o.publish("variant",{existingKey:e,variant:null}));this.#x(e)}#x(e){e={...e,...Object.fromEntries(this.#k)};for(const[t,s]of Object.entries(e))(this.assignments.get(t)??null)!==s&&(this.assignments.set(t,s),this.#o.publish("variant",{key:t,variant:s}))}}},"shared/api/index":(e,t,s)=>{s.r(t),s.d(t,{ApiError:()=>n.hD,ApiErrorInterceptor:()=>n.i7,AuthorizationInterceptor:()=>o.Z,ErrorCode:()=>n.O4,ResponseError:()=>n.o3,WeModApiClient:()=>i.S,WeModAuthClient:()=>r.Q,configure:()=>h,isTemporaryServerError:()=>n.hb}),s("aurelia-framework");var i=s(28035),n=s(17724),a=s(41548),r=s(60321),o=s(57503),l=s(68539),d=s(29702),c=s("shared/api/value-converters");function h(e,t){const s=new r.Q(t.baseUrl,t.clientId);e.container.registerInstance(r.Q,s);const h=new i.S(t.baseUrl,t.superProperties);e.container.registerInstance(i.S,h);const g=new d.K(t.baseUrl);e.container.registerInstance(d.K,g);const u=e.container.get(n.i7);h.addInterceptor(u);const p=new o.Z(s);e.container.registerInstance(o.Z,p),t.initialAccessTokenResponse&&p.setAccessTokenResponse(t.initialAccessTokenResponse),h.addInterceptor(p);const b=e.container.get(a.k);h.addInterceptor(b);const m=new l.z(h,t.activeExperiments,t.triggerableExperiments);e.container.registerInstance(l.z,m),h.addInterceptor(m.createInterceptor()),e.container.registerInstance(c.CdnValueConverter,new c.CdnValueConverter(t.cdnUrl)),e.globalResources(["./value-converters"])}},"shared/api/value-converters":(e,t,s)=>{s.r(t),s.d(t,{CdnValueConverter:()=>a});var i=s(15215),n=s("aurelia-framework");let a=class{#n;constructor(e){this.#n=e}toView(e,t,s){if(!e)return s||"";if(e.includes(":")||!e.startsWith("/"))return e;if(t)for(const s of Object.keys(t))e=e.replace(`{${s}}`,t[s]);if(e.includes("{"))throw new Error(`Missing CDN parameter in "${e}".`);return this.#n+e}};a=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[String])],a)},"shared/assistant/assistant-chat":(e,t,s)=>{s.r(t),s.d(t,{AssistantChat:()=>r});var i=s(15215),n=s("aurelia-framework"),a=s(59255);let r=class{constructor(){this.showDebug=!1}attached(){this.assistant.setActive(!0),this.assistant.analyticsEvent("assistant_open")}detached(){this.assistant.setActive(!1),this.assistant.analyticsEvent("assistant_close")}};(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",a.F)],r.prototype,"assistant",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],r.prototype,"client",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],r.prototype,"titleId",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",Function)],r.prototype,"close",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",Boolean)],r.prototype,"chatDisabled",void 0),(0,i.Cg)([(0,n.bindable)({defaultBindingMode:n.bindingMode.toView}),(0,i.Sn)("design:type",String)],r.prototype,"disabledChatMessageKey",void 0),r=(0,i.Cg)([(0,n.autoinject)()],r)},"shared/assistant/assistant-chat.html":(e,t,s)=>{s.r(t),s.d(t,{default:()=>i});const i='<template> <require from="./assistant-chat.scss"></require> <require from="./resources/elements/assistant-debug"></require> <require from="./resources/elements/assistant-history-list"></require> <require from="./resources/elements/assistant-input"></require> <require from="../resources/elements/loading-indicator"></require> <div class="control-buttons" inert.bind="inputMode === \'feedback\' && !chatDisabled"> <button class="debug-button" if.bind="assistant.session.debug" click.delegate="showDebug = !showDebug"></button> <button class="close-button" click.delegate="close()"></button> </div> <div class="layout" show.bind="assistant.isReady"> <assistant-history-list history.bind="assistant.history" assistant.bind="assistant" inert.bind="inputMode === \'feedback\' && !chatDisabled"></assistant-history-list> <div class="input-wrapper"> <assistant-input assistant.bind="assistant" disabled.bind="chatDisabled" disabled-message-key.bind="disabledChatMessageKey" debug-config.bind="debugConfig" mode.bind="inputMode" view-model.ref="assistantInput"></assistant-input> </div> <assistant-debug if.bind="showDebug" config.bind="debugConfig" assistant.bind="assistant"></assistant-debug> <div class="overlay ${inputMode === \'feedback\' && !chatDisabled ? \'show\' : \'\'}" click.delegate="assistantInput.submit()"></div> </div> <div class="loading-container" show.bind="!assistant.isReady"> <loading-indicator></loading-indicator> </div> </template> '},"shared/assistant/assistant-chat.scss":(e,t,s)=>{s.r(t),s.d(t,{default:()=>p});var i=s(31601),n=s.n(i),a=s(76314),r=s.n(a),o=s(4417),l=s.n(o),d=new URL(s(48789),s.b),c=new URL(s(2418),s.b),h=r()(n()),g=l()(d),u=l()(c);h.push([e.id,`assistant-chat{--assistant-color: #6046ff;--assistant-color--rgb: 96, 70, 255;--assistant-link-color: #5b8fff;--assistant-link-color--rgb: 91, 143, 255;background:var(--assistant-bg);color:#fff;position:relative;z-index:0}.theme-default assistant-chat{--assistant-bg-color: #15152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-purple-pro assistant-chat{--assistant-bg-color: #16142c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-green-pro assistant-chat{--assistant-bg-color: #14162c;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-orange-pro assistant-chat{--assistant-bg-color: #17152a;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}.theme-pro assistant-chat{--assistant-bg-color: #131023;--assistant-bg: linear-gradient(180deg, rgba(96, 70, 255, 0) 0%, rgba(96, 70, 255, 0.1) 100%), rgba(var(--theme--background-accent--rgb), 0.9)}assistant-chat .loading-container{height:100%;display:flex;align-items:center;justify-content:center}assistant-chat .layout{display:flex;flex-direction:column;height:100%;gap:11px}assistant-chat .control-buttons{display:flex;position:absolute;right:20px;top:20px}assistant-chat .control-buttons button{background:#423757 center no-repeat;display:block;width:30px;height:30px;border-radius:50%;border:0;z-index:1;transition:background-color .15s}assistant-chat .control-buttons button+button{margin-left:10px}assistant-chat .control-buttons button:hover{background-color:#645b74}assistant-chat .control-buttons .close-button{background-image:url(${g})}assistant-chat .control-buttons .debug-button{background-image:url(${u});background-size:14px}assistant-chat assistant-history{flex:1 1 auto}assistant-chat .input-wrapper{position:relative;z-index:2}assistant-chat assistant-input{flex:0 0 auto}assistant-chat assistant-debug{flex:0 0 auto;margin-top:-20px}assistant-chat .overlay{position:absolute;left:0;top:0;width:100%;height:100%;z-index:1;background:var(--assistant-bg-color);opacity:0;visibility:hidden;transition:opacity .15s,visibility 0s .15s}assistant-chat .overlay.show{opacity:.8;visibility:visible;transition-delay:0s}`,""]);const p=h}}]);
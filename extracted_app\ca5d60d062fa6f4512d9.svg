<svg width="637" height="310" viewBox="0 0 637 310" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_f_46_3)">
<path d="M89.8235 52.7284C98.6487 51.5665 106.748 57.8011 107.91 66.6263V66.6263C109.071 75.4515 102.862 83.5699 94.0368 84.7317V84.7317C85.2116 85.8936 77.1126 79.659 75.9507 70.8338V70.8338C74.7889 62.0086 80.9983 53.8902 89.8235 52.7284V52.7284Z" fill="url(#paint0_linear_46_3)"/>
<path d="M111.248 96.7557C110.114 94.0147 111.415 90.8731 114.156 89.737V89.737C116.898 88.5999 120.044 89.9027 121.179 92.6462L133.982 123.586C135.116 126.327 133.815 129.468 131.075 130.604V130.604C128.332 131.741 125.187 130.439 124.052 127.695L111.248 96.7557Z" fill="url(#paint1_linear_46_3)"/>
<path d="M126.404 181.379C126.017 178.438 123.318 176.367 120.377 176.755V176.755C117.435 177.142 114.737 175.071 114.349 172.13L105.606 105.714C104.444 96.887 96.3461 90.6736 87.5194 91.8356V91.8356C78.6927 92.9977 72.4793 101.095 73.6414 109.922L80.9985 165.805C82.1605 174.631 90.258 180.845 99.0847 179.683V179.683C107.911 178.521 116.009 184.734 117.171 193.561L120.124 215.993C120.511 218.935 123.21 221.005 126.151 220.618V220.618C129.093 220.231 131.163 217.532 130.776 214.591L126.404 181.379Z" fill="url(#paint2_linear_46_3)"/>
<path d="M83.7875 186.99C83.4003 184.048 80.7018 181.978 77.7603 182.365V182.365C74.8188 182.752 72.7482 185.451 73.1355 188.392L78.209 226.93V226.93C84.092 226.155 88.2332 220.758 87.4587 214.875L83.7875 186.99Z" fill="url(#paint3_linear_46_3)"/>
<path d="M35.1049 238.03C34.7172 235.085 36.7905 232.383 39.7358 231.995L78.209 226.93V226.93C78.9846 232.82 74.8379 238.224 68.9473 239L41.14 242.661C38.1947 243.049 35.4927 240.975 35.1049 238.03V238.03Z" fill="url(#paint4_linear_46_3)"/>
<path d="M121.794 226.612C121.406 223.669 118.707 221.598 115.765 221.986V221.986C112.823 222.373 110.752 225.072 111.139 228.014L115.511 261.22C115.898 264.162 118.597 266.234 121.539 265.846V265.846C124.482 265.459 126.553 262.76 126.165 259.818L121.794 226.612Z" fill="url(#paint5_linear_46_3)"/>
<path d="M140.575 125.729C138.059 127.303 137.294 130.618 138.865 133.136V133.136C140.439 135.657 143.76 136.423 146.279 134.847L174.628 117.112C177.144 115.538 177.909 112.223 176.338 109.705V109.705C174.764 107.184 171.444 106.418 168.924 107.994L140.575 125.729Z" fill="url(#paint6_linear_46_3)"/>
<path d="M46.4736 147.013C38.3431 137.612 40.1308 123.242 50.317 116.12L63.0753 107.199C65.5082 105.498 66.1031 102.147 64.4046 99.7127V99.7127C62.7036 97.2744 59.3471 96.6784 56.9106 98.382L29.7351 117.383C27.1753 119.173 26.5509 122.701 28.3381 125.262V125.262C29.9933 127.635 29.5919 130.876 27.4061 132.772V132.772C25.0487 134.815 24.7923 138.382 26.8333 140.742L48.5475 165.849C50.4916 168.097 53.8906 168.341 56.1361 166.395V166.395C58.3778 164.451 58.6215 161.059 56.6808 158.815L46.4736 147.013Z" fill="url(#paint7_linear_46_3)"/>
</g>
<path d="M89.8235 52.7284C98.6487 51.5665 106.748 57.8011 107.91 66.6263V66.6263C109.071 75.4515 102.862 83.5699 94.0368 84.7317V84.7317C85.2116 85.8936 77.1126 79.659 75.9507 70.8338V70.8338C74.7889 62.0086 80.9983 53.8902 89.8235 52.7284V52.7284Z" fill="url(#paint8_linear_46_3)"/>
<path d="M111.248 96.7557C110.114 94.0147 111.415 90.8731 114.156 89.737V89.737C116.898 88.5999 120.044 89.9027 121.179 92.6462L136.036 128.548L132.925 129.837C129.16 131.398 124.844 129.61 123.286 125.845L111.248 96.7557Z" fill="url(#paint9_linear_46_3)"/>
<path d="M126.665 183.364C126.134 179.326 122.43 176.484 118.392 177.016L115.051 177.456L105.606 105.714C104.444 96.887 96.3461 90.6736 87.5194 91.8356V91.8356C78.6927 92.9977 72.4793 101.095 73.6414 109.922L83.1025 181.787L115.067 177.578L120.124 215.993C120.511 218.935 123.21 221.005 126.151 220.618V220.618C129.093 220.231 131.163 217.532 130.776 214.591L126.665 183.364Z" fill="url(#paint10_linear_46_3)"/>
<path d="M83.0863 181.664V181.664C77.2034 182.438 73.0622 187.835 73.8367 193.718L78.209 226.93V226.93C84.092 226.155 88.2332 220.758 87.4587 214.875L83.0863 181.664Z" fill="url(#paint11_linear_46_3)"/>
<path d="M35.1049 238.03C34.7172 235.085 36.7905 232.383 39.7358 231.995L78.209 226.93V226.93C78.9846 232.82 74.8379 238.224 68.9473 239L41.14 242.661C38.1947 243.049 35.4927 240.975 35.1049 238.03V238.03Z" fill="url(#paint12_linear_46_3)"/>
<path d="M121.794 226.612C121.406 223.669 118.707 221.598 115.765 221.986V221.986C112.823 222.373 110.752 225.072 111.139 228.014L115.511 261.22C115.898 264.162 118.597 266.234 121.539 265.846V265.846C124.482 265.459 126.553 262.76 126.165 259.818L121.794 226.612Z" fill="url(#paint13_linear_46_3)"/>
<path d="M136.021 128.578L137.809 131.443C139.966 134.899 144.517 135.949 147.971 133.789L174.628 117.112C177.144 115.538 177.909 112.223 176.338 109.705V109.705C174.764 107.184 171.444 106.418 168.924 107.994L136.021 128.578Z" fill="url(#paint14_linear_46_3)"/>
<path d="M31.2719 129.436L63.0753 107.199C65.5082 105.498 66.1031 102.147 64.4046 99.7127V99.7127C62.7036 97.2744 59.3471 96.6784 56.9106 98.382L31.1279 116.41C27.7987 118.737 26.9846 123.322 29.3089 126.654L31.2511 129.438L28.6972 131.652C25.6265 134.314 25.2926 138.961 27.9511 142.035L48.5475 165.849C50.4916 168.097 53.8906 168.341 56.1361 166.395V166.395C58.3778 164.451 58.6215 161.059 56.6808 158.815L31.2719 129.436Z" fill="url(#paint15_linear_46_3)"/>
<g filter="url(#filter1_f_46_3)">
<path d="M89.8235 52.7284C98.6487 51.5666 106.748 57.8012 107.91 66.6264V66.6264C109.071 75.4516 102.862 83.5699 94.0368 84.7318V84.7318C85.2116 85.8936 77.1126 79.659 75.9507 70.8338V70.8338C74.7889 62.0086 80.9983 53.8903 89.8235 52.7284V52.7284Z" fill="#ACFF35"/>
</g>
<g filter="url(#filter2_f_46_3)">
<path d="M523.824 52.7284C532.649 51.5665 540.748 57.8011 541.91 66.6263V66.6263C543.072 75.4515 536.862 83.5699 528.037 84.7317V84.7317C519.212 85.8936 511.113 79.659 509.951 70.8338V70.8338C508.789 62.0086 514.999 53.8902 523.824 52.7284V52.7284Z" fill="url(#paint16_linear_46_3)"/>
<path d="M545.248 96.7557C544.114 94.0147 545.416 90.8731 548.156 89.737V89.737C550.899 88.5999 554.044 89.9027 555.179 92.6462L567.982 123.586C569.117 126.327 567.815 129.468 565.075 130.604V130.604C562.332 131.741 559.187 130.439 558.052 127.695L545.248 96.7557Z" fill="url(#paint17_linear_46_3)"/>
<path d="M560.404 181.379C560.017 178.438 557.318 176.367 554.377 176.755V176.755C551.435 177.142 548.737 175.071 548.35 172.13L539.606 105.714C538.444 96.887 530.346 90.6736 521.52 91.8356V91.8356C512.693 92.9977 506.48 101.095 507.642 109.922L514.999 165.805C516.161 174.631 524.258 180.845 533.085 179.683V179.683C541.912 178.521 550.009 184.734 551.171 193.561L554.124 215.993C554.512 218.935 557.21 221.005 560.152 220.618V220.618C563.093 220.231 565.164 217.532 564.776 214.591L560.404 181.379Z" fill="url(#paint18_linear_46_3)"/>
<path d="M517.788 186.99C517.401 184.048 514.702 181.978 511.761 182.365V182.365C508.819 182.752 506.748 185.451 507.136 188.392L512.209 226.93V226.93C518.092 226.155 522.233 220.758 521.459 214.875L517.788 186.99Z" fill="url(#paint19_linear_46_3)"/>
<path d="M469.105 238.03C468.717 235.085 470.791 232.383 473.736 231.995L512.209 226.93V226.93C512.985 232.82 508.838 238.224 502.948 239L475.14 242.661C472.195 243.049 469.493 240.975 469.105 238.03V238.03Z" fill="url(#paint20_linear_46_3)"/>
<path d="M555.794 226.612C555.407 223.669 552.707 221.598 549.765 221.986V221.986C546.823 222.373 544.752 225.072 545.139 228.014L549.511 261.22C549.898 264.162 552.597 266.234 555.54 265.846V265.846C558.482 265.459 560.553 262.76 560.166 259.818L555.794 226.612Z" fill="url(#paint21_linear_46_3)"/>
<path d="M574.575 125.729C572.059 127.303 571.294 130.618 572.866 133.136V133.136C574.439 135.657 577.76 136.423 580.279 134.847L608.628 117.112C611.145 115.538 611.91 112.223 610.338 109.705V109.705C608.764 107.184 605.444 106.418 602.924 107.994L574.575 125.729Z" fill="url(#paint22_linear_46_3)"/>
<path d="M480.474 147.013C472.343 137.612 474.131 123.242 484.317 116.12L497.076 107.199C499.508 105.498 500.103 102.147 498.405 99.7127V99.7127C496.704 97.2744 493.347 96.6784 490.911 98.382L463.735 117.383C461.176 119.173 460.551 122.701 462.338 125.262V125.262C463.994 127.635 463.592 130.876 461.406 132.772V132.772C459.049 134.815 458.793 138.382 460.834 140.742L482.548 165.849C484.492 168.097 487.891 168.341 490.136 166.395V166.395C492.378 164.451 492.622 161.059 490.681 158.815L480.474 147.013Z" fill="url(#paint23_linear_46_3)"/>
</g>
<path d="M523.824 52.7284C532.649 51.5665 540.748 57.8011 541.91 66.6263V66.6263C543.072 75.4515 536.862 83.5699 528.037 84.7317V84.7317C519.212 85.8936 511.113 79.659 509.951 70.8338V70.8338C508.789 62.0086 514.999 53.8902 523.824 52.7284V52.7284Z" fill="url(#paint24_linear_46_3)"/>
<path d="M545.248 96.7557C544.114 94.0147 545.416 90.8731 548.156 89.737V89.737C550.899 88.5999 554.044 89.9027 555.179 92.6462L570.036 128.548L566.925 129.837C563.161 131.398 558.844 129.61 557.286 125.845L545.248 96.7557Z" fill="url(#paint25_linear_46_3)"/>
<path d="M560.665 183.364C560.134 179.326 556.43 176.484 552.392 177.016L549.051 177.456L539.606 105.714C538.444 96.887 530.346 90.6736 521.52 91.8356V91.8356C512.693 92.9977 506.48 101.095 507.642 109.922L517.103 181.787L549.067 177.578L554.124 215.993C554.512 218.935 557.21 221.005 560.152 220.618V220.618C563.093 220.231 565.164 217.532 564.776 214.591L560.665 183.364Z" fill="url(#paint26_linear_46_3)"/>
<path d="M517.087 181.664V181.664C511.204 182.438 507.062 187.835 507.837 193.718L512.209 226.93V226.93C518.092 226.155 522.233 220.758 521.459 214.875L517.087 181.664Z" fill="url(#paint27_linear_46_3)"/>
<path d="M469.105 238.03C468.717 235.085 470.791 232.383 473.736 231.995L512.209 226.93V226.93C512.985 232.82 508.838 238.224 502.948 239L475.14 242.661C472.195 243.049 469.493 240.975 469.105 238.03V238.03Z" fill="url(#paint28_linear_46_3)"/>
<path d="M555.794 226.612C555.407 223.669 552.707 221.598 549.765 221.986V221.986C546.823 222.373 544.752 225.072 545.139 228.014L549.511 261.22C549.898 264.162 552.597 266.234 555.54 265.846V265.846C558.482 265.459 560.553 262.76 560.166 259.818L555.794 226.612Z" fill="url(#paint29_linear_46_3)"/>
<path d="M570.021 128.578L571.809 131.443C573.966 134.899 578.517 135.949 581.971 133.789L608.628 117.112C611.145 115.538 611.91 112.223 610.338 109.705V109.705C608.764 107.184 605.444 106.418 602.924 107.994L570.021 128.578Z" fill="url(#paint30_linear_46_3)"/>
<path d="M465.272 129.436L497.076 107.199C499.508 105.498 500.103 102.147 498.405 99.7127V99.7127C496.704 97.2744 493.347 96.6784 490.911 98.382L465.128 116.41C461.799 118.737 460.985 123.322 463.309 126.654L465.251 129.438L462.697 131.652C459.627 134.314 459.293 138.961 461.951 142.035L482.548 165.849C484.492 168.097 487.891 168.341 490.136 166.395V166.395C492.378 164.451 492.622 161.059 490.681 158.815L465.272 129.436Z" fill="url(#paint31_linear_46_3)"/>
<g filter="url(#filter3_f_46_3)">
<path d="M523.824 52.7284C532.649 51.5666 540.748 57.8012 541.91 66.6264V66.6264C543.072 75.4516 536.862 83.5699 528.037 84.7318V84.7318C519.212 85.8936 511.113 79.659 509.951 70.8338V70.8338C508.789 62.0086 514.999 53.8903 523.824 52.7284V52.7284Z" fill="#ACFF35"/>
</g>
<g opacity="0.2" filter="url(#filter4_f_46_3)">
<ellipse cx="123" cy="155" rx="19" ry="131" stroke="url(#paint32_linear_46_3)" stroke-linecap="round"/>
</g>
<g opacity="0.25" filter="url(#filter5_f_46_3)">
<ellipse cx="171" cy="155" rx="19" ry="131" stroke="url(#paint33_linear_46_3)" stroke-width="2" stroke-linecap="round"/>
</g>
<g opacity="0.5" filter="url(#filter6_f_46_3)">
<ellipse cx="219" cy="155" rx="19" ry="131" stroke="url(#paint34_linear_46_3)" stroke-width="4" stroke-linecap="round"/>
</g>
<g filter="url(#filter7_f_46_3)">
<ellipse cx="267" cy="155" rx="19" ry="131" stroke="url(#paint35_linear_46_3)" stroke-width="8" stroke-linecap="round"/>
</g>
<g filter="url(#filter8_f_46_3)">
<ellipse cx="315" cy="155" rx="19" ry="131" stroke="url(#paint36_linear_46_3)" stroke-width="16" stroke-linecap="round"/>
</g>
<g filter="url(#filter9_f_46_3)">
<ellipse cx="363" cy="155" rx="19" ry="131" stroke="url(#paint37_linear_46_3)" stroke-width="8" stroke-linecap="round"/>
</g>
<g opacity="0.5" filter="url(#filter10_f_46_3)">
<ellipse cx="411" cy="155" rx="19" ry="131" stroke="url(#paint38_linear_46_3)" stroke-width="4" stroke-linecap="round"/>
</g>
<g opacity="0.25" filter="url(#filter11_f_46_3)">
<ellipse cx="459" cy="155" rx="19" ry="131" stroke="url(#paint39_linear_46_3)" stroke-width="2" stroke-linecap="round"/>
</g>
<g opacity="0.2" filter="url(#filter12_f_46_3)">
<ellipse cx="507" cy="155" rx="19" ry="131" stroke="url(#paint40_linear_46_3)" stroke-linecap="round"/>
</g>
<defs>
<filter id="filter0_f_46_3" x="0.455627" y="27.5891" width="201.699" height="263.304" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="12.5" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter1_f_46_3" x="63.311" y="40.0891" width="57.2383" height="57.2819" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.25" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter2_f_46_3" x="434.456" y="27.5891" width="201.699" height="263.304" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="12.5" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter3_f_46_3" x="497.311" y="40.0891" width="57.2384" height="57.2819" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="6.25" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter4_f_46_3" x="102.5" y="22.5" width="41" height="265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter5_f_46_3" x="149" y="21" width="44" height="268" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter6_f_46_3" x="194" y="18" width="50" height="274" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter7_f_46_3" x="236" y="12" width="62" height="286" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter8_f_46_3" x="272" y="0" width="86" height="310" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="8" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter9_f_46_3" x="332" y="12" width="62" height="286" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="4" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter10_f_46_3" x="386" y="18" width="50" height="274" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter11_f_46_3" x="437" y="21" width="44" height="268" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_46_3"/>
</filter>
<filter id="filter12_f_46_3" x="486.5" y="22.5" width="41" height="265" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_46_3"/>
</filter>
<linearGradient id="paint0_linear_46_3" x1="169.697" y1="42.2128" x2="-14.574" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint1_linear_46_3" x1="169.697" y1="42.2128" x2="-14.574" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint2_linear_46_3" x1="169.697" y1="42.2128" x2="-14.574" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint3_linear_46_3" x1="169.697" y1="42.2128" x2="-14.574" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint4_linear_46_3" x1="169.697" y1="42.2128" x2="-14.574" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint5_linear_46_3" x1="169.697" y1="42.2128" x2="-14.574" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint6_linear_46_3" x1="169.697" y1="42.2128" x2="-14.574" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint7_linear_46_3" x1="169.697" y1="42.2128" x2="-14.574" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint8_linear_46_3" x1="169.101" y1="37.6862" x2="-17.5731" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint9_linear_46_3" x1="169.101" y1="37.6862" x2="-17.5731" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint10_linear_46_3" x1="169.101" y1="37.6862" x2="-17.5731" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint11_linear_46_3" x1="169.101" y1="37.6862" x2="-17.5731" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint12_linear_46_3" x1="169.101" y1="37.6862" x2="-17.5731" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint13_linear_46_3" x1="169.101" y1="37.6862" x2="-17.5731" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint14_linear_46_3" x1="169.101" y1="37.6862" x2="-17.5731" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint15_linear_46_3" x1="169.101" y1="37.6862" x2="-17.5731" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint16_linear_46_3" x1="603.697" y1="42.2128" x2="419.426" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint17_linear_46_3" x1="603.697" y1="42.2128" x2="419.426" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint18_linear_46_3" x1="603.697" y1="42.2128" x2="419.426" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint19_linear_46_3" x1="603.697" y1="42.2128" x2="419.426" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint20_linear_46_3" x1="603.697" y1="42.2128" x2="419.426" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint21_linear_46_3" x1="603.697" y1="42.2128" x2="419.426" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint22_linear_46_3" x1="603.697" y1="42.2128" x2="419.426" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint23_linear_46_3" x1="603.697" y1="42.2128" x2="419.426" y2="219.72" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="#13CFFF" stop-opacity="0.5"/>
</linearGradient>
<linearGradient id="paint24_linear_46_3" x1="603.102" y1="37.6862" x2="416.427" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint25_linear_46_3" x1="603.102" y1="37.6862" x2="416.427" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint26_linear_46_3" x1="603.102" y1="37.6862" x2="416.427" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint27_linear_46_3" x1="603.102" y1="37.6862" x2="416.427" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint28_linear_46_3" x1="603.102" y1="37.6862" x2="416.427" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint29_linear_46_3" x1="603.102" y1="37.6862" x2="416.427" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint30_linear_46_3" x1="603.102" y1="37.6862" x2="416.427" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint31_linear_46_3" x1="603.102" y1="37.6862" x2="416.427" y2="214.766" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint32_linear_46_3" x1="142" y1="24" x2="67.5808" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint33_linear_46_3" x1="190" y1="24" x2="115.581" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint34_linear_46_3" x1="238" y1="24" x2="163.581" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint35_linear_46_3" x1="286" y1="24" x2="211.581" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint36_linear_46_3" x1="334" y1="24" x2="259.581" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint37_linear_46_3" x1="382" y1="24" x2="307.581" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint38_linear_46_3" x1="430" y1="24" x2="355.581" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint39_linear_46_3" x1="478" y1="24" x2="403.581" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint40_linear_46_3" x1="526" y1="24" x2="451.581" y2="34.8463" gradientUnits="userSpaceOnUse">
<stop stop-color="#0BF2F6"/>
<stop offset="1" stop-color="white" stop-opacity="0"/>
</linearGradient>
</defs>
</svg>

"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6336],{"app/resources/elements/nav-header-actions":(t,o,e)=>{e.r(o),e.d(o,{NavHeaderActions:()=>i});var n=e(15215),r=e("aurelia-framework");let i=class{};(0,n.Cg)([r.bindable,(0,n.Sn)("design:type",Function)],i.prototype,"handleCollapseClick",void 0),(0,n.Cg)([r.bindable,(0,n.Sn)("design:type",Boolean)],i.prototype,"isSidebarForcedCollapsed",void 0),i=(0,n.Cg)([(0,r.autoinject)()],i)},"app/resources/elements/nav-header-actions.html":(t,o,e)=>{e.r(o),e.d(o,{default:()=>n});const n='<template> <require from="./nav-header-actions.scss"></require> <require from="./navigation-actions"></require> <navigation-actions></navigation-actions> <button if.bind="!isSidebarForcedCollapsed" click.delegate="handleCollapseClick()" class="collapse-button"> <i></i> </button> </template> '},"app/resources/elements/nav-header-actions.scss":(t,o,e)=>{e.r(o),e.d(o,{default:()=>m});var n=e(31601),r=e.n(n),i=e(76314),a=e.n(i),s=e(4417),l=e.n(s),p=new URL(e(83959),e.b),d=a()(r()),c=l()(p);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,nav-header-actions button.collapse-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}nav-header-actions{display:flex;flex-direction:row}nav-header-actions button{background:none;border:none;cursor:pointer;display:flex;align-items:center;justify-content:center;height:24px;width:32px;transition:background-color .15s;border-radius:8px}nav-header-actions button,nav-header-actions button *{cursor:pointer}nav-header-actions button i{color:#fff;font-size:20px}nav-header-actions button:hover{background:rgba(255,255,255,.1)}nav-header-actions button:hover i{font-variation-settings:"FILL" 1,"wght" 400 !important}nav-header-actions button.collapse-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}nav-header-actions button.collapse-button i:before{font-family:inherit;content:"dock_to_right"}`,""]);const m=d},"app/resources/elements/navigation-actions":(t,o,e)=>{e.r(o),e.d(o,{NavigationActions:()=>i});var n=e(15215),r=e("aurelia-framework");let i=class{constructor(){this.canNavigateBack=!1,this.canNavigateForward=!1}attached(){this.navigationListener=this.navigationListener.bind(this),window.navigation.addEventListener("navigatesuccess",this.navigationListener.bind(this))}detached(){window.navigation.removeEventListener("navigatesuccess",this.navigationListener)}navigationListener(){this.canNavigateBack=window.navigation.canGoBack,this.canNavigateForward=window.navigation.canGoForward}handleBackClick(){this.canNavigateBack&&window.history.back()}handleForwardClick(){this.canNavigateForward&&window.history.forward()}};i=(0,n.Cg)([(0,r.autoinject)()],i)},"app/resources/elements/navigation-actions.html":(t,o,e)=>{e.r(o),e.d(o,{default:()=>n});const n='<template> <require from="./navigation-actions.scss"></require> <div class="button-container"> <button disabled.bind="!canNavigateBack" click.delegate="handleBackClick()" class="back-button"><i></i></button> <button disabled.bind="!canNavigateForward" click.delegate="handleForwardClick()" class="forward-button"> <i></i> </button> </div> </template> '},"app/resources/elements/navigation-actions.scss":(t,o,e)=>{e.r(o),e.d(o,{default:()=>m});var n=e(31601),r=e.n(n),i=e(76314),a=e.n(i),s=e(4417),l=e.n(s),p=new URL(e(83959),e.b),d=a()(r()),c=l()(p);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,navigation-actions .button-container button.back-button i,navigation-actions .button-container button.forward-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}navigation-actions .button-container{display:flex;flex-direction:row}navigation-actions .button-container button{background:none;border:none;cursor:pointer;display:flex;align-items:center;justify-content:center;height:24px;width:32px;transition:background-color .15s;border-radius:8px}navigation-actions .button-container button,navigation-actions .button-container button *{cursor:pointer}navigation-actions .button-container button i{color:#fff;font-size:20px}navigation-actions .button-container button:hover{background:rgba(255,255,255,.1)}navigation-actions .button-container button:active{background:rgba(255,255,255,.1)}navigation-actions .button-container button:disabled{cursor:not-allowed;opacity:.5}navigation-actions .button-container button:disabled i{cursor:not-allowed}navigation-actions .button-container button.back-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}navigation-actions .button-container button.back-button i:before{font-family:inherit;content:"arrow_back"}navigation-actions .button-container button.forward-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}navigation-actions .button-container button.forward-button i:before{font-family:inherit;content:"arrow_forward"}`,""]);const m=d},"app/resources/elements/pro-onboarding-tooltip":(t,o,e)=>{e.r(o),e.d(o,{ProOnboardingTooltip:()=>p});var n=e(15215),r=e("aurelia-event-aggregator"),i=e("aurelia-framework"),a=e(62914),s=e("dialogs/pro-onboarding-dialog"),l=e(38777);let p=class{#t;#o;#e;#n;#r;constructor(t,o,e,n){this.open=!1,this.helpVisible=!1,this.#t=t,this.#e=o,this.#n=e,this.#r=n}attached(){const t=this.#r.parentElement,o=[this.#t.subscribe("open-pro-onboarding-tooltip",(()=>this.open=!0))];t&&(o.push((0,l.yB)(t,"mouseenter",this.#i.bind(this))),o.push((0,l.yB)(t,"mouseleave",this.#a.bind(this)))),this.#o=new l.Vd(o)}detached(){this.#o.dispose()}openProOnboardingDialog(){this.#e.open({trigger:"pro_onboarding_tooltip",mode:"post-pro-upgrade"}),this.open=!1}openChanged(){this.open&&this.#n.event("pro_onboarding_tooltip_open",{},a.Io)}#i(){this.open||(this.helpVisible=!0)}#a(){this.helpVisible=!1}};(0,n.Cg)([(0,i.bindable)({defaultBindingMode:i.bindingMode.twoWay}),(0,n.Sn)("design:type",Boolean)],p.prototype,"open",void 0),p=(0,n.Cg)([(0,i.autoinject)(),(0,n.Sn)("design:paramtypes",[r.EventAggregator,s.ProOnboardingDialogService,a.j0,Element])],p)},"app/resources/elements/pro-onboarding-tooltip.html":(t,o,e)=>{e.r(o),e.d(o,{default:()=>a});var n=e(14385),r=e.n(n),i=new URL(e(88536),e.b);const a='<template> <require from="./pro-onboarding-tooltip.scss"></require> <require from="../../../shared/resources/elements/tooltip"></require> <tooltip class="initial-tooltip" direction="bottom-left" open.bind="helpVisible"> <div slot="content">${\'pro_onboarding_tooltip.click_to_see_the_benefits_of_pro\' | i18n}</div> </tooltip> <tooltip class="pro-onboarding-tooltip custom-tooltip" direction="bottom-left" open.bind="open" click-to-open="true"> <div slot="content"> <div class="layout" click.delegate="$event.stopPropagation()"> <div class="left"> <h1 innerhtml.bind="\'pro_onboarding_tooltip.youre_a_true_pro\' | i18n | markdown"></h1> <p innerhtml.bind="\'pro_onboarding_tooltip.say_hello_to_the_ultimate_gaming_experience\' | i18n | markdown"></p> <button click.delegate="openProOnboardingDialog()"> ${\'pro_onboarding_tooltip.explore_pro_features\' | i18n} </button> </div> <div class="right"> <img src="'+r()(i)+'"> </div> </div> </div> </tooltip> </template> '},"app/resources/elements/pro-onboarding-tooltip.scss":(t,o,e)=>{e.r(o),e.d(o,{default:()=>s});var n=e(31601),r=e.n(n),i=e(76314),a=e.n(i)()(r());a.push([t.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}pro-onboarding-tooltip .initial-tooltip .tooltip .tooltip-content{white-space:nowrap}pro-onboarding-tooltip .pro-onboarding-tooltip{background:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.05);border-radius:10px}pro-onboarding-tooltip .pro-onboarding-tooltip:before{content:"";position:absolute;left:0;right:0;top:-10px;height:10px}pro-onboarding-tooltip .pro-onboarding-tooltip .tooltip-content{padding:0;border:0 !important}.theme-default pro-onboarding-tooltip .pro-onboarding-tooltip .tooltip-content{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro pro-onboarding-tooltip .pro-onboarding-tooltip .tooltip-content{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro pro-onboarding-tooltip .pro-onboarding-tooltip .tooltip-content{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro pro-onboarding-tooltip .pro-onboarding-tooltip .tooltip-content{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro pro-onboarding-tooltip .pro-onboarding-tooltip .tooltip-content{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}pro-onboarding-tooltip .pro-onboarding-tooltip .tooltip-content>*+*{margin-left:0}pro-onboarding-tooltip .layout{display:flex;width:500px}pro-onboarding-tooltip .layout .left{flex:1 1 auto;padding:13px 22px 31px 26px;background-color:#1d2836}pro-onboarding-tooltip .layout .left h1{font-weight:800;font-size:21px;line-height:30px;font-weight:700;margin:0 0 4px;color:#fff}pro-onboarding-tooltip .layout .left h1 em{color:var(--color--brand-blue)}pro-onboarding-tooltip .layout .left p{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.7);margin:0 0 24px}pro-onboarding-tooltip .layout .left button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}pro-onboarding-tooltip .layout .left button,pro-onboarding-tooltip .layout .left button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) pro-onboarding-tooltip .layout .left button{border:1px solid #fff}}pro-onboarding-tooltip .layout .left button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}pro-onboarding-tooltip .layout .left button>*:first-child{padding-left:0}pro-onboarding-tooltip .layout .left button>*:last-child{padding-right:0}pro-onboarding-tooltip .layout .left button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) pro-onboarding-tooltip .layout .left button svg *{fill:CanvasText}}pro-onboarding-tooltip .layout .left button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) pro-onboarding-tooltip .layout .left button svg{opacity:1}}pro-onboarding-tooltip .layout .left button img{height:50%}pro-onboarding-tooltip .layout .left button:disabled{opacity:.3}pro-onboarding-tooltip .layout .left button:disabled,pro-onboarding-tooltip .layout .left button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){pro-onboarding-tooltip .layout .left button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}pro-onboarding-tooltip .layout .left button:not(:disabled):hover svg{opacity:1}}pro-onboarding-tooltip .layout .left button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){pro-onboarding-tooltip .layout .left button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}pro-onboarding-tooltip .layout .left button:not(:disabled):active{background-color:var(--theme--highlight)}pro-onboarding-tooltip .layout .right{display:flex;flex:0 0 207px;padding:0;min-height:215px}',""]);const s=a},"app/resources/elements/promotion-banner":(t,o,e)=>{e.r(o),e.d(o,{PromotionBanner:()=>l});var n=e(15215),r=e("aurelia-framework"),i=e(20770),a=e(48881),s=e(811);let l=class{#s;#l;#r;#p;constructor(t,o,e){this.promotions=o,this.#r=t,this.#p=e}attached(){this.#d(),this.#s=new ResizeObserver((()=>this.#d())),this.#s.observe(this.#r),this.#l=new MutationObserver((()=>this.#d())),this.#l.observe(this.#r,{childList:!0})}detached(){this.#s.disconnect(),this.#l?.disconnect()}#d(){document.body.style.setProperty("--promotion-banner-height",`${this.#r.offsetHeight}px`)}get banner(){const t=this.promotions.promotion,o=t?.components?.appBanner;if(o){const e=this.promotions.promotionHistory[t.id]?.bannerDismissedAt;if(!o.dismissible||!e)return o}return null}get bannerUrl(){if(!this.banner?.url)return null;const t=new URL(this.banner.url);return"wemod:"!==t.protocol||t.searchParams.has("trigger")?this.banner.url:(t.searchParams.set("trigger",`promotion:${this.promotions.promotion?.id}/app_banner`),t.toString())}get bannerTheme(){const t=this.banner?.theme||{};return Object.fromEntries(Object.entries(t).map((([t,o])=>[`--promotion-banner--${t}`,o])))}dismiss(t){const o=this.promotions.promotion?.id;t.stopPropagation(),o&&this.#p.dispatch(a.ab,o,(new Date).toISOString())}};(0,n.Cg)([(0,r.computedFrom)("promotions.promotion","promotions.promotionHistory"),(0,n.Sn)("design:type",Object),(0,n.Sn)("design:paramtypes",[])],l.prototype,"banner",null),(0,n.Cg)([(0,r.computedFrom)("banner"),(0,n.Sn)("design:type",Object),(0,n.Sn)("design:paramtypes",[])],l.prototype,"bannerUrl",null),(0,n.Cg)([(0,r.computedFrom)("banner"),(0,n.Sn)("design:type",Object),(0,n.Sn)("design:paramtypes",[])],l.prototype,"bannerTheme",null),l=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[Element,s.n,i.il])],l)},"app/resources/elements/promotion-banner.html":(t,o,e)=>{e.r(o),e.d(o,{default:()=>a});var n=e(14385),r=e.n(n),i=new URL(e(77182),e.b);const a='<template> <require from="./promotion-banner.scss"></require> <require from="../../../resources/elements/pro-cta-label"></require> <require from="../../../shared/resources/elements/close-button"></require> <a class="promotion-banner" if.bind="banner" href.bind="bannerUrl" style.bind="bannerTheme"> <span class="promotion-banner-name" innerhtml.bind="banner.name | markdown"></span> <span class="promotion-banner-description" if.bind="banner.description" innerhtml.bind="banner.description | markdown"></span> <button class="promotion-banner-cta" tabindex="-1"> <pro-cta-label if.bind="banner.buttonType === \'pro_cta\'"></pro-cta-label> <span else>${banner.button}</span> <i><inline-svg src="'+r()(i)+'"></inline-svg></i> </button> <close-button class="light" if.bind="banner.dismissible" click.trigger="dismiss($event)"></close-button> </a> </template> '},"app/resources/elements/promotion-banner.scss":(t,o,e)=>{e.r(o),e.d(o,{default:()=>s});var n=e(31601),r=e.n(n),i=e(76314),a=e.n(i)()(r());a.push([t.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}promotion-banner .promotion-banner{--promotion-banner--foreground: var(--color--accent);--promotion-banner--background: rgba(var(--color--accent--rgb), 0.16);--promotion-banner--badge-background: var(--color--accent);--promotion-banner--badge-foreground: #000;background:var(--promotion-banner--background);display:flex;align-items:center;width:100%;gap:10px;padding:10px 20px 10px 10px;position:sticky}promotion-banner .promotion-banner,promotion-banner .promotion-banner *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) promotion-banner .promotion-banner{border:1px solid #fff}}promotion-banner .promotion-banner,promotion-banner .promotion-banner *{cursor:pointer}promotion-banner .promotion-banner:not([href]){pointer-events:none}promotion-banner .promotion-banner-name{font-weight:900;font-style:italic;font-size:22px;color:var(--promotion-banner--foreground);letter-spacing:-1.2px;flex:0 0 auto;text-transform:uppercase}promotion-banner .promotion-banner-description{font-weight:900;font-style:italic;font-size:12px;color:var(--promotion-banner--foreground);flex:1 1 auto;display:flex;justify-content:center;gap:5px;text-transform:uppercase}promotion-banner .promotion-banner-description em{font-style:inherit;opacity:.5}promotion-banner .promotion-banner-cta{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--promotion-banner--foreground);--cta__icon--color: var(--promotion-banner--foreground);font-size:12px;line-height:18px;font-weight:900;--cta--padding: 12px;--cta--height: 24px;--cta--hover--border-width: 1px;color:var(--promotion-banner--foreground) !important;background-color:rgba(0,0,0,0) !important;font-style:italic;text-transform:uppercase;box-shadow:none !important;position:relative}promotion-banner .promotion-banner-cta,promotion-banner .promotion-banner-cta *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) promotion-banner .promotion-banner-cta{border:1px solid #fff}}promotion-banner .promotion-banner-cta>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}promotion-banner .promotion-banner-cta>*:first-child{padding-left:0}promotion-banner .promotion-banner-cta>*:last-child{padding-right:0}promotion-banner .promotion-banner-cta svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) promotion-banner .promotion-banner-cta svg *{fill:CanvasText}}promotion-banner .promotion-banner-cta svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) promotion-banner .promotion-banner-cta svg{opacity:1}}promotion-banner .promotion-banner-cta img{height:50%}promotion-banner .promotion-banner-cta:disabled{opacity:.3}promotion-banner .promotion-banner-cta:disabled,promotion-banner .promotion-banner-cta:disabled *{cursor:default;pointer-events:none}@media(hover: hover){promotion-banner .promotion-banner-cta:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}promotion-banner .promotion-banner-cta:not(:disabled):hover svg{opacity:1}}promotion-banner .promotion-banner-cta:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){promotion-banner .promotion-banner-cta:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--promotion-banner--foreground);background-color:rgba(0,0,0,0)}}promotion-banner .promotion-banner-cta:not(:disabled):active{background-color:var(--promotion-banner--foreground)}promotion-banner .promotion-banner-cta svg{opacity:1}promotion-banner .promotion-banner-cta svg *{fill:var(--promotion-banner--foreground)}promotion-banner .promotion-banner-cta:before{content:"";position:absolute;top:0;right:0;bottom:0;left:0;border-radius:99px;box-shadow:inset 0 0 0 1px var(--promotion-banner--foreground);opacity:.5}',""]);const s=a},"app/resources/elements/remote-button":(t,o,e)=>{e.r(o),e.d(o,{RemoteButton:()=>m});var n=e(15215),r=e("aurelia-framework"),i=e(20770),a=e(85805),s=e(60692),l=e(68539),p=e(54995),d=e(70236),c=e(48881);let m=class{#p;#c;constructor(t,o,e){this.remote=t,this.tooltipOpen=!1,this.hasFreeRemote=!1,this.remoteEducationOpen=!1,this.#p=o,this.#c=e}attached(){this.hasFreeRemote=!!this.#c.assignments.get(s.n.MakeMobileAppFree),this.showRemoteEducation&&this.showRemoteEducationDialog()}detached(){this.tooltipOpen=!1}showRemoteEducationDialog(){this.remoteEducationOpen=!0,(0,c.JD)(this.#p,c.NX,"remoteEducationShown",!0)}disconnect(t){t&&t.preventDefault(),this.remote.disconnect()}reconnect(){this.remote.reconnect()}get isConnected(){return this.remote.status===a.t.Connected}get wasForceDisconnected(){return this.remote.status===a.t.ForceDisconnected}openTooltip(){this.isConnected&&!this.wasForceDisconnected||(this.tooltipOpen=!0)}handleRemoteButtonClick(){this.wasForceDisconnected?this.reconnect():this.openTooltip()}get canUseRemote(){return!!this.account?.subscription||this.hasFreeRemote}get showRemoteEducation(){return this.hasNotSeenRemoteDialog&&(this.hasFreeRemote||!!this.account.subscription)}get highlight(){return this.canUseRemote&&!this.hasUsedRemote}};(0,n.Cg)([r.observable,(0,n.Sn)("design:type",Boolean)],m.prototype,"tooltipOpen",void 0),(0,n.Cg)([r.observable,(0,n.Sn)("design:type",Boolean)],m.prototype,"hasFreeRemote",void 0),(0,n.Cg)([(0,r.computedFrom)("remote.status"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],m.prototype,"isConnected",null),(0,n.Cg)([(0,r.computedFrom)("remote.status"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],m.prototype,"wasForceDisconnected",null),(0,n.Cg)([(0,r.computedFrom)("account.subscription","hasFreeRemote"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],m.prototype,"canUseRemote",null),(0,n.Cg)([(0,r.computedFrom)("hasNotSeenRemoteDialog","account.subscription","hasFreeRemote"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],m.prototype,"showRemoteEducation",null),(0,n.Cg)([(0,r.computedFrom)("canUseRemote","hasUsedRemote"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],m.prototype,"highlight",null),m=(0,n.Cg)([(0,p.m6)({selectors:{account:(0,p.$t)((t=>t.account)),hasNotSeenRemoteDialog:(0,p.$t)((t=>!(0,d.Lt)(t.account?.flags??0,1024)&&!t.flags?.remoteEducationShown)),hasUsedRemote:(0,p.$t)((t=>(0,d.Lt)(t.account?.flags??0,1024)))}}),(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[a.e,i.il,l.z])],m)},"app/resources/elements/remote-button.html":(t,o,e)=>{e.r(o),e.d(o,{default:()=>n});const n='<template class="${isConnected ? \'connected\' : \'\'} ${highlight ? \'highlight\' : \'\'} ${wasForceDisconnected ? \'force-disconnected\' : \'\'}"> <require from="./remote-button.scss"></require> <require from="./remote-tooltip"></require> <require from="../../../pro-promos/tooltips/remote-app-tooltip"></require> <require from="../../../cheats/resources/elements/remote-education-popup"></require> <require from="../../../resources/custom-attributes/close-if-press-escape"></require> <require from="../../../resources/custom-attributes/detach-el"></require> <require from="../../../shared/resources/elements/tooltip"></require> <require from="../../../shared/resources/elements/pro-badge"></require> <require from="../../../shared/resources/custom-attributes/close-if-click-outside"></require> <template if.bind="canUseRemote"> <remote-education-popup if.bind="remoteEducationOpen" close-if-click-outside.two-way="remoteEducationOpen" detach-el></remote-education-popup> <div class="action-button"> <button class="remote-button ${highlight ? \'remote-button--highlighted\' : \'\'}" click.delegate="handleRemoteButtonClick()"> <span class="icon-container"><i class="icon">phone_iphone</i><span if.bind="highlight || isConnected || wasForceDisconnected" class="status-indicator"></span></span> <span if.bind="isConnected" class="label">${\'remote_button.connected\' | i18n}</span> <span if.bind="wasForceDisconnected" class="label">${\'remote_button.reconnect_phone\' | i18n}</span> <span if.bind="!isConnected && !wasForceDisconnected" class="label">${\'remote_button.connect_phone\' | i18n}</span> <span if.bind="isConnected" click.delegate="disconnect($event)" class="close-button"> <i>close</i> </span> </button> <div class="highlight-background" if.bind="highlight"></div> </div> <remote-tooltip if.bind="tooltipOpen" class="remote-tooltip" close-if-press-escape="open.bind: tooltipOpen" open.bind="tooltipOpen" detach-el></remote-tooltip> <template else> <tooltip if.bind="isConnected" class="initial-tooltip" direction="top-right"> <div slot="content">${\'remote_tooltip.disconnect_remote_app\' | i18n}</div> </tooltip> <tooltip if.bind="wasForceDisconnected" class="initial-tooltip disconnect-tooltip" direction="top-right" open.bind="helpVisible"> <div slot="content">${\'remote_tooltip.force_disconnected_message\' | i18n}</div> </tooltip> </template> </template> <div else> <div class="action-button tooltip-trigger"> <button class="remote-button" pro-cta="trigger: title_sidebar_remote_button; feature: remote;" data-tooltip-trigger-for="remote-tooltip-large-promo"> <span class="icon-container"> <i class="icon">phone_iphone</i> </span> <span class="label">${\'remote_button.connect_phone\' | i18n}</span> <pro-badge class="small"></pro-badge> </button> </div> <remote-app-tooltip tooltip-id="remote-tooltip-large-promo"></remote-app-tooltip> </div> </template> '},"app/resources/elements/remote-button.scss":(t,o,e)=>{e.r(o),e.d(o,{default:()=>m});var n=e(31601),r=e.n(n),i=e(76314),a=e.n(i),s=e(4417),l=e.n(s),p=new URL(e(83959),e.b),d=a()(r()),c=l()(p);d.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,remote-button .action-button i,remote-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}remote-button{flex:1 1 auto;position:relative}remote-button .action-button{border-radius:16px;overflow:hidden;position:relative;display:block}remote-button .action-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;height:16px}remote-button .action-button .icon-container{height:16px;width:16px;position:relative}remote-button .action-button button,remote-button .action-button .button{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;display:flex;align-items:center;transition:color .3s ease-in-out;background:rgba(var(--theme--background-accent--rgb), 0.9);border:1px solid rgba(255,255,255,.1);border-radius:16px;gap:12px;padding:10px 10px 10px 16px;color:rgba(255,255,255,.8);height:44px;width:100%;min-width:170px}remote-button .action-button button i,remote-button .action-button .button i{color:rgba(255,255,255,.6)}remote-button .action-button .label{line-height:14px;text-align:left;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;text-overflow:ellipsis;overflow:hidden}remote-button .action-button.disabled button,remote-button .action-button.disabled .button{background-color:rgba(var(--theme--background-accent--rgb), 0.4);color:var(--theme--text-secondary)}remote-button .action-button.tooltip-trigger{overflow:visible}remote-button .remote-button pro-badge{margin-left:-4px}remote-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}remote-button>.overlay{position:fixed;top:0;right:0;left:0;bottom:0;background:#0d0f12;opacity:.5;z-index:-1}remote-button .remote-button--highlighted{border:.5px solid rgba(255,255,255,.15)}.theme-default remote-button .remote-button--highlighted{color:#fff}.theme-purple-pro remote-button .remote-button--highlighted{color:#fff}.theme-green-pro remote-button .remote-button--highlighted{color:#fff}.theme-orange-pro remote-button .remote-button--highlighted{color:#fff}.theme-pro remote-button .remote-button--highlighted{color:#fff}.theme-default remote-button .remote-button--highlighted i{color:rgba(255,255,255,.8)}.theme-purple-pro remote-button .remote-button--highlighted i{color:rgba(255,255,255,.8)}.theme-green-pro remote-button .remote-button--highlighted i{color:rgba(255,255,255,.8)}.theme-orange-pro remote-button .remote-button--highlighted i{color:rgba(255,255,255,.8)}.theme-pro remote-button .remote-button--highlighted i{color:rgba(255,255,255,.8)}remote-button .remote-button--highlighted .status-indicator{background-color:var(--theme--highlight)}remote-button .highlight-background{position:absolute;height:100%;width:100%;top:0;pointer-events:none;background:linear-gradient(96deg, rgba(255, 255, 255, 0) 20.4%, rgba(255, 255, 255, 0.08) 46.92%, rgba(255, 255, 255, 0) 73.43%);animation:1s ease-in-out 2s infinite glow;transform:translateX(-100%)}@keyframes glow{from{transform:translateX(-100%)}to{transform:translateX(100%)}}remote-button .close-button{cursor:pointer !important;display:flex;align-items:center;justify-content:center;border-radius:6px;width:28px;height:28px;margin-left:auto;transition:background-color .3s ease-in-out}remote-button .close-button>*{cursor:pointer !important}remote-button .close-button:hover{background-color:rgba(255,255,255,.15)}remote-button .status-indicator{position:absolute;top:-2px;right:0;border-radius:100px;outline:2px solid rgba(var(--theme--background-accent--rgb));width:6px;height:6px}remote-button:not(.connected) .remote-button:hover{color:#fff;background:linear-gradient(0deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.03) 100%),rgba(var(--theme--background-accent--rgb), 0.9)}remote-button.force-disconnected .status-indicator{background:var(--color--alert);outline:2px solid rgba(var(--theme--background-accent--rgb))}remote-button.connected *{cursor:default}remote-button.connected .status-indicator{background:var(--color--accent);outline:2px solid rgba(var(--theme--background-accent--rgb))}remote-button.connected .remote-button:hover{background:rgba(var(--theme--background-accent--rgb), 0.9);color:rgba(255,255,255,.8)}remote-button.connected .border{stroke:var(--theme--highlight);stroke-opacity:1}remote-button .initial-tooltip .tooltip .tooltip-content{white-space:nowrap}remote-button .initial-tooltip .tooltip .tooltip-arrow{margin-right:-10px;right:15%}remote-button .initial-tooltip.disconnect-tooltip .tooltip .tooltip-content{white-space:wrap;width:300px}remote-button remote-tooltip{position:absolute;top:calc(100% + 8px);right:0}remote-button tooltip .tooltip-content{min-width:300px}`,""]);const m=d},"app/resources/elements/remote-tooltip":(t,o,e)=>{e.r(o),e.d(o,{RemoteTooltip:()=>s});var n=e(15215),r=e("aurelia-framework"),i=e(38777),a=e(85805);let s=class{#m;constructor(t){this.remote=t,this.open=!1,this.remoteUrl="website://remote"}attached(){this.#m=(new i.Vd).push(this.remote.onStatusChanged((t=>{t===a.t.Connected&&(this.open=!1)})))}detached(){this.#m?.dispose(),this.#m=null}get isConnected(){return this.remote.status===a.t.Connected}get wasForceDisconnected(){return this.remote.status===a.t.ForceDisconnected}};(0,n.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.twoWay}),(0,n.Sn)("design:type",Boolean)],s.prototype,"open",void 0),(0,n.Cg)([(0,r.computedFrom)("remote.status"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],s.prototype,"isConnected",null),(0,n.Cg)([(0,r.computedFrom)("remote.status"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],s.prototype,"wasForceDisconnected",null),s=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[a.e])],s)},"app/resources/elements/remote-tooltip.html":(t,o,e)=>{e.r(o),e.d(o,{default:()=>a});var n=e(14385),r=e.n(n),i=new URL(e(15963),e.b);const a='<template class="${isConnected ? \'connected\' : \'not-connected\'}"> <require from="./remote-tooltip.scss"></require> <require from="../../../resources/elements/remote-qr-code"></require> <require from="../../../resources/elements/remote-code"></require> <div class="overlay" click.delegate="open = false"></div> <div class="remote-tooltip ${isConnected ? \'connected\' : \'\'}"> <div class="remote-tooltip-content"> <template if.bind="!isConnected && !wasForceDisconnected"> <div class="top-wrapper"> <h1 class="header"> <i>phone_iphone</i> <span class="label">${\'remote_tooltip.connect_to_wemod_remote\' | i18n}</span> </h1> <div class="text">${\'remote_tooltip.enter_this_pin_on_your_device_to_connect\' | i18n}</div> <remote-code visible.bind="open"></remote-code> </div> <hr class="remote-tooltip-section-divider"> <div class="instructions-wrapper"> <div class="instructions"> <h2 class="header"> <i>download</i> ${\'remote_tooltip.get_the_app\' | i18n} <i class="platforms"><inline-svg src="'+r()(i)+'"></inline-svg></i> </h2> <div class="content"> <remote-qr-code></remote-qr-code> <p class="text" innerhtml.bind="\'remote_tooltip.scan_the_qr_code_or_visit_the_site\' | i18n:{url: remoteUrl} | markdown"></p> </div> </div> </div> </template> </div> </div> </template> '},"app/resources/elements/remote-tooltip.scss":(t,o,e)=>{e.r(o),e.d(o,{default:()=>m});var n=e(31601),r=e.n(n),i=e(76314),a=e.n(i),s=e(4417),l=e.n(s),p=new URL(e(83959),e.b),d=a()(r()),c=l()(p);d.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,remote-tooltip i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}remote-tooltip{display:block}remote-tooltip i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}remote-tooltip>.overlay{position:fixed;top:0;right:0;left:0;bottom:0;background:#0d0f12;opacity:.5;z-index:-1}remote-tooltip .remote-tooltip{background:var(--theme--background-accent) linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05)) !important;border-radius:16px;width:300px !important;border:1px solid rgba(255,255,255,.15);overflow:hidden}remote-tooltip .remote-tooltip *{font-size:14px}remote-tooltip .remote-tooltip i{display:flex;align-items:center;justify-content:center;height:20px;width:20px;color:rgba(255,255,255,.6)}remote-tooltip .remote-tooltip-section-divider{margin:12px 0;opacity:.1;mix-blend-mode:overlay;border:none;height:0;border-top:1px solid #fff}remote-tooltip .remote-tooltip .top-wrapper{padding:8px}remote-tooltip .remote-tooltip .top-wrapper .text{height:64px;padding:0 8px;display:flex;align-items:center}remote-tooltip .remote-tooltip .text{margin:0;padding:0;color:rgba(255,255,255,.6);font-weight:500;line-height:24px}remote-tooltip .remote-tooltip .text,remote-tooltip .remote-tooltip .text a{font-size:14px;font-weight:500}remote-tooltip .remote-tooltip .text a{text-decoration:none;color:rgba(var(--theme--highlight--rgb), 0.8)}remote-tooltip .remote-tooltip .text a:hover{color:var(--theme--highlight)}remote-tooltip .remote-tooltip .header{display:flex;align-items:center;gap:8px;color:rgba(255,255,255,.8);margin:0px;height:40px;padding:0 8px}remote-tooltip .remote-tooltip .header i{width:20px;height:20px;font-size:20px}remote-tooltip .remote-tooltip .help-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;--cta--height: 15px;--cta--hover--border-width: 1px;min-width:var(--cta--height);width:var(--cta--height);border-radius:50%;justify-content:center;align-items:center;position:absolute;right:18px;bottom:21px}remote-tooltip .remote-tooltip .help-button,remote-tooltip .remote-tooltip .help-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) remote-tooltip .remote-tooltip .help-button{border:1px solid #fff}}remote-tooltip .remote-tooltip .help-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}remote-tooltip .remote-tooltip .help-button>*:first-child{padding-left:0}remote-tooltip .remote-tooltip .help-button>*:last-child{padding-right:0}remote-tooltip .remote-tooltip .help-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) remote-tooltip .remote-tooltip .help-button svg *{fill:CanvasText}}remote-tooltip .remote-tooltip .help-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) remote-tooltip .remote-tooltip .help-button svg{opacity:1}}remote-tooltip .remote-tooltip .help-button img{height:50%}remote-tooltip .remote-tooltip .help-button:disabled{opacity:.3}remote-tooltip .remote-tooltip .help-button:disabled,remote-tooltip .remote-tooltip .help-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){remote-tooltip .remote-tooltip .help-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}remote-tooltip .remote-tooltip .help-button:not(:disabled):hover svg{opacity:1}}remote-tooltip .remote-tooltip .help-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}remote-tooltip .remote-tooltip .help-button,remote-tooltip .remote-tooltip .help-button>*{padding:0 !important}remote-tooltip .remote-tooltip .instructions-wrapper{margin:0;padding:0 8px 8px 8px;position:relative;text-align:center}remote-tooltip .remote-tooltip .instructions{display:flex;flex-direction:column}remote-tooltip .remote-tooltip .instructions .platforms{margin-left:auto;width:auto !important;height:auto !important}remote-tooltip .remote-tooltip .instructions .title{display:flex;align-items:center;color:rgba(255,255,255,.8);height:40px;gap:8px;margin:0 8px}remote-tooltip .remote-tooltip .instructions .title i{width:20px;height:20px;font-size:20px}remote-tooltip .remote-tooltip .instructions remote-qr-code{width:80px !important;height:80px !important;flex:0 0 auto;border-radius:8px}remote-tooltip .remote-tooltip .instructions .content{text-align:left;font-size:14px;display:flex;padding:8px;align-items:center;gap:12px;align-self:stretch}remote-tooltip .remote-tooltip .message{margin:0;padding:0;color:rgba(255,255,255,.8)}remote-tooltip .remote-tooltip .message+.connection-button{margin-top:9px}remote-tooltip .remote-tooltip remote-code{margin:12px auto 0 auto}remote-tooltip .remote-tooltip .connection-button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}remote-tooltip .remote-tooltip .connection-button,remote-tooltip .remote-tooltip .connection-button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) remote-tooltip .remote-tooltip .connection-button{border:1px solid #fff}}remote-tooltip .remote-tooltip .connection-button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}remote-tooltip .remote-tooltip .connection-button>*:first-child{padding-left:0}remote-tooltip .remote-tooltip .connection-button>*:last-child{padding-right:0}remote-tooltip .remote-tooltip .connection-button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) remote-tooltip .remote-tooltip .connection-button svg *{fill:CanvasText}}remote-tooltip .remote-tooltip .connection-button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) remote-tooltip .remote-tooltip .connection-button svg{opacity:1}}remote-tooltip .remote-tooltip .connection-button img{height:50%}remote-tooltip .remote-tooltip .connection-button:disabled{opacity:.3}remote-tooltip .remote-tooltip .connection-button:disabled,remote-tooltip .remote-tooltip .connection-button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){remote-tooltip .remote-tooltip .connection-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}remote-tooltip .remote-tooltip .connection-button:not(:disabled):hover svg{opacity:1}}remote-tooltip .remote-tooltip .connection-button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){remote-tooltip .remote-tooltip .connection-button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}remote-tooltip .remote-tooltip .connection-button:not(:disabled):active{background-color:var(--theme--highlight)}`,""]);const m=d},"app/resources/elements/reward-card":(t,o,e)=>{e.r(o),e.d(o,{RewardCard:()=>m});var n=e(15215),r=e(62914),i=e("aurelia-framework"),a=e(18776),s=e(20770),l=e("rewards/resources/elements/game-pass-reward"),p=e(98119),d=e(54995),c=e(48881);let m=class{#n;#b;#p;constructor(t,o,e){this.gamePassRewardName=l.GAME_PASS_REWARD_NAME,this.#n=t,this.#b=o,this.#p=e}openRewards(t){this.#n.event("sidebar_reward_card_click",{reward:t},r.Io),this.#b.navigateToRoute("rewards")}dismissRewardCard(t){this.#n.event("sidebar_reward_dismiss",{reward:t},r.Io),this.#p.dispatch(c.T9,t)}get rewardNameToShow(){if(!this.rewardsFeatureEnabled)return null;const t=Object.keys(this.acknowledgedRewards).filter((t=>!!this.acknowledgedRewards[t]));return this.account?.rewardOffers?.find((o=>(0,p.OX)(o)&&(0,p.oH)(o)&&!t.includes(o.rewardKey)))?.rewardKey??null}};(0,n.Cg)([(0,i.computedFrom)("acknowledgedRewards","account","rewardsFeatureEnabled"),(0,n.Sn)("design:type",Object),(0,n.Sn)("design:paramtypes",[])],m.prototype,"rewardNameToShow",null),m=(0,n.Cg)([(0,d.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,d.$t)((t=>t.account)),rewardsFeatureEnabled:(0,d.$t)((t=>t.catalog?.features?.partner_rewards)),acknowledgedRewards:(0,d.$t)((t=>t.acknowledgedRewards))}}),(0,i.autoinject)(),(0,n.Sn)("design:paramtypes",[r.j0,a.Ix,s.il])],m)},"app/resources/elements/reward-card.html":(t,o,e)=>{e.r(o),e.d(o,{default:()=>a});var n=e(14385),r=e.n(n),i=new URL(e(34626),e.b);const a='<template> <require from="./reward-card.scss"></require> <require from="../../../shared/resources/elements/close-button"></require> <div class="reward-card" if.bind="rewardNameToShow === gamePassRewardName"> <div class="game-pass-reward" click.delegate="openRewards(gamePassRewardName)"> <div class="header"> <div class="xbox-logo"> <inline-svg src="'+r()(i)+'"></inline-svg> </div> <span>${\'rewards.a_gift_for_you\' | i18n}</span> <close-button class="light" click.delegate="dismissRewardCard(gamePassRewardName)"></close-button> </div> <div class="content">${\'rewards.congrats_pro_subscriber\' | i18n}</div> <a class="claim-now">${\'rewards.claim_now_sidebar\' | i18n}</a> </div> </div> </template> '},"app/resources/elements/reward-card.scss":(t,o,e)=>{e.r(o),e.d(o,{default:()=>s});var n=e(31601),r=e.n(n),i=e(76314),a=e.n(i)()(r());a.push([t.id,'.reward-card{padding:8px}.reward-card .game-pass-reward{position:relative;overflow:hidden;display:flex;flex-direction:column;border-radius:16px;padding:12px;color:#fff;gap:10px;text-align:left;align-items:start;background:linear-gradient(250deg, #afb1b4 0%, #929497 21.9%, #626467 50.09%, #444649 70.11%, #333538 83.89%, #1e2023 100%),rgba(255,255,255,.05)}.reward-card .game-pass-reward::before{content:"";position:absolute;top:0;left:-100%;width:75%;height:100%;background:linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);transform:skewX(-25deg);animation:reward-card-shine 7s infinite}@keyframes reward-card-shine{0%{left:-100%}20%{left:100%}100%{left:200%}}.reward-card .game-pass-reward:hover{transform:scale(1.02)}.reward-card .game-pass-reward:hover,.reward-card .game-pass-reward:hover *{cursor:pointer}.reward-card .game-pass-reward .header{display:flex;align-items:center;gap:8px;width:100%;color:rgba(255,255,255,.8);font-size:11px;font-weight:700;line-height:16px;letter-spacing:.5px;text-transform:uppercase}.reward-card .game-pass-reward .header close-button{margin-left:auto;margin-right:-4px}.reward-card .game-pass-reward .header close-button:not(:hover){background:none !important}.reward-card .game-pass-reward .header .xbox-logo{display:flex}.reward-card .game-pass-reward .header .xbox-logo svg *{fill-opacity:1}.reward-card .game-pass-reward .content{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:rgba(255,255,255,.8)}.reward-card .game-pass-reward .claim-now{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px}',""]);const s=a}}]);
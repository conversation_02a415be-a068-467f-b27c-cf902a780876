"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2475],{12511:(t,e,r)=>{r.d(e,{u:()=>n});const n={catalog:null,catalogCacheKey:null,account:null,token:null,favoriteTitles:{},titleHistory:{},gameHistory:{},installedApps:{},installedGameVersions:{},trainers:{},trainerNotesRead:{},cheatBlueprintInstructionsRead:{},precisionModsSectionsViewed:{},modTimerMessagesDismissed:{},gameTranslations:{},gamePreferences:{},titlePreferences:{},correlatedUnavailableTitles:{},correlatedUnavailableTitleRefreshes:{},trainerFeedbackRequests:{},mapSettings:{},titleMapSettings:{},followedGames:[],settings:{theme:"default",analytics:!0,closeToTray:!0,cheatSounds:!0,cheatSoundVolume:50,confirmDisablingSaveCheats:!0,enableSaveCheatsByDefault:!0,useWindowsContrastMode:!0,allowDesktopNotifications:!0,preventCatalogRefresh:!1,reduceMotion:!1,showModHotkeys:!0,isTitleSidebarCollapsed:!1,enableOverlay:!0,lastChangelogIdSeen:r(58534).A},flags:{firstRun:!0,overlayAnnouncementShown:!0,hasUsedInteractiveControls:!1,hasUsedHotkeys:!1,hasUsedOverlay:!1,hasUsedMaps:!1,hasClickedModPin:!1,hideTrainerInstructionsNote:!1},timestamps:{},installation:{installedAt:(new Date).toISOString()},promotionHistory:{},coachingTipHistory:{},gameCollectionPreferences:{},counters:{secondsPlayedToday:0,timesAssistantPopoutSeen:0},sidebarCollapsedLists:{},assistantHistory:{},pinnedMods:{},modTimers:{},acknowledgedRewards:{},favoriteVideos:[],stateVersion:null}},26700:(t,e,r)=>{r.d(e,{h:()=>a,B:()=>s});var n=r(79896),i=r(16928);async function s(t,e){const r=i.join(t,(e??"").replace(/[\\/:"*?<>|]+/g,"").replace(/  +/g," "));return await n.promises.mkdir(r,{recursive:!0}),r}async function a(t){try{return await n.promises.access(t,n.constants.F_OK),!0}catch(t){return!1}}},29844:(t,e,r)=>{r.d(e,{LC:()=>g,P8:()=>d,fj:()=>m,pD:()=>b});const n=46,i=58,s=47,a=92,o=65,u=97,c=90,l=122;function f(t){return t===s||t===a}function h(t){return t>=o&&t<=c||t>=u&&t<=l}function d(t,e){let r=0,n=-1,s=!0;if(t.length>=2&&h(t.charCodeAt(0))&&t.charCodeAt(1)===i&&(r=2),void 0!==e&&e.length>0&&e.length<=t.length){if(e===t)return"";let i=e.length-1,a=-1;for(let o=t.length-1;o>=r;--o){const u=t.charCodeAt(o);if(f(u)){if(!s){r=o+1;break}}else-1===a&&(s=!1,a=o+1),i>=0&&(u===e.charCodeAt(i)?-1==--i&&(n=o):(i=-1,n=a))}r===n?n=a:-1===n&&(n=t.length)}else{for(let e=t.length-1;e>=r;--e)if(f(t.charCodeAt(e))){if(!s){r=e+1;break}}else-1===n&&(s=!1,n=e+1);if(-1===n)return""}return t.slice(r,n)}function b(t){const e=t.length;if(0===e)return".";let r=-1,n=0;const s=t.charCodeAt(0);if(1===e)return f(s)?t:".";if(f(s)){if(r=n=1,f(t.charCodeAt(1))){let i=2,s=i;for(;i<e&&!f(t.charCodeAt(i));)i++;if(i<e&&i!==s){for(s=i;i<e&&f(t.charCodeAt(i));)i++;if(i<e&&i!==s){for(s=i;i<e&&!f(t.charCodeAt(i));)i++;if(i===e)return t;i!==s&&(r=n=i+1)}}}}else h(s)&&t.charCodeAt(1)===i&&(r=e>2&&f(t.charCodeAt(2))?3:2,n=r);let a=-1,o=!0;for(let r=e-1;r>=n;--r)if(f(t.charCodeAt(r))){if(!o){a=r;break}}else o=!1;if(-1===a){if(-1===r)return".";a=r}return t.slice(0,a)}function g(t){let e=0,r=-1,s=0,a=-1,o=!0,u=0;t.length>=2&&t.charCodeAt(1)===i&&h(t.charCodeAt(0))&&(e=s=2);for(let i=t.length-1;i>=e;--i){const e=t.charCodeAt(i);if(f(e)){if(!o){s=i+1;break}}else-1===a&&(o=!1,a=i+1),e===n?-1===r?r=i:1!==u&&(u=1):-1!==r&&(u=-1)}return-1===r||-1===a||0===u||1===u&&r===a-1&&r===s+1?"":t.slice(r,a)}function m(...t){if(0===t.length)return".";let e="",r="";for(const n of t)n.length>0&&(e?e+=`\\${n}`:e=r=n);if(!e)return".";let a=!0,o=0;if(f(r.charCodeAt(0))){++o;const t=r.length;t>1&&f(r.charCodeAt(1))&&(++o,t>2&&(f(r.charCodeAt(2))?++o:a=!1))}if(a){for(;o<e.length&&f(e.charCodeAt(o));)o++;o>=2&&(e=`\\${e.slice(o)}`)}return function(t){const e=t.length;if(0===e)return".";const r=t.charCodeAt(0);if(1===e)return r===s?"\\":t;let a=0,o="",u=!1;if(f(r))if(u=!0,f(t.charCodeAt(1))){let r=2,n=r;for(;r<e&&!f(t.charCodeAt(r));)r++;if(r<e&&r!==n){const i=t.slice(n,r);for(n=r;r<e&&f(t.charCodeAt(r));)r++;if(r<e&&r!==n){for(n=r;r<e&&!f(t.charCodeAt(r));)r++;if(r===e)return`\\\\${i}\\${t.slice(n)}\\`;r!==n&&(o=`\\\\${i}\\${t.slice(n,r)}`,a=r)}}}else a=1;else h(r)&&t.charCodeAt(1)===i&&(o=t.slice(0,2),a=2,e>2&&f(t.charCodeAt(2))&&(u=!0,a=3));let c=a<e?function(t,e){let r="",i=0,a=-1,o=0,u=0;for(let c=0;c<=t.length;++c){if(c<t.length)u=t.charCodeAt(c);else{if(f(u))break;u=s}if(f(u)){if(a===c-1||1===o);else if(2===o){if(r.length<2||2!==i||r.charCodeAt(r.length-1)!==n||r.charCodeAt(r.length-2)!==n){if(r.length>2){const t=r.lastIndexOf("\\");-1===t?(r="",i=0):(r=r.slice(0,t),i=r.length-1-r.lastIndexOf("\\")),a=c,o=0;continue}if(0!==r.length){r="",i=0,a=c,o=0;continue}}e&&(r+=r.length>0?"\\..":"..",i=2)}else r.length>0?r+=`\\${t.slice(a+1,c)}`:r=t.slice(a+1,c),i=c-a-1;a=c,o=0}else u===n&&-1!==o?++o:o=-1}return r}(t.slice(a),!u):"";return 0!==c.length||u||(c="."),c.length>0&&f(t.charCodeAt(e-1))&&(c+="\\"),o?u?`${o}\\${c}`:`${o}${c}`:u?`\\${c}`:c}(e)}},38777:(t,e,r)=>{r.d(e,{$U:()=>i,HL:()=>n.HL,Ix:()=>n.Ix,SO:()=>n.SO,Vd:()=>n.Vd,Wn:()=>n.Wn,_M:()=>n._M,_T:()=>n._T,lE:()=>n.lE,nm:()=>n.nm,yB:()=>n.yB});var n=r(92465);function i(t,e,r){return t.on(e,r),(0,n.nm)((()=>t.off(e,r)))}},41224:(t,e,r)=>{function n(t,e){if(t){const r=e.releaseChannels?.find((e=>e.id===t));return!!r?.config.debug}return!1}r.d(e,{Z:()=>n})},41772:(t,e,r)=>{r.d(e,{d:()=>n});const n=["en-US","zh-CN","de-DE","es-ES","fr-FR","pl-PL","pt-BR","tr-TR","ja-JP","ko-KR","hi-IN","id-ID","it-IT","th-TH",`en-US-${r(77847).kA}`]},50643:(t,e,r)=>{r.d(e,{c:()=>n});class n{#t;#e;#r;#n;#i;#s;#a;constructor(t){this.#i=[],this.#s=[],this.#i.push({type:"text",text:t})}setScenario(t){return this.#t=t,this}setDuration(t){return this.#e=t,this}setActivationType(t){return this.#r=t,this}setLaunchString(t){return this.#n=t,this}addText(t,e){return this.#i.push({type:"text",text:t,placement:e}),this}addImage(t,e,r){return this.#i.push({type:"image",src:t,placement:e??void 0,hints:{crop:r}}),this}addAction(t){return this.#s.push(t),this}setAudio(t){return this.#a=t,this}toXml(){const t=document.implementation.createDocument("","",null),e=t.createElement("toast");this.#t&&e.setAttribute("scenario",this.#t),this.#e&&e.setAttribute("duration",this.#e),this.#r&&e.setAttribute("activationType",this.#r),this.#n&&e.setAttribute("launch",this.#n);const r=t.createElement("visual"),n=t.createElement("binding");n.setAttribute("template","ToastGeneric");for(const e of this.#i){const r=t.createElement(e.type);"text"===e.type&&(r.textContent=e.text,e.placement&&r.setAttribute("placement",e.placement),e.hints&&e.hints.maxLines&&r.setAttribute("hint-maxLines",e.hints.maxLines.toString())),"image"===e.type&&(r.setAttribute("src",e.src),e.placement&&r.setAttribute("placement",e.placement),e.hints&&e.hints.crop&&r.setAttribute("hint-crop",e.hints.crop)),n.appendChild(r)}if(r.appendChild(n),e.appendChild(r),this.#s.length>0){const r=t.createElement("actions");for(const e of this.#s){const n=t.createElement("action");n.setAttribute("content",e.content),n.setAttribute("arguments",e.arguments.toString()),e.activationType&&n.setAttribute("activationType",e.activationType),e.placement&&n.setAttribute("placement",e.placement),e.imageUri&&n.setAttribute("imageUri",e.imageUri),e.hints&&(e.hints.inputId&&n.setAttribute("hint-intputId",e.hints.inputId),e.hints.buttonStyle&&n.setAttribute("hint-buttonStyle",e.hints.buttonStyle),e.hints.toolTip&&n.setAttribute("hint-toolTip",e.hints.toolTip)),r.appendChild(n)}e.appendChild(r)}if(this.#a){const r=t.createElement("audio");this.#a.src&&r.setAttribute("src",this.#a.src),this.#a.loop&&r.setAttribute("loop",this.#a.loop?"true":"false"),this.#a.silent&&r.setAttribute("silent",this.#a.silent?"true":"false"),e.appendChild(r)}return t.appendChild(e),(new XMLSerializer).serializeToString(t.documentElement)}}},54700:(t,e,r)=>{r.d(e,{Q:()=>i});var n=r(35392);async function i(t){const e=await n.promises.open(t,"r");try{const t=Buffer.alloc(14);return(await e.read(t,0,t.length,0)).bytesRead===t.length&&67324752===t.readUInt32LE(0)?function(t,e){const r=t.toString(2).padStart(16,"0"),n=[parseInt(r.substring(0,7),2)+1980,parseInt(r.substring(7,11),2),parseInt(r.substring(11,16),2)].join("-"),i=e.toString(2).padStart(16,"0"),s=[parseInt(i.substring(0,5),2),parseInt(i.substring(5,11),2),2*parseInt(i.substring(11,16),2)].join(":");return new Date(`${n} ${s} GMT+0`)}(t.readUInt16LE(12),t.readUInt16LE(10)):null}finally{await e.close()}}},62679:(t,e,r)=>{r.d(e,{I6:()=>c,og:()=>u});var n=r(79896),i=r(16928),s=r(96610),a=(r("services/bugsnag/index"),r(26700));const o=(0,s.getLogger)("thumbnail-generator"),u=".thumbnails";async function c(t,e){const r=await(0,a.B)(t,u),s=i.normalize(e).split(i.sep),c=`${s.length>=2?s[s.length-2]:""}_${s[s.length-1]}`,l=i.join(r,`${c}_thumbnail.webp`);return new Promise(((t,r)=>{try{const s=document.createElement("video");s.crossOrigin="anonymous",s.preload="auto",s.muted=!0;const a=()=>{s.src&&(URL.revokeObjectURL(s.src),s.src="",s.load()),document.body.contains(s)&&document.body.removeChild(s),s.onloadedmetadata=null,s.onseeked=null,s.onerror=null};s.onloadedmetadata=()=>{s.currentTime=Math.max(1,.1*s.duration)},s.onseeked=()=>{try{const u=document.createElement("canvas");u.width=200,u.height=113;const c=u.getContext("2d");if(!c)throw a(),new Error("Failed to get canvas context");c.drawImage(s,0,0,u.width,u.height);const f=c.getImageData(0,0,u.width,u.height).data;let h=0;for(let t=0;t<f.length;t+=16){const e=f[t],r=f[t+1],n=f[t+2];e<20&&r<20&&n<20&&h++}if(h>f.length/16*.9&&s.currentTime<s.duration-5)return void(s.currentTime=Math.min(s.duration-1,s.currentTime+5));const d=u.toDataURL("image/webp",.9).replace(/^data:image\/webp;base64,/,""),b=Buffer.from(d,"base64");n.promises.writeFile(l,b).then((()=>{a();const r=i.basename(e);o.info(`Generated thumbnail for ${r} at ${l}`),t()})).catch((t=>{a(),r(t)}))}catch(t){a(),r(t)}},s.onerror=()=>{a(),r(new Error(`Thumbnail generation video element failed to load: ${s.error?.code??"Unknown error code."} ${s.error?.message??""}`))},n.readFile(e,((t,e)=>{if(t)return a(),void r(t);const n=new Blob([e],{type:"video/mp4"}),i=URL.createObjectURL(n);s.src=i,setTimeout((()=>{!s.duration&&document.body.contains(s)&&(a(),r(new Error("Timed out while loading video")))}),1e4)}))}catch(t){r(t)}}))}},64980:(t,e,r)=>{r.d(e,{u:()=>s});const n=new Map;function i(t){let e=n.get(t);if(!e){const r=[];for(let e=0;e<t.length;e++){const n=t.charAt(e);n>="A"&&n<="Z"?r.push("_",n.toLowerCase()):r.push(n)}e=r.join(""),n.set(t,e)}return e}function s(t){return"object"==typeof t&&null!==t?Array.isArray(t)?t.map(s):Object.fromEntries(Object.entries(t).map((([t,e])=>[i(t),s(e)]))):t}},75767:(t,e,r)=>{r.d(e,{x:()=>a});var n=r(79896),i=r(16928),s=r(73841);class a{#o;#u;#c;constructor(t,e){this.#o=t,this.#u=e}async getTemplates(t){if(this.#c??=this.#l(this.#u),t.toString()===this.#u.toString())return await this.#c;const[e,r]=await Promise.all([this.#c,this.#l(t)]);return e.forEach(((t,e)=>{r.has(e)||r.set(e,t)})),r}async#l(t){const e=await n.promises.readFile(i.normalize(`${this.#o}/${t.toString()}.json`),"utf8");return(0,s.T)(JSON.parse(e))}}},77372:(t,e,r)=>{r.d(e,{E3:()=>a,u5:()=>n});var n,i,s=r(35392);class a{#f;constructor(t){this.#f=t}static async load(t){const e=await s.promises.open(t,"r");try{const t=Buffer.alloc(2048),r=await e.read(t,0,t.length,0),n=new a(r.buffer);if(2048===r.bytesRead&&23117===n.dosHeader.magic)return n}finally{await e.close()}throw new Error("Not a PE image.")}get dosHeader(){return new o(this.#f)}get ntHeaders(){return new u(this.#f.slice(this.dosHeader.lfanew))}}class o{#f;constructor(t){this.#f=t}get magic(){return this.#f.readUInt16LE(0)}get lfanew(){return this.#f.readUInt32LE(60)}get stub(){return this.#f.slice(64,this.lfanew-64)}}class u{#f;constructor(t){this.#f=t}get signature(){return this.#f.readUInt32LE(0)}get fileHeader(){return new c(this.#f.slice(4,24))}get optionalHeader(){return new l(this.#f.slice(24,24+this.fileHeader.sizeOfOptionalHeader))}get sectionHeaders(){return new f(this.#f.slice(24+this.fileHeader.sizeOfOptionalHeader),this.fileHeader.numberOfSections)}async getResourceDirectory(t){const e=this.optionalHeader.directories[2].rva;if(0===e)return null;const r=this.sectionHeaders.all().find((t=>t.virtualAddress===e));if(!r)return null;const n=await s.promises.open(t,"r");try{const t=Buffer.alloc(r.sizeOfRawData),e=await n.read(t,0,t.length,r.pointerToRawData);return e.bytesRead!==r.sizeOfRawData?null:new h(e.buffer,r.virtualAddress)}finally{await n.close()}}}!function(t){t[t.I386=332]="I386",t[t.IA64=512]="IA64",t[t.AMD64=34404]="AMD64"}(n||(n={})),function(t){t[t.RELOCS_STRIPPED=1]="RELOCS_STRIPPED",t[t.EXECUTABLE_IMAGE=2]="EXECUTABLE_IMAGE",t[t.LINE_NUMS_STRIPPED=4]="LINE_NUMS_STRIPPED",t[t.IMAGE_FILE_LOCAL_SYMS_STRIPPED=8]="IMAGE_FILE_LOCAL_SYMS_STRIPPED",t[t.AGGRESIVE_WS_TRIM=16]="AGGRESIVE_WS_TRIM",t[t.LARGE_ADDRESS_AWARE=32]="LARGE_ADDRESS_AWARE",t[t.BYTES_REVERSED_LO=128]="BYTES_REVERSED_LO",t[t.X32BIT_MACHINE=256]="X32BIT_MACHINE",t[t.DEBUG_STRIPPED=512]="DEBUG_STRIPPED",t[t.REMOVABLE_RUN_FROM_SWAP=1024]="REMOVABLE_RUN_FROM_SWAP",t[t.NET_RUN_FROM_SWAP=2048]="NET_RUN_FROM_SWAP",t[t.SYSTEM=4096]="SYSTEM",t[t.DLL=8192]="DLL",t[t.UP_SYSTEM_ONLY=16384]="UP_SYSTEM_ONLY",t[t.BYTES_REVERSED_HI=32768]="BYTES_REVERSED_HI"}(i||(i={}));class c{#f;constructor(t){this.#f=t}get machine(){return this.#f.readUInt16LE(0)}get numberOfSections(){return this.#f.readUInt16LE(2)}get timestamp(){return this.#f.readUInt32LE(4)}get pointerToSymbolTable(){return this.#f.readUInt32LE(8)}get numberOfSymbols(){return this.#f.readUInt32LE(12)}get sizeOfOptionalHeader(){return this.#f.readUInt16LE(16)}get characteristics(){return this.#f.readUInt16LE(18)}}class l{#f;constructor(t){this.#f=t}get magic(){return this.#f.readUInt16LE(0)}get majorLinkerVersion(){return this.#f.readUInt8(2)}get minorLinkerVersion(){return this.#f.readUInt8(3)}get linkerVersion(){return`${this.majorLinkerVersion}.${this.minorLinkerVersion}`}get directories(){const t=[];for(let e=0;e<16;e++)t[e]=this.directoryInfo(e);return t}directoryInfo(t){const e=this.#h(t);return{rva:this.#f.readUInt32LE(e),size:this.#f.readUInt32LE(e+4)}}#h(t){return(267===this.magic?96:112)+8*t}}class f{#f;#d;constructor(t,e){this.#f=t,this.#d=e}find(t){for(let e=0;e<this.#d;e++){const r=40*e;let n=this.#f.toString("ascii",r,r+7);if(n=n.substring(0,n.indexOf("\0")),n===t)return this.#b(e)}return null}all(){const t=[];for(let e=0;e<this.#d;e++)t.push(this.#b(e));return t}#b(t){const e=40*t,r=this.#f.toString("ascii",e,e+7);return{name:r.substring(0,r.indexOf("\0")),virtualSize:this.#f.readUInt32LE(e+8),virtualAddress:this.#f.readUInt32LE(e+12),sizeOfRawData:this.#f.readUInt32LE(e+16),pointerToRawData:this.#f.readUInt32LE(e+20),pointerToRelocations:this.#f.readUInt32LE(e+24),pointerToLineNumbers:this.#f.readUInt32LE(e+28),numberOfRelocations:this.#f.readUInt16LE(e+32),numberOfLineNumbers:this.#f.readUInt16LE(e+34),characteristics:this.#f.readUInt32LE(e+36)}}}class h{#f;#g;#m;constructor(t,e,r=0){this.#f=t,this.#g=e,this.#m=r}get characteristics(){return this.#f.readUInt32LE(this.#m+0)}get timestamp(){return this.#f.readUInt32LE(this.#m+4)}get resourceMajorVersion(){return this.#f.readUInt16LE(this.#m+8)}get resourceMinorVersion(){return this.#f.readUInt16LE(this.#m+10)}get resourceVersionString(){return`${this.resourceMajorVersion}.${this.resourceMinorVersion}`}get numberOfNamedEntries(){return this.#f.readUInt16LE(this.#m+12)}get numberOfIdEntries(){return this.#f.readUInt16LE(this.#m+14)}get numberOfEntries(){return this.numberOfNamedEntries+this.numberOfIdEntries}getVersionInfo(){const t=this.entries().find((t=>16===t.id&&t.isSubdirectory));if(!t)return null;let e=t.subdirectory?.entries();if(!e||0===e.length||!e[0].isSubdirectory||!e[0].subdirectory)return null;if(e=e[0].subdirectory.entries(),0===e.length||!e[0].isData)return null;const r=e[0].data;if(!r)return null;const n=r.rva-this.#g,i=this.#f.slice(n,n+r.size);return new b(i)}entries(){const t=this.numberOfEntries,e=[];for(let r=0;r<t;r++)e.push(new d(this.#f,this.#g,this.#m+16+8*r));return e}}class d{#f;#g;#m;constructor(t,e,r){this.#f=t,this.#g=e,this.#m=r}get name(){let t=this.#f.readInt32LE(this.#m);if(t>=0)return null;t&=2147483647;const e=this.#f.readUInt16LE(t);return this.#f.toString("utf16le",t+2,t+2+e)}get id(){const t=this.#f.readInt32LE(this.#m);return t>=0?t:null}get hasName(){return null===this.id}get location(){return 2147483647&this.#f.readUInt32LE(this.#m+4)}get isSubdirectory(){return this.#f.readInt32LE(this.#m+4)<0}get isData(){return!this.isSubdirectory}get subdirectory(){return this.isSubdirectory?new h(this.#f,this.#g,this.location):null}get data(){if(!this.isData)return null;const t=this.location;return{rva:this.#f.readUInt32LE(t),size:this.#f.readUInt32LE(t+4),codePage:this.#f.readUInt32LE(t+8),reserved:this.#f.readUInt32LE(t+12)}}}class b{#f;#m;constructor(t){this.#f=t;let e=38;const r=e+32;for(;e<=r&&4277077181!==t.readUInt32LE(e);)e++;if(e>r)throw new Error("Invalid VS_VERSION_INFO structure.");this.#m=e}#p(t){return[this.#f.readUInt16LE(t+2),this.#f.readUInt16LE(t),this.#f.readUInt16LE(t+6),this.#f.readUInt16LE(t+4)].join(".")}get fileVersion(){return this.#p(this.#m+8)}get productVersion(){return this.#p(this.#m+16)}}},86824:(t,e,r)=>{r.d(e,{H:()=>i,V:()=>s});var n=r(76982);function i(t,e){return s(60*t,60*e)}function s(t,e){return n.randomInt(1e3*t,1e3*e)}},90211:(t,e,r)=>{r.d(e,{R:()=>i});const n=/^(\d+)\.(\d+)\.(\d+)\.(\d+)$/;class i{constructor(t,e,r,n){this.major=t,this.minor=e,this.build=r,this.revision=n}static parse(t){const e=n.exec(t);return e?new this(Number(e[1]),Number(e[2]),Number(e[3]),Number(e[4])):null}compare(t){return this.major-t.major||this.minor-t.minor||this.build-t.build||this.revision-t.revision}eq(t){return 0===this.compare(t)}gt(t){return this.compare(t)>0}gte(t){return this.compare(t)>=0}lt(t){return this.compare(t)<0}lte(t){return this.compare(t)<=0}}}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1782],{17019:(e,t,n)=>{n.d(t,{Q:()=>ye});var i=n(43964),o=n(15215),r=n(69544),s=function(e,t,n){return void 0===t&&(t=""),void 0===n&&(n=10),[r.r,t,e.substring(0,n)].filter(Boolean).join("_")},u=n(24900),a=function(){function e(){}return e.prototype.getApplicationContext=function(){return{versionName:this.versionName,language:c(),platform:"Web",os:void 0,deviceModel:void 0}},e}(),c=function(){return"undefined"!=typeof navigator&&(navigator.languages&&navigator.languages[0]||navigator.language)||""},d=function(){function e(){this.queue=[]}return e.prototype.logEvent=function(e){this.receiver?this.receiver(e):this.queue.length<512&&this.queue.push(e)},e.prototype.setEventReceiver=function(e){this.receiver=e,this.queue.length>0&&(this.queue.forEach((function(t){e(t)})),this.queue=[])},e}(),l=function(){return l=Object.assign||function(e){for(var t,n=1,i=arguments.length;n<i;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},l.apply(this,arguments)},v=function(e,t){var n=typeof e;if(n!==typeof t)return!1;for(var i=0,o=["string","number","boolean","undefined"];i<o.length;i++)if(o[i]===n)return e===t;if(null==e&&null==t)return!0;if(null==e||null==t)return!1;if(e.length!==t.length)return!1;var r=Array.isArray(e),s=Array.isArray(t);if(r!==s)return!1;if(!r||!s){var u=Object.keys(e).sort(),a=Object.keys(t).sort();if(!v(u,a))return!1;var c=!0;return Object.keys(e).forEach((function(n){v(e[n],t[n])||(c=!1)})),c}for(var d=0;d<e.length;d++)if(!v(e[d],t[d]))return!1;return!0};Object.entries||(Object.entries=function(e){for(var t=Object.keys(e),n=t.length,i=new Array(n);n--;)i[n]=[t[n],e[t[n]]];return i});var f=function(){function e(){this.identity={userProperties:{}},this.listeners=new Set}return e.prototype.editIdentity=function(){var e=this,t=l({},this.identity.userProperties),n=l(l({},this.identity),{userProperties:t});return{setUserId:function(e){return n.userId=e,this},setDeviceId:function(e){return n.deviceId=e,this},setUserProperties:function(e){return n.userProperties=e,this},setOptOut:function(e){return n.optOut=e,this},updateUserProperties:function(e){for(var t=n.userProperties||{},i=0,o=Object.entries(e);i<o.length;i++){var r=o[i],s=r[0],u=r[1];switch(s){case"$set":for(var a=0,c=Object.entries(u);a<c.length;a++){var d=c[a],l=d[0],v=d[1];t[l]=v}break;case"$unset":for(var f=0,p=Object.keys(u);f<p.length;f++)delete t[l=p[f]];break;case"$clearAll":t={}}}return n.userProperties=t,this},commit:function(){return e.setIdentity(n),this}}},e.prototype.getIdentity=function(){return l({},this.identity)},e.prototype.setIdentity=function(e){var t=l({},this.identity);this.identity=l({},e),v(t,this.identity)||this.listeners.forEach((function(t){t(e)}))},e.prototype.addIdentityListener=function(e){this.listeners.add(e)},e.prototype.removeIdentityListener=function(e){this.listeners.delete(e)},e}(),p="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof global?global:self,h=function(){function e(){this.identityStore=new f,this.eventBridge=new d,this.applicationContextProvider=new a}return e.getInstance=function(t){return p.analyticsConnectorInstances||(p.analyticsConnectorInstances={}),p.analyticsConnectorInstances[t]||(p.analyticsConnectorInstances[t]=new e),p.analyticsConnectorInstances[t]},e}(),g=function(e){return void 0===e&&(e="$default_instance"),h.getInstance(e)},m=n(29864),y=function(){function e(){this.name="identity",this.type=m.Q.BEFORE,this.identityStore=g().identityStore}return e.prototype.execute=function(e){return(0,o.sH)(this,void 0,void 0,(function(){var t;return(0,o.YH)(this,(function(n){return(t=e.user_properties)&&this.identityStore.editIdentity().updateUserProperties(t).commit(),[2,e]}))}))},e.prototype.setup=function(e){return(0,o.sH)(this,void 0,void 0,(function(){return(0,o.YH)(this,(function(t){return e.instanceName&&(this.identityStore=g(e.instanceName).identityStore),[2]}))}))},e}(),I=n(2511),b=n(89043),w=n(85168),k=n(31145),_=n(36325),S=n(74156),E=n(78440),O=n(43542),T=n(54734),H=n(6856),C=n(45860),P=n(67395),A=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,o.C6)(t,e),t.prototype.send=function(e,t){return(0,o.sH)(this,void 0,void 0,(function(){var n,i,r;return(0,o.YH)(this,(function(o){switch(o.label){case 0:if("undefined"==typeof fetch)throw new Error("FetchTransport is not supported");return n={headers:{"Content-Type":"application/json",Accept:"*/*"},body:JSON.stringify(t),method:"POST"},[4,fetch(e,n)];case 1:return[4,(i=o.sent()).text()];case 2:r=o.sent();try{return[2,this.buildResponse(JSON.parse(r))]}catch(e){return[2,this.buildResponse({code:i.status})]}return[2]}}))}))},t}(P.j),q=n(73579),x=function(){function e(e){this.options=(0,o.Cl)({},e)}return e.prototype.isEnabled=function(){return(0,o.sH)(this,void 0,void 0,(function(){var t,n;return(0,o.YH)(this,(function(i){switch(i.label){case 0:if(!(0,q.m)())return[2,!1];e.testValue=String(Date.now()),t=new e(this.options),n="AMP_TEST",i.label=1;case 1:return i.trys.push([1,4,5,7]),[4,t.set(n,e.testValue)];case 2:return i.sent(),[4,t.get(n)];case 3:return[2,i.sent()===e.testValue];case 4:return i.sent(),[2,!1];case 5:return[4,t.remove(n)];case 6:return i.sent(),[7];case 7:return[2]}}))}))},e.prototype.get=function(e){return(0,o.sH)(this,void 0,void 0,(function(){var t;return(0,o.YH)(this,(function(n){switch(n.label){case 0:return[4,this.getRaw(e)];case 1:if(!(t=n.sent()))return[2,void 0];try{try{t=decodeURIComponent(atob(t))}catch(e){}return[2,JSON.parse(t)]}catch(e){return[2,void 0]}return[2]}}))}))},e.prototype.getRaw=function(e){var t;return(0,o.sH)(this,void 0,void 0,(function(){var n,i,r;return(0,o.YH)(this,(function(o){return n=(0,q.m)(),i=null!==(t=null==n?void 0:n.document.cookie.split("; "))&&void 0!==t?t:[],(r=i.find((function(t){return 0===t.indexOf(e+"=")})))?[2,r.substring(e.length+1)]:[2,void 0]}))}))},e.prototype.set=function(e,t){var n;return(0,o.sH)(this,void 0,void 0,(function(){var i,r,s,u,a,c;return(0,o.YH)(this,(function(o){try{i=null!==(n=this.options.expirationDays)&&void 0!==n?n:0,s=void 0,(r=null!==t?i:-1)&&((u=new Date).setTime(u.getTime()+24*r*60*60*1e3),s=u),a="".concat(e,"=").concat(btoa(encodeURIComponent(JSON.stringify(t)))),s&&(a+="; expires=".concat(s.toUTCString())),a+="; path=/",this.options.domain&&(a+="; domain=".concat(this.options.domain)),this.options.secure&&(a+="; Secure"),this.options.sameSite&&(a+="; SameSite=".concat(this.options.sameSite)),(c=(0,q.m)())&&(c.document.cookie=a)}catch(e){}return[2]}))}))},e.prototype.remove=function(e){return(0,o.sH)(this,void 0,void 0,(function(){return(0,o.YH)(this,(function(t){switch(t.label){case 0:return[4,this.set(e,null)];case 1:return t.sent(),[2]}}))}))},e.prototype.reset=function(){return(0,o.sH)(this,void 0,void 0,(function(){return(0,o.YH)(this,(function(e){return[2]}))}))},e}(),R=1e3,N=function(){function e(e){this.loggerProvider=null==e?void 0:e.loggerProvider}return e.prototype.isEnabled=function(){return(0,o.sH)(this,void 0,void 0,(function(){var t,n,i;return(0,o.YH)(this,(function(o){switch(o.label){case 0:if(!(0,q.m)())return[2,!1];t=String(Date.now()),n=new e,i="AMP_TEST",o.label=1;case 1:return o.trys.push([1,4,5,7]),[4,n.set(i,t)];case 2:return o.sent(),[4,n.get(i)];case 3:return[2,o.sent()===t];case 4:return o.sent(),[2,!1];case 5:return[4,n.remove(i)];case 6:return o.sent(),[7];case 7:return[2]}}))}))},e.prototype.get=function(e){return(0,o.sH)(this,void 0,void 0,(function(){var t;return(0,o.YH)(this,(function(n){switch(n.label){case 0:return n.trys.push([0,2,,3]),[4,this.getRaw(e)];case 1:return(t=n.sent())?[2,JSON.parse(t)]:[2,void 0];case 2:return n.sent(),[2,void 0];case 3:return[2]}}))}))},e.prototype.getRaw=function(e){var t;return(0,o.sH)(this,void 0,void 0,(function(){return(0,o.YH)(this,(function(n){return[2,(null===(t=(0,q.m)())||void 0===t?void 0:t.localStorage.getItem(e))||void 0]}))}))},e.prototype.set=function(e,t){var n,i;return(0,o.sH)(this,void 0,void 0,(function(){var r,s,u;return(0,o.YH)(this,(function(o){r=Array.isArray(t)&&t.length>R;try{s=r?JSON.stringify(t.slice(0,R)):JSON.stringify(t),null===(n=(0,q.m)())||void 0===n||n.localStorage.setItem(e,s)}catch(e){}return r&&(u=t.length-R,null===(i=this.loggerProvider)||void 0===i||i.error("Failed to save ".concat(u," events because the queue length exceeded ").concat(R,"."))),[2]}))}))},e.prototype.remove=function(e){var t;return(0,o.sH)(this,void 0,void 0,(function(){return(0,o.YH)(this,(function(n){try{null===(t=(0,q.m)())||void 0===t||t.localStorage.removeItem(e)}catch(e){}return[2]}))}))},e.prototype.reset=function(){var e;return(0,o.sH)(this,void 0,void 0,(function(){return(0,o.YH)(this,(function(t){try{null===(e=(0,q.m)())||void 0===e||e.localStorage.clear()}catch(e){}return[2]}))}))},e}(),Y=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.state={done:4},t}return(0,o.C6)(t,e),t.prototype.send=function(e,t){return(0,o.sH)(this,void 0,void 0,(function(){var n=this;return(0,o.YH)(this,(function(i){return[2,new Promise((function(i,o){"undefined"==typeof XMLHttpRequest&&o(new Error("XHRTransport is not supported."));var r=new XMLHttpRequest;r.open("POST",e,!0),r.onreadystatechange=function(){if(r.readyState===n.state.done){var e=r.responseText;try{i(n.buildResponse(JSON.parse(e)))}catch(e){i(n.buildResponse({code:r.status}))}}},r.setRequestHeader("Content-Type","application/json"),r.setRequestHeader("Accept","*/*"),r.send(JSON.stringify(t))}))]}))}))},t}(P.j),D=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,o.C6)(t,e),t.prototype.send=function(e,t){return(0,o.sH)(this,void 0,void 0,(function(){var n=this;return(0,o.YH)(this,(function(i){return[2,new Promise((function(i,o){var r=(0,q.m)();if(!(null==r?void 0:r.navigator.sendBeacon))throw new Error("SendBeaconTransport is not supported");try{var s=JSON.stringify(t);return i(r.navigator.sendBeacon(e,JSON.stringify(t))?n.buildResponse({code:200,events_ingested:t.events.length,payload_size_bytes:s.length,server_upload_time:Date.now()}):n.buildResponse({code:500}))}catch(e){o(e)}}))]}))}))},t}(P.j),U=function(){return{cookieExpiration:365,cookieSameSite:"Lax",cookieSecure:!1,cookieStorage:new H.X,cookieUpgrade:!0,disableCookies:!1,domain:"",sessionTimeout:18e5,trackingOptions:{deviceManufacturer:!0,deviceModel:!0,ipAddress:!0,language:!0,osName:!0,osVersion:!0,platform:!0},transportProvider:new A}},j=function(e){function t(t,n){var i,r,s,u,a,c,d,l,v,f=this,p=U();return(f=e.call(this,(0,o.Cl)((0,o.Cl)({flushIntervalMillis:1e3,flushMaxRetries:5,flushQueueSize:30,transportProvider:p.transportProvider},n),{apiKey:t}))||this)._optOut=!1,f.cookieStorage=null!==(i=null==n?void 0:n.cookieStorage)&&void 0!==i?i:p.cookieStorage,f.deviceId=null==n?void 0:n.deviceId,f.lastEventId=null==n?void 0:n.lastEventId,f.lastEventTime=null==n?void 0:n.lastEventTime,f.optOut=Boolean(null==n?void 0:n.optOut),f.sessionId=null==n?void 0:n.sessionId,f.userId=null==n?void 0:n.userId,f.appVersion=null==n?void 0:n.appVersion,f.attribution=null==n?void 0:n.attribution,f.cookieExpiration=null!==(r=null==n?void 0:n.cookieExpiration)&&void 0!==r?r:p.cookieExpiration,f.cookieSameSite=null!==(s=null==n?void 0:n.cookieSameSite)&&void 0!==s?s:p.cookieSameSite,f.cookieSecure=null!==(u=null==n?void 0:n.cookieSecure)&&void 0!==u?u:p.cookieSecure,f.cookieUpgrade=null!==(a=null==n?void 0:n.cookieUpgrade)&&void 0!==a?a:p.cookieUpgrade,f.defaultTracking=null==n?void 0:n.defaultTracking,f.disableCookies=null!==(c=null==n?void 0:n.disableCookies)&&void 0!==c?c:p.disableCookies,f.defaultTracking=null==n?void 0:n.defaultTracking,f.domain=null!==(d=null==n?void 0:n.domain)&&void 0!==d?d:p.domain,f.partnerId=null==n?void 0:n.partnerId,f.sessionTimeout=null!==(l=null==n?void 0:n.sessionTimeout)&&void 0!==l?l:p.sessionTimeout,f.trackingOptions=null!==(v=null==n?void 0:n.trackingOptions)&&void 0!==v?v:p.trackingOptions,f}return(0,o.C6)(t,e),Object.defineProperty(t.prototype,"deviceId",{get:function(){return this._deviceId},set:function(e){this._deviceId!==e&&(this._deviceId=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"userId",{get:function(){return this._userId},set:function(e){this._userId!==e&&(this._userId=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"sessionId",{get:function(){return this._sessionId},set:function(e){this._sessionId!==e&&(this._sessionId=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"optOut",{get:function(){return this._optOut},set:function(e){this._optOut!==e&&(this._optOut=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastEventTime",{get:function(){return this._lastEventTime},set:function(e){this._lastEventTime!==e&&(this._lastEventTime=e,this.updateStorage())},enumerable:!1,configurable:!0}),Object.defineProperty(t.prototype,"lastEventId",{get:function(){return this._lastEventId},set:function(e){this._lastEventId!==e&&(this._lastEventId=e,this.updateStorage())},enumerable:!1,configurable:!0}),t.prototype.updateStorage=function(){var e,t={deviceId:this._deviceId,userId:this._userId,sessionId:this._sessionId,optOut:this._optOut,lastEventTime:this._lastEventTime,lastEventId:this._lastEventId};null===(e=this.cookieStorage)||void 0===e||e.set(s(this.apiKey),t)},t}(C.TS),V=function(e,t){return(0,o.sH)(void 0,void 0,void 0,(function(){var n,i,r,s,u,a,c,d,l,v,f,p,h,g,m;return(0,o.YH)(this,(function(y){switch(y.label){case 0:return n=U(),i=null!==(g=null==t?void 0:t.deviceId)&&void 0!==g?g:(0,w.k)(),r=null==t?void 0:t.lastEventId,s=null==t?void 0:t.lastEventTime,u=null==t?void 0:t.optOut,a=null==t?void 0:t.sessionId,c=null==t?void 0:t.userId,d=null==t?void 0:t.cookieStorage,l=null==t?void 0:t.domain,v=j.bind,f=[void 0,e],p=[(0,o.Cl)({},t)],h={cookieStorage:d,deviceId:i,domain:l,lastEventId:r,lastEventTime:s,optOut:u,sessionId:a},[4,L(t)];case 1:return[2,new(v.apply(j,f.concat([o.Cl.apply(void 0,p.concat([(h.storageProvider=y.sent(),h.trackingOptions=(0,o.Cl)((0,o.Cl)({},n.trackingOptions),null==t?void 0:t.trackingOptions),h.transportProvider=null!==(m=null==t?void 0:t.transportProvider)&&void 0!==m?m:F(null==t?void 0:t.transport),h.userId=c,h)]))])))]}}))}))},W=function(e,t){return void 0===t&&(t=U()),(0,o.sH)(void 0,void 0,void 0,(function(){var n,i,r;return(0,o.YH)(this,(function(s){switch(s.label){case 0:return n=(0,o.Cl)((0,o.Cl)({},t),e),i=null==e?void 0:e.cookieStorage,(r=!i)?[3,2]:[4,i.isEnabled()];case 1:r=!s.sent(),s.label=2;case 2:return r?[2,M(n)]:[2,i]}}))}))},M=function(e){return(0,o.sH)(void 0,void 0,void 0,(function(){var t,n;return(0,o.YH)(this,(function(i){switch(i.label){case 0:return t=new x({domain:e.domain,expirationDays:e.cookieExpiration,sameSite:e.cookieSameSite,secure:e.cookieSecure}),(n=e.disableCookies)?[3,2]:[4,t.isEnabled()];case 1:n=!i.sent(),i.label=2;case 2:return n?[4,(t=new N).isEnabled()]:[3,4];case 3:i.sent()||(t=new H.X),i.label=4;case 4:return[2,t]}}))}))},L=function(e){return(0,o.sH)(void 0,void 0,void 0,(function(){var t,n,i,r,s,u,a,c,d;return(0,o.YH)(this,(function(l){switch(l.label){case 0:if(t=e&&Object.prototype.hasOwnProperty.call(e,"storageProvider"),n=e&&e.loggerProvider,t&&!e.storageProvider)return[3,9];l.label=1;case 1:l.trys.push([1,7,8,9]),i=(0,o.Ju)([null==e?void 0:e.storageProvider,new N({loggerProvider:n})]),r=i.next(),l.label=2;case 2:return r.done?[3,6]:(s=r.value,(u=s)?[4,s.isEnabled()]:[3,4]);case 3:u=l.sent(),l.label=4;case 4:if(u)return[2,s];l.label=5;case 5:return r=i.next(),[3,2];case 6:return[3,9];case 7:return a=l.sent(),c={error:a},[3,9];case 8:try{r&&!r.done&&(d=i.return)&&d.call(i)}finally{if(c)throw c.error}return[7];case 9:return[2,void 0]}}))}))},F=function(e){return e===T.Y.XHR?new Y:e===T.Y.SendBeacon?new D:U().transportProvider},K="[Amplitude]",B="".concat(K," Page Viewed"),J="".concat(K," Form Started"),z="".concat(K," Form Submitted"),Q="".concat(K," File Downloaded"),X="session_start",G="session_end",$="".concat(K," File Extension"),Z="".concat(K," File Name"),ee="".concat(K," Link ID"),te="".concat(K," Link Text"),ne="".concat(K," Link URL"),ie="".concat(K," Form ID"),oe="".concat(K," Form Name"),re="".concat(K," Form Destination"),se=function(e,t){return(0,o.sH)(void 0,void 0,void 0,(function(){var n,i,s,u,a,c,d,l,v,f,p;return(0,o.YH)(this,(function(h){switch(h.label){case 0:return[4,W(t)];case 1:return n=h.sent(),i=function(e){return"".concat(r.r.toLowerCase(),"_").concat(e.substring(0,6))}(e),[4,n.getRaw(i)];case 2:return(s=h.sent())?(null!==(p=t.cookieUpgrade)&&void 0!==p?p:U().cookieUpgrade)?[4,n.remove(i)]:[3,4]:[2,{optOut:!1}];case 3:h.sent(),h.label=4;case 4:return u=(0,o.zs)(s.split("."),6),a=u[0],c=u[1],d=u[2],l=u[3],v=u[4],f=u[5],[2,{deviceId:a,userId:ae(c),sessionId:ue(l),lastEventId:ue(f),lastEventTime:ue(v),optOut:Boolean(d)}]}}))}))},ue=function(e){var t=parseInt(e,32);if(!isNaN(t))return t},ae=function(e){if(atob&&escape&&e)try{return decodeURIComponent(escape(atob(e)))}catch(e){return}},ce=n(26480),de=n.n(ce),le=function(){var e,t,n,i;if("undefined"==typeof navigator)return"";var o=navigator.userLanguage;return null!==(i=null!==(n=null!==(t=null===(e=navigator.languages)||void 0===e?void 0:e[0])&&void 0!==t?t:navigator.language)&&void 0!==n?n:o)&&void 0!==i?i:""},ve=function(){function e(){this.name="context",this.type=m.Q.BEFORE,this.library="amplitude-ts/".concat("1.13.6"),"undefined"!=typeof navigator&&(this.userAgent=navigator.userAgent),this.uaResult=new(de())(this.userAgent).getResult()}return e.prototype.setup=function(e){return this.config=e,Promise.resolve(void 0)},e.prototype.execute=function(e){var t,n;return(0,o.sH)(this,void 0,void 0,(function(){var i,r,s,u,a,c,d;return(0,o.YH)(this,(function(l){return i=(new Date).getTime(),r=this.uaResult.browser.name,s=this.uaResult.browser.version,u=this.uaResult.device.model||this.uaResult.os.name,a=this.uaResult.device.vendor,c=null!==(t=this.config.lastEventId)&&void 0!==t?t:-1,d=null!==(n=e.event_id)&&void 0!==n?n:c+1,this.config.lastEventId=d,e.time||(this.config.lastEventTime=i),[2,(0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)((0,o.Cl)({user_id:this.config.userId,device_id:this.config.deviceId,session_id:this.config.sessionId,time:i},this.config.appVersion&&{app_version:this.config.appVersion}),this.config.trackingOptions.platform&&{platform:"Web"}),this.config.trackingOptions.osName&&{os_name:r}),this.config.trackingOptions.osVersion&&{os_version:s}),this.config.trackingOptions.deviceManufacturer&&{device_manufacturer:a}),this.config.trackingOptions.deviceModel&&{device_model:u}),this.config.trackingOptions.language&&{language:le()}),this.config.trackingOptions.ipAddress&&{ip:"$remote"}),{insert_id:(0,w.k)(),partner_id:this.config.partnerId,plan:this.config.plan}),this.config.ingestionMetadata&&{ingestion_metadata:{source_name:this.config.ingestionMetadata.sourceName,source_version:this.config.ingestionMetadata.sourceVersion}}),e),{event_id:d,library:this.library,user_agent:this.userAgent})]}))}))},e}(),fe={page_domain:"".concat(K," Page Domain"),page_location:"".concat(K," Page Location"),page_path:"".concat(K," Page Path"),page_title:"".concat(K," Page Title"),page_url:"".concat(K," Page URL")},pe=function(){var e,t=[],n=function(e,n,i){e.addEventListener(n,i),t.push({element:e,type:n,handler:i})};return{name:"@amplitude/plugin-form-interaction-tracking-browser",type:m.Q.ENRICHMENT,setup:function(t,i){return(0,o.sH)(void 0,void 0,void 0,(function(){var r;return(0,o.YH)(this,(function(o){return i?("undefined"==typeof document||(r=function(e){var t=!1;n(e,"change",(function(){var n;t||i.track(J,((n={})[ie]=e.id,n[oe]=e.name,n[re]=e.action,n)),t=!0})),n(e,"submit",(function(){var n,o;t||i.track(J,((n={})[ie]=e.id,n[oe]=e.name,n[re]=e.action,n)),i.track(z,((o={})[ie]=e.id,o[oe]=e.name,o[re]=e.action,o)),t=!1}))},Array.from(document.getElementsByTagName("form")).forEach(r),"undefined"!=typeof MutationObserver&&(e=new MutationObserver((function(e){e.forEach((function(e){e.addedNodes.forEach((function(e){"FORM"===e.nodeName&&r(e),"querySelectorAll"in e&&"function"==typeof e.querySelectorAll&&Array.from(e.querySelectorAll("form")).map(r)}))}))}))).observe(document.body,{subtree:!0,childList:!0})),[2]):(t.loggerProvider.warn("Form interaction tracking requires a later version of @amplitude/analytics-browser. Form interaction events are not tracked."),[2])}))}))},execute:function(e){return(0,o.sH)(void 0,void 0,void 0,(function(){return(0,o.YH)(this,(function(t){return[2,e]}))}))},teardown:function(){return(0,o.sH)(void 0,void 0,void 0,(function(){return(0,o.YH)(this,(function(n){return null==e||e.disconnect(),t.forEach((function(e){var t=e.element,n=e.type,i=e.handler;null==t||t.removeEventListener(n,i)})),t=[],[2]}))}))}}},he=function(e,t){for(var n=0;n<t.length;n++){var i=t[n],o=i.name,r=i.args,s=i.resolve,u=e&&e[o];if("function"==typeof u){var a=u.apply(e,r);"function"==typeof s&&s(null==a?void 0:a.promise)}}return e},ge=function(e){return e&&void 0!==e._q},me=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return(0,o.C6)(t,e),t.prototype.init=function(e,t,n){return void 0===e&&(e=""),(0,I.m)(this._init((0,o.Cl)((0,o.Cl)({},n),{userId:t,apiKey:e})))},t.prototype._init=function(t){var n,i,r,a,c,d,l,v,f,p,h,I,w,k,_,S,T,H,C,P;return(0,o.sH)(this,void 0,void 0,(function(){var A,q,R,N,Y,D,U,j,M,L,F,K,J,z,X,G,ie,oe,re=this;return(0,o.YH)(this,(function(ue){switch(ue.label){case 0:return this.initializing?[2]:(this.initializing=!0,A=t,t.disableCookies?(q="",[3,5]):[3,1]);case 1:return null===(n=t.domain)||void 0===n?[3,2]:(R=n,[3,4]);case 2:return[4,(0,o.sH)(void 0,void 0,void 0,(function(){var e,t,n,i,r,s,u;return(0,o.YH)(this,(function(o){switch(o.label){case 0:return[4,(new x).isEnabled()];case 1:if(!o.sent()||"undefined"==typeof location||!location.hostname)return[2,""];for(e=location.hostname,t=e.split("."),n=[],i="AMP_TLDTEST",r=t.length-2;r>=0;--r)n.push(t.slice(r).join("."));r=0,o.label=2;case 2:return r<n.length?(s=n[r],[4,(u=new x({domain:"."+s})).set(i,1)]):[3,7];case 3:return o.sent(),[4,u.get(i)];case 4:return o.sent()?[4,u.remove(i)]:[3,6];case 5:return o.sent(),[2,"."+s];case 6:return r++,[3,2];case 7:return[2,""]}}))}))];case 3:R=ue.sent(),ue.label=4;case 4:q=R,ue.label=5;case 5:return A.domain=q,[4,se(t.apiKey,t)];case 6:return N=ue.sent(),[4,W(t)];case 7:return[4,(Y=ue.sent()).get(s(t.apiKey))];case 8:return D=ue.sent(),U=(0,u.v)(),j=null!==(a=null!==(r=null!==(i=t.deviceId)&&void 0!==i?i:U.deviceId)&&void 0!==r?r:null==D?void 0:D.deviceId)&&void 0!==a?a:N.deviceId,M=null!==(c=null==D?void 0:D.sessionId)&&void 0!==c?c:N.sessionId,L=null!==(l=null!==(d=t.optOut)&&void 0!==d?d:null==D?void 0:D.optOut)&&void 0!==l?l:N.optOut,F=null!==(v=null==D?void 0:D.lastEventId)&&void 0!==v?v:N.lastEventId,K=null!==(f=null==D?void 0:D.lastEventTime)&&void 0!==f?f:N.lastEventTime,J=null!==(h=null!==(p=t.userId)&&void 0!==p?p:null==D?void 0:D.userId)&&void 0!==h?h:N.userId,this.previousSessionDeviceId=null!==(I=null==D?void 0:D.deviceId)&&void 0!==I?I:N.deviceId,this.previousSessionUserId=null!==(w=null==D?void 0:D.userId)&&void 0!==w?w:N.userId,[4,V(t.apiKey,(0,o.Cl)((0,o.Cl)({},t),{deviceId:j,sessionId:M,optOut:L,lastEventId:F,lastEventTime:K,userId:J,cookieStorage:Y}))];case 9:return z=ue.sent(),[4,e.prototype._init.call(this,z)];case 10:return ue.sent(),X=!1,(!this.config.lastEventTime||!this.config.sessionId||this.config.lastEventTime&&Date.now()-this.config.lastEventTime>this.config.sessionTimeout)&&(this.setSessionId(null!==(_=null!==(k=t.sessionId)&&void 0!==k?k:this.config.sessionId)&&void 0!==_?_:Date.now()),X=!0),(G=g(t.instanceName)).identityStore.setIdentity({userId:this.config.userId,deviceId:this.config.deviceId}),[4,this.add(new b.D).promise];case 11:return ue.sent(),[4,this.add(new ve).promise];case 12:return ue.sent(),[4,this.add(new y).promise];case 13:return ue.sent(),("boolean"==typeof(de=this.config.defaultTracking)?de:null==de?void 0:de.fileDownloads)?[4,this.add((ce=[],{name:"@amplitude/plugin-file-download-tracking-browser",type:m.Q.ENRICHMENT,setup:function(e,t){return(0,o.sH)(void 0,void 0,void 0,(function(){var n,i;return(0,o.YH)(this,(function(o){return t?("undefined"==typeof document||(n=function(e){var n;try{n=new URL(e.href,window.location.href)}catch(e){return}var o,r,s,u=i.exec(n.href),a=null==u?void 0:u[1];a&&(r="click",s=function(){var i;a&&t.track(Q,((i={})[$]=a,i[Z]=n.pathname,i[ee]=e.id,i[te]=e.text,i[ne]=e.href,i))},(o=e).addEventListener(r,s),ce.push({element:o,type:r,handler:s}))},i=/\.(pdf|xlsx?|docx?|txt|rtf|csv|exe|key|pp(s|t|tx)|7z|pkg|rar|gz|zip|avi|mov|mp4|mpe?g|wmv|midi?|mp3|wav|wma)$/,Array.from(document.getElementsByTagName("a")).forEach(n),"undefined"!=typeof MutationObserver&&(ae=new MutationObserver((function(e){e.forEach((function(e){e.addedNodes.forEach((function(e){"A"===e.nodeName&&n(e),"querySelectorAll"in e&&"function"==typeof e.querySelectorAll&&Array.from(e.querySelectorAll("a")).map(n)}))}))}))).observe(document.body,{subtree:!0,childList:!0})),[2]):(e.loggerProvider.warn("File download tracking requires a later version of @amplitude/analytics-browser. File download events are not tracked."),[2])}))}))},execute:function(e){return(0,o.sH)(void 0,void 0,void 0,(function(){return(0,o.YH)(this,(function(t){return[2,e]}))}))},teardown:function(){return(0,o.sH)(void 0,void 0,void 0,(function(){return(0,o.YH)(this,(function(e){return null==ae||ae.disconnect(),ce.forEach((function(e){var t=e.element,n=e.type,i=e.handler;null==t||t.removeEventListener(n,i)})),ce=[],[2]}))}))}})).promise]:[3,15];case 14:ue.sent(),ue.label=15;case 15:return function(e){return"boolean"==typeof e?e:!!(null==e?void 0:e.formInteractions)}(this.config.defaultTracking)?[4,this.add(pe()).promise]:[3,17];case 16:ue.sent(),ue.label=17;case 17:return(null===(S=this.config.attribution)||void 0===S?void 0:S.disabled)?[3,19]:((ie=(0,O.x)({excludeReferrers:null===(T=this.config.attribution)||void 0===T?void 0:T.excludeReferrers,initialEmptyValue:null===(H=this.config.attribution)||void 0===H?void 0:H.initialEmptyValue,resetSessionOnNewCampaign:null===(C=this.config.attribution)||void 0===C?void 0:C.resetSessionOnNewCampaign})).__pluginEnabledOverride=!(!X&&!(null===(P=this.config.attribution)||void 0===P?void 0:P.trackNewCampaigns))&&void 0,[4,this.add(ie).promise]);case 18:ue.sent(),ue.label=19;case 19:return oe=function(e){var t,n,i=(null===(t=e.attribution)||void 0===t?void 0:t.trackPageViews)?"attribution":function(){return!1},o=void 0,r="Page View";return("boolean"==typeof(n=e.defaultTracking)?n:!!(!0===(null==n?void 0:n.pageViews)||(null==n?void 0:n.pageViews)&&"object"==typeof n.pageViews))&&(i=void 0,r=void 0,e.defaultTracking&&"object"==typeof e.defaultTracking&&e.defaultTracking.pageViews&&"object"==typeof e.defaultTracking.pageViews&&("trackOn"in e.defaultTracking.pageViews&&(i=e.defaultTracking.pageViews.trackOn),"trackHistoryChanges"in e.defaultTracking.pageViews&&(o=e.defaultTracking.pageViews.trackHistoryChanges),"eventType"in e.defaultTracking.pageViews&&e.defaultTracking.pageViews.eventType&&(r=e.defaultTracking.pageViews.eventType))),{trackOn:i,trackHistoryChanges:o,eventType:r}}(this.config),oe.eventType=oe.eventType||B,[4,this.add((0,E.D)(oe)).promise];case 20:return ue.sent(),[4,this.add({name:"@amplitude/plugin-default-page-view-event-enrichment-browser",type:m.Q.ENRICHMENT,setup:function(){return(0,o.sH)(void 0,void 0,void 0,(function(){return(0,o.YH)(this,(function(e){return[2,void 0]}))}))},execute:function(e){return(0,o.sH)(void 0,void 0,void 0,(function(){return(0,o.YH)(this,(function(t){return e.event_type===B&&e.event_properties&&(e.event_properties=Object.entries(e.event_properties).reduce((function(e,t){var n=(0,o.zs)(t,2),i=n[0],r=n[1],s=fe[i];return s?e[s]=r:e[i]=r,e}),{})),[2,e]}))}))}}).promise];case 21:return ue.sent(),this.initializing=!1,[4,this.runQueuedFunctions("dispatchQ")];case 22:return ue.sent(),G.eventBridge.setEventReceiver((function(e){re.track(e.eventType,e.eventProperties)})),[2]}var ae,ce,de}))}))},t.prototype.getUserId=function(){var e;return null===(e=this.config)||void 0===e?void 0:e.userId},t.prototype.setUserId=function(e){this.config?e===this.config.userId&&void 0!==e||(this.config.userId=e,function(e,t){g(t).identityStore.editIdentity().setUserId(e).commit()}(e,this.config.instanceName)):this.q.push(this.setUserId.bind(this,e))},t.prototype.getDeviceId=function(){var e;return null===(e=this.config)||void 0===e?void 0:e.deviceId},t.prototype.setDeviceId=function(e){this.config?(this.config.deviceId=e,function(e,t){g(t).identityStore.editIdentity().setDeviceId(e).commit()}(e,this.config.instanceName)):this.q.push(this.setDeviceId.bind(this,e))},t.prototype.setOptOut=function(t){!function(e,t){g(t).identityStore.editIdentity().setOptOut(e).commit()}(t,this.config.instanceName),e.prototype.setOptOut.call(this,t)},t.prototype.reset=function(){this.setDeviceId((0,w.k)()),this.setUserId(void 0)},t.prototype.getSessionId=function(){var e;return null===(e=this.config)||void 0===e?void 0:e.sessionId},t.prototype.setSessionId=function(e){var t;if(this.config){if(e!==this.config.sessionId){var n,i=this.getSessionId(),o=this.config.lastEventTime,r=null!==(t=this.config.lastEventId)&&void 0!==t?t:-1;this.config.sessionId=e,this.config.lastEventTime=void 0,("boolean"==typeof(n=this.config.defaultTracking)?n:null==n?void 0:n.sessions)&&(i&&o&&this.track(G,void 0,{device_id:this.previousSessionDeviceId,event_id:++r,session_id:i,time:o+1,user_id:this.previousSessionUserId}),this.config.lastEventTime=this.config.sessionId,this.track(X,void 0,{event_id:++r,session_id:this.config.sessionId,time:this.config.lastEventTime})),this.previousSessionDeviceId=this.config.deviceId,this.previousSessionUserId=this.config.userId}}else this.q.push(this.setSessionId.bind(this,e))},t.prototype.extendSession=function(){this.config?this.config.lastEventTime=Date.now():this.q.push(this.extendSession.bind(this))},t.prototype.setTransport=function(e){this.config?this.config.transportProvider=F(e):this.q.push(this.setTransport.bind(this,e))},t.prototype.identify=function(t,n){if(ge(t)){var i=t._q;t._q=[],t=he(new k.T,i)}return(null==n?void 0:n.user_id)&&this.setUserId(n.user_id),(null==n?void 0:n.device_id)&&this.setDeviceId(n.device_id),e.prototype.identify.call(this,t,n)},t.prototype.groupIdentify=function(t,n,i,o){if(ge(i)){var r=i._q;i._q=[],i=he(new k.T,r)}return e.prototype.groupIdentify.call(this,t,n,i,o)},t.prototype.revenue=function(t,n){if(ge(t)){var i=t._q;t._q=[],t=he(new _.n,i)}return e.prototype.revenue.call(this,t,n)},t.prototype.process=function(t){return(0,o.sH)(this,void 0,void 0,(function(){var n,i,r;return(0,o.YH)(this,(function(o){return n=Date.now(),i=this.config.lastEventTime||Date.now(),r=n-i,t.event_type!==X&&t.event_type!==G&&(!t.session_id||t.session_id===this.getSessionId())&&r>this.config.sessionTimeout&&this.setSessionId(n),[2,e.prototype.process.call(this,t)]}))}))},t}(S.n),ye=function(){var e=new me;return{init:(0,i.nA)(e.init.bind(e),"init",(0,i.WI)(e),(0,i.av)(e,["config"])),add:(0,i.nA)(e.add.bind(e),"add",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.plugins"])),remove:(0,i.nA)(e.remove.bind(e),"remove",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.plugins"])),track:(0,i.nA)(e.track.bind(e),"track",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.queue.length"])),logEvent:(0,i.nA)(e.logEvent.bind(e),"logEvent",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.queue.length"])),identify:(0,i.nA)(e.identify.bind(e),"identify",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.queue.length"])),groupIdentify:(0,i.nA)(e.groupIdentify.bind(e),"groupIdentify",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.queue.length"])),setGroup:(0,i.nA)(e.setGroup.bind(e),"setGroup",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.queue.length"])),revenue:(0,i.nA)(e.revenue.bind(e),"revenue",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.queue.length"])),flush:(0,i.nA)(e.flush.bind(e),"flush",(0,i.WI)(e),(0,i.av)(e,["config.apiKey","timeline.queue.length"])),getUserId:(0,i.nA)(e.getUserId.bind(e),"getUserId",(0,i.WI)(e),(0,i.av)(e,["config","config.userId"])),setUserId:(0,i.nA)(e.setUserId.bind(e),"setUserId",(0,i.WI)(e),(0,i.av)(e,["config","config.userId"])),getDeviceId:(0,i.nA)(e.getDeviceId.bind(e),"getDeviceId",(0,i.WI)(e),(0,i.av)(e,["config","config.deviceId"])),setDeviceId:(0,i.nA)(e.setDeviceId.bind(e),"setDeviceId",(0,i.WI)(e),(0,i.av)(e,["config","config.deviceId"])),reset:(0,i.nA)(e.reset.bind(e),"reset",(0,i.WI)(e),(0,i.av)(e,["config","config.userId","config.deviceId"])),getSessionId:(0,i.nA)(e.getSessionId.bind(e),"getSessionId",(0,i.WI)(e),(0,i.av)(e,["config"])),setSessionId:(0,i.nA)(e.setSessionId.bind(e),"setSessionId",(0,i.WI)(e),(0,i.av)(e,["config"])),extendSession:(0,i.nA)(e.extendSession.bind(e),"extendSession",(0,i.WI)(e),(0,i.av)(e,["config"])),setOptOut:(0,i.nA)(e.setOptOut.bind(e),"setOptOut",(0,i.WI)(e),(0,i.av)(e,["config"])),setTransport:(0,i.nA)(e.setTransport.bind(e),"setTransport",(0,i.WI)(e),(0,i.av)(e,["config"]))}};ye()},24900:(e,t,n)=>{n.d(t,{v:()=>o});var i=n(73579),o=function(){var e,t=(0,i.m)();return(null===(e=null==t?void 0:t.location)||void 0===e?void 0:e.search)?t.location.search.substring(1).split("&").filter(Boolean).reduce((function(e,t){var n=t.split("=",2),i=r(n[0]),o=r(n[1]);return o?(e[i]=o,e):e}),{}):{}},r=function(e){void 0===e&&(e="");try{return decodeURIComponent(e)}catch(e){return""}}},46257:(e,t,n)=>{n.d(t,{E:()=>s});var i=n(15215),o=n(24900),r=n(70442),s=function(){function e(){}return e.prototype.parse=function(){return(0,i.sH)(this,void 0,void 0,(function(){return(0,i.YH)(this,(function(e){return[2,(0,i.Cl)((0,i.Cl)((0,i.Cl)((0,i.Cl)({},r.F3),this.getUtmParam()),this.getReferrer()),this.getClickIds())]}))}))},e.prototype.getUtmParam=function(){var e=(0,o.v)();return{utm_campaign:e[r._9],utm_content:e[r.fL],utm_id:e[r.j7],utm_medium:e[r.Jn],utm_source:e[r.VU],utm_term:e[r.Wx]}},e.prototype.getReferrer=function(){var e,t,n={referrer:void 0,referring_domain:void 0};try{n.referrer=document.referrer||void 0,n.referring_domain=null!==(t=null===(e=n.referrer)||void 0===e?void 0:e.split("/")[2])&&void 0!==t?t:void 0}catch(e){}return n},e.prototype.getClickIds=function(){var e,t=(0,o.v)();return(e={})[r.h2]=t[r.h2],e[r.lT]=t[r.lT],e[r.wX]=t[r.wX],e[r.q2]=t[r.q2],e[r.KY]=t[r.KY],e[r.Ct]=t[r.Ct],e[r.qr]=t[r.qr],e[r.wu]=t[r.wu],e[r.tt]=t[r.tt],e[r.Gp]=t[r.Gp],e[r.SO]=t[r.SO],e},e}()},70442:(e,t,n)=>{n.d(t,{Ct:()=>p,F3:()=>b,Gp:()=>y,Jn:()=>s,KY:()=>f,SO:()=>I,VU:()=>u,Wx:()=>a,_9:()=>i,fL:()=>o,h2:()=>c,j7:()=>r,lT:()=>d,q2:()=>v,qr:()=>h,tt:()=>m,wX:()=>l,wu:()=>g});var i="utm_campaign",o="utm_content",r="utm_id",s="utm_medium",u="utm_source",a="utm_term",c="dclid",d="fbclid",l="gbraid",v="gclid",f="ko_click_id",p="li_fat_id",h="msclkid",g="rtd_cid",m="ttclid",y="twclid",I="wbraid",b={utm_campaign:void 0,utm_content:void 0,utm_id:void 0,utm_medium:void 0,utm_source:void 0,utm_term:void 0,referrer:void 0,referring_domain:void 0,dclid:void 0,gbraid:void 0,gclid:void 0,fbclid:void 0,ko_click_id:void 0,li_fat_id:void 0,msclkid:void 0,rtd_cid:void 0,ttclid:void 0,twclid:void 0,wbraid:void 0}},73579:(e,t,n)=>{n.d(t,{m:()=>i});var i=function(){return"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0}}}]);
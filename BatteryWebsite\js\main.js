// 主JavaScript文件
document.addEventListener('DOMContentLoaded', function() {
    // 初始化AOS动画库
    AOS.init({
        duration: 1000,
        easing: 'ease-in-out',
        once: true,
        offset: 100
    });

    // 页面加载完成后隐藏加载动画
    window.addEventListener('load', function() {
        const loader = document.getElementById('loader');
        setTimeout(() => {
            loader.classList.add('hidden');
        }, 1500);
    });

    // 导航栏滚动效果
    const navbar = document.getElementById('navbar');
    const hamburger = document.getElementById('hamburger');
    const navMenu = document.getElementById('nav-menu');

    window.addEventListener('scroll', function() {
        if (window.scrollY > 100) {
            navbar.classList.add('scrolled');
        } else {
            navbar.classList.remove('scrolled');
        }
    });

    // 移动端菜单切换
    hamburger.addEventListener('click', function() {
        hamburger.classList.toggle('active');
        navMenu.classList.toggle('active');
    });

    // 导航链接点击事件
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                targetSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }

            // 关闭移动端菜单
            hamburger.classList.remove('active');
            navMenu.classList.remove('active');

            // 更新活动链接
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });

    // 滚动指示器点击事件
    const scrollIndicator = document.querySelector('.scroll-indicator');
    if (scrollIndicator) {
        scrollIndicator.addEventListener('click', function() {
            const productsSection = document.getElementById('products');
            if (productsSection) {
                productsSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    }

    // 数字计数动画
    const statNumbers = document.querySelectorAll('.stat-number');
    const observerOptions = {
        threshold: 0.5,
        rootMargin: '0px 0px -100px 0px'
    };

    const statObserver = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const target = parseInt(entry.target.getAttribute('data-target'));
                animateNumber(entry.target, 0, target, 2000);
                statObserver.unobserve(entry.target);
            }
        });
    }, observerOptions);

    statNumbers.forEach(stat => {
        statObserver.observe(stat);
    });

    function animateNumber(element, start, end, duration) {
        const startTime = performance.now();
        
        function updateNumber(currentTime) {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // 使用缓动函数
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const current = Math.floor(start + (end - start) * easeOutQuart);
            
            element.textContent = current;
            
            if (progress < 1) {
                requestAnimationFrame(updateNumber);
            } else {
                element.textContent = end;
            }
        }
        
        requestAnimationFrame(updateNumber);
    }

    // 技术特性切换
    const techFeatures = document.querySelectorAll('.tech-feature');
    const batteryLayers = document.querySelectorAll('.layer');

    techFeatures.forEach((feature, index) => {
        feature.addEventListener('click', function() {
            // 移除所有活动状态
            techFeatures.forEach(f => f.classList.remove('active'));
            batteryLayers.forEach(l => l.style.opacity = '0.5');
            
            // 添加当前活动状态
            this.classList.add('active');
            if (batteryLayers[index]) {
                batteryLayers[index].style.opacity = '1';
                batteryLayers[index].style.transform = 'scale(1.05)';
                
                setTimeout(() => {
                    batteryLayers[index].style.transform = 'scale(1)';
                }, 300);
            }
        });
    });

    // 产品卡片悬停效果
    const productCards = document.querySelectorAll('.product-card');
    productCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) rotateY(5deg)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) rotateY(0)';
        });
    });

    // 联系表单处理
    const contactForm = document.getElementById('contactForm');
    if (contactForm) {
        contactForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // 获取表单数据
            const formData = new FormData(this);
            const data = Object.fromEntries(formData);
            
            // 显示提交动画
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 发送中...';
            submitBtn.disabled = true;
            
            // 模拟发送过程
            setTimeout(() => {
                submitBtn.innerHTML = '<i class="fas fa-check"></i> 发送成功';
                submitBtn.style.background = 'var(--success-color)';
                
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    submitBtn.style.background = '';
                    this.reset();
                    
                    // 显示成功消息
                    showNotification('消息发送成功！我们会尽快回复您。', 'success');
                }, 2000);
            }, 1500);
        });
    }

    // 返回顶部按钮
    const backToTopBtn = document.getElementById('backToTop');
    
    window.addEventListener('scroll', function() {
        if (window.scrollY > 500) {
            backToTopBtn.classList.add('visible');
        } else {
            backToTopBtn.classList.remove('visible');
        }
    });
    
    backToTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });

    // 粒子效果
    createParticleEffect();

    // 电池充电动画
    animateBatteryCharging();

    // 3D电池旋转控制
    setupBattery3DControls();

    // 页面可见性变化处理
    document.addEventListener('visibilitychange', function() {
        if (document.hidden) {
            // 页面隐藏时暂停动画
            pauseAnimations();
        } else {
            // 页面显示时恢复动画
            resumeAnimations();
        }
    });
});

// 滚动到指定区域
function scrollToSection(sectionId) {
    const section = document.getElementById(sectionId);
    if (section) {
        section.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

// 创建粒子效果
function createParticleEffect() {
    const particleContainer = document.querySelector('.energy-particles');
    if (!particleContainer) return;

    for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'particle';
        particle.style.cssText = `
            position: absolute;
            width: 2px;
            height: 2px;
            background: var(--primary-color);
            border-radius: 50%;
            opacity: 0;
            animation: particleFloat ${3 + Math.random() * 4}s ease-in-out infinite;
            animation-delay: ${Math.random() * 5}s;
            left: ${Math.random() * 100}%;
            top: ${Math.random() * 100}%;
        `;
        particleContainer.appendChild(particle);
    }

    // 添加粒子动画CSS
    if (!document.getElementById('particle-styles')) {
        const style = document.createElement('style');
        style.id = 'particle-styles';
        style.textContent = `
            @keyframes particleFloat {
                0%, 100% {
                    opacity: 0;
                    transform: translateY(0) scale(0);
                }
                50% {
                    opacity: 1;
                    transform: translateY(-50px) scale(1);
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// 电池充电动画
function animateBatteryCharging() {
    const powerLevel = document.querySelector('.power-level');
    if (!powerLevel) return;

    let level = 98;
    let increasing = false;

    setInterval(() => {
        if (increasing) {
            level += 1;
            if (level >= 100) {
                increasing = false;
            }
        } else {
            level -= 1;
            if (level <= 85) {
                increasing = true;
            }
        }
        
        powerLevel.textContent = level + '%';
        
        // 更新电池颜色
        const batteryCore = document.querySelector('.battery-core');
        if (batteryCore) {
            if (level > 90) {
                batteryCore.style.background = 'var(--gradient-primary)';
            } else if (level > 50) {
                batteryCore.style.background = 'linear-gradient(135deg, #ffa726, #ff9800)';
            } else {
                batteryCore.style.background = 'linear-gradient(135deg, #ff6b35, #f44336)';
            }
        }
    }, 100);
}

// 3D电池控制
function setupBattery3DControls() {
    const batteryContainer = document.querySelector('.battery-container');
    if (!batteryContainer) return;

    let isMouseDown = false;
    let mouseX = 0;
    let mouseY = 0;
    let rotationX = 0;
    let rotationY = 0;

    batteryContainer.addEventListener('mousedown', function(e) {
        isMouseDown = true;
        mouseX = e.clientX;
        mouseY = e.clientY;
        this.style.cursor = 'grabbing';
    });

    document.addEventListener('mousemove', function(e) {
        if (!isMouseDown) return;

        const deltaX = e.clientX - mouseX;
        const deltaY = e.clientY - mouseY;

        rotationY += deltaX * 0.5;
        rotationX -= deltaY * 0.5;

        // 限制旋转角度
        rotationX = Math.max(-30, Math.min(30, rotationX));

        batteryContainer.style.transform = `rotateX(${rotationX}deg) rotateY(${rotationY}deg)`;

        mouseX = e.clientX;
        mouseY = e.clientY;
    });

    document.addEventListener('mouseup', function() {
        isMouseDown = false;
        if (batteryContainer) {
            batteryContainer.style.cursor = 'grab';
        }
    });

    // 触摸设备支持
    batteryContainer.addEventListener('touchstart', function(e) {
        const touch = e.touches[0];
        mouseX = touch.clientX;
        mouseY = touch.clientY;
    });

    batteryContainer.addEventListener('touchmove', function(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const deltaX = touch.clientX - mouseX;
        const deltaY = touch.clientY - mouseY;

        rotationY += deltaX * 0.5;
        rotationX -= deltaY * 0.5;

        rotationX = Math.max(-30, Math.min(30, rotationX));

        this.style.transform = `rotateX(${rotationX}deg) rotateY(${rotationY}deg)`;

        mouseX = touch.clientX;
        mouseY = touch.clientY;
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? 'var(--success-color)' : 'var(--primary-color)'};
        color: white;
        padding: 15px 25px;
        border-radius: 10px;
        box-shadow: var(--shadow-light);
        z-index: 10000;
        animation: slideInRight 0.5s ease-out;
    `;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.5s ease-in forwards';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 500);
    }, 3000);

    // 添加通知动画CSS
    if (!document.getElementById('notification-styles')) {
        const style = document.createElement('style');
        style.id = 'notification-styles';
        style.textContent = `
            @keyframes slideInRight {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            @keyframes slideOutRight {
                from {
                    transform: translateX(0);
                    opacity: 1;
                }
                to {
                    transform: translateX(100%);
                    opacity: 0;
                }
            }
        `;
        document.head.appendChild(style);
    }
}

// 暂停动画
function pauseAnimations() {
    const animatedElements = document.querySelectorAll('[style*="animation"]');
    animatedElements.forEach(el => {
        el.style.animationPlayState = 'paused';
    });
}

// 恢复动画
function resumeAnimations() {
    const animatedElements = document.querySelectorAll('[style*="animation"]');
    animatedElements.forEach(el => {
        el.style.animationPlayState = 'running';
    });
}

// 性能监控
function monitorPerformance() {
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                console.log('页面加载性能:', {
                    DNS查询: perfData.domainLookupEnd - perfData.domainLookupStart,
                    TCP连接: perfData.connectEnd - perfData.connectStart,
                    页面加载: perfData.loadEventEnd - perfData.navigationStart,
                    DOM解析: perfData.domContentLoadedEventEnd - perfData.domContentLoadedEventStart
                });
            }, 0);
        });
    }
}

// 启动性能监控
monitorPerformance();

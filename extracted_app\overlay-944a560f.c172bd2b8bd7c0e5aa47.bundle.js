"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4504,7271],{15448:(t,e,a)=>{a.d(e,{p:()=>n,r:()=>r});const n="i18n:translation-locale-changed",r="i18n:formatting-locale-changed"},20057:(t,e,a)=>{a.d(e,{F2:()=>p,LW:()=>c});var n=a(7530),r=a(96610),s=a(92465),i=a(77847),o=a(15448);const l=(0,r.getLogger)("i18n");class c{constructor(t){this.value=t}toString(){return this.value}[Symbol.toStringTag](){return this.value}}class u{constructor(t,e){this.tag=t,this.source=e}}function m(t){try{return new Intl.Locale(t)}catch{return null}}const g=new Map([["en","English"],["zh","中文"],["de","Deutsch"],["es","español"],["fr","français"],["pl","polski"],["pt","português"],["ko","한국어"],["ja","日本語"],["tr","Türkçe"]]);function h(t){const e=t.toString();if(e.endsWith(`-${i.kA}`))return i._C;const a=t.minimize().toString();let n;try{n=new Intl.DisplayNames(e,{type:"language"}).of(a)}catch{}return n??g[a]??g[e]??e}class p{#t;#e;#a;#n;#r;#s;#i;#o;#l;#c;constructor(t,e,a,n){this.#t=new s._M,this.#o=new Intl.Locale(t),this.#a=m(e)??this.#o,this.locales=a.map((t=>{const e=new Intl.Locale(t);return{tag:e.toString(),name:h(e)}})),this.#c=new Map(this.locales.slice(0).reverse().flatMap((t=>this.#u(new Intl.Locale(t.tag)).map((e=>[e,t]))))),this.#l=n}dispose(){this.#t?.dispose(),this.#t=null}onLocaleChanged(t){return this.#t?.subscribe("changed",t)??null}setSystemLocale(t){const e=m(t);null!==e&&(this.#a=e,this.setLocale(this.#e?.toString()??null,"system_locale"))}async setLocale(t,e){let a=null;null!==t&&(this.locales.find((e=>e.tag===t))?a=new Intl.Locale(t):t=null),null===t&&(a=null,t=this.getClosestMatchingSupportedLocale(this.#a).tag);const r=this.#r?.toString(),s=new Intl.Locale(t);this.#i=await this.#l.getTemplates(new Intl.Locale(s.baseName)),this.#r=s,this.#s=t.endsWith(`-${i.kA}`),this.#e=a;const l=this.#n?.toString();this.#n=this.#a.region&&m(`${this.#r.language}-${this.#a.region}`)||this.#r,this.#t?.publish("changed",new u(this.#r.toString(),e)),r!==this.#r.toString()&&(0,n.N9)(o.p),l!==this.#n.toString()&&(0,n.N9)(o.r)}getClosestMatchingSupportedLocale(t=this.#a){return this.#u(t).map((t=>this.#c.get(t))).find((t=>!!t))??this.#c.get(this.#o.toString())}#u(t){const e=t.maximize();return[t.toString(),`${e.language}-${e.script}-${e.region}`,`${e.language}-${e.region}`,`${e.language}-${e.script}`,e.language]}static literal(t){return new c(t.toString())}isDefaultLanguage(){return this.#r.language===this.#o.language}getSystemLocale(){return this.#a}getPreferredLocale(){return this.#e}getEffectiveLocale(){return this.#r}getValue(t,e){if(void 0===t)return"";let a;if(t instanceof c)a=t.toString();else if(a=this.#i.get(t),void 0===a)return l.error(`Missing translation for key ${t}`),t;let n=e?this.#m(a,e):a;return this.#s&&(n=(0,i.bO)(n)),n}#m(t,e){return Object.entries(e).reduce(((t,[e,a])=>t.replace(`$${e}`,"number"==typeof a?this.formatNumber(a):a)),t)}formatNativeNumber(t,e){return this.#g(t,e,navigator.language)}formatNumber(t,e){return this.#g(t,e,this.#n.toString())}#g(t,e,a){return new Intl.NumberFormat(a,e).format(t)}parseNativeNumber(t,e){return this.#h(t,e,navigator.language)}parseNumber(t,e){return this.#h(t,e,this.#n.toString())}#h(t,e,a){if(null==t)return NaN;const n=new Intl.NumberFormat(a,e).formatToParts(11111.11),r=n.find((t=>"group"===t.type))?.value,s=n.find((t=>"decimal"===t.type))?.value;return(r||"."!==s)&&(t=t.split("").map((t=>t===r?"":t===s?".":t)).join("")),parseFloat(t)}formatCurrency(t,e,a){const n=function(t){return L.has(t)}(e);return n||(t/=100),new Intl.NumberFormat(this.#n.toString(),{style:"currency",currency:e,minimumFractionDigits:n?0:2,maximumFractionDigits:n?0:void 0,...a??{}}).format(t)}formatDateTime(t,e){return"string"==typeof t&&(t=Date.parse(t)),new Intl.DateTimeFormat(this.#n.toString(),e).format(t)}formatRelativeTime(t,e){"string"==typeof t&&(t=Date.parse(t)),t instanceof Date&&(t=t.valueOf());const[a,n]=this.#p(Date.now()-t);return this.getRelativeTimeFormatter(e).format(n,a)}getRelativeTimeFormatter(t={}){return new Intl.RelativeTimeFormat(this.#n.toString(),t)}#p(t){const e=t<0?-1:1;t=Math.abs(t);for(const[a,n]of f)if(t>=n)return[a,e*Math.round(t/n)];return["second",0]}getCountries(){const t={},e=new Intl.DisplayNames([this.#n.toString()],{type:"region"});for(const a of d)try{t[a]=e.of(a)??a}catch(e){t[a]=a}return t}}const f=[["year",31536e6],["quarter",7884e6],["month",2628e6],["week",6048e5],["day",864e5],["hour",36e5],["minute",6e4],["second",1e3]],L=new Set(["BIF","CLP","DJF","GNF","JPY","KMF","KRW","MGA","PYG","RWF","UGX","VND","VUV","XAF","XOF","XPF"]),d=new Set(["AF","AX","AL","DZ","AS","AD","AO","AI","AG","AR","AM","AW","AU","AT","AZ","BS","BH","BD","BB","BY","BE","BZ","BJ","BM","BT","BO","BQ","BA","BW","BR","BN","BG","BF","BI","CV","KH","CM","CA","KY","CF","TD","CL","CN","CX","CC","CO","KM","CG","CD","CK","CR","CI","HR","CU","CW","CY","CZ","DK","DJ","DM","DO","EC","EG","SV","GQ","ER","EE","ET","FK","FO","FJ","FI","FR","GF","PF","GA","GM","GE","DE","GH","GI","GR","GL","GD","GP","GU","GT","GG","GN","GW","GY","HT","VA","HN","HK","HU","IS","IN","ID","IR","IQ","IE","IM","IL","IT","JM","JP","JE","JO","KZ","KE","KI","KP","KR","KW","KG","LA","LV","LB","LS","LR","LY","LI","LT","LU","MO","MK","MG","MW","MY","MV","ML","MT","MH","MQ","MR","MU","YT","MX","FM","MD","MC","MN","ME","MS","MA","MZ","MM","NA","NR","NP","NL","NC","NZ","NI","NE","NG","NU","NF","MP","NO","OM","PK","PW","PS","PA","PG","PY","PE","PH","PN","PL","PT","PR","QA","RE","RO","RU","RW","BL","SH","KN","LC","MF","PM","VC","WS","SM","ST","SA","SN","RS","SC","SL","SG","SX","SK","SI","SB","SO","ZA","GS","SS","ES","LK","SD","SR","SZ","SE","CH","SY","TW","TJ","TZ","TH","TL","TG","TK","TO","TT","TN","TR","TM","TC","TV","UG","UA","AE","GB","US","UY","UZ","VU","VE","VN","VG","VI","WF","EH","YE","ZM","ZW"])},73841:(t,e,a)=>{function n(t){const e=new Map;for(const[a,n]of Object.entries(t))for(const[t,r]of Object.entries(n))e.set(`${a}.${t}`,r);return e}a.d(e,{T:()=>n})},77847:(t,e,a)=>{a.d(e,{_C:()=>r,bO:()=>i,kA:()=>n});const n="x-leet",r="13375p34k",s=Object.freeze({a:"4",e:"3",g:"6",i:"1",o:"0",s:"5",t:"7"});function i(t){let e,a=!1;return t.split("").map((t=>"("===t&&"]"===e?(a=!0,t):a?(a=")"!==t,t):(e=t,t=t.toLowerCase(),s[t]??t))).join("")}},"shared/i18n/index":(t,e,a)=>{a.r(e),a.d(e,{configure:()=>r}),a("aurelia-framework");var n=a(20057);function r(t,e){const a=new n.F2(e.defaultLocale,e.systemLocale,e.supportedLocales,e.templateRepository);t.container.registerInstance(n.F2,a),t.postTask((async()=>{let t=null;if(e.initialLocale)if("string"==typeof e.initialLocale)t=e.initialLocale;else try{t=await e.initialLocale()}catch{}await a.setLocale(t??null)})),t.globalResources(["./resources/value-converters"])}},"shared/i18n/resources/value-converters":(t,e,a)=>{a.r(e),a.d(e,{I18nCurrencyValueConverter:()=>h,I18nDateTimeNowValueConverter:()=>L,I18nDateTimeValueConverter:()=>f,I18nElaspedTimeValueConverter:()=>d,I18nLiteralValueConverter:()=>u,I18nNativeNumberValueConverter:()=>g,I18nNumberValueConverter:()=>m,I18nParamValueConverter:()=>c,I18nRelativeTimeValueConverter:()=>p,I18nValueConverter:()=>l});var n=a(15215),r=a("aurelia-framework"),s=a(12082),i=a(20057),o=a(15448);let l=class{#f;constructor(t){this.signals=[o.p,o.r],this.#f=t}toView(t,e){return null===t?"":this.#f.getValue(t,e)}};l=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],l);let c=class{#f;constructor(t){this.signals=[o.p,o.r],this.#f=t}toView(t,e,a,n){return n=Object.assign({[a]:t},n??{}),this.#f.getValue(e,n)}};c=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],c);let u=class{toView(t){return i.F2.literal(t)}};u=(0,n.Cg)([(0,r.autoinject)()],u);let m=class{#f;constructor(t){this.signals=[o.r],this.#f=t}toView(t,e){return"number"!=typeof t&&(t=parseFloat(t)),isNaN(t)?"":this.#f.formatNumber(t,e)}fromView(t,e){return"number"==typeof t?t:this.#f.parseNumber(t,e)}};m=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],m);let g=class{#f;constructor(t){this.#f=t}toView(t,e){return"number"!=typeof t&&(t=this.#f.parseNativeNumber(t,e)),isNaN(t)?"":this.#f.formatNativeNumber(t,e)}fromView(t,e){return"number"==typeof t?t:this.#f.parseNativeNumber(t,e)}};g=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],g);let h=class{#f;constructor(t){this.signals=[o.r],this.#f=t}toView(t,e,a){return"number"!=typeof t&&(t=parseFloat(t)),this.#f.formatCurrency(t,e,a)}};h=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],h);let p=class{#f;constructor(t){this.signals=[o.r,s.q],this.#f=t}toView(t,e){return t?this.#f.formatRelativeTime(t,e):""}};p=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],p);let f=class{#f;constructor(t){this.signals=[o.r],this.#f=t}toView(t,e,a){return t?this.#f.formatDateTime(t,e):a}};f=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],f);let L=class{#f;constructor(t){this.signals=[o.r,s.q],this.#f=t}toView(t){return this.#f.formatDateTime(Date.now(),t)}};L=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],L);let d=class{#f;constructor(t){this.signals=[o.p,o.r,s.q],this.#f=t}toView(t,e=!1){if(!t)return"";"string"==typeof t&&(t=Date.parse(t)),t instanceof Date&&(t=t.valueOf());const[a,n]=this.#L(Date.now()-t);return this.#f.getValue(`time.$x_${a}${e?"":"_ago"}`,{x:n})}#L(t){t=Math.abs(t);for(const[e,a,n]of S)if(t>=n){const r=Math.round(t/n);return[r>1?a:e,r]}return["sec",0]}};d=(0,n.Cg)([(0,r.autoinject)(),(0,n.Sn)("design:paramtypes",[i.F2])],d);const S=[["year","years",31536e6],["mo","mo",2592e6],["day","days",864e5],["hr","hr",36e5],["min","min",6e4],["sec","sec",1e3]]}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5590],{"app/resources/elements/go-pro-cta":(e,t,n)=>{n.r(t),n.d(t,{GoProCta:()=>i});var o=n(15215),a=n("aurelia-framework");let i=class{constructor(){this.isCollapsed=!1}};(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",Boolean)],i.prototype,"isCollapsed",void 0),i=(0,o.Cg)([a.autoinject],i)},"app/resources/elements/go-pro-cta.html":(e,t,n)=>{n.r(t),n.d(t,{default:()=>o});const o='<template> <require from="./go-pro-cta.scss"></require> <div pro-cta="trigger: header_support_us_button" class="go-pro-cta ${isCollapsed ? \'is-collapsed\' : \'\'}"> <div class="support-we-mod"> <div innerhtml.bind="\'sidebar_user.support_wemod\' | i18n | markdown"></div> </div> <div class="go-pro-cta-badge"> <div innerhtml.bind="isCollapsed ? \'sidebar_user.go_pro_collapsed\' : \'sidebar_user.go_pro\' | i18n | markdown"></div> </div> </div> </template> '},"app/resources/elements/go-pro-cta.scss":(e,t,n)=>{n.r(t),n.d(t,{default:()=>r});var o=n(31601),a=n.n(o),i=n(76314),l=n.n(i)()(a());l.push([e.id,"go-pro-cta{max-width:100%;overflow:hidden}.go-pro-cta{margin-right:6px;display:flex;border-radius:35px;align-items:center;background:linear-gradient(265deg, #1fbaf8 0%, #2a9bf9 10.94%, #3874fb 23.43%, #4743fc 47.07%, #2a1257 69.51%, #1c1625 93.6%, #0f1014 109.47%),linear-gradient(86deg, #320057 4.13%, #530de7 35.93%, #3874fb 64.42%, #0bf2f6 104.88%);transition:width .3s ease-in-out}.go-pro-cta,.go-pro-cta *{cursor:pointer}.go-pro-cta .support-we-mod{color:#fff;text-transform:uppercase;display:flex;flex-direction:column;text-align:center}.go-pro-cta .support-we-mod em{font-style:normal}.go-pro-cta-badge{color:#3d65f8;text-transform:uppercase;height:22px;background:#fff;border-radius:33.25px;font-weight:900;font-style:italic}.go-pro-cta.is-collapsed{padding:2px 6px !important;margin-right:0 !important}.go-pro-cta.is-collapsed .support-we-mod{display:none}.go-pro-cta.is-collapsed .go-pro-cta-badge{padding:0 !important;font-size:9.5px !important;min-width:45px;color:#fff;background:none;padding:0}.go-pro-cta.is-collapsed .go-pro-cta-badge div{display:flex;align-items:center;justify-content:center;width:100%;line-height:100% !important}.go-pro-cta.is-collapsed .go-pro-cta-badge:lang(en) em{margin-left:1.5px}.go-pro-cta.is-collapsed .go-pro-cta-badge:lang(fr){min-width:auto !important}.go-pro-cta.is-collapsed .go-pro-cta-badge:lang(ko) div{font-size:8.5px}.go-pro-cta:lang(en){padding:4px 4px 4px 8px;gap:6px;flex-shrink:1}.go-pro-cta:lang(en) .support-we-mod{text-align:center;justify-content:center;font-weight:800;font-size:8.21px;line-height:90%}.go-pro-cta:lang(en) .support-we-mod em{font-weight:900;font-size:9.57px}.go-pro-cta:lang(en) .go-pro-cta-badge{gap:1.5px;font-size:12.35px;letter-spacing:-0.88px;padding:6px;display:flex;line-height:80%;text-wrap:nowrap}.go-pro-cta:not(:lang(en)){padding:3px 3px 3px 8px;gap:4px}.go-pro-cta:not(:lang(en)) .support-we-mod{font-size:8.25px;font-weight:900;line-height:90%}.go-pro-cta:not(:lang(en)) .support-we-mod :first-of-type{font-weight:800}.go-pro-cta:not(:lang(en)) .go-pro-cta-badge{padding:0 2px;min-width:45px;height:22px;display:flex;flex-wrap:wrap;text-wrap:wrap;justify-content:center;align-items:center;letter-spacing:-0.5px;font-size:9px;line-height:90%;margin-left:auto}.go-pro-cta:not(:lang(en)) .go-pro-cta-badge div{line-height:90%;justify-content:center;display:flex}.go-pro-cta:not(:lang(en)):lang(de){margin-right:0}.go-pro-cta:not(:lang(en)):lang(de) .support-we-mod{font-size:7.25px}.go-pro-cta:not(:lang(en)):lang(de) .support-we-mod em{font-size:8.25px}.go-pro-cta:not(:lang(en)):lang(de) .go-pro-cta-badge{text-align:center;word-break:break-word}.go-pro-cta:not(:lang(en)):lang(de) .go-pro-cta-badge div{display:flex;flex-direction:column;text-align:center}.go-pro-cta:not(:lang(en)):lang(de) .go-pro-cta-badge em{margin-left:0}.go-pro-cta:not(:lang(en)):lang(es) .go-pro-cta-badge{letter-spacing:-1px}.go-pro-cta:not(:lang(en)):lang(es) .go-pro-cta-badge div{flex-wrap:wrap;display:inline-flex}.go-pro-cta:not(:lang(en)):lang(fr) .go-pro-cta-badge{max-width:55px;width:100%}.go-pro-cta:not(:lang(en)):lang(fr) .go-pro-cta-badge div{text-align:center;flex-direction:column;width:100%}.go-pro-cta:not(:lang(en)):lang(pt) .go-pro-cta-badge{max-width:55px;width:100%;align-items:center}.go-pro-cta:not(:lang(en)):lang(pt) .go-pro-cta-badge div{text-align:center;display:inline}.go-pro-cta:not(:lang(en)):lang(tr) .go-pro-cta-badge{width:45px}.go-pro-cta:not(:lang(en)):lang(tr) .go-pro-cta-badge div{display:inline;text-align:center}.go-pro-cta:not(:lang(en)):lang(ja) .support-we-mod div{display:flex;flex-direction:column;text-align:center}.go-pro-cta:not(:lang(en)):lang(ja) .go-pro-cta-badge div{flex-direction:column;text-align:center}.go-pro-cta:not(:lang(en)):lang(ko) .support-we-mod div{display:flex;flex-direction:column;text-align:center}.go-pro-cta:not(:lang(en)):lang(ko) .go-pro-cta-badge{width:60px;max-width:60px}.go-pro-cta:not(:lang(en)):lang(ko) .go-pro-cta-badge div{flex-direction:column;text-align:center}.go-pro-cta:not(:lang(en)):lang(pl) .support-we-mod :first-of-type{letter-spacing:-0.5px}.go-pro-cta:not(:lang(en)):lang(pl) .go-pro-cta-badge{min-width:50px}.go-pro-cta:not(:lang(en)):lang(pl) .go-pro-cta-badge div{text-align:center;display:inline}.go-pro-cta:not(:lang(en)):lang(zh){margin-right:0}.go-pro-cta:not(:lang(en)):lang(zh) .support-we-mod{width:35px;line-height:95%}.go-pro-cta:not(:lang(en)):lang(zh) .go-pro-cta-badge{letter-spacing:-0.5px;padding:3px;max-width:67px;width:100%}.go-pro-cta:not(:lang(en)):lang(id) .go-pro-cta-badge div{font-size:7px;display:inline;text-align:center}.go-pro-cta:not(:lang(en)):lang(it) .go-pro-cta-badge div{font-size:9px;display:inline;text-align:center}.go-pro-cta:not(:lang(en)):lang(th) .go-pro-cta-badge div{font-size:9px;display:inline;text-align:center}",""]);const r=l},"app/resources/elements/help-menu":(e,t,n)=>{n.r(t),n.d(t,{EVENT_HELP_MENU_REPORT_ISSUE_CLICK:()=>c,EVENT_HELP_MENU_SUGGEST_MOD_CLICK:()=>g,HelpMenu:()=>d});var o=n(15215),a=n("aurelia-event-aggregator"),i=n("aurelia-framework"),l=n(41572),r=n(9374),p=n(54995),s=n(62914);const g="help-menu-suggest-mod-click",c="help-menu-report-issue-click";let d=class{#e;#t;constructor(e,t){this.handleTrainerNotesButtonClick=()=>{this.menu.hide(),this.onTrainerNotesButtonClick?.(),this.sendOptionClickEvent("help_menu_item_click","setup_info")},this.handleReportBugClick=()=>{const e={menuButtonEl:this.buttonEl};this.#e.publish(c,e),this.menu.hide(),this.sendOptionClickEvent("help_menu_item_click","report_issue")},this.handleSuggestModClick=()=>{this.#e.publish(g),this.menu.hide(),this.sendOptionClickEvent("help_menu_item_click","suggest_mod")},this.handleFeaturebaseLinkClick=(e,t,n)=>{if(e.preventDefault(),e.stopPropagation(),this.account.featurebaseJwt){const e=new URL("https://wemod.featurebase.app/api/v1/auth/access/jwt");e.searchParams.set("jwt",this.account.featurebaseJwt??""),e.searchParams.set("return_to",`https://hub.wemod.com?b=${n}`),window.open(e.toString(),"_blank")}else window.open(`https://hub.wemod.com?b=${n}`);this.menu.hide(),this.sendOptionClickEvent("help_menu_item_click",t)},this.#e=e,this.#t=t}get isPro(){return(0,l.a)(this.account)}sendMenuOpenEvent(){this.#t.event("help_menu_open",{source:this.placement,title_id:this.titleId,trainer_id:this.trainerId},s.Io)}sendOptionClickEvent(e,t){this.#t.event("help_menu_item_click",{source:this.placement,option:t,title_id:this.titleId,trainer_id:this.trainerId},s.Io)}};(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",String)],d.prototype,"gameTitle",void 0),(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",String)],d.prototype,"titleId",void 0),(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",String)],d.prototype,"trainerId",void 0),(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",Boolean)],d.prototype,"showGameItems",void 0),(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",Boolean)],d.prototype,"showTrainerNotesButton",void 0),(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",String)],d.prototype,"placement",void 0),(0,o.Cg)([i.bindable,(0,o.Sn)("design:type",Function)],d.prototype,"onTrainerNotesButtonClick",void 0),(0,o.Cg)([(0,r._)((e=>[e.account])),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],d.prototype,"isPro",null),d=(0,o.Cg)([(0,p.m6)({setup:"attached",teardown:"detached",selectors:{account:(0,p.$t)((e=>e.account))}}),(0,i.autoinject)(),(0,o.Sn)("design:paramtypes",[a.EventAggregator,s.j0])],d)},"app/resources/elements/help-menu.html":(e,t,n)=>{n.r(t),n.d(t,{default:()=>o});const o='<template> <require from="./help-menu.scss"></require> <div class="help-menu ${placement}"> <button class="help-menu-button" data-tooltip-trigger-for="help-menu-${placement}-tooltip" title="${\'help_menu.help_menu\' | i18n}" ref="buttonEl"> <i>help</i> </button> <wm-list-menu tooltip-id="help-menu-${placement}-tooltip" accessible-label="${\'help_menu.help_menu\' | i18n}" placement="top-start" trigger-method="click" view-model.ref="menu" on-show.call="sendMenuOpenEvent()"> <div slot="content" class="help-menu-popup"> <div if.bind="showGameItems"> <h3 show.bind="gameTitle">${gameTitle}</h3> <menu> <li if.bind="showTrainerNotesButton"> <button type="button" click.trigger="handleTrainerNotesButtonClick()"> <i>assignment</i><span class="label">${\'help_menu.setup_information\' | i18n}</span> </button> </li> <li> <button type="button" click.trigger="handleReportBugClick()"> <i>bug_report</i><span class="label">${\'help_menu.report_an_issue\' | i18n}</span> </button> </li> <li if.bind="isPro"> <button type="button" click.trigger="handleSuggestModClick()"> <i>voting_chip</i><span class="label">${\'help_menu.suggest_a_mod\' | i18n}</span> </button> </li> </menu> </div> <div> <h3>${\'help_menu.general\' | i18n}</h3> <menu> <li> <a href="https://hub.wemod.com/?b=676064fc1c16dbbaa5b26e8d" click.trigger="handleFeaturebaseLinkClick($event, \'submit_feedback\', \'676064fc1c16dbbaa5b26e8d\')"><i>chat_bubble</i><span class="label">${\'help_menu.submit_feedback\' | i18n}</span></a> </li> <li> <a href="https://hub.wemod.com/?b=67befc894ee1145b5e7149e4" click.trigger="handleFeaturebaseLinkClick($event, \'feature_request\', \'67befc894ee1145b5e7149e4\')"><i>lightbulb</i><span class="label">${\'help_menu.feature_request\' | i18n}</span></a> </li> </menu> </div> </div> </wm-list-menu> </div> </template> '},"app/resources/elements/help-menu.scss":(e,t,n)=>{n.r(t),n.d(t,{default:()=>h});var o=n(31601),a=n.n(o),i=n(76314),l=n.n(i),r=n(4417),p=n.n(r),s=new URL(n(83959),n.b),g=new URL(n(23487),n.b),c=l()(a()),d=p()(s),u=p()(g);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${d}) format("woff2")}.material-symbols-outlined,.help-menu .help-menu-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.help-menu{position:relative}.help-menu .help-menu-button{width:100%;height:100%;background:none;border:none;cursor:pointer}.help-menu .help-menu-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;width:20px;height:20px;display:inline-flex;align-items:center;justify-content:center;color:var(--menu__item__icon--color, #fff);transition:color .15s}.help-menu.app-header .help-menu-button{transition:opacity 2s ease-in-out;opacity:1;padding:0 18px;height:36px;display:grid;place-content:center;transition:background-color .3s ease-in-out}.help-menu.app-header .help-menu-button:focus-visible,.help-menu.app-header .help-menu-button:hover{background-color:rgba(255,255,255,.3)}.help-menu.app-header .help-menu-button:focus-visible:has(.close),.help-menu.app-header .help-menu-button:hover:has(.close){background-color:var(--color--alert)}.help-menu.app-header .help-menu-button i{background-color:#fff;width:15px;height:15px;-webkit-mask-box-image:url(${u})}.help-menu.game-title-header .help-menu-button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;transition:background-color .15s}.help-menu.game-title-header .help-menu-button,.help-menu.game-title-header .help-menu-button *{cursor:pointer}.help-menu.game-title-header .help-menu-button:hover{background:rgba(255,255,255,.25)}.help-menu.game-title-header .help-menu-button i{color:rgba(255,255,255,.8)}.help-menu.game-title-header .help-menu-button:hover i{color:#fff}`,""]);const h=c}}]);
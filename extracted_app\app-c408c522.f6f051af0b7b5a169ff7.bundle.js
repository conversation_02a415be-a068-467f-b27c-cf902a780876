"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[68],{"settings/resources/elements/theme-selector":(e,t,r)=>{r.r(t),r.d(t,{ThemeSelector:()=>l});var o=r(15215),a=r("aurelia-framework"),i=r(20770),s=r(54995),n=r(48881);let l=class{#e;constructor(e){this.source="settings",this.#e=e}selectTheme(e){("default"===e||this.isPro)&&this.#e.dispatch(n.Kc,{theme:e},this.source)}get isPro(){return!!this.account?.subscription}};(0,o.Cg)([a.bindable,(0,o.Sn)("design:type",String)],l.prototype,"source",void 0),(0,o.Cg)([(0,a.computedFrom)("account"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],l.prototype,"isPro",null),l=(0,o.Cg)([(0,s.m6)({selectors:{selectedTheme:(0,s.$t)((e=>e.settings?.theme)),account:(0,s.$t)((e=>e.account))}}),(0,a.autoinject)(),(0,o.Sn)("design:paramtypes",[i.il])],l)},"settings/resources/elements/theme-selector.html":(e,t,r)=>{r.r(t),r.d(t,{default:()=>p});var o=r(14385),a=r.n(o),i=new URL(r(52280),r.b),s=new URL(r(59843),r.b),n=new URL(r(90188),r.b),l=new URL(r(80251),r.b),c=new URL(r(44170),r.b);const p='<template> <require from="./theme-selector.scss"></require> <div class="theme-selector"> <button class="theme ${selectedTheme == \'default\' ? \'selected\' : \'\'}" click.delegate="selectTheme(\'default\')"> <img class="thumbnail" src="'+a()(i)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.default\' | i18n}</span> </span> </footer> </button> <button class="theme ${selectedTheme == \'purple-pro\' ? \'selected\' : \'\'} ${isPro ? \'\' : \'trigger-pro-cta\'}" click.delegate="selectTheme(\'purple-pro\')" pro-cta="trigger: theme_selector_item; disabled.bind: isPro; feature: themes"> <img class="thumbnail" src="'+a()(s)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.purple\' | i18n}</span> </span> <span class="pro-badge">Pro</span> </footer> </button> <button class="theme ${selectedTheme == \'green-pro\' ? \'selected\' : \'\'} ${isPro ? \'\' : \'trigger-pro-cta\'}" click.delegate="selectTheme(\'green-pro\')" pro-cta="trigger: theme_selector_item; disabled.bind: isPro; feature: themes"> <img class="thumbnail" src="'+a()(n)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.green\' | i18n}</span> </span> <span class="pro-badge">Pro</span> </footer> </button> <button class="theme ${selectedTheme == \'orange-pro\' ? \'selected\' : \'\'} ${isPro ? \'\' : \'trigger-pro-cta\'}" click.delegate="selectTheme(\'orange-pro\')" pro-cta="trigger: theme_selector_item; disabled.bind: isPro; feature: themes"> <img class="thumbnail" src="'+a()(l)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.orange\' | i18n}</span> </span> <span class="pro-badge">Pro</span> </footer> </button> <button class="theme ${selectedTheme == \'pro\' ? \'selected\' : \'\'} ${isPro ? \'\' : \'trigger-pro-cta\'}" click.delegate="selectTheme(\'pro\')" pro-cta="trigger: theme_selector_item; disabled.bind: isPro; feature: themes"> <img class="thumbnail" src="'+a()(c)+'"> <footer> <span class="wrapper"> <span class="radio"></span> <span class="label">${\'theme_selector.black\' | i18n}</span> </span> <span class="pro-badge">Pro</span> </footer> </button> </div> </template> '},"settings/resources/elements/theme-selector.scss":(e,t,r)=>{r.r(t),r.d(t,{default:()=>h});var o=r(31601),a=r.n(o),i=r(76314),s=r.n(i),n=r(4417),l=r.n(n),c=new URL(r(81206),r.b),p=s()(a()),m=l()(c);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.theme-selector{display:flex;flex-wrap:wrap;margin-right:-20px}.theme-selector .theme{background:rgba(0,0,0,0);border:0;padding:0;margin-bottom:20px;margin-right:20px}.theme-selector .theme .thumbnail{width:124px;height:77px;border-radius:5px;overflow:hidden;transition:border-color .15s}.theme-selector .theme footer{display:flex;flex-direction:row;align-items:center;margin-top:10px}.theme-selector .theme footer .wrapper{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;flex:1}.theme-selector .theme footer .wrapper,.theme-selector .theme footer .wrapper *{cursor:pointer}.theme-selector .theme footer .wrapper>*:first-child{margin-right:9px}.theme-selector .theme footer .wrapper .radio{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;border-radius:50%;display:inline-flex;align-items:center;justify-content:center;width:15px;height:15px;border-color:rgba(255,255,255,.25);background-color:rgba(0,0,0,0)}.theme-selector .theme footer .wrapper .radio,.theme-selector .theme footer .wrapper .radio *{cursor:pointer}.theme-selector .theme footer .wrapper .radio:checked:before{opacity:1}.theme-selector .theme footer .wrapper .radio:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${m});mask:url(${m})}.theme-selector .theme footer .wrapper .radio:before{width:9px;height:9px;border-radius:50%;-webkit-mask-box-image:none;mask:none;position:relative;top:initial;left:initial;box-shadow:inset 0 0 0 1px var(--theme--background);transform:scale(1)}.theme-selector .theme footer .wrapper .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color)}.theme-selector .theme footer .wrapper .label,.theme-selector .theme footer .wrapper .label *{cursor:pointer}.theme-selector .theme footer .pro-badge{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:14px;font-size:10px;letter-spacing:.4px;padding:0 3px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;display:flex;flex:0 0 auto}@media(forced-colors: active){body:not(.override-contrast-mode) .theme-selector .theme footer .pro-badge{border:1px solid #fff}}.theme-selector .theme:hover .thumbnail,.theme-selector .theme.selected .thumbnail{border:1px solid var(--theme--highlight)}.theme-selector .theme:hover footer .radio,.theme-selector .theme.selected footer .radio{border-color:var(--theme--highlight)}.theme-selector .theme:hover footer .radio:before,.theme-selector .theme.selected footer .radio:before{opacity:1}`,""]);const h=p},"settings/resources/elements/time-machine":(e,t,r)=>{r.r(t),r.d(t,{TimeMachine:()=>i});let o=null,a=null;class i{constructor(){this.dateOptions={year:"numeric",month:"long",day:"numeric",weekday:"long",hour:"2-digit",minute:"2-digit",second:"2-digit",timeZoneName:"short"},this.hasTravelled=!!o&&!!a}#t;attached(){this.travelTime=this.#r(),this.#t=setInterval((()=>this.currentTime=new Date))}detached(){clearInterval(this.#t)}travel(){o||(o=Date),a||(a=o.now);const e=new Date,t=new o(this.travelTime).getTime()+1e3*e.getSeconds()+e.getMilliseconds()-a();function r(){return(a??Date.now)()+t}function i(...e){return 0===e.length?new(o||Date)(r()):new o(...e)}i.prototype.constructor=i,i.now=r,["parse","UTC","prototype"].forEach((e=>{i[e]=(o||Date)[e]})),Date=i,this.hasTravelled=!0}#r(){const e=new Date,t=60*e.getTimezoneOffset()*1e3;return new Date(e.getTime()-t).toISOString().slice(0,16)}reset(){this.hasTravelled&&(Date=o,a=null,this.hasTravelled=!1)}}},"settings/resources/elements/time-machine.html":(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});const o='<template> <require from="./time-machine.scss"></require> <div class="time-machine"> <div class="current-time">${currentTime | i18nDateTime:dateOptions}</div> <div class="travel-inputs"> <input type="datetime-local" value.bind="travelTime"> <button class="standard-button" click.delegate="travel()">Travel</button> <button class="standard-button" click.delegate="reset()" disabled.bind="!hasTravelled">Reset</button> </div> </div> </template> '},"settings/resources/elements/time-machine.scss":(e,t,r)=>{r.r(t),r.d(t,{default:()=>h});var o=r(31601),a=r.n(o),i=r(76314),s=r.n(i),n=r(4417),l=r.n(n),c=new URL(r(83959),r.b),p=s()(a()),m=l()(c);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.time-machine{display:flex;flex-direction:column;width:350px;text-align:right}.time-machine .current-time{font-size:11px;margin-bottom:10px}.time-machine .travel-inputs{display:flex;align-items:center;justify-content:flex-end}.time-machine .travel-inputs input{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;width:auto;color-scheme:dark}.time-machine .travel-inputs input::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.time-machine .travel-inputs input::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.time-machine .travel-inputs input:disabled{opacity:.5}.time-machine .travel-inputs input:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.time-machine .travel-inputs input:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.time-machine .travel-inputs button{margin-left:10px}`,""]);const h=p},"settings/resources/elements/toggle.html":(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});const o='<template bindable="value"> <require from="../../../shared/resources/elements/toggle.html"></require> <toggle value.two-way="value"></toggle> </template> '}}]);
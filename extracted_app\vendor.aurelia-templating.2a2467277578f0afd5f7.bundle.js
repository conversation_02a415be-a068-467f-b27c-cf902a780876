/*! For license information please see vendor.aurelia-templating.2a2467277578f0afd5f7.bundle.js.LICENSE.txt */
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5132],{30960:(e,t,n)=>{n.d(t,{$:()=>f,$e:()=>Ie,BI:()=>Ce,BY:()=>ne,CJ:()=>ie,Cu:()=>Ge,Cv:()=>we,EM:()=>qe,EO:()=>_,F4:()=>P,FF:()=>Ve,FK:()=>at,GO:()=>Qe,HI:()=>Ze,I6:()=>he,IM:()=>h,Ij:()=>g,Lq:()=>_e,NY:()=>et,OT:()=>S,Og:()=>Me,P5:()=>v,QB:()=>V,RH:()=>ae,Ss:()=>R,U4:()=>We,Ud:()=>p,Up:()=>ot,V8:()=>k,VP:()=>E,Wy:()=>I,Y_:()=>Fe,Yj:()=>A,Zc:()=>ze,_t:()=>Ke,a7:()=>Ae,bV:()=>se,dZ:()=>N,eu:()=>L,ev:()=>Z,fo:()=>Xe,fp:()=>j,gF:()=>G,gG:()=>re,h1:()=>Ue,hL:()=>$e,hl:()=>rt,i6:()=>Ne,iN:()=>ut,jf:()=>Pe,kS:()=>x,lQ:()=>nt,nJ:()=>T,oX:()=>tt,pH:()=>Te,rL:()=>st,rM:()=>F,rr:()=>it,sE:()=>me,v3:()=>Se,vW:()=>xe,x4:()=>oe,xI:()=>de,yU:()=>D,zH:()=>He,zT:()=>be});var i=n(16566),r=n(95260),o=n(38468),s=n(83260),a=n(96610),u=n(7530),l=n(27884),c=n(40896),h=function(){function e(e){this.element=e,this.subscriptions={}}return e.prototype._enqueueHandler=function(e){this.subscriptions[e.eventName]=this.subscriptions[e.eventName]||[],this.subscriptions[e.eventName].push(e)},e.prototype._dequeueHandler=function(e){var t,n=this.subscriptions[e.eventName];return n&&(t=n.indexOf(e))>-1&&n.splice(t,1),e},e.prototype.publish=function(e,t,n,r){void 0===t&&(t={}),void 0===n&&(n=!0),void 0===r&&(r=!0);var o=i.dv.createCustomEvent(e,{cancelable:r,bubbles:n,detail:t});this.element.dispatchEvent(o)},e.prototype.subscribe=function(t,n,i){if("function"==typeof n)return void 0===i&&(i=e.defaultListenerOptions),new d(this,t,n,i,!1)},e.prototype.subscribeOnce=function(t,n,i){if("function"==typeof n)return void 0===i&&(i=e.defaultListenerOptions),new d(this,t,n,i,!0)},e.prototype.dispose=function(e){if(e&&"string"==typeof e){var t=this.subscriptions[e];if(t)for(;t.length;){var n=t.pop();n&&n.dispose()}}else this.disposeAll()},e.prototype.disposeAll=function(){for(var e in this.subscriptions)this.dispose(e)},e.defaultListenerOptions=!0,e}(),d=function(){function e(e,t,n,i,r){this.owner=e,this.eventName=t,this.handler=n,this.capture="boolean"==typeof i?i:i.capture,this.bubbles=!this.capture,this.captureOrOptions=i,this.once=r,e.element.addEventListener(t,this,i),e._enqueueHandler(this)}return e.prototype.handleEvent=function(e){(0,this.handler)(e),this.once&&this.dispose()},e.prototype.dispose=function(){this.owner.element.removeEventListener(this.eventName,this,this.captureOrOptions),this.owner._dequeueHandler(this),this.owner=this.handler=null},e}(),p=function(){function e(){this.dependencies={}}return e.prototype.addDependency=function(e){this.dependencies[e]=!0},e.prototype.hasDependency=function(e){return e in this.dependencies},e}(),f=function(){function e(e,t){void 0===e&&(e=!1),void 0===t&&(t=!1),this.targetShadowDOM=e,this.compileSurrogate=t,this.associatedModuleId=null}return e.normal=new e,e}(),v=function(){function e(){}return e.enhance=function(){var t=new e;return t.enhance=!0,t},e.unitTest=function(t,n){var i=new e;return i.type=t,i.attributes=n||{},i},e.element=function(t,n){var i=new e;return i.type=n,i.attributes={},i.anchorIsContainer=!(t.hasAttribute("containerless")||n.containerless),i.initiatedByBehavior=!0,i},e.attribute=function(t,n){var i=new e;return i.attrName=t,i.type=n||null,i.attributes={},i},e.dynamic=function(t,n,i){var r=new e;return r.host=t,r.viewModel=n,r.viewFactory=i,r.inheritBindingContext=!0,r},e.normal=new e,e}(),m=v.prototype;m.initiatedByBehavior=!1,m.enhance=!1,m.partReplacements=null,m.viewFactory=null,m.originalAttrName=null,m.skipContentProcessing=!1,m.contentFactory=null,m.viewModel=null,m.anchorIsContainer=!1,m.host=null,m.attributes=null,m.type=null,m.attrName=null,m.inheritBindingContext=!1;var g=function(){function e(){}return e.shadowSlot=function(t){var n=new e;return n.parentInjectorId=t,n.shadowSlot=!0,n},e.contentExpression=function(t){var n=new e;return n.contentExpression=t,n},e.letElement=function(t){var n=new e;return n.expressions=t,n.letElement=!0,n},e.lifting=function(t,n){var i=new e;return i.parentInjectorId=t,i.expressions=e.noExpressions,i.behaviorInstructions=[n],i.viewFactory=n.viewFactory,i.providers=[n.type.target],i.lifting=!0,i},e.normal=function(t,n,i,r,o,s){var a=new e;return a.injectorId=t,a.parentInjectorId=n,a.providers=i,a.behaviorInstructions=r,a.expressions=o,a.anchorIsContainer=!s||s.anchorIsContainer,a.elementInstruction=s,a},e.surrogate=function(t,n,i,r){var o=new e;return o.expressions=i,o.behaviorInstructions=n,o.providers=t,o.values=r,o},e.noExpressions=Object.freeze([]),e}(),w=g.prototype;function y(e,t,n,i){var r,o=arguments.length,s=o<3?t:null===i?i=Object.getOwnPropertyDescriptor(t,n):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,n,i);else for(var a=e.length-1;a>=0;a--)(r=e[a])&&(s=(o<3?r(s):o>3?r(t,n,s):r(t,n))||s);return o>3&&s&&Object.defineProperty(t,n,s),s}w.injectorId=null,w.parentInjectorId=null,w.shadowSlot=!1,w.slotName=null,w.slotFallbackFactory=null,w.contentExpression=null,w.letElement=!1,w.expressions=null,w.expressions=null,w.providers=null,w.viewFactory=null,w.anchorIsContainer=!1,w.elementInstruction=null,w.lifting=!1,w.values=null;var b=/([A-Z])/g;function C(e){return"-"+e.toLowerCase()}function S(e){return(e.charAt(0).toLowerCase()+e.slice(1)).replace(b,C)}function V(e){return!(e.auInterpolationTarget||/[^\t\n\r ]/.test(e.textContent))}var A=function(){function e(e,t,n,i,r){this.taskQueue=e,this.obj=t,this.propertyName=n,this.notqueued=!0,this.publishing=!1,this.selfSubscriber=i,this.currentValue=this.oldValue=r}return e.prototype.getValue=function(){return this.currentValue},e.prototype.setValue=function(e){var t=this.currentValue;Object.is(e,t)||(this.oldValue=t,this.currentValue=e,this.publishing&&this.notqueued&&(this.taskQueue.flushing?this.call():(this.notqueued=!1,this.taskQueue.queueMicroTask(this))))},e.prototype.call=function(){var e=this.oldValue,t=this.currentValue;this.notqueued=!0,Object.is(t,e)||(this.selfSubscriber&&this.selfSubscriber(t,e),this.callSubscribers(t,e),this.oldValue=t)},e.prototype.callSubscribers=function(e,t){throw new Error("Method not implemented.")},e.prototype.subscribe=function(e,t){this.addSubscriber(e,t)},e.prototype.addSubscriber=function(e,t){throw new Error("Method not implemented.")},e.prototype.unsubscribe=function(e,t){this.removeSubscriber(e,t)},e.prototype.removeSubscriber=function(e,t){throw new Error("Method not implemented.")},y([(0,u.rq)()],e)}();function M(e,t){var n=e.__observers__;if(void 0===n){var i=Object.getPrototypeOf(e).constructor,r=o.yu.get(o.yu.resource,i);r.isInitialized||r.initialize(l.mc.instance||new l.mc,e.constructor),n=r.observerLocator.getOrCreateObserversLookup(e),r._ensurePropertiesDefined(e,n)}return n[t]}var N=function(){function e(e){"string"==typeof e?this.name=e:Object.assign(this,e),this.attribute=this.attribute||S(this.name);var t=this.defaultBindingMode;null==t?this.defaultBindingMode=u.BG.oneWay:"string"==typeof t&&(this.defaultBindingMode=u.BG[t]||u.BG.oneWay),this.changeHandler=this.changeHandler||null,this.owner=null,this.descriptor=null}return e.prototype.registerWith=function(e,t,n){if(t.properties.push(this),t.attributes[this.attribute]=this,this.owner=t,n)return this.descriptor=n,this._configureDescriptor(n)},e.prototype._configureDescriptor=function(e){var t=this.name;return e.configurable=!0,e.enumerable=!0,"initializer"in e&&(this.defaultValue=e.initializer,delete e.initializer,delete e.writable),"value"in e&&(this.defaultValue=e.value,delete e.value,delete e.writable),e.get=function(){return M(this,t).getValue()},e.set=function(e){M(this,t).setValue(e)},e.get.getObserver=function(e){return M(e,t)},e},e.prototype.defineOn=function(e,t){var n,i=this.name;null===this.changeHandler&&(n=i+"Changed")in e.prototype&&(this.changeHandler=n),null===this.descriptor&&Object.defineProperty(e.prototype,i,this._configureDescriptor({}))},e.prototype.createObserver=function(e){var t,n=null,i=this.defaultValue,r=this.changeHandler,o=this.name;if(!this.hasOptions){if(r in e)n="propertyChanged"in e?function(t,n){e[r](t,n),e.propertyChanged(o,t,n)}:function(t,n){return e[r](t,n)};else if("propertyChanged"in e)n=function(t,n){return e.propertyChanged(o,t,n)};else if(null!==r)throw new Error("Change handler ".concat(r," was specified but not declared on the class."));return void 0!==i&&(t="function"==typeof i?i.call(e):i),new A(this.owner.taskQueue,e,this.name,n,t)}},e.prototype._initialize=function(e,t,n,i,r){var o,s,a,u=this.defaultValue;if(this.isDynamic)for(var l in n)this._createDynamicProperty(e,t,i,l,n[l],r);else this.hasOptions||(s=t[this.name],null!==n&&(o=s.selfSubscriber,a=n[this.attribute],i&&(s.selfSubscriber=null),"string"==typeof a?(e[this.name]=a,s.call()):a?r.push({observer:s,binding:a.createBinding(e)}):void 0!==u&&s.call(),s.selfSubscriber=o),s.publishing=!0)},e.prototype._createDynamicProperty=function(e,t,n,i,r,o){var s,a,u=i+"Changed",l=null;u in e?l="propertyChanged"in e?function(t,n){e[u](t,n),e.propertyChanged(i,t,n)}:function(t,n){return e[u](t,n)}:"propertyChanged"in e&&(l=function(t,n){return e.propertyChanged(i,t,n)}),s=t[i]=new A(this.owner.taskQueue,e,i,l),Object.defineProperty(e,i,{configurable:!0,enumerable:!0,get:s.getValue.bind(s),set:s.setValue.bind(s)}),n&&(s.selfSubscriber=null),"string"==typeof r?(e[i]=r,s.call()):r&&(a={observer:s,binding:r.createBinding(e)},o.push(a)),s.publishing=!0,s.selfSubscriber=l},e}(),_=function(){function e(){}return e.prototype.getViewStrategy=function(t){if(!t)return null;if("object"==typeof t&&"getViewStrategy"in t){var n=o.$e.get(t.constructor);return"string"==typeof(t=t.getViewStrategy())&&(t=new Se(t)),Ce.assert(t),n.moduleId&&t.makeRelativeTo(n.moduleId),t}if("string"==typeof t&&(t=new Se(t)),Ce.validate(t))return t;if("function"!=typeof t&&(t=t.constructor),"$view"in t){var i,r=t.$view;return i=null===(r="function"==typeof r?r.call(t):r)?new Ae:r instanceof _e?r:new _e(r),o.yu.define(e.viewStrategyMetadataKey,i,t),i}var s=o.$e.get(t),a=o.yu.get(e.viewStrategyMetadataKey,t);if(a)s.moduleId&&(a.moduleId=s.moduleId);else{if(!s.moduleId)throw new Error("Cannot determine default view strategy for object.\n"+t);a=this.createFallbackViewStrategy(s)}return a},e.prototype.createFallbackViewStrategy=function(e){return new Ve(this,e)},e.prototype.convertOriginToViewUrl=function(e){var t=e.moduleId;return(t.endsWith(".js")||t.endsWith(".ts")?t.substring(0,t.length-3):t)+".html"},e.viewStrategyMetadataKey="aurelia:view-strategy",e}();function O(e){throw new Error("BindingLanguage must implement ".concat(e,"()."))}var I=function(){function e(){}return e.prototype.inspectAttribute=function(e,t,n,i){O("inspectAttribute")},e.prototype.createAttributeInstruction=function(e,t,n,i,r){O("createAttributeInstruction")},e.prototype.createLetExpressions=function(e,t){O("createLetExpressions")},e.prototype.inspectTextContent=function(e,t){O("inspectTextContent")},e}(),B=Object.freeze([]),x=function(){function e(e){this.element=e,this.element.auSlotAttribute=this}return e.inject=function(){return[i.dv.Element]},e.prototype.valueChanged=function(e,t){},e}(),T=function(){function e(e,t,n,i){this.anchor=e,this.anchor.viewSlot=this,this.name=t,this.destinationName=n,this.fallbackFactory=i,this.destinationSlot=null,this.projections=0,this.contentView=null,new x(this.anchor).value=this.destinationName}return Object.defineProperty(e.prototype,"needsFallbackRendering",{get:function(){return this.fallbackFactory&&0===this.projections},enumerable:!1,configurable:!0}),e.prototype.renderFallbackContent=function(e,t,n,i){if(null===this.contentView){this.contentView=this.fallbackFactory.create(this.ownerView.container),this.contentView.bind(this.ownerView.bindingContext,this.ownerView.overrideContext);var r=Object.create(null);r[this.destinationSlot.name]=this.destinationSlot,F.distributeView(this.contentView,r,n,i,this.destinationSlot.name)}},e.prototype.passThroughTo=function(e){this.destinationSlot=e},e.prototype.addNode=function(t,n,i,r){null!==this.contentView&&(this.contentView.removeNodes(),this.contentView.detached(),this.contentView.unbind(),this.contentView=null),n.viewSlot instanceof e?n.viewSlot.passThroughTo(this):(this.projections++,this.destinationSlot.addNode(t,n,i,r))},e.prototype.removeView=function(e,t){this.projections--,this.destinationSlot.removeView(e,t),this.needsFallbackRendering&&this.renderFallbackContent(null,B,t)},e.prototype.removeAll=function(e){this.projections=0,this.destinationSlot.removeAll(e),this.needsFallbackRendering&&this.renderFallbackContent(null,B,e)},e.prototype.projectFrom=function(e,t){this.destinationSlot.projectFrom(e,t)},e.prototype.created=function(e){this.ownerView=e},e.prototype.bind=function(e){this.contentView&&this.contentView.bind(e.bindingContext,e.overrideContext)},e.prototype.attached=function(){this.contentView&&this.contentView.attached()},e.prototype.detached=function(){this.contentView&&this.contentView.detached()},e.prototype.unbind=function(){this.contentView&&this.contentView.unbind()},e}(),j=function(){function e(e,t,n){this.anchor=e,this.anchor.isContentProjectionSource=!0,this.anchor.viewSlot=this,this.name=t,this.fallbackFactory=n,this.contentView=null,this.projections=0,this.children=[],this.projectFromAnchors=null,this.destinationSlots=null}return Object.defineProperty(e.prototype,"needsFallbackRendering",{get:function(){return this.fallbackFactory&&0===this.projections},enumerable:!1,configurable:!0}),e.prototype.addNode=function(e,t,n,i,r){var o=t;if(null!==this.contentView&&(this.contentView.removeNodes(),this.contentView.detached(),this.contentView.unbind(),this.contentView=null),o.viewSlot instanceof T)o.viewSlot.passThroughTo(this);else if(null!==this.destinationSlots)F.distributeNodes(e,[o],this.destinationSlots,this,i);else{o.auOwnerView=e,o.auProjectionSource=n,o.auAssignedSlot=this;var s=this._findAnchor(e,o,n,i);s.parentNode.insertBefore(o,s),this.children.push(o),this.projections++}},e.prototype.removeView=function(e,t){if(null!==this.destinationSlots)F.undistributeView(e,this.destinationSlots,this);else if(this.contentView&&this.contentView.hasSlots)F.undistributeView(e,this.contentView.slots,t);else{var n=this.children.find((function(e){return e.auSlotProjectFrom===t}));if(n){for(var i=n.auProjectionChildren,r=this.children,o=0,s=i.length;o<s;++o){var a=i[o];if(a.auOwnerView===e){i.splice(o,1),e.fragment.appendChild(a),o--,s--,this.projections--;var u=r.indexOf(a);u>-1&&r.splice(u,1)}}this.needsFallbackRendering&&this.renderFallbackContent(e,B,t)}}},e.prototype.removeAll=function(e){if(null!==this.destinationSlots)F.undistributeAll(this.destinationSlots,this);else if(this.contentView&&this.contentView.hasSlots)F.undistributeAll(this.contentView.slots,e);else{var t=this.children.find((function(t){return t.auSlotProjectFrom===e}));if(t){for(var n=t.auProjectionChildren,i=this.children,r=0,o=n.length;r<o;++r){var s=n[r];s.auOwnerView.fragment.appendChild(s),this.projections--;var a=i.indexOf(s);a>-1&&i.splice(a,1)}t.auProjectionChildren=[],this.needsFallbackRendering&&this.renderFallbackContent(null,B,e)}}},e.prototype._findAnchor=function(e,t,n,i){if(n){var r=this.children.find((function(e){return e.auSlotProjectFrom===n}));if(r){if(void 0!==i)for(var o=r.auProjectionChildren,s=-1,a=void 0,u=0,l=o.length;u<l;++u){var c=o[u];if(c.auOwnerView!==a&&(s++,a=c.auOwnerView,s>=i&&a!==e))return o.splice(u,0,t),c}return r.auProjectionChildren.push(t),r}}return this.anchor},e.prototype.projectTo=function(e){this.destinationSlots=e},e.prototype.projectFrom=function(e,t){var n=i.dv.createComment("anchor"),r=this.anchor.parentNode;n.auSlotProjectFrom=t,n.auOwnerView=e,n.auProjectionChildren=[],r.insertBefore(n,this.anchor),this.children.push(n),null===this.projectFromAnchors&&(this.projectFromAnchors=[]),this.projectFromAnchors.push(n)},e.prototype.renderFallbackContent=function(e,t,n,i){if(null===this.contentView&&(this.contentView=this.fallbackFactory.create(this.ownerView.container),this.contentView.bind(this.ownerView.bindingContext,this.ownerView.overrideContext),this.contentView.insertNodesBefore(this.anchor)),this.contentView.hasSlots){var r=this.contentView.slots,o=this.projectFromAnchors;if(null!==o)for(var s in r)for(var a=r[s],u=0,l=o.length;u<l;++u){var c=o[u];a.projectFrom(c.auOwnerView,c.auSlotProjectFrom)}this.fallbackSlots=r,F.distributeNodes(e,t,r,n,i)}},e.prototype.created=function(e){this.ownerView=e},e.prototype.bind=function(e){this.contentView&&this.contentView.bind(e.bindingContext,e.overrideContext)},e.prototype.attached=function(){this.contentView&&this.contentView.attached()},e.prototype.detached=function(){this.contentView&&this.contentView.detached()},e.prototype.unbind=function(){this.contentView&&this.contentView.unbind()},e}(),F=function(){function e(){}return e.getSlotName=function(t){return void 0===t.auSlotAttribute?e.defaultSlotKey:t.auSlotAttribute.value},e.distributeView=function(t,n,i,r,o){var s;if(null===t)s=B;else{var a=t.fragment.childNodes,u=a.length;s=new Array(u);for(var l=0;l<u;++l)s[l]=a[l]}e.distributeNodes(t,s,n,i,r,o)},e.undistributeView=function(e,t,n){for(var i in t)t[i].removeView(e,n)},e.undistributeAll=function(e,t){for(var n in e)e[n].removeAll(t)},e.distributeNodes=function(t,n,i,r,o,s){for(var a=0,u=n.length;a<u;++a){var l=n[a],c=l.nodeType;if(l.isContentProjectionSource){for(var h in l.viewSlot.projectTo(i),i)i[h].projectFrom(t,l.viewSlot);n.splice(a,1),u--,a--}else if(1===c||3===c||l.viewSlot instanceof T)if(3===c&&V(l))n.splice(a,1),u--,a--;else{var d=i[s||e.getSlotName(l)];d&&(d.addNode(t,l,r,o),n.splice(a,1),u--,a--)}else n.splice(a,1),u--,a--}for(var h in i){var p=i[h];p.needsFallbackRendering&&p.renderFallbackContent(t,n,r,o)}},e.defaultSlotKey="__au-default-slot-key__",e}(),P=function(){function e(e){this.owner=e,this.owner._compositionCount++}return e.prototype.done=function(){this.owner._compositionCount--,this.owner._tryCompleteTransaction()},e}(),k=function(){function e(e){this.owner=e,this.owner._ownershipToken=this,this.thenable=this._createThenable()}return e.prototype.waitForCompositionComplete=function(){return this.owner._tryCompleteTransaction(),this.thenable},e.prototype.resolve=function(){this._resolveCallback()},e.prototype._resolveCallback=function(){throw new Error("Method not implemented.")},e.prototype._createThenable=function(){var e=this;return new Promise((function(t){e._resolveCallback=t}))},e}(),E=function(){function e(){this._ownershipToken=null,this._compositionCount=0}return e.prototype.tryCapture=function(){return null===this._ownershipToken?new k(this):null},e.prototype.enlist=function(){return new P(this)},e.prototype._tryCompleteTransaction=function(){if(this._compositionCount<=0&&(this._compositionCount=0,null!==this._ownershipToken)){var e=this._ownershipToken;this._ownershipToken=null,e.resolve()}},e}(),R=function(){function e(e,t,n,i,r,o,s){for(var a in this.container=e,this.viewFactory=t,this.resources=t.resources,this.fragment=n,this.firstChild=n.firstChild,this.lastChild=n.lastChild,this.controllers=i,this.bindings=r,this.children=o,this.slots=s,this.hasSlots=!1,this.fromCache=!1,this.isBound=!1,this.isAttached=!1,this.bindingContext=null,this.overrideContext=null,this.controller=null,this.viewModelScope=null,this.animatableElement=void 0,this._isUserControlled=!1,this.contentView=null,s){this.hasSlots=!0;break}}return e.prototype.returnToCache=function(){this.viewFactory.returnViewToCache(this)},e.prototype.created=function(){var e,t,n=this.controllers;for(e=0,t=n.length;e<t;++e)n[e].created(this)},e.prototype.bind=function(e,t,n){var i,r,o,s,a;if(!n||!this._isUserControlled){if(this.isBound){if(this.bindingContext===e)return;this.unbind()}for(this.isBound=!0,this.bindingContext=e,this.overrideContext=t||(0,u.iI)(e),this.resources._invokeHook("beforeBind",this),s=0,a=(r=this.bindings).length;s<a;++s)r[s].bind(this);for(null!==this.viewModelScope&&(e.bind(this.viewModelScope.bindingContext,this.viewModelScope.overrideContext),this.viewModelScope=null),s=0,a=(i=this.controllers).length;s<a;++s)i[s].bind(this);for(s=0,a=(o=this.children).length;s<a;++s)o[s].bind(e,t,!0);this.hasSlots&&F.distributeView(this.contentView,this.slots)}},e.prototype.addBinding=function(e){this.bindings.push(e),this.isBound&&e.bind(this)},e.prototype.unbind=function(){var e,t,n,i,r;if(this.isBound){for(this.isBound=!1,this.resources._invokeHook("beforeUnbind",this),null!==this.controller&&this.controller.unbind(),i=0,r=(t=this.bindings).length;i<r;++i)t[i].unbind();for(i=0,r=(e=this.controllers).length;i<r;++i)e[i].unbind();for(i=0,r=(n=this.children).length;i<r;++i)n[i].unbind();this.bindingContext=null,this.overrideContext=null}},e.prototype.insertNodesBefore=function(e){e.parentNode.insertBefore(this.fragment,e)},e.prototype.appendNodesTo=function(e){e.appendChild(this.fragment)},e.prototype.removeNodes=function(){for(var e,t=this.fragment,n=this.firstChild,i=this.lastChild;n&&(e=n.nextSibling,t.appendChild(n),n!==i);)n=e},e.prototype.attached=function(){var e,t,n,i;if(!this.isAttached){for(this.isAttached=!0,null!==this.controller&&this.controller.attached(),n=0,i=(e=this.controllers).length;n<i;++n)e[n].attached();for(n=0,i=(t=this.children).length;n<i;++n)t[n].attached()}},e.prototype.detached=function(){var e,t,n,i;if(this.isAttached){for(this.isAttached=!1,null!==this.controller&&this.controller.detached(),n=0,i=(e=this.controllers).length;n<i;++n)e[n].detached();for(n=0,i=(t=this.children).length;n<i;++n)t[n].detached()}},e}(),D=function(){function e(){}return e.prototype.enter=function(e){return Promise.resolve(!1)},e.prototype.leave=function(e){return Promise.resolve(!1)},e.prototype.removeClass=function(e,t){return e.classList.remove(t),Promise.resolve(!1)},e.prototype.addClass=function(e,t){return e.classList.add(t),Promise.resolve(!1)},e.prototype.animate=function(e,t){return Promise.resolve(!1)},e.prototype.runSequence=function(e){return Promise.resolve(!1)},e.prototype.registerEffect=function(e,t){},e.prototype.unregisterEffect=function(e){},e}(),L=function(){function e(e,t,n){void 0===n&&(n=D.instance),this.anchor=e,this.anchorIsContainer=t,this.bindingContext=null,this.overrideContext=null,this.animator=n,this.children=[],this.isBound=!1,this.isAttached=!1,this.contentSelectors=null,e.viewSlot=this,e.isContentProjectionSource=!1}return e.prototype.animateView=function(e,t){void 0===t&&(t="enter");var n=function(e){if(void 0!==e.animatableElement)return e.animatableElement;for(var t=e.firstChild;t&&1!==t.nodeType;)t=t.nextSibling;return t&&1===t.nodeType?e.animatableElement=t.classList.contains("au-animate")?t:null:e.animatableElement=null}(e);if(null!==n)switch(t){case"enter":return this.animator.enter(n);case"leave":return this.animator.leave(n);default:throw new Error("Invalid animation direction: "+t)}},e.prototype.transformChildNodesIntoView=function(){var e=this.anchor;this.children.push({fragment:e,firstChild:e.firstChild,lastChild:e.lastChild,returnToCache:function(){},removeNodes:function(){for(var t;t=e.lastChild;)e.removeChild(t)},created:function(){},bind:function(){},unbind:function(){},attached:function(){},detached:function(){}})},e.prototype.bind=function(e,t){var n,i,r;if(this.isBound){if(this.bindingContext===e)return;this.unbind()}for(this.isBound=!0,this.bindingContext=e=e||this.bindingContext,this.overrideContext=t=t||this.overrideContext,n=0,i=(r=this.children).length;n<i;++n)r[n].bind(e,t,!0)},e.prototype.unbind=function(){if(this.isBound){var e,t=void 0,n=this.children;for(this.isBound=!1,this.bindingContext=null,this.overrideContext=null,t=0,e=n.length;t<e;++t)n[t].unbind()}},e.prototype.add=function(e){if(this.anchorIsContainer?e.appendNodesTo(this.anchor):e.insertNodesBefore(this.anchor),this.children.push(e),this.isAttached)return e.attached(),this.animateView(e,"enter")},e.prototype.insert=function(e,t){var n=this.children,i=n.length;return 0===e&&0===i||e>=i?this.add(t):(t.insertNodesBefore(n[e].firstChild),n.splice(e,0,t),this.isAttached?(t.attached(),this.animateView(t,"enter")):void 0)},e.prototype.move=function(e,t){if(e!==t){var n=this.children,i=n[e];i.removeNodes(),i.insertNodesBefore(n[t].firstChild),n.splice(e,1),n.splice(t,0,i)}},e.prototype.remove=function(e,t,n){return this.removeAt(this.children.indexOf(e),t,n)},e.prototype.removeMany=function(e,t,n){var i,r=this,o=this.children,s=e.length,a=[];e.forEach((function(e){if(n)e.removeNodes();else{var t=r.animateView(e,"leave");t?a.push(t.then((function(){return e.removeNodes()}))):e.removeNodes()}}));var u=function(){if(r.isAttached)for(i=0;i<s;++i)e[i].detached();if(t)for(i=0;i<s;++i)e[i].returnToCache();for(i=0;i<s;++i){var n=o.indexOf(e[i]);n>=0&&o.splice(n,1)}};return a.length>0?Promise.all(a).then((function(){return u()})):u()},e.prototype.removeAt=function(e,t,n){var i=this,r=this.children[e],o=function(){return e=i.children.indexOf(r),r.removeNodes(),i.children.splice(e,1),i.isAttached&&r.detached(),t&&r.returnToCache(),r};if(!n){var s=this.animateView(r,"leave");if(s)return s.then((function(){return o()}))}return o()},e.prototype.removeAll=function(e,t){var n,i=this,r=this.children,o=r.length,s=[];r.forEach((function(e){if(t)e.removeNodes();else{var n=i.animateView(e,"leave");n?s.push(n.then((function(){return e.removeNodes()}))):e.removeNodes()}}));var a=function(){if(i.isAttached)for(n=0;n<o;++n)r[n].detached();if(e)for(n=0;n<o;++n){var t=r[n];t&&t.returnToCache()}i.children=[]};return s.length>0?Promise.all(s).then((function(){return a()})):a()},e.prototype.attached=function(){var e,t,n,i;if(!this.isAttached)for(this.isAttached=!0,e=0,t=(n=this.children).length;e<t;++e)(i=n[e]).attached(),this.animateView(i,"enter")},e.prototype.detached=function(){var e,t,n;if(this.isAttached)for(this.isAttached=!1,e=0,t=(n=this.children).length;e<t;++e)n[e].detached()},e.prototype.projectTo=function(e){var t=this;this.projectToSlots=e,this.add=this._projectionAdd,this.insert=this._projectionInsert,this.move=this._projectionMove,this.remove=this._projectionRemove,this.removeAt=this._projectionRemoveAt,this.removeMany=this._projectionRemoveMany,this.removeAll=this._projectionRemoveAll,this.children.forEach((function(n){return F.distributeView(n,e,t)}))},e.prototype._projectionAdd=function(e){F.distributeView(e,this.projectToSlots,this),this.children.push(e),this.isAttached&&e.attached()},e.prototype._projectionInsert=function(e,t){0===e&&!this.children.length||e>=this.children.length?this.add(t):(F.distributeView(t,this.projectToSlots,this,e),this.children.splice(e,0,t),this.isAttached&&t.attached())},e.prototype._projectionMove=function(e,t){if(e!==t){var n=this.children,i=n[e];F.undistributeView(i,this.projectToSlots,this),F.distributeView(i,this.projectToSlots,this,t),n.splice(e,1),n.splice(t,0,i)}},e.prototype._projectionRemove=function(e,t){F.undistributeView(e,this.projectToSlots,this),this.children.splice(this.children.indexOf(e),1),this.isAttached&&e.detached(),t&&e.returnToCache()},e.prototype._projectionRemoveAt=function(e,t){var n=this.children[e];F.undistributeView(n,this.projectToSlots,this),this.children.splice(e,1),this.isAttached&&n.detached(),t&&n.returnToCache()},e.prototype._projectionRemoveMany=function(e,t){var n=this;e.forEach((function(e){return n.remove(e,t)}))},e.prototype._projectionRemoveAll=function(e){F.undistributeAll(this.projectToSlots,this);for(var t=this.children,n=t.length,i=0;i<n;++i)e?t[i].returnToCache():this.isAttached&&t[i].detached();this.children=[]},e}(),H=l.L2,z=new(function(){function e(){}return e.prototype.get=function(e,t){var n=t.__providerId__;return n in e?e[n]:e[n]=e.invoke(t)},y([H],e)}());function U(e){if(e===i.dv.Element)return this.element;if(e===G){if(this.boundViewFactory)return this.boundViewFactory;var t=this.instruction.viewFactory,n=this.partReplacements;return n&&(t=n[t.part]||t),this.boundViewFactory=new G(this,t,n),this.boundViewFactory}return e===L?(void 0===this.viewSlot&&(this.viewSlot=new L(this.element,this.instruction.anchorIsContainer),this.element.isContentProjectionSource=this.instruction.lifting,this.children.push(this.viewSlot)),this.viewSlot):e===h?this.elementEvents||(this.elementEvents=new h(this.element)):e===E?this.compositionTransaction||(this.compositionTransaction=this.parent.get(e)):e===be?this.viewResources:e===g?this.instruction:this.superGet(e)}function q(e){return this._element.hasAttribute(e)}function W(e){return this._element.getAttribute(e)}function $(e,t){this._element.setAttribute(e,t)}function K(e,t,n,r,o,s,a,u,l){var c,h,d,p,f,v=n.behaviorInstructions,m=n.expressions;if(n.contentExpression)return o.push(n.contentExpression.createBinding(t.nextSibling)),t.nextSibling.auInterpolationTarget=!0,void t.parentNode.removeChild(t);if(n.shadowSlot){var g,w=i.dv.createComment("slot");return g=n.slotDestination?new T(w,n.slotName,n.slotDestination,n.slotFallbackFactory):new j(w,n.slotName,n.slotFallbackFactory),i.dv.replaceNode(w,t),a[n.slotName]=g,void r.push(g)}if(n.letElement){for(h=0,d=m.length;h<d;++h)o.push(m[h].createBinding());t.parentNode.removeChild(t)}else{if(v.length)for(n.anchorIsContainer||(t=function(e,t){var n=i.dv.createComment("anchor");if(t){var r=e.firstChild;r&&"AU-CONTENT"===r.tagName&&(n.contentElement=r),n._element=e,n.hasAttribute=q,n.getAttribute=W,n.setAttribute=$}return i.dv.replaceNode(n,e),n}(t,n.elementInstruction)),e[n.injectorId]=c=function(e,t,n,i,r,o){var s,a,u=e.createChild();for(u.element=t,u.instruction=n,u.children=i,u.viewResources=o,u.partReplacements=r,a=(s=n.providers).length;a--;)u._resolvers.set(s[a],z);return u.superGet=u.get,u.get=U,u}(e[n.parentInjectorId],t,n,s,u,l),h=0,d=v.length;h<d;++h)f=(p=v[h]).type.create(c,p,t,o),r.push(f);for(h=0,d=m.length;h<d;++h)o.push(m[h].createBinding(t))}}function Q(e,t){var n,i,r,o,s,a=e.split(";");for(t=t||{},i=0;i<a.length;i++)n=(r=a[i]).indexOf(":"),o=r.substring(0,n).trim(),s=r.substring(n+1).trim(),t[o]=s;return t}function Y(e){var t="";for(var n in e)t+=n+":"+e[n]+";";return t}var G=function(){function e(e,t,n){this.parentContainer=e,this.viewFactory=t,this.factoryCreateInstruction={partReplacements:n}}return e.prototype.create=function(){var e=this.viewFactory.create(this.parentContainer.createChild(),this.factoryCreateInstruction);return e._isUserControlled=!0,e},Object.defineProperty(e.prototype,"isCaching",{get:function(){return this.viewFactory.isCaching},enumerable:!1,configurable:!0}),e.prototype.setCacheSize=function(e,t){this.viewFactory.setCacheSize(e,t)},e.prototype.getCachedView=function(){return this.viewFactory.getCachedView()},e.prototype.returnViewToCache=function(e){this.viewFactory.returnViewToCache(e)},e}(),Z=function(){function e(e,t,n){this.isCaching=!1,this.template=e,this.instructions=t,this.resources=n,this.cacheSize=-1,this.cache=null}return e.prototype.setCacheSize=function(e,t){e&&("*"===e?e=Number.MAX_VALUE:"string"==typeof e&&(e=parseInt(e,10))),-1!==this.cacheSize&&t||(this.cacheSize=Number(e)),this.cacheSize>0?this.cache=[]:this.cache=null,this.isCaching=this.cacheSize>0},e.prototype.getCachedView=function(){return null!==this.cache&&this.cache.pop()||null},e.prototype.returnViewToCache=function(e){e.isAttached&&e.detached(),e.isBound&&e.unbind(),null!==this.cache&&this.cache.length<this.cacheSize&&(e.fromCache=!0,this.cache.push(e))},e.prototype.create=function(e,t,n){t=t||v.normal;var i=this.getCachedView();if(null!==i)return i;var r,o,s,a,u,l=t.enhance?this.template:this.template.cloneNode(!0),c=l.querySelectorAll(".au-target"),h=this.instructions,d=this.resources,p=[],f=[],m=[],g=Object.create(null),w={root:e},y=t.partReplacements;for(this.resources._invokeHook("beforeCreate",this,e,l,t),n&&null!==this.surrogateInstruction&&function(e,t,n,i,r,o){var s,a,u,l,c,h=n.behaviorInstructions,d=n.expressions,p=n.providers,f=n.values;for(s=p.length;s--;)e._resolvers.set(p[s],z);for(var v in f)if(c=t.getAttribute(v)){if("class"===v)t.setAttribute("class",c+" "+f[v]);else if("style"===v){var m=Q(f[v]);Q(c,m),t.setAttribute("style",Y(m))}}else t.setAttribute(v,f[v]);if(h.length)for(s=0,a=h.length;s<a;++s)(l=(u=h[s]).type.create(e,u,t,r)).contentView&&o.push(l.contentView),i.push(l);for(s=0,a=d.length;s<a;++s)r.push(d[s].createBinding(t))}(e,n,this.surrogateInstruction,p,f,m),t.enhance&&l.hasAttribute("au-target-id")&&(u=h[(a=l).getAttribute("au-target-id")],K(w,a,u,p,f,m,g,y,d)),r=0,o=c.length;r<o;++r)u=h[(a=c[r]).getAttribute("au-target-id")],K(w,a,u,p,f,m,g,y,d);return s=new R(e,this,l,p,f,m,g),t.initiatedByBehavior||s.created(),this.resources._invokeHook("afterCreate",s),s},e}(),J=0,X=0;function ee(e){var t=e.getAttribute("class"),n=(++X).toString();return e.setAttribute("class",t?t+" au-target":"au-target"),e.setAttribute("au-target-id",n),n}var te=I.prototype.createLetExpressions,ne=function(){function e(e,t){this.bindingLanguage=e,this.resources=t}return e.inject=function(){return[I,be]},e.prototype.compile=function(e,t,n){var r,o,s;t=t||this.resources,n=n||f.normal,(e="string"==typeof e?i.dv.createTemplateFromMarkup(e):e).content?(o=e.getAttribute("part"),s=e.getAttribute("view-cache"),r=i.dv.adoptNode(e.content)):r=e,n.targetShadowDOM=n.targetShadowDOM&&i.RI.shadowDOM,t._invokeHook("beforeCompile",r,t,n);var a={};this._compileNode(r,t,a,e,"root",!n.targetShadowDOM);var u=r.firstChild;if(u&&1===u.nodeType){var l=u.getAttribute("au-target-id");if(l){var c=a[l];(c.shadowSlot||c.lifting||c.elementInstruction&&!c.elementInstruction.anchorIsContainer)&&r.insertBefore(i.dv.createComment("view"),u)}}var h=new Z(r,a,t);return h.surrogateInstruction=n.compileSurrogate?this._compileSurrogate(e,t):null,h.part=o,s&&h.setCacheSize(s),t._invokeHook("afterCompile",h),h},e.prototype._compileNode=function(e,t,n,r,o,s){switch(e.nodeType){case 1:return this._compileElement(e,t,n,r,o,s);case 3:var a=t.getBindingLanguage(this.bindingLanguage).inspectTextContent(t,e.wholeText);if(a){var u=i.dv.createElement("au-marker"),l=ee(u);for((e.parentNode||r).insertBefore(u,e),e.textContent=" ",n[l]=g.contentExpression(a);e.nextSibling&&3===e.nextSibling.nodeType;)(e.parentNode||r).removeChild(e.nextSibling)}else for(;e.nextSibling&&3===e.nextSibling.nodeType;)e=e.nextSibling;return e.nextSibling;case 11:for(var c=e.firstChild;c;)c=this._compileNode(c,t,n,e,o,s)}return e.nextSibling},e.prototype._compileSurrogate=function(e,t){var n,i,r,o,s,a,u,l,c,h,d,p=e.tagName.toLowerCase(),f=e.attributes,m=t.getBindingLanguage(this.bindingLanguage),w=[],y=[],b={},C=!1,S=[];for(o=0,s=f.length;o<s;++o){if(u=(a=f[o]).name,l=a.value,c=m.inspectAttribute(t,p,u,l),(h=t.getAttribute(c.attrName))&&(n=t.mapAttribute(c.attrName))&&(i=h.attributes[n])&&(c.defaultBindingMode=i.defaultBindingMode,c.command||c.expression||(c.command=i.hasOptions?"options":null),c.command&&"options"!==c.command&&h.primaryProperty)){var V=h.primaryProperty;u=c.attrName=V.attribute,c.defaultBindingMode=V.defaultBindingMode}if(r=m.createAttributeInstruction(t,e,c,void 0,h))if(r.alteredAttr&&(h=t.getAttribute(r.attrName)),r.discrete)w.push(r);else if(h){if(r.type=h,this._configureProperties(r,t),h.liftsContent)throw new Error("You cannot place a template controller on a surrogate element.");y.push(r)}else w.push(r.attributes[r.attrName]);else if(h){if((r=v.attribute(u,h)).attributes[t.mapAttribute(u)]=l,h.liftsContent)throw new Error("You cannot place a template controller on a surrogate element.");y.push(r)}else"id"!==u&&"part"!==u&&"replace-part"!==u&&(C=!0,b[u]=l)}if(w.length||y.length||C){for(o=0,s=y.length;o<s;++o)(r=y[o]).type.compile(this,t,e,r),S.push(r.type.target);for(o=0,s=w.length;o<s;++o)void 0!==(d=w[o]).attrToRemove&&e.removeAttribute(d.attrToRemove);return g.surrogate(S,y,w,b)}return null},e.prototype._compileElement=function(e,t,n,r,o,s){var a,u,l,c,h,d,p,f,m,w,y,b,C,S,V,A,M,N=e.tagName.toLowerCase(),_=e.attributes,O=[],I=[],B=[],x=t.getBindingLanguage(this.bindingLanguage);if("slot"===N)return s&&(e=function(e,t,n,r,o){var s=i.dv.createElement("au-shadow-slot");i.dv.replaceNode(s,n);var a=ee(s),u=g.shadowSlot(o);if(u.slotName=n.getAttribute("name")||F.defaultSlotKey,u.slotDestination=n.getAttribute("slot"),n.innerHTML.trim()){for(var l=i.dv.createDocumentFragment(),c=void 0;c=n.firstChild;)l.appendChild(c);u.slotFallbackFactory=e.compile(l,t)}return r[a]=u,s}(this,t,e,n,o)),e.nextSibling;if("template"===N){if(!("content"in e))throw new Error("You cannot place a template element within "+e.namespaceURI+" namespace");(l=this.compile(e,t)).part=e.getAttribute("part")}else{if(c=t.getElement(e.getAttribute("as-element")||N),"let"===N&&!c&&x.createLetExpressions!==te)return O=x.createLetExpressions(t,e),n[ee(e)]=g.letElement(O),e.nextSibling;c&&(h=v.element(e,c),c.processAttributes(this,t,e,_,h),I.push(h))}for(p=0,f=_.length;p<f;++p){if(b=w=(m=_[p]).name,y=m.value,S=x.inspectAttribute(t,N,w,y),s&&"slot"===S.attrName&&(S.attrName=w="au-slot"),d=null,c=t.getAttribute(S.attrName)){if((A=t.mapAttribute(S.attrName))&&(V=c.attributes[A])&&(S.defaultBindingMode=V.defaultBindingMode,S.command||S.expression||(S.command=V.hasOptions?"options":null),S.command&&"options"!==S.command&&c.primaryProperty)){var T=c.primaryProperty;w=S.attrName=T.attribute,S.defaultBindingMode=T.defaultBindingMode}}else h&&(d=h.type.attributes[S.attrName])&&(S.defaultBindingMode=d.defaultBindingMode);if(C=d?x.createAttributeInstruction(t,e,S,h):x.createAttributeInstruction(t,e,S,void 0,c))if(C.alteredAttr&&(c=t.getAttribute(C.attrName)),C.discrete)O.push(C);else if(c){if(C.type=c,this._configureProperties(C,t),c.liftsContent){C.originalAttrName=b,u=C;break}I.push(C)}else d?h.attributes[S.attrName].targetProperty=d.name:O.push(C.attributes[C.attrName]);else if(c){if((C=v.attribute(w,c)).attributes[t.mapAttribute(w)]=y,c.liftsContent){C.originalAttrName=b,u=C;break}I.push(C)}else d&&(h.attributes[w]=y)}if(u)u.viewFactory=l,n[ee(e=u.type.compile(this,t,e,u,r))]=g.lifting(o,u);else{var j=!1;if(O.length||I.length){for(M=!!I.length&&++J,p=0,f=I.length;p<f;++p)(C=I[p]).type.compile(this,t,e,C,r),B.push(C.type.target),j=j||C.skipContentProcessing;for(p=0,f=O.length;p<f;++p)void 0!==(a=O[p]).attrToRemove&&e.removeAttribute(a.attrToRemove);n[ee(e)]=g.normal(M,o,B,I,O,h)}if(j)return e.nextSibling;for(var P=e.firstChild;P;)P=this._compileNode(P,t,n,e,M||o,s)}return e.nextSibling},e.prototype._configureProperties=function(e,t){var n,i,r,o=e.type,s=e.attrName,a=e.attributes,u=t.mapAttribute(s);for(i in u&&s in a&&u!==s&&(a[u]=a[s],delete a[s]),a)null!==(r=a[i])&&"object"==typeof r&&(n=o.attributes[i],r.targetProperty=void 0!==n?n.name:i)},e}(),ie=function(){function e(){}return e.prototype.initialize=function(e,t){this.instance=e.get(t)},e.prototype.register=function(e,t){e.registerViewEngineHooks(this.instance)},e.prototype.load=function(e,t){},e.convention=function(t){if(t.endsWith("ViewEngineHooks"))return new e},e}();function re(e){var t=function(e){o.yu.define(o.yu.resource,new ie,e)};return e?t(e):t}var oe=function(){function e(e){this.id=e,this.moduleInstance=null,this.mainResource=null,this.resources=null,this.viewStrategy=null,this.isInitialized=!1,this.onLoaded=null,this.loadContext=null}return e.prototype.initialize=function(e){var t=this.mainResource,n=this.resources,i=this.viewStrategy;if(!this.isInitialized){this.isInitialized=!0,void 0!==t&&(t.metadata.viewStrategy=i,t.initialize(e));for(var r=0,o=n.length;r<o;++r)(t=n[r]).metadata.viewStrategy=i,t.initialize(e)}},e.prototype.register=function(e,t){var n=this.mainResource,i=this.resources;void 0!==n&&(n.register(e,t),t=null);for(var r=0,o=i.length;r<o;++r)i[r].register(e,t),t=null},e.prototype.load=function(e,t){if(null!==this.onLoaded)return this.loadContext===t?Promise.resolve():this.onLoaded;var n,i=this.mainResource,r=this.resources;if(void 0!==i){(n=new Array(r.length+1))[0]=i.load(e,t);for(var o=0,s=r.length;o<s;++o)n[o+1]=r[o].load(e,t)}else for(n=new Array(r.length),o=0,s=r.length;o<s;++o)n[o]=r[o].load(e,t);return this.loadContext=t,this.onLoaded=Promise.all(n),this.onLoaded},e}(),se=function(){function e(e,t,n){n||(n=o.yu.get(o.yu.resource,t))||((n=new me).elementName=S(e),o.yu.define(o.yu.resource,n,t)),n instanceof me?void 0===n.elementName?n.elementName=S(e):void 0===n.attributeName?n.attributeName=S(e):null===n.attributeName&&null===n.elementName&&me.convention(e,n):n.name||(n.name=S(e)),this.metadata=n,this.value=t}return e.prototype.initialize=function(e){this.metadata.initialize(e,this.value)},e.prototype.register=function(e,t){this.metadata.register(e,t)},e.prototype.load=function(e,t){return this.metadata.load(e,this.value,t)},e}(),ae=function(){function e(){this.cache=Object.create(null)}return e.prototype.getAnalysis=function(e){return this.cache[e]},e.prototype.analyze=function(e,t,n){var i,s,a,l,c,h,d,p,f,v=[];if(f=this.cache[e])return f;for(c in f=new oe(e),this.cache[e]=f,"function"==typeof t&&(t={default:t}),n&&(i=new se(n,t[n])),t)h=t[c],c!==n&&"function"==typeof h&&((l=o.yu.get(o.yu.resource,h))?(l instanceof me&&(be.convention(h,l),null===l.attributeName&&null===l.elementName&&me.convention(c,l),null===l.attributeName&&null===l.elementName&&(l.elementName=S(c))),!i&&l instanceof me&&null!==l.elementName?i=new se(c,h,l):v.push(new se(c,h,l))):Ce.decorates(h)?p=h:h instanceof r.sT?p=new Me(e,h):(d=be.convention(h))||(d=me.convention(c))?(null===d.elementName||i?v.push(new se(c,h,d)):i=new se(c,h,d),o.yu.define(o.yu.resource,d,h)):(d=u.MC.convention(c)||u.w7.convention(c)||ie.convention(c))?(v.push(new se(c,h,d)),o.yu.define(o.yu.resource,d,h)):s||(s=h,a=c));return!i&&s&&(i=new se(a,s)),f.moduleInstance=t,f.mainResource=i,f.resources=v,f.viewStrategy=p,f},e}(),ue=a.getLogger("templating"),le=function(){function e(e){var t=this;e.then((function(e){return t.viewFactory=e}))}return e.prototype.create=function(e,t,n,i){return this.viewFactory.create(e,t,n,i)},Object.defineProperty(e.prototype,"isCaching",{get:function(){return this.viewFactory.isCaching},enumerable:!1,configurable:!0}),e.prototype.setCacheSize=function(e,t){this.viewFactory.setCacheSize(e,t)},e.prototype.getCachedView=function(){return this.viewFactory.getCachedView()},e.prototype.returnViewToCache=function(e){this.viewFactory.returnViewToCache(e)},e}(),ce=null,he=function(){function e(e,t,n,i,r){this.loader=e,this.container=t,this.viewCompiler=n,this.moduleAnalyzer=i,this.appResources=r,this._pluginMap={},null===ce&&((ce=new me).attributeName="au-slot",o.yu.define(o.yu.resource,ce,x)),ce.initialize(t,x),ce.register(r)}return e.inject=function(){return[r.aH,l.mc,ne,ae,be]},e.prototype.addResourcePlugin=function(e,t){var n=e.replace(".","")+"-resource-plugin";this._pluginMap[e]=n,this.loader.addPlugin(n,t)},e.prototype.loadViewFactory=function(e,t,n,i){var o=this;return n=n||new p,function(e,t){return t instanceof r.sT?Promise.resolve(t):e.loadTemplate(t)}(this.loader,e).then((function(e){var r=e.address;return e.onReady?n.hasDependency(r)?null===e.template?e.onReady:Promise.resolve(new le(e.onReady)):(n.addDependency(r),e.onReady):(n.addDependency(r),e.onReady=o.loadTemplateResources(e,t,n,i).then((function(n){if(e.resources=n,null===e.template)return e.factory=null;var i=o.viewCompiler.compile(e.template,n,t);return e.factory=i})),e.onReady)}))},e.prototype.loadTemplateResources=function(t,n,i,r){var a,u,l=new be(this.appResources,t.address),c=t.dependencies;if(n=n||f.normal,0===c.length&&!n.associatedModuleId)return Promise.resolve(l);if(a=c.map((function(e){return e.src})),u=c.map((function(e){return e.name})),ue.debug("importing resources for ".concat(t.address),a),r){var h=o.yu.get(e.viewModelRequireMetadataKey,r);if(h){for(var d=a.length,p=0,v=h.length;p<v;++p){var m=h[p],g="function"==typeof m?o.$e.get(m).moduleId:(0,s.Yc)(m.src||m,t.address);-1===a.indexOf(g)&&(a.push(g),u.push(m.as))}ue.debug("importing ViewModel resources for ".concat(n.associatedModuleId),a.slice(d))}}return this.importViewResources(a,u,l,n,i)},e.prototype.importViewModelResource=function(e,t){var n=this;return this.loader.loadModule(e).then((function(i){var r=o.$e.get(i).moduleId,s=n.moduleAnalyzer.analyze(r,i,t);if(!s.mainResource)throw new Error('No view model found in module "'.concat(e,'".'));return s.initialize(n.container),s.mainResource}))},e.prototype.importViewResources=function(e,t,n,i,r){var s=this;return r=r||new p,i=i||f.normal,e=e.map((function(e){return s._applyLoaderPlugin(e)})),this.loader.loadAllModules(e).then((function(e){var a,u,l,c,h,d,p=s.container,f=s.moduleAnalyzer,v=new Array(e.length);for(a=0,u=e.length;a<u;++a)h=e[a],c=o.$e.get(h).moduleId,(l=f.analyze(c,h)).initialize(p),l.register(n,t[a]),v[a]=l;for(i.associatedModuleId&&(d=f.getAnalysis(i.associatedModuleId))&&d.register(n),a=0,u=v.length;a<u;++a)v[a]=v[a].load(p,r);return Promise.all(v).then((function(){return n}))}))},e.prototype._applyLoaderPlugin=function(e){var t=e.lastIndexOf(".");if(-1!==t){var n=e.substring(t),i=this._pluginMap[n];return void 0===i?e:this.loader.applyPluginToUrl(e,i)}return e},e.viewModelRequireMetadataKey="aurelia:view-model-require",e}(),de=function(){function e(e,t,n,i){this.behavior=e,this.instruction=t,this.viewModel=n,this.isAttached=!1,this.view=null,this.isBound=!1,this.scope=null,this.container=i,this.elementEvents=i.elementEvents||null;var r,o,s=e.observerLocator.getOrCreateObserversLookup(n),a=e.handlesBind,u=t.attributes,l=this.boundProperties=[],c=e.properties;for(e._ensurePropertiesDefined(n,s),r=0,o=c.length;r<o;++r)c[r]._initialize(n,s,u,a,l)}return e.prototype.created=function(e){this.behavior.handlesCreated&&this.viewModel.created(e,this.view)},e.prototype.automate=function(e,t){this.view.bindingContext=this.viewModel,this.view.overrideContext=e||(0,u.iI)(this.viewModel),this.view._isUserControlled=!0,this.behavior.handlesCreated&&this.viewModel.created(t||null,this.view),this.bind(this.view)},e.prototype.bind=function(e){var t,n,i,r,o,s,a=this.behavior.handlesBind,l=this.boundProperties;if(this.isBound){if(this.scope===e)return;this.unbind()}for(this.isBound=!0,this.scope=e,t=0,n=l.length;t<n;++t)o=(r=(i=l[t]).observer).selfSubscriber,r.publishing=!1,a&&(r.selfSubscriber=null),i.binding.bind(e),r.call(),r.publishing=!0,r.selfSubscriber=o;null!==this.view?(a&&(this.view.viewModelScope=e),this.viewModel===e.overrideContext.bindingContext?s=e.overrideContext:this.instruction.inheritBindingContext?s=(0,u.iI)(this.viewModel,e.overrideContext):(s=(0,u.iI)(this.viewModel)).__parentOverrideContext=e.overrideContext,this.view.bind(this.viewModel,s)):a&&(s=e.overrideContext,void 0!==e.overrideContext.__parentOverrideContext&&this.viewModel.viewFactory&&this.viewModel.viewFactory.factoryCreateInstruction.partReplacements&&((s=Object.assign({},e.overrideContext)).parentOverrideContext=e.overrideContext.__parentOverrideContext),this.viewModel.bind(e.bindingContext,s))},e.prototype.unbind=function(){if(this.isBound){var e,t=this.boundProperties,n=void 0;for(this.isBound=!1,this.scope=null,null!==this.view&&this.view.unbind(),this.behavior.handlesUnbind&&this.viewModel.unbind(),null!==this.elementEvents&&this.elementEvents.disposeAll(),n=0,e=t.length;n<e;++n)t[n].binding.unbind()}},e.prototype.attached=function(){this.isAttached||(this.isAttached=!0,this.behavior.handlesAttached&&this.viewModel.attached(),null!==this.view&&this.view.attached())},e.prototype.detached=function(){this.isAttached&&(this.isAttached=!1,null!==this.view&&this.view.detached(),this.behavior.handlesDetached&&this.viewModel.detached())},e}(),pe=0;function fe(){return!0}function ve(){}var me=function(){function e(){this.elementName=null,this.attributeName=null,this.attributeDefaultBindingMode=void 0,this.liftsContent=!1,this.targetShadowDOM=!1,this.shadowDOMOptions=null,this.processAttributes=ve,this.processContent=fe,this.usesShadowDOM=!1,this.childBindings=null,this.hasDynamicOptions=!1,this.containerless=!1,this.properties=[],this.attributes={},this.isInitialized=!1,this.primaryProperty=null}return e.convention=function(t,n){var i;return t.endsWith("CustomAttribute")&&((i=n||new e).attributeName=S(t.substring(0,t.length-15))),t.endsWith("CustomElement")&&((i=n||new e).elementName=S(t.substring(0,t.length-13))),i},e.prototype.addChildBinding=function(e){null===this.childBindings&&(this.childBindings=[]),this.childBindings.push(e)},e.prototype.initialize=function(e,t){var n,r,o,s=t.prototype,a=this.properties,l=this.attributeName,h=this.attributeDefaultBindingMode;if(!this.isInitialized)if(this.isInitialized=!0,t.__providerId__=++pe,this.observerLocator=e.get(u.Zr),this.taskQueue=e.get(c.P),this.target=t,this.usesShadowDOM=this.targetShadowDOM&&i.RI.shadowDOM,this.handlesCreated="created"in s,this.handlesBind="bind"in s,this.handlesUnbind="unbind"in s,this.handlesAttached="attached"in s,this.handlesDetached="detached"in s,this.htmlName=this.elementName||this.attributeName,null!==l)if(0===a.length&&new N({name:"value",changeHandler:"valueChanged"in s?"valueChanged":null,attribute:l,defaultBindingMode:h}).registerWith(t,this),o=a[0],1===a.length&&"value"===o.name)o.isDynamic=o.hasOptions=this.hasDynamicOptions,o.defineOn(t,this);else{for(n=0,r=a.length;n<r;++n)if(a[n].defineOn(t,this),a[n].primaryProperty){if(this.primaryProperty)throw new Error("Only one bindable property on a custom element can be defined as the default");this.primaryProperty=a[n]}(o=new N({name:"value",changeHandler:"valueChanged"in s?"valueChanged":null,attribute:l,defaultBindingMode:h})).hasOptions=!0,o.registerWith(t,this)}else{for(n=0,r=a.length;n<r;++n)a[n].defineOn(t,this);this._copyInheritedProperties(e,t)}},e.prototype.register=function(e,t){var n=this;null!==this.attributeName&&(e.registerAttribute(t||this.attributeName,this,this.attributeName),Array.isArray(this.aliases)&&this.aliases.forEach((function(t){e.registerAttribute(t,n,n.attributeName)}))),null!==this.elementName&&e.registerElement(t||this.elementName,this)},e.prototype.aliases=function(e){throw new Error("Method not implemented.")},e.prototype.load=function(e,t,n,i,r){var s,a=this;return null!==this.elementName?(i=e.get(_).getViewStrategy(i||this.viewStrategy||t),s=new f(this.targetShadowDOM,!0),i.moduleId||(i.moduleId=o.$e.get(t).moduleId),i.loadViewFactory(e.get(he),s,n,t).then((function(e){return r&&a.viewFactory||(a.viewFactory=e),e}))):Promise.resolve(this)},e.prototype.compile=function(e,t,n,r,o){if(this.liftsContent){if(!r.viewFactory){var s=i.dv.createElement("template"),a=i.dv.createDocumentFragment(),u=n.getAttribute("view-cache"),l=n.getAttribute("part");n.removeAttribute(r.originalAttrName),i.dv.replaceNode(s,n,o),a.appendChild(n),r.viewFactory=e.compile(a,t),l&&(r.viewFactory.part=l,n.removeAttribute("part")),u&&(r.viewFactory.setCacheSize(u),n.removeAttribute("view-cache")),n=s}}else if(null!==this.elementName){var c={};if(this.processContent(e,t,n,r)&&n.hasChildNodes()){for(var h=n.firstChild,d=this.usesShadowDOM?null:i.dv.createElement("au-content"),p=void 0,f=void 0;h;)p=h.nextSibling,"TEMPLATE"===h.tagName&&(f=h.getAttribute("replace-part"))?(c[f]=e.compile(h,t),i.dv.removeNode(h,o),r.partReplacements=c):null!==d&&(3===h.nodeType&&V(h)?i.dv.removeNode(h,o):d.appendChild(h)),h=p;null!==d&&d.hasChildNodes()&&n.appendChild(d),r.skipContentProcessing=!1}else r.skipContentProcessing=!0}else this.processContent(e,t,n,r)||(r.skipContentProcessing=!0);return n},e.prototype.create=function(e,t,n,r){var o,s=null;t=t||v.normal,n=n||null,r=r||null,null!==this.elementName&&n&&(this.usesShadowDOM?(o=n.attachShadow(this.shadowDOMOptions),e.registerInstance(i.dv.boundary,o)):(o=n,this.targetShadowDOM&&e.registerInstance(i.dv.boundary,o))),null!==n&&(n.au=s=n.au||{});var a,u=t.viewModel||e.get(this.target),l=new de(this,t,u,e),c=this.childBindings;if(this.liftsContent)s.controller=l;else if(null!==this.elementName){if(a=t.viewFactory||this.viewFactory,e.viewModel=u,a&&(l.view=a.create(e,t,n)),null!==n){if(s.controller=l,l.view){if(!this.usesShadowDOM&&(1===n.childNodes.length||n.contentElement)){var h=n.childNodes[0]||n.contentElement;l.view.contentView={fragment:h},h.parentNode&&i.dv.removeNode(h)}if(t.anchorIsContainer){if(null!==c)for(var d=0,p=c.length;d<p;++d)l.view.addBinding(c[d].create(n,u,l));l.view.appendNodesTo(o)}else l.view.insertNodesBefore(o)}else if(null!==c)for(d=0,p=c.length;d<p;++d)r.push(c[d].create(n,u,l))}else if(l.view){if(l.view.controller=l,null!==c)for(d=0,p=c.length;d<p;++d)l.view.addBinding(c[d].create(t.host,u,l))}else if(null!==c)for(d=0,p=c.length;d<p;++d)r.push(c[d].create(t.host,u,l))}else if(null!==c)for(d=0,p=c.length;d<p;++d)r.push(c[d].create(n,u,l));return null!==s&&(s[this.htmlName]=l),t.initiatedByBehavior&&a&&l.view.created(),l},e.prototype._ensurePropertiesDefined=function(e,t){var n,i,r,o;if(!("__propertiesDefined__"in t))for(t.__propertiesDefined__=!0,i=0,r=(n=this.properties).length;i<r;++i)void 0!==(o=n[i].createObserver(e))&&(t[o.propertyName]=o)},e.prototype._copyInheritedProperties=function(e,t){for(var n,i=t;;){var r=Object.getPrototypeOf(t.prototype);if(!(t=r&&r.constructor))return;if(n=o.yu.getOwn(o.yu.resource,t))break}n.initialize(e,t);for(var s=function(e,t){var r=n.properties[e];if(a.properties.some((function(e){return e.name===r.name})))return"continue";new N(r).registerWith(i,a)},a=this,u=0,l=n.properties.length;u<l;++u)s(u)},e}();function ge(e,t,n,i){if(t){var r=e[t];if(r){if(r!==n)throw new Error("Attempted to register ".concat(i," when one with the same name already exists. Name: ").concat(t,"."))}else e[t]=n}}function we(e,t){if(/[A-Z]/.test(e)){var n=S(e);return a.getLogger("templating").warn("'".concat(e,"' is not a valid ").concat(t," name and has been converted to '").concat(n,"'. Upper-case letters are not allowed because the DOM is not case-sensitive.")),n}return e}var ye="__au_resource__",be=function(){function e(e,t){this.bindingLanguage=null,this.parent=e||null,this.hasParent=null!==this.parent,this.viewUrl=t||"",this.lookupFunctions={valueConverters:this.getValueConverter.bind(this),bindingBehaviors:this.getBindingBehavior.bind(this)},this.attributes=Object.create(null),this.elements=Object.create(null),this.valueConverters=Object.create(null),this.bindingBehaviors=Object.create(null),this.attributeMap=Object.create(null),this.values=Object.create(null),this.beforeCompile=this.afterCompile=this.beforeCreate=this.afterCreate=this.beforeBind=this.beforeUnbind=!1}return e.convention=function(e,t){var n;if(t&&ye in t)return t;if("$resource"in e){var i=e.$resource;if("string"==typeof i)(n=t||new me)[ye]=!0,n.elementName||(n.elementName=we(i,"custom element"));else{"function"==typeof i&&(i=i.call(e)),"string"==typeof i&&(i={name:i});var r=(i=Object.assign({},i)).type||"element",o=i.name;switch(r){case"element":case"attribute":(n=t||new me)[ye]=!0,"element"===r?n.elementName||(n.elementName=o?we(o,"custom element"):S(e.name)):n.attributeName||(n.attributeName=o?we(o,"custom attribute"):S(e.name)),"templateController"in i&&(i.liftsContent=i.templateController,delete i.templateController),"defaultBindingMode"in i&&void 0!==n.attributeDefaultBindingMode&&(i.attributeDefaultBindingMode=i.defaultBindingMode,delete i.defaultBindingMode),delete i.name,Object.assign(n,i);break;case"valueConverter":n=new u.MC((0,u.xQ)(o||e.name));break;case"bindingBehavior":n=new u.w7((0,u.xQ)(o||e.name));break;case"viewEngineHooks":n=new ie}}if(n instanceof me){var s="string"==typeof i?void 0:i.bindables,a=n.properties;if(Array.isArray(s))for(var l=0,c=s.length;c>l;++l){var h=s[l];if(!h||"string"!=typeof h&&!h.name)throw new Error('Invalid bindable property at "'.concat(l,'" for class "').concat(e.name,'". Expected either a string or an object with "name" property.'));for(var d=new N(h),p=!1,f=0,v=a.length;v>f;++f)if(a[f].name===d.name){p=!0;break}p||d.registerWith(e,n)}}}return n},e.prototype._tryAddHook=function(e,t){if("function"==typeof e[t]){for(var n=e[t].bind(e),i=1,r=void 0;void 0!==this[r=t+i.toString()];)i++;this[t]=!0,this[r]=n}},e.prototype._invokeHook=function(e,t,n,i,r){if(this.hasParent&&this.parent._invokeHook(e,t,n,i,r),this[e]){this[e+"1"](t,n,i,r);var o=e+"2";if(this[o]&&(this[o](t,n,i,r),this[o=e+"3"])){this[o](t,n,i,r);for(var s=4;void 0!==this[o=e+s.toString()];)this[o](t,n,i,r),s++}}},e.prototype.registerViewEngineHooks=function(e){this._tryAddHook(e,"beforeCompile"),this._tryAddHook(e,"afterCompile"),this._tryAddHook(e,"beforeCreate"),this._tryAddHook(e,"afterCreate"),this._tryAddHook(e,"beforeBind"),this._tryAddHook(e,"beforeUnbind")},e.prototype.getBindingLanguage=function(e){return this.bindingLanguage||(this.bindingLanguage=e)},e.prototype.patchInParent=function(e){var t=this.parent;this.parent=e||null,this.hasParent=null!==this.parent,null===e.parent&&(e.parent=t,e.hasParent=null!==t)},e.prototype.relativeToView=function(e){return(0,s.Yc)(e,this.viewUrl)},e.prototype.registerElement=function(e,t){ge(this.elements,e,t,"an Element")},e.prototype.getElement=function(e){return this.elements[e]||(this.hasParent?this.parent.getElement(e):null)},e.prototype.mapAttribute=function(e){return this.attributeMap[e]||(this.hasParent?this.parent.mapAttribute(e):null)},e.prototype.registerAttribute=function(e,t,n){this.attributeMap[e]=n,ge(this.attributes,e,t,"an Attribute")},e.prototype.getAttribute=function(e){return this.attributes[e]||(this.hasParent?this.parent.getAttribute(e):null)},e.prototype.registerValueConverter=function(e,t){ge(this.valueConverters,e,t,"a ValueConverter")},e.prototype.getValueConverter=function(e){return this.valueConverters[e]||(this.hasParent?this.parent.getValueConverter(e):null)},e.prototype.registerBindingBehavior=function(e,t){ge(this.bindingBehaviors,e,t,"a BindingBehavior")},e.prototype.getBindingBehavior=function(e){return this.bindingBehaviors[e]||(this.hasParent?this.parent.getBindingBehavior(e):null)},e.prototype.registerValue=function(e,t){ge(this.values,e,t,"a value")},e.prototype.getValue=function(e){return this.values[e]||(this.hasParent?this.parent.getValue(e):null)},e.prototype.autoRegister=function(t,n){var i=o.yu.getOwn(o.yu.resource,n);return i?i instanceof me&&(e.convention(n,i),null===i.attributeName&&null===i.elementName&&me.convention(n.name,i),null===i.attributeName&&null===i.elementName&&(i.elementName=S(n.name))):((i=e.convention(n)||me.convention(n.name)||u.MC.convention(n.name)||u.w7.convention(n.name)||ie.convention(n.name))||((i=new me).elementName=S(n.name)),o.yu.define(o.yu.resource,i,n)),i.initialize(t,n),i.register(this,void 0),i},e}(),Ce=o.TB.create("aurelia:view-strategy",{validate:function(e){return"function"==typeof e.loadViewFactory||"View strategies must implement: loadViewFactory(viewEngine: ViewEngine, compileInstruction: ViewCompileInstruction, loadContext?: ResourceLoadContext): Promise<ViewFactory>"},compose:function(e){"function"!=typeof e.makeRelativeTo&&(e.makeRelativeTo=i.i9.noop)}}),Se=function(){function e(e){this.path=e,this.absolutePath=null}return e.prototype.loadViewFactory=function(e,t,n,i){return null===this.absolutePath&&this.moduleId&&(this.absolutePath=(0,s.Yc)(this.path,this.moduleId)),t.associatedModuleId=this.moduleId,e.loadViewFactory(this.absolutePath||this.path,t,n,i)},e.prototype.makeRelativeTo=function(e){null===this.absolutePath&&(this.absolutePath=(0,s.Yc)(this.path,e))},y([Ce()],e)}(),Ve=function(){function e(e,t){this.moduleId=t.moduleId,this.viewUrl=e.convertOriginToViewUrl(t)}return e.prototype.loadViewFactory=function(e,t,n,i){return t.associatedModuleId=this.moduleId,e.loadViewFactory(this.viewUrl,t,n,i)},y([Ce()],e)}(),Ae=function(){function e(e,t){this.dependencies=e||null,this.dependencyBaseUrl=t||""}return e.prototype.loadViewFactory=function(e,t,n,i){var o=this.entry,s=this.dependencies;if(o&&o.factoryIsReady)return Promise.resolve(null);if(this.entry=o=new r.sT(this.moduleId||this.dependencyBaseUrl),o.dependencies=[],o.templateIsLoaded=!0,null!==s)for(var a=0,u=s.length;a<u;++a){var l=s[a];"string"==typeof l||"function"==typeof l?o.addDependency(l):o.addDependency(l.from,l.as)}return t.associatedModuleId=this.moduleId,e.loadViewFactory(o,t,n,i)},y([Ce()],e)}(),Me=function(){function e(e,t){this.moduleId=e,this.entry=t}return e.prototype.loadViewFactory=function(e,t,n,i){var r=this.entry;return r.factoryIsReady?Promise.resolve(r.factory):(t.associatedModuleId=this.moduleId,e.loadViewFactory(r,t,n,i))},y([Ce()],e)}(),Ne=function(){function e(e,t,n){this.markup=e,this.dependencies=t||null,this.dependencyBaseUrl=n||""}return e.prototype.loadViewFactory=function(e,t,n,o){var s=this.entry,a=this.dependencies;if(s&&s.factoryIsReady)return Promise.resolve(s.factory);if(this.entry=s=new r.sT(this.moduleId||this.dependencyBaseUrl),s.template=i.dv.createTemplateFromMarkup(this.markup),null!==a)for(var u=0,l=a.length;u<l;++u){var c=a[u];"string"==typeof c||"function"==typeof c?s.addDependency(c):s.addDependency(c.from,c.as)}return t.associatedModuleId=this.moduleId,e.loadViewFactory(s,t,n,o)},y([Ce()],e)}(),_e=function(){function e(e){("string"==typeof e||e instanceof i.dv.Element&&"TEMPLATE"===e.tagName)&&(e={template:e}),this.template=e.template,this.dependencies=e.dependencies||[],this.factoryIsReady=!1,this.onReady=null,this.moduleId="undefined"}return e.prototype.loadViewFactory=function(e,t,n,i){var r=this;if(this.factoryIsReady)return Promise.resolve(this.factory);var o=this.dependencies,s="function"==typeof o?o():o;return s=s||[],s=Array.isArray(s)?s:[s],Promise.all(s).then((function(n){var o,s=e.container,a=e.appResources,u=e.viewCompiler,l=new be(a),c=[];i&&l.autoRegister(s,i);for(var h=0,d=n;h<d.length;h++){var p=d[h];if("function"==typeof p)null!==(o=l.autoRegister(s,p)).elementName&&c.push(o);else{if(!p||"object"!=typeof p)throw new Error('dependency neither function nor object. Received: "'.concat(typeof p,'"'));for(var f in p){var v=p[f];"function"==typeof v&&null!==(o=l.autoRegister(s,v)).elementName&&c.push(o)}}}return Promise.all(c.map((function(e){return e.load(s,e.target)}))).then((function(){var e=null!==r.template?u.compile(r.template,l,t):null;return r.factoryIsReady=!0,r.factory=e,e}))}))},y([Ce()],e)}();function Oe(e,t){return Array.isArray(t)?e.removeMany(t,!0):e.remove(t,!0)}var Ie={before:function(e,t,n){return void 0===t?n():n().then((function(){return Oe(e,t)}))},with:function(e,t,n){return void 0===t?n():Promise.all([Oe(e,t),n()])},after:function(e,t,n){return Promise.resolve(e.removeAll(!0)).then(n)}};function Be(e){return e.skipActivation||"function"!=typeof e.viewModel.activate?Promise.resolve():e.viewModel.activate(e.model)||Promise.resolve()}var xe=function(){function e(e,t){this.viewEngine=e,this.viewLocator=t}return e.prototype._swap=function(e,t){var n=Ie[e.swapOrder]||Ie.after,i=e.viewSlot.children.slice();return n(e.viewSlot,i,(function(){return Promise.resolve(e.viewSlot.add(t)).then((function(){e.currentController&&e.currentController.unbind()}))})).then((function(){e.compositionTransactionNotifier&&e.compositionTransactionNotifier.done()}))},e.prototype._createControllerAndSwap=function(e){var t=this;return this.createController(e).then((function(n){return e.compositionTransactionOwnershipToken?e.compositionTransactionOwnershipToken.waitForCompositionComplete().then((function(){return n.automate(e.overrideContext,e.owningView),t._swap(e,n.view)})).then((function(){return n})):(n.automate(e.overrideContext,e.owningView),t._swap(e,n.view).then((function(){return n})))}))},e.prototype.createController=function(e){var t,n,i,r,o=this;return this.ensureViewModel(e).then(Be).then((function(){t=e.childContainer,n=e.viewModel,i=e.viewModelResource,r=i.metadata;var s=o.viewLocator.getViewStrategy(e.view||n);return e.viewResources&&s.makeRelativeTo(e.viewResources.viewUrl),r.load(t,i.value,null,s,!0)})).then((function(i){return r.create(t,v.dynamic(e.host,n,i))}))},e.prototype.ensureViewModel=function(e){var t=e.childContainer=e.childContainer||e.container.createChild();if("string"==typeof e.viewModel)return e.viewModel=e.viewResources?e.viewResources.relativeToView(e.viewModel):e.viewModel,this.viewEngine.importViewModelResource(e.viewModel).then((function(n){return t.autoRegister(n.value),e.host&&t.registerInstance(i.dv.Element,e.host),e.viewModel=t.viewModel=t.get(n.value),e.viewModelResource=n,e}));var n=e.viewModel.constructor,r="function"==typeof e.viewModel;r&&(n=e.viewModel,t.autoRegister(n));var s=o.yu.getOrCreateOwn(o.yu.resource,me,n);return s.elementName=s.elementName||"dynamic-element",s.initialize(r?t:e.container||t,n),e.viewModelResource={metadata:s,value:n},e.host&&t.registerInstance(i.dv.Element,e.host),t.viewModel=e.viewModel=r?t.get(n):e.viewModel,Promise.resolve(e)},e.prototype.compose=function(e){var t=this;e.childContainer=e.childContainer||e.container.createChild(),e.view=this.viewLocator.getViewStrategy(e.view);var n=e.childContainer.get(E),i=n.tryCapture();return i?e.compositionTransactionOwnershipToken=i:e.compositionTransactionNotifier=n.enlist(),e.viewModel?this._createControllerAndSwap(e):e.view?(e.viewResources&&e.view.makeRelativeTo(e.viewResources.viewUrl),e.view.loadViewFactory(this.viewEngine,new f).then((function(n){var i=n.create(e.childContainer);return i.bind(e.bindingContext,e.overrideContext),e.compositionTransactionOwnershipToken?e.compositionTransactionOwnershipToken.waitForCompositionComplete().then((function(){return t._swap(e,i)})).then((function(){return i})):t._swap(e,i).then((function(){return i}))}))):e.viewSlot?(e.viewSlot.removeAll(),e.compositionTransactionNotifier&&e.compositionTransactionNotifier.done(),Promise.resolve(null)):Promise.resolve(null)},y([(0,l.WQ)(he,_)],e)}(),Te={enterBegin:"animation:enter:begin",enterActive:"animation:enter:active",enterDone:"animation:enter:done",enterTimeout:"animation:enter:timeout",leaveBegin:"animation:leave:begin",leaveActive:"animation:leave:active",leaveDone:"animation:leave:done",leaveTimeout:"animation:leave:timeout",staggerNext:"animation:stagger:next",removeClassBegin:"animation:remove-class:begin",removeClassActive:"animation:remove-class:active",removeClassDone:"animation:remove-class:done",removeClassTimeout:"animation:remove-class:timeout",addClassBegin:"animation:add-class:begin",addClassActive:"animation:add-class:active",addClassDone:"animation:add-class:done",addClassTimeout:"animation:add-class:timeout",animateBegin:"animation:animate:begin",animateActive:"animation:animate:active",animateDone:"animation:animate:done",animateTimeout:"animation:animate:timeout",sequenceBegin:"animation:sequence:begin",sequenceDone:"animation:sequence:done"};function je(e,t){return function(n,i,r){var s="string"==typeof i?n.constructor:n,a=o.yu.getOrCreateOwn(o.yu.resource,me,s);"string"==typeof e&&(e={selector:e,name:i}),r&&(r.writable=!0,r.configurable=!0),e.all=t,a.addChildBinding(new ke(e))}}function Fe(e){return je(e,!0)}function Pe(e){return je(e,!1)}var ke=function(){function e(e){this.name=e.name,this.changeHandler=e.changeHandler||this.name+"Changed",this.selector=e.selector,this.all=e.all}return e.prototype.create=function(e,t,n){return new Le(this.selector,e,this.name,t,n,this.changeHandler,this.all)},e}(),Ee=[];function Re(e,t,n){var i=e.get(t);i||(i=[],e.set(t,i)),i.push(n)}function De(e,t){for(var n=t.binders,i=n.length,r=new Map,o=0,s=e.length;o<s;++o){for(var a=e[o],u=a.addedNodes,l=a.removedNodes,c=0,h=l.length;c<h;++c)if(1===(p=l[c]).nodeType)for(var d=0;d<i;++d)(f=n[d]).onRemove(p)&&Re(r,f,a);for(c=0,h=u.length;c<h;++c){var p;if(1===(p=u[c]).nodeType)for(d=0;d<i;++d){var f;(f=n[d]).onAdd(p)&&Re(r,f,a)}}}r.forEach((function(e,t){t.isBound&&null!==t.changeHandler&&t.viewModel[t.changeHandler](e)}))}var Le=function(){function e(e,t,n,i,r,o,s){this.selector=e,this.viewHost=t,this.property=n,this.viewModel=i,this.controller=r,this.changeHandler=o in i?o:null,this.usesShadowDOM=r.behavior.usesShadowDOM,this.all=s,!this.usesShadowDOM&&r.view&&r.view.contentView?this.contentView=r.view.contentView:this.contentView=null,this.source=null,this.isBound=!1}return e.prototype.matches=function(e){if(e.matches(this.selector)){if(null===this.contentView)return!0;var t=this.contentView,n=e.auAssignedSlot;if(n&&n.projectFromAnchors){for(var i=n.projectFromAnchors,r=0,o=i.length;r<o;++r)if(i[r].auOwnerView===t)return!0;return!1}return e.auOwnerView===t}return!1},e.prototype.bind=function(e){if(this.isBound){if(this.source===e)return;this.source=e}this.isBound=!0;var t=this.viewHost,n=this.viewModel,r=t.__childObserver__;if(!r){r=t.__childObserver__=i.dv.createMutationObserver(De);var o={childList:!0,subtree:!this.usesShadowDOM};r.observe(t,o),r.binders=[]}if(r.binders.push(this),this.usesShadowDOM){var s=t.firstElementChild;if(this.all){var a=n[this.property];for(a?a.splice(0):a=n[this.property]=[];s;)this.matches(s)&&a.push(s.au&&s.au.controller?s.au.controller.viewModel:s),s=s.nextElementSibling;null!==this.changeHandler&&this.viewModel[this.changeHandler](Ee)}else for(;s;){if(this.matches(s)){var u=s.au&&s.au.controller?s.au.controller.viewModel:s;this.viewModel[this.property]=u,null!==this.changeHandler&&this.viewModel[this.changeHandler](u);break}s=s.nextElementSibling}}},e.prototype.onRemove=function(e){if(this.matches(e)){var t=e.au&&e.au.controller?e.au.controller.viewModel:e;if(this.all){var n=this.viewModel[this.property]||(this.viewModel[this.property]=[]),i=n.indexOf(t);return-1!==i&&n.splice(i,1),!0}this.viewModel[this.property]===t&&(this.viewModel[this.property]=null,this.isBound&&null!==this.changeHandler&&this.viewModel[this.changeHandler](t))}return!1},e.prototype.onAdd=function(e){if(this.matches(e)){var t=e.au&&e.au.controller?e.au.controller.viewModel:e;if(this.all){var n=this.viewModel[this.property]||(this.viewModel[this.property]=[]);if("*"===this.selector)return n.push(t),!0;for(var i=0,r=e.previousElementSibling;r;)this.matches(r)&&i++,r=r.previousElementSibling;return n.splice(i,0,t),!0}this.viewModel[this.property]=t,this.isBound&&null!==this.changeHandler&&this.viewModel[this.changeHandler](t)}return!1},e.prototype.unbind=function(){if(this.isBound){this.isBound=!1,this.source=null;var e=this.viewHost.__childObserver__;if(e){var t=e.binders;if(t&&t.length){var n=t.indexOf(this);-1!==n&&t.splice(n,1),0===t.length&&(e.disconnect(),this.viewHost.__childObserver__=null)}this.usesShadowDOM&&(this.viewModel[this.property]=null)}}},e}(),He=function(){function e(){}return e.prototype.initialize=function(e,t){},e.prototype.register=function(e,t){},e.prototype.load=function(e,t){var n=new t;e.get(u.EU).registerElementConfig(n)},e}();function ze(e){return function(t){"string"==typeof e||Object.getPrototypeOf(e)===Object.prototype?t.$resource=e:o.yu.define(o.yu.resource,e,t)}}function Ue(e){return function(t){if(e instanceof me)o.yu.define(o.yu.resource,e,t);else{var n=o.yu.getOrCreateOwn(o.yu.resource,me,t);Object.assign(n,e)}}}function qe(e){return function(t){o.yu.getOrCreateOwn(o.yu.resource,me,t).elementName=we(e,"custom element")}}function We(e,t,n){return function(i){var r=o.yu.getOrCreateOwn(o.yu.resource,me,i);r.attributeName=we(e,"custom attribute"),r.attributeDefaultBindingMode=t,r.aliases=n}}function $e(e){var t=function(e){o.yu.getOrCreateOwn(o.yu.resource,me,e).liftsContent=!0};return e?t(e):t}function Ke(e,t,n){var i=function(t,n,i){var r=n?t.constructor:t,s=o.yu.getOrCreateOwn(o.yu.resource,me,r);return n&&((e=e||{}).name=n),new N(e).registerWith(r,s,i)};if(!e)return i;if(t){var r=e;return e=null,i(r,t,n)}return i}function Qe(e){var t=function(e){o.yu.getOrCreateOwn(o.yu.resource,me,e).hasDynamicOptions=!0};return e?t(e):t}var Ye={mode:"open"};function Ge(e){var t="function"!=typeof e&&e?e:Ye,n=function(e){var n=o.yu.getOrCreateOwn(o.yu.resource,me,e);n.targetShadowDOM=!0,n.shadowDOMOptions=t};return"function"==typeof e?n(e):n}function Ze(e){return function(t){o.yu.getOrCreateOwn(o.yu.resource,me,t).processAttributes=function(t,n,i,r,o){try{e(t,n,i,r,o)}catch(e){a.getLogger("templating").error(e)}}}}function Je(){return!1}function Xe(e){return function(t){o.yu.getOrCreateOwn(o.yu.resource,me,t).processContent=e?function(t,n,i,r){try{return e(t,n,i,r)}catch(e){return a.getLogger("templating").error(e),!1}}:Je}}function et(e){var t=function(e){o.yu.getOrCreateOwn(o.yu.resource,me,e).containerless=!0};return e?t(e):t}function tt(e){return function(t){o.yu.define(_.viewStrategyMetadataKey,e,t)}}function nt(e){return tt(new Se(e))}function it(e,t,n){return tt(new Ne(e,t,n))}function rt(e,t){var n,i;"function"==typeof e?n=e:(i=e,n=void 0);var r=function(e){o.yu.define(_.viewStrategyMetadataKey,new Ae(i,t),e)};return n?r(n):r}function ot(e){return function(t){t.$view=e}}function st(e){var t=function(e){o.yu.define(o.yu.resource,new He,e)};return e?t(e):t}function at(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(t){o.yu.define(he.viewModelRequireMetadataKey,e,t)}}var ut=function(){function e(e,t,n,i){this._container=e,this._moduleAnalyzer=t,this._viewCompiler=n,this._compositionEngine=i,e.registerInstance(D,D.instance=new D)}return e.prototype.configureAnimator=function(e){this._container.unregister(D),this._container.registerInstance(D,D.instance=e)},e.prototype.compose=function(e){return this._compositionEngine.compose(e)},e.prototype.enhance=function(e){e instanceof i.dv.Element&&(e={element:e});var t={letExpressions:[]},n=e.resources||this._container.get(be);this._viewCompiler._compileNode(e.element,n,t,e.element.parentNode,"root",!0);var r=new Z(e.element,t,n),o=e.container||this._container.createChild(),s=r.create(o,v.enhance());return s.bind(e.bindingContext||{},e.overrideContext),s.firstChild=s.lastChild=s.fragment,s.fragment=i.dv.createDocumentFragment(),s.attached(),s},e.inject=[l.mc,ae,ne,xe],e}()}}]);
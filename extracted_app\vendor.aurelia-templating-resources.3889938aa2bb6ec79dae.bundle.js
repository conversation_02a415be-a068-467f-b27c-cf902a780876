"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[4900],{"aurelia-templating-resources":(e,t,i)=>{i.r(t),i.d(t,{AbstractRepeater:()=>G,ArrayRepeatStrategy:()=>P,AttrBindingBehavior:()=>ae,BindingSignaler:()=>_e,Compose:()=>w,ComposeActivationStrategy:()=>n,DebounceBindingBehavior:()=>me,Else:()=>_,Focus:()=>ne,FromViewBindingBehavior:()=>he,HTMLSanitizer:()=>ee,Hide:()=>J,If:()=>C,MapRepeatStrategy:()=>L,NullRepeatStrategy:()=>k,NumberRepeatStrategy:()=>A,OneTimeBindingBehavior:()=>ce,OneWayBindingBehavior:()=>le,Repeat:()=>U,RepeatStrategyLocator:()=>F,Replaceable:()=>ie,SanitizeHTMLValueConverter:()=>te,SelfBindingBehavior:()=>Ce,SetRepeatStrategy:()=>q,Show:()=>Z,SignalBindingBehavior:()=>Se,ThrottleBindingBehavior:()=>fe,ToViewBindingBehavior:()=>de,TwoWayBindingBehavior:()=>ve,UpdateTriggerBindingBehavior:()=>xe,With:()=>S,configure:()=>Me,createFullOverrideContext:()=>M,getItemsSourceExpression:()=>I,isOneTime:()=>T,unwrapExpression:()=>B,updateOneTimeBinding:()=>E,updateOverrideContext:()=>O,viewsRequireLifecycle:()=>N});var n,o=i(27884),r=i(16566),s=i(40896),a=i(30960),u=i(7530),c=i(95260),l=i(83260),d=i(38468),h=i(96610),v=function(e,t){return v=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])},v(e,t)};function p(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Class extends value "+String(t)+" is not a constructor or null");function i(){this.constructor=e}v(e,t),e.prototype=null===t?Object.create(t):(i.prototype=t.prototype,new i)}function f(e,t,i,n){var o,r=arguments.length,s=r<3?t:null===n?n=Object.getOwnPropertyDescriptor(t,i):n;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(e,t,i,n);else for(var a=e.length-1;a>=0;a--)(o=e[a])&&(s=(r<3?o(s):r>3?o(t,i,s):o(t,i))||s);return r>3&&s&&Object.defineProperty(t,i,s),s}!function(e){e.InvokeLifecycle="invoke-lifecycle",e.Replace="replace"}(n||(n={}));var w=function(){function e(e,t,i,o,r,s){this.activationStrategy=n.InvokeLifecycle,this.element=e,this.container=t,this.compositionEngine=i,this.viewSlot=o,this.viewResources=r,this.taskQueue=s,this.currentController=null,this.currentViewModel=null,this.changes=Object.create(null)}return e.inject=function(){return[r.dv.Element,o.mc,a.vW,a.eu,a.zT,s.P]},e.prototype.created=function(e){this.owningView=e},e.prototype.bind=function(e,t){this.bindingContext=e,this.overrideContext=t;var i=this.changes;i.view=this.view,i.viewModel=this.viewModel,i.model=this.model,this.pendingTask||g(this)},e.prototype.unbind=function(){this.changes=Object.create(null),this.bindingContext=null,this.overrideContext=null,this.viewSlot.removeAll(!0,!0)},e.prototype.modelChanged=function(e,t){this.changes.model=e,m(this)},e.prototype.viewChanged=function(e,t){this.changes.view=e,m(this)},e.prototype.viewModelChanged=function(e,t){this.changes.viewModel=e,m(this)},f([a._t],e.prototype,"model",void 0),f([a._t],e.prototype,"view",void 0),f([a._t],e.prototype,"viewModel",void 0),f([a._t],e.prototype,"activationStrategy",void 0),f([a._t],e.prototype,"swapOrder",void 0),f([a.hl,(0,a.EM)("compose")],e)}();function g(e){var t=e.changes;e.changes=Object.create(null);var i=function(e){var t=e.activationStrategy,i=e.currentViewModel;return i&&"function"==typeof i.determineActivationStrategy&&(t=i.determineActivationStrategy()),t}(e);if(function(e,t){return"view"in t||"viewModel"in t||e===n.Replace}(i,t)){var o=i===n.Replace?null:e.currentViewModel,r={view:e.view,viewModel:o||e.viewModel,model:e.model};r=Object.assign(r,t),r=function(e,t){return Object.assign(t,{bindingContext:e.bindingContext,overrideContext:e.overrideContext,owningView:e.owningView,container:e.container,viewSlot:e.viewSlot,viewResources:e.viewResources,currentController:e.currentController,host:e.element,swapOrder:e.swapOrder})}(e,r),e.pendingTask=e.compositionEngine.compose(r).then((function(t){e.currentController=t,e.currentViewModel=t?t.viewModel:null}))}else if(e.pendingTask=function(e,t){if(e&&"function"==typeof e.activate)return Promise.resolve(e.activate(t))}(e.currentViewModel,t.model),!e.pendingTask)return;e.pendingTask=e.pendingTask.then((function(){y(e)}),(function(t){throw y(e),t}))}function y(e){e.pendingTask=null,function(e){for(var t in e)return!1;return!0}(e.changes)||g(e)}function m(e){e.pendingTask||e.updateRequested||(e.updateRequested=!0,e.taskQueue.queueMicroTask((function(){e.updateRequested=!1,g(e)})))}var b=function(){function e(e,t){this.viewFactory=e,this.viewSlot=t,this.view=null,this.bindingContext=null,this.overrideContext=null,this.showing=!1,this.cache=!0}return e.prototype.bind=function(e,t){this.bindingContext=e,this.overrideContext=t},e.prototype.unbind=function(){null!==this.view&&(this.view.unbind(),this.viewFactory.isCaching&&(this.showing?(this.showing=!1,this.viewSlot.remove(this.view,!0,!0)):this.view.returnToCache(),this.view=null))},e.prototype._show=function(){if(!this.showing)return null===this.view&&(this.view=this.viewFactory.create()),this.view.isBound||this.view.bind(this.bindingContext,this.overrideContext),this.showing=!0,this.viewSlot.add(this.view);this.view.isBound||this.view.bind(this.bindingContext,this.overrideContext)},e.prototype._hide=function(){var e=this;if(this.showing){this.showing=!1;var t=this.viewSlot.remove(this.view);if(t instanceof Promise)return t.then((function(){e._unbindView()}));this._unbindView()}},e.prototype._unbindView=function(){var e="false"!==this.cache&&!!this.cache;this.view.unbind(),e||(this.view=null)},e}(),C=function(e){function t(){var t=null!==e&&e.apply(this,arguments)||this;return t.cache=!0,t}return p(t,e),t.prototype.bind=function(t,i){e.prototype.bind.call(this,t,i),this.condition?this._show():this._hide()},t.prototype.conditionChanged=function(e){this._update(e)},t.prototype._update=function(e){var t,i=this;this.animating||(t=this.elseVm?e?this._swap(this.elseVm,this):this._swap(this,this.elseVm):e?this._show():this._hide())&&(this.animating=!0,t.then((function(){i.animating=!1,i.condition!==i.showing&&i._update(i.condition)})))},t.prototype._swap=function(e,t){switch(this.swapOrder){case"before":return Promise.resolve(t._show()).then((function(){return e._hide()}));case"with":return Promise.all([e._hide(),t._show()]);default:var i=e._hide();return i?i.then((function(){return t._show()})):t._show()}},f([(0,a._t)({primaryProperty:!0})],t.prototype,"condition",void 0),f([a._t],t.prototype,"swapOrder",void 0),f([a._t],t.prototype,"cache",void 0),f([(0,a.U4)("if"),a.hL,(0,o.WQ)(a.gF,a.eu)],t)}(b),_=function(e){function t(t,i){var n=e.call(this,t,i)||this;return n._registerInIf(),n}return p(t,e),t.prototype.bind=function(t,i){e.prototype.bind.call(this,t,i),this.ifVm.condition?this._hide():this._show()},t.prototype._registerInIf=function(){for(var e=this.viewSlot.anchor.previousSibling;e&&!e.au;)e=e.previousSibling;if(!e||!e.au.if)throw new Error("Can't find matching If for Else custom attribute.");this.ifVm=e.au.if.viewModel,this.ifVm.elseVm=this},f([(0,a.U4)("else"),a.hL,(0,o.WQ)(a.gF,a.eu)],t)}(b),S=function(){function e(e,t){this.viewFactory=e,this.viewSlot=t,this.parentOverrideContext=null,this.view=null}return e.prototype.bind=function(e,t){this.parentOverrideContext=t,this.valueChanged(this.value)},e.prototype.valueChanged=function(e){var t=(0,u.iI)(e,this.parentOverrideContext),i=this.view;i?i.bind(e,t):((i=this.view=this.viewFactory.create()).bind(e,t),this.viewSlot.add(i))},e.prototype.unbind=function(){var e=this.view;this.parentOverrideContext=null,e&&e.unbind()},f([(0,a.U4)("with"),a.hL,(0,o.WQ)(a.gF,a.eu)],e)}(),V=u.BG.oneTime;function x(e,t){var i=e.length;for(t>0&&(t-=1);t<i;++t)O(e[t].overrideContext,t,i)}function M(e,t,i,n,o){var r={},s=(0,u.iI)(r,e.scope.overrideContext);return void 0!==o?(r[e.key]=o,r[e.value]=t):r[e.local]=t,O(s,i,n),s}function O(e,t,i){var n=0===t,o=t===i-1,r=t%2==0;e.$index=t,e.$first=n,e.$last=o,e.$middle=!(n||o),e.$odd=!r,e.$even=r}function I(e,t){return e.behaviorInstructions.filter((function(e){return e.originalAttrName===t}))[0].attributes.items.sourceExpression}function B(e){for(var t=!1;e instanceof u.aI;)e=e.expression;for(;e instanceof u.i1;)e=e.expression,t=!0;return t?e:null}function T(e){for(;e instanceof u.aI;){if("oneTime"===e.name)return!0;e=e.expression}return!1}function E(e){e.call&&e.mode===V?e.call(u.RH):e.updateOneTimeBindings&&e.updateOneTimeBindings()}function R(e,t,i,n){if(!i)return e.indexOf(t);for(var o=e.length,r=n||0;r<o;r++)if(i(e[r],t))return r;return-1}var P=function(){function e(){}return e.prototype.getCollectionObserver=function(e,t){return e.getArrayObserver(t)},e.prototype.instanceChanged=function(e,t){var i=this,n=e,o=t.length;if(t&&0!==o){var r=n.views(),s=r.length;if(0!==s)if(n.viewsRequireLifecycle){for(var a=r.slice(0),u=n.local,c=n.matcher(),l=[],d=[],h=0;h<s;h++){var v=a[h],p=v.bindingContext[u];-1===R(t,p,c)?d.push(v):l.push(p)}var f=void 0,w=void 0;l.length>0?(w=n.removeViews(d,!0,!n.viewsRequireLifecycle),f=function(){for(var e=0;e<o;e++){var s=t[e],a=R(l,s,c,e),u=void 0;if(-1===a){var d=M(n,t[e],e,o);n.insertView(e,d.bindingContext,d),l.splice(e,0,void 0)}else a===e?(u=r[a],l[a]=void 0):(u=r[a],n.moveView(a,e),l.splice(a,1),l.splice(e,0,void 0));u&&O(u.overrideContext,e,o)}i._inPlaceProcessItems(n,t)}):(w=n.removeAllViews(!0,!n.viewsRequireLifecycle),f=function(){return i._standardProcessInstanceChanged(n,t)}),w instanceof Promise?w.then(f):f()}else this._inPlaceProcessItems(n,t);else this._standardProcessInstanceChanged(n,t)}else n.removeAllViews(!0,!n.viewsRequireLifecycle)},e.prototype._standardProcessInstanceChanged=function(e,t){for(var i=0,n=t.length;i<n;i++){var o=M(e,t[i],i,n);e.addView(o.bindingContext,o)}},e.prototype._inPlaceProcessItems=function(e,t){for(var i=t.length,n=e.viewCount();n>i;)n--,e.removeView(n,!0,!e.viewsRequireLifecycle);for(var o=e.local,r=0;r<n;r++){var s=e.view(r),a=r===i-1,u=0!==r&&!a,c=s.bindingContext,l=s.overrideContext;c[o]===t[r]&&l.$middle===u&&l.$last===a||(c[o]=t[r],l.$middle=u,l.$last=a,e.updateBindings(s))}for(r=n;r<i;r++)l=M(e,t[r],r,i),e.addView(l.bindingContext,l)},e.prototype.instanceMutated=function(e,t,i){var n=this;if(e.__queuedSplices){for(var o=0,r=i.length;o<r;++o){var s=i[o],a=s.index,c=s.removed,l=s.addedCount;(0,u.xj)(e.__queuedSplices,a,c,l)}e.__array=t.slice(0)}else{var d=this._runSplices(e,t.slice(0),i);if(d instanceof Promise){var h=e.__queuedSplices=[],v=function(){if(!h.length)return e.__queuedSplices=void 0,void(e.__array=void 0);var t=n._runSplices(e,e.__array,h)||Promise.resolve();h=e.__queuedSplices=[],t.then(v)};d.then(v)}}},e.prototype._runSplices=function(e,t,i){for(var n=this,o=0,r=[],s=0,a=i.length;s<a;++s){for(var u=i[s],c=0,l=u.removed.length;c<l;++c){var d=e.removeView(u.index+o+r.length,!0);d instanceof Promise&&r.push(d)}o-=u.addedCount}if(r.length>0)return Promise.all(r).then((function(){var o=n._handleAddedSplices(e,t,i);x(e.views(),o)}));var h=this._handleAddedSplices(e,t,i);x(e.views(),h)},e.prototype._handleAddedSplices=function(e,t,i){for(var n,o,r=t.length,s=0,a=i.length;s<a;++s){var u=i[s],c=n=u.index,l=u.index+u.addedCount;for((null==o||o>u.index)&&(o=n);c<l;++c){var d=M(e,t[c],c,r);e.insertView(c,d.bindingContext,d)}}return o},e}(),L=function(){function e(){}return e.prototype.getCollectionObserver=function(e,t){return e.getMapObserver(t)},e.prototype.instanceChanged=function(e,t){var i=this,n=e.removeAllViews(!0,!e.viewsRequireLifecycle);n instanceof Promise?n.then((function(){return i._standardProcessItems(e,t)})):this._standardProcessItems(e,t)},e.prototype._standardProcessItems=function(e,t){var i,n=0;t.forEach((function(o,r){i=M(e,o,n,t.size,r),e.addView(i.bindingContext,i),++n}))},e.prototype.instanceMutated=function(e,t,i){var n,o,r,s,a,u,c,l,d=[];for(o=0,r=i.length;o<r;++o)switch(n=(c=i[o]).key,c.type){case"update":a=this._getViewIndexByKey(e,n),(l=e.removeView(a,!0,!e.viewsRequireLifecycle))instanceof Promise&&d.push(l),s=M(e,t.get(n),a,t.size,n),e.insertView(a,s.bindingContext,s);break;case"add":u=e.viewCount()<=t.size-1?e.viewCount():t.size-1,s=M(e,t.get(n),u,t.size,n),e.insertView(t.size-1,s.bindingContext,s);break;case"delete":if(void 0===c.oldValue)return;a=this._getViewIndexByKey(e,n),(l=e.removeView(a,!0,!e.viewsRequireLifecycle))instanceof Promise&&d.push(l);break;case"clear":e.removeAllViews(!0,!e.viewsRequireLifecycle);break;default:continue}d.length>0?Promise.all(d).then((function(){x(e.views(),0)})):x(e.views(),0)},e.prototype._getViewIndexByKey=function(e,t){var i,n;for(i=0,n=e.viewCount();i<n;++i)if(e.view(i).bindingContext[e.key]===t)return i},e}(),k=function(){function e(){}return e.prototype.instanceChanged=function(e,t){e.removeAllViews(!0)},e.prototype.getCollectionObserver=function(e,t){},e}(),A=function(){function e(){}return e.prototype.getCollectionObserver=function(){return null},e.prototype.instanceChanged=function(e,t){var i=this,n=e.removeAllViews(!0,!e.viewsRequireLifecycle);n instanceof Promise?n.then((function(){return i._standardProcessItems(e,t)})):this._standardProcessItems(e,t)},e.prototype._standardProcessItems=function(e,t){var i,n,o,r,s=e.viewCount();if((r=s-(t=Math.floor(t)))>0)for(r>s&&(r=s),i=0,n=r;i<n;++i)e.removeView(s-(i+1),!0,!e.viewsRequireLifecycle);else{for(i=s,n=t;i<n;++i)o=M(e,i,i,n),e.addView(o.bindingContext,o);x(e.views(),0)}},e}(),q=function(){function e(){}return e.prototype.getCollectionObserver=function(e,t){return e.getSetObserver(t)},e.prototype.instanceChanged=function(e,t){var i=this,n=e.removeAllViews(!0,!e.viewsRequireLifecycle);n instanceof Promise?n.then((function(){return i._standardProcessItems(e,t)})):this._standardProcessItems(e,t)},e.prototype._standardProcessItems=function(e,t){var i,n=0;t.forEach((function(o){i=M(e,o,n,t.size),e.addView(i.bindingContext,i),++n}))},e.prototype.instanceMutated=function(e,t,i){var n,o,r,s,a,u,c,l=[];for(o=0,r=i.length;o<r;++o)switch(n=(u=i[o]).value,u.type){case"add":var d=Math.max(t.size-1,0);s=M(e,n,d,t.size),e.insertView(d,s.bindingContext,s);break;case"delete":a=this._getViewIndexByValue(e,n),(c=e.removeView(a,!0,!e.viewsRequireLifecycle))instanceof Promise&&l.push(c);break;case"clear":e.removeAllViews(!0,!e.viewsRequireLifecycle);break;default:continue}l.length>0?Promise.all(l).then((function(){x(e.views(),0)})):x(e.views(),0)},e.prototype._getViewIndexByValue=function(e,t){var i,n;for(i=0,n=e.viewCount();i<n;++i)if(e.view(i).bindingContext[e.local]===t)return i},e}(),F=function(){function e(){this.matchers=[],this.strategies=[],this.addStrategy((function(e){return null==e}),new k),this.addStrategy((function(e){return e instanceof Array}),new P),this.addStrategy((function(e){return e instanceof Map}),new L),this.addStrategy((function(e){return e instanceof Set}),new q),this.addStrategy((function(e){return"number"==typeof e}),new A)}return e.prototype.addStrategy=function(e,t){this.matchers.push(e),this.strategies.push(t)},e.prototype.getStrategy=function(e){for(var t=this.matchers,i=0,n=t.length;i<n;++i)if(t[i](e))return this.strategies[i];return null},e}(),z=["focus","if","else","repeat","show","hide","with"];function j(e){var t=e.type,i=null!==t.elementName?t.elementName:t.attributeName;return-1===z.indexOf(i)&&(t.handlesAttached||t.handlesBind||t.handlesCreated||t.handlesDetached||t.handlesUnbind)||t.viewFactory&&N(t.viewFactory)||e.viewFactory&&N(e.viewFactory)}function W(e){var t=e.behaviorInstructions;if(t)for(var i=t.length;i--;)if(j(t[i]))return!0;return e.viewFactory&&N(e.viewFactory)}function N(e){if("_viewsRequireLifecycle"in e)return e._viewsRequireLifecycle;if(e._viewsRequireLifecycle=!1,e.viewFactory)return e._viewsRequireLifecycle=N(e.viewFactory),e._viewsRequireLifecycle;if(e.template.querySelector(".au-animate"))return e._viewsRequireLifecycle=!0,!0;for(var t in e.instructions)if(W(e.instructions[t]))return e._viewsRequireLifecycle=!0,!0;return e._viewsRequireLifecycle=!1,!1}var G=function(){function e(e){Object.assign(this,{local:"items",viewsRequireLifecycle:!0},e)}return e.prototype.viewCount=function(){throw new Error("subclass must implement `viewCount`")},e.prototype.views=function(){throw new Error("subclass must implement `views`")},e.prototype.view=function(e){throw new Error("subclass must implement `view`")},e.prototype.matcher=function(){throw new Error("subclass must implement `matcher`")},e.prototype.addView=function(e,t){throw new Error("subclass must implement `addView`")},e.prototype.insertView=function(e,t,i){throw new Error("subclass must implement `insertView`")},e.prototype.moveView=function(e,t){throw new Error("subclass must implement `moveView`")},e.prototype.removeAllViews=function(e,t){throw new Error("subclass must implement `removeAllViews`")},e.prototype.removeViews=function(e,t,i){throw new Error("subclass must implement `removeView`")},e.prototype.removeView=function(e,t,i){throw new Error("subclass must implement `removeView`")},e.prototype.updateBindings=function(e){throw new Error("subclass must implement `updateBindings`")},e}(),H="__marker_extracted__",U=function(e){function t(t,i,n,o,r,s){var a=e.call(this,{local:"item",viewsRequireLifecycle:N(t)})||this;return a.viewFactory=t,a.instruction=i,a.viewSlot=n,a.lookupFunctions=o.lookupFunctions,a.observerLocator=r,a.key="key",a.value="value",a.strategyLocator=s,a.ignoreMutation=!1,a.sourceExpression=I(a.instruction,"repeat.for"),a.isOneTime=T(a.sourceExpression),a.viewsRequireLifecycle=N(t),a}var i;return p(t,e),i=t,t.prototype.call=function(e,t){this[e](this.items,t)},t.prototype.bind=function(e,t){this.scope={bindingContext:e,overrideContext:t};var i=this.instruction;H in i||(i[H]=this._captureAndRemoveMatcherBinding()),this.matcherBinding=i[H],this.itemsChanged()},t.prototype.unbind=function(){this.scope=null,this.items=null,this.matcherBinding=null,this.viewSlot.removeAll(!0,!0),this._unsubscribeCollection()},t.prototype._unsubscribeCollection=function(){this.collectionObserver&&(this.collectionObserver.unsubscribe(this.callContext,this),this.collectionObserver=null,this.callContext=null)},t.prototype.itemsChanged=function(){var e=this;if(this._unsubscribeCollection(),this.scope){var t=this.items;if(this.strategy=this.strategyLocator.getStrategy(t),!this.strategy)throw new Error("Value for '".concat(this.sourceExpression,"' is non-repeatable"));this.isOneTime||this._observeInnerCollection()||this._observeCollection(),this.ignoreMutation=!0,this.strategy.instanceChanged(this,t),this.observerLocator.taskQueue.queueMicroTask((function(){e.ignoreMutation=!1}))}},t.prototype._getInnerCollection=function(){var e=B(this.sourceExpression);return e?e.evaluate(this.scope,null):null},t.prototype.handleCollectionMutated=function(e,t){this.collectionObserver&&(this.ignoreMutation||this.strategy.instanceMutated(this,e,t))},t.prototype.handleInnerCollectionMutated=function(e,t){var i=this;if(this.collectionObserver&&!this.ignoreMutation){this.ignoreMutation=!0;var n=this.sourceExpression.evaluate(this.scope,this.lookupFunctions);this.observerLocator.taskQueue.queueMicroTask((function(){return i.ignoreMutation=!1})),n===this.items?this.itemsChanged():this.items=n}},t.prototype._observeInnerCollection=function(){var e=this._getInnerCollection(),t=this.strategyLocator.getStrategy(e);return!!t&&(this.collectionObserver=t.getCollectionObserver(this.observerLocator,e),!!this.collectionObserver&&(this.callContext="handleInnerCollectionMutated",this.collectionObserver.subscribe(this.callContext,this),!0))},t.prototype._observeCollection=function(){var e=this.items;this.collectionObserver=this.strategy.getCollectionObserver(this.observerLocator,e),this.collectionObserver&&(this.callContext="handleCollectionMutated",this.collectionObserver.subscribe(this.callContext,this))},t.prototype._captureAndRemoveMatcherBinding=function(){var e=this.viewFactory.viewFactory;if(e){var t=e.template,n=e.instructions;if(i.useInnerMatcher)return D(n);if(Q(t)>1)return;var o=$(t);if(!o.hasAttribute("au-target-id"))return;var r=o.getAttribute("au-target-id");return D(n,r)}},t.prototype.viewCount=function(){return this.viewSlot.children.length},t.prototype.views=function(){return this.viewSlot.children},t.prototype.view=function(e){return this.viewSlot.children[e]},t.prototype.matcher=function(){var e=this.matcherBinding;return e?e.sourceExpression.evaluate(this.scope,e.lookupFunctions):null},t.prototype.addView=function(e,t){var i=this.viewFactory.create();i.bind(e,t),this.viewSlot.add(i)},t.prototype.insertView=function(e,t,i){var n=this.viewFactory.create();n.bind(t,i),this.viewSlot.insert(e,n)},t.prototype.moveView=function(e,t){this.viewSlot.move(e,t)},t.prototype.removeAllViews=function(e,t){return this.viewSlot.removeAll(e,t)},t.prototype.removeViews=function(e,t,i){return this.viewSlot.removeMany(e,t,i)},t.prototype.removeView=function(e,t,i){return this.viewSlot.removeAt(e,t,i)},t.prototype.updateBindings=function(e){for(var t=e,i=t.bindings.length;i--;)E(t.bindings[i]);for(i=t.controllers.length;i--;)for(var n=t.controllers[i].boundProperties.length;n--;)E(t.controllers[i].boundProperties[n].binding)},t.useInnerMatcher=!0,f([a._t],t.prototype,"items",void 0),f([a._t],t.prototype,"local",void 0),f([a._t],t.prototype,"key",void 0),f([a._t],t.prototype,"value",void 0),i=f([(0,a.U4)("repeat"),a.hL,(0,o.WQ)(a.gF,a.Ij,a.eu,a.zT,u.Zr,F)],t)}(G),D=function(e,t){for(var i=Object.keys(e),n=0;n<i.length;n++){var o=i[n];if(void 0===t||o===t){var r=e[o].expressions;if(r)for(var s=0;s<r.length;s++)if("matcher"===r[s].targetProperty){var a=r[s];return r.splice(s,1),a}}}},Q=function(e){for(var t=e.childNodes,i=0,n=0,o=t.length;o>n;++n)1===t[n].nodeType&&++i;return i},$=function(e){for(var t=e.firstChild;null!==t;){if(1===t.nodeType)return t;t=t.nextSibling}return null},K="aurelia-hide",X=".".concat(K," { display:none !important; }");function Y(e){r.RI.shadowDOM&&e&&!e.hasAureliaHideStyle&&(e.hasAureliaHideStyle=!0,r.dv.injectStyles(X,e))}var Z=function(){function e(e,t,i){this.element=e,this.animator=t,this.domBoundary=i}return e.inject=function(){return[r.dv.Element,a.yU,o.Xx.of(r.dv.boundary,!0)]},e.prototype.created=function(){Y(this.domBoundary)},e.prototype.valueChanged=function(e){var t=this.element,i=this.animator;e?i.removeClass(t,K):i.addClass(t,K)},e.prototype.bind=function(e){this.valueChanged(this.value)},f([(0,a.U4)("show")],e)}(),J=function(){function e(e,t,i){this.element=e,this.animator=t,this.domBoundary=i}return e.inject=function(){return[r.dv.Element,a.yU,o.Xx.of(r.dv.boundary,!0)]},e.prototype.created=function(){Y(this.domBoundary)},e.prototype.valueChanged=function(e){e?this.animator.addClass(this.element,K):this.animator.removeClass(this.element,K)},e.prototype.bind=function(e){this.valueChanged(this.value)},e.prototype.value=function(e){throw new Error("Method not implemented.")},f([(0,a.U4)("hide")],e)}(),ee=function(){function e(){}return e.prototype.sanitize=function(e){throw new Error("To protect the application against a wide variety of sophisticated XSS attacks.\nPlease see https://aurelia.io/docs/binding/basics#element-content for instructions on how to use a secure solution like DOMPurify or sanitize-html.")},e}(),te=function(){function e(e){this.sanitizer=e}return e.prototype.toView=function(e){return null==e?null:this.sanitizer.sanitize(e)},f([(0,u.Yw)("sanitizeHTML"),(0,o.WQ)(ee)],e)}(),ie=function(){function e(e,t){this.viewFactory=e,this.viewSlot=t,this.view=null}return e.prototype.bind=function(e,t){null===this.view&&(this.view=this.viewFactory.create(),this.viewSlot.add(this.view)),this.view.bind(e,t)},e.prototype.unbind=function(){this.view.unbind()},f([(0,a.U4)("replaceable"),a.hL,(0,o.WQ)(a.gF,a.eu)],e)}(),ne=function(){function e(e,t){this.element=e,this.taskQueue=t,this.isAttached=!1,this.needsApply=!1}return e.inject=function(){return[r.dv.Element,s.P]},e.prototype.valueChanged=function(){this.isAttached?this._apply():this.needsApply=!0},e.prototype._apply=function(){var e=this;this.value?this.taskQueue.queueMicroTask((function(){e.value&&e.element.focus()})):this.element.blur()},e.prototype.attached=function(){this.isAttached=!0,this.needsApply&&(this.needsApply=!1,this._apply()),this.element.addEventListener("focus",this),this.element.addEventListener("blur",this)},e.prototype.detached=function(){this.isAttached=!1,this.element.removeEventListener("focus",this),this.element.removeEventListener("blur",this)},e.prototype.handleEvent=function(e){"focus"===e.type?this.value=!0:r.dv.activeElement!==this.element&&(this.value=!1)},f([(0,a.U4)("focus",u.BG.twoWay)],e)}(),oe=/url\((?!['"]data)([^)]+)\)/gi,re=function(){function e(e){this.address=e,this._scoped=null,this._global=!1,this._alreadyGloballyInjected=!1}return e.prototype.initialize=function(e,t){this._scoped=new t(this)},e.prototype.register=function(e,t){"scoped"===t?e.registerViewEngineHooks(this._scoped):this._global=!0},e.prototype.load=function(e){var t=this;return e.get(c.aH).loadText(this.address).catch((function(){return null})).then((function(e){return e=function(e,t){if("string"!=typeof t)throw new Error("Failed loading required CSS file: ".concat(e));return t.replace(oe,(function(t,i){var n=i.charAt(0);return"'"!==n&&'"'!==n||(i=i.substr(1,i.length-2)),"url('"+(0,l.Yc)(i,e)+"')"}))}(t.address,e),t._scoped.css=e,t._global&&(t._alreadyGloballyInjected=!0,r.dv.injectStyles(e)),t}))},e}(),se=function(){function e(e){this.owner=e,this.css=null}return e.prototype.beforeCompile=function(e,t,i){i.targetShadowDOM?r.dv.injectStyles(this.css,e,!0):r.RI.scopedCSS?r.dv.injectStyles(this.css,e,!0).setAttribute("scoped","scoped"):this._global&&!this.owner._alreadyGloballyInjected&&(r.dv.injectStyles(this.css),this.owner._alreadyGloballyInjected=!0)},e}(),ae=function(){function e(){}return e.prototype.bind=function(e,t){e.targetObserver=new u.Re(e.target,e.targetProperty)},e.prototype.unbind=function(e,t){},f([(0,u.gz)("attr")],e)}(),ue={bind:function(e,t,i){e.originalMode=e.mode,e.mode=this.mode},unbind:function(e,t){e.mode=e.originalMode,e.originalMode=null}},ce=function(){function e(){this.mode=u.BG.oneTime}return f([(0,d.co)(ue),(0,u.gz)("oneTime")],e)}(),le=function(){function e(){this.mode=u.BG.toView}return f([(0,d.co)(ue),(0,u.gz)("oneWay")],e)}(),de=function(){function e(){this.mode=u.BG.toView}return f([(0,d.co)(ue),(0,u.gz)("toView")],e)}(),he=function(){function e(){this.mode=u.BG.fromView}return f([(0,d.co)(ue),(0,u.gz)("fromView")],e)}(),ve=function(){function e(){this.mode=u.BG.twoWay}return f([(0,d.co)(ue),(0,u.gz)("twoWay")],e)}();function pe(e){var t=this,i=this.throttleState,n=+new Date-i.last;if(n>=i.delay)return clearTimeout(i.timeoutId),i.timeoutId=null,i.last=+new Date,void this.throttledMethod(e);i.newValue=e,null===i.timeoutId&&(i.timeoutId=setTimeout((function(){i.timeoutId=null,i.last=+new Date,t.throttledMethod(i.newValue)}),i.delay-n))}var fe=function(){function e(){}return e.prototype.bind=function(e,t,i){void 0===i&&(i=200);var n="updateTarget";e.callSource?n="callSource":e.updateSource&&e.mode===u.BG.twoWay&&(n="updateSource"),e.throttledMethod=e[n],e.throttledMethod.originalName=n,e[n]=pe,e.throttleState={delay:i,last:0,timeoutId:null}},e.prototype.unbind=function(e,t){e[e.throttledMethod.originalName]=e.throttledMethod,e.throttledMethod=null,clearTimeout(e.throttleState.timeoutId),e.throttleState=null},f([(0,u.gz)("throttle")],e)}(),we={};function ge(e){var t=this,i=this.debounceState;clearTimeout(i.timeoutId),i.timeoutId=setTimeout((function(){return t.debouncedMethod(e)}),i.delay)}function ye(e,t,i){var n=this,o=this.debounceState;if(clearTimeout(o.timeoutId),e!==o.callContextToDebounce)return o.oldValue=we,void this.debouncedMethod(e,t,i);o.oldValue===we&&(o.oldValue=i),o.timeoutId=setTimeout((function(){var i=o.oldValue;o.oldValue=we,n.debouncedMethod(e,t,i)}),o.delay)}var me=function(){function e(){}return e.prototype.bind=function(e,t,i){void 0===i&&(i=200);var n=void 0!==e.callSource,o=n?"callSource":"call",r=n?ge:ye,s=e.mode,a=s===u.BG.twoWay||s===u.BG.fromView?u.Tw:u.RH;e.debouncedMethod=e[o],e.debouncedMethod.originalName=o,e[o]=r,e.debounceState={callContextToDebounce:a,delay:i,timeoutId:0,oldValue:we}},e.prototype.unbind=function(e,t){e[e.debouncedMethod.originalName]=e.debouncedMethod,e.debouncedMethod=null,clearTimeout(e.debounceState.timeoutId),e.debounceState=null},f([(0,u.gz)("debounce")],e)}();function be(e){var t=function(e){return e.path&&e.path[0]||e.deepPath&&e.deepPath[0]||e.target}(e);this.target===t&&this.selfEventCallSource(e)}var Ce=function(){function e(){}return e.prototype.bind=function(e,t){if(!e.callSource||!e.targetEvent)throw new Error("Self binding behavior only supports event.");e.selfEventCallSource=e.callSource,e.callSource=be},e.prototype.unbind=function(e,t){e.callSource=e.selfEventCallSource,e.selfEventCallSource=null},f([(0,u.gz)("self")],e)}(),_e=function(){function e(){this.signals={}}return e.prototype.signal=function(e){var t=this.signals[e];if(t)for(var i=t.length;i--;)t[i].call(u.RH)},e}(),Se=function(){function e(e){this.signals=e.signals}return e.inject=function(){return[_e]},e.prototype.bind=function(e,t){for(var i=[],n=2;n<arguments.length;n++)i[n-2]=arguments[n];if(!e.updateTarget)throw new Error("Only property bindings and string interpolation bindings can be signaled.  Trigger, delegate and call bindings cannot be signaled.");var o=this.signals;if(1===i.length){var r=i[0];(o[r]||(o[r]=[])).push(e),e.signalName=r}else{if(!(i.length>1))throw new Error("Signal name is required.");for(var s=i.length;s--;){var a=i[s];(o[a]||(o[a]=[])).push(e)}e.signalName=i}},e.prototype.unbind=function(e,t){var i=this.signals,n=e.signalName;if(e.signalName=null,Array.isArray(n))for(var o=n,r=o.length;r--;){var s;(s=i[o[r]]).splice(s.indexOf(e),1)}else(s=i[n]).splice(s.indexOf(e),1)},f([(0,u.gz)("signal")],e)}(),Ve="The updateTrigger binding behavior can only be applied to two-way/ from-view bindings on input/select elements.",xe=function(){function e(){}return e.prototype.bind=function(e,t){for(var i=[],n=2;n<arguments.length;n++)i[n-2]=arguments[n];if(0===i.length)throw new Error("The updateTrigger binding behavior requires at least one event name argument: eg <input value.bind=\"firstName & updateTrigger:'blur'\">");if(e.mode!==u.BG.twoWay&&e.mode!==u.BG.fromView)throw new Error(Ve);var o=e.observerLocator.getObserver(e.target,e.targetProperty);if(!o.handler)throw new Error(Ve);e.targetObserver=o,o.originalHandler=e.targetObserver.handler;var r=new u.vP(i);o.handler=r},e.prototype.unbind=function(e,t){var i=e.targetObserver;i.handler.dispose(),i.handler=i.originalHandler,i.originalHandler=null},f([(0,u.gz)("updateTrigger")],e)}();function Me(e){r.dv.injectStyles(X),e.globalResources(w,C,_,S,U,Z,J,ie,ne,te,ce,le,de,he,ve,fe,me,Ce,Se,xe,ae),function(e){var t=e.container.get(a.I6),i=e.aurelia.loader;t.addResourcePlugin(".html",{fetch:function(e){return i.loadTemplate(e).then((function(t){var i,n=t.template.getAttribute("bindable"),o=t.template.getAttribute("use-shadow-dom"),r=/([^\/^\?]+)\.html/i.exec(e)[1].toLowerCase();return n?(n=n.split(",").map((function(e){return e.trim()})),t.template.removeAttribute("bindable")):n=[],(i={})[r]=function(e){for(var t=e.name,i=e.viewUrl,n=e.bindableNames,o=e.useShadowDOMmode,r=function(){function e(){}return e.prototype.bind=function(e){this.$parent=e},f([(0,a.EM)(t),(0,a.lQ)(i)],e)}(),s=0,u=n.length;s<u;++s)(0,a._t)(n[s])(r);switch(o){case"open":(0,a.Cu)({mode:"open"})(r);break;case"closed":(0,a.Cu)({mode:"closed"})(r);break;case"":(0,a.Cu)(r);break;case null:break;default:(0,h.getLogger)("aurelia-html-only-element").warn('Expected \'use-shadow-dom\' value to be "close", "open" or "", received '.concat(o))}return r}({name:r,viewUrl:e,bindableNames:n,useShadowDOMmode:o}),i}))}})}(e);var t=e.container.get(a.I6),i={fetch:function(e){var t;return(t={})[e]=function(e){var t=function(t){function i(){return null!==t&&t.apply(this,arguments)||this}return p(i,t),f([(0,a.Zc)(new re(e))],i)}(se);return t}(e),t}};[".css",".less",".sass",".scss",".styl"].forEach((function(e){return t.addResourcePlugin(e,i)}))}}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8466],{"dashboard/dashboard":(e,t,o)=>{o.r(t),o.d(t,{Dashboard:()=>l});var n=o(15215),a=o("aurelia-framework"),i=o(18776),r=o(62914),s=o(43050),d=o(54995),c=o(85975);let l=class{#e;#t;constructor(e,t,o){this.router=t,this.maxItemsPerFeed=10,this.#e=e,this.#t=o}attached(){this.#e.screenView({name:"Dashboard",class:"Dashboard",params:{installed_games:Object.values(this.state.installedGameVersions).length>0}})}bind(){this.myGames=this.#t.getFilteredFeed(s.DE,{maxItems:10}),this.mostPopular=this.#t.getFilteredFeed(s.dL,{maxItems:10}),this.freeGames=this.#t.getFilteredFeed(s.Vr,{maxItems:10,tags:["free"]})}detached(){this.myGames.dispose(),this.mostPopular.dispose()}get gameFeedsVisible(){return 1+(this.myGames?.items.length?1:0)+(this.freeGames?.items.length?1:0)}get shouldShowMyGamesCoachingTip(){return 0===Object.values(this.state.gameHistory).filter((e=>!!e.lastPlayedAt)).length}get shouldShowFreeGamesCoachingTip(){return 0===this.myGames?.items.length&&!!this.state.timestamps.appsRefreshedAt}};(0,n.Cg)([(0,a.computedFrom)("myGames.items","mostPopular","freeGames.items"),(0,n.Sn)("design:type",Number),(0,n.Sn)("design:paramtypes",[])],l.prototype,"gameFeedsVisible",null),(0,n.Cg)([(0,a.computedFrom)("state.gameHistory"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],l.prototype,"shouldShowMyGamesCoachingTip",null),(0,n.Cg)([(0,a.computedFrom)("myGames.items","timestamps.appsRefreshedAt"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],l.prototype,"shouldShowFreeGamesCoachingTip",null),l=(0,n.Cg)([(0,d.m6)((e=>e.state.pipe((0,c.r)("catalog","gameHistory","installedGameVersions","gameHistory","timestamps")))),(0,a.autoinject)(),(0,n.Sn)("design:paramtypes",[r.j0,i.Ix,s.Y2])],l)},"dashboard/dashboard.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});const n='<template> <require from="./dashboard.scss"></require> <require from="gamification/objectives"></require> <require from="./resources/elements/announcements"></require> <require from="./resources/elements/activity"></require> <require from="./resources/elements/featured-content"></require> <require from="../cheats/resources/elements/horizontal-game-feed"></require> <require from="../cheats/resources/elements/routed-text-button"></require> <require from="../resources/elements/coaching-tip"></require> <section class="dashboard view-background au-animate" css="--game-feeds-visible: ${gameFeedsVisible}"> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" overflow-fade="vertical"> <div class="dashboard-layout"> <div class="column"> <featured-content></featured-content> <section class="game-feed" if.bind="myGames.items.length"> <header> <h2> <span class="relative"> ${\'dashboard.my_games\' | i18n} </span> </h2> <routed-text-button route="collection" params.bind="{previousRoute: \'dashboard\', slug: \'my-games\'}"> ${\'dashboard.see_all\' | i18n} </routed-text-button> </header> <article class="my-games-feed"> <horizontal-game-feed items.bind="myGames.items" location="my_games" previous-route="dashboard" parent-route="collection/my-games" metadata-types.bind="[\'status-badges\']" metadata-position="on-card"></horizontal-game-feed> </article> </section> <section class="game-feed" if.bind="mostPopular.items.length"> <header> <h2>${\'dashboard.most_popular\' | i18n}</h2> <routed-text-button route="collection" params.bind="{previousRoute: \'dashboard\', slug: \'most-popular\'}">${\'dashboard.see_all\' | i18n}</routed-text-button> </header> <article> <horizontal-game-feed items.bind="mostPopular.items" location="most_popular" previous-route="dashboard" metadata-types.bind="[\'status-badges\']" metadata-position="on-card"></horizontal-game-feed> </article> </section> <section class="game-feed"> <header> <h2>${\'dashboard.free_games\' | i18n}</h2> <routed-text-button route="collection" params.bind="{previousRoute: \'dashboard\', slug: \'free-games\'}">${\'dashboard.see_all\' | i18n}</routed-text-button> </header> <article class="free-games-feed"> <horizontal-game-feed items.bind="freeGames.items" location="free_games" previous-route="dashboard" metadata-types.bind="[\'status-badges\']" metadata-position="on-card"></horizontal-game-feed> </article> </section> </div> <div class="column sections"> <objectives></objectives> <section class="announcements"> <header> <h2>${\'dashboard.announcements\' | i18n}</h2> <routed-text-button route="queue"> ${\'dashboard.see_upcoming_games\' | i18n} </routed-text-button> </header> <article class="overflow-fade__wrapper overflow-fade__wrapper--vertical--no-scrollbar"> <div overflow-fade="vertical"> <announcements></announcements> <activity></activity> </div> </article> </section> </div> </div> </div> </div> </section> </template> '},"dashboard/dashboard.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var n=o(31601),a=o.n(n),i=o(76314),r=o.n(i)()(a());r.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}section.dashboard{--game-feed-height: 262px;--game-feeds-visible: 0}section.dashboard .view-scrollable{padding-bottom:40px !important}section.dashboard .dashboard-layout{z-index:0;display:grid;grid-template-columns:1fr 400px;padding:8px 0px 4px;gap:20px;width:100%}section.dashboard .dashboard-layout .column{gap:24px;height:calc(100vh - var(--constant--appHeaderHeight) + 40px);display:flex;flex-direction:column;min-width:0}section.dashboard .dashboard-layout .column dashboard-free-games{display:flex}section.dashboard .dashboard-layout .column dashboard-ad{margin-top:0}section.dashboard .dashboard-layout header{margin-bottom:16px}section.dashboard .dashboard-layout header h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;margin:0}.theme-default section.dashboard .dashboard-layout header h2{color:#fff}.theme-purple-pro section.dashboard .dashboard-layout header h2{color:#fff}.theme-green-pro section.dashboard .dashboard-layout header h2{color:#fff}.theme-orange-pro section.dashboard .dashboard-layout header h2{color:#fff}.theme-pro section.dashboard .dashboard-layout header h2{color:#fff}section.dashboard .game-feed{display:flex;flex-direction:column;flex:0 0 auto}.theme-default section.dashboard .game-feed{--overflow-fade--background: #17191b}.theme-purple-pro section.dashboard .game-feed{--overflow-fade--background: #18181e}.theme-green-pro section.dashboard .game-feed{--overflow-fade--background: #151b1d}.theme-orange-pro section.dashboard .game-feed{--overflow-fade--background: #19191b}.theme-pro section.dashboard .game-feed{--overflow-fade--background: #141414}section.dashboard .game-feed header{display:flex;align-items:baseline;gap:8px}section.dashboard .game-feed:hover routed-text-button{opacity:1}section.dashboard .game-feed routed-text-button{opacity:0;transition:opacity .2s}section.dashboard .sections section{display:flex;flex-direction:column;flex:1;overflow:hidden}section.dashboard .sections section header{display:flex;align-items:center}section.dashboard .sections section article{padding:0}@media(forced-colors: active){body:not(.override-contrast-mode) section.dashboard .sections section article{border:1px solid #fff}}section.dashboard .sections section article [overflow-fade]{height:100%;overflow:hidden}section.dashboard .sections section article [overflow-fade]::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}section.dashboard .sections section article [overflow-fade]::-webkit-scrollbar-thumb:window-inactive,section.dashboard .sections section article [overflow-fade]::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}section.dashboard .sections section article [overflow-fade]::-webkit-scrollbar-thumb:window-inactive:hover,section.dashboard .sections section article [overflow-fade]::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}section.dashboard .sections section article [overflow-fade]::-webkit-scrollbar-button:single-button:vertical:decrement{height:10px}section.dashboard .sections section article [overflow-fade]::-webkit-scrollbar-button:single-button:vertical:increment{height:10px}section.dashboard .sections section article [overflow-fade]::-webkit-scrollbar{background:rgba(0,0,0,0)}section.dashboard .sections section article [overflow-fade]:hover{overflow-y:overlay}.theme-default section.dashboard .sections section.announcements{--overflow-fade--background: #17191b}.theme-purple-pro section.dashboard .sections section.announcements{--overflow-fade--background: #18181e}.theme-green-pro section.dashboard .sections section.announcements{--overflow-fade--background: #151b1d}.theme-orange-pro section.dashboard .sections section.announcements{--overflow-fade--background: #19191b}.theme-pro section.dashboard .sections section.announcements{--overflow-fade--background: #141414}section.dashboard .sections section.announcements activity{display:block;padding:20px}section.dashboard .sections section.announcements header{justify-content:space-between;display:flex;align-items:baseline}section.dashboard announcements{border-top-left-radius:10px;border-top-right-radius:10px;overflow:hidden}section.dashboard announcements.hide{display:none}section.dashboard announcements:not(.hide)+article{border-top-left-radius:0;border-top-right-radius:0}section.dashboard announcements:not(.hide)+article::-webkit-scrollbar-button:single-button:vertical:decrement{height:0}section.dashboard announcements:not(.hide)+article::-webkit-scrollbar-button:single-button:vertical:increment{height:10px}section.dashboard .relative{position:relative}',""]);const s=r},"dashboard/resources/elements/activity":(e,t,o)=>{o.r(t),o.d(t,{Activity:()=>f});var n=o(15215),a=o("aurelia-framework"),i=o(20770),r=o(32534),s=o(62914),d=o(50654),c=o(43050),l=o(24008),m=o(54995),u=o(70236),p=o(48881);let f=class{#o;#n;#e;#a;constructor(e,t,o,n){this.followed=!1,this.followInProgress=!1,this.#o=e,this.#n=t,this.#e=o,this.#a=n}attached(){this.myGamesActivityFeed=this.#o.getFilteredFeed(c.Rq,{}),this.#i(),this.#r()}detached(){this.myGamesActivityFeed?.dispose()}catalogChanged(){this.#i()}#i(){const e=Object.values(this.catalog.games).filter((e=>(0,u.Lt)(e.flags,l.rT.Available))).sort(((e,t)=>(t.trainer?.updatedAt??"").localeCompare(e.trainer?.updatedAt??""))),t=[];let o;for(const n of e)if(o?.titleId===n.titleId)o.platformIds.includes(n.platformId)||o.platformIds.push(n.platformId);else if(o=this.#s(n),t.push(o),10===t.length)break;this.feed=t}#s(e){const t=this.catalog.titles[e.titleId],o=this.catalog.creators[e.creatorId??""];return{type:e.trainer?.createdAt===e.trainer?.updatedAt?"trainer_release":"trainer_update",titleId:t.id,titleName:t.name,titleThumbnail:t.thumbnail,gameId:e.id,creatorUsername:o.username,creatorAvatar:o.avatar,happenedAt:e.trainer?.updatedAt??"",platformIds:[e.platformId]}}get myGamesFeed(){return this.myGamesActivityFeed?this.myGamesActivityFeed.items.slice(0,10).map((e=>({type:e.createdAt===e.updatedAt?"trainer_release":"trainer_update",titleId:e.titleId,titleName:e.titleName,titleThumbnail:e.titleThumbnail,gameId:e.gameId,creatorUsername:e.creator,creatorAvatar:e.creatorAvatar,happenedAt:e.updatedAt,platformIds:e.platformIds}))):[]}#r(){if(this.selectedFeed="all",this.myGamesFeed[0]){const e=Date.now(),t=(0,r.A)(new Date(this.myGamesFeed[0].happenedAt??""),e)<=7,o=(0,r.A)(new Date(this.lastMyGamesActivityFeedSeen),e)<=7;!t||this.lastMyGamesActivityFeedSeen&&o||(this.selectedFeed="my_games")}}selectedFeedChanged(){"my_games"===this.selectedFeed&&this.#n.dispatch(p.vk,"lastMyGamesActivityFeedSeen")}get selectedFeedItems(){return this.feed&&this.myGamesActivityFeed?"all"===this.selectedFeed?this.feed:this.myGamesFeed:[]}get shouldShowFollowPrompt(){return!this.lastFollowedMyGames&&this.myGamesActivityFeed?.items.length>0}async follow(){if(!this.followInProgress){let e;this.followInProgress=!0;try{e=this.#o.getFilteredFeed(c.AD,{}),e.updateItems();const t=e.items.map((e=>e.gameId)).filter((e=>!!e)),o=[...new Set(t)];await this.#a.followGames(o,2),await this.#n.dispatch(p.vk,"lastFollowedMyGames"),this.#e.event("my_games_dashboard_follow",{},s.Io),this.followed=!0}catch{}finally{this.followInProgress=!1,e?.dispose()}}}};(0,n.Cg)([a.observable,(0,n.Sn)("design:type",String)],f.prototype,"selectedFeed",void 0),(0,n.Cg)([(0,a.computedFrom)("myGamesActivityFeed.items"),(0,n.Sn)("design:type",Array),(0,n.Sn)("design:paramtypes",[])],f.prototype,"myGamesFeed",null),(0,n.Cg)([(0,a.computedFrom)("selectedFeed","feed","myGamesFeed"),(0,n.Sn)("design:type",Array),(0,n.Sn)("design:paramtypes",[])],f.prototype,"selectedFeedItems",null),(0,n.Cg)([(0,a.computedFrom)("lastFollowedMyGames","myGamesActivityFeed.items"),(0,n.Sn)("design:type",Boolean),(0,n.Sn)("design:paramtypes",[])],f.prototype,"shouldShowFollowPrompt",null),f=(0,n.Cg)([(0,m.m6)({selectors:{catalog:(0,m.$t)((e=>e.catalog)),lastMyGamesActivityFeedSeen:(0,m.$t)((e=>e.timestamps?.lastMyGamesActivityFeedSeen)),lastFollowedMyGames:(0,m.$t)((e=>e.timestamps?.lastFollowedMyGames))}}),(0,a.autoinject)(),(0,n.Sn)("design:paramtypes",[c.Y2,i.il,s.j0,d.O])],f)},"dashboard/resources/elements/activity.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var n=o(14385),a=o.n(n),i=new URL(o(16280),o.b),r=new URL(o(5263),o.b);const s='<template> <require from="./activity.scss"></require> <require from="../../../resources/elements/title-thumbnail"></require> <nav class="tabs" if.bind="myGamesFeed.length"> <button class="${selectedFeed === \'all\' ? \'selected\' : \'\'}" click.delegate="selectedFeed = \'all\'"> <span>${\'activity.all\' | i18n}</span> </button> <button class="${selectedFeed === \'my_games\' ? \'selected\': \'\'}" click.delegate="selectedFeed = \'my_games\'"> <span>${\'activity.my_games\' | i18n}</span> </button> </nav> <div class="follow-prompt" if.bind="selectedFeed === \'my_games\' && (shouldShowFollowPrompt || followed)"> <div class="prompt wrapper ${followed ? \'hidden\' : \'\'}"> <span innerhtml.bind="\'activity.get_notifications_for_games_you_care_about\' | i18n | markdown"></span> <button click.delegate="follow()" disabled.bind="followInProgress">${\'activity.follow_all\' | i18n}</button> </div> <a class="followed-message wrapper ${followed ? \'\' : \'hidden\'}" href="wemod:/settings/notifications"> <i class="checkmark"><inline-svg src="'+a()(i)+'"></inline-svg></i> <span innerhtml.bind="\'activity.your_notifications_are_set\' | i18n | markdown"></span> <i class="caret"><inline-svg src="'+a()(r)+'"></inline-svg></i> </a> </div> <div class="items"> <a repeat.for="item of selectedFeedItems" class="item" if.bind="item.type === \'trainer_release\' || item.type === \'trainer_update\'" route-href="route: title; params.bind: {titleId: item.titleId, gameId: item.gameId, previousRoute: \'dashboard\'}" title-link="value.bind: \'activity\'; title-id.bind: item.titleId; game-id.bind: item.gameId;"> <title-thumbnail class="thumbnail" src.bind="item.titleThumbnail" width="120"></title-thumbnail> <div class="content"> <template if.bind="item.type === \'trainer_release\'"> ${\'activity.$creator_created_cheats_for\' | i18n:{creator: item.creatorUsername}} </template> <strong> ${item.titleName} <i repeat.for="platformId of item.platformIds" class="platform-icon" title.bind="platformId | platformName"> <inline-svg src.bind="platformId | platformIconSvg"></inline-svg> </i> </strong> <template if.bind="item.type === \'trainer_update\'"> ${\'activity.was_updated_by_$creator\' | i18n:{creator: item.creatorUsername}} </template> <span class="time">${item.happenedAt | i18nElaspedTime}</span> </div> </a> </div> </template> '},"dashboard/resources/elements/activity.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var n=o(31601),a=o.n(n),i=o(76314),r=o.n(i)()(a());r.push([e.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}activity{position:relative;z-index:0;background:var(--theme--background-accent);border-radius:12px}activity .tabs{display:flex;margin:-20px -20px 20px -20px;position:relative}activity .tabs:before{content:"";position:absolute;left:0;right:0;bottom:0;border-bottom:1px solid rgba(255,255,255,.1)}activity .tabs button{font-weight:600;font-size:16px;line-height:25px;color:rgba(255,255,255,.5);padding:0 9.5px;margin:0;background:rgba(0,0,0,0);border:none;flex:0 0 auto;transition:color .15s}activity .tabs button.selected{color:#fff}activity .tabs button.selected>span:before{border-bottom-color:#fff}activity .tabs button>span{display:inline-block;padding:14px 0;position:relative}activity .tabs button>span:before{content:"";position:absolute;left:0;right:0;bottom:0;border-bottom:1px solid rgba(0,0,0,0);transition:border-color .15s}activity .tabs button:first-of-type{padding-left:19px}activity .tabs button:last-of-type{padding-right:19px}activity .items .item{display:flex;position:relative;text-decoration:none;align-items:center}activity .items .item:nth-child(1n+0) img{animation-delay:0.1s}activity .items .item:nth-child(2n+0) img{animation-delay:0.2s}activity .items .item:nth-child(3n+0) img{animation-delay:0.3s}activity .items .item:nth-child(4n+0) img{animation-delay:0.4s}activity .items .item:nth-child(5n+0) img{animation-delay:0.5s}activity .items .item:nth-child(6n+0) img{animation-delay:0.6s}activity .items .item:nth-child(7n+0) img{animation-delay:0.7s}activity .items .item:nth-child(8n+0) img{animation-delay:0.8s}activity .items .item:nth-child(9n+0) img{animation-delay:0.9s}activity .items .item+.item{margin-top:20px}activity .items .item .thumbnail{display:inline-block;background:rgba(0,0,0,.1);width:74px;height:34px;border-radius:5px;flex:0 0 auto;margin-right:14px;position:relative;z-index:1;border-radius:2px;overflow:hidden}activity .items .item .thumbnail:not(.loaded){animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box}activity .items .item .content{font-size:14px;line-height:21px;line-height:19px;font-weight:500;color:rgba(255,255,255,.6);flex:1}activity .items .item .content strong{font-weight:700;color:#fff}activity .items .item .content .platform-icon{vertical-align:middle;margin:0 3px 3px}activity .items .item .content .platform-icon svg *{fill:rgba(255,255,255,.4)}activity .items .item .content .time{font-size:12px;line-height:18px;font-weight:500;color:rgba(255,255,255,.3);white-space:nowrap}activity .items .item .content .time:before{content:" · "}activity .follow-prompt{background:rgba(var(--color--accent--rgb), 0.12);position:relative;margin:-20px -20px 20px}activity .follow-prompt .wrapper{font-size:14px;line-height:21px;transition:opacity .15s,transform .15s,visibility 0s .15s;visibility:visible;opacity:1;transform:translateX(0);padding:15px 20px;color:rgba(255,255,255,.6);transition-delay:0s;display:flex;align-items:center}activity .follow-prompt .wrapper strong{font-weight:700;color:var(--color--accent)}activity .follow-prompt .wrapper em{font-style:normal;color:rgba(255,255,255,.8)}activity .follow-prompt .wrapper.hidden{visibility:hidden;opacity:0;transform:translateX(10px)}activity .follow-prompt button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px;background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}activity .follow-prompt button,activity .follow-prompt button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) activity .follow-prompt button{border:1px solid #fff}}activity .follow-prompt button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}activity .follow-prompt button>*:first-child{padding-left:0}activity .follow-prompt button>*:last-child{padding-right:0}activity .follow-prompt button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) activity .follow-prompt button svg *{fill:CanvasText}}activity .follow-prompt button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) activity .follow-prompt button svg{opacity:1}}activity .follow-prompt button img{height:50%}activity .follow-prompt button:disabled{opacity:.3}activity .follow-prompt button:disabled,activity .follow-prompt button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){activity .follow-prompt button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}activity .follow-prompt button:not(:disabled):hover svg{opacity:1}}activity .follow-prompt button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){activity .follow-prompt button:hover{filter:brightness(1.1)}}activity .follow-prompt .prompt span{flex:1 1 auto}activity .follow-prompt .prompt button{margin-left:10px}activity .follow-prompt .followed-message{position:absolute;left:0;top:0;width:100%;height:100%;display:flex;align-items:center}activity .follow-prompt .followed-message span{margin:0 6px;flex:1 1 auto}activity .follow-prompt .followed-message .checkmark svg *{fill:var(--color--accent)}activity .follow-prompt .followed-message .caret{transition:transform .15s ease-in-out}activity .follow-prompt .followed-message .caret svg *{fill:rgba(255,255,255,.4);transition:fill .15s}activity .follow-prompt .followed-message:hover .caret{transform:translateX(3px)}activity .follow-prompt .followed-message:hover .caret svg *{fill:#fff}',""]);const s=r},"dashboard/resources/elements/announcements":(e,t,o)=>{o.r(t),o.d(t,{Announcements:()=>s});var n=o(15215),a=o("aurelia-framework"),i=o(68368),r=o(54995);let s=class{#d;#c;constructor(e){this.#d=e}#i(){this.announcements=this.catalog.announcements.filter((e=>this.#d.isApplicable(e)))}bind(){this.#c=this.#d.onReevaluationRequired((()=>this.#i())),this.#i()}unbind(){this.#c?.dispose(),this.#c=null}catalogChanged(){this.#i()}};s=(0,n.Cg)([(0,r.m6)({selectors:{catalog:(0,r.$t)((e=>e.catalog))}}),(0,a.autoinject)(),(0,n.Sn)("design:paramtypes",[i.u])],s)},"dashboard/resources/elements/announcements.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});const n='<template class="${!announcements.length ? \'hide\' : \'\'}"> <require from="./announcements.scss"></require> <require from="../../../resources/elements/alert-icon.html"></require> <require from="../../../resources/elements/happy-icon.html"></require> <a repeat.for="announcement of announcements" class="announcement ${announcement.type}" href.bind="announcement.url" target="_blank"> <div class="icon"> <alert-icon if.bind="announcement.type == \'alert\'"></alert-icon> <happy-icon else></happy-icon> </div> <div class="content"> <p innerhtml.bind="announcement.message | markdown"></p> </div> </a> </template> '},"dashboard/resources/elements/announcements.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var n=o(31601),a=o.n(n),i=o(76314),r=o.n(i)()(a());r.push([e.id,'announcements{display:block}announcements.hide{display:none}announcements .announcement{background:var(--theme--background-accent);display:flex;align-items:center;padding:20px;text-decoration:none}announcements .announcement,announcements .announcement *{cursor:default}announcements .announcement[href],announcements .announcement[href] *{cursor:pointer}announcements .announcement .icon{margin-right:9px}announcements .announcement .icon>i{display:inline-block}announcements .announcement .icon>i:after{content:"";display:block;clear:both}announcements .announcement .icon>i svg{float:left}announcements .announcement .icon>i svg{max-width:15px;max-height:15px}announcements .announcement .icon>i svg *{color:#fff}announcements .announcement .content{flex:1}announcements .announcement .content,announcements .announcement .content *{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.6)}announcements .announcement .content em{font-weight:700;color:var(--theme--highlight);font-style:normal}announcements .announcement .content strong{font-weight:700;color:rgba(255,255,255,.8)}announcements .announcement .content p{margin:0;padding:0}announcements .announcement .content p+p{margin-top:10px}announcements .announcement.alert{background:rgba(var(--color--alert--rgb), 0.12)}announcements .announcement.alert .content em{color:var(--color--alert)}',""]);const s=r},"dashboard/resources/elements/featured-content":(e,t,o)=>{o.r(t),o.d(t,{FeaturedContent:()=>y});var n,a=o(15215),i=o("aurelia-framework"),r=o(15652),s=o(71133),d=o(21516),c=o(33511),l=o(41572),m=o(9374),u=o(62914),p=o(68368),f=o(20057),h=o(54995),g=o(38777),b=o(32426);const v={forward:(e,t)=>(e+1)%t.length,backward:(e,t)=>(e-1+t.length)%t.length};let y=n=class{#l;#m;#e;#d;#u;#p;static{this.promotionalFeaturedContentId="promotional-featured-content"}#f;constructor(e,t,o){this.autoplaying=!0,this.autoplayDuration=1e4,this.activeTransition=null,this.currentIndex=0,this.nextIndex=null,this.title="",this.#l=new r.B7,this.#m=null,this.#p=!1,this.isWindowFocused=!1,this.windowFocusSubscription=null,this.FeaturedContent=n,this.next=this.next.bind(this),this.previous=this.previous.bind(this),this.#e=e,this.#d=t,this.#f=o}generateAndTrackActiveImpressionUrl(){const e=`${this.promotionalFeaturedContent?.trackingImageSrc}${Math.round(1e12*Math.random())}`;return this.#e.event("ad_impression",{network:"CPMStar",location:"featured-content-carousel"}),e}get showTrackingImage(){return Boolean(this.promotionalFeaturedContent?.trackingImageSrc&&this.featuredContent[this.currentIndex].id===n.promotionalFeaturedContentId&&this.isWindowFocused)}attached(){this.#p=!1,this.windowFocusSubscription=b.o.subscribe((e=>{this.isWindowFocused=e})),this.#u=new g.Vd([this.#d.onReevaluationRequired((()=>this.#i())),this.#f.onLocaleChanged((()=>this.#i())),(0,g.nm)((()=>this.windowFocusSubscription?.unsubscribe()))]),this.#h(),this.featuredContent.length&&(this.title=this.featuredContent[0].title,this.subtitle=this.featuredContent[0].subtitle),this.#m=this.#l.pipe((0,d.n)((()=>(0,s.O)(500))),(0,c.M)((e=>this.transitionContent(e)))).subscribe()}detached(){this.#m?.unsubscribe(),this.autoplayInterval?.dispose(),this.#u?.dispose(),this.#u=null,this.#p=!0}bind(){this.#i()}catalogChanged(){this.#i()}get viewport(){return this.featuredContent?Array(5).fill(0).reduce((e=>[...e,v.forward(e[e.length-1],this.featuredContent)]),[v.backward(this.currentIndex,this.featuredContent),this.currentIndex]):[]}#i(){this.featuredContent=this.catalog.featured.filter((e=>this.#d.isApplicable(e))),!this.isPro&&this.promotionalFeaturedContent?.featuredImageSrc&&(this.featuredContent=[{id:n.promotionalFeaturedContentId,title:this.promotionalFeaturedContent.title??"",image:this.promotionalFeaturedContent.featuredImageSrc,subtitle:null,overlay:null,url:this.promotionalFeaturedContent.url??null,badgeType:"text",badgeText:"Ad",badgeColor:""},...this.featuredContent]),this.currentIndex>this.featuredContent.length-1&&this.transitionContent("forward")}transitionContent(e){if(!this.featuredContent.length)return;this.activeTransition=e;const t=v[e](this.currentIndex,this.featuredContent);this.nextIndex=t,setTimeout((()=>{this.title=this.featuredContent[t].title,this.subtitle=this.featuredContent[t].subtitle,setTimeout((()=>{this.currentIndex=t,this.nextIndex=null,this.activeTransition=null,this.#h()}),250)}),250)}handleItemClick(e){const t=this.featuredContent[e];e===this.currentIndex&&t.url&&(window.open(t.url,t.url.startsWith("http")?"_blank":void 0),this.#e.event("featured_content_click",{id:t.id,title:t.title,url:t.url,position:this.featuredContent.indexOf(t)},u.Io)),e!==this.currentIndex&&null===this.activeTransition&&(e===this.currentIndex+1||0===e&&this.currentIndex===this.featuredContent.length-1?this.#l.next("forward"):(this.currentIndex=e,this.title=this.featuredContent[e].title,this.subtitle=this.featuredContent[e].subtitle,this.nextIndex=null,this.activeTransition=null,this.#h()))}next(){this.#l.next("forward")}previous(){this.#l.next("backward")}#h(){this.#p||(this.autoplayInterval?.dispose(),this.autoplayInterval=(0,g.SO)((()=>{this.autoplaying&&this.next()}),this.autoplayDuration))}};(0,a.Cg)([(0,m._)((e=>[e.promotionalFeaturedContent?.trackingImageSrc,e.featuredContent,e.currentIndex,e.isWindowFocused])),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],y.prototype,"showTrackingImage",null),(0,a.Cg)([(0,i.computedFrom)("currentIndex","featuredContent"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],y.prototype,"viewport",null),y=n=(0,a.Cg)([(0,h.m6)({selectors:{catalog:(0,h.$t)((e=>e.catalog)),isPro:(0,h.$t)((e=>e.account&&(0,l.a)(e.account))),promotionalFeaturedContent:(0,h.$t)((e=>e.catalog?.features?.cpmstar_featured_content_2025_06))}}),(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[u.j0,p.u,f.F2])],y)},"dashboard/resources/elements/featured-content-item":(e,t,o)=>{o.r(t),o.d(t,{FeaturedContentItem:()=>i});var n=o(15215),a=o(30960);class i{}(0,n.Cg)([a._t,(0,n.Sn)("design:type",Object)],i.prototype,"content",void 0),(0,n.Cg)([a._t,(0,n.Sn)("design:type",Boolean)],i.prototype,"isAd",void 0)},"dashboard/resources/elements/featured-content-item.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var n=o(14385),a=o.n(n),i=new URL(o(40380),o.b),r=new URL(o(65286),o.b);const s='<template> <require from="./featured-content-item.scss"></require> <div class="image ${imageLoaded ? \'loaded\' : \'\'}" css="background-image: url(\'${content.image | cdn}\')"> <img style="display:none" src.bind="content.image | cdn" load.trigger="imageLoaded = true"> <img class="overlay" src.bind="content.overlay | cdn"> <div class="badge"> <template if.bind="content.badgeType === \'text\' && content.badgeText"> <span if.bind="isAd" class="text ad">${content.badgeText}</span> <span else class="text" css.bind="{background: content.badgeColor}">${content.badgeText}</span> </template> <span if.bind="content.badgeType === \'epic_gotw\'"> <inline-svg src="'+a()(i)+'"></inline-svg> </span> <span if.bind="content.badgeType === \'community_choice\'"> <inline-svg src="'+a()(r)+'"></inline-svg> </span> </div> </div> </template> '},"dashboard/resources/elements/featured-content-item.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>s});var n=o(31601),a=o.n(n),i=o(76314),r=o.n(i)()(a());r.push([e.id,'featured-content-item{display:flex;flex-direction:column;gap:12px}featured-content-item .image{aspect-ratio:16/9;display:flex;position:relative;width:100%;height:248px;background-position:center;background-size:cover;transition:height .2s;border-radius:16px;opacity:1}featured-content-item .image:hover:after{border:2px solid var(--theme--highlight);mix-blend-mode:normal}featured-content-item .image .overlay{position:absolute;bottom:20px;left:20px;max-width:225px;transition:max-width .2s,left .2s}@media(min-width: 1600px){featured-content-item .image{height:400px}featured-content-item .image .overlay{bottom:20px;left:28px;max-width:300px}}featured-content-item .image:after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);border-radius:16px;z-index:1;mix-blend-mode:overlay;cursor:pointer;transition:border-color .1s ease-in-out}featured-content-item .image:not(.loaded){animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box}featured-content-item .image .badge{position:absolute;right:20px;top:20px}featured-content-item .image .badge>.text{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal;padding:0 4px;border-radius:4px}featured-content-item .image .badge>.text.ad{background-color:rgba(255,255,255,.15);color:rgba(255,255,255,.6)}',""]);const s=r},"dashboard/resources/elements/featured-content.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>n});const n='<template mouseover.delegate="autoplaying = false" mouseout.delegate="autoplaying = true" show.bind="featuredContent.length"> <require from="./featured-content.scss"></require> <require from="./featured-content-item"></require> <div class="featured-header"> <div class="title-container ${activeTransition ? \'title-change-animation\' : \'\'}"> <div class="title">${title}</div> <div class="subtitle">${subtitle}</div> </div> <div class="navigate-buttons"> <button class="previous-item" click.delegate="previous()"></button> <button class="next-item" click.delegate="next()"></button> </div> </div> <div class="featured-items" if.bind="viewport"> <featured-content-item repeat.for="i of viewport" class="${(nextIndex === null && i === currentIndex) || i === nextIndex ? \'current-item\' : \'\'} ${activeTransition ? \'item-change-animation--\' + activeTransition : \'\'}" click.delegate="handleItemClick(i)" content.bind="featuredContent[i]" is-ad.bind="featuredContent[i].id === FeaturedContent.promotionalFeaturedContentId"></featured-content-item> <img if.bind="showTrackingImage" src.bind="generateAndTrackActiveImpressionUrl()"> </div> </template> '},"dashboard/resources/elements/featured-content.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>u});var n=o(31601),a=o.n(n),i=o(76314),r=o.n(i),s=o(4417),d=o.n(s),c=new URL(o(83959),o.b),l=r()(a()),m=d()(c);l.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,featured-content .previous-item,featured-content .next-item{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}featured-content{display:flex;flex-direction:column;gap:16px}featured-content .featured-header{display:flex;flex-direction:row;align-items:center}featured-content .featured-header .title-container{display:flex;flex-direction:column;gap:0px;height:40px;justify-content:center}featured-content .featured-header .title-container .title{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;color:#fff}featured-content .featured-header .title-container .subtitle{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default featured-content .featured-header .title-container .subtitle{color:rgba(255,255,255,.6)}.theme-purple-pro featured-content .featured-header .title-container .subtitle{color:rgba(255,255,255,.6)}.theme-green-pro featured-content .featured-header .title-container .subtitle{color:rgba(255,255,255,.6)}.theme-orange-pro featured-content .featured-header .title-container .subtitle{color:rgba(255,255,255,.6)}.theme-pro featured-content .featured-header .title-container .subtitle{color:rgba(255,255,255,.6)}featured-content .featured-header .navigate-buttons{display:flex;gap:6px;margin-left:auto}featured-content .featured-items{display:flex;flex-direction:row;overflow:hidden;position:relative}featured-content .featured-items::after{content:"";position:absolute;right:0;width:48px;height:100%;z-index:2;background:linear-gradient(270deg, var(--theme--background) 0%, rgba(13, 15, 18, 0.37) 34.63%, rgba(13, 15, 18, 0.16) 61.15%, rgba(13, 15, 18, 0.05) 83.34%, rgba(13, 15, 18, 0) 100%)}featured-content .featured-items featured-content-item{transform:translateX(calc(-100% - 8px));padding:0px 8px;transition:transform 500ms ease-in,background-color 500ms ease-in,opacity 500ms ease-in,filter 500ms ease-in}featured-content .featured-items featured-content-item:not(.current-item){opacity:.7;filter:grayscale(0.5)}featured-content .featured-items featured-content-item:not(.current-item):after{content:"";position:absolute;top:0;left:0;right:0;bottom:0;z-index:1;border:none;background:radial-gradient(355.47% 141.42% at 100% 0%, rgba(19, 20, 23, 0) 49.93%, #131417 100%)}featured-content .link{position:absolute;left:0;top:0;right:0;bottom:40px;display:block}featured-content .previous-item,featured-content .next-item{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;padding:8px;background-color:rgba(0,0,0,.35);transform:scale(1);backdrop-filter:blur(6px);--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);width:20px;height:20px;box-sizing:content-box;z-index:1}featured-content .previous-item,featured-content .previous-item *,featured-content .next-item,featured-content .next-item *{cursor:pointer}featured-content .previous-item:hover,featured-content .next-item:hover{background:rgba(255,255,255,.25)}.theme-default featured-content .previous-item,.theme-default featured-content .next-item{color:#fff}.theme-purple-pro featured-content .previous-item,.theme-purple-pro featured-content .next-item{color:#fff}.theme-green-pro featured-content .previous-item,.theme-green-pro featured-content .next-item{color:#fff}.theme-orange-pro featured-content .previous-item,.theme-orange-pro featured-content .next-item{color:#fff}.theme-pro featured-content .previous-item,.theme-pro featured-content .next-item{color:#fff}featured-content .previous-item:hover,featured-content .next-item:hover{background-color:rgba(0,0,0,.6)}featured-content .previous-item:active,featured-content .next-item:active{background-color:var(--theme--highlight)}featured-content .previous-item,featured-content .previous-item *,featured-content .next-item,featured-content .next-item *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) featured-content .previous-item,body:not(.override-contrast-mode) featured-content .next-item{border:1px solid #fff}}featured-content .previous-item>*,featured-content .next-item>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}featured-content .previous-item>*:first-child,featured-content .next-item>*:first-child{padding-left:0}featured-content .previous-item>*:last-child,featured-content .next-item>*:last-child{padding-right:0}featured-content .previous-item svg,featured-content .next-item svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) featured-content .previous-item svg *,body:not(.override-contrast-mode) featured-content .next-item svg *{fill:CanvasText}}featured-content .previous-item svg *,featured-content .next-item svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) featured-content .previous-item svg,body:not(.override-contrast-mode) featured-content .next-item svg{opacity:1}}featured-content .previous-item img,featured-content .next-item img{height:50%}featured-content .previous-item:disabled,featured-content .next-item:disabled{opacity:.3}featured-content .previous-item:disabled,featured-content .previous-item:disabled *,featured-content .next-item:disabled,featured-content .next-item:disabled *{cursor:default;pointer-events:none}@media(hover: hover){featured-content .previous-item:not(:disabled):hover,featured-content .next-item:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}featured-content .previous-item:not(:disabled):hover svg,featured-content .next-item:not(:disabled):hover svg{opacity:1}}featured-content .previous-item:not(:disabled):active,featured-content .next-item:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){featured-content .previous-item:not(:disabled):hover,featured-content .next-item:not(:disabled):hover{background:rgba(255,255,255,.3)}}featured-content .previous-item{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;padding:8px;background-color:rgba(0,0,0,.35);transform:scale(1);backdrop-filter:blur(6px);font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;background:rgba(255,255,255,.1);left:20px}featured-content .previous-item,featured-content .previous-item *{cursor:pointer}featured-content .previous-item:hover{background:rgba(255,255,255,.25)}.theme-default featured-content .previous-item{color:#fff}.theme-purple-pro featured-content .previous-item{color:#fff}.theme-green-pro featured-content .previous-item{color:#fff}.theme-orange-pro featured-content .previous-item{color:#fff}.theme-pro featured-content .previous-item{color:#fff}featured-content .previous-item:hover{background-color:rgba(0,0,0,.6)}featured-content .previous-item:active{background-color:var(--theme--highlight)}featured-content .previous-item:before{font-family:inherit;content:"keyboard_arrow_left"}featured-content .next-item{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s;padding:8px;background-color:rgba(0,0,0,.35);transform:scale(1);backdrop-filter:blur(6px);font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;background:rgba(255,255,255,.1);right:20px}featured-content .next-item,featured-content .next-item *{cursor:pointer}featured-content .next-item:hover{background:rgba(255,255,255,.25)}.theme-default featured-content .next-item{color:#fff}.theme-purple-pro featured-content .next-item{color:#fff}.theme-green-pro featured-content .next-item{color:#fff}.theme-orange-pro featured-content .next-item{color:#fff}.theme-pro featured-content .next-item{color:#fff}featured-content .next-item:hover{background-color:rgba(0,0,0,.6)}featured-content .next-item:active{background-color:var(--theme--highlight)}featured-content .next-item:before{font-family:inherit;content:"keyboard_arrow_right"}featured-content .item-change-animation--forward{animation:next-item-slide 500ms cubic-bezier(0.25, 1, 0.5, 1) forwards}featured-content .item-change-animation--backward{animation:previous-item-slide 500ms cubic-bezier(0.25, 1, 0.5, 1) forwards}featured-content .title-change-animation{animation:title-pulse 500ms cubic-bezier(0.25, 1, 0.5, 1)}@keyframes next-item-slide{0%{transform:translateX(calc(-100% - 8px))}100%{transform:translateX(calc(-200% - 8px))}}@keyframes previous-item-slide{0%{transform:translateX(calc(-100% - 8px))}100%{transform:translateX(calc(0% - 8px))}}@keyframes title-pulse{0%{opacity:100%}50%{opacity:0%}100%{opacity:100%}}`,""]);const u=l}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8281],{15182:(e,o,t)=>{t.d(o,{Xz:()=>r,pF:()=>a});const s=["save_mods","pin_mods","remote","game_guide","boosts"],i=[...s,"themes","no_ads","support"],r=e=>s.includes(e),a=e=>i.includes(e)},"pro-promos/pro-showcase-columns/pro-showcase-columns":(e,o,t)=>{t.r(o),t.d(o,{ProShowcaseColumns:()=>u});var s=t(15215),i=t(96111),r=t("aurelia-framework"),a=t(20770),n=t(71341),l=t(68539),p=t(92465),c=t("shared/i18n/resources/value-converters"),d=t(54995);const m=["save_mods","pin_mods","remote","game_guide","themes","no_ads","boosts"];let u=class{#e;#o;#t;constructor(e,o,t,s,i){this.currentFeature="save_mods",this.hasHoveredFeature=!1,this.#o=e,this.#t=o,this.timeLimitEnforcer=i}attached(){this.#s()}detached(){this.#e?.dispose()}get hasTimeLimit(){return this.timeLimitEnforcer.isEnabled}get hasInteractiveControls(){return this.timeLimitEnforcer.canUseInAppControls}bind(){this.defaultFeature&&(this.currentFeature=this.defaultFeature)}get localizedGamesCount(){return this.#o.toView(3e3)}get localizedModsCount(){return this.#o.toView(35e3)}get localizedMapsCount(){return this.#o.toView(this.catalog.maps.length)}handleContinueClick(){this.onClose?.({result:!1})}handleUpgradeClick(){this.#t.open({trigger:"pro_showcase_columns"}),this.onClose?.({result:!0})}setCurrentFeature(e){this.currentFeature=e,this.hasHoveredFeature=!0}#s(){this.#e=(0,p.SO)((()=>{if(this.hasHoveredFeature)return;const e=(m.indexOf(this.currentFeature)+1)%m.length;this.currentFeature=m[e]}),2e3)}};(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",String)],u.prototype,"defaultFeature",void 0),(0,s.Cg)([r.bindable,(0,s.Sn)("design:type",Function)],u.prototype,"onClose",void 0),(0,s.Cg)([(0,r.computedFrom)("timeLimitEnforcer.isEnabled"),(0,s.Sn)("design:type",Boolean),(0,s.Sn)("design:paramtypes",[])],u.prototype,"hasTimeLimit",null),(0,s.Cg)([(0,r.computedFrom)("timeLimitEnforcer.canUseInAppControls"),(0,s.Sn)("design:type",Boolean),(0,s.Sn)("design:paramtypes",[])],u.prototype,"hasInteractiveControls",null),(0,s.Cg)([(0,r.computedFrom)("language"),(0,s.Sn)("design:type",String),(0,s.Sn)("design:paramtypes",[])],u.prototype,"localizedGamesCount",null),(0,s.Cg)([(0,r.computedFrom)("language"),(0,s.Sn)("design:type",String),(0,s.Sn)("design:paramtypes",[])],u.prototype,"localizedModsCount",null),(0,s.Cg)([(0,r.computedFrom)("language","catalog"),(0,s.Sn)("design:type",String),(0,s.Sn)("design:paramtypes",[])],u.prototype,"localizedMapsCount",null),u=(0,s.Cg)([(0,d.m6)({selectors:{catalog:(0,d.$t)((e=>e.catalog)),language:(0,d.$t)((e=>e.settings.language))}}),(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[c.I18nNumberValueConverter,n.U,a.il,l.z,i.Y])],u)},"pro-promos/pro-showcase-columns/pro-showcase-columns.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>g});var s=t(14385),i=t.n(s),r=new URL(t(62152),t.b),a=new URL(t(77103),t.b),n=new URL(t(63803),t.b),l=new URL(t(59184),t.b),p=new URL(t(43159),t.b),c=new URL(t(70150),t.b),d=new URL(t(20363),t.b),m=new URL(t(48021),t.b),u=new URL(t(72583),t.b),h=new URL(t(49388),t.b);const g='<template> <require from="./pro-showcase-columns.scss"></require> <require from="./resources/elements/pro-showcase-columns-list-item"></require> <require from="./resources/elements/pro-showcase-columns-feature"></require> <require from="./resources/elements/hover-me"></require> <require from="resources/elements/pro-cta-label"></require> <require from="pro-promos/resources/elements/faux-mods-ui.html"></require> <require from="shared/pro-promos/pin-mods-illustration"></require> <require from="shared/pro-promos/game-guide-illustration"></require> <section> <header>${\'pro_showcase_columns.basic\' | i18n}</header> <ul> <li if.bind="!hasTimeLimit"> <pro-showcase-columns-list-item icon="all_inclusive" label="${\'pro_showcase_columns.unlimited_modding\' | i18n}"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item label="${\'pro_showcase_columns.$games_games_with_$mods_mods\' | i18n:{games: localizedGamesCount, mods:\n                localizedModsCount}}"> <span slot="customIcon"> <span><inline-svg src="'+i()(r)+'"></inline-svg></span> </span> </pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="layers" label="${\'pro_showcase_columns.native_overlay\' | i18n}"></pro-showcase-columns-list-item> </li> <li if.bind="hasInteractiveControls"> <pro-showcase-columns-list-item icon="page_info" label="${\'pro_showcase_columns.interactive_controls\' | i18n}"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="map" label="${\'pro_showcase_columns.$count_maps\' | i18n:{count: localizedMapsCount}}"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="sprint" label="${\'pro_showcase_columns.one_click_teleport\' | i18n}"> </pro-showcase-columns-list-item> </li> </ul> <footer> <wm-button style="--wm-button-overlay-opacity:0.15" color="inverse" click.delegate="handleContinueClick()">${\'pro_showcase_columns.continue_with_free\' | i18n}</wm-button> </footer> <hover-me if.bind="!hasHoveredFeature"></hover-me> </section> <section> <header> <em>${\'pro_showcase_columns.pro\' | i18n}</em> </header> <ul> <li> <pro-showcase-columns-list-item icon="new_releases" label="${\'pro_showcase_columns.everything_in_free_plus\' | i18n}"></pro-showcase-columns-list-item> </li> <li if.bind="!hasInteractiveControls"> <pro-showcase-columns-list-item icon="page_info" label="${\'pro_showcase_columns.interactive_controls\' | i18n}"></pro-showcase-columns-list-item> </li> <li else if.bind="hasTimeLimit"> <pro-showcase-columns-list-item icon="all_inclusive" label="${\'pro_showcase_columns.unlimited_modding\' | i18n}"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item can-hover.bind="true" is-popular.bind="true" label="${\'pro_showcase_columns.save_mods\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'save_mods\')" is-current.bind="currentFeature === \'save_mods\'"> <i slot="customIcon"> <span><inline-svg src="'+i()(a)+'"></inline-svg></span> </i> </pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="keep" can-hover.bind="true" label="${\'pro_showcase_columns.pin_mods\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'pin_mods\')" is-current.bind="currentFeature === \'pin_mods\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="smartphone" can-hover.bind="true" label="${\'pro_showcase_columns.mobile_remote_app\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'remote\')" is-current.bind="currentFeature === \'remote\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item can-hover.bind="true" label="${\'pro_showcase_columns.game_guides\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'game_guide\')" is-current.bind="currentFeature === \'game_guide\'"> <i slot="customIcon"> <img src="'+i()(n)+'"> </i> </pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="colors" can-hover.bind="true" label="${\'pro_showcase_columns.custom_themes\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'themes\')" is-current.bind="currentFeature === \'themes\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="ad_off" can-hover.bind="true" label="${\'pro_showcase_columns.no_ads\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'no_ads\')" is-current.bind="currentFeature === \'no_ads\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="double_arrow" can-hover.bind="true" label="${\'pro_showcase_columns.boost_games\' | i18n}" mouseover.delegate="setCurrentFeature(currentFeature = \'boosts\')" is-current.bind="currentFeature === \'boosts\'"></pro-showcase-columns-list-item> </li> <li> <pro-showcase-columns-list-item icon="support" label="${\'pro_showcase_columns.priority_support_and_more\' | i18n}"></pro-showcase-columns-list-item> </li> </ul> <footer> <wm-button click.delegate="handleUpgradeClick()"><pro-cta-label></pro-cta-label></wm-button> </footer> </section> <section> <pro-showcase-columns-feature if.bind="currentFeature === \'save_mods\'" is-popular.bind="true" title="${\'pro_showcase_columns.save_mods_title_case\' | i18n}" description="${\'pro_showcase_columns.save_mods_description\' | i18n}" image-src="'+i()(l)+'"> <faux-mods-ui></faux-mods-ui> </pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'pin_mods\'" title="${\'pro_showcase_columns.pin_mods_title_case\' | i18n}" description="${\'pro_showcase_columns.pin_mods_description\' | i18n}" image-src="'+i()(p)+'" video-src="https://media.wemod.com/videos/upgrade-promo/pro-showcase-columns/pin-mods.webm" video-type="video/webm"> <pin-mods-illustration></pin-mods-illustration> </pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'remote\'" title="${\'pro_showcase_columns.remote_title_case\' | i18n}" description="${\'pro_showcase_columns.remote_description\' | i18n}" image-src="'+i()(c)+'" video-src="https://media.wemod.com/videos/upgrade-promo/pro-showcase-columns/remote.webm" video-type="video/webm"></pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'game_guide\'" title="${\'pro_showcase_columns.game_guide_title_case\' | i18n}" description="${\'pro_showcase_columns.game_guide_description\' | i18n}" image-src="'+i()(d)+'" video-src="https://media.wemod.com/videos/upgrade-promo/pro-showcase-columns/game-guide.webm" video-type="video/webm"> <game-guide-illustration></game-guide-illustration> </pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'themes\'" title="${\'pro_showcase_columns.themes_title_case\' | i18n}" description="${\'pro_showcase_columns.themes_description\' | i18n}" image-src="'+i()(m)+'"></pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'no_ads\'" title="${\'pro_showcase_columns.no_ads_title_case\' | i18n}" description="${\'pro_showcase_columns.no_ads_description\' | i18n}" image-src="'+i()(u)+'"></pro-showcase-columns-feature> <pro-showcase-columns-feature if.bind="currentFeature === \'boosts\'" title="${\'pro_showcase_columns.boosts_title_case\' | i18n}" description="${\'pro_showcase_columns.boosts_description\' | i18n}" image-src="'+i()(h)+'" video-src="https://media.wemod.com/videos/upgrade-promo/pro-showcase-columns/boosts.webm" video-type="video/webm"></pro-showcase-columns-feature> </section> </template> '},"pro-promos/pro-showcase-columns/pro-showcase-columns.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r)()(i());a.push([e.id,"pro-showcase-columns{--pro-showcase-accent: #fa1280;background:var(--theme--background) linear-gradient(rgba(255, 255, 255, 0.025), rgba(255, 255, 255, 0.025)) !important;display:flex;width:900px;min-height:612px;height:100%}pro-showcase-columns>section{width:320px;padding:20px;display:flex;flex-direction:column;position:relative}pro-showcase-columns>section:first-child{width:260px;background:rgba(255,255,255,.025)}pro-showcase-columns>section:nth-child(2){--pro-showcase-columns-list-item--icon-color: #fff}pro-showcase-columns>section:last-child{padding:0}pro-showcase-columns>section>header{font-weight:400;font-size:36px;line-height:36px;letter-spacing:-1px;color:#fff;margin-bottom:12px}pro-showcase-columns>section>header em{font-weight:900;font-style:italic;letter-spacing:-1.5px;color:#fff}pro-showcase-columns>section>ul{list-style:none;padding:0;margin:0;flex:1 0 auto}pro-showcase-columns>section>footer .wm-button{display:block;width:100%;justify-content:center}pro-showcase-columns faux-mods-ui{display:block;margin:50px auto 0;width:280px;transform:scale(1.2)}pro-showcase-columns game-guide-illustration{transform:scale(0.9);display:block;margin-bottom:-50px}pro-showcase-columns pin-mods-illustration{display:block;transform:scale(0.8) translate(-54px, 120px)}pro-showcase-columns hover-me{position:absolute;right:25px;bottom:200px}",""]);const n=a},"pro-promos/pro-showcase-columns/resources/elements/hover-me":(e,o,t)=>{t.r(o),t.d(o,{HoverMe:()=>s});class s{}},"pro-promos/pro-showcase-columns/resources/elements/hover-me.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s="<template> <require from=\"./hover-me.scss\"></require> ${'hover_me.hover_me' | i18n} </template> "},"pro-promos/pro-showcase-columns/resources/elements/hover-me.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r)()(i());a.push([e.id,'hover-me{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;display:inline-block;color:var(--theme--background);text-transform:uppercase;padding:4px 6px;position:relative;z-index:0;border-radius:6px;background:#fff;animation:hover-me-bounce 1s ease-in-out infinite}hover-me:before{content:"";position:absolute;right:-5px;top:2.5px;border-radius:6px;background:#fff;transform:rotate(45deg);width:20px;height:20px;z-index:-1}@keyframes hover-me-bounce{0%{transform:translateX(0)}20%{transform:translateX(-6px)}38%{transform:translateX(2px)}40%{transform:translateX(0)}}',""]);const n=a},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-feature":(e,o,t)=>{t.r(o),t.d(o,{ProShowcaseColumnsFeature:()=>r});var s=t(15215),i=t("aurelia-framework");class r{constructor(){this.isPopular=!1}}(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Boolean)],r.prototype,"isPopular",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"title",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"description",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Object)],r.prototype,"imageSrc",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Object)],r.prototype,"videoSrc",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Object)],r.prototype,"videoType",void 0)},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-feature.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s='<template class="pro-showcase-columns-feature"> <require from="./pro-showcase-columns-feature.scss"></require> <require from="pro-promos/resources/elements/pro-promo-badge"></require> <div class="pro-showcase-columns-feature__other"> <slot></slot> </div> <div class="pro-showcase-columns-feature__content"> <pro-promo-badge if.bind="isPopular" size="m">${\'pro_showcase_columns.popular\' | i18n}</pro-promo-badge> <h1>${title}</h1> <p>${description}</p> </div> <div class="pro-showcase-columns-feature__background"> <wm-background-video if.bind="videoSrc" slot="bg" poster.bind="imageSrc" src.bind="videoSrc" type.bind="videoType"></wm-background-video> <img if.bind="!videoSrc" src.bind="imageSrc"> </div> </template> '},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-feature.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r)()(i());a.push([e.id,'.pro-showcase-columns-feature{height:100%;display:flex;flex-direction:column;justify-content:flex-end;position:relative;z-index:0}.pro-showcase-columns-feature__background{position:absolute;z-index:-1;top:0;left:0;width:100%;height:100%}.pro-showcase-columns-feature__background img{position:absolute;left:0;top:0;width:100%;height:100%;object-fit:cover}.pro-showcase-columns-feature__background wm-background-video{position:absolute;left:0;top:0;width:100%;height:100%}.pro-showcase-columns-feature__other{flex:1 1 auto;overflow:hidden}.pro-showcase-columns-feature__content{flex:0 0 auto;display:flex;flex-direction:column;align-items:center;text-align:center;position:relative;z-index:0;padding:30px 30px 70px;overflow:hidden}.pro-showcase-columns-feature__content h1{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;color:rgba(255,255,255,.9);margin:0 0 4px}.pro-showcase-columns-feature__content p{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:28px;color:rgba(255,255,255,.9);margin:0;padding:0}',""]);const n=a},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-list-item":(e,o,t)=>{t.r(o),t.d(o,{ProShowcaseColumnsListItem:()=>r});var s=t(15215),i=t("aurelia-framework");class r{constructor(){this.isPopular=!1,this.canHover=!1}}(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Boolean)],r.prototype,"isPopular",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Boolean)],r.prototype,"canHover",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"label",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"icon",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Boolean)],r.prototype,"isCurrent",void 0)},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-list-item.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s='<template class="pro-showcase-columns-list-item ${canHover ? \'pro-showcase-columns-list-item--can-hover\' : \'\'} ${isCurrent ? \'pro-showcase-columns-list-item--is-current\' : \'\'}"> <require from="./pro-showcase-columns-list-item.scss"></require> <require from="pro-promos/resources/elements/pro-promo-badge"></require> <i> <template if.bind="icon">${icon}</template> <slot name="customIcon"></slot> </i> <span>${label}</span> <pro-promo-badge if.bind="isPopular" size="s">${\'pro_showcase_columns.popular\' | i18n}</pro-promo-badge> </template> '},"pro-promos/pro-showcase-columns/resources/elements/pro-showcase-columns-list-item.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r),n=t(4417),l=t.n(n),p=new URL(t(83959),t.b),c=a()(i()),d=l()(p);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${d}) format("woff2")}.material-symbols-outlined,.pro-showcase-columns-list-item>i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.pro-showcase-columns-list-item{display:flex;align-items:center;color:var(--theme--text-primary);padding:12px 0;position:relative;z-index:0}.pro-showcase-columns-list-item>i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;flex:0 0 20px;display:inline-flex;width:20px;align-items:center;margin-right:9px;overflow:hidden;color:var(--pro-showcase-columns-list-item--icon-color, rgba(255, 255, 255, 0.8))}.pro-showcase-columns-list-item>i img{width:20px;height:20px}.pro-showcase-columns-list-item>span{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;flex:0 1 auto}.pro-showcase-columns-list-item>pro-promo-badge{flex:0 0 auto;margin-left:8px}.pro-showcase-columns-list-item:before{content:"";position:absolute;left:-10px;top:0;bottom:0;right:-10px;border-radius:8px;background-color:#fff;z-index:-1;opacity:0;transition:opacity .3s}.pro-showcase-columns-list-item--can-hover:hover{color:var(--theme--background)}.pro-showcase-columns-list-item--can-hover:hover>i{color:var(--theme--background)}.pro-showcase-columns-list-item--can-hover:hover:before{opacity:1}.pro-showcase-columns-list-item--is-current{color:var(--theme--background)}.pro-showcase-columns-list-item--is-current>i{color:var(--theme--background)}.pro-showcase-columns-list-item--is-current:before{opacity:1}`,""]);const m=c},"pro-promos/pro-showcase-slideshow/pro-showcase-slideshow":(e,o,t)=>{t.r(o),t.d(o,{ProShowcaseSlideshow:()=>n});var s=t(15215),i=t("aurelia-framework"),r=t(38777);const a=[{title:"pro_showcase_slideshow.games_title",description:"pro_showcase_slideshow.games_description"},{title:"pro_showcase_slideshow.auto_detect_title",description:"pro_showcase_slideshow.auto_detect_description"},{title:"pro_showcase_slideshow.overlay_title",description:"pro_showcase_slideshow.overlay_description"},{title:"pro_showcase_slideshow.remote_title",description:"pro_showcase_slideshow.remote_description"},{title:"pro_showcase_slideshow.play_title",description:"pro_showcase_slideshow.play_description"},{title:"pro_showcase_slideshow.maps_title",description:"pro_showcase_slideshow.maps_description"}];class n{constructor(){this.currentSlide=0,this.#i=null,this.#r=null}#i;#r;attached(){this.#a(),this.gaugeFillEl?.classList.add("gauge-fill-animation")}detached(){this.#n(),this.#l()}get currentSlideData(){return a[this.currentSlide]}#a(){this.#n(),this.#i=(0,r.SO)((()=>this.handleSlideSwitch(!0)),7e3)}#n(){this.#i?.dispose(),this.#i=null}#l(){this.#r?.dispose(),this.#r=null}handleSlideSwitch(e){this.footerCaptionEl&&(this.footerCaptionEl.style.opacity="0"),this.#l(),this.#r=(0,r.Ix)((()=>{this.currentSlide=e?(this.currentSlide+1)%a.length:(this.currentSlide-1+a.length)%a.length,this.#p(),this.#a(),this.footerCaptionEl&&(this.footerCaptionEl.style.opacity="1")}),300)}handleArrowClick(e){this?.onArrowClick(),this.handleSlideSwitch(e)}#p(){this.gaugeFillEl&&(this.gaugeFillEl.classList.remove("gauge-fill-animation"),this.gaugeFillEl.offsetWidth,this.gaugeFillEl?.classList.add("gauge-fill-animation"))}}(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Function)],n.prototype,"onArrowClick",void 0),(0,s.Cg)([(0,i.computedFrom)("currentSlide"),(0,s.Sn)("design:type",Object),(0,s.Sn)("design:paramtypes",[])],n.prototype,"currentSlideData",null)},"pro-promos/pro-showcase-slideshow/pro-showcase-slideshow.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>d});var s=t(14385),i=t.n(s),r=new URL(t(2997),t.b),a=new URL(t(6393),t.b),n=new URL(t(11061),t.b),l=new URL(t(75451),t.b),p=new URL(t(89769),t.b),c=new URL(t(17764),t.b);const d='<template> <require from="./pro-showcase-slideshow.scss"></require> <div class="pro-showcase-slideshow"> <div class="pro-showcase-slideshow-bg"> <img if.bind="currentSlide === 0" src="'+i()(r)+'"> <wm-background-video if.bind="currentSlide === 1" src="https://media.wemod.com/videos/features/slideshow/game-detection.webm" poster="'+i()(a)+'" type="video/webm"></wm-background-video> <wm-background-video if.bind="currentSlide === 2" src="https://media.wemod.com/videos/features/slideshow/overlay.webm" poster="'+i()(n)+'" type="video/webm"></wm-background-video> <wm-background-video if.bind="currentSlide === 3" src="https://media.wemod.com/videos/features/slideshow/remote.webm" poster="'+i()(l)+'" type="video/webm"></wm-background-video> <wm-background-video if.bind="currentSlide === 4" src="https://media.wemod.com/videos/features/slideshow/play-games.webm" poster="'+i()(p)+'" type="video/webm"></wm-background-video> <img if.bind="currentSlide === 5" src="'+i()(c)+'"> </div> <div class="pro-showcase-slideshow-fg"> <div class="footer"> <div class="caption" ref="footerCaptionEl"> <span class="title">${currentSlideData.title | i18n}</span> <span class="description">${currentSlideData.description | i18n}</span> </div> <div class="actions"> <button click.trigger="handleArrowClick(false)"><i class="icon-arrow-left"></i></button> <button click.trigger="handleArrowClick(true)"><i class="icon-arrow-right"></i></button> </div> </div> </div> <div class="gauge"> <div class="gauge-fill" ref="gaugeFillEl"></div> </div> </div> </template> '},"pro-promos/pro-showcase-slideshow/pro-showcase-slideshow.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r),n=t(4417),l=t.n(n),p=new URL(t(83959),t.b),c=a()(i()),d=l()(p);c.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${d}) format("woff2")}.material-symbols-outlined,.pro-showcase-slideshow .footer .actions button .icon-arrow-left,.pro-showcase-slideshow .footer .actions button .icon-arrow-right{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}pro-showcase-slideshow{display:flex;align-self:stretch;height:calc(100vh - 96px)}.pro-showcase-slideshow{display:flex;width:576px;height:100%;border-radius:20px;border:2px solid rgba(255,255,255,.05);position:relative;overflow:hidden;transition:opacity .5s ease-in-out}.pro-showcase-slideshow-bg{width:100%;height:100%}.pro-showcase-slideshow-bg>*{display:flex;width:100%;height:100%}.pro-showcase-slideshow-bg img{width:100%;height:100%;object-fit:cover}.pro-showcase-slideshow-fg{position:absolute;display:flex;flex-direction:column;align-items:flex-start;align-self:flex-end}.pro-showcase-slideshow .footer{display:flex;flex-direction:column;align-items:flex-start;align-self:flex-end;justify-content:center;gap:8px;padding:32px 24px}.pro-showcase-slideshow .footer .caption{display:flex;flex-direction:column;text-align:left;gap:4px;transition:opacity .3s ease-in-out}.pro-showcase-slideshow .footer .caption .title{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px;color:#fff}.pro-showcase-slideshow .footer .caption .description{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:20px;line-height:28px;color:rgba(255,255,255,.7)}.pro-showcase-slideshow .footer .actions{display:flex;flex-direction:row;align-items:center;justify-content:center;gap:8px}.pro-showcase-slideshow .footer .actions button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);width:48px;height:48px;border-radius:50%;display:flex;align-items:center;justify-content:center}.pro-showcase-slideshow .footer .actions button,.pro-showcase-slideshow .footer .actions button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-showcase-slideshow .footer .actions button{border:1px solid #fff}}.pro-showcase-slideshow .footer .actions button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.pro-showcase-slideshow .footer .actions button>*:first-child{padding-left:0}.pro-showcase-slideshow .footer .actions button>*:last-child{padding-right:0}.pro-showcase-slideshow .footer .actions button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-showcase-slideshow .footer .actions button svg *{fill:CanvasText}}.pro-showcase-slideshow .footer .actions button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-showcase-slideshow .footer .actions button svg{opacity:1}}.pro-showcase-slideshow .footer .actions button img{height:50%}.pro-showcase-slideshow .footer .actions button:disabled{opacity:.3}.pro-showcase-slideshow .footer .actions button:disabled,.pro-showcase-slideshow .footer .actions button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.pro-showcase-slideshow .footer .actions button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.pro-showcase-slideshow .footer .actions button:not(:disabled):hover svg{opacity:1}}.pro-showcase-slideshow .footer .actions button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.pro-showcase-slideshow .footer .actions button:not(:disabled):hover{background:rgba(255,255,255,.3)}}.pro-showcase-slideshow .footer .actions button i{height:18px;width:18px;font-size:18px}.pro-showcase-slideshow .footer .actions button .icon-arrow-left{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.pro-showcase-slideshow .footer .actions button .icon-arrow-left:before{font-family:inherit;content:"arrow_back"}.pro-showcase-slideshow .footer .actions button .icon-arrow-right{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.pro-showcase-slideshow .footer .actions button .icon-arrow-right:before{font-family:inherit;content:"arrow_forward"}.pro-showcase-slideshow .gauge{position:absolute;bottom:0;left:0;width:100%;height:8px;background:rgba(255,255,255,.1);overflow:hidden;border-radius:0 0 20px 20px}.pro-showcase-slideshow .gauge-fill{width:0;height:100%;background:rgba(255,255,255,.3)}.pro-showcase-slideshow .gauge-fill.gauge-fill-animation{animation:fillGauge 7s linear forwards}@keyframes fillGauge{from{width:0}to{width:100%}}@media(max-height: 640px)or (max-width: 1200px){.pro-showcase-slideshow{width:40vw;max-width:576px}}`,""]);const m=c},"pro-promos/pro-showcase/pro-showcase":(e,o,t)=>{t.r(o),t.d(o,{ProShowcase:()=>r});var s=t(15215),i=t("aurelia-framework");class r{constructor(){this.currentFeature="save_mods"}}(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"currentFeature",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Function)],r.prototype,"onProCtaClick",void 0)},"pro-promos/pro-showcase/pro-showcase.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var s=t(14385),i=t.n(s),r=new URL(t(20909),t.b),a=new URL(t(89041),t.b),n=new URL(t(99845),t.b),l=new URL(t(95926),t.b),p=new URL(t(9278),t.b),c=new URL(t(77103),t.b),d=new URL(t(63803),t.b);const m='<template> <require from="./pro-showcase.scss"></require> <require from="./resources/elements/pro-showcase-feature"></require> <require from="./resources/elements/pro-showcase-option"></require> <require from="shared/pro-promos/save-mods-illustration"></require> <require from="shared/pro-promos/pin-mods-illustration"></require> <require from="shared/pro-promos/game-guide-illustration"></require> <div class="pro-showcase"> <div class="pro-showcase-feature-display"> <pro-showcase-feature if.bind="currentFeature === \'save_mods\'" feature="${\'pro_showcase.save_mods\' | i18n}" description="${\'pro_showcase.save_mods_description\' | i18n}" is-popular.bind="true" on-pro-cta-click.call="onProCtaClick()"> <wm-background-video slot="bg" poster="'+i()(r)+'" src="https://media.wemod.com/videos/upgrade-promo/pro-showcase/save-mods.webm" type="video/webm"> </wm-background-video> <save-mods-illustration></save-mods-illustration> </pro-showcase-feature> <pro-showcase-feature if.bind="currentFeature === \'pin_mods\'" feature="${\'pro_showcase.pin_mods\' | i18n}" description="${\'pro_showcase.pin_mods_description\' | i18n}" on-pro-cta-click.call="onProCtaClick()"> <wm-background-video slot="bg" poster="'+i()(a)+'" src="https://media.wemod.com/videos/upgrade-promo/pro-showcase/pin-mods.webm" type="video/webm"> </wm-background-video> <pin-mods-illustration></pin-mods-illustration> </pro-showcase-feature> <pro-showcase-feature if.bind="currentFeature === \'remote\'" feature="${\'pro_showcase.remote\' | i18n}" description="${\'pro_showcase.remote_description\' | i18n}" on-pro-cta-click.call="onProCtaClick()"> <img slot="bg" src="'+i()(n)+'"> </pro-showcase-feature> <pro-showcase-feature if.bind="currentFeature === \'boosts\'" feature="${\'pro_showcase.boosts\' | i18n}" description="${\'pro_showcase.boosts_description\' | i18n}" on-pro-cta-click.call="onProCtaClick()"> <wm-background-video slot="bg" poster="'+i()(l)+'" src="https://media.wemod.com/videos/upgrade-promo/pro-showcase/boost.webm" type="video/webm"> </wm-background-video> </pro-showcase-feature> <pro-showcase-feature if.bind="currentFeature === \'game_guide\'" feature="${\'pro_showcase.game_guide\' | i18n}" description="${\'pro_showcase.game_guide_description\' | i18n}" on-pro-cta-click.call="onProCtaClick()"> <wm-background-video slot="bg" poster="'+i()(p)+'" src="https://media.wemod.com/videos/upgrade-promo/pro-showcase/game-guide.webm" type="video/webm"> </wm-background-video> <game-guide-illustration></game-guide-illustration> </pro-showcase-feature> </div> <div class="pro-showcase-option-list"> <pro-showcase-option mouseover.delegate="currentFeature = \'remote\'" selected.bind="currentFeature === \'remote\'"> <content slot="title"> ${\'pro_showcase.remote\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.remote_subtitle\' | i18n}</content> <content slot="icon"> <span class="pro-showcase-option-font-icon"> devices </span> </content> </pro-showcase-option> <pro-showcase-option mouseover.delegate="currentFeature = \'save_mods\'" selected.bind="currentFeature === \'save_mods\'" is-popular.bind="true"> <content slot="title"> ${\'pro_showcase.save_mods\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.save_mods_subtitle\' | i18n}</content> <content slot="icon"> <span class="save-cheats-icon"> <inline-svg src="'+i()(c)+'"></inline-svg> </span> </content> </pro-showcase-option> <pro-showcase-option mouseover.delegate="currentFeature = \'pin_mods\'" selected.bind="currentFeature === \'pin_mods\'"> <content slot="title"> ${\'pro_showcase.pin_mods\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.pin_mods_subtitle\' | i18n}</content> <content slot="icon"><span class="pro-showcase-option-font-icon--filled"> keep </span></content> </pro-showcase-option> <pro-showcase-option mouseover.delegate="currentFeature = \'boosts\'" selected.bind="currentFeature === \'boosts\'"> <content slot="title"> ${\'pro_showcase.boosts\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.boosts_subtitle\' | i18n}</content> <content slot="icon"><span class="pro-showcase-option-font-icon"> double_arrow </span></content> </pro-showcase-option> <pro-showcase-option mouseover.delegate="currentFeature = \'game_guide\'" selected.bind="currentFeature === \'game_guide\'"> <content slot="title"> ${\'pro_showcase.game_guide\' | i18n}</content> <content slot="subtitle">${\'pro_showcase.game_guide_subtitle\' | i18n}</content> <content slot="icon"> <img src="'+i()(d)+'" alt="${\'pro_showcase.game_guide_assistant_icon_alt\' | i18n}"> </content> </pro-showcase-option> </div> </div> </template> '},"pro-promos/pro-showcase/pro-showcase.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r),n=t(4417),l=t.n(n),p=new URL(t(83959),t.b),c=a()(i()),d=l()(p);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${d}) format("woff2")}.material-symbols-outlined,pro-showcase .pro-showcase-option-font-icon,pro-showcase .pro-showcase-option-font-icon--filled{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}pro-showcase{--pro-showcase-accent: #fa1280}pro-showcase .pro-showcase{display:flex;flex-direction:column}pro-showcase .pro-showcase-feature-display{height:466px}pro-showcase .pro-showcase-option-list{display:flex;gap:4px;padding:20px;background-color:var(--theme--background);border-top:1px solid rgba(255,255,255,.15)}pro-showcase .pro-showcase-option-list pro-showcase-option{flex:1 1 0}pro-showcase .pro-showcase-option-list svg,pro-showcase .pro-showcase-option-list img{width:20px;height:20px}pro-showcase .pro-showcase-option-list img{border-radius:100px;overflow:hidden}pro-showcase .pro-showcase-option-font-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}pro-showcase .pro-showcase-option-font-icon--filled{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important}`,""]);const m=c},"pro-promos/pro-showcase/resources/elements/pro-showcase-feature":(e,o,t)=>{t.r(o),t.d(o,{ProShowcaseFeature:()=>n});var s=t(15215),i=t("aurelia-framework"),r=t(71341),a=t(54995);let n=class{#c;constructor(e){this.#c=e}handleProCtaClick(){this.onProCtaClick?.(),this.subscription||this.#c.open({trigger:"pro_showcase"})}};(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],n.prototype,"feature",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],n.prototype,"description",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Boolean)],n.prototype,"isPopular",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Function)],n.prototype,"onProCtaClick",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],n.prototype,"proUserCtaKey",void 0),n=(0,s.Cg)([(0,i.autoinject)(),(0,a.m6)({selectors:{subscription:(0,a.$t)((e=>e.account?.subscription))}}),(0,s.Sn)("design:paramtypes",[r.U])],n)},"pro-promos/pro-showcase/resources/elements/pro-showcase-feature.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s='<template> <require from="./pro-showcase-feature.scss"></require> <require from="pro-promos/resources/elements/pro-promo-badge"></require> <require from="pro-promos/resources/elements/pro-promo-copy"></require> <require from="resources/elements/pro-cta-label"></require> <div class="pro-showcase-feature"> <div class="pro-showcase-feature-bg"> <slot name="bg"></slot> </div> <div class="pro-showcase-feature-fg"> <div class="pro-showcase-feature-column"> <pro-promo-copy tagline.bind="subscription ? \'pro_showcase.new_in_wemod_pro\' : \'pro_showcase.ready_to_level_up_go_pro\' | i18n | markdown" description.bind="description" heading.bind="feature" is-popular.bind="isPopular"> </pro-promo-copy> <wm-button class="free-trial-button" size="m" trailing-icon="arrow_forward" click.delegate="handleProCtaClick()"> <pro-cta-label if.bind="!subscription"></pro-cta-label> <span else>${(proUserCtaKey || \'pro_showcase.check_it_out\') | i18n}</span> </wm-button> </div> <div class="pro-showcase-feature-illustration"> <slot></slot> </div> </div> </div> </template> '},"pro-promos/pro-showcase/resources/elements/pro-showcase-feature.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r)()(i());a.push([e.id,'pro-showcase-feature .pro-showcase-feature{display:flex;align-items:center;position:relative;width:100%;height:100%}pro-showcase-feature .pro-showcase-feature-fg{position:absolute;display:flex;align-items:center;justify-content:space-between;padding:0 70px 0 36px;width:100%}pro-showcase-feature .pro-showcase-feature-bg{width:100%;height:100%}pro-showcase-feature .pro-showcase-feature-bg>*{display:flex;width:100%;height:100%}pro-showcase-feature .pro-showcase-feature-column{width:100%;height:100%;gap:4;width:320px;max-width:320px}pro-showcase-feature .pro-showcase-feature-column-header{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:40px;line-height:48px;letter-spacing:-2.5px;font-weight:900;color:var(--theme--text-highlight);margin:0;font-style:italic}pro-showcase-feature .pro-showcase-feature-column-header-container{display:flex;gap:10px}pro-showcase-feature .pro-showcase-feature-column-header-container .popular-badge{margin-top:18px}pro-showcase-feature .pro-showcase-feature-column-tagline{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;color:var(--theme--text-highlight)}pro-showcase-feature .pro-showcase-feature-column-tagline em{font-weight:500;font-style:italic}pro-showcase-feature .pro-showcase-feature-column-description{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:28px;color:var(--theme--text-primary)}pro-showcase-feature .pro-showcase-feature-column .free-trial-button{display:block;margin-top:20px;width:fit-content}',""]);const n=a},"pro-promos/pro-showcase/resources/elements/pro-showcase-option":(e,o,t)=>{t.r(o),t.d(o,{ProShowcaseOption:()=>r});var s=t(15215),i=t("aurelia-framework");class r{}(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Boolean)],r.prototype,"selected",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Boolean)],r.prototype,"isPopular",void 0)},"pro-promos/pro-showcase/resources/elements/pro-showcase-option.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s='<template> <require from="./pro-showcase-option.scss"></require> <div class="pro-showcase-option ${selected ? \'pro-showcase-option--selected\' : \'\'}"> <span class="pro-showcase-option-icon"><slot name="icon"></slot></span> <span class="pro-showcase-option-title-container"><h2 class="pro-showcase-option-title"><slot name="title"></slot></h2> <span if.bind="isPopular" class="pro-showcase-option-popular-badge"> ${\'pro_showcase.popular\' | i18n} </span> </span> <span class="pro-showcase-option-subtitle"><slot name="subtitle"></slot></span> </div> </template> '},"pro-promos/pro-showcase/resources/elements/pro-showcase-option.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r)()(i());a.push([e.id,'pro-showcase-option .pro-showcase-option{display:flex;flex-direction:column;color:var(--theme--text-primary);border-radius:16px;padding:12px;transition:background-color .2s}pro-showcase-option .pro-showcase-option--selected{background-color:rgba(255,255,255,.05)}pro-showcase-option .pro-showcase-option--selected .pro-showcase-option-icon{color:var(--theme--text-highlight)}pro-showcase-option .pro-showcase-option--selected .pro-showcase-option-title{color:var(--theme--text-highlight)}pro-showcase-option .pro-showcase-option-icon{margin-bottom:6px}pro-showcase-option .pro-showcase-option-popular-badge{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;color:var(--theme--text-highlight);font-size:8px;line-height:12px;height:min-content;padding:1px 3px;border-radius:4px;background-color:var(--pro-showcase-accent)}pro-showcase-option .pro-showcase-option-title{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:800;margin:0;font-style:italic}pro-showcase-option .pro-showcase-option-title-container{display:inline-flex;align-items:center;gap:6px}pro-showcase-option .pro-showcase-option-subtitle{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}',""]);const n=a},"pro-promos/resources/elements/faux-mods-ui.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(14385),i=t.n(s),r=new URL(t(51091),t.b),a=i()(r);const n='<template> <require from="./faux-mods-ui.scss"></require> <div class="faux-mods-ui"> <div class="faux-mods-ui-save-toggle"> <span class="faux-mods-ui-save-toggle-button"><i class="faux-mods-ui-save-toggle-button-icon"><inline-svg src="'+a+'"></inline-svg></i></span> ${\'faux_mods_ui.save_mods\' | i18n | markdown} </div> <ul class="faux-mods-ui-mods-list"> <li> <i class="faux-mods-ui-mod-icon"><inline-svg src="'+a+'"></inline-svg></i> <span class="faux-mods-ui-mod-copy">${\'faux_mods_ui.mod_unlimited_health\' | i18n | markdown}</span> <span class="faux-mods-ui-mod-toggle-button">${\'faux_mods_ui.mod_off\' | i18n | markdown} <span>${\'faux_mods_ui.mod_on\' | i18n | markdown}</span></span> </li> <li> <i class="faux-mods-ui-mod-icon"><inline-svg src="'+a+'"></inline-svg></i> <span class="faux-mods-ui-mod-copy">${\'faux_mods_ui.mod_unlimited_stamina\' | i18n | markdown}</span> <span class="faux-mods-ui-mod-toggle-button">${\'faux_mods_ui.mod_off\' | i18n | markdown} <span>${\'faux_mods_ui.mod_on\' | i18n | markdown}</span></span> </li> <li> <i class="faux-mods-ui-mod-icon"><inline-svg src="'+a+'"></inline-svg></i> <span class="faux-mods-ui-mod-copy">${\'faux_mods_ui.mod_game_speed\' | i18n | markdown}</span> <span class="faux-mods-ui-mod-slider"> <span class="value">${\'faux_mods_ui.mod_game_speed_value\' | i18n | markdown}</span> <span class="outer-bar"><span class="inner-bar"></span></span> </span> </li> </ul> </div> </template> '},"pro-promos/resources/elements/faux-mods-ui.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r),n=t(4417),l=t.n(n),p=new URL(t(11494),t.b),c=a()(i()),d=l()(p);c.push([e.id,`.faux-mods-ui{display:flex;align-items:center;flex-direction:column;width:100%;height:100%;padding:14px 28px;overflow:hidden;color:#fff}.faux-mods-ui-save-toggle{display:flex;align-items:center;gap:8px;border-radius:96px;background:rgba(255,255,255,.3);backdrop-filter:blur(12px);padding:10px 16px 10px 12px;font-size:13.5px;font-weight:700;margin:0 0 10px}.faux-mods-ui-save-toggle-button{display:block;position:relative;border-radius:12px;width:35px;height:21px;background:#fa1280}.faux-mods-ui-save-toggle-button-icon{position:absolute;top:2px;right:2px;display:flex;align-items:center;justify-content:center;width:17px;height:17px;border-radius:17px;background:#fff}.faux-mods-ui-save-toggle-button-icon svg{height:11px}.faux-mods-ui-save-toggle-button-icon svg *{fill:#fa1280}.faux-mods-ui-mods-list{display:block;border-radius:10px;background:rgba(255,255,255,.3);backdrop-filter:blur(12px);margin:0;font-size:10px;font-weight:500;width:100%;padding:0}.faux-mods-ui-mods-list li{display:flex;gap:9px;align-items:center;padding:7.5px 9px;margin:0}.faux-mods-ui-mod-icon svg{height:11px}.faux-mods-ui-mod-icon svg *{fill:#fa1280}.faux-mods-ui-mod-copy{flex-grow:1;white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.faux-mods-ui-mod-toggle-button{padding:2px 2px 2px 8px;border-radius:21px;background:var(--white-20, rgba(255, 255, 255, 0.2));font-size:9px;line-height:200%;word-wrap:none;flex-shrink:0}.faux-mods-ui-mod-toggle-button span{display:inline-block;background:#fa1280;border-radius:10px;padding:0 6px;margin:0 0 0 4px}.faux-mods-ui-mod-slider{display:flex;align-items:center}.faux-mods-ui-mod-slider .value{background:var(--white-20, rgba(255, 255, 255, 0.2));border-radius:6px;font-size:9px;line-height:18px;padding:2px 6px;margin:0 4px 0}.faux-mods-ui-mod-slider .outer-bar{background:var(--white-20, rgba(255, 255, 255, 0.2));border-radius:6px;width:50px;height:22px;overflow:hidden}.faux-mods-ui-mod-slider .inner-bar{display:block;position:relative;height:100%;width:55%;background:#fa1280}.faux-mods-ui-mod-slider .inner-bar::after{display:block;position:absolute;content:"";top:0;right:0;transform:translateX(50%);width:12px;height:100%;border-radius:6px;background:#fff;box-shadow:0px 1px 1px rgba(0,0,0,.14),0px 2px 1px rgba(0,0,0,.12),0px 1px 3px rgba(0,0,0,.2);background-image:url(${d});background-position:center;background-repeat:no-repeat}`,""]);const m=c},"pro-promos/resources/elements/mod-timers-illustration.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s='<template> <require from="./mod-timers-illustration.scss"></require> <div class="mod-timers-illustration"> <svg width="189" height="40" viewBox="0 0 189 40" fill="none" xmlns="http://www.w3.org/2000/svg" class="refill-stamina"> <rect x="0.25" width="188" height="40" rx="12" fill="white" fill-opacity="0.2"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="16" font-style="italic" font-weight="800" letter-spacing="-0.75px"> <tspan x="10.2578" y="25.8182">${\'pro_showcase.refill_stamina\' | i18n}</tspan> </text> <rect x="117.25" y="6" width="65" height="28" rx="8" fill="#FFE61C"/> <mask id="mask0_17973_14893" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="125" y="12" width="17" height="16"> <rect x="125.25" y="12" width="16" height="16" fill="#D9D9D9"/> </mask> <g mask="url(#mask0_17973_14893)"> <path d="M129.832 26.9962L126.93 24.094L129.832 21.1918L131.001 22.394L130.128 23.2578H136.565V20.5912H138.237V24.9303H130.128L131.001 25.794L129.832 26.9962ZM128.263 19.5027V15.1637H136.382L135.509 14.2998L136.678 13.0977L139.58 15.9998L136.678 18.902L135.509 17.6998L136.382 16.836H129.936V19.5027H128.263Z" fill="#7C460B"/> </g> <text fill="#7C460B" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="bold" letter-spacing="-0.25px"> <tspan x="145.25" y="25.0909">10${\'pro_showcase.minute\' | i18n}</tspan> </text> </svg> <svg width="280" height="144" viewBox="0 0 280 144" fill="none" xmlns="http://www.w3.org/2000/svg" class="mod-timer-tooltip"> <g filter="url(#filter0_di_17973_14901)"> <rect x="0" y="0" width="280" height="144" rx="16" fill="white" fill-opacity="0.05" shape-rendering="crispEdges"/> <rect x="0" y="0" width="280.5" height="144.5" rx="16.25" stroke="white" stroke-opacity="0.1" stroke-width="0.5" style="mix-blend-mode:overlay" shape-rendering="crispEdges"/> <rect x="12" y="12" width="256" height="38" rx="19" fill="white" fill-opacity="0.2"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="bold" letter-spacing="-0.5px" class="off"> <tspan x="40.75" y="36.091">${\'pro_showcase.off\' | i18n}</tspan> </text> <path d="M98.5 31C98.5 21.6112 106.111 14 115.5 14H162C171.389 14 179 21.6112 179 31C179 40.389 171.389 48 162 48H115.5C106.111 48 98.5 40.389 98.5 31Z" fill="white"/> <text fill="#7C460B" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-style="italic" font-weight="800" letter-spacing="-0.75px" class="on"> <tspan x="128.495" y="36.091">${\'pro_showcase.on\' | i18n}</tspan> </text> <mask id="mask0_17973_14901" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="195" y="21" width="20" height="20"> <rect x="195" y="21" width="20" height="20" fill="#D9D9D9"/> </mask> <g mask="url(#mask0_17973_14901)"> <path d="M200.833 39.335L197.5 36.001L200.833 32.668L202 33.876L200.708 35.168H209.167V31.835H210.833V36.835H200.708L202 38.126L200.833 39.335ZM199.167 30.168V25.168H209.292L208 23.8763L209.167 22.668L212.5 26.0013L209.167 29.335L208 28.1263L209.292 26.8346H200.833V30.168H199.167Z" fill="white"/> </g> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="bold" letter-spacing="-0.5px"> <tspan x="221" y="36.091">${\'pro_showcase.loop\' | i18n}</tspan> </text> <rect x="12" y="56" width="61" height="32" rx="8" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="33.005" y="77.091">1${\'pro_showcase.minute\' | i18n}</tspan> </text> <rect x="77" y="56" width="61" height="32" rx="8" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="96.973" y="77.091">5${\'pro_showcase.minute\' | i18n}</tspan> </text> <path d="M142 64C142 59.582 145.582 56 150 56H195C199.418 56 203 59.582 203 64V80C203 84.418 199.418 88 195 88H150C145.582 88 142 84.418 142 80V64Z" fill="white"/> <text fill="#7C460B" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-style="italic" font-weight="800" letter-spacing="-0.75px"> <tspan x="158.327" y="77.091">10${\'pro_showcase.minute\' | i18n}</tspan> </text> <rect x="207" y="56" width="61" height="32" rx="8" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="222.283" y="77.091">30${\'pro_showcase.minute\' | i18n}</tspan> </text> <g clip-path="url(#clip0_17973_14901)"> <rect x="8" y="96" width="264" height="40" rx="20" fill="#FFE61C"/> <text fill="#7C460B" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="bold" letter-spacing="-0.25px"> <tspan x="102" y="121.091">${\'pro_showcase.start_timer\' | i18n}</tspan> </text> </g> </g> <path d="M229.49 138.105L235.403 141.519C237.214 142.565 239.365 142.848 241.385 142.307C245.59 141.18 248.086 136.857 246.959 132.652L246.153 129.641L237.256 129.494L235.674 123.591L232.132 124.54L235.573 137.384L231.323 134.93L229.49 138.105Z" fill="white" stroke="black" stroke-width="1.08586"/> <defs> <filter id="filter0_di_17973_14901" x="0.5" y="0.5" width="280" height="144" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="35"/> <fecomposite in2="hardAlpha" operator="out"/> <fecolormatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/> <feblend mode="plus-darker" in2="BackgroundImageFix" result="effect1_dropShadow_17973_14901"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_17973_14901" result="shape"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="20"/> <fecomposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/> <fecolormatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"/> <feblend mode="normal" in2="shape" result="effect2_innerShadow_17973_14901"/> </filter> <clippath id="clip0_17973_14901"> <rect x="8" y="96" width="264" height="40" rx="20" fill="white"/> </clippath> </defs> </svg> <svg width="214" height="40" viewBox="0 0 214 40" fill="none" xmlns="http://www.w3.org/2000/svg" class="unlimited-health"> <rect x="0.333984" width="213" height="40" rx="12" fill="white" fill-opacity="0.2"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="16" font-style="italic" font-weight="800" letter-spacing="-0.75px"> <tspan x="10.4512" y="25.8182">${\'pro_showcase.unlimited_health\' | i18n}</tspan> </text> <rect x="139.334" y="6" width="68" height="28" rx="8" fill="#FFE61C"/> <mask id="mask0_17973_14888" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="147" y="12" width="17" height="16"> <rect x="147.334" y="12" width="16" height="16" fill="#D9D9D9"/> </mask> <g mask="url(#mask0_17973_14888)"> <path d="M157.393 23.1905L158.543 22.0593L156.143 19.6587V16.7145H154.527V20.3197L157.393 23.1905ZM155.335 26.9877C154.362 26.9877 153.452 26.8046 152.603 26.4383C151.754 26.0721 151.016 25.5751 150.388 24.9472C149.76 24.3194 149.263 23.5812 148.897 22.7325C148.531 21.8837 148.348 20.9731 148.348 20.0007C148.348 19.0282 148.531 18.1176 148.897 17.2688C149.263 16.4202 149.76 15.6819 150.388 15.0542C151.016 14.4263 151.754 13.9292 152.603 13.563C153.452 13.1968 154.362 13.0137 155.335 13.0137C156.307 13.0137 157.218 13.1968 158.066 13.563C158.915 13.9292 159.653 14.4263 160.281 15.0542C160.909 15.6819 161.406 16.4202 161.772 17.2688C162.139 18.1176 162.322 19.0282 162.322 20.0007C162.322 20.9731 162.139 21.8837 161.772 22.7325C161.406 23.5812 160.909 24.3194 160.281 24.9472C159.653 25.5751 158.915 26.0721 158.066 26.4383C157.218 26.8046 156.307 26.9877 155.335 26.9877ZM155.334 25.221C156.781 25.221 158.013 24.7141 159.03 23.7003C160.047 22.6866 160.555 21.4533 160.555 20.0007C160.555 18.548 160.047 17.3148 159.03 16.301C158.013 15.2872 156.781 14.7803 155.335 14.7803C153.888 14.7803 152.657 15.2872 151.64 16.301C150.623 17.3148 150.114 18.548 150.114 20.0007C150.114 21.4533 150.623 22.6866 151.64 23.7003C152.657 24.7141 153.888 25.221 155.334 25.221Z" fill="#7C460B"/> </g> <text fill="#7C460B" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="bold" letter-spacing="-0.25px"> <tspan x="167.334" y="25.0909">30${\'pro_showcase.minute\' | i18n}</tspan> </text> </svg> </div> </template> '},"pro-promos/resources/elements/mod-timers-illustration.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r)()(i());a.push([e.id,"mod-timers-illustration .mod-timers-illustration svg{backdrop-filter:blur(10px);border-radius:16px;position:absolute}mod-timers-illustration .mod-timers-illustration svg .off,mod-timers-illustration .mod-timers-illustration svg .on{text-transform:uppercase}mod-timers-illustration .mod-timers-illustration .refill-stamina{top:-15%;left:45%}mod-timers-illustration .mod-timers-illustration .mod-timer-tooltip{top:20%;left:55%;padding:0}mod-timers-illustration .mod-timers-illustration .unlimited-health{left:70%;top:100%}",""]);const n=a},"pro-promos/resources/elements/pro-promo-badge":(e,o,t)=>{t.r(o),t.d(o,{ProPromoBadge:()=>r});var s=t(15215),i=t(30960);class r{constructor(){this.size="m"}}(0,s.Cg)([i._t,(0,s.Sn)("design:type",String)],r.prototype,"size",void 0)},"pro-promos/resources/elements/pro-promo-badge.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s='<template> <require from="./pro-promo-badge.scss"></require> <span class="pro-promo-badge pro-promo-badge--${size}"> <slot></slot> </span> </template> '},"pro-promos/resources/elements/pro-promo-badge.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r)()(i());a.push([e.id,'pro-promo-badge{display:inline-flex}pro-promo-badge .pro-promo-badge{height:min-content;color:var(--theme--text-highlight);background-color:var(--pro-showcase-accent)}pro-promo-badge .pro-promo-badge--s{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;font-size:8px;line-height:12px;padding:1px 3px;border-radius:4px}pro-promo-badge .pro-promo-badge--m{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:11px;line-height:16px;text-transform:uppercase;letter-spacing:.5px;padding:2px 6px;border-radius:6px}',""]);const n=a},"pro-promos/resources/elements/pro-promo-copy":(e,o,t)=>{t.r(o),t.d(o,{ProPromoCopy:()=>r});var s=t(15215),i=t("aurelia-framework");class r{}(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"heading",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"description",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Boolean)],r.prototype,"isPopular",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"tagline",void 0)},"pro-promos/resources/elements/pro-promo-copy.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s='<template> <require from="./pro-promo-copy.scss"></require> <div class="pro-promo-copy"> <span class="pro-promo-copy-tagline" innerhtml.bind="tagline"> </span> <div class="pro-promo-copy-header-container"> <h1 class="pro-promo-copy-header">${heading}</h1> <pro-promo-badge if.bind="isPopular" class="popular-badge"> ${\'pro_showcase.popular\' | i18n} </pro-promo-badge> </div> <span class="pro-promo-copy-description"> ${description}</span> </div> </template> '},"pro-promos/resources/elements/pro-promo-copy.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>n});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r)()(i());a.push([e.id,'pro-promo-copy .pro-promo-copy{display:flex;flex-direction:column;width:100%;height:100%;gap:4;width:320px;max-width:320px}pro-promo-copy .pro-promo-copy-header{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:40px;line-height:48px;letter-spacing:-2.5px;font-weight:900;color:var(--theme--text-highlight);margin:0;font-style:italic}pro-promo-copy .pro-promo-copy-header-container{display:flex;gap:10px}pro-promo-copy .pro-promo-copy-header-container .popular-badge{margin-top:18px}pro-promo-copy .pro-promo-copy-tagline{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:16px;line-height:24px;color:var(--theme--text-highlight)}pro-promo-copy .pro-promo-copy-tagline em{font-weight:500;font-style:italic}pro-promo-copy .pro-promo-copy-description{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:28px;color:var(--theme--text-primary)}pro-promo-copy .pro-promo-copy-content-wrapper{display:block;margin-top:20px}',""]);const n=a},"pro-promos/tooltips/pro-precision-mods-tooltip":(e,o,t)=>{t.r(o),t.d(o,{ProPrecisionModsTooltip:()=>r});var s=t(15215),i=t("aurelia-framework");let r=class{constructor(){this.size="large"}};(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Object)],r.prototype,"tooltipId",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Object)],r.prototype,"useDelegate",void 0),(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",String)],r.prototype,"size",void 0),r=(0,s.Cg)([(0,i.autoinject)()],r)},"pro-promos/tooltips/pro-precision-mods-tooltip.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>s});const s='<template> <require from="shared/resources/elements/pro-badge"></require> <require from="resources/elements/pro-cta-label"></require> <require from="shared/pro-promos/precision-mods-illustration.html"></require> <require from="./pro-precision-mods-tooltip.scss"></require> <wm-tooltip-large-promo tooltip-id.bind="tooltipId" use-delegate.bind="useDelegate" placement="right"> <div slot="title" class="pro-precision-mods-tooltip-title"> <span>${\'precision_mods_tooltip.precision_controls\' | i18n | markdown}</span> <span class="icon pro-precision-mods-tooltip-pro-badge"><pro-badge class="small"></pro-badge></span> </div> <div slot="copy" class="pro-precision-mods-tooltip-copy"> ${\'precision_mods_tooltip.precision_mods_info\' | i18n | markdown} </div> <div slot="cta"> <wm-button size="m" pro-cta="trigger: pro_precision_mods_tooltip_large"><pro-cta-label></pro-cta-label><span slot="trailing"><i class="pro-precision-mods-tooltip-cta-arrow"></i></span></wm-button> </div> <div slot="media" class="pro-precision-mods-tooltip-media"> <div class="pro-precision-mods-tooltip-media-overlay"> <precision-mods-illustration></precision-mods-illustration> </div> </div> </wm-tooltip-large-promo> </template> '},"pro-promos/tooltips/pro-precision-mods-tooltip.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>h});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r),n=t(4417),l=t.n(n),p=new URL(t(83959),t.b),c=new URL(t(15781),t.b),d=a()(i()),m=l()(p),u=l()(c);d.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,.pro-precision-mods-tooltip-cta-arrow{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.pro-precision-mods-tooltip-title{display:flex;align-items:center;gap:8px}.pro-precision-mods-tooltip-pro-badge{line-height:0;font-style:normal}.pro-precision-mods-tooltip-cta-arrow{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;line-height:1;font-size:16px;vertical-align:middle}.pro-precision-mods-tooltip-cta-arrow:before{font-family:inherit;content:"arrow_forward"}.pro-precision-mods-tooltip-media{position:relative;background:no-repeat center/cover url(${u})}.pro-precision-mods-tooltip-media-overlay{position:absolute;top:0;left:0;width:100%;height:100%;padding:30px 15.5px 0}.pro-precision-mods-tooltip-media-overlay .precision-mods-illustration svg{border-radius:12px;width:100%;height:100%}.pro-precision-mods-tooltip-small-tooltip-content{font-feature-settings:"liga" off,"clig" off;font-weight:700;max-width:300px;padding:8px;display:flex;align-items:center;gap:6px}.pro-precision-mods-tooltip-small-tooltip-content h3{font-size:12px;line-height:16px;margin:0}`,""]);const h=d},"pro-promos/tooltips/remote-app-tooltip":(e,o,t)=>{t.r(o),t.d(o,{RemoteAppTooltip:()=>r});var s=t(15215),i=t("aurelia-framework");let r=class{constructor(){this.handleShow=()=>{this.promoVideoVM?.play?.()}}};(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Object)],r.prototype,"tooltipId",void 0),r=(0,s.Cg)([(0,i.autoinject)()],r)},"pro-promos/tooltips/remote-app-tooltip.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>a});var s=t(14385),i=t.n(s),r=new URL(t(88891),t.b);const a='<template> <require from="./remote-app-tooltip.scss"></require> <require from="../../resources/elements/pro-cta-label"></require> <require from="../../shared/resources/elements/pro-badge"></require> <wm-tooltip-large-promo tooltip-id.bind="tooltipId" on-show.bind="handleShow"> <div slot="title" class="remote-app-tooltip-title"> <span>${\'remote_app_tooltip.remote_app\' | i18n | markdown}</span> <span class="icon remote-app-tooltip-pro-badge"><pro-badge class="small"></pro-badge></span> </div> <div slot="copy" class="remote-app-tooltip-copy"> ${\'remote_app_tooltip.no_more_alt_tabbing\' | i18n | markdown} </div> <div slot="cta" class="remote-app-tooltip-cta-button"> <wm-button size="m" pro-cta="trigger: save_cheats_tooltip; feature: remote;"><pro-cta-label></pro-cta-label><span slot="trailing"><i class="remote-app-tooltip-cta-arrow"></i></span></wm-button> </div> <div slot="media" class="remote-app-tooltip-media"> <wm-background-video poster="'+i()(r)+'" src="https://media.wemod.com/videos/upgrade-promo/tooltip/remote-app.webm" type="video/webm" view-model.ref="promoVideoVM"></wm-background-video> </div> </wm-tooltip-large-promo> </template> '},"pro-promos/tooltips/remote-app-tooltip.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r),n=t(4417),l=t.n(n),p=new URL(t(83959),t.b),c=a()(i()),d=l()(p);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${d}) format("woff2")}.material-symbols-outlined,.remote-app-tooltip-cta-arrow{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.remote-app-tooltip-title{display:flex;align-items:center;gap:8px;color:var(--theme--text-highlight)}.remote-app-tooltip-pro-badge{line-height:0;font-style:normal}.remote-app-tooltip-cta-button button{background-color:var(--wm-button-color-bg);color:var(--wm-button-color-fg);width:auto;min-width:auto;height:auto;border-radius:100px;padding:10px 16px;gap:4px}.remote-app-tooltip-cta-button i{color:var(--wm-button-color-fg)}.remote-app-tooltip-cta-arrow{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;line-height:1;font-size:16px;vertical-align:middle}.remote-app-tooltip-cta-arrow:before{font-family:inherit;content:"arrow_forward"}`,""]);const m=c},"pro-promos/tooltips/save-mods-tooltip":(e,o,t)=>{t.r(o),t.d(o,{SaveModsTooltip:()=>r});var s=t(15215),i=t("aurelia-framework");let r=class{constructor(){this.handleShow=()=>{this.promoVideoVM?.play?.()}}};(0,s.Cg)([i.bindable,(0,s.Sn)("design:type",Object)],r.prototype,"tooltipId",void 0),r=(0,s.Cg)([(0,i.autoinject)()],r)},"pro-promos/tooltips/save-mods-tooltip.html":(e,o,t)=>{t.r(o),t.d(o,{default:()=>a});var s=t(14385),i=t.n(s),r=new URL(t(59016),t.b);const a='<template> <require from="./save-mods-tooltip.scss"></require> <require from="../../shared/resources/elements/pro-badge"></require> <require from="../../resources/elements/pro-cta-label"></require> <require from="../resources/elements/faux-mods-ui.html"></require> <wm-tooltip-large-promo tooltip-id.bind="tooltipId" on-show.bind="handleShow"> <div slot="title" class="save-mods-tooltip-title"> <span>${\'save_mods_tooltip.save_mods\' | i18n | markdown}</span> <span class="icon save-mods-tooltip-pro-badge"><pro-badge class="small"></pro-badge></span> </div> <div slot="copy" class="save-mods-tooltip-copy">${\'save_mods_tooltip.save_mods_info\' | i18n | markdown}</div> <div slot="cta"> <wm-button size="m" pro-cta="trigger: save_mods_tooltip; feature: save_mods"><pro-cta-label></pro-cta-label><span slot="trailing"><i class="save-mods-tooltip-cta-arrow"></i></span></wm-button> </div> <div slot="media" class="save-mods-tooltip-media"> <wm-background-video poster="'+i()(r)+'" src="https://media.wemod.com/videos/upgrade-promo/tooltip/save-mods.webm" type="video/webm" view-model.ref="promoVideoVM"></wm-background-video> <div class="save-mods-tooltip-media-overlay"> <faux-mods-ui></faux-mods-ui> </div> </div> </wm-tooltip-large-promo> </template> '},"pro-promos/tooltips/save-mods-tooltip.scss":(e,o,t)=>{t.r(o),t.d(o,{default:()=>m});var s=t(31601),i=t.n(s),r=t(76314),a=t.n(r),n=t(4417),l=t.n(n),p=new URL(t(83959),t.b),c=a()(i()),d=l()(p);c.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${d}) format("woff2")}.material-symbols-outlined,.save-mods-tooltip-cta-arrow{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.save-mods-tooltip-title{display:flex;align-items:center;gap:8px}.save-mods-tooltip-pro-badge{line-height:0;font-style:normal}.save-mods-tooltip-cta-arrow{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;line-height:1;font-size:16px;vertical-align:middle}.save-mods-tooltip-cta-arrow:before{font-family:inherit;content:"arrow_forward"}.save-mods-tooltip-media{position:relative}.save-mods-tooltip-media-overlay{position:absolute;top:0;left:0;width:100%;height:100%}`,""]);const m=c}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[7458],{70893:(e,t,i)=>{i.d(t,{L:()=>d});var a=i(76982),s=i(65692),o=i(35392),r=i(49442),n=i(38777),l=i(29844);class d{#e;constructor(e){this.#e=e}async fetchBinary(e,t){const i=this.#t(e);if(await o.promises.stat(i).then((e=>e.isFile())).catch((()=>!1)))return i;try{return await this.#i(e,t)}catch{return await this.#i(e,t)}}isLocal(e){return o.promises.stat(this.#t(e)).then((e=>e.isFile())).catch((()=>!1))}async#i(e,t){const i=await this.#a(e);return await new Promise(((l,d)=>{const c=(0,o.createWriteStream)(i,{flags:"w+"}),p=(0,a.createHash)("sha256"),g=new n.Vd([t.onCancel((()=>{g.dispose(),d(new n._T("Binary download was canceled."))})),(0,n.nm)((()=>{p.destroy(),c.end()}))]);g.push((0,n.$U)(c,"open",(()=>{const t=(0,s.get)(e.url,(e=>{g.push((0,n.$U)(e,"end",(()=>c.end()))),e.statusCode&&e.statusCode>=300?e.resume():g.push((0,n.$U)(e,"data",(e=>{c.write(e),p.update(e)})))}));g.push((0,n.$U)(t,"error",(()=>g.dispose())))}))),g.push((0,n.$U)(c,"error",(()=>{g.dispose(),d(new Error("Failed to create temporary trainer file."))}))),g.push((0,n.$U)(c,"close",(async()=>{const t=p.digest("hex");if(g.dispose(),t!==e.hash)return o.promises.rm(i).catch(r.Y),void d(new Error("Failed to download trainer."));try{l(await this.#s(i,e))}catch(e){d(e)}})))}))}async#s(e,t){const i=this.#t(t);return await o.promises.rename(e,i),i}#t(e){return(0,l.fj)(this.#e,this.#o(e))}async#a(e){await o.promises.stat(this.#e).then((e=>e.isDirectory())).catch((()=>!1))||await o.promises.mkdir(this.#e);const t=(0,l.fj)(this.#e,"download");return await o.promises.stat(t).then((e=>e.isDirectory())).catch((()=>!1))||await o.promises.mkdir(t),(0,l.fj)(t,`${this.#o(e)}.${Date.now()}.tmp`)}#o(e){return`Trainer_${e.trainerId}_${e.hash.substring(0,10)}.dll`}}},"cheats/storage/index":(e,t,i)=>{i.r(t),i.d(t,{configure:()=>s});var a=i(70893);function s(e,t){e.container.registerInstance(a.L,new a.L(t.cacheDirectory))}},"cheats/title":(e,t,i)=>{i.r(t),i.d(t,{Title:()=>S});var a=i(15215),s=i(68663),o=i("app/resources/elements/help-menu"),r=i("aurelia-event-aggregator"),n=i("aurelia-framework"),l=i(18776),d=i(20770),c=i(89045),p=i("dialogs/suggest-mod-dialog"),g=i(41572),m=i(20057),h=i(62914),u=i(10191),b=i(96276),f=i(35030),v=i(62614),x=i(24008),w=i(92465),y=i(54995),k=i(70236),I=i(88849),_=i(48881);let S=class{#r;#n;#l;#d;#c;#p;#g;#m;#h;#u;constructor(e,t,i,a,s,o,r,n,l,d,c){this.showFavoriteButton=!1,this.handleSupportAssistantOpen=({menuButtonEl:e})=>{this.supportAssistantTriggerEl=e,this.isSupportAssistantOpen=!0},this.handleSupportAssistantClose=()=>{this.isSupportAssistantOpen=!1},this.#r=e,this.router=t,this.#n=i,this.#h=a,this.#l=s,this.#d=o,this.#c=r,this.#p=n,this.#g=l,this.overlayService=d,this.#m=c}get isTrainerAvailable(){return!!this.selectedGame&&(!!(0,k.Lt)(this.selectedGame.flags,x.rT.Available)||(this.creatorMode||(0,k.Lt)(this.account.flags,16384))&&(this.catalog.games.hasOwnProperty(this.selectedGame.id)??!1))}get translations(){const e=this.#g.getEffectiveLocale().baseName,t=this.gameTranslations?.[this.selectedTrainer?.gameId]?.[e];return{locale:e,strings:t??{}}}get canUseOverlay(){return this.overlayService.gameSupportsOverlay(this.selectedTrainer?.gameId)&&this.overlayService.canUseOverlay}canActivate(e){return this.#b(e.titleId)}#b(e){return"number"==typeof e||"string"==typeof e&&/^[0-9]+$/.test(e)}activate(e){this.titleId=e.titleId.toString(),this.#b(e.gameId)&&(this.selectedGameId=e.gameId?.toString()),this.#b(e.trainerId)&&(this.desiredTrainerId=e.trainerId?.toString()),this.autoLaunch=!!e.autoLaunch,this.autoLaunchhWithoutMods=!!e.autoLaunchhWithoutMods,this.#f(),this.#u=new w.Vd([this.#l.onInstallationsChanged((()=>this.#v())),this.#h.subscribe(o.EVENT_HELP_MENU_REPORT_ISSUE_CLICK,this.handleSupportAssistantOpen),this.#h.subscribe(o.EVENT_HELP_MENU_SUGGEST_MOD_CLICK,(()=>this.openSuggestModDialog()))])}deactivate(){this.#u?.dispose()}async bind(){this.envInfo=await(0,v.cR)()}async attached(){try{this.model=await this.#d.watch(this.titleId),this.showFavoriteButton=this.model?.games.some((e=>e.gameInstalled||e.appInstalled||(0,k.Lt)(e.flags,x.rT.Available)))??!1,this.#x()}catch{}}detached(){this.#d.unwatch(this.titleId)}get selectedTrainerNotesRead(){return this.selectedTrainer?.blueprint?.notes?(this.selectedTrainerNotesHash=(0,I.YZ)(this.selectedTrainer.blueprint.notes),this.trainerNotesRead?.[this.selectedTrainer.id]===this.selectedTrainerNotesHash):(this.selectedTrainerNotesHash=null,!0)}#x(){if(this.selectedGame)return;if(0===this.model?.games.length)throw new Error("No games found in title.");if(!this.selectedGameId){const e=this.titlePreferences[this.titleId]?.selectedGameId;e&&this.model?.games.find((t=>t.id===e))&&(this.selectedGameId=e)}if(this.selectedGameId){const e=this.model?.games.find((e=>e.id===this.selectedGameId));if(e)return void this.selectGame(e)}const e=this.model?.games.find((e=>e.gameInstalled||e.appInstalled));if(e)return void this.selectGame(e);const t=this.model?.games.map((e=>this.catalog.games[e.id])).filter((e=>void 0!==e)),i=t?.find((e=>(0,k.Lt)(e.flags,x.rT.Active)))||t?.find((e=>(0,k.Lt)(e.flags,x.rT.Available)));if(i){const e=this.model?.games.find((e=>e.id===i.id));if(e)return void this.selectGame(e)}this.model&&this.selectGame(this.model.games[0])}selectGame(e){this.#n.dispatch(_.B9,e.id),this.selectedGame?e!==this.selectedGame&&(this.selectedGame=null,setTimeout((()=>this.#w(e)),0)):this.#w(e)}#w(e){e!==this.selectedGame&&(this.desiredTrainerId=null,this.selectedGame=e,this.selectedGameId=this.selectedGame?.id,this.router.navigateToRoute("title",{titleId:this.titleId,gameId:this.selectedGameId,trainerId:""},{replace:!0,trigger:!1}),this.#n.dispatch(_.IH,this.titleId,{selectedGameId:e.id}))}get breadcrumbs(){const e=this.router.currentInstruction.queryParams;return[e.previousRoute||"collection/my-games"!==e.parentRoute?["app_header.explore","titles"]:["app_header.my_games","collection",{slug:"my-games"}]]}async openSuggestModDialog(){this.selectedGameId&&(0,g.a)(this.account)&&((await this.#c.open({isGameRecentlyUpdated:!!this.game.trainer?.updatedAt&&(0,c.A)(new Date,new Date(this.game.trainer?.updatedAt))<90,gameId:this.selectedGameId,gameSuggestedMods:this.suggestedMods,account:this.account})).wasCancelled||(this.suggestedMods=await this.#y(),this.#m.querySelector("mod-suggestion-list")?.scrollIntoView({behavior:"smooth"})))}get installedApp(){if(!this.selectedGame||!this.installedApps)return null;const e=this.selectedGame.correlationIds.find((e=>this.installedApps[e]));return this.installedApps[e??""]}#k(){if(!this.model)return;if(!this.selectedGame)return;const e=(0,k.Lt)(this.selectedGame.flags,x.rT.Available),t=(0,k.br)(this.selectedGame.flags,[x.rT.Outdated,x.rT.UpdateQueued]),i=(0,k.Lt)(this.selectedGame.flags,x.rT.Queued)?this.catalog.queue.indexOf(this.selectedGame.id)+1:null,a=!!this.preferredInstallation?.app&&("custom"!==this.preferredInstallation.app.platform||"custom"),s=this.catalog.titles[this.titleId],o=(0,k.Lt)(s?.flags,x.D1.HasAssistant),r=this.catalog.maps.some((e=>e.titleId===this.titleId));let n="unavailable";e&&(t?t&&i?n="pending_update":t&&!i&&(n="requires_testing"):n="active"),this.#r.screenView({name:this.model.name,class:"Game",params:{state:n,installed:a,title_id:this.titleId,platform:this.selectedGame.platformId,game_map:r,game_guide:o}})}#v(){const e=this.preferredInstallation;this.#f(),this.preferredInstallation?.app?.platform!==e?.app?.platform&&this.#k()}#f(){this.preferredInstallation=this.selectedGame?.id?this.#l.getPreferredInstallationInfo(this.selectedGame.id):null}async selectedGameChanged(){this.#f(),this.#k(),this.suggestedMods=await this.#y()}async#y(){return this.selectedGameId?await this.#p.getSuggestedMods(this.selectedGameId):{suggestable:{},suggested:[]}}titlePreferencesChanged(e,t){if(t[this.titleId]?.selectedGameId!==e[this.titleId]?.selectedGameId){const t=this.model?.games.find((t=>t.id===e[this.titleId]?.selectedGameId));t&&this.selectGame(t)}}};(0,a.Cg)([n.bindable,(0,a.Sn)("design:type",Object)],S.prototype,"selectedTrainer",void 0),(0,a.Cg)([n.observable,(0,a.Sn)("design:type",Object)],S.prototype,"selectedGame",void 0),(0,a.Cg)([n.bindable,(0,a.Sn)("design:type",Object)],S.prototype,"envInfo",void 0),(0,a.Cg)([n.bindable,n.observable,(0,a.Sn)("design:type",Object)],S.prototype,"game",void 0),(0,a.Cg)([(0,n.computedFrom)("selectedGame.flags","creatorMode","catalog.games"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"isTrainerAvailable",null),(0,a.Cg)([(0,n.computedFrom)("language","gameTranslations","selectedTrainer.gameId"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],S.prototype,"translations",null),(0,a.Cg)([(0,n.computedFrom)("overlayService.canUseOverlay","selectedTrainer.gameId"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"canUseOverlay",null),(0,a.Cg)([(0,n.computedFrom)("selectedTrainer","trainerNotesRead"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"selectedTrainerNotesRead",null),(0,a.Cg)([(0,n.computedFrom)("router.currentInstruction.queryParams"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],S.prototype,"breadcrumbs",null),(0,a.Cg)([(0,n.computedFrom)("selectedGame","installedApps"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],S.prototype,"installedApp",null),S=(0,a.Cg)([(0,y.m6)({selectors:{catalog:(0,y.$t)((e=>e.catalog)),creatorMode:(0,y.$t)((e=>e.settings?.creatorMode)),subscription:(0,y.$t)((e=>e.account?.subscription)),titlePreferences:(0,y.$t)((e=>e.titlePreferences)),lastTimeLimitPreGameDialog:(0,y.$t)((e=>e.timestamps?.lastTimeLimitPreGameDialog)),trainerNotesRead:(0,y.$t)((e=>e.trainerNotesRead)),installedApps:(0,y.$t)((e=>e.installedApps)),isTitleSidebarCollapsed:(0,y.$t)((e=>e.settings.isTitleSidebarCollapsed)),account:(0,y.$t)((e=>e.account)),gameTranslations:(0,y.$t)((e=>e.gameTranslations)),language:(0,y.$t)((e=>e.settings.language))}}),(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[h.j0,l.Ix,d.il,r.EventAggregator,b.T,f.U,p.SuggestModDialogService,s.x,m.F2,u.s,Element])],S)},"cheats/title.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>a});const a='<template> <require from="./title.scss"></require> <require from="./game"></require> <require from="./resources/custom-attributes/steam-hero-bg"></require> <require from="shared/cheats/resources/custom-attributes/steam-capsule-bg"></require> <require from="../shared/resources/custom-attributes/add-class-if-overflow"></require> <require from="../resources/elements/favorite-button"></require> <require from="../resources/elements/follow-button"></require> <require from="./resources/elements/title-settings-button"></require> <require from="./resources/elements/title-help-button"></require> <require from="./resources/elements/unavailable-game"></require> <require from="./resources/elements/trainer-play-button"></require> <require from="./resources/elements/save-cheats-toggle"></require> <require from="./resources/elements/title-sidebar"></require> <require from="./resources/elements/launch-without-mods-button"></require> <require from="../app/resources/elements/title-sidebar-collapse-button"></require> <require from="../support-assistant/support-assistant"></require> <section if.bind="model" class="title view-background au-animate"> <div class="background" steam-hero-bg="steam-id.bind: model.steamAppId; title-thumbnail.bind: model.thumbnail"></div> <div class="header-background" steam-hero-bg="steam-id.bind: model.steamAppId; title-thumbnail.bind: model.thumbnail"></div> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" ref="titleScrollContainer" overflow-fade="vertical"> <div class="title-layout ${isTrainerAvailable ? \'trainer-available\' : \'trainer-unavailable\'} ${isTitleSidebarCollapsed ? \'collapsed\' : \'\'}"> <section class="title-wrapper"> <header class="title-header"> <div class="title-section"> <div class="game-image" steam-capsule-bg="steam-id.bind: model.steamAppId"></div> <div class="title" add-class-if-overflow="condensed" title="${model.name}"> ${model.name} </div> <div class="title-actions"> <favorite-button class="title-header-button" if.bind="showFavoriteButton" show-tooltip="true" title-id.bind="model.id"></favorite-button> <follow-button class="title-header-button" game-id.bind="selectedGame.id" show-tooltip="true"></follow-button> <title-settings-button class="title-header-button" title-id.bind="titleId" selected-trainer.bind="selectedTrainer"></title-settings-button> <title-help-button class="title-header-button" selected-trainer.bind="selectedTrainer" selected-game.bind="selectedGame" title-name.bind="model.name"></title-help-button> </div> <div class="trainer-actions" if.bind="isTrainerAvailable"> <save-cheats-toggle game.bind="game" trainer.bind="selectedTrainer" if.bind="trainerPlayButtonState !== null" class="au-animate"></save-cheats-toggle> <trainer-play-button trainer-info.bind="selectedTrainer" model.bind="model" selected-trainer-notes-read.bind="selectedTrainerNotesRead" disabled-message.bind="selectedTrainerNotesRead ? null : \'game.read_notes\'" button-state.bind="trainerPlayButtonState" auto-launch.bind="autoLaunch" auto-launch-without-mods.bind="autoLaunchhWithoutMods" class="au-animate"></trainer-play-button> <title-sidebar-collapse-button if.bind="subscription && isTitleSidebarCollapsed"></title-sidebar-collapse-button> </div> <template else> <launch-without-mods-button if.bind="installedApp" class="large" game-id.bind="selectedGame.id" app.bind="installedApp" trigger="unavailable_game" primary.bind="true"></launch-without-mods-button> </template> </div> </header> <div class="title-game"> <template if.bind="selectedGame"> <game if.bind="isTrainerAvailable" selected-trainer.two-way="selectedTrainer" game.two-way="game" game-id.bind="selectedGame.id" selected-trainer-notes-read.bind="selectedTrainerNotesRead" trainer-id.bind="desiredTrainerId" trainer-play-button-state.bind="trainerPlayButtonState" title-info.bind="model" suggested-mods.bind="suggestedMods" suggest-mod.call="openSuggestModDialog()"></game> <unavailable-game else title-info.bind="model" game-info.bind="selectedGame"></unavailable-game> </template> </div> </section> <title-sidebar class="${isTitleSidebarCollapsed ? \'collapsed\' : \'\'}" if.bind="selectedGame" title-info.bind="model" game.bind="game" selected-trainer.bind="selectedTrainer" title-scroll-container.bind="titleScrollContainer"></title-sidebar> <support-assistant if.bind="isSupportAssistantOpen" class="title-support-assistant" trainer-info.bind="selectedTrainer" title-info.bind="model" game-info.bind="selectedGame" preferred-installation.bind="preferredInstallation" env-info.bind="envInfo" account.bind="account" can-use-overlay.bind="canUseOverlay" game-translations.bind="translations" on-close.bind="handleSupportAssistantClose" trigger-el.bind="supportAssistantTriggerEl"></support-assistant> </div> </div> </div> </section> </template> '},"cheats/title.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>h});var a=i(31601),s=i.n(a),o=i(76314),r=i.n(o),n=i(4417),l=i.n(n),d=new URL(i(83959),i.b),c=new URL(i(98593),i.b),p=r()(s()),g=l()(d),m=l()(c);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,.app-sidebar .nav a i.home-icon,.app-sidebar .nav a i.my-games-icon,.app-sidebar .nav a i.my-videos-icon,.app-sidebar .nav a i.explore-icon,.app-sidebar .nav a i.upcoming-icon,.app-sidebar .nav a i.maps-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.app-sidebar{display:flex;flex-direction:column;height:100%;width:220px;transition:.3s width ease-in-out;background:linear-gradient(to bottom, rgba(var(--theme--background-accent--rgb), 0.5) 0%, var(--theme--background-accent) 100%);position:relative;z-index:1}.app-sidebar sidebar-game-lists{overflow-y:hidden;height:-webkit-fill-available;padding-bottom:24px;scrollbar-gutter:stable}.app-sidebar sidebar-game-lists::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:window-inactive:hover,.app-sidebar sidebar-game-lists::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:decrement{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar-button:single-button:vertical:increment{height:8px}.app-sidebar sidebar-game-lists::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent 8px, rgba(255, 255, 255, 0.1) 8px, rgba(255, 255, 255, 0.1) calc(100% - 8px), transparent calc(100% - 8px))}.app-sidebar sidebar-game-lists:hover{overflow-y:auto}.app-sidebar hr.sidebar-divider{display:flex;align-items:center;width:100%;height:0px;opacity:.5;border:none;border-top:1px solid rgba(255,255,255,.15);margin:8px 0}.app-sidebar hr.sidebar-divider.nav-divider{width:calc(100% - 16px);margin:8px auto}.app-sidebar.collapsed{width:72px}.app-sidebar.collapsed hr.sidebar-divider.nav-divider{width:100%}.app-sidebar.collapsed .sidebar-header{align-items:center;justify-content:center}.app-sidebar.collapsed .sidebar-header-branding .wemod-logo{margin:0 auto}.app-sidebar.collapsed .sidebar-header-branding .wemod-copy{visibility:hidden;position:absolute;left:40px;scale:.5;opacity:0}.app-sidebar.collapsed .nav{align-items:center;min-width:0;width:100%;margin:0 auto}.app-sidebar.collapsed .nav-text{visibility:hidden;position:absolute;transform:translateX(-10px);opacity:0}.app-sidebar.collapsed .nav a{padding:0}.app-sidebar.collapsed .nav a i{font-size:24px !important;margin:0 auto}.app-sidebar.collapsed .nav a new-badge{display:none}.app-sidebar .sidebar-header{position:relative;z-index:1;display:flex;flex-direction:row;align-items:start;gap:22px;min-height:40px;padding:8px}.app-sidebar .sidebar-header-branding{display:flex;flex-direction:row;align-items:center;gap:6px;padding:0 8px}.app-sidebar .sidebar-header-branding .wemod-logo{display:flex;width:20px;height:12px;margin:0 auto}.app-sidebar .sidebar-header-branding .wemod-copy{font-weight:600;font-size:14px;line-height:24px;display:flex;align-items:center;color:#fff;opacity:1;transition:.2s all ease-in-out;scale:1}.app-sidebar app-sidebar-search-button{margin:4px 0 8px}.app-sidebar .nav{position:relative;z-index:1;display:flex;flex-direction:column;gap:4px;min-width:72px;width:100%;padding:0 8px 0px 8px;border-radius:8px}.app-sidebar .nav-text{transition:opacity .5s ease-in-out,transform .2s ease-in-out}.app-sidebar .nav a{display:flex;flex-direction:row;align-items:center;padding:8px 8px 8px 12px;gap:10px;height:40px;mix-blend-mode:normal;border-radius:8px;font-weight:500;font-size:14px;line-height:24px;color:rgba(255,255,255,.8);border-radius:8px;width:100%;white-space:nowrap}.app-sidebar .nav a.current{background:rgba(255,255,255,.25);color:#fff}.app-sidebar .nav a.current i{color:rgba(255,255,255,.8) !important}.app-sidebar .nav a:hover{color:rgba(255,255,255,.9);background:rgba(255,255,255,.15)}.app-sidebar .nav a:hover i{color:rgba(255,255,255,.6) !important}.app-sidebar .nav a i{color:rgba(255,255,255,.6);font-size:20px}.app-sidebar .nav a .nav-text{flex:1}.app-sidebar .nav a i.home-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.home-icon:before{font-family:inherit;content:"home"}.app-sidebar .nav a i.my-games-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-games-icon:before{font-family:inherit;content:"browse"}.app-sidebar .nav a i.my-videos-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.my-videos-icon:before{font-family:inherit;content:"live_tv"}.app-sidebar .nav a i.explore-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.explore-icon:before{font-family:inherit;content:"feature_search"}.app-sidebar .nav a i.upcoming-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.upcoming-icon:before{font-family:inherit;content:"double_arrow"}.app-sidebar .nav a i.maps-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}.app-sidebar .nav a i.maps-icon:before{font-family:inherit;content:"map"}section.title .background,section.title .header-background{--background-crop-amount: calc(-1 * 36px);position:absolute;left:-220px;right:var(--background-crop-amount);top:var(--background-crop-amount);background-size:100% auto;background-repeat:no-repeat;background-image:url(${m});background-position:top center;pointer-events:none}section.title .background:before,section.title .header-background:before{content:"";display:block;width:100%;height:100%;position:absolute;left:0;top:0;background:-webkit-linear-gradient(top, rgba(var(--theme--background--rgb), 0.4) 0, var(--theme--background) 300px)}@media(max-width: 1200px){section.title .background,section.title .header-background{background-size:140% auto}}@media(forced-colors: active){body:not(.override-contrast-mode) section.title .background,body:not(.override-contrast-mode) section.title .header-background{background-image:none !important;background-color:#000 !important}}section.title .background{z-index:0;height:300px}section.title .header-background{--fade-start: calc(var(--constant--appHeaderHeight) + var(--constant--trainerHeaderHeight) + 10px);--fade-height: 20px;z-index:1;height:calc(var(--fade-start) + var(--fade-height));mask:-webkit-linear-gradient(top, #fff 0px, #fff var(--fade-start), transparent 100%);clip-path:inset(0 calc(7px - var(--background-crop-amount)) 0 0)}section.title>.overflow-fade__wrapper{z-index:initial}section.title>.overflow-fade__wrapper:before{display:none !important}section.title .view-scrollable:before{display:none}section.title .title-layout{display:flex;gap:24px}section.title .title-layout title-sidebar{transition:width ease-in-out .15s}section.title .title-layout title-sidebar.collapsed{width:0 !important;margin:0}section.title .title-wrapper{display:flex;flex-direction:column;flex:1 1 auto;gap:20px;min-height:100%;min-width:0;position:relative}section.title title-sidebar{flex:0 0 auto}section.title .title-header{position:sticky;top:0;z-index:9999;width:100%}section.title .title-header favorite-button button{display:flex;appearance:none;border:none;padding:6px;border-radius:50%;background:rgba(255,255,255,.1);display:inline-flex;align-items:center;justify-content:center;margin-top:0;transition:background-color .15s}section.title .title-header favorite-button button,section.title .title-header favorite-button button *{cursor:pointer}section.title .title-header favorite-button button:hover{background:rgba(255,255,255,.25)}section.title .title-section{font-weight:800;font-size:35px;line-height:40px;font-weight:800;color:#fff;display:inline-flex;align-items:center;margin-bottom:33px;display:grid;grid-template-columns:auto minmax(0, auto) 1fr auto;gap:8px;margin-bottom:0;width:100%}section.title .title-section>*+*{margin-left:15px;line-height:0}section.title .title-section>*{margin-left:0}section.title .title-section .title-actions{display:flex;align-items:center;gap:8px}section.title .title-section .trainer-actions{display:flex;align-items:center;min-height:39px;gap:8px}section.title .title-section .trainer-actions save-cheats-toggle.au-enter-active{animation:trainer-actions-fade .15s}section.title .title-section .trainer-actions save-cheats-toggle.au-leave-active{animation:trainer-actions-fade .15s reverse forwards}@media(max-width: 1250px){section.title .title-section .game-image{width:0;min-width:0 !important;visibility:hidden}}section.title .title-section .game-image{min-width:40px;height:48px;background-size:cover;background-position:center center;background-repeat:no-repeat;border-radius:12px;position:relative;display:inline-block !important}section.title .title-section .game-image.is-fallback{width:0;min-width:0;visibility:hidden}section.title .title-section .game-image:after{content:"";position:absolute;border-radius:12px;top:0;left:0;right:0;bottom:0;border:.5px solid rgba(255,255,255,.5);mix-blend-mode:overlay;z-index:1}section.title .title-section .title{display:flex;align-items:center;font-size:24px;font-style:normal;font-weight:800;line-height:24px;max-width:250px;display:-webkit-box;-webkit-line-clamp:2;-webkit-box-orient:vertical;text-overflow:ellipsis;overflow:hidden;align-self:center;height:auto;vertical-align:middle;border-top:2px solid rgba(0,0,0,0);border-bottom:2px solid rgba(0,0,0,0)}section.title .title-section .title.condensed{font-size:14px;line-height:14px;font-weight:800;-webkit-line-clamp:3;word-break:break-word;overflow:hidden !important}section.title .title-game{display:block;width:100%;padding-bottom:var(--safe-area-padding, 90px)}section.title .title-game unavailable-game .title-actions,section.title .title-game game .title-actions{position:fixed;right:24px;top:calc(var(--constant--appHeaderHeight) + 30px);z-index:9999;display:flex;flex-direction:column;align-items:flex-end}section.title .title-support-assistant{position:fixed;right:24px;bottom:0;z-index:10000}@keyframes trainer-actions-fade{from{opacity:0}to{opacity:1}}.app-content>router-view .view-background.title .view-scrollable,.app-content>.app-view .view-background.title .view-scrollable{padding:calc(var(--constant--appHeaderHeight) + 8px) 24px 20px}.app-content>router-view .view-background.title .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement,.app-content>.app-view .view-background.title .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement{height:calc(var(--constant--appHeaderHeight) + 80px)}.app-content>router-view .view-background.title .view-scrollable::-webkit-scrollbar-button:single-button:vertical:increment,.app-content>.app-view .view-background.title .view-scrollable::-webkit-scrollbar-button:single-button:vertical:increment{height:0px}.app-content>router-view .view-background.title .view-scrollable::-webkit-scrollbar,.app-content>.app-view .view-background.title .view-scrollable::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent calc(var(--constant--appHeaderHeight) + 80px), rgba(255, 255, 255, 0.1) calc(var(--constant--appHeaderHeight) + 80px), rgba(255, 255, 255, 0.1) calc(100% - 0px), transparent calc(100% - 0px))}`,""]);const h=p},"cheats/titles":(e,t,i)=>{i.r(t),i.d(t,{Titles:()=>d});var a=i(15215),s=i("aurelia-framework"),o=i(18088),r=i(62914),n=i(43050),l=i(54995);let d=class{#r;#I;constructor(e,t){this.selectedGenre=null,this.searchTerms="",this.ALL_GAMES_CONFIG=n.Vr,this.getGenreIcon=e=>(0,o.y)(e),this.#r=e,this.#I=t}activate(e){e.search&&(this.searchTerms=e.search),e.search||this.#k("Explore")}bind(){this.mostPopular=this.#I.getFilteredFeed(n.dL,{maxItems:12,genres:this.selectedGenre?[this.selectedGenre.id]:void 0}),this.recentlyPlayed=this.#I.getFilteredFeed(n._4,{maxItems:15}),this.newReleases=this.#I.getFilteredFeed(n.dZ,{maxItems:12}),this.queue=this.#I.getFilteredFeed(n.Rp,{maxItems:9}),this.favorites=this.#I.getFilteredFeed(n.pK,{maxItems:1}),this.communityChoice=this.#I.getFilteredFeed(n.Vr,{tags:["choice"],maxItems:1}),this.free=this.#I.getFilteredFeed(n.Vr,{maxItems:9,tags:["free"]})}unbind(){this.mostPopular.dispose(),this.recentlyPlayed.dispose(),this.newReleases.dispose(),this.queue.dispose(),this.favorites.dispose(),this.communityChoice.dispose()}get genresToDisplay(){return Object.values(this.genres).sort(((e,t)=>e.name.localeCompare(t.name)))}genresChanged(){this.selectedGenre&&!this.genres[this.selectedGenre.id]&&(this.selectedGenre=null)}onSearchComplete(){this.#k("Search Results")}onSearchClear(){this.#k("Explore")}#k(e){this.#r?.screenView({name:"Games",class:"Games",params:{state:e}})}};(0,a.Cg)([s.observable,(0,a.Sn)("design:type",Object)],d.prototype,"genres",void 0),(0,a.Cg)([(0,s.computedFrom)("genres"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],d.prototype,"genresToDisplay",null),d=(0,a.Cg)([(0,l.m6)({selectors:{genres:(0,l.$t)((e=>e.catalog?.genres))}}),(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[r.j0,n.Y2])],d)},"cheats/titles.html":(e,t,i)=>{i.r(t),i.d(t,{default:()=>n});var a=i(14385),s=i.n(a),o=new URL(i(34626),i.b),r=new URL(i(19332),i.b);const n='<template> <require from="./titles.scss"></require> <require from="./resources/elements/grid-game-feed"></require> <require from="./resources/elements/horizontal-game-feed"></require> <require from="./resources/elements/featured-game-feed"></require> <require from="./resources/elements/titles-search"></require> <require from="./resources/elements/list-game-feed"></require> <require from="./resources/elements/game-feed-item"></require> <require from="./resources/value-converters/group-feed-items"></require> <require from="../shared/resources/elements/back-link"></require> <require from="../resources/elements/toast-anchor"></require> <require from="../cheats/resources/elements/routed-text-button"></require> <section class="titles view-background au-animate"> <div class="overflow-fade__wrapper overflow-fade__wrapper--vertical"> <div class="view-scrollable" overflow-fade="vertical" ref="scrollEl"> <div class="title-wrapper"> <header class="title"> <template if.bind="searching">${\'titles.search\' | i18n}</template> <template else>${\'titles.explore\' | i18n}</template> </header> </div> <titles-search search-results.bind="searchResults" search-terms.bind="searchTerms" layout.bind="searchResultsLayout" searching.bind="searching" search-session.bind="searchSession" scroll-el.bind="scrollEl" view-model.ref="titlesSearch" on-search-complete.call="onSearchComplete()" on-search-clear.call="onSearchClear()"></titles-search> <template if.bind="searching"> <section if.bind="searchResults.length" class="games-section game-feed" repeat.for="group of searchResults | groupFeedItems:[\'playable\', \'launch_without_mods\', \'installable\', \'unsupported_and_not_installed\']"> <div class="header-container"> <toast-anchor name.bind="group.group" scroll-el.bind="scrollEl" label-key="game_collection.${group.group}"></toast-anchor> <header> <h2>${`game_collection.${group.group}` | i18n}</h2> <span class="count">${group.items.length}</span> </header> </div> <div class="grid" if.bind="searchResultsLayout === \'thumbnail\'"> <game-feed-item repeat.for="item of group.items" item.bind="item" location="games" metadata-types.bind="[\'platform-icons\', \'status-badges\', \'genres\']" large-thumbnails.bind="true" previous-route="games" search-result.bind="true"></game-feed-item> </div> <div class="list" if.bind="searchResultsLayout === \'list\'"> <list-game-feed items.bind="group.items" location="games" metadata-types.bind="[\'platform-icons\', \'status-badges\', \'genres\']" previous-route="games" search-result.bind="true"></list-game-feed> </div> </section> <div if.bind="!searchResults.length && !searchSession.pendingSearchTerms" class="no-results-message"> <h4>${\'titles.no_results\' | i18n}</h4> <p>${\'titles.no_results_advice\' | i18n}</p> </div> </template> <template if.bind="!searching"> <nav class="pills top-links"> <a route-href="route.bind: \'collection\'; params.bind: {slug: \'my-games\', selectedFeed: \'favorites\'}"> <i class="icon favorite-icon"></i> <span>${\'game_collection.favorites\' | i18n}</span> </a> <a route-href="route.bind: \'collection\'; params.bind: {previousRoute: \'games\', slug: \'free-games\'}"> <i class="icon free-games-icon"></i> ${\'game_collection.free_games\' | i18n} </a> <a route-href="route.bind: \'collection\'; params.bind: {previousRoute: \'games\', slug: \'supported-games\'}"> <i class="icon all-games-icon"></i> ${\'game_collection.all_supported_games\' | i18n} </a> <a route-href="route.bind: \'collection\'; params.bind: {previousRoute: \'games\', slug: \'game-pass\'}"> <i class="icon xbox-icon"> <inline-svg src="'+s()(o)+"\"></inline-svg> </i> ${'game_collection.game_pass' | i18n} </a> </nav> <nav class=\"pills top-links\"> <a route-href=\"route.bind: 'collection'; params.bind: {previousRoute: 'games', slug: 'games-with-maps'}\"> <i class=\"icon maps-icon\"></i> <span>${'game_collection.games_with_maps' | i18n}</span> </a> <a route-href=\"route.bind: 'collection'; params.bind: {previousRoute: 'games', slug: 'games-with-teleport'}\"> <i class=\"icon teleport-icon\"> <inline-svg src=\""+s()(r)+'"></inline-svg> </i> ${\'game_collection.teleport\' | i18n} </a> <a route-href="route.bind: \'collection\'; params.bind: {previousRoute: \'games\', slug: \'games-with-precision-mods\'}"> <i class="icon precision-mods-icon"></i> <span>${\'game_collection.precision_mods\' | i18n}</span> </a> <a route-href="route.bind: \'collection\'; params.bind: {previousRoute: \'games\', slug: \'games-with-overlay-support\'}"> <i class="icon overlay-icon"></i> <span>${\'game_collection.overlay\' | i18n}</span> </a> </nav> <section class="games-section game-feed" show.bind="featuredGamesCount"> <header> <h2>${\'titles.featured_games\' | i18n}</h2> </header> <article> <featured-game-feed location="games:featured" previous-route="games" item-count.bind="featuredGamesCount" metadata-types.bind="[\'status-badges\']"></featured-game-feed> </article> </section> <section class="games-section game-feed" if.bind="newReleases.items.length"> <header> <h2>${\'titles.new_and_updated_games\' | i18n}</h2> <routed-text-button route="collection" params.bind="{previousRoute: \'games\', slug: \'new-and-updated\'}">${\'titles.view_all\' | i18n}</routed-text-button> </header> <article> <horizontal-game-feed items.bind="newReleases.items" location="games:new-releases" previous-route="games" metadata-types.bind="[\'status-badges\']" metadata-position="on-card"></horizontal-game-feed> </article> </section> <section class="games-section game-feed" if.bind="mostPopular.items.length"> <header> <h2>${\'dashboard.most_popular\' | i18n}</h2> <routed-text-button route="collection" params.bind="{previousRoute: \'games\', slug: \'most-popular\'}">${\'dashboard.see_all\' | i18n}</routed-text-button> </header> <article> <horizontal-game-feed items.bind="mostPopular.items" location="games:most-popular" previous-route="games" metadata-types.bind="[\'status-badges\']" metadata-position="on-card"></horizontal-game-feed> </article> </section> <section class="games-section game-feed"> <header> <h2>${\'titles.browse_by_genre\' | i18n}</h2> </header> <nav class="pills"> <a repeat.for="genre of genresToDisplay" route-href="route.bind: \'collection\'; params.bind: {previousRoute: \'games\', slug: \'games-by-genre\', genre: genre}"><span class="emoji">${getGenreIcon(genre)}</span>${`genres.${genre.slug.replaceAll(\'-\', \'_\')}` | i18n}</a> </nav> </section> <section class="games-section game-feed" if.bind="newReleases.items.length"> <header> <h2>${\'titles.trending_free_games_to_install\' | i18n}</h2> <routed-text-button route="collection" params.bind="{previousRoute: \'games\', slug: \'free-games\'}">${\'titles.view_all\' | i18n}</routed-text-button> </header> <article> <horizontal-game-feed items.bind="free.items" location="games:free-games" previous-route="games" metadata-types.bind="[\'status-badges\']" metadata-position="on-card"></horizontal-game-feed> </article> </section> <section class="games-section game-feed" if.bind="queue.items.length"> <header> <h2>${\'titles.upcoming_games_and_updates\' | i18n}</h2> <routed-text-button route="queue">${\'titles.see_all\' | i18n}</routed-text-button> </header> <article> <horizontal-game-feed items.bind="queue.items" location="games:queue" previous-route="games" metadata-types.bind="[\'status-badges\']" metadata-position="on-card"></horizontal-game-feed> </article> </section> </template> </div> </div> </section> </template> '},"cheats/titles.scss":(e,t,i)=>{i.r(t),i.d(t,{default:()=>h});var a=i(31601),s=i.n(a),o=i(76314),r=i.n(o),n=i(4417),l=i.n(n),d=new URL(i(25175),i.b),c=new URL(i(83959),i.b),p=r()(s()),g=l()(d),m=l()(c);p.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Noto Color Emoji";font-weight:400;font-style:normal;src:url(${g}) format("woff2")}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,section.titles .pills a .icon.favorite-icon,section.titles .pills a .icon.free-games-icon,section.titles .pills a .icon.all-games-icon,section.titles .pills a .icon.maps-icon,section.titles .pills a .icon.precision-mods-icon,section.titles .pills a .icon.overlay-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}section.titles .header-container{display:flex;flex-direction:column}section.titles .view-scrollable{padding-top:24px;gap:24px}section.titles .title-wrapper{display:flex;flex-direction:column;gap:4px;position:relative;padding-top:14px}section.titles .title-wrapper .title{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:32px;line-height:36px;letter-spacing:-1.5px}.theme-default section.titles .title-wrapper .title{color:#fff}.theme-purple-pro section.titles .title-wrapper .title{color:#fff}.theme-green-pro section.titles .title-wrapper .title{color:#fff}.theme-orange-pro section.titles .title-wrapper .title{color:#fff}.theme-pro section.titles .title-wrapper .title{color:#fff}section.titles .pills{display:flex;flex-direction:row;align-items:center;flex-wrap:wrap;gap:12px}section.titles .pills.top-links a{flex:1 0 0}section.titles .pills.genres a{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;background:rgba(255,255,255,.04);height:52px;border-radius:12px;color:rgba(255,255,255,.8)}section.titles .pills.genres a,section.titles .pills.genres a *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) section.titles .pills.genres a{border:1px solid #fff}}section.titles .pills.genres a>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}section.titles .pills.genres a>*:first-child{padding-left:0}section.titles .pills.genres a>*:last-child{padding-right:0}section.titles .pills.genres a svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) section.titles .pills.genres a svg *{fill:CanvasText}}section.titles .pills.genres a svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) section.titles .pills.genres a svg{opacity:1}}section.titles .pills.genres a img{height:50%}section.titles .pills.genres a:disabled{opacity:.3}section.titles .pills.genres a:disabled,section.titles .pills.genres a:disabled *{cursor:default;pointer-events:none}@media(hover: hover){section.titles .pills.genres a:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}section.titles .pills.genres a:not(:disabled):hover svg{opacity:1}}section.titles .pills.genres a:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){section.titles .pills.genres a:not(:disabled):hover{background:rgba(255,255,255,.3)}}section.titles .pills.genres a:not(:disabled):hover{background:rgba(255,255,255,.1)}section.titles .pills.genres a span{font-family:"Noto Color Emoji",sans-serif;margin-right:10px}section.titles .pills a{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:14px;line-height:21px;height:32px;box-shadow:none;line-height:24px;letter-spacing:-0.5px;font-weight:700;color:#fff;border-radius:56px;padding:4px 16px;background:rgba(255,255,255,.1);font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;display:flex;align-items:center;justify-content:center;padding:16px 20px;gap:10px;height:52px;background:rgba(255,255,255,.04);border-radius:12px;color:rgba(255,255,255,.8);transition:color .15s,border-color .15s,background-color .15s}section.titles .pills a,section.titles .pills a *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) section.titles .pills a{border:1px solid #fff}}section.titles .pills a>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}section.titles .pills a>*:first-child{padding-left:0}section.titles .pills a>*:last-child{padding-right:0}section.titles .pills a svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) section.titles .pills a svg *{fill:CanvasText}}section.titles .pills a svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) section.titles .pills a svg{opacity:1}}section.titles .pills a img{height:50%}section.titles .pills a:disabled{opacity:.3}section.titles .pills a:disabled,section.titles .pills a:disabled *{cursor:default;pointer-events:none}@media(hover: hover){section.titles .pills a:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}section.titles .pills a:not(:disabled):hover svg{opacity:1}}section.titles .pills a:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){section.titles .pills a:not(:disabled):hover{background:rgba(255,255,255,.3)}}section.titles .pills a:not(:disabled):hover{background:rgba(255,255,255,.1)}section.titles .pills a span.emoji{font-family:"Noto Color Emoji",sans-serif}section.titles .pills a>*{padding:0}section.titles .pills a .icon{font-variation-settings:"FILL" 1,"wght" 400 !important;display:flex;align-items:center;font-size:20px}.theme-default section.titles .pills a .icon{color:rgba(255,255,255,.6)}.theme-purple-pro section.titles .pills a .icon{color:rgba(255,255,255,.6)}.theme-green-pro section.titles .pills a .icon{color:rgba(255,255,255,.6)}.theme-orange-pro section.titles .pills a .icon{color:rgba(255,255,255,.6)}.theme-pro section.titles .pills a .icon{color:rgba(255,255,255,.6)}section.titles .pills a .icon.favorite-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}section.titles .pills a .icon.favorite-icon:before{font-family:inherit;content:"kid_star"}section.titles .pills a .icon.free-games-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}section.titles .pills a .icon.free-games-icon:before{font-family:inherit;content:"sell"}section.titles .pills a .icon.all-games-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}section.titles .pills a .icon.all-games-icon:before{font-family:inherit;content:"window"}section.titles .pills a .icon.xbox-icon svg{width:24px;height:24px;opacity:.6;fill:#fff}section.titles .pills a .icon.maps-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}section.titles .pills a .icon.maps-icon:before{font-family:inherit;content:"map"}section.titles .pills a .icon.teleport-icon svg{width:24px;height:24px;opacity:.6;fill:#fff}section.titles .pills a .icon.precision-mods-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}section.titles .pills a .icon.precision-mods-icon:before{font-family:inherit;content:"page_info"}section.titles .pills a .icon.overlay-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px}section.titles .pills a .icon.overlay-icon:before{font-family:inherit;content:"layers"}section.titles .pills+.pills{margin-top:-8px}section.titles .grid{display:grid;grid-template-columns:repeat(auto-fill, minmax(198px, 1fr));gap:16px}section.titles .game-feed{display:flex;flex-direction:column;flex:0 0 auto;gap:16px}.theme-default section.titles .game-feed{--overflow-fade--background: #0d0f12}.theme-purple-pro section.titles .game-feed{--overflow-fade--background: #0e0e15}.theme-green-pro section.titles .game-feed{--overflow-fade--background: #0b1114}.theme-orange-pro section.titles .game-feed{--overflow-fade--background: #0f0f12}.theme-pro section.titles .game-feed{--overflow-fade--background: #0a0a0a}section.titles .game-feed header{display:flex;align-items:baseline;gap:8px}section.titles .game-feed header h2{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px;margin:0}.theme-default section.titles .game-feed header h2{color:#fff}.theme-purple-pro section.titles .game-feed header h2{color:#fff}.theme-green-pro section.titles .game-feed header h2{color:#fff}.theme-orange-pro section.titles .game-feed header h2{color:#fff}.theme-pro section.titles .game-feed header h2{color:#fff}section.titles .game-feed header .count{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px}.theme-default section.titles .game-feed header .count{color:rgba(255,255,255,.6)}.theme-purple-pro section.titles .game-feed header .count{color:rgba(255,255,255,.6)}.theme-green-pro section.titles .game-feed header .count{color:rgba(255,255,255,.6)}.theme-orange-pro section.titles .game-feed header .count{color:rgba(255,255,255,.6)}.theme-pro section.titles .game-feed header .count{color:rgba(255,255,255,.6)}section.titles .game-feed:hover routed-text-button{opacity:1}section.titles .game-feed routed-text-button{opacity:0;transition:opacity .2s}section.titles .no-results-message h4{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:24px;letter-spacing:-0.75px}.theme-default section.titles .no-results-message h4{color:#fff}.theme-purple-pro section.titles .no-results-message h4{color:#fff}.theme-green-pro section.titles .no-results-message h4{color:#fff}.theme-orange-pro section.titles .no-results-message h4{color:#fff}.theme-pro section.titles .no-results-message h4{color:#fff}section.titles .no-results-message p{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default section.titles .no-results-message p{color:rgba(255,255,255,.6)}.theme-purple-pro section.titles .no-results-message p{color:rgba(255,255,255,.6)}.theme-green-pro section.titles .no-results-message p{color:rgba(255,255,255,.6)}.theme-orange-pro section.titles .no-results-message p{color:rgba(255,255,255,.6)}.theme-pro section.titles .no-results-message p{color:rgba(255,255,255,.6)}`,""]);const h=p}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6377],{"root/loading":(e,t,o)=>{o.r(t),o.d(t,{Loading:()=>$});var a=o(15215),r=o("aurelia-event-aggregator"),i=o("aurelia-framework"),n=o(96610),l=o(20770),d=o(89045),s=o(68663),g=o(62914),c=o(49765),h=o(71461),u=o(43050),p=o(10704),b=o(29865),m=o(16953),f=o(60692),x=o(19072),v=o(83974),y=o(3972),w=o("services/bugsnag/index"),k=o(60321),R=o(57503),S=o(68539),_=o(44759),C=o(54995),I=o(49442),L=o(48881),E=o(86977);const A=(0,n.getLogger)("load");let $=class{#e;#t;#o;#a;#r;#i;#n;#l;#d;#s;#g;#c;#h;#u;#p;#b;#m;#f;#x;#v;#y;#w;constructor(e,t,o,a,r,i,n,l,d,s,g,c,h,u,p){this.#m=null,this.#f=null,this.consentGiven=!1,this.authMode=null,this.#x=null,this.#v=null,this.#y=!1,this.#w=[this.#k,this.#R,this.#S,this.#_,this.#C,this.#I,this.#L,this.#E,this.#A,this.#$,this.#T,this.#U,this.#z,this.#A,this.#q,this.#M,this.#D],this.#N=e=>{const t=e.target;if("A"===t.tagName&&t.href){if(t.href.startsWith("website://")){const e=t.href.replace("website://",m.A.websiteUrl+"/");this.#e.launchExternal(e).catch(I.Y)}e.preventDefault()}},this.#e=e,this.#t=t,this.#o=o,this.#a=a,this.#r=r,this.#i=i,this.#n=n,this.#l=l,this.#d=d,this.#s=s,this.#g=g,this.#c=c,this.#h=h,this.#u=u,this.#p=p,this.#O(),n.setDeauthorizedHandler((()=>this.#t.dispatch(L.k$))),n.setTokenRefreshedHandler((e=>t.dispatch(L.xQ,e)))}get isLoading(){return!this.error&&!this.confirmConsent&&!this.authMode}#F(e,t){this.message=e,this.actions=t}#O(){this.#F("loading.loading_message")}#P(e,t){this.error={message:e,actions:t},this.#e.show()}#j(e){this.#P("maintenance"===this.#i.status?"loading.maintenance_message":"loading.offline_message",[{message:"loading.retry",handle:()=>{this.#G(),e()}}])}#G(){this.error=null}attached(){this.loadingContentEl.addEventListener("click",this.#N),this.loadingContentEl.addEventListener("auxclick",this.#N),this.#e.show();let e=0;const t=()=>{this.#O(),this.#G(),this.#w[e++].bind(this)(t)};t()}detached(){this.loadingContentEl.removeEventListener("click",this.#N),this.loadingContentEl.removeEventListener("auxclick",this.#N)}#N;async#k(e){if(this.state.installation.id||this.state.installation.token)return e();const t=(0,i.parseQueryString)((this.#e.info.launchUri||"").split("?",2)[1]||"");t&&"string"==typeof t._inst&&(A.debug("Persisting installation token..."),await this.#t.dispatch(L.f6,t._inst),this.#v=t._inst),e()}#_(e){this.#l.activate(),this.#d.activate(),e()}async#C(e){if(this.state.installation.id)return e();A.debug("Acquiring new installation ID...");try{const e=await this.#o.registerInstallation(this.state.installation.token||null);await this.#t.dispatch(L.Ih,e),this.#m=e.gdpr.consentRequired&&!e.gdpr.consentGiven}catch(t){return A.debug("Failed to register installation.",t),void this.#j((()=>this.#C(e)))}e()}async#I(e){if(this.state.flags.signOutOnStartup){if(this.state.token){A.debug("Revoking previous access token...");try{await this.#r.revokeAccessToken(this.state.token.accessToken)}catch(e){A.error("Failed to revoke access token.",e)}}await this.#t.dispatch(L.k$)}e()}async#L(e){this.state.token&&(A.debug("Access token is available."),this.#n.setAccessTokenResponse(this.state.token)),e()}async#H(){A.debug(`Refreshing account (${this.state.token?.userId}) details...`),this.#y||(this.#t.registerMiddleware(((e,t,o,a)=>{a?.name===L.Ui&&this.#W(e.account)}),l._s.After),this.#y=!0);try{await this.#t.dispatch(L.Ui,await this.#o.getUserAccount())}catch{this.#W(this.state.account)}}#W(e){const t=e?.activeExperiments;t&&this.#p.setAllExperiments(t)}async#E(e){if(!this.state.token)return e();const t=this.state.account;if(await this.#H(),!this.state.token)return A.debug("Server did not accept access token. Auth state cleared."),e();this.state.account?(await this.#X(t),e()):(A.debug("Prior account not cached. A refresh is required."),this.#j((()=>this.#E(e))))}async#X(e){const t=!!e?.subscription,o=!!t&&!!e.subscription.gift;await Promise.all([this.#t.dispatch(L.NX,"proOnStartup",t),this.#t.dispatch(L.NX,"giftOnStartup",o)])}async#A(e){if(this.state.account)this.#f=(this.state.account.gdpr?.consentRequired??!1)&&!this.state.account.gdpr?.consentGiven;else if(this.consentGiven)this.#f=!1;else if(null!==this.#m)this.#f=this.#m,this.#m=null;else try{this.#f=(await this.#o.getConsentRequirements()).consentRequired}catch{return A.debug("Failed to fetch GDPR consent requirements."),void this.#j((()=>this.#A(e)))}this.#f?(this.#b=e,this.confirmConsent=!0):e()}async giveConsent(){if(this.confirmConsent=!1,this.consentGiven=!0,this.state.account)try{const e=await this.#o.giveConsent();await this.#t.dispatch(L.Ui,e)}catch{this.#P("loading.consent_error_message",[{message:"loading.retry",handle:()=>{this.#G(),this.giveConsent()}}])}this.#g.event("gdpr_consent_give",{location:"loading"},g.Io),this.#b()}async#T(e){const t=await new Promise((e=>{setTimeout((()=>e(!1)),5e3),this.#g.activate().then((()=>e(!0))).catch((()=>e(!1)))}));this.state.installation.acquisition&&(this.#g.event("firebase_campaign",this.state.installation.acquisition,g.Io),t&&setTimeout((()=>this.#t.dispatch(L.q9)),1e4)),this.#v&&this.#g.adConversion("Cvs5CM6vvO4YEIGptsMD"),this.#e.isInTraySinceStartup||(this.#g.event("app_start",{osArch:this.#e.info.osArch,osVersion:this.#e.info.osVersion,updaterAvailable:"not-available"!==this.#e.updateState,firstRun:this.state.flags.firstRun??!1,launchUri:this.#e.info.launchUri??"",systemRegion:this.#e.info.region??"",systemLocale:this.#e.info.locale,installerToken:this.#v??""},g.Io),this.#Y().catch(I.Y)),e()}async#Y(){const e=["NVIDIA","Advanced Micro Devices, Inc.","Intel Corporation"],t=t=>{const o=e.indexOf(t);return-1===o?e.length:o},o=await this.#a.getGpuDevices(),a=o.filter((e=>"OK"===e.status)).sort(((e,o)=>t(e.adapterCompatibility)-t(o.adapterCompatibility)));this.#g.event("gpu_device_info",{device:a[0]??null,devices:o},g.Io)}async#U(e){return this.state.token?this.#x=null:(this.authMode=this.state.flags.firstRun?"new_user":"existing_user",this.#x=await new Promise((e=>this.authResolve=e)),this.#n.setAccessTokenResponse(this.state.token),this.authMode=null),this.#n.setDeauthorizedHandler((async()=>{A.debug("Server did not accept access token. Resetting auth state and app..."),await this.#t.dispatch(L.k$),await this.#e.reload()})),e()}async#z(e){if(this.state.account)return e();await this.#H(),this.state.account?(await this.#X(this.state.account),this.#x&&(this.#g.event("authenticate",this.#x,g.Io),"register"===this.#x.method&&this.#x.emailCollected&&this.#g.event("email_collect",{location:"authenticate_view",firstEmail:!0},g.Io),"register"===this.#x.method&&this.#g.adConversion("DMxRCNGvvO4YEIGptsMD")),e()):(A.debug("Failed to fetch account."),this.#j((()=>this.#z(e))))}async#q(e){await this.#e.setInstallationInfo(this.state.installation.id??"",this.state.account?.uuid??""),e()}async#R(e){if("win32"!==this.#e.info.osPlatform)return e();A.debug("Checking dotnet version...");const t=this.#e.getWindowsFolder();if(t&&await(0,E.w)(t))return e();A.debug("Required dotnet version not installed."),this.#P("loading.dotnet_error_message",[{message:"loading.download",handle:()=>this.#e.launchExternal("https://wemod.gg/dotnet")},{message:"loading.retry",handle:()=>{this.#G(),setTimeout((()=>this.#R(e)),2e3)}}])}async#S(e){if("win32"!==this.#e.info.osPlatform)return e();A.debug("Initializing support process...");try{await this.#a.initialize()}catch(t){return t instanceof v.M?t.reported||(0,w.report)(t):A.error(t),void this.#P("loading.support_initialization_error_message",[{message:"loading.retry",handle:()=>{this.#G(),setTimeout((()=>this.#S(e)),1e3)}},{message:"loading.reinstall",handle:()=>this.#e.launchExternal("https://www.wemod.com")},{message:"loading.more_info",handle:()=>this.#e.launchExternal("https://wemod.gg/av")}])}e()}async#$(e){await this.#t.dispatch(L.Vy);try{const e=await this.#o.getCatalog(this.state.catalogCacheKey);e.body&&await this.#t.dispatch(L.If,e.body,e.cacheKey)}catch{return void(this.state.catalog?(A.debug("Using cached catalog for offline mode."),e()):(A.debug("Failed to fetch catalog. No cached copy exists."),this.#j((()=>this.#$(e)))))}e()}async#M(e){await this.#h.initialize().catch(I.Y),await this.#u.initialize().catch(I.Y),await this.#c.initialize().catch(I.Y),e()}async#D(){const e=this.state.flags.firstRun,t=this.state.account,o=e&&t&&(0,d.A)(Date.now(),new Date(t.joinedAt))<7;if(("register"===this.#x?.method||o)&&await this.#p.trigger(f.n.Onboarding).catch(I.Y))return A.debug("Entering onboarding mode..."),void this.#s.publish("onboarding-loaded");A.debug("Entering app mode..."),this.#s.publish("app-loaded")}};(0,a.Cg)([(0,i.computedFrom)("error","confirmConsent","authMode"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],$.prototype,"isLoading",null),$=(0,a.Cg)([(0,C.m6)(),(0,i.autoinject)(),(0,a.Sn)("design:paramtypes",[x.s,l.il,s.x,y.Mz,k.Q,_.WA,R.Z,h.i,c.I,r.EventAggregator,g.j0,u.Y2,p.r,b.Z,S.z])],$)},"root/loading.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>l});var a=o(14385),r=o.n(a),i=new URL(o(18285),o.b),n=new URL(o(2877),o.b);const l='<template> <require from="./loading.scss"></require> <require from="../app/toasts"></require> <require from="../app/resources/elements/app-header"></require> <require from="./auth"></require> <div class="loading-layout theme-default ${isLoading ? \'is-loading\' : \'\'}"> <div id="dialogs"></div> <toasts></toasts> <app-header></app-header> <div class="loading-wrapper"> <div class="loading-content" ref="loadingContentEl"> <header class="loading-header" if.bind="error || !authMode"> <img src="'+r()(i)+'" class="logo ${!confirmConsent ? \'animate\' : \'\'}"> <h1 class="tagline">${\'loading.tagline\' | i18n}</h1> </header> <template if.bind="error"> <div class="error-icon"><inline-svg src="'+r()(n)+'"></inline-svg></div> <div class="message error" innerhtml.bind="error.message | i18n | markdown"></div> <div class="actions" if.bind="error.actions"> <a repeat.for="action of error.actions" href="#" click.delegate="action.handle()">${action.message | i18n}</a> </div> </template> <template if.bind="!error && confirmConsent"> <div class="message consent" innerhtml.bind="\'loading.review_terms\' | i18n | markdown"></div> <div class="actions"> <a click.delegate="giveConsent()" href="#">${\'loading.accept_and_continue\' | i18n}</a> </div> </template> <template if.bind="!error && authMode"> <auth mode.one-way="authMode" gdpr-consent-given.bind="consentGiven" on-complete.call="authResolve(result)"></auth> </template> <template if.bind="isLoading"> <div class="progress-ring"> <svg> <circle cx="15" cy="15" r="10" fill="none" stroke-width="1" stroke-miterlimit="10"></circle> </svg> </div> <div class="message loading">${message | i18n}</div> <div class="actions" if.bind="actions"> <a repeat.for="action of actions" click.delegate="action.handle()" href="#">${action.message | i18n}</a> </div> </template> </div> </div> </div> </template> '},"root/loading.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>h});var a=o(31601),r=o.n(a),i=o(76314),n=o.n(i),l=o(4417),d=o.n(l),s=new URL(o(94204),o.b),g=n()(r()),c=d()(s);g.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.loading-layout{background:var(--theme--background);display:flex;flex-direction:column;height:100vh}.loading-layout app-header{-webkit-app-region:drag}.loading-layout app-header .logo{display:none}.loading-layout .loading-wrapper{width:100vw;margin-bottom:var(--constant--appHeaderHeight);flex:1 1 auto;overflow:hidden;display:flex;align-items:center;justify-content:center}.loading-layout .loading-wrapper .loading-content{text-align:center}.loading-layout .loading-wrapper .loading-header .logo{width:121px;height:75px}.loading-layout .loading-wrapper .loading-header .logo.animate{animation:beat .5s infinite alternate}.loading-layout .loading-wrapper .loading-header .tagline{font-style:normal;font-weight:bold;font-size:35px;line-height:40px;color:#fff;margin:30px 0 0 0;text-align:center;text-shadow:0px 0px 48px rgba(255,255,255,.5);letter-spacing:-2px}.loading-layout .loading-wrapper .progress-ring{display:inline-block;margin-top:25px;opacity:.4}.loading-layout .loading-wrapper .progress-ring svg{animation:progress-rotate 2s linear infinite;width:30px;height:30px}.loading-layout .loading-wrapper .progress-ring svg circle{stroke:#fff;stroke-dasharray:1,200;stroke-dashoffset:0;animation:progress-stroke 1.5s ease-in-out infinite}.loading-layout .loading-wrapper .error-icon{width:20px;height:20px;margin:30px auto 5px auto;background:rgba(var(--color--alert--rgb), 0.3);border-radius:50%;display:flex;align-items:center;justify-content:center}.loading-layout .loading-wrapper .error-icon svg{width:10px}.loading-layout .loading-wrapper .error-icon svg *{fill:var(--color--alert)}.loading-layout .loading-wrapper .message{font-style:normal;font-weight:500;font-size:13px;line-height:20px;color:rgba(255,255,255,.4);max-width:400px;margin:0 auto}.loading-layout .loading-wrapper .message a{color:#fff}.loading-layout .loading-wrapper .message a:hover{text-decoration:underline}.loading-layout .loading-wrapper .message.error{color:var(--color--alert)}.loading-layout .loading-wrapper .message.consent{margin:25px 0 15px}.loading-layout .actions{margin-top:10px}.loading-layout .actions a,.loading-layout .actions button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}.loading-layout .actions a,.loading-layout .actions a *,.loading-layout .actions button,.loading-layout .actions button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .loading-layout .actions a,body:not(.override-contrast-mode) .loading-layout .actions button{border:1px solid #fff}}.loading-layout .actions a>*,.loading-layout .actions button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.loading-layout .actions a>*:first-child,.loading-layout .actions button>*:first-child{padding-left:0}.loading-layout .actions a>*:last-child,.loading-layout .actions button>*:last-child{padding-right:0}.loading-layout .actions a svg,.loading-layout .actions button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .loading-layout .actions a svg *,body:not(.override-contrast-mode) .loading-layout .actions button svg *{fill:CanvasText}}.loading-layout .actions a svg *,.loading-layout .actions button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .loading-layout .actions a svg,body:not(.override-contrast-mode) .loading-layout .actions button svg{opacity:1}}.loading-layout .actions a img,.loading-layout .actions button img{height:50%}.loading-layout .actions a:disabled,.loading-layout .actions button:disabled{opacity:.3}.loading-layout .actions a:disabled,.loading-layout .actions a:disabled *,.loading-layout .actions button:disabled,.loading-layout .actions button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.loading-layout .actions a:not(:disabled):hover,.loading-layout .actions button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.loading-layout .actions a:not(:disabled):hover svg,.loading-layout .actions button:not(:disabled):hover svg{opacity:1}}.loading-layout .actions a:not(:disabled):active,.loading-layout .actions button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.loading-layout .actions a+a,.loading-layout .actions a button,.loading-layout .actions button+a,.loading-layout .actions button button{margin-left:10px}.loading-layout a,.loading-layout button,.loading-layout input{-webkit-app-region:no-drag}.loading-layout toasts .messages .message-wrapper{-webkit-app-region:no-drag}.loading-layout.is-loading{background:var(--theme--background) url(${c}) center/cover no-repeat}.loading-layout.is-loading .loading-content{margin-bottom:-25px}.ux-dialog-open~.loading-wrapper{-webkit-app-region:no-drag}@keyframes beat{to{transform:scale(1.03)}}@keyframes progress-rotate{to{transform:rotate(360deg)}}@keyframes progress-stroke{0%{stroke-dasharray:1,200;stroke-dashoffset:0}50%{stroke-dasharray:89,200;stroke-dashoffset:-35}100%{stroke-dasharray:89,200;stroke-dashoffset:-124}}`,""]);const h=g},"root/root":(e,t,o)=>{o.r(t),o.d(t,{Root:()=>g});var a=o(15215),r=o("aurelia-dialog"),i=o("aurelia-event-aggregator"),n=o("aurelia-framework"),l=o(51436),d=o(54995);o(16566);const s=[{route:"",name:"loading",moduleId:"root/loading"},{route:"onboarding",name:"onboarding",moduleId:"onboarding/onboarding"},{route:"app",name:"app",moduleId:"app/app"}];let g=class{#s;#V;#K;constructor(e){this.#s=e}configureRouter(e,t){this.#Q(),e.title="WeMod",e.options.root="/",e.addAuthorizeStep(l.$),e.addPipelineStep("postcomplete",u),e.addPipelineStep("preActivate",p),e.addPipelineStep("postcomplete",b),e.map(s),e.fallbackRoute("/app/dashboard"),this.#K=t}activate(){this.#s.subscribeOnce("app-loaded",(()=>{this.#Z()})),this.#s.subscribeOnce("onboarding-loaded",(()=>{this.#B()})),this.#s.subscribeOnce("onboarding-complete",(e=>{e?.titleId?this.#K.navigate(`/app/titles/${e.titleId}`,{replace:!0}):this.#Z()}))}attached(){document.addEventListener("dragover",c),document.addEventListener("drop",h),this.#J()}detached(){document.removeEventListener("dragover",c),document.removeEventListener("drop",h)}#B(){this.#K.navigate("/onboarding",{replace:!0})}#Z(){this.#V&&this.#V.startsWith("/app/")?this.#K.navigate(this.#V,{replace:!0}):this.#K.navigate("/app/dashboard",{replace:!0})}#Q(){document.location.hash&&(this.#V=document.location.hash.substring(1),window.history.replaceState(null,"",window.location.href.split("#",2)[0]))}useWindowsContrastModeChanged(){this.#J()}#J(){document.body.classList.toggle("override-contrast-mode",!this.useWindowsContrastMode)}};function c(e){e.dataTransfer&&(e.dataTransfer.dropEffect="none"),e.preventDefault()}function h(e){e.preventDefault()}g=(0,a.Cg)([(0,n.autoinject)(),(0,d.m6)({selectors:{useWindowsContrastMode:(0,d.$t)((e=>e.settings?.useWindowsContrastMode))}}),(0,a.Sn)("design:paramtypes",[i.EventAggregator])],g);class u{run(e,t){const o=document.querySelector("router-view");return o&&(o.scrollTop=0),t()}}let p=class{#ee;#te;#oe;constructor(e,t){this.#oe=!0,this.#ee=e,this.#te=t}async run(e,t){this.#ee.publish("router:pipeline:preActivate"),this.#oe||await this.#te.closeAll(),this.#oe=!1;const o=document.querySelector(".view-background");return o&&o.classList.add("au-leave-active"),document.body.classList.toggle("disable-view-transition",!o),t()}};p=(0,a.Cg)([(0,n.autoinject)(),(0,a.Sn)("design:paramtypes",[i.EventAggregator,r.DialogService])],p);class b{#ae;run(e,t){const o=document.querySelector(".view-background");return o&&(clearTimeout(this.#ae),this.#ae=setTimeout((()=>{o.classList.remove("au-leave-active")}),1e3)),t()}}},"root/root.html":(e,t,o)=>{o.r(t),o.d(t,{default:()=>a});const a='<template> <require from="./root.scss"></require> <router-view></router-view> </template> '},"root/root.scss":(e,t,o)=>{o.r(t),o.d(t,{default:()=>ce});var a=o(31601),r=o.n(a),i=o(76314),n=o.n(i),l=o(98896),d=o(4417),s=o.n(d),g=new URL(o(2012),o.b),c=new URL(o(64943),o.b),h=new URL(o(17579),o.b),u=new URL(o(82051),o.b),p=new URL(o(89392),o.b),b=new URL(o(40730),o.b),m=new URL(o(80240),o.b),f=new URL(o(17820),o.b),x=new URL(o(88442),o.b),v=new URL(o(83959),o.b),y=new URL(o(44013),o.b),w=new URL(o(3427),o.b),k=new URL(o(45993),o.b),R=new URL(o(68955),o.b),S=new URL(o(61543),o.b),_=new URL(o(30550),o.b),C=new URL(o(47281),o.b),I=new URL(o(44580),o.b),L=new URL(o(81556),o.b),E=new URL(o(51234),o.b),A=new URL(o(21379),o.b),$=new URL(o(50437),o.b),T=new URL(o(74188),o.b),U=new URL(o(30410),o.b),z=new URL(o(85311),o.b),q=new URL(o(65244),o.b),M=new URL(o(24809),o.b),D=n()(r());D.i(l.A);var N=s()(g),O=s()(c),F=s()(h),P=s()(u),j=s()(p),G=s()(b),H=s()(m),W=s()(f),X=s()(x),Y=s()(v),V=s()(y),K=s()(w),Q=s()(k),Z=s()(R),B=s()(S),J=s()(_),ee=s()(C),te=s()(I),oe=s()(L),ae=s()(E),re=s()(A),ie=s()($),ne=s()(T),le=s()(U),de=s()(z),se=s()(q),ge=s()(M);D.push([e.id,`@font-face{font-family:"Inter";font-style:normal;font-weight:100;font-display:block;src:url(${N}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:200;font-display:block;src:url(${O}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:300;font-display:block;src:url(${F}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:400;font-display:block;src:url(${P}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:500;font-display:block;src:url(${j}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:600;font-display:block;src:url(${G}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:700;font-display:block;src:url(${H}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:800;font-display:block;src:url(${W}) format("woff2")}@font-face{font-family:"Inter";font-style:normal;font-weight:900;font-display:block;src:url(${X}) format("woff2")}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${Y}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}body{--constant--trainerHeaderHeight: 80px;--constant--appHeaderHeight: 36px}ux-dialog-container>div{margin-top:36px}ux-dialog.scrollable{max-height:calc(100vh - 98px) !important}ux-dialog.fullscreen-dialog close-button{top:36px !important}ux-dialog.fullscreen-dialog .app-content{background-color:var(--theme--background)}ux-dialog.fullscreen-dialog .app-content>router-view .view-background>.overflow-fade__wrapper:before,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background>.overflow-fade__wrapper:before{top:36px}ux-dialog.fullscreen-dialog .app-content>router-view .view-background .view-scrollable,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background .view-scrollable{padding-top:40px}ux-dialog.fullscreen-dialog .app-content>router-view .view-background .view-scrollable::-webkit-scrollbar,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar{background:-webkit-linear-gradient(transparent 0px, transparent 36px, rgba(255, 255, 255, 0.1) 36px)}ux-dialog.fullscreen-dialog .app-content>router-view .view-background .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background .view-scrollable::-webkit-scrollbar-button:single-button:vertical:decrement{height:36px}ux-dialog.fullscreen-dialog .app-content>router-view .view-background .view-scrollable:before,ux-dialog.fullscreen-dialog .app-content>.app-view .view-background .view-scrollable:before{display:none}@keyframes cta--boost--hover{0%{background-color:rgba(0,0,0,0)}100%{background-color:rgba(var(--theme--highlight--rgb), 0.5)}}@keyframes cta--pulse{from{margin:0}to{margin:-5px;width:calc(100% + 10px);height:calc(100% + 10px)}}@keyframes cta--inner-pulse{from{opacity:0}to{opacity:.5}}@keyframes favorite-bump{0%{transform:scale(1)}50%{transform:scale(1.4)}100%{transform:scale(1)}}body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}#fullscreen-dialogs ux-dialog-container>div{margin:0 !important}#fullscreen-dialogs ux-dialog-overlay{background:var(--theme--secondary-background);opacity:1}#dialogs ux-dialog{animation:dialog-pop .2s ease-in-out}#dialogs ux-dialog-container.active:not(:last-of-type){opacity:.0001}ux-dialog-overlay{background:rgba(var(--theme--background--rgb), 0.75);transition:opacity .2s !important}ux-dialog-overlay:nth-of-type(1n + 2){display:none}ux-dialog-container{display:flex;overflow:hidden auto;transition-duration:.2s !important}ux-dialog-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}ux-dialog-container::-webkit-scrollbar-thumb:window-inactive,ux-dialog-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}ux-dialog-container::-webkit-scrollbar-thumb:window-inactive:hover,ux-dialog-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}ux-dialog-container>div{padding:0;display:flex;flex:1 1 auto;justify-content:center;align-items:center}@media(max-width: 500px){ux-dialog-container>div{margin:15px !important}}@media(min-width: 501px){ux-dialog-container>div{margin:50px !important}}ux-dialog{box-shadow:0 0 5px rgba(var(--theme--background--rgb), 0.5);position:relative;border-radius:10px;border:1px solid rgba(255,255,255,.05);padding:11px 15px;text-align:left;display:inline-flex;align-items:center;padding:18px 26px 26px 26px;width:550px;display:block;border-radius:20px;max-width:calc(100vw - 126px)}.theme-default ux-dialog{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro ux-dialog{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro ux-dialog{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro ux-dialog{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro ux-dialog{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}ux-dialog>*+*{margin-left:9px}ux-dialog,ux-dialog a{font-size:14px;line-height:21px;line-height:19px;font-weight:500;color:rgba(255,255,255,.5)}ux-dialog strong,ux-dialog em{font-weight:700;color:#fff;font-style:normal}ux-dialog p{margin:0;padding:0}ux-dialog p+p{margin-top:10px}ux-dialog>*+*{margin-left:0}ux-dialog.dialog-loaded{animation:dialog-pop2 .2s ease-in-out}ux-dialog.align-center ux-dialog-header,ux-dialog.align-center ux-dialog-body,ux-dialog.align-center ux-dialog-footer{text-align:center}ux-dialog.align-left ux-dialog-header,ux-dialog.align-left ux-dialog-body,ux-dialog.align-left ux-dialog-footer{text-align:left}ux-dialog.scrollable{max-height:calc(100vh - 100px);padding:0}ux-dialog.scrollable>*+*{margin:0}ux-dialog.scrollable .dialog-scroll-wrapper{width:100%;max-height:inherit;overflow-x:hidden;overflow-y:overlay}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-button:single-button:vertical:decrement{height:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-button:single-button:vertical:increment{height:10px}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar{width:25px;border-top-left-radius:0;border-top-right-radius:20px;border-bottom-right-radius:20px;border-bottom-left-radius:0;background:rgba(0,0,0,0)}ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,ux-dialog.scrollable .dialog-scroll-wrapper::-webkit-scrollbar-thumb{border:10px solid rgba(0,0,0,0)}@media(max-width: 500px){ux-dialog.scrollable{max-height:calc(100vh - 30px)}}ux-dialog.secondary-gradient-bg{border:0;background:linear-gradient(180deg, var(--theme--secondary-background) 0%, #000 100%) !important}.theme-pro ux-dialog.secondary-gradient-bg{background:linear-gradient(180deg, #2b2b2b 0%, #000 100%) !important}ux-dialog header{font-weight:800;color:#fff;margin-bottom:20px}ux-dialog ux-dialog-header{border:0;padding:0}ux-dialog ux-dialog-header h1{font-weight:800;font-size:21px;line-height:30px;font-weight:700;color:#fff;margin:0}ux-dialog ux-dialog-header h1 sub{color:rgba(255,255,255,.5);vertical-align:unset}ux-dialog ux-dialog-header .dialog-close{display:none}ux-dialog ux-dialog-body{font-weight:700;font-size:15px;line-height:24px;font-weight:500;color:rgba(255,255,255,.6);padding:0}ux-dialog ux-dialog-body em{font-style:normal;font-weight:bold;color:rgba(255,255,255,.8)}ux-dialog ux-dialog-body p{margin:0}ux-dialog ux-dialog-body p+p{margin-top:12px}ux-dialog ux-dialog-footer{border:0;padding:20px 0 0 0;white-space:nowrap}ux-dialog ux-dialog-footer button{margin:0 15px 0 0}ux-dialog ux-dialog-footer button:last-child{margin-right:0}ux-dialog ux-dialog-footer button.secondary{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}ux-dialog ux-dialog-footer button.secondary,ux-dialog ux-dialog-footer button.secondary *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary{border:1px solid #fff}}ux-dialog ux-dialog-footer button.secondary>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}ux-dialog ux-dialog-footer button.secondary>*:first-child{padding-left:0}ux-dialog ux-dialog-footer button.secondary>*:last-child{padding-right:0}ux-dialog ux-dialog-footer button.secondary svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary svg *{fill:CanvasText}}ux-dialog ux-dialog-footer button.secondary svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.secondary svg{opacity:1}}ux-dialog ux-dialog-footer button.secondary img{height:50%}ux-dialog ux-dialog-footer button.secondary:disabled{opacity:.3}ux-dialog ux-dialog-footer button.secondary:disabled,ux-dialog ux-dialog-footer button.secondary:disabled *{cursor:default;pointer-events:none}@media(hover: hover){ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover svg{opacity:1}}ux-dialog ux-dialog-footer button.secondary:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){ux-dialog ux-dialog-footer button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}ux-dialog ux-dialog-footer button.cancel{font-size:12px;line-height:18px;background:rgba(0,0,0,0) !important;padding:0;border:0;display:inline-flex;align-items:center;color:rgba(255,255,255,.4);text-decoration:none}ux-dialog ux-dialog-footer button.cancel svg *{fill:rgba(255,255,255,.4)}@media(hover: hover){ux-dialog ux-dialog-footer button.cancel:hover{color:#fff}ux-dialog ux-dialog-footer button.cancel:hover svg *{fill:#fff}}ux-dialog ux-dialog-footer button.cancel:after{content:"";display:inline-block;width:5px;height:9px;-webkit-mask-box-image:url(${V});margin-left:5px;background:rgba(255,255,255,.4);transition:transform .15s}@media(hover: hover){ux-dialog ux-dialog-footer button.cancel:hover{color:#fff}ux-dialog ux-dialog-footer button.cancel:hover:after{transform:translateX(3px);background:#fff}}ux-dialog ux-dialog-footer button.primary,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel){--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}ux-dialog ux-dialog-footer button.primary,ux-dialog ux-dialog-footer button.primary *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel),ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel){border:1px solid #fff}}ux-dialog ux-dialog-footer button.primary>*,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}ux-dialog ux-dialog-footer button.primary>*:first-child,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*:first-child{padding-left:0}ux-dialog ux-dialog-footer button.primary>*:last-child,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel)>*:last-child{padding-right:0}ux-dialog ux-dialog-footer button.primary svg,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary svg *,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg *{fill:CanvasText}}ux-dialog ux-dialog-footer button.primary svg *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button.primary svg,body:not(.override-contrast-mode) ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) svg{opacity:1}}ux-dialog ux-dialog-footer button.primary img,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel) img{height:50%}ux-dialog ux-dialog-footer button.primary:disabled,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled{opacity:.3}ux-dialog ux-dialog-footer button.primary:disabled,ux-dialog ux-dialog-footer button.primary:disabled *,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):disabled *{cursor:default;pointer-events:none}@media(hover: hover){ux-dialog ux-dialog-footer button.primary:not(:disabled):hover,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}ux-dialog ux-dialog-footer button.primary:not(:disabled):hover svg,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover svg{opacity:1}}ux-dialog ux-dialog-footer button.primary:not(:disabled):active,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){ux-dialog ux-dialog-footer button.primary:not(:disabled):hover,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}ux-dialog ux-dialog-footer button.primary:not(:disabled):active,ux-dialog ux-dialog-footer button:not(.secondary):not(.cancel):not(:disabled):active{background-color:var(--theme--highlight)}ux-dialog close-button{position:absolute;right:-13px;top:-13px}ux-dialog.fullscreen-dialog{background:var(--theme--secondary-background);max-width:initial}ux-dialog.fullscreen-dialog close-button{position:fixed;right:20px;top:40px}@media(max-width: 500px){ux-dialog{max-width:calc(100vw - 30px)}ux-dialog close-button{right:-10px;top:-10px}}.dialog-loading-indicator{padding:60px 150px}.dialog-loading-indicator svg{width:40px;height:40px}@keyframes dialog-fade{0%{opacity:0}100%{opacity:1}}@keyframes dialog-pop{0%{opacity:0;transform:scale(0.8)}25%{opacity:1}50%{transform:scale(1.05)}100%{opacity:1;transform:scale(1)}}@keyframes dialog-pop2{0%{opacity:0;transform:scale(0)}25%{opacity:1}50%{transform:scale(1.05)}100%{transform:scale(1)}}.fullscreen-dialog{width:100%;height:100%;border:0;padding:0;background:rgba(0,0,0,0);max-height:100%;border-radius:0}@keyframes dropdown-menu-transition-right{from{opacity:0;transform:translateX(-10px)}to{opacity:1;transform:translateX(0)}}@keyframes dropdown-menu-transition-left{from{opacity:0;transform:translateX(10px)}to{opacity:1;transform:translateX(0)}}@keyframes dropdown-menu-transition-down{from{opacity:0;transform:translateY(-10px)}to{opacity:1;transform:translateY(0)}}.wemod-icon,i{display:inline-block}.wemod-icon:after,i:after{content:"";display:block;clear:both}.wemod-icon svg,i svg{float:left}.wemod-icon--xs svg{max-width:8px;max-height:8px}.wemod-icon--xs svg *{color:#fff}.wemod-icon--s svg{max-width:15px;max-height:15px}.wemod-icon--s svg *{color:#fff}.wemod-icon--m svg{max-width:20px;max-height:20px}.wemod-icon--m svg *{color:#fff}.wemod-icon--l svg{max-width:75px;max-height:75px}.wemod-icon--l svg *{color:#fff}.wemod-payment-method--american-express,.wemod-payment-method--amex{background-image:url(${K}) !important;background-color:#fff}.wemod-payment-method--diners{background-image:url(${Q}) !important;background-color:#fff}.wemod-payment-method--discover{background-image:url(${Z}) !important;background-color:#fff}.wemod-payment-method--jcb{background-image:url(${B}) !important;background-color:#fff}.wemod-payment-method--mastercard{background-image:url(${J}) !important;background-color:#fff}.wemod-payment-method--visa{background-image:url(${ee}) !important;background-color:#fff}.wemod-payment-method--paypal{background-image:url(${te}) !important;background-color:#fff}.wemod-payment-method--amazon-pay{background-image:url(${oe}) !important;background-color:#000}.wemod-payment-method--google-pay{background-image:url(${ae}) !important;background-color:#fff;width:48px !important;background-size:auto 12px;background-color:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.1)}.wemod-payment-method--apple-pay{background-image:url(${re}) !important;background-color:#fff;width:48px !important;background-size:auto 12px;background-color:rgba(0,0,0,0) !important;border:1px solid rgba(255,255,255,.1)}.wemod-payment-method--alipay{background-image:url(${ie}) !important;background-color:#fff}.wemod-payment-method--direct-debit{background-image:url(${ne}) !important;background-color:#fff}.wemod-payment-method--kr-market{background-image:url(${le}) !important;background-color:#fff}.wemod-payment-method--kakao-pay{background-image:url(${de}) !important;background-color:#000}.wemod-payment-method--kr-card{background-image:url(${se}) !important;background-color:#fff}.wemod-payment-method--naver-pay{background-image:url(${ge}) !important;background-color:#fff}@keyframes placeholder__el--initial-fade{0%{opacity:0}100%{opacity:1}}@keyframes placeholder__el--fade{0%{background:rgba(255,255,255,.02)}100%{background:rgba(255,255,255,.15)}}.wemod-tag{background-color:var(--color--accent);display:inline-block;font-weight:700;font-size:10px;color:#111;line-height:16px;letter-spacing:.5px;text-transform:uppercase;padding:0 4px;border-radius:4px;min-width:0;font-style:normal}.wemod-tag--pro{background-color:var(--color--pro)}.wemod-tag--new{background-color:#ddf00c;color:#3b5209}.wemod-tag--updated{background-color:rgba(255,255,255,.1);color:var(--theme--text-primary)}.wemod-tag--alert{background-color:var(--color--alert)}.wemod-tag--warn{background-color:rgba(var(--color--accent-yellow--rgb), 0.2);color:var(--color--accent-yellow)}.theme-default{--theme--background:#0d0f12;--theme--background--rgb:13,15,18;--theme--secondary-background:#18293a;--theme--secondary-background--rgb:24,41,58;--theme--background-accent:#131417;--theme--background-accent--rgb:19,20,23;--theme--highlight:#00c7f2;--theme--highlight--rgb:0,199,242;--theme--highlight-darker:#009fc2;--theme--highlight-darker--rgb:0,159,194;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0d0f12;--theme--text-inverse--rgb:13,15,18}.theme-purple-pro{--theme--background:#0e0e15;--theme--background--rgb:14,14,21;--theme--secondary-background:#191f48;--theme--secondary-background--rgb:25,31,72;--theme--background-accent:#131319;--theme--background-accent--rgb:19,19,25;--theme--highlight:#a341ff;--theme--highlight--rgb:163,65,255;--theme--highlight-darker:#8234cc;--theme--highlight-darker--rgb:130,52,204;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0e0e15;--theme--text-inverse--rgb:14,14,21}.theme-green-pro{--theme--background:#0b1114;--theme--background--rgb:11,17,20;--theme--secondary-background:#0f2b27;--theme--secondary-background--rgb:15,43,39;--theme--background-accent:#10171a;--theme--background-accent--rgb:16,23,26;--theme--highlight:#00e7c6;--theme--highlight--rgb:0,231,198;--theme--highlight-darker:#00b99e;--theme--highlight-darker--rgb:0,185,158;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0b1114;--theme--text-inverse--rgb:11,17,20}.theme-orange-pro{--theme--background:#0f0f12;--theme--background--rgb:15,15,18;--theme--secondary-background:#212c38;--theme--secondary-background--rgb:33,44,56;--theme--background-accent:#15151a;--theme--background-accent--rgb:21,21,26;--theme--highlight:#ff6368;--theme--highlight--rgb:255,99,104;--theme--highlight-darker:#cc4f53;--theme--highlight-darker--rgb:204,79,83;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0f0f12;--theme--text-inverse--rgb:15,15,18}.theme-pro{--theme--background:#0a0a0a;--theme--background--rgb:10,10,10;--theme--secondary-background:#000000;--theme--secondary-background--rgb:0,0,0;--theme--background-accent:#161617;--theme--background-accent--rgb:22,22,23;--theme--highlight:#1293ff;--theme--highlight--rgb:18,147,255;--theme--highlight-darker:#0e76cc;--theme--highlight-darker--rgb:14,118,204;--theme--text-highlight:white;--theme--text-highlight--rgb:255,255,255;--theme--text-primary:rgba(255, 255, 255, 0.8);--theme--text-primary--rgb:255,255,255;--theme--text-secondary:rgba(255, 255, 255, 0.6);--theme--text-secondary--rgb:255,255,255;--theme--text-disabled:rgba(255, 255, 255, 0.4);--theme--text-disabled--rgb:255,255,255;--theme--text-placeholder:rgba(255, 255, 255, 0.3);--theme--text-placeholder--rgb:255,255,255;--theme--text-inverse:#0a0a0a;--theme--text-inverse--rgb:10,10,10}:root,:before,:after{--color--accent:#acff35;--color--accent--rgb:172,255,53;--color--accent-yellow:#ffaa2b;--color--accent-yellow--rgb:255,170,43;--color--alert:#ff0056;--color--alert--rgb:255,0,86;--color--secondary-purple:#834bff;--color--secondary-purple--rgb:131,75,255;--color--pro:#9200ff;--color--pro--rgb:146,0,255;--color--brand-blue:#13cfff;--color--brand-blue--rgb:19,207,255;--color--brand-green:#0bf2f6;--color--brand-green--rgb:11,242,246;--color--warning:#ffed48;--color--warning--rgb:255,237,72;--theme--default--background:#0d0f12;--theme--default--background--rgb:13,15,18;--theme--default--secondary-background:#18293a;--theme--default--secondary-background--rgb:24,41,58;--theme--default--background-accent:#131417;--theme--default--background-accent--rgb:19,20,23;--theme--default--highlight:#00c7f2;--theme--default--highlight--rgb:0,199,242;--theme--default--highlight-darker:#009fc2;--theme--default--highlight-darker--rgb:0,159,194;--theme--default--text-highlight:white;--theme--default--text-highlight--rgb:255,255,255;--theme--default--text-primary:rgba(255, 255, 255, 0.8);--theme--default--text-primary--rgb:255,255,255;--theme--default--text-secondary:rgba(255, 255, 255, 0.6);--theme--default--text-secondary--rgb:255,255,255;--theme--default--text-disabled:rgba(255, 255, 255, 0.4);--theme--default--text-disabled--rgb:255,255,255;--theme--default--text-placeholder:rgba(255, 255, 255, 0.3);--theme--default--text-placeholder--rgb:255,255,255;--theme--default--text-inverse:#0d0f12;--theme--default--text-inverse--rgb:13,15,18;--theme--purple-pro--background:#0e0e15;--theme--purple-pro--background--rgb:14,14,21;--theme--purple-pro--secondary-background:#191f48;--theme--purple-pro--secondary-background--rgb:25,31,72;--theme--purple-pro--background-accent:#131319;--theme--purple-pro--background-accent--rgb:19,19,25;--theme--purple-pro--highlight:#a341ff;--theme--purple-pro--highlight--rgb:163,65,255;--theme--purple-pro--highlight-darker:#8234cc;--theme--purple-pro--highlight-darker--rgb:130,52,204;--theme--purple-pro--text-highlight:white;--theme--purple-pro--text-highlight--rgb:255,255,255;--theme--purple-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--purple-pro--text-primary--rgb:255,255,255;--theme--purple-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--purple-pro--text-secondary--rgb:255,255,255;--theme--purple-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--purple-pro--text-disabled--rgb:255,255,255;--theme--purple-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--purple-pro--text-placeholder--rgb:255,255,255;--theme--purple-pro--text-inverse:#0e0e15;--theme--purple-pro--text-inverse--rgb:14,14,21;--theme--green-pro--background:#0b1114;--theme--green-pro--background--rgb:11,17,20;--theme--green-pro--secondary-background:#0f2b27;--theme--green-pro--secondary-background--rgb:15,43,39;--theme--green-pro--background-accent:#10171a;--theme--green-pro--background-accent--rgb:16,23,26;--theme--green-pro--highlight:#00e7c6;--theme--green-pro--highlight--rgb:0,231,198;--theme--green-pro--highlight-darker:#00b99e;--theme--green-pro--highlight-darker--rgb:0,185,158;--theme--green-pro--text-highlight:white;--theme--green-pro--text-highlight--rgb:255,255,255;--theme--green-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--green-pro--text-primary--rgb:255,255,255;--theme--green-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--green-pro--text-secondary--rgb:255,255,255;--theme--green-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--green-pro--text-disabled--rgb:255,255,255;--theme--green-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--green-pro--text-placeholder--rgb:255,255,255;--theme--green-pro--text-inverse:#0b1114;--theme--green-pro--text-inverse--rgb:11,17,20;--theme--orange-pro--background:#0f0f12;--theme--orange-pro--background--rgb:15,15,18;--theme--orange-pro--secondary-background:#212c38;--theme--orange-pro--secondary-background--rgb:33,44,56;--theme--orange-pro--background-accent:#15151a;--theme--orange-pro--background-accent--rgb:21,21,26;--theme--orange-pro--highlight:#ff6368;--theme--orange-pro--highlight--rgb:255,99,104;--theme--orange-pro--highlight-darker:#cc4f53;--theme--orange-pro--highlight-darker--rgb:204,79,83;--theme--orange-pro--text-highlight:white;--theme--orange-pro--text-highlight--rgb:255,255,255;--theme--orange-pro--text-primary:rgba(255, 255, 255, 0.8);--theme--orange-pro--text-primary--rgb:255,255,255;--theme--orange-pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--orange-pro--text-secondary--rgb:255,255,255;--theme--orange-pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--orange-pro--text-disabled--rgb:255,255,255;--theme--orange-pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--orange-pro--text-placeholder--rgb:255,255,255;--theme--orange-pro--text-inverse:#0f0f12;--theme--orange-pro--text-inverse--rgb:15,15,18;--theme--pro--background:#0a0a0a;--theme--pro--background--rgb:10,10,10;--theme--pro--secondary-background:#000000;--theme--pro--secondary-background--rgb:0,0,0;--theme--pro--background-accent:#161617;--theme--pro--background-accent--rgb:22,22,23;--theme--pro--highlight:#1293ff;--theme--pro--highlight--rgb:18,147,255;--theme--pro--highlight-darker:#0e76cc;--theme--pro--highlight-darker--rgb:14,118,204;--theme--pro--text-highlight:white;--theme--pro--text-highlight--rgb:255,255,255;--theme--pro--text-primary:rgba(255, 255, 255, 0.8);--theme--pro--text-primary--rgb:255,255,255;--theme--pro--text-secondary:rgba(255, 255, 255, 0.6);--theme--pro--text-secondary--rgb:255,255,255;--theme--pro--text-disabled:rgba(255, 255, 255, 0.4);--theme--pro--text-disabled--rgb:255,255,255;--theme--pro--text-placeholder:rgba(255, 255, 255, 0.3);--theme--pro--text-placeholder--rgb:255,255,255;--theme--pro--text-inverse:#0a0a0a;--theme--pro--text-inverse--rgb:10,10,10}@keyframes thumbnail-loading{0%{background-color:rgba(255,255,255,.02)}100%{background-color:rgba(255,255,255,.15)}}@keyframes trainer-play-button-loading-scroll{0%{transform:translateX(0)}100%{transform:translateX(-100%)}}@keyframes trainer-play-button-line{from{transform:translateX(215px)}to{transform:translateX(-215px)}}.overflow-fade__wrapper{position:relative;z-index:0}.overflow-fade__wrapper:before,.overflow-fade__wrapper:after{content:"";display:block;position:absolute;pointer-events:none;z-index:9999;opacity:0;transition:opacity .15s}.overflow-fade__wrapper--vertical{height:100%}.overflow-fade__wrapper--vertical:before{top:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to bottom, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/calc(100% - 7px) var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--vertical--no-scrollbar{height:100%}.overflow-fade__wrapper--vertical--no-scrollbar:before{top:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to bottom, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/100% var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--vertical--no-scrollbar.overflow-fade-bottom:after{opacity:1;bottom:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to top, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) bottom left/100% var(--overflow-fade--width, 25px) no-repeat}.overflow-fade__wrapper--horizontal{width:100%}.overflow-fade__wrapper--horizontal:before{top:0;left:0;width:var(--overflow-fade--width, 25px);height:100%;background:linear-gradient(to right, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top left/var(--overflow-fade--width, 25px) calc(100% - 7px) no-repeat}.overflow-fade__wrapper--horizontal:after{top:0;right:0;width:var(--overflow-fade--width, 25px);height:100%;background:linear-gradient(to left, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) top right/var(--overflow-fade--width, 25px) calc(100% - 7px) no-repeat}.overflow-fade-left:before{opacity:1}.overflow-fade-right:after{opacity:1}.overflow-fade-top:before{opacity:1}.overflow-fade-bottom:after{opacity:1;bottom:0;left:0;width:100%;height:var(--overflow-fade--width, 25px);background:linear-gradient(to top, var(--overflow-fade--background) 0%, transparent var(--overflow-fade--width, 25px)) bottom left/calc(100% - 7px) var(--overflow-fade--width, 25px) no-repeat}html{background:rgba(0,0,0,.5);-webkit-font-smoothing:antialiased}body{margin:0;padding:0;width:100%;overflow:hidden}body.override-contrast-mode,body.override-contrast-mode *{forced-color-adjust:none}*,a{cursor:default;box-sizing:border-box;-webkit-user-select:none;font-family:"Inter"}a{text-decoration:none}a,img{-webkit-user-drag:none}a,a *,button,button *{cursor:pointer}a:not(:focus-visible):not(input):not(textarea),a *:not(:focus-visible):not(input):not(textarea),button:not(:focus-visible):not(input):not(textarea),button *:not(:focus-visible):not(input):not(textarea){outline:none}input[type=text],input[type=password]{cursor:initial}input,textarea{outline:none}textarea{cursor:initial}*:focus-visible{outline:2px solid var(--theme--highlight) !important}`,""]);const ce=D}}]);
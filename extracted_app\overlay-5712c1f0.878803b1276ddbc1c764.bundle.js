"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[621],{68663:(e,t,n)=>{function r(e){const t=e.headers.get("etag")||null;return t?t.replace(/^W\//,"").replace(/"/g,""):null}n.d(t,{x:()=>s});class s{#e;#t;#n;constructor(e,t,n){this.#t="",this.#e=e,this.#n=t,this.#t=n}registerInstallation(e){return this.#e.post("/v3/installation",{token:e})}requestRemoteAuthCode(){return this.#e.post("/v3/auth/remote_code")}async requestOverlayAuthCode(){return await this.#r("/v3/auth/overlay_code")}async requestWebAuthCode(){return await this.#r("/v3/auth/web_code")}async#r(e){return(await this.#e.post(e)).code}createWebview(e){return this.#e.post("/v3/webview",e)}getUserAccount(){return this.#e.fetch({endpoint:"/v3/account",method:"GET",name:"/v3/account",collectMetrics:!1})}async getUserAccountFlags(e){return(await this.#e.fetch({endpoint:`/v3/account/flags/${e}`,method:"GET",name:"/v3/account/flags/:mask",collectMetrics:!1})).flags}changeAccountEmail(e,t=null){return this.#e.post("/v3/account/email",{email:e,currentPassword:t})}changeAccountPassword(e,t=null){return this.#e.post("/v3/account/password",{password:e,currentPassword:t})}changeAccountUsername(e){return this.#e.post("/v3/account/username",{username:e,currentPassword:null})}changeAccountProfileImage(e){return this.#e.post("/v3/account/profile_image",{profileImage:e})}setAccountLanguage(e,t){return this.#e.post("/v3/account/language",{tag:e,auto:t})}resumeSubscription(){return this.#e.post("/v3/account/subscription",{state:"active"})}getLastInvoice(){return this.#e.get("/v3/account/invoice")}async getBillingPortalUrl(){const e=await this.#e.get("/v3/account/billing_portal");return e?e.url:null}async getCatalog(e){const t={method:"GET",cache:"no-store",headers:{Accept:"application/json"}};e&&t.headers&&(t.headers["If-None-Match"]=`"${e}"`);const n=Date.now(),s=await fetch(this.#t,t);return 200===s.status&&this.#n.report({endpoint:this.#t,method:"GET",responseTime:Date.now()-n}),{body:304!==s.status?await s.json():null,cacheKey:r(s)}}getUnavailableTitle(e){return this.#e.get(`/v3/unavailable_titles/${e}`)}searchUnavailableTitles(e,t){return this.#e.get("/v3/unavailable_titles",{q:e,limit:t.toString()})}getUnavailableTitlesByCorrelationIds(e){return this.#e.post("/v3/unavailable_titles",e)}getMostCompatibleTrainerForGame(e,t,n){return this.#e.fetch({endpoint:`/v3/games/${e}/trainer`,method:"GET",query:{gameVersions:n.join(","),locale:t,v:"3"},name:"/v3/games/:gameId/trainer",collectMetrics:!1})}getTrainerById(e,t){return this.#e.get(`/v3/trainers/${e}`,{locale:t,v:"3"})}getLocalTrainerById(e,t){return this.#e.get(`/v3/trainers/${e}/local`,{locale:t,v:"3"})}getLatestLocalTrainerForGame(e){return this.#e.get(`/v3/games/${e}/local_trainer`,{v:"3"})}getTrainerHistoryForGame(e){return this.#e.get(`/v3/games/${e}/trainers`)}reportInstalledGameVersions(e){return this.#e.post("/v3/installed_games",e)}submitTrainerFeedback(e){return this.#e.post("/v3/trainer_feedback",e)}claimRewardOffer(e){return this.#e.post("/v3/partner_rewards/claim",{rewardKey:e})}submitAppRating(e,t,n=!1){return this.#e.post("/v3/ratings"+(n?"?nps=true":""),{rating:e,feedback:t})}recordGamePresence(e){return this.#e.post("/v3/game_presence",{gameId:e})}boostGame(e,t){return this.#e.post(`/v3/games/${e}/boost`,{availableBoosts:t})}respondToPoll(e,t,n,r){return this.#e.post(`/v3/polls/${e}/cast`,{selections:t,customSelection:n,details:r})}getFollowedGames(){return this.#e.fetch({endpoint:"/v3/account/followed_games",method:"GET",name:"/v3/account/followed_games",collectMetrics:!1})}followGames(e,t){return this.#e.post("/v3/account/followed_games",{type:t,gameIds:e})}unfollowGames(e){return this.#e.post("/v3/account/followed_games",{type:0,gameIds:e})}unfollowAllGames(){return this.#e.delete("/v3/account/followed_games")}suggestCheats(e,t){return this.#e.post(`/v3/games/${e}/suggest`,{suggestions:t})}getPaymentMethods(){return this.#e.get("/v3/checkout/methods")}removePaymentMethod(e){return this.#e.delete(`/v3/checkout/methods/${e}`)}getConsentRequirements(){return this.#e.get("/v3/gdpr")}giveConsent(){return this.#e.post("/v3/account/gdpr",{consentGiven:!0})}revokeConsent(){return this.#e.post("/v3/account/gdpr",{consentGiven:!1})}getPromotion(){return this.#e.fetch({endpoint:"/v3/promotion",method:"GET",name:"/v3/promotion",collectMetrics:!1})}claimObjectiveReward(e){return this.#e.post("/v3/gamify/objective/claim",{id:e})}getCurrentObjectiveSets(){return this.#e.get("/v3/gamify/set/current")}reportUserEvent(e,...t){return this.#e.post("/v3/user/event",{event:e,...void 0===t[0]?{}:{data:t[0]}})}getUserGamePreferences(e){return this.#e.get(`/v3/user/game/${e}`)}updatePinnedMods(e,t){return this.#e.post(`/v3/user/game/${e}/pinned_mods`,{data:t})}getSuggestedMods(e){return this.#e.get(`/v3/games/${e}/mod_suggestions`)}createModSuggestion(e,t){return this.#e.post(`/v3/games/${e}/mod_suggestion`,{suggestion:t})}boostModSuggestion(e,t){return this.#e.post(`/v3/games/${e}/mod_suggestion/${t}/boost`)}}},73592:(e,t,n)=>{n.d(t,{b:()=>a});var r=n(15215),s=n("aurelia-framework"),i=n(55816),o=n.n(i);let a=class{play({loop:e=!1,autoplay:t=!0,path:n}){const r=document.createElement("div"),s=o().loadAnimation({container:r,renderer:"svg",loop:e,autoplay:t,path:n});return r.style.cssText="\n      position: fixed;\n      top: 0;\n      left: 0;\n      width: 100%;\n      height: 100%;\n      z-index: 9999;\n      pointer-events: none;\n    ",document.body.appendChild(r),s.addEventListener("complete",(()=>{document.body.removeChild(r)})),s}};a=(0,r.Cg)([(0,s.singleton)()],a)},"api/index":(e,t,n)=>{n.r(t),n.d(t,{configure:()=>o});var r=n("shared/api/index"),s=n(84551),i=n(68663);function o(e,t){e.container.registerSingleton(i.x,(function(){return new i.x(e.container.get(r.WeModApiClient),e.container.get(s.Y),t.catalogUrl)}))}}}]);
/*! For license information please see vendor.image-q.9ee38146d869d420b7f7.bundle.js.LICENSE.txt */
(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6194],{65565:i=>{var t=Object.defineProperty,e=Object.getOwnPropertyDescriptor,s=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,r=(i,e)=>{for(var s in e)t(i,s,{get:e[s],enumerable:!0})},n=(i=>(r,n)=>i&&i.get(r)||(n=((i,r,n,h)=>{if(r&&"object"==typeof r||"function"==typeof r)for(let n of s(r))a.call(i,n)||t(i,n,{get:()=>r[n],enumerable:!(h=e(r,n))||h.enumerable});return i})(t({},"__esModule",{value:!0}),r),i&&i.set(r,n),n))("undefined"!=typeof WeakMap?new WeakMap:0),h=(i,e,s)=>(((i,e,s)=>{e in i?t(i,e,{enumerable:!0,configurable:!0,writable:!0,value:s}):i[e]=s})(i,"symbol"!=typeof e?e+"":e,s),s),m={};r(m,{applyPalette:()=>Ji,applyPaletteSync:()=>$i,buildPalette:()=>Xi,buildPaletteSync:()=>Vi,constants:()=>u,conversion:()=>d,distance:()=>W,image:()=>vi,palette:()=>si,quality:()=>Qi,utils:()=>ui});var u={};r(u,{bt709:()=>o});var o={};r(o,{Y:()=>l,x:()=>_,y:()=>c});var l=(i=>(i[i.RED=.2126]="RED",i[i.GREEN=.7152]="GREEN",i[i.BLUE=.0722]="BLUE",i[i.WHITE=1]="WHITE",i))(l||{}),_=(i=>(i[i.RED=.64]="RED",i[i.GREEN=.3]="GREEN",i[i.BLUE=.15]="BLUE",i[i.WHITE=.3127]="WHITE",i))(_||{}),c=(i=>(i[i.RED=.33]="RED",i[i.GREEN=.6]="GREEN",i[i.BLUE=.06]="BLUE",i[i.WHITE=.329]="WHITE",i))(c||{}),d={};function g(i){return i>.04045?((i+.055)/1.055)**2.4:i/12.92}function M(i,t,e){return{x:.4124*(i=g(i/255))+.3576*(t=g(t/255))+.1805*(e=g(e/255)),y:.2126*i+.7152*t+.0722*e,z:.0193*i+.1192*t+.9505*e}}r(d,{lab2rgb:()=>L,lab2xyz:()=>q,rgb2hsl:()=>z,rgb2lab:()=>E,rgb2xyz:()=>M,xyz2lab:()=>I,xyz2rgb:()=>U});var p={};function b(i){return i*(Math.PI/180)}function f(i,t,e){let s=i;return s<t&&(s=t),s<e&&(s=e),s}function x(i,t,e){let s=i;return s>t&&(s=t),s>e&&(s=e),s}function w(i,t,e){return i>e&&(i=e),i<t&&(i=t),0|i}function S(i){return(i=Math.round(i))>255?i=255:i<0&&(i=0),i}function y(i){return i>255?i=255:i<0&&(i=0),i}function k(i,t){const e=typeof i[0];let s;if("number"===e||"string"===e){const e=Object.create(null);for(let t=0,s=i.length;t<s;t++){const s=i[t];e[s]||0===e[s]||(e[s]=t)}s=i.sort(((i,s)=>t(i,s)||e[i]-e[s]))}else{const e=i.slice(0);s=i.sort(((i,s)=>t(i,s)||e.indexOf(i)-e.indexOf(s)))}return s}function z(i,t,e){const s=x(i,t,e),a=f(i,t,e),r=a-s,n=(s+a)/510;let h=0;n>0&&n<1&&(h=r/(n<.5?a+s:510-a-s));let m=0;return r>0&&(m=a===i?(t-e)/r:a===t?2+(e-i)/r:4+(i-t)/r,m*=60,m<0&&(m+=360)),{h:m,s:h,l:n}}r(p,{degrees2radians:()=>b,inRange0to255:()=>y,inRange0to255Rounded:()=>S,intInRange:()=>w,max3:()=>f,min3:()=>x,stableSort:()=>k});var A=.95047,B=1,P=1.08883;function R(i){return i>.008856?i**(1/3):7.787*i+16/116}function I(i,t,e){if(i=R(i/A),t=R(t/B),e=R(e/P),116*t-16<0)throw new Error("xxx");return{L:Math.max(0,116*t-16),a:500*(i-t),b:200*(t-e)}}function E(i,t,e){const s=M(i,t,e);return I(s.x,s.y,s.z)}var v=.95047,C=1,D=1.08883;function G(i){return i>.206893034?i**3:(i-16/116)/7.787}function q(i,t,e){const s=(i+16)/116,a=s-e/200;return{x:v*G(t/500+s),y:C*G(s),z:D*G(a)}}function N(i){return i>.0031308?1.055*i**(1/2.4)-.055:12.92*i}function U(i,t,e){const s=N(3.2406*i+-1.5372*t+-.4986*e),a=N(-.9689*i+1.8758*t+.0415*e),r=N(.0557*i+-.204*t+1.057*e);return{r:S(255*s),g:S(255*a),b:S(255*r)}}function L(i,t,e){const s=q(i,t,e);return U(s.x,s.y,s.z)}var W={};r(W,{AbstractDistanceCalculator:()=>Q,AbstractEuclidean:()=>V,AbstractManhattan:()=>Y,CIE94GraphicArts:()=>H,CIE94Textiles:()=>F,CIEDE2000:()=>O,CMetric:()=>K,Euclidean:()=>X,EuclideanBT709:()=>$,EuclideanBT709NoAlpha:()=>J,Manhattan:()=>Z,ManhattanBT709:()=>ti,ManhattanNommyde:()=>ii,PNGQuant:()=>ei});var Q=class{constructor(){h(this,"_maxDistance"),h(this,"_whitePoint"),this._setDefaults(),this.setWhitePoint(255,255,255,255)}setWhitePoint(i,t,e,s){this._whitePoint={r:i>0?255/i:0,g:t>0?255/t:0,b:e>0?255/e:0,a:s>0?255/s:0},this._maxDistance=this.calculateRaw(i,t,e,s,0,0,0,0)}calculateNormalized(i,t){return this.calculateRaw(i.r,i.g,i.b,i.a,t.r,t.g,t.b,t.a)/this._maxDistance}},T=class extends Q{calculateRaw(i,t,e,s,a,r,n,h){const m=E(y(i*this._whitePoint.r),y(t*this._whitePoint.g),y(e*this._whitePoint.b)),u=E(y(a*this._whitePoint.r),y(r*this._whitePoint.g),y(n*this._whitePoint.b)),o=m.L-u.L,l=m.a-u.a,_=m.b-u.b,c=Math.sqrt(m.a*m.a+m.b*m.b),d=c-Math.sqrt(u.a*u.a+u.b*u.b);let g=l*l+_*_-d*d;g=g<0?0:Math.sqrt(g);const M=(h-s)*this._whitePoint.a*this._kA;return Math.sqrt((o/this._Kl)**2+(d/(1+this._K1*c))**2+(g/(1+this._K2*c))**2+M**2)}},F=class extends T{_setDefaults(){this._Kl=2,this._K1=.048,this._K2=.014,this._kA=12.5/255}},H=class extends T{_setDefaults(){this._Kl=1,this._K1=.045,this._K2=.015,this._kA=25/255}},j=class extends Q{_setDefaults(){}static _calculatehp(i,t){const e=Math.atan2(i,t);return e>=0?e:e+j._deg360InRad}static _calculateRT(i,t){const e=t**7,s=2*Math.sqrt(e/(e+j._pow25to7)),a=j._deg30InRad*Math.exp(-(((i-j._deg275InRad)/j._deg25InRad)**2));return-Math.sin(2*a)*s}static _calculateT(i){return 1-.17*Math.cos(i-j._deg30InRad)+.24*Math.cos(2*i)+.32*Math.cos(3*i+j._deg6InRad)-.2*Math.cos(4*i-j._deg63InRad)}static _calculate_ahp(i,t,e,s){const a=e+s;return 0===i?a:t<=j._deg180InRad?a/2:a<j._deg360InRad?(a+j._deg360InRad)/2:(a-j._deg360InRad)/2}static _calculate_dHp(i,t,e,s){let a;return a=0===i?0:t<=j._deg180InRad?e-s:e<=s?e-s+j._deg360InRad:e-s-j._deg360InRad,2*Math.sqrt(i)*Math.sin(a/2)}calculateRaw(i,t,e,s,a,r,n,h){const m=E(y(i*this._whitePoint.r),y(t*this._whitePoint.g),y(e*this._whitePoint.b)),u=E(y(a*this._whitePoint.r),y(r*this._whitePoint.g),y(n*this._whitePoint.b)),o=(h-s)*this._whitePoint.a*j._kA,l=this.calculateRawInLab(m,u);return Math.sqrt(l+o*o)}calculateRawInLab(i,t){const e=i.L,s=i.a,a=i.b,r=t.L,n=t.a,h=t.b,m=((Math.sqrt(s*s+a*a)+Math.sqrt(n*n+h*h))/2)**7,u=.5*(1-Math.sqrt(m/(m+j._pow25to7))),o=(1+u)*s,l=(1+u)*n,_=Math.sqrt(o*o+a*a),c=Math.sqrt(l*l+h*h),d=_*c,g=j._calculatehp(a,o),M=j._calculatehp(h,l),p=Math.abs(g-M),b=r-e,f=c-_,x=j._calculate_dHp(d,p,M,g),w=j._calculate_ahp(d,p,g,M),S=(_+c)/2,y=((e+r)/2-50)**2,k=f/(1+.045*S),z=x/(1+.015*j._calculateT(w)*S);return(b/(1+.015*y/Math.sqrt(20+y)))**2+k**2+z**2+j._calculateRT(w,S)*k*z}},O=j;h(O,"_kA",25/255),h(O,"_pow25to7",25**7),h(O,"_deg360InRad",b(360)),h(O,"_deg180InRad",b(180)),h(O,"_deg30InRad",b(30)),h(O,"_deg6InRad",b(6)),h(O,"_deg63InRad",b(63)),h(O,"_deg275InRad",b(275)),h(O,"_deg25InRad",b(25));var K=class extends Q{calculateRaw(i,t,e,s,a,r,n,h){const m=(i+a)/2*this._whitePoint.r,u=(i-a)*this._whitePoint.r,o=(t-r)*this._whitePoint.g,l=(e-n)*this._whitePoint.b,_=((512+m)*u*u>>8)+4*o*o+((767-m)*l*l>>8),c=(h-s)*this._whitePoint.a;return Math.sqrt(_+c*c)}_setDefaults(){}},V=class extends Q{calculateRaw(i,t,e,s,a,r,n,h){const m=a-i,u=r-t,o=n-e,l=h-s;return Math.sqrt(this._kR*m*m+this._kG*u*u+this._kB*o*o+this._kA*l*l)}},X=class extends V{_setDefaults(){this._kR=1,this._kG=1,this._kB=1,this._kA=1}},$=class extends V{_setDefaults(){this._kR=.2126,this._kG=.7152,this._kB=.0722,this._kA=1}},J=class extends V{_setDefaults(){this._kR=.2126,this._kG=.7152,this._kB=.0722,this._kA=0}},Y=class extends Q{calculateRaw(i,t,e,s,a,r,n,h){let m=a-i,u=r-t,o=n-e,l=h-s;return m<0&&(m=0-m),u<0&&(u=0-u),o<0&&(o=0-o),l<0&&(l=0-l),this._kR*m+this._kG*u+this._kB*o+this._kA*l}},Z=class extends Y{_setDefaults(){this._kR=1,this._kG=1,this._kB=1,this._kA=1}},ii=class extends Y{_setDefaults(){this._kR=.4984,this._kG=.8625,this._kB=.2979,this._kA=1}},ti=class extends Y{_setDefaults(){this._kR=.2126,this._kG=.7152,this._kB=.0722,this._kA=1}},ei=class extends Q{calculateRaw(i,t,e,s,a,r,n,h){const m=(h-s)*this._whitePoint.a;return this._colordifferenceCh(i*this._whitePoint.r,a*this._whitePoint.r,m)+this._colordifferenceCh(t*this._whitePoint.g,r*this._whitePoint.g,m)+this._colordifferenceCh(e*this._whitePoint.b,n*this._whitePoint.b,m)}_colordifferenceCh(i,t,e){const s=i-t,a=s+e;return s*s+a*a}_setDefaults(){}},si={};r(si,{AbstractPaletteQuantizer:()=>ai,ColorHistogram:()=>wi,NeuQuant:()=>Mi,NeuQuantFloat:()=>fi,RGBQuant:()=>yi,WuColorCube:()=>Ri,WuQuant:()=>Ei});var ai=class{quantizeSync(){for(const i of this.quantize())if(i.palette)return i.palette;throw new Error("unreachable")}},ri=class{constructor(){h(this,"r"),h(this,"g"),h(this,"b"),h(this,"a"),h(this,"uint32"),h(this,"rgba"),this.uint32=-1>>>0,this.r=this.g=this.b=this.a=0,this.rgba=new Array(4),this.rgba[0]=0,this.rgba[1]=0,this.rgba[2]=0,this.rgba[3]=0}static createByQuadruplet(i){const t=new ri;return t.r=0|i[0],t.g=0|i[1],t.b=0|i[2],t.a=0|i[3],t._loadUINT32(),t._loadQuadruplet(),t}static createByRGBA(i,t,e,s){const a=new ri;return a.r=0|i,a.g=0|t,a.b=0|e,a.a=0|s,a._loadUINT32(),a._loadQuadruplet(),a}static createByUint32(i){const t=new ri;return t.uint32=i>>>0,t._loadRGBA(),t._loadQuadruplet(),t}from(i){this.r=i.r,this.g=i.g,this.b=i.b,this.a=i.a,this.uint32=i.uint32,this.rgba[0]=i.r,this.rgba[1]=i.g,this.rgba[2]=i.b,this.rgba[3]=i.a}getLuminosity(i){let t=this.r,e=this.g,s=this.b;return i&&(t=Math.min(255,255-this.a+this.a*t/255),e=Math.min(255,255-this.a+this.a*e/255),s=Math.min(255,255-this.a+this.a*s/255)),.2126*t+.7152*e+.0722*s}_loadUINT32(){this.uint32=(this.a<<24|this.b<<16|this.g<<8|this.r)>>>0}_loadRGBA(){this.r=255&this.uint32,this.g=this.uint32>>>8&255,this.b=this.uint32>>>16&255,this.a=this.uint32>>>24&255}_loadQuadruplet(){this.rgba[0]=this.r,this.rgba[1]=this.g,this.rgba[2]=this.b,this.rgba[3]=this.a}},ni=class{constructor(){h(this,"_pointArray"),h(this,"_width"),h(this,"_height"),this._width=0,this._height=0,this._pointArray=[]}getWidth(){return this._width}getHeight(){return this._height}setWidth(i){this._width=i}setHeight(i){this._height=i}getPointArray(){return this._pointArray}clone(){const i=new ni;i._width=this._width,i._height=this._height;for(let t=0,e=this._pointArray.length;t<e;t++)i._pointArray[t]=ri.createByUint32(0|this._pointArray[t].uint32);return i}toUint32Array(){const i=this._pointArray.length,t=new Uint32Array(i);for(let e=0;e<i;e++)t[e]=this._pointArray[e].uint32;return t}toUint8Array(){return new Uint8Array(this.toUint32Array().buffer)}static fromHTMLImageElement(i){const t=i.naturalWidth,e=i.naturalHeight,s=document.createElement("canvas");return s.width=t,s.height=e,s.getContext("2d").drawImage(i,0,0,t,e,0,0,t,e),ni.fromHTMLCanvasElement(s)}static fromHTMLCanvasElement(i){const t=i.width,e=i.height,s=i.getContext("2d").getImageData(0,0,t,e);return ni.fromImageData(s)}static fromImageData(i){const t=i.width,e=i.height;return ni.fromUint8Array(i.data,t,e)}static fromUint8Array(i,t,e){switch(Object.prototype.toString.call(i)){case"[object Uint8ClampedArray]":case"[object Uint8Array]":break;default:i=new Uint8Array(i)}const s=new Uint32Array(i.buffer);return ni.fromUint32Array(s,t,e)}static fromUint32Array(i,t,e){const s=new ni;s._width=t,s._height=e;for(let t=0,e=i.length;t<e;t++)s._pointArray[t]=ri.createByUint32(0|i[t]);return s}static fromBuffer(i,t,e){const s=new Uint32Array(i.buffer,i.byteOffset,i.byteLength/Uint32Array.BYTES_PER_ELEMENT);return ni.fromUint32Array(s,t,e)}};function hi(i,t){const e=360/t;for(let s=1,a=e-e/2;s<t;s++,a+=e)if(i>=a&&i<a+e)return s;return 0}var mi=class{constructor(){h(this,"_pointContainer"),h(this,"_pointArray",[]),h(this,"_i32idx",{}),this._pointContainer=new ni,this._pointContainer.setHeight(1),this._pointArray=this._pointContainer.getPointArray()}add(i){this._pointArray.push(i),this._pointContainer.setWidth(this._pointArray.length)}has(i){for(let t=this._pointArray.length-1;t>=0;t--)if(i.uint32===this._pointArray[t].uint32)return!0;return!1}getNearestColor(i,t){return this._pointArray[0|this._getNearestIndex(i,t)]}getPointContainer(){return this._pointContainer}_nearestPointFromCache(i){return"number"==typeof this._i32idx[i]?this._i32idx[i]:-1}_getNearestIndex(i,t){let e=this._nearestPointFromCache(""+t.uint32);if(e>=0)return e;let s=Number.MAX_VALUE;e=0;for(let a=0,r=this._pointArray.length;a<r;a++){const r=this._pointArray[a],n=i.calculateRaw(t.r,t.g,t.b,t.a,r.r,r.g,r.b,r.a);n<s&&(s=n,e=a)}return this._i32idx[t.uint32]=e,e}sort(){this._i32idx={},this._pointArray.sort(((i,t)=>{const e=z(i.r,i.g,i.b),s=z(t.r,t.g,t.b),a=i.r===i.g&&i.g===i.b?0:1+hi(e.h,10),r=(t.r===t.g&&t.g===t.b?0:1+hi(s.h,10))-a;if(r)return-r;const n=i.getLuminosity(!0),h=t.getLuminosity(!0);if(h-n!=0)return h-n;const m=(100*s.s|0)-(100*e.s|0);return m?-m:0}))}},ui={};r(ui,{HueStatistics:()=>li,Palette:()=>mi,Point:()=>ri,PointContainer:()=>ni,ProgressTracker:()=>ci,arithmetic:()=>p});var oi=class{constructor(){h(this,"num",0),h(this,"cols",[])}},li=class{constructor(i,t){h(this,"_numGroups"),h(this,"_minCols"),h(this,"_stats"),h(this,"_groupsFull"),this._numGroups=i,this._minCols=t,this._stats=[];for(let t=0;t<=i;t++)this._stats[t]=new oi;this._groupsFull=0}check(i){this._groupsFull===this._numGroups+1&&(this.check=()=>{});const t=255&i,e=i>>>8&255,s=i>>>16&255,a=t===e&&e===s?0:1+hi(z(t,e,s).h,this._numGroups),r=this._stats[a],n=this._minCols;r.num++,r.num>n||(r.num===n&&this._groupsFull++,r.num<=n&&this._stats[a].cols.push(i))}injectIntoDictionary(i){for(let t=0;t<=this._numGroups;t++)this._stats[t].num<=this._minCols&&this._stats[t].cols.forEach((t=>{i[t]?i[t]++:i[t]=1}))}injectIntoArray(i){for(let t=0;t<=this._numGroups;t++)this._stats[t].num<=this._minCols&&this._stats[t].cols.forEach((t=>{-1===i.indexOf(t)&&i.push(t)}))}},_i=class{constructor(i,t){h(this,"progress"),h(this,"_step"),h(this,"_range"),h(this,"_last"),h(this,"_progressRange"),this._range=i,this._progressRange=t,this._step=Math.max(1,this._range/(_i.steps+1)|0),this._last=-this._step,this.progress=0}shouldNotify(i){return i-this._last>=this._step&&(this._last=i,this.progress=Math.min(this._progressRange*this._last/this._range,this._progressRange),!0)}},ci=_i;h(ci,"steps",100);var di=class{constructor(i){h(this,"r"),h(this,"g"),h(this,"b"),h(this,"a"),this.r=this.g=this.b=this.a=i}toPoint(){return ri.createByRGBA(this.r>>3,this.g>>3,this.b>>3,this.a>>3)}subtract(i,t,e,s){this.r-=0|i,this.g-=0|t,this.b-=0|e,this.a-=0|s}},gi=class extends ai{constructor(i,t=256){super(),h(this,"_pointArray"),h(this,"_networkSize"),h(this,"_network"),h(this,"_sampleFactor"),h(this,"_radPower"),h(this,"_freq"),h(this,"_bias"),h(this,"_distance"),this._distance=i,this._pointArray=[],this._sampleFactor=1,this._networkSize=t,this._distance.setWhitePoint(2040,2040,2040,2040)}sample(i){this._pointArray=this._pointArray.concat(i.getPointArray())}*quantize(){this._init(),yield*this._learn(),yield{palette:this._buildPalette(),progress:100}}_init(){this._freq=[],this._bias=[],this._radPower=[],this._network=[];for(let i=0;i<this._networkSize;i++)this._network[i]=new di((i<<11)/this._networkSize|0),this._freq[i]=gi._initialBias/this._networkSize|0,this._bias[i]=0}*_learn(){let i=this._sampleFactor;const t=this._pointArray.length;t<gi._minpicturebytes&&(i=1);const e=30+(i-1)/3|0,s=t/i|0;let a,r=s/gi._nCycles|0,n=gi._initAlpha,h=(this._networkSize>>3)*gi._radiusBias,m=h>>gi._radiusBiasShift;m<=1&&(m=0);for(let i=0;i<m;i++)this._radPower[i]=n*((m*m-i*i)*gi._radBias/(m*m))>>>0;a=t<gi._minpicturebytes?1:t%gi._prime1!=0?gi._prime1:t%gi._prime2!=0?gi._prime2:t%gi._prime3!=0?gi._prime3:gi._prime4;const u=new ci(s,99);for(let i=0,o=0;i<s;){u.shouldNotify(i)&&(yield{progress:u.progress});const s=this._pointArray[o],l=s.b<<3,_=s.g<<3,c=s.r<<3,d=s.a<<3,g=this._contest(l,_,c,d);if(this._alterSingle(n,g,l,_,c,d),0!==m&&this._alterNeighbour(m,g,l,_,c,d),o+=a,o>=t&&(o-=t),i++,0===r&&(r=1),i%r==0){n-=n/e|0,h-=h/gi._radiusDecrease|0,m=h>>gi._radiusBiasShift,m<=1&&(m=0);for(let i=0;i<m;i++)this._radPower[i]=n*((m*m-i*i)*gi._radBias/(m*m))>>>0}}}_buildPalette(){const i=new mi;return this._network.forEach((t=>{i.add(t.toPoint())})),i.sort(),i}_alterNeighbour(i,t,e,s,a,r){let n=t-i;n<-1&&(n=-1);let h=t+i;h>this._networkSize&&(h=this._networkSize);let m=t+1,u=t-1,o=1;for(;m<h||u>n;){const i=this._radPower[o++]/gi._alphaRadBias;if(m<h){const t=this._network[m++];t.subtract(i*(t.r-a),i*(t.g-s),i*(t.b-e),i*(t.a-r))}if(u>n){const t=this._network[u--];t.subtract(i*(t.r-a),i*(t.g-s),i*(t.b-e),i*(t.a-r))}}}_alterSingle(i,t,e,s,a,r){i/=gi._initAlpha;const n=this._network[t];n.subtract(i*(n.r-a),i*(n.g-s),i*(n.b-e),i*(n.a-r))}_contest(i,t,e,s){let a=~(1<<31),r=a,n=-1,h=n;for(let m=0;m<this._networkSize;m++){const u=this._network[m],o=8160*this._distance.calculateNormalized(u,{r:e,g:t,b:i,a:s})|0;o<a&&(a=o,n=m);const l=o-(this._bias[m]>>gi._initialBiasShift-3);l<r&&(r=l,h=m);const _=this._freq[m]>>gi._betaShift;this._freq[m]-=_,this._bias[m]+=_<<gi._gammaShift}return this._freq[n]+=gi._beta,this._bias[n]-=gi._betaGamma,h}},Mi=gi;h(Mi,"_prime1",499),h(Mi,"_prime2",491),h(Mi,"_prime3",487),h(Mi,"_prime4",503),h(Mi,"_minpicturebytes",gi._prime4),h(Mi,"_nCycles",100),h(Mi,"_initialBiasShift",16),h(Mi,"_initialBias",1<<gi._initialBiasShift),h(Mi,"_gammaShift",10),h(Mi,"_betaShift",10),h(Mi,"_beta",gi._initialBias>>gi._betaShift),h(Mi,"_betaGamma",gi._initialBias<<gi._gammaShift-gi._betaShift),h(Mi,"_radiusBiasShift",6),h(Mi,"_radiusBias",1<<gi._radiusBiasShift),h(Mi,"_radiusDecrease",30),h(Mi,"_alphaBiasShift",10),h(Mi,"_initAlpha",1<<gi._alphaBiasShift),h(Mi,"_radBiasShift",8),h(Mi,"_radBias",1<<gi._radBiasShift),h(Mi,"_alphaRadBiasShift",gi._alphaBiasShift+gi._radBiasShift),h(Mi,"_alphaRadBias",1<<gi._alphaRadBiasShift);var pi=class{constructor(i){h(this,"r"),h(this,"g"),h(this,"b"),h(this,"a"),this.r=this.g=this.b=this.a=i}toPoint(){return ri.createByRGBA(this.r>>3,this.g>>3,this.b>>3,this.a>>3)}subtract(i,t,e,s){this.r-=i,this.g-=t,this.b-=e,this.a-=s}},bi=class extends ai{constructor(i,t=256){super(),h(this,"_pointArray"),h(this,"_networkSize"),h(this,"_network"),h(this,"_sampleFactor"),h(this,"_radPower"),h(this,"_freq"),h(this,"_bias"),h(this,"_distance"),this._distance=i,this._pointArray=[],this._sampleFactor=1,this._networkSize=t,this._distance.setWhitePoint(2040,2040,2040,2040)}sample(i){this._pointArray=this._pointArray.concat(i.getPointArray())}*quantize(){this._init(),yield*this._learn(),yield{palette:this._buildPalette(),progress:100}}_init(){this._freq=[],this._bias=[],this._radPower=[],this._network=[];for(let i=0;i<this._networkSize;i++)this._network[i]=new pi((i<<11)/this._networkSize),this._freq[i]=bi._initialBias/this._networkSize,this._bias[i]=0}*_learn(){let i=this._sampleFactor;const t=this._pointArray.length;t<bi._minpicturebytes&&(i=1);const e=30+(i-1)/3,s=t/i;let a,r=s/bi._nCycles|0,n=bi._initAlpha,h=(this._networkSize>>3)*bi._radiusBias,m=h>>bi._radiusBiasShift;m<=1&&(m=0);for(let i=0;i<m;i++)this._radPower[i]=n*((m*m-i*i)*bi._radBias/(m*m));a=t<bi._minpicturebytes?1:t%bi._prime1!=0?bi._prime1:t%bi._prime2!=0?bi._prime2:t%bi._prime3!=0?bi._prime3:bi._prime4;const u=new ci(s,99);for(let i=0,o=0;i<s;){u.shouldNotify(i)&&(yield{progress:u.progress});const s=this._pointArray[o],l=s.b<<3,_=s.g<<3,c=s.r<<3,d=s.a<<3,g=this._contest(l,_,c,d);if(this._alterSingle(n,g,l,_,c,d),0!==m&&this._alterNeighbour(m,g,l,_,c,d),o+=a,o>=t&&(o-=t),i++,0===r&&(r=1),i%r==0){n-=n/e,h-=h/bi._radiusDecrease,m=h>>bi._radiusBiasShift,m<=1&&(m=0);for(let i=0;i<m;i++)this._radPower[i]=n*((m*m-i*i)*bi._radBias/(m*m))}}}_buildPalette(){const i=new mi;return this._network.forEach((t=>{i.add(t.toPoint())})),i.sort(),i}_alterNeighbour(i,t,e,s,a,r){let n=t-i;n<-1&&(n=-1);let h=t+i;h>this._networkSize&&(h=this._networkSize);let m=t+1,u=t-1,o=1;for(;m<h||u>n;){const i=this._radPower[o++]/bi._alphaRadBias;if(m<h){const t=this._network[m++];t.subtract(i*(t.r-a),i*(t.g-s),i*(t.b-e),i*(t.a-r))}if(u>n){const t=this._network[u--];t.subtract(i*(t.r-a),i*(t.g-s),i*(t.b-e),i*(t.a-r))}}}_alterSingle(i,t,e,s,a,r){i/=bi._initAlpha;const n=this._network[t];n.subtract(i*(n.r-a),i*(n.g-s),i*(n.b-e),i*(n.a-r))}_contest(i,t,e,s){let a=~(1<<31),r=a,n=-1,h=n;for(let m=0;m<this._networkSize;m++){const u=this._network[m],o=8160*this._distance.calculateNormalized(u,{r:e,g:t,b:i,a:s});o<a&&(a=o,n=m);const l=o-(this._bias[m]>>bi._initialBiasShift-3);l<r&&(r=l,h=m);const _=this._freq[m]>>bi._betaShift;this._freq[m]-=_,this._bias[m]+=_<<bi._gammaShift}return this._freq[n]+=bi._beta,this._bias[n]-=bi._betaGamma,h}},fi=bi;h(fi,"_prime1",499),h(fi,"_prime2",491),h(fi,"_prime3",487),h(fi,"_prime4",503),h(fi,"_minpicturebytes",bi._prime4),h(fi,"_nCycles",100),h(fi,"_initialBiasShift",16),h(fi,"_initialBias",1<<bi._initialBiasShift),h(fi,"_gammaShift",10),h(fi,"_betaShift",10),h(fi,"_beta",bi._initialBias>>bi._betaShift),h(fi,"_betaGamma",bi._initialBias<<bi._gammaShift-bi._betaShift),h(fi,"_radiusBiasShift",6),h(fi,"_radiusBias",1<<bi._radiusBiasShift),h(fi,"_radiusDecrease",30),h(fi,"_alphaBiasShift",10),h(fi,"_initAlpha",1<<bi._alphaBiasShift),h(fi,"_radBiasShift",8),h(fi,"_radBias",1<<bi._radBiasShift),h(fi,"_alphaRadBiasShift",bi._alphaBiasShift+bi._radBiasShift),h(fi,"_alphaRadBias",1<<bi._alphaRadBiasShift);var xi=class{constructor(i,t){h(this,"_method"),h(this,"_hueStats"),h(this,"_histogram"),h(this,"_initColors"),h(this,"_minHueCols"),this._method=i,this._minHueCols=t<<2,this._initColors=t<<2,this._hueStats=new li(xi._hueGroups,this._minHueCols),this._histogram=Object.create(null)}sample(i){switch(this._method){case 1:this._colorStats1D(i);break;case 2:this._colorStats2D(i)}}getImportanceSortedColorsIDXI32(){const i=k(Object.keys(this._histogram),((i,t)=>this._histogram[t]-this._histogram[i]));if(0===i.length)return[];let t;switch(this._method){case 1:const e=Math.min(i.length,this._initColors),s=i[e-1],a=this._histogram[s];t=i.slice(0,e);let r=e;const n=i.length;for(;r<n&&this._histogram[i[r]]===a;)t.push(i[r++]);this._hueStats.injectIntoArray(t);break;case 2:t=i;break;default:throw new Error("Incorrect method")}return t.map((i=>+i))}_colorStats1D(i){const t=this._histogram,e=i.getPointArray(),s=e.length;for(let i=0;i<s;i++){const s=e[i].uint32;this._hueStats.check(s),s in t?t[s]++:t[s]=1}}_colorStats2D(i){const t=i.getWidth(),e=i.getHeight(),s=i.getPointArray(),a=xi._boxSize[0],r=xi._boxSize[1],n=a*r,h=this._makeBoxes(t,e,a,r),m=this._histogram;h.forEach((i=>{let e=Math.round(i.w*i.h/n)*xi._boxPixels;e<2&&(e=2);const a={};this._iterateBox(i,t,(i=>{const t=s[i].uint32;this._hueStats.check(t),t in m?m[t]++:t in a?++a[t]>=e&&(m[t]=a[t]):a[t]=1}))})),this._hueStats.injectIntoDictionary(m)}_iterateBox(i,t,e){const s=i,a=s.y*t+s.x,r=(s.y+s.h-1)*t+(s.x+s.w-1),n=t-s.w+1;let h=0,m=a;do{e.call(this,m),m+=++h%s.w==0?n:1}while(m<=r)}_makeBoxes(i,t,e,s){const a=i%e,r=t%s,n=i-a,h=t-r,m=[];for(let u=0;u<t;u+=s)for(let t=0;t<i;t+=e)m.push({x:t,y:u,w:t===n?a:e,h:u===h?r:s});return m}},wi=xi;h(wi,"_boxSize",[64,64]),h(wi,"_boxPixels",2),h(wi,"_hueGroups",10);var Si=class{constructor(i,t,e){h(this,"index"),h(this,"color"),h(this,"distance"),this.index=i,this.color=t,this.distance=e}},yi=class extends ai{constructor(i,t=256,e=2){super(),h(this,"_colors"),h(this,"_initialDistance"),h(this,"_distanceIncrement"),h(this,"_histogram"),h(this,"_distance"),this._distance=i,this._colors=t,this._histogram=new wi(e,t),this._initialDistance=.01,this._distanceIncrement=.005}sample(i){this._histogram.sample(i)}*quantize(){const i=this._histogram.getImportanceSortedColorsIDXI32();if(0===i.length)throw new Error("No colors in image");yield*this._buildPalette(i)}*_buildPalette(i){const t=new mi,e=t.getPointContainer().getPointArray(),s=new Array(i.length);for(let t=0;t<i.length;t++)e.push(ri.createByUint32(i[t])),s[t]=1;const a=e.length,r=[];let n=a,h=this._initialDistance;const m=new ci(n-this._colors,99);for(;n>this._colors;){r.length=0;for(let i=0;i<a;i++){if(m.shouldNotify(a-n)&&(yield{progress:m.progress}),0===s[i])continue;const t=e[i];for(let m=i+1;m<a;m++){if(0===s[m])continue;const i=e[m],a=this._distance.calculateNormalized(t,i);a<h&&(r.push(new Si(m,i,a)),s[m]=0,n--)}}h+=n>3*this._colors?this._initialDistance:this._distanceIncrement}if(n<this._colors){k(r,((i,t)=>t.distance-i.distance));let i=0;for(;n<this._colors&&i<r.length;)s[r[i].index]=1,n++,i++}let u=e.length;for(let i=u-1;i>=0;i--)0===s[i]&&(i!==u-1&&(e[i]=e[u-1]),--u);e.length=u,t.sort(),yield{palette:t,progress:100}}};function ki(i){const t=[];for(let e=0;e<i;e++)t[e]=0;return t}function zi(i,t,e,s){const a=new Array(i);for(let r=0;r<i;r++){a[r]=new Array(t);for(let i=0;i<t;i++){a[r][i]=new Array(e);for(let t=0;t<e;t++){a[r][i][t]=new Array(s);for(let e=0;e<s;e++)a[r][i][t][e]=0}}}return a}function Ai(i,t,e){const s=new Array(i);for(let a=0;a<i;a++){s[a]=new Array(t);for(let i=0;i<t;i++){s[a][i]=new Array(e);for(let t=0;t<e;t++)s[a][i][t]=0}}return s}function Bi(i,t,e,s,a){for(let r=0;r<t;r++){i[r]=[];for(let t=0;t<e;t++){i[r][t]=[];for(let e=0;e<s;e++)i[r][t][e]=a}}}function Pi(i,t,e){for(let s=0;s<t;s++)i[s]=e}var Ri=class{constructor(){h(this,"redMinimum"),h(this,"redMaximum"),h(this,"greenMinimum"),h(this,"greenMaximum"),h(this,"blueMinimum"),h(this,"blueMaximum"),h(this,"volume"),h(this,"alphaMinimum"),h(this,"alphaMaximum")}},Ii=class extends ai{constructor(i,t=256,e=5){super(),h(this,"_reds"),h(this,"_greens"),h(this,"_blues"),h(this,"_alphas"),h(this,"_sums"),h(this,"_weights"),h(this,"_momentsRed"),h(this,"_momentsGreen"),h(this,"_momentsBlue"),h(this,"_momentsAlpha"),h(this,"_moments"),h(this,"_table"),h(this,"_pixels"),h(this,"_cubes"),h(this,"_colors"),h(this,"_significantBitsPerChannel"),h(this,"_maxSideIndex"),h(this,"_alphaMaxSideIndex"),h(this,"_sideSize"),h(this,"_alphaSideSize"),h(this,"_distance"),this._distance=i,this._setQuality(e),this._initialize(t)}sample(i){const t=i.getPointArray();for(let i=0,e=t.length;i<e;i++)this._addColor(t[i]);this._pixels=this._pixels.concat(t)}*quantize(){yield*this._preparePalette();const i=new mi;for(let t=0;t<this._colors;t++)if(this._sums[t]>0){const e=this._sums[t],s=this._reds[t]/e,a=this._greens[t]/e,r=this._blues[t]/e,n=this._alphas[t]/e,h=ri.createByRGBA(0|s,0|a,0|r,0|n);i.add(h)}i.sort(),yield{palette:i,progress:100}}*_preparePalette(){yield*this._calculateMoments();let i=0;const t=ki(this._colors);for(let e=1;e<this._colors;++e){this._cut(this._cubes[i],this._cubes[e])?(t[i]=this._cubes[i].volume>1?this._calculateVariance(this._cubes[i]):0,t[e]=this._cubes[e].volume>1?this._calculateVariance(this._cubes[e]):0):(t[i]=0,e--),i=0;let s=t[0];for(let a=1;a<=e;++a)t[a]>s&&(s=t[a],i=a);if(s<=0){this._colors=e+1;break}}const e=[],s=[],a=[],r=[];for(let i=0;i<this._colors;++i){const t=Ii._volume(this._cubes[i],this._weights);t>0?(e[i]=Ii._volume(this._cubes[i],this._momentsRed)/t|0,s[i]=Ii._volume(this._cubes[i],this._momentsGreen)/t|0,a[i]=Ii._volume(this._cubes[i],this._momentsBlue)/t|0,r[i]=Ii._volume(this._cubes[i],this._momentsAlpha)/t|0):(e[i]=0,s[i]=0,a[i]=0,r[i]=0)}this._reds=ki(this._colors+1),this._greens=ki(this._colors+1),this._blues=ki(this._colors+1),this._alphas=ki(this._colors+1),this._sums=ki(this._colors+1);for(let i=0,t=this._pixels.length;i<t;i++){const t=this._pixels[i];let n=-1,h=Number.MAX_VALUE;for(let i=0;i<this._colors;i++){const m=e[i],u=s[i],o=a[i],l=r[i],_=this._distance.calculateRaw(m,u,o,l,t.r,t.g,t.b,t.a);_<h&&(h=_,n=i)}this._reds[n]+=t.r,this._greens[n]+=t.g,this._blues[n]+=t.b,this._alphas[n]+=t.a,this._sums[n]++}}_addColor(i){const t=8-this._significantBitsPerChannel,e=1+(i.r>>t),s=1+(i.g>>t),a=1+(i.b>>t),r=1+(i.a>>t);this._weights[r][e][s][a]++,this._momentsRed[r][e][s][a]+=i.r,this._momentsGreen[r][e][s][a]+=i.g,this._momentsBlue[r][e][s][a]+=i.b,this._momentsAlpha[r][e][s][a]+=i.a,this._moments[r][e][s][a]+=this._table[i.r]+this._table[i.g]+this._table[i.b]+this._table[i.a]}*_calculateMoments(){const i=[],t=[],e=[],s=[],a=[],r=[],n=Ai(this._sideSize,this._sideSize,this._sideSize),h=Ai(this._sideSize,this._sideSize,this._sideSize),m=Ai(this._sideSize,this._sideSize,this._sideSize),u=Ai(this._sideSize,this._sideSize,this._sideSize),o=Ai(this._sideSize,this._sideSize,this._sideSize),l=Ai(this._sideSize,this._sideSize,this._sideSize);let _=0;const c=new ci(this._alphaMaxSideIndex*this._maxSideIndex,99);for(let d=1;d<=this._alphaMaxSideIndex;++d){Bi(n,this._sideSize,this._sideSize,this._sideSize,0),Bi(h,this._sideSize,this._sideSize,this._sideSize,0),Bi(m,this._sideSize,this._sideSize,this._sideSize,0),Bi(u,this._sideSize,this._sideSize,this._sideSize,0),Bi(o,this._sideSize,this._sideSize,this._sideSize,0),Bi(l,this._sideSize,this._sideSize,this._sideSize,0);for(let g=1;g<=this._maxSideIndex;++g,++_){c.shouldNotify(_)&&(yield{progress:c.progress}),Pi(i,this._sideSize,0),Pi(t,this._sideSize,0),Pi(e,this._sideSize,0),Pi(s,this._sideSize,0),Pi(a,this._sideSize,0),Pi(r,this._sideSize,0);for(let _=1;_<=this._maxSideIndex;++_){let c=0,M=0,p=0,b=0,f=0,x=0;for(let w=1;w<=this._maxSideIndex;++w)c+=this._weights[d][g][_][w],M+=this._momentsRed[d][g][_][w],p+=this._momentsGreen[d][g][_][w],b+=this._momentsBlue[d][g][_][w],f+=this._momentsAlpha[d][g][_][w],x+=this._moments[d][g][_][w],i[w]+=c,t[w]+=M,e[w]+=p,s[w]+=b,a[w]+=f,r[w]+=x,n[g][_][w]=n[g-1][_][w]+i[w],h[g][_][w]=h[g-1][_][w]+t[w],m[g][_][w]=m[g-1][_][w]+e[w],u[g][_][w]=u[g-1][_][w]+s[w],o[g][_][w]=o[g-1][_][w]+a[w],l[g][_][w]=l[g-1][_][w]+r[w],this._weights[d][g][_][w]=this._weights[d-1][g][_][w]+n[g][_][w],this._momentsRed[d][g][_][w]=this._momentsRed[d-1][g][_][w]+h[g][_][w],this._momentsGreen[d][g][_][w]=this._momentsGreen[d-1][g][_][w]+m[g][_][w],this._momentsBlue[d][g][_][w]=this._momentsBlue[d-1][g][_][w]+u[g][_][w],this._momentsAlpha[d][g][_][w]=this._momentsAlpha[d-1][g][_][w]+o[g][_][w],this._moments[d][g][_][w]=this._moments[d-1][g][_][w]+l[g][_][w]}}}}static _volumeFloat(i,t){return t[i.alphaMaximum][i.redMaximum][i.greenMaximum][i.blueMaximum]-t[i.alphaMaximum][i.redMaximum][i.greenMinimum][i.blueMaximum]-t[i.alphaMaximum][i.redMinimum][i.greenMaximum][i.blueMaximum]+t[i.alphaMaximum][i.redMinimum][i.greenMinimum][i.blueMaximum]-t[i.alphaMinimum][i.redMaximum][i.greenMaximum][i.blueMaximum]+t[i.alphaMinimum][i.redMaximum][i.greenMinimum][i.blueMaximum]+t[i.alphaMinimum][i.redMinimum][i.greenMaximum][i.blueMaximum]-t[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMaximum]-(t[i.alphaMaximum][i.redMaximum][i.greenMaximum][i.blueMinimum]-t[i.alphaMinimum][i.redMaximum][i.greenMaximum][i.blueMinimum]-t[i.alphaMaximum][i.redMaximum][i.greenMinimum][i.blueMinimum]+t[i.alphaMinimum][i.redMaximum][i.greenMinimum][i.blueMinimum]-t[i.alphaMaximum][i.redMinimum][i.greenMaximum][i.blueMinimum]+t[i.alphaMinimum][i.redMinimum][i.greenMaximum][i.blueMinimum]+t[i.alphaMaximum][i.redMinimum][i.greenMinimum][i.blueMinimum]-t[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMinimum])}static _volume(i,t){return 0|Ii._volumeFloat(i,t)}static _top(i,t,e,s){let a;switch(t){case Ii._alpha:a=s[e][i.redMaximum][i.greenMaximum][i.blueMaximum]-s[e][i.redMaximum][i.greenMinimum][i.blueMaximum]-s[e][i.redMinimum][i.greenMaximum][i.blueMaximum]+s[e][i.redMinimum][i.greenMinimum][i.blueMaximum]-(s[e][i.redMaximum][i.greenMaximum][i.blueMinimum]-s[e][i.redMaximum][i.greenMinimum][i.blueMinimum]-s[e][i.redMinimum][i.greenMaximum][i.blueMinimum]+s[e][i.redMinimum][i.greenMinimum][i.blueMinimum]);break;case Ii._red:a=s[i.alphaMaximum][e][i.greenMaximum][i.blueMaximum]-s[i.alphaMaximum][e][i.greenMinimum][i.blueMaximum]-s[i.alphaMinimum][e][i.greenMaximum][i.blueMaximum]+s[i.alphaMinimum][e][i.greenMinimum][i.blueMaximum]-(s[i.alphaMaximum][e][i.greenMaximum][i.blueMinimum]-s[i.alphaMaximum][e][i.greenMinimum][i.blueMinimum]-s[i.alphaMinimum][e][i.greenMaximum][i.blueMinimum]+s[i.alphaMinimum][e][i.greenMinimum][i.blueMinimum]);break;case Ii._green:a=s[i.alphaMaximum][i.redMaximum][e][i.blueMaximum]-s[i.alphaMaximum][i.redMinimum][e][i.blueMaximum]-s[i.alphaMinimum][i.redMaximum][e][i.blueMaximum]+s[i.alphaMinimum][i.redMinimum][e][i.blueMaximum]-(s[i.alphaMaximum][i.redMaximum][e][i.blueMinimum]-s[i.alphaMaximum][i.redMinimum][e][i.blueMinimum]-s[i.alphaMinimum][i.redMaximum][e][i.blueMinimum]+s[i.alphaMinimum][i.redMinimum][e][i.blueMinimum]);break;case Ii._blue:a=s[i.alphaMaximum][i.redMaximum][i.greenMaximum][e]-s[i.alphaMaximum][i.redMaximum][i.greenMinimum][e]-s[i.alphaMaximum][i.redMinimum][i.greenMaximum][e]+s[i.alphaMaximum][i.redMinimum][i.greenMinimum][e]-(s[i.alphaMinimum][i.redMaximum][i.greenMaximum][e]-s[i.alphaMinimum][i.redMaximum][i.greenMinimum][e]-s[i.alphaMinimum][i.redMinimum][i.greenMaximum][e]+s[i.alphaMinimum][i.redMinimum][i.greenMinimum][e]);break;default:throw new Error("impossible")}return 0|a}static _bottom(i,t,e){switch(t){case Ii._alpha:return-e[i.alphaMinimum][i.redMaximum][i.greenMaximum][i.blueMaximum]+e[i.alphaMinimum][i.redMaximum][i.greenMinimum][i.blueMaximum]+e[i.alphaMinimum][i.redMinimum][i.greenMaximum][i.blueMaximum]-e[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMaximum]-(-e[i.alphaMinimum][i.redMaximum][i.greenMaximum][i.blueMinimum]+e[i.alphaMinimum][i.redMaximum][i.greenMinimum][i.blueMinimum]+e[i.alphaMinimum][i.redMinimum][i.greenMaximum][i.blueMinimum]-e[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMinimum]);case Ii._red:return-e[i.alphaMaximum][i.redMinimum][i.greenMaximum][i.blueMaximum]+e[i.alphaMaximum][i.redMinimum][i.greenMinimum][i.blueMaximum]+e[i.alphaMinimum][i.redMinimum][i.greenMaximum][i.blueMaximum]-e[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMaximum]-(-e[i.alphaMaximum][i.redMinimum][i.greenMaximum][i.blueMinimum]+e[i.alphaMaximum][i.redMinimum][i.greenMinimum][i.blueMinimum]+e[i.alphaMinimum][i.redMinimum][i.greenMaximum][i.blueMinimum]-e[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMinimum]);case Ii._green:return-e[i.alphaMaximum][i.redMaximum][i.greenMinimum][i.blueMaximum]+e[i.alphaMaximum][i.redMinimum][i.greenMinimum][i.blueMaximum]+e[i.alphaMinimum][i.redMaximum][i.greenMinimum][i.blueMaximum]-e[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMaximum]-(-e[i.alphaMaximum][i.redMaximum][i.greenMinimum][i.blueMinimum]+e[i.alphaMaximum][i.redMinimum][i.greenMinimum][i.blueMinimum]+e[i.alphaMinimum][i.redMaximum][i.greenMinimum][i.blueMinimum]-e[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMinimum]);case Ii._blue:return-e[i.alphaMaximum][i.redMaximum][i.greenMaximum][i.blueMinimum]+e[i.alphaMaximum][i.redMaximum][i.greenMinimum][i.blueMinimum]+e[i.alphaMaximum][i.redMinimum][i.greenMaximum][i.blueMinimum]-e[i.alphaMaximum][i.redMinimum][i.greenMinimum][i.blueMinimum]-(-e[i.alphaMinimum][i.redMaximum][i.greenMaximum][i.blueMinimum]+e[i.alphaMinimum][i.redMaximum][i.greenMinimum][i.blueMinimum]+e[i.alphaMinimum][i.redMinimum][i.greenMaximum][i.blueMinimum]-e[i.alphaMinimum][i.redMinimum][i.greenMinimum][i.blueMinimum]);default:return 0}}_calculateVariance(i){const t=Ii._volume(i,this._momentsRed),e=Ii._volume(i,this._momentsGreen),s=Ii._volume(i,this._momentsBlue),a=Ii._volume(i,this._momentsAlpha);return Ii._volumeFloat(i,this._moments)-(t*t+e*e+s*s+a*a)/Ii._volume(i,this._weights)}_maximize(i,t,e,s,a,r,n,h,m){const u=0|Ii._bottom(i,t,this._momentsRed),o=0|Ii._bottom(i,t,this._momentsGreen),l=0|Ii._bottom(i,t,this._momentsBlue),_=0|Ii._bottom(i,t,this._momentsAlpha),c=0|Ii._bottom(i,t,this._weights);let d=0,g=-1;for(let M=e;M<s;++M){let e=u+Ii._top(i,t,M,this._momentsRed),s=o+Ii._top(i,t,M,this._momentsGreen),p=l+Ii._top(i,t,M,this._momentsBlue),b=_+Ii._top(i,t,M,this._momentsAlpha),f=c+Ii._top(i,t,M,this._weights);if(0!==f){let i=e*e+s*s+p*p+b*b,t=i/f;e=a-e,s=r-s,p=n-p,b=h-b,f=m-f,0!==f&&(i=e*e+s*s+p*p+b*b,t+=i/f,t>d&&(d=t,g=M))}}return{max:d,position:g}}_cut(i,t){let e;const s=Ii._volume(i,this._momentsRed),a=Ii._volume(i,this._momentsGreen),r=Ii._volume(i,this._momentsBlue),n=Ii._volume(i,this._momentsAlpha),h=Ii._volume(i,this._weights),m=this._maximize(i,Ii._red,i.redMinimum+1,i.redMaximum,s,a,r,n,h),u=this._maximize(i,Ii._green,i.greenMinimum+1,i.greenMaximum,s,a,r,n,h),o=this._maximize(i,Ii._blue,i.blueMinimum+1,i.blueMaximum,s,a,r,n,h),l=this._maximize(i,Ii._alpha,i.alphaMinimum+1,i.alphaMaximum,s,a,r,n,h);if(l.max>=m.max&&l.max>=u.max&&l.max>=o.max){if(e=Ii._alpha,l.position<0)return!1}else e=m.max>=l.max&&m.max>=u.max&&m.max>=o.max?Ii._red:u.max>=l.max&&u.max>=m.max&&u.max>=o.max?Ii._green:Ii._blue;switch(t.redMaximum=i.redMaximum,t.greenMaximum=i.greenMaximum,t.blueMaximum=i.blueMaximum,t.alphaMaximum=i.alphaMaximum,e){case Ii._red:t.redMinimum=i.redMaximum=m.position,t.greenMinimum=i.greenMinimum,t.blueMinimum=i.blueMinimum,t.alphaMinimum=i.alphaMinimum;break;case Ii._green:t.greenMinimum=i.greenMaximum=u.position,t.redMinimum=i.redMinimum,t.blueMinimum=i.blueMinimum,t.alphaMinimum=i.alphaMinimum;break;case Ii._blue:t.blueMinimum=i.blueMaximum=o.position,t.redMinimum=i.redMinimum,t.greenMinimum=i.greenMinimum,t.alphaMinimum=i.alphaMinimum;break;case Ii._alpha:t.alphaMinimum=i.alphaMaximum=l.position,t.blueMinimum=i.blueMinimum,t.redMinimum=i.redMinimum,t.greenMinimum=i.greenMinimum}return i.volume=(i.redMaximum-i.redMinimum)*(i.greenMaximum-i.greenMinimum)*(i.blueMaximum-i.blueMinimum)*(i.alphaMaximum-i.alphaMinimum),t.volume=(t.redMaximum-t.redMinimum)*(t.greenMaximum-t.greenMinimum)*(t.blueMaximum-t.blueMinimum)*(t.alphaMaximum-t.alphaMinimum),!0}_initialize(i){this._colors=i,this._cubes=[];for(let t=0;t<i;t++)this._cubes[t]=new Ri;this._cubes[0].redMinimum=0,this._cubes[0].greenMinimum=0,this._cubes[0].blueMinimum=0,this._cubes[0].alphaMinimum=0,this._cubes[0].redMaximum=this._maxSideIndex,this._cubes[0].greenMaximum=this._maxSideIndex,this._cubes[0].blueMaximum=this._maxSideIndex,this._cubes[0].alphaMaximum=this._alphaMaxSideIndex,this._weights=zi(this._alphaSideSize,this._sideSize,this._sideSize,this._sideSize),this._momentsRed=zi(this._alphaSideSize,this._sideSize,this._sideSize,this._sideSize),this._momentsGreen=zi(this._alphaSideSize,this._sideSize,this._sideSize,this._sideSize),this._momentsBlue=zi(this._alphaSideSize,this._sideSize,this._sideSize,this._sideSize),this._momentsAlpha=zi(this._alphaSideSize,this._sideSize,this._sideSize,this._sideSize),this._moments=zi(this._alphaSideSize,this._sideSize,this._sideSize,this._sideSize),this._table=[];for(let i=0;i<256;++i)this._table[i]=i*i;this._pixels=[]}_setQuality(i=5){this._significantBitsPerChannel=i,this._maxSideIndex=1<<this._significantBitsPerChannel,this._alphaMaxSideIndex=this._maxSideIndex,this._sideSize=this._maxSideIndex+1,this._alphaSideSize=this._alphaMaxSideIndex+1}},Ei=Ii;h(Ei,"_alpha",3),h(Ei,"_red",2),h(Ei,"_green",1),h(Ei,"_blue",0);var vi={};r(vi,{AbstractImageQuantizer:()=>Ci,ErrorDiffusionArray:()=>qi,ErrorDiffusionArrayKernel:()=>Gi,ErrorDiffusionRiemersma:()=>Wi,NearestColor:()=>Di});var Ci=class{quantizeSync(i,t){for(const e of this.quantize(i,t))if(e.pointContainer)return e.pointContainer;throw new Error("unreachable")}},Di=class extends Ci{constructor(i){super(),h(this,"_distance"),this._distance=i}*quantize(i,t){const e=i.getPointArray(),s=i.getWidth(),a=i.getHeight(),r=new ci(a,99);for(let i=0;i<a;i++){r.shouldNotify(i)&&(yield{progress:r.progress});for(let a=0,r=i*s;a<s;a++,r++){const i=e[r];i.from(t.getNearestColor(this._distance,i))}}yield{pointContainer:i,progress:100}}},Gi=(i=>(i[i.FloydSteinberg=0]="FloydSteinberg",i[i.FalseFloydSteinberg=1]="FalseFloydSteinberg",i[i.Stucki=2]="Stucki",i[i.Atkinson=3]="Atkinson",i[i.Jarvis=4]="Jarvis",i[i.Burkes=5]="Burkes",i[i.Sierra=6]="Sierra",i[i.TwoSierra=7]="TwoSierra",i[i.SierraLite=8]="SierraLite",i))(Gi||{}),qi=class extends Ci{constructor(i,t,e=!0,s=0,a=!1){super(),h(this,"_minColorDistance"),h(this,"_serpentine"),h(this,"_kernel"),h(this,"_calculateErrorLikeGIMP"),h(this,"_distance"),this._setKernel(t),this._distance=i,this._minColorDistance=s,this._serpentine=e,this._calculateErrorLikeGIMP=a}*quantize(i,t){const e=i.getPointArray(),s=new ri,a=i.getWidth(),r=i.getHeight(),n=[];let h=1,m=1;for(const i of this._kernel){const t=i[2]+1;m<t&&(m=t)}for(let i=0;i<m;i++)this._fillErrorLine(n[i]=[],a);const u=new ci(r,99);for(let i=0;i<r;i++){u.shouldNotify(i)&&(yield{progress:u.progress}),this._serpentine&&(h*=-1);const m=i*a,o=1===h?0:a-1,l=1===h?a:-1;this._fillErrorLine(n[0],a),n.push(n.shift());const _=n[0];for(let u=o,c=m+o;u!==l;u+=h,c+=h){const m=e[c],o=_[u];s.from(m);const l=ri.createByRGBA(S(m.r+o[0]),S(m.g+o[1]),S(m.b+o[2]),S(m.a+o[3])),d=t.getNearestColor(this._distance,l);if(m.from(d),this._minColorDistance&&this._distance.calculateNormalized(s,d)<this._minColorDistance)continue;let g,M,p,b;this._calculateErrorLikeGIMP?(g=l.r-d.r,M=l.g-d.g,p=l.b-d.b,b=l.a-d.a):(g=s.r-d.r,M=s.g-d.g,p=s.b-d.b,b=s.a-d.a);const f=1===h?0:this._kernel.length-1,x=1===h?this._kernel.length:-1;for(let t=f;t!==x;t+=h){const e=this._kernel[t][1]*h,s=this._kernel[t][2];if(e+u>=0&&e+u<a&&s+i>=0&&s+i<r){const i=this._kernel[t][0],a=n[s][e+u];a[0]+=g*i,a[1]+=M*i,a[2]+=p*i,a[3]+=b*i}}}}yield{pointContainer:i,progress:100}}_fillErrorLine(i,t){i.length>t&&(i.length=t);const e=i.length;for(let t=0;t<e;t++){const e=i[t];e[0]=e[1]=e[2]=e[3]=0}for(let s=e;s<t;s++)i[s]=[0,0,0,0]}_setKernel(i){switch(i){case 0:this._kernel=[[7/16,1,0],[3/16,-1,1],[5/16,0,1],[1/16,1,1]];break;case 1:this._kernel=[[3/8,1,0],[3/8,0,1],[2/8,1,1]];break;case 2:this._kernel=[[8/42,1,0],[4/42,2,0],[2/42,-2,1],[4/42,-1,1],[8/42,0,1],[4/42,1,1],[2/42,2,1],[1/42,-2,2],[2/42,-1,2],[4/42,0,2],[2/42,1,2],[1/42,2,2]];break;case 3:this._kernel=[[1/8,1,0],[1/8,2,0],[1/8,-1,1],[1/8,0,1],[1/8,1,1],[1/8,0,2]];break;case 4:this._kernel=[[7/48,1,0],[5/48,2,0],[3/48,-2,1],[5/48,-1,1],[7/48,0,1],[5/48,1,1],[3/48,2,1],[1/48,-2,2],[3/48,-1,2],[5/48,0,2],[3/48,1,2],[1/48,2,2]];break;case 5:this._kernel=[[.25,1,0],[4/32,2,0],[2/32,-2,1],[4/32,-1,1],[.25,0,1],[4/32,1,1],[2/32,2,1]];break;case 6:this._kernel=[[5/32,1,0],[3/32,2,0],[2/32,-2,1],[4/32,-1,1],[5/32,0,1],[4/32,1,1],[2/32,2,1],[2/32,-1,2],[3/32,0,2],[2/32,1,2]];break;case 7:this._kernel=[[.25,1,0],[3/16,2,0],[1/16,-2,1],[2/16,-1,1],[3/16,0,1],[2/16,1,1],[1/16,2,1]];break;case 8:this._kernel=[[.5,1,0],[1/4,-1,1],[1/4,0,1]];break;default:throw new Error(`ErrorDiffusionArray: unknown kernel = ${i}`)}}};function*Ni(i,t,e){const s=Math.max(i,t),a={width:i,height:t,level:Math.floor(Math.log(s)/Math.log(2)+1),callback:e,tracker:new ci(i*t,99),index:0,x:0,y:0};yield*Ui(a,1),Li(a,0)}function*Ui(i,t){if(!(i.level<1)){switch(i.tracker.shouldNotify(i.index)&&(yield{progress:i.tracker.progress}),i.level--,t){case 2:yield*Ui(i,1),Li(i,3),yield*Ui(i,2),Li(i,4),yield*Ui(i,2),Li(i,2),yield*Ui(i,4);break;case 3:yield*Ui(i,4),Li(i,2),yield*Ui(i,3),Li(i,1),yield*Ui(i,3),Li(i,3),yield*Ui(i,1);break;case 1:yield*Ui(i,2),Li(i,4),yield*Ui(i,1),Li(i,3),yield*Ui(i,1),Li(i,1),yield*Ui(i,3);break;case 4:yield*Ui(i,3),Li(i,1),yield*Ui(i,4),Li(i,2),yield*Ui(i,4),Li(i,4),yield*Ui(i,2)}i.level++}}function Li(i,t){switch(i.x>=0&&i.x<i.width&&i.y>=0&&i.y<i.height&&(i.callback(i.x,i.y),i.index++),t){case 2:i.x--;break;case 3:i.x++;break;case 1:i.y--;break;case 4:i.y++}}var Wi=class extends Ci{constructor(i,t=16,e=1){super(),h(this,"_distance"),h(this,"_weights"),h(this,"_errorQueueSize"),this._distance=i,this._errorQueueSize=t,this._weights=Wi._createWeights(e,t)}*quantize(i,t){const e=i.getPointArray(),s=i.getWidth(),a=i.getHeight(),r=[];let n=0;for(let i=0;i<this._errorQueueSize;i++)r[i]={r:0,g:0,b:0,a:0};yield*Ni(s,a,((i,a)=>{const h=e[i+a*s];let{r:m,g:u,b:o,a:l}=h;for(let i=0;i<this._errorQueueSize;i++){const t=this._weights[i],e=r[(i+n)%this._errorQueueSize];m+=e.r*t,u+=e.g*t,o+=e.b*t,l+=e.a*t}const _=ri.createByRGBA(S(m),S(u),S(o),S(l)),c=t.getNearestColor(this._distance,_);n=(n+1)%this._errorQueueSize;const d=(n+this._errorQueueSize-1)%this._errorQueueSize;r[d].r=h.r-c.r,r[d].g=h.g-c.g,r[d].b=h.b-c.b,r[d].a=h.a-c.a,h.from(c)})),yield{pointContainer:i,progress:100}}static _createWeights(i,t){const e=[],s=Math.exp(Math.log(t)/(t-1));for(let a=0,r=1;a<t;a++)e[a]=(r+.5|0)/t*i,r*=s;return e}},Qi={};r(Qi,{ssim:()=>Hi});var Ti=.01,Fi=.03;function Hi(i,t){if(i.getHeight()!==t.getHeight()||i.getWidth()!==t.getWidth())throw new Error("Images have different sizes!");const e=(255*Ti)**2,s=(255*Fi)**2;let a=0,r=0;return function(i,t,e){const s=i.getWidth(),a=i.getHeight();for(let r=0;r<a;r+=8)for(let n=0;n<s;n+=8){const h=Math.min(8,s-n),m=Math.min(8,a-r),u=ji(i,n,r,h,m),o=ji(t,n,r,h,m);e(u,o,Oi(u),Oi(o))}}(i,t,((i,t,n,h)=>{let m=0,u=0,o=0;for(let e=0;e<i.length;e++)u+=(i[e]-n)**2,o+=(t[e]-h)**2,m+=(i[e]-n)*(t[e]-h);const l=i.length-1;u/=l,o/=l,m/=l,r+=(2*n*h+e)*(2*m+s)/((n**2+h**2+e)*(u+o+s)),a++})),r/a}function ji(i,t,e,s,a){const r=i.getPointArray(),n=[];let h=0;for(let m=e;m<e+a;m++){const e=m*i.getWidth();for(let i=t;i<t+s;i++){const t=r[e+i];n[h]=.2126*t.r+.7152*t.g+.0722*t.b,h++}}return n}function Oi(i){let t=0;for(const e of i)t+=e;return t/i.length}var Ki="function"==typeof setImmediate?setImmediate:"undefined"!=typeof process&&"function"==typeof(null==process?void 0:process.nextTick)?i=>process.nextTick(i):i=>setTimeout(i,0);function Vi(i,{colorDistanceFormula:t,paletteQuantization:e,colors:s}={}){const a=it(Yi(t),e,s);return i.forEach((i=>a.sample(i))),a.quantizeSync()}async function Xi(i,{colorDistanceFormula:t,paletteQuantization:e,colors:s,onProgress:a}={}){return new Promise(((r,n)=>{const h=it(Yi(t),e,s);let m;i.forEach((i=>h.sample(i)));const u=h.quantize(),o=()=>{try{const i=u.next();i.done?r(m):(i.value.palette&&(m=i.value.palette),a&&a(i.value.progress),Ki(o))}catch(i){n(i)}};Ki(o)}))}function $i(i,t,{colorDistanceFormula:e,imageQuantization:s}={}){return Zi(Yi(e),s).quantizeSync(i,t)}async function Ji(i,t,{colorDistanceFormula:e,imageQuantization:s,onProgress:a}={}){return new Promise(((r,n)=>{let h;const m=Zi(Yi(e),s).quantize(i,t),u=()=>{try{const i=m.next();i.done?r(h):(i.value.pointContainer&&(h=i.value.pointContainer),a&&a(i.value.progress),Ki(u))}catch(i){n(i)}};Ki(u)}))}function Yi(i="euclidean-bt709"){switch(i){case"cie94-graphic-arts":return new H;case"cie94-textiles":return new F;case"ciede2000":return new O;case"color-metric":return new K;case"euclidean":return new X;case"euclidean-bt709":return new $;case"euclidean-bt709-noalpha":return new J;case"manhattan":return new Z;case"manhattan-bt709":return new ti;case"manhattan-nommyde":return new ii;case"pngquant":return new ei;default:throw new Error(`Unknown colorDistanceFormula ${i}`)}}function Zi(i,t="floyd-steinberg"){switch(t){case"nearest":return new Di(i);case"riemersma":return new Wi(i);case"floyd-steinberg":return new qi(i,0);case"false-floyd-steinberg":return new qi(i,1);case"stucki":return new qi(i,2);case"atkinson":return new qi(i,3);case"jarvis":return new qi(i,4);case"burkes":return new qi(i,5);case"sierra":return new qi(i,6);case"two-sierra":return new qi(i,7);case"sierra-lite":return new qi(i,8);default:throw new Error(`Unknown imageQuantization ${t}`)}}function it(i,t="wuquant",e=256){switch(t){case"neuquant":return new Mi(i,e);case"rgbquant":return new yi(i,e);case"wuquant":return new Ei(i,e);case"neuquant-float":return new fi(i,e);default:throw new Error(`Unknown paletteQuantization ${t}`)}}i.exports=n(m)}}]);
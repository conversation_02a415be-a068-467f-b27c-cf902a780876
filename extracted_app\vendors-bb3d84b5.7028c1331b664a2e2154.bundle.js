/*! For license information please see vendors-bb3d84b5.7028c1331b664a2e2154.bundle.js.LICENSE.txt */
(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8375],{251:(t,e)=>{e.read=function(t,e,n,r,s){var i,o,c=8*s-r-1,a=(1<<c)-1,h=a>>1,l=-7,u=n?s-1:0,d=n?-1:1,g=t[e+u];for(u+=d,i=g&(1<<-l)-1,g>>=-l,l+=c;l>0;i=256*i+t[e+u],u+=d,l-=8);for(o=i&(1<<-l)-1,i>>=-l,l+=r;l>0;o=256*o+t[e+u],u+=d,l-=8);if(0===i)i=1-h;else{if(i===a)return o?NaN:1/0*(g?-1:1);o+=Math.pow(2,r),i-=h}return(g?-1:1)*o*Math.pow(2,i-r)},e.write=function(t,e,n,r,s,i){var o,c,a,h=8*i-s-1,l=(1<<h)-1,u=l>>1,d=23===s?Math.pow(2,-24)-Math.pow(2,-77):0,g=r?0:i-1,f=r?1:-1,p=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(c=isNaN(e)?1:0,o=l):(o=Math.floor(Math.log(e)/Math.LN2),e*(a=Math.pow(2,-o))<1&&(o--,a*=2),(e+=o+u>=1?d/a:d*Math.pow(2,1-u))*a>=2&&(o++,a/=2),o+u>=l?(c=0,o=l):o+u>=1?(c=(e*a-1)*Math.pow(2,s),o+=u):(c=e*Math.pow(2,u-1)*Math.pow(2,s),o=0));s>=8;t[n+g]=255&c,g+=f,c/=256,s-=8);for(o=o<<s|c,h+=s;h>0;t[n+g]=255&o,g+=f,o/=256,h-=8);t[n+g-f]|=128*p}},6287:(t,e,n)=>{"use strict";const r=n(43946),s=n(18106),i=n(64991),{MIME_PNG:o}=n(70474),c={isICO:s,parse:(t,e=o)=>i(t,e,r)};t.exports=c},10229:(t,e,n)=>{"use strict";t.exports=n(6287)},14385:t=>{"use strict";t.exports=function(t,e){return e||(e={}),t?(t=String(t.__esModule?t.default:t),e.hash&&(t+=e.hash),e.maybeNeedQuotes&&/[\t\n\f\r "'=<>`]/.test(t)?'"'.concat(t,'"'):t):t}},18106:(t,e,n)=>{"use strict";const r=n(32047);t.exports=t=>{const e=r(t);return 0===e.getUint16(0,!0)&&1===e.getUint16(2,!0)}},27058:(t,e,n)=>{"use strict";n.d(e,{A:()=>r});const r=2750==n.j?function(t,e,n,r){var s={};t=t||[],s.gramSizeLower=n||2,s.gramSizeUpper=r||3,s.useLevenshtein="boolean"!=typeof e||e,s.exactSet={},s.matchDict={},s.items={};var i=function(t,e){if(null===t&&null===e)throw"Trying to compare two null values";if(null===t||null===e)return 0;var n=function(t,e){for(var n,r,s=[],i=0;i<=e.length;i++)for(var o=0;o<=t.length;o++)r=i&&o?t.charAt(o-1)===e.charAt(i-1)?n:Math.min(s[o],s[o-1],n)+1:i+o,n=s[o],s[o]=r;return s.pop()}(t=String(t),e=String(e));return t.length>e.length?1-n/t.length:1-n/e.length},o=/[^a-zA-Z0-9\u00C0-\u00FF\u0621-\u064A\u0660-\u0669, ]+/g,c=function(t,e){for(var n={},r=function(t,e){e=e||2;var n="-"+t.toLowerCase().replace(o,"")+"-",r=e-n.length,s=[];if(r>0)for(var i=0;i<r;++i)n+="-";for(i=0;i<n.length-e+1;++i)s.push(n.slice(i,i+e));return s}(t,e=e||2),s=0;s<r.length;++s)r[s]in n?n[r[s]]+=1:n[r[s]]=1;return n};s.get=function(t,e,n){void 0===n&&(n=.33);var r=this._get(t,n);return r||void 0===e?r:e},s._get=function(t,e){for(var n=[],r=this.gramSizeUpper;r>=this.gramSizeLower;--r)if((n=this.__get(t,r,e))&&n.length>0)return n;return null},s.__get=function(t,e,n){var r,s,o,a,h=this._normalizeStr(t),l={},u=c(h,e),d=this.items[e],g=0;for(r in u)if(s=u[r],g+=Math.pow(s,2),r in this.matchDict)for(v=0;v<this.matchDict[r].length;++v)o=this.matchDict[r][v][0],a=this.matchDict[r][v][1],o in l?l[o]+=s*a:l[o]=s*a;if(function(t){for(var e in t)if(t.hasOwnProperty(e))return!1;return!0}(l))return null;var f,p=Math.sqrt(g),m=[];for(var M in l)f=l[M],m.push([f/(p*d[M][0]),d[M][1]]);var y=function(t,e){return t[0]<e[0]?1:t[0]>e[0]?-1:0};if(m.sort(y),this.useLevenshtein){for(var x=[],w=Math.min(50,m.length),v=0;v<w;++v)x.push([i(m[v][1],h),m[v][1]]);(m=x).sort(y)}return x=[],m.forEach(function(t){t[0]>=n&&x.push([t[0],this.exactSet[t[1]]])}.bind(this)),x},s.add=function(t){if(this._normalizeStr(t)in this.exactSet)return!1;for(var e=this.gramSizeLower;e<this.gramSizeUpper+1;++e)this._add(t,e)},s._add=function(t,e){var n=this._normalizeStr(t),r=this.items[e]||[],s=r.length;r.push(0);var i,o,a=c(n,e),h=0;for(i in a)o=a[i],h+=Math.pow(o,2),i in this.matchDict?this.matchDict[i].push([s,o]):this.matchDict[i]=[[s,o]];var l=Math.sqrt(h);r[s]=[l,n],this.items[e]=r,this.exactSet[n]=t},s._normalizeStr=function(t){if("[object String]"!==Object.prototype.toString.call(t))throw"Must use a string as argument to FuzzySet functions";return t.toLowerCase()},s.length=function(){var t,e=0;for(t in this.exactSet)this.exactSet.hasOwnProperty(t)&&(e+=1);return e},s.isEmpty=function(){for(var t in this.exactSet)if(this.exactSet.hasOwnProperty(t))return!1;return!0},s.values=function(){var t,e=[];for(t in this.exactSet)this.exactSet.hasOwnProperty(t)&&e.push(this.exactSet[t]);return e};for(var a=s.gramSizeLower;a<s.gramSizeUpper+1;++a)s.items[a]=[];for(a=0;a<t.length;++a)s.add(t[a]);return s}:null},43946:(t,e,n)=>{"use strict";const{MIME_PNG:r}=n(70474),s={decode:t=>new Promise((e=>{const n=URL.createObjectURL(new Blob([t])),r=document.createElement("img");r.src=n,r.onload=()=>{const{naturalHeight:t,naturalWidth:n}=r,s=document.createElement("canvas");s.width=n,s.height=t;const i=s.getContext("2d");i.drawImage(r,0,0);const{data:o}=i.getImageData(0,0,n,t);e({data:o,height:t,width:n})}})),encode:(t,e=r)=>new Promise((n=>{const{data:r,height:s,width:i}=t,o=document.createElement("canvas");o.width=i,o.height=s;const c=o.getContext("2d"),a=c.createImageData(i,s),h=a.data;for(let t=0;t<h.length;t++)h[t]=r[t];c.putImageData(a,0,0),n((t=>{const e=atob(t.replace(/.+,/u,"")),n=new Uint8Array(e.length);for(let t=0;t<e.length;t++)n[t]=e.charCodeAt(t);return n.buffer})(o.toDataURL(e)))}))};t.exports=s},47120:(t,e,n)=>{"use strict";function r(t){return Array.isArray?Array.isArray(t):"[object Array]"===h(t)}function s(t){return"string"==typeof t}function i(t){return"number"==typeof t}function o(t){return"object"==typeof t}function c(t){return null!=t}function a(t){return!t.trim().length}function h(t){return null==t?void 0===t?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}n.d(e,{A:()=>D});const l=Object.prototype.hasOwnProperty;class u{constructor(t){this._keys=[],this._keyMap={};let e=0;t.forEach((t=>{let n=d(t);e+=n.weight,this._keys.push(n),this._keyMap[n.id]=n,e+=n.weight})),this._keys.forEach((t=>{t.weight/=e}))}get(t){return this._keyMap[t]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}}function d(t){let e=null,n=null,i=null,o=1,c=null;if(s(t)||r(t))i=t,e=g(t),n=f(t);else{if(!l.call(t,"name"))throw new Error("Missing name property in key");const r=t.name;if(i=r,l.call(t,"weight")&&(o=t.weight,o<=0))throw new Error((t=>`Property 'weight' in key '${t}' must be a positive integer`)(r));e=g(r),n=f(r),c=t.getFn}return{path:e,id:n,weight:o,src:i,getFn:c}}function g(t){return r(t)?t:t.split(".")}function f(t){return r(t)?t.join("."):t}var p={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(t,e)=>t.score===e.score?t.idx<e.idx?-1:1:t.score<e.score?-1:1,includeMatches:!1,findAllMatches:!1,minMatchCharLength:1,location:0,threshold:.6,distance:100,useExtendedSearch:!1,getFn:function(t,e){let n=[],a=!1;const l=(t,e,u)=>{if(c(t))if(e[u]){const d=t[e[u]];if(!c(d))return;if(u===e.length-1&&(s(d)||i(d)||function(t){return!0===t||!1===t||function(t){return o(t)&&null!==t}(t)&&"[object Boolean]"==h(t)}(d)))n.push(function(t){return null==t?"":function(t){if("string"==typeof t)return t;let e=t+"";return"0"==e&&1/t==-1/0?"-0":e}(t)}(d));else if(r(d)){a=!0;for(let t=0,n=d.length;t<n;t+=1)l(d[t],e,u+1)}else e.length&&l(d,e,u+1)}else n.push(t)};return l(t,s(e)?e.split("."):e,0),a?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1};const m=/[^ ]+/g;class M{constructor({getFn:t=p.getFn,fieldNormWeight:e=p.fieldNormWeight}={}){this.norm=function(t=1,e=3){const n=new Map,r=Math.pow(10,e);return{get(e){const s=e.match(m).length;if(n.has(s))return n.get(s);const i=1/Math.pow(s,.5*t),o=parseFloat(Math.round(i*r)/r);return n.set(s,o),o},clear(){n.clear()}}}(e,3),this.getFn=t,this.isCreated=!1,this.setIndexRecords()}setSources(t=[]){this.docs=t}setIndexRecords(t=[]){this.records=t}setKeys(t=[]){this.keys=t,this._keysMap={},t.forEach(((t,e)=>{this._keysMap[t.id]=e}))}create(){!this.isCreated&&this.docs.length&&(this.isCreated=!0,s(this.docs[0])?this.docs.forEach(((t,e)=>{this._addString(t,e)})):this.docs.forEach(((t,e)=>{this._addObject(t,e)})),this.norm.clear())}add(t){const e=this.size();s(t)?this._addString(t,e):this._addObject(t,e)}removeAt(t){this.records.splice(t,1);for(let e=t,n=this.size();e<n;e+=1)this.records[e].i-=1}getValueForItemAtKeyId(t,e){return t[this._keysMap[e]]}size(){return this.records.length}_addString(t,e){if(!c(t)||a(t))return;let n={v:t,i:e,n:this.norm.get(t)};this.records.push(n)}_addObject(t,e){let n={i:e,$:{}};this.keys.forEach(((e,i)=>{let o=e.getFn?e.getFn(t):this.getFn(t,e.path);if(c(o))if(r(o)){let t=[];const e=[{nestedArrIndex:-1,value:o}];for(;e.length;){const{nestedArrIndex:n,value:i}=e.pop();if(c(i))if(s(i)&&!a(i)){let e={v:i,i:n,n:this.norm.get(i)};t.push(e)}else r(i)&&i.forEach(((t,n)=>{e.push({nestedArrIndex:n,value:t})}))}n.$[i]=t}else if(s(o)&&!a(o)){let t={v:o,n:this.norm.get(o)};n.$[i]=t}})),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}}function y(t,e,{getFn:n=p.getFn,fieldNormWeight:r=p.fieldNormWeight}={}){const s=new M({getFn:n,fieldNormWeight:r});return s.setKeys(t.map(d)),s.setSources(e),s.create(),s}function x(t,{errors:e=0,currentLocation:n=0,expectedLocation:r=0,distance:s=p.distance,ignoreLocation:i=p.ignoreLocation}={}){const o=e/t.length;if(i)return o;const c=Math.abs(r-n);return s?o+c/s:c?1:o}const w=32;function v(t){let e={};for(let n=0,r=t.length;n<r;n+=1){const s=t.charAt(n);e[s]=(e[s]||0)|1<<r-n-1}return e}class _{constructor(t,{location:e=p.location,threshold:n=p.threshold,distance:r=p.distance,includeMatches:s=p.includeMatches,findAllMatches:i=p.findAllMatches,minMatchCharLength:o=p.minMatchCharLength,isCaseSensitive:c=p.isCaseSensitive,ignoreLocation:a=p.ignoreLocation}={}){if(this.options={location:e,threshold:n,distance:r,includeMatches:s,findAllMatches:i,minMatchCharLength:o,isCaseSensitive:c,ignoreLocation:a},this.pattern=c?t:t.toLowerCase(),this.chunks=[],!this.pattern.length)return;const h=(t,e)=>{this.chunks.push({pattern:t,alphabet:v(t),startIndex:e})},l=this.pattern.length;if(l>w){let t=0;const e=l%w,n=l-e;for(;t<n;)h(this.pattern.substr(t,w),t),t+=w;if(e){const t=l-w;h(this.pattern.substr(t),t)}}else h(this.pattern,0)}searchIn(t){const{isCaseSensitive:e,includeMatches:n}=this.options;if(e||(t=t.toLowerCase()),this.pattern===t){let e={isMatch:!0,score:0};return n&&(e.indices=[[0,t.length-1]]),e}const{location:r,distance:s,threshold:i,findAllMatches:o,minMatchCharLength:c,ignoreLocation:a}=this.options;let h=[],l=0,u=!1;this.chunks.forEach((({pattern:e,alphabet:d,startIndex:g})=>{const{isMatch:f,score:m,indices:M}=function(t,e,n,{location:r=p.location,distance:s=p.distance,threshold:i=p.threshold,findAllMatches:o=p.findAllMatches,minMatchCharLength:c=p.minMatchCharLength,includeMatches:a=p.includeMatches,ignoreLocation:h=p.ignoreLocation}={}){if(e.length>w)throw new Error("Pattern length exceeds max of 32.");const l=e.length,u=t.length,d=Math.max(0,Math.min(r,u));let g=i,f=d;const m=c>1||a,M=m?Array(u):[];let y;for(;(y=t.indexOf(e,f))>-1;){let t=x(e,{currentLocation:y,expectedLocation:d,distance:s,ignoreLocation:h});if(g=Math.min(t,g),f=y+l,m){let t=0;for(;t<l;)M[y+t]=1,t+=1}}f=-1;let v=[],_=1,L=l+u;const S=1<<l-1;for(let r=0;r<l;r+=1){let i=0,c=L;for(;i<c;)x(e,{errors:r,currentLocation:d+c,expectedLocation:d,distance:s,ignoreLocation:h})<=g?i=c:L=c,c=Math.floor((L-i)/2+i);L=c;let a=Math.max(1,d-c+1),p=o?u:Math.min(d+c,u)+l,y=Array(p+2);y[p+1]=(1<<r)-1;for(let i=p;i>=a;i-=1){let o=i-1,c=n[t.charAt(o)];if(m&&(M[o]=+!!c),y[i]=(y[i+1]<<1|1)&c,r&&(y[i]|=(v[i+1]|v[i])<<1|1|v[i+1]),y[i]&S&&(_=x(e,{errors:r,currentLocation:o,expectedLocation:d,distance:s,ignoreLocation:h}),_<=g)){if(g=_,f=o,f<=d)break;a=Math.max(1,2*d-f)}}if(x(e,{errors:r+1,currentLocation:d,expectedLocation:d,distance:s,ignoreLocation:h})>g)break;v=y}const b={isMatch:f>=0,score:Math.max(.001,_)};if(m){const t=function(t=[],e=p.minMatchCharLength){let n=[],r=-1,s=-1,i=0;for(let o=t.length;i<o;i+=1){let o=t[i];o&&-1===r?r=i:o||-1===r||(s=i-1,s-r+1>=e&&n.push([r,s]),r=-1)}return t[i-1]&&i-r>=e&&n.push([r,i-1]),n}(M,c);t.length?a&&(b.indices=t):b.isMatch=!1}return b}(t,e,d,{location:r+g,distance:s,threshold:i,findAllMatches:o,minMatchCharLength:c,includeMatches:n,ignoreLocation:a});f&&(u=!0),l+=m,f&&M&&(h=[...h,...M])}));let d={isMatch:u,score:u?l/this.chunks.length:1};return u&&n&&(d.indices=h),d}}class L{constructor(t){this.pattern=t}static isMultiMatch(t){return S(t,this.multiRegex)}static isSingleMatch(t){return S(t,this.singleRegex)}search(){}}function S(t,e){const n=t.match(e);return n?n[1]:null}class b extends L{constructor(t,{location:e=p.location,threshold:n=p.threshold,distance:r=p.distance,includeMatches:s=p.includeMatches,findAllMatches:i=p.findAllMatches,minMatchCharLength:o=p.minMatchCharLength,isCaseSensitive:c=p.isCaseSensitive,ignoreLocation:a=p.ignoreLocation}={}){super(t),this._bitapSearch=new _(t,{location:e,threshold:n,distance:r,includeMatches:s,findAllMatches:i,minMatchCharLength:o,isCaseSensitive:c,ignoreLocation:a})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(t){return this._bitapSearch.searchIn(t)}}class k extends L{constructor(t){super(t)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(t){let e,n=0;const r=[],s=this.pattern.length;for(;(e=t.indexOf(this.pattern,n))>-1;)n=e+s,r.push([e,n-1]);const i=!!r.length;return{isMatch:i,score:i?0:1,indices:r}}}const C=[class extends L{constructor(t){super(t)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(t){const e=t===this.pattern;return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},k,class extends L{constructor(t){super(t)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(t){const e=t.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,this.pattern.length-1]}}},class extends L{constructor(t){super(t)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(t){const e=!t.startsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},class extends L{constructor(t){super(t)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(t){const e=!t.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},class extends L{constructor(t){super(t)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(t){const e=t.endsWith(this.pattern);return{isMatch:e,score:e?0:1,indices:[t.length-this.pattern.length,t.length-1]}}},class extends L{constructor(t){super(t)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(t){const e=-1===t.indexOf(this.pattern);return{isMatch:e,score:e?0:1,indices:[0,t.length-1]}}},b],I=C.length,E=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,A=new Set([b.type,k.type]);const N=[];function O(t,e){for(let n=0,r=N.length;n<r;n+=1){let r=N[n];if(r.condition(t,e))return new r(t,e)}return new _(t,e)}const $="$and",F="$path",R=t=>!(!t[$]&&!t.$or),j=t=>({[$]:Object.keys(t).map((e=>({[e]:t[e]})))});function z(t,e,{auto:n=!0}={}){const i=t=>{let c=Object.keys(t);const a=(t=>!!t[F])(t);if(!a&&c.length>1&&!R(t))return i(j(t));if((t=>!r(t)&&o(t)&&!R(t))(t)){const r=a?t[F]:c[0],i=a?t.$val:t[r];if(!s(i))throw new Error((t=>`Invalid value for key ${t}`)(r));const o={keyId:f(r),pattern:i};return n&&(o.searcher=O(i,e)),o}let h={children:[],operator:c[0]};return c.forEach((e=>{const n=t[e];r(n)&&n.forEach((t=>{h.children.push(i(t))}))})),h};return R(t)||(t=j(t)),i(t)}function W(t,e){const n=t.matches;e.matches=[],c(n)&&n.forEach((t=>{if(!c(t.indices)||!t.indices.length)return;const{indices:n,value:r}=t;let s={indices:n,value:r};t.key&&(s.key=t.key.src),t.idx>-1&&(s.refIndex=t.idx),e.matches.push(s)}))}function P(t,e){e.score=t.score}class D{constructor(t,e={},n){this.options={...p,...e},this.options.useExtendedSearch,this._keyStore=new u(this.options.keys),this.setCollection(t,n)}setCollection(t,e){if(this._docs=t,e&&!(e instanceof M))throw new Error("Incorrect 'index' type");this._myIndex=e||y(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(t){c(t)&&(this._docs.push(t),this._myIndex.add(t))}remove(t=()=>!1){const e=[];for(let n=0,r=this._docs.length;n<r;n+=1){const s=this._docs[n];t(s,n)&&(this.removeAt(n),n-=1,r-=1,e.push(s))}return e}removeAt(t){this._docs.splice(t,1),this._myIndex.removeAt(t)}getIndex(){return this._myIndex}search(t,{limit:e=-1}={}){const{includeMatches:n,includeScore:r,shouldSort:o,sortFn:c,ignoreFieldNorm:a}=this.options;let h=s(t)?s(this._docs[0])?this._searchStringList(t):this._searchObjectList(t):this._searchLogical(t);return function(t,{ignoreFieldNorm:e=p.ignoreFieldNorm}){t.forEach((t=>{let n=1;t.matches.forEach((({key:t,norm:r,score:s})=>{const i=t?t.weight:null;n*=Math.pow(0===s&&i?Number.EPSILON:s,(i||1)*(e?1:r))})),t.score=n}))}(h,{ignoreFieldNorm:a}),o&&h.sort(c),i(e)&&e>-1&&(h=h.slice(0,e)),function(t,e,{includeMatches:n=p.includeMatches,includeScore:r=p.includeScore}={}){const s=[];return n&&s.push(W),r&&s.push(P),t.map((t=>{const{idx:n}=t,r={item:e[n],refIndex:n};return s.length&&s.forEach((e=>{e(t,r)})),r}))}(h,this._docs,{includeMatches:n,includeScore:r})}_searchStringList(t){const e=O(t,this.options),{records:n}=this._myIndex,r=[];return n.forEach((({v:t,i:n,n:s})=>{if(!c(t))return;const{isMatch:i,score:o,indices:a}=e.searchIn(t);i&&r.push({item:t,idx:n,matches:[{score:o,value:t,norm:s,indices:a}]})})),r}_searchLogical(t){const e=z(t,this.options),n=(t,e,r)=>{if(!t.children){const{keyId:n,searcher:s}=t,i=this._findMatches({key:this._keyStore.get(n),value:this._myIndex.getValueForItemAtKeyId(e,n),searcher:s});return i&&i.length?[{idx:r,item:e,matches:i}]:[]}const s=[];for(let i=0,o=t.children.length;i<o;i+=1){const o=t.children[i],c=n(o,e,r);if(c.length)s.push(...c);else if(t.operator===$)return[]}return s},r=this._myIndex.records,s={},i=[];return r.forEach((({$:t,i:r})=>{if(c(t)){let o=n(e,t,r);o.length&&(s[r]||(s[r]={idx:r,item:t,matches:[]},i.push(s[r])),o.forEach((({matches:t})=>{s[r].matches.push(...t)})))}})),i}_searchObjectList(t){const e=O(t,this.options),{keys:n,records:r}=this._myIndex,s=[];return r.forEach((({$:t,i:r})=>{if(!c(t))return;let i=[];n.forEach(((n,r)=>{i.push(...this._findMatches({key:n,value:t[r],searcher:e}))})),i.length&&s.push({idx:r,item:t,matches:i})})),s}_findMatches({key:t,value:e,searcher:n}){if(!c(e))return[];let s=[];if(r(e))e.forEach((({v:e,i:r,n:i})=>{if(!c(e))return;const{isMatch:o,score:a,indices:h}=n.searchIn(e);o&&s.push({score:a,key:t,value:e,idx:r,norm:i,indices:h})}));else{const{v:r,n:i}=e,{isMatch:o,score:c,indices:a}=n.searchIn(r);o&&s.push({score:c,key:t,value:r,norm:i,indices:a})}return s}}D.version="6.6.2",D.createIndex=y,D.parseIndex=function(t,{getFn:e=p.getFn,fieldNormWeight:n=p.fieldNormWeight}={}){const{keys:r,records:s}=t,i=new M({getFn:e,fieldNormWeight:n});return i.setKeys(r),i.setIndexRecords(s),i},D.config=p,D.parseQuery=z,function(...t){N.push(...t)}(class{constructor(t,{isCaseSensitive:e=p.isCaseSensitive,includeMatches:n=p.includeMatches,minMatchCharLength:r=p.minMatchCharLength,ignoreLocation:s=p.ignoreLocation,findAllMatches:i=p.findAllMatches,location:o=p.location,threshold:c=p.threshold,distance:a=p.distance}={}){this.query=null,this.options={isCaseSensitive:e,includeMatches:n,minMatchCharLength:r,findAllMatches:i,ignoreLocation:s,location:o,threshold:c,distance:a},this.pattern=e?t:t.toLowerCase(),this.query=function(t,e={}){return t.split("|").map((t=>{let n=t.trim().split(E).filter((t=>t&&!!t.trim())),r=[];for(let t=0,s=n.length;t<s;t+=1){const s=n[t];let i=!1,o=-1;for(;!i&&++o<I;){const t=C[o];let n=t.isMultiMatch(s);n&&(r.push(new t(n,e)),i=!0)}if(!i)for(o=-1;++o<I;){const t=C[o];let n=t.isSingleMatch(s);if(n){r.push(new t(n,e));break}}}return r}))}(this.pattern,this.options)}static condition(t,e){return e.useExtendedSearch}searchIn(t){const e=this.query;if(!e)return{isMatch:!1,score:1};const{includeMatches:n,isCaseSensitive:r}=this.options;t=r?t:t.toLowerCase();let s=0,i=[],o=0;for(let r=0,c=e.length;r<c;r+=1){const c=e[r];i.length=0,s=0;for(let e=0,r=c.length;e<r;e+=1){const r=c[e],{isMatch:a,indices:h,score:l}=r.search(t);if(!a){o=0,s=0,i.length=0;break}if(s+=1,o+=l,n){const t=r.constructor.type;A.has(t)?i=[...i,...h]:i.push(h)}}if(s){let t={isMatch:!0,score:o/s};return n&&(t.indices=i),t}}return{isMatch:!1,score:1}}})},49697:(t,e,n)=>{var r,s="undefined"!=typeof global?global:"undefined"!=typeof window?window:{},i=n(80542);"undefined"!=typeof document?r=document:(r=s["__GLOBAL_DOCUMENT_CACHE@4"])||(r=s["__GLOBAL_DOCUMENT_CACHE@4"]=i),t.exports=r},64991:(t,e,n)=>{"use strict";const r=n(80707),{MIME_PNG:s}=n(70474);t.exports=async(t,e,n)=>{const i=r(t);return await Promise.all(i.map((async t=>{if(e===s&&"png"===t.type)return{...t,buffer:t.data.buffer.slice(t.data.byteOffset,t.data.byteOffset+t.data.byteLength)};if("png"===t.type){const e=await n.decode(t.data);Object.assign(t,{data:e.data,type:"bmp"})}return Object.assign(t,{buffer:await n.encode(t,e),type:e.replace("image/","")})})))}},70474:t=>{"use strict";t.exports={MIME_BMP:"image/bmp",MIME_JPEG:"image/jpeg",MIME_PNG:"image/png"}},89840:t=>{var e;e="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},t.exports=e}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1228],{"settings/resources/elements/cheat-volume-slider":(t,e,i)=>{i.r(e),i.d(e,{CheatVolumeSlider:()=>n});var a=i(15215),s=i("aurelia-framework"),o=i(43570);let n=class{#t;constructor(t){this.#t=t,this.cheatSoundVolume=t.volume}async cheatSoundVolumeChanged(t,e){void 0!==e&&await this.#t.setVolume(Number(t),"settings")}};(0,a.Cg)([s.observable,(0,a.Sn)("design:type",Object)],n.prototype,"cheatSoundVolume",void 0),n=(0,a.Cg)([(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[o.L])],n)},"settings/resources/elements/cheat-volume-slider.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template> <require from="../../../shared/resources/elements/range-input"></require> <range-input value.bind="cheatSoundVolume" min="0" max="100" class="label-first"></range-input> </template> '},"settings/resources/elements/debug-components":(t,e,i)=>{i.r(e),i.d(e,{DebugComponents:()=>o});var a=i(15215),s=i("aurelia-framework");let o=class{constructor(){this.componentsDisabled=!1}handleDisableComponentsClick(){this.componentsDisabled=!this.componentsDisabled}};o=(0,a.Cg)([(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[])],o)},"settings/resources/elements/debug-components.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template> <require from="./debug-components.scss"></require> <require from="../../../shared/resources/elements/toggle.html"></require> <require from="../../../shared/resources/elements/range-input"></require> <require from="../../../shared/resources/elements/scalar-input"></require> <require from="../../../shared/resources/elements/selection-input"></require> <require from="../../../shared/resources/elements/incremental-input"></require> <div class="components-container"> <div class="setting"> <wm-button color="inverse" size="s" click.delegate="handleDisableComponentsClick()">Toggle Disabled State</wm-button> </div> <div class="setting"> <div class="setting-name">Mod control inputs</div> <div class="components-section"> <span>Range</span> <range-input disabled.bind="componentsDisabled" value="40" min="0" max="100" step.bind="1"></range-input> <span>Toggle</span> <toggle disabled.bind="componentsDisabled" value="true"></toggle> <span>Scalar</span> <scalar-input disabled.bind="componentsDisabled" default="1.5" options.bind="[0.5, 1, 1.5, 2]"></scalar-input> <span>Selection</span> <selection-input disabled.bind="componentsDisabled" value="Default" options.bind="[\'Default\', \'Option 1\', \'Option 2\', \'Option 3\']" enable-i18n.bind="false"> </selection-input> <span>Incremental</span> <incremental-input disabled.bind="componentsDisabled" options.bind="[-100, -10, 10, 100, 1000, 10000, 1000000]"> </incremental-input> </div> </div> </div> </template> '},"settings/resources/elements/debug-components.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>l});var a=i(31601),s=i.n(a),o=i(76314),n=i.n(o)()(s());n.push([t.id,".components-container .components-section{display:flex;flex-direction:column;resize:both;overflow:auto;gap:12px;margin-top:12px;border:1px solid rgba(255,255,255,.1);border-radius:6px;padding:12px}",""]);const l=n},"settings/resources/elements/debug-settings":(t,e,i)=>{i.r(e),i.d(e,{DebugSettings:()=>nt});var a=i(15215),s=i("aurelia-dialog"),o=i("aurelia-framework"),n=i(96610),l=i(20770),d=i("dialogs/choose-plan-promo-dialog"),r=i("dialogs/pro-showcase-dialog"),c=i(73592),g=i(10699),u=i(50654),m=i(97170),p=i(43861),b=i(78576),h=i(811),f=i(96111),v=i(59327),y=i(8712),w=i(67064),k=i("cheats/resources/elements/feedback-dialog"),D=i("cheats/resources/elements/first-play-upgrade-prompt-dialog"),S=i("cheats/resources/elements/save-cheats-disable-confirm-dialog"),P=i(68502),A=i(71341),L=i("dialogs/email-dialog"),T=i("dialogs/failed-payment-dialog"),x=i(67761),C=i(52871),G=i("dialogs/maps-education-dialog"),E=i("dialogs/maps-nps-dialog"),F=i("dialogs/mod-timers-education-dialog"),R=i("dialogs/nps-dialog"),M=i(18165),O=i("dialogs/plan-details-dialog"),_=i("dialogs/post-assistant-nps-dialog"),N=i("dialogs/precision-mods-education-dialog"),U=i(30770),I=i("dialogs/pro-showcase-columns-dialog"),q=i(86319),H=i("dialogs/secure-account-dialog"),B=i("dialogs/selection-dialog"),V=i("dialogs/teleport-education-dialog"),$=i("dialogs/time-limit-pre-game-dialog"),z=i("dialogs/time-limit-reached-post-game-dialog"),j=i("dialogs/time-limit-reached-pre-game-dialog"),W=i("dialogs/time-remaining-post-game-dialog"),Q=i("dialogs/webview-dialog"),J=i("dialogs/welcome-mat-dialog"),Y=i(19072),K=i(24008),X=i(68539),Z=i("shared/dialogs/basic-dialog"),tt=i(20057),et=i(54995),it=i(70236),at=i(48881),st=i(12511);const ot=(0,n.getLogger)("debug-settings");let nt=class{#e;#i;#a;#s;constructor(t,e,i,a,s,o,n,l,d,r,c,g,u,m,p,b,h,f,v,y,w,k,D,S,P,A,L,T,x,C,G,E,F,R,M,O,_,N,U,I,q,H,B,V,$,z,j){this.checkoutDialog=t,this.failedPaymentDialog=e,this.failedPaymentToast=i,this.reactivatePro=a,this.proDialog=s,this.feedbackDialog=o,this.selectionDialog=n,this.pollDialog=l,this.basicDialog=d,this.toaster=r,this.changePaymentMethod=c,this.fullscreenAnimationPlayer=g,this.postProUpgrade=u,this.dialog=m,this.secureAccountDialog=p,this.planDetailsDialog=b,this.welcomeMatDialog=h,this.requiresElevationDialog=f,this.emailDialog=v,this.accountRefresher=y,this.webviewDialog=w,this.saveCheatsDisableConfirmDialog=k,this.promotions=D,this.followedGameManager=S,this.firstPlayUpgradePromptDialog=P,this.dailyTimeLimitEnforcer=A,this.perGameDailyTimeLimitEnforcer=L,this.timeLimitReachedPostGameDialog=T,this.timeLimitReachedPreGameDialog=x,this.timeLimitPreGameDialog=C,this.npsDialog=G,this.mapsNpsDialog=E,this.postAssistantNpsDialog=F,this.timeRemainingPostGameDialog=R,this.mapsEducationDialog=M,this.teleportEducationDialog=O,this.overlayAnnouncementDialog=_,this.proShowcaseDialog=N,this.precisionModsEducationDialog=U,this.modTimersEducationDialog=I,this.proShowcaseColumnsDialog=q,this.choosePlanPromoDialog=H,this.liveLocationAnnouncementDialog=B,this.instantHighlightAnnouncementDialog=V,this.#i=$,this.#a=z,this.#s=j}bind(){this.#o(),this.#e=this.#s.onVariantChanged((()=>this.#o()))}unbind(){this.#e?.dispose()}#o(){const t=[];this.#s.assignments.forEach(((e,i)=>{t.push({key:i,variant:e})})),this.assignedVariants=t}filterChanged(){const t="string"==typeof this.filter?this.filter.toLocaleLowerCase():"";Array.from(this.containerEl.querySelectorAll(".setting:not(:first-child)")).forEach((e=>{let i=!0;Array.from(e.querySelectorAll(".standard-button")).forEach((e=>{e.classList.toggle("hidden",!e.innerText.toLocaleLowerCase().includes(t))||(i=!1)})),e.classList.toggle("hidden",i)}))}async openFeedbackDialog(t){const e=await this.feedbackDialog.collectFeedback("Gears of Duty",{gameId:"1",blueprint:{notes:"These are some notes...",cheats:[{name:"Unlimited Health",uuid:"aaaaaaaa"},{name:"Jump Height",uuid:"bbbbbbbb"},{name:"Gravity",uuid:"cccccccc"}]}},"steam:550",Math.floor(99999*Math.random()),["aaaaaaaa","bbbbbbbb"],t,!0);ot.info("Feedback dialog result",JSON.parse(JSON.stringify(e)))}async openCancelDialog(){const t=await this.pollDialog.openByUsage("subscription_cancelation",!0);ot.info("Cancel dialog result",t?JSON.parse(JSON.stringify(t)):"canceled")}async openPollDialog(){const t=await this.pollDialog.open({id:"",usage:"generic",options:["Google Search","Youtube","Forum","A friend","Other"],startedAt:"2018-11-20T17:20:27Z",title:"How did you hear about WeMod"});ot.info("Poll dialog result",t?JSON.parse(JSON.stringify(t)):"canceled")}async openSelectionDialog(){const t=await this.selectionDialog.open({title:tt.F2.literal("Animals"),message:tt.F2.literal("Pick any two"),options:["Lions","Tigers","Bears","Other"].map(tt.F2.literal),defaultSelections:["Lions"].map(tt.F2.literal),multiselect:{minSelections:1,maxSelections:2},detailsField:{lastOptionOnly:!0,placeholder:tt.F2.literal("Some other animal"),minLength:20,maxLength:100},submitLabel:tt.F2.literal("Submit")});ot.info("Selection dialog result",JSON.parse(JSON.stringify(t)))}triggerPostProUpgrade(){this.dialog.closeAll(),this.postProUpgrade.trigger()}triggerProTooltip(){this.dialog.closeAll(),this.postProUpgrade.openTooltip()}openProDialog(){this.proDialog.open({trigger:"debug",nonInteraction:!1})}openProCheckoutDialog(t){this.checkoutDialog.open({...t||{},trigger:"debug"})}addPromotion(){this.promotions.refresh({id:"debug_promotion",flags:1,components:{dialog:{triggers:["navigation"],route:"promotion",params:{id:"debug_promotion"}},appBanner:{icon:"gift",name:"25% off Pro annual subscriptions",description:"Get 3 months free",button:"Claim gift now",url:"wemod://checkout",theme:{}},notification:{delay:0,image:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAMAAABEpIrGAAAA3lBMVEUAAAD0nBLAOSvyxQ7AOSvyvRDyuxDFRyjAOSvAOSvAOSvAOSvxxA/AOSvANyvAOSvznBLAOSvAOSvxxA/AOSvAOSvxxA/zohLyrBHskBrzoxLAOSvyrRHxxA/znBLAOSvznBLAOSvOYSPprxPprxPxxA/xxA/tuRHuvBDznBLAOSvtqRbtqBbAOSvKVSXAOSvLWCXxxA/AOSvznBLznBLAOSvxxQ7nTDvAOSvznBLqhR7rnRrysBDyphHUQzLRQjLKPi/hSTnBOyv0nhLytRDhSzjsnxnpsRLzohLzqBGeoOfQAAAANnRSTlMA+vvUh4N7BdTOpzL2Gfbk5NyybjbnyYWDgXx6enIoIRAOCf796uTg3926uraYko+MfHFtZi6JURAMAAABAElEQVQ4y+3P6VLCMBDA8bVGmyb0ooAgh4D3fet6K5Tr/V/InZCE1hkLD8DvQ2a7829mAivzBb/sgdIT1bIPf8gQEUOpUk5jtQI5buw0ZRNjl0YPmbxzQheyHrFM5zUyAIaCRoH5K3zk9Ec/dirSCfbUNXRmCeSMsTPkHno0eHRjnnsTYEZw1QcjqW0qJweT0Y8ymuwfz3e1NkD7aeFZy6xu4ag4OIRScVBaHiQXb8b4Sxvb1XkC0Hi1BqgMFpsGAOy+GEMTDO1qZ5Wg9W6kn1pqV3UKtotesbUObNAqCuoUPKT/B+k9Bd3T6cfc7Fub6cU06gLpRBuao5nvqAPL/QL2XpTCbKc44AAAAABJRU5ErkJggg==",title:"Get 25% off Pro",message:"Purchase an annual subscription today and get 3 months free.",url:"wemod://settings",actions:[{type:"protocol",arguments:"wemod://settings",label:"Claim gift"}]}},endsAt:"2099-11-29T17:59:59+0000"})}addSwitchToAnnualPromotion(){this.promotions.refresh({id:"switch_to_annual_debug_promotion",flags:1,components:{dialog:{triggers:["navigation"],route:"switch-to-annual",params:{}},appBanner:{name:"Save 25% on **Pro**",description:"Switch to yearly plan today",button:"Switch now",url:"wemod://webview/switch-to-annual",theme:{}}},endsAt:"2099-11-29T17:59:59+0000"})}markAllCheatInstructionsUnread(){this.#i.dispatch(at.BD)}clearReadTrainerNotes(){this.#i.dispatch(at.gi)}showSaveCheatDisableConfirmDialog(){this.#i.dispatch(at.Kc,{confirmDisablingSaveCheats:!0},"debug")}clearRemoteEducationFlags(){this.#i.dispatch(at.NX,"remoteEducationShown",!1)}clearPromotionHistory(){this.#i.dispatch(at.aC)}clearMyGamesFollowPrompt(){this.#i.dispatch(at.vk,"lastFollowedMyGames",null)}async resetState(){return await this.#i.dispatch(at.B2),this.#a.reload()}resetCoachingTipHistory(){this.#i.dispatch(at.df)}playFireworksAnimation(){this.fullscreenAnimationPlayer.play({path:"static/animations/fireworks.json"})}fakeApplyUpdate(){setTimeout((()=>this.#a.fakeApplyUpdate()),1e4)}fakeApplyUpdateError(){this.#a.fakeApplyUpdateError()}showRandomFollowedGameNotification(t,e){const i=this.followedGameManager.followedGames;if(0===i.length)return void alert("You're not following any games!");const a=i[Math.floor(Math.random()*i.length)];setTimeout((()=>{const t=new Date(Date.now()).toISOString();this.#i.dispatch(at.If,{...this.catalog,games:{...this.catalog.games,[a.gameId]:{...this.catalog.games[a.gameId],trainer:{...this.catalog.games[a.gameId].trainer,updatedAt:t,createdAt:"release"===e?t:this.catalog.games[a.gameId]?.trainer?.createdAt}}}})}),t)}resetMyGamesFollowPrompt(){this.#i.dispatch(at.vk,"lastFollowedMyGames",null)}clearMapSettings(){this.#i.dispatch(at.y0)}resetDailyTimeLimit(){this.dailyTimeLimitEnforcer.resetTimeLimit()}resetPerGameDailyTimeLimits(){this.perGameDailyTimeLimitEnforcer.resetAllTimeLimits()}async exceedDailyTimeLimit(){await this.#i.dispatch(at.TU,"secondsPlayedToday",this.dailyTimeLimitEnforcer.dailyPlayLimitSeconds),this.dailyTimeLimitEnforcer.triggerPostTrainerEvents()}exceedPerGameDailyTimeLimits(){this.perGameDailyTimeLimitEnforcer.exceedAllTimeLimits()}resetLastFirstPlayUpgradePromptDialog(){this.#i.dispatch(at.vk,"lastFirstPlayUpgradePrompt",null),this.#i.dispatch(at.vk,"lastFirstPlayUpgradeCheck",null)}clearAssistantPopout(){this.#i.dispatch(at.NX,"assistantPopoutHidden",!1),this.#i.dispatch(at.TU,"timesAssistantPopoutSeen",0),this.#i.dispatch(at.vk,"lastAssistantPopout",null)}clearAssistantOverlayMessage(){this.#i.dispatch(at.TU,"timesOverlayAssistantMessageSeen",0),this.#i.dispatch(at.vk,"lastOverlayAssistantMessage",null)}clearAssistantHistory(){this.#i.dispatch(at.PZ)}resetTimeLimitPreGameDialog(){this.#i.dispatch(at.vk,"lastTimeLimitPreGameDialog",null)}secondsToTime(t){return new Date(1e3*t).toISOString().substr(11,8)}openMapsEducationDialog(){const t=this.catalog.maps[Math.floor(Math.random()*this.catalog.maps.length)];this.mapsEducationDialog.open({titleId:t.titleId})}openTeleportEducationDialog(){const t=this.catalog.maps.find((t=>(0,it.Lt)(t.flags??0,K.nC.HasGameCoordinates)));this.teleportEducationDialog.open({titleId:t?.titleId??""})}get flagsWithValues(){return st.M.map((t=>({key:t,value:this.flags[t]}))).filter((t=>void 0!==t.value)).filter((t=>!this.filter||t.key.toLocaleLowerCase().includes(this.filter.toLocaleLowerCase()))).sort(((t,e)=>t.key.localeCompare(e.key)))}updateFlag(t){this.#i.dispatch(at.NX,t.key,t.value)}get emailSet(){return(0,it.Lt)(this.account?.flags,2)}get passwordSet(){return(0,it.Lt)(this.account?.flags,8)}clearChangelog(){this.#i.dispatch(at.Kc,{lastChangelogIdSeen:null})}clearDismissedModTimerMessages(){this.#i.dispatch(at.rQ)}};(0,a.Cg)([(0,o.observable)(),(0,a.Sn)("design:type",String)],nt.prototype,"filter",void 0),(0,a.Cg)([(0,o.computedFrom)("flags","filter"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],nt.prototype,"flagsWithValues",null),(0,a.Cg)([(0,o.computedFrom)("account.flags"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],nt.prototype,"emailSet",null),(0,a.Cg)([(0,o.computedFrom)("account.flags"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],nt.prototype,"passwordSet",null),nt=(0,a.Cg)([(0,et.m6)({selectors:{catalog:(0,et.$t)((t=>t.catalog)),account:(0,et.$t)((t=>t.account)),flags:(0,et.$t)((t=>t.flags))}}),(0,o.autoinject)(),(0,a.Sn)("design:paramtypes",[A.U,T.FailedPaymentDialogService,v.t,y.J,U.f,k.FeedbackDialogService,B.SelectionDialogService,p.Z,Z.BasicDialogService,w.l,P.N,c.b,b.G,s.DialogService,H.SecureAccountDialogService,O.PlanDetailsDialogService,J.WelcomeMatDialogService,q.T,L.EmailDialogService,g.G,Q.WebviewDialogService,S.SaveCheatsDisableConfirmDialogService,h.n,u.O,D.FirstPlayUpgradePromptDialogService,f.Y,m.V,z.TimeLimitReachedPostGameDialogService,j.TimeLimitReachedPreGameDialogService,$.TimeLimitPreGameDialogService,R.NpsDialogService,E.MapsNpsDialogService,_.PostAssistantNpsDialogService,W.TimeRemainingPostGameDialogService,G.MapsEducationDialogService,V.TeleportEducationDialogService,M.v,r.ProShowcaseDialogService,N.PrecisionModsEducationDialogService,F.ModTimersEducationDialogService,I.ProShowcaseColumnsDialogService,d.ChoosePlanPromoDialogService,C.U,x.N,l.il,Y.s,X.z])],nt)},"settings/resources/elements/debug-settings.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template> <require from="./debug-settings.scss"></require> <require from="../../../shared/resources/elements/toggle.html"></require> <div class="debug-groups" ref="containerEl"> <div class="setting"> <div class="setting-info"> <div class="setting-input"> <div class="text-setting"> <input type="text" placeholder="Filter..." value.two-way="filter" maxlength="100"> </div> </div> </div> </div> <div class="setting"> <div class="setting-name">Misc Dialogs</div> <div class="setting-info"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="openSelectionDialog()">Selection</button> <button class="standard-button" click.delegate="openCancelDialog()">Cancel</button> <button class="standard-button" click.delegate="openPollDialog()">Poll</button> <button class="standard-button" click.delegate="secureAccountDialog.open()" disabled.bind="emailSet && passwordSet"> Secure Account </button> <button class="standard-button" click.delegate="secureAccountDialog.open({emailOnly: true})" disabled.bind="emailSet"> Secure Account Email </button> <button class="standard-button" click.delegate="welcomeMatDialog.open()">Welcome Mat</button> <button class="standard-button" click.delegate="requiresElevationDialog.open()"> Requires Elevation </button> <button class="standard-button" click.delegate="npsDialog.open({ trigger: \'debug\' })">NPS</button> <button class="standard-button" click.delegate="postAssistantNpsDialog.open({ trigger: \'debug\' })"> Post Assistant NPS </button> <button class="standard-button" click.delegate="mapsNpsDialog.open({ trigger: \'maps-feedback\' })"> Maps NPS </button> </div> </div> </div> <div class="setting"> <div class="setting-name">Education & Announcement Dialogs</div> <div class="setting-info"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="openTeleportEducationDialog()"> Teleport Education </button> <button class="standard-button" click.delegate="overlayAnnouncementDialog.open()"> Overlay Announcement </button> <button class="standard-button" click.delegate="openMapsEducationDialog()">Maps Education</button> <button class="standard-button" click.delegate="precisionModsEducationDialog.open()"> Precision Mods Education </button> <button class="standard-button" click.delegate="modTimersEducationDialog.open()"> Mod Timers Education </button> <button class="standard-button" click.delegate="liveLocationAnnouncementDialog.open()"> Live Location Announcement </button> <button class="standard-button" click.delegate="instantHighlightAnnouncementDialog.open()"> Instant Highlight Announcement </button> </div> </div> </div> <div class="setting"> <div class="setting-name">Pro Dialogs</div> <div class="setting-info"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="openProDialog()">Pro</button> <button class="standard-button" click.delegate="openProCheckoutDialog()">Pro Checkout</button> <button class="standard-button" click.delegate="openProCheckoutDialog({discountCode: \'halfoff\'})"> Pro Checkout (with coupon) </button> <button class="standard-button" click.delegate="reactivatePro.showReactivateDialog()"> Reactivate Pro </button> <button class="standard-button" click.delegate="changePaymentMethod.open()"> Change Payment Method </button> <button class="standard-button" click.delegate="failedPaymentDialog.open()"> Failed Payment Dialog </button> <button class="standard-button" click.delegate="failedPaymentToast.add()"> Failed Payment Toast </button> <button class="standard-button" click.delegate="planDetailsDialog.open()">Plan Details</button> <button class="standard-button" click.delegate="emailDialog.open()">Email Collection</button> <button class="standard-button" click.delegate="proShowcaseDialog.open({defaultFeature: \'save_mods\'})"> Pro Showcase </button> <button class="standard-button" click.delegate="proShowcaseColumnsDialog.open()"> Pro Showcase (Columns) </button> <button class="standard-button" click.delegate="choosePlanPromoDialog.open()"> Choose Plan Promotional </button> </div> </div> </div> <div class="setting"> <div class="setting-name">Webviews</div> <div class="setting-info"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="webviewDialog.open({route: \'switch-to-annual\', params: {route: \'switch-to-annual\'}})"> Switch to Annual </button> <button class="standard-button" click.delegate="webviewDialog.open({route: \'cancel-flow\'})"> Cancel Win-Back </button> <button class="standard-button" click.delegate="webviewDialog.open({route: \'cancel-flow\', params: {route: \'switch-to-monthly\'}})"> Switch to Monthly </button> <button class="standard-button" click.delegate="webviewDialog.open({route: \'cancel-flow\', params: {route: \'trial-cancel-discount\'}})"> Trial Cancel Discount </button> <button class="standard-button" click.delegate="webviewDialog.open({route: \'cancel-flow\', params: {route: \'cancel-reason-poll\'}})"> Cancel Reason Poll </button> <button class="standard-button" click.delegate="webviewDialog.open({route: \'cancel-win-back\'})"> Cancel Win-Back (Legacy) </button> <button class="standard-button" click.delegate="webviewDialog.open({route: \'switch-to-monthly\'})"> Switch to Monthly (Legacy) </button> </div> </div> </div> <div class="setting"> <div class="setting-name">Misc Experiments</div> <div class="setting-info column-layout"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="firstPlayUpgradePromptDialog.open()"> First Play Upgrade Prompt </button> <button class="standard-button" click.delegate="resetLastFirstPlayUpgradePromptDialog()"> Reset Last First Play Upgrade Prompt </button> </div> </div> </div> <div class="setting"> <div class="setting-name">Time Limit Experiment</div> <div class="setting-info column-layout"> <table> <tr> <td>Limit hours</td> <td>${dailyTimeLimitEnforcer.dailyPlayLimitHours}</td> </tr> <tr> <td>Hours played today</td> <td>${secondsToTime(dailyTimeLimitEnforcer.secondsPlayedToday || 0)}</td> </tr> </table> <div class="setting-input debug-buttons"> <div class="debug-buttons-row"> <button class="standard-button" click.delegate="timeLimitReachedPreGameDialog.open({perGame: false, limitHours: 1})"> Time Limit Reached (Pre Game) Dialog (1 Hour Limit) </button> <button class="standard-button" click.delegate="timeLimitReachedPreGameDialog.open({perGame: false, limitHours: 2})"> 2 Hour Limit </button> <button class="standard-button" click.delegate="timeLimitReachedPreGameDialog.open({perGame: false, limitHours: 3})"> 3 Hour Limit </button> <button class="standard-button" click.delegate="timeLimitReachedPreGameDialog.open({perGame: false, limitHours: 4})"> 4 Hour Limit </button> </div> <div class="debug-buttons-row"> <button class="standard-button" click.delegate="timeLimitReachedPostGameDialog.open({perGame: false, limitHours: 1})"> Time Limit Reached (Post Game) Dialog (1 Hour Limit) </button> <button class="standard-button" click.delegate="timeLimitReachedPostGameDialog.open({perGame: false, limitHours: 2})"> 2 Hour Limit </button> <button class="standard-button" click.delegate="timeLimitReachedPostGameDialog.open({perGame: false, limitHours: 3})"> 3 Hour Limit </button> <button class="standard-button" click.delegate="timeLimitReachedPostGameDialog.open({perGame: false, limitHours: 4})"> 4 Hour Limit </button> </div> <div class="debug-buttons-row"> <button class="standard-button" click.delegate="timeLimitPreGameDialog.open({limitHours: 3})"> Time Limit (Pre-Game) Dialog </button> <button class="standard-button" click.delegate="resetTimeLimitPreGameDialog()"> Reset Dialog </button> </div> <div class="debug-buttons-row"> <button class="standard-button" click.delegate="timeRemainingPostGameDialog.open()"> Time Remaining (Post-Game) Dialog </button> </div> <div class="debug-buttons-row"> <button class="standard-button" click.delegate="resetDailyTimeLimit()">Reset Time Limit</button> <button class="standard-button" click.delegate="exceedDailyTimeLimit()"> Exceed Time Limit </button> </div> </div> </div> </div> <div class="setting"> <div class="setting-name">Per Game Time Limit Experiment</div> <div class="setting-info column-layout"> <table if.bind="perGameDailyTimeLimitEnforcer.isEnabled"> <tr> <td>Limit hours</td> <td>${perGameDailyTimeLimitEnforcer.dailyPlayLimitHours}</td> </tr> <tr repeat.for="entry of perGameDailyTimeLimitEnforcer.getCounterEntries()"> <td> <template if.bind="$index === 0">Seconds played today</template> </td> <td>${secondsToTime(entry[1])}</td> </tr> </table> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="timeLimitReachedPreGameDialog.open({perGame: true})"> Time Limit Reached (Pre Game) Dialog </button> <button class="standard-button" click.delegate="timeLimitReachedPostGameDialog.open({perGame: true})"> Time Limit Reached (Post Game) Dialog </button> <button class="standard-button" click.delegate="resetPerGameDailyTimeLimits()"> Reset Time Limits </button> <button class="standard-button" click.delegate="exceedPerGameDailyTimeLimits()"> Exceed Time Limits </button> </div> </div> </div> <div class="setting" if.bind="assignedVariants.length"> <div class="setting-name">Assigned Experiments</div> <div class="setting-info column-layout"> <table> <tr> <th>Experiment Key</th> <th>Variant</th> </tr> <tr repeat.for="assignment of assignedVariants"> <td>${assignment.key}</td> <td>${assignment.variant}</td> </tr> </table> </div> </div> <div class="setting"> <div class="setting-name">Feedback Dialog</div> <div class="setting-info"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="openFeedbackDialog(null)">Indeterminate</button> <button class="standard-button" click.delegate="openFeedbackDialog(false)">Failure</button> <button class="standard-button" click.delegate="openFeedbackDialog(true)">Success</button> </div> </div> </div> <div class="setting"> <div class="setting-name">Basic Dialogs</div> <div class="setting-info"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="basicDialog.ok(\'This is a test\')">Ok</button> <button class="standard-button" click.delegate="basicDialog.show({message: \'This is a test\', options: [{label: \'Cancel\'}, {label: \'Ok\', style: \'primary\'}]})"> Show </button> <button class="standard-button" click.delegate="basicDialog.help(\'There was a problem\', \'some-help-topic\')"> Help </button> <button class="standard-button" click.delegate="basicDialog.yesNo(\'Are you sure?\')">Yes/No</button> </div> </div> </div> <div class="setting"> <div class="setting-name">Toasts</div> <div class="setting-info debug-column"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="toaster.toast({content: \'This is a normal toast with an action\', persist: true, actions: [{label: \'Ok\'}], onTop: true})"> Normal </button> <button class="standard-button" click.delegate="toaster.toast({content: \'This is an alert toast with two actions\', type: \'alert\', persist: true, actions: [{label: \'Yep\'}, {label: \'Nope\'}], onTop: true})"> Alert </button> <button class="standard-button" click.delegate="toaster.toast({content: \'This is a boost toast\', type: \'boost\', persist: true, onTop: true})"> Boost </button> <button class="standard-button" click.delegate="toaster.toast({content: \'This toast will not be persisted\', persist: false, actions: [{label: \'Ok\'}], onTop: true})"> Not Persisted </button> <button class="standard-button" click.delegate="toaster.toast({content: \'This toast has no actions\', persist: false, onTop: true})"> No Actions </button> </div> </div> </div> <div class="setting"> <div class="setting-name">Animations</div> <div class="setting-info debug-column"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="playFireworksAnimation()">Fireworks</button> </div> </div> </div> <div class="setting"> <div class="setting-name">Triggers</div> <div class="setting-info debug-column"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="triggerPostProUpgrade()">Pro Onboarding</button> <button class="standard-button" click.delegate="triggerProTooltip()">Pro Onboarding Tooltip</button> <button class="standard-button" click.delegate="addPromotion()">Promotion</button> <button class="standard-button" click.delegate="addSwitchToAnnualPromotion()"> Switch to Annual Promotion </button> <button class="standard-button" click.delegate="accountRefresher.refreshAccount()"> Refresh Account </button> <button class="standard-button" click.delegate="fakeApplyUpdate()">Fake Update in 10s</button> <button class="standard-button" click.delegate="fakeApplyUpdateError()">Fake Update Error</button> </div> </div> </div> <div class="setting"> <div class="setting-name">Followed Games</div> <div class="setting-info debug-column"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="showRandomFollowedGameNotification(0, \'update\')"> Immediate Update </button> <button class="standard-button" click.delegate="showRandomFollowedGameNotification(60000, \'update\')"> Update After 1 Minute </button> <button class="standard-button" click.delegate="showRandomFollowedGameNotification(0, \'release\')"> Immediate Release </button> <button class="standard-button" click.delegate="showRandomFollowedGameNotification(60000, \'release\')"> Release After 1 Minute </button> <button class="standard-button" click.delegate="followedGameManager.unfollowAllGames()"> Unfollow All </button> <button class="standard-button" click.delegate="resetMyGamesFollowPrompt()"> Reset My Games Follow Prompt </button> </div> </div> </div> <div class="setting"> <div class="setting-name">Save Mods</div> <div class="setting-info debug-column"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="saveCheatsDisableConfirmDialog.open()"> Disable confirm </button> <button class="standard-button" click.delegate="showSaveCheatDisableConfirmDialog()"> Reset disable confirm dialog </button> </div> </div> </div> <div class="setting"> <div class="setting-name">AI Game Guide</div> <div class="setting-info debug-column"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="clearAssistantPopout()"> Reset Discovery Popout </button> <button class="standard-button" click.delegate="clearAssistantHistory()"> Clear Message History </button> <button class="standard-button" click.delegate="clearAssistantOverlayMessage()"> Clear Overlay Message </button> </div> </div> </div> <div class="setting"> <div class="setting-name">Flags</div> <div class="setting-info column-layout"> <table> <tr repeat.for="flag of flagsWithValues"> <td>${flag.key}</td> <td><toggle value.two-way="flag.value" change.delegate="updateFlag(flag)"></toggle></td> </tr> </table> </div> </div> <div class="setting"> <div class="setting-name">Store</div> <div class="setting-info debug-column"> <div class="setting-input debug-buttons"> <button class="standard-button" click.delegate="markAllCheatInstructionsUnread()"> Clear Read Cheat Instructions </button> <button class="standard-button" click.delegate="clearOverlayEducationFlags()"> Clear Overlay Education Flags </button> <button class="standard-button" click.delegate="clearRemoteEducationFlags()"> Clear Remote Education Flags </button> <button class="standard-button" click.delegate="resetCoachingTipHistory()"> Clear Coaching Tip History </button> <button class="standard-button" click.delegate="clearReadTrainerNotes()"> Clear Read Trainer Notes </button> <button class="standard-button" click.delegate="clearPromotionHistory()"> Clear Promotion History </button> <button class="standard-button" click.delegate="clearMyGamesFollowPrompt()"> Clear Follow My Games </button> <button class="standard-button" click.delegate="clearMapSettings()">Clear Map Settings</button> <button class="standard-button" click.delegate="updateFlag({key: \'overlayAnnouncementShown\', value: false})"> Clear Overlay Announcement Shown </button> <button class="standard-button" click.delegate="clearChangelog()">Clear Changelog Seen</button> <button class="standard-button" click.delegate="clearDismissedModTimerMessages()"> Clear Dismissed Mod Timer Messages </button> <button class="standard-button alert" click.delegate="resetState()">Reset</button> </div> </div> </div> <div class="setting"> <div class="setting-name">External Links</div> <div class="setting-info debug-column"> <div class="setting-input debug-buttons"> <a class="standard-button" href="wemod://play?titleId=26670&gameId=26896">Play Game</a> </div> </div> </div> </div> </template> '},"settings/resources/elements/debug-settings.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>l});var a=i(31601),s=i.n(a),o=i(76314),n=i.n(o)()(s());n.push([t.id,'.settings-menu .debug-buttons button{float:left;margin:0 10px 10px 0}.settings-menu .debug-buttons-row:after{content:"";display:block;clear:both}.settings-menu .debug-column{flex-direction:column}.settings-menu .debug-column .setting-description{margin-bottom:10px}.settings-menu .debug-groups .hidden{display:none !important}.settings-menu table{width:auto;max-width:100%;border-spacing:0}.settings-menu table td{padding:10px;border:1px solid var(--theme--secondary-background)}.settings-menu table th{font-weight:700;padding:10px;background:var(--theme--secondary-background);border:0;color:#fff}.settings-menu table+.debug-buttons{margin-top:20px}',""]);const l=n},"settings/resources/elements/general-settings":(t,e,i)=>{i.r(e),i.d(e,{GeneralSettings:()=>b});var a=i(15215),s=i("aurelia-framework"),o=i(20770),n=i(68663),l=i(62914),d=i(67064),r=i(19072),c=i(62614),g=i("shared/dialogs/basic-dialog"),u=i(20057),m=i(54995),p=i(48881);let b=class{#n;#l;#a;#d;#r;#i;#c;constructor(t,e,i,a,s,o,n){this.#n=t,this.#l=e,this.#a=i,this.#d=a,this.#r=s,this.#i=o,this.#c=n}async saveLog(){const t=await this.#a.showSaveFileDialog({defaultPath:`WeMod-${Date.now()}.log`,filters:[{extensions:["log"],name:this.#n.getValue("general_settings.log_files")}]});if(!t.canceled&&t.filePath)try{await(0,c.X8)(t.filePath)}catch{this.#l.toast({content:"general_settings.failed_to_save_toast",type:"alert"})}}async toggleConsent(){if(this.account.gdpr?.consentGiven)if("general_settings.ok"===await this.#r.show({cancelable:!0,message:"general_settings.gdpr_revoke_confirm_message",options:[{label:"general_settings.cancel",style:"primary"},{label:"general_settings.ok"}]})){this.#c.event("gdpr_consent_revoke",{location:"settings"},l.Io);try{await this.#d.revokeConsent(),await this.#a.reload()}catch{}}else this.#c.event("gdpr_consent_cancel_revoke",{location:"settings"},l.Io);else try{const t=await this.#d.giveConsent();this.#c.event("gdpr_consent_give",{location:"settings"},l.Io),await this.#i.dispatch(p.Ui,t)}catch{}}};b=(0,a.Cg)([(0,m.m6)({selectors:{account:(0,m.$t)((t=>t.account))}}),s.autoinject,(0,a.Sn)("design:paramtypes",[u.F2,d.l,r.s,n.x,g.BasicDialogService,o.il,l.j0])],b)},"settings/resources/elements/general-settings.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template> <require from="../../../shared/resources/elements/toggle.html"></require> <div class="setting"> <div class="setting-name">${\'general_settings.save_log\' | i18n}</div> <div class="setting-info"> <div class="setting-description">${\'general_settings.save_messages\' | i18n}</div> <div class="setting-input"> <button click.delegate="saveLog()" class="standard-button"> ${\'general_settings.save_log\' | i18n}... </button> </div> </div> </div> <div class="setting" if.bind="account.gdpr.consentRequired"> <div class="setting-name">${\'general_settings.gdpr_consent\' | i18n}</div> <div class="setting-info"> <div class="setting-description">${\'general_settings.gdpr_consent_description\' | i18n}</div> <div class="setting-input"> <toggle value.bind="account.gdpr.consentGiven" click.delegate="toggleConsent()" tabindex="0"></toggle> </div> </div> </div> </template> '},"settings/resources/elements/language-selector":(t,e,i)=>{i.r(e),i.d(e,{LanguageSelector:()=>r});var a=i(15215),s=i("aurelia-framework"),o=i("resources/value-converters/flags"),n=i(41772),l=i(20057);const d="auto";let r=class{#g;#n;#u;constructor(t,e){this.secondary=!1,this.busy=!1,this.#n=t,this.#u=e}bind(){const t=this.#n.locales.map((t=>t.tag));t.unshift(d),this.languageLocales=t,this.#g=this.#n.onLocaleChanged((()=>this.#m())),this.#m()}unbind(){this.#g?.dispose(),this.#g=null}#m(){const t=Object.fromEntries(this.#n.locales.map((t=>[t.tag,l.F2.literal(t.name)])));t[d]=l.F2.literal(this.#n.getValue("language_selector.automatic_$lang",{lang:this.#n.getClosestMatchingSupportedLocale().name})),this.languageNames=t,this.selectedLanguageLocale=this.#n.getPreferredLocale()?.toString()??d}async selectedLanguageLocaleChanged(t,e){if(e&&!this.busy){this.busy=!0;try{await this.#n.setLocale(t===d?null:t,"settings")}catch(t){throw this.selectedLanguageLocale=e,t}finally{this.busy=!1}}}get supportedLocales(){const t={};return this.languageLocales.forEach((e=>{n.d.includes(e)&&this.#u.toView(e)?t[e]=!0:t[e]=!1})),t}};(0,a.Cg)([s.observable,(0,a.Sn)("design:type",String)],r.prototype,"selectedLanguageLocale",void 0),(0,a.Cg)([s.bindable,(0,a.Sn)("design:type",Boolean)],r.prototype,"secondary",void 0),(0,a.Cg)([(0,s.computedFrom)("languageLocales"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],r.prototype,"supportedLocales",null),r=(0,a.Cg)([(0,s.autoinject)(),(0,a.Sn)("design:paramtypes",[l.F2,o.FlagIconSvgValueConverter])],r)},"settings/resources/elements/language-selector.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template> <require from="../../../shared/resources/elements/selection-input"></require> <selection-input value.two-way="selectedLanguageLocale" options.bind="languageLocales" labels.bind="languageNames" disabled.bind="busy" secondary.bind="secondary" supported-locales.bind="supportedLocales"></selection-input> </template> '},"settings/resources/elements/notifications-settings":(t,e,i)=>{i.r(e),i.d(e,{NotificationsSettings:()=>g});var a=i(15215),s=i("aurelia-event-aggregator"),o=i("aurelia-framework"),n=i(27958),l=i(50654),d=i(67064),r=i(21795),c=i(64415);let g=class{#l;#p;#b;constructor(t,e,i,a){this.follows=e,this.searchTerms="",this.#l=t,this.#p=i,this.#b=a}async unfollowGame(t){await this.follows.unfollowGames([t.gameId])&&this.#l.toast({content:"notifications_settings.$game_removed_from_followed",i18nParams:{game:t.titleName}})}get filteredFollowedGames(){return(0,c.$)(this.follows.followedGames,this.searchTerms,["titleName","titleTerms"],"titleName")}navigateToFollowedGame(t){this.#p.router.navigateToRoute("title",{titleId:t.titleId,gameId:t.gameId}),this.#b.publish(new r.dY("notifications_settings",t.titleId,t.gameId||null,null,!1))}};(0,a.Cg)([(0,o.computedFrom)("follows.followedGames","searchTerms"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],g.prototype,"filteredFollowedGames",null),g=(0,a.Cg)([(0,o.autoinject)(),(0,a.Sn)("design:paramtypes",[d.l,l.O,n.L,s.EventAggregator])],g)},"settings/resources/elements/notifications-settings.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>n});var a=i(14385),s=i.n(a),o=new URL(i(34635),i.b);const n='<template> <require from="./notifications-settings.scss"></require> <require from="../../../shared/resources/elements/tooltip"></require> <require from="../../../resources/elements/title-thumbnail"></require> <div class="notification-settings"> <div class="setting"> <div class="setting-name"> ${\'settings_dialog.followed_games\' | i18n} <span class="search" if.bind="follows.followedGames.length"> <input type="text" placeholder.bind="\'notifications_settings.search_games\' | i18n" value.bind="searchTerms" disabled.bind="!follows.followedGames.length"> <button click.delegate="searchTerms = \'\'" if.bind="searchTerms && follows.followedGames.length"> ${\'notifications_settings.clear_search\' | i18n} </button> </span> </div> <div class="setting-info"> <div class="setting-input"> <div if.bind="searchTerms && follows.followedGames.length && !filteredFollowedGames.length" class="no-results-message"> ${\'notifications_settings.no_results_message\' | i18n} </div> <div if.bind="!follows.followedGames.length" class="no-results-message"> ${\'notifications_settings.no_followed_games_message\' | i18n} </div> <div repeat.for="followedGame of filteredFollowedGames" class="followed-game"> <a href="#" class="title-link" click.delegate="navigateToFollowedGame(followedGame)"> <title-thumbnail class="thumbnail" src.bind="followedGame.titleThumbnail" width="40"></title-thumbnail> <img class="platform-icon" src.bind="followedGame.gamePlatformId | platformIconSvg"> <span class="name">${followedGame.titleName}</span> </a> <span class="spacer"></span> <span class="delete-wrapper"> <button class="delete" click.delegate="unfollowGame(followedGame)"> <i><inline-svg src="'+s()(o)+'"></inline-svg></i> </button> <tooltip direction="top-right"> <div slot="content">${\'notifications_settings.remove_from_followed\' | i18n}</div> </tooltip> </span> </div> </div> </div> </div> </div> </template> '},"settings/resources/elements/notifications-settings.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>u});var a=i(31601),s=i.n(a),o=i(76314),n=i.n(o),l=i(4417),d=i.n(l),r=new URL(i(84785),i.b),c=n()(s()),g=d()(r);c.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.notification-settings .setting-name{display:flex;align-items:center}.notification-settings .search{flex:1;position:relative;display:flex;align-items:center;margin-left:15px}.notification-settings .search input{font-size:13px;line-height:20px;font-weight:500;flex:1 1 auto;background:url(${g}) 0 center no-repeat;border:0;color:#fff;padding:0 0 0 25px;margin-right:20px}.notification-settings .search input:placeholder{color:rgba(255,255,255,.25)}.notification-settings .search button{font-size:13px;line-height:20px;font-weight:500;background:rgba(0,0,0,0);padding:0;border:0;outline:none;color:rgba(255,255,255,.25);transition:color .15s}.notification-settings .search button:hover{color:#fff}.notification-settings .no-results-message{font-size:15px;color:rgba(255,255,255,.4)}.notification-settings .setting .setting-info .setting-input{flex-grow:1 !important}.notification-settings .followed-game{display:flex;align-items:center;max-width:710px;overflow:hidden}.notification-settings .followed-game:nth-child(1n+0) img{animation-delay:0.1s}.notification-settings .followed-game:nth-child(2n+0) img{animation-delay:0.2s}.notification-settings .followed-game:nth-child(3n+0) img{animation-delay:0.3s}.notification-settings .followed-game:nth-child(4n+0) img{animation-delay:0.4s}.notification-settings .followed-game:nth-child(5n+0) img{animation-delay:0.5s}.notification-settings .followed-game:nth-child(6n+0) img{animation-delay:0.6s}.notification-settings .followed-game:nth-child(7n+0) img{animation-delay:0.7s}.notification-settings .followed-game:nth-child(8n+0) img{animation-delay:0.8s}.notification-settings .followed-game:nth-child(9n+0) img{animation-delay:0.9s}.notification-settings .followed-game+.followed-game{margin-top:15px}.notification-settings .followed-game .thumbnail{display:inline-block;background:rgba(0,0,0,.1);width:40px;height:19px;border-radius:2.5px;margin-right:14px;overflow:hidden;flex:0 0 auto}.notification-settings .followed-game .thumbnail:not(.loaded){animation-name:thumbnail-loading;animation-duration:1s;animation-timing-function:linear;animation-direction:alternate;animation-iteration-count:infinite;background-clip:padding-box}.notification-settings .followed-game .name{font-size:14px;line-height:21px;line-height:19px;font-weight:500;overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;color:rgba(255,255,255,.5);margin-right:14px;flex:0 1 auto}.notification-settings .followed-game .platform-icon{opacity:.4;margin-right:8px;flex:0 0 auto}.notification-settings .followed-game .title-link{flex:0 1 auto;display:flex;overflow:hidden}.notification-settings .followed-game .title-link:hover .name{color:#fff}.notification-settings .followed-game .title-link:hover .platform-icon{opacity:.6}.notification-settings .followed-game .spacer{flex:1 1 auto}.notification-settings .followed-game .delete-wrapper{flex:0 0 auto;position:relative}.notification-settings .followed-game .delete-wrapper .tooltip{white-space:nowrap}.notification-settings .followed-game .delete{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;display:inline-flex;width:20px;height:20px;box-shadow:none;padding:0;border-radius:50%;border:0;align-items:center;justify-content:center;transition:background-color .15s}.notification-settings .followed-game .delete,.notification-settings .followed-game .delete *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .notification-settings .followed-game .delete{border:1px solid #fff}}.notification-settings .followed-game .delete>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.notification-settings .followed-game .delete>*:first-child{padding-left:0}.notification-settings .followed-game .delete>*:last-child{padding-right:0}.notification-settings .followed-game .delete svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .notification-settings .followed-game .delete svg *{fill:CanvasText}}.notification-settings .followed-game .delete svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .notification-settings .followed-game .delete svg{opacity:1}}.notification-settings .followed-game .delete img{height:50%}.notification-settings .followed-game .delete:disabled{opacity:.3}.notification-settings .followed-game .delete:disabled,.notification-settings .followed-game .delete:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.notification-settings .followed-game .delete:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.notification-settings .followed-game .delete:not(:disabled):hover svg{opacity:1}}.notification-settings .followed-game .delete:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.notification-settings .followed-game .delete i{padding:0}.notification-settings .followed-game .delete svg{opacity:1;width:5.5px;height:5.5px}.notification-settings .followed-game .delete,.notification-settings .followed-game .delete:hover{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.25)) !important}`,""]);const u=c},"settings/resources/elements/number.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template bindable="value"> <require from="./text.scss"></require> <require from="../../../resources/value-converters/number"></require> <div class="text-setting"> <input type="number" value.two-way="value | number" maxlength.bind="setting.maxLength || 9999"> </div> </template> '}}]);
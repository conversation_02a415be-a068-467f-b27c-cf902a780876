"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6142],{52871:(e,t,a)=>{a.d(t,{U:()=>c,g:()=>d});var o=a(15215),i=a("aurelia-framework"),n=a(20770),r=a(45660),s=a(59239),l=a("dialogs/feature-announcement-dialog");const d=["2","4","35","36","37","38","45","46","48","139","258","78","90","147","208","230","257","275","56","13","51","148","175","31"];let c=class{#e;#t;constructor(e,t){this.#e=e,this.#t=t}async openIfTitleSupportsLiveLocation(e){const t=await this.#t.state.pipe((0,r.$)(),(0,s.E)("catalog","maps")).toPromise(),a=t?.filter((t=>t.titleId===e)),o=a?.filter((e=>d.includes(e.id)));return o?.length>0&&await this.open()}async open(){return await this.#e.open({featureName:"liveLocation",headerKey:"live_location_announcement_dialog.introducing_the_all_new",featureTitleKey:"live_location_announcement_dialog.live_location_tracking",messageKey:"live_location_announcement_dialog.see_your_position_in_real_time",messageParams:{},videoUrl:"https://media.wemod.com/videos/feature-announcements/live-location.webm",loopVideo:!0}),!0}};c=(0,o.Cg)([(0,i.autoinject)(),(0,o.Sn)("design:paramtypes",[l.FeatureAnnouncementDialogService,n.il])],c)},67761:(e,t,a)=>{a.d(t,{N:()=>c});var o=a(15215),i=a("aurelia-framework"),n=a(20770),r=a(27958),s=a(40127),l=a(48881),d=a("dialogs/feature-announcement-dialog");let c=class{#e;#a;#t;#o;constructor(e,t,a,o){this.#e=e,this.#a=t,this.#t=a,this.#o=o}async open(){await this.#e.open({featureName:"instantHighlight",icon:"replay_30",beta:!0,highlightColor:"#ee343f",headerKey:"instant_highlight_announcement_dialog.introducing_the_all_new",featureTitleKey:"instant_highlight_announcement_dialog.instant_highlights",messageKey:"instant_highlight_announcement_dialog.never_miss_a_gaming_highlight_again",messageParams:{hotkey:this.#o.displayHotkey},videoUrl:"https://media.wemod.com/videos/feature-announcements/instant-highlights.webm",loopVideo:!0,actions:[{labelKey:"instant_highlight_announcement_dialog.okay",onClick:()=>{this.#t.dispatch(l.Kc,{enableCapture:!0}),this.#a.router.navigateToRoute("my-videos")}},{labelKey:"instant_highlight_announcement_dialog.not_now",isSecondary:!0}],cancelable:!1})}};c=(0,o.Cg)([(0,i.autoinject)(),(0,o.Sn)("design:paramtypes",[d.FeatureAnnouncementDialogService,r.L,n.il,s.Re])],c)},"dialogs/maps-education-dialog":(e,t,a)=>{a.r(t),a.d(t,{MapsEducationDialog:()=>p,MapsEducationDialogService:()=>g});var o=a(15215),i=a("aurelia-dialog"),n=a("aurelia-framework"),r=a(62914),s=a(39835),l=a(17275),d=a(54995),c=a(30770);let p=class{#i;#n;#r;#s;#l;constructor(e,t,a,o){this.controller=e,this.#r=t,this.#s=a,this.#l=o}activate(e){this.#i=e.titleId}attached(){const e=this.maps.filter((e=>e.titleId===this.#i));e||this.controller.cancel(),this.#n=e,this.#r.event("maps_education_dialog_open",{},r.Io)}openCheckout(){this.#r.event("maps_education_dialog_pro_cta_click",{},r.Io),this.controller.close(!0),this.#s.open({trigger:"maps_education_dialog",frequency:"yearly",nonInteraction:!1})}openGameMap(){this.#r.event("maps_education_dialog_open_map_cta_click",{},r.Io),this.controller.close(!0),this.#l.openMap(this.#i.toString(),this.#n[0].id)}};p=(0,o.Cg)([(0,d.m6)({selectors:{maps:(0,d.$t)((e=>e.catalog?.maps)),subscription:(0,d.$t)((e=>e.account?.subscription))}}),(0,n.autoinject)(),(0,o.Sn)("design:paramtypes",[i.DialogController,r.j0,c.f,s.I])],p);let g=class extends l.C{constructor(){super(...arguments),this.viewModelClass="dialogs/maps-education-dialog"}};g=(0,o.Cg)([(0,n.autoinject)()],g)},"dialogs/maps-education-dialog.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});const o='<template> <require from="./maps-education-dialog.scss"></require> <require from="./resources/elements/maps-graphic.html"></require> <require from="../resources/elements/pro-cta-label"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="maps-education-dialog"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <div class="layout"> <h1 if.bind="subscription" innerhtml.bind="\'maps_education_dialog.check_out_our_new_interactive_maps\' | i18n | markdown"></h1> <h1 else innerhtml.bind="\'maps_education_dialog.upgrade_to_pro_and_try_interactive_maps\' | i18n | markdown"></h1> <p innerhtml.bind="\'maps_education_dialog.find_everything_youre_looking_for\' | i18n | markdown"></p> <maps-graphic></maps-graphic> <div class="buttons"> <button if.bind="subscription" click.delegate="openGameMap()" class="primary"> ${\'maps_education_dialog.open_maps\' | i18n} </button> <button else click.delegate="openCheckout()" class="primary"> <pro-cta-label></pro-cta-label> </button> <button class="secondary" click.delegate="controller.cancel()"> ${\'maps_education_dialog.ill_try_later\' | i18n} </button> </div> </div> </ux-dialog-body> </ux-dialog> </template> '},"dialogs/maps-education-dialog.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>s});var o=a(31601),i=a.n(o),n=a(76314),r=a.n(n)()(i());r.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.maps-education-dialog{border:0;background:linear-gradient(180deg, var(--theme--secondary-background) 0%, #000 100%) !important;width:750px;height:560px;padding:43px 48px;position:relative}.maps-education-dialog h1{font-size:30px;line-height:36px;color:#fff;text-align:center;margin:0 0 11px}.maps-education-dialog h1 strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;line-height:28px;font-size:20px;letter-spacing:.9px;padding:0 6px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;vertical-align:middle}@media(forced-colors: active){body:not(.override-contrast-mode) .maps-education-dialog h1 strong{border:1px solid #fff}}.maps-education-dialog h1 em{font-style:normal;color:var(--color--accent)}.maps-education-dialog p{font-size:18px;line-height:30px;text-align:center;color:rgba(255,255,255,.6)}.maps-education-dialog p em{font-style:normal;color:#fff}.maps-education-dialog maps-graphic{position:absolute;left:0;bottom:0;width:100%;display:block;border-bottom-left-radius:20px;border-bottom-right-radius:20px;overflow:hidden}.maps-education-dialog maps-graphic svg{display:block}.maps-education-dialog .buttons{position:absolute;left:0;bottom:50px;width:100%;display:inline-flex;align-items:center;justify-content:center}.maps-education-dialog .buttons button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px;flex:0 0 auto}.maps-education-dialog .buttons button,.maps-education-dialog .buttons button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .maps-education-dialog .buttons button{border:1px solid #fff}}.maps-education-dialog .buttons button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.maps-education-dialog .buttons button>*:first-child{padding-left:0}.maps-education-dialog .buttons button>*:last-child{padding-right:0}.maps-education-dialog .buttons button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .maps-education-dialog .buttons button svg *{fill:CanvasText}}.maps-education-dialog .buttons button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .maps-education-dialog .buttons button svg{opacity:1}}.maps-education-dialog .buttons button img{height:50%}.maps-education-dialog .buttons button:disabled{opacity:.3}.maps-education-dialog .buttons button:disabled,.maps-education-dialog .buttons button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.maps-education-dialog .buttons button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.maps-education-dialog .buttons button:not(:disabled):hover svg{opacity:1}}.maps-education-dialog .buttons button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.maps-education-dialog .buttons button.primary{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.maps-education-dialog .buttons button.primary:hover{filter:brightness(1.1)}}.maps-education-dialog .buttons button.secondary{background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}@media(hover: hover){.maps-education-dialog .buttons button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}.maps-education-dialog .buttons button+button{margin-left:10px}",""]);const s=r},"dialogs/maps-nps-dialog":(e,t,a)=>{a.r(t),a.d(t,{MapsNpsDialog:()=>d,MapsNpsDialogService:()=>c});var o=a(15215),i=a("aurelia-dialog"),n=a("aurelia-framework"),r=a(62914),s=a(17275),l=a(54995);let d=class{#d;#r;constructor(e,t){this.controller=e,this.feedback="",this.#r=t}setRating(e){this.rating=e}activate(e){this.#d=e}attached(){this.#r.event("maps_nps_dialog_open",{trigger:this.#d.trigger,userId:this.account.uuid,mapId:this.#d.mapId},r.Io)}async close(){void 0!==this.rating&&this.#r.event("maps_nps_dialog_submit",{score:this.rating,userId:this.account.uuid,mapId:this.#d.mapId,feedback:this.feedback??""},r.Io),await this.controller.close(!0,this.rating)}async skipFeedback(){this.feedback="",await this.close()}async sendFeedback(){await this.close()}};d=(0,o.Cg)([(0,n.autoinject)(),(0,l.m6)({selectors:{account:(0,l.$t)((e=>e.account))}}),(0,o.Sn)("design:paramtypes",[i.DialogController,r.j0])],d);let c=class extends s.C{constructor(){super(...arguments),this.viewModelClass="dialogs/maps-nps-dialog"}};c=(0,o.Cg)([(0,n.autoinject)()],c)},"dialogs/maps-nps-dialog.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});const o='<template> <require from="./maps-nps-dialog.scss"></require> <require from="./resources/elements/nps-score-selector"></require> <require from="../shared/resources/elements/close-button"></require> <ux-dialog class="maps-nps-dialog align-center"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <template if.bind="rating === undefined"> <header>${\'maps_nps_dialog.how_satisfied_are_you_with_the_map\' | i18n}</header> <nps-score-selector set-rating.call="setRating(rating)"> <span slot="lowScoreLabel"> ${\'maps_nps_dialog.not_satisfied_at_all\' | i18n} </span> <span slot="highScoreLabel"> ${\'maps_nps_dialog.extremely_satisfied\' | i18n} </span> </nps-score-selector> </template> <template else> <header>${\'maps_nps_dialog.thank_you_for_your_feedback\' | i18n}</header> <p>${\'maps_nps_dialog.let_us_know_how_to_improve\' | i18n}</p> <textarea placeholder="${\'maps_nps_dialog.feedback_placeholder\' | i18n}" value.bind="feedback" rows="5"></textarea> </template> </ux-dialog-body> <ux-dialog-footer if.bind="rating !== undefined"> <div class="buttons"> <button click.delegate="sendFeedback()">${\'maps_nps_dialog.send\' | i18n}</button> <button class="secondary" click.delegate="skipFeedback()"> ${\'maps_nps_dialog.skip_feedback\' | i18n} </button> </div> </ux-dialog-footer> </ux-dialog> </template> '},"dialogs/maps-nps-dialog.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var o=a(31601),i=a.n(o),n=a(76314),r=a.n(n),s=a(4417),l=a.n(s),d=new URL(a(83959),a.b),c=r()(i()),p=l()(d);c.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.nps-dialog nps-score-selector{margin:23px auto 8px}.nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea:disabled{opacity:.5}.nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.nps-dialog header+p{margin-top:12px}.nps-dialog .score-selector{display:inline-block}.nps-dialog .buttons{display:flex;align-items:start}.maps-nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.maps-nps-dialog nps-score-selector{margin:23px auto 8px}.maps-nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.maps-nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.maps-nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.maps-nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.maps-nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.maps-nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.maps-nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.maps-nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.maps-nps-dialog textarea:disabled{opacity:.5}.maps-nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.maps-nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.maps-nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.maps-nps-dialog header+p{margin-top:12px}.maps-nps-dialog .score-selector{display:inline-block}.maps-nps-dialog .buttons{display:flex;align-items:start}`,""]);const g=c},"dialogs/mod-timers-education-dialog":(e,t,a)=>{a.r(t),a.d(t,{ModTimersEducationDialog:()=>d,ModTimersEducationDialogService:()=>c});var o=a(15215),i=a("aurelia-dialog"),n=a("aurelia-framework"),r=a(62914),s=a(17275),l=a(54995);let d=class{#r;constructor(e,t){this.controller=e,this.#r=t}attached(){this.#r.event("mod_timers_education_dialog_open",{},r.Io)}handleProCtaClick(){this.#r.event("mod_timers_education_pro_cta_click",{isUserPro:!!this.subscription},r.Io),this.controller.close(!0)}};d=(0,o.Cg)([(0,l.m6)({selectors:{subscription:(0,l.$t)((e=>e.account?.subscription))}}),(0,n.autoinject)(),(0,o.Sn)("design:paramtypes",[i.DialogController,r.j0])],d);let c=class extends s.C{constructor(){super(...arguments),this.viewModelClass="dialogs/mod-timers-education-dialog"}};c=(0,o.Cg)([(0,n.autoinject)()],c)},"dialogs/mod-timers-education-dialog.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>r});var o=a(14385),i=a.n(o),n=new URL(a(7096),a.b);const r='<template> <require from="./mod-timers-education-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../pro-promos/resources/elements/mod-timers-illustration.html"></require> <require from="../pro-promos/pro-showcase/resources/elements/pro-showcase-feature"></require> <ux-dialog class="mod-timers-education-dialog"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <pro-showcase-feature feature="${\'pro_showcase.mod_timers_controls\' | i18n}" description="${\'pro_showcase.mod_timers_controls_description\' | i18n}" pro-user-cta-key="pro_showcase.try_now" on-pro-cta-click.call="handleProCtaClick()"> <img class="bg" slot="bg" src="'+i()(n)+'"> <mod-timers-illustration></mod-timers-illustration> </pro-showcase-feature> </ux-dialog> </template> '},"dialogs/mod-timers-education-dialog.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>s});var o=a(31601),i=a.n(o),n=a(76314),r=a.n(n)()(i());r.push([e.id,".mod-timers-education-dialog{width:900px;max-width:100%;padding:0;position:relative}.mod-timers-education-dialog close-button{z-index:2}.mod-timers-education-dialog pro-showcase-feature .bg{border-radius:20px}.mod-timers-education-dialog .mod-timers-graphic .mods-graphic{width:400px;backdrop-filter:blur(10px);position:relative}",""]);const s=r},"dialogs/nps-dialog":(e,t,a)=>{a.r(t),a.d(t,{NpsDialog:()=>m,NpsDialogService:()=>h});var o=a(15215),i=a(44301),n=a("aurelia-dialog"),r=a("aurelia-framework"),s=a(20770),l=a(68663),d=a(62914),c=a(17275),p=a(54995),g=a(14046),u=a(48881);let m=class{#d;#t;#r;#c;constructor(e,t,a,o){this.controller=e,this.feedback="",this.service="TrustPilot",this.#t=t,this.#r=a,this.#c=o}activate(e){this.#d=e}attached(){this.#r.event("nps_dialog_open",{trigger:this.#d.trigger,...this.#p()},d.Io)}setRating(e){this.rating=e}async close(){if(void 0!==this.rating){const e=this.rating<=6?"Detractor":this.rating<=8?"Neutral":"Promoter";this.#r.event("nps_dialog_submit",{score:this.rating,feedback:this.feedback??"",category:e,...this.#p()},d.Io),this.#c.reportUserEvent(i.Q.feedbackProvided);const t=await this.#c.submitAppRating(this.rating,this.feedback??"",!0);this.#t.dispatch(u.Ui,t)}this.controller.close(!0,this.rating)}skipFeedback(){this.feedback="",this.close()}sendFeedback(){this.close()}rateExternal(){this.#r.event("nps_dialog_trustpilot_cta_click",{score:this.rating,...this.#p()},d.Io),window.open("https://wemod.gg/trustpilot-nps","_blank"),this.close()}#p(){return{name:this.#d.name,accountAgeDays:(0,g.c_)(Date.now(),new Date(this.account.joinedAt))}}};m=(0,o.Cg)([(0,p.m6)({selectors:{account:(0,p.$t)((e=>e.account))}}),(0,r.autoinject)(),(0,o.Sn)("design:paramtypes",[n.DialogController,s.il,d.j0,l.x])],m);let h=class extends c.C{constructor(){super(...arguments),this.viewModelClass="dialogs/nps-dialog"}};h=(0,o.Cg)([(0,r.autoinject)()],h)},"dialogs/nps-dialog.html":(e,t,a)=>{a.r(t),a.d(t,{default:()=>o});const o='<template> <require from="./nps-dialog.scss"></require> <require from="../shared/resources/elements/close-button"></require> <require from="./resources/elements/nps-score-selector"></require> <ux-dialog class="nps-dialog align-center"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <template if.bind="rating === undefined"> <header>${\'nps_dialog.how_likely_are_you_to_recommend_wemod_to_a_friend\' | i18n}</header> <nps-score-selector set-rating.call="setRating(rating)"> <span slot="lowScoreLabel"> ${\'nps_dialog.not_likely\' | i18n} </span> <span slot="highScoreLabel"> ${\'nps_dialog.extremely_likely\' | i18n} </span> </nps-score-selector> </template> <template else> <template if.bind="rating < 9"> <template if.bind="rating < 7"> <header>${\'nps_dialog.sorry_you_arent_satisfied\' | i18n}</header> <p>${\'nps_dialog.what_can_we_do_better\' | i18n}</p> </template> <template else> <header>${\'nps_dialog.share_feedback\' | i18n}</header> </template> <textarea placeholder="${\'nps_dialog.feedback_placeholder\' | i18n}" value.bind="feedback" rows="5"></textarea> </template> <template else> <header>${\'nps_dialog.glad_youre_enjoying_the_app\' | i18n}</header> <p>${\'nps_dialog.review_on_trustpilot\' | i18n}</p> </template> </template> </ux-dialog-body> <ux-dialog-footer if.bind="rating !== undefined"> <template if.bind="rating < 9"> <div class="buttons"> <button click.delegate="sendFeedback()">${\'nps_dialog.send\' | i18n}</button> <button class="secondary" click.delegate="skipFeedback()"> ${\'nps_dialog.skip_feedback\' | i18n} </button> </div> </template> <template else> <button class="secondary" click.delegate="close()">${\'nps_dialog.no_thanks\' | i18n}</button> <button click.delegate="rateExternal()">${\'nps_dialog.sure\' | i18n}</button> </template> </ux-dialog-footer> </ux-dialog> </template> '},"dialogs/nps-dialog.scss":(e,t,a)=>{a.r(t),a.d(t,{default:()=>g});var o=a(31601),i=a.n(o),n=a(76314),r=a.n(n),s=a(4417),l=a.n(s),d=new URL(a(83959),a.b),c=r()(i()),p=l()(d);c.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${p}) format("woff2")}.material-symbols-outlined{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.nps-dialog{width:600px;padding:20px 20px 30px;background:var(--theme--secondary-background) !important;text-align:center}.nps-dialog nps-score-selector{margin:23px auto 8px}.nps-dialog textarea{font-size:14px;line-height:21px;font-weight:500;display:block;color:rgba(255,255,255,.9);padding:4px 9px 5px 9px;border:1px solid rgba(0,0,0,0);transition:color .15s,border-color .15s;position:relative;background:rgba(255,255,255,.05);border-radius:8px;height:28px;width:100%;height:auto;resize:none;margin-top:20px}.nps-dialog textarea::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive,.nps-dialog textarea::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.nps-dialog textarea::-webkit-scrollbar-thumb:window-inactive:hover,.nps-dialog textarea::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.nps-dialog textarea::-ms-input-placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea::placeholder{font-size:14px;line-height:21px;font-weight:500;color:rgba(255,255,255,.5)}.nps-dialog textarea:disabled{opacity:.5}.nps-dialog textarea:focus{border-color:var(--theme--highlight);caret-color:var(--theme--highlight);color:#fff}.nps-dialog textarea:focus+*{--input__label--color: rgba(255, 255, 255, 0.6)}.nps-dialog header{font-weight:800;font-size:21px;line-height:30px;font-weight:700;text-align:center;margin:0 auto;color:#fff;display:block}.nps-dialog header+p{margin-top:12px}.nps-dialog .score-selector{display:inline-block}.nps-dialog .buttons{display:flex;align-items:start}`,""]);const g=c}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[3613],{61107:(t,e,s)=>{function i(t,e,s,i){return new(s||(s=Promise))((function(n,r){function o(t){try{h(i.next(t))}catch(t){r(t)}}function a(t){try{h(i.throw(t))}catch(t){r(t)}}function h(t){var e;t.done?n(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}h((i=i.apply(t,e||[])).next())}))}s.d(e,{U:()=>f}),"function"==typeof SuppressedError&&SuppressedError;class n{static get touch(){return window.Modernizr&&!0===window.Modernizr.touch||!!(window.navigator.maxTouchPoints>0||"ontouchstart"in window||window.DocumentTouch&&document instanceof window.DocumentTouch)}static get observer(){return"MutationObserver"in window||"WebkitMutationObserver"in window}static get backdropFilter(){return CSS.supports("backdrop-filter","blur(0px)")||CSS.supports("-webkit-backdrop-filter","blur(0px)")}static get passiveListener(){let t=!1;try{const e=Object.defineProperty({},"passive",{get(){t=!0}});window.addEventListener("testPassiveListener",null,e)}catch(t){}return t}static get gestures(){return"ongesturestart"in window}}class r{constructor(){this.ios=!1,this.android=!1,this.androidChrome=!1,this.desktop=!1,this.iphone=!1,this.ipod=!1,this.ipad=!1,this.edge=!1,this.ie=!1,this.firefox=!1,this.macos=!1,this.windows=!1,this.cordova=!(!window.cordova&&!window.phonegap),this.phonegap=!(!window.cordova&&!window.phonegap),this.electron=!1,this.ionic=!!document.querySelector("ion-app");const t=window.navigator.platform,e=window.navigator.userAgent,s=window.screen.width,i=window.screen.height;let r=e.match(/(Android);?[\s\/]+([\d.]+)?/),o=e.match(/(iPad).*OS\s([\d_]+)/),a=e.match(/(iPod)(.*OS\s([\d_]+))?/),h=!this.ipad&&e.match(/(iPhone\sOS|iOS)\s([\d_]+)/),l=e.indexOf("MSIE ")>=0||e.indexOf("Trident/")>=0,c=e.indexOf("Edge/")>=0,p=e.indexOf("Gecko/")>=0&&e.indexOf("Firefox/")>=0,d="Win32"===t,u=e.toLowerCase().indexOf("electron")>=0,g="MacIntel"===t;!o&&g&&n.touch&&(1024===s&&1366===i||834===s&&1194===i||834===s&&1112===i||768===s&&1024===i)&&(o=e.match(/(Version)\/([\d.]+)/),g=!1),this.ie=l,this.edge=c,this.firefox=p,r&&!d&&(this.os="android",this.osVersion=r[2],this.android=!0,this.androidChrome=e.toLowerCase().indexOf("chrome")>=0),(o||h||a)&&(this.os="ios",this.ios=!0),h&&!a&&(this.osVersion=h[2].replace(/_/g,"."),this.iphone=!0),o&&(this.osVersion=o[2].replace(/_/g,"."),this.ipad=!0),a&&(this.osVersion=a[3]?a[3].replace(/_/g,"."):null,this.ipod=!0),this.ios&&this.osVersion&&e.indexOf("Version/")>=0&&"10"===this.osVersion.split(".")[0]&&(this.osVersion=e.toLowerCase().split("version/")[1].split(" ")[0]),this.webView=!(!(h||o||a)||!e.match(/.*AppleWebKit(?!.*Safari)/i)&&!window.navigator.standalone)||window.matchMedia&&window.matchMedia("(display-mode: standalone)").matches,this.webview=this.webView,this.standalone=this.webView,this.desktop=!(this.ios||this.android)||u,this.desktop&&(this.electron=u,this.macos=g,this.windows=d,this.macos&&(this.os="macos"),this.windows&&(this.os="windows")),this.pixelRatio=window.devicePixelRatio||1}}class o{constructor(t){this.instance=t,this.allowClick=!0,this.disableDragAngle=!1,this.mouseDown=!1,this.contentScrollTop=0,this.steps=[],this.isScrolling=!1,this.touchStartCb=t=>this.touchStart(t),this.touchMoveCb=t=>this.touchMove(t),this.touchEndCb=t=>this.touchEnd(t),this.onScrollCb=t=>this.onScroll(t),this.onClickCb=t=>this.onClick(t),this.settings=this.instance.settings,this.device=this.instance.device,this.breakpoints=this.instance.breakpoints,this.transitions=this.instance.transitions,this.keyboardEvents=this.instance.keyboardEvents,this.touchEvents=this.getTouchEvents(),this.swipeNextSensivity=window.hasOwnProperty("cordova")?this.settings.fastSwipeSensivity+2:this.settings.fastSwipeSensivity}getTouchEvents(){const t=["touchstart","touchmove","touchend","touchcancel"];let e=["mousedown","mousemove","mouseup","mouseleave"];const s={start:t[0],move:t[1],end:t[2],cancel:t[3]},i={start:e[0],move:e[1],end:e[2],cancel:e[3]};return n.touch||!this.settings.simulateTouch?s:i}attachAllEvents(){this.settings.dragBy?this.settings.dragBy.forEach((t=>{const e=document.querySelector(t);e&&this.eventListeners("addEventListener",e)})):this.eventListeners("addEventListener",this.instance.paneEl),this.settings.topperOverflow&&this.instance.overflowEl.addEventListener("scroll",this.onScrollCb),this.settings.handleKeyboard&&this.device.cordova&&(window.addEventListener("keyboardWillShow",this.keyboardEvents.onKeyboardShowCb),window.addEventListener("keyboardWillHide",this.keyboardEvents.onKeyboardWillHideCb)),this.device.ionic&&this.device.android&&document.querySelectorAll(".ion-page").forEach((t=>{t.addEventListener("scroll",(e=>{t.scrollTop&&t.scrollTo({top:0})}))})),window.addEventListener("resize",this.keyboardEvents.onWindowResizeCb)}detachAllEvents(){this.settings.dragBy?this.settings.dragBy.forEach((t=>{const e=document.querySelector(t);e&&this.eventListeners("removeEventListener",e)})):this.eventListeners("removeEventListener",this.instance.paneEl),this.settings.topperOverflow&&this.instance.overflowEl.removeEventListener("scroll",this.onScrollCb),this.settings.handleKeyboard&&this.device.cordova&&(window.removeEventListener("keyboardWillShow",this.keyboardEvents.onKeyboardShowCb),window.removeEventListener("keyboardWillHide",this.keyboardEvents.onKeyboardWillHideCb)),window.removeEventListener("resize",this.keyboardEvents.onWindowResizeCb)}resetEvents(){this.detachAllEvents(),this.attachAllEvents()}eventListeners(t,e){if(n.touch){const s=!("touchstart"!==this.touchEvents.start||!n.passiveListener||!this.settings.passiveListeners)&&{passive:!0,capture:!1};e[t](this.touchEvents.start,this.touchStartCb,s),e[t](this.touchEvents.move,this.touchMoveCb,!!n.passiveListener&&{passive:!1,capture:!1}),e[t](this.touchEvents.end,this.touchEndCb,s),e[t](this.touchEvents.cancel,this.touchEndCb,s)}else e[t](this.touchEvents.start,this.touchStartCb,!1),e[t](this.touchEvents.move,this.touchMoveCb,!1),e[t](this.touchEvents.end,this.touchEndCb,!1),e[t](this.touchEvents.cancel,this.touchEndCb,!1);this.settings.preventClicks&&e[t]("click",this.onClickCb,!0)}touchStart(t){if(this.instance.emit("onDragStart",t),this.allowClick=!0,this.instance.disableDragEvents)return;this.disableDragAngle=!1,this.isScrolling=!1,this.instance.preventedDismiss=!1;const{clientY:e,clientX:s}=this.getEventClientYX(t,"touchstart");e&&s&&(this.startY=e,this.startX=s,"mousedown"===t.type&&(this.mouseDown=!0),this.contentScrollTop&&this.willScrolled()&&!this.isDraggableElement(t)&&(this.startY+=this.contentScrollTop),this.steps.push({posY:this.startY,posX:this.startX,time:Date.now()}))}touchMove(t){var e;const{clientY:s,clientX:i,velocityY:n}=this.getEventClientYX(t,"touchmove");if(!s||!i)return;if("mousemove"===t.type&&!this.mouseDown)return;if(this.steps.length||this.steps.push({posY:s,posX:i,time:Date.now()}),t.delta=(null===(e=this.steps[0])||void 0===e?void 0:e.posY)-s,this.allowClick=!1,this.isFormElement(t.target)&&this.isElementScrollable(t.target))return;if(this.instance.disableDragEvents)return void(this.steps=[]);if(this.disableDragAngle)return;if(this.instance.preventedDismiss)return;this.settings.touchMoveStopPropagation&&t.stopPropagation();const r=s-this.steps[this.steps.length-1].posY,o=i-this.steps[this.steps.length-1].posX;if(!Math.abs(r)&&!Math.abs(o))return;this.instance.emit("onDrag",t),this.instance.setGrabCursor(!0,!0);let a=this.instance.getPanelTransformY()+r,h=this.instance.getPanelTransformX()+o;if(this.steps.length<2){n<1&&(a=this.instance.getPanelTransformY()+r*n);let t=new WebKitCSSMatrix(window.getComputedStyle(this.instance.paneEl).transform).m42-this.instance.getPanelTransformY();Math.abs(t)&&(a+=t)}if(this.steps.length>2&&this.isFormElement(document.activeElement)&&!this.isFormElement(t.target)&&(document.activeElement.blur(),this.keyboardEvents.inputBluredbyMove=!0),this.settings.touchAngle&&!this.isScrolling){let t;const e=i-this.startX,n=s-this.startY;if(t=180*Math.atan2(Math.abs(n),Math.abs(e))/Math.PI,e*e+n*n>=25&&90-t>this.settings.touchAngle&&1===this.steps.length)return void(this.disableDragAngle=!0)}if("auto"===this.instance.overflowEl.style.overflowY&&this.scrollPreventDrag(t)&&!this.isDraggableElement(t))return;let l=this.handleSuperposition({clientX:i,clientY:s,newVal:a,newValX:h,diffY:r,diffX:o});if(l&&(isNaN(l.y)||(a=l.y),isNaN(l.x)||(h=l.x)),!1!==l&&(this.instance.getPanelTransformY()!==a||this.instance.getPanelTransformX()!==h)){if(!this.instance.preventedDismiss&&this.instance.preventDismissEvent&&this.settings.bottomClose){let t=(-this.breakpoints.topper+this.breakpoints.topper-this.instance.getPanelTransformY())/this.breakpoints.topper/-8;if(a=this.instance.getPanelTransformY()+r*(.5-t),-1*(s-220-this.instance.screen_height)<=this.instance.screen_height-this.breakpoints.bottomer)return this.instance.preventedDismiss=!0,this.instance.emit("onWillDismiss",{prevented:!0}),void this.instance.moveToBreak(this.breakpoints.prevBreakpoint)}this.instance.checkOpacityAttr(a),this.instance.checkOverflowAttr(a),this.transitions.doTransition({type:"move",translateY:a,translateX:h}),this.steps.push({posY:s,posX:i,time:Date.now()})}}touchEnd(t){var e,s;if(this.instance.disableDragEvents)return;if("mouseleave"===t.type&&!this.mouseDown)return;"mouseup"!==t.type&&"mouseleave"!==t.type||(this.mouseDown=!1);let i,n=this.breakpoints.getClosestBreakY();this.fastSwipeNext("Y")&&(n=this.instance.swipeNextPoint((null===(e=this.steps[this.steps.length-1])||void 0===e?void 0:e.posY)-(null===(s=this.steps[this.steps.length-2])||void 0===s?void 0:s.posY),this.swipeNextSensivity,n),i=this.settings.fastSwipeClose&&this.breakpoints.currentBreakpoint<n),this.breakpoints.currentBreakpoint=n;let r=!1;this.isFormElement(document.activeElement)&&!this.isFormElement(t.target)&&2===this.steps.length&&(r=!0),this.instance.emit("onDragEnd",t),this.steps=[],delete this.startPointOverTop,this.allowClick||r||(i?this.instance.destroy({animate:!0}):(this.instance.checkOpacityAttr(n),this.instance.checkOverflowAttr(n),this.instance.setGrabCursor(!0,!1),this.settings.bottomClose&&n===this.breakpoints.breaks.bottom?this.instance.destroy({animate:!0}):(this.instance.getPanelTransformY()===n&&this.instance.emit("onTransitionEnd",{target:this.instance.paneEl}),this.transitions.doTransition({type:"end",translateY:n}))))}onScroll(t){return i(this,void 0,void 0,(function*(){this.isScrolling=!0,this.contentScrollTop=t.target.scrollTop}))}onClick(t){if(this.allowClick){if(!this.device.cordova&&this.device.android&&this.isFormElement(t.target))this.keyboardEvents.onKeyboardShowCb({keyboardHeight:this.instance.screen_height-window.innerHeight});else if(this.settings.clickBottomOpen){if(this.isFormElement(document.activeElement))return;if(this.breakpoints.breaks.bottom===this.instance.getPanelTransformY()){let t;this.settings.breaks.top.enabled&&(t="top"),this.settings.breaks.middle.enabled&&(t="middle"),this.instance.moveToBreak(t)}}}else this.settings.preventClicks&&(t.preventDefault(),t.stopPropagation(),t.stopImmediatePropagation())}fastSwipeNext(t){var e,s;const i=(null===(e=this.steps[this.steps.length-1])||void 0===e?void 0:e["pos"+t])-(null===(s=this.steps[this.steps.length-2])||void 0===s?void 0:s["pos"+t]);return Math.abs(i)>=this.swipeNextSensivity}handleSuperposition(t){if(!this.settings.upperThanTop&&(t.newVal<=this.breakpoints.topper||t.clientY<=this.breakpoints.topper&&!this.settings.zStack))return this.steps=[],{y:this.breakpoints.topper};if(this.settings.upperThanTop&&(t.newVal<=this.breakpoints.topper||this.startPointOverTop)){this.startPointOverTop||(this.startPointOverTop=t.clientY),this.startPointOverTop<t.clientY&&delete this.startPointOverTop;const e=this.instance.screen_height-this.instance.screenHeightOffset,s=(e-this.instance.getPanelTransformY())/(e-this.breakpoints.topper)/8;return{y:this.instance.getPanelTransformY()+t.diffY*s}}return!this.settings.lowerThanBottom&&t.newVal>=this.breakpoints.bottomer?{y:this.breakpoints.bottomer}:void 0}getEventClientYX(t,e){var s,i;const n=t.type===e&&t.targetTouches&&(t.targetTouches[0]||t.changedTouches[0]),r=t.type===e?null==n?void 0:n.clientY:t.clientY,o=t.type===e?null==n?void 0:n.clientX:t.clientX,a=Date.now()-((null===(s=this.steps[this.steps.length-1])||void 0===s?void 0:s.time)||0);return{clientY:r,clientX:o,velocityY:Math.abs(r-((null===(i=this.steps[this.steps.length-1])||void 0===i?void 0:i.posY)||0))/a}}scrollPreventDrag(t){let e=!1;return this.contentScrollTop>0&&(e=!0),e}willScrolled(){return!(!this.isElementScrollable(this.instance.overflowEl)||"hidden"===this.instance.overflowEl.style.overflow)}isDraggableElement(t){return t.target===this.instance.draggableEl||t.target===this.instance.moveEl}isFormElement(t){return!!(t&&t.tagName&&["input","select","option","textarea","button","label"].includes(t.tagName.toLowerCase()))}isElementScrollable(t){return t.scrollHeight>t.clientHeight}}class a{constructor(t){this.instance=t,this.inputBluredbyMove=!1,this.keyboardVisibleResize=!1,this.inputBottomOffset=0,this.previousInputBottomOffset=0,this.prevNewHeight=0,this.onKeyboardShowCb=t=>this.onKeyboardShow(t),this.onKeyboardWillHideCb=t=>this.onKeyboardWillHide(t),this.onWindowResizeCb=t=>this.onWindowResize(t),this.device=this.instance.device,this.breakpoints=this.instance.breakpoints}onKeyboardShow(t){return i(this,void 0,void 0,(function*(){if(!this.isPaneDescendant(document.activeElement))return;if(!this.isOnViewport())return;this.keyboardVisibleResize=!0,this.fixBodyKeyboardResize(!0);let e=-1*(this.instance.getPanelTransformY()-this.instance.screen_height);const s=document.activeElement,i=this.getActiveInputClientBottomRect(),n=this.instance.screen_height-i-this.inputBottomOffset;let r=this.device.cordova&&this.device.android?130:100,o=0,a=e+(t.keyboardHeight-n);if(this.prevNewHeight&&(o=this.previousInputBottomOffset-i,a=this.prevNewHeight),!s.isEqualNode(this.prevFocusedElement)&&t.keyboardHeight>n){this.prevNewHeight=a-o,this.prevFocusedElement=document.activeElement;let e=a-o+r;e>this.instance.getPaneHeight()+t.keyboardHeight&&(e=this.instance.getPaneHeight()+t.keyboardHeight),yield this.instance.moveToHeight(e);const s=this.getActiveInputClientBottomRect();this.previousInputBottomOffset=s,this.inputBottomOffset||(this.inputBottomOffset=i-s)}}))}onKeyboardWillHide(t){this.isOnViewport()&&(this.fixBodyKeyboardResize(!1),this.inputBottomOffset=0,this.previousInputBottomOffset=0,this.prevNewHeight=0,delete this.prevFocusedElement,this.inputBluredbyMove?this.inputBluredbyMove=!1:this.instance.isHidden()||this.instance.getPanelTransformY()!==this.breakpoints.breaks[this.breakpoints.prevBreakpoint]&&this.instance.moveToBreak(this.breakpoints.prevBreakpoint))}onWindowResize(t){return i(this,void 0,void 0,(function*(){if(this.isFormElement(document.activeElement))this.device.cordova||this.onKeyboardShow({keyboardHeight:this.instance.screen_height-window.innerHeight});else{if(this.keyboardVisibleResize)return this.keyboardVisibleResize=!1,void(this.device.cordova||this.onKeyboardWillHide({}));yield new Promise((t=>setTimeout((()=>t(!0)),150))),this.instance.updateScreenHeights(),this.breakpoints.buildBreakpoints(JSON.parse(this.breakpoints.lockedBreakpoints))}}))}isPaneDescendant(t){if(!t)return!1;let e=t.parentNode;for(;null!=e;){if(e==this.instance.paneEl)return!0;e=e.parentNode}return!1}isFormElement(t){return!!(t&&t.tagName&&["input","select","option","textarea","button","label"].includes(t.tagName.toLowerCase()))}isOnViewport(){return!this.instance.paneEl||0!==this.instance.paneEl.offsetWidth||0!==this.instance.paneEl.offsetHeight}getActiveInputClientBottomRect(){var t,e;return document.activeElement.classList.contains("native-textarea")||document.activeElement.classList.contains("native-input")?(null===(e=null===(t=document.activeElement.parentElement)||void 0===t?void 0:t.parentElement)||void 0===e?void 0:e.parentElement).getBoundingClientRect().bottom:document.activeElement.getBoundingClientRect().bottom}fixBodyKeyboardResize(t){if(!this.instance.paneEl)return;const e=document.querySelector("meta[name=viewport]");window.requestAnimationFrame((()=>{t?(document.documentElement.style.setProperty("overflow","hidden"),document.body.style.setProperty("min-height",`${this.instance.screen_height}px`),e.setAttribute("content","height="+this.instance.screen_height+", width=device-width, initial-scale=1.0")):(document.documentElement.style.removeProperty("overflow"),document.body.style.removeProperty("min-height"),e.setAttribute("content","viewport-fit=cover, width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=no"))}))}}class h{constructor(){this.instance={initialBreak:"middle",horizontal:!1,horizontalOffset:null,inverse:!1,parentElement:null,followerElement:null,cssClass:null,fitHeight:!1,maxFitHeight:null,fitScreenHeight:!0,ionContentScroll:!1,backdrop:!1,backdropBlur:!1,backdropOpacity:.6,animationType:"ease",animationDuration:300,dragBy:null,bottomOffset:0,bottomClose:!1,fastSwipeClose:!1,fastSwipeSensivity:3,freeMode:!1,buttonDestroy:!0,topperOverflow:!0,topperOverflowOffset:0,lowerThanBottom:!0,upperThanTop:!1,showDraggable:!0,draggableOver:!1,clickBottomOpen:!0,preventClicks:!0,handleKeyboard:!0,simulateTouch:!0,passiveListeners:!0,touchMoveStopPropagation:!1,touchAngle:45,breaks:{},modal:null,zStack:null,events:null,modules:null}}}class l{constructor(t){this.instance=t,this.breaks={},this.brs=[],this.beforeBuildBreakpoints=()=>{},this.defaultBreaksConf={top:{enabled:!0,height:window.innerHeight-47.25},middle:{enabled:!0,height:300},bottom:{enabled:!0,height:100}},this.settings=this.instance.settings}buildBreakpoints(t,e=0,s=!0){var n,r;return i(this,void 0,void 0,(function*(){if(this.breaks={},this.conf=t,this.settings.bottomOffset=e||this.settings.bottomOffset,yield this.beforeBuildBreakpoints(),["top","middle","bottom"].forEach((t=>{var e;this.settings.breaks[t]||(this.settings.breaks[t]=this.defaultBreaksConf[t]),this.conf&&this.conf[t]&&(this.settings.breaks[t]=this.conf[t]),this.instance.emit("beforeBreakHeightApplied",{break:t}),(null===(e=this.settings.breaks[t])||void 0===e?void 0:e.enabled)&&(this.breaks[t]=this.breaks[t]||this.instance.screenHeightOffset,this.breaks[t]-=this.settings.bottomOffset,this.breaks[t]-=this.settings.breaks[t].height)})),this.lockedBreakpoints||(this.lockedBreakpoints=JSON.stringify(this.settings.breaks)),this.instance.isPanePresented()||this.settings.breaks[this.settings.initialBreak].enabled||console.warn("Cupertino Pane: Please set initialBreak for enabled breakpoint"),this.settings.breaks.middle.height>=this.settings.breaks.top.height&&console.warn("Cupertino Pane: Please set middle height lower than top height"),this.settings.breaks.middle.height<=this.settings.breaks.bottom.height&&console.warn("Cupertino Pane: Please set bottom height lower than middle height"),this.brs=[],["top","middle","bottom"].forEach((t=>{this.settings.breaks[t].enabled&&this.brs.push(this.breaks[t])})),this.topper=this.brs.reduce(((t,e)=>e<t?e:t)),this.bottomer=this.brs.reduce(((t,e)=>Math.abs(e)>Math.abs(t)?e:t)),this.instance.isPanePresented()||(this.currentBreakpoint=this.breaks[this.settings.initialBreak]),this.instance.isPanePresented()&&((null===(n=this.settings.breaks[this.prevBreakpoint])||void 0===n?void 0:n.enabled)&&(this.instance.isHidden()||this.instance.moveToBreak(this.prevBreakpoint,s?"breakpoint":"move")),!(null===(r=this.settings.breaks[this.prevBreakpoint])||void 0===r?void 0:r.enabled)&&!this.instance.isHidden())){let t=this.instance.swipeNextPoint(1,1,this.getClosestBreakY());const e=Object.entries(this.breaks).find((e=>e[1]===t));this.instance.moveToBreak(e[0])}this.instance.scrollElementInit(),this.instance.checkOpacityAttr(this.currentBreakpoint),this.instance.checkOverflowAttr(this.currentBreakpoint),this.instance.emit("buildBreakpointsCompleted")}))}getCurrentBreakName(){return this.breaks.top===this.currentBreakpoint?"top":this.breaks.middle===this.currentBreakpoint?"middle":this.breaks.bottom===this.currentBreakpoint?"bottom":null}getClosestBreakY(){return this.brs.reduce(((t,e)=>Math.abs(e-this.instance.getPanelTransformY())<Math.abs(t-this.instance.getPanelTransformY())?e:t))}}var c,p;(p=c||(c={})).Present="present",p.Destroy="destroy",p.Move="move",p.Breakpoint="breakpoint",p.Hide="hide",p.TouchEnd="end";class d{constructor(t){this.instance=t,this.isPaneHidden=!1,this.settings=this.instance.settings,this.breakpoints=this.instance.breakpoints}doTransition(t={}){return new Promise((e=>i(this,void 0,void 0,(function*(){var s,i;if(t.type===c.Move)return this.instance.emit("onMoveTransitionStart",{translateY:t.translateY}),this.instance.paneEl.style.transition="all 0ms linear 0ms",this.setPaneElTransform(t),e(!0);const n=()=>(t.type===c.Destroy&&this.instance.destroyResets(),this.instance.paneEl.style.transition="initial",t.type===c.Hide&&(this.isPaneHidden=!0),t.type!==c.Breakpoint&&t.type!==c.Present&&t.type!==c.TouchEnd||(this.isPaneHidden=!1),t.type!==c.Hide&&t.type!==c.Destroy||!this.instance.ionContent||this.settings.ionContentScroll||this.doesPanesExists()||this.instance.ionContent.setAttribute("scroll-y","true"),this.instance.emit("onTransitionEnd",{type:t.type,target:document.body.contains(this.instance.paneEl)?this.instance.paneEl:null}),this.instance.paneEl.removeEventListener("transitionend",n),e(!0));if(t.type===c.Breakpoint||t.type===c.TouchEnd||t.type===c.Present||t.type===c.Hide||t.type===c.Destroy){let r=(null===(s=t.conf)||void 0===s?void 0:s.transition)?JSON.parse(JSON.stringify(t.conf.transition)):{};if(t.type===c.TouchEnd&&this.settings.freeMode)return e(!0);const o=Object.entries(this.breakpoints.breaks).find((e=>e[1]===t.translateY));let a=o&&(null===(i=this.settings.breaks[o[0]])||void 0===i?void 0:i.bounce),h=this.buildTransitionValue(a,r.duration);this.instance.paneEl.style.setProperty("transition",h),this.instance.emit("onTransitionStart",{type:t.type,translateY:{new:t.translateY},transition:this.instance.paneEl.style.transition}),this.setPaneElTransform(t),r.to&&(r.to.transform||(r.to.transform=`translateY(${this.breakpoints.breaks[this.settings.initialBreak]}px) translateZ(0px)`),Object.assign(this.instance.paneEl.style,r.to));let l=Object.entries(this.breakpoints.breaks).find((e=>e[1]===t.translateY));l&&(this.breakpoints.prevBreakpoint=l[0]),this.instance.paneEl.addEventListener("transitionend",n)}}))))}setPaneElTransform(t){this.instance.paneEl.style.transform=`translateY(${t.translateY}px) translateZ(0px)`}buildTransitionValue(t,e){return t?"all 300ms cubic-bezier(.155,1.105,.295,1.12)":`all ${e||this.settings.animationDuration}ms ${this.settings.animationType}`}doesPanesExists(){return!!document.querySelector(".cupertino-pane-wrapper")}}function u(t,e,s){if(!this.eventsListeners)return;if("function"!=typeof e)return;const i=s?"unshift":"push";t.split(" ").forEach((t=>{this.eventsListeners[t]||(this.eventsListeners[t]=[]),this.eventsListeners[t][i](e)}))}function g(...t){if(!this.eventsListeners)return;let e=t[0],s=t.slice(1,t.length);(Array.isArray(e)?e:e.split(" ")).forEach((t=>{var e;(null===(e=this.eventsListeners)||void 0===e?void 0:e[t])&&this.eventsListeners[t].forEach((t=>t.apply(this,s)))}))}class b{static CollectSettings(t){return t.horizontal?Object.assign(Object.assign({},t),b.forceSettings):t}constructor(t){if(this.instance=t,this.settings=this.instance.settings,this.transitions=this.instance.transitions,this.events=this.instance.events,!this.settings.horizontal)return null;this.transitions.setPaneElTransform=t=>this.setPaneElTransform(t),this.instance.on("onTransitionEnd",(t=>{"breakpoint"!==t.type&&"present"!==t.type||this.instance.getPanelTransformX()||this.calcHorizontalBreaks()})),this.instance.on("onDidPresent",(t=>{t.animate||this.calcHorizontalBreaks()})),this.instance.on("onDragEnd",(t=>{this.fastSwipeNext=this.events.fastSwipeNext("X")}))}calcHorizontalBreaks(){this.defaultRect={width:this.instance.paneEl.getBoundingClientRect().width,left:this.instance.paneEl.getBoundingClientRect().left,right:this.instance.paneEl.getBoundingClientRect().right},this.horizontalBreaks=[-this.defaultRect.left+this.settings.horizontalOffset,window.innerWidth-this.defaultRect.left-this.defaultRect.width-this.settings.horizontalOffset]}setPaneElTransform(t){let e=t.translateX;"end"===t.type&&(e=this.getClosestBreakX(),this.fastSwipeNext&&("left"===this.currentBreakpoint&&this.instance.getPanelTransformX()>this.horizontalBreaks[0]&&(e=this.horizontalBreaks[1]),"right"===this.currentBreakpoint&&this.instance.getPanelTransformX()<this.horizontalBreaks[1]&&(e=this.horizontalBreaks[0])),this.currentBreakpoint=e===this.horizontalBreaks[0]?"left":"right"),this.instance.paneEl.style.transform=`translateX(${e||0}px) translateY(${t.translateY}px) translateZ(0px)`}getClosestBreakX(){return this.horizontalBreaks.reduce(((t,e)=>Math.abs(e-this.instance.getPanelTransformX())<Math.abs(t-this.instance.getPanelTransformX())?e:t))}}b.forceSettings={touchAngle:null};class m{static CollectSettings(t){return t.modal?Object.assign(Object.assign({},t),m.ForceSettings):t}constructor(t){this.instance=t,this.modalDefaults={transition:"fade",flying:!1,dismissOnIntense:!1},this.settings=this.instance.settings,this.events=this.instance.events,this.breakpoints=this.instance.breakpoints,this.transitions=this.instance.transitions,this.settings.modal&&(this.settings.modal="object"==typeof this.settings.modal?Object.assign(Object.assign({},this.modalDefaults),this.settings.modal):this.modalDefaults,this.instance.customPresent=this.instance.present,this.instance.present=t=>this.present(t),this.instance.customDestroy=this.instance.destroy,this.instance.destroy=t=>this.destroy(t),this.events.handleSuperposition=t=>this.handleSuperposition(t),this.transitions.setPaneElTransform=t=>this.setPaneElTransform(t),this.instance.on("beforeBreakHeightApplied",(t=>{"top"===t.break&&(this.settings.breaks.top.height-=2*this.settings.bottomOffset,this.settings.breaks.top.height+=(this.instance.screen_height-this.settings.breaks.top.height)/2),"bottom"===t.break&&(this.settings.breaks.bottom={enabled:!1}),this.instance.addStyle(`\n        .cupertino-pane-wrapper .pane {\n          transform-origin: center ${this.breakpoints.breaks[this.settings.initialBreak]}px\n        }\n      `)})),this.instance.on("rendered",(()=>{this.instance.addStyle("\n        .cupertino-pane-wrapper .pane {\n          border-radius: var(--cupertino-pane-border-radius, 20px) \n                         var(--cupertino-pane-border-radius, 20px)\n                         var(--cupertino-pane-border-radius, 20px)\n                         var(--cupertino-pane-border-radius, 20px);\n          width: calc(100% - 16px) !important;\n          margin: auto;\n        }\n        .cupertino-pane-wrapper .pane.modal-flying {\n          animation: modalFlyingX 2000ms ease-in-out infinite alternate,\n                     modalFlyingY 3000ms ease-in-out infinite alternate;\n        }\n        @keyframes modalFlyingX {\n          0% { left: -10px; }\n          100% { left: 10px; }\n        }\n        @keyframes modalFlyingY {\n          0% { top: -10px; }\n          100% { top: 0px; }\n        }\n      "),this.settings.modal.flying&&this.instance.paneEl.classList.add("modal-flying"),this.settings.modal.dismissOnIntense&&this.instance.enableDrag()})))}setPaneElTransform(t){let e="end"===t.type?0:t.translateX;this.instance.paneEl.style.transform=`translateX(${e||0}px) translateY(${t.translateY}px) translateZ(0px)`}present(t){let{transition:e}=t;return e||(e=m.BuildInTransition[this.settings.modal.transition]),this.instance.customPresent(Object.assign(Object.assign({},t),{transition:e}))}destroy(t){let{transition:e}=t;if(e||(e=JSON.parse(JSON.stringify({duration:m.BuildInTransition[this.settings.modal.transition].duration,from:m.BuildInTransition[this.settings.modal.transition].to,to:m.BuildInTransition[this.settings.modal.transition].from}))),t.fromCurrentPosition){let t=new WebKitCSSMatrix(window.getComputedStyle(this.instance.paneEl).transform);e.to.transform=`translateY(${t.m42}px) translateX(${t.m41}px) translateZ(0px)`}return this.instance.customDestroy(Object.assign(Object.assign({},t),{transition:e}))}handleSuperposition(t){let e=Math.abs(this.instance.getPanelTransformY()-this.breakpoints.topper),s=Math.abs(this.instance.getPanelTransformX());if(this.settings.modal.dismissOnIntense&&(e>40||s>30))return this.instance.disableDrag(),this.destroy({animate:!0,fromCurrentPosition:!0}),!1;const i=this.instance.getPanelTransformY()/this.breakpoints.topper/8,n=this.instance.getPanelTransformX()/this.breakpoints.topper/8;return{y:this.instance.getPanelTransformY()+t.diffY*(i+n),x:this.instance.getPanelTransformX()+t.diffX*(i+n)}}}m.BuildInTransition={fade:{duration:300,from:{opacity:0},to:{opacity:1}},zoom:{duration:300,from:{opacity:0,scale:.5},to:{opacity:1,scale:1}}},m.ForceSettings={fitHeight:!0,touchAngle:null,showDraggable:!1};const v={ZStackModule:class{constructor(t){this.instance=t,this.zStackDefaults={pushElements:null,minPushHeight:null,cardBorderRadius:null,cardYOffset:0,cardZScale:.93,cardContrast:.85,stackZAngle:160},this.breakpoints=this.instance.breakpoints,this.settings=this.instance.settings,this.settings.zStack&&(this.instance.setZstackConfig=t=>i(this,void 0,void 0,(function*(){return this.setZstackConfig(t)})),this.instance.on("rendered",(()=>{this.setZstackConfig(this.settings.zStack),this.setPushMultiplicators()})),this.instance.on("beforePresentTransition",(t=>{t.animate||this.settings.zStack.pushElements.forEach((t=>this.pushTransition(document.querySelector(t),this.breakpoints.breaks[this.settings.initialBreak],"unset")))})),this.instance.on("onMoveTransitionStart",(()=>{this.settings.zStack.pushElements.forEach((t=>this.pushTransition(document.querySelector(t),this.instance.getPanelTransformY(),"all 0ms linear 0ms")))})),this.instance.on("onTransitionStart",(t=>{this.settings.zStack.pushElements.forEach((e=>this.pushTransition(document.querySelector(e),t.translateY.new,`all ${this.settings.animationDuration}ms ${this.settings.animationType} 0s`)))})))}setZstackConfig(t){this.settings.zStack=t?Object.assign(Object.assign({},this.zStackDefaults),t):null}pushTransition(t,e,s){let i=this.settings.zStack.pushElements;t.style.transition=s,t.style.overflow=this.settings.zStack.cardBorderRadius&&"hidden",e=this.instance.screenHeightOffset-e;const n=this.settings.zStack.minPushHeight?this.settings.zStack.minPushHeight:this.instance.screenHeightOffset-this.breakpoints.bottomer,r=this.instance.screenHeightOffset-this.breakpoints.topper;let o=this.getPushMulitplicator(t),a=Math.pow(this.settings.zStack.cardZScale,o),h=Math.pow(this.settings.zStack.cardZScale,o-1),l=6+this.settings.zStack.cardYOffset,c=l*o*-1,p=c+l,d=Math.pow(this.settings.zStack.cardContrast,o),u=Math.pow(this.settings.zStack.cardContrast,o-1);const g=(s,n,r,o)=>{let a=Math.pow(s,this.settings.zStack.stackZAngle/100);t.style.transform=`translateY(${n*(a/s)}px) scale(${s})`,t.style.borderRadius=`${o}px`,t.style.filter=`contrast(${r})`;let h=document.querySelector(i[i.length-1]);e||t.className!==h.className||this.clearPushMultiplicators()};if(e<=n)return void g(h,p,u,0);const b=(t,s)=>{let i=-1*(r*s-n*t);return i-=(t-s)*e,i/=n-r,i>s&&(i=s),i<t&&(i=t),i};g(b(a,h),b(c,p),b(d,u),-1*b(-1*this.settings.zStack.cardBorderRadius,0))}setPushMultiplicators(){this.settings.zStack.pushElements.forEach((t=>{let e=document.querySelector(t),s=this.getPushMulitplicator(e);s=s?s+1:1,e.style.setProperty("--push-multiplicator",`${s}`)}))}getPushMulitplicator(t){let e=t.style.getPropertyValue("--push-multiplicator");return parseInt(e)}clearPushMultiplicators(){for(let t=0;t<this.settings.zStack.pushElements.length;t++){let e=document.querySelector(this.settings.zStack.pushElements[t]),s=this.getPushMulitplicator(e);s-=1,s?e.style.setProperty("--push-multiplicator",`${s}`):e.style.removeProperty("--push-multiplicator")}}},FollowerModule:class{constructor(t){this.instance=t,this.breakpoints=this.instance.breakpoints,this.transitions=this.instance.transitions,this.settings=this.instance.settings,this.settings.followerElement&&(this.instance.on("rendered",(()=>{var t;document.querySelector(this.settings.followerElement)?(this.followerEl=document.querySelector(this.settings.followerElement),this.followerEl.style.willChange="transform, border-radius",this.followerEl.style.transform="translateY(0px) translateZ(0px)",this.followerEl.style.transition=this.transitions.buildTransitionValue(null===(t=this.settings.breaks[this.instance.currentBreak()])||void 0===t?void 0:t.bounce)):console.warn("Cupertino Pane: wrong follower element selector specified",this.settings.followerElement)})),this.instance.on("onMoveTransitionStart",(t=>{this.followerEl.style.transition="all 0ms linear 0ms",this.followerEl.style.transform=`translateY(${t.translateY-this.breakpoints.breaks[this.settings.initialBreak]}px) translateZ(0px)`})),this.instance.on("onMoveTransitionStart",(t=>{this.followerEl.style.transition="initial"})),this.instance.on("onTransitionStart",(t=>{this.followerEl.style.transition=t.transition,this.followerEl.style.transform=`translateY(${t.translateY.new-this.breakpoints.breaks[this.settings.initialBreak]}px) translateZ(0px)`})))}},BackdropModule:class{constructor(t){this.instance=t,this.touchMoveBackdropCb=t=>this.touchMoveBackdrop(t),this.settings=this.instance.settings,this.events=this.instance.events,this.settings.backdrop&&(this.instance.backdrop=t=>this.backdrop(t),this.instance.on("rendered",(()=>{this.instance.addStyle(`\n        .cupertino-pane-wrapper .backdrop {\n          overflow: hidden;\n          position: fixed;\n          width: 100%;\n          bottom: 0;\n          right: 0;\n          left: 0;\n          top: 0;\n          display: none;\n          z-index: 10;\n          ${n.backdropFilter&&this.settings.backdropBlur?"\n            backdrop-filter: saturate(180%) blur(10px);\n            -webkit-backdrop-filter: saturate(180%) blur(10px);\n          ":""}\n        }\n      `),this.settings.backdrop&&this.renderBackdrop()})),this.instance.on("beforePresentTransition",(t=>{t.animate||(this.backdropEl.style.display="block")})),this.instance.on("onTransitionStart",(t=>{this.settings.backdrop&&(this.instance.isHidden()||t.type===c.Hide||t.type===c.Destroy||t.type===c.Present)&&(this.backdropEl.style.backgroundColor="rgba(0,0,0,.0)",this.backdropEl.style.transition=`all ${this.settings.animationDuration}ms ${this.settings.animationType} 0s`,t.type!==c.Hide&&t.type!==c.Destroy&&(this.backdropEl.style.display="block",setTimeout((()=>{this.backdropEl.style.backgroundColor=`rgba(0,0,0, ${this.settings.backdropOpacity})`}),50)))})),this.instance.on("onTransitionEnd",(t=>{this.backdropEl&&(t.type!==c.Destroy&&t.type!==c.Hide||(this.backdropEl.style.transition="initial",this.backdropEl.style.display="none"))})),n.touch&&(this.instance.on("onDidPresent",(()=>{var t;null===(t=this.backdropEl)||void 0===t||t.addEventListener(this.events.touchEvents.move,this.touchMoveBackdropCb,!!n.passiveListener&&{passive:!1,capture:!1})})),this.instance.on("onDidDismiss",(t=>{var e;null===(e=this.backdropEl)||void 0===e||e.removeEventListener(this.events.touchEvents.move,this.touchMoveBackdropCb)}))))}backdrop(t={show:!0}){var e,s;if(!this.instance.isPanePresented())return console.warn("Cupertino Pane: Present pane before call backdrop()"),null;this.isBackdropPresented()||(this.renderBackdrop(),n.touch&&(null===(e=this.backdropEl)||void 0===e||e.removeEventListener(this.events.touchEvents.move,this.touchMoveBackdropCb),null===(s=this.backdropEl)||void 0===s||s.addEventListener(this.events.touchEvents.move,this.touchMoveBackdropCb,!!n.passiveListener&&{passive:!1,capture:!1})));const i=()=>{this.backdropEl.style.transition="initial",this.backdropEl.style.display="none",this.backdropEl.removeEventListener("transitionend",i)};if(this.backdropEl.style.transition=`all ${this.settings.animationDuration}ms ${this.settings.animationType} 0s`,this.backdropEl.style.backgroundColor="rgba(0,0,0,.0)",t.show)this.backdropEl.style.display="block",setTimeout((()=>{this.backdropEl.style.backgroundColor=`rgba(0,0,0, ${this.settings.backdropOpacity})`}),50);else{if("none"===this.backdropEl.style.display)return;this.backdropEl.addEventListener("transitionend",i)}}renderBackdrop(){this.backdropEl=document.createElement("div"),this.backdropEl.classList.add("backdrop"),this.backdropEl.style.transition=`all ${this.settings.animationDuration}ms ${this.settings.animationType} 0s`,this.backdropEl.style.backgroundColor=`rgba(0,0,0, ${this.settings.backdropOpacity})`,this.instance.wrapperEl.appendChild(this.backdropEl),this.backdropEl.addEventListener("click",(t=>this.instance.emit("onBackdropTap",t)))}isBackdropPresented(){return!!document.querySelector(".cupertino-pane-wrapper .backdrop")}touchMoveBackdrop(t){this.settings.touchMoveStopPropagation&&t.stopPropagation()}},FitHeightModule:class{constructor(t){this.instance=t,this.calcHeightInProcess=!1,this.breakpoints=this.instance.breakpoints,this.settings=this.instance.settings,this.settings.fitHeight&&(this.instance.calcFitHeight=t=>i(this,void 0,void 0,(function*(){return this.calcFitHeight(t)})),this.instance.setOverflowHeight=()=>this.setOverflowHeight(),this.instance.on("DOMElementsReady",(()=>{this.instance.wrapperEl.classList.add("fit-height")})),this.instance.on("onDidPresent",(()=>{this.instance.paneEl.style.height="unset"})),this.instance.on("onTransitionEnd",(()=>{this.instance.paneEl.style.height="unset"})),this.instance.on("onWillPresent",(()=>{this.breakpoints.beforeBuildBreakpoints=()=>this.beforeBuildBreakpoints()})),this.instance.on("beforeBreakHeightApplied",(t=>{var e;this.settings.fitScreenHeight&&((null===(e=this.settings.breaks[t.break])||void 0===e?void 0:e.height)>this.instance.screen_height&&(this.settings.breaks[t.break].height=this.instance.screen_height-this.settings.bottomOffset),this.settings.breaks.top&&this.settings.breaks.middle&&this.settings.breaks.top.height-50<=this.settings.breaks.middle.height&&(this.settings.breaks.middle.enabled=!1,this.settings.initialBreak="top")),"top"===t.break&&(this.settings.breaks.top.height>this.instance.screen_height?(this.settings.breaks.top.height=this.instance.screen_height-2*this.settings.bottomOffset,this.settings.topperOverflow=!0,this.settings.upperThanTop=!1):this.instance.overflowEl&&!this.settings.maxFitHeight&&(this.settings.topperOverflow=!1,this.instance.overflowEl.style.overflowY="hidden"))}),!0))}beforeBuildBreakpoints(){var t,e,s;return i(this,void 0,void 0,(function*(){this.settings.fitScreenHeight=!1,this.settings.initialBreak="top",this.settings.topperOverflow=!1;let i=yield this.getPaneFitHeight();this.settings.maxFitHeight&&i>this.settings.maxFitHeight&&(i=this.settings.maxFitHeight,this.settings.topperOverflow=!0),this.breakpoints.conf={top:{enabled:!0,height:i},middle:{enabled:!1}},this.breakpoints.conf.top.bounce=null===(e=null===(t=this.settings.breaks)||void 0===t?void 0:t.top)||void 0===e?void 0:e.bounce,this.breakpoints.conf.bottom=(null===(s=this.settings.breaks)||void 0===s?void 0:s.bottom)||{enabled:!0,height:0}}))}calcFitHeight(t=!0){return i(this,void 0,void 0,(function*(){return this.instance.wrapperEl&&this.instance.el?this.calcHeightInProcess?(console.warn("Cupertino Pane: calcFitHeight() already in process"),null):void(yield this.breakpoints.buildBreakpoints(this.breakpoints.lockedBreakpoints,null,t)):null}))}setOverflowHeight(t=0){this.paneElHeight>this.instance.screen_height&&(this.instance.paneEl.style.height=`${this.instance.getPaneHeight()}px`,this.instance.overflowEl.style.height=this.instance.getPaneHeight()-this.settings.topperOverflowOffset-this.instance.overflowEl.offsetTop-t+"px")}getPaneFitHeight(){return i(this,void 0,void 0,(function*(){this.calcHeightInProcess=!0;let t=this.instance.el.querySelectorAll("img");this.instance.el.style.height="unset",this.instance.rendered||(this.instance.el.style.visibility="hidden",this.instance.el.style.pointerEvents="none",this.instance.el.style.display="block",this.instance.wrapperEl.style.visibility="hidden",this.instance.wrapperEl.style.pointerEvents="none",this.instance.wrapperEl.style.display="block");let e=[];t.length&&(e=Array.from(t).map((t=>new Promise((e=>{if(t.height||t.complete&&t.naturalHeight)return e(!0);t.onload=()=>e(!0),t.onerror=()=>e(!0)}))))),yield Promise.all(e),yield new Promise((t=>requestAnimationFrame(t)));let s=Math.floor(this.instance.paneEl.getBoundingClientRect().height);return this.paneElHeight!==s&&(this.instance.paneEl.style.height=`${s<=this.paneElHeight?this.paneElHeight:s}px`),this.instance.rendered||(this.instance.el.style.visibility="unset",this.instance.el.style.pointerEvents="unset",this.instance.el.style.display="none",this.instance.wrapperEl.style.visibility="unset",this.instance.wrapperEl.style.pointerEvents="unset",this.instance.wrapperEl.style.display="none"),this.calcHeightInProcess=!1,this.paneElHeight=s,this.paneElHeight}))}},InverseModule:class{constructor(t){this.instance=t,this.breakpoints=this.instance.breakpoints,this.settings=this.instance.settings,this.events=this.instance.events,this.settings.inverse&&(this.settings.buttonDestroy=!1,this.instance.getPaneHeight=()=>this.getPaneHeight(),this.instance.updateScreenHeights=()=>this.updateScreenHeights(),this.instance.setOverflowHeight=()=>this.settings.fitHeight?{}:this.setOverflowHeight(),this.instance.checkOpacityAttr=()=>{},this.instance.checkOverflowAttr=t=>this.checkOverflowAttr(t),this.instance.prepareBreaksSwipeNextPoint=()=>this.prepareBreaksSwipeNextPoint(),this.events.handleSuperposition=t=>this.handleSuperposition(t),this.events.scrollPreventDrag=t=>this.scrollPreventDrag(t),this.events.onScroll=()=>this.onScroll(),this.instance.on("DOMElementsReady",(()=>{this.instance.wrapperEl.classList.add("inverse")})),this.instance.on("rendered",(()=>{this.instance.addStyle("\n        .cupertino-pane-wrapper.inverse .pane {\n          border-radius: 0 0 20px 20px;\n          border-radius: 0 0\n                        var(--cupertino-pane-border-radius, 20px) \n                        var(--cupertino-pane-border-radius, 20px);\n        }\n        .cupertino-pane-wrapper.inverse:not(.fit-height) .pane {\n          padding-bottom: 15px; \n        }\n        .cupertino-pane-wrapper.inverse .draggable {\n          bottom: 0;\n          top: initial;\n        }\n        .cupertino-pane-wrapper.inverse .draggable.over {\n          bottom: -30px;\n          top: initial;\n        }\n        .cupertino-pane-wrapper.inverse .move {\n          margin-top: 15px;\n        }\n        .cupertino-pane-wrapper.inverse .draggable.over .move {\n          margin-top: -5px;\n        }\n      ")})),this.instance.on("beforeBreakHeightApplied",(t=>{var e;(null===(e=this.settings.breaks[t.break])||void 0===e?void 0:e.enabled)&&(this.breakpoints.breaks[t.break]=2*(this.settings.breaks[t.break].height+this.settings.bottomOffset))}),!1),this.instance.on("buildBreakpointsCompleted",(()=>{this.breakpoints.topper=this.breakpoints.bottomer,this.instance.paneEl.style.top=`-${this.breakpoints.bottomer-this.settings.bottomOffset}px`})))}getPaneHeight(){return this.breakpoints.bottomer-this.settings.bottomOffset}updateScreenHeights(){this.instance.screen_height=window.innerHeight,this.instance.screenHeightOffset=0}setOverflowHeight(){this.instance.overflowEl.style.height=this.getPaneHeight()-30-this.settings.topperOverflowOffset-this.instance.overflowEl.offsetTop+"px"}checkOverflowAttr(t){this.settings.topperOverflow&&this.instance.overflowEl&&(this.instance.overflowEl.style.overflowY=t>=this.breakpoints.bottomer?"auto":"hidden")}prepareBreaksSwipeNextPoint(){let t={},e={};return t.top=this.breakpoints.breaks.bottom,t.middle=this.breakpoints.breaks.middle,t.bottom=this.breakpoints.breaks.top,e.top=Object.assign({},this.settings.breaks.bottom),e.middle=Object.assign({},this.settings.breaks.middle),e.bottom=Object.assign({},this.settings.breaks.top),{brs:t,settingsBreaks:e}}handleSuperposition(t){if(this.settings.upperThanTop&&(t.newVal>=this.breakpoints.topper||this.events.startPointOverTop)){this.events.startPointOverTop||(this.events.startPointOverTop=t.clientY),this.events.startPointOverTop>t.clientY&&delete this.events.startPointOverTop;const e=this.instance.screen_height-this.instance.screenHeightOffset,s=(e-this.instance.getPanelTransformY())/(e-this.breakpoints.topper)/8;return{y:this.instance.getPanelTransformY()+t.diffY*s}}if(!this.settings.upperThanTop&&t.newVal>=this.breakpoints.topper)return{y:this.breakpoints.topper}}scrollPreventDrag(t){let e=!1;return this.events.willScrolled()&&this.isOverflowEl(t.target)&&(e=!0),e}isOverflowEl(t){if(!t)return!1;let e=t.parentNode;for(;null!=e;){if(e==this.instance.overflowEl)return!0;e=e.parentNode}return!1}onScroll(){return i(this,void 0,void 0,(function*(){this.events.isScrolling=!0}))}},HorizontalModule:b,ModalModule:m};class f{constructor(t,e={}){if(this.selector=t,this.disableDragEvents=!1,this.preventDismissEvent=!1,this.preventedDismiss=!1,this.rendered=!1,this.settings=(new h).instance,this.device=new r,this.modules={},this.eventsListeners={},this.on=u,this.emit=g,this.calcFitHeight=()=>{if(!this.settings.fitHeight)return console.warn("Cupertino Pane: calcFitHeight() should be used for auto-height panes with enabled fitHeight option"),null},this.swipeNextPoint=(t,e,s)=>{let{brs:i,settingsBreaks:n}=this.prepareBreaksSwipeNextPoint();if(this.breakpoints.currentBreakpoint===i.top){if(t>e){if(n.middle.enabled)return i.middle;if(n.bottom.enabled)return i.middle<s?s:i.bottom}return i.top}if(this.breakpoints.currentBreakpoint===i.middle)return t<-e&&n.top.enabled?i.top:t>e&&n.bottom.enabled?i.bottom:i.middle;if(this.breakpoints.currentBreakpoint===i.bottom){if(t<-e){if(n.middle.enabled)return i.middle>s?s:i.middle;if(n.top.enabled)return i.top}return i.bottom}return s},t instanceof HTMLElement?this.selector=t:this.selector=document.querySelector(t),!this.selector)return void console.warn("Cupertino Pane: wrong selector or DOM element specified",this.selector);if(this.isPanePresented())return void console.error("Cupertino Pane: specified selector or DOM element already in use",this.selector);this.el=this.selector,this.el.style.display="none",this.settings=Object.assign(Object.assign({},this.settings),e);let s=Object.keys(v).map((t=>v[t])),i=this.settings.modules||s;i.forEach((t=>t.CollectSettings?this.settings=t.CollectSettings(this.settings):null));let n=this.el.parentElement;this.settings.parentElement&&(n=this.settings.parentElement instanceof HTMLElement?this.settings.parentElement:document.querySelector(this.settings.parentElement)),this.settings.parentElement=n,this.device.ionic&&(this.ionContent=document.querySelector("ion-content"),this.ionApp=document.querySelector("ion-app")),this.settings.events&&Object.keys(this.settings.events).forEach((t=>this.on(t,this.settings.events[t]))),this.breakpoints=new l(this),this.transitions=new d(this),this.keyboardEvents=new a(this),this.events=new o(this),i.forEach((t=>this.modules[this.getModuleRef(t.name)]=new t(this)))}drawBaseElements(){this.styleEl=document.createElement("style"),this.styleEl.id=`cupertino-pane-${(Math.random()+1).toString(36).substring(7)}`,this.parentEl=this.settings.parentElement,this.wrapperEl=document.createElement("div"),this.wrapperEl.classList.add("cupertino-pane-wrapper"),this.settings.cssClass&&this.settings.cssClass.split(" ").filter((t=>!!t)).forEach((t=>this.wrapperEl.classList.add(t)));let t="";t+="\n      .cupertino-pane-wrapper {\n        display: none;\n        position: absolute;\n        top: 0;\n        left: 0;\n      }\n    ",this.paneEl=document.createElement("div"),this.paneEl.style.transform=`translateY(${this.screenHeightOffset}px) translateZ(0px)`,this.paneEl.classList.add("pane"),t+="\n      .cupertino-pane-wrapper .pane {\n        position: fixed;\n        z-index: 11;\n        width: 100%;\n        max-width: 500px;\n        left: 0px;\n        right: 0px;\n        margin-left: auto;\n        margin-right: auto;\n        background: var(--cupertino-pane-background, #ffffff);\n        color: var(--cupertino-pane-color, #333333);\n        box-shadow: var(--cupertino-pane-shadow, 0 4px 16px rgba(0,0,0,.12));\n        will-change: transform;\n        padding-top: 15px; \n        border-radius: var(--cupertino-pane-border-radius, 20px) \n                       var(--cupertino-pane-border-radius, 20px) \n                       0 0;\n        -webkit-user-select: none;\n      }\n      .cupertino-pane-wrapper .pane img {\n        -webkit-user-drag: none;\n      }\n    ",this.draggableEl=document.createElement("div"),this.draggableEl.classList.add("draggable"),this.settings.draggableOver&&this.draggableEl.classList.add("over"),t+="\n      .cupertino-pane-wrapper .draggable {\n        padding: 5px;\n        position: absolute;\n        left: 0;\n        right: 0;\n        margin-left: auto;\n        margin-right: auto;\n        height: 30px;\n        z-index: -1;\n        top: 0;\n        bottom: initial;\n      }\n      .cupertino-pane-wrapper .draggable.over {\n        top: -30px;\n        padding: 15px;\n      }\n    ",this.moveEl=document.createElement("div"),this.moveEl.classList.add("move"),t+=`\n      .cupertino-pane-wrapper .move {\n        margin: 0 auto;\n        height: 5px;\n        background: var(--cupertino-pane-move-background, #c0c0c0);\n        width: 36px;\n        border-radius: 4px;\n      }\n      .cupertino-pane-wrapper .draggable.over .move {\n        width: 70px; \n        background: var(--cupertino-pane-move-background, rgba(225, 225, 225, 0.6));\n        ${n.backdropFilter?"\n          backdrop-filter: saturate(180%) blur(20px);\n          -webkit-backdrop-filter: saturate(180%) blur(20px);\n        ":""}\n      }\n    `,this.destroyButtonEl=document.createElement("div"),this.destroyButtonEl.classList.add("destroy-button"),t+="\n      .cupertino-pane-wrapper .destroy-button {\n        width: 26px;\n        height: 26px;\n        cursor: pointer;\n        position: absolute;\n        background: var(--cupertino-pane-destroy-button-background, #ebebeb);\n        fill: var(--cupertino-pane-icon-close-color, #7a7a7e);\n        right: 20px;\n        z-index: 14;\n        border-radius: 100%;\n        top: 16px;\n      }\n    ",this.contentEl=this.el,this.contentEl.style.transition=`opacity ${this.settings.animationDuration}ms ${this.settings.animationType} 0s`,this.contentEl.style.overflowX="hidden",this.styleEl.textContent=t.replace(/\s\s+/g," "),document.head.prepend(this.styleEl),this.parentEl.appendChild(this.wrapperEl),this.wrapperEl.appendChild(this.paneEl),this.paneEl.appendChild(this.contentEl),this.settings.showDraggable&&(this.paneEl.appendChild(this.draggableEl),this.draggableEl.appendChild(this.moveEl)),this.emit("DOMElementsReady")}present(t={animate:!1}){var e;return i(this,void 0,void 0,(function*(){if(!this.el||!document.body.contains(this.el))return void console.warn("Cupertino Pane: specified DOM element must be attached to the DOM");if(this.isPanePresented()&&this.rendered)return void this.moveToBreak(this.settings.initialBreak);if(this.isPanePresented()&&!this.rendered)return void console.warn("Cupertino Pane: specified selector or DOM element already in use",this.selector);t.animate&&this.device.ionic&&(this.ionApp.componentOnReady&&(yield this.ionApp.componentOnReady()),yield new Promise((t=>requestAnimationFrame(t)))),this.emit("onWillPresent"),this.updateScreenHeights(),this.drawBaseElements(),yield this.setBreakpoints();let s=(null===(e=null==t?void 0:t.transition)||void 0===e?void 0:e.from)?JSON.parse(JSON.stringify(t.transition.from)):null;return s&&(s.transform||(s.transform=`translateY(${this.breakpoints.breaks[this.settings.initialBreak]}px) translateZ(0px)`),Object.assign(this.paneEl.style,s)),this.wrapperEl.style.display="block",this.contentEl.style.display="block",this.wrapperEl.classList.add("rendered"),this.rendered=!0,this.scrollElementInit(),this.emit("rendered"),this.setGrabCursor(!0),this.settings.buttonDestroy&&(this.paneEl.appendChild(this.destroyButtonEl),this.destroyButtonEl.addEventListener("click",(t=>this.destroy({animate:!0,destroyButton:!0}))),this.destroyButtonEl.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512">\n          <path d="M278.6 256l68.2-68.2c6.2-6.2 6.2-16.4 0-22.6-6.2-6.2-16.4-6.2-22.6 0L256 233.4l-68.2-68.2c-6.2-6.2-16.4-6.2-22.6 0-3.1 3.1-4.7 7.2-4.7 11.3 0 4.1 1.6 8.2 4.7 11.3l68.2 68.2-68.2 68.2c-3.1 3.1-4.7 7.2-4.7 11.3 0 4.1 1.6 8.2 4.7 11.3 6.2 6.2 16.4 6.2 22.6 0l68.2-68.2 68.2 68.2c6.2 6.2 16.4 6.2 22.6 0 6.2-6.2 6.2-16.4 0-22.6L278.6 256z"/>\n        </svg>'),this.device.ionic&&!this.settings.ionContentScroll&&this.ionContent.setAttribute("scroll-y","false"),this.settings.bottomClose&&(this.settings.breaks.bottom.enabled=!0),this.settings.freeMode&&(this.settings.lowerThanBottom=!1),this.device.android&&(document.body.style.overscrollBehaviorY="none"),this.emit("beforePresentTransition",{animate:t.animate}),yield new Promise((t=>requestAnimationFrame(t))),t.animate?yield this.transitions.doTransition({type:"present",conf:t,translateY:this.breakpoints.breaks[this.settings.initialBreak]}):(this.breakpoints.prevBreakpoint=this.settings.initialBreak,this.paneEl.style.transform=`translateY(${this.breakpoints.breaks[this.settings.initialBreak]}px) translateZ(0px)`),this.events.attachAllEvents(),this.emit("onDidPresent",{animate:t.animate}),this}))}getPaneHeight(){return this.screen_height-this.breakpoints.topper-this.settings.bottomOffset}updateScreenHeights(){this.screen_height=window.innerHeight,this.screenHeightOffset=window.innerHeight}scrollElementInit(){let t=this.el.querySelectorAll("[overflow-y]");!t.length||t.length>1?this.overflowEl=this.contentEl:(this.overflowEl=t[0],this.overflowEl.style.overflowX="hidden"),this.overflowEl.style.overscrollBehavior="none",this.settings.topperOverflow&&this.settings.upperThanTop&&console.warn('Cupertino Pane: "upperThanTop" allowed for disabled "topperOverflow"'),this.setOverflowHeight()}setOverflowHeight(t=0){this.paneEl.style.height=`${this.getPaneHeight()}px`,this.overflowEl.style.height=this.getPaneHeight()-this.settings.topperOverflowOffset-this.overflowEl.offsetTop-t+"px"}checkOpacityAttr(t){let e=this.el.querySelectorAll("[hide-on-bottom]");e.length&&e.forEach((e=>{e.style.transition=`opacity ${this.settings.animationDuration}ms ${this.settings.animationType} 0s`,e.style.opacity=t>=this.breakpoints.breaks.bottom?"0":"1"}))}checkOverflowAttr(t){this.settings.topperOverflow&&this.overflowEl&&(this.overflowEl.style.overflowY=t<=this.breakpoints.topper?"auto":"hidden")}isPanePresented(){let t=Array.from(document.querySelectorAll(".cupertino-pane-wrapper.rendered"));return!!t.length&&!!t.find((t=>t.contains(this.selector)))}prepareBreaksSwipeNextPoint(){return{brs:Object.assign({},this.breakpoints.breaks),settingsBreaks:Object.assign({},this.settings.breaks)}}addStyle(t){this.styleEl.textContent+=t.replace(/\s\s+/g," ")}getModuleRef(t){return(t.charAt(0).toLowerCase()+t.slice(1)).replace("Module","")}getPanelTransformY(){return parseFloat(/\.*translateY\((.*)px\)/i.exec(this.paneEl.style.transform)[1])}getPanelTransformX(){let t=/\.*translateX\((.*)px\)/i.exec(this.paneEl.style.transform);return t?parseFloat(t[1]):0}preventDismiss(t=!1){this.preventDismissEvent=t}setGrabCursor(t,e){this.device.desktop&&(this.paneEl.style.cursor=t?e?"grabbing":"grab":"")}disableDrag(){this.disableDragEvents=!0,this.setGrabCursor(!1)}enableDrag(){this.disableDragEvents=!1,this.setGrabCursor(!0)}setBreakpoints(t,e){return i(this,void 0,void 0,(function*(){!this.isPanePresented()||t?yield this.breakpoints.buildBreakpoints(t,e):console.warn("Cupertino Pane: Provide any breaks configuration")}))}moveToBreak(t,e="breakpoint"){return i(this,void 0,void 0,(function*(){return this.isPanePresented()?this.settings.breaks[t].enabled?(this.checkOpacityAttr(this.breakpoints.breaks[t]),this.checkOverflowAttr(this.breakpoints.breaks[t]),yield this.transitions.doTransition({type:e,translateY:this.breakpoints.breaks[t]}),this.breakpoints.currentBreakpoint=this.breakpoints.breaks[t],Promise.resolve(!0)):void console.warn("Cupertino Pane: %s breakpoint disabled",t):(console.warn("Cupertino Pane: Present pane before call moveToBreak()"),null)}))}moveToHeight(t){return i(this,void 0,void 0,(function*(){if(!this.isPanePresented())return console.warn("Cupertino Pane: Present pane before call moveToHeight()"),null;let e=this.screenHeightOffset?this.screen_height-t:t;this.checkOpacityAttr(e),yield this.transitions.doTransition({type:"breakpoint",translateY:e})}))}hide(){return i(this,void 0,void 0,(function*(){return this.isPanePresented()?this.isHidden()?(console.warn("Cupertino Pane: Pane already hidden"),null):void(yield this.transitions.doTransition({type:"hide",translateY:this.screenHeightOffset})):(console.warn("Cupertino Pane: Present pane before call hide()"),null)}))}isHidden(){return this.isPanePresented()?this.transitions.isPaneHidden:(console.warn("Cupertino Pane: Present pane before call isHidden()"),null)}currentBreak(){return this.isPanePresented()?this.breakpoints.getCurrentBreakName():(console.warn("Cupertino Pane: Present pane before call currentBreak()"),null)}destroy(t={animate:!1,destroyButton:!1}){return i(this,void 0,void 0,(function*(){if(!this.rendered)return console.warn("Cupertino Pane: Present pane before call destroy()"),null;this.preventDismissEvent?this.preventedDismiss||(this.emit("onWillDismiss",{prevented:!0}),this.moveToBreak(this.breakpoints.prevBreakpoint)):(this.emit("onWillDismiss"),t.animate?yield this.transitions.doTransition({type:"destroy",conf:t,translateY:this.screenHeightOffset,destroyButton:t.destroyButton}):this.destroyResets(),this.emit("onDidDismiss",{destroyButton:t.destroyButton}))}))}destroyResets(){this.keyboardEvents.fixBodyKeyboardResize(!1),this.parentEl.appendChild(this.contentEl),this.wrapperEl.remove(),this.styleEl.remove(),this.events.detachAllEvents(),delete this.rendered,delete this.breakpoints.prevBreakpoint,this.contentEl.style.display="none"}}}}]);
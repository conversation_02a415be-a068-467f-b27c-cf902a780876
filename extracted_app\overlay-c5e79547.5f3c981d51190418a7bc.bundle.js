"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1219],{84551:(i,t,e)=>{e.d(t,{Y:()=>n});var s=e(15215),a=e("aurelia-framework"),o=e(92694);let n=class{#i;constructor(i){this.#i=i}report(i){this.#i.collect({name:"api_timing",value:i.responseTime,tags:[{key:"http.endpoint",value:i.endpoint},{key:"http.method",value:i.method},{key:"country",value:"$country"}]})}};n=(0,s.Cg)([(0,a.autoinject)(),(0,s.Sn)("design:paramtypes",[o.k])],n)},92694:(i,t,e)=>{e.d(t,{k:()=>l});var s=e(15215),a=e("aurelia-framework"),o=e(29702);const n=["host","device","source","service","env","version"];let l=class{#t;#e;#s;#a;#o;constructor(i,t=[]){this.#e=[],this.#s=!0,this.#a=100,this.#t=i,this.#o=t}disableAutoRelease(){this.#s=!1}enableAutoRelease(){this.#s=!0}setAutoReleaseCount(i){this.#a=i}collectImmediately(i){return this.#t.collectOne(this.#n(i))}collect(i){this.#e.push(this.#n(i)),this.#s&&this.#e.length>=this.#a&&this.releaseCollected()}releaseCollected(){if(0===this.#e.length)return;const i=this.#e;this.#e=[],this.#t.collectMany(i)}#n(i){const t=this.#l(i.tags||[]);return this.#r(t),{...i,tags:t}}#r(i){const t=i.filter((i=>n.includes(i.key)));if(t.length)throw new Error(`Reserved tags used: ${t}`)}#l(i){return this.#o?i.concat(this.#o):i}};l=(0,s.Cg)([(0,a.autoinject)(),(0,s.Sn)("design:paramtypes",[o.K,Array])],l)},"shared/markdown/index":(i,t,e)=>{e.r(t),e.d(t,{MarkdownValueConverter:()=>n.MarkdownValueConverter,configure:()=>l}),e("aurelia-framework");var s=e(42922),a=e.n(s),o=e(17873),n=e("shared/markdown/value-converter");function l(i,t){i.container.registerHandler(a(),(0,o.R)((()=>function(i){const t=a()(i??{html:!1,breaks:!0}),e=t.validateLink.bind(t);return t.validateLink=i=>/^data:image\/svg\+xml;/.test(i)||e(i),t}(t)))),i.globalResources(["./value-converter"])}},"shared/markdown/value-converter":(i,t,e)=>{e.r(t),e.d(t,{MarkdownValueConverter:()=>l});var s=e(15215),a=e("aurelia-framework"),o=e(42922),n=e.n(o);let l=class{#d;constructor(i){this.#d=i}toView(i,t=!0){if("string"==typeof i){i=i.replace(/\\n/g,"\n");let e=t?this.#d.renderInline(i):this.#d.render(i);return t&&(e=e.replace(/\n/g,"<br>")),e}return""}};l=(0,s.Cg)([(0,a.autoinject)(),(0,s.Sn)("design:paramtypes",[n()])],l)},"shared/pro-promos/dummy-number-input":(i,t,e)=>{e.r(t),e.d(t,{DummyNumberInput:()=>s});class s{}},"shared/pro-promos/dummy-number-input.html":(i,t,e)=>{e.r(t),e.d(t,{default:()=>s});const s='<template> <require from="./dummy-number-input.scss"></require> <div class="dummy-number-input"> <div class="dummy-number-input-text-box">500</div> <div class="dummy-number-input-slider"> <div class="dummy-number-input-handle"></div> </div> <div class="dummy-number-input-check"></div> </div> </template> '},"shared/pro-promos/dummy-number-input.scss":(i,t,e)=>{e.r(t),e.d(t,{default:()=>g});var s=e(31601),a=e.n(s),o=e(76314),n=e.n(o),l=e(4417),r=e.n(l),d=new URL(e(83959),e.b),u=n()(a()),m=r()(d);u.push([i.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,dummy-number-input .dummy-number-input-check{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}dummy-number-input{--border-radius: 8px}dummy-number-input .dummy-number-input{font-weight:500;color:var(--theme--text-primary);height:26px;gap:6px;display:flex;align-items:center}dummy-number-input .dummy-number-input-text-box{padding:2px 6px;height:26px;border-radius:var(--border-radius);font-size:11px;line-height:22px;width:75px;background:rgba(255,255,255,.2)}dummy-number-input .dummy-number-input-slider{content:"";position:relative;display:flex;border-radius:var(--border-radius);width:75px;height:100%;background:rgba(255,255,255,.2);overflow:hidden}dummy-number-input .dummy-number-input-slider::before{content:"";position:absolute;background-color:#fa1280;height:100%;width:50%}dummy-number-input .dummy-number-input-slider::after{content:"";filter:drop-shadow(0px 1.735px 3.471px rgba(16, 24, 40, 0.06)) drop-shadow(0px 1.735px 5.206px rgba(16, 24, 40, 0.1));position:absolute;background-color:#fff;border-radius:var(--border-radius);height:100%;width:12px;left:calc(50% - 6px)}dummy-number-input .dummy-number-input-slider .dummy-number-input-handle{position:absolute;height:calc(100% - 16px);width:4px;left:calc(50% - 2px);top:8px;content:"";border-left:1px solid #fa1280;border-right:1px solid #fa1280;z-index:1}dummy-number-input .dummy-number-input-check{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;padding:4px;background-color:#fa1280;border-radius:var(--border-radius);font-size:18px;display:flex}dummy-number-input .dummy-number-input-check:before{font-family:inherit;content:"check"}`,""]);const g=u},"shared/pro-promos/dummy-toggle":(i,t,e)=>{e.r(t),e.d(t,{DummyToggle:()=>s});class s{}},"shared/pro-promos/dummy-toggle.html":(i,t,e)=>{e.r(t),e.d(t,{default:()=>s});const s="<template> <require from=\"./dummy-toggle.scss\"></require> <div class=\"pro-showcase-dummy-toggle\"> <span class=\"pro-showcase-dummy-toggle-off\">${'toggle.off' | i18n | maxLengthReplace:3:'|'}</span> <span class=\"pro-showcase-dummy-toggle-on\">${'toggle.on' | i18n | maxLengthReplace:3:'|'}</span> </div> </template> "},"shared/pro-promos/dummy-toggle.scss":(i,t,e)=>{e.r(t),e.d(t,{default:()=>l});var s=e(31601),a=e.n(s),o=e(76314),n=e.n(o)()(a());n.push([i.id,"dummy-toggle .pro-showcase-dummy-toggle{color:var(--theme--text-primary);font-weight:500;line-height:22px;font-size:11px;background:rgba(255,255,255,.2);border-radius:28px;padding:2px;display:flex;align-items:center}dummy-toggle .pro-showcase-dummy-toggle-off,dummy-toggle .pro-showcase-dummy-toggle-on{padding:0px 14px}dummy-toggle .pro-showcase-dummy-toggle-on{border-radius:14px;background:#fa1280;color:#fff}",""]);const l=n},"shared/pro-promos/game-guide-illustration":(i,t,e)=>{e.r(t),e.d(t,{GameGuideIllustration:()=>a});var s=e(38951);class a{constructor(){this.showCursor=!0,this.cursorSubscription=null}attached(){this.cursorSubscription=(0,s.Y)(750).subscribe((()=>{this.showCursor=!this.showCursor}))}detached(){this.cursorSubscription?.unsubscribe()}}},"shared/pro-promos/game-guide-illustration.html":(i,t,e)=>{e.r(t),e.d(t,{default:()=>l});var s=e(14385),a=e.n(s),o=new URL(e(53444),e.b),n=new URL(e(80086),e.b);const l='<template> <require from="./game-guide-illustration.scss"></require> <div class="game-guide-illustration"> <div class="game-guide-illustration-chat"> <div class="game-guide-illustration-chat-message"> <img class="game-guide-illustration-avatar" src="'+a()(o)+'" alt="${\'pro_showcase.game_guide_illustration_example_avatar_alt\' | i18n}"> <span class="game-guide-illustration-chat-content"> ${\'pro_showcase.game_guide_illustration_question\' | i18n} </span> </div> <div class="game-guide-illustration-chat-message"> <img class="game-guide-illustration-avatar" src="'+a()(n)+'" alt="${\'pro_showcase.game_guide_illustration_assistant_avatar_alt\' | i18n}"> <span class="game-guide-illustration-chat-content"> ${\'pro_showcase.game_guide_illustration_response\' | i18n} </span> <div class="game-guide-illustration-chat-icons"> <span class="thumbs-up"></span> <span class="thumbs-down"></span> <span class="feedback"></span> </div> </div> <div class="game-guide-illustration-pending-question"> ${\'pro_showcase.game_guide_illustration_pending_question\' | i18n} ${showCursor ? \'|\' : \'\'} <span class="game-guide-illustration-dummy-send-button"> </span> </div> </div> </div> </template> '},"shared/pro-promos/game-guide-illustration.scss":(i,t,e)=>{e.r(t),e.d(t,{default:()=>g});var s=e(31601),a=e.n(s),o=e(76314),n=e.n(o),l=e(4417),r=e.n(l),d=new URL(e(83959),e.b),u=n()(a()),m=r()(d);u.push([i.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-up,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-up,game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-down,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-down,game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .feedback,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .feedback,game-guide-illustration .game-guide-illustration-dummy-send-button{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}game-guide-illustration .game-guide-illustration{display:flex;flex-direction:column;gap:12px;background:rgba(96,70,255,.1);backdrop-filter:blur(30px);width:376px;max-height:398px;overflow:hidden;padding:20px;border-radius:20px}game-guide-illustration .game-guide-illustration-avatar{width:32px;height:32px;border-radius:50%}game-guide-illustration .game-guide-illustration-chat,game-guide-illustration .game-guide-illustration-message{display:flex;flex-direction:column;gap:12px}game-guide-illustration .game-guide-illustration-chat-message,game-guide-illustration .game-guide-illustration-message-message{display:flex;flex-direction:column;gap:12px}game-guide-illustration .game-guide-illustration-chat-message:first-of-type,game-guide-illustration .game-guide-illustration-message-message:first-of-type{margin-bottom:12px}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons{display:flex;align-items:center;gap:4px;padding:4px}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-up,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-up{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-secondary)}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-up:before,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-up:before{font-family:inherit;content:"thumb_up"}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-down,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-down{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-secondary)}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-down:before,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-down:before{font-family:inherit;content:"thumb_down"}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .feedback,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .feedback{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-secondary)}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .feedback:before,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .feedback:before{font-family:inherit;content:"article"}game-guide-illustration .game-guide-illustration-chat-content,game-guide-illustration .game-guide-illustration-message-content{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-primary);max-height:120px;overflow:hidden}game-guide-illustration .game-guide-illustration-pending-question{color:var(--theme--text-primary);display:flex;gap:8px;border-radius:12px;background:linear-gradient(0deg, rgba(96, 70, 255, 0.1) 0%, rgba(96, 70, 255, 0.1) 100%),rgba(255,255,255,.1);backdrop-filter:blur(25px);padding:8px 8px 8px 16px;align-items:end}game-guide-illustration .game-guide-illustration-dummy-send-button{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-highlight);height:min-content;display:flex;align-items:center;padding:4px;border-radius:28px;background:#6046ff}game-guide-illustration .game-guide-illustration-dummy-send-button:before{font-family:inherit;content:"arrow_upward"}`,""]);const g=u},"shared/pro-promos/pin-mods-illustration":(i,t,e)=>{e.r(t),e.d(t,{PinModsIllustration:()=>s});class s{}},"shared/pro-promos/pin-mods-illustration.html":(i,t,e)=>{e.r(t),e.d(t,{default:()=>r});var s=e(14385),a=e.n(s),o=new URL(e(77103),e.b),n=new URL(e(54626),e.b),l=a()(o);const r='<template> <require from="./pin-mods-illustration.scss"></require> <div class="pin-mods-illustration"> <div class="pin-mods-illustration-row-header"> <span class="pin-mods-illustration-pin-header"></span> <span class="pin-mods-illustration-mod-name">${\'pro_showcase.pinned\' | i18n}</span> <span class="pin-mods-illustration-collapse"></span> </div> <div class="pin-mods-illustration-row"> <span class="pin-mods-illustration-bolt"> <inline-svg src="'+l+'"></inline-svg> </span> <span class="pin-mods-illustration-mod-name">${\'pro_showcase.demo_mod_1\' | i18n}</span> <span class="pin-mods-illustration-pin-button"> <span class="pin-mods-illustration-pointer"> <inline-svg src="'+a()(n)+'"></inline-svg> </span> </span> </div> <div class="pin-mods-illustration-row"> <span class="pin-mods-illustration-bolt"> <inline-svg src="'+l+'"></inline-svg> </span> <span class="pin-mods-illustration-mod-name">${\'pro_showcase.demo_mod_2\' | i18n}</span> <span class="pin-mods-illustration-pin"></span> </div> <div class="pin-mods-illustration-row"> <span class="pin-mods-illustration-bolt"> <inline-svg src="'+l+'"></inline-svg> </span> <span class="pin-mods-illustration-mod-name">${\'pro_showcase.demo_mod_3\' | i18n}</span> <span class="pin-mods-illustration-pin"></span> </div> </div> </template> '},"shared/pro-promos/pin-mods-illustration.scss":(i,t,e)=>{e.r(t),e.d(t,{default:()=>g});var s=e(31601),a=e.n(s),o=e(76314),n=e.n(o),l=e(4417),r=e.n(l),d=new URL(e(83959),e.b),u=n()(a()),m=r()(d);u.push([i.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,pin-mods-illustration .pin-mods-illustration-collapse,pin-mods-illustration .pin-mods-illustration-pin,pin-mods-illustration .pin-mods-illustration-pin-button,pin-mods-illustration .pin-mods-illustration-pin-header{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}pin-mods-illustration .pin-mods-illustration{display:flex;flex-direction:column;align-items:center;box-shadow:0px 0px 69.011px 0px rgba(255,255,255,.08) inset;backdrop-filter:blur(22px);border-radius:16px;width:376px}pin-mods-illustration .pin-mods-illustration-mod-name{flex-grow:1}pin-mods-illustration .pin-mods-illustration-bolt{display:inline-flex;color:var(--theme--text-disabled)}pin-mods-illustration .pin-mods-illustration-bolt svg{width:21px;height:21px}pin-mods-illustration .pin-mods-illustration-collapse{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;color:rgba(255,255,255,.03)}pin-mods-illustration .pin-mods-illustration-collapse:before{font-family:inherit;content:"unfold_less"}pin-mods-illustration .pin-mods-illustration-pin,pin-mods-illustration .pin-mods-illustration-pin-button,pin-mods-illustration .pin-mods-illustration-pin-header{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;font-size:21px}pin-mods-illustration .pin-mods-illustration-pin:before,pin-mods-illustration .pin-mods-illustration-pin-button:before,pin-mods-illustration .pin-mods-illustration-pin-header:before{font-family:inherit;content:"keep"}pin-mods-illustration .pin-mods-illustration-pin-header,pin-mods-illustration .pin-mods-illustration-pin-button{color:var(--theme--text-highlight)}pin-mods-illustration .pin-mods-illustration-pin,pin-mods-illustration .pin-mods-illustration-pin-button{padding:5px}pin-mods-illustration .pin-mods-illustration-pin{color:rgba(255,255,255,.03)}pin-mods-illustration .pin-mods-illustration-bolt,pin-mods-illustration .pin-mods-illustration-pin-header{margin-right:10px}pin-mods-illustration .pin-mods-illustration-pin-button{position:relative;border-radius:10px;background:rgba(255,255,255,.05);box-shadow:0px 4.564px 64.111px 0px rgba(0,0,0,.25),0px 0px 34.006px 0px rgba(255,255,255,.8)}pin-mods-illustration .pin-mods-illustration-pointer{position:absolute;width:20px;height:20px;filter:drop-shadow(0px 2.715px 4.886px rgba(19, 24, 28, 0.5));right:-6px;bottom:-6px}pin-mods-illustration .pin-mods-illustration-row,pin-mods-illustration .pin-mods-illustration-row-header{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:28px;line-height:26px;color:var(--theme--text-highlight);display:flex;align-items:center;padding:10px 16px 10px 18px;width:100%;background:linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),rgba(255,255,255,.2)}pin-mods-illustration .pin-mods-illustration-row-body-wrapper{display:flex;flex-direction:column;align-items:center;width:100%}pin-mods-illustration .pin-mods-illustration-row-header{font-weight:700;border-radius:16px 16px 0 0;margin-bottom:1px}pin-mods-illustration .pin-mods-illustration-row:last-child{border-radius:0 0 16px 16px}`,""]);const g=u},"shared/pro-promos/save-mods-illustration":(i,t,e)=>{e.r(t),e.d(t,{SaveModsIllustration:()=>s});class s{constructor(){}}},"shared/pro-promos/save-mods-illustration.html":(i,t,e)=>{e.r(t),e.d(t,{default:()=>l});var s=e(14385),a=e.n(s),o=new URL(e(77103),e.b),n=a()(o);const l='<template> <require from="./save-mods-illustration.scss"></require> <require from="./dummy-toggle"></require> <require from="./dummy-number-input"></require> <div class="save-mods-illustration"> <div class="save-mods-illustration-button"> <div class="save-mods-illustration-button-toggle"> <span> <inline-svg src="'+n+'"></inline-svg> </span> </div> ${\'pro_showcase.save_mods\' | i18n} </div> <div class="save-mods-illustration-list"> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-user"></span> <span>${\'pro_showcase.save_mods_mods\' | i18n}</span> </div> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-bolt"> <inline-svg src="'+n+'"></inline-svg> </span> <span class="save-mods-illustration-mod-name">${\'pro_showcase.demo_mod_1\' | i18n}</span> <dummy-toggle></dummy-toggle> </div> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-bolt"> <inline-svg src="'+n+'"></inline-svg> </span> <span class="save-mods-illustration-mod-name">${\'pro_showcase.demo_mod_2\' | i18n}</span> <dummy-toggle></dummy-toggle> </div> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-bolt"> <inline-svg src="'+n+'"></inline-svg> </span> <span class="save-mods-illustration-mod-name">${\'pro_showcase.demo_mod_3\' | i18n}</span> <dummy-toggle></dummy-toggle> </div> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-bolt"> <inline-svg src="'+n+'"></inline-svg> </span> <span class="save-mods-illustration-mod-name">${\'pro_showcase.demo_mod_4\' | i18n}</span> <dummy-number-input></dummy-number-input> </div> </div> </div> </template> '},"shared/pro-promos/save-mods-illustration.scss":(i,t,e)=>{e.r(t),e.d(t,{default:()=>g});var s=e(31601),a=e.n(s),o=e(76314),n=e.n(o),l=e(4417),r=e.n(l),d=new URL(e(83959),e.b),u=n()(a()),m=r()(d);u.push([i.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,save-mods-illustration .save-mods-illustration-list-user{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}save-mods-illustration .save-mods-illustration{display:flex;flex-direction:column;align-items:center;--video-overlay-bg: linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%), var(--white-20, rgba(255, 255, 255, 0.2))}save-mods-illustration .save-mods-illustration-button{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:24px;line-height:28px;letter-spacing:-1px;font-weight:700;color:var(--theme--text-highlight);display:flex;align-items:center;gap:16px;border-radius:100px;padding:16px 28px 16px 20px;background:var(--video-overlay-bg);backdrop-filter:blur(22px)}save-mods-illustration .save-mods-illustration-button-toggle{display:flex;align-items:center;justify-content:flex-end;background-color:#fa1280;border-radius:100px;width:62px;padding:4px}save-mods-illustration .save-mods-illustration-button-toggle span{display:inline-flex;padding:6px;background-color:#fff;border-radius:100px;filter:drop-shadow(0px 1.735px 3.471px rgba(16, 24, 40, 0.06)) drop-shadow(0px 1.735px 5.206px rgba(16, 24, 40, 0.1));color:#fa1280}save-mods-illustration .save-mods-illustration-button-toggle span svg{width:20px;height:20px}save-mods-illustration .save-mods-illustration-mod-name{flex-grow:1}save-mods-illustration .save-mods-illustration-list{box-shadow:0px 0px 69.011px 0px rgba(255,255,255,.08) inset;backdrop-filter:blur(22px);border-radius:16px;overflow:hidden;margin-top:16px;width:376px}save-mods-illustration .save-mods-illustration-list-bolt{display:inline-flex;color:#fa1280}save-mods-illustration .save-mods-illustration-list-bolt svg{width:16px;height:16px}save-mods-illustration .save-mods-illustration-list-user{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--text-highlight);font-size:16px}save-mods-illustration .save-mods-illustration-list-user:before{font-family:inherit;content:"person"}save-mods-illustration .save-mods-illustration-list-bolt,save-mods-illustration .save-mods-illustration-list-user{margin-right:10px}save-mods-illustration .save-mods-illustration-list-row{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-highlight);display:flex;align-items:center;background:var(--video-overlay-bg);padding:8px 12px}save-mods-illustration .save-mods-illustration-list-row:nth-child(1){font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;border-radius:16px 16px 0 0;margin-bottom:1px}save-mods-illustration .save-mods-illustration-list-row:last-child{border-radius:0 0 16px 16px}`,""]);const g=u}}]);
uniform float4x4 ViewProj;
uniform float4 color = {1.0, 1.0, 1.0, 1.0};

uniform float4 randomvals1;
uniform float4 randomvals2;
uniform float4 randomvals3;

struct SolidVertInOut {
	float4 pos : POSITION;
};

SolidVertInOut VSSolid(SolidVertInOut vert_in)
{
	SolidVertInOut vert_out;
	vert_out.pos = mul(float4(vert_in.pos.xyz, 1.0), ViewProj);
	return vert_out;
}

float4 PSSolid(SolidVertInOut vert_in) : TARGET
{
	return color;
}

float rand(float4 pos, float4 rand_vals)
{
	return 0.5 + 0.5 * frac(sin(dot(pos.xy, float2(rand_vals.x, rand_vals.y))) * rand_vals.z);
}

float4 PSRandom(SolidVertInOut vert_in) : TARGET
{
	return float4(rand(vert_in.pos, randomvals1),
	              rand(vert_in.pos, randomvals2),
	              rand(vert_in.pos, randomvals3),
	              1.0);
}

struct SolidColoredVertInOut {
	float4 pos   : POSITION;
	float4 color : COLOR;
};

SolidColoredVertInOut VSSolidColored(SolidColoredVertInOut vert_in)
{
	SolidColoredVertInOut vert_out;
	vert_out.pos   = mul(float4(vert_in.pos.xyz, 1.0), ViewProj);
	vert_out.color = vert_in.color;
	return vert_out;
}

float4 PSSolidColored(SolidColoredVertInOut vert_in) : TARGET
{
	return vert_in.color * color;
}

technique Solid
{
	pass
	{
		vertex_shader = VSSolid(vert_in);
		pixel_shader  = PSSolid(vert_in);
	}
}

technique SolidColored
{
	pass
	{
		vertex_shader = VSSolidColored(vert_in);
		pixel_shader  = PSSolidColored(vert_in);
	}
}

technique Random
{
	pass
	{
		vertex_shader = VSSolid(vert_in);
		pixel_shader  = PSRandom(vert_in);
	}
}

"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[2132],{27267:(t,e,s)=>{s.d(e,{y:()=>o});var a=s(15215),r=s("aurelia-framework");class o{activate(t){this.metadata=t.metadata||{},this.onInput=t.onInput||(()=>{}),this.debugInfo=t.debugInfo,this.gameTranslations=t.gameTranslations,this.currentFlow=t.currentFlow||""}}(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],o.prototype,"metadata",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],o.prototype,"onInput",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],o.prototype,"debugInfo",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],o.prototype,"gameTranslations",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",String)],o.prototype,"currentFlow",void 0)},41772:(t,e,s)=>{s.d(e,{d:()=>a});const a=["en-US","zh-CN","de-DE","es-ES","fr-FR","pl-PL","pt-BR","tr-TR","ja-JP","ko-KR","hi-IN","id-ID","it-IT","th-TH",`en-US-${s(77847).kA}`]},77502:(t,e,s)=>{s.d(e,{g:()=>a}),s("aurelia-framework");const a={AccountIssue:{key:"account-issue",component:"./account-issue"},AgreedToGameGeneralSetup:{key:"agreed-to-game-general-setup",component:"./agreed-to-game-general-setup"},AgreedToGameInstructions:{key:"agreed-to-game-instructions",component:"./agreed-to-game-instructions"},AgreedToGamePlatformNotes:{key:"agreed-to-game-platform-notes",component:"./agreed-to-game-platform-notes"},AgreedToModsInstructions:{key:"agreed-to-mods-instructions",component:"./agreed-to-mods-instructions"},FlowError:{key:"flow-error",component:"./flow-error"},FlowResolved:{key:"flow-resolved",component:"./flow-resolved"},GameCrashAdvancedTroubleshoot:{key:"game-crash-advanced-troubleshoot",component:"./game-crash-advanced-troubleshoot"},GameCrashBasicTroubleshoot:{key:"game-crash-basic-troubleshoot",component:"./game-crash-basic-troubleshoot"},GameCrashTiming:{key:"game-crash-timing",component:"./game-crash-timing"},GameGeneralSetup:{key:"game-general-setup",component:"./game-general-setup"},GameGeneralSetupFollowup:{key:"game-general-setup-followup",component:"./game-general-setup-followup"},GameInstructionsFollowup:{key:"game-instructions-followup",component:"./game-instructions-followup"},GameInstructionsReview:{key:"game-instructions-review",component:"./game-instructions-review"},GamePlatformNotes:{key:"game-platform-notes",component:"./game-platform-notes"},GamePlatformNotesFollowup:{key:"game-platform-notes-followup",component:"./game-platform-notes-followup"},GamePlatformSelection:{key:"game-platform-selection",component:"./game-platform-selection"},GeneralFeedback:{key:"general-feedback",component:"./general-feedback"},HotkeyNumpadCheck:{key:"hotkey-numpad-check",component:"./hotkey-numpad-check"},HotkeyNumpadCheckFollowup:{key:"hotkey-numpad-check-followup",component:"./hotkey-numpad-check-followup"},ModAdvancedTroubleshoot:{key:"mod-advanced-troubleshoot",component:"./mod-advanced-troubleshoot"},ModBasicTroubleshoot:{key:"mod-basic-troubleshoot",component:"./mod-basic-troubleshoot"},OverlayBasicTroubleshoot:{key:"overlay-basic-troubleshoot",component:"./overlay-basic-troubleshoot"},SelectMods:{key:"select-mods",component:"./select-mods"},SelectedModsInstructions:{key:"selected-mods-instructions",component:"./selected-mods-instructions"},SelectedModsInstructionsFollowup:{key:"selected-mods-instructions-followup",component:"./selected-mods-instructions-followup"},Start:{key:"start",component:"./start"},SubmitIssueToDiscord:{key:"submit-issue-to-discord",component:"./submit-issue-to-discord"},VerifyGameFiles:{key:"verify-game-files",component:"./verify-game-files"},VerifyGameFilesResolved:{key:"verify-game-files-resolved",component:"./verify-game-files-resolved"},WhichModsNotWorking:{key:"which-mods-not-working",component:"./which-mods-not-working"}}},"support-assistant/resources/elements/input-options":(t,e,s)=>{s.r(e),s.d(e,{InputOptions:()=>o});var a=s(15215),r=s("aurelia-framework");class o{constructor(){this.options=[],this.selectedOption=null,this.handleInput=t=>{this.selectedOption=t,this.onInput(t.value)}}get displayOptions(){return this.options.map(((t,e)=>({...t,animationDelay:100*(e+1)+300})))}}(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Array)],o.prototype,"options",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Function)],o.prototype,"onInput",void 0),(0,a.Cg)([(0,r.computedFrom)("options"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],o.prototype,"displayOptions",null)},"support-assistant/resources/elements/input-options.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="./step.scss"></require> <require from="./input-options.scss"></require> <ul if.bind="!selectedOption" class="support-assistant-input-options"> <li repeat.for="option of displayOptions" css="animation-delay: ${option.animationDelay}ms;"> <button type="button" class="support-assistant-step-user-message button" click.trigger="handleInput(option)"> ${option.labelKey | i18n} </button> </li> </ul> <div else class="support-assistant-input-options-answer"> <span class="accessible-label">${\'support_assistant.you_selected\' | i18n}</span> <div class="support-assistant-step-user-message">${selectedOption.labelKey | i18n}</div> </div> </template> '},"support-assistant/resources/elements/input-options.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>n});var a=s(31601),r=s.n(a),o=s(76314),i=s.n(o)()(r());i.push([t.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@keyframes support-assistant-show-message{100%{opacity:1;transform:none}}.support-assistant-step{display:flex;flex-direction:column}.support-assistant-step-message{animation:support-assistant-show-message 300ms 100ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;opacity:0;transform:translateY(20px);align-self:flex-start;max-width:90%;padding:16px;margin:0 0 12px;border-radius:12px;background:#353b44;color:rgba(255,255,255,.8);font-size:14px;font-weight:500;line-height:20px}.support-assistant-step-message a{color:var(--theme--highlight);transition:color 75ms ease-in-out;text-decoration:none}.support-assistant-step-message a:hover{color:rgba(var(--theme--highlight--rgb), 0.8)}.support-assistant-step-message ul{margin:0 0 14px;padding:0}.support-assistant-step-message ul li{padding:0;margin:0 0 0 20px}.support-assistant-step-message p{margin:0 0 1em 0}.support-assistant-step-message p:last-child{margin:0}.support-assistant-step-user-message{align-self:flex-end;max-width:90%;padding:8px 12px;margin:0 0 4px;border-radius:12px;background:#202329;color:rgba(255,255,255,.8);font-size:14px;font-weight:500;line-height:20px}.support-assistant-step-user-message.button{border:none;align-self:flex-end;transition:background-color 75ms ease-in-out}.support-assistant-step-user-message.button:hover{background-color:#353b44}.support-assistant-input-options{list-style-type:none;padding:0;margin:0 0 8px;display:flex;flex-direction:column}.support-assistant-input-options .accessible-label{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0}.support-assistant-input-options li,.support-assistant-input-options-answer{display:flex;flex-direction:row-reverse}.support-assistant-input-options li{animation:support-assistant-show-message 300ms 300ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;opacity:0;transform:translateX(-20px)}.support-assistant-input-options-answer{animation:support-assistant-show-message 300ms 100ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;opacity:0;transform:translateY(20px);margin:0 0 8px}.support-assistant-input-options button{border:none}",""]);const n=i},"support-assistant/resources/elements/markdown-content":(t,e,s)=>{s.r(e),s.d(e,{MarkdownContent:()=>o});var a=s(15215),r=s("aurelia-framework");class o{constructor(){this.containerEl=null}attached(){this.containerEl&&this.containerEl.addEventListener("click",(t=>{"IMG"===t.target.tagName&&this.handleImageClick(t)}))}handleImageClick(t){const e=t.target;if(e&&e.src){const t=e.src;window.open(t,"_blank")}}}(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",String)],o.prototype,"content",void 0)},"support-assistant/resources/elements/markdown-content.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="./markdown-content.scss"></require> <div innerhtml.bind="content | markdown" class="markdown-content" ref="containerEl"></div> </template> '},"support-assistant/resources/elements/markdown-content.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>n});var a=s(31601),r=s.n(a),o=s(76314),i=s.n(o)()(r());i.push([t.id,".markdown-content img{max-width:100%;height:auto;cursor:pointer}",""]);const n=i},"support-assistant/resources/elements/step-wrapper":(t,e,s)=>{s.r(e),s.d(e,{StepWrapper:()=>o});var a=s(15215),r=s("aurelia-framework");class o{constructor(){this.isLoaded=!1,this.loadTimeout=null}attached(){const t=1500*Math.random()+1e3;this.loadTimeout=setTimeout((()=>{this.isLoaded=!0}),t)}detached(){this.loadTimeout&&(clearTimeout(this.loadTimeout),this.loadTimeout=null)}}(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],o.prototype,"isLoaded",void 0)},"support-assistant/resources/elements/step-wrapper.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="./step-wrapper.scss"></require> <div class="support-assistant-step-wrapper"> <div if.bind="isLoaded" class="support-assistant-step-wrapper-content"> <span class="accessible-label">${\'support_assistant.assistant_says\' | i18n}</span> <template replaceable part="step-content"></template> </div> <div else class="support-assistant-step-wrapper-loader-outer"> <div class="support-assistant-step-wrapper-loader"> <span class="accessible-label">${\'support_assistant.loading\' | i18n}</span> </div> </div> </div> </template> '},"support-assistant/resources/elements/step-wrapper.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>n});var a=s(31601),r=s.n(a),o=s(76314),i=s.n(o)()(r());i.push([t.id,'body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.support-assistant-step-wrapper .accessible-label{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);white-space:nowrap;border:0}.support-assistant-step-wrapper-loader-outer{width:100%;display:flex;align-items:left}.support-assistant-step-wrapper-loader{width:auto;padding:10px 16px;border-radius:12px;background:#353b44}.support-assistant-step-wrapper-loader::before{content:"";display:block;--gradient: no-repeat radial-gradient(farthest-side, rgba(255, 255, 255, 0.8) 90%, #fff0);width:15px;height:13px;aspect-ratio:1.154;background:var(--gradient) 50% 0,var(--gradient) 0 100%,var(--gradient) 100% 100%;background-size:35% 40.39%;animation:support-assistant-loader 1s infinite}.support-assistant-step-wrapper-content{display:flex;flex-direction:column}@keyframes support-assistant-loader{50%,100%{background-position:100% 100%,50% 0,0 100%}}',""]);const n=i},"support-assistant/resources/elements/step.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>n});var a=s(31601),r=s.n(a),o=s(76314),i=s.n(o)()(r());i.push([t.id,"@keyframes support-assistant-show-message{100%{opacity:1;transform:none}}.support-assistant-step{display:flex;flex-direction:column}.support-assistant-step-message{animation:support-assistant-show-message 300ms 100ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;opacity:0;transform:translateY(20px);align-self:flex-start;max-width:90%;padding:16px;margin:0 0 12px;border-radius:12px;background:#353b44;color:rgba(255,255,255,.8);font-size:14px;font-weight:500;line-height:20px}.support-assistant-step-message a{color:var(--theme--highlight);transition:color 75ms ease-in-out;text-decoration:none}.support-assistant-step-message a:hover{color:rgba(var(--theme--highlight--rgb), 0.8)}.support-assistant-step-message ul{margin:0 0 14px;padding:0}.support-assistant-step-message ul li{padding:0;margin:0 0 0 20px}.support-assistant-step-message p{margin:0 0 1em 0}.support-assistant-step-message p:last-child{margin:0}.support-assistant-step-user-message{align-self:flex-end;max-width:90%;padding:8px 12px;margin:0 0 4px;border-radius:12px;background:#202329;color:rgba(255,255,255,.8);font-size:14px;font-weight:500;line-height:20px}.support-assistant-step-user-message.button{border:none;align-self:flex-end;transition:background-color 75ms ease-in-out}.support-assistant-step-user-message.button:hover{background-color:#353b44}",""]);const n=i},"support-assistant/steps/account-issue":(t,e,s)=>{s.r(e),s.d(e,{AccountIssue:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{};i=(0,a.Cg)([r.autoinject],i)},"support-assistant/steps/account-issue.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p innerhtml.bind="\'support_assistant.we_are_unable_to_assist_with_account\' | i18n | markdown"></p> </div> </template> </step-wrapper> </template> '},"support-assistant/steps/agreed-to-game-general-setup":(t,e,s)=>{s.r(e),s.d(e,{AgreedToGameGeneralSetup:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.close",value:"close"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/agreed-to-game-general-setup.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.great_try_those_general_setup_steps\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/agreed-to-game-instructions":(t,e,s)=>{s.r(e),s.d(e,{AgreedToGameInstructions:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.close",value:"close"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/agreed-to-game-instructions.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.great_try_following_those_game_instructions\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/agreed-to-game-platform-notes":(t,e,s)=>{s.r(e),s.d(e,{AgreedToGamePlatformNotes:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.close",value:"close"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/agreed-to-game-platform-notes.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message">${\'support_assistant.great_try_that_out\' | i18n}</div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/agreed-to-mods-instructions":(t,e,s)=>{s.r(e),s.d(e,{AgreedToModsInstructions:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.close",value:"close"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/agreed-to-mods-instructions.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.great_try_following_those_mod_specific_notes\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/flow-error":(t,e,s)=>{s.r(e),s.d(e,{FlowError:()=>r});var a=s(27267);class r extends a.y{}},"support-assistant/steps/flow-error.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <span innerhtml.bind="\'support_assistant.sorry_something_went_wrong\' | i18n | markdown"></span> </div> <button type="button" class="support-assistant-step-user-message button" click.trigger="onInput(\'close\')"> ${\'support_assistant.close\' | i18n} </button> </template> </step-wrapper> </template> '},"support-assistant/steps/flow-resolved":(t,e,s)=>{s.r(e),s.d(e,{FlowResolved:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.i_have_another_issue",value:"restart"},{labelKey:"support_assistant.no_thats_all_thanks",value:"close"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/flow-resolved.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message">${\'support_assistant.great_im_glad_that_helped\' | i18n}</div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/game-crash-advanced-troubleshoot":(t,e,s)=>{s.r(e),s.d(e,{GameCrashBasicTroubleshoot:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_its_fixed_now",value:"yes"},{labelKey:"support_assistant.still_crashing",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-crash-advanced-troubleshoot.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a="<template> <require from=\"../resources/elements/step.scss\"></require> <require from=\"../resources/elements/step-wrapper\"></require> <require from=\"../resources/elements/input-options\"></require> <step-wrapper> <template replace-part=\"step-content\"> <div class=\"support-assistant-step-message\"> <p>${'support_assistant.advanced_crash_troubleshooting' | i18n }</p> <ul> <li>${'support_assistant.advanced_crash_troubleshooting_1' | i18n}</li> <li>${'support_assistant.advanced_crash_troubleshooting_2' | i18n}</li> <li>${'support_assistant.advanced_crash_troubleshooting_3' | i18n}</li> <li>${'support_assistant.advanced_crash_troubleshooting_4' | i18n}</li> <li>${'support_assistant.advanced_crash_troubleshooting_5' | i18n}</li> </ul> <p>${'support_assistant.advanced_crash_troubleshooting_confirmation' | i18n}</p> </div> <input-options options.bind=\"inputOptions\" on-input.bind=\"onInput\"></input-options> </template> </step-wrapper> </template> "},"support-assistant/steps/game-crash-basic-troubleshoot":(t,e,s)=>{s.r(e),s.d(e,{GameCrashBasicTroubleshoot:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.that_fixed_it",value:"yes"},{labelKey:"support_assistant.no_still_having_issues",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-crash-basic-troubleshoot.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a="<template> <require from=\"../resources/elements/step.scss\"></require> <require from=\"../resources/elements/step-wrapper\"></require> <require from=\"../resources/elements/input-options\"></require> <step-wrapper> <template replace-part=\"step-content\"> <div class=\"support-assistant-step-message\"> <p>${'support_assistant.crash_troubleshooting' | i18n }</p> <ul> <li>${'support_assistant.crash_troubleshooting_1' | i18n}</li> <li>${'support_assistant.crash_troubleshooting_2' | i18n}</li> <li>${'support_assistant.crash_troubleshooting_3' | i18n}</li> <li>${'support_assistant.crash_troubleshooting_4' | i18n}</li> <li>${'support_assistant.crash_troubleshooting_5' | i18n}</li> <li if.bind=\"metadata.canUseOverlay\">${'support_assistant.crash_troubleshooting_6' | i18n}</li> </ul> <p>${'support_assistant.crash_troubleshooting_confirmation' | i18n}</p> </div> <input-options options.bind=\"inputOptions\" on-input.bind=\"onInput\"></input-options> </template> </step-wrapper> </template> "},"support-assistant/steps/game-crash-timing":(t,e,s)=>{s.r(e),s.d(e,{GameCrashTiming:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.when_launching_the_game",value:"game-launch-with-wemod"},{labelKey:"support_assistant.when_activating_specific_mod",value:"activate-specific-mod"},{labelKey:"support_assistant.random_times_during_gameplay",value:"random"},{labelKey:"support_assistant.when_using_the_overlay",value:"when-using-overlay"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-crash-timing.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.lets_troubleshoot_game_crashes\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/game-general-setup":(t,e,s)=>{s.r(e),s.d(e,{GameGeneralSetup:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_ive_followed_all_these_steps",value:"yes"},{labelKey:"support_assistant.no_i_havent_tried_all_of_them",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-general-setup-followup":(t,e,s)=>{s.r(e),s.d(e,{GameGeneralSetupFollowup:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.ill_try_these_steps",value:"yes"},{labelKey:"support_assistant.continue_anyway",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-general-setup-followup.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.recommend_game_general_setup_steps\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/game-general-setup.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p>${\'support_assistant.game_general_setup_instructions\' | i18n:{gameTitle: metadata.titleName}}</p> <ul> <li>${\'support_assistant.game_general_setup_instructions_1\' | i18n}</li> <li>${\'support_assistant.game_general_setup_instructions_2\' | i18n}</li> <li>${\'support_assistant.game_general_setup_instructions_3\' | i18n}</li> <li>${\'support_assistant.game_general_setup_instructions_4\' | i18n}</li> </ul> <p>${\'support_assistant.game_general_setup_instructions_confirmation\' | i18n}</p> </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/game-instructions-followup":(t,e,s)=>{s.r(e),s.d(e,{GameInstructionsFollowup:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.ill_try_these_steps",value:"yes"},{labelKey:"support_assistant.continue_anyway",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-instructions-followup.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.i_recommend_following_these_game_instructions\' | i18n:{gameTitle: metadata.titleName}} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/game-instructions-review":(t,e,s)=>{s.r(e),s.d(e,{GameInstructionsReview:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_ive_followed_these_instructions",value:"yes"},{labelKey:"support_assistant.no_i_havent_tried_all_of_these",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-instructions-review.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <require from="../resources/elements/markdown-content"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p> ${\'support_assistant.now_lets_check_if_youve_followed_game_instructions\' | i18n:{gameTitle: metadata.titleName}} </p> <markdown-content content.bind="metadata.gameInstructions"></markdown-content> <p>${\'support_assistant.have_you_followed_these_game_instructions\' | i18n}</p> </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/game-platform-notes":(t,e,s)=>{s.r(e),s.d(e,{GamePlatformNotes:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_ive_followed_these_instructions",value:"yes"},{labelKey:"support_assistant.no_i_havent_tried_all_of_these",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-platform-notes-followup":(t,e,s)=>{s.r(e),s.d(e,{GamePlatformNotesFollowup:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.ill_try_these_steps",value:"yes"},{labelKey:"support_assistant.continue_anyway",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-platform-notes-followup.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.we_recommend_following_game_platform_instructions\' | i18n:{gamePlatform: metadata.gamePlatform}} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/game-platform-notes.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p>${\'support_assistant.game_platform_notes\' | i18n:{gamePlatform: metadata.gamePlatform}}</p> <ul if.bind="metadata.gamePlatform === \'steam\'"> <li innerhtml.bind="\'support_assistant.game_platform_notes_steam_1\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_steam_2\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_steam_3\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_steam_4\' | i18n | markdown"></li> </ul> <ul if.bind="metadata.gamePlatform === \'epic\'"> <li innerhtml.bind="\'support_assistant.game_platform_notes_epic_1\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_epic_2\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_epic_3\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_epic_4\' | i18n | markdown"></li> </ul> <ul if.bind="metadata.gamePlatform === \'gog\'"> <li innerhtml.bind="\'support_assistant.game_platform_notes_gog_1\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_gog_2\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_gog_3\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_gog_4\' | i18n | markdown"></li> </ul> <ul if.bind="metadata.gamePlatform === \'xbox\'"> <li innerhtml.bind="\'support_assistant.game_platform_notes_xbox_1\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_xbox_2\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_xbox_3\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_xbox_4\' | i18n | markdown"></li> <li innerhtml.bind="\'support_assistant.game_platform_notes_xbox_5\' | i18n | markdown"></li> </ul> <p>${\'support_assistant.game_platform_notes_confirmation\' | i18n}</p> </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/game-platform-selection":(t,e,s)=>{s.r(e),s.d(e,{GamePlatformSelection:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.steam",value:"steam"},{labelKey:"support_assistant.epic_games",value:"epic"},{labelKey:"support_assistant.gog",value:"gog"},{labelKey:"support_assistant.xbox_microsoft_store",value:"xbox"},{labelKey:"support_assistant.other",value:"other"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/game-platform-selection.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.where_did_you_purchase_game\' | i18n:{gameTitle: metadata.titleName}} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/general-feedback":(t,e,s)=>{s.r(e),s.d(e,{GeneralFeedback:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(...arguments),this.boardId="676064fc1c16dbbaa5b26e8d",this.handleFeaturebaseLinkClick=t=>{if(t.preventDefault(),t.stopPropagation(),this.metadata?.featurebaseJwt){const t=new URL("https://wemod.featurebase.app/api/v1/auth/access/jwt");t.searchParams.set("jwt",this.metadata.featurebaseJwt??""),t.searchParams.set("return_to",`https://hub.wemod.com?b=${this.boardId}`),window.open(t.toString(),"_blank")}else window.open(`https://hub.wemod.com?b=${this.boardId}`)}}};i=(0,a.Cg)([r.autoinject],i)},"support-assistant/steps/general-feedback.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p>${\'support_assistant.please_head_over_to_our_online_feedback_hub\' | i18n}</p> <a href="https://hub.wemod.com/?b=${boardId}" click.trigger="handleFeaturebaseLinkClick($event)">${\'support_assistant.share_feedback\' | i18n}</a> </div> </template> </step-wrapper> </template> '},"support-assistant/steps/hotkey-numpad-check":(t,e,s)=>{s.r(e),s.d(e,{HotkeyNumbpadCheck:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes",value:"yes"},{labelKey:"support_assistant.no",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/hotkey-numpad-check-followup":(t,e,s)=>{s.r(e),s.d(e,{HotkeyNumbpadCheckFollowup:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.that_fixed_it",value:"yes"},{labelKey:"support_assistant.no_still_having_issues",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/hotkey-numpad-check-followup.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.make_sure_numlock_is_turned_on\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/hotkey-numpad-check.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.ill_help_you_troubleshoot_hotkey_issues\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/mod-advanced-troubleshoot":(t,e,s)=>{s.r(e),s.d(e,{ModAdvancedTroubleshoot:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_its_fixed_now",value:"yes"},{labelKey:"support_assistant.no_still_having_issues",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/mod-advanced-troubleshoot.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p>${\'support_assistant.advanced_troubleshooting\' | i18n}</p> <ul> <li>${\'support_assistant.advanced_troubleshooting_1\' | i18n}</li> <li>${\'support_assistant.advanced_troubleshooting_2\' | i18n}</li> </ul> <p>${\'support_assistant.advanced_troubleshooting_confirmation\' | i18n}</p> </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/mod-basic-troubleshoot":(t,e,s)=>{s.r(e),s.d(e,{ModBasicTroubleshoot:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_its_fixed_now",value:"yes"},{labelKey:"support_assistant.no_still_having_issues",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/mod-basic-troubleshoot.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p>${\'support_assistant.mod_basic_troubleshooting\' | i18n}</p> <ul> <li>${\'support_assistant.mod_basic_troubleshooting_1\' | i18n}</li> <li>${\'support_assistant.mod_basic_troubleshooting_2\' | i18n}</li> <li>${\'support_assistant.mod_basic_troubleshooting_3\' | i18n}</li> <li>${\'support_assistant.mod_basic_troubleshooting_4\' | i18n}</li> </ul> <p>${\'support_assistant.mod_basic_troubleshooting_confirmation\' | i18n}</p> </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/overlay-basic-troubleshoot":(t,e,s)=>{s.r(e),s.d(e,{OverlayBasicTroubleshoot:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_its_fixed_now",value:"yes"},{labelKey:"support_assistant.no_still_having_issues",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/overlay-basic-troubleshoot.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p>${\'support_assistant.overlay_basic_troubleshoot\' | i18n}</p> <ul> <li>${\'support_assistant.overlay_basic_troubleshoot_1\' | i18n}</li> <li>${\'support_assistant.overlay_basic_troubleshoot_2\' | i18n}</li> </ul> <p>${\'support_assistant.overlay_basic_troubleshoot_confirmation\' | i18n}</p> </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/select-mods":(t,e,s)=>{s.r(e),s.d(e,{SelectMods:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(...arguments),this.formEl=null,this.selectedMods={},this.isFormValid=!1,this.isSubmitted=!1,this.handleModSelection=(t,e)=>{e&&t.target?.checked?this.selectedMods[e.uuid]=e:this.selectedMods[e.uuid]&&delete this.selectedMods[e.uuid],this.isFormValid=!!Object.keys(this.selectedMods).length},this.handleSubmit=t=>{if(t.preventDefault(),this.isSubmitted=!0,this.selectedModsArray.length){const t=this.selectedModsArray.map((t=>`${t.name} (${t.uuid})`)).join(", ");this.onInput?.(this.selectedModsArray,t)}}}get selectedModsArray(){return Object.values(this.selectedMods)}};i=(0,a.Cg)([r.autoinject],i)},"support-assistant/steps/select-mods.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="shared/cheats/resources/value-converters/blueprint-translation"></require> <require from="shared/cheats/resources/value-converters/group-cheats"></require> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <require from="./select-mods.scss"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.which_specific_mods_arent_working_correctly\' | i18n} </div> <ul if.bind="isSubmitted" class="support-assistant-step-user-message support-assistant-select-mods-selected-list"> <li repeat.for="mod of selectedModsArray"> ${mod.name | blueprintTranslation: metadata.gameTranslations} </li> </ul> <form else submit.trigger="handleSubmit($event)" class="support-assistant-select-mods"> <div class="support-assistant-step-user-message support-assistant-select-mods-scroll-container"> <div repeat.for="category of metadata.modsList | groupCheats"> <h3>${\'trainer_cheats_list.category_\' + category.category | i18n}</h3> <ul> <li repeat.for="mod of category.values"> <input type="checkbox" value.bind="mod.uuid" name="support-assistant-mod-input-${mod.uuid}" id="support-assistant-mod-input-${mod.uuid}" change.delegate="handleModSelection($event, mod)"> <label for="support-assistant-mod-input-${mod.uuid}">${mod.name | blueprintTranslation: metadata.gameTranslations}</label> </li> </ul> </div> </div> <div if.bind="!isFormValid" class="support-assistant-step-user-message"> ${\'support_assistant.please_select_mods\' | i18n} </div> <button else type="submit" class="support-assistant-step-user-message"> ${\'support_assistant.submit\' | i18n} </button> </form> </template> </step-wrapper> </template> '},"support-assistant/steps/select-mods.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>d});var a=s(31601),r=s.n(a),o=s(76314),i=s.n(o),n=s(4417),p=s.n(n),l=new URL(s(81206),s.b),u=i()(r()),c=p()(l);u.push([t.id,`@keyframes support-assistant-show-message{100%{opacity:1;transform:none}}.support-assistant-step{display:flex;flex-direction:column}.support-assistant-step-message{animation:support-assistant-show-message 300ms 100ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;opacity:0;transform:translateY(20px);align-self:flex-start;max-width:90%;padding:16px;margin:0 0 12px;border-radius:12px;background:#353b44;color:rgba(255,255,255,.8);font-size:14px;font-weight:500;line-height:20px}.support-assistant-step-message a{color:var(--theme--highlight);transition:color 75ms ease-in-out;text-decoration:none}.support-assistant-step-message a:hover{color:rgba(var(--theme--highlight--rgb), 0.8)}.support-assistant-step-message ul{margin:0 0 14px;padding:0}.support-assistant-step-message ul li{padding:0;margin:0 0 0 20px}.support-assistant-step-message p{margin:0 0 1em 0}.support-assistant-step-message p:last-child{margin:0}.support-assistant-step-user-message{align-self:flex-end;max-width:90%;padding:8px 12px;margin:0 0 4px;border-radius:12px;background:#202329;color:rgba(255,255,255,.8);font-size:14px;font-weight:500;line-height:20px}.support-assistant-step-user-message.button{border:none;align-self:flex-end;transition:background-color 75ms ease-in-out}.support-assistant-step-user-message.button:hover{background-color:#353b44}.support-assistant-select-mods{display:flex;flex-direction:column;color:rgba(255,255,255,.6)}.support-assistant-select-mods-scroll-container{animation:support-assistant-show-message 300ms 300ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;opacity:0;transform:translateX(-20px);max-height:200px;width:100%;overflow-y:auto;padding:8px 0}.support-assistant-select-mods-scroll-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.support-assistant-select-mods-scroll-container::-webkit-scrollbar-thumb:window-inactive,.support-assistant-select-mods-scroll-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.support-assistant-select-mods-scroll-container::-webkit-scrollbar-thumb:window-inactive:hover,.support-assistant-select-mods-scroll-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.support-assistant-select-mods-selected-list{animation:support-assistant-show-message 300ms 100ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;opacity:0;transform:translateY(20px);list-style-type:none;display:flex;flex-direction:column;gap:16px}.support-assistant-select-mods-selected-list li{display:block;padding:0;margin:0}.support-assistant-select-mods h3{margin:0 0 8px;padding:0 12px;font-size:11px;font-style:normal;font-weight:700;line-height:16px;letter-spacing:.5px;text-transform:uppercase}.support-assistant-select-mods ul{list-style-type:none;padding:0;margin:0 0 8px}.support-assistant-select-mods li{padding:0;margin:0;display:block;position:relative}.support-assistant-select-mods label{display:block;padding:6px 12px 6px 36px;margin:0;transition:background-color 75ms ease-in-out;cursor:pointer}.support-assistant-select-mods label:hover{background:rgba(255,255,255,.1)}.support-assistant-select-mods input[type=checkbox]{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;--checkbox--checked-color: var(--theme--highlight);position:absolute;top:50%;left:12px;transform:translateY(-50%);width:16px;height:16px;margin:0;cursor:pointer;pointer-events:none}.support-assistant-select-mods input[type=checkbox],.support-assistant-select-mods input[type=checkbox] *{cursor:pointer}.support-assistant-select-mods input[type=checkbox]:checked:before{opacity:1}.support-assistant-select-mods input[type=checkbox]:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${c});mask:url(${c})}.support-assistant-select-mods input[type=checkbox]:checked:before{opacity:1}.support-assistant-select-mods button{animation:support-assistant-show-message 300ms 100ms cubic-bezier(0.38, 0.97, 0.56, 0.76) forwards;opacity:0;transform:translateY(20px);border:none;transition:background-color 75ms ease-in-out}.support-assistant-select-mods button:hover:not(:disabled){background-color:#353b44}.support-assistant-select-mods button:disabled{cursor:default;animation:none}`,""]);const d=u},"support-assistant/steps/selected-mods-instructions":(t,e,s)=>{s.r(e),s.d(e,{SelectedModsInstructions:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_ive_followed_all_these_notes",value:"yes"},{labelKey:"support_assistant.no_i_havent_tried_all_of_them",value:"no"}]}get modsWithNotes(){return this.metadata?.selectedMods.filter((t=>t.instructions||t.description))}};(0,a.Cg)([(0,r.computedFrom)("metadata.selectedMods"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],i.prototype,"modsWithNotes",null),i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/selected-mods-instructions-followup":(t,e,s)=>{s.r(e),s.d(e,{SelectedModsInstructionsFollowup:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.ill_try_these_steps",value:"yes"},{labelKey:"support_assistant.continue_anyway",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/selected-mods-instructions-followup.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.i_recommend_following_these_mod_specific_notes\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/selected-mods-instructions.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="shared/cheats/resources/value-converters/blueprint-translation"></require> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <require from="../resources/elements/markdown-content"></require> <require from="./selected-mods-instructions.scss"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.have_you_followed_these_mod_specific_notes\' | i18n} </div> <div class="support-assistant-step-message support-assistant-selected-mods-instructions"> <ul> <li repeat.for="mod of modsWithNotes"> <div class="mod-name"> <strong>${$index + 1}. ${mod.name | blueprintTranslation: metadata.gameTranslations}</strong> </div> <div if.bind="mod.description" class="notes" innerhtml.bind="mod.description | markdown"></div> <span if.bind="mod.instructions" class="instructions-heading">${\'support_assistant.instructions\' | i18n}</span> <markdown-content if.bind="mod.instructions" class="instructions" content.bind="mod.instructions"></markdown-content> </li> </ul> </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/selected-mods-instructions.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>n});var a=s(31601),r=s.n(a),o=s(76314),i=s.n(o)()(r());i.push([t.id,".support-assistant-selected-mods-instructions{padding:0;width:100%}.support-assistant-selected-mods-instructions .instructions-heading{display:block;overflow:hidden;color:rgba(255,255,255,.6);text-overflow:ellipsis;white-space:nowrap;font-size:11px;font-weight:700;line-height:16px;letter-spacing:.5px;text-transform:uppercase}.support-assistant-selected-mods-instructions .mod-name,.support-assistant-selected-mods-instructions .notes,.support-assistant-selected-mods-instructions .instructions-heading,.support-assistant-selected-mods-instructions .instructions{margin:0 0 8px}.support-assistant-selected-mods-instructions ul{list-style-type:none;padding:0;margin:0 0 8px}.support-assistant-selected-mods-instructions ul li{display:block;width:100%;padding:16px 16px 8px;margin:0;border-bottom:.5px solid rgba(255,255,255,.15)}.support-assistant-selected-mods-instructions ul li:last-child{border-bottom:none}",""]);const n=i},"support-assistant/steps/start":(t,e,s)=>{s.r(e),s.d(e,{Start:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{get inputOptions(){return[{labelKey:"support_assistant.mods_not_working",value:"mods-not-working"},...this.metadata?.canUseOverlay?[{labelKey:"support_assistant.overlay_not_working",value:"overlay-not-working"}]:[],{labelKey:"support_assistant.hotkeys_not_working",value:"hotkeys-not-working"},{labelKey:"support_assistant.game_is_crashing",value:"game-crashing"},{labelKey:"support_assistant.i_have_general_feedback",value:"general-feedback"},{labelKey:"support_assistant.i_have_an_account_issue",value:"account-issue"}]}};(0,a.Cg)([(0,r.computedFrom)("metadata.canUseOverlay"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],i.prototype,"inputOptions",null),i=(0,a.Cg)([r.autoinject],i)},"support-assistant/steps/start.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div if.bind="metadata.isRestart" class="support-assistant-step-message"> ${\'support_assistant.what_else_can_i_help_you_with\' | i18n} </div> <div else class="support-assistant-step-message"> ${\'support_assistant.welcome\' | i18n:{username: metadata.username}} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/submit-issue-to-discord":(t,e,s)=>{s.r(e),s.d(e,{SubmitIssueToDiscord:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);class i extends o.y{constructor(){super(...arguments),this.isTranscriptCopied=!1,this.transcriptEl=null,this.copyIconTimeout=null}detached(){this.copyIconTimeout&&(clearTimeout(this.copyIconTimeout),this.copyIconTimeout=null)}get flowName(){return this.currentFlow?.replace(/-/g," ")||"unknown flow"}get debugInfoDisplay(){const{envInfo:t,titleName:e,gamePlatform:s,correlationId:a,trainerId:r}=this.debugInfo,{appVersion:o,osVersion:i,osArch:n,locale:p,freeMemory:l,totalMemory:u,antivirusProducts:c}=t,d={"Title Name":e,"Selected mods":this.metadata?.selectedMods?.map((t=>t?.name||"")).join(", "),"Game Platform":s,"Correlation ID":a,"Trainer ID":r,"App version":o,"OS version":i,"OS architecture":n,Locale:p,"Free memory":l,"Total memory":u,"Antivirus products":c?.join(", ")};return Object.entries(d).filter((([t,e])=>!!e)).map((([t,e])=>({label:t,value:e})))}async handleCopyClick(){if(this.transcriptEl){const t=this.metadata?.transcript.map((({label:t,value:e})=>`- ${t}: ${e}`)).join("\n")||"",e=this.debugInfoDisplay.map((({label:t,value:e})=>`- ${t}: ${e}`)).join("\n"),s=`Support request: ${this.flowName}\n\n${t}\n\n${e}`;try{await navigator.clipboard.writeText(s),this.isTranscriptCopied=!0}catch(t){}this.copyIconTimeout=setTimeout((()=>{this.isTranscriptCopied=!1}),2e3)}}}(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],i.prototype,"isTranscriptCopied",void 0),(0,a.Cg)([(0,r.computedFrom)("currentFlow"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],i.prototype,"flowName",null),(0,a.Cg)([(0,r.computedFrom)("debugInfo","metadata.selectedMods"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],i.prototype,"debugInfoDisplay",null)},"support-assistant/steps/submit-issue-to-discord.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="./submit-issue-to-discord.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message submit-issue-to-discord"> <p innerhtml.bind="\'support_assistant.further_troubleshooting_needed\' | i18n | markdown"></p> <div class="submit-issue-to-discord-transcript-container"> \x3c!--# transcript intentionally not localized, for use by support team --\x3e <div class="submit-issue-to-discord-transcript" ref="transcriptEl"> <strong>Support request: ${flowName}</strong> <ul> <li repeat.for="item of metadata.transcript">${item.label}: ${item.value}</li> </ul> <ul> <li repeat.for="item of debugInfoDisplay">${item.label}: ${item.value}</li> </ul> </div> <button type="button" click.trigger="handleCopyClick()" class="submit-issue-to-discord-copy-button"> ${\'support_assistant.copy\' | i18n} <i>${isTranscriptCopied ? \'check\' : \'copy_all\'}</i> </button> </div> </div> </template> </step-wrapper> </template> '},"support-assistant/steps/submit-issue-to-discord.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>d});var a=s(31601),r=s.n(a),o=s(76314),i=s.n(o),n=s(4417),p=s.n(n),l=new URL(s(83959),s.b),u=i()(r()),c=p()(l);u.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,.submit-issue-to-discord-copy-button i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.submit-issue-to-discord-transcript,.submit-issue-to-discord-transcript *{cursor:text;user-select:all}.submit-issue-to-discord-transcript-container{position:relative;margin-top:20px}.submit-issue-to-discord-transcript{font-size:12px;line-height:16px;max-width:100%;max-height:200px;overflow-y:auto;padding:16px 8px 8px;background:rgba(0,0,0,.2);word-wrap:break-word;border-radius:4px}.submit-issue-to-discord-transcript::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.submit-issue-to-discord-transcript::-webkit-scrollbar-thumb:window-inactive,.submit-issue-to-discord-transcript::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.submit-issue-to-discord-transcript::-webkit-scrollbar-thumb:window-inactive:hover,.submit-issue-to-discord-transcript::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.submit-issue-to-discord-transcript strong{display:block;margin:0 0 8px 0}.submit-issue-to-discord-transcript li{margin:0 0 0 12px}.submit-issue-to-discord-copy-button{position:absolute;top:0;right:12px;transform:translateY(-50%);display:flex;align-items:center;gap:4px;text-align:center;border:none;background:#202329;border-radius:4px;padding:4px 4px 6px 8px;font-size:12px;color:rgba(255,255,255,.8);transition:background-color 75ms ease-in-out}.submit-issue-to-discord-copy-button:hover{background-color:#131417}.submit-issue-to-discord-copy-button i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;width:16px;height:16px}`,""]);const d=u},"support-assistant/steps/verify-game-files":(t,e,s)=>{s.r(e),s.d(e,{VerifyGameFiles:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.yes_its_fixed_now",value:"yes"},{labelKey:"support_assistant.no_still_having_issues",value:"no"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/verify-game-files-resolved":(t,e,s)=>{s.r(e),s.d(e,{VerifyGameFilesResolved:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.i_have_another_issue",value:"restart"},{labelKey:"support_assistant.no_thats_all_thanks",value:"close"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/verify-game-files-resolved.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.great_game_file_verification_often_fixes\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/verify-game-files.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> <p>${\'support_assistant.verify_game_files\' | i18n }</p> <ul> <li>${\'support_assistant.verify_game_files_1\' | i18n}</li> <li>${\'support_assistant.verify_game_files_2\' | i18n}</li> <li>${\'support_assistant.verify_game_files_3\' | i18n}</li> <li>${\'support_assistant.verify_game_files_4\' | i18n}</li> </ul> <p>${\'support_assistant.verify_game_files_confirmation\' | i18n}</p> </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/steps/which-mods-not-working":(t,e,s)=>{s.r(e),s.d(e,{GameGeneralSetup:()=>i});var a=s(15215),r=s("aurelia-framework"),o=s(27267);let i=class extends o.y{constructor(){super(),this.inputOptions=[{labelKey:"support_assistant.all_mods",value:"all"},{labelKey:"support_assistant.specific_mods",value:"specific"}]}};i=(0,a.Cg)([r.autoinject,(0,a.Sn)("design:paramtypes",[])],i)},"support-assistant/steps/which-mods-not-working.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>a});const a='<template> <require from="../resources/elements/step.scss"></require> <require from="../resources/elements/step-wrapper"></require> <require from="../resources/elements/input-options"></require> <step-wrapper> <template replace-part="step-content"> <div class="support-assistant-step-message"> ${\'support_assistant.which_mods_arent_working_correctly\' | i18n} </div> <input-options options.bind="inputOptions" on-input.bind="onInput"></input-options> </template> </step-wrapper> </template> '},"support-assistant/support-assistant":(t,e,s)=>{s.r(e),s.d(e,{SupportAssistant:()=>g});var a=s(15215),r=s("aurelia-framework"),o=s(24523),i=s(48335),n=s(62914),p=s("services/bugsnag/index"),l=s(83854),u=s(92004),c=s(39402),d=s(42806),m=s(77502);let g=class{#t;constructor(t){this.onClose=()=>{},this.isMinimized=!1,this.contentMaxHeight=0,this.resizePlaceholder=()=>{},this.currentFlowName="start",this.stepHistory=[],this.handleStartInput=t=>{switch(this.currentFlowName=t,t){case"mods-not-working":this.currentFlow=new c.X({config:{trainerInfo:this.trainerInfo,titleInfo:this.titleInfo,debugInfo:this.debugInfo,gameTranslations:this.gameTranslations}});break;case"overlay-not-working":this.currentFlow=new d.B({config:{titleInfo:this.titleInfo,debugInfo:this.debugInfo}});break;case"hotkeys-not-working":this.currentFlow=new u.D({config:{titleInfo:this.titleInfo,debugInfo:this.debugInfo}});break;case"game-crashing":this.currentFlow=new l.c({config:{titleInfo:this.titleInfo,debugInfo:this.debugInfo,canUseOverlay:this.canUseOverlay}});break;case"general-feedback":return this.currentFlowName="general-feedback",void this.#e({step:m.g.GeneralFeedback,metadata:{featurebaseJwt:this.account.featurebaseJwt}});case"account-issue":return this.currentFlowName="account-issue",void this.#e({step:m.g.AccountIssue,metadata:{}});default:throw new Error(`Support Assistant: No flow for ${t}`)}this.#t.event("support_assistant_step_interaction",{flow_name:"start",step_name:this.currentStep.step.key,value:t,title_id:this.titleInfo?.id||void 0,trainer_id:this.trainerInfo?.id},n.Io),this.#s(null)},this.handleInput=(t,e)=>{if("close"!==t)if("restart"!==t)if("start"===this.currentFlowName)this.handleStartInput(t);else{const s=this.currentStep.step.key,a=this.currentFlowName;this.#t.event("support_assistant_step_interaction",{flow_name:a,step_name:s,value:e||t,title_id:this.titleInfo?.id||void 0,trainer_id:this.trainerInfo?.id},n.Io),this.#s(t)}else this.#a();else this.onClose()},this.handleCloseClick=()=>{this.onClose(),this.focusTrap.release(),this.#t.event("support_assistant_closed",{flow_name:this.currentFlowName,step_name:this.currentStep.step.key,title_id:this.titleInfo?.id||void 0,trainer_id:this.trainerInfo?.id},n.Io)},this.handleMinimizeClick=()=>{this.isMinimized=!this.isMinimized,this.isMinimized?(this.focusTrap.pause(),this.#t.event("support_assistant_minimized",{flow_name:this.currentFlowName,step_name:this.currentStep.step.key,title_id:this.titleInfo?.id||void 0,trainer_id:this.trainerInfo?.id},n.Io)):this.focusTrap.resume()},this.#a=()=>{const t=this.currentFlowName;this.#e(this.#r(!0)),this.currentFlowName="start",this.currentFlow=null,this.#t.event("support_assistant_restarted",{previous_flow:t,title_id:this.titleInfo?.id||void 0,trainer_id:this.trainerInfo?.id},n.Io)},this.#r=t=>({step:m.g.Start,metadata:{username:this.account.username,isRestart:!!t,canUseOverlay:this.canUseOverlay}}),this.#s=t=>{const e=this.currentFlow?.goToNextStep(t);e?this.#e(e):(this.#e({step:m.g.FlowError}),(0,p.report)(new Error(`Support Assistant: No next step for ${this.currentStep.step.key} step with value ${t} in flow ${this.currentFlowName}`)))},this.#e=t=>{this.stepHistory.push(t),this.#t.event("support_assistant_step_view",{flow_name:this.currentFlowName,step_name:t.step.key,title_id:this.titleInfo?.id||void 0,trainer_id:this.trainerInfo?.id},n.Io)},this.#t=t}bind(){this.#e(this.#r())}attached(){this.maintainContentHeight(),this.focusTrap=new o.s(this.focusTrapEl,{returnTarget:this.triggerEl,onEscape:()=>{this.handleCloseClick()}}),this.focusTrap.capture()}detached(){this.contentElResizeObserver.disconnect(),this.currentFlow=null,this.currentFlowName="start",this.stepHistory=[],this.contentMaxHeight=0,this.resizePlaceholder=()=>{},this.focusTrap.release()}get currentStep(){return this.stepHistory[this.stepHistory.length-1]}get debugInfo(){return{envInfo:this.envInfo,trainerId:this.trainerInfo?.id,titleName:this.titleInfo?.name,gamePlatform:this.gameInfo?.platformId,preferredInstallation:this.preferredInstallation,correlationId:this.gameInfo?.correlationIds[this.gameInfo.correlationIds.length-1]}}getStepModel(t){return{metadata:t.metadata,onInput:this.handleInput,debugInfo:this.debugInfo,currentFlow:this.currentFlowName}}contentMaxHeightChanged(){this.resizePlaceholder?.()}maintainContentHeight(){this.contentMaxHeight=this.scrollContainerEl?.clientHeight||0,this.heightPlaceholderEl?.style.setProperty("height",`${this.contentMaxHeight||0}px`),this.resizePlaceholder=(0,i.s)((()=>{this.heightPlaceholderEl?.style.setProperty("height",`${this.contentMaxHeight}px`)}),150),this.contentElResizeObserver=new ResizeObserver((t=>{for(const e of t)e.target.scrollHeight>this.contentMaxHeight&&!this.isMinimized&&(this.contentMaxHeight=e.target.scrollHeight)})),this.contentElResizeObserver.observe(this.contentEl)}#a;#r;#s;#e};(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],g.prototype,"trainerInfo",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],g.prototype,"titleInfo",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],g.prototype,"gameInfo",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],g.prototype,"preferredInstallation",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],g.prototype,"account",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],g.prototype,"envInfo",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],g.prototype,"canUseOverlay",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],g.prototype,"gameTranslations",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Function)],g.prototype,"onClose",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",HTMLElement)],g.prototype,"triggerEl",void 0),(0,a.Cg)([r.observable,(0,a.Sn)("design:type",Number)],g.prototype,"contentMaxHeight",void 0),(0,a.Cg)([(0,r.computedFrom)("envInfo","trainerInfo","titleInfo","gameInfo"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],g.prototype,"debugInfo",null),g=(0,a.Cg)([(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[n.j0])],g)},"support-assistant/support-assistant.html":(t,e,s)=>{s.r(e),s.d(e,{default:()=>i});var a=s(14385),r=s.n(a),o=new URL(s(18285),s.b);const i='<template> <require from="./support-assistant.scss"></require> <section class="support-assistant ${isMinimized ? \'--minimized\' : \'\'}" ref="focusTrapEl"> <div class="support-assistant-top-bar"> <i>bug_report</i> <span class="support-assistant-top-bar-label"><span class="support-assistant-top-bar-label-text">${\'support_assistant.report_an_issue\' | i18n}</span> <span class="beta-badge">${\'support_assistant.beta\' | i18n}</span> </span> <div class="support-assistant-top-bar-actions"> <button type="button" click.trigger="handleMinimizeClick()" class="${isMinimized ? \'--minimized\' : \'\'}" title="${(isMinimized ? \'support_assistant.restore\' : \'support_assistant.minimize\') | i18n}"> <i>${isMinimized ? \'expand_less\' : \'remove\'}</i> </button> <button show.bind="!isMinimized" type="button" click.trigger="handleCloseClick()" title="${\'support_assistant.close\' | i18n}"> <i>close</i> </button> </div> </div> <div ref="scrollContainerEl" class="support-assistant-scroll-container"> <div class="support-assistant-scroll-container-inner"> <div ref="heightPlaceholderEl" class="support-assistant-content-height-placeholder"></div> <div ref="contentEl" class="support-assistant-content" aria-live="polite" role="log" aria-label="${\'support_assistant.title\' | i18n}"> <div class="support-assistant-header"> <span class="support-assistant-header-logo"><img src="'+r()(o)+'" alt="${\'support_assistant.wemod_logo\' | i18n}"></span> <h2 class="support-assistant-header-title">${\'support_assistant.title\' | i18n}</h2> <span class="support-assistant-header-subtitle">${\'support_assistant.subtitle\' | i18n}</span> </div> <compose repeat.for="step of stepHistory" view-model="./steps/${step.step.component}" model.bind="getStepModel(step)" class="support-assistant-step"></compose> </div> </div> </div> </section> </template> '},"support-assistant/support-assistant.scss":(t,e,s)=>{s.r(e),s.d(e,{default:()=>d});var a=s(31601),r=s.n(a),o=s(76314),i=s.n(o),n=s(4417),p=s.n(n),l=new URL(s(83959),s.b),u=i()(r()),c=p()(l);u.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,.support-assistant-top-bar i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.support-assistant{--top-bar-height: 50px;position:absolute;bottom:0;right:0;width:400px;height:75vh;max-height:720px;border-radius:16px 16px 0px 0px;overflow:hidden;border:.5px solid rgba(255,255,255,.15);background:#131417;box-shadow:0px 20px 30px 0px rgba(0,0,0,.35);transform:translateY(30%);opacity:0;animation:support-assistant-open-animation 400ms cubic-bezier(0.39, 0.02, 0, 1.03) forwards;transition:width 200ms ease-in-out,height 200ms ease-in-out}.support-assistant.--minimized{transform:none;opacity:1;animation:support-assistant-minimize-animation 500ms cubic-bezier(0.29, 0.09, 0.47, 1) forwards}.support-assistant.--minimized .support-assistant-scroll-container{opacity:0}@keyframes support-assistant-open-animation{100%{transform:none;opacity:1;width:400px;height:75vh}}@keyframes support-assistant-minimize-animation{100%{width:276px;height:56px}}.support-assistant-top-bar{height:var(--top-bar-height);display:flex;align-items:center;padding:8px 8px 8px 16px;border-bottom:.5px solid rgba(255,255,255,.15);color:rgba(255,255,255,.8);font-size:14px;font-style:normal;font-weight:700;line-height:24px}.support-assistant-top-bar i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px}.support-assistant-top-bar-label{flex-grow:1;min-width:0;margin:0 0 0 14px;display:flex;align-items:center;gap:8px}.support-assistant-top-bar-label-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.support-assistant-top-bar-label .beta-badge{background:#6046ff linear-gradient(rgba(0, 0, 0, 0.2), rgba(0, 0, 0, 0.2)) !important;flex-shrink:0;color:#fff;font-weight:700;line-height:16px;letter-spacing:.5px;font-size:10px;padding:0px 4px;border-radius:4px;text-transform:uppercase}.support-assistant-top-bar-actions{display:flex;align-items:center;flex-grow:0}.support-assistant-top-bar-actions button{padding:8px 10px;border:none;border-radius:4px;color:rgba(255,255,255,.6);background-color:rgba(255,255,255,0);transition:background-color 75ms ease-in-out;line-height:0}.support-assistant-top-bar-actions button:hover{background-color:rgba(255,255,255,.1)}.support-assistant-top-bar-actions button i{font-size:17px}.support-assistant-top-bar-actions button.--minimized i{font-size:20px}.support-assistant-scroll-container{width:100%;height:calc(100% - var(--top-bar-height));overflow-y:auto;overflow-x:hidden;display:flex;flex-direction:column-reverse;transition:opacity 100ms ease-in}.support-assistant-scroll-container::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.support-assistant-scroll-container::-webkit-scrollbar-thumb:window-inactive,.support-assistant-scroll-container::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.support-assistant-scroll-container::-webkit-scrollbar-thumb:window-inactive:hover,.support-assistant-scroll-container::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.support-assistant-scroll-container-inner{display:flex;flex-direction:row;align-items:stretch;width:100%}.support-assistant-content-height-placeholder{width:1px}.support-assistant-content{padding:40px 16px 100px;min-height:100%;max-width:100%;flex-grow:1}.support-assistant-header{display:flex;flex-direction:column;align-items:center;text-align:center;padding:20px}.support-assistant-header-logo{display:flex;width:64px;height:64px;margin:0 0 8px;justify-content:center;align-items:center;border-radius:133.333px;border:.5px solid rgba(255,255,255,.15);background:#202329}.support-assistant-header-logo img{width:30px}.support-assistant-header-title{color:#fff;font-size:14px;font-weight:700;line-height:20px;letter-spacing:-0.5px;margin:0}.support-assistant-header-subtitle{color:rgba(255,255,255,.8);font-size:12px;font-weight:500;line-height:16px}`,""]);const d=u}}]);
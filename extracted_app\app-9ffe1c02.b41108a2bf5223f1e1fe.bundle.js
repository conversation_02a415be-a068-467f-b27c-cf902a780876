"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[5157],{5177:(e,t,i)=>{i.d(t,{G:()=>n});var s=i(16953),a=i(92465);class n{constructor(){this.#e=null,this.#t=null,this.#i=null}#e;#t;#i;attached(){this.#e=document.createElement("iframe"),this.#e.style.display="none",this.#i=(0,a.Ix)((()=>this.#s()),12e4),this.#t=()=>this.#s(),this.#e.addEventListener("load",this.#t),this.#e.src=`${s.A.websiteUrl}/webview/preload`,document.body.appendChild(this.#e)}detached(){this.#s()}#s(){this.#e&&this.#t&&this.#e.removeEventListener("load",this.#t),this.#t=null,this.#e?.remove(),this.#e=null,this.#i?.dispose()}}},19648:(e,t,i)=>{i.d(t,{Z:()=>f});var s=i(15215),a=i(79896),n=i(16928),o=i("aurelia-event-aggregator"),r=i("aurelia-framework"),h=i(96610),d=i(19072),l=i("services/bugsnag/index"),c=i(54995),u=i(48335),w=i(26700),m=i(62679),p=i(62914);const g=(0,h.getLogger)("video-library"),v="_thumbnail.webp",y=[".mp4",".mkv"];function b(e){return y.some((t=>t.toLowerCase()===e.toLowerCase()))}let f=class{#a;#n;#o;#r;#h;#d;constructor(e,t,i){this.#a=null,this.#h=null,this.#r=e,this.#o=t,this.#d=i,this.#n=(0,u.s)((()=>{this.#o.publish("EVENT_VIDEOS_CHANGED")}),500)}attached(){this.#l()}detached(){this.#c()}enableCaptureChanged(e,t){!1===e&&!0===t?this.#c():!0===e&&!1===t&&this.#l()}async#l(){try{if(!this.enableCapture)return;const e=await this.#u();this.#c();const t=a.watch(e,{recursive:!0},this.#w.bind(this));this.#a=t,g.info("Started watching WeMod videos directory")}catch(e){this.#m(e)}}#c(){this.#a?.close(),this.#a=null,g.info("Stopped watching WeMod videos directory")}#w(e,t){t&&!t.includes(v)&&t&&b(n.extname(t))&&(g.info(`Video directory change detected: ${e} - ${t}`),this.#n(),this.synchronizeThumbnails(t))}onVideosChanged(e){return this.#o.subscribe("EVENT_VIDEOS_CHANGED",e)}async showVideoFileLocation(e){try{this.#d.event("show_video_location",{},p.Io),await this.#r.showFileLocation(await this.#p(e))}catch(e){this.#m(e)}}async openCaptureFolder(e,t){const i=await this.#u(t);try{await a.promises.mkdir(i,{recursive:!0}),this.#d.event("capture_folder_open",{trigger:e},p.Io),await this.#r.openFilePath(i)}catch(e){this.#m(e)}}async getAllVideos(){try{const e=await this.#u(),t=(await a.promises.readdir(e,{withFileTypes:!0})).filter((e=>e.isDirectory()&&e.name!==m.og)),i=[];for(const s of t){const t=n.join(e,s.name),o=await a.promises.readdir(t,{withFileTypes:!0}),r=[];let h=0;for(const i of o){h++;try{if(i.isFile()&&b(n.extname(i.name))){const o=n.join(t,i.name),d=n.relative(e,o).replace(/\\/g,"/"),l=await a.promises.stat(o),c=this.getVideoUuid(s.name,l,h),u=await this.getThumbnailPath(s.name,i.name);let w="";try{w=`data:image/webp;base64,${(await a.promises.readFile(u)).toString("base64")}`}catch(e){}const m={id:c,path:d,filename:i.name,createdAt:l.birthtime,thumbnailDataUrl:w,titleName:s.name};r.push(m)}}catch(e){g.error(`Error processing video file ${i.name}: ${e}`)}}i.push({titleName:s.name,folderPath:t,videos:r})}return i}catch(e){return this.#m(e),[]}}async synchronizeThumbnails(e){try{const t=await this.#u(),i=await this.#u(m.og);if(e)return this.#g(e,t);const s=new Set,a=await this.#v(t,s);await this.#y(a);const n=await this.#b(i,s);return g.info(`Thumbnail synchronization complete: ${a.length} generated, ${n} deleted`),a.length>0||n>0}catch(e){return!1}}async#g(e,t){const i=n.normalize(e).split(n.sep);if(2!==i.length)return!1;const[s,o]=i,r=await this.getThumbnailPath(s,o),h=n.join(t,s,o);return await(0,w.h)(h)?!await(0,w.h)(r)&&(await(0,m.I6)(this.getVideosDirectory(),h),!0):!!await(0,w.h)(r)&&(await a.promises.unlink(r),g.info(`Deleted orphaned thumbnail for missing video: ${r}`),!0)}async#v(e,t){const i=(await a.promises.readdir(e,{withFileTypes:!0})).filter((e=>e.isDirectory()&&e.name!==m.og)),s=[];for(const o of i){const i=n.join(e,o.name),r=await a.promises.readdir(i,{withFileTypes:!0});for(const e of r)if(e.isFile()&&b(n.extname(e.name))){const r=e.name,h=`${o.name}_${r}`;t.add(h);const d=await this.getThumbnailPath(o.name,r);try{await a.promises.access(d,a.constants.F_OK)}catch(e){const t=n.join(i,r);s.push((0,m.I6)(this.getVideosDirectory(),t))}}}return s}async#y(e){for(let t=0;t<e.length;t+=3){const i=e.slice(t,t+3);await Promise.all(i)}}async#b(e,t){const i=await a.promises.readdir(e),s=[];for(const o of i)if(o.endsWith(v)){const i=o.substring(0,o.length-15);t.has(i)||s.push(a.promises.unlink(n.join(e,o)).catch((e=>g.error(`Failed to delete orphaned thumbnail: ${e}`))))}return await Promise.all(s),s.length}getVideosDirectory(){return this.#h||(this.#h=n.join(this.#r.info.osHomeDir,"Videos","WeMod")),this.#h}getVideoUuid(e,t,i){return`${e}_${t.birthtimeMs.toString().replaceAll(".","")}${i?`_${i}`:""}`}async getThumbnailPath(e,t){const i=await this.#u(m.og),s=`${e}_${t}`;return n.join(i,`${s}${v}`)}renameVideoFile(e,t){const i=t.trim();if(!t||""===i)return{success:!1,errorMessageKey:"my_videos.invalid_filename"};try{const t=this.getVideosDirectory(),s=n.join(t,e.path),o=n.dirname(s),r=n.extname(e.filename),h=i.endsWith(r)?i:`${i}${r}`,d=n.join(o,h);if(h===e.filename)return{success:!0};if(a.existsSync(d))return{success:!1,errorMessageKey:"my_videos.file_already_exists"};a.renameSync(s,d);const l={...e,filename:h,path:n.normalize(n.relative(t,d))};return this.#n(),{success:!0,updatedVideo:l}}catch(e){return g.error(`Failed to rename video: ${e}`),{success:!1,errorMessageKey:"my_videos.file_rename_failed"}}}async#u(e){return await(0,w.B)(this.getVideosDirectory(),e)}async#p(e){return n.join(this.getVideosDirectory(),e.path)}#m(e){(0,l.report)(e),g.error(e.toString())}dispose(){this.#c()}};(0,s.Cg)([r.observable,(0,s.Sn)("design:type",Object)],f.prototype,"enableCapture",void 0),f=(0,s.Cg)([(0,c.m6)({setup:"attached",teardown:"detached",selectors:{enableCapture:(0,c.$t)((e=>e.settings.enableCapture))}}),(0,r.autoinject)(),(0,s.Sn)("design:paramtypes",[d.s,o.EventAggregator,p.j0])],f)},71006:(e,t,i)=>{i.d(t,{T:()=>y});var s=i(15215),a=i("aurelia-event-aggregator"),n=i("aurelia-framework"),o=i(68663),r=i("dialogs/webview-dialog"),h=i(16953),d=i(21795),l=i(19072),c=i(20057),u=i(92694),w=i(54995),m=i(29944),p=i(38110),g=i(38777),v=i(62914);let y=class{#r;#f;#E;#o;#C;#_;#V;constructor(e,t,i,s,a){this.#V=new Map,this.#r=e,this.#f=t,this.#E=i,this.#o=s,this.#C=a}attached(){this.#_=new g.Vd([this.#r.onWebviewWindowClosed(this.#W.bind(this)),this.#r.onWebviewWindowLoadError(this.#D.bind(this))])}detached(){this.#_?.dispose(),this.#_=null}#D(e){this.#V.get(e)?.handleLoadError("window_load"),this.#V.delete(e)}#W(e){const t=this.#V.get(e);t&&(this.#V.delete(e),t.handleClosed())}makeFrameName(e,t=!1){return`webview:${e}${t?":debug":""}`}openWindow(e,t,i,s,a,n=!1){const o=this.makeFrameName(e,n);let h=this.#V.get(o);h&&n&&(h.dispose(),h=void 0);const d=h??new b(o,t,s,a,this.#f.createWebview({mode:"window",settings:(0,r.getWebviewSupportedSettings)(this.settings),locale:this.#E.getEffectiveLocale().baseName,route:t,params:i}),this.#o,this.#r,this.#C);return this.#V.set(o,d),d.load().then((()=>this.#r.showWebviewWindow(d.frameName))),d}};y=(0,s.Cg)([(0,n.autoinject)(),(0,w.m6)({setup:"attached",teardown:"detached",selectors:{settings:(0,w.$t)((e=>e.settings))}}),(0,s.Sn)("design:paramtypes",[l.s,o.x,c.F2,a.EventAggregator,u.k])],y);class b{#$;#k;#T;#P;#j;#F;#o;#r;#x;constructor(e,t,i,s,n,o,r,h){this.frameName=e,this.route=t,this.defaultEventParams=i,this.displayOptions=s,this.#P=null,this.#o=new a.EventAggregator,this.#x=o,this.#r=r;const d=Date.now();this.#F=new Promise(((e,t)=>{this.#k=e,this.#T=t,n.then((e=>this.#S(e))).then((()=>this.#A("webview_window_load",{}))).catch((e=>this.handleLoadError("webview_request_error",e)))})).finally((()=>{const e=Date.now()-d;h.collectImmediately({name:"webview_window_loadtime",value:e,tags:[{key:"client_version",value:"$clientVersion"},{key:"webview_route",value:this.route}]}),this.#k=null,this.#T=null}))}#S(e){const t=(0,p.UU)("--theme--background-accent")?.trim(),i={titleColor:t,...this.displayOptions},s=Object.entries(i).map((([e,t])=>`${e}=${t}`)).join(",");this.#P=(0,g.Ix)((()=>this.handleLoadError("timeout")),45e3),this.#j=window.open(e.url,this.frameName,s),this.#j&&(this.#$=new m.Jx(this.#j,e.origin),this.initializeRpcHandlers())}load(){return this.#F}async show(){this.#A("webview_window_show",{}),await this.#r.showWebviewWindow(this.frameName),this.#P&&(this.#P.dispose(),this.#P=null),this.#k?.()}initializeRpcHandlers(){this.#$?.setHandler("router_event",(e=>("router:navigation:error"!==e.event&&"router:navigation:canceled"!==e.event||this.handleLoadError("router_error"),"router:navigation:success"===e.event&&this.show(),!0))),this.#$?.setHandler("open_uri",(e=>this.#N(e))),this.#$?.setHandler("event",(e=>"string"==typeof e.name&&"object"==typeof e.data&&null!==e.data&&(this.#A(e.name,e.data),!0))),this.#o.publish("rpc-ready",this.#$)}handleLoadError(e,t){this.#T&&(this.#A("webview_window_load_error",{code:e}),this.#T(t??new Error(`Failed to load webview window: ${e}`))),this.dispose()}handleClosed(){this.#A("webview_window_close",{}),this.#o.publish("close"),this.dispose()}dispose(){this.#$&&(this.#$.dispose(),this.#$=null),h.A.debug||!this.#j||this.#j.closed||(this.#j.close(),this.#j=null)}#N(e){return window.open(e.uri,"_blank"),!0}async execute(e,t){return await(this.#$?.execute(e,t))}async executeIgnoreUnhandled(e,t){try{return await(this.#$?.execute(e,t))}catch(e){if(e instanceof m.v)return!1;throw e}}#A(e,t){this.#x.publish(new d.WC(e,this.#L(t),v.Io))}#L(e){return{...this.defaultEventParams,route:this.route,...e}}onRpcReady(e){return this.#o.subscribe("rpc-ready",e)}onClose(e){return this.#o.subscribe("close",e)}onShow(e){return this.#o.subscribe("show",e)}}}}]);
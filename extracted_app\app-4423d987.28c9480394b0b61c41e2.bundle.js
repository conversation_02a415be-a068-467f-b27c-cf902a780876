"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[8018],{"cheats/resources/elements/trainer-mods-list":(t,e,i)=>{i.r(e),i.d(e,{CHEAT_INSTRUCTIONS_READ_THRESHOLD_MS:()=>k,TrainerModsList:()=>S});var a=i(15215),o=i(62914),n=i("aurelia-event-aggregator"),r=i("aurelia-framework"),s=i(20770),l=i(56188),d=i(70236),p=i(68663),c=i(72208),h=i(56705),u=i(43570),g=i(96111),b=i(7892),m=i(67064),f=i(3972),y=i(20057),v=i(54995),w=i(48881),x=i(38777),I=i(83802);const k=500;let S=class{#t;#e;#i;#a;#o;#n;#r;#s;#l;#d;#p;#c;#h;#u;#g;#b;#m;#f;constructor(t,e,i,a,o,r,s,l,d,p,c,h,u){this.ea=new n.EventAggregator,this.enabled=!1,this.variables={},this.pinnedMods=null,this.previousPinnedModsList=[],this.#b=null,this.#m=null,this.#f=!1,this.#t=t,this.#e=e,this.#i=i,this.#a=a,this.#o=o,this.#n=r,this.#r=s,this.#s=l,this.timeLimitEnforcer=d,this.ea=p,this.#l=c,this.#d=h,this.#p=u}attached(){this.#y().then((()=>{this.#v()})).catch((()=>{})),this.#c=new x.Vd([this.#e.onNewTrainer((t=>this.#w(t))),this.#e.onTrainerEnded((()=>this.#x()))]),this.#e.trainer&&this.#w(this.#e.trainer),this.#a.onValueSaved((t=>{t.gameId===this.trainer.gameId&&this.variables[t.target]!==t.value&&(this.variables[t.target]=t.value)})),this.#c.push(this.ea.subscribe("precisionModsExpanded",(({cheatUuid:t})=>this.precisionModsExpanded(t)))),this.#c.push(this.ea.subscribe("handleCheatButtonChange",(({cheat:t})=>this.handleButtonClick(t)))),this.#c.push(this.ea.subscribe("handleCheatNumericChange",(({cheat:t,value:e})=>this.handleNumericChange(t,e)))),this.#c.push(this.ea.subscribe("handleCheatSelectionChange",(({cheat:t,value:e})=>this.handleSelectionChange(t,e)))),this.#c.push(this.ea.subscribe("handleCheatScalarChange",(({cheat:t,value:e})=>this.handleScalarChange(t,e)))),this.#c.push(this.ea.subscribe("handleCheatIncrementalChange",(({cheat:t,value:e})=>this.handleIncrementalChange(t,e)))),this.#c.push(this.ea.subscribe("handleCheatToggleChange",(({cheat:t,value:e,isTimer:i})=>this.handleToggleChange(t,!!e,i)))),this.#c.push(this.#o.onCheatStatesChanged((t=>{t.gameId!==this.trainer?.gameId||"pinnedMods"!==t?.dep||this.disableAutoPins||this.#v()})))}detached(){this.#c.dispose(),this.#b?.dispose(),this.enabled=!1,this.#h=null}bind(){this.#I()}trainerChanged(t,e){const i=e?.blueprint?.cheats?.some((t=>"pinned"===t.category));i&&!t?.blueprint?.cheats?.some((t=>"pinned"===t.category))&&this.#v()}precisionModsExpanded(t){this.#n.dispatch(w.GO,t)}gamePreferencesChanged(t,e){const i=!!t?.[this.trainer.gameId]?.saveCheats?.enabled;!!e?.[this.trainer.gameId]?.saveCheats?.enabled===i||this.#k()||this.#I()}get inputsDisabled(){return this.trainer?Object.fromEntries(this.trainer.blueprint.cheats.map((t=>[t.uuid,this.canUseInAppControls||this.canUseSaveCheats?!(this.enabled||this.#a.cheatSupportsActivateOnLoad(t)&&this.saveCheatsEnabled):!this.enabled]))):{}}get isPro(){return"object"==typeof this.account.subscription&&null!==this.account.subscription}get isCreatorOrTester(){return(0,d.br)(this.account?.flags,[64,16384])}get canUseSaveCheats(){return this.#u=this.isPro,this.#u}get canUseInAppControls(){return this.#g=this.timeLimitEnforcer?.canUseInAppControls||!1,this.#g}get saveCheatsEnabled(){return this.#a.enabledForGame(this.trainer.gameId)}#k(){return this.#h?.isActive()??!1}#w(t){const e=t.getMetadata(I.vO);e&&e.info.id===this.trainer.id&&(this.#I(),t.isActive()?this.#S(t):this.#c.push(t.onActivated((()=>{this.#S(t)}))),this.#c.push(t.onEnded((()=>{this.enabled=!1,this.#h=null,this.#I()}))))}#x(){this.#n.dispatch(w.kB)}get translations(){const t=this.#p.getEffectiveLocale().baseName,e=this.gameTranslations?.[this.trainer?.gameId]?.[t];return{locale:t,strings:e??{}}}#I(){this.saveCheatsEnabled&&this.canUseSaveCheats&&!this.#k()?(this.#C(),this.#_()):this.#C()}#C(){this.variables={};const t=new Set(this.trainer.blueprint.cheats.filter((t=>"selection"===t.type)).map((t=>(this.variables[t.target]=-1,t.target))));this.trainer.blueprint.cheats.filter((e=>!t.has(e.target))).forEach((t=>{this.#T(t)}))}#T(t){switch(t.type){case"number":case"slider":this.variables[t.target]=Math.min(t.args?.max??1/0,Math.max(0,t.args?.min??0));break;case"scalar":this.variables[t.target]=Number(t.args?.default??1);break;default:this.variables[t.target]=0}}#_(){if(this.saveCheatsEnabled){const t=this.gamePreferences[this.trainer.gameId]?.saveCheats;t&&this.trainer.blueprint.cheats.forEach((e=>{if(!(this.trainer.brokenCheatUuids??[]).includes(e.uuid)&&this.#a.cheatSupportsActivateOnLoad(e)){const i=t.trainerState[e.target];void 0!==i&&(this.variables[e.target]=i)}}))}}#S(t){this.#c.push(t.onValueSet((t=>{this.variables[t.name]=t.value}))),t.values.forEach(((t,e)=>{this.variables[e]=t})),this.enabled=!0,this.#h=t}get canUsePinnedMods(){return this.isPro}get pinnedModsList(){if(null===this.pinnedMods?.[this.trainer?.gameId]&&!this.disableAutoPins)return this.previousPinnedModsList;const t=this.pinnedMods?.[this.trainer?.gameId]?.map((t=>t.uuid))||[];return this.previousPinnedModsList=t,t}isModPinned(t){return this.pinnedMods?.[this.trainer.gameId]?.some((e=>e?.uuid===t?.uuid))||!1}async handlePinClick(t,e){const i=this.trainer?.pinnedMods||[];if(e)await this.#s.updatePinnedMods(this.trainer.gameId,i),await this.#n.dispatch(w.oz,this.trainer.gameId,this.trainer.blueprint.cheats.filter((t=>i.includes(t.uuid)&&"pinned"!==t.category))),this.#l.event("reset_pinned_mods_click",{source:"desktop",gameId:this.trainer.gameId,trainerId:this.trainer.id},o.Io);else{let e=this.pinnedMods?.[this.trainer.gameId]||[];this.canUsePinnedMods||this.hasClickedModPin||this.#n.dispatch(w.NX,"hasClickedModPin",!0),this.isModPinned(t)?(this.canUsePinnedMods||this.disableAutoPins||1!==e.length||this.#d.toast({content:"trainer_cheats_list.auto_pin_setting_toast",persist:!1}),e=e.filter((e=>e.uuid!==t.uuid)),this.#l.event("unpin_mod_click",{source:"desktop",gameId:this.trainer.gameId,trainerId:this.trainer.id,modId:t.uuid},o.Io)):this.canUsePinnedMods&&(e.push(t),this.#l.event("pin_mod_click",{source:"desktop",gameId:this.trainer.gameId,trainerId:this.trainer.id,modId:t.uuid},o.Io)),await this.#n.dispatch(w.oz,this.trainer.gameId,e),this.#P((()=>this.#s.updatePinnedMods(this.trainer.gameId,e.map((t=>t.uuid)))))}this.#v()}#P(t){this.#b&&this.#b.dispose(),this.#m=t,this.#b=(0,x.Ix)((async()=>{if(!this.#f&&this.#m)try{this.#f=!0,await this.#m()}catch(t){}finally{this.#f=!1,this.#m=null}}),1e3)}async#v(){this.disableAutoPins&&await this.#n.dispatch(w.oz,this.trainer.gameId,null);let t=this.pinnedMods?.[this.trainer.gameId];if(!t)if(this.canUsePinnedMods){const e=(await this.#s.getUserGamePreferences(this.trainer.gameId))?.pinnedMods||[];t=e.map((t=>this.trainer.blueprint.cheats.find((e=>e.uuid===t)))).filter((t=>!!t)),e?.length>0&&await this.#n.dispatch(w.oz,this.trainer.gameId,t)}else this.disableAutoPins||(t=this.trainer.blueprint.cheats.filter((t=>this.trainer.pinnedMods?.includes(t.uuid))),t?.length>0&&await this.#n.dispatch(w.oz,this.trainer.gameId,t));if(t){const t=(0,l.x)(this.trainer.blueprint.cheats,this.#o.cheatStates);this.trainer={...this.trainer,blueprint:{...this.trainer.blueprint,cheats:t}}}}async#y(){if(!this.canUsePinnedMods){const t=this.pinnedMods?.[this.trainer.gameId]?.some((t=>!this.trainer.pinnedMods?.includes(t.uuid)));t&&await this.#n.dispatch(w.oz,this.trainer.gameId,null)}}handleButtonClick(t){const e=this.#k();this.#g&&e?(this.#h?.setValue(t.target,t.args?.value??0,2,t.uuid),this.#t.play(u.A.Execute)):this.#u&&!e&&(this.#a.saveValue(this.trainer.gameId,t.target,t.args?.value??0),this.variables[t.target]=t.args?.value??0)}handleToggleChange(t,e,i){const a=this.#k();this.#g&&a?this.#h?.setValue(t.target,e,i?7:2,t.uuid):this.#u&&!a&&(this.#a.saveValue(this.trainer.gameId,t.target,e?1:0),this.variables[t.target]=e?1:0)}handleNumericChange(t,e){const i=this.#k();e>=(t.args?.min??0)&&e<=(t.args?.max??1/0)&&(this.#g&&i?this.#h?.setValue(t.target,e,2,t.uuid):this.#u&&!i&&(this.#a.saveValue(this.trainer.gameId,t.target,e),this.variables[t.target]=e))}handleSelectionChange(t,e){const i=this.#k();e>=0&&e<(t.args?.options.length??0)&&(this.#g&&i?this.#h?.setValue(t.target,e,2,t.uuid):this.#u&&!i&&(this.#a.saveValue(this.trainer.gameId,t.target,e),this.variables[t.target]=e))}handleScalarChange(t,e){const i=this.#k(),a=t.args?.options?.includes(e);a&&(this.#g&&i?this.#h?.setValue(t.target,e,2,t.uuid):this.#u&&!i&&(this.#a.saveValue(this.trainer.gameId,t.target,e),this.variables[t.target]=e))}handleIncrementalChange(t,e){const i=this.#k(),a=t.args?.options?.includes(e);a&&this.#g&&i&&this.#h?.setValue(t.target,e,2,t.uuid)}setCustomHotkey(){this.ea.subscribeOnce("setCustomHotkey",(({gameId:t,cheatUuid:e,hotkeyIndex:i,newHotkey:a})=>{this.#n.dispatch(w.jZ,t,e,i,a)}))}onHotkeyPress(){return this.#r.onHotkeyPress((t=>{this.ea.publish("customHotkeyPressed",t)}))}get cheatInstructionsRead(){const t={};return this.trainer&&this.trainer.blueprint.cheats.filter((t=>!!t.instructions)).forEach((e=>t[e.uuid]=this.#i.areInstructionsRead(e.uuid,e.instructions??""))),t}markCheatInstructionsRead(){this.ea.subscribeOnce("markCheatInstructionsRead",(t=>{this.#i.areInstructionsRead(t.uuid,t.instructions??"")||this.#i.markRead("desktop",this.trainer.gameId,t.uuid,t.instructions??"",t.description)}))}dismissModTimerMessage(t){this.#n.dispatch(w.Yt,t)}handleModTimer(t,e){const i=t.cancel?"mod_timer_cancel_click":"mod_timer_start_click",a={gameId:this.trainer.gameId,modId:t.modId,modType:t.modType,timestamp:new Date,duration:t.duration,type:t.type,start:"loop"===t.type?t?.duration:void 0,end:"loop"===t.type?t?.durationLoop:void 0,cancel:t.cancel};this.#n.dispatch(w.$Z,a),e&&this.#l.event(i,{duration:String(t.duration),durationLoop:String(t.durationLoop)||void 0,titleId:this.trainer.titleId,modId:t.modId,modType:t.modType,type:t.type,source:"desktop"},o.Io)}};(0,a.Cg)([r.bindable,r.observable,(0,a.Sn)("design:type",Object)],S.prototype,"trainer",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",String)],S.prototype,"trainerPlayButtonState",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],S.prototype,"betaModsEnabled",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],S.prototype,"gamePreferences",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],S.prototype,"ea",void 0),(0,a.Cg)([r.observable,(0,a.Sn)("design:type",Boolean)],S.prototype,"disableAutoPins",void 0),(0,a.Cg)([(0,r.computedFrom)("trainer.blueprint.cheats","saveCheatsEnabled","canUseSaveCheats","canUseInAppControls","enabled"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],S.prototype,"inputsDisabled",null),(0,a.Cg)([(0,r.computedFrom)("account.subscription"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"isPro",null),(0,a.Cg)([(0,r.computedFrom)("account.flags"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"isCreatorOrTester",null),(0,a.Cg)([(0,r.computedFrom)("isPro"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"canUseSaveCheats",null),(0,a.Cg)([(0,r.computedFrom)("timeLimitEnforcer.canUseInAppControls"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"canUseInAppControls",null),(0,a.Cg)([(0,r.computedFrom)("account.subscription","gamePreferences","enableSaveCheatsByDefault"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"saveCheatsEnabled",null),(0,a.Cg)([(0,r.computedFrom)("language","gameTranslations","trainer.gameId"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],S.prototype,"translations",null),(0,a.Cg)([(0,r.computedFrom)("isPro"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],S.prototype,"canUsePinnedMods",null),(0,a.Cg)([(0,r.computedFrom)("pinnedMods","trainer.gameId"),(0,a.Sn)("design:type",Array),(0,a.Sn)("design:paramtypes",[])],S.prototype,"pinnedModsList",null),(0,a.Cg)([(0,r.computedFrom)("trainer.blueprint","cheatBlueprintInstructionsRead"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],S.prototype,"cheatInstructionsRead",null),S=(0,a.Cg)([(0,v.m6)({selectors:{account:(0,v.$t)((t=>t.account)),cheatBlueprintInstructionsRead:(0,v.$t)((t=>t.cheatBlueprintInstructionsRead)),gamePreferences:(0,v.$t)((t=>t.gamePreferences)),language:(0,v.$t)((t=>t.settings.language)),enableSaveCheatsByDefault:(0,v.$t)((t=>t.settings?.enableSaveCheatsByDefault)),showModHotkeys:(0,v.$t)((t=>t.settings?.showModHotkeys)),gameTranslations:(0,v.$t)((t=>t.gameTranslations)),pinnedMods:(0,v.$t)((t=>t?.pinnedMods)),hasClickedModPin:(0,v.$t)((t=>t.flags?.hasClickedModPin)),disableAutoPins:(0,v.$t)((t=>t.settings?.disableAutoPins)),precisionModsSectionsViewed:(0,v.$t)((t=>t.precisionModsSectionsViewed)),modTimerMessagesDismissed:(0,v.$t)((t=>t.modTimerMessagesDismissed)),modTimers:(0,v.$t)((t=>t.modTimers))}}),(0,r.inject)(u.L,I.jR,c.u,h.Q,b.p,s.il,f.Mz,p.x,g.Y,n.EventAggregator,o.j0,m.l,y.F2),(0,a.Sn)("design:paramtypes",[u.L,I.jR,c.u,h.Q,b.p,s.il,f.Mz,p.x,g.Y,n.EventAggregator,o.j0,m.l,y.F2])],S)},"cheats/resources/elements/trainer-mods-list-placeholder.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template> <require from="./trainer-mods-list.scss"></require> <require from="./trainer-mods-list-placeholder.scss"></require> <div class="category collapse"> <div class="category-header"> <span class="content"> <span class="icon" style="--placeholder--index:0"> <i></i> </span> <span class="label">Category</span> </span> <span class="spacer" style="--placeholder--index:2"></span> </div> <div class="cheats"> <div class="cheats-wrapper"> <div repeat.for="cheat of [1,2,3,4,5,6,7,8,9]" class="cheat" css="--placeholder--index: ${$index}"> <div class="cheat-name"> <span class="label">Cheat Name</span> </div> <div class="input"> <div class="input-inner"> <div class="input-shrinkwrap"> <button class="button-input">Input Name</button> </div> </div> </div> <div class="hotkeys"> <trainer-hotkey></trainer-hotkey> </div> </div> </div> </div> </div> </template> '},"cheats/resources/elements/trainer-mods-list-placeholder.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>s});var a=i(31601),o=i.n(a),n=i(76314),r=i.n(n)()(o());r.push([t.id,"trainer-mods-list-placeholder .category-header .icon i{background:rgba(255,255,255,.02);animation:placeholder__el--fade 1s ease-in-out calc(-1.5s*var(--placeholder--index)) alternate infinite,placeholder__el--initial-fade .1s linear 0s normal both;border-radius:100px}trainer-mods-list-placeholder .category-header .icon i,trainer-mods-list-placeholder .category-header .icon i *{color:rgba(0,0,0,0) !important;border:0 !important}trainer-mods-list-placeholder .category-header .icon i *{visibility:hidden}trainer-mods-list-placeholder .category-header .spacer:before{background:rgba(255,255,255,.02);animation:placeholder__el--fade 1s ease-in-out calc(-1.5s*var(--placeholder--index)) alternate infinite,placeholder__el--initial-fade .1s linear 0s normal both}trainer-mods-list-placeholder .category-header .spacer:before,trainer-mods-list-placeholder .category-header .spacer:before *{color:rgba(0,0,0,0) !important;border:0 !important}trainer-mods-list-placeholder .category-header .spacer:before *{visibility:hidden}trainer-mods-list-placeholder .cheat,trainer-mods-list-placeholder .cheat *{pointer-events:none}trainer-mods-list-placeholder .cheat .cheat-name .label{background:rgba(255,255,255,.02);animation:placeholder__el--fade 1s ease-in-out calc(-1.5s*var(--placeholder--index)) alternate infinite,placeholder__el--initial-fade .1s linear 0s normal both;border-radius:2px;max-height:10px}trainer-mods-list-placeholder .cheat .cheat-name .label,trainer-mods-list-placeholder .cheat .cheat-name .label *{color:rgba(0,0,0,0) !important;border:0 !important}trainer-mods-list-placeholder .cheat .cheat-name .label *{visibility:hidden}trainer-mods-list-placeholder .cheat .input .input-shrinkwrap>*{background:rgba(255,255,255,.02);animation:placeholder__el--fade 1s ease-in-out calc(-1.5s*var(--placeholder--index)) alternate infinite,placeholder__el--initial-fade .1s linear 0s normal both}trainer-mods-list-placeholder .cheat .input .input-shrinkwrap>*,trainer-mods-list-placeholder .cheat .input .input-shrinkwrap>* *{color:rgba(0,0,0,0) !important;border:0 !important}trainer-mods-list-placeholder .cheat .input .input-shrinkwrap>* *{visibility:hidden}trainer-mods-list-placeholder .cheat trainer-hotkey{background:rgba(255,255,255,.02);animation:placeholder__el--fade 1s ease-in-out calc(-1.5s*var(--placeholder--index)) alternate infinite,placeholder__el--initial-fade .1s linear 0s normal both;display:inline-block;width:125px;height:26px}trainer-mods-list-placeholder .cheat trainer-hotkey,trainer-mods-list-placeholder .cheat trainer-hotkey *{color:rgba(0,0,0,0) !important;border:0 !important}trainer-mods-list-placeholder .cheat trainer-hotkey *{visibility:hidden}",""]);const s=r},"cheats/resources/elements/trainer-mods-list.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>a});const a='<template> <require from="shared/resources/elements/pro-badge"></require> <require from="shared/cheats/resources/elements/mods-list"></require> <require from="pro-promos/tooltips/pro-precision-mods-tooltip"></require> <mods-list data-tooltip-delegate-for="save-mods-tooltip-small-pro-promo pro-precision-mod-tooltip" trainer.bind="trainer" trainer-play-button-state.bind="trainerPlayButtonState" variables.bind="variables" beta-mods-enabled.bind="betaModsEnabled" show-inputs.bind="true" show-hotkeys.bind="showModHotkeys" is-pro.bind="isPro" is-creator-or-tester.bind="isCreatorOrTester" is-save-cheats-enabled.bind="saveCheatsEnabled" can-use-save-cheats.bind="canUseSaveCheats" enabled.bind="enabled" can-use-in-app-controls.bind="canUseInAppControls" cheats-read.bind="cheatInstructionsRead" cheat-instructions-read.call="markCheatInstructionsRead()" set-custom-hotkey.call="setCustomHotkey()" on-hotkey-press.call="onHotkeyPress()" handle-pin-mod.call="handlePinClick(cheat, reset)" pinned-mods-list.bind="pinnedModsList" has-clicked-mod-pin.bind="hasClickedModPin" mod-timer-messages-dismissed.bind="modTimerMessagesDismissed" inputs-disabled.bind="inputsDisabled" game-preferences.bind="gamePreferences" translations.bind="translations" language.bind="language" ea.bind="ea" precision-mods-sections-viewed.bind="precisionModsSectionsViewed" dismiss-mod-timer-message.call="dismissModTimerMessage(timerType)" handle-mod-timer.call="handleModTimer(data, firstCall)" mod-timers.bind="modTimers"> </mods-list> <wm-tooltip-small-promo if.bind="!canUseSaveCheats" tooltip-id="save-mods-tooltip-small-pro-promo" placement="top-start" use-delegate="true"> <span slot="title" class="save-mods-tooltip-pro-promo-title">${\'save_cheats_icon.unlock_save_mods\' | i18n} <pro-badge class="small pro-badge"></pro-badge></span> <span slot="cta-copy" class="save-mods-tooltip-pro-promo-cta" pro-cta="save_mods_tooltip_small_pro_promo">${\'save_cheats_icon.join_now\' | i18n} <i class="arrow-icon"></i></span> </wm-tooltip-small-promo> <pro-precision-mods-tooltip if.bind="!isPro" tooltip-id="pro-precision-mod-tooltip" use-delegate="true"></pro-precision-mods-tooltip> </template> '},"cheats/resources/elements/trainer-mods-list.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>h});var a=i(31601),o=i.n(a),n=i(76314),r=i.n(n),s=i(4417),l=i.n(s),d=new URL(i(83959),i.b),p=r()(o()),c=l()(d);p.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${c}) format("woff2")}.material-symbols-outlined,.save-mods-tooltip-pro-promo-cta i.arrow-icon{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}.theme-default tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}.theme-purple-pro tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}.theme-green-pro tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}.theme-orange-pro tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}.theme-pro tooltip.alert .tooltip .tooltip-content{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}tooltip.alert .tooltip [slot=content]{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}tooltip.alert .tooltip [slot=content],tooltip.alert .tooltip [slot=content] a{color:rgba(var(--color--alert--rgb), 0.9)}tooltip.alert .tooltip .tooltip-arrow{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(var(--color--alert--rgb), 0.12), rgba(var(--color--alert--rgb), 0.12)) !important}tooltip.info .tooltip .tooltip-content{max-width:300px;width:max-content}.custom-tooltip .tooltip [slot=content]{margin:0 !important;padding:0 !important;overflow:hidden;display:block !important}.tooltip{position:absolute;opacity:0;visibility:hidden;transition:visiblity 0s linear .15s,opacity .15s,transform .15s,margin .15s;z-index:99}.tooltip .tooltip-content{border:1px solid rgba(255,255,255,.05);border-radius:10px;position:relative;text-align:left;display:block}.theme-default .tooltip .tooltip-content{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro .tooltip .tooltip-content{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro .tooltip .tooltip-content{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro .tooltip .tooltip-content{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro .tooltip .tooltip-content{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.tooltip .tooltip-content>*+*{margin-left:0}.tooltip [slot=content]{border-radius:10px;border:1px solid rgba(255,255,255,.05);padding:11px 15px;text-align:left;display:inline-flex;align-items:center;border:0 !important;position:relative;z-index:1}.theme-default .tooltip [slot=content]{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro .tooltip [slot=content]{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro .tooltip [slot=content]{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro .tooltip [slot=content]{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro .tooltip [slot=content]{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.tooltip [slot=content]>*+*{margin-left:9px}.tooltip [slot=content],.tooltip [slot=content] a{font-size:14px;line-height:21px;line-height:19px;font-weight:500;color:rgba(255,255,255,.5)}.tooltip [slot=content] strong,.tooltip [slot=content] em{font-weight:700;color:#fff;font-style:normal}.tooltip [slot=content] p{margin:0;padding:0}.tooltip [slot=content] p+p{margin-top:10px}.tooltip .tooltip-arrow{display:block;width:20px;height:20px;position:absolute;border-radius:2px;border:1px solid rgba(255,255,255,.05);transform:rotate(45deg)}.theme-default .tooltip .tooltip-arrow{background:#0d0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-purple-pro .tooltip .tooltip-arrow{background:#0e0e15 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-green-pro .tooltip .tooltip-arrow{background:#0b1114 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-orange-pro .tooltip .tooltip-arrow{background:#0f0f12 linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.theme-pro .tooltip .tooltip-arrow{background:#0a0a0a linear-gradient(rgba(255, 255, 255, 0.05), rgba(255, 255, 255, 0.05))}.tooltip.arrow-left-top{left:100%;top:calc(50% - 24px)}.tooltip.arrow-left-top .tooltip-arrow{left:-2px;top:10px}.tooltip.arrow-left-center{left:100%;top:50%}.tooltip.arrow-left-center .tooltip-arrow{left:-5px;top:50%;margin-top:-10px}.tooltip.arrow-right-center{right:100%;top:50%}.tooltip.arrow-right-center .tooltip-arrow{right:-5px;top:50%;margin-top:-10px}.tooltip.arrow-right-top{right:100%;top:calc(50% - 24px)}.tooltip.arrow-right-top .tooltip-arrow{right:-5px;top:10px}.tooltip.arrow-top-center{top:100%;left:50%}.tooltip.arrow-top-center .tooltip-arrow{left:50%;top:-5px;margin-left:-10px}.tooltip.arrow-bottom-center{bottom:100%;left:50%}.tooltip.arrow-bottom-center .tooltip-arrow{left:50%;bottom:-5px;margin-left:-10px}.tooltip.arrow-top-left{top:100%;left:50%;margin-left:-25px}.tooltip.arrow-top-left .tooltip-arrow{left:14px;top:-5px}.tooltip.arrow-bottom-left{bottom:100%;left:50%;margin-left:-25px}.tooltip.arrow-bottom-left .tooltip-arrow{left:14px;bottom:-5px}.tooltip.arrow-top-right{top:100%;right:50%;margin-right:-25px}.tooltip.arrow-top-right .tooltip-arrow{right:14px;top:-5px}.tooltip.arrow-bottom-right{bottom:100%;right:50%;margin-right:-25px}.tooltip.arrow-bottom-right .tooltip-arrow{right:14px;bottom:-5px}*:hover>.tooltip:not(.hidden),*:hover>tooltip .tooltip:not(.hidden),.tooltip.visible{opacity:1;visibility:visible;transition-delay:0s}*:hover>.tooltip:not(.hidden).arrow-left-top,*:hover>.tooltip:not(.hidden).arrow-left-center,*:hover>tooltip .tooltip:not(.hidden).arrow-left-top,*:hover>tooltip .tooltip:not(.hidden).arrow-left-center,.tooltip.visible.arrow-left-top,.tooltip.visible.arrow-left-center{margin-left:10px}*:hover>.tooltip:not(.hidden).arrow-right-top,*:hover>.tooltip:not(.hidden).arrow-right-center,*:hover>tooltip .tooltip:not(.hidden).arrow-right-top,*:hover>tooltip .tooltip:not(.hidden).arrow-right-center,.tooltip.visible.arrow-right-top,.tooltip.visible.arrow-right-center{margin-right:10px}*:hover>.tooltip:not(.hidden).arrow-top-center,*:hover>.tooltip:not(.hidden).arrow-top-left,*:hover>.tooltip:not(.hidden).arrow-top-right,*:hover>tooltip .tooltip:not(.hidden).arrow-top-center,*:hover>tooltip .tooltip:not(.hidden).arrow-top-left,*:hover>tooltip .tooltip:not(.hidden).arrow-top-right,.tooltip.visible.arrow-top-center,.tooltip.visible.arrow-top-left,.tooltip.visible.arrow-top-right{margin-top:10px}*:hover>.tooltip:not(.hidden).arrow-bottom-left,*:hover>.tooltip:not(.hidden).arrow-bottom-center,*:hover>.tooltip:not(.hidden).arrow-bottom-right,*:hover>tooltip .tooltip:not(.hidden).arrow-bottom-left,*:hover>tooltip .tooltip:not(.hidden).arrow-bottom-center,*:hover>tooltip .tooltip:not(.hidden).arrow-bottom-right,.tooltip.visible.arrow-bottom-left,.tooltip.visible.arrow-bottom-center,.tooltip.visible.arrow-bottom-right{margin-bottom:10px}.save-mods-tooltip-pro-promo-title .pro-badge{margin-left:2px}.save-mods-tooltip-pro-promo-cta i.arrow-icon{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:inherit;line-height:1;font-size:14px;vertical-align:middle}.save-mods-tooltip-pro-promo-cta i.arrow-icon:before{font-family:inherit;content:"arrow_forward"}`,""]);const h=p},"cheats/resources/elements/trainer-notify":(t,e,i)=>{i.r(e),i.d(e,{TrainerNotify:()=>d,TrainerNotifyReason:()=>a});var a,o=i(15215),n=i("aurelia-framework"),r=i(50654),s=i(54995),l=i(70236);!function(t){t.UpdatePending="update_pending",t.RequiresTesting="requires_testing",t.ReleaseQueued="release_queued",t.Unavailable="unavailable"}(a||(a={}));let d=class{constructor(t){this.followedGames=t,this.busy=!1}get hideEmail(){return this.account&&(0,l.Lt)(this.account.flags,2)}get isFollowing(){return!!this.gameId&&!!this.followedGames.followedGames.find((t=>t.gameId===this.gameId))}async notify(){if(this.busy)return;this.busy=!0;const t=this.gameId;try{if(!(0,l.Lt)(this.account.flags,2)&&!await this.accountEmail.submit())return;await this.followedGames.followGames([t],1)}finally{this.busy=!1}}async unfollow(){const t=this.gameId;this.followedGames.unfollowGames([t])}};(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",String)],d.prototype,"reason",void 0),(0,o.Cg)([n.bindable,(0,o.Sn)("design:type",String)],d.prototype,"gameId",void 0),(0,o.Cg)([(0,n.computedFrom)("account.flags"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],d.prototype,"hideEmail",null),(0,o.Cg)([(0,n.computedFrom)("followedGames.followedGames","gameId"),(0,o.Sn)("design:type",Boolean),(0,o.Sn)("design:paramtypes",[])],d.prototype,"isFollowing",null),d=(0,o.Cg)([(0,s.m6)({selectors:{account:(0,s.$t)((t=>t.account))}}),(0,n.autoinject)(),(0,o.Sn)("design:paramtypes",[r.O])],d)},"cheats/resources/elements/trainer-notify.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>r});var a=i(14385),o=i.n(a),n=new URL(i(16280),i.b);const r='<template> <require from="./trainer-notify.scss"></require> <require from="../../../resources/elements/account-email"></require> <p if.bind="reason === \'unavailable\'">${\'trainer_notify.no_cheats_yet\' | i18n}</p> <p if.bind="reason === \'release_queued\'">${\'trainer_notify.do_you_want_us_to_notify_you_when_its_ready\' | i18n}</p> <p if.bind="reason === \'update_pending\'">${\'trainer_notify.notify_me_when_mods_updated\' | i18n}</p> <p if.bind="reason === \'requires_testing\'">${\'trainer_notify.notify_me_when_mods_updated\' | i18n}</p> <form if.bind="!isFollowing" class="notify-form"> <account-email view-model.ref="accountEmail" status.bind="accountEmailStatus" placeholder.bind="\'trainer_notify.email_address\' | i18n" location="trainer_notify" if.bind="!hideEmail"></account-email> <wm-button disabled.bind="busy || (!hideEmail && accountEmailStatus !== \'valid\' && accountEmailStatus !== \'unsure\')" click.delegate="notify()"> ${reason === \'unavailable\' ? \'trainer_notify.request_mods\' : \'trainer_notify.notify_me\' | i18n} </wm-button> </form> <div if.bind="isFollowing" class="notify-success"> <i><inline-svg src="'+o()(n)+'"></inline-svg></i> <span if.bind="reason === \'update_pending\' || reason === \'requires_testing\'"> ${\'trainer_notify.well_send_you_an_email\' | i18n} <a class="unfollow-link" href="#" click.delegate="unfollow()">&times;</a> </span> <span else> ${\'trainer_notify.well_send_you_an_email_when_available\' | i18n} <a class="unfollow-link" href="#" click.delegate="unfollow()">${\'trainer_notify.unfollow\' | i18n}</a> </span> </div> </template> '},"cheats/resources/elements/trainer-notify.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>s});var a=i(31601),o=i.n(a),n=i(76314),r=i.n(n)()(o());r.push([t.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}trainer-notify{display:block}trainer-notify .notify-form{display:flex;flex-wrap:wrap;overflow:hidden;align-items:center}trainer-notify .notify-form account-email{margin-right:10px;margin-top:10px}trainer-notify .notify-form account-email email-input input:focus-visible{outline:none !important}trainer-notify .notify-form button{margin-top:10px}trainer-notify .notify-success{font-size:13px;line-height:20px;font-weight:600;display:inline-flex;align-items:center;margin:4.5px 0;min-height:29px;color:#fff}trainer-notify .notify-success i{margin-right:9px}trainer-notify .notify-success i svg *{fill:var(--theme--highlight)}trainer-notify:hover .unfollow-link{opacity:1}trainer-notify .unfollow-link{font-size:13px;line-height:20px;font-weight:700;color:var(--theme--highlight);margin-left:6px;opacity:0;transition:opacity .15s}",""]);const s=r},"cheats/resources/elements/trainer-play-button":(t,e,i)=>{i.r(e),i.d(e,{TrainerPlayButton:()=>T});var a=i(15215),o=i("aurelia-dialog"),n=i("aurelia-event-aggregator"),r=i("aurelia-framework"),s=i(20770),l=i(70763),d=i(79810),p=i(10704),c=i(58293),h=i(97170),u=i(96276),g=i(85805),b=i(96111),m=i("dialogs/time-limit-pre-game-dialog"),f=i("dialogs/time-limit-reached-pre-game-dialog"),y=i("dialogs/trainer-notes-dialog"),v=i(21795),w=i("shared/dialogs/basic-dialog"),x=i(54995),I=i(85975),k=i("shared/utility/resources/value-converters/platform"),S=i(48881),C=i(38777),_=i(83802);let T=class{#L;#M;#A;#D;#U;#E;#G;#O;#$;#R;#V;#B;#N;#z;#q;#W;#n;#F;constructor(t,e,i,a,o,n,r,s,l,d,p,c,h,u,g,b,m){this.trainerService=m,this.buttonState=null,this.isTrainerInstructionsDialog=!1,this.selectedTrainerNotesRead=!1,this.hideDropdown=!1,this.disabled=!1,this.preferredInstallation=null,this.dailyTimeLimitEnforcer=t,this.#E=e,this.#V=i,this.#M=a,this.#U=o,this.#W=n,this.#B=r,this.#F=s,this.#A=l,this.#q=d,this.#z=p,this.#N=c,this.#n=h,this.#L=u,this.#D=g,this.#G=b}attached(){this.dropdownOpen=!1}#j(){this.#$&&(this.#$.dispose(),this.#$=null,this.#R=null)}#H(){if(this.trainerInfo){const t=this.trainerInfo.gameId;this.#$&&t!==this.#R&&this.#j(),this.#$||(this.#$=this.#B.watchGame(t),this.#R=t)}else this.#j()}#Y(){if(!this.trainerInfo)return this.buttonState=null,void(this.dropdownOpen=!1);this.selectedGame=this.state.catalog.games[this.trainerInfo.gameId];const t=this.#Q(this.selectedGame),e=this.#Z(this.selectedGame);if(!this.preferredInstallation)return void(this.buttonState=e&&t?"install_for_free":"install");const i=this.trainerService.trainer;i?(this.dropdownOpen=!1,i.getMetadata(_.vO).info.id===this.trainerInfo.id?this.buttonState=i.isActive()?"playing":"loading":this.buttonState="playing_other"):this.buttonState="play"}bind(){const t=()=>{this.#Y(),this.#H()};this.#O=new C.Vd([this.trainerService.onNewTrainer(t),this.trainerService.onTrainerActivated(this.#Y.bind(this)),this.trainerService.onTrainerEnded(t),this.#N.onLaunchTrainer((t=>this.#K(t)))]),this.#X(),this.#Y(),this.#H()}unbind(){this.#O.dispose(),this.#j()}stateChanged(){this.#X(),this.#Y()}trainerInfoChanged(){this.#X(),this.#Y(),this.#H()}get shouldShowTrainerNotesDialog(){return!this.isTrainerInstructionsDialog&&!!this.trainerInfo?.blueprint?.notes&&!this.selectedTrainerNotesRead}openTrainerNotesDialog(){this.#G.open({steamId:this.model.steamAppId||"",titleThumbnail:this.model.thumbnail||"",trainerNotes:this.trainerInfo.blueprint.notes||"",selectedTrainer:this.trainerInfo,autoLaunch:this.autoLaunch,autoLaunchWithoutMods:this.autoLaunchWithoutMods})}async launch(t,e,i){this.hasLaunched=!0;const a=this.trainerInfo,o="remote"!==i&&this.shouldShowTrainerNotesDialog?"trainer_instruction_dialog":i,n=a?.blueprint?.notes?this.selectedTrainerNotesRead??!1:void 0;if(this.#M.publish(new v.VY(this.selectedGame.id,o,n)),this.dailyTimeLimitEnforcer.isEnabled&&this.dailyTimeLimitEnforcer.isOverDailyLimit)this.#D.open({perGame:!1,limitHours:this.dailyTimeLimitEnforcer.dailyPlayLimitHours});else if(this.#A.isEnabled&&this.#A.isOverDailyLimit(a.gameId))this.#D.open({perGame:!0,limitHours:this.#A.dailyPlayLimitHours});else{if("remote"!==i){if(this.shouldShowTrainerNotesDialog)return void this.openTrainerNotesDialog();this.#V.closeAll();let t=this.buttonState;if(this.buttonState="loading",this.lastTimeLimitPreGameDialog||3!==this.dailyTimeLimitEnforcer.e39Variant){if(!await this.#U.open())return void(this.buttonState=t)}else{const e=await this.#L.open({limitHours:2});if(this.#n.dispatch(S.vk,"lastTimeLimitPreGameDialog"),!e.wasCancelled)return void(this.buttonState=t)}this.buttonState=t;const i=!!this.state?.flags?.brokenModDialogHidden;if("number"==typeof e&&!this.trainerInfo.supportedVersions.includes(e)&&!i){const t=await this.#E.yesNo("game.cheats_might_not_work_dialog",{},!1,w.DialogTextKey.Confirm,w.DialogTextKey.Cancel,"brokenModDialogHidden",!1);t===w.DialogResult.Yes&&this.#n.dispatch(S.NX,"brokenModDialogHidden",!0);const i=t===w.DialogResult.Yes;if(this.#M.publish(new v.aV(this.selectedGame.id,e??null,a.id,i)),!i)return}t=this.buttonState,this.buttonState="loading"}try{await this.trainerService.launch(new _.vO(this.trainerInfo,t,e,i))}catch(t){if(!(t instanceof _.ZS))throw t}}}#X(){const t=this.#z.getPreferredInstallationInfo(this.trainerInfo?.gameId);this.preferredInstallation=t.app,this.preferredGameVersion=t.version}async installSelectedGame(){const t=this.trainerInfo.gameId,e=this.state.catalog.games[t];await this.#W.installGame(e,"game_page")}buttonStateChanged(t,e){e||"play"!==t||this.#J()}async#J(){(this.autoLaunch||this.autoLaunchWithoutMods)&&(await(0,C.Wn)(),this.preferredInstallation&&(this.autoLaunchWithoutMods?this.#F.launch(this.preferredInstallation,this.selectedGame.id,"shortcut"):this.launch(this.preferredInstallation,this.preferredGameVersion,"shortcut")),this.autoLaunch=!1,this.autoLaunchWithoutMods=!1)}get shouldShowInstallCoachingTip(){return!this.state?.flags.hasClickedInstallGame}get shouldShowPlayCoachingTip(){return!this.hasLaunched&&this.state&&0===Object.values(this.state.gameHistory).filter((t=>!!t.lastPlayedAt)).length}#K(t){t===this.trainerInfo.id&&(this.preferredInstallation&&this.launch(this.preferredInstallation,this.preferredGameVersion,"remote"),this.#V.closeAll())}get platformId(){return this.trainerInfo?this.state.catalog.games[this.trainerInfo.gameId]?.platformId:null}get platformName(){return this.#q.toView(this.platformId??"")}endTrainer(){const t=this.trainerService.trainer;t?.isEnding()||t?.dispose()}#Q(t){return!!t&&t.tags.includes("free")}#Z(t){return!!t&&(t.purchaseUris??[]).length>0}get loading(){return!this.buttonState}};(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],T.prototype,"trainerInfo",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Object)],T.prototype,"model",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",String)],T.prototype,"disabledMessage",void 0),(0,a.Cg)([(0,r.bindable)({defaultBindingMode:r.bindingMode.twoWay}),(0,a.Sn)("design:type",Object)],T.prototype,"buttonState",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],T.prototype,"autoLaunch",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],T.prototype,"autoLaunchWithoutMods",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],T.prototype,"isTrainerInstructionsDialog",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],T.prototype,"selectedTrainerNotesRead",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],T.prototype,"hideDropdown",void 0),(0,a.Cg)([r.bindable,(0,a.Sn)("design:type",Boolean)],T.prototype,"disabled",void 0),(0,a.Cg)([(0,r.computedFrom)("selectedTrainerNotesRead","isTrainerInstructionsDialog","trainerInfo.blueprint.notes"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],T.prototype,"shouldShowTrainerNotesDialog",null),(0,a.Cg)([(0,r.computedFrom)("state.flags.hasClickedInstallGame"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],T.prototype,"shouldShowInstallCoachingTip",null),(0,a.Cg)([(0,r.computedFrom)("hasLaunched","state.gameHistory"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],T.prototype,"shouldShowPlayCoachingTip",null),(0,a.Cg)([(0,r.computedFrom)("trainerInfo.gameId"),(0,a.Sn)("design:type",Object),(0,a.Sn)("design:paramtypes",[])],T.prototype,"platformId",null),(0,a.Cg)([(0,r.computedFrom)("platformId"),(0,a.Sn)("design:type",String),(0,a.Sn)("design:paramtypes",[])],T.prototype,"platformName",null),(0,a.Cg)([(0,r.computedFrom)("buttonState"),(0,a.Sn)("design:type",Boolean),(0,a.Sn)("design:paramtypes",[])],T.prototype,"loading",null),T=(0,a.Cg)([(0,x.m6)((t=>t.state.pipe((0,I.r)("installedGameVersions","installedApps","catalog","gameHistory","flags","trainerNotesRead")))),(0,r.autoinject)(),(0,a.Sn)("design:paramtypes",[b.Y,w.BasicDialogService,o.DialogService,n.EventAggregator,l.i,d.r,p.r,c.L,h.V,k.PlatformNameValueConverter,u.T,g.e,s.il,m.TimeLimitPreGameDialogService,f.TimeLimitReachedPreGameDialogService,y.TrainerNotesDialogService,_.jR])],T)},"cheats/resources/elements/trainer-play-button.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>r});var a=i(14385),o=i.n(a),n=new URL(i(29509),i.b);const r='<template> <require from="./trainer-play-button.scss"></require> <require from="./play-game-menu"></require> <require from="./add-game-menu"></require> <require from="../../../resources/elements/coaching-tip"></require> <require from="../../../resources/custom-attributes/detach-el"></require> <require from="../../../shared/resources/elements/tooltip"></require> <require from="../../../resources/custom-attributes/close-if-press-escape"></require> <div class="button-wrapper"> <div class="button-container au-animate ${loading ? \'loading\' : \'\'} ${disabled ? \'disabled\' : \'\'}" if.bind="buttonState"> <template if.bind="preferredInstallation"> <span if.bind="buttonState === \'play\'" class="button play pulse-play ${hideDropdown ? \'hide-dropdown\' : \'\'}"> <span class="gradient"></span> <span class="label" click.delegate="launch(preferredInstallation, preferredGameVersion)" tabindex="0"> <img class="play-icon" src.bind="platformId | playIconSrc"> <span>${\'trainer_play_button.play\' | i18n}</span> </span> <i if.bind="!hideDropdown" click.delegate="dropdownOpen = true" class="caret" tabindex="0"></i> </span> <span if.bind="buttonState === \'loading\'" class="button loading disabled"> <span class="gradient"></span> <span class="scroll-wrapper"> <span class="label allow-looping-animation">${\'trainer_play_button.loading_mods\' | i18n}</span> <span class="label allow-looping-animation">${\'trainer_play_button.loading_mods\' | i18n}</span> </span> </span> <span if.bind="buttonState === \'playing\'" class="button playing disabled"> <span class="gradient"></span> <span class="label">${\'trainer_play_button.playing\' | i18n}</span> <button class="end-button" click.delegate="endTrainer()"> <tooltip direction="top-right"> <div slot="content">${\'trainer_play_button.stop_playing\' | i18n}</div> </tooltip> </button> </span> <span if.bind="buttonState === \'playing_other\'" class="button playing disabled"> <span class="gradient"></span> <span class="label">${\'trainer_play_button.play\' | i18n}</span> <i class="caret"></i> </span> </template> <template else> <span if.bind="buttonState === \'install_for_free\'" class="button no-installation install pulse-accent ${isInstalling ? \'disabled\' : \'\'}"> <span class="label" click.delegate="installSelectedGame()" tabindex="0"> <img class="play-icon" src.bind="platformId | playIconSrc"> <span class="regular-label">${\'trainer_play_button.install_for_free\' | i18n}</span> <span class="condensed-label">${\'trainer_play_button.install\' | i18n}</span> </span> <i click.delegate="dropdownOpen = true" class="caret" tabindex="0"></i> </span> <template if.bind="buttonState === \'install\'"> <span class="button no-installation install ${isInstalling ? \'disabled\' : \'\'}" click.delegate="dropdownOpen = true"> <span class="label" tabindex="0"> ${\'trainer_play_button.add_game\' | i18n} </span> <i class="caret" tabindex="0"></i> </span> </template> </template> <span class="glow"></span> <span class="lines allow-looping-animation"> <inline-svg src="'+o()(n)+'"></inline-svg> </span> </div> <div if.bind="trainerInfo.titleId" class="play-button-dropdown"> <play-game-menu if.bind="dropdownOpen && buttonState === \'play\'" close-if-press-escape="open.bind: dropdownOpen" title-id.bind="trainerInfo.titleId" open.bind="dropdownOpen" detach-el></play-game-menu> <add-game-menu if.bind="dropdownOpen && [\'install\', \'install_for_free\'].includes(buttonState)" close-if-press-escape="open.bind: dropdownOpen" title-id.bind="trainerInfo.titleId" open.bind="dropdownOpen" detach-el></add-game-menu> </div> </div> </template> '},"cheats/resources/elements/trainer-play-button.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>y});var a=i(31601),o=i.n(a),n=i(76314),r=i.n(n),s=i(4417),l=i.n(s),d=new URL(i(83959),i.b),p=new URL(i(41533),i.b),c=new URL(i(88392),i.b),h=new URL(i(63706),i.b),u=r()(o()),g=l()(d),b=l()(p),m=l()(c),f=l()(h);u.push([t.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${g}) format("woff2")}.material-symbols-outlined,trainer-play-button .button .caret{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}body.reduce-motion coaching-tip .hotspot .ring:not(:first-child){opacity:0 !important}coaching-tip{display:inline-block;position:relative}coaching-tip .wrapper{position:relative;width:32px}coaching-tip .hotspot{--hotspot--color: var(--color--accent);position:relative;width:32px;height:32px}coaching-tip .hotspot,coaching-tip .hotspot *{cursor:pointer}coaching-tip .hotspot .ring{position:absolute;left:50%;top:50%;border-radius:50%;transition:background-color .15s,border-color .15s}coaching-tip .hotspot .ring:nth-child(1){width:37.5%;height:37.5%;margin-left:-18.75%;margin-top:-18.75%;background:var(--hotspot--color)}coaching-tip .hotspot .ring:nth-child(2){width:62.5%;height:62.5%;margin-left:-31.25%;margin-top:-31.25%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out 0s infinite}coaching-tip .hotspot .ring:nth-child(3){width:81.25%;height:81.25%;margin-left:-40.625%;margin-top:-40.625%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out .1s infinite}coaching-tip .hotspot .ring:nth-child(4){width:100%;height:100%;margin-left:-50%;margin-top:-50%;border:1.5px solid var(--hotspot--color);animation:coaching-tip-hotspot 1s ease-in-out .2s infinite}coaching-tip .popup{width:325px;padding:20px 25px 25px;border-radius:10px;background:var(--theme--secondary-background);border:1px solid rgba(255,255,255,.05);opacity:0;visibility:hidden;transition:visibility 0s .2s;z-index:1;box-shadow:0px 0px 5px rgba(17,17,17,.5)}coaching-tip .popup h5{font-weight:600;font-size:16px;line-height:25px;font-weight:700;margin:0 0 5px;color:#fff}coaching-tip .popup p{font-size:14px;line-height:22px;font-weight:500;color:rgba(255,255,255,.6);margin:0}coaching-tip .popup p a{font-weight:500;color:var(--theme--highlight);text-decoration:underline}coaching-tip .popup p a:hover{color:#fff}coaching-tip .popup p strong{font-weight:500;color:#fff}coaching-tip .popup button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}coaching-tip .popup button,coaching-tip .popup button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button{border:1px solid #fff}}coaching-tip .popup button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}coaching-tip .popup button>*:first-child{padding-left:0}coaching-tip .popup button>*:last-child{padding-right:0}coaching-tip .popup button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button svg *{fill:CanvasText}}coaching-tip .popup button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip .popup button svg{opacity:1}}coaching-tip .popup button img{height:50%}coaching-tip .popup button:disabled{opacity:.3}coaching-tip .popup button:disabled,coaching-tip .popup button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){coaching-tip .popup button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}coaching-tip .popup button:not(:disabled):hover svg{opacity:1}}coaching-tip .popup button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){coaching-tip .popup button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}coaching-tip .popup button:not(:disabled):active{background-color:var(--theme--highlight)}coaching-tip .popup hr{border:0;border-top:1px solid rgba(255,255,255,.2);margin:15px 0}coaching-tip close-button{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0.15)) !important;display:inline-flex;width:26px;height:26px;box-shadow:none;padding:0;border-radius:50%;border:0;align-items:center;justify-content:center;transition:background-color .15s;position:absolute;right:-12.5px;top:-12.5px}@media(forced-colors: active){body:not(.override-contrast-mode) coaching-tip close-button{border:1px solid #fff}}coaching-tip close-button svg{opacity:1}@media(hover: hover){coaching-tip close-button:hover{background:rgba(var(--theme--background--rgb), 0.95) linear-gradient(rgba(255, 255, 255, 0.25), rgba(255, 255, 255, 0.25)) !important}}coaching-tip.show .hotspot{--hotspot--color: var(--color--brand-blue)}coaching-tip.show .popup{animation:dialog-pop .2s ease-in-out forwards;visibility:visible;transition-delay:0s}coaching-tip.position-relative-right{margin-left:16px}coaching-tip.position-right{position:absolute;left:100%;top:50%;margin-left:16px;margin-top:-16px}coaching-tip.position-left{position:absolute;right:100%;top:50%;margin-right:16px;margin-top:-16px}coaching-tip.popup-right .popup{position:absolute;left:100%;top:0;margin-left:12px}coaching-tip.popup-left .popup{position:absolute;right:100%;top:0;margin-right:12px}coaching-tip.popup-bottom-left .popup{position:absolute;right:0px;top:100%;margin-top:12px}coaching-tip.hide-hotspot .wrapper{width:0}coaching-tip.hide-hotspot .hotspot{display:none}coaching-tip.hide-hotspot .popup{margin:0}coaching-tip.close-button-left close-button{right:initial;left:-12px}body.disable-looping-animation coaching-tip .hotspot .ring:not(:first-child){animation-fill-mode:forwards !important}@keyframes coaching-tip-hotspot{0%{opacity:0}25%{opacity:1}50%,100%{opacity:0}}.coaching-tip-highlight{animation:coaching-tip-highlight .15s linear forwards}@keyframes coaching-tip-highlight{to{box-shadow:0 0 0 1px var(--theme--highlight),0 0 40px 0 rgba(var(--theme--highlight--rgb), 0.3)}}trainer-play-button{display:flex;align-items:center;position:relative}trainer-play-button .button-wrapper{position:relative}trainer-play-button .button-container{position:relative;z-index:0}trainer-play-button .button-container.au-enter-active{animation:trainer-play-button-fade .15s}trainer-play-button .button-container.au-leave-active{animation:trainer-play-button-fade .15s reverse forwards}trainer-play-button .button-container.loading:before{background:rgba(255,255,255,.02);animation:placeholder__el--fade 1s ease-in-out calc(-1.5s*var(--placeholder--index)) alternate infinite,placeholder__el--initial-fade .1s linear 0s normal both;content:"";display:inline-block;width:96px;height:49px;border-radius:100px}trainer-play-button .button-container.loading:before,trainer-play-button .button-container.loading:before *{color:rgba(0,0,0,0) !important;border:0 !important}trainer-play-button .button-container.loading:before *{visibility:hidden}trainer-play-button .button-container.loading .button{display:none}trainer-play-button .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;padding:0 !important;color:#fff !important;background:rgba(0,0,0,0);display:inline-flex;position:relative;z-index:0;height:44px;align-items:center;justify-content:center;gap:2px;--cta--hover--border-width: 1px;--cta--height: 44px;position:relative;color:#fff;padding:0px 10px 0px 16px !important}trainer-play-button .button,trainer-play-button .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-play-button .button{border:1px solid #fff}}trainer-play-button .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}trainer-play-button .button>*:first-child{padding-left:0}trainer-play-button .button>*:last-child{padding-right:0}trainer-play-button .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-play-button .button svg *{fill:CanvasText}}trainer-play-button .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) trainer-play-button .button svg{opacity:1}}trainer-play-button .button img{height:50%}trainer-play-button .button:disabled{opacity:.3}trainer-play-button .button:disabled,trainer-play-button .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){trainer-play-button .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}trainer-play-button .button:not(:disabled):hover svg{opacity:1}}trainer-play-button .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}trainer-play-button .button>*:first-child{padding-left:var(--cta--padding)}trainer-play-button .button>*:last-child{padding-right:var(--cta--padding)}trainer-play-button .button svg *{fill:#fff}trainer-play-button .button:disabled{opacity:1 !important}trainer-play-button .button:disabled>*{opacity:.3}trainer-play-button .button>*{transition:opacity .15s}trainer-play-button .button.loading>*{opacity:1 !important}trainer-play-button .button.loading .scroll-wrapper{width:160px;height:100%;position:relative;display:flex;overflow:hidden;-webkit-mask-image:linear-gradient(90deg, transparent 0, transparent 13px, black 18px, black calc(100% - 18px), transparent calc(100% - 13px));mask-image:linear-gradient(90deg, transparent 0, transparent 13px, black 18px, black calc(100% - 18px), transparent calc(100% - 13px))}trainer-play-button .button.loading~.glow,trainer-play-button .button.loading~.lines{opacity:1}trainer-play-button .button.loading:not(.no-scroll) .label{display:block;animation:trainer-play-button-loading-scroll linear 3s infinite;padding-right:50px}trainer-play-button .button.loading .gradient{transform:rotate(180deg)}trainer-play-button .button .label{display:flex;gap:4px;font-size:24px;font-style:normal;font-weight:900;line-height:normal;letter-spacing:-1px}trainer-play-button .button.playing .gradient:after{background:linear-gradient(298deg, #0bf2f6 -7.1%, #9200ff 100.65%)}trainer-play-button .button .gradient{position:absolute;left:0;top:0;width:100%;height:100%;border-radius:84px;overflow:hidden;z-index:-1;transition:transform 1s linear}trainer-play-button .button .gradient:after{content:"";position:absolute;left:0;top:50%;width:100%;height:0;padding-top:100%;transform:translateY(-50%);animation:var(--cta__play-gradient--animation, none);background:var(--accent-brand-logo-purple-green, linear-gradient(225deg, #0bf2f6 0%, #9200ff 100%))}@media(hover: hover){trainer-play-button .button:not(.disabled):hover{filter:brightness(1.1)}}trainer-play-button .button.install{box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent)}@media(hover: hover){trainer-play-button .button.install:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}trainer-play-button .button.install:not(:disabled):active{background-color:var(--color--accent)}trainer-play-button .button.install:not(:disabled):active{--cta__icon--color: #000;color:#000}trainer-play-button .button.install:hover{background:rgba(var(--color--accent--rgb), 0.1) !important}trainer-play-button .button.install:active{background:var(--color--accent) !important}trainer-play-button .button.pulse-accent::before{content:"";display:block;position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1;background:var(--color--accent);opacity:.2;animation:cta--pulse .5s ease-in-out infinite alternate;border-radius:99px}trainer-play-button .button.pulse-accent::before{background-color:var(--color--accent);animation:cta--inner-pulse 1s ease-in-out infinite alternate}trainer-play-button .button.pulse-play::before{content:"";display:block;position:absolute;left:0;top:0;width:100%;height:100%;z-index:-1;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);opacity:.4;animation:cta--pulse .5s ease-in-out infinite alternate;border-radius:99px}trainer-play-button .button.disabled,trainer-play-button .button.disabled *{cursor:default}trainer-play-button .button.disabled .caret{pointer-events:none}trainer-play-button .button.hide-dropdown{padding-right:20px !important}trainer-play-button .button .play-icon img{width:24px;height:24px}trainer-play-button .button .caret{font-variation-settings:"FILL" 1,"wght" 400 !important;font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;display:flex;font-size:20px;color:rgba(255,255,255,.8);border-radius:100%;width:24px;height:24px;justify-content:center;align-items:center;padding:2px}trainer-play-button .button .caret:before{font-family:inherit;content:"expand_more"}trainer-play-button .button .caret:hover{background:rgba(255,255,255,.15)}trainer-play-button .button .end-button{border:none;width:32px;height:32px;background:url(${b}) center no-repeat;outline:none;border-radius:50%;padding:0;margin:8.5px;position:relative}trainer-play-button .button .end-button,trainer-play-button .button .end-button *{cursor:pointer}trainer-play-button .button .end-button:hover{background:rgba(255,255,255,.15) url(${b}) center no-repeat}trainer-play-button .glow{position:absolute;width:230%;height:368px;left:50%;top:50%;transform:translate(-50%, -50%);background:url(${m}) center/100% 100%;z-index:-2;pointer-events:none;opacity:0;transition:opacity .3s;visibility:hidden}trainer-play-button .lines{-webkit-mask-image:radial-gradient(circle at center, black 50%, transparent);position:absolute;left:50%;top:50%;transform:translate(-50%, -50%);width:134%;height:158px;pointer-events:none;opacity:0;z-index:-1;transition:opacity .3s}trainer-play-button .lines svg{width:100%;height:100%}trainer-play-button .lines .line{animation:trainer-play-button-line 5s ease-in-out infinite}trainer-play-button .lines .line.highlight,trainer-play-button .lines .line .highlight{fill:var(--theme--highlight) !important;fill-opacity:1 !important}trainer-play-button .lines .line:nth-child(1){animation-duration:2s}trainer-play-button .lines .line:nth-child(2){animation-duration:2.25s}trainer-play-button .lines .line:nth-child(3){animation-duration:2.5s}trainer-play-button .lines .line:nth-child(4){animation-duration:2.75s}trainer-play-button .lines .line:nth-child(5){animation-duration:3s}trainer-play-button .lines .line:nth-child(6){animation-duration:3.25s}trainer-play-button .lines .line:nth-child(7){animation-duration:3.5s}trainer-play-button .lines .line:nth-child(8){animation-duration:4s}trainer-play-button .lines .line:nth-child(9){animation-duration:4.5s}trainer-play-button .lines .line:nth-child(10){animation-duration:4.75s}trainer-play-button .lines .line:nth-child(11){animation-duration:5s}trainer-play-button .lines .line:nth-child(12){animation-duration:5.5s}trainer-play-button .lines .line:nth-child(13){animation-duration:5.76s}trainer-play-button .lines .line:nth-child(14){animation-duration:6s}trainer-play-button .lines .line:nth-child(15){animation-duration:6.5s}trainer-play-button .lines .line:nth-child(16){animation-duration:6.75s}trainer-play-button .lines .line:nth-child(17){animation-duration:7s}trainer-play-button .play-button-dropdown{position:absolute;right:0;top:calc(100% + 5px) !important}trainer-play-button .label .condensed-label{display:none}@media(max-width: 1440px){trainer-play-button .label .condensed-label{display:initial}trainer-play-button .label .regular-label{display:none}}trainer-play-button .button-container.disabled{opacity:.6}trainer-play-button .button-container.disabled,trainer-play-button .button-container.disabled *{cursor:not-allowed;pointer-events:none}trainer-play-button .button-container.disabled .button{filter:grayscale(30%)}trainer-play-button .button-container.disabled .button:hover{filter:grayscale(30%);transform:none}trainer-play-button .button-container.disabled .button.pulse-play::before,trainer-play-button .button-container.disabled .button.pulse-play::after,trainer-play-button .button-container.disabled .button.pulse-accent::before,trainer-play-button .button-container.disabled .button.pulse-accent::after{animation:none !important;opacity:0 !important}trainer-play-button .button-container.disabled .glow{display:none}trainer-play-button .button-container.disabled .lines{display:none}trainer-play-button coaching-tip{position:absolute;left:0;top:0;margin-left:-10px}trainer-play-button coaching-tip#trainer_install_button p br{display:block;content:"";margin-top:12px}trainer-play-button coaching-tip#trainer_install_button p em{font-style:normal}trainer-play-button coaching-tip#trainer_install_button p em:after{content:"";display:inline-block;vertical-align:middle;width:12px;height:12px;-webkit-mask-box-image:url(${f});background-color:var(--theme--highlight);margin:0 2px 0 5px}@keyframes trainer-play-button-fade{from{opacity:0}to{opacity:1}}`,""]);const y=u}}]);
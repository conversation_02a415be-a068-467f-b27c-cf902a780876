"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1701,2590],{12082:(e,t,s)=>{s.d(t,{q:()=>r});const r="tick-minute"},14046:(e,t,s)=>{s.d(t,{Ov:()=>c,bu:()=>l,c_:()=>a,dS:()=>d});var r=s(32534),n=s(69735),i=s(34884),o=s(51977);function a(e,t){return(0,r.A)(t,e)}function l(e,t){return(0,n.A)(t,e)}function c(e,t){return(0,i.A)(t,e)}function d(e,t){return(0,o.A)(t,e)}},17873:(e,t,s)=>{function r(e){let t;return()=>(e&&(t=e(),e=void 0),t)}s.d(t,{R:()=>r})},27378:(e,t,s)=>{s.d(t,{c:()=>a});var r=s(5836),n=s(43938),i=s(48100),o=s(88120);const a=e=>{const t=window.matchMedia(e);return(0,r.R)(t,"change").pipe((0,n.Z)(t),(0,i.T)((e=>e.matches)),(0,o.F)())}},29278:(e,t,s)=>{s.d(t,{H:()=>r});class r{toView(e,t){const s={};return e?.forEach((e=>{const r=e[t];s[r]=s[r]||[],s[r].push(e)})),Object.keys(s).map((e=>({[t]:e,values:s[e]})))}}},29944:(e,t,s)=>{s.d(t,{Jx:()=>l,Td:()=>a,kk:()=>o,v:()=>n});var r=s(92465);class n extends Error{constructor(e){super(e),Object.setPrototypeOf(this,n.prototype)}}class i{#e;#t;#s;#r;constructor(e,t){this.#r=new r._M,this.#e=e,this.#s=t;const s=e=>{e.origin===this.#s&&e.source===this.#e&&this.#r.publish("_",e)};window.addEventListener("message",s),this.#t=(0,r.nm)((()=>window.removeEventListener("message",s)))}onMessage(e){return this.#r.subscribe("_",e)}postMessage(e,t){this.#e?.postMessage(e,this.#s,t)}close(){this.#t?.dispose(),this.#t=null,this.#r?.dispose(),this.#e=null}}class o{#n;constructor(e){this.#n=e,e.start()}onMessage(e){if(!this.#n)throw new Error("RPC channel was disposed.");return this.#n.addEventListener("message",e),(0,r.nm)((()=>this.#n?.removeEventListener("message",e)))}postMessage(e){this.#n?.postMessage(e)}close(){this.#n?.close(),this.#n=null}}class a{#i;#n;#t;#o;#a;constructor(e){this.#i=1,this.#o=new Map,this.#a=new Map,this.#n=e,this.#t=e.onMessage((e=>{const t=e.data;"object"==typeof t&&null!==t&&"number"==typeof t.id&&"string"==typeof t.type&&this.#l(t)}))}execute(e,t){if(!this.#n)throw new Error("RPC channel was disposed.");const s=this.#i++,r=new Promise(((e,t)=>{this.#a.set(s,[e,t])}));return this.#c({id:s,type:e,data:t}),r}setHandler(e,t){if(!this.#n)throw new Error("RPC channel was disposed.");this.#o.set(e,t)}async#l(e){if("_resolve"===e.type||"_reject"===e.type||"_unhandled"===e.type){const t=this.#a.get(e.id);if(t)if(this.#a.delete(e.id),"_resolve"===e.type){const s=e;t[0](s.data)}else if("_reject"===e.type){const s=e;t[1](s.data)}else if("_unhandled"===e.type){const s=e;t[1](new n(`No handler defined for message type ${s.data.type}.`))}}else{const t=this.#o.get(e.type);if(t){let s,r;try{r=await t(e.data),s="_resolve"}catch(e){r=e,s="_reject"}this.#c({id:e.id,type:s,data:r})}else this.#c({id:e.id,type:"_unhandled",data:{type:e.type}})}}#c(e){this.#n?.postMessage(e)}dispose(){this.#t?.dispose(),this.#t=null,this.#o.clear(),this.#n?.close(),this.#n=null;for(const[e,t]of this.#a.values())t(new Error("RPC channel was disposed."));this.#a.clear()}}class l extends a{constructor(e,t){super(new i(e,t))}}},38110:(e,t,s)=>{function r(e){return window.getComputedStyle(document.body).getPropertyValue(e)}function n(e){return r(`--color--${e}`)}function i(e){return r(`--theme--default--${e}`)}s.d(t,{IX:()=>i,UU:()=>r,oU:()=>n})},44242:(e,t,s)=>{s.d(t,{g:()=>n});var r=s("aurelia-logging-console");s(16566);class n extends r.ConsoleAppender{#d;constructor(e){super(),this.#d=e}#u(e){return!this.#d.includes(e.id)}debug(e,...t){this.#u(e)&&super.debug(e,...t)}info(e,...t){this.#u(e)&&super.info(e,...t)}warn(e,...t){this.#u(e)&&super.warn(e,...t)}error(e,...t){this.#u(e)&&super.error(e,...t)}}},48335:(e,t,s)=>{function r(e,t){let s;return function(){clearTimeout(s),s=window.setTimeout((()=>{e()}),t)}}s.d(t,{s:()=>r})},49442:(e,t,s)=>{s.d(t,{Y:()=>r});const r=Object.freeze((function(){}))},54995:(e,t,s)=>{s.d(t,{$t:()=>u,m6:()=>h});var r=s(27884),n=s(20770),i=s(87237),o=s(42812),a=s(48100),l=s(88120);const c=e=>e.state,d={selectors:{state:c},setup:"bind",teardown:"unbind"};function u(e,t){return s=>s.state.pipe((0,a.T)(e),(0,l.F)(t))}function h(e){const t=r.mc.instance.get(n.il),s="function"==typeof e||void 0===e?{...d,selectors:{state:e||c}}:{...d,...e};return e=>{const r=s.setup??d.setup,n=s.selectors??d.selectors,a=s.teardown??d.teardown,l=e.prototype[r];e.prototype[r]=function(){this._bindCalled=!1,this._stateSubscriptions=Object.keys(n).map((e=>function(e){const s=e(t);return s instanceof i.c?s:t.state}(n[e]).subscribe((t=>{const s=this[e];if(this[e]=t,this._bindCalled){"propertyChanged"in this&&this.propertyChanged(e,t,s);const r=`${e}Changed`;r in this&&this[r](t,s)}}))));try{if(l)return l.apply(this,arguments)}finally{this._bindCalled=!0}};const c=e.prototype[a];e.prototype[a]=function(){if(this._stateSubscriptions&&Array.isArray(this._stateSubscriptions)&&this._stateSubscriptions.forEach((e=>{e instanceof o.y&&!1===e.closed&&e.unsubscribe()})),this._bindCalled=!1,c)return c.apply(this,arguments)}}}},70236:(e,t,s)=>{function r(e,t){return!!((e??0)&(t??0))}function n(e,t){return t.some((t=>!!((e??0)&(t??0))))}s.d(t,{Lt:()=>r,br:()=>n})},85975:(e,t,s)=>{s.d(t,{r:()=>n});var r=s(88120);function n(...e){return(0,r.F)(((t,s)=>e.every((e=>t[e]===s[e]))))}},88849:(e,t,s)=>{s.d(t,{DU:()=>a,YZ:()=>i,jD:()=>o,z3:()=>n});const r=["B","KB","MB","GB","TB","PB","EB","ZB","YB"];function n(e,t=2){if(!e)return"0 "+r[0];const s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(t))+" "+r[s]}function i(e){let t=0;for(let s=0;s<e.length;s++)t=(t<<5)-t+e.charCodeAt(s)|0;return t}function o(e){return"string"==typeof e&&e.trim()||null}function a(e){const t=new Uint8Array(e);return crypto.getRandomValues(t),BigInt("0x"+Array.from(t,(e=>e.toString(16).padStart(2,"0"))).join("")).toString(36).substring(0,e)}},"shared/store/index":(e,t,s)=>{s.r(t),s.d(t,{configure:()=>o});var r=s(20770),n=s(5923),i=s(48870);function o(e,t){const s=function(e){let t;try{const s=localStorage.getItem(e.storageKey);if("string"==typeof s){const e=JSON.parse(s);"object"==typeof e&&null!==e&&"number"==typeof e.stateVersion&&(t=e)}}catch{}return function(e,t,s){let r=!1;try{for(;e&&(e.stateVersion??0)<s.length;)r=!0,e=s[e.stateVersion??0](e)}catch{e=null}return{state:e??{...t,stateVersion:s.length},migrated:r}}(t??e.getFallbackState?.()??null,e.initialState,e.migrations)}(t);s.migrated&&a(s.state,t.storageKey);const o=new r.il(s.state,{logDispatchedActions:t.debug,logDefinitions:{dispatchedActions:r.$b.debug}});let l;e.container.registerInstance(r.il,o),t.debug&&(o.state.subscribe((e=>window.state=e)),o.registerMiddleware(r.Pd,r._s.After,{logType:"debug"})),Object.entries(t.actions).forEach((([e,t])=>o.registerAction(e,t))),o.state.pipe((0,n.i)(1),(0,i.B)(2e3)).subscribe((e=>a(e,t.storageKey))),o.state.subscribe((e=>l=e)),window.addEventListener("unload",(()=>{t.debug&&!localStorage.getItem(t.storageKey)||a(l,t.storageKey)}))}function a(e,t){localStorage.setItem(t,JSON.stringify(e))}},"shared/utility/index":(e,t,s)=>{s.r(t),s.d(t,{configure:()=>o});var r=s(7530),n=(s("aurelia-framework"),s("shared/utility/resources/elements/inline-svg")),i=s(12082);function o(e,t){e.globalResources(["./resources/attributes/fallback-src","./resources/attributes/overflow-fade","./resources/elements/inline-svg","./resources/value-converters/platform","./resources/value-converters/string"]),setInterval((()=>(0,r.N9)(i.q)),6e4),t?.debug&&(n.InlineSvgCustomElement.debug=!0)}},"shared/utility/resources/attributes/fallback-src":(e,t,s)=>{s.r(t),s.d(t,{FallbackSrcCustomAttribute:()=>o});var r=s(15215),n=s("aurelia-framework"),i=s(92465);let o=class{#h;#p;constructor(e){this.#h=e}attached(){this.#p=(0,i.yB)(this.#h,"error",(()=>{this.value&&(this.#h.src=this.value,this.#h.srcset&&(this.#h.srcset=this.value)),this.#h.classList.add("is-fallback")}))}detached(){this.#p?.dispose(),this.#p=null}};o=(0,r.Cg)([(0,n.inject)(Element),(0,n.noView)(),(0,r.Sn)("design:paramtypes",[HTMLImageElement])],o)},"shared/utility/resources/attributes/overflow-fade":(e,t,s)=>{s.r(t),s.d(t,{OverflowFadeCustomAttribute:()=>a});var r=s(15215),n=s("aurelia-framework");const i=new ResizeObserver((e=>e.forEach((e=>e.target.au["overflow-fade"].viewModel.update())))),o=new MutationObserver((e=>e.forEach((e=>e.target.au["overflow-fade"].viewModel.update()))));let a=class{#h;#g;#f;constructor(e){this.#g=!1,this.#h=e,this.update=this.update.bind(this),this.#f=this.#m.bind(this)}bind(){i.observe(this.#h),o.observe(this.#h,{childList:!0}),this.#h.addEventListener("scroll",this.update)}unbind(){i.unobserve(this.#h),o.disconnect(),this.#h.removeEventListener("scroll",this.update)}update(){this.#g||(this.#g=!0,window.requestAnimationFrame(this.#f))}#m(){if("horizontal"===this.value){const e=this.#h;e.parentElement?.classList.toggle("overflow-fade-left",e.scrollLeft>0),e.parentElement?.classList.toggle("overflow-fade-right",e.scrollLeft+e.clientWidth<e.scrollWidth-1)}if("vertical"===this.value){const e=this.#h;e.parentElement?.classList.toggle("overflow-fade-top",e.scrollTop>0),e.parentElement?.classList.toggle("overflow-fade-bottom",e.scrollTop+e.clientHeight<e.scrollHeight-1)}this.#g=!1}};a=(0,r.Cg)([(0,n.inject)(Element),(0,n.noView)(),(0,r.Sn)("design:paramtypes",[Element])],a)},"shared/utility/resources/elements/inline-svg":(e,t,s)=>{s.r(t),s.d(t,{InlineSvgCustomElement:()=>c});var r,n=s(15215),i=s(92126),o=s("aurelia-framework");const a=new Map,l=new Map;let c=r=class{#w;#v;#y;constructor(e,t){this.#y=!1,this.#w=e,this.#v=t}attached(){this.#y=!0,this.srcChanged()}detached(){this.#y=!1}async srcChanged(){if(!this.#y)return;if(!this.src)return void this.#b("","");const e=a.get(this.src);if(void 0!==e)return void this.#b(this.src,e);let t=l.get(this.src);t||(t=this.#C(this.src),l.set(this.src,t));const s=this.src,r=await t;this.#y&&s===this.src&&this.#b(s,r)}async#C(e){try{const t=await this.#v.get(e);if(t.ok){let s=await t.text();const r=s.indexOf("<svg");return r>0&&(s=s.substring(r)),a.set(e,s),s}if(r.debug)throw new Error(`Image request failed (${e})`);return""}finally{l.delete(e)}}#b(e,t){const s=this.#w.parentNode;s&&(s.innerHTML=t,r.debug&&s.prepend(document.createComment(e)))}};(0,n.Cg)([o.bindable,(0,n.Sn)("design:type",String)],c.prototype,"src",void 0),c=r=(0,n.Cg)([(0,o.noView)(),(0,o.containerless)(),(0,o.customElement)("inline-svg"),(0,o.inject)(Element,i.Qq),(0,n.Sn)("design:paramtypes",[HTMLElement,i.Qq])],c)},"shared/utility/resources/value-converters/dates":(e,t,s)=>{s.r(t),s.d(t,{DateOrTodayValueConverter:()=>c});var r=s(15215),n=s("aurelia-framework"),i=s(21447),o=s(20057),a=s(15448),l=s(12082);let c=class{constructor(e){this.i18n=e,this.signals=[a.p,a.r,l.q]}toView(e){return(0,i.A)(e)?this.i18n.getRelativeTimeFormatter({numeric:"auto"}).format(0,"day"):this.i18n.formatDateTime(e,{dateStyle:"medium"})}};c=(0,r.Cg)([(0,n.autoinject)(),(0,r.Sn)("design:paramtypes",[o.F2])],c)},"shared/utility/resources/value-converters/platform":(e,t,s)=>{s.r(t),s.d(t,{PLATFORM_NAMES:()=>A,PlatformIconSvgValueConverter:()=>S,PlatformNameValueConverter:()=>_,PlayIconSrcValueConverter:()=>V,platformIcons:()=>M,playIcons:()=>E});var r=s(41697),n=s(11384),i=s(21268),o=s(86508),a=s(17058),l=s(12609),c=s(83382),d=s(71230),u=s(92397),h=s(65761),p=s(43549),g=s(96674),f=s(74482),m=s(89018),w=s(45720),v=s(21537),y=s(6404),b=s(40712),C=s(92877),L=s(99501);const M=new Map([["epic",n],["gog",i],["origin",r],["rockstar",o],["standalone",a],["steam",l],["twitch",c],["uplay",d],["uwp",u],["itch",h]]),E=new Map([["epic",g],["gog",f],["origin",p],["rockstar",w],["standalone",w],["steam",v],["twitch",y],["uplay",b],["uwp",C],["itch",L],["rockstar",m]]);class S{toView(e){return k(e)?M.get(e):M.get("standalone")}}class V{toView(e){return k(e)?E.get(e):E.get("standalone")}}const A={discord:"Discord",epic:"Epic",gog:"GOG",itch:"itch.io",twitch:"twitch",origin:"EA",rockstar:"Rockstar Games",standalone:"PC",steam:"Steam",uplay:"Ubisoft",uwp:"Xbox"},k=e=>e in A;class _{toView(e){return e?A[e]||e.charAt(0).toLocaleUpperCase()+e.slice(1):""}}},"shared/utility/resources/value-converters/string":(e,t,s)=>{s.r(t),s.d(t,{AppendValueConverter:()=>l,DashCaseValueConverter:()=>n,LowerCaseValueConverter:()=>r,MaxLengthReplaceValueConverter:()=>o,NullIfEmptyValueConverter:()=>a,StrReplaceValueConverter:()=>i,TrimValueConverter:()=>c});class r{toView(e){return e.toLowerCase()}}class n{toView(e){return e.replace(/[\s_]/g,"-")}}class i{toView(e,t,s,r){return e?("string"==typeof t&&(t=new RegExp(t,r)),e.toString().replace(t,s)):e}}class o{toView(e,t,s){return e&&e.toString().length>t?s:e}}class a{toView(e){return"string"==typeof e||"number"==typeof e?e.toString():""}fromView(e){return"string"==typeof e&&e.trim()||null}}class l{toView(e,t){return e?e.toString()+t:t}}class c{toView(e){return e.trim()}}}}]);
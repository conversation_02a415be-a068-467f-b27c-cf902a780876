/*! For license information please see vendor.aurelia-store.336fbd237cfb41a2a2e8.bundle.js.LICENSE.txt */
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1906],{20770:(t,e,r)=>{r.d(e,{$b:()=>a,Pd:()=>m,_s:()=>o,il:()=>w});var n=r(72430),i=(r(27884),r(96610)),s=r(16566);Object.entries||(Object.entries=function(t){for(var e=Object.keys(t),r=e.length,n=new Array(r);r--;)n[r]=[e[r],t[e[r]]];return n});var o,a,u,c,p=function(t,e){return p=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)e.hasOwnProperty(r)&&(t[r]=e[r])},p(t,e)};function l(t,e){function r(){this.constructor=t}p(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}function h(t,e,r,n){return new(r||(r=Promise))((function(i,s){function o(t){try{u(n.next(t))}catch(t){s(t)}}function a(t){try{u(n.throw(t))}catch(t){s(t)}}function u(t){t.done?i(t.value):new r((function(e){e(t.value)})).then(o,a)}u((n=n.apply(t,e||[])).next())}))}function f(t,e){var r,n,i,s,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return s={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(s){return function(a){return function(s){if(r)throw new TypeError("Generator is already executing.");for(;o;)try{if(r=1,n&&(i=2&s[0]?n.return:s[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,s[1])).done)return i;switch(n=0,i&&(s=[2&s[0],i.value]),s[0]){case 0:case 1:i=s;break;case 4:return o.label++,{value:s[1],done:!1};case 5:o.label++,n=s[1],s=[0];continue;case 7:s=o.ops.pop(),o.trys.pop();continue;default:if(!((i=(i=o.trys).length>0&&i[i.length-1])||6!==s[0]&&2!==s[0])){o=0;continue}if(3===s[0]&&(!i||s[1]>i[0]&&s[1]<i[3])){o.label=s[1];break}if(6===s[0]&&o.label<i[1]){o.label=i[1],i=s;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(s);break}i[2]&&o.ops.pop(),o.trys.pop();continue}s=e.call(t,o)}catch(t){s=[6,t],n=0}finally{r=i=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,a])}}}function d(t,e){return y(t)?e>0?function(t,e){if(e<0||e>=t.future.length)return t;var r=t.past,n=t.future,i=t.present;return{past:r.concat([i],n.slice(0,e)),present:n[e],future:n.slice(e+1)}}(t,e-1):e<0?function(t,e){if(e<0||e>=t.past.length)return t;var r=t.past,n=t.future,i=t.present,s=r.slice(0,e),o=r.slice(e+1).concat([i],n);return{past:s,present:r[e],future:o}}(t,t.past.length+e):t:t}function y(t){return void 0!==t.present&&void 0!==t.future&&void 0!==t.past&&Array.isArray(t.future)&&Array.isArray(t.past)}function m(t,e,r){var n=r&&r.logType&&console.hasOwnProperty(r.logType)?r.logType:"log";console[n]("New state: ",t)}function g(t,e,r){return e&&t.logDefinitions&&t.logDefinitions.hasOwnProperty(e)&&t.logDefinitions[e]&&Object.values(a).includes(t.logDefinitions[e])?t.logDefinitions[e]:r}!function(t){t.Before="before",t.After="after"}(o||(o={})),function(t){t.trace="trace",t.debug="debug",t.info="info",t.log="log",t.warn="warn",t.error="error"}(a||(a={})),l((function(){return null!==c&&c.apply(this,arguments)||this}),c=i.Logger),function(t){t.StartEnd="startEnd",t.All="all"}(u||(u={}));var v=function(t){function e(e){return t.call(this,"Tried to dispatch an unregistered action "+(e&&("string"==typeof e?e:e.name)))||this}return l(e,t),e}(Error),w=function(){function t(t,e){this.initialState=t,this.logger=(0,i.getLogger)("aurelia-store"),this.devToolsAvailable=!1,this.actions=new Map,this.middlewares=new Map,this._markNames=new Set,this._measureNames=new Set,this.dispatchQueue=[],this.options=e||{};var r=this.options.history&&!0===this.options.history.undoable;this._state=new n.t(t),this.state=this._state.asObservable(),this.options.devToolsOptions&&!0===this.options.devToolsOptions.disable||this.setupDevTools(),r&&this.registerHistoryMethods()}return t.prototype.registerMiddleware=function(t,e,r){this.middlewares.set(t,{placement:e,settings:r})},t.prototype.unregisterMiddleware=function(t){this.middlewares.has(t)&&this.middlewares.delete(t)},t.prototype.isMiddlewareRegistered=function(t){return this.middlewares.has(t)},t.prototype.registerAction=function(t,e){if(0===e.length)throw new Error("The reducer is expected to have one or more parameters, where the first will be the present state");this.actions.set(e,{type:t})},t.prototype.unregisterAction=function(t){this.actions.has(t)&&this.actions.delete(t)},t.prototype.isActionRegistered=function(t){return"string"==typeof t?void 0!==Array.from(this.actions).find((function(e){return e[1].type===t})):this.actions.has(t)},t.prototype.resetToState=function(t){this._state.next(t)},t.prototype.dispatch=function(t){for(var e=[],r=1;r<arguments.length;r++)e[r-1]=arguments[r];var n=this.lookupAction(t);return n?this.queueDispatch([{reducer:n,params:e}]):Promise.reject(new v(t))},t.prototype.pipe=function(t){for(var e=this,r=[],n=1;n<arguments.length;n++)r[n-1]=arguments[n];var i=[],s={dispatch:function(){return e.queueDispatch(i)},pipe:function(r){for(var n=[],o=1;o<arguments.length;o++)n[o-1]=arguments[o];var a=e.lookupAction(r);if(!a)throw new v(t);return i.push({reducer:a,params:n}),s}};return s.pipe.apply(s,[t].concat(r))},t.prototype.lookupAction=function(t){if("string"==typeof t){var e=Array.from(this.actions).find((function(e){return e[0],e[1].type===t}));if(e)return e[0]}else if(this.actions.has(t))return t},t.prototype.queueDispatch=function(t){var e=this;return new Promise((function(r,n){e.dispatchQueue.push({actions:t,resolve:r,reject:n}),1===e.dispatchQueue.length&&e.handleQueue()}))},t.prototype.handleQueue=function(){return h(this,void 0,void 0,(function(){var t,e;return f(this,(function(r){switch(r.label){case 0:if(!(this.dispatchQueue.length>0))return[3,5];t=this.dispatchQueue[0],r.label=1;case 1:return r.trys.push([1,3,,4]),[4,this.internalDispatch(t.actions)];case 2:return r.sent(),t.resolve(),[3,4];case 3:return e=r.sent(),t.reject(e),[3,4];case 4:this.dispatchQueue.shift(),this.handleQueue(),r.label=5;case 5:return[2]}}))}))},t.prototype.internalDispatch=function(t){return h(this,void 0,void 0,(function(){var e,r,n,i,c,p,l,h,d,m,w,b,T=this;return f(this,(function(f){switch(f.label){case 0:if(e=t.find((function(t){return!T.actions.has(t.reducer)})))throw new v(e.reducer);return this.mark("dispatch-start"),r=t.map((function(t){return{type:T.actions.get(t.reducer).type,params:t.params,reducer:t.reducer}})),n={name:r.map((function(t){return t.type})).join("->"),params:r.reduce((function(t,e){return t.concat(e.params)}),[]),pipedActions:r.map((function(t){return{name:t.type,params:t.params}}))},this.options.logDispatchedActions&&this.logger[g(this.options,"dispatchedActions",a.info)]("Dispatching: "+n.name),[4,this.executeMiddlewares(this._state.getValue(),o.Before,n)];case 1:if(!1===(i=f.sent()))return this.clearMarks(),this.clearMeasures(),[2];c=i,p=0,l=r,f.label=2;case 2:return p<l.length?[4,(h=l[p]).reducer.apply(h,[c].concat(h.params))]:[3,5];case 3:if(!1===(c=f.sent()))return this.clearMarks(),this.clearMeasures(),[2];if(this.mark("dispatch-after-reducer-"+h.type),!c&&"object"!=typeof c)throw new Error("The reducer has to return a new state");f.label=4;case 4:return p++,[3,2];case 5:return[4,this.executeMiddlewares(c,o.After,n)];case 6:return!1===(d=f.sent())?(this.clearMarks(),this.clearMeasures(),[2]):(y(d)&&this.options.history&&this.options.history.limit&&(_=d,A=this.options.history.limit,y(_)&&(_.past.length>A&&(_.past=_.past.slice(_.past.length-A)),_.future.length>A&&(_.future=_.future.slice(0,A))),d=_),this._state.next(d),this.mark("dispatch-end"),this.options.measurePerformance===u.StartEnd?(this.measure("startEndDispatchDuration","dispatch-start","dispatch-end"),m=s.i9.performance.getEntriesByName("startEndDispatchDuration","measure"),this.logger[g(this.options,"performanceLog",a.info)]("Total duration "+m[0].duration+" of dispatched action "+n.name+":",m)):this.options.measurePerformance===u.All&&(w=s.i9.performance.getEntriesByType("mark"),b=w[w.length-1].startTime-w[0].startTime,this.logger[g(this.options,"performanceLog",a.info)]("Total duration "+b+" of dispatched action "+n.name+":",w)),this.clearMarks(),this.clearMeasures(),this.updateDevToolsState({type:n.name,params:n.params},d),[2])}var _,A}))}))},t.prototype.executeMiddlewares=function(t,e,r){var n=this;return Array.from(this.middlewares).filter((function(t){return t[1].placement===e})).reduce((function(t,i,s,o){return h(n,void 0,void 0,(function(){var n,s,o,a,u;return f(this,(function(c){switch(c.label){case 0:return c.trys.push([0,5,7,8]),o=(s=i)[0],[4,t];case 1:return[4,o.apply(s,[c.sent(),this._state.getValue(),i[1].settings,r])];case 2:return!1===(n=c.sent())?[2,!1]:(a=n)?[3,4]:[4,t];case 3:a=c.sent(),c.label=4;case 4:return[2,a];case 5:if(u=c.sent(),this.options.propagateError)throw u;return[4,t];case 6:return[2,c.sent()];case 7:return this.mark("dispatch-"+e+"-"+i[0].name),[7];case 8:return[2]}}))}))}),t)},t.prototype.setupDevTools=function(){var t=this;s.i9.global.devToolsExtension&&(this.logger[g(this.options,"devToolsStatus",a.debug)]("DevTools are available"),this.devToolsAvailable=!0,this.devTools=s.i9.global.__REDUX_DEVTOOLS_EXTENSION__.connect(this.options.devToolsOptions),this.devTools.init(this.initialState),this.devTools.subscribe((function(e){if(t.logger[g(t.options,"devToolsStatus",a.debug)]("DevTools sent change "+e.type),"ACTION"===e.type&&e.payload){var r=Array.from(t.actions).find((function(t){return t[0].name===e.payload.name})),n=t.lookupAction(e.payload.name)||r&&r[0];if(!n)throw new Error("Tried to remotely dispatch an unregistered action");if(!e.payload.args||e.payload.args.length<1)throw new Error("No action arguments provided");t.dispatch.apply(t,[n].concat(e.payload.args.slice(1).map((function(t){return JSON.parse(t)}))))}else if("DISPATCH"===e.type&&e.payload)switch(e.payload.type){case"JUMP_TO_STATE":case"JUMP_TO_ACTION":return void t._state.next(JSON.parse(e.state));case"COMMIT":return void t.devTools.init(t._state.getValue());case"RESET":return t.devTools.init(t.initialState),void t.resetToState(t.initialState);case"ROLLBACK":var i=JSON.parse(e.state);return t.resetToState(i),void t.devTools.init(i)}})))},t.prototype.updateDevToolsState=function(t,e){this.devToolsAvailable&&this.devTools.send(t,e)},t.prototype.registerHistoryMethods=function(){this.registerAction("jump",d)},t.prototype.mark=function(t){this._markNames.add(t),s.i9.performance.mark(t)},t.prototype.clearMarks=function(){this._markNames.forEach((function(t){return s.i9.performance.clearMarks(t)})),this._markNames.clear()},t.prototype.measure=function(t,e,r){this._measureNames.add(t),s.i9.performance.measure(t,e,r)},t.prototype.clearMeasures=function(){this._measureNames.forEach((function(t){return s.i9.performance.clearMeasures(t)})),this._measureNames.clear()},t}()}}]);
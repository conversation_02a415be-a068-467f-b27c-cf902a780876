"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[6279],{"queue/resources/elements/creators-list":(e,t,r)=>{r.r(t),r.d(t,{CreatorsList:()=>n});var s=r(15215),a=r("aurelia-framework"),i=r(61116),o=r("shared/i18n/resources/value-converters");let n=class{constructor(e,t){this.creators=e,this.i18nNumber=t}};n=(0,s.Cg)([(0,a.autoinject)(),(0,s.Sn)("design:paramtypes",[i.c,o.I18nNumberValueConverter])],n)},"queue/resources/elements/creators-list.html":(e,t,r)=>{r.r(t),r.d(t,{default:()=>o});var s=r(14385),a=r.n(s),i=new URL(r(89247),r.b);const o='<template> <require from="./creators-list.scss"></require> <div class="creators"> <div class="creator" repeat.for="creator of creators.creators"> <img src.bind="creator.avatar | cdn:{size: 48}" fallback-src="'+a()(i)+'"> <div class="meta"> <div class="username">${creator.username}</div> <div class="cheats" innerhtml.bind="(creator.gameCount === 1 ? \'creators_list.$x_game\' : \'creators_list.$x_games\') | i18n:{x: i18nNumber.toView(creator.gameCount)} | markdown"></div> </div> </div> </div> </template> '},"queue/resources/elements/creators-list.scss":(e,t,r)=>{r.r(t),r.d(t,{default:()=>n});var s=r(31601),a=r.n(s),i=r(76314),o=r.n(i)()(a());o.push([e.id,'creators-list .creator{display:flex;align-items:flex-start}creators-list .creator+.creator{margin-top:12px}creators-list .creator img{flex:0 0 auto;width:40px;height:40px;margin:0 14px 0 0;border-radius:50%;overflow:hidden}creators-list .creator .username{font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px}.theme-default creators-list .creator .username{color:#fff}.theme-purple-pro creators-list .creator .username{color:#fff}.theme-green-pro creators-list .creator .username{color:#fff}.theme-orange-pro creators-list .creator .username{color:#fff}.theme-pro creators-list .creator .username{color:#fff}creators-list .creator .cheats{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default creators-list .creator .cheats{color:rgba(255,255,255,.4)}.theme-purple-pro creators-list .creator .cheats{color:rgba(255,255,255,.4)}.theme-green-pro creators-list .creator .cheats{color:rgba(255,255,255,.4)}.theme-orange-pro creators-list .creator .cheats{color:rgba(255,255,255,.4)}.theme-pro creators-list .creator .cheats{color:rgba(255,255,255,.4)}creators-list .creator .cheats strong{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:12px;line-height:16px}.theme-default creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}.theme-purple-pro creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}.theme-green-pro creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}.theme-orange-pro creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}.theme-pro creators-list .creator .cheats strong{color:rgba(255,255,255,.6)}',""]);const n=o},"resources/custom-attributes/attach-src":(e,t,r)=>{r.r(t),r.d(t,{AttachSrcCustomAttribute:()=>i});var s=r(15215),a=r("aurelia-framework");let i=class{#e;constructor(e){this.#e=e}attached(){this.#e.src=this.value}};i=(0,s.Cg)([(0,a.inject)(Element),(0,a.noView)(),(0,s.Sn)("design:paramtypes",[Object])],i)},"resources/custom-attributes/close-if-press-escape":(e,t,r)=>{r.r(t),r.d(t,{CloseIfPressEscapeCustomAttribute:()=>i});var s=r(15215),a=r("aurelia-framework");let i=class{constructor(){this.handleKeydown=this.handleKeydown.bind(this)}bind(){document.addEventListener("keydown",this.handleKeydown)}unbind(){document.removeEventListener("keydown",this.handleKeydown)}handleKeydown(e){"Escape"===e.key&&(this.open=!1)}};(0,s.Cg)([(0,a.bindable)({defaultBindingMode:a.bindingMode.twoWay}),(0,s.Sn)("design:type",Boolean)],i.prototype,"open",void 0),i=(0,s.Cg)([(0,a.noView)(),(0,s.Sn)("design:paramtypes",[])],i)},"resources/custom-attributes/detach-el":(e,t,r)=>{r.r(t),r.d(t,{DetachElCustomAttribute:()=>i});var s=r(15215),a=r("aurelia-framework");let i=class{#e;#t;#r;constructor(e){this.#e=e,this.#t=e.parentElement}attached(){const e=this.#e.getBoundingClientRect(),t=document.querySelector("router-view")??document.body;this.#r=document.createElement("DIV"),t.appendChild(this.#r),this.#r.style.position="fixed",this.#r.style.left=`${e.left}px`,this.#r.style.top=`${e.top}px`,this.#r.style.zIndex="999",this.#r.style.width=`${e.width}px`,this.#r.style.height=`${e.height}px`,this.#r.appendChild(this.#e)}detached(){this.#r?.parentElement?.removeChild(this.#r),this.#r=null,this.#t?.appendChild(this.#e)}};i=(0,s.Cg)([(0,a.inject)(Element),(0,a.noView)(),(0,s.Sn)("design:paramtypes",[Object])],i)},"resources/custom-attributes/pro-cta":(e,t,r)=>{r.r(t),r.d(t,{ProCtaCustomAttribute:()=>c});var s=r(15215),a=r(733),i=r("aurelia-event-aggregator"),o=r("aurelia-framework"),n=r(21795),l=r(38777);let c=class{#s;#e;#a;#i;#o;constructor(e,t,r){this.feature="save_mods",this.#e=e,this.#a=t,this.#i=r}attached(){this.#s="string"==typeof this.#e.getAttribute("tabIndex"),this.disabledChanged()}bind(){this.#o=(0,l.yB)(this.#e,"click",(async()=>{this.trigger&&!this.disabled&&(this.#a.publish(new n.kK(this.trigger)),await this.#i.openDialog(this.trigger,this.feature),this.callback?.())}))}unbind(){this.#o&&(this.#o?.dispose(),this.#o=null)}disabledChanged(){this.#s||this.#e.setAttribute("tabIndex",this.disabled?"-1":"0")}};(0,s.Cg)([(0,o.bindable)({primaryProperty:!0}),(0,s.Sn)("design:type",String)],c.prototype,"trigger",void 0),(0,s.Cg)([(0,o.bindable)(),(0,s.Sn)("design:type",Boolean)],c.prototype,"disabled",void 0),(0,s.Cg)([(0,o.bindable)(),(0,s.Sn)("design:type",Function)],c.prototype,"callback",void 0),(0,s.Cg)([(0,o.bindable)(),(0,s.Sn)("design:type",String)],c.prototype,"feature",void 0),c=(0,s.Cg)([(0,o.inject)(Element,i.EventAggregator,a.V),(0,o.noView)(),(0,s.Sn)("design:paramtypes",[Element,i.EventAggregator,a.V])],c)},"resources/custom-attributes/title-link":(e,t,r)=>{r.r(t),r.d(t,{TitleLinkCustomAttribute:()=>l});var s=r(15215),a=r("aurelia-event-aggregator"),i=r("aurelia-framework"),o=r(21795),n=r(38777);let l=class{#e;#a;#o;constructor(e,t){this.#e=e,this.#a=t}bind(){this.#o=(0,n.yB)(this.#e,"click",(async()=>{this.value&&this.titleId&&this.#a.publish(new o.dY(this.value,this.titleId,this.gameId||null,this.trainerId||null,this.searchResult||!1))}))}unbind(){this.#o?.dispose(),this.#o=null}};(0,s.Cg)([(0,i.bindable)({primaryProperty:!0}),(0,s.Sn)("design:type",String)],l.prototype,"value",void 0),(0,s.Cg)([(0,i.bindable)(),(0,s.Sn)("design:type",String)],l.prototype,"titleId",void 0),(0,s.Cg)([(0,i.bindable)(),(0,s.Sn)("design:type",String)],l.prototype,"gameId",void 0),(0,s.Cg)([(0,i.bindable)(),(0,s.Sn)("design:type",String)],l.prototype,"trainerId",void 0),(0,s.Cg)([(0,i.bindable)(),(0,s.Sn)("design:type",Boolean)],l.prototype,"searchResult",void 0),l=(0,s.Cg)([(0,i.inject)(Element,a.EventAggregator),(0,i.noView)(),(0,s.Sn)("design:paramtypes",[Element,a.EventAggregator])],l)}}]);
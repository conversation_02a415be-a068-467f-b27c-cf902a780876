"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[1219,4704],{84551:(t,e,i)=>{i.d(e,{Y:()=>l});var s=i(15215),a=i("aurelia-framework"),o=i(92694);let l=class{#t;constructor(t){this.#t=t}report(t){this.#t.collect({name:"api_timing",value:t.responseTime,tags:[{key:"http.endpoint",value:t.endpoint},{key:"http.method",value:t.method},{key:"country",value:"$country"}]})}};l=(0,s.Cg)([(0,a.autoinject)(),(0,s.Sn)("design:paramtypes",[o.k])],l)},92694:(t,e,i)=>{i.d(e,{k:()=>n});var s=i(15215),a=i("aurelia-framework"),o=i(29702);const l=["host","device","source","service","env","version"];let n=class{#e;#i;#s;#a;#o;constructor(t,e=[]){this.#i=[],this.#s=!0,this.#a=100,this.#e=t,this.#o=e}disableAutoRelease(){this.#s=!1}enableAutoRelease(){this.#s=!0}setAutoReleaseCount(t){this.#a=t}collectImmediately(t){return this.#e.collectOne(this.#l(t))}collect(t){this.#i.push(this.#l(t)),this.#s&&this.#i.length>=this.#a&&this.releaseCollected()}releaseCollected(){if(0===this.#i.length)return;const t=this.#i;this.#i=[],this.#e.collectMany(t)}#l(t){const e=this.#n(t.tags||[]);return this.#r(e),{...t,tags:e}}#r(t){const e=t.filter((t=>l.includes(t.key)));if(e.length)throw new Error(`Reserved tags used: ${e}`)}#n(t){return this.#o?t.concat(this.#o):t}};n=(0,s.Cg)([(0,a.autoinject)(),(0,s.Sn)("design:paramtypes",[o.K,Array])],n)},"shared/markdown/index":(t,e,i)=>{i.r(e),i.d(e,{MarkdownValueConverter:()=>l.MarkdownValueConverter,configure:()=>n}),i("aurelia-framework");var s=i(42922),a=i.n(s),o=i(17873),l=i("shared/markdown/value-converter");function n(t,e){t.container.registerHandler(a(),(0,o.R)((()=>function(t){const e=a()(t??{html:!1,breaks:!0}),i=e.validateLink.bind(e);return e.validateLink=t=>/^data:image\/svg\+xml;/.test(t)||i(t),e}(e)))),t.globalResources(["./value-converter"])}},"shared/markdown/value-converter":(t,e,i)=>{i.r(e),i.d(e,{MarkdownValueConverter:()=>n});var s=i(15215),a=i("aurelia-framework"),o=i(42922),l=i.n(o);let n=class{#d;constructor(t){this.#d=t}toView(t,e=!0){if("string"==typeof t){t=t.replace(/\\n/g,"\n");let i=e?this.#d.renderInline(t):this.#d.render(t);return e&&(i=i.replace(/\n/g,"<br>")),i}return""}};n=(0,s.Cg)([(0,a.autoinject)(),(0,s.Sn)("design:paramtypes",[l()])],n)},"shared/metrics/index":(t,e,i)=>{i.r(e),i.d(e,{configure:()=>o});var s=i(29702),a=i(92694);function o(t,e){t.container.registerSingleton(a.k,(function(){const i=new a.k(t.container.get(s.K),e.tags);return!1===e.autoReleaseEnabled&&i.disableAutoRelease(),e.autoReleaseCount&&i.setAutoReleaseCount(e.autoReleaseCount),i}))}},"shared/pro-promos/dummy-number-input":(t,e,i)=>{i.r(e),i.d(e,{DummyNumberInput:()=>s});class s{}},"shared/pro-promos/dummy-number-input.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>s});const s='<template> <require from="./dummy-number-input.scss"></require> <div class="dummy-number-input"> <div class="dummy-number-input-text-box">500</div> <div class="dummy-number-input-slider"> <div class="dummy-number-input-handle"></div> </div> <div class="dummy-number-input-check"></div> </div> </template> '},"shared/pro-promos/dummy-number-input.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>u});var s=i(31601),a=i.n(s),o=i(76314),l=i.n(o),n=i(4417),r=i.n(n),d=new URL(i(83959),i.b),p=l()(a()),m=r()(d);p.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,dummy-number-input .dummy-number-input-check{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}dummy-number-input{--border-radius: 8px}dummy-number-input .dummy-number-input{font-weight:500;color:var(--theme--text-primary);height:26px;gap:6px;display:flex;align-items:center}dummy-number-input .dummy-number-input-text-box{padding:2px 6px;height:26px;border-radius:var(--border-radius);font-size:11px;line-height:22px;width:75px;background:rgba(255,255,255,.2)}dummy-number-input .dummy-number-input-slider{content:"";position:relative;display:flex;border-radius:var(--border-radius);width:75px;height:100%;background:rgba(255,255,255,.2);overflow:hidden}dummy-number-input .dummy-number-input-slider::before{content:"";position:absolute;background-color:#fa1280;height:100%;width:50%}dummy-number-input .dummy-number-input-slider::after{content:"";filter:drop-shadow(0px 1.735px 3.471px rgba(16, 24, 40, 0.06)) drop-shadow(0px 1.735px 5.206px rgba(16, 24, 40, 0.1));position:absolute;background-color:#fff;border-radius:var(--border-radius);height:100%;width:12px;left:calc(50% - 6px)}dummy-number-input .dummy-number-input-slider .dummy-number-input-handle{position:absolute;height:calc(100% - 16px);width:4px;left:calc(50% - 2px);top:8px;content:"";border-left:1px solid #fa1280;border-right:1px solid #fa1280;z-index:1}dummy-number-input .dummy-number-input-check{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;padding:4px;background-color:#fa1280;border-radius:var(--border-radius);font-size:18px;display:flex}dummy-number-input .dummy-number-input-check:before{font-family:inherit;content:"check"}`,""]);const u=p},"shared/pro-promos/dummy-toggle":(t,e,i)=>{i.r(e),i.d(e,{DummyToggle:()=>s});class s{}},"shared/pro-promos/dummy-toggle.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>s});const s="<template> <require from=\"./dummy-toggle.scss\"></require> <div class=\"pro-showcase-dummy-toggle\"> <span class=\"pro-showcase-dummy-toggle-off\">${'toggle.off' | i18n | maxLengthReplace:3:'|'}</span> <span class=\"pro-showcase-dummy-toggle-on\">${'toggle.on' | i18n | maxLengthReplace:3:'|'}</span> </div> </template> "},"shared/pro-promos/dummy-toggle.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>n});var s=i(31601),a=i.n(s),o=i(76314),l=i.n(o)()(a());l.push([t.id,"dummy-toggle .pro-showcase-dummy-toggle{color:var(--theme--text-primary);font-weight:500;line-height:22px;font-size:11px;background:rgba(255,255,255,.2);border-radius:28px;padding:2px;display:flex;align-items:center}dummy-toggle .pro-showcase-dummy-toggle-off,dummy-toggle .pro-showcase-dummy-toggle-on{padding:0px 14px}dummy-toggle .pro-showcase-dummy-toggle-on{border-radius:14px;background:#fa1280;color:#fff}",""]);const n=l},"shared/pro-promos/game-guide-illustration":(t,e,i)=>{i.r(e),i.d(e,{GameGuideIllustration:()=>a});var s=i(38951);class a{constructor(){this.showCursor=!0,this.cursorSubscription=null}attached(){this.cursorSubscription=(0,s.Y)(750).subscribe((()=>{this.showCursor=!this.showCursor}))}detached(){this.cursorSubscription?.unsubscribe()}}},"shared/pro-promos/game-guide-illustration.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>n});var s=i(14385),a=i.n(s),o=new URL(i(53444),i.b),l=new URL(i(80086),i.b);const n='<template> <require from="./game-guide-illustration.scss"></require> <div class="game-guide-illustration"> <div class="game-guide-illustration-chat"> <div class="game-guide-illustration-chat-message"> <img class="game-guide-illustration-avatar" src="'+a()(o)+'" alt="${\'pro_showcase.game_guide_illustration_example_avatar_alt\' | i18n}"> <span class="game-guide-illustration-chat-content"> ${\'pro_showcase.game_guide_illustration_question\' | i18n} </span> </div> <div class="game-guide-illustration-chat-message"> <img class="game-guide-illustration-avatar" src="'+a()(l)+'" alt="${\'pro_showcase.game_guide_illustration_assistant_avatar_alt\' | i18n}"> <span class="game-guide-illustration-chat-content"> ${\'pro_showcase.game_guide_illustration_response\' | i18n} </span> <div class="game-guide-illustration-chat-icons"> <span class="thumbs-up"></span> <span class="thumbs-down"></span> <span class="feedback"></span> </div> </div> <div class="game-guide-illustration-pending-question"> ${\'pro_showcase.game_guide_illustration_pending_question\' | i18n} ${showCursor ? \'|\' : \'\'} <span class="game-guide-illustration-dummy-send-button"> </span> </div> </div> </div> </template> '},"shared/pro-promos/game-guide-illustration.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>u});var s=i(31601),a=i.n(s),o=i(76314),l=i.n(o),n=i(4417),r=i.n(n),d=new URL(i(83959),i.b),p=l()(a()),m=r()(d);p.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-up,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-up,game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-down,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-down,game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .feedback,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .feedback,game-guide-illustration .game-guide-illustration-dummy-send-button{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}game-guide-illustration .game-guide-illustration{display:flex;flex-direction:column;gap:12px;background:rgba(96,70,255,.1);backdrop-filter:blur(30px);width:376px;max-height:398px;overflow:hidden;padding:20px;border-radius:20px}game-guide-illustration .game-guide-illustration-avatar{width:32px;height:32px;border-radius:50%}game-guide-illustration .game-guide-illustration-chat,game-guide-illustration .game-guide-illustration-message{display:flex;flex-direction:column;gap:12px}game-guide-illustration .game-guide-illustration-chat-message,game-guide-illustration .game-guide-illustration-message-message{display:flex;flex-direction:column;gap:12px}game-guide-illustration .game-guide-illustration-chat-message:first-of-type,game-guide-illustration .game-guide-illustration-message-message:first-of-type{margin-bottom:12px}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons{display:flex;align-items:center;gap:4px;padding:4px}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-up,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-up{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-secondary)}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-up:before,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-up:before{font-family:inherit;content:"thumb_up"}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-down,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-down{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-secondary)}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .thumbs-down:before,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .thumbs-down:before{font-family:inherit;content:"thumb_down"}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .feedback,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .feedback{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-secondary)}game-guide-illustration .game-guide-illustration-chat-message .game-guide-illustration-chat-icons .feedback:before,game-guide-illustration .game-guide-illustration-message-message .game-guide-illustration-chat-icons .feedback:before{font-family:inherit;content:"article"}game-guide-illustration .game-guide-illustration-chat-content,game-guide-illustration .game-guide-illustration-message-content{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-primary);max-height:120px;overflow:hidden}game-guide-illustration .game-guide-illustration-pending-question{color:var(--theme--text-primary);display:flex;gap:8px;border-radius:12px;background:linear-gradient(0deg, rgba(96, 70, 255, 0.1) 0%, rgba(96, 70, 255, 0.1) 100%),rgba(255,255,255,.1);backdrop-filter:blur(25px);padding:8px 8px 8px 16px;align-items:end}game-guide-illustration .game-guide-illustration-dummy-send-button{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;color:var(--theme--text-highlight);height:min-content;display:flex;align-items:center;padding:4px;border-radius:28px;background:#6046ff}game-guide-illustration .game-guide-illustration-dummy-send-button:before{font-family:inherit;content:"arrow_upward"}`,""]);const u=p},"shared/pro-promos/pin-mods-illustration":(t,e,i)=>{i.r(e),i.d(e,{PinModsIllustration:()=>s});class s{}},"shared/pro-promos/pin-mods-illustration.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>r});var s=i(14385),a=i.n(s),o=new URL(i(77103),i.b),l=new URL(i(54626),i.b),n=a()(o);const r='<template> <require from="./pin-mods-illustration.scss"></require> <div class="pin-mods-illustration"> <div class="pin-mods-illustration-row-header"> <span class="pin-mods-illustration-pin-header"></span> <span class="pin-mods-illustration-mod-name">${\'pro_showcase.pinned\' | i18n}</span> <span class="pin-mods-illustration-collapse"></span> </div> <div class="pin-mods-illustration-row"> <span class="pin-mods-illustration-bolt"> <inline-svg src="'+n+'"></inline-svg> </span> <span class="pin-mods-illustration-mod-name">${\'pro_showcase.demo_mod_1\' | i18n}</span> <span class="pin-mods-illustration-pin-button"> <span class="pin-mods-illustration-pointer"> <inline-svg src="'+a()(l)+'"></inline-svg> </span> </span> </div> <div class="pin-mods-illustration-row"> <span class="pin-mods-illustration-bolt"> <inline-svg src="'+n+'"></inline-svg> </span> <span class="pin-mods-illustration-mod-name">${\'pro_showcase.demo_mod_2\' | i18n}</span> <span class="pin-mods-illustration-pin"></span> </div> <div class="pin-mods-illustration-row"> <span class="pin-mods-illustration-bolt"> <inline-svg src="'+n+'"></inline-svg> </span> <span class="pin-mods-illustration-mod-name">${\'pro_showcase.demo_mod_3\' | i18n}</span> <span class="pin-mods-illustration-pin"></span> </div> </div> </template> '},"shared/pro-promos/pin-mods-illustration.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>u});var s=i(31601),a=i.n(s),o=i(76314),l=i.n(o),n=i(4417),r=i.n(n),d=new URL(i(83959),i.b),p=l()(a()),m=r()(d);p.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,pin-mods-illustration .pin-mods-illustration-collapse,pin-mods-illustration .pin-mods-illustration-pin,pin-mods-illustration .pin-mods-illustration-pin-button,pin-mods-illustration .pin-mods-illustration-pin-header{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}pin-mods-illustration .pin-mods-illustration{display:flex;flex-direction:column;align-items:center;box-shadow:0px 0px 69.011px 0px rgba(255,255,255,.08) inset;backdrop-filter:blur(22px);border-radius:16px;width:376px}pin-mods-illustration .pin-mods-illustration-mod-name{flex-grow:1}pin-mods-illustration .pin-mods-illustration-bolt{display:inline-flex;color:var(--theme--text-disabled)}pin-mods-illustration .pin-mods-illustration-bolt svg{width:21px;height:21px}pin-mods-illustration .pin-mods-illustration-collapse{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-size:16px;color:rgba(255,255,255,.03)}pin-mods-illustration .pin-mods-illustration-collapse:before{font-family:inherit;content:"unfold_less"}pin-mods-illustration .pin-mods-illustration-pin,pin-mods-illustration .pin-mods-illustration-pin-button,pin-mods-illustration .pin-mods-illustration-pin-header{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;font-size:21px}pin-mods-illustration .pin-mods-illustration-pin:before,pin-mods-illustration .pin-mods-illustration-pin-button:before,pin-mods-illustration .pin-mods-illustration-pin-header:before{font-family:inherit;content:"keep"}pin-mods-illustration .pin-mods-illustration-pin-header,pin-mods-illustration .pin-mods-illustration-pin-button{color:var(--theme--text-highlight)}pin-mods-illustration .pin-mods-illustration-pin,pin-mods-illustration .pin-mods-illustration-pin-button{padding:5px}pin-mods-illustration .pin-mods-illustration-pin{color:rgba(255,255,255,.03)}pin-mods-illustration .pin-mods-illustration-bolt,pin-mods-illustration .pin-mods-illustration-pin-header{margin-right:10px}pin-mods-illustration .pin-mods-illustration-pin-button{position:relative;border-radius:10px;background:rgba(255,255,255,.05);box-shadow:0px 4.564px 64.111px 0px rgba(0,0,0,.25),0px 0px 34.006px 0px rgba(255,255,255,.8)}pin-mods-illustration .pin-mods-illustration-pointer{position:absolute;width:20px;height:20px;filter:drop-shadow(0px 2.715px 4.886px rgba(19, 24, 28, 0.5));right:-6px;bottom:-6px}pin-mods-illustration .pin-mods-illustration-row,pin-mods-illustration .pin-mods-illustration-row-header{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:18px;line-height:28px;line-height:26px;color:var(--theme--text-highlight);display:flex;align-items:center;padding:10px 16px 10px 18px;width:100%;background:linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%),rgba(255,255,255,.2)}pin-mods-illustration .pin-mods-illustration-row-body-wrapper{display:flex;flex-direction:column;align-items:center;width:100%}pin-mods-illustration .pin-mods-illustration-row-header{font-weight:700;border-radius:16px 16px 0 0;margin-bottom:1px}pin-mods-illustration .pin-mods-illustration-row:last-child{border-radius:0 0 16px 16px}`,""]);const u=p},"shared/pro-promos/precision-mods-illustration.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>s});const s='<template> <require from="./precision-mods-illustration.scss"></require> <div class="precision-mods-illustration"> <svg width="388" height="234" viewBox="0 0 388 234" fill="none" xmlns="http://www.w3.org/2000/svg"> <g filter="url(#filter0_bi_16842_6685)"> <g clip-path="url(#clip0_16842_6685)"> <rect width="388" height="234" rx="20" fill="white" fill-opacity="0.01"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="500" letter-spacing="0px"> <tspan x="14" y="31.0909">${\'pro_showcase.unlimited_health\' | i18n}</tspan> </text> <rect x="203" y="12" width="100" height="28" rx="14" fill="white" fill-opacity="0.2"/> <rect x="205" y="14" width="48" height="24" rx="12" fill="white" fill-opacity="0.2"/> <text fill="white" fill-opacity="0.75" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="219.959" y="30.3636">${\'pro_showcase.off\' | i18n}</tspan> </text> <text fill="white" fill-opacity="0.5" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="268.803" y="30.3636">${\'pro_showcase.on\' | i18n}</tspan> </text> <rect x="315" y="12" width="61" height="28" rx="8" fill="white"/> <mask id="mask0_16842_6685" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="323" y="18" width="16" height="16"> <rect x="323" y="18" width="16" height="16" fill="#D9D9D9"/> </mask> <g mask="url(#mask0_16842_6685)"> <path d="M334.833 31.5C334.133 31.5 333.542 31.2583 333.058 30.775C332.575 30.2917 332.333 29.7 332.333 29C332.333 28.3 332.575 27.7083 333.058 27.225C333.542 26.7417 334.133 26.5 334.833 26.5C335.533 26.5 336.125 26.7417 336.608 27.225C337.092 27.7083 337.333 28.3 337.333 29C337.333 29.7 337.092 30.2917 336.608 30.775C336.125 31.2583 335.533 31.5 334.833 31.5ZM325.667 29.6667V28.3333H331V29.6667H325.667ZM327.167 25.5C326.467 25.5 325.875 25.2583 325.392 24.775C324.908 24.2917 324.667 23.7 324.667 23C324.667 22.3 324.908 21.7083 325.392 21.225C325.875 20.7417 326.467 20.5 327.167 20.5C327.867 20.5 328.458 20.7417 328.942 21.225C329.425 21.7083 329.667 22.3 329.667 23C329.667 23.7 329.425 24.2917 328.942 24.775C328.458 25.2583 327.867 25.5 327.167 25.5ZM331 23.6667V22.3333H336.333V23.6667H331Z" fill="#0060FF"/> </g> <text fill="#0060FF" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-style="italic" font-weight="900" letter-spacing="-0.5px"> <tspan x="343" y="30.3636">${\'pro_showcase.pro\' | i18n}</tspan> </text> <mask id="path-13-inside-1_16842_6685" fill="white"> <path fill-rule="evenodd" clip-rule="evenodd" d="M321 6C314.373 6 309 11.3726 309 18V40C309 46.6274 303.627 52 297 52H20C12.268 52 6 58.268 6 66V214C6 221.732 12.268 228 20 228H368C375.732 228 382 221.732 382 214V52V18C382 11.3726 376.627 6 370 6H321Z"/> </mask> <g filter="url(#filter1_i_16842_6685)"> <path fill-rule="evenodd" clip-rule="evenodd" d="M321 6C314.373 6 309 11.3726 309 18V40C309 46.6274 303.627 52 297 52H20C12.268 52 6 58.268 6 66V214C6 221.732 12.268 228 20 228H368C375.732 228 382 221.732 382 214V52V18C382 11.3726 376.627 6 370 6H321Z" fill="white" fill-opacity="0.2"/> </g> <path d="M309.5 40V18H308.5V40H309.5ZM20 52.5H297V51.5H20V52.5ZM6.5 66C6.5 58.5442 12.5442 52.5 20 52.5V51.5C11.9919 51.5 5.5 57.9919 5.5 66H6.5ZM6.5 214V66H5.5V214H6.5ZM20 227.5C12.5442 227.5 6.5 221.456 6.5 214H5.5C5.5 222.008 11.9919 228.5 20 228.5V227.5ZM368 227.5H20V228.5H368V227.5ZM381.5 214C381.5 221.456 375.456 227.5 368 227.5V228.5C376.008 228.5 382.5 222.008 382.5 214H381.5ZM381.5 52V214H382.5V52H381.5ZM381.5 18V52H382.5V18H381.5ZM321 6.5H370V5.5H321V6.5ZM382.5 18C382.5 11.0964 376.904 5.5 370 5.5V6.5C376.351 6.5 381.5 11.6487 381.5 18H382.5ZM309.5 18C309.5 11.6487 314.649 6.5 321 6.5V5.5C314.096 5.5 308.5 11.0964 308.5 18H309.5ZM308.5 40C308.5 46.3513 303.351 51.5 297 51.5V52.5C303.904 52.5 309.5 46.9036 309.5 40H308.5Z" fill="white" fill-opacity="0.15" style="mix-blend-mode:overlay" mask="url(#path-13-inside-1_16842_6685)"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="20" y="80.3636">${\'pro_showcase.refill_health\' | i18n}</tspan> </text> <rect x="323" y="62" width="53" height="28" rx="8" fill="white"/> <text fill="#0060FF" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-style="italic" font-weight="900" letter-spacing="-0.5px"> <tspan x="335" y="80.3636">${\'pro_showcase.refill\' | i18n}</tspan> </text> <g filter="url(#filter2_d_16842_6685)"> <rect x="315" y="12" width="61" height="28" rx="8" fill="white"/> <mask id="mask1_16842_6685" style="mask-type:alpha" maskUnits="userSpaceOnUse" x="323" y="18" width="16" height="16"> <rect x="323" y="18" width="16" height="16" fill="#D9D9D9"/> </mask> <g mask="url(#mask1_16842_6685)"> <path d="M334.833 31.5C334.133 31.5 333.542 31.2583 333.058 30.775C332.575 30.2917 332.333 29.7 332.333 29C332.333 28.3 332.575 27.7083 333.058 27.225C333.542 26.7417 334.133 26.5 334.833 26.5C335.533 26.5 336.125 26.7417 336.608 27.225C337.092 27.7083 337.333 28.3 337.333 29C337.333 29.7 337.092 30.2917 336.608 30.775C336.125 31.2583 335.533 31.5 334.833 31.5ZM325.667 29.6667V28.3333H331V29.6667H325.667ZM327.167 25.5C326.467 25.5 325.875 25.2583 325.392 24.775C324.908 24.2917 324.667 23.7 324.667 23C324.667 22.3 324.908 21.7083 325.392 21.225C325.875 20.7417 326.467 20.5 327.167 20.5C327.867 20.5 328.458 20.7417 328.942 21.225C329.425 21.7083 329.667 22.3 329.667 23C329.667 23.7 329.425 24.2917 328.942 24.775C328.458 25.2583 327.867 25.5 327.167 25.5ZM331 23.6667V22.3333H336.333V23.6667H331Z" fill="#0060FF"/> </g> <text fill="#0060FF" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-style="italic" font-weight="900" letter-spacing="-0.5px"> <tspan x="343" y="30.3636">${\'pro_showcase.pro\' | i18n}</tspan> </text> </g> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="20" y="124.364">${\'pro_showcase.set_max_health\' | i18n}</tspan> </text> <path d="M204 114C204 109.582 207.582 106 212 106H234V134H212C207.582 134 204 130.418 204 126V114Z" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="900" letter-spacing="-0.5px"> <tspan x="212.321" y="125.091">←</tspan> </text> <rect width="110" height="28" transform="translate(235 106)" fill="white" fill-opacity="0.3"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-style="italic" font-weight="900" letter-spacing="-0.5px"> <tspan x="276.415" y="123.591">${\'pro_showcase.one_and_a_half_times\' | i18n}</tspan> </text> <path d="M239 129.5C238.448 129.5 238 129.948 238 130.5C238 131.052 238.448 131.5 239 131.5L239 129.5ZM239 131.5L258.2 131.5L258.2 129.5L239 129.5L239 131.5Z" fill="white" fill-opacity="0.4"/> <path d="M259.7 130.5L278.9 130.5" stroke="white" stroke-opacity="0.4" stroke-width="2"/> <path d="M280.4 130.5L299.6 130.5" stroke="white" stroke-width="2"/> <path d="M301.1 130.5L320.3 130.5" stroke="white" stroke-opacity="0.4" stroke-width="2"/> <path d="M341 131.5C341.552 131.5 342 131.052 342 130.5C342 129.948 341.552 129.5 341 129.5L341 131.5ZM321.8 131.5L341 131.5L341 129.5L321.8 129.5L321.8 131.5Z" fill="white" fill-opacity="0.4"/> <path d="M346 106H368C372.418 106 376 109.582 376 114V126C376 130.418 372.418 134 368 134H346V106Z" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="900" letter-spacing="-0.5px"> <tspan x="354.321" y="125.091">→</tspan> </text> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="20" y="168.364">${\'pro_showcase.regeneration_rate\' | i18n}</tspan> </text> <path d="M204 158C204 153.582 207.582 150 212 150H234V178H212C207.582 178 204 174.418 204 170V158Z" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="900" letter-spacing="-0.5px"> <tspan x="212.321" y="169.091">←</tspan> </text> <rect width="110" height="28" transform="translate(235 150)" fill="white" fill-opacity="0.3"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-style="italic" font-weight="900" letter-spacing="-0.5px"> <tspan x="281.548" y="167.591">${\'pro_showcase.two_times\' | i18n}</tspan> </text> <path d="M239 173.5C238.448 173.5 238 173.948 238 174.5C238 175.052 238.448 175.5 239 175.5L239 173.5ZM239 175.5L258.2 175.5L258.2 173.5L239 173.5L239 175.5Z" fill="white" fill-opacity="0.4"/> <path d="M259.7 174.5L278.9 174.5" stroke="white" stroke-opacity="0.4" stroke-width="2"/> <path d="M280.4 174.5L299.6 174.5" stroke="white" stroke-opacity="0.4" stroke-width="2"/> <path d="M301.1 174.5L320.3 174.5" stroke="white" stroke-width="2"/> <path d="M341 175.5C341.552 175.5 342 175.052 342 174.5C342 173.948 341.552 173.5 341 173.5L341 175.5ZM321.8 175.5L341 175.5L341 173.5L321.8 173.5L321.8 175.5Z" fill="white" fill-opacity="0.4"/> <path d="M346 150H368C372.418 150 376 153.582 376 158V170C376 174.418 372.418 178 368 178H346V150Z" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="900" letter-spacing="-0.5px"> <tspan x="354.321" y="169.091">→</tspan> </text> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="12" font-weight="500" letter-spacing="0px"> <tspan x="20" y="212.364">${\'pro_showcase.regeneration_delay\' | i18n}</tspan> </text> <path d="M204 202C204 197.582 207.582 194 212 194H234V222H212C207.582 222 204 218.418 204 214V202Z" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="900" letter-spacing="-0.5px"> <tspan x="212.321" y="213.091">←</tspan> </text> <rect width="110" height="28" transform="translate(235 194)" fill="white" fill-opacity="0.3"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-style="italic" font-weight="900" letter-spacing="-0.5px"> <tspan x="274.815" y="211.591">${\'pro_showcase.half_times\' | i18n}</tspan> </text> <path d="M239 217.5C238.448 217.5 238 217.948 238 218.5C238 219.052 238.448 219.5 239 219.5L239 217.5ZM239 219.5L258.2 219.5L258.2 217.5L239 217.5L239 219.5Z" fill="white" fill-opacity="0.4"/> <path d="M259.7 218.5L278.9 218.5" stroke="white" stroke-width="2"/> <path d="M280.4 218.5L299.6 218.5" stroke="white" stroke-opacity="0.4" stroke-width="2"/> <path d="M301.1 218.5L320.3 218.5" stroke="white" stroke-opacity="0.4" stroke-width="2"/> <path d="M341 219.5C341.552 219.5 342 219.052 342 218.5C342 217.948 341.552 217.5 341 217.5L341 219.5ZM321.8 219.5L341 219.5L341 217.5L321.8 217.5L321.8 219.5Z" fill="white" fill-opacity="0.4"/> <path d="M346 194H368C372.418 194 376 197.582 376 202V214C376 218.418 372.418 222 368 222H346V194Z" fill="white" fill-opacity="0.15"/> <text fill="white" xml:space="preserve" style="white-space:pre" font-family="Inter" font-size="14" font-weight="900" letter-spacing="-0.5px"> <tspan x="354.321" y="213.091">→</tspan> </text> </g> <rect x="0.25" y="0.25" width="387.5" height="233.5" rx="19.75" stroke="white" stroke-opacity="0.1" stroke-width="0.5" style="mix-blend-mode:overlay"/> </g> <defs> <filter id="filter0_bi_16842_6685" x="-40" y="-40" width="468" height="314" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fegaussianblur in="BackgroundImageFix" stdDeviation="20"/> <fecomposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_16842_6685"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_16842_6685" result="shape"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="20"/> <fecomposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/> <fecolormatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.4 0"/> <feblend mode="normal" in2="shape" result="effect2_innerShadow_16842_6685"/> </filter> <filter id="filter1_i_16842_6685" x="6" y="6" width="376" height="222" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <feblend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="4"/> <fecomposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/> <fecolormatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.3 0"/> <feblend mode="soft-light" in2="shape" result="effect1_innerShadow_16842_6685"/> </filter> <filter id="filter2_d_16842_6685" x="303" y="0" width="85" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB"> <feflood flood-opacity="0" result="BackgroundImageFix"/> <fecolormatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/> <feoffset/> <fegaussianblur stdDeviation="6"/> <fecomposite in2="hardAlpha" operator="out"/> <fecolormatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.55 0"/> <feblend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_16842_6685"/> <feblend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_16842_6685" result="shape"/> </filter> <clippath id="clip0_16842_6685"> <rect width="388" height="234" rx="20" fill="white"/> </clippath> </defs> </svg> </div> </template> '},"shared/pro-promos/precision-mods-illustration.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>n});var s=i(31601),a=i.n(s),o=i(76314),l=i.n(o)()(a());l.push([t.id,"precision-mods-illustration .precision-mods-illustration svg{backdrop-filter:blur(10px);border-radius:20px}",""]);const n=l},"shared/pro-promos/save-mods-illustration":(t,e,i)=>{i.r(e),i.d(e,{SaveModsIllustration:()=>s});class s{constructor(){}}},"shared/pro-promos/save-mods-illustration.html":(t,e,i)=>{i.r(e),i.d(e,{default:()=>n});var s=i(14385),a=i.n(s),o=new URL(i(77103),i.b),l=a()(o);const n='<template> <require from="./save-mods-illustration.scss"></require> <require from="./dummy-toggle"></require> <require from="./dummy-number-input"></require> <div class="save-mods-illustration"> <div class="save-mods-illustration-button"> <div class="save-mods-illustration-button-toggle"> <span> <inline-svg src="'+l+'"></inline-svg> </span> </div> ${\'pro_showcase.save_mods\' | i18n} </div> <div class="save-mods-illustration-list"> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-user"></span> <span>${\'pro_showcase.save_mods_mods\' | i18n}</span> </div> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-bolt"> <inline-svg src="'+l+'"></inline-svg> </span> <span class="save-mods-illustration-mod-name">${\'pro_showcase.demo_mod_1\' | i18n}</span> <dummy-toggle></dummy-toggle> </div> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-bolt"> <inline-svg src="'+l+'"></inline-svg> </span> <span class="save-mods-illustration-mod-name">${\'pro_showcase.demo_mod_2\' | i18n}</span> <dummy-toggle></dummy-toggle> </div> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-bolt"> <inline-svg src="'+l+'"></inline-svg> </span> <span class="save-mods-illustration-mod-name">${\'pro_showcase.demo_mod_3\' | i18n}</span> <dummy-toggle></dummy-toggle> </div> <div class="save-mods-illustration-list-row"> <span class="save-mods-illustration-list-bolt"> <inline-svg src="'+l+'"></inline-svg> </span> <span class="save-mods-illustration-mod-name">${\'pro_showcase.demo_mod_4\' | i18n}</span> <dummy-number-input></dummy-number-input> </div> </div> </div> </template> '},"shared/pro-promos/save-mods-illustration.scss":(t,e,i)=>{i.r(e),i.d(e,{default:()=>u});var s=i(31601),a=i.n(s),o=i(76314),l=i.n(o),n=i(4417),r=i.n(n),d=new URL(i(83959),i.b),p=l()(a()),m=r()(d);p.push([t.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${m}) format("woff2")}.material-symbols-outlined,save-mods-illustration .save-mods-illustration-list-user{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}save-mods-illustration .save-mods-illustration{display:flex;flex-direction:column;align-items:center;--video-overlay-bg: linear-gradient(0deg, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0.2) 100%), var(--white-20, rgba(255, 255, 255, 0.2))}save-mods-illustration .save-mods-illustration-button{font-weight:800;font-feature-settings:"liga" off,"clig" off;font-size:24px;line-height:28px;letter-spacing:-1px;font-weight:700;color:var(--theme--text-highlight);display:flex;align-items:center;gap:16px;border-radius:100px;padding:16px 28px 16px 20px;background:var(--video-overlay-bg);backdrop-filter:blur(22px)}save-mods-illustration .save-mods-illustration-button-toggle{display:flex;align-items:center;justify-content:flex-end;background-color:#fa1280;border-radius:100px;width:62px;padding:4px}save-mods-illustration .save-mods-illustration-button-toggle span{display:inline-flex;padding:6px;background-color:#fff;border-radius:100px;filter:drop-shadow(0px 1.735px 3.471px rgba(16, 24, 40, 0.06)) drop-shadow(0px 1.735px 5.206px rgba(16, 24, 40, 0.1));color:#fa1280}save-mods-illustration .save-mods-illustration-button-toggle span svg{width:20px;height:20px}save-mods-illustration .save-mods-illustration-mod-name{flex-grow:1}save-mods-illustration .save-mods-illustration-list{box-shadow:0px 0px 69.011px 0px rgba(255,255,255,.08) inset;backdrop-filter:blur(22px);border-radius:16px;overflow:hidden;margin-top:16px;width:376px}save-mods-illustration .save-mods-illustration-list-bolt{display:inline-flex;color:#fa1280}save-mods-illustration .save-mods-illustration-list-bolt svg{width:16px;height:16px}save-mods-illustration .save-mods-illustration-list-user{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;font-variation-settings:"FILL" 1,"wght" 400 !important;color:var(--theme--text-highlight);font-size:16px}save-mods-illustration .save-mods-illustration-list-user:before{font-family:inherit;content:"person"}save-mods-illustration .save-mods-illustration-list-bolt,save-mods-illustration .save-mods-illustration-list-user{margin-right:10px}save-mods-illustration .save-mods-illustration-list-row{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;color:var(--theme--text-highlight);display:flex;align-items:center;background:var(--video-overlay-bg);padding:8px 12px}save-mods-illustration .save-mods-illustration-list-row:nth-child(1){font-weight:700;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:-0.5px;border-radius:16px 16px 0 0;margin-bottom:1px}save-mods-illustration .save-mods-illustration-list-row:last-child{border-radius:0 0 16px 16px}`,""]);const u=p}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[211,5913],{30770:(e,i,o)=>{o.d(i,{f:()=>d});var t=o(15215),r=o("aurelia-framework"),a=o(62914),n=o(78576),s=o("dialogs/fullscreen-webview-dialog"),l=o(38110);let d=class{#e;#i;#o;constructor(e,i,o){this.#e=e,this.#i=i,this.#o=o}async open(e){const i={frequency:e?.selectedPlan?e.selectedPlan?.recurring?.frequency:e?.frequency,nonInteraction:e?.nonInteraction,trigger:e?.trigger,discountCode:e?.discountCode};this.#i.event("pro_popup",{popupId:"pro",...i});const o=await this.#e.open({route:"checkout-flow",params:i,styles:{background:(0,l.IX)("secondary-background")}});o&&!o.wasCancelled&&this.#o.check(),this.#i.event("pro_popup_close",{})}};d=(0,t.Cg)([(0,r.autoinject)(),(0,t.Sn)("design:paramtypes",[s.FullscreenWebviewDialogService,a.j0,n.G])],d)},"dialogs/pro-onboarding-dialog":(e,i,o)=>{o.r(i),o.d(i,{ProOnboardingDialog:()=>h,ProOnboardingDialogService:()=>u});var t=o(15215),r=o("aurelia-dialog"),a=o("aurelia-framework"),n=o(78268),s=o(62914),l=o(98300),d=o(85805),g=o(19072),c=o(54995),p=o(70236),b=o(38777),m=o(23218);let h=class{#t;#r;#i;#a;#n;constructor(e,i,o,t,r,a){this.controller=e,this.remote=i,this.remoteHasConnected=!1,this.saveCheatsLinkClicked=!1,this.boostsLinkClicked=!1,this.#n=null,this.#r=o,this.#i=t,this.#a=r,this.host=a}async activate(e){this.config=e,this.#i.event("pro_onboarding_open",{trigger:this.config.trigger},s.Io)}attached(){this.remoteHasConnected=this.remote.status===d.t.Connected,this.#t=(new b.Vd).push(this.remote.onStatusChanged((e=>{e===d.t.Connected&&(this.remoteHasConnected=!0)}))),this.#s(),this.#n=setInterval((()=>this.#s()),5e3)}detached(){this.#t?.dispose(),this.#t=null,clearInterval(this.#n),this.#n=null}deactivate(e){const i=e.wasCancelled&&!e.output?"escape":e.output;this.#i.event("pro_onboarding_dismiss",{method:i,overlayStatus:this.overlayStatus,remoteHasConnected:this.remoteHasConnected,discordIsConnected:this.discordIsConnected,saveModsLinkClicked:this.saveCheatsLinkClicked,boostsLinkClicked:this.boostsLinkClicked,theme:this.theme},s.Io)}async#s(){this.overlayStatus=await this.#r.refreshFeatureStatus()}get discordIsConnected(){return(0,p.Lt)(this.account.flags,256)}handleDiscordConnectClick(){return this.#a.watchFlag(256,30),this.#i.event("discord_connect_click",{trigger:"pro_onboarding_dialog"},s.Io),!0}handleDiscordServerClick(){this.#i.event("discord_server_click",{trigger:"pro_onboarding_dialog"},s.Io)}get showDiscordStep(){return!this.host.info.locale.endsWith("CN")}};(0,t.Cg)([(0,a.computedFrom)("account.flags"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],h.prototype,"discordIsConnected",null),(0,t.Cg)([(0,a.computedFrom)("host.info.locale"),(0,t.Sn)("design:type",Boolean),(0,t.Sn)("design:paramtypes",[])],h.prototype,"showDiscordStep",null),h=(0,t.Cg)([(0,a.autoinject)(),(0,c.m6)({selectors:{account:(0,c.$t)((e=>e.account)),theme:(0,c.$t)((e=>e.settings?.theme))}}),(0,t.Sn)("design:paramtypes",[r.DialogController,d.e,l.Hy,s.j0,n.s,g.s])],h);let u=class extends m.D{constructor(){super(...arguments),this.viewModelClass="dialogs/pro-onboarding-dialog"}};u=(0,t.Cg)([(0,a.autoinject)()],u)},"dialogs/pro-onboarding-dialog.html":(e,i,o)=>{o.r(i),o.d(i,{default:()=>b});var t=o(14385),r=o.n(t),a=new URL(o(2761),o.b),n=new URL(o(38032),o.b),s=new URL(o(33806),o.b),l=new URL(o(30904),o.b),d=new URL(o(60605),o.b),g=new URL(o(55318),o.b),c=new URL(o(26232),o.b),p=new URL(o(68118),o.b);const b='<template> <require from="./pro-onboarding-dialog.scss"></require> <require from="./resources/elements/remote-platforms.html"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../shared/resources/elements/feature-number"></require> <require from="../resources/elements/remote-code"></require> <require from="../resources/elements/remote-qr-code"></require> <require from="../settings/resources/elements/theme-selector"></require> <ux-dialog class="pro-onboarding-dialog fullscreen-dialog"> <div class="scroll-wrapper" ref="scrollWrapperEl"> <div class="scroll-inner"> <header class="top"> <close-button click.delegate="controller.close(false, \'close\')" tabindex="0"></close-button> <img src="'+r()(a)+'"> <template if.bind="config.mode === \'post-pro-upgrade\'"> <h1> <span>${\'pro_onboarding_dialog.congrats\' | i18n}</span><br> <span innerhtml.bind="\'pro_onboarding_dialog.you_are_now_pro\' | i18n | markdown"></span> </h1> <h4 innerhtml.bind="\'pro_onboarding_dialog.thanks_for_joining\' | i18n | markdown"></h4> </template> <template else> <h1> <span innerhtml.bind="\'pro_onboarding_dialog.your_exclusive_pro_features\' | i18n | markdown"></span> </h1> <h4 innerhtml.bind="\'pro_onboarding_dialog.were_happy_to_have_you\' | i18n | markdown"></h4> </template> </header> <div class="step remote"> <feature-number>1</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.connect_wemod_remote\' | i18n | markdown"></h2> <template if.bind="remoteHasConnected"> <div class="success"> <span class="pulse"></span> <span class="label">${\'pro_onboarding_dialog.connected\' | i18n}</span> </div> </template> <template else> <div class="block app"> <remote-qr-code></remote-qr-code> <p class="stretch medium" innerhtml.bind="\'pro_onboarding_dialog.get_our_mobile_app\' | i18n | markdown"></p> <remote-platforms highlight="true"></remote-platforms> </div> <div class="block code"> <div> <p class="medium arrow" innerhtml.bind="\'pro_onboarding_dialog.then_enter_this_pin\' | i18n | markdown"></p> <remote-code></remote-code> </div> </div> </template> </div> <div class="step-image"> <img src="'+r()(n)+'"> </div> </div> <div class="step save-mods"> <feature-number>2</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.enable_save_mods\' | i18n | markdown"></h2> <p class="instructions" innerhtml.bind="\'pro_onboarding_dialog.save_mods_explanation\' | i18n | markdown"></p> <a class="button" href="https://wemod.gg/support-save-mods" target="_blank" click.delegate="saveCheatsLinkClicked = true">${\'pro_onboarding_dialog.how_it_works\' | i18n}</a> </div> <div class="step-image"> <div class="save-mods-image-wrapper"> <span class="toggle"> <img src="'+r()(s)+"\"> <span>${'pro_onboarding_dialog.save_mods' | i18n}</span> <img src=\""+r()(l)+'"> </span> <img class="graphic" src="'+r()(d)+'"> </div> </div> </div> <div class="step boosts"> <feature-number>3</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.influence_game_priority\' | i18n | markdown"></h2> <p class="instructions" innerhtml.bind="\'pro_onboarding_dialog.boosts_explanation\' | i18n | markdown"></p> <a class="button" href="https://wemod.gg/support-queue" target="_blank" click.delegate="boostsLinkClicked = true">${\'pro_onboarding_dialog.how_it_works\' | i18n}</a> </div> <div class="step-image"> <img src="'+r()(g)+'"> </div> </div> <div class="step themes"> <feature-number>4</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.choose_your_theme\' | i18n | markdown"></h2> <p class="instructions" innerhtml.bind="\'pro_onboarding_dialog.themes_explanation\' | i18n | markdown"></p> <theme-selector source="pro_onboarding_dialog"></theme-selector> </div> <div class="step-image"> <img src="'+r()(c)+'"> </div> </div> <div class="step discord" if.bind="showDiscordStep"> <feature-number>5</feature-number> <div class="step-content"> <h2 innerhtml.bind="\'pro_onboarding_dialog.receive_special_role_in_discord\' | i18n | markdown"></h2> <p class="instructions" innerhtml.bind="\'pro_onboarding_dialog.first_your_account_must_be_linked_to_discord\' | i18n | markdown"></p> <div> <template if.bind="discordIsConnected"> <div class="success"> <span class="pulse"></span> <span class="label">${\'pro_onboarding_dialog.connected\' | i18n}</span> </div> </template> <template else> <a class="button accent" href="website://account/connections/discord#auth" target="_blank" click.delegate="handleDiscordConnectClick()">${\'pro_onboarding_dialog.connect_discord\' | i18n}</a> </template> </div> <a class="discord" href="website://discord" target="_blank" click.delegate="handleDiscordServerClick()">${\'pro_onboarding_dialog.join_server\' | i18n}</a> </div> <div class="step-image"> <img src="'+r()(p)+'"> </div> </div> <footer class="bottom"> <h2 class="accent" innerhtml.bind="`pro_onboarding_dialog.${config.mode === \'post-pro-upgrade\' ? \'plus_now_that_youre_pro\' : \'plus_with_your_pro_subscription\'}` | i18n | markdown"></h2> <p innerhtml.bind="\'pro_onboarding_dialog.you_support_creators_and_wemod_development\' | i18n | markdown"></p> <div class="buttons"> <button class="button secondary" if.bind="config.mode === \'post-pro-upgrade\'" click.delegate="controller.close(false, \'skip\')"> ${\'pro_onboarding_dialog.skip_for_now\' | i18n} </button> <button class="button highlight" click.delegate="controller.close(true, \'ok\')" innerhtml.bind="`pro_onboarding_dialog.${config.mode === \'post-pro-upgrade\' ? \'i_am_all_set_up_for_pro\' : \'i_understand_the_pro_features\' }` | i18n | markdown"></button> </div> </footer> </div> </div> </ux-dialog> </template> '},"dialogs/pro-onboarding-dialog.scss":(e,i,o)=>{o.r(i),o.d(i,{default:()=>y});var t=o(31601),r=o.n(t),a=o(76314),n=o.n(a),s=o(4417),l=o.n(s),d=new URL(o(96369),o.b),g=new URL(o(76048),o.b),c=new URL(o(81206),o.b),p=new URL(o(79948),o.b),b=new URL(o(58821),o.b),m=new URL(o(11394),o.b),h=n()(r()),u=l()(d),f=l()(g),x=l()(c),v=l()(p),w=l()(b),_=l()(m);h.push([e.id,`body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}@keyframes -pulse{0%{transform:scale(0.3)}50%{transform:scale(0.5)}100%{transform:scale(0.3)}}@keyframes pulse-2{0%{transform:scale(1);opacity:0}70%{transform:scale(1.5);opacity:1}100%{transform:scale(2);opacity:0}}.billing-settings section{padding:20px;border-radius:10px;border:1px solid rgba(255,255,255,.04);transition:opacity .15s}.billing-settings section.filled{background:rgba(255,255,255,.04);border:none;padding-top:18px}.billing-settings section.filled h5{color:rgba(255,255,255,.6)}.billing-settings section.filled h5.plan-header{margin-bottom:16px;color:#fff}.billing-settings section+section{margin-top:20px}.billing-settings section.loading{opacity:.4}.billing-settings section.loading,.billing-settings section.loading *{pointer-events:none}.billing-settings section.layout{display:flex}.billing-settings section.layout>*:first-child{flex:1 1 auto}.billing-settings section.layout>*:last-child{flex:0 0 auto}.billing-settings .details{display:flex;align-items:center}.billing-settings .details .meta{display:flex;align-items:center;color:rgba(255,255,255,.5)}.billing-settings .details .meta>*+*{margin-left:7px}.billing-settings .details .meta,.billing-settings .details .meta strong,.billing-settings .details .meta em{font-size:13px;line-height:20px;font-weight:500}.billing-settings .details .meta em{font-style:normal;color:rgba(255,255,255,.8)}.billing-settings .details .card-type{width:34px;height:22px;border-radius:4px;background-position:center;background-repeat:no-repeat;background-image:url(${u});display:inline-block;flex:0 0 auto}.billing-settings .details .meta.payment{display:flex;flex-direction:column;align-items:flex-start;justify-content:center}.billing-settings .details .meta.payment>*+*{margin-left:0}.billing-settings .details .meta.payment.canceled{border-left:1px solid rgba(255,255,255,.1);padding-left:20px;flex-direction:row;align-items:center;gap:10px}.billing-settings .details .meta.warning,.billing-settings .details .meta.warning *{color:var(--color--accent-yellow)}.billing-settings .details .row-actions{display:flex;align-items:center}.billing-settings .details .row-actions .links{display:flex;align-items:center;margin-left:20px;padding-left:20px;border-left:1px solid rgba(255,255,255,.1)}.billing-settings .details .row-actions .links .remove{color:rgba(var(--color--alert--rgb), 0.8)}.billing-settings .details .row-actions .links .remove:hover{color:#fff}.billing-settings .link{font-size:13px;line-height:20px;font-weight:700;background:rgba(0,0,0,0);border:0;color:rgba(255,255,255,.25);padding:0}.billing-settings .link:hover{color:#fff}.billing-settings .resume-btn{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-weight:700;font-size:15px;line-height:24px;font-weight:700;--cta--padding: 12px;--cta--height: 28px;--cta--hover--border-width: 1px;box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight);font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;font-weight:700;padding:20px 16px}.billing-settings .resume-btn,.billing-settings .resume-btn *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn{border:1px solid #fff}}.billing-settings .resume-btn>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .resume-btn>*:first-child{padding-left:0}.billing-settings .resume-btn>*:last-child{padding-right:0}.billing-settings .resume-btn svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn svg *{fill:CanvasText}}.billing-settings .resume-btn svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .resume-btn svg{opacity:1}}.billing-settings .resume-btn img{height:50%}.billing-settings .resume-btn:disabled{opacity:.3}.billing-settings .resume-btn:disabled,.billing-settings .resume-btn:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .resume-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .resume-btn:not(:disabled):hover svg{opacity:1}}.billing-settings .resume-btn:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.billing-settings .resume-btn:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.billing-settings .resume-btn:not(:disabled):active{background-color:var(--theme--highlight)}.billing-settings h5{font-weight:600;font-size:16px;line-height:25px;font-weight:700;color:#fff;margin:0 0 11px}.billing-settings h5 em{font-style:normal;color:var(--theme--highlight)}.billing-settings h5 strong{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;margin-right:4px;vertical-align:middle}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings h5 strong{border:1px solid #fff}}.billing-settings h5+.details{margin-top:2px}.billing-settings h5.warning{color:var(--color--accent-yellow) !important;display:inline-flex;align-items:center}.billing-settings h5.warning:before{display:inline-block;content:"";width:15px;height:15px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${f}) 6px 3px no-repeat}.billing-settings h5.warning:before{margin-right:8px}.billing-settings .canceled-details{display:flex;align-items:center;border-left:1px solid rgba(255,255,255,.1);padding-left:20px;margin-left:20px}.billing-settings .canceled-details .meta{display:flex;align-items:center;color:rgba(255,255,255,.5)}.billing-settings .canceled-details .meta>*+*{margin-left:7px}.billing-settings .canceled-details .meta,.billing-settings .canceled-details .meta strong,.billing-settings .canceled-details .meta em{font-size:13px;line-height:20px;font-weight:500}.billing-settings .canceled-details .meta em{font-style:normal;color:rgba(255,255,255,.8)}.billing-settings .canceled-details .card-type{width:34px;height:22px;border-radius:4px;background-position:center;background-repeat:no-repeat;background-image:url(${u});display:inline-block;flex:0 0 auto}.billing-settings .main-actions{margin:17px 0 0 0}.billing-settings .main-actions>*+*{margin-left:15px}.billing-settings .main-actions .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent);font-weight:800;--cta--padding: 18px;--cta--height: 40px;--cta--hover--border-width: 2px;font-size:18px}.billing-settings .main-actions .button,.billing-settings .main-actions .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button{border:1px solid #fff}}.billing-settings .main-actions .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .main-actions .button>*:first-child{padding-left:0}.billing-settings .main-actions .button>*:last-child{padding-right:0}.billing-settings .main-actions .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button svg *{fill:CanvasText}}.billing-settings .main-actions .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .main-actions .button svg{opacity:1}}.billing-settings .main-actions .button img{height:50%}.billing-settings .main-actions .button:disabled{opacity:.3}.billing-settings .main-actions .button:disabled,.billing-settings .main-actions .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .main-actions .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .main-actions .button:not(:disabled):hover svg{opacity:1}}.billing-settings .main-actions .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}@media(hover: hover){.billing-settings .main-actions .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}.billing-settings .main-actions .button:not(:disabled):active{background-color:var(--color--accent)}.billing-settings .main-actions .button:not(:disabled):active{--cta__icon--color: #000;color:#000}.billing-settings .main-actions .button.main{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.billing-settings .main-actions .button.main:hover{filter:brightness(1.1)}}.billing-settings .main-actions .button.secondary{background-color:rgba(255,255,255,.6);box-shadow:none;color:var(--theme--background)}.billing-settings .main-actions .button.secondary svg{opacity:1}.billing-settings .main-actions .button.secondary svg *{--cta__icon--color: var(--theme--background)}@media(hover: hover){.billing-settings .main-actions .button.secondary:not(:disabled):hover{background-color:var(--theme--highlight)}}.billing-settings .main-actions .button.accent{background-color:rgba(var(--color--accent--rgb), 0.08) !important;color:var(--color--accent) !important;transition:filter .15s;box-shadow:none !important}@media(hover: hover){.billing-settings .main-actions .button.accent:hover{filter:brightness(1.1)}}.billing-settings .main-actions .button.small{font-size:13px;line-height:20px;font-weight:700;--cta--padding: 11px;--cta--height: 26px;--cta--hover--border-width: 1px}.billing-settings .main-actions .divider{border-left:1px solid rgba(255,255,255,.1);height:28px}.billing-settings .main-actions .promo{font-size:13px;line-height:20px;color:var(--color--accent);display:inline-flex;align-items:center}.billing-settings .main-actions .promo i{margin-left:7px}.billing-settings .main-actions .promo i svg *{fill:var(--color--accent)}.billing-settings .alert{margin-top:8px}.billing-settings .info{font-size:14px;line-height:21px;font-weight:500;line-height:19px;color:rgba(255,255,255,.5)}.billing-settings .disclaimer{font-size:13px;line-height:20px;font-weight:500;color:rgba(255,255,255,.3);display:flex;align-items:center;margin:20px 0 0 0}.billing-settings .disclaimer .icon{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;--cta--height: 15px;--cta--hover--border-width: 1px;min-width:var(--cta--height);width:var(--cta--height);border-radius:50%;justify-content:center;align-items:center;position:relative;background:rgba(255,255,255,.1);box-shadow:none !important;pointer-events:none;margin-right:7px}.billing-settings .disclaimer .icon,.billing-settings .disclaimer .icon *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon{border:1px solid #fff}}.billing-settings .disclaimer .icon>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.billing-settings .disclaimer .icon>*:first-child{padding-left:0}.billing-settings .disclaimer .icon>*:last-child{padding-right:0}.billing-settings .disclaimer .icon svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon svg *{fill:CanvasText}}.billing-settings .disclaimer .icon svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .billing-settings .disclaimer .icon svg{opacity:1}}.billing-settings .disclaimer .icon img{height:50%}.billing-settings .disclaimer .icon:disabled{opacity:.3}.billing-settings .disclaimer .icon:disabled,.billing-settings .disclaimer .icon:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.billing-settings .disclaimer .icon:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.billing-settings .disclaimer .icon:not(:disabled):hover svg{opacity:1}}.billing-settings .disclaimer .icon:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.billing-settings .disclaimer .icon,.billing-settings .disclaimer .icon>*{padding:0 !important}.billing-settings .disclaimer .icon:active{background-color:rgba(0,0,0,0) !important;color:rgba(255,255,255,.8) !important}.billing-settings .disclaimer .icon svg{opacity:.5}.billing-settings .disclaimer a{color:rgba(255,255,255,.5)}.billing-settings .disclaimer a:hover{color:#fff}.billing-settings .graphic{margin:0 0 -20px 0;justify-self:flex-end}.billing-settings .subscribed{--checkbox--checked-color: var(--theme--highlight);--checkbox__label--color: rgba(255, 255, 255, 0.5);display:inline-flex;align-items:center;--checkbox--checked-color: var(--color--accent);border:1px solid rgba(255,255,255,.15);border-radius:100px;padding:8px 15px;transition:background-color .15s,border-color .15s;background-color:rgba(var(--color--accent--rgb), 0.08);cursor:initial;border-color:rgba(0,0,0,0);--checkbox__label--color: var(--checkbox--checked-color);margin-right:15px}.billing-settings .subscribed,.billing-settings .subscribed *{cursor:pointer}.billing-settings .subscribed,.billing-settings .subscribed *{cursor:pointer}.billing-settings .subscribed>*:first-child{margin-right:9px}.billing-settings .subscribed:hover>*{--checkbox__label--color: #fff}.billing-settings .subscribed,.billing-settings .subscribed *:not(info-tooltip){cursor:default !important}.billing-settings .subscribed .checkbox{display:inline-block;width:15px;height:15px;border:1px solid rgba(255,255,255,.25);border-radius:4px;position:relative;background:rgba(0,0,0,0);transition:background-color .15s;-webkit-appearance:none;width:15px;height:15px;background:rgba(0,0,0,0);border-color:rgba(255,255,255,.25);border-color:rgba(0,0,0,0)}.billing-settings .subscribed .checkbox,.billing-settings .subscribed .checkbox *{cursor:pointer}.billing-settings .subscribed .checkbox:checked:before{opacity:1}.billing-settings .subscribed .checkbox:before{background:var(--checkbox--checked-color);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:12px;opacity:0;-webkit-mask-box-image:url(${x});mask:url(${x})}.billing-settings .subscribed .checkbox:before{left:1px;top:0;width:15px;height:11px;transform:scale(1)}.billing-settings .subscribed .checkbox:before{opacity:1}.billing-settings .subscribed>.icon{margin-right:9px}.billing-settings .subscribed>.icon svg *{fill:var(--color--accent)}.billing-settings .subscribed .label{font-size:13px;line-height:20px;font-weight:500;color:var(--checkbox__label--color);transition:color .15s;color:var(--color--accent) !important}.billing-settings .subscribed .label,.billing-settings .subscribed .label *{cursor:pointer}.billing-settings .subscribed info-tooltip{margin-left:10px}.billing-settings .subscribed info-tooltip,.billing-settings .subscribed info-tooltip *{cursor:pointer}.billing-settings .subscribed.warning{background:rgba(var(--color--accent-yellow--rgb), 0.08)}.billing-settings .subscribed.warning .label{color:var(--color--accent-yellow) !important}.billing-settings .subscribed.warning .checkbox{display:none}.billing-settings .subscribed.warning:before{display:inline-block;content:"";width:19px;height:19px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${v}) center no-repeat;margin-right:11px}.billing-settings .subscribed.canceled{background:rgba(var(--theme--highlight--rgb), 0.08)}.billing-settings .subscribed.canceled .checkbox:before{background:var(--theme--highlight) !important}.billing-settings .subscribed.canceled .label{color:var(--theme--highlight) !important}.billing-settings .wemod-tag{font-weight:500;font-feature-settings:"liga" off,"clig" off;font-size:14px;line-height:20px;letter-spacing:normal;text-transform:capitalize;white-space:nowrap;padding:1px 6px}.billing-settings hr{border:0;border-top:1px solid rgba(255,255,255,.1);margin:20px 0 16px}.pro-onboarding-dialog .scroll-wrapper{width:100vw;height:100vh;overflow-x:hidden;overflow-y:auto}.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.pro-onboarding-dialog .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.pro-onboarding-dialog .scroll-inner{position:relative;padding:0 80px}.pro-onboarding-dialog .top{text-align:center;padding:65px 25px 8px}.pro-onboarding-dialog .top img{margin-bottom:8px}.pro-onboarding-dialog .top h1{font-weight:700;font-size:52px;line-height:55px;margin:0 0 25px;color:#fff}.pro-onboarding-dialog .top h1 strong{color:var(--color--accent)}.pro-onboarding-dialog .top h4{font-size:16px;line-height:24px;font-weight:500;margin:0;color:rgba(255,255,255,.6)}.pro-onboarding-dialog .top h4 strong{font-weight:600;color:#fff}.pro-onboarding-dialog .step{background:linear-gradient(180deg, transparent, var(--theme--background));border-radius:20px;position:relative;padding:27px 34px;max-width:1210px;margin:0 auto 40px;display:flex}.pro-onboarding-dialog .step feature-number{position:absolute;left:-14px;top:-6px}.pro-onboarding-dialog .step .step-content{flex:1;padding-right:20px}.pro-onboarding-dialog .step .step-content h2{font-size:24px;line-height:32px;font-weight:700;color:#fff;margin:0 0 13px}.pro-onboarding-dialog .step .step-content h2 strong{font-weight:700;color:var(--theme--highlight)}.pro-onboarding-dialog .step .step-image{flex:1;margin-bottom:-27px}.pro-onboarding-dialog .step.remote .step-image{display:flex;align-items:flex-end;justify-content:center}.pro-onboarding-dialog .step.remote .step-image img{width:100%;max-width:380px}.pro-onboarding-dialog .step.remote .block{padding:20px;border-radius:10px;background:var(--theme--secondary-background);max-width:535px}.pro-onboarding-dialog .step.remote .block+.block{margin-top:20px}.pro-onboarding-dialog .step.remote .app{display:flex;align-items:flex-start}.pro-onboarding-dialog .step.remote .app>*{flex:0 0 auto}.pro-onboarding-dialog .step.remote .app>*+*{margin-left:18px}.pro-onboarding-dialog .step.remote .app>*.stretch{flex:1 1 auto}.pro-onboarding-dialog .step.remote .code{text-align:center}.pro-onboarding-dialog .step.remote .code p{margin:0 0 14px}.pro-onboarding-dialog .step.remote remote-qr-code{width:80px;height:80px}.pro-onboarding-dialog .step.remote remote-platforms{width:150px}@media(max-width: 1080px){.pro-onboarding-dialog .step.remote .app{flex-wrap:wrap}.pro-onboarding-dialog .step.remote .app remote-qr-code{order:2}.pro-onboarding-dialog .step.remote .app remote-platforms{order:3;width:calc(100% - 100px)}.pro-onboarding-dialog .step.remote .app p{order:1;margin:0 0 20px}}.pro-onboarding-dialog .step.save-mods .instructions{max-width:504px}.pro-onboarding-dialog .step.save-mods .button{margin-top:24px}.pro-onboarding-dialog .step.save-mods .step-image{display:flex;justify-content:flex-end;align-items:flex-start;margin:-27px -34px -27px 0}.pro-onboarding-dialog .step.save-mods .step-image .save-mods-image-wrapper{position:relative;height:333px;overflow:hidden}.pro-onboarding-dialog .step.save-mods .step-image .save-mods-image-wrapper .toggle{display:inline-flex;align-items:center;position:absolute;right:201px;top:30px;height:47px;border:1px solid rgba(255,255,255,.15);padding:11px 20px 11px 11px;border-radius:50px;font-size:15px;color:rgba(255,255,255,.6)}.pro-onboarding-dialog .step.save-mods .step-image .save-mods-image-wrapper .toggle>*+*{margin-left:10px}.pro-onboarding-dialog .step.save-mods .step-image .graphic{width:491px}.pro-onboarding-dialog .step.boosts .button{margin-top:24px}.pro-onboarding-dialog .step.boosts .step-content{max-width:524px}.pro-onboarding-dialog .step.boosts .step-image{display:flex;justify-content:flex-end;align-items:flex-end;margin-right:-34px;margin-top:-27px}.pro-onboarding-dialog .step.boosts .step-image img{width:100%;max-width:520px}.pro-onboarding-dialog .step.themes .step-content{padding-right:100px}.pro-onboarding-dialog .step.themes .step-image{display:flex;justify-content:flex-end;align-items:flex-end;margin-right:-34px;margin-top:-27px;border-top-left-radius:20px;border-bottom-right-radius:20px;overflow:hidden}.pro-onboarding-dialog .step.themes .step-image img{width:100%;max-width:526px}.pro-onboarding-dialog .step.themes theme-selector{display:block;margin-top:30px}.pro-onboarding-dialog .step.discord .instructions{max-width:504px;margin-bottom:24px}.pro-onboarding-dialog .step.discord .step-image{display:flex;align-items:center;justify-content:center;margin-bottom:0}.pro-onboarding-dialog .step.discord .step-image img{width:100%;max-width:250px}.pro-onboarding-dialog .step.discord a.discord{font-size:13px;line-height:20px;font-weight:500;padding:4px 10px 4px 29px;color:rgba(255,255,255,.8);background:var(--theme--secondary-background) url(${w}) 9px center no-repeat;border-radius:4px;display:inline-block;margin-top:20px}.pro-onboarding-dialog .step.discord a.discord:hover{color:#fff}.pro-onboarding-dialog .bottom{width:100%;max-width:795px;margin:0 auto 70px;background:linear-gradient(247.39deg, rgba(255, 255, 255, 0.15) 0%, rgba(var(--color--brand-blue--rgb), 0.06) 70.59%);border-radius:20px;padding:27px 34px;text-align:center}.pro-onboarding-dialog .bottom .buttons{display:flex;align-items:center;justify-content:center;margin:9px 0 0 -15px;flex-wrap:wrap}.pro-onboarding-dialog .bottom .buttons .button{margin:15px 0 0 15px}.pro-onboarding-dialog h2{font-size:24px;line-height:32px;font-weight:700;color:#fff;margin:0 0 13px}.pro-onboarding-dialog h2 strong{font-weight:700;color:var(--theme--highlight)}.pro-onboarding-dialog h2.accent strong{color:var(--color--accent)}.pro-onboarding-dialog p{font-size:16px;line-height:24px;font-weight:600;color:rgba(255,255,255,.5)}.pro-onboarding-dialog p strong{font-weight:600;color:#fff}.pro-onboarding-dialog p em{font-weight:600;color:var(--theme--highlight)}.pro-onboarding-dialog p.medium,.pro-onboarding-dialog p.medium *{font-weight:500}.pro-onboarding-dialog p.arrow:after{content:"";display:inline-block;position:relative;left:5px;top:10px;width:15px;height:15px;background:url(${_}) top left no-repeat}.pro-onboarding-dialog p.warning{display:flex;align-items:center;margin:22px 0 0 0;padding:15px 0 0 0;border-top:1px solid rgba(255,255,255,.1);max-width:535px}.pro-onboarding-dialog p.warning:before{display:inline-block;content:"";width:15px;height:15px;border-radius:50%;background:rgba(var(--color--accent-yellow--rgb), 0.3) url(${f}) 6px 3px no-repeat}.pro-onboarding-dialog p.warning>span{font-size:13px;line-height:20px;color:rgba(255,255,255,.4);display:inline-block;max-width:246px;margin-left:12px}.pro-onboarding-dialog p.warning>span em{color:#fff}.pro-onboarding-dialog .button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px}.pro-onboarding-dialog .button,.pro-onboarding-dialog .button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-onboarding-dialog .button{border:1px solid #fff}}.pro-onboarding-dialog .button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.pro-onboarding-dialog .button>*:first-child{padding-left:0}.pro-onboarding-dialog .button>*:last-child{padding-right:0}.pro-onboarding-dialog .button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-onboarding-dialog .button svg *{fill:CanvasText}}.pro-onboarding-dialog .button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .pro-onboarding-dialog .button svg{opacity:1}}.pro-onboarding-dialog .button img{height:50%}.pro-onboarding-dialog .button:disabled{opacity:.3}.pro-onboarding-dialog .button:disabled,.pro-onboarding-dialog .button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.pro-onboarding-dialog .button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.pro-onboarding-dialog .button:not(:disabled):hover svg{opacity:1}}.pro-onboarding-dialog .button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.pro-onboarding-dialog .button.accent{box-shadow:inset 0 0 0 1px var(--color--accent);--cta__icon--color: var(--color--accent)}@media(hover: hover){.pro-onboarding-dialog .button.accent:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--color--accent);background-color:rgba(0,0,0,0)}}.pro-onboarding-dialog .button.accent:not(:disabled):active{background-color:var(--color--accent)}.pro-onboarding-dialog .button.accent:not(:disabled):active{--cta__icon--color: #000;color:#000}.pro-onboarding-dialog .button.highlight{box-shadow:inset 0 0 0 1px var(--theme--highlight);--cta__icon--color: var(--theme--highlight)}@media(hover: hover){.pro-onboarding-dialog .button.highlight:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--theme--highlight);background-color:rgba(0,0,0,0)}}.pro-onboarding-dialog .button.highlight:not(:disabled):active{background-color:var(--theme--highlight)}.pro-onboarding-dialog .button.secondary{background-color:rgba(255,255,255,.1);box-shadow:none !important;color:rgba(255,255,255,.5)}@media(hover: hover){.pro-onboarding-dialog .button.secondary:not(:disabled):hover{background-color:rgba(255,255,255,.2);color:#fff}}.pro-onboarding-dialog .success{display:flex;align-items:center;margin-top:24px}.pro-onboarding-dialog .success .label{font-weight:800;font-size:21px;line-height:30px;font-weight:700;break-inside:avoid;white-space:nowrap;color:#fff;margin:0}.pro-onboarding-dialog .success .pulse{width:16px;height:16px;position:relative;margin-right:10px;display:inline-block}.pro-onboarding-dialog .success .pulse:after{background:rgba(var(--theme--highlight--rgb), 0.75);content:"";position:absolute;left:0;top:0;width:16px;height:16px;display:inline-block;border-radius:50%;animation:pulse 1s infinite ease-in-out}.pro-onboarding-dialog .success .pulse:before{background:rgba(var(--theme--highlight--rgb), 0.24);content:"";display:block;position:absolute;left:0;top:0;width:16px;height:16px;border-radius:50%;animation:pulse-2 1s infinite}`,""]);const y=h},"dialogs/pro-showcase-columns-dialog":(e,i,o)=>{o.r(i),o.d(i,{ProShowcaseColumnsDialog:()=>s,ProShowcaseColumnsDialogService:()=>l});var t=o(15215),r=o("aurelia-dialog"),a=o("aurelia-framework"),n=o(17275);let s=class{constructor(e){this.controller=e}async activate(e){e?.defaultFeature&&(this.defaultFeature=e?.defaultFeature)}handleClose(e){e?this.controller.ok():this.controller.cancel()}};s=(0,t.Cg)([(0,a.autoinject)(),(0,t.Sn)("design:paramtypes",[r.DialogController])],s);let l=class extends n.C{constructor(){super(...arguments),this.viewModelClass="dialogs/pro-showcase-columns-dialog"}};l=(0,t.Cg)([(0,a.autoinject)()],l)},"dialogs/pro-showcase-columns-dialog.html":(e,i,o)=>{o.r(i),o.d(i,{default:()=>t});const t='<template> <require from="./pro-showcase-columns-dialog.scss"></require> <require from="shared/resources/elements/close-button"></require> <require from="pro-promos/pro-showcase-columns/pro-showcase-columns"></require> <ux-dialog class="pro-showcase-columns-dialog"> <ux-dialog-header> <close-button click.trigger="controller.cancel()" tabindex="0"></close-button> </ux-dialog-header> <ux-dialog-body> <pro-showcase-columns default-feature.bind="defaultFeature" on-close.call="handleClose(result)"></pro-showcase-columns> </ux-dialog-body> </ux-dialog> </template> '},"dialogs/pro-showcase-columns-dialog.scss":(e,i,o)=>{o.r(i),o.d(i,{default:()=>s});var t=o(31601),r=o.n(t),a=o(76314),n=o.n(a)()(r());n.push([e.id,".pro-showcase-columns-dialog{width:900px;max-width:100%;max-height:80vh;padding:0}.pro-showcase-columns-dialog pro-showcase-columns{border-radius:20px;overflow:hidden;position:relative;z-index:0}.pro-showcase-columns-dialog close-button{z-index:1}",""]);const s=n},"dialogs/pro-showcase-dialog":(e,i,o)=>{o.r(i),o.d(i,{ProShowcaseDialog:()=>s,ProShowcaseDialogService:()=>l});var t=o(15215),r=o("aurelia-dialog"),a=o("aurelia-framework"),n=o(17275);let s=class{constructor(e){this.controller=e}activate(e){this.defaultFeature=e?.defaultFeature}handleProCtaClick(){this.controller.ok(this.defaultFeature)}handleCloseClick(){this.controller.cancel(this.defaultFeature)}};s=(0,t.Cg)([(0,a.autoinject)(),(0,t.Sn)("design:paramtypes",[r.DialogController])],s);let l=class extends n.C{constructor(){super(...arguments),this.viewModelClass="dialogs/pro-showcase-dialog"}};l=(0,t.Cg)([(0,a.autoinject)()],l)},"dialogs/pro-showcase-dialog.html":(e,i,o)=>{o.r(i),o.d(i,{default:()=>t});const t='<template> <require from="pro-promos/pro-showcase/pro-showcase"></require> <require from="./pro-showcase-dialog.scss"></require> <require from="shared/resources/elements/close-button"></require> <ux-dialog class="pro-showcase-dialog"> <close-button click.trigger="handleCloseClick()" tabindex="0"></close-button> <pro-showcase current-feature.two-way="defaultFeature" on-pro-cta-click.call="handleProCtaClick()"> </pro-showcase> </ux-dialog> </template> '},"dialogs/pro-showcase-dialog.scss":(e,i,o)=>{o.r(i),o.d(i,{default:()=>s});var t=o(31601),r=o.n(t),a=o(76314),n=o.n(a)()(r());n.push([e.id,".pro-showcase-dialog{width:900px;max-width:100%;padding:0}.pro-showcase-dialog close-button{z-index:1}.pro-showcase-dialog pro-showcase{overflow:hidden;border-radius:20px;display:flex}",""]);const s=n},"dialogs/resources/elements/remote-platforms.html":(e,i,o)=>{o.r(i),o.d(i,{default:()=>n});var t=o(14385),r=o.n(t),a=new URL(o(26991),o.b);const n='<template bindable="highlight" class="${highlight ? \'highlight\' : \'\'}"> <require from="./remote-platforms.scss"></require> <div class="icons"> <inline-svg src="'+r()(a)+'"></inline-svg> </div> <div class="label" innerhtml.bind="\'remote_platforms.available_on_mobile\' | i18n | markdown"></div> </template> '},"dialogs/resources/elements/remote-platforms.scss":(e,i,o)=>{o.r(i),o.d(i,{default:()=>s});var t=o(31601),r=o.n(t),a=o(76314),n=o.n(a)()(r());n.push([e.id,"remote-platforms{display:block;border-radius:10px;background:linear-gradient(180deg, rgba(var(--color--accent--rgb), 0.25) 0%, transparent 100%);width:158px;padding:15px 18px 14px 19px;overflow:hidden;text-align:center}remote-platforms .icons{margin:0 0 7px 0}remote-platforms .label{font-size:14px;line-height:22px;color:rgba(255,255,255,.8)}remote-platforms .label em{font-style:normal;color:#fff}remote-platforms.highlight{background:rgba(var(--theme--default--background--rgb), 0.4)}remote-platforms.highlight svg *{fill:var(--theme--highlight)}",""]);const s=n},"dialogs/resources/elements/support-wemod-footer":(e,i,o)=>{o.r(i),o.d(i,{SupportWemodFooter:()=>n});var t=o(15215),r=o("aurelia-framework"),a=o(33700);let n=class{constructor(e){this.supportedGameCount=e}};n=(0,t.Cg)([(0,r.autoinject)(),(0,t.Sn)("design:paramtypes",[a.q])],n)},"dialogs/resources/elements/support-wemod-footer.html":(e,i,o)=>{o.r(i),o.d(i,{default:()=>n});var t=o(14385),r=o.n(t),a=new URL(o(2761),o.b);const n='<template> <require from="./support-wemod-footer.scss"></require> <img class="support-icon" src="'+r()(a)+'"> <div class="support-message" innerhtml.bind="\'support_wemod_footer.members_like_you_make_wemod_possible\' | i18n:{x: supportedGameCount.formattedCount} | markdown"></div> </template> '},"dialogs/resources/elements/support-wemod-footer.scss":(e,i,o)=>{o.r(i),o.d(i,{default:()=>s});var t=o(31601),r=o.n(t),a=o(76314),n=o.n(a)()(r());n.push([e.id,"support-wemod-footer{display:flex;flex-direction:column;align-items:center;justify-content:center}support-wemod-footer .support-icon{display:block;width:56px;height:43px;margin:0 0 21px 0}support-wemod-footer .support-message{font-size:16px;line-height:24px;font-weight:500;color:rgba(255,255,255,.6);width:290px;text-align:center}",""]);const s=n},"dialogs/resources/elements/time-limit-graphic.html":(e,i,o)=>{o.r(i),o.d(i,{default:()=>n});var t=o(14385),r=o.n(t),a=new URL(o(12124),o.b);const n='<template> <require from="./time-limit-graphic.scss"></require> <require from="../../../shared/resources/elements/pro-badge"></require> <div> <inline-svg src="'+r()(a)+'"></inline-svg> </div> <span class="unlimited-label"> <span class="text">${\'time_limit_graphic.unlimited\' | i18n}</span> <pro-badge></pro-badge> </span> </template> '},"dialogs/resources/elements/time-limit-graphic.scss":(e,i,o)=>{o.r(i),o.d(i,{default:()=>s});var t=o(31601),r=o.n(t),a=o(76314),n=o.n(a)()(r());n.push([e.id,"time-limit-graphic{display:inline-block;position:relative}time-limit-graphic .unlimited-label{font-weight:700;display:inline-flex;align-items:center;font-size:27px;position:absolute;right:157px;top:240px;transform:translateX(50%)}time-limit-graphic .unlimited-label .text{margin-right:10px;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);-webkit-background-clip:text;-webkit-text-fill-color:rgba(0,0,0,0)}",""]);const s=n},"dialogs/time-limit-reached-post-game-dialog":(e,i,o)=>{o.r(i),o.d(i,{TimeLimitReachedPostGameDialog:()=>h,TimeLimitReachedPostGameDialogService:()=>u});var t=o(15215),r=o("aurelia-dialog"),a=o("aurelia-framework"),n=o(81512),s=o(62914),l=o(40930),d=o(60692),g=o(68539),c=o(54995),p=o(14046),b=o(71341),m=o(23218);let h=class{#l;#i;#d;constructor(e,i,o,t){this.controller=e,this.#l=i,this.#i=o,this.#d=t}activate(e){this.perGame=e.perGame,this.limitHours=e.limitHours||1,this.#i.event("time_limit_reached_post_game_dialog_show",{type:this.perGame?"per_game_daily":"daily",limitHours:this.limitHours},s.Io),this.showForegroundTimeLimit=3===this.#d.assignments.get(d.n.E39)}openCheckout(){this.#l.open({trigger:`time_limit_reached_${this.perGame?"per_game_":""}post_game_dialog`,frequency:"yearly"}),this.controller.close(!0)}subscriptionChanged(){this.subscription&&this.controller.cancel()}secondsPlayedTodayChanged(){0===this.secondsPlayedToday&&this.controller.cancel()}get resetTime(){const e=(0,n.A)().setHours(l.rf,l.px),i=new Date,o=new Date(e),t=(0,p.Ov)(o,i),r=new Date(1e3*t);return{hours:r.getUTCHours(),minutes:r.getUTCMinutes()}}};(0,t.Cg)([(0,a.computedFrom)("lastResetDailyPlayLimit"),(0,t.Sn)("design:type",Object),(0,t.Sn)("design:paramtypes",[])],h.prototype,"resetTime",null),h=(0,t.Cg)([(0,a.autoinject)(),(0,c.m6)({selectors:{subscription:(0,c.$t)((e=>e.account?.subscription)),secondsPlayedToday:(0,c.$t)((e=>e.counters?.secondsPlayedToday)),lastResetDailyPlayLimit:(0,c.$t)((e=>e.timestamps?.lastResetDailyPlayLimit))}}),(0,t.Sn)("design:paramtypes",[r.DialogController,b.U,s.j0,g.z])],h);let u=class extends m.D{constructor(){super(...arguments),this.viewModelClass="dialogs/time-limit-reached-post-game-dialog"}};u=(0,t.Cg)([(0,a.autoinject)()],u)},"dialogs/time-limit-reached-post-game-dialog.html":(e,i,o)=>{o.r(i),o.d(i,{default:()=>d});var t=o(14385),r=o.n(t),a=new URL(o(71662),o.b),n=new URL(o(76581),o.b),s=new URL(o(96539),o.b),l=new URL(o(1196),o.b);const d='<template> <require from="./time-limit-reached-post-game-dialog.scss"></require> <require from="./resources/elements/time-limit-graphic.html"></require> <require from="../shared/resources/elements/close-button"></require> <require from="../resources/elements/pro-cta-label"></require> <ux-dialog class="time-limit-reached-post-game-dialog fullscreen-dialog"> <close-button click.delegate="controller.cancel()" tabindex="0"></close-button> <div class="scroll-wrapper"> <div class="scroll-inner"> <div class="wrapper"> <h1 innerhtml.bind="`time_limit_reached_post_game_dialog.daily_time_limit${perGame ? \'_per_game\' : \'\'}_exceeded` | i18n | markdown"></h1> <p> </p><div innerhtml.bind="`time_limit_reached_post_game_dialog.free_can_use_wemod_for_${limitHours}_hour${limitHours > 1 ? \'s\' : \'\'}${perGame ? \'_per_game\' : \'\'}_each_day` | i18n | markdown"></div> <div if.bind="showForegroundTimeLimit"> <span>${\'time_limit_reached_post_game_dialog.time_limit_resets_in\' | i18n}</span> <template if.bind="resetTime.hours > 0"> <strong if.bind="resetTime.hours === 1" innerhtml.bind="\'time_limit_reached_post_game_dialog.1_hour\' | i18n | markdown"></strong> <strong else innerhtml.bind="\'time_limit_reached_post_game_dialog.$hours_hours\' | i18n:{hours: resetTime.hours} | markdown"></strong> </template> <template if.bind="resetTime.minutes > 0"> <strong if.bind="resetTime.minutes === 1" innerhtml.bind="\'time_limit_reached_post_game_dialog.1_minute\' | i18n | markdown"></strong> <strong else innerhtml.bind="\'time_limit_reached_post_game_dialog.$minutes_minutes\' | i18n:{minutes: resetTime.minutes} | markdown"></strong> </template> </div> <p></p> <span class="graphic ${showForegroundTimeLimit ? \'alternate\' : \'\'}"> <time-limit-graphic if.bind="showForegroundTimeLimit"></time-limit-graphic> <template else> <img if.bind="limitHours === 1" src="'+r()(a)+'"> <img if.bind="limitHours === 2" src="'+r()(n)+'"> <img if.bind="limitHours === 3" src="'+r()(s)+'"> <img if.bind="limitHours === 4" src="'+r()(l)+'"> <span class="free-badge">${\'time_limit_reached_post_game_dialog.free\' | i18n}</span> <span class="pro-badge">${\'time_limit_reached_post_game_dialog.pro\' | i18n}</span> </template> </span> <div class="buttons"> <button click.delegate="openCheckout()" class="primary"><pro-cta-label></pro-cta-label></button> <button click.delegate="controller.cancel()" class="secondary">${`time_limit_reached_post_game_dialog.${perGame ? \'play_another_game_today\' : \'ill_wait_until_tomorrow\'}` | i18n}</button> </div> </div> </div> </div> </ux-dialog> </template>'},"dialogs/time-limit-reached-post-game-dialog.scss":(e,i,o)=>{o.r(i),o.d(i,{default:()=>s});var t=o(31601),r=o.n(t),a=o(76314),n=o.n(a)()(r());n.push([e.id,"body.reduce-motion *,body.reduce-motion *:before,body.reduce-motion *:after{transition-duration:0s !important;animation-duration:0s !important;transition-delay:0s !important;animation-delay:0s !important;animation-iteration-count:1 !important}.time-limit-reached-post-game-dialog .scroll-wrapper{width:100vw;height:100vh;overflow-x:hidden;overflow-y:auto;background:linear-gradient(0deg, var(--theme--secondary-background) 0%, #000 100%)}.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,.time-limit-reached-post-game-dialog .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}.time-limit-reached-post-game-dialog .scroll-inner{display:flex;flex-direction:column;min-height:100vh}.time-limit-reached-post-game-dialog .wrapper{padding:50px;display:flex;flex-direction:column;align-items:center;justify-content:center;min-height:100vh}.time-limit-reached-post-game-dialog .wrapper h1{font-weight:700;text-align:center;font-size:52px;line-height:55px;color:#fff;margin-bottom:25px}.time-limit-reached-post-game-dialog .wrapper h1 em{font-style:normal;color:var(--theme--highlight)}.time-limit-reached-post-game-dialog .wrapper h1 s{background-color:var(--theme--highlight);display:inline-block;font-weight:800;font-size:14px;text-transform:uppercase;color:#fff;line-height:20px;letter-spacing:.6px;padding:0 4px;border-radius:2.5px;min-width:0;text-rendering:geometricPrecision;background:linear-gradient(225deg, var(--color--brand-green) 0%, var(--color--pro) 100%);color:#fff;text-decoration:none;line-height:42px;font-size:30px;letter-spacing:.9px;padding:0 9px 0 11px;border-radius:7.5px;vertical-align:middle;margin-top:-10px}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-post-game-dialog .wrapper h1 s{border:1px solid #fff}}.time-limit-reached-post-game-dialog .wrapper h1 strong{color:var(--color--accent)}.time-limit-reached-post-game-dialog .wrapper p{font-size:12px;line-height:18px;color:rgba(255,255,255,.6);margin:0 0 34px;padding:0}.time-limit-reached-post-game-dialog .wrapper p em{font-style:none;color:#fff}.time-limit-reached-post-game-dialog .wrapper button{--cta__icon--color: #fff;display:inline-flex;background:rgba(0,0,0,0);box-shadow:inset 0 0 0 1px rgba(255,255,255,.25);transition:box-shadow .15s,background-color .15s;text-decoration:none;border-radius:99px;color:rgba(255,255,255,.8);align-items:center;justify-content:center;height:var(--cta--height);padding-top:0;padding-bottom:0;padding-right:var(--cta--padding);padding-left:var(--cta--padding);white-space:nowrap;border:0;font-size:20px;line-height:34px;font-weight:600;--cta--padding: 20.5px;--cta--height: 43px;--cta--hover--border-width: 2px}.time-limit-reached-post-game-dialog .wrapper button,.time-limit-reached-post-game-dialog .wrapper button *{cursor:pointer}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-post-game-dialog .wrapper button{border:1px solid #fff}}.time-limit-reached-post-game-dialog .wrapper button>*{padding-right:4.5px;padding-left:4.5px;display:flex;height:100%;align-items:center}.time-limit-reached-post-game-dialog .wrapper button>*:first-child{padding-left:0}.time-limit-reached-post-game-dialog .wrapper button>*:last-child{padding-right:0}.time-limit-reached-post-game-dialog .wrapper button svg{opacity:.25;transition:opacity .15s}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-post-game-dialog .wrapper button svg *{fill:CanvasText}}.time-limit-reached-post-game-dialog .wrapper button svg *{fill:var(--cta__icon--color)}@media(forced-colors: active){body:not(.override-contrast-mode) .time-limit-reached-post-game-dialog .wrapper button svg{opacity:1}}.time-limit-reached-post-game-dialog .wrapper button img{height:50%}.time-limit-reached-post-game-dialog .wrapper button:disabled{opacity:.3}.time-limit-reached-post-game-dialog .wrapper button:disabled,.time-limit-reached-post-game-dialog .wrapper button:disabled *{cursor:default;pointer-events:none}@media(hover: hover){.time-limit-reached-post-game-dialog .wrapper button:not(:disabled):hover{box-shadow:inset 0 0 0 var(--cta--hover--border-width) var(--cta--hover--color, var(--theme--highlight));background-color:rgba(0,0,0,0);color:#fff}.time-limit-reached-post-game-dialog .wrapper button:not(:disabled):hover svg{opacity:1}}.time-limit-reached-post-game-dialog .wrapper button:not(:disabled):active{background-color:var(--cta--active--background-color, var(--theme--highlight));color:var(--cta--active--color, #fff)}.time-limit-reached-post-game-dialog .wrapper button.primary{background-color:var(--color--accent) !important;color:var(--theme--background) !important;transition:filter .15s;box-shadow:none !important;min-width:307px}@media(hover: hover){.time-limit-reached-post-game-dialog .wrapper button.primary:hover{filter:brightness(1.1)}}.time-limit-reached-post-game-dialog .wrapper .buttons{display:inline-flex;align-items:center;margin-top:20px}.time-limit-reached-post-game-dialog .wrapper .buttons button+button{margin-left:10px;margin-top:0}.time-limit-reached-post-game-dialog .wrapper .graphic{position:relative;width:480px;height:200px;margin-bottom:30px;pointer-events:none}.time-limit-reached-post-game-dialog .wrapper .graphic.alternate{height:300px}.time-limit-reached-post-game-dialog .wrapper .graphic img{position:absolute;left:0;top:-50px}.time-limit-reached-post-game-dialog .wrapper .graphic .free-badge{font-weight:700;font-size:16.1616px;line-height:27px;color:var(--theme--highlight);opacity:.6;position:absolute;left:100px;bottom:43px;transform:translateX(-50%)}.time-limit-reached-post-game-dialog .wrapper .graphic .pro-badge{font-weight:700;color:var(--color--accent);font-size:14px;line-height:26px;letter-spacing:.6px;text-transform:uppercase;background:rgba(var(--theme--secondary-background), 0.25);border:1px solid var(--color--accent);padding:0 5.5px;border-radius:4px;position:absolute;right:100px;bottom:43px;transform:translateX(50%)}",""]);const s=n}}]);
"use strict";(global.webpackChunkWeMod=global.webpackChunkWeMod||[]).push([[9099],{"cheats/resources/elements/version-history-menu":(e,r,a)=>{a.r(r),a.d(r,{VersionHistoryMenu:()=>p});var i=a(15215),n=a("aurelia-event-aggregator"),o=a("aurelia-framework"),t=a(20770),l=a(45660),s=a(59239),c=a(68663);let p=class{#e;#r;#a;constructor(e,r,a){this.#e=e,this.#r=r,this.#a=a}attached(){this.#i()}async#i(){try{this.trainerHistory=await this.#r.getTrainerHistoryForGame(this.gameId)}catch{try{this.trainerHistory=await this.#n()}catch{}}}async#n(){return(await this.#e.state.pipe((0,l.$)(),(0,s.E)("trainers",this.gameId)).toPromise()||[]).slice().sort(((e,r)=>r.releasedAt.localeCompare(e.releasedAt))).map((e=>({id:e.id,cheatCount:e.blueprint.cheats.length,supportedVersions:e.supportedVersions,releasedAt:e.releasedAt,createdAt:e.releasedAt})))}loadTrainer(e){this.#a.publish("load-trainer",e),this.open=!1}};(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",String)],p.prototype,"gameId",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",String)],p.prototype,"titleId",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.toView}),(0,i.Sn)("design:type",Object)],p.prototype,"selectedTrainer",void 0),(0,i.Cg)([(0,o.bindable)({defaultBindingMode:o.bindingMode.twoWay}),(0,i.Sn)("design:type",Boolean)],p.prototype,"open",void 0),p=(0,i.Cg)([(0,o.autoinject)(),(0,i.Sn)("design:paramtypes",[t.il,c.x,n.EventAggregator])],p)},"cheats/resources/elements/version-history-menu.html":(e,r,a)=>{a.r(r),a.d(r,{default:()=>i});const i='<template class="au-animate"> <require from="./version-history-menu.scss"></require> <require from="../../../shared/resources/elements/toggle.html"></require> <div class="container"> <div class="scroll-wrapper"> <nav> <template if.bind="!trainerHistory"> <div class="placeholder" repeat.for="p of [1,2,3]" css="--placeholder--index: ${$index}"> <i></i> <span class="label">9/9/1999 - 99 Mods</span> </div> </template> <template else> <a repeat.for="otherTrainer of trainerHistory" click.delegate="loadTrainer(otherTrainer.id)" title-link="value.bind: \'trainer_history\'; title-id.bind: titleId; game-id.bind: gameId; trainer-id.bind: otherTrainer.id;"> <i>${selectedTrainer.id === otherTrainer.id ? \'check\' : \'\'}</i> <span class="label"> <template if.bind="otherTrainer.releasedAt"> ${otherTrainer.releasedAt | i18nDateTime:{dateStyle:\'short\'}} </template> <template else> ${\'version_history_menu.unreleased\' | i18n} </template> - ${`version_history_menu.$x_mod${otherTrainer.cheatCount > 1 ? \'s\' : \'\'}` | i18n:{x: otherTrainer.cheatCount}} </span> <span class="badge" if.bind="$index === 0">${\'version_history_menu.latest\' | i18n}</span> </a> </template> </nav> </div> </div> </template> '},"cheats/resources/elements/version-history-menu.scss":(e,r,a)=>{a.r(r),a.d(r,{default:()=>h});var i=a(31601),n=a.n(i),o=a(76314),t=a.n(o),l=a(4417),s=a.n(l),c=new URL(a(83959),a.b),p=t()(n()),d=s()(c);p.push([e.id,`@font-face{font-family:"Material Symbols Outlined";font-style:normal;font-weight:100 700;font-display:block;src:url(${d}) format("woff2")}.material-symbols-outlined,version-history-menu>.container .scroll-wrapper>nav>a i,version-history-menu>.container .scroll-wrapper>nav .placeholder i{font-family:"Material Symbols Outlined";font-weight:normal;font-style:normal;font-size:24px;line-height:1;letter-spacing:normal;text-transform:none;display:inline-block;white-space:nowrap;word-wrap:normal;direction:ltr;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;text-rendering:optimizeLegibility;font-feature-settings:"liga"}version-history-menu{position:relative}version-history-menu>.container{display:flex;flex-direction:column;justify-content:flex-start;padding:8px;position:relative;width:280px;background:rgba(128,128,128,.1);backdrop-filter:blur(25px);-webkit-transform:translate3d(0, 0, 0);transform:translate3d(0, 0, 0);border-radius:16px}version-history-menu>.container .scroll-wrapper{max-height:300px;overflow-y:overlay;overflow-x:hidden}version-history-menu>.container .scroll-wrapper::-webkit-scrollbar{width:7px;height:7px;background:rgba(255,255,255,.1);border-radius:10px;margin:10px}version-history-menu>.container .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive,version-history-menu>.container .scroll-wrapper::-webkit-scrollbar-thumb{background-color:rgba(255,255,255,.2);border-radius:99px;border:1px solid rgba(0,0,0,.2);background-clip:padding-box}version-history-menu>.container .scroll-wrapper::-webkit-scrollbar-thumb:window-inactive:hover,version-history-menu>.container .scroll-wrapper::-webkit-scrollbar-thumb:hover{background-color:rgba(255,255,255,.5)}version-history-menu>.container .scroll-wrapper>nav{display:flex;flex-direction:column;gap:1px}version-history-menu>.container .scroll-wrapper>nav>a,version-history-menu>.container .scroll-wrapper>nav .placeholder{display:flex;align-items:center;gap:8px;padding:8px;background:rgba(0,0,0,0);border:none;transition:background-color .15s;border-radius:8px;text-align:left}version-history-menu>.container .scroll-wrapper>nav>a:hover,version-history-menu>.container .scroll-wrapper>nav .placeholder:hover{--menu__item__icon--color: #fff;--menu__item__label--color: #fff;--menu__item__tag--color: #fff;--menu__item__badge--color: #fff;background:rgba(255,255,255,.15)}version-history-menu>.container .scroll-wrapper>nav>a,version-history-menu>.container .scroll-wrapper>nav>a *,version-history-menu>.container .scroll-wrapper>nav .placeholder,version-history-menu>.container .scroll-wrapper>nav .placeholder *{cursor:pointer}version-history-menu>.container .scroll-wrapper>nav>a i,version-history-menu>.container .scroll-wrapper>nav .placeholder i{font-variation-settings:"FILL" 0,"wght" 400;font-size:20px;flex:0 0 auto;width:20px;height:20px;display:inline-flex;align-items:center;justify-content:center;color:var(--menu__item__icon--color, rgba(255, 255, 255, 0.6));transition:color .15s}version-history-menu>.container .scroll-wrapper>nav>a .label,version-history-menu>.container .scroll-wrapper>nav .placeholder .label{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;min-width:0;flex:1 1 auto;color:var(--menu__item__label--color, rgba(255, 255, 255, 0.6));padding:2px 0;transition:color .15s;font-size:14px;line-height:20px}version-history-menu>.container .scroll-wrapper>nav>a .label .description,version-history-menu>.container .scroll-wrapper>nav .placeholder .label .description{font-weight:500;white-space:initial}version-history-menu>.container .scroll-wrapper>nav>a .badge,version-history-menu>.container .scroll-wrapper>nav .placeholder .badge{font-weight:700;flex:0 0 auto;font-size:10px;line-height:16px;text-transform:uppercase;color:var(--menu__item__badge--color, rgba(255, 255, 255, 0.8));transition:color .15s;background:var(--menu__item__badge--background-color, rgba(255, 255, 255, 0.1));padding:0 4px;border-radius:4px;--menu__item__badge--background-color: var(--theme--highlight)}version-history-menu>.container .scroll-wrapper>nav>a.placeholder,version-history-menu>.container .scroll-wrapper>nav>a.placeholder *,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder *{pointer-events:none}version-history-menu>.container .scroll-wrapper>nav>a.placeholder .label,version-history-menu>.container .scroll-wrapper>nav>a.placeholder i,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder .label,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder i{background:rgba(255,255,255,.02);animation:placeholder__el--fade 1s ease-in-out calc(-1.5s*var(--placeholder--index)) alternate infinite,placeholder__el--initial-fade .1s linear 0s normal both;border-radius:2px}version-history-menu>.container .scroll-wrapper>nav>a.placeholder .label,version-history-menu>.container .scroll-wrapper>nav>a.placeholder .label *,version-history-menu>.container .scroll-wrapper>nav>a.placeholder i,version-history-menu>.container .scroll-wrapper>nav>a.placeholder i *,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder .label,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder .label *,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder i,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder i *{color:rgba(0,0,0,0) !important;border:0 !important}version-history-menu>.container .scroll-wrapper>nav>a.placeholder .label *,version-history-menu>.container .scroll-wrapper>nav>a.placeholder i *,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder .label *,version-history-menu>.container .scroll-wrapper>nav .placeholder.placeholder i *{visibility:hidden}`,""]);const h=p},"cheats/resources/value-converters/blueprint-translation":(e,r,a)=>{a.r(r),a.d(r,{BlueprintTranslationValueConverter:()=>p});var i=a(15215),n=a("aurelia-framework"),o=a(20770),t=a(59239),l=a(88120),s=a(20057),c=a(15448);let p=class{#o;#t;constructor(e,r){this.signals=[c.p],this.#o=e,r.state.pipe((0,t.E)("gameTranslations"),(0,l.F)()).subscribe((e=>{this.#t=e}))}toView(e,r){return e&&r?this.#t?.[r.gameId]?.[this.#o.getEffectiveLocale().baseName]?.[e]??e:e}};p=(0,i.Cg)([(0,n.autoinject)(),(0,i.Sn)("design:paramtypes",[s.F2,o.il])],p)},"cheats/resources/value-converters/group-feed-items":(e,r,a)=>{a.r(r),a.d(r,{GroupFeedItemsValueConverter:()=>i});class i{filterItems(e,r){switch(r){case"playable":return e.filter((e=>e.isInstalled&&e.isAvailable));case"unsupported":return e.filter((e=>!e.isAvailable));case"launch_without_mods":return e.filter((e=>e.isInstalled&&!e.isAvailable));case"unsupported_and_not_installed":return e.filter((e=>!e.isInstalled&&!e.isAvailable));case"installable":return e.filter((e=>!e.isInstalled&&e.isAvailable));case"free_games_to_install":return e.filter((e=>e.isFree&&!e.isFavorite&&!e.isInstalled));case"favorites_not_installed":return e.filter((e=>e.isAvailable&&e.isFavorite&&!e.isInstalled));case"my_games":return e.filter((e=>e.isInstalled));default:return e}}toView(e,r){return r.map((r=>({group:r,items:this.filterItems(e,r)}))).filter((e=>e.items.length>0))}}}}]);